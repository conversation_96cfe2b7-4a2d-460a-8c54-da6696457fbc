<?php
return [
    'drama' => [
        'url' => 'http://example.drama.com',
        'key' => 'xxxxxxxxxxxxxxxxxxxxxxxx'
    ],
    'pushserver' => [
        'url' => 'http://172.16.10.20:8098/',
        'key' => 'xxxxxxxxxxxxxxxxxxxxxxxx'
    ],
    'iap-gateway' => [
        'url' => 'http://172.16.10.20:8090',
    ],
    'proxy' => [
        'http' => 'http://proxy.maoer.co:8123',
    ],
    'bilibili-flow' => [
        'url' => 'http://api.example.com/',
        // Bilibili 接入联通 API 用
        'cpid' => 'xxxxxx',
        'cpkey' => 'xxxxxxxx',
        // 免流规则
        'rules' => [
            // 以下规则前三个按运营商标识做键名，1：联通；2：电信；3：移动
            1 => 'http://static.example.com/flow-rules.json',  // 联通
            2 => 'http://static.example.com/flow-rules.json',  // 电信
            3 => 'http://static.example.com/flow-rules.json',  // 移动
            4 => 'http://static.example.com/flow-rules-unicom-card.json',  // 联通卡免流规则（包含直播免流）
            5 => 'http://static.example.com/flow-rules-unicom-old-pack.json',  // 旧的联通包免流规则（包含直播免流）
            6 => 'http://static.example.com/flow-rules-unicom.json',  // 电信免流规则（包含直播免流）
            7 => 'http://static.example.com/flow-rules-mobile.json',  // 移动免流规则（包含直播免流）
            8 => 'http://static.example.com/flow-rules-unicom-pack.json',  // 联通包免流规则（包含直播免流，客户端支持不含 query 参数的 url）
        ],
    ],
    'go' => [
        // 此处的 URL 不需要以 / 结尾
        'url' => 'http://example.com',
    ],
    'audio-chatroom' => [
        'url' => 'http://example.com',
        'key' => 'xxxxxxxxxxxxxxxxxxxxxxxx'
    ],
    'live' => [
        'url' => 'http://live-service.srv.maoer.co:3013',
        'key' => 'xxxxxxxxxxxxxxxxxxxxxxxx',
    ],
    'apple' => [
        // 苹果 ASA 搜索广告投放
        'apple_search_ads' => [
            '12345' => [  // org_id 组织 ID
                'client_id' => 'test_client_id',
                'team_id' => 'test_team_id',
                'key_id' => 'test_key_id',
                'private_key' => "-----BEGIN EC PRIVATE KEY-----\ntest....test\n-----END EC PRIVATE KEY-----",
            ],
            '67890' => [
                'client_id' => 'test_client_id2',
                'team_id' => 'test_team_id2',
                'key_id' => 'test_key_id2',
                'private_key' => "-----BEGIN EC PRIVATE KEY-----\ntest2....test2\n-----END EC PRIVATE KEY-----",
            ],
            'default_org_id' => '12345',  // 默认的组织 ID（用于 iOS < 14.3 ASA 获取广告信息）
        ],
        // 用于苹果支付验证小票
        'apple_shared_secret' => 'xxxxxx',
        // https://www.apple.com/certificateauthority/
        // 用于解析苹果服务端通知 v2 报文
        'apple_root_ca_g3' => "-----BEGIN CERTIFICATE-----\nxxxxxxx\n-----END CERTIFICATE-----",
        'appstore_server_api' => [
            'base_uri' => 'https://api.storekit-sandbox.itunes.apple.com',
            'kid' => 'test-kid',
            'private_key' => "-----BEGIN PRIVATE KEY-----\nxxxxx\nxxxxx\nxxxxx\nxxxxx\n-----END PRIVATE KEY-----",
            'iss' => 'test-iss',
            'bid' => 'com.missevan.CatEarFM',
            'proxy' => 'http://proxy.maoer.co:8123',
        ],
    ],
    'doudian' => [
        'app_key' => '111111111',
        'app_secret' => 'abc-cde-efg-hij-klmn',
        'shop_id' => '123456',
        // 精选联盟中走普通渠道费率的主播 ID
        'normal_fee_rate_author_ids' => [
            // 喵喵广播站
            2683221693703400,
        ],
    ],
    'ad' => [
        'wangyiyun' => [
            // 账号 ID，之后可能存在多个账号
            '123456' => [
                'app_key' => 'xxxxxxxxxxxxxxxxxxxxxxxx',
                'secret_key' => 'xxxxxxxxxxxxxxxxxxxxxxxx',
                'source' => 1
            ],
        ],
        'vivo' => [
            // advertise_id
            '234324343' => [
                'client_id' => 'xxxxxxxxxxxxxxxxxxxxxxxx',  // 应用 ID
                'client_secret' => 'xxxxxxxxxxxxxxxxxxxxxxxx',  // 应用密钥
                'src_id' => 'xxxxxxxxxxxxxxxxxxxxxxxx',  // 数据源 ID
            ],
        ],
    ],
    'httpdns' => [
        'aliyun_service_url' => 'http://203.107.1.33/191607/ss',
    ],
    'jingdong' => [
        'key' => 'test-key',
        'vendor_id' => 12345,
    ],
    'jingdong-v2' => [
        'private_key' => 'xxxx',
        'customer_id' => 'yyyy',
    ],
    'wechatpay' => [
        // https://info.missevan.com/pages/viewpage.action?pageId=97887348
        'jsapi' => [
            'app_id' => '',  // 应用 ID
            'app_secret' => '',  // 应用秘钥
            'apiv3_key' => '',  // APIv3 Key
            'merchant_id' => '',  // 商户号
            'merchant_serial_no' => '',  // 商户 API 私钥的证书序列号
            'platform_cert' => [  // 微信平台证书
                // 证书序列号 => 证书（新旧证书更新的过渡期间，两个证书都在使用）
                '2222' => [
                    'content' => "-----BEGIN CERTIFICATE-----\ntest certificate2\n-----END CERTIFICATE-----",
                    'latest' => true,  // 是否为最新证书
                ],
                '1111' => [
                    'content' => "-----BEGIN CERTIFICATE-----\ntest certificate1\n-----END CERTIFICATE-----",
                    'latest' => false,
                ],
            ],
            'apiclient_key' => "-----BEGIN PRIVATE KEY-----\ntest private key\n-----END PRIVATE KEY-----",
            'authorize_redirect_url' => 'https://www.missevan.com/member/wechatpubaccauthorize?backurl=https%3A%2F%2Fm.missevan.com%2Fwallet',  // 授权后的跳回地址
            'notify_url' => 'https://app.missevan.com/callback/wechatpay-v3',  // 支付回调地址
        ],
    ],
    'wechat-offiaccount' => [
        // https://developers.weixin.qq.com/doc/offiaccount/Getting_Started/Getting_Started_Guide.html
        // 'app_id' => 'test_app_id',  // 应用 ID，暂不需要配置
        // 'app_secret' => 'test_app_secret',  // 应用秘钥，暂不需要配置
        // 'url' => 'https://api.weixin.qq.com',  // 请求微信接口，暂不需要配置
        'token' => 'test_token',  // 微信公众号的 Token
    ],
    'openapi_key_secret_map' => [
        'abcde' => '12345',
    ],
    'oa_flow_coin_topup' => [
        'app_id' => 'xxxxxxxxxxxx',
        'app_secret' => 'xxxxxxxxxxxxxxxxxxxxxxxx',
        'process_name' => '猫耳充值后台充钻审批流',
    ],
    'alipay' => [
        'gateway' => 'https://openapi-sandbox.dl.alipaydev.com/gateway.do',
        'app_id' => '123456',
        'public_key' => '',  // 支付宝公钥
        'develop_private_key' => '',  // 支付宝开发者私钥
        'notify_url' => [
            'default' => 'http://127.0.0.1:8017/callback/alipay',
            'guild_live_order' => 'http://127.0.0.1:8017/callback/guild-live-alipay',
        ],
    ],
    // 微博开发平台授权，到端任务换量接口相关
    'weibo_third_party_task' => [
        'app_key' => '',
        'access_token' => '',  // 申请时间：2025-04-16 17:00，有效期 5 年
    ]
];
