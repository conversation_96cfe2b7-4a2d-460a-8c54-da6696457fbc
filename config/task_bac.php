<?php

return [
    'additional_task_older_623' => [  // 安卓及 iOS < 6.2.3 版本展示的任务信息，不配置时不下发
        'gtype' => 7,  // 任务类型
        'name' => '免费领钻石',
        'reward_type' => 2,  // 奖励类型。1：小鱼干，2：钻石
        'reward_num' => 5,  // 每次完成任务可获得奖励数量
        'info' => '每天完成从猫耳FM进入淘宝农场，必得 5 钻',  // 任务说明
        'finished_info_format' => '恭喜获得 %d 钻',  // 完成任务提示。%d 为占位符，不要替换为具体数字
        'icon_url' => 'https://abc.png',  // 任务图标
        'dark_icon_url' => 'https://abc.png',  // 夜间模式任务图标
        'button_text' => '去完成',  // 按钮上展示的文字
        'native_url' => 'tbopen://m.taobao.com/tbopen/index.html?foo=bar',  // 唤起端外应用的链接，不下发时不跳转
        'web_url' => 'https://market.m.test.com/xx?foo=bar',  // 打开的网页链接。当下发了 native_url 时，优先唤起 native_url 的应用，不下发时不跳转
        'event_id' => 799,  // 用于统计该任务每日发放钻石总量推送的活动 ID, 当不需要推送时可删除该配置或设置为 0
        'diamond_random_rule' => [  // 随机奖励发放规则，仅对钻石奖励生效。当不配置或数组为空时表示不支持随机奖励
            'random_limit' => 100000,  // 随机奖励发放总人次限制。当每自然日发放随机奖励次数超出该配置时，固定发放 reward_num 个奖励，不配置或值为 0 时无限制
            'weights' => [  // 奖励数量及其权重。key 为奖励数量，value 为该奖励的随机权重
                1 => 3470,
                2 => 5000,
                3 => 1000,
                4 => 200,
                5 => 100,
                6 => 100,
                7 => 60,
                8 => 40,
                9 => 20,
                10 => 10,
            ],
        ],
        'legacy' => [  // 安卓及 iOS < 6.2.2 版本任务配置
            'reward_num' => 10,  // 每次完成任务可获得奖励数量
            'name' => '淘宝农场',  // 任务名称
            'button_text' => '去农场',  // 按钮上展示的文字
            'info' => '点击逛逛淘宝农场，必得 10 条小鱼干',  // 任务说明
            'finished_info_format' => '恭喜获得 %d 条小鱼干',  // 完成任务提示。%d 为占位符，不要替换为具体数字
        ],
    ],
    // 每日任务：限时任务列表
    'additional_task_list' => [  // 不配置时不下发
        [  // 淘宝农场任务
            'gtype' => 7,  // 任务类型
            'scene' => 3,
            'name' => '免费领钻石',
            'reward_type' => 2,  // 奖励类型。1：小鱼干，2：钻石
            'reward_num' => 5,  // 每次完成任务可获得奖励数量
            'info' => '每天完成从猫耳FM进入淘宝农场，必得 5 钻',  // 任务说明
            'finished_info_format' => '恭喜获得 %d 钻',  // 完成任务提示。%d 为占位符，不要替换为具体数字
            'icon_url' => 'https://abc.png',  // 任务图标
            'dark_icon_url' => 'https://abc.png',  // 夜间模式任务图标
            'native_url' => [
                'android' => 'tbopen://android.taobao.com/tbopen/index.html?foo=bar',  // 唤起端外应用的链接，配置为空时不跳转
                'ios' => 'tbopen://ios.taobao.com/tbopen/index.html?foo=bar',  // 唤起端外应用的链接，配置为空时不跳转
            ],
            'web_url' => 'https://market.m.test.com/xx?foo=bar',  // 打开的网页链接。当下发了 native_url 时，优先唤起 native_url 的应用，不下发时不跳转
            'event_id' => 799,  // 用于统计该任务每日发放钻石总量推送的活动 ID, 当不需要推送时可删除该配置或设置为 0
            // 支持的版本
            'support_version' => [
                'android' => '6.2.3',
                'ios' => '6.2.3',
            ],
            'start_time' => 0,  // 任务开始时间，0 表示立即开始。单位：秒
            'end_time' => 0, // 任务结束时间，0 表示永久有效。单位：秒
            'diamond_random_rule' => [  // 随机奖励发放规则，仅对钻石奖励生效。当不配置或数组为空时表示不支持随机奖励
                'random_limit' => 100000,  // 随机奖励发放总人次限制。当每自然日发放随机奖励次数超出该配置时，固定发放 reward_num 个奖励，不配置或值为 0 时无限制
                'weights' => [  // 奖励数量及其权重。key 为奖励数量，value 为该奖励的随机权重
                    1 => 3470,
                    2 => 5000,
                    3 => 1000,
                    4 => 200,
                    5 => 100,
                    6 => 100,
                    7 => 60,
                    8 => 40,
                    9 => 20,
                    10 => 10,
                ],
            ],
            'button_texts' => [  // 按钮上展示的文字，任务完成状态 finished 作为 field
                0 => '去完成',  // 任务未完成 finished = 0
                1 => '已完成',  // 任务已完成奖励 finished = 1
            ],
            'to_third_party' => false, // 是否为到端任务，true：是；false：否
        ],
        [  // 测试任务
            'gtype' => 8,
            'name' => '每日抽钻石',
            'reward_type' => 2,
            'info' => '每天完成从猫耳FM进入支付宝农场，必得 4 钻',
            'finished_info_format' => '恭喜获得 %d 钻',  // 完成任务提示
            'icon_url' => 'https://abc.png',
            'dark_icon_url' => 'https://abc.png',  // 夜间模式任务图标
            'reward_num' => 4,
            'native_url' => [
                'android' => 'tbopen://m.taobao.com/tbopen/index.html?foo=bar',  // 唤起端外应用的链接，配置为空时不跳转
                'ios' => 'tbopen://m.taobao.com/tbopen/index.html?foo=bar',  // 唤起端外应用的链接，配置为空时不跳转
            ],
            'web_url' => 'https://market.m.test.com/xx?foo=bar',
            // 支持的版本
            'support_version' => [
                'android' => '6.2.3',
                'ios' => '6.2.3',
            ],
            'start_time' => 0,  // 任务开始时间，0 表示立即开始。单位：秒
            'end_time' => 0, // 任务结束时间，0 表示永久有效。单位：秒
            'diamond_random_rule' => [
                'random_limit' => 2,
                'weights' => [
                    1 => 3470,
                    2 => 5000,
                ],
            ],
            'button_texts' => [  // 按钮上展示的文字，任务完成状态 finished 作为 field
                0 => '去完成',  // 任务未完成 finished = 0
                1 => '已完成',  // 任务已完成奖励 finished = 1
            ],
            'to_third_party' => false,
        ],
        [
            'gtype' => 9,  // 百度引流任务
            'scene' => 1,
            'name' => '每日抽钻石',
            'reward_type' => 2,
            'info' => 'test',
            'finished_info_format' => '恭喜获得 %d 钻',  // 完成任务提示
            'icon_url' => 'https://abc.png',
            'dark_icon_url' => 'https://abc.png',  // 夜间模式任务图标
            'reward_num' => 4,
            'native_url' => [
                'android' => 'tbopen://m.baidu.com/tbopen/index.html?foo=bar',  // 唤起端外应用的链接，配置为空时不跳转
                'ios' => 'tbopen://m.baidu.com/tbopen/index.html?foo=bar',  // 唤起端外应用的链接，配置为空时不跳转
            ],
            'web_url' => 'https://baidu.m.test.com/xx?foo=bar',
            // 支持的版本
            'support_version' => [
                'android' => '6.2.4',
                'ios' => '6.2.4',
            ],
            'start_time' => 0,  // 任务开始时间，0 表示立即开始。单位：秒
            'end_time' => 0, // 任务结束时间，0 表示永久有效。单位：秒
            'diamond_random_rule' => [
                'random_limit' => 2,
                'weights' => [
                    1 => 3470,
                    2 => 5000,
                ],
            ],
            'button_texts' => [  // 按钮上展示的文字，任务完成状态 finished 作为 field
                0 => '去完成',  // 任务未完成 finished = 0
                1 => '领钻石',  // 任务已完成未领取奖励 finished = 1
                2 => '已完成',  // 任务已完成已领取奖励 finished = 2
            ],
            'to_third_party' => true,
            'legacy' => [  // 安卓及 iOS < 6.2.7 版本（不支持到端任务）任务配置
                'name' => '百度极速版',  // 任务名称
                'reward_type' => 1,  // 奖励类型。1：小鱼干，2：钻石
                'reward_num' => 10,  // 每次完成任务可获得奖励数量
                'info' => '点击前往百度极速版，必得 10 条小鱼干（更新新版本奖励升级，随机获取 1-10 钻）',  // 任务说明
                'finished_info_format' => '恭喜获得 %d 条小鱼干',  // 完成任务提示。%d 为占位符，不要替换为具体数字
                'button_texts' => [  // 按钮上展示的文字，任务完成状态 finished 作为 field
                    0 => '去完成',  // 任务未完成 finished = 0
                    1 => '已完成',  // 任务已完成奖励 finished = 1
                ],
                'to_third_party' => false,
            ],
        ],
        [
            'gtype' => 13,  // 携程引流任务
            'scene' => 2,
            'name' => '每日抽钻石',
            'reward_type' => 2,
            'info' => '携程引流任务',
            'finished_info_format' => '恭喜获得 %d 钻',  // 完成任务提示
            'icon_url' => 'https://abc.png',
            'dark_icon_url' => 'https://abc.png',  // 夜间模式任务图标
            'reward_num' => 4,
            'native_url' => 'tbopen://m.ctrip.com/tbopen/index.html?foo=bar',
            'web_url' => 'https://ctrip.m.test.com/xx?foo=bar',
            // 支持的版本
            'support_version' => [
                'android' => '6.2.4',
                'ios' => '6.2.4',
            ],
            'start_time' => 2,  // 任务开始时间，0 表示立即开始。单位：秒
            'end_time' => 0, // 任务结束时间，0 表示永久有效。单位：秒
            'diamond_random_rule' => [
                'random_limit' => 2,
                'weights' => [
                    1 => 3470,
                    2 => 5000,
                ],
            ],
            'button_texts' => [  // 按钮上展示的文字，任务完成状态 finished 作为 field
                0 => '去完成',  // 任务未完成 finished = 0
                1 => '领钻石',  // 任务已完成未领取奖励 finished = 1
                2 => '已完成',  // 任务已完成已领取奖励 finished = 2
            ],
            'to_third_party' => false,
        ],
    ],
    'live_task_map' => [  // 不下发直播任务时配置空数组
        10 => [  // 进入直播间任务
            'gtype' => 10,  // 任务类型
            'type' => 1,  // 直播任务类型 1：普通直播任务，2：直播发消息任务
            'name' => '进入直播间',
            'reward_type' => 1,  // 奖励类型。1：小鱼干，2：钻石
            'info' => '每天进入直播间可摸鱼 1 次',  // 任务说明
            'icon_url' => 'https://abc.png',  // 任务图标
            'dark_icon_url' => 'https://abc.png',  // 夜间模式任务图标
            'icon_url_scene_live' => 'https://kk.png',  // 任务图标，在直播间半窗下发
            'button_texts' => [
                0 => '去摸鱼',  // 未完成
                1 => '摸鱼',    // 待领奖
                2 => '已完成',  // 已领奖
            ],
            'button_texts_scene_live' => [
                0 => '进行中',  // 未完成
                1 => '摸鱼',    // 待领奖
                2 => '已完成',  // 已领奖
            ],
        ],
        11 => [  // 收听直播时长任务 - 新人专享
            'gtype' => 11,  // 任务类型
            'type' => 1,  // 直播任务类型 1：普通直播任务，2：直播发消息任务
            'name' => '【惊喜任务】收听直播',
            'reward_type' => 2,  // 奖励类型。1：小鱼干，2：钻石
            'reward_num' => 1,  // 每次完成任务可获得奖励数量
            'task_value' => 180000, // 收听直播 3 分钟，单位：毫秒
            'info' => '收听直播满 3min 可随机抽取 1-10 钻石哦~',  // 任务说明
            'finished_info_format' => '恭喜获得 %d 钻',  // 完成钻石任务获取奖励的提示。%d 为占位符，不要替换为具体数字
            'icon_url' => 'https://abc.png',  // 任务图标
            'reward_icon_url' => 'https://ff.png',  // 奖励图标，当配置了奖励图标，需下发该图标
            'dark_icon_url' => 'https://abc.png',  // 夜间模式任务图标
            'icon_url_scene_live' => 'https://mmm.png',  // 任务图标，在直播间半窗下发
            'button_texts' => [
                0 => '去完成',  // 未完成
                1 => '抽钻石',  // 待领奖
                2 => '已完成',  // 已领奖
            ],
            'button_texts_scene_live' => [
                0 => '进行中',  // 未完成
                1 => '抽钻石',  // 待领奖
                2 => '已完成',  // 已领奖
            ],
            'diamond_random_rule' => [  // 随机奖励发放规则，仅对钻石奖励生效。当不配置或数组为空时表示不支持随机奖励
                'random_limit' => 100000,  // 随机钻石数量限制。当每自然日发放随机钻石数超出该配置时，固定发放 reward_num 个奖励，不配置或值为 0 时无限制
                'weights' => [  // 奖励数量及其权重。key 为奖励数量，value 为该奖励的随机权重
                    1 => 3470,
                    2 => 5000,
                    3 => 1000,
                    4 => 200,
                    5 => 100,
                    6 => 100,
                    7 => 60,
                    8 => 40,
                    9 => 20,
                    10 => 10,
                ],
            ],
        ],
        12 => [  // 收听直播时长任务 - 非新人
            'gtype' => 12,  // 任务类型
            'type' => 1,  // 直播任务类型 1：普通直播任务，2：直播发消息任务
            'name' => '【惊喜任务】收听直播',
            'reward_type' => 2,  // 奖励类型。1：小鱼干，2：钻石
            'reward_num' => 1,  // 每次完成任务可获得奖励数量
            'task_value' => 300000, // 收听直播 5 分钟，单位：毫秒
            'info' => '收听直播满 5min 可随机抽取 1-10 钻石哦~',  // 任务说明
            'finished_info_format' => '恭喜获得 %d 钻',  // 完成钻石任务获取奖励的提示。%d 为占位符，不要替换为具体数字
            'icon_url' => 'https://abc.png',  // 任务图标
            'dark_icon_url' => 'https://abc.png',  // 夜间模式任务图标
            'icon_url_scene_live' => 'https://yy.png',  // 任务图标，在直播间半窗下发
            'button_texts' => [
                0 => '去完成',  // 未完成
                1 => '抽钻石',  // 待领奖
                2 => '已完成',  // 已领奖
            ],
            'button_texts_scene_live' => [
                0 => '进行中',  // 未完成
                1 => '抽钻石',  // 待领奖
                2 => '已完成',  // 已领奖
            ],
            'event_id' => 801,  // 用于统计该任务每日发放钻石总量推送的活动 ID, 当不需要推送时可删除该配置或设置为 0
            'diamond_random_rule' => [  // 随机奖励发放规则，仅对钻石奖励生效。当不配置或数组为空时表示不支持随机奖励
                'random_limit' => 100000,  // 随机钻石数量限制。当每自然日发放随机钻石数超出该配置时，固定发放 reward_num 个奖励，不配置或值为 0 时无限制
                'weights' => [  // 奖励数量及其权重。key 为奖励数量，value 为该奖励的随机权重
                    1 => 3470,
                    2 => 5000,
                    3 => 1000,
                    4 => 200,
                    5 => 100,
                    6 => 100,
                    7 => 60,
                    8 => 40,
                    9 => 20,
                    10 => 10,
                ],
            ],
        ],
        15 => [  // 进入直播间任务
            'gtype' => 15,  // 任务类型
            'type' => 2,  // 直播任务类型 1：普通直播任务，2：直播发消息任务
            'name' => '发消息',
            'reward_type' => 2,  // 奖励类型。1：小鱼干，2：钻石
            'info' => '每天进入直播间发 3 条消息可抽 1-10 钻',  // 任务说明
            'task_value' => 3,  // 任务达标值
            'icon_url' => 'https://abc.png',  // 任务图标
            'dark_icon_url' => 'https://abc.png',  // 夜间模式任务图标
            'icon_url_scene_live' => 'https://aa.png',  // 任务图标，在直播间半窗下发
            'button_texts' => [
                0 => '去完成',  // 未完成
                1 => '抽钻石',  // 待领奖
                2 => '已完成',  // 已领奖
            ],
            'button_texts_scene_live' => [
                0 => '去完成',  // 未完成
                1 => '抽钻石',  // 待领奖
                2 => '已完成',  // 已领奖
            ],
            'diamond_random_rule' => [  // 随机奖励发放规则，仅对钻石奖励生效。当不配置或数组为空时表示不支持随机奖励
                'random_limit' => 100000,  // 随机钻石数量限制。当每自然日发放随机钻石数超出该配置时，固定发放 reward_num 个奖励，不配置或值为 0 时无限制
                'weights' => [  // 奖励数量及其权重。key 为奖励数量，value 为该奖励的随机权重
                    1 => 3470,
                    2 => 5000,
                    3 => 1000,
                    4 => 200,
                    5 => 100,
                    6 => 100,
                    7 => 60,
                    8 => 40,
                    9 => 20,
                    10 => 10,
                ],
            ],
        ],
    ],
    'wechat_offiaccount_task' => [  // 微信公众号任务
        'gtype' => 19,  // 任务类型
        'name' => '关注猫耳FM微信公众号',
        'reward_type' => 1,  // 奖励类型。1：小鱼干。注意当前仅支持小鱼干
        'reward_num' => 5,  // 每次完成任务可获得奖励数量
        'info' => '在公众号发送暗号完成任务，最多摸到 88 小鱼干',  // 任务说明
        'finished_info_format' => '运气爆棚，摸到 %d 个小鱼干！',  // 完成任务提示。%d 为占位符，不要替换为具体数字
        'icon_url' => 'https://abc.png',  // 任务图标
        'dark_icon_url' => 'https://abc.png',  // 夜间模式任务图标
        'web_url' => 'https://test.wechat.com/xx?foo=bar',  // 点击【去完成】按钮后打开的网页链接
        'open_client_text_format' => '<a href="https://test.test.com/event?third_task_token=%s">领取小鱼干</a>',  // 微信公众号回复的唤端超链接模板，%s 在业务代码中需要处理为实际 token 值
        // 支持的版本
        'support_version' => [
            'show_in_additional_task_list' => [ // 在限时任务中下发
                'android' => '6.2.3',
                'ios' => '6.2.3',
            ],
            'show_alone' => [ // 单独下发
                'android' => '6.4.2',
                'ios' => '6.4.2',
            ],
        ],
        'keywords' => ['活动任务'],  // 触发公众号任务回复关键词组
        'start_time' => 0,  // 任务开始时间，0 表示立即开始。单位：秒
        'end_time' => 0,  // 任务结束时间，0 表示永久有效。单位：秒
        'random_rule' => [  // 随机奖励发放规则，当前仅对小鱼干奖励生效。当不配置或数组为空时表示不支持随机奖励
            'weights' => [  // 奖励数量及其权重。key 为奖励数量，value 为该奖励的随机权重
                20 => 5,
                50 => 65,
                66 => 29,
                88 => 1,
            ],
            'finished_info_format' => [ // 奖励提示文案，对应的奖励不配置时，默认使用 wechat_offiaccount_task.finished_info_format，配置时优先使用此处文案
                20 => '好幸运，摸到了 %d 个小鱼干~',
            ]
        ],
        'button_texts' => [  // 按钮上展示的文字，任务完成状态 finished 作为 field
            0 => '去完成',  // 任务未完成 finished = 0
            1 => '摸鱼',  // 已完成任务，未领奖 finished = 1
        ],
        'to_third_party' => false, // 是否为到端任务，true：是；false：否
    ],
];
