<?php
return [
    'oss' => [
        'class' => 'missevan\storage\OssClient',
        'bucket' => '',
        'config' => [
            'accessKeyId' => '',
            'accessKeySecret' => '',
            'endpoint' => '',
            'publicUrl' => 'https://static.missevan.com',
            'protocolUrl' => 'oss:/',
        ],
        'buckets' => [  // 新协议地址配置格式 e.g. oss://missevan-test/path/test.jpg
            'bucket_name' => [
                'config' => [
                    'accessKeyId' => '',
                    'accessKeySecret' => '',
                    'endpoint' => '',
                    'publicUrl' => '',
                ],
            ],
        ],
    ],
    'oss_archive' => [
        'class' => 'missevan\storage\OssClient',
        'bucket' => '',
        'config' => [
            'accessKeyId' => '',
            'accessKeySecret' => '',
            'endpoint' => '',
            'publicUrl' => '',
            'protocolUrl' => 'oss_archive:/',
        ]
    ],
    // 语音包资源文件地址
    'voice' => [
        'class' => 'missevan\storage\OssClient',
        'bucket' => '',
        'config' => [
            'accessKeyId' => '',
            'accessKeySecret' => '',
            'endpoint' => '',
            'publicUrl' => '',
            'privateKey' => '',
            'protocolUrl' => 'voice:/',
        ],
    ],
    's3' => [
        'class' => 'missevan\storage\S3Client',
        'bucket' => 'mimifm-icon',
        'config' => [
            'accessKeyId' => '',
            'accessKeySecret' => '',
            'publicUrl' => 'https://static.mimi.jp',
            'protocolUrl' => 'oss:/',
            'region' => 'ap-northeast-1',
            // 'endpoint' => 'http://localhost:8000',
            // 'endpoint_discovery' => [
            //   'enabled' => true,
            //   'cache_limit' => 1000
            // ],
        ]
    ],
    // TODO: 以下配置不用于上传至相关 bucket，accessKeyId 与 accessKeySecret 可配置为“xxxx”，之后改为金山 Client
    'sound' => [
        'class' => 'missevan\storage\OssClient',
        'bucket' => 'missevan-sound',
        'config' => [
            'accessKeyId' => '',
            'accessKeySecret' => '',
            'endpoint' => '',
            'protocolUrl' => 'sound:/',
            'publicUrl' => '',
            'abroadPublicUrl' => '',  // 海外 CDN 域名，若该项不配置则不下发海外 CDN 域名资源
        ],
    ],
    'upos' => [
        'class' => 'missevan\storage\UposClient',
        'protocolUrl' => 'upos:/',
        'buckets' => [
            'mefmxcodeboss' => [
                'config' => [
                    'accessKeySecret' => 'test',
                    'businessSignUrl' => 'http://upos.test.com/maoerplayurl',
                    'force_host' => 1,
                ],
            ],
            'mefmexboss' => [
                'config' => [
                    'publicUrl' => 'https://static-test.test.com',
                    'no_sign' => 1,
                ],
            ],
        ],
    ],
];
