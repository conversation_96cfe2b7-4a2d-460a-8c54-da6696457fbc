<?php

$params = require(__DIR__ . '/params.php');
$db = require(__DIR__ . '/db.php');
$storage = require(__DIR__ . '/storage.php');
$version = require(__DIR__ . '/version.php');

$config = [
    'id' => 'basic-console',
    'name' => $version['name'],
    'version' => $version['version'],
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log'],
    'timeZone' => 'Asia/Shanghai',
    'controllerNamespace' => 'app\commands',
    'components' => [
        'cache' => [
            'class' => 'yii\caching\FileCache',
        ],
        'memcache' => $db['redis_lru'],
        'redis' => $db['redis'],
        'log' => [
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'tools' => [
            'class' => 'app\components\util\Tools'
        ],
        'db' => $db['mysql'],
        'db2' => $db['mysql2'],
        'storage' => $storage['oss'],
        'archiveStorage' => $storage['oss_archive'],
    ],
    'params' => $params,
    /*
    'controllerMap' => [
        'fixture' => [ // Fixture generation command line.
            'class' => 'yii\faker\FixtureController',
        ],
    ],
    */
];

if (YII_ENV_DEV) {
    // configuration adjustments for 'dev' environment
    $config['bootstrap'][] = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
    ];
}

return $config;
