<?php

$params = require(__DIR__ . '/params.php');
$db = require(__DIR__ . '/db.php');
$util = require(__DIR__ . '/util.php');
$storage = require(__DIR__ . '/storage.php');
$log = require(__DIR__ . '/log.php');
$insetparams = require(__DIR__ . '/insetparams.php');
$version = require(__DIR__ . '/version.php');

$config = [
    'id' => 'missevan',  // 或 mimi
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log'],
    'language' => 'zh-CN',  // 或 ja-JP
    'timeZone' => 'Asia/Shanghai',  // Asia/Tokyo
    'name' => $version['name'],
    'version' => $version['version'],
    'modules' => [
        'concept' => [
            'class' => 'app\modules\concept\Concept',
        ],
        'rpc' => [
            'class' => 'app\modules\rpc\Rpc',
        ],
        'openapi' => [
            'class' => 'app\modules\openapi\OpenApi',
        ],
    ],
    'components' => [
        'request' => [
            // !!! insert a secret key in the following (if it is empty) - this is required by cookie validation
            // 'cookieValidationKey' => 'hW4cTnpyJzhz9ZGI4BHN0E05YK_rJZke',
            'enableCookieValidation' => false,
        ],
        'authManager' => [
            'class' => 'app\components\web\AuthManage',
        ],
        'user' => [
            'class' => 'app\components\web\User',
        ],
        'errorHandler' => [
            'class' => 'app\middlewares\ErrorHandler',
        ],
        'log' => $log,
        'equip' => [
            'class' => 'app\components\util\Equipment'
        ],
        'tools' => [
            'class' => 'app\components\util\Tools'
        ],
        'db' => $db['maindb'],
        'db1' => $db['new_maindb'],
        'db2' => $db['voicedb'],
        'logdb' => $db['logdb'],
        'malldb' => $db['malldb'],
        'messagedb' => $db['messagedb'],
        'readonly_messagedb' => $db['readonly_messagedb'],
        'growthdb' => $db['growthdb'],
        'paydb' => $db['paydb'],
        'cache' => [
            'class' => 'yii\caching\FileCache',
        ],
        'redis' => $db['redis'],
        'redis_sound_view' => $db['redis_sound_view'],
        'memcache' => $db['redis_lru'],
        'databus' => $db['databus'],
        'paydatabus' => $db['pay_databus'],

        'storage' => $storage['oss'],
        'voiceStorage' => $storage['voice'],
        'upos' => $storage['upos'],
        'captcha' => $util['captcha'],
        'go' => $util['go'],
        'live' => $util['live'],  // DEPRECATED: 后续应使用 liveRpc 组件
        'liveRpc' => $util['liveRpc'],
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
            'rules' => [
            ],
        ],
        'insetParams' => [
            'class' => 'app\components\util\InsetParams',
            'params' => $insetparams,
        ],
        'i18n' => [
            'translations' => [
                'app*' => [
                    'class' => 'yii\i18n\PhpMessageSource',
                    'basePath' => '@app/messages',
                    'sourceLanguage' => 'en',
                    'fileMap' => [
                        'app/base' => 'base.php',
                        'app/error' => 'error.php',
                    ],
                ],
            ],
        ],
        'sso' => $util['sso'],
        'minigame' => $util['minigame'],
        'serviceRpc' => $util['serviceRpc'],
    ],
    'params' => $params,
];

if (YII_ENV_DEV) {
    // configuration adjustments for 'dev' environment
    $config['bootstrap'][] = 'debug';
    $config['modules']['debug'] = [
        'class' => 'app\components\base\debug\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        //'allowedIPs' => ['127.0.0.1', '::1'],
    ];

    $config['bootstrap'][] = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        //'allowedIPs' => ['127.0.0.1', '::1'],
    ];
}

return $config;
