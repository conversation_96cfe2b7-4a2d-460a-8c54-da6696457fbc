<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/12
 * Time: 12:07
 */
return [
    // 阿里云人机验证
    'captcha' => [
        'class' => 'app\components\util\Captcha',
        'accessKeyId' => 'xxxxxxxxxxxxxxxx',
        'accessKeySecret' => 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
        'appKey' => [
            'android' => 'xxxxxxxxxxxxxxxxxxxx',
            'ios' => 'xxxxxxxxxxxxxxxxxxxx',
        ],
        'slideAppKey' => 'xxxxxxxxxxxxxxxxxxxx',
        'regionId' => 'cn-hangzhou',
    ],
    'go' => [
        'class' => 'app\components\util\Go',
        'secret' => 'xxxxxxxxxxxxxxxxxxxxxx',
    ],
    'live' => [
        'class' => 'app\components\util\LiveRpc',
        'url' => 'xxxxxxxxxxxxxxxxxxxxxx',
        'secret' => 'xxxxxxxxxxxxxxxxxxxxxx',
        'schemeUrl' => 'missevan://live',
    ],
    'liveRpc' => [
        'class' => 'missevan\rpc\LiveRpc',
        'url' => 'http://live-service-rpc:3011',
        'secret' => 'testkey',
    ],
    'sso' => [
        'class' => 'app\components\util\SSOClient',
        'url' => 'xxxxxxxxxxxxxxxxxxxxxx',
        'secret' => 'xxxxxxxxxxxxxxxxxxxxxx',
    ],
    'minigame' => [
        'class' => 'missevan\util\MiniGame',
        'url' => 'xxxxxxxxxxxxxxxxxxxxxx',
        'secret' => 'xxxxxxxxxxxxxxxxxxxxxx',
    ],
    'serviceRpc' => [
        'class' => 'missevan\rpc\ServiceRpc',
        'url' => 'http://rpc-services',
        'secret' => 'testkey',
    ],
];
