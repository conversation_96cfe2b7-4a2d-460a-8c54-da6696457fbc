<?php
/**
 * Created by PhpStorm.
 * User: TomCao
 * Date: 2018/4/10
 * Time: 上午11:20
 */
use yii\web\Request;

$date = date('Ymd');

$targets = [
    'receipt' => [
        'class' => 'yii\log\FileTarget',
        'levels' => ['warning'],
        'logFile' => "@app/runtime/logs/receipt-$date.log",
        'logVars' => [],
        'categories' => ['app\forms\RechargeForm:*'],
        'prefix' => function ($message) {
            $hostname = gethostname();

            $request = Yii::$app->getRequest();
            $ip = $request instanceof Request ? $request->getUserIP() : '-';

            $user_id = Yii::$app->user->id ?? '-';

            $equipment = Yii::$app->equip ?? null;
            $equip_id = $equipment ? $equipment->getEquipId() : '-';

            return "[$hostname][$ip][$equip_id][$user_id]";
        }
    ],
    'agent' => [
        'class' => 'app\components\log\LogAgentTarget',
        'levels' => ['error', 'warning', 'info'],
        'logVars' => [],
        'except' => ['yii\web\HttpException:4*', 'yii\db\Connection::open'],
        'dsn' => 'udp://logstash.example.com:8911',
        'app_id' => 'foo',
        'host' => gethostname(),
        'instance_id' => 'bar-1',
        'prefix' => function ($message) {
            $request = Yii::$app->getRequest();
            $ip = $request instanceof Request ? $request->getUserIP() : '-';

            $user_id = Yii::$app->user->id ?? '-';

            $equipment = Yii::$app->equip ?? null;
            $equip_id = $equipment ? $equipment->getEquipId() : '-';

            return "[$ip][$equip_id][$user_id]";
        },
    ],
];

if (YII_DEBUG) {
    $targets['all'] = [
        'class' => 'yii\log\FileTarget',
        'levels' => ['error', 'warning', 'info'],
    ];
}

return [
    'traceLevel' => YII_DEBUG ? 3 : 0,
    'targets' => $targets,
];
