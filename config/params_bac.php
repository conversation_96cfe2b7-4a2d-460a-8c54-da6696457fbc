<?php
$_service = require(__DIR__ . '/service.php');
$templates = require(__DIR__ . '/templates.php');

$static_domain = 'https://static.missevan.com';
$domain = 'https://www.missevan.com';
$domain_mobile_web = 'https://m.missevan.com';
$link_domain = 'https://link.missevan.com';

// 日常任务
$task = require(__DIR__ . '/task.php');

// iOS、谷歌新充值档位开启时间
$ios_googlepay_new_topup_menu_time = strtotime('2025-07-01 00:00:00');
// 新渠道费率生效时间
$new_fee_rate_time = strtotime('2025-07-01 00:00:00');

$coin_rate_live_scene = $coin_rate_radio_scene = [
    'ios' => 0.0314,
    'android' => 0.001,
    'paypal' => 0.01,
    'tmallios' => 0.0012,
    'googlepay' => 0.0314,
    'doudian' => 0.001,
    'vip' => 0.0314,
    'new_ios' => 0.0314,
    'new_googlepay' => 0.0314,
];
$vip_fee_rate = [
    'ios' => 0.4,
    'android' => 0.05,
];
if ($_SERVER['REQUEST_TIME'] >= $new_fee_rate_time) {
    $coin_rate_live_scene = [
        'ios' => 0,
        'android' => 0.001,
        'paypal' => 0.01,
        'tmallios' => 0.0012,
        'googlepay' => 0,
        'doudian' => 0.001,
        'vip' => 0.001,
        'new_ios' => 0,
        'new_googlepay' => 0,
    ];
    $coin_rate_radio_scene = [
        'ios' => 0.01,
        'android' => 0.01,
        'paypal' => 0.01,
        'tmallios' => 0.01,
        'googlepay' => 0.01,
        'doudian' => 0.01,
        'vip' => 0.01,
        'new_ios' => 0.01,
        'new_googlepay' => 0.01,
    ];
    $vip_fee_rate = [
        'ios' => 0.314,
        'android' => 0.01,
    ];
}

return [
    'domainMissevan' => $domain,
    'domainMobileWeb' => $domain_mobile_web,
    'dramaDomain' => $domain . '/drama/',
    'standaloneUrl' => $domain . '/standalone/',
    'static_domain' => $static_domain . '/',
    'feedBackKey' => 'iblyKGTAPNxuVhiRimBglPi98zdUy5xFigvLOXiAjOgApCmlXl',
    'mimagesbigUrl' => $static_domain . '/mimages/',
    'avatarUrl' => $static_domain . '/avatars/',
    'profileUrl' => $static_domain . '/profile/',
    'coverUrl' => $static_domain . '/coversmini/',  // 缩略图
    'originalCoverUrl' => $static_domain . '/covers/',  // 原图
    'mimagesUrl' => $static_domain . '/mimagesmini/',
    'topicUrl' => $domain . '/mtopic/',
    'dramaCoverUrl' => $static_domain . '/dramacoversmini/',
    'catalogIconUrl' => $static_domain . '/app/catalog/icon/',
    'userCoverUrl' => $static_domain . '/usercover/',
    'seiyIconUrl' => $static_domain . '/seiys/',
    'service' => $_service,
    'templates' => $templates,

    'defaultAvatarSoundId' => 14299,  // 默认头像音 ID
    'sounds_for_notice' => [
        'not_exist' => 75854,  // 音频不存在
        'transcoding' => 115193,  // 转码中
        'unchecked' => 223692,  // 未过审
        'upgrade_app' => 518008,  // 需要升级 App
        'user_in_blacklist' => 1217690,  // 用户已被拉黑
        'forbidden_in_japan' => 1616367,  // 日本 IP 访问被屏蔽
        'equip_play_num_limit' => 75854,  // 播放设备受限
    ],
    'defaultAvatarSounds' => [14299, 34072, 34197, 35040],  // 默认可选头像音 ID
    'ringtone_catalog_ids' => [76, 79, 48, 50, 109, 110, 66, 67, 68, 73, 71, 101],  // 可设置为铃声的音频分类 ID
    'special_id_number' => [],  // 可用于直接认证通过的身份证号

    'update_links' => [
        'interactive_drama' => 'https://link.example.com/mtopic/5',  // 互动剧版本更新提示页面
        'radio_hypnosis' => 'https://link.example.com/mtopic/6',  // 催眠专享版本更新提示页面
        'mall_youzan' => 'https://link.example.com/mtopic/7',  // 接入有赞商城版本更新提示页面
        'sobot' => 'https://link.example.com/mtopic/8',  // 接入智齿客服版本更新提示页面
        'super_fan' => 'https://link.example.com/mtopic/9',  // 直播间超粉功能更新提示页面
        'theatre' => 'https://link.example.com/mtopic/10',  // 盲盒剧场版本更新提示页面
        'dramalist' => 'https://link.example.com/mtopic/1015',  // 剧单版本更新提示页面
        'vip' => 'https://link.example.com/mtopic/1016',  // 点播会员版本更新提示页面
    ],
    'help_links' => [
        'drama_reward_ranks' => $link_domain . '/help/drama-reward-ranks',  // 打赏榜说明
        'drama_reward_agreement' => $link_domain . '/rule/drama-reward-agreement',  // 《广播剧打赏计划》用户须知
        'trade_agreement' => $link_domain . '/rule/diamond-agreement',
    ],
    'web_links' => [
        'homepage_rank_details' => $domain_mobile_web . '/ranking',  // 首页排行榜跳转链接
    ],

    'defaultAvatarUrl' => $static_domain . '/avatars/icon01.png',
    'defaultRoomCoverUrl' => $static_domain . '/avatars/icon01.png',
    'blacklistUserAvatarUrl' => $static_domain . '/avatars/blacklist.png',  // 评论显示黑名单用户头像
    'defaultCoverUrl' => $static_domain . '/coversmini/nocover.png',
    'defaultUserCoverUrl' => $static_domain . '/usercover/background.png',  // 个人主页默认背景图
    'feedbackIconUrl' => 'oss://avatars/msg-feedback.png',  // 猫耳FM客服娘头像
    'foldMsgIconUrl' => 'oss://avatars/msg-fold.png',  // 折叠后的未关注人私信头像
    'defaultAlbumMiniCoverUrl' => 'oss://coversmini/album-nocover.png',  // 音单默认封面小图地址（音单列表页使用）
    'defaultAlbumCoverUrl' => 'oss://covers/album-nocover.png',  // 音单默认封面大图地址（音单详情页使用）
    'privateAlbumCoverUrl' => 'oss://coversmini/album-private.png',  // 私密音单封面图地址

    'privacy_data' => $static_domain . '/app/privacy.json',
    'privacy_concept_data' => $static_domain . '/app/privacy/privacy-20211209.json',
    'privacy_google_data' => $static_domain . '/app/privacy-google.json',

    'emote' => [
        'no_exclusive_package_url' => 'oss://emote/no_exclusive_emote.zip',  // 无专属表情包地址
        'package_url' => 'oss://emote/emote.zip',  // 表情包地址
    ],

    // 新人 Tab 页配置
    'new_user' => [
        'banner' => [  // 广告模块配置
            'guest' => [  // 游客广告条内容（未配置时不下发该广告条）
                'intro' => '广告条描述内容',  // 广告条描述内容
            ],
            'user' => [  // 登录用户广告条内容（未配置时不下发该广告条）
                'title' => '广告条标题',  // 广告条标题
                'intro' => '广告条描述内容',  // 广告条描述内容
                'btn_title' => '按钮文案',  // 按钮文案
                'btn_url' => 'missevan://sound/1',  // 按钮跳转链接
            ],
        ],
        'luckybag' => [  // 福袋模块配置
            'title' => '好剧免费送',  // 福袋模块名称
            'more_url' => 'https://www.test.com/test',  // "更多"按钮跳转链接
        ]
    ],

    // 新人用户白名单
    // 适用范围：https://info.missevan.com/pages/viewpage.action?pageId=90799125 新人白名单
    'newuser_user_allowlist' => [1234],
    // 充值活动 ID
    'topup_discount_event_id' => 233,
    'ios_topup_guidance' => "<div style='color: #cccccc'><p>1. 购买钻石成功后无法退款，不可提现</p>
<p>2. 使用钻石兑换的任意商品不可退款，由此带来的损失由用户自行承担</p>
<p>3. 若扣款成功（iOS 端）但钻石未到账，点此<a href='https://link.missevan.com/help/topup-ios' target='_blank' style='color: #ff0000'>查看帮助</a></p>
<p>4. 渠道商服务手续费说明：充值过程中，App Store 会按其标准收取充值渠道手续费，具体以实际页面展示为准</p></div>",

    // 鱼干任务页广告条配置
    'point_task_banner' => [
        'tip' => '连签解锁新人专属头像框！了解详情',
        'url' => 'https://www.test.com/blackboard/bm6w9Tih14rVeq0v.html',
    ],

    // 特定渠道包首次启动跳转到对应的活动页
    'channel_open_urls' => [
        'missevan_bilibili_18' => 'https://www.example.com/mevent/1?ct=bilibili_18',
        'missevan_bilibili_19' => 'https://www.example.com/mevent/2?ct=bilibili_19',
        'missevan_bilibili_20' => 'https://www.example.com/mevent/3?ct=bilibili_20',
        'missevan_bilibili_24' => 'https://www.example.com/mevent/4?ct=bilibili_24',
        'missevan_bilibili_test' => 'https://www.example.com/mevent/5?ct=bilibili_test',
    ],
    // 剧集福袋热搜词配置
    'drama_luckybag_hot_word' => [  // 不配置时不下发
        'key' => '热门好剧免费送',  // 热搜词
        'url' => 'missevan://test',  // 跳转地址
        'icon_url' => $static_domain . '/path/icon.png'  // 图标地址
    ],
    'additional_task_older_623' => $task['additional_task_older_623'],
    // 每日任务：限时任务列表
    'additional_task_list' => $task['additional_task_list'],
    'wechat_offiaccount_task' => $task['wechat_offiaccount_task'],
    'live_task_map' => $task['live_task_map'],
    // 会员相关参数配置
    'vip' => [
        'center_url' => 'missevan://vip/center',  // 会员中心页跳转地址
        'claim_diamonds_limit' => [  // 会员每天可领取会员钻石次数限制
            'equip' => 2,  // 设备号限制，当不配置或配置为 0 时为不限制
            'ip' => 10,  // IP 限制，当不配置或配置为 0 时为不限制
        ],
    ],
    // iOS、谷歌新充值档位开启时间
    'ios_googlepay_new_topup_menu_time' => $ios_googlepay_new_topup_menu_time,
    'coin_rate' => [
        'live_scene' => $coin_rate_live_scene,
        'radio_scene' => $coin_rate_radio_scene,
    ],
    'vip_fee_rate' => $vip_fee_rate,
    'bili_app_key' => [
        'test_key' => 'test_secret',
        'abcde12345' => 'xyzabc67890',
    ],
];
