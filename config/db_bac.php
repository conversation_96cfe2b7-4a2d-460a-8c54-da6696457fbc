<?php

return [
    'maindb' => [
        'class' => 'yii\db\Connection',
        'dsn' => 'mysql:host=localhost;dbname=app_missevan',
        'username' => '',
        'password' => '',
        'charset' => 'utf8mb4',
    ],
    'new_maindb' => [
        'class' => 'yii\db\Connection',
        'dsn' => 'mysql:host=localhost;dbname=missevan_main',
        'username' => '',
        'password' => '',
        'charset' => 'utf8mb4',
    ],
    'voicedb' => [
        'class' => 'yii\db\Connection',
        'dsn' => 'mysql:host=localhost;dbname=app_missevan_voice',
        'username' => '',
        'password' => '',
        'charset' => 'utf8mb4',
    ],
    'logdb' => [
        'class' => 'yii\db\Connection',
        'dsn' => 'mysql:host=localhost;dbname=app_missevan_log',
        'username' => '',
        'password' => '',
        'charset' => 'utf8mb4',
        'attributes' => [
            PDO::ATTR_TIMEOUT => 10,
        ],
    ],
    'malldb' => [
        'class' => 'yii\db\Connection',
        'dsn' => 'mysql:host=localhost;dbname=app_missevan_mall',
        'username' => '',
        'password' => '',
        'charset' => 'utf8mb4',
    ],
    'messagedb' => [
        'class' => 'yii\db\Connection',
        'dsn' => 'mysql:host=localhost;dbname=missevan_message',
        'username' => '',
        'password' => '',
        'charset' => 'utf8mb4',
    ],
    'readonly_messagedb' => [
        'class' => 'yii\db\Connection',
        'dsn' => 'mysql:host=localhost;dbname=missevan_message',
        'username' => '',
        'password' => '',
        'charset' => 'utf8mb4',
        'serverStatusCache' => 'cache',  // DB 健康状态存储的缓存组件
        'serverRetryInterval' => 600,  // 此时间间隔内不再请求挂掉了的从库
        // 从库的通用配置
        'slaveConfig' => [
            'attributes' => [
                PDO::ATTR_TIMEOUT => 10,  // 超出此时间则认为从库实例挂掉
            ],
        ],
        // 从库的配置列表
        'slaves' => [
            [
                'dsn' => 'mysql:host=**************;port=3307;dbname=missevan_message',
                'username' => 'root',
                'password' => '123456',
            ],
            [
                'dsn' => 'mysql:host=**************;port=3308;dbname=missevan_message',
                'username' => 'root',
                'password' => '123456',
            ],
            [
                'dsn' => 'mysql:host=**************;port=3309;dbname=missevan_message',
                'username' => 'root',
                'password' => '123456',
            ],
        ],
    ],
    'growthdb' => [
        'class' => 'yii\db\Connection',
        'dsn' => 'mysql:host=localhost;dbname=missevan_growth',
        'username' => '',
        'password' => '',
        'charset' => 'utf8mb4',
    ],
    'paydb' => [
        'class' => 'yii\db\Connection',
        'dsn' => 'mysql:host=localhost;dbname=missevan_pay',
        'username' => '',
        'password' => '',
        'charset' => 'utf8mb4',
    ],
    'redis' => [
        'class' => 'app\components\db\RedisConnection',
        'hostname' => '',
        'port' => 6379,
        // 'password' => '',
        'connectionTimeout' => 180
    ],
    'redis_sound_view' => [
        'class' => 'app\components\db\RedisConnection',
        'hostname' => '',
        'port' => 6379,
        // 'password' => '',
        'connectionTimeout' => 180,
        'database' => 4,
    ],
    'redis_lru' => [
        'class' => 'app\components\cache\RedisCache',
        'redis' => [
            'hostname' => '',
            'port' => 6379,
            // 'password' => '',
            'connectionTimeout' => 180,
        ],
        'keyPrefix' => 'test_',
    ],
    'databus' => [
        'class' => 'missevan\util\DataBus',
        'host' => '',
        'port' => 6205,
        'key' => '',
        'secret' => '',
        'group' => '',
        'topic' => '',
        'max_retry' => 3,
        'retry_interval_ms' => 50,
    ],
    'pay_databus' => [
        'class' => 'missevan\util\DataBus',
        'host' => '',
        'port' => 6205,
        'key' => '',
        'secret' => '',
        'group' => '',
        'topic' => '',
        'max_retry' => 3,
        'retry_interval_ms' => 50,
    ],
];
