<?php

namespace app\middlewares;

use Yii;
use yii\base\InlineAction;
use yii\filters\ContentNegotiator;
use app\middlewares\filters\ErrorAction;
use app\middlewares\filters\UserFilter;
use yii\web\BadRequestHttpException;
use yii\web\Response;

// 配置 apiDoc 全局 @apiDefine 项
/**
 * @apiDefine staff 仅办公人员可用
 * 该接口仅绑定 OA 的办公人员可访问（需要拥有相关权限）
 */

/**
 * @apiDefine user 仅登录用户可用
 * 该接口仅登录用户可访问
 */

/**
 * @apiDefine none 不限制访问用户
 */

/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/12
 * Time: 10:22
 */
class Controller extends \yii\web\Controller
{
    // 请求行为类型，0：取消某种行为，1：进行某种行为（如取消点赞和点赞）
    const ACTION_TYPE_CANCEL = 0;
    const ACTION_TYPE_ADD = 1;

    // 全局弹窗提示错误 code
    const PROMPT_MODAL_CODE = 100010018;
    // 弹出的框有联系客服按钮的 code
    const PROMPT_FEEDBACK_MODAL_CODE = 100010021;

    public $enableCsrfValidation = false;

    public function behaviors()
    {
        return [
            [
                'class' => ContentNegotiator::class,
                'except' => ['captcha'],
                'formats' => [
                    'text/html' => Response::FORMAT_HTML,
                    'application/xml' => Response::FORMAT_XML,
                    'application/json' => Response::FORMAT_JSON,
                    'application/javascript' => Response::FORMAT_JSONP,
                ]
            ],
            [
                'class' => UserFilter::class,
            ]
        ];
    }

    public function actions()
    {
        return [
            'error' => [
                'class' => ErrorAction::class,
            ],
            'captcha' => [
                'class' => 'yii\captcha\CaptchaAction',
                'fixedVerifyCode' => YII_ENV_TEST ? 'testme' : null,
            ],
        ];
    }

    /**
     * 继承自父类（web/Controller）
     * 改动：
     *  - 增加「提前转换类型」（防止类型约束导致的报错）
     *
     * @param \yii\base\Action $action
     * @param array $params
     * @return array
     * @throws BadRequestHttpException
     * @throws \ReflectionException
     */
    public function bindActionParams($action, $params)
    {
        if ($action instanceof InlineAction) {
            $method = new \ReflectionMethod($this, $action->actionMethod);
        } else {
            $method = new \ReflectionMethod($action, 'run');
        }

        $args = [];
        $missing = [];
        $actionParams = [];
        foreach ($method->getParameters() as $param) {
            $name = $param->getName();
            if (array_key_exists($name, $params)) {
                // 格式化参数
                if ($param->isArray()) {
                    $args[] = $actionParams[$name] = (array)$params[$name];
                } elseif (!is_array($params[$name])) {
                    // 提前转换类型
                    if ($param->getType()) {
                        switch ($param->getType()->getName()) {
                            case 'int':
                                $params[$name] = (int)$params[$name];
                                break;
                            case 'float':
                                $params[$name] = (float)$params[$name];
                                break;
                            case 'bool':
                                $params[$name] = (bool)$params[$name];
                                break;
                            default:
                                break;
                        }
                    }
                    $args[] = $actionParams[$name] = $params[$name];
                } else {
                    throw new BadRequestHttpException(Yii::t('yii', 'Invalid data received for parameter "{param}".', [
                        'param' => $name,
                    ]));
                }
                unset($params[$name]);
            } elseif ($param->isDefaultValueAvailable()) {
                $args[] = $actionParams[$name] = $param->getDefaultValue();
            } else {
                $missing[] = $name;
            }
        }

        if (!empty($missing)) {
            throw new BadRequestHttpException(Yii::t('yii', 'Missing required parameters: {params}', [
                'params' => implode(', ', $missing),
            ]));
        }

        $this->actionParams = $actionParams;

        return $args;
    }
}
