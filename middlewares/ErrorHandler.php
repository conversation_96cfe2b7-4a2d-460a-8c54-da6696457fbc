<?php

namespace app\middlewares;

use Yii;
use yii\web\NotFoundHttpException;
use yii\web\Response;

class <PERSON>rrorHandler extends \yii\web\ErrorHandler
{

    protected function renderException($exception)
    {
        if (Yii::$app->has('response')) {
            $response = Yii::$app->getResponse();
            // reset parameters of response to avoid interference with partially created response data
            // in case the error occurred while sending the response.
            $response->isSent = false;
            $response->stream = null;
            $response->data = null;
            $response->content = null;
        } else {
            $response = new Response();
        }

        if ($exception instanceof NotFoundHttpException) {
            // WORKAROUND: 当接口不存在时，返回一个 JSON 响应报错，避免直接抛出异常导致 XSS 攻击风险
            // https://www.tapd.cn/35612194/bugtrace/bugs/view/1135612194002205009
            $response->format = Response::FORMAT_JSON;
            $response->data = [
                'code' => 100010007,
                'message' => $exception->getMessage(),
                'data' => null,
            ];
        } else {
            $response->data = $exception;
        }
        $response->setStatusCodeByException($exception);
        $response->send();
    }

}
