<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/12
 * Time: 10:57
 */

namespace app\middlewares\filters;

use Yii;
use yii\base\Action;
use yii\base\UserException;
use yii\base\Exception;
use yii\web\HttpException;
use yii\web\NotFoundHttpException;

class ErrorAction extends Action
{
    public $defaultName;

    public $defaultMessage;

    protected $exception;

    public function init()
    {
        UserFilter::$no_error = false;
        $this->exception = $this->findException();

        if ($this->defaultMessage === null) {
            $this->defaultMessage = Yii::t('yii', 'An internal server error occurred.');
        }

        if ($this->defaultName === null) {
            $this->defaultName = Yii::t('yii', 'Error');
        }
    }

    public function run()
    {
        Yii::$app->getResponse()->setStatusCodeByException($this->exception);

        return $this->getExceptionMessage();
    }

    protected function findException()
    {
        if (($exception = Yii::$app->getErrorHandler()->exception) === null) {
            $exception = new NotFoundHttpException(Yii::t('yii', 'Page not found.'));
        }

        return $exception;
    }

    protected function getExceptionCode()
    {
        if ($this->exception instanceof HttpException) {
            return $this->exception->statusCode;
        }

        return $this->exception->getCode();
    }

    protected function getExceptionName()
    {
        if ($this->exception instanceof Exception) {
            $name = $this->exception->getName();
        } else {
            $name = $this->defaultName;
        }

        if ($code = $this->getExceptionCode()) {
            $name .= " (#$code)";
        }

        return $name;
    }

    protected function getExceptionMessage()
    {
        if ($this->exception instanceof UserException) {
            return $this->exception->getMessage();
        }

        return $this->defaultMessage;
    }
}
