<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/13
 * Time: 16:42
 */

namespace app\middlewares\filters;

use app\components\util\Captcha;
use app\components\util\HttpExceptionWithData;
use app\components\util\MUtils;
use app\components\util\SuccessResponseWithMessage;
use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\base\ActionFilter;
use app\components\util\ApiTest;
use app\components\util\Equipment;
use yii\web\HttpException;
use yii\web\Response;

class UserFilter extends ActionFilter
{
    private $_startTime;
    // 全版本验签接口。接口数量超过 6 个，可换为键值对结构，更高效
    private $exception_uri = [
        'sound/add-play-times'
    ];
    public static $no_error = true;
    public static $once = true;

    const RESPONSE_VERSION_V1 = 'v1';
    const RESPONSE_VERSION_V2 = 'v2';

    public $responseVersion = self::RESPONSE_VERSION_V1;

    public function beforeAction($action)
    {
        if (parent::beforeAction($action)) {
            if (self::$once) {
                self::$once = false;
                if (YII_DEBUG) {
                    $this->setXResponseTime();
                }
                if ($this->responseVersion === self::RESPONSE_VERSION_V2) {
                    $this->handleResponseV2();
                } else {
                    $this->handleResponse();
                }
            }
            try {
                if (!YII_DEBUG && !Yii::$app->equip->isFromApp() && self::$no_error) {
                    self::$no_error = false;
                    throw new HttpException(400, '非法请求，代码:00000003', 100010004);
                }
                if (!ENABLE_LOAD_TEST || !MUtils::isLoadTest()) {
                    // 未开启压测或不为压测请求时，需要进行合法性验证
                    // 验证请求签名
                    $this->validateRequestSign();
                }
            } catch (\Exception $e) {
                Yii::$app->response->data = $e;
                Yii::$app->response->setStatusCodeByException($e)->send();
                Yii::$app->end();
            }
            return true;
        }
        return false;
    }

    private function setXResponseTime()
    {
        $this->_startTime = microtime(true);
        $response = Yii::$app->response;
        $response->on($response::EVENT_BEFORE_SEND, function($event){
            $time = microtime(true) - $this->_startTime;
            $headers = Yii::$app->response->headers;
            $headers->add('X-Response-Time', $time . 's');
        });
    }

    private function handleResponse()
    {
        $response = Yii::$app->response;
        $response->on($response::EVENT_BEFORE_SEND, function($event){
            $response = $event->sender;
            $format = $response->format;

            if (302 === $response->statusCode
                || 301 === $response->statusCode) return;

            if ('string' !== gettype($response->data)
                && !('json' === $format || 'xml' === $format)) {
                $response->format = 'xml';
                $format = 'xml';
            }

            if ('json' === $format || 'xml' === $format) {
                $data = $response->data;
                $code = 0;
                if (200 !== $response->statusCode) {
                    if ($response->data instanceof HttpExceptionWithData) {
                        $code = $response->data->getCode();
                        $data = $response->data->getData();
                    } elseif ($response->data instanceof Exception) {
                        $code = $response->data->getCode();
                        $data = $response->data->getMessage();
                    }
                    if (!$code || !is_int($code)) {
                        $code = ((400 === $response->statusCode) ? 201010002 : 100010007);
                    }
                }
                if ($this->needTestApi(Yii::$app->request->pathInfo)) {
                    $data = ApiTest::generateArbitraryData(Yii::$app->request->pathInfo, $data);
                }
                $response->data = [
                    'success' => $response->isSuccessful,
                    'code' => $code,
                    'info' => $data,
                ];
            }
        });
    }

    private function handleResponseV2()
    {
        $response = Yii::$app->response;
        $response->on($response::EVENT_BEFORE_SEND, function ($event) {
            /**
             * @var yii\web\Response $response
             */
            $response = $event->sender;

            if (302 === $response->statusCode || 301 === $response->statusCode) {
                return;
            }
            if (!in_array($response->format, [Response::FORMAT_JSON, Response::FORMAT_XML])) {
                $response->format = Response::FORMAT_XML;
            }
            if (!in_array($response->format, [Response::FORMAT_JSON, Response::FORMAT_XML])) {
                return;
            }

            $response_data = $this->getFormatDataFromResponse($response);
            if ($this->needTestApi(Yii::$app->request->pathInfo)) {
                $response_data['data'] = ApiTest::generateArbitraryData(Yii::$app->request->pathInfo, $data);
            }

            $response->data = $response_data;
        });
    }

    private function getFormatDataFromResponse(Response $response)
    {
        $code = 0;
        $message = '';
        $data = null;

        if ($response->getIsOk()) {
            if ($response->data instanceof SuccessResponseWithMessage) {
                $message = $response->data->getMessage();
                $data = $response->data->getData();
            } else {
                $data = $response->data;
            }
            return [
                'code' => $code,
                'message' => $message,
                'data' => $data,
            ];
        }

        if ($response->data instanceof Exception) {
            $code = $response->data->getCode();
            $message = $response->data->getMessage();

            if ($response->data instanceof HttpExceptionWithData) {
                $data = $response->data->getData();
            }
        }

        if (!$code || !is_int($code)) {
            $code = ((400 === $response->statusCode) ? 201010002 : 100010007);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * 是否需要测试 API
     * 此方法在生产环境应永远返回 false
     *
     * @param string $api_info API 路由
     * @return bool 是否需要测试
     */
    private function needTestApi(string $api_info): bool
    {
        if (!YII_ENV_DEV || !defined('YII_API_TEST') || !YII_API_TEST) return false;
        if (defined('YII_API_TEST_PR') && rand(0, 99) >= YII_API_TEST_PR) return false;
        $path_info = strtolower($api_info);
        $match = false;
        foreach (YII_API_TEST as $pattern) {
            if (fnmatch($pattern, $path_info)) {
                $match = true;
                break;
            }
        }
        return $match;
    }

    /**
     * 对请求进行签名验证
     *
     * @throws HttpException 若验证不通过，则抛出异常
     */
    private function validateRequestSign()
    {
        $path_info = strtolower(Yii::$app->request->pathInfo);
        if (((defined('ENABLE_API_VALIDATE') && ENABLE_API_VALIDATE)
                    || (YII_ENV === 'test' && Yii::$app->request->headers['X-Validate-Sign']))
                && (Equipment::Web !== Yii::$app->equip->getOs() && (!Equipment::isAppOlderThan('4.2.9', '5.1.9')
                    || in_array($path_info, $this->exception_uri))
                )) {
            // WORKAROUND: 对 iOS 4.2.9 及以后的版本、Android 5.1.9 及以后的版本进行验证签名的操作
            $key = APP_API_SIGN_KEY;
            $validate_urls = [
                'sound/add-play-log',
                'discovery/collect-clicks',
            ];
            if (in_array($path_info, $validate_urls)) {
                // 播放日志接口使用特定的 key
                $cache_key_name = MUtils::generateCacheKey(KEY_REQUEST_SIGN_UUID,
                    Yii::$app->equip->getEquipId());
                $key = MUtils::getSignKeyByRedis($cache_key_name);
            }
            if ($path_info === 'sound/sound') {
                // WORKAROUND: Android 5.7.6 之前的版本，重定向后请求提示音频接口时验签存在问题，此时不进行验签
                $sound_id = (int)Yii::$app->request->get('sound_id');
                if (in_array($sound_id, Yii::$app->params['sounds_for_notice']) && Equipment::isAppOlderThan(null, '5.7.6')) {
                    return;
                }
            }
            if (!MUtils::validateSign($key)) {
                $message = '非法请求';
                if (!MUtils2::isProdEnv()) {
                    $message .= '，strToSign is: ' . MUtils::getApiSignStr();
                }
                throw new HttpException(401, $message);
            }
        }
    }

}
