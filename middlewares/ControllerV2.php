<?php

namespace app\middlewares;

use app\middlewares\filters\UserFilter;
use yii\filters\ContentNegotiator;
use yii\web\Response;

class ControllerV2 extends \yii\web\Controller
{
    public $enableCsrfValidation = false;

    public function behaviors()
    {
        return [
            [
                'class' => ContentNegotiator::class,
                'formats' => [
                    'text/html' => Response::FORMAT_HTML,
                    'application/xml' => Response::FORMAT_XML,
                    'application/json' => Response::FORMAT_JSON,
                    'application/javascript' => Response::FORMAT_JSONP,
                ]
            ],
            [
                'class' => UserFilter::class,
                'responseVersion' => UserFilter::RESPONSE_VERSION_V2,
            ],
        ];
    }

}
