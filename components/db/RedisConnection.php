<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/5/3
 * Time: 17:09
 */

namespace app\components\db;

use Exception;
use Redis;
use RedisException;
use Yii;
use yii\base\Configurable;
use yii\base\InvalidArgumentException;

class RedisConnection extends Redis implements Configurable
{
    public $hostname = 'localhost';
    public $port = 6379;
    public $unixSocket;
    public $password;
    public $database = 0;
    public $connectionTimeout = 0.0;
    public $lockPrefix = 'lock_';

    private $_db = 0;

    public static function className()
    {
        return get_called_class();
    }

    public function __construct(array $config = [])
    {
        if ($config) {
            Yii::configure($this, $config);
        }
        $this->init();
    }

    public function init()
    {
        $this->_connect();
    }

    private function _connect()
    {
        try {
            if ($this->unixSocket !== null) {
                $is_connected = $this->connect($this->unixSocket);
            } else {
                if ($this->password !== null && $this->password !== '') {
                    // WORKAROUND: 修复 TCP 连接被杀掉后，复用原链接出现的 "NOAUTH Authentication required" 问题
                    // https://github.com/phpredis/phpredis/issues/1757
                    $is_connected = $this->pconnect($this->hostname, $this->port, $this->connectionTimeout,
                        NULL, 0, 0, ['auth' => [$this->password]]);
                } else {
                    $is_connected = $this->pconnect($this->hostname, $this->port, $this->connectionTimeout);
                }
            }
            if ($is_connected === false) {
                throw new Exception('Redis connection error: ' . $this->getLastError());
            }

            if (!$this->ping()) {
                throw new Exception('Redis ping error: ' . $this->getLastError());
            }

            if ($this->database !== null && !$this->select($this->database)) {
                throw new Exception('Redis switch db error: ' . $this->getLastError());
            }
        } catch (Exception $e) {
            Yii::error($e->getMessage(), __METHOD__);
            // 若原 TCP 连接已被杀掉，pconnect 复用原连接时将报错 "NOAUTH Authentication required"
            // 需要 close 掉后重新建立连接
            $this->close();
            throw new Exception(Yii::t('app/error', 'The server is busy, please try again later'));
        }
    }

    /**
     * 生成某类型的 Key
     *
     * @param string $pattern key 的模式
     * @param mixed ...$args 模式中替代成数据
     * @return string
     *
     * generateKey('test:*', 'arg') 会返回 test:arg
     *
     */
    public function generateKey($pattern, ...$args)
    {
        $str_count = substr_count($pattern, '*');
        $arg_count = count($args);
        if ($str_count !== $arg_count) {
            throw new InvalidArgumentException("pattern expects $str_count arguments $arg_count given");
        }
        $pattern = str_replace('*', '%s', $pattern);
        $key = sprintf($pattern, ...$args);
        return $key;
    }

    /**
     * 返回某个 key 模式下所有的 key
     *
     * @param string $pattern key 的模式
     * @param int $limit limit 为正整数。返回的最大数量（需要注意的是，最大返回数量可能略大于1000）
     * @param null $cursor 游标，此变量为引用变量。
     * @return array 返回的key
     */
    public function getPatternKeys(string $pattern, int $limit = 200, &$cursor = Null): array
    {
        $keys = [];

        if ($limit < 0) throw new InvalidArgumentException('limit experts bigger than zero');
        $limit = ($limit && $limit > 1000) ? 1000 : $limit;

        do {
            $sig_keys = $this->scan($cursor, $pattern, 200);
            array_push($keys, ...$sig_keys);
            if ($limit <= count($keys)) break;
        } while ($cursor);
        return array_unique($keys);
    }

    /**
     * 删除某个 key 模式下所有的 key
     *
     * @param string $pattern key 的模式
     * @return int 删除key的数量
     */
    public function delPatternKeys(string $pattern): int
    {
        $cursor = Null;
        $count = 0;
        do {
            $keys = $this->scan($cursor, $pattern, 1000);
            $count += $this->del($keys);
        } while($cursor);
        return $count;
    }

    /**
     * 设置分布式锁，有至多1秒的延时
     *
     * @param string $lock_key 锁名
     * @param int $timeout 超时时间
     * @return bool 上锁是否成功
     */
    public function lock($lock_key, int $timeout)
    {
        return $this->set($this->lockPrefix . $lock_key, 1, ['nx', 'ex' => $timeout]);
    }

    /**
     * 设置分布式锁
     *
     * @param string $lock_key 锁名
     * @param int $timestamp 锁过期时间
     * @return bool 上锁是否成功
     */
    public function lockAt($lock_key, int $timestamp)
    {
        $now = time();
        if (($timeout = $timestamp - $now) > 0) {
            return $this->lock($lock_key, $timeout);
        }
        return false;
    }

    /**
     * 分布式锁解锁
     *
     * @param string $lock_key 锁名
     * @return bool 是否解锁成功
     */
    public function unlock($lock_key)
    {
        if ($this->del($this->lockPrefix . $lock_key)) {
            return true;
        }
        return false;
    }

    /**
     * 获取带有锁前缀的 key
     *
     * @param string $pattern key 的模式
     * @param array ...$args 模式中替代成数据
     * @return string 带有锁前缀的 key
     */
    public function getLockKey($pattern, ...$args)
    {
        $key = $this->generateKey($pattern, ...$args);
        return $this->lockPrefix . $key;
    }

    /**
     * 计数器
     *
     * @param $key 计数器的键
     * @param int $timeout 过期时间，0为不设置过期时间
     * @param int $max_count 最大次数，0为不设置最大次数
     * @return bool|int|mixed 超过最大次数返回 false, 其他则返回计数次数
     */
    public function counter($key, $timeout = 0, $max_count = 0)
    {
        $count = $this->incr($key);
        if ($timeout && $count == 1) {
            $this->expire($key, $timeout);
        }
        if ($max_count && $count > $max_count) {
            return false;
        }
        return $count;
        /*
        if (0 !== $timeout) {
            $script = <<<'EOT'
local current
current = redis.call("incr", KEYS[1])
if tonumber(current) == 1 then
    redis.call("expire", KEYS[1], KEYS[2])
end
return current
EOT;
            $count = $this->eval($script, [$key, $timeout], 2);
        } else {
            $count = $this->incr($key);
        }
        if ($max_count && $count > $max_count) {
            return false;
        } else {
            return $count;
        }
        */
    }

    /**
     * 获取某个模式下永久key
     *
     * @param string $pattern key 的模式，当为 null 时匹配所有key
     * @param int $limit 默认值 200
     * @param &int $cursor 游标，引用类型
     * @return array
     */
    public function getPermanentKeys($pattern = Null, int $limit = 200, &$cursor = Null)
    {
        $array = [];

        if ($limit < 0) throw new InvalidArgumentException('limit experts bigger than zero');
        $limit = ($limit && $limit > 1000) ? 1000 : $limit;

        do {
            $keys = $this->scan($cursor, $pattern, 200);
            foreach ($keys as $key) {
                if (-1 === $this->ttl($key)) {
                    array_push($array, $key);
                }
            }
            if ($limit <= count($keys)) break;
        } while($cursor);
        return array_unique($array);
    }

    /**
     * 获取某个模式下的闲置key
     *
     * @param null $pattern key 的模式，当为 null 时匹配所有key
     * @param int $limit 默认值 200
     * @param int $time 默认值 1天
     * @param $cursor 游标，引用类型
     * @return array
     */
    public function getIdleKeys($pattern = Null, int $limit = 200, &$cursor, $time = ONE_DAY)
    {
        $idle_keys = [];

        if ($time < 0) throw new InvalidArgumentException('time experts bigger than zero');
        if ($limit < 0) throw new InvalidArgumentException('limit experts bigger than zero');
        $limit = ($limit && $limit > 1000) ? 1000 : $limit;

        do {
            $keys = $this->scan($cursor, $pattern, 200);
            $part_keys = array_filter($keys, function($key) use($time) {
                return $this->object('idletime', $key) > $time;
            });
            $idle_keys = array_merge($idle_keys, $part_keys);
            if ($limit <= count($idle_keys)) break;
        } while($cursor);
        return $idle_keys;
    }

    /**
     * 刪除並获取 hash 对象
     *
     * @param $key  hash 对象的 key
     * @param int $timeout 获取超时时间, 0为不超时，默认值为5秒
     * @return array 返回 hash 对象
     * @throws RedisException 超时抛出异常
     */
    public function hGetDel($key, $timeout = 5)
    {
        $type = $this->type($key);
        if (self::REDIS_HASH !== $type) {
            throw new InvalidArgumentException('key must be a HASH KEY');
        }
        $count = 0;
        $time = 1;
        $stime = time();
        $this->watch($key);
        while(true) {
            $ret = $this->multi()
                ->hGetAll($key)
                ->del($key)
                ->exec();
            if (false !== $ret) break;
            if ($timeout && (time() - $stime) > $timeout) throw new RedisException('Get Hash Key TIMEOUT');
            // 尝试多次后失败后，会延时尝试。
            if (!(++$count % 3)) {
                sleep($time);
                $time *= 2;
            }
        }
        return $ret[0];
    }

    /**
     * 选择 database
     *
     * @param int $db_index database 的序列号
     * @return bool
     */
    public function select($db_index): bool|Redis
    {
        $is_success = parent::select($db_index);
        if ($is_success) {
            $this->_db = $db_index;
        }
        return $is_success;
    }

    /**
     * @return int 返回database的序列号
     */
    public function getDb()
    {
        return $this->_db;
    }

    /**
     * 兼容处理 ZRANGE/ZREVRANGE 的 WITHSCORES 参数
     *
     * @param bool|array $withscores 例：true 或 ['withscores' => true]
     * @return bool
     * @throws Exception
     */
    private function getWithscoresArg($withscores)
    {
        // https://pecl.php.net/package-changelog.php?package=redis
        // php-redis 5 版本支持布尔类型或 map 类型，例 ['withscores' => true]
        // php-redis 4、php-redis 3 版本参数为布尔类型
        if (is_bool($withscores)) {
            $arg = $withscores;
        } elseif (is_array($withscores)) {
            $arg = ($withscores['withscores'] ?? false) === true;
        } else {
            throw new Exception(Yii::t('app/error', 'params error'));
        }
        return $arg;
    }

    /**
     * @inheritdoc
     */
    public function zRange($key, $start, $end, $withscores = false): array|Redis
    {
        $arg = $this->getWithscoresArg($withscores);
        return parent::zRange($key, $start, $end, $arg);
    }

    /**
     * @inheritdoc
     */
    public function zRevRange($key, $start, $end, $withscores = false): array|Redis
    {
        $arg = $this->getWithscoresArg($withscores);
        return parent::zRevRange($key, $start, $end, $arg);
    }

    /**
     * 设置 Redis 键值对，若已存在对应的 key 则会进行覆盖
     *
     * @param string $key 键
     * @param mixed $value 值
     * @param int $duration 过期时间长度（单位为秒），若为 0 表示永不过期
     * @return bool
     */
    public function setValue($key, $value, $duration)
    {
        if ($duration < 0) {
            throw new InvalidArgumentException('duration must be greater than or equal to zero');
        }
        if ($duration === 0) {
            return $this->set($key, $value);
        }
        return $this->set($key, $value, $duration);
    }

    /**
     * 添加 Redis 键值对，若已存在对应的 key 则不进行任何操作
     *
     * @param string $key 键
     * @param mixed $value 值
     * @param int $duration 过期时间长度（单位为秒），若为 0 表示永不过期
     * @return bool
     */
    public function addValue($key, $value, $duration)
    {
        if ($duration < 0) {
            throw new InvalidArgumentException('duration must be greater than or equal to zero');
        }
        if ($duration === 0) {
            return $this->setnx($key, $value);
        }
        return $this->set($key, $value, ['nx', 'ex' => $duration]);
    }

}
