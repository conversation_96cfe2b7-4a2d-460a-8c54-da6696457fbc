<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/18
 * Time: 16:00
 */

namespace app\components\controllers;

// 首页接口
interface SiteInterface
{
    // ios 审核通过
    public function actionAfterPassed();

    // 启动接口 是否升级，启动音，启动图
    public function actionLaunch();

    // 获取闹铃分类
    public function actionRingingCatalog();

    // 获取闹铃单音
    public function actionRingingSound();

    // 顶部编辑推荐部分，包括自定义图标
    public function actionEditorChoice();

    // 首页下方分类
    public function actionCatalogs();

    //换一批功能
    public function actionCatalogChange(int $cid, $page = 1);

    //分类根信息
    public function actionCatalogRoot();

    //根据id获取分类数据
    public function actionCatalogHomepage(int $cid, int $tag_id = 0, int $limit = 6, int $dark = 0);

    // 获取 GET 或设置 POST 首页自定义栏目及所有栏目
    public function actionCustomCatalog();
}