<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/18
 * Time: 17:27
 */

namespace app\components\controllers;


interface PersonInterface
{
    // 分享
    public function actionShare();

    // 意见反馈
    public function actionFeedBack();

    // 日常任务，签到任务
    public function actionTask();

    //根据传入的参数（默认$feed_type=0用户及频道；1为剧集），分类型获取获取用户动态
    public function actionUserFeed(int $feed_type, int $page_size, int $sound_id);

    //获取用户热门单音
    public function actionUserHotSound(int $user_id);

    // 收藏用户热门单音到音单
    public function actionCollectUserHotSound(int $user_id);

    //版头图
    public function actionBanner();
}
