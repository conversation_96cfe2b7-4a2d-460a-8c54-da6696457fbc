<?php
/**
 * Created by PhpStorm.
 * User: InVinCiblezz
 * Date: 2017/7/18
 * Time: 17:27
 */

namespace app\components\controllers;


interface SoundInterface
{
    // 获取弹幕
    public function actionGetDm(int $sound_id);

    // 根据id获取单音
    public function actionSound(int $sound_id);

    // 根据id收藏单音
    public function actionCollect(int $sound_id, int $album_id);

    // 根据单音id获取推荐单音
    public function actionRecommend(int $sound_id);

    // 根据单音id点赞单音
    public function actionLike(int $sound_id);

    // 根据标签/频道获取单音
    public function actionSoundByTag(int $tag_id, int $page_size = 10, int $order = 0);

    // 小鱼干投食
    public function actionTs(int $sound_id);
}