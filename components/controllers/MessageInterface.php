<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/18
 * Time: 17:28
 */

namespace app\components\controllers;


// 评论, 弹幕，私信接口
interface MessageInterface
{
    // 获取未读消息（评论，系统消息，点赞消息，私信）
    public function actionUnreadNotice();

    //私信列表及未读数目
    public function actionMessageList();

    //私信详情
    public function actionMessageDetail(int $used_id, int $page_size = 30);

    //关闭私信聊天
    public function actionRemoveRoom();

    // 黑名单列表
    public function actionGetBlacklist();

    // 发送私信
    public function actionSendMessage();

    // 添加评论
    public function actionAdd();

    // 添加子评论
    public function actionAddSub();

    //删除评论
    public function actionDel(int $comment_id, int $sub = 0);

    // 添加弹幕
    public function actionAddDm();

    // 评论点赞
    public function actionLike(int $sound_id);

    //收到的赞
    public function actionGetLikes();
}
