<?php
/**
 * Created by PhpStorm.
 * User: InVinCiblezz
 * Date: 2017/7/18
 * Time: 17:27
 */

namespace app\components\controllers;


interface CollectionInterface
{
    // 频道列表
    public function actionChannelList();

	// 频道管理员
    public function actionChannelAdmin(int $channel_id);
    
    // 频道订阅
    public function actionSubscribeChannel();

	// 根据标签获取音单
    public function actionAlbumByTag();

    // 获取音单的标签
    public function actionAlbumTags();

    // 根据id获取音单
    public function actionAlbum(int $album_id);

    // 收藏音单
    public function actionAlbumCollect();
    
    // 创建音单
    public function actionAlbumCreate();

    // 更新音单
    public function actionAlbumUpdate();

    // 删除音单
    public function actionAlbumDelete();

    // 批量收藏移除
    public function actionSoundCollect();
}
