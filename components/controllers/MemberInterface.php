<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/18
 * Time: 15:54
 */

namespace app\components\controllers;

// 用户登录及用户个人信息操作
interface MemberInterface
{
    // 注册
    public function actionRegist();

    // 手机邮箱登陆
    public function actionLogin();

    // 退出登陆
    public function actionLogout();



    // 获取用户信息
    public function actionInfo();

    // 第三方登陆
    public function actionThirdAuth();

    // 绑定手机
    public function actionBindMobile();

    // 绑定邮箱
    public function actionBindEmail();

    // 第三方绑定
    public function actionThirdBind();


    // 修改用户性别
    public function actionSetSex();

    // 修改用户生日
    public function actionSetBirthday();

    // 重置用户密码
    public function actionResetPassword();


    //发送验证码
    public function actionSendVCode();
}