<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/18
 * Time: 16:37
 */

namespace app\components\controllers;

// 活动和专题相关操作
interface EventInterface
{
    //活动列表
    public function actionList();

    //活动详情
    public function actionDetail(int $event_id, int $version = 0);

    //活动作品
    public function actionWorks(int $event_id, int $order = 0, int $page_size = 20);

    //查询音是否参加活动
    public function actionInWork(int $id);

    //活动投票
    public function actionVote(int $event_id, int $sound_id);

    //获取配音秀素材
    public function actionDub(int $eid);
}