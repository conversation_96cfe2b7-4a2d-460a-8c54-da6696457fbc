<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/10
 * Time: 18:59
 */

namespace app\components\tree;

use app\components\util\Equipment;
use app\components\util\MUtils;
use app\models\Catalog;
use Yii;
use yii\web\HttpException;

class Catalogs extends TreeBase
{
    const SOUND_CATALOG_ID = 1;

    /**
     * Catalogs constructor.
     *
     * @param int $root_id 根分类 ID
     * @param null|int $sort_flags 排序方式（按 sort 字段）
     * @throws HttpException
     */
    public function __construct($root_id = self::SOUND_CATALOG_ID, $sort_flags = null)
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }
        if ($sort_flags && !in_array($sort_flags, [SORT_ASC, SORT_DESC])) {
            throw new \Exception('排序参数错误');
        }
        // 初始数据
        $query = Catalog::find()
            ->select('id, catalog_name name, parent_id, sort_order sort')
            ->where('status_is = 1');
        if ($sort_flags) {
            $query = $query->orderBy(['sort' => $sort_flags]);
        }
        $nodes = $query->asArray()->all();
        $is_chinamainland = MUtils::isChinaMainland();
        $nodes = array_map(function ($item) use ($is_chinamainland) {
            // 非中国大陆地区不显示日抓分类、有声日漫分区
            if (in_array($item['id'], [Catalog::CATALOG_ID_JAPAN_AUDIO_COMICS, Catalog::CATALOG_ID_JAPAN_DRAMA])
                    && !$is_chinamainland) {
                return null;
            }
            $item['id'] = (int)$item['id'];
            $item['parent_id'] = (int)$item['parent_id'];
            $item['sort'] = (int)$item['sort'];
            return $item;
        }, $nodes);
        $nodes = array_values(array_filter($nodes));
        $this->generateTree($nodes, $root_id);
    }
}
