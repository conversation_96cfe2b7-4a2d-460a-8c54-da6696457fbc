<?php

namespace app\components\base;

use Yii;

trait ModelTrait
{

    /**
     * 重写 \yii\base\Model::addError()
     * @inheritdoc
     */
    public function addError($attribute, $error = '')
    {
        // 添加开始
        if (Yii::$app instanceof \yii\web\Application) {
            Yii::$app->response->setStatusCode(400);
        }
        // 添加结束

        return parent::addError($attribute, $error);
    }

    /**
     * 重写 \yii\base\Model::load()
     * @inheritdoc
     */
    public function load($data, $formName = null)
    {
        if (parent::load($data, $formName)) {
            return true;
        }

        // 添加开始
        if (Yii::$app instanceof \yii\web\Application) {
            Yii::$app->response->setStatusCode(400);
        }
        // 添加结束

        return false;
    }

}
