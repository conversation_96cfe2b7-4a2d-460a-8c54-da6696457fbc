<?php

namespace app\components\base\filter;

use app\components\web\User;
use yii\di\Instance;
use Yii;

class AccessControl extends \yii\filters\AccessControl
{
    private $user_rewrited = false;

    /**
     * @throws \yii\base\InvalidConfigException
     */
    public function init()
    {
        // 修改开始：使用自定义的 User 替换默认的 yii\web\User
        if (!$this->user_rewrited) {
            $this->user = Instance::ensure($this->user, User::class);
            $this->user_rewrited = true;
        }
        // 修改结束
        foreach ($this->rules as $i => $rule) {
            if (is_array($rule)) {
                $this->rules[$i] = Yii::createObject(array_merge($this->ruleConfig, $rule));
            }
        }
    }

}
