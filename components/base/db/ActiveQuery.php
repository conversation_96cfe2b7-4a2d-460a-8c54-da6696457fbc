<?php

namespace app\components\base\db;

use yii\db\ActiveRecord;

class ActiveQuery extends \yii\db\ActiveQuery
{
    /**
     * 按指定顺序返回数据记录（自定义的新方法）
     *
     * Executes query and returns all results order by column values as an array.
     * @param $column string Order by which column.
     * @param $values array the conditions that should be put in in condition.
     * @param null $db Connection $db the DB connection used to create the DB command.
     * If null, the DB connection returned by [[modelClass]] will be used.
     * @return array|ActiveRecord[] the query results. If the query results in nothing,
     * an empty array will be returned.
     */
    public function allByColumnValues($column, $values, $db = null)
    {
        if ($this->select && !in_array($column, $this->select)) {
            array_push($this->select, $column);
        }
        $this->andWhere([$column => $values]);
        $items = parent::all($db);
        if ($items) {
            $values_flip = array_flip($values);
            uasort($items, function ($pre, $next) use ($column, $values_flip) {
                return $values_flip[$pre->$column] <=> $values_flip[$next->$column];
            });
            $items = array_values($items);
        }
        return $items;
    }

    public function populate($rows)
    {
        $rows = parent::populate($rows);
        if (!$this->asArray && count($rows) > 0) {
            // 添加开始
            current($rows)::afterAllFind($rows);
            // 添加结束
        }
        return $rows;
    }

}
