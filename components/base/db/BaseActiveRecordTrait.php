<?php

namespace app\components\base\db;

trait BaseActiveRecordTrait
{
    /**
     * 自定义的新方法
     *
     * This method is called when the object is created and populated with the query result.
     * The default implementation will trigger an [[EVENT_AFTER_FIND]] event.
     * When overriding this method, make sure you call the parent implementation to ensure the
     * event is triggered.
     */
    public static function afterAllFind(&$models)
    {
        // PASS
    }

}
