<?php

namespace app\components\base\debug;

use app\components\base\filter\AccessControl;
use yii\debug\models\UserSwitch;

class UserPanel extends \yii\debug\panels\UserPanel
{
    /**
     * {@inheritdoc}
     */
    public function init()
    {
        if (!$this->isEnabled() || $this->getUser()->isGuest) {
            return;
        }

        $this->userSwitch = new UserSwitch(['userComponent' => $this->userComponent]);
        $this->addAccessRules();

        if (!is_object($this->filterModel)
            && class_exists($this->filterModel)
            && in_array('yii\debug\models\search\UserSearchInterface', class_implements($this->filterModel), true)
        ) {
            $this->filterModel = new $this->filterModel;
        } elseif ($this->getUser() && $this->getUser()->identityClass) {
            if (is_subclass_of($this->getUser()->identityClass, 'yii\db\ActiveRecord')) {
                $this->filterModel = new \yii\debug\models\search\User();
            }
        }
    }

    /**
     * @inheritdoc
     */
    private function addAccessRules()
    {
        $this->ruleUserSwitch['controllers'] = [$this->module->id . '/user'];
        $this->module->attachBehavior(
            'access_debug',
            [
                'class' => AccessControl::class,  // 修改部分：使用自定义的 AccessControl 替换默认的 \yii\filters\AccessControl
                'only' => [$this->module->id . '/user', $this->module->id . '/default'],
                'user' => $this->userSwitch->getMainUser(),
                'rules' => [
                    $this->ruleUserSwitch,
                ],
            ]
        );
    }

}
