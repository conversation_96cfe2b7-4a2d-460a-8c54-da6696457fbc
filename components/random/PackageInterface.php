<?php
/**
 * Created by PhpStorm.
 * User: Tom<PERSON>ao
 * Date: 2018/5/4
 * Time: 下午5:33
 */

namespace app\components\random;

interface PackageInterface
{
    // Array[card]: id, card_package_id, work_id, role_id, level, special, coupon
    public function getCards();

    public function getPushRules(): array;

    public static function getObtainingMethod();

    public function getGiveCoupon(): int;

    public function getDrawTimes(): int;
}
