<?php
/**
 * Created by PhpStorm.
 * User: Tom<PERSON>ao
 * Date: 2018/4/4
 * Time: 下午1:55
 */

namespace app\components\random;

use app\models\Card;
use \Exception;
use \RuntimeException;
use Yii;
use yii\base\InvalidArgumentException;

/**
 * Class Lottery
 * @package app\components\random
 * @property Card $item a engine to get items
 */
class Lottery implements LotteryInterface
{
    private $probabilities = [50, 100];
    private $max = 0;

    private $item;

    public function __construct(ItemInterface $item, array $weights = [50, 50])
    {
        $this->setProbabilities($weights);
        $this->item = $item;
    }

    public function setProbabilities(array $weights)
    {
        $probability = 0;
        foreach ($weights as &$weight) {
            if (!(is_int($weight) && $weight > 0)) {
                throw new InvalidArgumentException('权重必须为正整数');
            }
            $weight = $probability + $weight;
            $probability = $weight;
        }
        unset($weight);
        $this->max = $probability;
        $this->probabilities = $weights;
    }

    private function getRand()
    {
        while (true) {
            try {
                return random_int(1, $this->max);
            } catch (Exception $e) {}
        }
    }

    private function getType()
    {
        $rand = $this->getRand();
        foreach ($this->probabilities as $type => $probability) {
            if ($rand <= $probability) {
                return $type;
            }
        }
        Yii::error('概率逻辑有误', __METHOD__);
        throw new RuntimeException('概率逻辑有误');
    }

    public function getItem()
    {
        $items = $this->getItems(1);
        return $items[0] ?? null;
    }

    public function getItems(int $num): array
    {
        if (0 >= $num) {
            throw new InvalidArgumentException('返回数量必须为自然数');
        }
        $types = [];
        for ($i = 0; $i < $num; ++$i) {
            $types[] = $this->getType();
        }
        return $this->item::getRandomItemsByTypes($types);
    }
}
