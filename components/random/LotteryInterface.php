<?php
/**
 * Created by PhpStorm.
 * User: Tom<PERSON>ao
 * Date: 2018/5/2
 * Time: 下午6:03
 */

namespace app\components\random;

/**
 * Interface LotteryInterface
 * @package app\components\random
 * 抽奖接口
 */
interface LotteryInterface
{
    // 设置抽奖概率
    public function setProbabilities(array $weights);

    // 单次抽奖
    public function getItem();

    // 多次抽奖
    public function getItems(int $num): array;
}
