<?php

namespace app\components\cache;

use app\components\db\RedisConnection;
use yii\base\Configurable;
use yii\caching\Cache;

class RedisCache extends Cache implements Configurable
{
    /**
     * @var array
     */
    public $redis = [];

    /**
     * @var null|RedisConnection
     */
    private $_cache = null;

    public function init()
    {
        parent::init();
        $this->_connect();
    }

    private function _connect()
    {
        if (!is_null($this->_cache)) {
            return $this->_cache;
        }
        $this->_cache = new RedisConnection($this->redis);
    }

    /**
     * @inheritdoc
     */
    public function buildKey($key)
    {
        // 父类方法会对 key 进行哈希处理，在需要手工删除缓存时会不便于操作，故进行重写
        if (is_string($key)) {
            return $this->keyPrefix . $key;
        }
        // 父类会进行 keyPrefix 的拼接
        return parent::buildKey($key);
    }

    /**
     * @inheritdoc
     */
    protected function setValue($key, $value, $duration)
    {
        return $this->_cache->setValue($key, $value, $duration);
    }

    /**
     * @inheritdoc
     */
    protected function getValue($key)
    {
        return $this->_cache->get($key);
    }

    /**
     * @inheritdoc
     */
    protected function addValue($key, $value, $duration)
    {
        return $this->_cache->addValue($key, $value, $duration);
    }

    /**
     * @inheritdoc
     */
    protected function deleteValue($key)
    {
        return $this->_cache->del($key);
    }

    /**
     * @inheritdoc
     */
    protected function flushValues()
    {
        return $this->_cache->flushDB();
    }
}
