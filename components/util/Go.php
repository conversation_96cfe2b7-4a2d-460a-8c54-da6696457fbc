<?php

namespace app\components\util;

use app\models\AnMsg;
use app\models\Discovery;
use app\models\Live;
use app\models\MAlbum;
use app\models\Mowangsksoundseiy;
use app\models\Mowangskuser;
use app\models\MSound;
use Exception;
use Yii;
use yii\db\ActiveQuery;
use yii\helpers\Json;
use yii\web\HttpException;

/**
 * 封装对 golang 项目接口调用并进行处理数据的类
 *
 * @property string $secret 发送请求的签名密钥
 *
 * <AUTHOR> <<EMAIL>>
 */
class Go
{
    // 检测状态，200：成功
    const STATUS_SUCCESS = 200;

    // 检测接口调用成功状态码
    const CODE_SUCCESS = 0;
    const CODE_ERROR_FORBIDDEN_SEARCH_WORD = 200510002;
    const CODE_SEND_MESSAGE_USER_COUNT_LIMIT = 200020006;
    const CODE_SEND_MESSAGE_LIMIT = 200020008;

    // 场景：私信
    const SCENE_PRIVATE_MSG = 'private_message';
    // 场景：评论
    const SCENE_COMMENT = 'comment';
    // 场景：弹幕
    const SCENE_DANMAKU = 'danmaku';
    // 场景：简介
    const SCENE_INTRO = 'intro';
    // 场景：用户信息
    const SCENE_USER_INFO = 'user_info';
    // 场景：搜索
    const SCENE_SEARCH = 'search';
    // 场景：音单
    const SCENE_ALBUM = 'album';
    // 风控场景：评论
    const RISK_SCENE_COMMENT = 'comment';
    // 风控场景：投票
    const RISK_SCENE_VOTE = 'vote';

    // 检测内容分类
    const LABEL_AD = 'ad';
    const LABEL_EVIL = 'evil';
    const LABEL_PORN = 'porn';
    const LABEL_POLITICS = 'politics';
    const LABEL_TERRORISM = 'terrorism';
    const LABEL_ABUSE = 'abuse';
    const LABEL_CUSTOMIZED = 'customized';

    // 文本检测 API
    const API_SCAN_TEXT = '/rpc/scan/text';
    // 图片检测 API
    const API_SCAN_IMAGE = '/rpc/scan/image';
    // 风险识别检测 API
    const API_SCAN_RISK = '/rpc/scan/risk';

    // 风险识别等级 low：低风险；medium：中等风险；high：高风险
    const LEVEL_LOW = 'low';
    const LEVEL_MEDIUM = 'medium';
    const LEVEL_HIGH = 'high';

    // 来源（搜集用户信息、关注、评论）
    const FROM_APP = 2;

    // 搜索内容、搜索联想词、关键词匹配数量 API
    const API_DISCOVERY_SEARCH = '/rpc/discovery/search';
    const API_DISCOVERY_SEARCH_CARD = '/rpc/discovery/search-card';
    const API_DISCOVERY_SUGGEST = '/rpc/discovery/suggest';
    const API_DISCOVERY_SEARCH_COUNT = '/rpc/discovery/searchcount';

    // 添加父评论 API
    const API_ADD_COMMENT = '/rpc/message/add-comment';
    // 添加子评论 API
    const API_ADD_SUBCOMMENT = '/rpc/message/add-subcomment';

    // 添加弹幕 API
    const API_ADD_DM = '/rpc/message/add-dm';

    // 私信检查 API
    const API_CHECK_PM = '/rpc/scan/check-pm';

    // 更新活动积分接口
    const API_UPDATE_EVENT_DRAW_POINT = '/rpc/event/drawpoint/update';
    // 点赞弹幕 API
    const API_LIKE_DM = '/rpc/message/like-dm';

    // 关注用户 API
    const API_PERSON_FOLLOW = '/rpc/person/follow';
    // 取消关注用户 API
    const API_PERSON_UNFOLLOW = '/rpc/person/unfollow';

    // 根据 IP ID 获取周边信息
    const API_GET_DERIVATIVES_BY_IP = '/rpc/drama/get-derivatives-by-ip';

    // 添加催眠专享播放历史记录
    const API_ADD_RADIO_PLAY_HISTORY = '/rpc/history/add-radio-play-history';
    // 获取催眠专享播放历史记录
    const API_GET_RADIO_PLAY_HISTORY = '/rpc/history/get-radio-play-history';

    // 人机验证 API
    const API_CAPTCHA_VALIDATE = '/rpc/captcha/validate';

    // 搜索场景
    const SCENARIO_MAIN_SEARCH = 0;  // 主搜（首页、发现页）
    const SCENARIO_PERSON_SEARCH = 1;  // 个人搜索（个人主页音频）
    const SCENARIO_BACKEND_SEARCH = 2;  // 后台搜索（评论搜索后台）
    const SCENARIO_ORGANIZATION_SEARCH = 3;  // 社团搜索（社团剧集搜索页）

    // 发送请求的签名密钥
    public $secret;

    /**
     * 检测文本违规情况
     *
     * @param string|array $check_text 相关字符串或字符串组成的数组，如 '你好北京' 或 ['你好', '北京']
     * @param string $scene 检测场景
     * @param bool $ad_free 是否放过广告 true：放过；false：不放过
     *
     * @return array|null 文本检测结果。如：[['pass' => true, 'labels' => ['abuse']]]；若检测失败返回 null
     * @throws \Exception 传入不支持的文字检测场景参数时抛出异常
     */
    public function checkText($check_text, $scene = null, $ad_free = false)
    {
        if (empty($check_text)) {
            return null;
        }
        $service = Yii::$app->params['service'];
        $base_uri = $service['go']['url'];
        if (is_string($check_text)) {
            $check_text = [$check_text];
        }
        $data = [
            'text' => $check_text,
            'ad_free' => $ad_free,
            'user_id' => (int)Yii::$app->user->id,
        ];
        if ($scene) {
            if (!in_array($scene, [self::SCENE_PRIVATE_MSG, self::SCENE_COMMENT, self::SCENE_DANMAKU,
                self::SCENE_INTRO, self::SCENE_USER_INFO, self::SCENE_SEARCH])) {
                throw new \Exception('不支持的文字检测场景');
            }
            $data['scene'] = $scene;
        }
        $res = Yii::$app->tools->requestApi(self::API_SCAN_TEXT, $data, $base_uri, $this->secret);
        if (!$res) {
            Yii::error('Request ' . self::API_SCAN_TEXT . ' api fail', __METHOD__);
            return null;
        }
        if ($res['code'] !== self::CODE_SUCCESS) {
            // 若请求接口出错，不直接抛出异常避免影响用户，将错误记录到日志中
            Yii::error("请求文本检测接口出错：{$res['info']}", __METHOD__);
            return null;
        }
        return $res['info'];
    }

    /**
     * 检测图片违规情况
     *
     * @param string|array $check_url 图片网络地址完整路径字符串或由其组成的数组
     * @param string $scene 检测场景
     *
     * @return array|null 图片检测结果。如：[['pass' => false, 'labels' => ['politics']]]；若检测失败返回 null
     */
    public function checkImage($check_url, $scene = null)
    {
        if (empty($check_url)) {
            return null;
        }
        $service = Yii::$app->params['service'];
        $base_uri = $service['go']['url'];

        $data = [
            'url' => $check_url,
            'scene' => $scene,
            'user_id' => (int)Yii::$app->user->id,
        ];
        $res = Yii::$app->tools->requestApi(self::API_SCAN_IMAGE, $data, $base_uri, $this->secret);
        if (!$res) {
            Yii::error('Request ' . self::API_SCAN_IMAGE . ' api fail', __METHOD__);
            return null;
        }
        if ($res['code'] !== self::CODE_SUCCESS) {
            // 若请求接口出错，不直接抛出异常避免影响用户，将错误记录到日志中
            Yii::error("请求图片检测接口出错：{$res['info']}", __METHOD__);
            return null;
        }
        return $res['info'];
    }

    /**
     * 检测高危行为的情况
     *
     * @param string $scene 检测场景 vote：投票；comment：评论
     *
     * @return array|null 检测结果。如：[['pass' => true, 'labels' => ['low']]]；若检测失败返回 null
     * @throws \Exception 传入不支持的风险识别检测场景参数时抛出异常
     */
    public function checkRisk(?string $scene = null)
    {
        $service = Yii::$app->params['service'];
        $base_uri = $service['go']['url'];

        // TODO: 这里还有注册场景暂时用不到先不添加
        if (!in_array($scene, [self::RISK_SCENE_VOTE, self::RISK_SCENE_COMMENT])) {
            throw new \Exception('不支持的风险识别检测场景');
        }
        $data = [
            'scene' => $scene,
            'user_id' => (int)Yii::$app->user->id
        ];
        $res = Yii::$app->tools->requestApi(self::API_SCAN_RISK, $data, $base_uri, $this->secret);
        if (!$res) {
            Yii::error('Request ' . self::API_SCAN_RISK . ' api fail', __METHOD__);
            return null;
        }
        if ($res['code'] !== self::CODE_SUCCESS) {
            // 若请求接口出错，不直接抛出异常避免影响用户，将错误记录到日志中
            Yii::error("请求风险识别检测接口出错：{$res['info']}", __METHOD__);
            return null;
        }
        return $res['info'];
    }

    /**
     * @param string $keyword
     * @param int $search_type
     * @param int $page
     * @param int $page_size
     * @param array $options 例 ['sort' => 0, 'sensitive' => 0, 'cid' => 0]
     * @return array
     * @throws HttpException 网络出错时抛出异常
     */
    public function search(string $keyword, int $search_type, int $page, int $page_size, array $options = [])
    {
        $service = Yii::$app->params['service'];
        $base_uri = $service['go']['url'];

        // 请求搜索
        try {
            $resp = Yii::$app->tools->requestApi(self::API_DISCOVERY_SEARCH, array_merge([
                'keyword' => $keyword,
                'search_type' => $search_type,
                'page' => $page,
                'page_size' => $page_size,
                'guide_word' => trim(Yii::$app->request->get('guide_word')),
                'suggest_request_id' => trim(Yii::$app->request->get('suggest_request_id')),
            ], $options), $base_uri, $this->secret);
            if ($resp['code'] !== self::CODE_SUCCESS) {
                if ($resp['status'] >= 500) {
                    throw new \Exception($resp['info']);
                }
                // 请求失败且非 500 的情况返回空结构数据
                Yii::error("搜索请求接口出错：{$resp['info']}", __METHOD__);
                return $this->getEmptySearch($page, $page_size);
            }
        } catch (\Exception $e) {
            Yii::error("Request search rpc api fail: {$e->getMessage()}", __METHOD__);
            // 若搜索关键词接口出错，抛出异常让页面出现重试按钮
            throw new HttpException(500, '页面加载失败');
        }

        $result = $resp['info']['result'];
        $count = $result['viewtotal'];
        $models = $result['items'];
        $class = null;
        switch ($search_type) {
            case Discovery::SEARCH_USER:
                $class = Mowangskuser::class;
                break;
            case Discovery::SEARCH_DRAMA:
                // drama 请求时调用 rpc 处理
                break;
            case Discovery::SEARCH_MALBUM:
                $class = MAlbum::class;
                break;
            case Discovery::SEARCH_SOUND:
                $class = MSound::class;
                break;
            case Discovery::SEARCH_SEIY:
                $class = Mowangsksoundseiy::class;
                break;
            case Discovery::SEARCH_LIVE:
                $class = Live::class;
                break;
        }
        if ($class) {
            // 触发 afterFind 获取数据
            $query = Yii::createObject(ActiveQuery::class, [$class]);
            $models = $query->populate($models);
        }

        // 综合搜索 $this->_page_size 可能为 0，除法运算的分母不能为 0
        // 综合搜索的搜索分类数量 max_page 为 0
        $max_page = $page_size ? ceil($count / $page_size) : 0;
        return [
            'Datas' => $models,
            'pagination' => [
                'p' => $page,
                'pagesize' => $page_size,
                'count' => $count,
                'maxpage' => $max_page,
            ],
            'ops_request_misc' => $resp['info']['ops_request_misc']
        ];
    }

    /**
     * @param string $keyword 关键词
     * @param int $user_id 用户 ID
     * @param int $page 页数
     * @return array|null 若接口请求失败返回 null
     */
    public function searchCard(string $keyword, int $user_id, int $page): ?array
    {
        $service = Yii::$app->params['service'];
        $base_uri = $service['go']['url'];

        $resp = Yii::$app->tools->requestApi(self::API_DISCOVERY_SEARCH_CARD, [
            'keyword' => $keyword,
            'user_id' => $user_id,
            'page' => $page,
            'os' => Yii::$app->equip->getOs(),
        ], $base_uri, $this->secret);
        if ($resp['code'] !== self::CODE_SUCCESS) {
            Yii::error("搜索卡片接口出错：{$resp['info']}", __METHOD__);
            return null;
        }
        return $resp['info'];
    }

    public function getEmptySearch($page, $page_size)
    {
        return [
            'Datas' => [],
            'pagination' => [
                'p' => $page,
                'pagesize' => $page_size,
                'count' => 0,
                'maxpage' => 0,
            ],
        ];
    }

    /**
     * 获取搜索建议词
     *
     * @param string $keyword
     * @param int $search_type
     * @param int $count
     * @return array 例 ['request_id' => '158252397619723301338522', 'suggestions' => ['捕捉到一只咸鱼', '不知甜的冰淇淋']]
     */
    public function suggest(string $keyword, int $search_type, $count = Discovery::SUGGEST_COUNT)
    {
        $service = Yii::$app->params['service'];
        $base_uri = $service['go']['url'];
        // 请求搜索联想词
        try {
            $resp = Yii::$app->tools->requestApi(self::API_DISCOVERY_SUGGEST, [
                'keyword' => $keyword,
                'search_type' => $search_type,
                'page_size' => $count,
                'user_id' => (int)Yii::$app->user->id,
            ], $base_uri, $this->secret);
            if ($resp['code'] !== self::CODE_SUCCESS) {
                if ($resp['status'] >= 500) {
                    throw new \Exception($resp['info']);
                }
                return null;
            }
        } catch (\Exception $e) {
            // 若搜索关键词建议接口出错，不直接抛出异常避免影响用户，将错误记录到日志中
            Yii::error("Request search suggest rpc api fail: {$e->getMessage()}", __METHOD__);
            return null;
        }

        return [
            'request_id' => $resp['info']['request_id'],
            'suggestions' => array_column($resp['info']['suggestions'], 'suggestion'),
        ];
    }

    /**
     * @param string $keyword
     * @param int|null $user_id
     * @return array
     */
    public function searchCount(string $keyword, ?int $user_id)
    {
        $service = Yii::$app->params['service'];
        $base_uri = $service['go']['url'];
        // 请求搜索关键词匹配数量
        try {
            $resp = Yii::$app->tools->requestApi(self::API_DISCOVERY_SEARCH_COUNT, [
                'keyword' => $keyword,
            ], $base_uri, $this->secret);
            if ($resp['code'] !== self::CODE_SUCCESS) {
                if ($resp['status'] >= 500) {
                    throw new \Exception($resp['info']);
                }
                return [];
            }
        } catch (\Exception $e) {
            // 若搜索关键词匹配数量接口出错，不直接抛出异常避免影响用户，将错误记录到日志中
            Yii::error("Request search count rpc api fail:：{$e->getMessage()}", __METHOD__);
            return [];
        }
        return array_reduce($resp['info'], function ($ret, $item) {
            if ($item['type'] !== Discovery::SEARCH_CHANNEL) { // APP 没有搜索频道
                $item['name'] = Discovery::SEARCH_NAME_MAP[$item['type']] ?? '';
                $ret[] = $item;
            }
            return $ret;
        }, []);
    }

    /**
     * 请求 missevan-go web 服务 rpc 接口
     *
     * @param string $api 接口地址，如 /rpc/util/geoip
     * @param array $params 接口参数
     * @return array|null rpc 接口响应数据，为 null 时代表接口请求出错
     */
    public function rpc(string $api, array $params = [])
    {
        $service = Yii::$app->params['service'];
        $base_uri = $service['go']['url'];
        $res = Yii::$app->tools->requestApi($api, $params, $base_uri);
        if (!$res) {
            Yii::error('Request ' . $api . ' api fail', __METHOD__);
            return null;
        }
        if ($res['status'] >= 500) {
            // 若请求接口出错，不直接抛出异常避免影响用户，将错误记录到日志中
            Yii::error("请求 {$api} 接口出错：{$res['info']}", __METHOD__);
            return null;
        }
        return $res;
    }

    /**
     * 检查发送的私信
     *
     * @param int $from_user_id 发送用户
     * @param int $to_user_id 接收用户
     * @param string $text 私信
     *
     * @return array
     *  [
     *    'status' => 1,  // 发送状态: 0 假发送，1 真发送
     *    'msg' => "响应话术"
     *  ];
     */
    public function checkPM(int $from_user_id, int $to_user_id, string $text): array
    {
        $data = [
            'from_user_id' => $from_user_id,
            'to_user_id' => $to_user_id,
            'text' => $text,
        ];
        $res = self::rpc(self::API_CHECK_PM, $data);
        if (!$res) {
            // 如果 RPC 服务出现问题，允许发送私信，避免因为 RPC 出现问题，导致无法发送私信
            return ['status' => AnMsg::SEND_STATUS_ALLOW];
        }
        if ($res['code'] !== self::CODE_SUCCESS) {
            throw new HttpException($res['status'], $res['info'], $res['code']);
        }
        return $res['info'];
    }

    /**
     * 添加父评论
     *
     * @param integer $user_id 用户 ID
     * @param integer $c_type 评论类型 1：单音；2：音单；3：新闻；4：频道；5：用户；6：专题；7：活动；8：语音包；
     * 9：求签语音
     * @param integer $element_id 对象 ID
     * @param string $comment_content 评论内容
     *
     * @return array|string 添加评论的处理结果
     * @throws HttpException
     */
    public function addComment(int $user_id, int $c_type, int $element_id, string $comment_content)
    {
        $data = array_merge([
            'c_type' => $c_type,
            'element_id' => $element_id,
            'comment_content' => $comment_content,
            'user_id' => $user_id,
            'from' => self::FROM_APP,
        ], self::addUserParam());
        $res = self::rpc(self::API_ADD_COMMENT, $data);
        if (!$res) {
            throw new HttpException(500, '网络错误');
        }
        if ($res['code'] !== self::CODE_SUCCESS) {
            throw new HttpException($res['status'], $res['info'], $res['code']);
        }
        return $res['info'];
    }

    /**
     * 添加子评论
     *
     * @param integer $user_id 用户 ID
     * @param integer $comment_id 评论 ID
     * @param string $comment_content 评论内容
     *
     * @return array 添加子评论的处理结果
     * @throws HttpException
     */
    public function addSubComment(int $user_id, int $comment_id, string $comment_content)
    {
        $data = array_merge([
            'comment_id' => $comment_id,
            'comment_content' => $comment_content,
            'user_id' => $user_id,
            'from' => self::FROM_APP,
        ], self::addUserParam());
        $res = self::rpc(self::API_ADD_SUBCOMMENT, $data);
        if (!$res) {
            throw new HttpException(500, '网络错误');
        }
        if ($res['code'] !== self::CODE_SUCCESS) {
            throw new HttpException($res['status'], $res['info'], $res['code']);
        }
        return $res['info'];
    }

    /**
     * 添加弹幕
     *
     * @param integer $user_id 用户 ID
     * @param integer $sound_id 音频 ID
     * @param integer $color 弹幕颜色
     * @param integer $size 字体大小
     * @param integer $mode 弹幕显示方式
     * @param string $text 发送内容
     * @param string $stime 发送时间（单位为秒）
     *
     * @return array 添加弹幕的处理结果
     * @throws HttpException
     */
    public function addDm(int $user_id, int $sound_id, int $color, int $size, int $mode, string $text, string $stime)
    {
        $data = array_merge([
            'user_id' => $user_id,
            'sound_id' => $sound_id,
            'color' => $color,
            'size' => $size,
            'mode' => $mode,
            'text' => $text,
            'stime' => $stime,
            'from' => self::FROM_APP,
        ], self::addUserParam());
        $res = self::rpc(self::API_ADD_DM, $data);
        if (!$res) {
            throw new HttpException(500, '网络错误');
        }
        if ($res['code'] !== self::CODE_SUCCESS) {
            throw new HttpException($res['status'], $res['info'], $res['code']);
        }
        return $res['info'];
    }

    /**
     * 增加传参
     *
     * @return array
     */
    public function addUserParam()
    {
        return [
            'ip' => Yii::$app->request->userIP,
            'user_agent' => Yii::$app->request->userAgent,
            'equip_id' => Yii::$app->equip->getEquipId(),
            'buvid' => Yii::$app->equip->getBuvid(),
        ];
    }

    /**
     * 点赞弹幕
     *
     * @param int $element_type 弹幕所属元素类型
     * @param int $danmaku_id 弹幕 ID
     * @param int $user_id 点赞用户 ID
     * @param int $action 点赞或取消点赞
     * @return array 点赞状态 ['status' => true]
     * @throws HttpException 请求失败抛出异常
     */
    public function likeDm(int $element_type, int $danmaku_id, int $user_id, int $action): array
    {
        $data = [
            'element_type' => $element_type,
            'danmaku_id' => $danmaku_id,
            'user_id' => $user_id,
            'action' => $action,
        ];
        $res = self::rpc(self::API_LIKE_DM, $data);
        if (!$res) {
            throw new HttpException(500, '网络错误，请稍后再试');
        }
        if ($res['code'] !== self::CODE_SUCCESS) {
            throw new HttpException($res['status'], $res['info'], $res['code']);
        }
        return $res['info'];
    }

    /**
     * 关注用户
     *
     * @param integer $user_id 用户 ID
     * @param integer $follow_user_id 被关注者 ID
     * @param integer $follow_from 关注来源
     * @return string 关注用户处理结果
     * @throws HttpException
     */
    public function follow(int $user_id, int $follow_user_id, int $follow_from)
    {
        $data = [
            'user_id' => $user_id,
            'follow_user_id' => $follow_user_id,
            'follow_from' => $follow_from
        ];
        $res = self::rpc(self::API_PERSON_FOLLOW, $data);
        if (!$res) {
            throw new HttpException(500, '网络错误');
        }
        if ($res['code'] !== self::CODE_SUCCESS) {
            throw new HttpException($res['status'], $res['info'], $res['code']);
        }
        return $res['info'];
    }

    /**
     * 取消关注用户
     *
     * @param integer $user_id 用户 ID
     * @param integer $follow_user_id 被关注者 ID
     * @param integer $follow_from 关注来源
     * @return string 取消关注用户处理结果
     * @throws HttpException
     */
    public function unfollow(int $user_id, int $follow_user_id, int $follow_from)
    {
        $data = [
            'user_id' => $user_id,
            'follow_user_id' => $follow_user_id,
            'follow_from' => $follow_from
        ];
        $res = self::rpc(self::API_PERSON_UNFOLLOW, $data);
        if (!$res) {
            throw new HttpException(500, '网络错误');
        }
        if ($res['code'] !== self::CODE_SUCCESS) {
            throw new HttpException($res['status'], $res['info'], $res['code']);
        }
        return $res['info'];
    }

    /**
     * 根据 IP ID 获取周边信息（IP 是指 Intellectual Property，不是 Internet Protocol。后续产权 IP 我们统一用 IPR，用以区分。）
     *
     * @param int $ip_id IP ID
     * @param int $count 周边信息返回数量
     * @return array
     * @throws HttpException
     */
    public function getDerivatives(int $ip_id, int $count = 10)
    {
        $data = [
            'ip_id' => $ip_id,
            'count' => $count,
        ];
        $res = self::rpc(self::API_GET_DERIVATIVES_BY_IP, $data);
        if (!$res) {
            throw new HttpException(500, '网络错误');
        }
        if ($res['code'] !== self::CODE_SUCCESS) {
            throw new HttpException($res['status'], $res['info'], $res['code']);
        }
        return $res['info'];
    }

    /**
     * 更新用户活动积分
     *
     * @deprecated 之后统一使用 MiniGame 组件中的 updateEventDrawPoint 方法
     * @param int $event_id 活动 ID
     * @param int $user_id 用户 ID
     * @param int $point 新增积分
     * @throws HttpException 请求接口出错抛出异常
     * @return int 更新后用户总积分
     */
    public function updateEventDrawPoint(int $event_id, int $user_id, int $point): int
    {
        $res = Yii::$app->go->rpc(self::API_UPDATE_EVENT_DRAW_POINT, [
            'event_id' => $event_id,
            'user_id' => $user_id,
            'point' => $point,
        ]);
        if (is_null($res)) {
            // 若请求失败，则抛出异常
            throw new HttpException(500, '网络错误');
        }
        if ($res['code'] !== self::CODE_SUCCESS) {
            throw new HttpException($res['status'], $res['info'], $res['code']);
        }
        return $res['info']['point'];
    }

    /**
     * 添加催眠专享播放历史记录
     *
     * @param int $user_id 用户 ID
     * @param int $sound_id 催眠专享音频 ID
     * @param float $completion 最后的时间点占总时长的比例
     * @return string 成功提示文案
     * @throws HttpException rpc 请求出错时抛出异常
     */
    public function addRadioPlayHistory(int $user_id, int $sound_id, float $completion)
    {
        $data = [
            'user_id' => $user_id,
            'sound_id' => $sound_id,
            'completion' => $completion,
        ];
        $res = self::rpc(self::API_ADD_RADIO_PLAY_HISTORY, $data);
        if (!$res) {
            throw new HttpException(500, '网络错误');
        }
        if ($res['code'] !== self::CODE_SUCCESS) {
            throw new HttpException($res['status'], $res['info'], $res['code']);
        }
        return $res['info'];
    }

    /**
     * 获取催眠专享播放历史记录
     *
     * @param int $user_id 用户 ID
     * @param int $page_size 每页条数
     * @param string $marker 分页标识（最后访问时间戳，单位：毫秒）
     * @return array
     * @throws HttpException rpc 请求出错时抛出异常
     */
    public function getRadioPlayHistory(int $user_id, int $page_size, $marker = '')
    {
        $data = [
            'user_id' => $user_id,
            'page_size' => $page_size,
            'marker' => $marker,
        ];
        $res = self::rpc(self::API_GET_RADIO_PLAY_HISTORY, $data);
        if (!$res) {
            throw new HttpException(500, '网络错误');
        }
        if ($res['code'] !== self::CODE_SUCCESS) {
            throw new HttpException($res['status'], $res['info'], $res['code']);
        }
        return $res['info'];
    }

    const CAPTCHA_RISK_LEVEL_LOW = 1;
    const CAPTCHA_RISK_LEVEL_MEDIUM = 2;
    const CAPTCHA_RISK_LEVEL_HIGH = 3;

    /**
     * 人机验证是否通过
     *
     * @param string $captcha_token 验证 token
     * @param string|null $unique_id 标识用户的唯一 ID
     * @param string|null $scene 标识验证的场景
     * @return array 验证明细
     */
    public function captchaValidate(string $captcha_token, ?string $unique_id = null, ?string $scene = null): array
    {
        if ($captcha_token === '') {
            return ['success' => false];
        }
        $params = ['captcha_token' => $captcha_token] + self::addUserParam();
        if (!is_null($unique_id)) {
            $params['unique_id'] = $unique_id;
        }
        if (!is_null($scene)) {
            $params['scene'] = $scene;
        }
        $res = self::rpc(self::API_CAPTCHA_VALIDATE, $params);
        if (!$res) {
            // 若请求接口出错，视作验证失败，将错误记录到日志中
            Yii::error('Request ' . self::API_CAPTCHA_VALIDATE . ' api fail', __METHOD__);
            return ['success' => false];
        }
        if ($res['code'] !== self::CODE_SUCCESS) {
            // 若请求接口失败，视作验证失败，将错误记录到日志中
            Yii::error('Request ' . self::API_CAPTCHA_VALIDATE . " api fail: {$res['info']}", __METHOD__);
            return ['success' => false];
        }
        return $res['info'];
    }

}
