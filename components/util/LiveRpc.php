<?php

namespace app\components\util;

use Exception;
use yii\base\Component;
use yii\web\HttpException;
use Yii;

/**
 * @deprecated 后续应该使用 php-utils 中的 LiveRpc
 */
class LiveRpc extends Component
{
    // 检测接口调用成功状态码
    const CODE_SUCCESS = 0;

    // 随机获取一个小时榜开播直播间
    const API_LIVE_HOURRANK_RANDOM = '/rpc/live/hourrank/random';
    // 获取用户直播动态
    const API_USER_LIVE_FEED = '/rpc/user/live/feed';
    // 获取主播收益榜列表
    const API_USER_LIVE_RANK = '/rpc/user/live/rank';

    public $url;
    public $secret;
    public $schemeUrl;

    /**
     * 请求 live-service 服务 rpc 接口
     *
     * @param string $api 接口地址，如 /rpc/live/hourrank/random
     * @param array $params 接口参数
     * @return array|null rpc 接口响应数据，为 null 时代表接口请求出错
     */
    public function rpc(string $api, array $params = [])
    {
        $res = Yii::$app->tools->requestApi($api, $params, $this->url, $this->secret);
        if (!$res) {
            Yii::error('Request ' . $api . ' api fail', __METHOD__);
            return null;
        }
        if ($res['status'] >= 500) {
            // 若请求接口出错，不直接抛出异常避免影响用户，将错误记录到日志中
            Yii::error("请求 {$api} 接口出错：{$res['info']}", __METHOD__);
            return null;
        }
        return $res;
    }

    /**
     * 随机获取一个小时榜开播直播间
     *
     * @return array
     * @throws HttpException 请求失败抛出异常
     */
    public function getLiveHourrankRandom()
    {
        $res = self::rpc(self::API_LIVE_HOURRANK_RANDOM);
        if (!$res) {
            throw new HttpException(500, '网络错误');
        }
        if ($res['code'] !== self::CODE_SUCCESS) {
            throw new HttpException($res['status'], $res['info'], $res['code']);
        }
        return $res['info'];
    }

    /**
     * 获取直播间 SchemeUrl
     *
     * @param integer $room_id 直播间 ID
     * @param string|boolean $query url 链接后面的 query 参数，例如：?from=ad.0.1.80000_0_0 \
     * 也可能为 false 表示没有 query 参数
     * @return string
     */
    public function roomSchemeUrl(int $room_id, $query = ''): string
    {
        $url = $this->schemeUrl . '/' . $room_id;
        if ($query) {
            $url .= $query;
        }
        return $url;
    }

    /**
     * 从 SchemeUrl 中处理获取到直播间 ID
     *
     * @param string $recommend_url 链接地址 \
     * 例如：missevan://live/463640018?from=ad.0.1.80000_0_0 或者 tab:live?from=ad.0.1.80000_0_0
     * @return integer
     */
    public function parseRoomIdFromSchemeUrl(string $recommend_url): int
    {
        $room_id = 0;
        if (strpos($recommend_url, $this->schemeUrl) === 0 && $path = parse_url($recommend_url, PHP_URL_PATH)) {
            // 从协议地址 missevan://live/463640018 取出 463640018
            $room_id = (int)basename($path);
        }
        return $room_id;
    }

    /**
     * 获取小时榜开播直播间的 open_url
     *
     * @param string $recommend_url 链接地址 \
     * 例如：missevan://live/463640018?from=ad.0.1.80000_0_0 或者 tab:live?from=ad.0.1.80000_0_0
     * @return string|null
     */
    public function getOpenUrlFromLiveHourRank(string $recommend_url): ?string
    {
        try {
            $open_url = null;
            $live = $this->getLiveHourrankRandom();
            if ($live['room_id'] !== 0) {
                if ($query = parse_url($recommend_url, PHP_URL_QUERY)) {
                    $query = '?' . $query;
                }
                $open_url = $this->roomSchemeUrl($live['room_id'], $query) ?: null;
            }
            return $open_url;
        } catch (Exception $e) {
            // PASS
        }
        return null;
    }

    /**
     * 获取用户直播动态
     *
     * @param int $user_id 用户 ID
     * @return mixed
     */
    public function getLiveFeed(int $user_id)
    {
        $res = $this->rpc(self::API_USER_LIVE_FEED, ['user_id' => $user_id]);
        if (!$res) {
            return null;
        }
        if ($res['code'] !== self::CODE_SUCCESS) {
            // 如果没有请求成功，则降级并记录错误日志
            Yii::error("获取用户直播动态 rpc 接口出错：{$res['info']}", __METHOD__);
            return null;
        }
        return $res['info'];
    }

    /**
     * 获取主播收益榜列表
     *
     * @param int $type 榜单类型
     * @param int $rank_num 榜单数量
     * @return mixed|null
     */
    public function getUserLiveRankList(int $type, int $rank_num)
    {
        $res = $this->rpc(self::API_USER_LIVE_RANK,
            ['type' => $type, 'rank_num' => $rank_num]);
        if (!$res) {
            return null;
        }
        if ($res['code'] !== self::CODE_SUCCESS) {
            // 如果没有请求成功，则降级并记录错误日志
            Yii::error("获取主播收益榜列表 rpc 接口出错：{$res['info']}", __METHOD__);
            return null;
        }
        return $res['info'];
    }
}
