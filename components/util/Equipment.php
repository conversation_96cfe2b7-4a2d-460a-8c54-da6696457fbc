<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/13
 * Time: 17:41
 */

namespace app\components\util;

use DeviceDetector\DeviceDetector;
use Exception;
use missevan\util\MUtils as MUtils2;
use missevan\util\NotImplementedException;
use Tuupola\Base62;
use Yii;
use yii\base\Component;
use yii\helpers\Json;
use yii\web\HttpException;

/**
 * Class Equipment
 * @package app\components\util
 *
 * @method static bool isFromTencentChannel()
 * @method static bool isFromYunYouXiChannel()
 * @method static bool isFromGoogleChannel()
 *
 * @property-read bool isValidEquipId
 */
class Equipment extends Component
{
    // Android 客户端设备号进行生成时用于 HMAC 运算的密钥
    const HMAC_KEY = 'd6fc3a4a06adbde89223bvefedc24fecde188aaa9161';

    // device token 版本
    const DEVICE_TOKEN_VERSION = 1;

    /**
     * @var string 设备号
     */
    protected $equip_id;

    /**
     * @var string|null BUVID
     */
    protected $buvid;

    /**
     * @var string|null 渠道标识
     */
    protected $channel;

    /**
     * @var int|null 设备激活时间（单位：秒）
     */
    protected $activate_time;

    /**
     * @var string 用户代理
     * iOS: MissEvanApp/3.9.7 (iOS;10.3;iPhone8,1)
     * Android: MissEvanApp/3.4.7 (Android;PRO6 6.0)
     * HarmonyOS: MissEvanApp/6.2.0 (HarmonyOS;5.0.0;HUAWEI BRA-AL00 HL1FLSM)
     */
    private $user_agent;

    /**
     * @var string App 版本号
     */
    private $appVersion;

    /**
     * @var int App 操作系统 iOS、Android、HarmonyOS
     */
    private $os;

    /**
     * @var string App 操作系统版本号
     */
    private $osVersion;

    /**
     * @var string App 手机型号
     */
    private $phone;

    /**
     * @var bool 请求是否来自app
     */
    private $from_app = false;

    /**
     * @var string MissEvanApp 或 MiMiApp
     */
    private $app_name = '';

    const APPNAME_MISSEVAN = 'MissEvanApp';
    const APPNAME_MIMI = 'MiMiApp';

    const DEBUG_USER_AGENT = 0;
    // 设备类型
    const All = 0;
    const Android = 1;
    const iOS = 2;
    const Web = 3;
    const MobileWeb = 4;
    const Windows = 5;  // 直播助手
    const HarmonyOS = 6;

    // 设备种类
    const DEVICE_TYPE_UNKNOWN = -1;  // 未知
    const DEVICE_TYPE_PHONE = 1;  // 手机
    const DEVICE_TYPE_PAD = 2;  // Pad

    // App 应用安装来源
    const CHANNEL_OFFICIAL = 'missevan';
    const CHANNEL_GOOGLE = 'missevan_google';
    // DEPRECATED: 下面列出的 missevan64_ 的都不使用了
    const CHANNEL_GOOGLE_64BIT = 'missevan64_google';
    const CHANNEL_BILIBILI = 'missevan_bilibili';
    const CHANNEL_BILIBILI_64BIT = 'missevan64_bilibili';
    const CHANNEL_TENCENT = 'missevan_tencent';
    const CHANNEL_TENCENT_64BIT = 'missevan64_tencent';
    const CHANNEL_YUNYOUXI = 'missevan_yunyouxi';
    const CHANNEL_YUNYOUXI_64BIT = 'missevan64_yunyouxi';
    const CHANNEL_VIVO = 'missevan_vivo';
    const CHANNEL_HUAWEI = 'missevan_huawei';

    // 猫耳概念版渠道标识：
    // iOS channel=missevan_concept
    // Android channel=missevan_concept_huawei, missevan_concept_vivo, 或 missevan64_concept_vivo 等
    const CHANNEL_CONCEPT = 'missevan_concept';
    const CHANNEL_CONCEPT_64BIT = 'missevan64_concept';  // 64 位渠道包

    public function init()
    {
        $cookies = Yii::$app->request->cookies;
        $this->equip_id = $cookies->getValue('equip_id');
        $this->buvid = $cookies->getValue('buvid');
        $this->channel = Yii::$app->request->headers['channel'];

        $this->parseUserAgent(Yii::$app->request->userAgent);
        $this->parseDeviceToken($cookies->getValue('device_token'));

        if (YII_DEBUG || !$this->isFromApp()) {
            return;
        }
        $this->checkOs();
    }

    protected function checkOs()
    {
        if (!$this->checkEquipID() || !$this->checkUrl() || !$this->isHttpsRequest()) {
            throw new HttpException(403, '非法请求');
        }
        if ($this->isAndroid() && !$this->checkChannel()) {
            throw new HttpException(403, '非法请求');
        }
    }

    public function parseUserAgent($user_agent)
    {
        $this->user_agent = $user_agent;

        preg_match(sprintf('/^(%s|%s)\/([^ ]+) \(([^\)]+)\)/', self::APPNAME_MISSEVAN, self::APPNAME_MIMI),
            $this->user_agent, $matches);

        if (4 === count($matches)) {
            // iOS App 请求的 User-Agent: MissEvanApp/4.9.2 (iOS;16.1.2;iPhone15,2)
            // Android App 请求的 User-Agent: MissEvanApp/5.7.6 (Android;12;HUAWEI MRX-AL19 HWMRX)
            // HarmonyOS App 请求的 User-Agent: MissEvanApp/6.2.0 (HarmonyOS;5.0.0;HUAWEI BRA-AL00 HL1FLSM)
            // 客户端里 WebView 页面过来的请求，User-Agent 包含 MissEvanApp/4.9.2 (Theme Dark; NetType 4G)
            // 例：Mozilla/5.0 (iPhone; CPU iPhone OS 16_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MissEvanApp/4.9.2 (Theme Dark; NetType 4G)
            // Mozilla/5.0 (iPhone; CPU iPhone OS 12_1_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/16C101 MissEvanApp/4.3.1 (Theme Dark; NetType Wi-Fi; SafeArea 31,13)
            // Mozilla/5.0 (Linux; Android 9; MIX 2 Build/PKQ1.190118.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/74.0.3729.136 Mobile Safari/537.36 MissEvanApp/5.2.3 (Theme Light; NetType 4G; SafeArea 31,13; FreeFlow)
            // Mozilla/5.0 (Phone; OpenHarmony 5.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36  ArkWeb/******* Mobile os/HarmonyOS MissEvanApp/6.2.2 (NetType Wi-Fi; SafeArea 123,91)
            // 文档：https://info.missevan.com/pages/viewpage.action?pageId=41746907
            $user_agent_items = explode(';', $matches[3]);
            if (3 !== count($user_agent_items) || !in_array($user_agent_items[0], ['Android', 'iOS', 'HarmonyOS'])) {
                $this->os = self::Web;
                $this->from_app = false;
            } else {
                [$os, $this->osVersion, $this->phone] = $user_agent_items;
                switch ($os) {
                    case 'Android':
                        $this->os = self::Android;
                        break;
                    case 'iOS':
                        $this->os = self::iOS;
                        break;
                    case 'HarmonyOS':
                        $this->os = self::HarmonyOS;
                        break;
                    default:
                        throw new HttpException(403, '非法请求，代码：00000002', 100010004);
                }
                $this->app_name = $matches[1];
                $this->appVersion = $matches[2];
                $this->from_app = true;
            }
        } else {
            $this->os = self::Web;
            $this->from_app = false;
            $this->equip_id = '';
        }
    }

    /**
     * 解析 device token 信息，并赋值到相关设备属性
     *
     * @param string|null $device_token
     * @return void
     */
    public function parseDeviceToken(?string $device_token)
    {
        if (!$device_token) {
            return;
        }
        $device_info = $this->getDeviceInfoByToken($device_token);
        if (!$device_info) {
            return;
        }
        if (!key_exists('activate_time', $device_info) || !is_numeric($device_info['activate_time'])) {
            Yii::warning("设备（{$this->equip_id}）的 device_token（{$device_token}）中 activate_time 数据错误",
                __METHOD__);
            return;
        }
        $this->activate_time = (int)$device_info['activate_time'];
    }

    public function getUserAgent()
    {
        return $this->user_agent;
    }

    public function getEquipId()
    {
        return $this->equip_id;
    }

    public function getBuvid()
    {
        return $this->buvid;
    }

    public function setBuvid(string $buvid)
    {
        $this->buvid = $buvid;
    }

    /**
     * 获取渠道标识
     * @return string
     */
    public function getChannel(): string
    {
        return (string)$this->channel;
    }

    public function getAppVersion()
    {
        return $this->appVersion;
    }

    public function getOs()
    {
        return $this->os;
    }

    public function getOsVersion()
    {
        return $this->osVersion;
    }

    public function getPhone()
    {
        return $this->phone;
    }

    public function isAndroid()
    {
        return $this->os === self::Android;
    }

    public function isIOS()
    {
        return $this->os === self::iOS;
    }

    public function isHarmonyOS()
    {
        return $this->os === self::HarmonyOS;
    }

    public function isAndroidOrHarmonyOS()
    {
        return $this->isAndroid() || $this->isHarmonyOS();
    }

    public function getOSLabel()
    {
        switch ($this->os) {
            case self::Android:
                return 'Android';
            case self::iOS:
                return 'iOS';
            case self::HarmonyOS:
                return 'HarmonyOS';
            default:
                return '';
        }
    }

    public function isIOS8()
    {
        return ($this->os === self::iOS && version_compare($this->osVersion, '9.0', '<')
            && version_compare($this->osVersion, '8.0', '>='));
    }

    public function isIOS9()
    {
        return ($this->os === self::iOS && version_compare($this->osVersion, '10.0', '<')
            && version_compare($this->osVersion, '9.0', '>='));
    }

    /**
     * 是否为 iOS10 系统
     *
     * @return bool
     * @todo 之后需要与判断其他 iOS 版本系统整合下
     */
    public function isIOS10()
    {
        return ($this->os === self::iOS && version_compare($this->osVersion, '11.0', '<')
            && version_compare($this->osVersion, '10.0', '>='));
    }

    /**
     * 是否为 iOS 11 系统
     *
     * @return bool
     */
    public function isIOS11()
    {
        return ($this->isIOS() && version_compare($this->osVersion, '12.0', '<')
            && version_compare($this->osVersion, '11.0', '>='));
    }

    public function isFromApp(?string $app_name = null): bool
    {
        if (!is_null($app_name)) {
            return $this->from_app && $this->app_name === $app_name;
        }

        return $this->from_app;
    }

    public function isFromMissEvanApp()
    {
        return $this->isFromApp(self::APPNAME_MISSEVAN);
    }

    public function isFromMiMiApp()
    {
        return $this->isFromApp(self::APPNAME_MIMI);
    }

    public static function isAppVersion($os, $version)
    {
        $equipment = Yii::$app->equip;
        if ($os === $equipment->getOs() && $version === $equipment->getAppVersion()) return true;
        return false;
    }

    /**
     * 判断当前 App 版是否小于指定的 iOS、或 Android、HarmonyOS App 版本
     *
     * @param null|string $old_app_version_ios iOS App 老版本
     * @param null|string $old_app_version_android 安卓 App 老版本
     * @param null|string $old_app_version_harmonyos 鸿蒙 App 老版本
     * @param string $app_name [optional] App 名称（MissEvanApp 或 MiMiApp），默认为 MissEvanApp
     * @return bool
     */
    public static function isAppOlderThan(?string $old_app_version_ios = '', ?string $old_app_version_android = '',
            ?string $old_app_version_harmonyos = '', string $app_name = self::APPNAME_MISSEVAN): bool
    {
        $equipment = Yii::$app->equip;
        if (!$equipment->isFromApp($app_name)) {
            return false;
        }
        $app_version = $equipment->getAppVersion();
        $device = $equipment->getOs();
        switch ($device) {
            case self::iOS:
                if (version_compare($app_version, $old_app_version_ios, '<')) {
                    return true;
                }
                break;
            case self::Android:
                if (version_compare($app_version, $old_app_version_android, '<')) {
                    return true;
                }
                break;
            case self::HarmonyOS:
                if (version_compare($app_version, $old_app_version_harmonyos, '<')) {
                    return true;
                }
                break;
            default:
                return false;
        }
        return false;
    }

    public static function isMiMiAppOlderThan(?string $old_ios_app_version = '', ?string $old_android_app_version = '', ?string $old_harmonyos_app_version = '')
    {
        return self::isAppOlderThan($old_ios_app_version, $old_android_app_version, $old_harmonyos_app_version, self::APPNAME_MIMI);
    }

    protected function isHttpsRequest()
    {
        return (isset($_SERVER['HTTPS']) && 'on' === $_SERVER['HTTPS']);
    }

    protected function checkEquipID()
    {
        if (!preg_match('/^[a-z0-9]{8}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{12}$/', $this->equip_id, $match)) {
            return false;
        }
        if (Yii::$app->redis->sIsMember(KEY_BLACK_LIST_EQUIP_ID, $this->equip_id)) {
            return false;
        }
        return true;
    }

    protected function checkUrl()
    {
        $query_str = Yii::$app->request->queryString;
        if (preg_match('/&=/', $query_str)) {
            return false;
        }
        parse_str($query_str, $params_arr);
        if (isset($params_arr['token'])) return false;
        if (Yii::$app->request->isPost && Yii::$app->request->post('token')) {
            return false;
        }
        return true;
    }

    protected function checkChannel()
    {
        return !is_null($this->channel);
    }

    protected function checkPort($port)
    {
        return ((int)$_SERVER['SERVER_PORT'] === $port) ? true : false;
    }

    public function assertEquipIdValid()
    {
        if ($this->isValidEquipId) {
            return;
        }
        // TODO: 检查 UUID 不合法时，加入日志方便排查原因，稳定以后可改为在拦截器中对非法 UUID 进行拦截
        Yii::warning(sprintf('Wrong %s equip_id: %s', $this->getOSLabel(), $this->getEquipId()), __METHOD__);
        // 当 equip_id 不合法时，返回相应错误码，以便客户端更新 equip_id 后重新请求安装接口
        throw new HttpException(400, '需要更新 equip_id', 100010012);
    }

    public function getIsValidEquipId()
    {
        // NOTICE: 目前仅支持 Android 及鸿蒙设备 equip_id 的校验
        if ($this->isAndroidOrHarmonyOS()) {
            return self::checkUuidHash($this->getEquipId());
        }

        return true;
    }

    /**
     * 检查 UUID 携带中的 HASH 值是否合法
     *
     * @param string $uuid UUID
     * @return bool 是否合法
     */
    public static function checkUuidHash(string $uuid): bool
    {
        if (!$uuid) return false;
        $uuid_hash = substr($uuid, -8);
        // 去掉分隔符
        $uuid = str_replace('-', '', $uuid);
        $bytes = self::getBytesFromHex($uuid);
        // 补上常量位并去掉最后 4 个字节
        array_splice($bytes, 8, 0, [3, 0]);
        $bytes = array_slice($bytes, 0, -4);
        $str = self::getStringFromBytes($bytes);
        // 获取生成 hash 的数据
        $sign = base64_encode(hash_hmac('sha1', $str, self::HMAC_KEY, true));
        $hash = sprintf('%08x', self::hashCode($sign));
        // 去掉表示负数的高位
        $hash = substr($hash, -8);
        return $uuid_hash === $hash;
    }

    /**
     * 检查 mac 地址是否合法
     *
     * @param string $mac mac 地址，例如：f2:b6:43:a2:50:40
     * @return bool 是否合法
     */
    public static function checkMac(string $mac): bool
    {
        return preg_match('/([A-Fa-f0-9]{2}:){5}[A-Fa-f0-9]{2}/', $mac);
    }

    /**
     * 将十六进制的字符转为十进制字节的数组
     *
     * @param string $string 十六进制的字符穿
     * @return array 十进制字节的数组
     */
    private static function getBytesFromHex(string $string): array
    {
        $bytes = [];
        for ($i = 0; $i < strlen($string); $i += 2) {
            $bytes[] = hexdec(substr($string, $i, 2));
        }
        return $bytes;
    }

    /**
     * 将字节数组转为字符串
     *
     * @param array $bytes 字节数组
     * @return string 转好的字符串
     */
    private static function getStringFromBytes(array $bytes): string
    {
        $str2 = '';
        foreach ($bytes as $ch) {
            $str2 .= chr($ch);
        }
        return $str2;
    }

    /**
     * 对超过 32 位的整型做处理
     *
     * @param int $number 要处理的数值
     * @return int 处理好的数值
     */
    private static function overflow32($number): int
    {
        // 消掉高 32 位
        $number = $number & 0xFFFFFFFF;
        // 取第一位 判断是正数还是负数
        $p = $number >> 31;
        if ($p === 1) {
            $number = $number - 1;
            // 取反
            $number = ~$number;
            // 要消掉取反后的高 32 位
            $number = $number & 0xFFFFFFFF;
            return $number * -1;
        }
        return $number;
    }

    /**
     * 获取 HASH 值
     *
     * @param string $str 生成 HASH 的字符串
     * @return int HASH 值
     */
    private static function hashCode(string $str): int
    {
        $hash = 0;
        $len = strlen($str);
        for ($i = 0; $i < $len; $i++) {
            $hash = self::overflow32(self::overflow32(31 * $hash) + ord($str[$i]));
        }
        return $hash;
    }

    public static function isFromChannel(string $channel)
    {
        return Yii::$app->equip->getChannel() === $channel;
    }

    public static function __callStatic(string $method_name, $arguments)
    {
        switch ($method_name) {
            case 'isFromTencentChannel':
                return self::isFromChannel(self::CHANNEL_TENCENT) || self::isFromChannel(self::CHANNEL_TENCENT_64BIT);
            case 'isFromYunYouXiChannel':
                return self::isFromChannel(self::CHANNEL_YUNYOUXI) || self::isFromChannel(self::CHANNEL_YUNYOUXI_64BIT);
            case 'isFromGoogleChannel':
                return self::isFromChannel(self::CHANNEL_GOOGLE) || self::isFromChannel(self::CHANNEL_GOOGLE_64BIT);
        }

        throw new NotImplementedException('method not implemented');
    }

    /**
     * 是否为禁止使用版本
     */
    public static function isBanVersion(bool $isMimi = false): bool
    {
        if ($isMimi) {
            // MiMi 当前无禁止使用的版本
            return false;
        }
        $ios_min_version = '4.6.5';
        if (Yii::$app->equip->isIOS8()) {
            // 若为 iOS 8 系统，则需要兼容 4.5.5 及以上版本
            $ios_min_version = '4.5.5';
        }
        // NOTICE: Android 4.x 最低兼容的 Android 客户端版本是 5.6.5
        return self::isAppOlderThan($ios_min_version, '5.4.9');
    }

    /**
     * 是否为概念版本
     */
    public static function isConceptVersion(): bool
    {
        $channel = Yii::$app->equip->getChannel();
        return strpos($channel, self::CHANNEL_CONCEPT) === 0
            || strpos($channel, self::CHANNEL_CONCEPT_64BIT) === 0;
    }

    /**
     * 检查是否为灰度版本
     *
     * @param int $ratio 灰度比例，取值范围 0 ~ 100，即 0% ~ 100%
     * @return bool true：灰度；false：非灰度
     */
    public function isBeta(int $ratio): bool
    {
        if (!$this->equip_id) {
            // 无 equip_id 时，为 /ci/build-params 来源的请求，此时视作非灰度
            return false;
        }
        return (crc32($this->equip_id) % 100) < $ratio;
    }

    /**
     * 获取设备激活时间
     * NOTICE: 对于安卓 >= 6.1.7、iOS >= 6.1.8 的客户端版本，激活时间从 device_token 中获取，该信息存在窜改风险
     * 对于重要业务（如支付相关）判断激活时间，需要从缓存（激活 7 天内）或 install_buvid 数据表获取
     *
     * @param bool $from_cache 是否直接从缓存（新设备锁）中获取激活时间
     * @return int 设备激活时间（单位：秒）
     * 直接从设备新人锁缓存中获取时，若无缓存，返回 0；
     * 不直接从设备新人锁缓存中获取时，当设备无 device_token 且无设备新人锁时，返回 0
     */
    public function getActivateTime(bool $from_cache = false): int
    {
        if ($from_cache || is_null($this->activate_time)) {
            $redis = Yii::$app->redis;
            // NOTICE: 目前仅 7 * 24h 内新安装的设备有值，其他的默认为 0
            $this->activate_time = (int)$redis->get($redis->generateKey(KEY_NEW_DEVICE_EQUIP_ID, $this->getEquipId()));
        }
        return (int)$this->activate_time;
    }

    /**
     * 判断设备是否是新设备
     * 初次安装时间距当前小于 7 * 24h 的设备视作新设备
     *
     * @return bool
     */
    public function isNewEquipment()
    {
        $activate_time = $this->getActivateTime();
        if (!$activate_time) {
            // 没有设备激活时间（device_token 信息不存在并且设备新人锁不存在），说明是老设备
            return false;
        }
        $now = $_SERVER['REQUEST_TIME'];
        // 新设备：距首次激活 <= 7 * 24h 的设备
        $is_new_equipment = ($now - $activate_time) <= ONE_WEEK;
        if (Equipment::isAppOlderThan('6.1.8', '6.1.7')) {
            // 安卓 < 6.1.7、iOS < 6.1.8 版本，只能通过设备锁缓存获取 activate_time，可直接返回
            return $is_new_equipment;
        }
        // 若为安卓 >= 6.1.7、iOS >= 6.1.8 的版本，equip 组件初始化会通过 device_token 获取到 activate_time
        // 若为新人，为了避免伪造 device_token 信息的情况，需要使用新设备锁缓存中的时间再次验证是否为新设备
        if ($is_new_equipment) {
            $activate_time = $this->getActivateTime(true);
            if (!$activate_time) {
                // 缓存中没有设备激活时间，说明是老设备
                return false;
            }
            $is_new_equipment = ($now - $activate_time) <= ONE_WEEK;
        }
        return $is_new_equipment;
    }

    /**
     * 检查并删除新人锁
     */
    public function checkAndRemoveNewEquipmentFlag()
    {
        // 新设备登录了老用户账号（注册时长 > 7 * 24h 的账号），则该设备记为老设备，删除新人锁
        if (Yii::$app->user->id && ($_SERVER['REQUEST_TIME'] - Yii::$app->user->registerAt) > ONE_WEEK) {
            $redis = Yii::$app->redis;
            $redis->del($redis->generateKey(KEY_NEW_DEVICE_EQUIP_ID, $this->getEquipId()));
        }
    }

    /**
     * 生成 device token 并返回
     *
     * @param int $activate_time 新设备安装激活时间点，单位：秒
     * @param string $equip_id 设备号
     * @return string device token
     * @link https://info.missevan.com/pages/viewpage.action?pageId=109696216
     */
    public static function generateDeviceToken(int $activate_time, string $equip_id): string
    {
        $device_info = [
            'activate_time' => $activate_time,
            'v' => self::DEVICE_TOKEN_VERSION,
        ];
        $device_info_binary = MUtils2::encrypt(Json::encode($device_info), $equip_id, DEVICE_TOKEN_KEY,
            'aes-256-cbc', true);
        $base62 = new Base62();
        $device_info_base62 = $base62->encode($device_info_binary);
        // device_token = v + $version + | + $device_info_base62
        return 'v' . self::DEVICE_TOKEN_VERSION . '|' . $device_info_base62;
    }

    /**
     * 获取 device token 中包含的设备信息
     *
     * @param string $device_token
     * @return null|array
     * @link https://info.missevan.com/pages/viewpage.action?pageId=109696216
     */
    private function getDeviceInfoByToken(string $device_token): ?array
    {
        try {
            if (!preg_match('/^v(\d+)\|(.*)/', $device_token, $matches)) {
                throw new Exception('device token 非法');
            }
            if (count($matches) !== 3) {
                throw new Exception('device token 非法');
            }
            $device_token_version = (int)$matches[1];
            if ($device_token_version !== self::DEVICE_TOKEN_VERSION) {
                // 当前仅一个 device token 数据版本，若版本不对，则不解析
                throw new Exception('device token 数据版本非法');
            }
            $base62 = new Base62();
            $device_info_binary = $base62->decode($matches[2]);
            if (!$device_info_binary) {
                throw new Exception('device token base62 decode 失败');
            }
            $device_info_json = MUtils2::decrypt($device_info_binary, $this->equip_id, DEVICE_TOKEN_KEY,
                'aes-256-cbc', true);
            if (!$device_info_json) {
                throw new Exception('device token 解密失败');
            }
            $device_info = Json::decode($device_info_json);
            if (!key_exists('v', $device_info) || (int)$device_info['v'] !== self::DEVICE_TOKEN_VERSION) {
                throw new Exception('device token 数据版本非法');
            }
            return $device_info;
        } catch (Exception $e) {
            Yii::warning("解析设备（{$this->equip_id}）device token（{$device_token}）错误："
                . $e->getMessage(), __METHOD__);
            return null;
        }
    }

    /**
     * 返回请求的设备种类
     *
     * @return int
     */
    public function getDeviceType(): int
    {
        $device_type = self::DEVICE_TYPE_UNKNOWN;
        if ($this->isFromApp()) {
            $dd = new DeviceDetector($this->user_agent);
            $dd->parse();
            $device_type = $dd->isTablet() ? self::DEVICE_TYPE_PAD : self::DEVICE_TYPE_PHONE;
        }
        // TODO: 若之后需要区分来源于非 App 的设备，再做调整
        return $device_type;
    }

    /**
     * 是否为不支持点播会员的客户端版本
     *
     * @return bool
     */
    public static function isAppOlderThanVipVersion()
    {
        // TODO: 鸿蒙确认版本号后再修改成正确的会员版本号
        return self::isAppOlderThan('6.3.6', '6.3.6', '9.9.9');
    }
}
