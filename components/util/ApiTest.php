<?php
/**
 * <AUTHOR>
 */

namespace app\components\util;

use Yii;
use yii\helpers\ArrayHelper;

class ApiTest
{
    /**
     * 用于客户端 API 对于错误值的类型的容错处理
     * 客户端必须在任意数据反馈的情况下不崩溃，需要对每个 API 有个返回数据的 scheme
     * 对于错误的数据类型可以不予正常解析
     */

    // API 错误格式兼容性测试
    const API_TEST_TYPE_REMOVE = ':remove:';
    const API_TEST_NONE = 0;
    const API_TEST_STRING_TO_NULL = 1;
    const API_TEST_STRING_TO_STRING = 2;
    const API_TEST_STRING_TO_INT = 3;
    const API_TEST_STRING_TO_FLOAT = 4;
    const API_TEST_STRING_TO_BOOL = 5;
    const API_TEST_STRING_TO_RANDOM = 6;
    const API_TEST_STRING_REMOVE = 7;
    const API_TEST_INT_TO_NULL = 8;
    const API_TEST_INT_TO_STRING = 9;
    const API_TEST_INT_TO_INT = 10;
    const API_TEST_INT_TO_FLOAT = 11;
    const API_TEST_INT_TO_BOOL = 12;
    const API_TEST_INT_TO_RANDOM = 13;
    const API_TEST_INT_REMOVE = 14;
    const API_TEST_BOOL_TO_NULL = 15;
    const API_TEST_BOOL_TO_STRING = 16;
    const API_TEST_BOOL_TO_INT = 17;
    const API_TEST_BOOL_TO_FLOAT = 18;
    const API_TEST_BOOL_TO_BOOL = 19;
    const API_TEST_BOOL_TO_RANDOM = 20;
    const API_TEST_BOOL_REMOVE = 21;
    const API_TEST_ARRAY_TO_NULL = 22;
    const API_TEST_ARRAY_TO_FIRST = 23;
    const API_TEST_ARRAY_TO_MAP = 24;
    const API_TEST_ARRAY_TO_RANDOM = 25;
    const API_TEST_ARRAY_REMOVE = 26;
    const API_TEST_MAP_TO_NULL = 27;
    const API_TEST_MAP_TO_FIRST = 28;
    const API_TEST_MAP_TO_ARRAY = 29;
    const API_TEST_MAP_TO_RANDOM = 30;
    const API_TEST_MAP_REMOVE = 31;
    const API_TEST_BASIC_TO_RANDOM = 32;
    const API_TEST_STEP_COUNT = 33;

    /**
     * 返回随机数据
     *
     * @param mixed $info 原始数据
     * @return mixed 随机数据值
     */
    private static function randomData($info)
    {
        switch (rand(0, 2)) {
            case 0:
                return $info;
            case 1:
                return [1, 2, self::randomData($info)];
            case 2:
                $map = [];
                $map[MUtils::randomKeys(5, 7)] = self::randomData($info);
                $map[MUtils::randomKeys(5, 7)] = MUtils::randomKeys(5, 7);
                return $map;
        }
        return $info;
    }

    /**
     * 生成任意数据
     *
     * @param mixed $info 原始数据
     * @return mixed 修改后的数据
     */
    public static function generateArbitraryData($path, $info, $step = -1, $depth = 0)
    {
        if ($step === -1) {
            $step = Yii::$app->redis->hIncrBy(KEY_API_TEST_STEP, $path, 1) % self::API_TEST_STEP_COUNT;
        }
        if (is_array($info)) {
            $indexed = ArrayHelper::isIndexed($info);
            if ($indexed) {
                switch ($step) {
                    case self::API_TEST_ARRAY_TO_NULL:
                        if ($depth >= rand(0, 3)) {
                            return null;
                        }
                        break;
                    case self::API_TEST_ARRAY_TO_FIRST:
                        return $info[0] ?? null;
                    case self::API_TEST_ARRAY_TO_MAP:
                        $map = [];
                        foreach ($info as $value) {
                            $map[MUtils::randomKeys(5, 7)] = $value;
                        }
                        if (empty($map)) {
                            $map[MUtils::randomKeys(5, 7)] = MUtils::randomKeys(5, 7);
                        }
                        return $map;
                    case self::API_TEST_ARRAY_TO_RANDOM:
                        return self::randomData($info);
                    case self::API_TEST_ARRAY_REMOVE:
                        if ($depth >= rand(0, 3)) {
                            return self::API_TEST_TYPE_REMOVE;
                        }
                        break;
                }
            } else {
                switch ($step) {
                    case self::API_TEST_MAP_TO_NULL:
                        if ($depth >= rand(0, 3)) {
                            return null;
                        }
                        break;
                    case self::API_TEST_MAP_TO_FIRST:
                        return current($info);
                    case self::API_TEST_MAP_TO_ARRAY:
                        $list = [];
                        foreach ($info as $value) {
                            $list[] = $value;
                        }
                        return $list;
                    case self::API_TEST_MAP_TO_RANDOM:
                        return self::randomData($info);
                    case self::API_TEST_MAP_REMOVE:
                        if ($depth >= rand(0, 3)) {
                            return self::API_TEST_TYPE_REMOVE;
                        }
                        break;
                }
            }
            foreach ($info as $key => $value) {
                $info[$key] = self::generateArbitraryData($path, $value, $step, $depth + 1);
                if ($info[$key] === self::API_TEST_TYPE_REMOVE) {
                    unset($info[$key]);
                }
            }
            if ($indexed) {
                $list = [];
                foreach ($info as $value) {
                    $list[] = $value;
                }
                return $list;
            }
        }
        if ($step === self::API_TEST_BASIC_TO_RANDOM) {
            return self::randomData($info);
        } elseif (is_string($info)) {
            switch ($step) {
                case self::API_TEST_STRING_TO_NULL:
                    return null;
                case self::API_TEST_STRING_TO_BOOL:
                    return rand(0, 1) === 0;
                case self::API_TEST_STRING_TO_STRING:
                    return MUtils::randomKeys(5, 7);
                case self::API_TEST_STRING_TO_INT:
                    return rand(PHP_INT_MIN, PHP_INT_MAX);
                case self::API_TEST_STRING_TO_FLOAT:
                    return rand(PHP_INT_MIN, PHP_INT_MAX) / 1000;
                case self::API_TEST_STRING_TO_RANDOM:
                    return self::randomData($info);
                case self::API_TEST_STRING_REMOVE:
                    return self::API_TEST_TYPE_REMOVE;
            }
        } elseif (is_int($info)) {
            switch ($step) {
                case self::API_TEST_INT_TO_NULL:
                    return null;
                case self::API_TEST_INT_TO_BOOL:
                    return rand(0, 1) === 0;
                case self::API_TEST_INT_TO_STRING:
                    return MUtils::randomKeys(5, 7);
                case self::API_TEST_INT_TO_INT:
                    return rand(PHP_INT_MIN, PHP_INT_MAX);
                case self::API_TEST_INT_TO_FLOAT:
                    return rand(PHP_INT_MIN, PHP_INT_MAX) / 1000;
                case self::API_TEST_INT_TO_RANDOM:
                    return self::randomData($info);
                case self::API_TEST_INT_REMOVE:
                    return self::API_TEST_TYPE_REMOVE;
            }
        } elseif (is_bool($info)) {
            switch ($step) {
                case self::API_TEST_BOOL_TO_NULL:
                    return null;
                case self::API_TEST_BOOL_TO_BOOL:
                    return rand(0, 1) === 0;
                case self::API_TEST_BOOL_TO_STRING:
                    return MUtils::randomKeys(5, 7);
                case self::API_TEST_BOOL_TO_INT:
                    return rand(PHP_INT_MIN, PHP_INT_MAX);
                case self::API_TEST_BOOL_TO_FLOAT:
                    return rand(PHP_INT_MIN, PHP_INT_MAX) / 1000;
                case self::API_TEST_BOOL_TO_RANDOM:
                    return self::randomData($info);
                case self::API_TEST_BOOL_REMOVE:
                    return self::API_TEST_TYPE_REMOVE;
            }
        }
        return $info;
    }
}
