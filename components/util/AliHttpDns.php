<?php

namespace app\components\util;

use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;

class AliHttpDns
{
    /**
     * 获取服务 IP
     *
     * @link https://help.aliyun.com/document_detail/111585.html
     * @param string $region 国家或地区
     * @return array
     * @throws Exception
     */
    public function getServiceIP(string $region = 'hk'): array
    {
        $key = MUtils2::generateCacheKey(KEY_ALIYUN_SERVICE_IP, $region);
        return MUtils2::getOrSetDistrubutedCache($key, function () use ($region) {
                $service = Yii::$app->params['service'];
                $base_uri = $service['httpdns']['aliyun_service_url'];
                $url = $base_uri . '?region=' . $region;
                $res = Yii::$app->tools->requestRemote($url);
                // 请求失败时，会返回具体的错误码，表明错误信息
                if (!$res || array_key_exists('code', $res)) {
                    throw new Exception($res['code'] ?? '服务 IP 调度接口请求失败');
                }
                return $res['service_ip'] ?: [];
            }, ONE_HOUR * 2, function ($e) {
                // 错误信息记录到日志，返回空数组进行服务降级，避免缓存穿透
                Yii::error('aliyun httpdns api error: ' . $e->getMessage(), __METHOD__);
                return [];
            });
    }
}
