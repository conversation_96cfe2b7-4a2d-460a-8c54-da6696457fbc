<?php

namespace app\components\util;

use Exception;
use Firebase\JWT\JWT;
use missevan\util\MUtils;
use Yii;
use yii\helpers\Json;

/**
 * Class AppleAdManagementClient
 * @package app\components\util
 * @link https://developer.apple.com/documentation/apple_search_ads
 * @link https://searchads.apple.com/help/campaigns/0022-use-the-campaign-management-api
 */
class AppleAdManagementClient
{
    /**
     * @var array|self[]
     */
    private static $org_id_instance_map = [];

    /**
     * @var string|null
     */
    private $access_token;
    /**
     * @var string|null
     */
    private $client_id;
    /**
     * @var string|null
     */
    private $team_id;
    /**
     * @var string|null
     */
    private $key_id;
    /**
     * @var string|null
     */
    private $private_key;

    /**
     * @var string|null
     */
    private $org_id;

    /**
     * @var string|null
     */
    private $org_name;

    /**
     * @var string|null
     */
    private $parent_org_id;

    private function __construct($client_id, $team_id, $key_id, $private_key)
    {
        $this->client_id = $client_id;
        $this->team_id = $team_id;
        $this->key_id = $key_id;
        $this->private_key = $private_key;
    }

    public static function instance(int $org_id)
    {
        if (!array_key_exists($org_id, self::$org_id_instance_map)) {
            $config = Yii::$app->params['service']['apple']['apple_search_ads'][$org_id];
            $instance = new self(
                $config['client_id'],
                $config['team_id'],
                $config['key_id'],
                $config['private_key']
            );

            $instance->access_token = $instance->getAccessToken();

            $data = $instance->getACLs();
            $instance->org_id = $data['data'][0]['orgId'];
            $instance->org_name = $data['data'][0]['orgName'];
            $instance->parent_org_id = $data['data'][0]['parentOrgId'];

            self::$org_id_instance_map[$org_id] = $instance;
        }

        return self::$org_id_instance_map[$org_id];
    }

    private function getClientSecret()
    {
        $key = MUtils::generateCacheKey(KEY_APPLE_SEARCH_ADS_CLIENT_SECRET, $this->client_id);
        $memcache = Yii::$app->memcache;
        if ($client_secret = $memcache->get($key)) {
            return $client_secret;
        }

        $now = $_SERVER['REQUEST_TIME'];
        $client_secret = JWT::encode(
            [
                'sub' => $this->client_id,
                'aud' => 'https://appleid.apple.com',
                'iat' => $now,
                'exp' => $now + ONE_WEEK + ONE_HOUR,
                'iss' => $this->team_id,
            ],
            $this->private_key,
            'ES256',
            $this->key_id,
            [
                'alg' => 'ES256',
                'kid' => $this->key_id
            ]
        );

        $memcache->set($key, $client_secret, ONE_WEEK);
        return $client_secret;
    }

    private function getAccessToken()
    {
        $key = MUtils::generateCacheKey(KEY_APPLE_SEARCH_ADS_ACCESS_TOKEN, $this->client_id);
        $memcache = Yii::$app->memcache;
        if ($access_token = $memcache->get($key)) {
            return $access_token;
        }

        $resp = Yii::$app->tools->requestRemoteWithProxy(
            'https://appleid.apple.com/auth/oauth2/token?' . http_build_query([
                'grant_type' => 'client_credentials',
                'client_id' => $this->client_id,
                'client_secret' => $this->getClientSecret(),
                'scope' => 'searchadsorg',
            ]),
            [], 'POST', null, 0, [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ]
        );

        $memcache->set($key, $resp['access_token'], intdiv($resp['expires_in'], 2) ?: HALF_HOUR);
        return $resp['access_token'];
    }

    private function request($api, $method = 'GET')
    {
        $headers = ['Authorization' => 'Bearer ' . $this->access_token];
        if ($this->org_id) {
            $headers['X-AP-Context'] = 'orgId=' . $this->org_id;
        }
        try {
            return Yii::$app->tools->requestRemoteWithProxy($api, [], $method, null, 0, $headers);
        } catch (Exception $e) {
            Yii::error('apple ad management api error: ' . $e->getMessage(), __METHOD__);
            return null;
        }
    }

    /**
     * @link https://developer.apple.com/documentation/apple_search_ads/get_user_acl
     */
    private function getACLs()
    {
        $key = MUtils::generateCacheKey(KEY_APPLE_SEARCH_ADS_ACL_DATA, $this->client_id);
        $memcache = Yii::$app->memcache;
        $acl_cache = $memcache->get($key);
        if ($acl_cache && $acl = Json::decode($acl_cache)) {
            return $acl;
        }

        $acl = $this->request('https://api.searchads.apple.com/api/v5/acls');
        $memcache->set($key, Json::encode($acl), ONE_WEEK);
        return $acl;
    }

    public function getCampaign($campaign_id)
    {
        return $this->request(sprintf('https://api.searchads.apple.com/api/v5/campaigns/%s', $campaign_id));
    }

    public function getAdGroup($campaign_id, $ad_group_id)
    {
        return $this->request(sprintf('https://api.searchads.apple.com/api/v5/campaigns/%s/adgroups/%s', $campaign_id, $ad_group_id));
    }

    public function getCreativeSet($creative_set_id)
    {
        return $this->request(sprintf('https://api.searchads.apple.com/api/v5/creativesets/%s', $creative_set_id));
    }

    public function getKeyword($campaign_id, $ad_group_id, $keyword_id)
    {
        return $this->request(sprintf('https://api.searchads.apple.com/api/v5/campaigns/%s/adgroups/%s/targetingkeywords/%s', $campaign_id, $ad_group_id, $keyword_id));
    }

    public function getCampaignName($campaign_id)
    {
        if (!$data = $this->getCampaign($campaign_id)) {
            return null;
        }
        return $data['data']['name'];
    }

    public function getAdGroupName($campaign_id, $ad_group_id)
    {
        if (!$data = $this->getAdGroup($campaign_id, $ad_group_id)) {
            return null;
        }
        return $data['data']['name'];
    }

    public function getCreativeSetName($creative_set_id)
    {
        if (!$data = $this->getCreativeSet($creative_set_id)) {
            return null;
        }
        return $data['data']['name'];
    }

    public function getKeywordName($campaign_id, $ad_group_id, $keyword_id)
    {
        if (!$data = $this->getKeyword($campaign_id, $ad_group_id, $keyword_id)) {
            return null;
        }
        return $data['data']['text'];
    }

    public static function getDefaultOrgId()
    {
        return Yii::$app->params['service']['apple']['apple_search_ads']['default_org_id'];
    }

    public function getOrgId()
    {
        return $this->org_id;
    }

    public function getOrgName()
    {
        return $this->org_name;
    }

    public function getParentOrgId()
    {
        return $this->parent_org_id;
    }

}
