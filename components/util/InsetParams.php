<?php

namespace app\components\util;

use ArrayAccess;
use Exception;
use yii\base\Component;
use Yii;

/**
 * 内置参数类
 *
 * 模拟 Yii::$app->params['name'] 的调用，内含值为无需改动的参数。
 *
 * 当 params.php 内的参数与 insetparams.php 结构一致时，移除本类，
 * 转而采用 array_merge 或更好的方法，将参数都放在 params.php 中。
 *
 * @package app\components\util
 */
class InsetParams extends Component implements ArrayAccess
{
    public $params;

    protected $offsets = [];
    protected $props = [];

    public function init(array $config = [])
    {
        parent::init();
        foreach ($this->params as $key => $value) {
            $this->offsets[$key] = $value;
        }
    }

    /**
     * 覆盖 Component::__set
     *
     * @param string $key 不存在的属性名
     * @param mixed $value 属性值
     * @throws Exception
     */
    public function __set($key, $value)
    {
        throw new Exception('InsetParams properties readonly');
    }

    /**
     * 覆盖 Component::__get
     *
     * @param string $name 不存在的属性名
     * @return mixed|null
     */
    public function __get($name)
    {
        if (!key_exists($name, $this->props)) {
            $config_file = CONFIG_PATH . '/' . $name . '.php';
            if (file_exists($config_file)) {
                $this->props[$name] = require($config_file);
            } else {
                $this->props[$name] = null;
            }
        }
        return $this->props[$name];
    }

    /**
     * ArrayAccess 接口方法
     *
     * {@inheritdoc}
     */
    public function offsetExists($offset)
    {
        return key_exists($offset, $this->offsets);
    }

    /**
     * ArrayAccess 接口方法
     *
     * {@inheritdoc}
     */
    public function offsetGet($offset)
    {
        return $this->offsets[$offset];
    }

    /**
     * ArrayAccess 接口方法
     *
     * {@inheritdoc}
     */
    public function offsetSet($offset, $value)
    {
        throw new Exception('InsetParams offsets readonly');
    }

    /**
     * ArrayAccess 接口方法
     *
     * {@inheritdoc}
     */
    public function offsetUnset($offset)
    {
        throw new Exception('InsetParams offsets readonly');
    }
}
