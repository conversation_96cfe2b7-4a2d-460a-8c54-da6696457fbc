<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/12
 * Time: 16:29
 */

namespace app\components\util;

use Imagick;
use yii\web\HttpException;

class Image
{
    // 图片格式（是 Imagick::getImageFormat() 方法的结果）
    const FORMAT_GIF = 'GIF';
    const FORMAT_BMP = 'BMP';
    const FORMAT_PNG = 'PNG';
    const FORMAT_JPEG = 'JPEG';
    const FORMAT_WEBP = 'WEBP';
    // 图片格式数组
    const IMAGE_FORMAT_LIST = ['PNG', 'JPEG', 'GIF', 'WEBP'];

    public static function mosaic(string $image_path, string $mosaic_path)
    {
        // TODO: 完善给图片打码方法
        $imagick = new Imagick($image_path);
        $imagick->clear();
    }

    /**
     * 判断文件是否是图片文件
     *
     * @param string $image_format 文件格式名
     * @return bool 是图片文件则返回 true
     * @throws HttpException
     */
    public static function isImageFile($image_format)
    {
        if (!in_array($image_format, self::IMAGE_FORMAT_LIST)) {
            throw new HttpException(400, '不支持该图片格式');
        }
        return true;
    }

    /**
     * 调整图片大小
     *
     * @param string $image_path 原来图片的地址
     * @param int $default_short_edge 默认最短边长
     * @param int $default_long_edge 默认最长边长
     * @return array 调整大小后图片的地址和处理过的文件扩展名
     * @throws \Exception
     */
    public static function resizeImage(string $image_path, $default_short_edge, $default_long_edge)
    {
        $imagick = new Imagick($image_path);
        try {
            $image_width = $imagick->getImageWidth();
            $image_height = $imagick->getImageHeight();

            // 获取图片的长边和短边
            [$image_short_edge, $image_long_edge] = $image_width > $image_height ?
                [$image_height, $image_width] : [$image_width, $image_height];

            // 获取压缩后的短边和长边
            if ($image_short_edge <= $default_short_edge) {
                if ($image_long_edge >= $default_long_edge) {
                    $target_long_edge = $default_long_edge;
                    $target_short_edge = round($image_short_edge * ($image_long_edge / $default_long_edge));
                } else {
                    $target_short_edge = $default_short_edge;
                    $target_long_edge = round($image_long_edge * ($image_short_edge / $default_short_edge));
                }
            } elseif ($image_long_edge >= $default_long_edge) {
                $target_long_edge = $default_long_edge;
                $target_short_edge = round($image_short_edge * ($image_long_edge / $default_long_edge));
            } else {
                $target_long_edge = $image_long_edge;
                $target_short_edge = $image_short_edge;
            }

            // 缩略图文件地址
            $path_info = pathinfo($image_path);
            if (!isset($path_info['dirname']) || !isset($path_info['filename'])) {
                throw new HttpException(400, '图片文件路径错误');
            }
            $file_path = $path_info['dirname'] . '/' . $path_info['filename'];
            // 获取图片格式
            $image_format = $imagick->getImageFormat();
            if (self::FORMAT_WEBP === $image_format) {
                // WEBP 格式转换为 PNG 格式存储
                $imagick->setImageFormat(self::FORMAT_PNG);
                $image_format = self::FORMAT_PNG;
            }
            // 获取处理过的文件扩展名
            $extension = self::getImageExtension($image_format);
            // 获取新的文件路径
            $target_path = $file_path . '-mini_image' . $extension;

            // 缩略图片
            if ($image_format === self::FORMAT_GIF) {
                // 处理 GIF 格式的图片
                $images = $imagick->coalesceImages();
                foreach ($images as $image) {
                    $image->thumbnailImage($target_short_edge, $target_long_edge, true);
                }
                $imagick = $imagick->deconstructImages();
                // 生成缩略图
                $imagick->writeImages($target_path, true);
            } else {
                $imagick->thumbnailImage($target_short_edge, $target_long_edge, true);
                // 生成缩略图
                $imagick->writeImage($target_path);
            }
            return [$target_path, $extension, $image_width, $image_height];
        } catch (\Exception $e) {
            throw $e;
        } finally {
            $imagick->clear();
        }
    }

    /**
     * 获取图片处理过的扩展名
     *
     * @param string $image_format 图片格式
     * @return string 处理过的文件扩展名
     * @throws HttpException
     */
    public static function getImageExtension($image_format)
    {
        // 检查文件是否为图片格式
        if (self::isImageFile($image_format)) {
            switch ($image_format) {
                case self::FORMAT_JPEG:
                    return '.jpg';
                case self::FORMAT_PNG:
                    return '.png';
                case self::FORMAT_WEBP:
                    return '.webp';
                case self::FORMAT_GIF:
                    return '.gif';
                default:
                    throw new HttpException(400, '不支持该图片格式');
            }
        }
    }

    /**
     * 转换图片格式
     *
     * @param string $image_path 原来图片的地址
     * @param string $format 要转换的图片格式，如 jpg
     * @return string 调整后缀名后图片的地址
     * @throws \Exception 图片文件不存在或转格式错误时抛出异常
     */
    public static function convertImageFormat(string $image_path, string $format): string
    {
        $path_info = pathinfo($image_path);
        if (!is_file($image_path) || !isset($path_info['dirname']) || !isset($path_info['filename'])) {
            throw new \Exception('图片文件路径错误');
        }
        $imagick = new Imagick($image_path);
        try {
            if ($imagick->getImageFormat() === strtoupper($format)) {
                // 若原文件格式与目标格式相同，则不进行转换
                return $image_path;
            }
            $imagick->setImageFormat($format);
            // 获取文件扩展名
            $extension = self::getImageExtension($format);
            $new_file_path = $path_info['dirname'] . '/' . $path_info['filename'] . $extension;
            // 生成新的图片
            $imagick->writeImage($new_file_path);
            return $new_file_path;
        } catch (\Exception $e) {
            throw $e;
        } finally {
            $imagick->clear();
        }
    }
}
