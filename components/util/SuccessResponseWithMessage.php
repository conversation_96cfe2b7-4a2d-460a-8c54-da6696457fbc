<?php

namespace app\components\util;

class SuccessResponseWithMessage
{
    private $_data = null;
    private $_message = '';

    public function __construct($data, string $message = '')
    {
        $this->_data = $data;
        $this->_message = $message;
    }

    /**
     * @return mixed
     */
    public function getData()
    {
        return $this->_data;
    }

    /**
     * @return string
     */
    public function getMessage(): string
    {
        return $this->_message;
    }

}
