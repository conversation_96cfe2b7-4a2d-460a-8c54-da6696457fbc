<?php
/**
 * 人机验证类
 *
 * <AUTHOR>
 */

namespace app\components\util;

use Yii;
use Exception;
use DefaultProfile;
use DefaultAcsClient;
use yii\base\Component;
use afs\Request\V20180112\AuthenticateSigRequest;

class Captcha extends Component
{
    private static $client;
    public $accessKeyId;
    public $accessKeySecret;
    public $appKey;
    public $slideAppKey; // 滑动验证 AppKey
    public $regionId;

    // 接口返回值 100：验签通过；900：验签失败
    const SUCCESS = 100;
    const ERROR = 900;

    // 登录场景
    const SCENEORIGINAL_LOGIN = 'login';
    // 发送短信场景
    const SCENEORIGINAL_MESSAGE = 'message';
    // 其他场景
    const SCENEORIGINAL_OTHER = 'other';

    // 登录接口允许请求的次数
    const LIMIT_REQUEST = 5;

    public function init()
    {
        if (!self::$client) {
            $iclient_profile = DefaultProfile::getProfile(
                $this->regionId,
                $this->accessKeyId,
                $this->accessKeySecret
            );
            DefaultProfile::addEndpoint(
                $this->regionId,
                $this->regionId,
                'afs',
                'afs.aliyuncs.com'
            );
            self::$client = new DefaultAcsClient($iclient_profile);
        }
    }

    public static function getCaptchaToken()
    {
        return urldecode(Yii::$app->request->headers['x-m-captcha']);
    }

    /**
     * 获取滑动验证参数
     *
     * @return array 从请求头中解析出的滑动验证参数
     * @throws Exception 从请求头中解析出的滑动验证参数出现问题
     */
    public static function getSlideParams()
    {
        // x-m-captcha: session_id|token|sig
        $captcha_str = self::getCaptchaToken();
        if (!$captcha_str) return [];

        $captcha_arr = explode('|', $captcha_str);
        if (count($captcha_arr) !== 3 || !$captcha_arr[0] || !$captcha_arr[1] || !$captcha_arr[2]) {
            throw new Exception('参数错误');
        }
        return $captcha_arr;
    }

    /**
     * 人机验证（滑动验证）
     *
     * @param string $session_id 滑动验证的验证会话 ID
     * @param string $token 请求唯一标识
     * @param string $sig 签名串
     * @param string $scene 使用场景
     * @return boolean true or false 验证的结果
     * @throws Exception 请求阿里云人机验证接口出现问题
     */
    public function verifyCaptcha(string $session_id, string $token, string $sig, string $scene)
    {
        $scene = 'nc_' . $scene . '_h5';

        $request = new AuthenticateSigRequest();
        $request->setSessionId($session_id);
        $request->setToken($token);
        $request->setSig($sig);
        $request->setScene($scene);
        $request->setAppKey($this->slideAppKey);
        $request->setRemoteIp(Yii::$app->request->userIP);

        try {
            $response = self::$client->getAcsResponse($request);
            return $response;
        } catch (Exception $e) {
            throw new Exception('网络错误，请稍后再试');
        }
    }

    /**
     * 是否需要启用滑动验证
     * 同个 IP 在 1 小时内分别请求登录、发送验证码等接口超过 5 次，在请求第 6 次时打开滑动验证
     * 同个 equip_id 在 1 小时内请求登录接口超过 5 次，在请求第 6 次时打开滑动验证
     *
     * @param string $scene 使用场景
     * @return boolean true or false 是否需要启用滑动验证
     */
    public static function isRequired($scene)
    {
        $check_ip_pass = $check_equip_id_pass = true;
        switch ($scene) {
            case self::SCENEORIGINAL_LOGIN:
                $equipment = Yii::$app->equip;
                if ($equipment->isValidEquipId) {
                    $check_equip_id_pass = self::checkEquipId($scene, $equipment->getEquipId());
                } else {
                    $check_equip_id_pass = false;
                }
                // fallthrough
            case self::SCENEORIGINAL_MESSAGE:
                // fallthrough
            case self::SCENEORIGINAL_OTHER:
                $check_ip_pass = self::checkIp($scene, Yii::$app->request->userIP);
                break;
            default:
                throw new \Exception('验证场景错误');
        }

        return !$check_ip_pass || !$check_equip_id_pass;
    }

    private static function checkIp(string $scene, string $ip)
    {
        $redis = Yii::$app->redis;
        $ip_key = $redis->generateKey(KEY_COUNTER_CAPTCHA_SCENE_IP, $scene, $ip);
        $ip_req_num = (int)$redis->incr($ip_key);
        if ($ip_req_num === 1) {
            $redis->expire($ip_key, ONE_HOUR);
        } elseif ($ip_req_num > self::LIMIT_REQUEST) {
            return false;
        }
        return true;
    }

    private static function checkEquipId(string $scene, string $equip_id)
    {
        $redis = Yii::$app->redis;
        $equip_id_key = $redis->generateKey(KEY_COUNTER_CAPTCHA_SCENE_EQUIP_ID, $scene, $equip_id);
        $equip_id_req_num = (int)$redis->incr($equip_id_key);
        if ($equip_id_req_num === 1) {
            $redis->expire($equip_id_key, ONE_HOUR);
        } elseif ($equip_id_req_num > self::LIMIT_REQUEST) {
            return false;
        }
        return true;
    }

    /**
     * 删除 key
     *
     * @param string $scene 使用场景
     */
    public function resetCounter(string $scene)
    {
        $redis = Yii::$app->redis;
        $keys = [$redis->generateKey(KEY_COUNTER_CAPTCHA_SCENE_IP, $scene, Yii::$app->request->userIP)];
        if ($scene === self::SCENEORIGINAL_LOGIN) {
            $keys[] = $redis->generateKey(KEY_COUNTER_CAPTCHA_SCENE_EQUIP_ID, $scene, Yii::$app->equip->getEquipId());
        }
        Yii::$app->redis->del(...$keys);
    }
}
