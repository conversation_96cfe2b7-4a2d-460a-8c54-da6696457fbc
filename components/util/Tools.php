<?php

namespace app\components\util;

use Yii;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use yii\helpers\Json;
use yii\helpers\ArrayHelper;
use yii\web\HttpException;

class Tools
{
    // 发送短信
    const SEND_SMS = 1;
    // 发送邮件
    const SEND_EMAIL = 2;
    // 推送消息
    const SEND_PUSH = 3;
    // 发送私信
    const SEND_PM = 4;
    // 发送系统通知
    // NOTICE: 需要注意 html encode
    const SEND_SYS_MSG = 5;

    // 接口调用成功 code
    const CODE_SUCCESS = 0;

    // 私信发送类型为失败
    const PM_TYPE_FAIL = 'failed-push';

    public function requestApi(string $url, $data, $base_uri, $key = MISSEVAN_PRIVATE_KEY)
    {
        $data = base64_encode(Json::encode($data));
        $timestamp = time();
        $sign = hash_hmac('sha256', "$data $timestamp", $key);
        $body = "$data $sign $timestamp";
        $client = new Client([
            'base_uri' => $base_uri,
            'timeout' => 15.0,
        ]);
        try {
            $headers = [
                'Accept' => 'application/json',
                'X-Forwarded-For' => Yii::$app->request->userIP ?? '', // 通过命令行脚本 ./yii 执行时获取不到 IP
                'Content-Type' => 'text/plain',
                'User-Agent' => Yii::$app->name . '/' . Yii::$app->version,
            ];
            if (isset($_SERVER['HTTP_X_M_SWIMLANE'])) {
                $headers['X-M-Swimlane'] = $_SERVER['HTTP_X_M_SWIMLANE'];
            }
            $cookie = [
                'equip_id=' . Yii::$app->equip->getEquipId(),
                'buvid=' . Yii::$app->equip->getBuvid(),
            ];
            if ($token = Yii::$app->request->cookies->getValue('token')) {
                $cookie[] = 'token=' . $token;
            }
            $headers['Cookie'] = implode('; ', $cookie);
            $res = $client->request('POST', $url, [
                'headers' => $headers,
                'body' => $body,
            ]);
        } catch (ClientException $e) {
            $res = $e->getResponse();
        }
        $body = $res->getBody();
        $content = $body->getContents();
        $result = json_decode($content, true);
        if (is_array($result)) $result['status'] = $res->getStatusCode();
        return $result;
    }

    public function sendNotification($notifications, $action_type)
    {
        $pushserver_url = Yii::$app->params['service']['pushserver']['url'];
        $pushserver_key = Yii::$app->params['service']['pushserver']['key'];
        if (ArrayHelper::isAssociative($notifications)) {
            $notifications = [$notifications];
        }

        try {
            switch ($action_type) {
                case self::SEND_SMS:
                    $res = $this->requestApi('/api/sms', ['smses' => $notifications], $pushserver_url, $pushserver_key);
                    break;
                case self::SEND_EMAIL:
                    $res = $this->requestApi('/api/email', ['emails' => $notifications], $pushserver_url, $pushserver_key);
                    break;
                case self::SEND_PUSH:
                    $res = $this->requestApi('/api/push', ['notifications' => $notifications,], $pushserver_url, $pushserver_key);
                    break;
                case self::SEND_PM:
                    $res = $this->requestApi('/api/pm', ['pm' => $notifications],
                        $pushserver_url, $pushserver_key);
                    break;
                case self::SEND_SYS_MSG:
                    $res = $this->requestApi('/api/systemmsg', ['systemmsgs' => $notifications],
                        $pushserver_url, $pushserver_key);
                    break;
                default:
                    throw new \Exception('sendNotification 参数错误');
            }
            if ($res['code'] !== self::CODE_SUCCESS) {
                throw new HttpException($res['status'], $res['info'], $res['code']);
            }
        } catch (\Exception $e) {
            Yii::error("Request mpush rpc api fail, action type: {$action_type}, {$e->getMessage()}",
                __METHOD__);
            $res = null;
        }
        return $res;
    }

    public function requestDrama($api, $params = [], $method = 'GET')
    {
        $_service = Yii::$app->params['service'];
        if (0 === strpos($api, '/rpc/')) {
            try {
                $content = $this->requestApi($api, $params, $_service['drama']['url'], $_service['drama']['key']);
            } catch (\Exception $e) {
                Yii::error("Request drama rpc api fail: {$e->getMessage()}", __METHOD__);
                $content = null;
            }
            if ($content) {
                if (0 === $content['code']) return $content['info'];
                throw new HttpException($content['status'], $content['info'], $content['code']);
            }
            throw new HttpException(500, '网络错误');
        } else {
            try {
                $domain = $_service['drama']['url'];
                $content = $this->requestRemote($domain . $api, $params, $method);
            } catch (\Exception $e) {
                $content = null;
            }
            return $content;
        }
    }

    /**
     * 请求外部资源
     *
     * @param string $url
     * @param array $params
     * @param string $method
     * @param null $raw_data
     * @param int $timeout
     * @param array $headers
     * @param integer $status_code
     * @param bool $with_proxy
     * @return mixed|null
     * @throws \GuzzleHttp\Exception\GuzzleException|\Exception
     */
    public function requestRemote($url, $params = [], $method = 'GET', $raw_data = null, $timeout = 0, array $headers = [], int &$status_code = -1, $with_proxy = false)
    {
        $method = strtoupper($method);
        if (!in_array($method, ['POST', 'GET'])) {
            throw new \Exception('参数错误');
        }

        $content = null;
        try {
            $client = new Client();
            $options = [
                'headers' => [
                    'X-Forwarded-For' => Yii::$app->request->userIP ?? '',
                    'User-Agent' => Yii::$app->name . '/' . Yii::$app->version,
                ],
            ];
            if ($headers) {
                $options['headers'] = array_merge($options['headers'], $headers);
            }
            if ('GET' === $method) {
                if ($params) {
                    $options['query'] = $params;
                }
            } else {
                if ($raw_data) {
                    $options['body'] = $raw_data;
                } else {
                    $options['form_params'] = $params;
                }
            }
            if ($timeout) {
                $options['timeout'] = $timeout;
            }
            if ($with_proxy && ($proxy = Yii::$app->params['service']['proxy']['http'] ?? null)) {
                $options['proxy'] = $proxy;
            }
            $res = $client->request($method, $url, $options);
            $status_code = $res->getStatusCode();
            $content = Json::decode($res->getBody(), true);
        } catch (ClientException $e) {
            $status_code = $e->getResponse()->getStatusCode();
            if ($status_code !== 404) {
                Yii::error('request remote error: ' . $e->getMessage(), __METHOD__);
            }
        } catch (\Exception $e) {
            Yii::error('request remote error: ' . $e->getMessage(), __METHOD__);
        }
        return $content;
    }

    /**
     * 通过代理请求外部资源
     *
     * @see Tools::requestRemote()
     */
    public function requestRemoteWithProxy($url, $params = [], $method = 'GET', $raw_data = null, $timeout = 0, array $headers = [], int &$status_code = -1)
    {
        return $this->requestRemote($url, $params, $method, $raw_data, $timeout, $headers, $status_code, true);
    }
}
