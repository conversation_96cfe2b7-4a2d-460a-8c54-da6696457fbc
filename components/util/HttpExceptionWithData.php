<?php

namespace app\components\util;

use yii\web\HttpException;

class HttpExceptionWithData extends HttpException
{

    private $_data = null;

    public function __construct(int $status, $message = null, int $code = 0, $data = null)
    {
        $this->_data = $data;
        parent::__construct($status, $message, $code);
    }

    public function getData()
    {
        return $this->_data;
    }

}
