<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2017/7/27
 * Time: 20:08
 */

namespace app\components\util;

use app\forms\VcodeForm;
use app\models\Drama;
use app\models\MobileNumber;
use app\models\MSoundNode;
use app\models\PaginationParams;
use app\models\ReturnModel;
use app\models\SoundVideo;
use Crypt_Diffie<PERSON>ellman;
use Exception;
use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumberUtil;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\data\Pagination;
use yii\db\ActiveQuery;
use yii\db\Query;
use yii\web\HttpException;

class MUtils
{
    const ACTION_DO = 1;
    const ACTION_UNDO = 0;

    // 屏蔽词类型：
    // 0 搜索违规屏蔽词
    // 1 音单违规屏蔽词
    // 2 搜索热词
    // 3 音频擦边球
    // 4 私信提醒词
    // 5 搜索剧集
    // 6 私信假发送屏蔽词
    const FORBIDDEN_WORD_TYPE_SEARCH = 0;
    const FORBIDDEN_WORD_TYPE_ALBUM = 1;
    const FORBIDDEN_WORD_TYPE_PRIVATE_MESSAGE_FAKE_SEND = 6;

    // 需要脱敏字符串类型 1：真实姓名；2：手机；3：邮箱；4：身份证号；5：银行卡号
    const MOSAIC_NAME = 1;
    const MOSAIC_MOBILE = 2;
    const MOSAIC_EMAIL = 3;
    const MOSAIC_ID_CARD = 4;
    const MOSAIC_BANK_ACCOUNT = 5;

    // 脱敏字符串：手机号开头显示 3 位，结尾显示 2 位；身份证开头显示 2 位，结尾显示 1 位；银行卡只显示后四位
    const SHOW_MOBILE_HEAD_OFFSET = 3;
    const SHOW_MOBILE_TAIL_OFFSET = -2;
    const SHOW_ID_CARD_HEAD_OFFSET = 2;
    const SHOW_ID_CARD_TAIL_OFFSET = -1;
    const SHOW_BANK_ACCOUNT_TAIL_OFFSET = -4;

    // 需要补充长度的字符串类型 1：初始化向量；2：密钥
    const PAD_TYPE_IV = 1;
    const PAD_TYPE_KEY = 2;

    public static $forbidden_keywords = null;

    private static $_geoip_record = null;

    /**
     * 获取文件后缀名
     *
     * @param string $file_path
     * @return string|null
     */
    public static function getFileExtension(string $file_path)
    {
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime = finfo_file($finfo, $file_path);
        switch ($mime) {
            case 'audio/x-m4a':
                return 'm4a';
            case 'audio/mpeg':
                return 'mp3';
            case 'audio/x-wav':
                return 'wav';
            case 'image/jpeg':
                return 'jpg';
            case 'image/gif':
                return 'gif';
            case 'image/png':
                return 'png';
            default:
                return null;
        }
    }

    /**
     * 判断是否为中国大陆地区
     * （其中港澳台 iso_code 值为 HK, MO 及 TW）
     * @link https://dev.maxmind.com/geoip/legacy/codes/iso3166
     * @return bool
     */
    public static function isChinaMainland()
    {
        return self::getCountryCode() === 'CN';
    }

    /**
     * 判断是否为日本
     *
     * @return bool
     */
    public static function isJapan()
    {
        return self::getCountryCode() === 'JP';
    }

    /**
     * 获取国家或地区码
     *
     * @link https://dev.maxmind.com/geoip/legacy/codes/iso3166
     * @return string
     */
    public static function getCountryCode()
    {
        $record = self::getGeoIpRecord();
        return $record['country_code'];
    }

    /**
     * 获取 IP 位置记录
     *
     * @return array
     */
    public static function getGeoIpRecord()
    {
        if (is_null(self::$_geoip_record)) {
            if ($record = Yii::$app->serviceRpc->getLocationInfo(Yii::$app->request->userIP)) {
                self::$_geoip_record = $record;
            } else {
                // 若获取不到对应的 IP 记录，则降级到 CN
                self::$_geoip_record = ['country_code' => 'CN', 'region_name' => ''];
            }
        }
        return self::$_geoip_record;
    }

    /**
     * 获取随机排序的数据
     *
     * @param ActiveQuery $query 查询规则
     * @param int $limit 查询条数 默认值是 10
     * @return array|\yii\db\ActiveRecord[] 查询结果
     */
    public static function getRandomModels(ActiveQuery $query, int $limit = 10)
    {
        $count = $query->count();
        if (!$count || $limit <= 0) {
            return [];
        } elseif ($count < $limit * 10) {
            return $query->orderBy('RAND()')->limit($limit)->all();
        } else {
            $threshold = ($limit / $count) * 3;
            $select = $query->select;

            $query = $query->select('id tt_id')
                ->andWhere(['<', 'RAND()', $threshold])
                ->orderBy('RAND()')
                ->limit($limit);

            return $query->modelClass::find()->alias('tt1')
                ->select($select)
                ->rightJoin('(' . $query->createCommand()->getRawSql() . ') tt2', 'tt1.id = tt2.tt_id')
                ->all();
        }
    }

    //随机数
    public static function randomKeys($length, $type = 1)
    {
        $key = '';
        $pattern = '';
        if ($type & 1)
            $pattern .= '1234567890';
        if ($type & 2)
            $pattern .= 'abcdefghijklmnopqrstuvwxyz';
        if ($type & 4)
            $pattern .= 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $p_length = strlen($pattern) - 1;
        for ($i = 0; $i < $length; $i++) {
            $key .= $pattern[mt_rand(0, $p_length)];
        }
        return $key;
    }

    /**
     * 对数据进行分页
     * @param Query $query 查询对象
     * @param integer $page_size 每页个数
     * @param array $params 额外参数
     *    $params = [
     *      'scenario'     => (string) 场景，默认为 default
     *      'current_page' => (int) 当前页数，首页为 1；若无本参数，则试图读取 Query[page]，还没有则为首页值
     *    ]
     * @param integer $total_count 总个数，若为 -1 表示无总个数需要从数据库获取
     * @param bool $as_array 返回字段 Datas 结果集的类型 false：对象返回；true：数组返回
     * @param \yii\db\Connection $db
     * @param integer $max_page_size 最大每页个数
     * @return ReturnModel
     * @throws Exception
     */
    public static function getPaginationModels(Query $query, int $page_size = DEFAULT_PAGE_SIZE, $params = [],
            int $total_count = -1, bool $as_array = false, $db = null,
            int $max_page_size = PaginationParams::MAX_PAGE_SIZE)
    {
        if ($total_count === -1) {
            $total_count = (int)$query->count('*', $db);
        }
        if ($page_size <= 0) {
            throw new HttpException(400, '参数错误');
        }
        $page = new Pagination([
            'totalCount' => $total_count,
            'defaultPageSize' => $page_size,
        ]);
        $params += ['scenario' => 'default'];
        if (array_key_exists('current_page', $params)) {
            // 实际首页为 0，故需减一
            $page->setPage($params['current_page'] - 1);
        }
        if (0 === $page->page) {
            // 加载第一页的时候，个数最大为 120，之后的则限制为 50
            $page->pageSizeLimit = [1, $max_page_size];
            if ($page_size > PaginationParams::MAX_PAGE_SIZE) {
                $page_size = $max_page_size;
            }
            $page->setPageSize($page_size);
        }
        $list = [];
        if ($total_count > 0) {
            $list = $query->offset($page->offset)
                ->limit($page->limit);
            if ($as_array) {
                $list = $list->asArray()->all($db);
            } else {
                $list = $list->all($db);
                foreach ($list as $item) {
                    $item->setScenario($params['scenario']);
                }
            }
        }

        return ReturnModel::getPaginationData($list, (int)$page->totalCount, $page->getPage() + 1, $page->pageSize);
    }

    /**
     * 根据数组中相同的值进行分组
     *
     * @param array $elems 待分组的数组
     * @param string|integer $group_by 分组标志
     * @param string|integer|null $group_value 要取的值
     * @param callable|null $func 回调处理
     * @return array
     */
    public static function groupArray(array $elems, $group_by, $group_value = null, callable $func = null)
    {
        $elem_group = [];
        foreach ($elems as $elem) {
            $group_key = $elem[$group_by];
            if (!isset($elem_group[$group_key])) {
                $elem_group[$group_key] = [];
            }
            if (is_callable($func)) {
                $elem = $func($elem);
            }
            if ($group_value === null) {
                $elem_group[$group_key][] = $elem;
            } else {
                $elem_group[$group_key][] = $elem[$group_value];
            }
        }
        return $elem_group;
    }

    public static function plainText($text, $length = 0)
    {
        $text = strip_tags($text);
        $text = html_entity_decode($text);
        if ($length) {
            $text = mb_substr($text, 0, $length);
        }
        return $text;
    }

    /**
     * 生成某类型的 key
     * @param string $pattern key 的模式
     * @param array ...$args 模式中替代成数据
     * @return string
     */
    public static function generateCacheKey($pattern, ...$args)
    {
        $str_count = substr_count($pattern, '*');
        $arg_count = count($args);
        if ($str_count !== $arg_count) return null;
        $pattern = str_replace('*', '%s', $pattern);
        $key = sprintf($pattern, ...$args);
        return $key;
    }

    /**
     * 过渡文本中的非法的 HTML 标签
     * @param string $content 待过滤的文本
     * @param  array $config 配置项
     * @return string 过滤后文本
     */
    public static function formatHTML($content, $config = null)
    {
        $config = \HTMLPurifier_Config::create($config);
        $config->set('Cache.SerializerPath', Yii::$app->runtimePath . '/HTMLPurifier/Cache/Serializer');
        $purify = new \HTMLPurifier($config);
        return $purify->purify($content);
    }

    /**
     * 过滤字符串中的 HTML 标签
     * 仅过滤标签，特殊的字符如“<”、“&”等不进行转义
     *
     * @param string $str 需要格式化的 HTML 内容
     * @param array 过滤规则，参考 HTMLPurifier 配置规则
     * @return string
     */
    public static function filterHtml(string $str, $config = ['HTML.Allowed' => '']): string
    {
        if (!$str) {
            return '';
        }
        return htmlspecialchars_decode(MUtils::formatHtml($str, $config));
    }

    /**
     * 组合整型字段的 SQL 查询条件语句
     *
     * @param string $column 字段名
     * @param array $values 字段的值组成的数组
     * @param string $attribute 属性名，当 $values 中的元素为对象时传入此值
     * @return string SQL 条件语句的字符串
     * @throws Exception 构成条件的字段值不全为数字时
     */
    public static function generateIntegerIn(string $column, array $values, string $attribute = '')
    {
        $values = array_filter($values, function ($value) use ($attribute) {
            if (($attribute && !is_numeric($value->$attribute)) || (!$attribute && !is_numeric($value))) {
                throw new \Exception('查询的字段值需都为数字');
            }
            return true;
        });
        if (($n = count($values)) < 1) {
            $condition = '0 = 1';
        } elseif (1 === $n) {
            $values = reset($values);
            $condition = $column . ' = ' . (int)($attribute ? $values->$attribute : $values);
        } else {
            if (!$attribute) {
                $values = array_map(function ($value) {
                    return (int)$value;
                }, $values);
            }
            $condition = $column . ' IN (' .
                ($attribute ? self::arrayToStringMap($values, function ($value) use ($attribute) {
                    return (int)$value->$attribute;
                }) : implode(', ', $values)) . ')';
        }
        return $condition;
    }

    /**
     * 获取屏蔽词列表
     *
     * @param integer $type 屏蔽词类型（默认为搜索屏蔽词类型）
     * @return array
     * @throws \Exception
     */
    public static function getForbiddenKeywords(int $type = self::FORBIDDEN_WORD_TYPE_SEARCH): array
    {
        $type_array = [
            self::FORBIDDEN_WORD_TYPE_SEARCH => self::FORBIDDEN_WORD_TYPE_SEARCH,
            self::FORBIDDEN_WORD_TYPE_ALBUM => self::FORBIDDEN_WORD_TYPE_ALBUM,
            self::FORBIDDEN_WORD_TYPE_PRIVATE_MESSAGE_FAKE_SEND => self::FORBIDDEN_WORD_TYPE_PRIVATE_MESSAGE_FAKE_SEND,
        ];
        if (!in_array($type, $type_array)) {
            throw new \Exception('参数错误');
        }
        if (is_null(self::$forbidden_keywords)) {
            self::$forbidden_keywords = array_map(function ($v) {
                return null;
            }, $type_array);
        }
        if (is_null(self::$forbidden_keywords[$type])) {
            // 只有在静态变量 $forbidden_keywords[$type] 为 null 的情况下去 Redis 获取数组
            // Redis 中若 key 没有对应内容，或不存在的 key，则 sMembers 返回空数组
            // 若 $forbidden_keywords 为空数组则代表已获取过避免重复获取
            $cache_key = MUtils::generateCacheKey(KEY_FORBIDDEN_WORDS, $type);
            self::$forbidden_keywords[$type] = Yii::$app->redis->sMembers($cache_key);
        }
        return self::$forbidden_keywords[$type];
    }

    /**
     * 检查关键词当中是否包含屏蔽词
     *
     * @param string $key_word 关键词
     * @param integer $type 屏蔽词类型（默认搜索屏蔽词类型）
     * @return boolean
     * @throws \Exception
     */
    public static function hasForbiddenKeywords(string $key_word, int $type = self::FORBIDDEN_WORD_TYPE_SEARCH): bool
    {
        if (strtolower($key_word) === 'h') {
            // WORKAROUND: 搜索“h”时临时屏蔽该词
            return true;
        }
        $forbidden_words = self::getForbiddenKeywords($type);
        $words = array_map(function ($word) {
            return preg_quote($word, '/');
        }, $forbidden_words);
        $pregs = implode('|', $words);
        $REGEX_MAX_LENGTH = 20000;
        if (mb_strlen($pregs) < $REGEX_MAX_LENGTH) {
            if ($pregs && preg_match("/($pregs)/i", $key_word)) {
                return true;
            }
        } else {
            foreach ($forbidden_words as $item) {
                if (stripos($key_word, $item) !== false) return true;
            }
        }
        return false;
    }

    /**
     * 获取文本中的数字或文字（中文、日文）
     *
     * @param string $text 待处理的文本
     * @return string
     */
    public static function getNumberOrLetter(string $text): string
    {
        $result = '';
        $preg = '/([\x{4e00}-\x{9fa5}\x{3040}-\x{309f}\x{30a0}-\x{30ff}\x{31f0}-\x{31ff}0-9])|([a-zA-Z])/u';
        if (preg_match_all($preg, $text, $match)) {
            // 若为英文词则保留词中的空格、符号，去除首尾的空格、符号
            // 否则只保留其中的数字或文字（中文及日文，去除所有的空格、符号）
            $result = empty(array_filter($match[1]))
                ? preg_replace('/.*?([a-zA-Z]+(.*[a-zA-Z])?).*/', '\1', $text)
                : join('', $match[0]);
        }

        return $result;
    }

    /**
     * 生成将要保存在数据库中的文件地址
     *
     * @param string $local_file 文件的本地路径
     * @return string|null 将要保存在数据库中的文件地址
     */
    public static function generateFilePath($local_file)
    {
        if (!is_file($local_file)) return null;
        if (!$extension = self::getFileExtension($local_file)) return null;
        $save_name = date('Ym') . '/' . date('d') . '/' . md5($local_file)
            . date('His') . '.' . $extension;
        return $save_name;
    }

    /**
     * 过滤字符串中的特殊字符
     *
     * @param string $string 需要被过滤的字符串
     * @param bool $filter_newline 是否过滤换行符
     * @return string 过滤掉特殊字符后的字符串
     */
    public static function filterSpecialCodes(string $string, bool $filter_newline = false): string
    {
        if ($string === '') return $string;
        $preg = $filter_newline ? '/[\\x00-\\x08\\x0B-\\x0C\\x0E-\\x1F\\n\\r]/'
            : '/[\\x00-\\x08\\x0B-\\x0C\\x0E-\\x1F\\r]/';
        return preg_replace($preg, '', $string);
    }

    /**
     * 1 到 1 兆的阿拉伯数字格式化成汉字
     * 如 “233” 转为 “二百三十三”
     *
     * @param string|int $number 待格式化的数字
     * @return string 格式化后以中文表述的数字
     */
    public static function numToChinese($number)
    {
        $number_char = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
        $unit_char = ['', '十', '百', '千', '', '万', '亿'];
        // 单位长度，每隔 4 位标注单位
        $UNIT_LENGTH = 4;
        $str = strrev((int)$number);
        $length = strlen($str);
        $out = [];
        for ($i = 0; $i < $length; $i++) {
            // 避免 10 ~ 19 时出现如 “一十九” 这样拗口的文案
            $out[$i] = ($i === 1 && $number >= 10 && $number < 20) ? '' : $number_char[$str[$i]];
            $out[$i] .= $str[$i] !== '0' ? $unit_char[$i % $UNIT_LENGTH] : '';
            if ($i > 0 && $str[$i] + $str[$i - 1] === 0) {
                $out[$i] = '';
            }
            if ($i % $UNIT_LENGTH === 0) {
                $temp = substr($str, $i, $UNIT_LENGTH);
                $out[$i] = str_replace($number_char[0], '', $out[$i]);
                if (strrev($temp) > 0) {
                    $out[$i] .= $unit_char[$UNIT_LENGTH + floor($i / $UNIT_LENGTH)];
                } else {
                    $out[$i] .= $number_char[0];
                }
            }
        }
        return join('', array_reverse($out));
    }

    /**
     * 检查绑定的手机是否通过
     *
     * @return bool
     */
    public static function checkUserMobile(): bool
    {
        if (!ENABLE_MOBILE_CHECK) {
            return true;
        }
        return Yii::$app->user->isBindMobile;
    }

    /**
     * 字符串是否以 http 或 https 协议头开头
     *
     * @param string $text 要判断的字符串
     * @return bool
     */
    public static function hasHttpScheme(string $text): bool
    {
        return (bool)preg_match('/^https?:\/\//i', $text);
    }

    /**
     * 获取远程文件到本地
     *
     * @param string $url 远程文件地址
     * @return null|string 本地文件地址
     */
    public static function getRemoteFile(string $url)
    {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            $img = curl_exec($ch);
            curl_close($ch);

            $dir = MUtils2::makeLocalFilesDir();
            $local_file_name = $dir . md5($url . time());
            $fp = fopen($local_file_name, 'w');
            fwrite($fp, $img);
            fclose($fp);
        } catch (\Exception $e) {
            $local_file_name = null;
        }

        unset($img, $url);
        return $local_file_name;
    }

    /**
     * 转义 uri 字符
     *
     * @param string $uri
     * @return string
     */
    public static function uriEncode(string $uri, bool $encode_slash = true): string
    {
        if ($uri === '') return $uri;
        $chars = str_split($uri);
        $encode_chars = array_map(function ($char) use ($encode_slash) {
            if (($char >= 'A' && $char <= 'Z') || ($char >= 'a' && $char <= 'z') || ($char >= '0' && $char <= '9')
                    || $char === '_' || $char === '-' || $char === '~' || $char === '.') {
                return $char;
            } elseif ($char === '/') {
                return $encode_slash ? '%2F' : $char;
            } else {
                return '%' . strtoupper(bin2hex($char));
            }
        }, $chars);
        return implode('', $encode_chars);
    }

    /**
     * 返回用于生成请求签名的 key
     *
     * @param string $public_key 设备生成的公钥
     * @param string $cache_key_name 存入缓存的键名
     * @return string
     */
    public static function getSignKey(string $public_key, string $cache_key_name, $expire = HALF_HOUR): string
    {
        $dh = new Crypt_DiffieHellman(REQUEST_SIGN_DH_P, REQUEST_SIGN_DH_G);
        // 生成随机共享私钥
        $compute_key = $dh->computeSecretKey($public_key)->getSharedSecretKey();
        // 将共享密钥存入 Redis
        $redis = Yii::$app->redis;
        $redis->setex($cache_key_name, $expire, $compute_key);
        // 返回随机生成的公钥
        return $dh->generateKeys()->getPublicKey();
    }

    /**
     * 返回校验请求签名的 key
     *
     * @param string $cache_key_name 相关 key 值的缓存的键名
     * @return string 校验请求签名的 key
     */
    public static function getSignKeyByRedis(string $cache_key_name): string
    {
        $redis = Yii::$app->redis;
        return $redis->get($cache_key_name);
    }

    /**
     * 验证请求的签名是否正确
     * 签名规则参考文档：https://github.com/MiaoSiLa/missevan-doc/blob/master/backend/application/api_sign.md
     *
     * @param string $key 生成签名的密钥
     * @return bool 签名是否正确
     */
    public static function validateSign(string $key)
    {
        $authorization = Yii::$app->request->headers['authorization'];
        if (!$authorization) {
            return false;
        }
        // 获取请求头中的签名
        // 请求头 authorization 格式为“MissEvan <sign>”，即 sign 为该字符串第 9 位之后的内容
        $request_sign = substr($authorization, 9);
        $sign = self::buildSign($key);
        return $request_sign === $sign;
    }

    /**
     * 生成签名
     *
     * @param string $key 生成签名的密钥
     * @return string 签名
     */
    public static function buildSign(string $key): string
    {
        // 获得待签名字符串
        $str_to_sign = self::getApiSignStr();
        // 生成签名
        // WORKAROUND: 对于 iOS 4.2.8 和 Android 5.1.9 及之后版本，需要 hash 的二进制数据进行加密
        $raw_output = !Equipment::isAppOlderThan('4.2.8', '5.1.9');
        $sign = base64_encode(hash_hmac('sha256', $str_to_sign, $key, $raw_output));
        return $sign;
    }

    /**
     * 获取接口待签名字符串
     *
     * @return string
     * @throws HttpException
     */
    public static function getApiSignStr(): string
    {
        $method = Yii::$app->request->method;
        $request_url = Yii::$app->request->hostInfo . '/' . Yii::$app->request->pathInfo;
        $canonical_url = self::uriEncode($request_url, false);
        $canonical_query_str = self::getCanonicalQueryStr();
        $canonical_headers = self::getCanonicalHeaders();
        $str_to_sign = $method . "\n" . $canonical_url . "\n" . $canonical_query_str . "\n"
            . $canonical_headers . "\n";
        if ($method === 'POST') {
            $canonical_body = self::getCanonicalBody();
            $str_to_sign .= $canonical_body . "\n";
        }
        return $str_to_sign;
    }

    /**
     * 获得用于验签的 Query 参数字符串
     *
     * @return string
     */
    private static function getCanonicalQueryStr(): string
    {
        $query_params = Yii::$app->request->getQueryParams();
        return self::formatRequestKeyValue($query_params);
    }

    /**
     * 获得用于验签的 header 字符串
     *
     * @return string
     */
    private static function getCanonicalHeaders(): string
    {
        $headers = Yii::$app->request->headers;
        $header_names = array_keys($headers->toArray());
        $pattern = '/^(x-m-.*)$/i';
        $need_headers = [];
        if (Equipment::isAppOlderThan(null, '5.1.9')) {
            // WORKAROUND: 若小于 Android 5.1.9 版本，则使用请求头中的 equip-code 与 token 参与验签
            $pattern = '/^(equip-code|token|(x-m-).*)$/i';
        } elseif (Equipment::isAppOlderThan('4.2.9')) {
            // WORKAROUND: 若小于 iOS 4.2.9 版本，则请求头中的 equip-code 参与验签
            $pattern = '/^(equip-code|(x-m-).*)$/i';
        } else {
            // WORKAROUND: 若为 iOS 4.2.9 和 Android 5.1.9 及以上版本，则 cookie 中 equip_id 与 token 参与验签
            if ($token = Yii::$app->request->cookies->getValue('token')) {
                $need_headers['token'] = 'token:' . $token;
            }
            $need_headers['equip_id'] = 'equip_id:' . Yii::$app->equip->getEquipId();
        }
        foreach ($header_names as $name) {
            if (preg_match($pattern, $name)) {
                $need_headers[$name] = strtolower($name) . ':' . trim($headers[$name]);
            }
        }
        ksort($need_headers);
        return implode("\n", $need_headers);
    }

    /**
     * 获得用于验签的 body 字符串
     *
     * @return string
     */
    public static function getCanonicalBody()
    {
        $content_type = Yii::$app->request->headers['content-type'];
        $content = '';
        if (strpos($content_type, 'application/x-www-form-urlencoded') !== false) {
            $content = self::getPostParamStr();
        } elseif (strpos($content_type, 'multipart/form-data') !== false) {
            $content = self::getPostParamStr();
            if (!empty($_FILES)) {
                // 用 unsigned 标记 file 参数
                $param_names = [];
                foreach ($_FILES as $param_name => $param) {
                    if (is_array($param['name'])) {
                        // 对于上传文件中出现 xxx[] 的情况，需要都进行组合，如：
                        // files%5B%5D&files%5B%5D（“flies[]&files[]”转义之后的字符串）
                        foreach ($param['name'] as $value) {
                            // TODO: 暂不考虑出现 xxx[xx] 索引的情况
                            $param_names[] = self::uriEncode($param_name . '[]');
                        }
                    } else {
                        $param_names[] = self::uriEncode($param_name);
                    }
                }
                sort($param_names);
                $param_names_str = implode('&', $param_names);
                $unsigned_params = "UNSIGNED-PARAMTERS:{$param_names_str}";
                $content = $content . "\n" . $unsigned_params;
            }
        } elseif (strpos($content_type, 'application/json') !== false) {
            $content = Yii::$app->request->rawBody;
        } elseif (!$content_type && !Yii::$app->request->rawBody) {
            $content = '';
        } else {
            throw new HttpException(400, '不支持的 MIME 类型');
        }
        // WORKAROUND: 对于 iOS 4.2.8 和 Android 5.1.9 及之后版本，需要 hash 的二进制数据进行加密
        $raw_output = !Equipment::isAppOlderThan('4.2.8', '5.1.9');
        return base64_encode(hash('sha256', $content, $raw_output));
    }

    /**
     * 获得 POST 参数用于验签的字符串
     *
     * @return string
     */
    private static function getPostParamStr()
    {
        $post_params = Yii::$app->request->post();
        return self::formatRequestKeyValue($post_params);
    }

    /**
     * 格式化请求中的 body 及 query 参数
     *
     * @param array $params 参数
     * @return string 格式化后的参数字符串
     */
    private static function formatRequestKeyValue(array $params): string
    {
        $str = '';
        if (empty($params)) {
            return $str;
        }
        $params_format = [];
        foreach ($params as $param_name => $param) {
            if (is_array($param)) {
                // 对于参数中出现 xxx[]=xxx 这样的情况，需要按参数原来的顺序进行组合，如：
                // sound_ids%5B%5D=1132625&sound_ids%5B%5D=1066241
                // TODO: 之后可能出现 param[foo][] 这种形式的参数，需要做处理
                $child_params = [];
                $is_assoc = array_values($param) === $param;
                foreach ($param as $key => $child_param) {
                    // 若出现 sound_id[id]=XXX 这种情况，键名也要需要参与验签
                    if ($is_assoc) {
                        $key_name = "{$param_name}[]";
                        $child_params[] = self::uriEncode($key_name) . '='
                            . self::uriEncode($child_param);
                    } else {
                        $key_name = "{$param_name}[{$key}]";
                        $child_params[$key_name] = self::uriEncode($key_name) . '='
                            . self::uriEncode($child_param);
                    }
                }
                ksort($child_params);
                $params_format[$param_name] = implode('&', $child_params);
            } else {
                $params_format[$param_name] = self::uriEncode($param_name) . '=' . self::uriEncode($param);
            }
        }
        ksort($params_format);
        return implode('&', $params_format);
    }

    /**
     * 根据版本号转化 App 跳转链接为可用链接
     */
    public static function getUsableAppLink(string $url): string
    {
        if (!$url) return $url;
        $update_links = Yii::$app->params['update_links'];
        if (Equipment::isAppOlderThan('4.8.7', '5.7.3')) {
            // 若为拥有催眠专享 2.0 功能之前的版本，则相关跳转链接更新为升级链接
            if (preg_match('/^missevan:\/\/radio\/hypnosis/', $url)) {
                return $update_links['radio_hypnosis'] ?? $url;
            }
        }
        if (Equipment::isAppOlderThan('4.6.0', '5.4.9')
                && substr($url, 0, 22) === 'missevan://mall/youzan') {
            // WORKAROUND: 若为接入有赞商城之前的版本，则相关跳转链接调整为提示升级页面链接
            return $update_links['mall_youzan'] ?? $url;
        }
        if (Equipment::isAppOlderThan('4.6.2', '5.5.1')
                && substr($url, 0, 25) === 'missevan://feedback/sobot') {
            // WORKAROUND: 若为接入智齿客服之前的版本，则相关跳转链接调整为提示升级页面链接
            return $update_links['sobot'] ?? $url;
        }
        if (Equipment::isAppOlderThan('4.6.4', '5.5.3')
                && substr($url, 0, 25) === 'missevan://live/superfans') {
            return $update_links['super_fan'] ?? $url;
        }

        if (Equipment::isAppOlderThan('4.7.6', '5.6.4')) {
            // WORKAROUND: 针对 iOS 4.7.6 以下、安卓 5.6.4 以下，将链接中的 integrity 转换成对应的 type
            $pattern = '/^missevan:\/\/catalog\/drama\/detail\?catalog_id=89&integrity=(\d+)/';
            if (preg_match($pattern, $url, $matches)) {
                switch ($matches[1]) {
                    case Drama::INTEGRITY_NAME_SERIALIZING:
                        $url = preg_replace(sprintf('/integrity=%d/', Drama::INTEGRITY_NAME_SERIALIZING),
                            sprintf('type=%d', Drama::TYPE_SERIALIZING), $url);
                        break;
                    case Drama::INTEGRITY_NAME_END:
                        $url = preg_replace(sprintf('/integrity=%d/', Drama::INTEGRITY_NAME_END),
                            sprintf('type=%d', Drama::TYPE_END), $url);
                        break;
                    default:
                        $url = preg_replace(sprintf('/integrity=%d/', Drama::INTEGRITY_NAME_ONE_AND_MINI),
                            sprintf('type=%d', Drama::TYPE_ONE_AND_MINI), $url);
                }
            }
        }
        // WORKAROUND: 音频链接中的相关参数需要根据版本做调整以支持进入视频播放页
        // e.g.
        // 旧版本链接：https://www.test.com/sound/player?id=233&webview=1、https://www.test.com/sound/233?webview=1
        // 新版本链接：https://www.test.com/sound/player?id=233&video=1、https://www.test.com/sound/233?video=1
        $domain_missevan_regex = str_replace(['http\:', 'https\:'], 'https?\:',
            preg_quote(Yii::$app->params['domainMissevan'], '/'));
        if (preg_match("/^{$domain_missevan_regex}\/sound\/(player|\d*)\?.*/", $url)) {
            return SoundVideo::isUsableVersion() ? str_replace('webview=1', 'video=1', $url)
                : str_replace('video=1', 'webview=1', $url);
        }
        if (Equipment::isAppOlderThan('4.8.1', '5.6.9')
                && substr($url, 0, 18) === 'missevan://theatre') {
            // WORKAROUND: 若为不支持盲盒剧场的版本，需要跳转至升级提醒页面
            return $update_links['theatre'] ?? $url;
        }
        if (Equipment::isAppOlderThan('4.9.8', '5.8.1')
                && substr($url, 0, 18) === 'missevan://message') {
            // WORKAROUND: iOS < 4.9.8 版本及 Android < 5.8.1 版本时，我的消息图标返回老的 msr-0 地址
            return 'missevan://msg';
        }
        if (Equipment::isAppOlderThan('6.0.1', '6.0.1')
                && substr($url, 0, 20) === 'missevan://dramalist') {
            // WORKAROUND: 若为不支持剧单功能的版本，则相关跳转链接调整为提示升级页面链接
            return $update_links['dramalist'] ?? $url;
        }
        if (Equipment::isAppOlderThanVipVersion() && str_starts_with($url, 'missevan://vip/')) {
            // WORKAROUND: 若为不支持会员功能的版本，则相关跳转链接调整为提示升级页面链接
            return $update_links['vip'] ?? $url;
        }
        return $url;
    }

    public static function XMLToArray($xml)
    {
        return (array)simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
    }

    public static function arrayToXML($array, $root = false, $cdata = false)
    {
        $xml = '';
        foreach ($array as $key => $val) {
            $key = preg_replace('/\[\d*\]/', '', $key);
            if (is_array($val)) {
                $val = self::arrayToXML($val, false, $cdata);
                $xml .= "<{$key}>{$val}</{$key}>";
            } elseif ($cdata && !is_numeric($val)) {
                $xml .= "<{$key}><![CDATA[{$val}]]></{$key}>";
            } else {
                $xml .= "<{$key}>{$val}</{$key}>";
            }
        }

        if ($root) {
            $xml = "<xml>{$xml}</xml>";
        }
        return $xml;
    }

    /**
     * 获取模型或表单的首个错误提示
     *
     * @param \app\components\base\Model|\app\models\ActiveRecord $model
     * @return string
     */
    public static function getFirstError($model): string
    {
        $errors = $model->getFirstErrors();
        if (empty($errors)) {
            return '';
        }
        return current($errors);
    }

    /**
     * 验证身份证账号（中国）
     *
     * @param string $str
     * @return bool
     */
    public static function idCard(string $str)
    {
        $str = trim($str);
        if (!$str) {
            return false;
        }
        if (preg_match('/^([0-9]{15}|[0-9]{17}[0-9a-z])$/i', $str)) {
            return true;
        }
        return false;
    }

    /**
     * 字符串脱敏函数
     *
     * @param String $string 需要脱敏的字符串
     * @param int $type 类型
     * @return string
     * @throws
     */
    public static function mosaicString($string, int $type = 0)
    {
        if (!$string || !is_string($string)) return $string;
        switch ($type) {
            case self::MOSAIC_NAME:
                // 真实姓名，例：刘**
                // 如果只有一个字符，则在该字符后面补充一个 * 号，例：刘*
                $mosaic_count = mb_strlen($string) - 1 ?: 1;
                $mosaic_string = mb_substr($string, 0, 1) . str_repeat('*', $mosaic_count);
                break;
            case self::MOSAIC_MOBILE:
                // 手机号，例：187******18
                $mosaic_count = mb_strlen($string) - (self::SHOW_MOBILE_HEAD_OFFSET - self::SHOW_MOBILE_TAIL_OFFSET);
                if ($mosaic_count <= 0) {
                    // 手机号位数小于或等于脱敏的位数则全部脱敏处理
                    return str_repeat('*', mb_strlen($string));
                }
                $mosaic_string = mb_substr($string, 0, self::SHOW_MOBILE_HEAD_OFFSET) .
                    str_repeat('*', $mosaic_count) . mb_substr($string, self::SHOW_MOBILE_TAIL_OFFSET);
                break;
            case self::MOSAIC_EMAIL:
                // 邮箱
                // 显示前面 3 位字符，@ 符号和后面全部字符，中间固定三个马赛克，例：865***@qq.com
                // 前缀少于 3 个字符，则只显示首字符，例：8***@qq.com
                $i = strpos($string, '@');
                $SHOW_COUNT = 3;
                $total_count = strlen($string);
                $prefix_count = $total_count - strlen(mb_substr($string, $i));
                $show_count = $prefix_count > $SHOW_COUNT ? $SHOW_COUNT : 1;
                $mosaic_string = mb_substr($string, 0, $show_count) . '***' . mb_substr($string, $i);
                break;
            case self::MOSAIC_ID_CARD:
                // 身份证号
                // 马赛克显示的数量 = 字符串总长度 - (开头显示的长度 + 结尾显示的长度)，例：36***************3
                // 结尾显示的长度是负数，所以用减
                $mosaic_count = mb_strlen($string) - (self::SHOW_ID_CARD_HEAD_OFFSET - self::SHOW_ID_CARD_TAIL_OFFSET);
                if ($mosaic_count <= 0) {
                    // 身份证号位数小于或等于脱敏的位数则全部脱敏处理
                    return str_repeat('*', mb_strlen($string));
                }
                $mosaic_string = mb_substr($string, 0, self::SHOW_ID_CARD_HEAD_OFFSET) .
                    str_repeat('*', $mosaic_count) . mb_substr($string, self::SHOW_ID_CARD_TAIL_OFFSET);
                break;
            case self::MOSAIC_BANK_ACCOUNT:
                // 银行卡，只显示后四位 例：**** **** **** ***0 018
                $mosaic_count = mb_strlen($string) + self::SHOW_BANK_ACCOUNT_TAIL_OFFSET;
                if ($mosaic_count <= 0) {
                    // 银行卡位数小于或等于脱敏的位数则全部脱敏处理
                    return str_repeat('*', mb_strlen($string));
                }
                $mosaic_string = str_repeat('*', $mosaic_count) .
                    mb_substr($string, self::SHOW_BANK_ACCOUNT_TAIL_OFFSET);
                $mosaic_string = trim(chunk_split($mosaic_string, 4, ' '));
                break;
            default:
                throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        return $mosaic_string;
    }

    /**
     * 根据加密算法获取 key 的大小
     *
     * @param string $method 加密算法
     * @return integer
     * @throws 由于传入不支持的加密算法而抛出异常
     */
    public static function getCipherKeySize($method): int
    {
        switch ($method) {
            case 'aes-256-cbc':
                return 32;
            case 'aes-192-cbc':
                return 24;
            case 'aes-128-cbc':
                return 16;
            default:
                throw new HttpException(400, '暂不支持');
        }
    }

    /**
     * 获取特定长度字符串
     *
     * @param string $origin_str 原始字符串
     * @param int $type 需要补充长度的字符串类型
     * @param string $method
     * @param string $padding
     * @return string
     * @throws
     */
    public static function encryptPad(string $origin_str, int $type = self::PAD_TYPE_IV, string $method = 'aes-256-cbc',
            $padding = "\0")
    {
        switch ($type) {
            case self::PAD_TYPE_IV:
                $origin_length = strlen($origin_str);
                $standard_length = openssl_cipher_iv_length($method);
                break;
            case self::PAD_TYPE_KEY:
                $origin_length = strlen($origin_str);
                $standard_length = self::getCipherKeySize($method);
                break;
            default:
                return $origin_str;
        }
        if ($standard_length > $origin_length) {
            // 未达到特定长度则补 $padding
            $padding_num = $standard_length - $origin_length % $standard_length;
            return $origin_str . str_repeat($padding, $padding_num);
        } elseif ($standard_length < $origin_length) {
            // 超长则截断到特定长度
            return substr($origin_str, 0, $standard_length);
        } else {
            return $origin_str;
        }
    }

    /**
     * 加密数据
     *
     * @param string $string 需要加密的字符串
     * @param string $iv 初始化向量
     * @param string $method 加密算法
     * @param string $key 加密密钥
     * @return bool|string 成功返回加密后的字符串，失败返回 false
     */
    public static function encrypt($string, $iv, $method = 'aes-256-cbc', $key = SENSITIVE_INFORMATION_KEY)
    {
        if (!$string || !is_string($string)) {
            return false;
        }
        // 获取特定长度的初始化向量
        $iv = self::encryptPad($iv, self::PAD_TYPE_IV, $method);
        // 获取指定长度的密钥
        $key = self::encryptPad($key, self::PAD_TYPE_KEY, $method);
        if (!$encrypt_data = openssl_encrypt($string, $method, $key, OPENSSL_RAW_DATA, $iv)) {
            return false;
        }
        return base64_encode($encrypt_data);
    }

    /**
     * 解密数据
     *
     * @param string $string 需要解密的字符串
     * @param string $iv 初始化向量
     * @param string $method 解密算法
     * @param string $key 解密密钥
     * @return bool|string 成功返回解密后的字符串，失败返回 false
     */
    public static function decrypt($string, $iv, $method = 'aes-256-cbc', $key = SENSITIVE_INFORMATION_KEY)
    {
        if (!$string || !is_string($string)) {
            return false;
        }
        // 获取特定长度的初始化向量
        $iv = self::encryptPad($iv, self::PAD_TYPE_IV, $method);
        // 获取指定长度的密钥
        $key = self::encryptPad($key, self::PAD_TYPE_KEY, $method);
        if (!$decrypt_data = base64_decode($string)) {
            return false;
        }
        return openssl_decrypt($decrypt_data, $method, $key, OPENSSL_RAW_DATA, $iv);
    }

    /**
     * 获取手机网页的对应 URL 地址
     * @param string 路径地址
     * @return string
     */
    public static function getMobileWebUrl(string $path): string
    {
        return Yii::$app->params['domainMobileWeb'] . $path;
    }

    /**
     * 检查人机验证
     *
     * @param string $scene 使用场景
     * @throws Exception
     */
    public static function checkCaptcha(string $scene)
    {
        $captcha_params = $scene === Captcha::SCENEORIGINAL_LOGIN ? Captcha::getCaptchaToken() : Captcha::getSlideParams();
        if ($captcha_params) {
            if ($scene === Captcha::SCENEORIGINAL_LOGIN) {
                $unique_id = Yii::$app->user->isGuest ? null : (string)Yii::$app->user->id;
                $validate_result = Yii::$app->go->captchaValidate($captcha_params, $unique_id, $scene);
                if (array_key_exists('risk_level', $validate_result) && $validate_result['risk_level'] > Go::CAPTCHA_RISK_LEVEL_LOW) {
                    Yii::info(sprintf('login risk found: equip_id=%s, ip=%s, risk_level=%d',
                        Yii::$app->equip->getEquipId(), Yii::$app->request->remoteIP, $validate_result['risk_level']), __METHOD__);
                    throw new HttpException(403, '当前登录环境存在风险，请使用短信验证码登录');
                }
                $validate_success = $validate_result['success'];
            } else {
                [$session_id, $token, $sig] = $captcha_params;
                $result = Yii::$app->captcha->verifyCaptcha($session_id, $token, $sig, $scene);
                $validate_success = Captcha::SUCCESS === $result->Code;
            }
            if ($validate_success) {
                // 验证成功，删除缓存
                Yii::$app->captcha->resetCounter($scene);
            } else {
                throw new HttpException(403, Yii::$app->params['standaloneUrl'] . '403/slide.html?scene=' . $scene, 100010017);
            }
        } elseif (Captcha::isRequired($scene)) {
            throw new HttpException(403, Yii::$app->params['standaloneUrl'] . '403/slide.html?scene=' . $scene, 100010017);
        }
    }

    /**
     * 以多维数组某个字段对数组进行排序
     *
     * @param array $arr 需要排序的二维数组
     * @param string $field 排序字段
     * @param int $sort_flags 排序方式
     * @return array 排序好的数组
     * @throws \Exception 排序字段
     */
    public static function arrayOrderByField(array $arr, string $field, $sort_flags = SORT_ASC)
    {
        if (empty($arr)) {
            return $arr;
        }
        $fields = array_column($arr, $field);
        if (count($fields) !== count($arr)) {
            throw new \Exception('数组排序失败，排序字段错误');
        }
        array_multisort($fields, $sort_flags, $arr);
        return $arr;
    }

    public static function isMiMiApp()
    {
        return Yii::$app->id === 'mimi';
    }

    public static function isMissEvanApp()
    {
        return Yii::$app->id === 'missevan';
    }

    public static function getNotNullObjectFields($object)
    {
        // 过滤对象中不为 null 的字段
        $new_fields = array_keys(array_filter(get_object_vars($object), function ($value) {
            return !is_null($value);
        }));
        return array_combine($new_fields, $new_fields);
    }

    /**
     * bitSet 按位或运算
     *
     * @param int $num
     * @param int $i
     * @return int
     */
    public static function bitSet(int $num, int $i): int
    {
        return $num | $i;
    }

    /**
     * bitUnset 对 num 中的特定位取反
     *
     * @param int $num
     * @param int $i
     * @return int
     */
    public static function bitUnset(int $num, int $i): int
    {
        return $num & (~$i);
    }

    /**
     * bitIsSet 按位与运算是否不为 0
     *
     * @param int $num
     * @param int $i
     * @return bool
     */
    public static function bitIsSet(int $num, int $i): bool
    {
        return ($num & $i) === $i;
    }

    /**
     * 获取时间戳的整分钟/小时/天等时刻
     *
     * @param int $unit
     * @param int|null $timestamp
     * @return int
     * @throws Exception
     */
    public static function getUnitTime(int $unit = ONE_MINUTE, ?int $timestamp = null)
    {
        if ($unit <= 0) {
            throw new Exception('参数错误');
        }
        if (is_null($timestamp)) {
            $timestamp = $_SERVER['REQUEST_TIME'];
        }
        return $timestamp - $timestamp % $unit;
    }

    /**
     * 检测传入的 URL 地址是否为 App 协议地址
     * 参考文档：https://github.com/MiaoSiLa/missevan-doc/blob/master/app/rules/msr-0.md
     *
     * @param string $url 传入的 URL 地址
     * @return boolean
     * @todo 待迁移至 php-utils 项目
     */
    public static function isAppSchemeUrl(string $url): bool
    {
        if (!$url) {
            return false;
        }
        return strtolower(substr($url, 0, 11)) === 'missevan://';
    }

    /**
     * 是否为 upos 协议地址
     *
     * @param string $url
     * @return bool
     * @todo 需要迁移至 php-utils 项目
     */
    public static function isUposUrl(string $url): bool
    {
        return strtolower(substr($url, 0, 7)) === 'upos://';
    }

    /**
     * 处理文本内容中的 a 标签链接，保证用户可用
     *
     * @param string $content
     * @return string
     */
    public static function formatContentUrl(string $content): string
    {
        if (!$content) {
            return $content;
        }
        preg_match_all('/<a .*?href=["|\'](.*?)["|\'].*?>.*?<\/a>/', $content, $matches);
        if (empty($matches)) {
            return $content;
        }
        // $matches[0] 为 a 标签集合，$matches[1] 为链接集合
        foreach ($matches[0] as $key => $match) {
            $url = $matches[1][$key];
            $new_url = self::getUsableAppLink($url);
            if ($url !== $new_url) {
                $a_tag = str_replace($url, $new_url, $match);
                $content = str_replace($match, $a_tag, $content);
            }
        }
        return $content;
    }

    /**
     * 对数组按其中某列指定顺序进行排序
     * 例：
     * $arr = [['id' => 1], ['id' => 2], ['id' => 3]];
     * $new_arr = \app\components\util\MUtils::sortByValueOrder($arr, 'id', [3, 1, 2]);
     * print_r($new_arr); // [['id' => 3], ['id' => 1], ['id' => 2]]
     *
     * @param array $arr
     * @param string $column
     * @param array $target_order
     * @return array
     */
    public static function sortByValueOrder(array $arr, string $column, array $target_order)
    {
        $tmp = array_column($arr, null, $column);
        $new_arr = [];
        foreach ($target_order as $column_value) {
            if (array_key_exists($column_value, $tmp)) {
                $new_arr[] = $tmp[$column_value];
            }
        }
        return $new_arr;
    }

    /**
     * 获取当前季节（1：春季；2：夏季；3：秋季；4：冬季）
     *
     * @return int
     */
    public static function getSeason(): int
    {
        $month = date('n');
        switch ($month)
        {
            case 1:
            case 2:
            case 12:
                return 4;
            case 3:
            case 4:
            case 5:
                return 1;
            case 6:
            case 7:
            case 8:
                return 2;
            case 9:
            case 10:
            case 11:
                return 3;
            default:
                throw new Exception('获取当前月份出错');
        }
    }

    /**
     * 当没有事务开启时开启事务并提交
     *
     * @param $model
     * @param callable $fun
     * @param mixed ...$args
     * @return mixed
     * @throws Exception
     */
    public static function ensureDbTransaction($model, callable $fun, ...$args)
    {
        if (!is_null($model::getDb()->getTransaction())) {
            $result = $fun($model, ...$args);
            return $result;
        }
        $transaction = $model::getDb()->beginTransaction();
        try {
            $result = $fun($model, ...$args);
            $transaction->commit();
            return $result;
        } catch (Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * 是否为压力测试请求
     *
     * @return bool
     * @todo 迁移至 php-utils
     */
    public static function isLoadTest(): bool
    {
        $buvid = Yii::$app->equip->getBuvid();
        if ($buvid && substr($buvid, 0, 7) === 'PTSTEST') {
            // 当请求的 buvid 以 PTSTEST 开头时，识别为压测请求
            return true;
        }
        return false;
    }

    /**
     * 对数字进行万、亿的转化，详细规则如下：
     * 1. 播放量 < 10000 时：完整展示
     * 2. 10000 ≤ 播放量 < 100 万时：显示单位“万”，四舍五入保留一位小数，当小数点后一位为 0 时，不展示小数点
     * 3. 100 万 ≤ 播放量 < 1 亿时：不展示小数，向下取整
     * 4. 播放量 ≥ 1 亿时：显示单位“亿”，保留小数点后两位，之后小数位直接舍弃，当小数点后第二位为 0 时，只展示一位小数；当小数点后两位均为 0 时，不展示小数点
     *
     * @param int $number 需要转换的数字
     * @return string
     * @throws Exception
     */
    public static function formatNumber(int $number)
    {
        if ($number < 0) {
            throw new Exception('参数错误');
        }
        if ($number < 1e4) {
            return (string)$number;
        } elseif ($number < 1e6) {
            return sprintf('%s 万', round(($number / 1e4), 1));
        } elseif ($number < 1e8) {
            return sprintf('%s 万', floor($number / 1e4));
        } else {
            return sprintf('%s 亿', floor($number / 1e6) / 100);
        }
    }

    /**
     * 获取随机标识参数信息
     *
     * @param string $marker 当前抽取标识，格式为“随机因子:当前序列索引”，例 "50:1"
     * @param int $pool_size 池子中元素的数量
     * @return array 返回随机因子、当前序列索引和下一个抽取标识，例 [50, 1, "50:2"]
     * @throws Exception
     */
    public static function markerInfo(string $marker, int $pool_size): array
    {
        $random_seed = $current_position = 0;
        if ($marker) {
            $marker_info = explode(':', $marker);
            if (count($marker_info) === 2 && MUtils2::isUintArr($marker_info)) {
                [$random_seed, $current_position] = array_map('intval', $marker_info);
            }
        }
        if (!$random_seed) {
            // 若未传递随机因子，则生成一个新的
            $random_seed = random_int(1, 9999999);
        }
        if ($current_position >= $pool_size) {
            // 若抽取位置超过池子中元素的数量，则从第一个开始
            $current_position = 0;
        }
        // 获取下一个 marker 信息
        $next_position = $current_position + 1;
        if ($next_position >= $pool_size) {
            $next_position = 0;
        }
        $next_marker = $random_seed . ':' . $next_position;
        return [$random_seed, $current_position, $next_marker];
    }

    public static function isLimitLocation()
    {
        $env = getenv('DEPLOY_ENV', true);
        if ($env !== 'prod') {
            return false;
        }
        $limit_location = false;
        $record = MUtils2::getGeoIpRecord();
        if ($record && $record['country_code'] === 'CN') {
            if (in_array($record['region_name'], ['Beijing', 'Shanghai'])
                || ($record['region_name'] === 'Guangdong' && in_array($record['city_name'], ['Guangzhou', 'Shenzhen']))
                || ($record['region_name'] === 'Hebei' && in_array($record['city_name'], ['Shijiazhuang']))) {
                $limit_location = true;
            }
        }
        return $limit_location;
    }
}
