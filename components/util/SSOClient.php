<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/14
 * Time: 15:20
 */

namespace app\components\util;

use app\models\AdTrack;
use Yii;
use yii\base\Exception;
use yii\helpers\Json;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ConnectException;
use app\forms\LoginForm;
use yii\web\HttpException;
use yii\base\Component;

class SSOClient extends Component
{
    // SSO 用户信息字段
    const USER_FIELDS = [
        'email',
        'qquid',
        'weibouid',
        'bilibiliuid',
        'wechatuid',
        'appleuid',
        'username',
        'mobile',
        'teamid',
        'teamname',
        'subtitle',
        'confirm',
        'region'
    ];

    // SSO 请求 Code
    const CODE_SUCCESS = 0;
    const LEGACY_CODE_USERNAME_NOT_EXISTS = 1;
    const LEGACY_CODE_PASSWORD_ERROR = 2;
    const LEGACY_CODE_EMAIL_EXISTS = 3;
    const LEGACY_CODE_TOKEN_INVALID = 5;
    const LEGACY_CODE_USERNAME_EXISTS = 6;
    const CODE_REQUIRE_RESET_PASSWORD = 200010005;
    const CODE_HIGH_RISK = *********;
    // SSO 用户不存在的 Code
    const CODE_USER_NOT_EXISTS = *********;
    // 该第三方账号已绑定于其它账号的 Code
    const CODE_AUTH_USER_EXISTS = *********;
    // 密码错误
    const CODE_PASSWORD_ERROR = *********;
    // Token 过期或不存在的 Code
    const CODE_USER_TOKEN_EXPIRED = *********;
    // 账号有注销申请在处理的 Code
    const CODE_USER_DELETE_STATUS_PENDING = *********;
    // 有赞账号不存在
    const CODE_YOUZAN_ACCOUNT_NOT_EXIST = *********;

    // SSO Token 有效期（0：两小时、1：一天、2：一月、3：一年）
    public const MAX_AGE_TWO_HOURS = 0;
    public const MAX_AGE_ONE_DAY = 1;
    public const MAX_AGE_ONE_MONTH = 2;
    public const MAX_AGE_ONE_YEAR = 3;

    // 注册方式 0：手机号；1：邮箱；2：QQ；3：微博；4：b 站；5：微信；6：Apple
    const REGIST_AUTH_MOBILE = 0;
    const REGIST_AUTH_EMAIL = 1;
    const REGIST_AUTH_QQ = 2;
    const REGIST_AUTH_WEIBO = 3;
    const REGIST_AUTH_BILIBILI = 4;
    const REGIST_AUTH_WECHAT = 5;
    const REGIST_AUTH_APPLE = 6;

    // 更新密码类型 0：用户更改密码；1：用户忘记密码
    const TYPE_CHANGE_PASSWORD = 0;
    const TYPE_FORGET_PASSWORD = 1;

    // 操作类型 0：一键登录；1：手机号验证码登录；2：第三方账号一键绑定手机号；3：第三方账号手机号验证码绑定手机号
    const TYPE_FAST_LOGIN = 0;
    const TYPE_SMS_LOGIN = 1;
    const TYPE_FAST_AUTH_BIND = 2;
    const TYPE_SMS_AUTH_BIND = 3;

    // 检查用户账号 API
    const API_CHECK = '/rpc/check';
    // 根据参数获取用户记录 API
    const API_GET = '/rpc/get';
    // 更新用户密码 API
    const API_UPDATE_PASSWORD = '/rpc/update/password';
    // 清空用户所有 session API
    const API_CLEAR_SESSION = '/rpc/util/clearsession';
    // 用户登录
    const API_LOGIN = '/rpc/login';
    // 用户退出登录
    const API_LOGOUT = '/rpc/logout';
    // 一键登录 API
    const API_FAST_LOGIN = '/rpc/sso/fastlogin';
    // 第三方账号登录 API
    const API_AUTH_LOGIN = '/rpc/sso/authlogin';
    // 有赞登录
    const API_YOUZAN_LOGIN = '/rpc/youzan/login';
    // 有赞退出
    const API_YOUZAN_LOGOUT = '/rpc/youzan/logout';

    // 同步用户青少年模式状态
    const API_TEENAGER_STATUS = '/rpc/util/set-teenager-status';

    // 获取用户 vip 等级
    const API_USER_VIP_LEVEL = '/rpc/vip/user-level';

    // 获取用户的 buvid
    const API_GET_BUVIDS = '/rpc/getbuvids';

    // 当用户不存在时创建新用户，并返回用户信息
    const API_SSO_ENSURE = '/rpc/sso/ensure';

    const API_YOUZAN_TAKE_COUPON = '/rpc/youzan/take-coupon';

    // 开启/关闭青少年模式
    const TEENAGER_STATUS_CLOSE = 0;
    const TEENAGER_STATUS_OPEN = 1;
    const TEENAGER_STATUS_SYNC = 2;
    public static $teenager_status = [
        self::TEENAGER_STATUS_CLOSE,
        self::TEENAGER_STATUS_OPEN,
        self::TEENAGER_STATUS_SYNC
    ];

    public $url;
    public $secret;
    private static $_client = false;

    public function init()
    {
        parent::init();
        if (false === self::$_client) {
            self::$_client = new Client([
                'base_uri' => $this->url,
                'connect_timeout' => 5.0,
                'timeout' => 30.0
            ]);
        }
    }

    /**
     * sso 登录请求
     *
     * @param string|int $account 账号
     * @param int $type 账号类型 1 email 2 mobile 3 qq 4 weibo 5 wechat 6 bilibili
     * @param string $password 密码
     * @param int $region 手机的国家区号
     * @param string $openid 微信登录需要传递的 openid
     * @return mixed|string
     * @throws Exception
     * @throws HttpException
     */
    public function login($account, $type, $password, $region = 86, $openid = null)
    {
        $msg = [];
        switch ($type) {
            case LoginForm::EMAIL:
                $msg['email'] = $account;
                break;
            case LoginForm::MOBILE:
                $msg['mobile'] = (int)$account;
                $msg['region'] = $region;
                break;
            case LoginForm::QQ:
                $msg['qquid'] = $password;
                break;
            case LoginForm::WEIBO:
                $msg['weibouid'] = $password;
                break;
            case LoginForm::WECHAT:
                $msg['wechatuid'] = $password;
                $msg['wechatopenid'] = $openid;
                break;
            case LoginForm::BILIBILI:
                $msg['bilibiliuid'] = $password;
                break;
            case LoginForm::APPLE:
                $msg['appleuid'] = $password;
                break;
            default:
                throw new Exception(Yii::t('app/error', 'Account type error'));
        }

        $msg['maxAgeType'] = self::MAX_AGE_ONE_YEAR;

        if ($password) {
            $msg['password'] = $password;
            $msg['pwhash'] = false;
        } else {
            $msg['pwhash'] = true;
        }
        self::addEquipParam($msg);
        $res = $this->requestRpc(self::API_LOGIN, $msg);
        if (!$res) {
            Yii::error(sprintf('Request %s api fail', self::API_LOGIN), __METHOD__);
            throw new Exception(Yii::t('app/error', 'Server connection error'));
        }
        if (self::CODE_SUCCESS !== $res['code']) {
            self::throwException(self::API_LOGIN, $res['code'], $res['info'] ?? '未知错误');
        }
        return $res['info'];
    }

    /**
     * 更新用户信息
     *
     * @param int $user_id 被更新的用户 ID
     * @param array $data 更新内容
     * @param string $token 登录 Token，默认无
     * @return array 返回 SSO 服务器的处理结果
     * @throws Exception
     * @throws HttpException
     */
    public function update($user_id, $data, $token = '')
    {
        $msg = [
            'user_id' => $user_id,
            'update' => $data,
        ];

        // WORKAROUND: token 设置在 cookie 中，兼容 body 中的 token
        if ($token) {
            $msg['token'] = Yii::$app->user->identity->token;
        }

        self::addEquipParam($msg);
        return $this->_request('update', $msg);
    }

    public function logout()
    {
        $res = $this->requestRpc(self::API_LOGOUT);
        if (!$res) {
            Yii::error(sprintf('Request %s api fail', self::API_LOGOUT), __METHOD__);
            throw new Exception(Yii::t('app/error', 'Server connection error'));
        }
        if (self::CODE_SUCCESS !== $res['code']) {
            self::throwException(self::API_LOGOUT, $res['code'], $res['info'] ?? '未知错误');
        }
        return $res['info'];
    }

    public function regist($user)
    {
        self::addEquipParam($user);
        $ret = $this->_request('register', $user);
        $this->afterRegister($ret['user']['user_id'], $user['buvid']);
        return $ret;
    }

    /**
     * 获取用户信息
     *
     * @param $user_id
     * @return array 返回 SSO 服务器传来 code 参数（请求的处理结果标识符，0 为正常）、user 数组（若成功，包含用户信息）
     * @throws Exception
     * @throws HttpException
     */
    public function get($user_id)
    {
        $msg = ['user_id' => $user_id];
        return $this->_request('get', $msg);
    }

    public function session($token)
    {
        // WORKAROUND: token 设置在 cookie 中，兼容 body 中的 token
        $msg = ['token' => $token];
        return $this->_request('session', $msg);
    }

    private function _request($path, $msg)
    {
        $msg['ip'] = Yii::$app->request->userIP;
        $timestamp = time();
        $message = base64_encode(Json::encode($msg));
        $sign = hash_hmac('sha1', "$message $timestamp", $this->secret);
        $str = "$message $sign $timestamp";

        try {
            $path = '/sso/' . $path;
            $headers = [
                'Accept' => 'application/json',
                'X-Forwarded-For' => Yii::$app->request->getUserIP(),
                'Content-Type' => 'text/plain',
                'User-Agent' => Yii::$app->name . '/' . Yii::$app->version,
            ];
            $cookie = [
                'equip_id=' . Yii::$app->equip->getEquipId(),
                'buvid=' . Yii::$app->equip->getBuvid(),
            ];
            if ($token = Yii::$app->request->cookies->getValue('token')) {
                $cookie[] = 'token=' . $token;
            }
            $headers['Cookie'] = implode('; ', $cookie);
            $response = self::$_client->request('POST', $path, [
                'form_params' => ['auth' => $str],
                'headers' => $headers,
            ]);
            $body = $response->getBody();
            $content = $body->getContents();
            if ('Not auth' === $content) {
                throw new Exception('SSO token or server time is error');
            }
            $content = Json::decode($content);
            $code = $content['code'];
            if ($code === self::LEGACY_CODE_USERNAME_NOT_EXISTS || $code === self::LEGACY_CODE_PASSWORD_ERROR) {
                throw new HttpException(400, Yii::t('app/error', 'Incorrect username or password'), 200010001);
            }
            if ($code === self::LEGACY_CODE_TOKEN_INVALID) {
                throw new HttpException(401, Yii::t('yii', 'Login Required'), 100010006);
            }
            if ($code === self::CODE_REQUIRE_RESET_PASSWORD) {
                throw new HttpException(403, Yii::t('app/error', 'Reset password required'));
            }
            if (self::CODE_SUCCESS < $code) {
                throw new HttpException(400, $content['message']);
            }
            if (self::CODE_SUCCESS > $code) {
                throw new Exception($content['message']);
            }
            return $content;
        } catch (ConnectException $e) {
            throw new Exception(Yii::t('app/error', 'Server connection error'));
        }
    }

    /**
     * 清空用户所有 session
     *
     * @param integer $user_id 用户 ID
     * @return boolean true：成功；false：失败
     */
    public function clearSession(int $user_id): bool
    {
        $data = [
            'user_id' => $user_id
        ];
        $res = $this->requestRpc(self::API_CLEAR_SESSION, $data);
        if (!$res) {
            Yii::error('Request ' . self::API_CLEAR_SESSION . ' api fail', __METHOD__);
            return false;
        }
        if (self::CODE_SUCCESS !== $res['code']) {
            // 若请求接口出错，不直接抛出异常避免影响用户，将错误记录到日志中
            Yii::error("请求清空用户所有 session 接口出错：{$res['info']}", __METHOD__);
            return false;
        }
        return true;
    }

    /**
     * 同步青少年模式状态
     *
     * @param int $type TEENAGER_STATUS_OPEN 1 开启
     *                  TEENAGER_STATUS_CLOSE 0 关闭
     *                  TEENAGER_STATUS_SYNC 2 登录后同步
     * @param string $password
     * @param string $token
     * @return array
     *     {
     *       "confirm": 512  // 0 关闭 confirm & Mowangskuser::CONFIRM_TEENAGER != 0 开启
     *     }
     * @throws Exception
     * @throws HttpException
     */
    public function setTeenagerStatus(int $type, string $password, string $token): array
    {
        $data = [
            'type' => $type,
            'password' => $password,
            'token' => $token,
        ];
        $res = $this->requestRpc(self::API_TEENAGER_STATUS, $data);
        if (!$res) {
            throw new Exception('调用 SSO RPC 服务失败');
        }

        if (self::CODE_SUCCESS !== $res['code']) {
            throw new HttpException($res['status'], $res['info'], $res['code']);
        }
        return $res['info'];
    }

    /**
     * 请求 RPC 数据
     *
     * @param string $api 请求 RPC 地址
     * @param array $params 请求参数
     * @return mixed
     */
    protected function requestRpc(string $api, array $params = [])
    {
        return Yii::$app->tools->requestApi($api, $params, $this->url, $this->secret);
    }

    /**
     * 检查手机号是否存在
     *
     * @param int $mobile 手机号
     * @param int $region 区号
     * @return boolean
     */
    public function checkMobileExists(int $mobile, int $region = 86)
    {
        $data = [
            'mobile' => $mobile,
            'region' => $region,
        ];
        $res = $this->requestRpc(self::API_CHECK, $data);
        if (!$res) {
            Yii::error('Request ' . self::API_CHECK . ' api fail', __METHOD__);
            return true;
        }
        if (self::CODE_SUCCESS !== $res['code']) {
            // 若请求接口出错，不直接抛出异常避免影响用户，将错误记录到日志中
            Yii::error("请求检查用户手机号是否注册过接口出错：{$res['info']}", __METHOD__);
            return true;
        }
        return self::CODE_SUCCESS !== $res['info']['mobile']['code'];
    }

    /**
     * 检查邮箱是否存在
     *
     * @param string $email 邮箱
     * @return boolean
     */
    public function checkEmailExists(string $email)
    {
        $data = [
            'email' => $email,
        ];
        $res = $this->requestRpc(self::API_CHECK, $data);
        if (!$res) {
            Yii::error('Request ' . self::API_CHECK . ' api fail', __METHOD__);
            return true;
        }
        if (self::CODE_SUCCESS !== $res['code']) {
            // 若请求接口出错，不直接抛出异常避免影响用户，将错误记录到日志中
            Yii::error("请求检查用户邮箱是否注册接口出错：{$res['info']}", __METHOD__);
            return true;
        }
        return self::CODE_SUCCESS !== $res['info']['email']['code'];
    }

    /**
     * 更新用户密码（用户更改密码，用户忘记密码）
     *
     * @param array $data 用户更新需要的数据集合
     * @return array
     */
    public function updatePassword($data)
    {
        if ($data['type'] === self::TYPE_CHANGE_PASSWORD) {
            $data_map = [
                'type' => $data['type'],
                // TODO: 待 Go 接口完善之后去掉旧密码
                'old_password' => $data['old_password'],
                'new_password' => $data['new_password'],
            ];
        } elseif ($data['type'] === self::TYPE_FORGET_PASSWORD) {
            $data_map = [
                'type' => $data['type'],
                'new_password' => $data['new_password'],
            ];
            if (array_key_exists('email', $data)) {
                $data_map = array_merge($data_map, ['email' => $data['email']]);
            } elseif (array_key_exists('mobile', $data)) {
                $data_map = array_merge($data_map,
                    ['mobile' => $data['mobile'], 'region' => $data['region']]);
            }
        } else {
            throw new Exception('参数错误');
        }
        self::addEquipParam($data_map);
        $res = $this->requestRpc(self::API_UPDATE_PASSWORD, $data_map);
        if (!$res) {
            Yii::error('Request ' . self::API_UPDATE_PASSWORD . ' api fail', __METHOD__);
            return true;
        }
        return $res;
    }

    /**
     * 通过 ID 获取用户信息
     *
     * @param integer $user_id 用户 ID
     * @return array|boolean user 数组，若为 false 表示接口返回失败
     */
    public function getUser(int $user_id)
    {
        $data = ['user_id' => $user_id];
        $res = $this->requestRpc(self::API_GET, $data);
        if (self::CODE_SUCCESS !== $res['code']) {
            // 只记录服务器相关错误
            if ($res['code'] !== self::CODE_USER_NOT_EXISTS) {
                // 只记录服务器相关错误
                Yii::error('Request ' . self::API_GET . ' api fail' . ', code: ' . $res['code'] . ', info: ' . $res['info'],
                    __METHOD__);
                return false;
            }
            return false;
        }
        // 为了解决 sso 返回值中可能有不存在的字段，我们将对应的字段设置为空值方便程序直接使用
        $fields = ['email', 'mobile', 'region', 'qquid', 'weibouid', 'bilibiliuid', 'wechatuid', 'appleuid'];
        foreach ($fields as $field) {
            if (!array_key_exists($field, $res['info']) || $res['info'][$field] === '') {
                $res['info'][$field] = null;
            }
        }
        return $res['info'];
    }

    /**
     * 通过邮箱获取用户信息
     *
     * @param string $email 邮箱账号
     * @return array|boolean user 数组，若为 false 表示接口返回失败
     */
    public function getUserInfoByEmail(string $email)
    {
        $data = ['email' => $email];
        $res = $this->requestRpc(self::API_GET, $data);
        if (self::CODE_SUCCESS !== $res['code']) {
            Yii::error('Request ' . self::API_GET .
                " api fail, email: $email, code: {$res['code']}, info: {$res['info']}", __METHOD__);
            return false;
        }
        return $res['info'];
    }

    /**
     * 通过手机号获取用户信息
     *
     * @param integer $mobile 手机号
     * @param integer $region 区号
     * @return array|boolean user 数组，若为 false 表示接口返回失败
     */
    public function getUserInfoByMobile(int $mobile, int $region = 86)
    {
        $data = ['mobile' => $mobile, 'region' => $region];
        $res = $this->requestRpc(self::API_GET, $data);
        if (self::CODE_SUCCESS !== $res['code']) {
            Yii::error('Request ' . self::API_GET .
                " api fail, region: $region, mobile: $mobile, code: {$res['code']}, info: {$res['info']}", __METHOD__);
            return false;
        }
        return $res['info'];
    }

    /**
     * 一键登录
     *
     * @param array $data 参数数组
     * @return array|boolean 用户信息组成的数组，若为 false 表示接口返回失败
     * @throws Exception
     * @throws HttpException
     */
    public function fastLogin($data)
    {
        if (!in_array($data['type'], [self::TYPE_FAST_LOGIN, self::TYPE_SMS_LOGIN, self::TYPE_FAST_AUTH_BIND,
            self::TYPE_SMS_AUTH_BIND])) {
            throw new Exception(Yii::t('app/error', 'params error'));
        }
        self::addEquipParam($data);
        $res = $this->requestRpc(self::API_FAST_LOGIN, $data);
        if (!$res) {
            Yii::error('Request ' . self::API_FAST_LOGIN . ' api fail', __METHOD__);
            return false;
        }
        if (self::CODE_SUCCESS !== $res['code']) {
            if (in_array($res['code'], [self::CODE_AUTH_USER_EXISTS, self::CODE_HIGH_RISK,
                self::CODE_REQUIRE_RESET_PASSWORD, self::CODE_USER_DELETE_STATUS_PENDING])) {
                $error_msg = "请求一键登录接口失败：{$res['info']}，code: {$res['code']}, type: {$data['type']}, buvid: {$data['buvid']}";
                Yii::warning($error_msg, __METHOD__);
                throw new HttpException($res['status'], $res['info'], $res['code']);
            }
            $error = "请求一键登录接口出错：{$res['info']}，code: {$res['code']}, type: {$data['type']}";
            Yii::warning($error, __METHOD__);
            return false;
        }
        if ($res['info']['is_new']) {
            $this->afterRegister($res['info']['user']['user_id'], $data['buvid']);
        }
        return $res['info'];
    }

    private function afterRegister(int $user_id, ?string $buvid)
    {
        AdTrack::callbackRegister($user_id, $buvid);
    }

    /**
     * 获取用户的邮箱，手机和手机区号信息
     *
     * @param int $user_id 用户 ID
     * @return array
     * @deprecated 改成调用 getUser 方法
     */
    public function getUserAccount(int $user_id)
    {
        $data = [
            'user_id' => $user_id
        ];
        $res = $this->requestRpc(self::API_GET, $data);
        $accounts = [
            'email' => null,
            'mobile' => null,
            'region' => null,
        ];
        if (self::CODE_SUCCESS === $res['code'] && $res['info']) {
            $accounts['email'] = $res['info']['email'] ?? null;
            $accounts['mobile'] = $res['info']['mobile'] ?? null;
            $accounts['region'] = $res['info']['region'] ?? null;
        }
        return $accounts;
    }

    /**
     * 获取用户贵族身份
     *
     * @param array $types 贵族类型
     * @param int $user_id 用户 ID
     * @param int $status 是否只含未过期的贵族（0 否，1 是）
     * @return array
     */
    public function getUserVipList(array $types, int $user_id, int $status = 0): array
    {
        $data = [
            'types' => $types,
            'user_ids' => [$user_id],
            'status' => $status,
        ];
        $res = $this->requestRpc(self::API_USER_VIP_LEVEL, $data);
        if (!$res) {
            // PASS: 若请求接口出错，不直接抛出异常避免影响用户，将错误记录到日志中
            Yii::error('Request ' . self::API_USER_VIP_LEVEL . ' api fail', __METHOD__);
            return [];
        }
        if (self::CODE_SUCCESS !== $res['code']) {
            // PASS: 若请求接口出错，不直接抛出异常避免影响用户，将错误记录到日志中
            Yii::error("获取用户贵族身份接口出错：{$res['info']}", __METHOD__);
            return [];
        }

        return $res['info'];
    }

    /**
     * 错误码到错误提示的转换，目前只支持修改密码和忘记密码的操作
     *
     * @param integer $code 错误码
     * @return string 错误提示
     */
    public function codeToMessage(int $code)
    {
        if ($code === 201010001 || $code === 201010002) {
            return Yii::t('app/error', 'params error');
        } elseif ($code === *********) {
            return Yii::t('app/error', 'The password is wrong, please enter again');
        } elseif ($code === self::CODE_USER_TOKEN_EXPIRED) {
            return Yii::t('app/error', 'Login has expired, please login again');
        } else {
            // TODO: 由于英文和 SSO 没关系，考虑修改提示
            return Yii::t('app/error', 'Server connection error');
        }
    }

    /**
     * 抛出异常
     *
     * @param string $api 接口地址，如 /rpc/login
     * @param int $code 错误码
     * @param string $message SSO 错误信息
     * @return void
     * @throws HttpException
     */
    public static function throwException(string $api, int $code, string $message)
    {
        if ($code === self::CODE_USER_NOT_EXISTS || $code === self::CODE_PASSWORD_ERROR) {
            throw new HttpException(400, Yii::t('app/error', 'Incorrect username or password'), 200010001);
        } elseif ($code === self::CODE_USER_TOKEN_EXPIRED) {
            throw new HttpException(401, Yii::t('yii', 'Login Required'), 100010006);
        } elseif ($code === self::CODE_REQUIRE_RESET_PASSWORD) {
            throw new HttpException(403, Yii::t('app/error', 'Reset password required'));
        } else {
            Yii::error('Request ' . $api . ' api fail' . ', code: ' . $code .
                ', info: ' . $message, __METHOD__);
            throw new HttpException(400, $message);
        }
    }

    /**
     * 第三方账号登录
     *
     * @param array $data 参数数组
     * @return array|boolean 用户信息组成的数组，若为 false 表示接口返回失败
     * @throws Exception
     */
    public function authLogin($data)
    {
        if (!in_array($data['auth_type'], [LoginForm::QQ, LoginForm::WEIBO, LoginForm::WECHAT, LoginForm::BILIBILI,
            LoginForm::APPLE])) {
            throw new Exception(Yii::t('app/error', 'params error'));
        }
        self::addEquipParam($data);
        $res = $this->requestRpc(self::API_AUTH_LOGIN, $data);
        if (!$res) {
            Yii::error('Request ' . self::API_AUTH_LOGIN . ' api fail', __METHOD__);
            return false;
        }
        if (self::CODE_SUCCESS !== $res['code']) {
            $error = '请求第三方账号登录接口出错：' . $res['info'];
            $this->logError($res['status'], $error);
            return false;
        }
        return $res['info'];
    }

    /**
     * 添加错误日志，非 5XX 的业务错误只是记录 debug 方便调试
     *
     * @param integer $status 接口请求的状态码
     * @param string $error 错误内容
     */
    protected function logError($status, $error)
    {
        if ($status >= 500) {
            // 若请求接口出错，不直接抛出异常避免影响用户，将错误记录到日志中
            Yii::error($error, __METHOD__);
        } else {
            // 业务上的错误我们使用 DEBUG 等级
            Yii::debug($error, __METHOD__);
        }
    }

    /**
     * 有赞登录
     *
     * @return array|boolean 有赞信息组成的数组，若为 false 表示接口返回失败
     * @throws HttpException
     */
    public function youzanLogin()
    {
        $data = [
            'token' => Yii::$app->user->identity->token,
        ];
        self::addEquipParam($data);
        $res = $this->requestRpc(self::API_YOUZAN_LOGIN, $data);
        if (!$res) {
            Yii::error('Request ' . self::API_YOUZAN_LOGIN . ' api fail', __METHOD__);
            return false;
        }
        if (self::CODE_SUCCESS !== $res['code']) {
            // 若请求接口出错，将错误记录到日志中并抛出异常
            Yii::error("请求有赞登录接口出错：{$res['info']}", __METHOD__);
            throw new HttpException($res['status'], $res['info'], $res['code']);
        }
        return $res['info'];
    }

    /**
     * 有赞退出
     *
     * @return string|boolean 退出成功，若为 false 表示接口返回失败
     * @throws HttpException
     */
    public function youzanLogout()
    {
        $data = [
            'token' => Yii::$app->user->identity->token,
        ];
        self::addEquipParam($data);
        $res = $this->requestRpc(self::API_YOUZAN_LOGOUT, $data);
        if (!$res) {
            Yii::error('Request ' . self::API_YOUZAN_LOGOUT . ' api fail', __METHOD__);
            return false;
        }
        if (self::CODE_SUCCESS !== $res['code']) {
            // 若请求接口出错，将错误记录到日志中并抛出异常
            Yii::error("请求有赞退出接口出错：{$res['info']}", __METHOD__);
            throw new HttpException($res['status'], $res['info'], $res['code']);
        }
        return $res['info'];
    }

    /**
     * 获取用户的 buvid
     *
     * @param int $user_id 用户 ID
     * @param int $from_time 起始时间戳
     * @param int $to_time 结束时间戳
     * @param int $type 类型（-1 全部，0 注册的 buvid，1 登录的 buvid）
     * @return array
     * @throws HttpException
     */
    public function getBuvids(int $user_id, int $from_time, int $to_time, int $type = 0)
    {
        $res = $this->requestRpc(self::API_GET_BUVIDS, [
            'user_id' => $user_id,
            'from_time' => $from_time,
            'to_time' => $to_time,
            'type' => $type,
        ]);
        if (!$res) {
            Yii::error(sprintf('Request %s api fail', self::API_GET_BUVIDS), __METHOD__);
            return [];
        }
        if (self::CODE_SUCCESS !== $res['code']) {
            Yii::error(sprintf('Request %s api fail: %s', self::API_GET_BUVIDS, $res['info']), __METHOD__);
            throw new HttpException($res['status'], $res['info'], $res['code']);
        }

        return $res['info'];
    }

    /**
     * 增加设备参数
     *
     * @param array $data 参数数组
     */
    public static function addEquipParam(&$data)
    {
        $data['user_agent'] = Yii::$app->request->userAgent;
        $data['equip_id'] = Yii::$app->equip->getEquipId();
        $data['buvid'] = Yii::$app->equip->getBuvid();
        $data['channel'] = Yii::$app->equip->getChannel();
        $data['device_type'] = Yii::$app->equip->getDeviceType();
    }

    /**
     * 当用户不存在时创建新用户（手机号），并返回用户信息
     *
     * @param integer $mobile 手机号
     * @param integer $region 区号
     * @return array
     * @throws HttpException
     */
    public function ensure(int $mobile, int $region = 86)
    {
        $data = [
            'mobile' => $mobile,
            'region' => $region,
            'ip' => Yii::$app->request->userIP,
        ];
        self::addEquipParam($data);
        $res = $this->requestRpc(self::API_SSO_ENSURE, $data);
        if (!$res) {
            Yii::error(sprintf('Request %s api fail', self::API_SSO_ENSURE), __METHOD__);
            throw new HttpException(500, '网络错误');
        }
        if (self::CODE_SUCCESS !== $res['code']) {
            Yii::error('Request ' . self::API_SSO_ENSURE . ' api fail' . ', code: ' . $res['code'] .
                ', info: ' . $res['info'], __METHOD__);
            throw new HttpException($res['status'], $res['info'], $res['code']);
        }
        return $res['info'];
    }

    public function takeYouzanCoupon(array|int $user_ids, int $coupon_id)
    {
        if (is_int($user_ids)) {
            $user_ids = [$user_ids];
        }
        $data = [
            'user_ids' => $user_ids,
            'coupon_group_id' => $coupon_id,
        ];
        self::addEquipParam($data);
        $res = $this->requestRpc(self::API_YOUZAN_TAKE_COUPON, $data);
        if (!$res) {
            Yii::error(sprintf('Request %s api fail', self::API_SSO_ENSURE), __METHOD__);
            throw new HttpException(500, '网络错误');
        }
        return $res;
    }

}
