<?php

namespace app\components\util;

use app\forms\TmallPayForm;

class TmallPayException extends \Exception
{
    public $type;
    public $status;
    public $fail_code;
    public $extra;

    public function __construct(string $method = '', string $status = '', string $fail_code = '', string $reason = '',
            array $extra = [])
    {
        $this->type = TmallPayForm::getResponseType($method);
        $this->status = $status;
        $this->fail_code = $fail_code;
        $this->extra = $extra;

        parent::__construct($reason, 0, null);
    }

}
