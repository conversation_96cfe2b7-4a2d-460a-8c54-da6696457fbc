<?php

namespace app\components\models\traits;

use app\components\util\MUtils;
use Yii;

trait AlbumTrait
{
    /**
     * 用户自建或用户收藏音单自定义排序
     *
     * @param int $reorder 是否需要重新排序 0：否；1：是
     * @param int $user_id 用户 ID
     * @param array $positions 音单位置 例：['5218' => 0, '5219' => 2097152]
     * @return bool
     * @throws \Exception
     */
    public static function sortAlbums(int $reorder, int $user_id, array $positions)
    {
        // 自定义排序技术方案文档：https://info.missevan.com/pages/viewpage.action?pageId=53183980
        // 更新排序值
        self::updateAlbumSort($user_id, $positions);
        if ($reorder) {
            // 重新计算排序值
            self::reorderAlbums($user_id);
        }
        return true;
    }

    /**
     * 批量更新音单的顺序值
     *
     * @param int $user_id 用户 ID
     * @param array $positions 音单和新位置信息 例：['5218' => 0, '5219' => 2097152]
     * @return bool
     * @throws \Exception
     */
    private static function updateAlbumSort(int $user_id, array $positions)
    {
        if (!$positions) {
            return false;
        }
        $table_name = self::tableName();
        $album_id_column = self::ALBUM_ID_COLUMN;
        // 生成的批量更新单集顺序的 SQL 如下：
        // UPDATE m_album
        // SET sort = CASE id
        //   WHEN 5218 THEN 0
        //   WHEN 5219 THEN 2097152
        // END
        // WHERE id IN (521888, 521889)
        $album_ids = array_keys($positions);
        $in_condition = MUtils::generateIntegerIn($album_id_column, $album_ids);
        $case_value = "CASE $album_id_column";
        foreach ($positions as $album_id => $sort) {
            $case_value .= " WHEN $album_id THEN $sort ";
        }
        $case_value .= 'END';
        $sql = <<<SQL
UPDATE {$table_name} 
SET sort = {$case_value}
WHERE user_id = {$user_id} AND {$in_condition};
SQL;
        return (bool)Yii::$app->db->createCommand($sql)->execute();
    }

    /**
     * 重新计算音单的顺序值
     *
     * @param int $user_id 用户 ID
     * @return bool
     * @throws \Exception
     */
    private static function reorderAlbums(int $user_id)
    {
        $album_id_column = self::ALBUM_ID_COLUMN;
        $album_ids = self::find()->select($album_id_column)->where(['user_id' => $user_id])
            ->orderBy('sort ASC')
            ->column();
        $case_value = "CASE $album_id_column";
        $sort = 0;
        foreach ($album_ids as $album_id) {
            $case_value .= " WHEN $album_id THEN $sort ";
            $sort += self::SORT_INTERVAL;
        }
        $table_name = self::tableName();
        $case_value .= 'END';
        $in_album_condition = MUtils::generateIntegerIn($album_id_column, $album_ids);
        $sql = <<<SQL
            UPDATE {$table_name}
            SET sort = {$case_value}
            WHERE user_id = {$user_id} AND {$in_album_condition};
SQL;
        return (bool)Yii::$app->db->createCommand($sql)->execute();
    }

    /**
     * 获取首个音单的排序值
     *
     * @param int $user_id
     * @return int
     */
    public static function getFirstAlbumSort(int $user_id): int
    {
        $first_album_sort = self::find()
            ->select('sort')
            ->where(['user_id' => $user_id])
            ->orderBy('sort ASC')
            ->limit(1)
            ->scalar();
        // 确保排在第一的音单排序值为 0
        $first_album_sort = (is_null($first_album_sort) || false === $first_album_sort)
            ? self::SORT_INTERVAL
            : (int)$first_album_sort;
        return $first_album_sort;
    }
}
