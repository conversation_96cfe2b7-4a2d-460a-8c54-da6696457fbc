<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/8/1
 * Time: 12:38
 */

namespace app\components\models\traits;

use app\components\util\MUtils;
use app\models\Mowangskuser;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\web\HttpException;

// NOTICE: 尽量避免 afterFind 有特殊处理字段，初次保存数据后需要重新加载数据，保存后如果有特殊要处理的可以放到 afterSave 中
trait UserTrait
{
    public static function getByPk($id)
    {
        if ($id < 0) {
            throw new HttpException(404, Yii::t('app/error', 'User does not exist'), 200020001);
        }
        $instance = null;
        if ($instance = static::findOne($id)) {
            return $instance;
        }

        if (!Mowangskuser::find()->where(['id' => $id])->exists()) {
            throw new HttpException(404, Yii::t('app/error', 'User does not exist'), 200020001);
        }
        $redis = Yii::$app->redis;
        $class = static::class;
        $key = $redis->generateKey(LOCK_GENERATE_CLASS, $class, $id);
        try {
            if ($redis->lock($key, 1)) {
                $instance = new static(['id' => $id]);
                try {
                    if (!$instance->save()) {
                        throw new \Exception(MUtils::getFirstError($instance));
                    }
                } catch (yii\db\Exception $e) {
                    if (!MUtils2::isUniqueError($e, static::getDb())) {
                        // 忽略唯一索引抛出的异常
                        throw $e;
                    }
                    $instance = null;
                }
            }
            if (!$instance) {
                // 若为加锁失败的情况（并发），则延迟 1 秒进行查询
                sleep(1);
                $instance = static::findOne($id);
            }
            if (!$instance) {
                throw new \Exception(Yii::t('yii', '{:class} {:id} generate failed', [
                    ':class' => static::class,
                    ':id' => $id
                ]));
            }
        } finally {
            $redis->unlock($key);
        }
        return $instance;
    }
}
