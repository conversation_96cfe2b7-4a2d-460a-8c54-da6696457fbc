<?php
/**
 * Created by PhpStorm.
 * User: jiepe
 * Date: 2017/8/29
 * Time: 14:08
 */

namespace app\components\auth;

use app\components\util\Equipment;
use app\components\util\ModelTrait;
use app\components\util\MUtils;
use app\models\Mowangskuser;
use app\models\MVip;
use app\models\RechargeOrder;
use app\models\VipFeeDeductedRecord;
use app\models\VipSubscriptionSignAgreement;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\RequestException;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\helpers\Json;
use yii\web\HttpException;

class AuthWechat
{
    use ModelTrait;

    // 人民币元转分比例
    const RMB_YUAN_FEN_RATE = 100;

    // 交易类型
    const TRADE_TYPE_PUBLIC_ACCOUNT = 'JSAPI'; // 公众号支付
    const TRADE_TYPE_APP = 'APP'; // app 支付
    const TRADE_TYPE_NATIVE_SCAN = 'NATIVE'; // 原生扫码支付
    const TRADE_TYPE_MOBILE_WEB = 'MWEB'; // 手机网页支付

    // 微信授权登录
    const AUTHORIZE_GATEWAY = 'https://open.weixin.qq.com/connect/oauth2/authorize';
    // 授权 code 换取 access_token
    const ACCESS_TOKEN_GATEWAY = 'https://api.weixin.qq.com/sns/oauth2/access_token';

    // 协议改变类型
    const AGREEMENT_CHANGE_TYPE_ADD = 'ADD';  // 签约
    const AGREEMENT_CHANGE_TYPE_DELETE = 'DELETE';  // 解约

    private static $_client = false;

    public function __construct()
    {
        if (false === self::$_client) {
            self::$_client = new Client([
                'base_uri' => 'https://api.mch.weixin.qq.com/',
                'timeout' => 5.0
            ]);
        }
    }

    public function tradeAppPay(RechargeOrder $order)
    {
        $nonce_str = MUtils::randomKeys(30, 7);
        $appid = self::getAppId();
        $mch_id = self::getMchId($appid);
        $form = [
            'appid' => $appid,
            'body' => '猫耳FM--购买钻石',
            'mch_id' => $mch_id,
            'nonce_str' => $nonce_str,
            'notify_url' => WECHATPAY_CALLBACK,
            'out_trade_no' => $order->getOrderId(),
            'spbill_create_ip' => Yii::$app->request->userIP,
            'total_fee' => $order->priceInFen(),
            'trade_type' => 'APP',
        ];

        $content = $this->_request('pay/unifiedorder', $form);

        $xml_array = (array)simplexml_load_string($content, "SimpleXMLElement", LIBXML_NOCDATA);

        if ($xml_array['return_code'] !== 'SUCCESS') {
            throw new Exception('创建订单失败：' . json_encode($xml_array));
        }

        $pay = [
            'appid' => $xml_array['appid'],
            'noncestr' => $nonce_str,
            'package' => 'Sign=WXPay',
            'partnerid' => $xml_array['mch_id'],
            'prepayid' => $xml_array['prepay_id'],
            'timestamp' => time()
        ];

        $pay['sign'] = self::getWechatSign($pay);

        return $pay;
    }

    public function tradePagePay(RechargeOrder $order)
    {
        $nonce_str = MUtils::randomKeys(30, 7);
        $form = [
            'appid' => WECHATPAY_APP_ID,
            'mch_id' => WECHATPAY_MCH_ID,
            'nonce_str' => $nonce_str,
            'body' => '猫耳FM--购买钻石',
            'notify_url' => WECHATPAY_CALLBACK,
            'out_trade_no' => $order->getOrderId(),
            'spbill_create_ip' => Yii::$app->request->userIP,
            'total_fee' => $order->priceInFen(),
            'trade_type' => 'NATIVE',
        ];

        $content = $this->_request('pay/unifiedorder', $form);

        $xml_array = (array)simplexml_load_string($content, "SimpleXMLElement", LIBXML_NOCDATA);

        if ($xml_array['return_code'] !== 'SUCCESS') throw new \Exception('创建订单失败');

        return $xml_array['code_url'];
    }

    public function tradeMobileWebPay(RechargeOrder $order)
    {
        $nonce_str = MUtils::randomKeys(30, 7);
        $order_id = $order->getOrderId();
        $form = [
            'appid' => WECHATPAY_APP_ID,
            'mch_id' => WECHATPAY_MCH_ID,
            'nonce_str' => $nonce_str,
            'body' => '猫耳FM--购买钻石',
            'notify_url' => WECHATPAY_CALLBACK,
            'out_trade_no' => $order_id,
            'spbill_create_ip' => Yii::$app->request->userIP,
            'total_fee' => $order->priceInFen(),
            'trade_type' => self::TRADE_TYPE_MOBILE_WEB,
        ];

        $content = $this->_request('pay/unifiedorder', $form);

        $xml_array = (array)simplexml_load_string($content, "SimpleXMLElement", LIBXML_NOCDATA);

        if ($xml_array['return_code'] !== 'SUCCESS') throw new \Exception('创建订单失败');

        $redirect_url = urlencode(Yii::$app->params['domainMobileWeb'] . "/wallet/topupresult?out_trade_no={$order_id}");
        $mweb_url = $xml_array['mweb_url'] . '&redirect_url=' . $redirect_url;
        return $mweb_url;
    }

    private function _request($path, $form)
    {
        $form['sign'] = self::getWechatSign($form);
        $body = MUtils::arrayToXml($form, true);
        try {
            $resp = self::$_client->request('POST', $path, [
                'body' => $body
            ]);
            $body = $resp->getBody();
            return $body->getContents();
        } catch (ClientException $e) {
            throw new \Exception('微信支付服务器连接错误');
        }
    }

    public static function getWechatSign($params)
    {
        $key = WECHATPAY_KEY;
        if (isset($params['appid'])) {
            $key = self::getKey($params['appid']);
        }
        ksort($params);
        $str = urldecode(http_build_query($params)) . '&key=' . $key;
        return strtoupper(md5($str));
    }

    private function getAppId()
    {
        if (Equipment::isConceptVersion()) {
            return WECHATPAY_APP_ID_CONCEPT_CHANNEL;
        }
        return WECHATPAY_APP_ID;
    }

    private function getMchId(string $app_id)
    {
        switch ($app_id) {
            case WECHATPAY_APP_ID:
            case WECHATPAY_APP_ID_CONCEPT_CHANNEL:
                return WECHATPAY_MCH_ID;
            default:
                Yii::error(sprintf('wechat get mch id error: app_id[%s]', $app_id), __METHOD__);
                throw new Exception('未找到对应配置');
        }
    }

    private static function getKey(string $app_id)
    {
        switch ($app_id) {
            case WECHATPAY_APP_ID:
            case WECHATPAY_APP_ID_CONCEPT_CHANNEL:
                return WECHATPAY_KEY;
            default:
                Yii::error(sprintf('wechat get key error: app_id[%s]', $app_id), __METHOD__);
                throw new Exception('未找到对应配置');
        }
    }

    /**
     * 校验微信登录参数
     *
     * @param string $openid 微信登录 openid
     * @param string $access_token 微信登录 token
     * @param integer $type 设备类型
     * @return bool|array
     */
    public function auth($openid, $access_token, $type)
    {
        $form = [
            'access_token' => $access_token,
            'openid' => $openid,
            'lang' => 'zh-CN',
        ];
        try {
            $client = new Client([
                'base_uri' => 'https://api.weixin.qq.com/',
                'timeout' => 5.0,
            ]);
            $res = $client->request('POST', 'sns/userinfo', [
                'form_params' => $form
            ]);
            if (200 === $res->getStatusCode()) {
                $content = json_decode($res->getBody());
                if (isset($content->unionid)) {
                    return [
                        'nickname' => $content->nickname,
                        'iconurl' => $content->headimgurl,
                        'unionid' => $content->unionid,
                    ];
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } catch (\Exception $e) {
            return false;
        }
    }

    public static function getWechatPayFailCallbackResponse()
    {
        return <<<XML
<xml>
  <return_code><![CDATA[FAIL]]></return_code>
  <return_msg><![CDATA[failed]]></return_msg>
</xml>
XML;
    }

    public static function getWechatPaySuccessCallbackResponse()
    {
        return <<<XML
<xml>
  <return_code><![CDATA[SUCCESS]]></return_code>
  <return_msg><![CDATA[OK]]></return_msg>
</xml>
XML;
    }

    /**
     * 获取静默授权登录地址
     *
     * @param int $user_id
     * @return string
     */
    public static function getSilentAuthorizeUrl(int $user_id)
    {
        $jsapi = Yii::$app->params['service']['wechatpay']['jsapi'];
        $query = http_build_query([
            'appid' => $jsapi['app_id'],
            'redirect_uri' => $jsapi['authorize_redirect_url'],
            'response_type' => 'code',
            'scope' => 'snsapi_base',
            'state' => self::generateAuthorizeUrlState($user_id),
        ]);

        return self::AUTHORIZE_GATEWAY . '?' . $query . '#wechat_redirect';
    }

    private static function generateAuthorizeUrlState(int $user_id)
    {
        $state_key = MUtils::generateCacheKey(KEY_WECHAT_PUBLIC_ACCOUNT_AUTHORIZE_URL_STATE, $user_id);
        $state_value = uniqid($user_id);
        Yii::$app->memcache->set($state_key, $state_value, FIVE_MINUTE);
        return $state_value;
    }

    public static function validateAuthorizeUrlState(int $user_id, string $state)
    {
        $memcache = Yii::$app->memcache;
        $state_key = MUtils::generateCacheKey(KEY_WECHAT_PUBLIC_ACCOUNT_AUTHORIZE_URL_STATE, $user_id);
        if ($memcache->get($state_key) === $state) {
            $memcache->delete($state_key);
            return true;
        }

        return false;
    }

    /**
     * 微信授权码换取 access_token
     *
     * @param string $authorize_code
     * @return string
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function authorizeCodeToAccessToken(string $authorize_code)
    {
        try {
            $jsapi = Yii::$app->params['service']['wechatpay']['jsapi'];
            $client = new Client(['timeout' => 5.0]);
            $resp = $client->request('GET', self::ACCESS_TOKEN_GATEWAY, [
                'query' => [
                    'appid' => $jsapi['app_id'],
                    'secret' => $jsapi['app_secret'],
                    'code' => $authorize_code,
                    'grant_type' => 'authorization_code',
                ],
            ]);
            if ($resp->getStatusCode() !== 200) {
                throw new Exception('authorize code to access_token fail');
            }

            return Json::decode($resp->getBody()->getContents());
        } catch (Exception $e) {
            $message = $e->getMessage();
            if ($e instanceof RequestException && $e->hasResponse()) {
                $error_resp = $e->getResponse();
                $message .= sprintf(' status_code[%d], body[%s]', $error_resp->getStatusCode(), $error_resp->getBody()->getContents());
            }
            Yii::error($message, __METHOD__);
            throw new Exception('服务器繁忙，请稍候重试');
        }
    }

    /**
     * App 开通点播会员下单
     *
     * @param VipFeeDeductedRecord $order 支付记录
     * @param VipSubscriptionSignAgreement|null $sign_agreement 签约记录（单次付费时为 null）
     * @param MVip $vip 价目信息
     * @param Mowangskuser $user 用户信息
     * @param bool $is_subscription 是否是订阅付费（连续包月、连续包季）
     * @return array
     * @throws Exception
     */
    public function tradeVipAppPay(VipFeeDeductedRecord $order, ?VipSubscriptionSignAgreement $sign_agreement,
            MVip $vip, Mowangskuser $user, bool $is_subscription)
    {
        $nonce_str = MUtils::randomKeys(30, 7);
        $appid = self::getAppId();
        $mch_id = self::getMchId($appid);
        $user_ip = Yii::$app->request->userIP;
        $trade_no = $order->getOutTradeNo();
        $order_title = $vip->getOrderSubject();
        if (is_null($order_title)) {
            Yii::error(sprintf('会员价目未配置订单标题, 价目 ID: %d', $vip->id),
                __METHOD__);
            throw new HttpException(500, '服务器内部错误');
        }
        if ($is_subscription) {
            // 订阅付费（连续包月、连续包季）走支付中签约接口
            $plan_id = $vip->getWechatPlanId();
            if (is_null($plan_id)) {
                Yii::error(sprintf('会员价目未配置微信协议代扣模板 ID, 价目 ID: %d', $vip->id),
                    __METHOD__);
                throw new HttpException(500, '服务器内部错误');
            }
            $form = [
                'appid' => $appid,
                'mch_id' => $mch_id,
                'contract_mchid' => $mch_id,
                'contract_appid' => $appid,
                'out_trade_no' => $trade_no,
                'nonce_str' => $nonce_str,
                'body' => $order_title,
                'notify_url' => WECHATPAY_CALLBACK,
                'total_fee' => $order->price,
                'spbill_create_ip' => $user_ip,
                'trade_type' => self::TRADE_TYPE_APP,
                'plan_id' => $plan_id,
                'contract_code' => $sign_agreement->getOutAgreementNo(),
                'request_serial' => $order->id + $_SERVER['REQUEST_TIME'],
                // 签约用户的名称，用于页面展示，参数值不支持 UTF8 非 3 字节编码的字符，例如表情符号
                // 由于用户昵称可能包含表情符号等特殊字符，直接使用用户昵称会导致下单失败，因此使用唯一M号用于页面展示
                'contract_display_account' => "M号：{$user->id}",
                'contract_notify_url' => WECHATPAY_SIGN_VIP_CALLBACK,
            ];
            $wechat_return = $this->payContractOrder($form);
        } else {
            // 单次付费走统一下单接口
            $form = [
                'appid' => $appid,
                'mch_id' => $mch_id,
                'body' => $order_title,
                'nonce_str' => $nonce_str,
                'notify_url' => WECHATPAY_CALLBACK,
                'out_trade_no' => $trade_no,
                'spbill_create_ip' => $user_ip,
                'total_fee' => $order->price,
                'trade_type' => self::TRADE_TYPE_APP,
            ];
            $wechat_return = $this->payUnifiedorder($form);
        }
        $wechatpay_body = [
            'appid' => $wechat_return['appid'],
            'partnerid' => $wechat_return['mch_id'],
            'prepayid' => $wechat_return['prepay_id'],
            'package' => 'Sign=WXPay',
            'noncestr' => $nonce_str,
            'timestamp' => $_SERVER['REQUEST_TIME'],
        ];
        $wechatpay_body['sign'] = self::getWechatSign($wechatpay_body);
        return [
            'trade_no' => $trade_no,
            'wechatpay_body' => $wechatpay_body
        ];
    }

    /**
     * 请求统一下单接口
     *
     * @link https://pay.weixin.qq.com/wiki/doc/api/app/app.php?chapter=9_1
     * @param array $form 请求参数
     * @return array
     * @throws Exception
     */
    public function payUnifiedOrder(array $form)
    {
        $content = $this->_request('pay/unifiedorder', $form);
        $xml_array = (array)simplexml_load_string($content, 'SimpleXMLElement', LIBXML_NOCDATA);
        if ($xml_array['return_code'] !== 'SUCCESS' || $xml_array['result_code'] !== 'SUCCESS') {
            Yii::error(sprintf('创建订单失败：%s, %s', Json::encode($xml_array), Json::encode($form)), __METHOD__);
            throw new Exception('创建订单失败');
        }
        // 请求和接收数据均需要校验签名，详细方法请参考: https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=4_3
        $res_sign = $xml_array['sign'];
        unset($xml_array['sign']);
        $sign = self::getWechatSign($xml_array);
        if ($sign !== $res_sign) {
            Yii::error(sprintf('微信统一下单接口返回结果中签名校验失败：%s, %s, %s',
                $sign, $res_sign, Json::encode($xml_array)), __METHOD__);
            throw new Exception('创建订单失败');
        }
        return $xml_array;
    }

    /**
     * 请求支付中签约接口
     *
     * @link https://pay.weixin.qq.com/wiki/doc/api/wxpay_v2/papay/chapter3_5.shtml
     * @param array $form 请求参数
     * @return array
     * @throws Exception
     */
    public function payContractOrder(array $form)
    {
        $content = $this->_request('pay/contractorder', $form);
        $xml_array = (array)simplexml_load_string($content, 'SimpleXMLElement', LIBXML_NOCDATA);
        if ($xml_array['return_code'] !== 'SUCCESS' || $xml_array['result_code'] !== 'SUCCESS') {
            Yii::error(sprintf('支付中签约失败：%s, %s', Json::encode($xml_array), Json::encode($form)), __METHOD__);
            throw new Exception('支付中签约失败');
        }
        // 请求和接收数据均需要校验签名，详细方法请参考: https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=4_3
        $res_sign = $xml_array['sign'];
        unset($xml_array['sign']);
        $sign = self::getWechatSign($xml_array);
        if ($sign !== $res_sign) {
            Yii::error(sprintf('微信支付中签约接口返回结果中签名校验失败：%s, %s, %s',
                $sign, $res_sign, Json::encode($xml_array)), __METHOD__);
            throw new Exception('支付中签约失败');
        }
        return $xml_array;
    }

    /**
     * 申请解约
     *
     * @link https://pay.weixin.qq.com/wiki/doc/api/wxpay_v2/papay/chapter3_9.shtml
     * @param string $contract_id 微信签约协议 ID
     * @param string $reason 解约原因
     * @return bool
     */
    public function deleteContract(string $contract_id, string $reason)
    {
        try {
            $appid = self::getAppId();
            $mch_id = self::getMchId($appid);
            $form = [
                'appid' => $appid,
                'mch_id' => $mch_id,
                'contract_id' => $contract_id,
                'contract_termination_remark' => $reason,
                'version' => '1.0',  // 固定值 1.0
            ];
            $content = $this->_request('papay/deletecontract', $form);
            $xml_array = (array)simplexml_load_string($content, 'SimpleXMLElement', LIBXML_NOCDATA);
            if ($xml_array['return_code'] !== 'SUCCESS' || $xml_array['result_code'] !== 'SUCCESS') {
                throw new Exception(sprintf('申请解约接口失败：%s', Json::encode($xml_array)));
            }
            // 请求和接收数据均需要校验签名，详细方法请参考: https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=4_3
            $res_sign = $xml_array['sign'];
            unset($xml_array['sign']);
            $sign = self::getWechatSign($xml_array);
            if ($sign !== $res_sign) {
                throw new Exception(sprintf('验签失败：%s, %s, %s', $sign, $res_sign, Json::encode($xml_array)));
            }
            return true;
        } catch (Exception $e) {
            Yii::error(sprintf('微信申请解约失败：%s，%s', $contract_id, $e->getMessage()), __METHOD__);
            return false;
        }
    }
}
