<?php

namespace app\components\auth;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use GuzzleHttp\Client;
use Yii;

/**
 * Class AppleAppStoreServerAPIUtil
 * @package app\components\auth
 * @link https://developer.apple.com/documentation/appstoreserverapi
 */
class AppleAppStoreServerAPIUtil
{
    const TYP = 'JWT';  // token 签名类型
    const AUD = 'appstoreconnect-v1';  // 受众，固定值
    const ALGORITHM = 'ES256';  // 加密方式

    public $kid;  // 密钥 ID
    public $private_key;  // 密钥
    public $iss;  // 发卡机构 ID
    public $bid;  // Bundle ID

    public $base_uri;  // 网关地址
    public $proxy;  // 代理

    private static $_instance;
    private $_client;

    public function __construct(array $config)
    {
        $this->kid = $config['kid'];
        $this->private_key = $config['private_key'];
        $this->iss = $config['iss'];
        $this->bid = $config['bid'];

        if (array_key_exists('proxy', $config)) {
            $this->proxy = $config['proxy'];
        }

        $client_config = [
            'base_uri' => $this->base_uri,
            'timeout' => 15.0,
        ];
        if ($this->proxy) {
            $client_config['proxy'] = $this->proxy;
        }
        $this->_client = new Client($client_config);
    }

    public static function instance()
    {
        if (!self::$_instance) {
            $conf = Yii::$app->params['apple']['appstore_server_api'];
            self::$_instance = new self($conf);
        }

        return self::$_instance;
    }

    /**
     * https://developer.apple.com/documentation/appstoreserverapi/jwsdecodedheader \
     * https://developer.apple.com/documentation/appstoreserverapi/jwstransactiondecodedpayload
     *
     * @param string $signed_payload 加密的消息
     * @return \stdClass
     */
    public static function decodeSignedPayload(string $signed_payload)
    {
        $header = explode('.', $signed_payload, 2)[0];
        $header_json = json_decode(base64_decode($header), true);

        $pub_key = "-----BEGIN CERTIFICATE-----\n" . $header_json['x5c'][0] . "\n-----END CERTIFICATE-----";
        return JWT::decode($signed_payload, new Key($pub_key, $header_json['alg']));
    }

    public function requestGet(string $api, array $params)
    {
        $options = [];
        if ($params) {
            $options['query'] = $params;
        }

        return $this->request('GET', $api, $options);
    }

    public function requestPost(string $api, array $params)
    {
        $options = [];
        if ($params) {
            $options['body'] = $params;
        }

        return $this->request('POST', $api, $options);
    }

    public function request(string $method, string $api, array $options = [])
    {
        $token = $this->buildToken();
        $options['headers']['Authorization'] = "Bearer $token";

        $response = $this->_client->request($method, $api, $options);
        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * 获取请求的 token
     * 参考文档：https://developer.apple.com/documentation/appstoreserverapi/generating_tokens_for_api_requests
     *
     * @return string token
     */
    private function buildToken(): string
    {
        $payload = [
            'iss' => $this->iss,
            'iat' => $_SERVER['REQUEST_TIME'],
            'exp' => $_SERVER['REQUEST_TIME'] + 60,
            'aud' => self::AUD,
            'bid' => $this->bid,
        ];
        $header = [
            'alg' => self::ALGORITHM,
            'kid' => $this->kid,
            'typ' => self::TYP
        ];
        return JWT::encode($payload, $this->private_key, self::ALGORITHM, $this->kid, $header);
    }

}
