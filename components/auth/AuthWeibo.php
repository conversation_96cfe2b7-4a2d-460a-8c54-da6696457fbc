<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/8/11
 * Time: 17:34
 */

namespace app\components\auth;


use app\components\util\Equipment;
use GuzzleHttp\Client;
use Symfony\Component\DomCrawler\Field\InputFormField;
use Yii;
use yii\helpers\Json;

class AuthWeibo implements AuthInterface
{
    private static $_client = false;

    public function __construct()
    {
        if (false === self::$_client) {
            self::$_client = new Client([
                'base_uri' => 'https://api.weibo.com',
                'timeout' => 2.0
            ]);
        }
    }

    /**
     * 获取 uid 对应的微博用户信息
     * NOTICE: access_token 仅作鉴权使用，access_token 与 uid 不匹配时仍可获取 uid 对应用户信息
     *
     * @param string $uid 微博登录 uid
     * @param string $access_token 微博登录 token
     * @param integer $type 设备类型
     * @return bool|array
     */
    public function auth($uid, $access_token, $type)
    {
        $form = [
            'access_token' => $access_token,
            'uid' => $uid,
        ];
        try {
            $content = $this->_request('/2/users/show.json', $form);
            return [
                'nickname' => $content['name'],
                'iconurl' => $content['avatar_large'],
            ];
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 验证 uuid 与 access_token 是否匹配
     *
     * @param string $uuid 微博登录 uid
     * @param string $access_token 微博登录 token
     * @return bool 是否匹配
     */
    public function validateUuid(string $uuid, string $access_token): bool
    {
        if (!$uuid) {
            return false;
        }
        $access_token_uuid = $this->getUuidByToken($access_token);
        return $access_token_uuid === $uuid;
    }

    /**
     * 获取微博登录的 access_token 对应的 uid
     *
     * @param string $access_token 微博登录 token
     * @return string 第三方用户 uid，若请求失败返回空字符串
     */
    private function getUuidByToken(string $access_token): string
    {
        if ($access_token === '') {
            return '';
        }
        $form = [
            'access_token' => $access_token,
        ];
        try {
            $content = $this->_request('/oauth2/get_token_info', $form, self::METHOD_POST);
            return $content['uid'] ?? '';
        } catch (\Exception $e) {
            Yii::error('获取微博 uid 失败：' . $e->getMessage(), __METHOD__);
            return '';
        }
    }

    private function _request($path, $form, $method = self::METHOD_GET)
    {
        $options = [];
        switch ($method) {
            case self::METHOD_GET:
                $options['query'] = $form;
                break;
            case self::METHOD_POST:
                $options['form_params'] = $form;
                break;
            default:
                throw new \Exception('不支持的微博 api 请求方式');
        }
        $resp = self::$_client->request($method, $path, $options);
        $body = $resp->getBody();
        $content = $body->getContents();
        $content = Json::decode($content);
        return $content;
    }
}
