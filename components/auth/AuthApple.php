<?php

namespace app\components\auth;

use Exception;
use Firebase\JWT\JWK;
use Firebase\JWT\JWT;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use yii\helpers\Json;
use Yii;

class AuthApple
{
    const AUTH_KEYS_GATEWAY = 'https://appleid.apple.com/auth/keys';

    /**
     * 获取用于解密 JWT 的公钥
     *
     * @link https://developer.apple.com/documentation/signinwithapplerestapi/fetch_apple_s_public_key_for_verifying_token_signature
     * @return array
     * 例 [['kty' => 'RSA', 'kid' => 'eXaunmL', 'use' => 'sig', 'alg' => 'RS256', 'n' => 'GQ7bQ...Od', 'e' => 'AQAB']]
     */
    public static function getJWKSetKeys()
    {
        try {
            $client = new Client();
            $res = $client->request('GET', self::AUTH_KEYS_GATEWAY);
            if (200 === $res->getStatusCode()) {
                $content = Json::decode($res->getBody());
                return $content;
            } else {
                Yii::error('Apple 登录获取公钥请求失败', __METHOD__);
                return [];
            }
        } catch (ClientException $e) {
            Yii::error(sprintf('Apple 登录获取公钥请求失败: %s', $e->getMessage()), __METHOD__);
            return [];
        }
    }

    /**
     * 解析用户信息
     *
     * @link https://developer.apple.com/documentation/signinwithapplerestapi/authenticating_users_with_sign_in_with_apple
     * @link https://segmentfault.com/a/1190000020840290
     * @param string $identity_token
     * @return \stdClass|false
     * 例：
     *   stdClass Object
     *   (
     *     [iss] => https://appleid.apple.com
     *     [aud] => com.missevan.CatEarFM
     *     [exp] => 1584955292
     *     [iat] => 1584954692
     *     [sub] => 001736.01990f853ad648e29484fd97a7a779b3.0451
     *     [c_hash] => Nywaym7s3yFE18qYfsb1rw
     *     [email] => <EMAIL>
     *     [email_verified] => true
     *     [is_private_email] => true
     *     [auth_time] => 1584954692
     *     [nonce_supported] => 1
     *   )
     */
    public static function parseUserIdentity(string $identity_token)
    {
        try {
            $tks = explode('.', $identity_token);
            if (count($tks) !== 3) {
                throw new Exception('非法 token');
            }
            $keys = self::getJWKSetKeys();
            $user_identity = JWT::decode($identity_token, JWK::parseKeySet($keys));
            return $user_identity ?: false;
        } catch (Exception $e) {
            Yii::error(sprintf('Apple 解析用户信息失败: %s', $e->getMessage()), __METHOD__);
            return false;
        }
    }

    /**
     * @param string $uid 登录 uid
     * @param string $identity_token 授权登录 token
     * @param integer $os 设备类型
     * @return bool|array
     */
    public function auth($uid, $identity_token, $os)
    {
        try {
            $content = self::parseUserIdentity($identity_token);
            return [
                'nickname' => Yii::$app->request->post('username') ?: null,
                'iconurl' => '',
                'sub' => $content->sub,
            ];
        } catch (\Exception $e) {
            return false;
        }
    }

}
