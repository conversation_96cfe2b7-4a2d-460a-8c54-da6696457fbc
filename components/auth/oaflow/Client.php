<?php

namespace app\components\auth\oaflow;

use Exception;
use yii\helpers\Json;
use yii\web\Request;

class Client
{

    /**
     * @var string
     */
    private $_appId;
    /**
     * @var string
     */
    private $_appSecret;

    public function __construct(string $app_id, string $app_secret)
    {
        $this->_appId = $app_id;
        $this->_appSecret = $app_secret;
    }

    /**
     * 校验事件订阅的 SHA1 签名
     *
     * @link https://eeapi.bilibili.co/open-doc/zh/document/flow/eventcallback/%E4%BA%8B%E4%BB%B6%E5%AE%89%E5%85%A8%E6%A0%A1%E9%AA%8C.html
     *
     * @param Request $request
     * @return bool
     */
    public function verifyEventSignature(Request $request)
    {
        $url = $request->url;
        $method = $request->getMethod();
        $appkey = $request->headers->get('x-appkey');
        $timestamp = $request->headers->get('x-timestamp');
        $signature = $request->headers->get('x-signature');

        if ($appkey !== $this->_appId) {
            throw new Exception('未知的 app_id: ' . $this->_appId);
        }

        $str_to_sign = $url . '@' . $method . '@' . $appkey . '@' . $timestamp;
        $expected_signature = base64_encode(hash_hmac('sha1', $str_to_sign, $this->_appSecret, true));
        return hash_equals($expected_signature, $signature);
    }

}
