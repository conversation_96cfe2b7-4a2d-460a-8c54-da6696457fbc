<?php

namespace app\components\auth\oaflow;
use Yii;
use yii\helpers\Json;

class EventBody
{
    private $_body;

    public function __construct(array $body)
    {
        $this->_body = $body;
    }

    public function isTestNotify()
    {
        return ($this->_body['checkTest'] ?? 0) === 1;
    }

    public function getOrderId()
    {
        return $this->_body['args']['basicInfo']['orderId'];
    }

    /**
     * 用户工单提交的内容
     *
     * @return array
     */
    public function getForm()
    {
        return $this->_body['args']['form'];
    }

    /**
     * 工单唯一标识（发起工单时手动设置）
     *
     * @return string
     */
    public function getIdempotentId()
    {
        return $this->_body['args']['idempotentId'] ?? null;
    }

    public function getActionInfo()
    {
        return $this->_body['args']['actionInfo'];
    }

    /**
     *  事件回调唯一标识
     *
     * @return string
     */
    public function getCallbackIdempotentId()
    {
        return $this->_body['callbackIdempotentId'];
    }

    public function getNextTasks()
    {
        return $this->_body['nextTasks'];
    }

    /**
     * 是否为流程新发起事件
     *
     * @return bool
     */
    public function isLaunchEvent()
    {
        $action_info = $this->getActionInfo();
        return $action_info['action'] === 'execute' && ($action_info['auditComment'] ?? null) === 'start';
    }

    /**
     * 流程是否是驳回事件
     *
     * @return bool
     */
    public function isRejectedEvent()
    {
        $action_info = $this->getActionInfo();
        return $action_info['action'] === 'reject';
    }

    /**
     * 流程是否被撤回事件
     *
     * @return bool
     */
    public function isRevokedEvent()
    {
        $action_info = $this->getActionInfo();
        return $action_info['action'] === 'back';
    }

    /**
     * 是否为抄送事件
     *
     * @return bool
     */
    public function isCcEvent()
    {
        $action_info = $this->getActionInfo();
        return $action_info['cc'] === 'back';
    }

    /**
     * 是否审批通过事件
     *
     * @return bool
     */
    public function isApprovedEvent()
    {
        $action_info = $this->getActionInfo();
        return $action_info['action'] === 'execute' && !$this->isLaunchEvent();
    }

    /**
     * 是否工单废弃事件
     *
     * @return bool
     */
    public function isTerminated()
    {
        return $this->getActionInfo()['action'] === 'terminate';
    }

    /**
     * 是否审批完结事件
     *
     * @return bool
     */
    public function isSuccessfullyFinished()
    {
        return $this->isApprovedEvent() && count($this->getNextTasks()) === 0;
    }

    /**
     * @link https://eeapi.bilibili.co/open-doc/zh/document/flow/eventcallback/%E9%87%8D%E8%AF%95%E6%9C%BA%E5%88%B6.html
     *
     * @param int $http_code
     * @param string $message
     * @return string
     */
    public function response(int $http_code, string $message)
    {
        Yii::$app->response->setStatusCode($http_code);
        $biz_code = Yii::$app->response->isOk ? 0 : -1;
        if (Yii::$app->response->isServerError) {
            Yii::error(sprintf('oa flow error: %s, callbackIdempotentId=%s', $message, $this->getCallbackIdempotentId()), __METHOD__);
        }
        return Json::encode(['code' => $biz_code, 'message' => $message]);
    }

}
