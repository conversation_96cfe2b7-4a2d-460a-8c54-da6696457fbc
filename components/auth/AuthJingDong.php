<?php

namespace app\components\auth;

use app\models\RechargeOrder;
use Exception;
use Yii;
use yii\web\Response;

/**
 * Class AuthJingDong
 * @package app\components\auth
 * @link https://docs.qq.com/doc/DT2dtQlBIcE9XcVZs
 */
class AuthJingDong implements AuthJingDongInterface
{
    // 生产状态
    const PRODUCE_STATUS_SUCCESS = 1;  // 生产成功
    const PRODUCE_STATUS_FAILED = 2;  // 生产失败
    const PRODUCE_STATUS_PROCESSING = 3;  // 生产中
    const PRODUCE_STATUS_EXCEPTION = 4;  // 生产异常（需要京东侧进行重试的场景）

    // 生产错误码
    const PRODUCE_CODE_SUCCESS = 'JDO_200';  // 生产成功
    const PRODUCE_CODE_PROCESSING = 'JDO_201';  // 生产中
    const PRODUCE_CODE_ERROR_ACCOUNT = 'JDO_301';  // 生产账号错误
    const PRODUCE_CODE_GOODS_NOT_FOUND = 'JDO_302';  // 没有对应的商品
    const PRODUCE_CODE_ERROR_PARAMS = 'JDO_303';  // 传入参数不正确
    const PRODUCE_CODE_ERROR_SIGN = 'JDO_304';  // 验证签名不正确
    const PRODUCE_CODE_GOODS_CANNOT_SELL = 'JDO_305';  // 商品不可售
    const PRODUCE_CODE_SYSTEM_ERROR = 'JDO_500';  // 系统错误

    // 回调通知错误码
    const CALLBACK_CODE_SUCCESS = 0;  // 成功
    const CALLBACK_CODE_ERROR_PARAMS = 2;  // 参数校验错误
    const CALLBACK_CODE_ORDER_NOT_FOUND = 3;  // 订单不存在
    const CALLBACK_CODE_ERROR_SIGN = 10;  // 签名校验错误
    const CALLBACK_CODE_IP_NO_PERMISSION = 11;  // IP 没有权限
    const CALLBACK_CODE_ERROR_QUANTITY = 12;  // 数量错误
    const CALLBACK_CODE_ERROR_ORDER_STATUS = 13;  // 订单状态错误
    const CALLBACK_CODE_VENDOR_NOT_FOUND = 19;  // 商家不存在
    const CALLBACK_CODE_SYSTEM_ERROR = 500;  // 系统异常

    // 签名算法
    const SIGN_TYPE_DEFAULT = 'MD5';

    // api 版本（京东不同类目 API 不同）
    const API_VERSION = 'v1';

    /**
     * @var string 商家密钥
     */
    private $_key;

    /**
     * @var int 商家 ID
     */
    private $_vendor_id;

    public function __construct(array $config = [])
    {
        if (!$config) {
            $config = Yii::$app->params['service']['jingdong'];
        }
        $this->_key = $config['key'];
        $this->_vendor_id = $config['vendor_id'];
    }

    public static function formatTime(int $unix_timestamp): string
    {
        return date('YmdHis', $unix_timestamp);
    }

    /**
     * 生成签名
     *
     * @link https://docs.qq.com/doc/DT2dtQlBIcE9XcVZs
     * @param array $params
     * @return string
     */
    public function makeSign(array $params): string
    {
        ksort($params);
        $content_str = '';
        foreach ($params as $key => $value) {
            if ($key === 'sign' || $key === 'signType' || $value === '') {
                continue;
            }
            $content_str .= "{$key}{$value}";
        }

        return md5($content_str . $this->_key);
    }

    public function isLegalVendorId(string $vendor_id): bool
    {
        return $vendor_id === (string)$this->_vendor_id;
    }

    /**
     * 回调通知京东订单状态
     *
     * @param RechargeOrder $order
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function syncProductResult(RechargeOrder $order): void
    {
        $order_detail = RechargeOrder::queryDetail($order->id);
        $notify_url = $order_detail->more['jingdong_notify_url'];
        $quantity = $order_detail->more['jingdong_goods_quantity'];

        switch ($order->status) {
            case RechargeOrder::STATUS_SUCCESS:
                $produce_status = self::PRODUCE_STATUS_SUCCESS;
                break;
            case RechargeOrder::STATUS_CREATE:
                throw new Exception('处理中状态的订单不可进行回调通知');
            default:
                $produce_status = self::PRODUCE_STATUS_EXCEPTION;
        }

        $params = [
            'agentOrderNo' => $order->getOrderId(),
            'jdOrderNo' => $order->tid,
            'produceStatus' => $produce_status,
            // 商品卡密信息
            // 'product' => '',
            'quantity' => $quantity,
            'timestamp' => self::formatTime($_SERVER['REQUEST_TIME']),
            'vendorId' => $this->_vendor_id,
        ];
        $params['sign'] = $this->makeSign($params);
        $resp = Yii::$app->tools->requestRemote($notify_url, $params, 'POST', null, 0,
            ['Content-Type' => 'application/x-www-form-urlencoded']);
        if (self::CALLBACK_CODE_SUCCESS !== (int)$resp['code']) {
            Yii::error(sprintf('京东旗舰店充值回调失败：code[%d], message[%s]', $resp['code'], $resp['message']));
        }
    }

    public function renderResponse(string $jd_order_no, string $produce_code, string $out_trade_no = ''): array
    {
        switch ($produce_code) {
            case self::PRODUCE_CODE_SUCCESS:
                $produce_status = self::PRODUCE_STATUS_SUCCESS;
                break;
            case self::PRODUCE_CODE_PROCESSING:
                $produce_status = self::PRODUCE_STATUS_PROCESSING;
                break;
            case self::PRODUCE_CODE_SYSTEM_ERROR:
                $produce_status = self::PRODUCE_STATUS_EXCEPTION;
                break;
            default:
                $produce_status = self::PRODUCE_STATUS_FAILED;
        }
        $resp = [
            'agentOrderNo' => $out_trade_no,
            'code' => $produce_code,
            'jdOrderNo' => $jd_order_no,
            'produceStatus' => $produce_status,
            'signType' => self::SIGN_TYPE_DEFAULT,
            'timestamp' => self::formatTime($_SERVER['REQUEST_TIME']),
        ];
        $resp['sign'] = $this->makeSign($resp);

        Yii::$app->response->format = Response::FORMAT_JSON;
        Yii::$app->response->headers->add('Content-Type', 'application/json; charset=UTF-8');
        return $resp;
    }

    public function getVersion(): string
    {
        return self::API_VERSION;
    }

    public function getErrorCodeErrorAccount(): string
    {
        return self::PRODUCE_CODE_ERROR_ACCOUNT;
    }

    public function getErrorCodeSystemError(): string
    {
        return self::PRODUCE_CODE_SYSTEM_ERROR;
    }

}
