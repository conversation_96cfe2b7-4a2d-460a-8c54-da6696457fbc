<?php

namespace app\components\auth;

interface AuthJingDongInterface
{
    public function getVersion(): string;

    public function makeSign(array $params): string;

    public static function formatTime(int $unix_timestamp): string;

    public function isLegalVendorId(string $vendor_id): bool;

    public function syncProductResult(\app\models\RechargeOrder $order): void;

    public function getErrorCodeErrorAccount(): string;

    public function getErrorCodeSystemError(): string;
}
