<?php

namespace app\components\auth;

interface AuthInterface
{
    const METHOD_GET = 'GET';
    const METHOD_POST = 'POST';

    /**
     * 获取第三方 token 及 uid 对应的（第三方）用户信息
     *
     * @param string $uuid uuid 第三方 token 对应的 uid
     * @param string $access_token 第三方登录 token
     * @return array|bool
     */
    public function auth($uuid, $access_token, $type);

    /**
     * 验证 uuid 与 access_token 是否匹配
     *
     * @param string $uuid uuid 第三方 token 对应的 uid
     * @param string $access_token 第三方登录 token
     * @return bool 是否匹配
     */
    public function validateUuid(string $uuid, string $access_token): bool;
}
