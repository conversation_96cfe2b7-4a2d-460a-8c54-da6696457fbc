<?php

namespace app\components\auth;

use app\components\util\Equipment;
use app\components\util\MUtils;
use app\models\RechargeOrder;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use yii\helpers\Json;
use Yii;

/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/8/11
 * Time: 16:40
 */
class AuthQQ
{
    // 人民币元转分比例
    const CNY_YUAN_FEN_RATE = 100;
    // 支付方式（App 支付，公众号支付，原生扫描支付）
    const TRADE_TYPE_APP = 'APP';
    const TRADE_TYPE_PUBLIC_ACCOUNT = 'JSAPI';
    const TRADE_TYPE_NATIVE_SCAN = 'NATIVE';

    private static $_client = false;

    public function __construct()
    {
        if (false === self::$_client) {
            self::$_client = new Client([
                'base_uri' => 'https://graph.qq.com',
                'timeout' => 2.0
            ]);
        }
    }

    /**
     * 校验 QQ 登录参数
     *
     * @param string $uid QQ 登录 uid
     * @param string $access_token QQ 登录 token
     * @param integer $type 设备类型
     * @return bool|array
     */
    public function auth($uid, $access_token, $type)
    {
        $query = [
            'access_token' => $access_token,
            'openid' => $uid,
        ];
        switch ($type) {
            case Equipment::Android:
                $query['oauth_consumer_key'] = QQ_AUTH_ANDROID;
                break;
            case Equipment::iOS:
                $query['oauth_consumer_key'] = QQ_AUTH_IOS;
                break;
        }
        try {
            $content = $this->_request('/user/get_user_info', $query);
            if (0 === $content['ret']) {
                return [
                    'nickname' => $content['nickname'],
                    'iconurl' => $content['figureurl_qq_2'],
                ];
            } else {
                return false;
            }
        } catch (\Exception $e) {
            return false;
        }
    }

    private function _request($path, $query)
    {
        $query['format'] = 'json';
        $resp = self::$_client->request('GET', $path, [
            'query' => $query
        ]);
        $body = $resp->getBody();
        $content = $body->getContents();
        $content = Json::decode($content);
        return $content;
    }

    /**
     * APP 应用唤醒 QQ 支付
     *
     * @param RechargeOrder $order
     * @return array
     * @throws \Exception
     */
    public function tradeAppPay(RechargeOrder $order)
    {
        return $this->tradePay($order, self::TRADE_TYPE_APP);
    }

    /**
     * 手机网页版（QQ 内置浏览器打开）支付（QQ 公众号支付）
     *
     * @param RechargeOrder $order
     * @return array
     * @throws \Exception
     */
    public function tradeMobileWebPay(RechargeOrder $order)
    {
        return $this->tradePay($order, self::TRADE_TYPE_PUBLIC_ACCOUNT);
    }

    /**
     * QQ 扫码支付
     *
     * @param RechargeOrder $order
     * @return array
     * @throws \Exception
     */
    public function tradeNativeScanPay(RechargeOrder $order)
    {
        return $this->tradePay($order, self::TRADE_TYPE_NATIVE_SCAN);
    }

    /**
     * @param RechargeOrder $order
     * @param string $trade_type
     * @return array
     * @throws \Exception
     */
    private function tradePay(RechargeOrder $order, string $trade_type)
    {
        $nonce_str = MUtils::randomKeys(30, 7);
        $form = [
            'appid' => QQPAY_APP_ID,
            'body' => '猫耳FM--购买钻石',
            'mch_id' => QQPAY_MCH_ID,
            'nonce_str' => $nonce_str,
            'notify_url' => QQPAY_CALLBACK,
            'out_trade_no' => $order->getOrderId(),
            'spbill_create_ip' => Yii::$app->request->userIP,
            'total_fee' => (int)bcmul($order->price, self::CNY_YUAN_FEN_RATE),
            'trade_type' => $trade_type,
            'time_start' => date('YmdHis', $order['create_time']),
            'time_expire' => date('YmdHis', ($order['create_time'] + HALF_HOUR)),
            'fee_type' => 'CNY',
        ];

        $content = $this->QQPayUnifiedOrder($form);
        $xml_array = (array)simplexml_load_string($content, 'SimpleXMLElement', LIBXML_NOCDATA);

        if ($xml_array['return_code'] !== 'SUCCESS') {
            throw new \Exception('创建订单失败: ' . $xml_array['return_msg']);
        }
        if ($xml_array['result_code'] !== 'SUCCESS') {
            throw new \Exception('创建订单失败: ' . $xml_array['err_code_des']);
        }
        $pay = [
            'appId' => $xml_array['appid'],
            'bargainorId' => $xml_array['mch_id'],
            'nonce' => $xml_array['nonce_str'],
            'tokenId' => $xml_array['prepay_id'],
            'pubAcc' => '',  // TODO: QQ 公众号
        ];
        if (self::TRADE_TYPE_APP === $trade_type) {
            // App 支付客户端需要签名（与服务端的签名算法不一致）
            // $pay 中的参数名写法同 App 支付 SDK 文档中保持一致（文档地址：https://qpay.qq.com/buss/wiki/38/1196）
            $pay['sig'] = self::getQQPayClientSign($pay);
        }
        if (self::TRADE_TYPE_NATIVE_SCAN === $trade_type) {
            // 网页端的支付二维码
            $pay['code_url'] = $xml_array['code_url'];
        }
        $pay['timeStamp'] = $order['create_time'];
        return $pay;
    }

    private function QQPayUnifiedOrder(array $form)
    {
        $form['sign'] = self::getQQPayServerSign($form);
        $form = array_flip($form);
        $xml = new \SimpleXMLElement('<xml/>');
        array_walk_recursive($form, [$xml, 'addChild']);
        $body = $xml->asXML();
        try {
            $client = new Client([
                'base_uri' => 'https://qpay.qq.com',
                'timeout' => 5.0,
            ]);
            $resp = $client->request('POST', '/cgi-bin/pay/qpay_unified_order.cgi', [
                'body' => $body
            ]);
            $body = $resp->getBody();
            return $body->getContents();
        } catch (ClientException $e) {
            throw new \Exception('QQ 钱包支付服务器连接错误');
        }
    }

    /**
     * 生成服务端 QQ 支付签名
     * @link https://qpay.qq.com/buss/wiki/38/1192
     * @param array $params
     * @return string
     */
    public static function getQQPayServerSign(array $params)
    {
        ksort($params);
        $string_a = urldecode(http_build_query($params)) . '&key=' . QQPAY_KEY;
        return strtoupper(md5($string_a));
    }

    /**
     * 生成客户端 QQ 支付签名
     * @link https://qpay.qq.com/buss/wiki/38/1196
     * @param array $params
     * @return string
     */
    public static function getQQPayClientSign(array $params)
    {
        ksort($params);
        $string_a = http_build_query($params);
        return base64_encode(hash_hmac('sha1', $string_a, QQPAY_APP_SIGN . '&', true));
    }

    /**
     * QQ 支付完成异步通知的失败响应内容
     *
     * @return string
     */
    public static function getQQPayFailCallbackResponse()
    {
        return <<<XML
<xml>
    <return_code>FAIL</return_code>
</xml>
XML;
    }

    /**
     * QQ 支付完成异步通知的成功响应内容
     *
     * @return string
     */
    public static function getQQPaySuccessCallbackResponse()
    {
        return <<<XML
<xml>
    <return_code>SUCCESS</return_code>
</xml>
XML;
    }

}
