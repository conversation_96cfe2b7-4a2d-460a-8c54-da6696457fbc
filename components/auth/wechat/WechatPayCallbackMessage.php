<?php

namespace app\components\auth\wechat;

class WechatPayCallbackMessage
{
    /**
     * @var string 交易状态
     */
    private $trade_status;
    /**
     * @var string 外部订单号（猫耳平台对外订单号）
     */
    private $out_trade_no;
    /**
     * @var string 交易 ID（微信支付订单号）
     */
    private $transaction_id;
    /**
     * @var int 订单金额（单位：分）
     */
    private $price_in_fen;

    /**
     * @var string 用户在直连商户 appid 下的唯一标识
     */
    private $openid;

    public function __construct(string $trade_status, string $out_trade_no, string $transaction_id, int $price_in_fen)
    {
        $this->trade_status = $trade_status;
        $this->out_trade_no = $out_trade_no;
        $this->transaction_id = $transaction_id;
        $this->price_in_fen = $price_in_fen;
    }

    /**
     * @return string
     */
    public function getTradeStatus(): string
    {
        return $this->trade_status;
    }

    /**
     * @return string
     */
    public function getOutTradeNo(): string
    {
        return $this->out_trade_no;
    }

    /**
     * @return string
     */
    public function getTransactionId(): string
    {
        return $this->transaction_id;
    }

    /**
     * @return int
     */
    public function getPriceInFen(): int
    {
        return $this->price_in_fen;
    }

    /**
     * @return string
     */
    public function getOpenid(): string
    {
        return $this->openid;
    }

    /**
     * @param string $openid
     */
    public function setOpenid(string $openid): void
    {
        $this->openid = $openid;
    }

}
