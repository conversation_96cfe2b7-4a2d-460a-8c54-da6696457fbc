<?php

namespace app\components\auth\wechat;

/**
 * Class WechatConfig
 * @property-read $app_id
 * @property-read $app_secret
 * @property-read $merchant_id
 * @property-read $merchant_serial_no
 * @property-read $platform_cert
 * @property-read $apiclient_key
 * @property-read $apiv3_key
 * @property-read $authorize_redirect_url
 * @property-read $notify_url
 */
class WechatConfig
{
    private $_app_id;
    private $_app_secret;
    private $_merchant_id;
    private $_merchant_serial_no;
    private $_platform_cert;
    private $_apiclient_key;
    private $_apiv3_key;
    private $_authorize_redirect_url;
    private $_notify_url;

    public function __construct(array $config)
    {
        foreach ($config as $key => $value) {
            $this->{'_' . $key} = $value;
        }
    }

    public function __get($name)
    {
        return $this->{'_' . $name};
    }

}
