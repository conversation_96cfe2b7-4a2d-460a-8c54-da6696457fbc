<?php

namespace app\components\auth\wechat;

use Exception;
use Yii;
use app\models\RechargeOrder;
use GuzzleHttp\Exception\RequestException;
use WeChatPay\Builder;
use WeChatPay\Crypto\Rsa;
use WeChatPay\Crypto\AesGcm;
use WeChatPay\Formatter;
use WeChatPay\Util\PemUtil;
use yii\helpers\Json;
use yii\web\HttpException;
use yii\web\Request;

class WechatApiV3
{
    const TYPE_JSAPI = 'jsapi';

    /**
     * @var \WeChatPay\BuilderChainable
     */
    private $_client;
    /**
     * @var WechatConfig
     */
    private $_config;

    public function __construct(WechatConfig $config)
    {
        $this->_config = $config;
        $platform_cert = array_reduce($this->_config->platform_cert, function ($ret, $item) {
            $ret[] = $item['content'];
            return $ret;
        }, []);
        $this->_client = self::newClient($this->_config->merchant_id, $this->_config->merchant_serial_no, $this->_config->apiclient_key, $platform_cert);
    }

    public static function newInstance(string $type = self::TYPE_JSAPI)
    {
        $config = Yii::$app->params['service']['wechatpay'][$type];
        return new self(new WechatConfig($config));
    }

    /**
     * @param string $merchant_id 商户号
     * @param string $merchant_serial_no 商户 API 私钥的证书序列号
     * @param string $apiclient_key 商户 API 私钥
     * @param string|array $platform_cert 微信平台证书
     * @return \WeChatPay\BuilderChainable
     */
    private static function newClient(string $merchant_id, string $merchant_serial_no, string $apiclient_key, $platform_cert)
    {
        if (is_string($platform_cert)) {
            $platform_cert = [$platform_cert];
        }
        $certs = [];
        foreach ($platform_cert as $item) {
            $certs[PemUtil::parseCertificateSerialNo($item)] = Rsa::from($item, Rsa::KEY_TYPE_PUBLIC);
        }
        return Builder::factory([
            'mchid' => $merchant_id,
            'serial' => $merchant_serial_no,
            'privateKey' => Rsa::from($apiclient_key, Rsa::KEY_TYPE_PRIVATE),
            'certs' => $certs,
        ]);
    }

    /**
     * 公众号支付
     * @link https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_1_1.shtml
     *
     * @param RechargeOrder $order
     * @param string $openid
     * @return array
     * @throws Exception
     */
    public function tradeJSAPIPay(RechargeOrder $order, string $openid)
    {
        try {
            $resp = $this->_client
                ->chain('v3/pay/transactions/jsapi')
                ->post([
                    'json' => [
                        'appid' => $this->_config->app_id,
                        'mchid' => $this->_config->merchant_id,
                        'description' => '猫耳FM--购买钻石',
                        'out_trade_no' => $order->getOrderId(),
                        'notify_url' => $this->_config->notify_url,
                        'amount' => [
                            'total' => $order->priceInFen(),
                            'currency' => 'CNY',
                        ],
                        'payer' => [
                            'openid' => $openid,
                        ],
                    ],
                ]);
            if ($resp->getStatusCode() !== 200) {
                throw new Exception('wechat jsapi pay error');
            }

            $resp = Json::decode($resp->getBody()->getContents());
            return $this->makeJSAPIInvokeBody($resp['prepay_id']);
        } catch (Exception $e) {
            $message = $e->getMessage();
            if ($e instanceof RequestException && $e->hasResponse()) {
                $error_resp = $e->getResponse();
                $message .= sprintf(' status_code[%d], body[%s]', $error_resp->getStatusCode(), $error_resp->getBody()->getContents());
            }
            Yii::error($message, __METHOD__);
            throw new Exception('服务器繁忙，请稍候重试');
        }
    }

    /**
     * 生成 JSAPI 前端唤起微信支付报文
     * @link https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_1_4.shtml#menu1
     * @link https://github.com/wechatpay-apiv3/wechatpay-php
     *
     * @param string $prepay_id 预支付单号
     * @return array
     */
    private function makeJSAPIInvokeBody(string $prepay_id): array
    {
        $params = [
            'appId' => $this->_config->app_id,
            'timeStamp' => (string)Formatter::timestamp(),
            'nonceStr' => Formatter::nonce(),
            'package' => sprintf('prepay_id=%s', $prepay_id),
        ];
        $params += [
            'paySign' => Rsa::sign(
                Formatter::joinedByLineFeed(...array_values($params)),
                Rsa::from($this->_config->apiclient_key)
            ),
            'signType' => 'RSA',
        ];
        return $params;
    }

    /**
     * API v3 回调报文验证
     * @link https://pay.weixin.qq.com/wiki/doc/apiv3/wechatpay/wechatpay4_2.shtml
     * @link https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_1_5.shtml
     * @link https://github.com/wechatpay-apiv3/wechatpay-php
     *
     * @param Request $request
     * @return bool
     */
    public function verifySignature(Request $request)
    {
        $headers = $request->getHeaders();
        $wechatpay_signature = $headers->get('wechatpay-signature');
        $wechatpay_serial = $headers->get('wechatpay-serial');
        $wechatpay_nonce = $headers->get('wechatpay-nonce');
        $wechatpay_timestamp = $headers->get('wechatpay-timestamp');

        if (!array_key_exists($wechatpay_serial, $this->_config->platform_cert)) {
            Yii::error(sprintf('序列号 %s 未找到对应的平台证书', $wechatpay_serial), __METHOD__);
            return false;
        }

        $verified_status = Rsa::verify(
            Formatter::joinedByLineFeed($wechatpay_timestamp, $wechatpay_nonce, $request->getRawBody()),
            $wechatpay_signature,
            Rsa::from($this->_config->platform_cert[$wechatpay_serial]['content'], Rsa::KEY_TYPE_PUBLIC)
        );
        return $verified_status;
    }

    /**
     * 解密 API v3 报文
     *
     * @param string $row_body
     * @return mixed
     */
    public function decryptBody(string $row_body)
    {
        [
            'resource' => [
                'ciphertext' => $ciphertext,
                'nonce' => $nonce,
                'associated_data' => $associated_data
            ],
        ] = Json::decode($row_body);

        $decrypted_message = AesGcm::decrypt($ciphertext, $this->_config->apiv3_key, $nonce, $associated_data);
        return Json::decode($decrypted_message);
    }

    /**
     * 解析微信支付回调报文
     *
     * @link https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_1_5.shtml
     * @link https://github.com/wechatpay-apiv3/wechatpay-php
     *
     * @param Request $request 请求信息
     * @return WechatPayCallbackMessage
     * @throws HttpException
     */
    public function parseCallbackBody(Request $request)
    {
        if (!$this->verifySignature($request)) {
            throw new HttpException(403, '非法请求');
        }

        $detail = $this->decryptBody($request->getRawBody());
        [
            'out_trade_no' => $out_trade_no,
            'transaction_id' => $transaction_id,
            'trade_state' => $trade_state,
            'amount' => [
                // 用户支付金额，单位为分
                // 'payer_total' => $payer_total,
                // 可能有优惠券情形（商户出资、微信出资、其他出资）
                // 'promotion_detail' => $promotion_detail,
                // 订单总金额，单位为分
                'total' => $total,
            ],
            'payer' => [
                'openid' => $openid,
            ],
        ] = $detail;

        $message = new WechatPayCallbackMessage($trade_state, $out_trade_no, $transaction_id, $total);
        $message->setOpenid($openid);
        return $message;
    }

    public static function getWechatPayFailCallbackResponse(Exception $e = null)
    {
        Yii::$app->response->setStatusCodeByException($e);
        return Json::encode(['code' => 'FAIL', 'message' => $e->getMessage()]);
    }

    public static function getWechatPaySuccessCallbackResponse()
    {
        return Json::encode(['code' => 'SUCCESS', 'message' => '成功']);
    }

}
