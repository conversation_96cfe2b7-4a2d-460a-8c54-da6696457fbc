<?php

namespace app\components\auth\wechat;

use app\components\util\MUtils;
use app\models\MThirdPartyTask;
use app\models\MWechatOffiaccountReply;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\web\Request;

class WechatOffiaccount
{
    // 微信公众号消息类型
    const MSG_TYPE_TEXT = 'text';  // 文本
    const MSG_TYPE_IMAGE = 'image';  // 图片
    const MSG_TYPE_VOICE = 'voice';  // 语音
    const MSG_TYPE_VIDEO = 'video';  // 视频
    const MSG_TYPE_SHORT_VIDEO = 'shortvideo';  // 小视频
    const MSG_TYPE_LOCATION = 'location';  // 地理位置
    const MSG_TYPE_LINK = 'link';  // 链接
    const MSG_TYPE_EVENT = 'event';  // 事件

    // 消息推送类型数组
    const MESSAGE_TYPES = [
        self::MSG_TYPE_TEXT,
        self::MSG_TYPE_IMAGE,
        self::MSG_TYPE_VOICE,
        self::MSG_TYPE_VIDEO,
        self::MSG_TYPE_SHORT_VIDEO,
        self::MSG_TYPE_LOCATION,
        self::MSG_TYPE_LINK,
    ];

    // 事件类型
    const EVENT_TYPE_SUBSCRIBE = 'subscribe';  // 关注公众号
    const EVENT_TYPE_UNSUBSCRIBE = 'unsubscribe';  // 取消关注公众号
    const EVENT_TYPE_CLICK = 'CLICK';  // 点击自定义菜单

    // 关注公众号任务 token 长度
    const TASK_TOKEN_LEN = 32;

    /**
     * @var string
     */
    private $_token;

    public function __construct(array $config)
    {
        $this->_token = $config['token'];
        // TODO: 后续若有需要，可以支持请求微信公众号服务器相关 client
    }

    public function __get($name)
    {
        return $this->{'_' . $name};
    }

    public static function newInstance()
    {
        $config = Yii::$app->params['service']['wechat-offiaccount'];
        return new self($config);
    }

    /**
     * 验证微信公众号服务器请求的签名
     * @link https://developers.weixin.qq.com/doc/offiaccount/Basic_Information/Access_Overview.html
     *
     * @param Request $request
     * @return bool
     */
    public function verifySignature(Request $request)
    {
        $signature = trim($request->get('signature'));
        $timestamp = trim($request->get('timestamp'));
        $nonce = trim($request->get('nonce'));
        if ($signature === '' || $timestamp === '' || $nonce === '') {
            return false;
        }
        $sign_arr = [$this->_token, $timestamp, $nonce];
        sort($sign_arr, SORT_STRING);
        $sign_str = implode($sign_arr);
        $sign_str = sha1($sign_str);
        return $sign_str === $signature;
    }

    /**
     * 获取不需要回复消息时响应的内容
     *
     * @return string
     */
    public static function getMsgNoReplyResponse(): string
    {
        // 若无需回复，直接响应"success"字符串
        return 'success';
    }

    /**
     * 获取微信公众号服务器发送的消息
     * @link https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Receiving_standard_messages.html
     *
     * @param Request $request
     * @return array|null
     */
    public function getMessageInfo(Request $request): ?array
    {
        $body = $request->getRawBody();
        if (!$body) {
            return null;
        }
        $body_arr = (array)simplexml_load_string($body, 'SimpleXMLElement', LIBXML_NOCDATA);
        $to_user_name = trim($body_arr['ToUserName'] ?? '');
        $from_user_name = trim($body_arr['FromUserName'] ?? '');
        $msg_type = trim($body_arr['MsgType'] ?? '');
        $content = trim($body_arr['Content'] ?? '');
        if (!$to_user_name || !$from_user_name || !$msg_type) {
            // 非消息内容，直接返回 null
            return null;
        }
        if (!in_array($msg_type, self::MESSAGE_TYPES)) {
            // 如果消息类型不在支持的类型中，直接返回 null
            return null;
        }
        return [
            'to_user_name' => $to_user_name,
            'from_user_name' => $from_user_name,
            'create_time' => (int)($body_arr['CreateTime'] ?? 0),  // 消息创建时间戳，单位：秒
            'msg_type' => $msg_type,  // 消息类型
            'content' => $content,  // 文本消息内容
            'msg_id' => (int)($body_arr['MsgId'] ?? 0),  // 消息 ID
            'msg_data_id' => $body_arr['MsgDataId'] ?? '',  // 消息的数据 ID（消息如果来自文章时才有）
            'idx' => $body_arr['Idx'] ?? '',  // 多图文时第几篇文章，从 1 开始（消息如果来自文章时才有）
        ];
    }

    /**
     * 获取微信公众号服务器发送的事件信息
     * @link https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Receiving_event_pushes.html
     *
     * @param Request $request
     * @return array|null
     */
    public function getEventInfo(Request $request): ?array
    {
        $body = $request->getRawBody();
        if (!$body) {
            return null;
        }
        $body_arr = (array)simplexml_load_string($body, 'SimpleXMLElement', LIBXML_NOCDATA);
        $to_user_name = trim($body_arr['ToUserName'] ?? '');
        $from_user_name = trim($body_arr['FromUserName'] ?? '');
        $msg_type = trim($body_arr['MsgType'] ?? '');
        $event = trim($body_arr['Event'] ?? '');
        $event_key = trim($body_arr['EventKey'] ?? '');
        // TODO: 目前仅支持关注/取消关注事件和自定义菜单事件，需要支持其他事件时，可以在此补充处理
        if (!$to_user_name || !$from_user_name || !$msg_type || !$event) {
            // 非事件内容，直接返回 null
            return null;
        }
        if ($msg_type !== self::MSG_TYPE_EVENT) {
            // 如果消息类型不是事件类型，直接返回 null
            return null;
        }
        return [
            'to_user_name' => $to_user_name,
            'from_user_name' => $from_user_name,
            'create_time' => (int)($body_arr['CreateTime'] ?? 0),  // 消息创建时间戳，单位：秒
            'msg_type' => $msg_type,  // 消息类型
            'event' => $event,
            'event_key' => $event_key,
        ];
    }

    public function handleTextMessage(array $message): string
    {
        $content = $message['content'] ?? '';
        if (!$content) {
            return self::getMsgNoReplyResponse();
        }
        // 获取公众号任务回复内容
        $reply = $this->getPointTaskReply($message['content']);
        if (!$reply) {
            // 非公众号任务回复，则按消息匹配自动回复的内容
            $reply = MWechatOffiaccountReply::getReply(MWechatOffiaccountReply::SCENE_MESSAGE, $content);
        }
        if (!$reply) {
            // 如果没有回复内容，则使用默认回复内容
            $reply = MWechatOffiaccountReply::formatTextReply(MWechatOffiaccountReply::DEFAULT_TEXT_REPLY);
        }
        // 响应的公共内容（每种类型消息都必须有的字段）
        $resp_public_arr = [
            'ToUserName' => $message['from_user_name'],  // 接收方为发消息过来的人
            'FromUserName' => $message['to_user_name'],
            'CreateTime' => $_SERVER['REQUEST_TIME'],
        ];
        // 合并回复信息
        $resp_arr = array_merge($resp_public_arr, $reply);
        return MUtils::arrayToXML($resp_arr, true, true);
    }

    public function handleEventMessage(array $message): string
    {
        $event = $message['event'] ?? '';
        if (!$event) {
            return self::getMsgNoReplyResponse();
        }
        $reply = [];
        if ($event === self::EVENT_TYPE_SUBSCRIBE) {
            // 关注公众号事件，需要回复特定内容
            $reply = MWechatOffiaccountReply::getReply(MWechatOffiaccountReply::SCENE_EVENT,
                MWechatOffiaccountReply::SUBSCRIBE_EVENT_KEY);
        }
        if (!$reply) {
            // 如果没有回复内容，则使用默认回复内容
            return self::getMsgNoReplyResponse();
        }
        // 响应的公共内容（每种类型消息都必须有的字段）
        $resp_public_arr = [
            'ToUserName' => $message['from_user_name'],  // 接收方为发消息过来的人
            'FromUserName' => $message['to_user_name'],
            'CreateTime' => $_SERVER['REQUEST_TIME'],
        ];
        // 合并回复信息
        $resp_arr = array_merge($resp_public_arr, $reply);
        return MUtils::arrayToXML($resp_arr, true, true);
    }

    /**
     * 获取鱼干任务回复
     *
     * @param string $content
     * @return array|null
     */
    public function getPointTaskReply(string $content): ?array
    {
        $task_config = Yii::$app->params['wechat_offiaccount_task'] ?? null;
        $now = $_SERVER['REQUEST_TIME'];
        if (!$task_config || !key_exists('start_time', $task_config) || !key_exists('end_time', $task_config)
                || $task_config['start_time'] > $now || ($task_config['end_time'] > 0 && $task_config['end_time'] <= $now)) {
            // 关注公众号任务未配置或不在任务期间时，无回复内容
            return null;
        }
        $task_keywords = $task_config['keywords'] ?? [];
        $open_client_text_format = $task_config['open_client_text_format'] ?? '';
        if (empty($task_keywords) && !$open_client_text_format) {
            // 关注公众号任务未配置关键词或唤端活动链接时，无回复内容
            Yii::error('关注公众号任务未配置关键词或唤端活动链接', __METHOD__);
            return null;
        }
        if (!in_array($content, $task_keywords)) {
            return null;
        }
        $reply_content = $this->generateTaskReply($open_client_text_format);
        return MWechatOffiaccountReply::formatTextReply($reply_content);
    }

    private static function generateTaskReply(string $open_client_text_format): string
    {
        // 生成任务 token
        $token = MUtils2::randomKeys(self::TASK_TOKEN_LEN, 7);
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_THIRD_TASK_TOKEN, MThirdPartyTask::SCENE_WECHAT_OFFIACCOUNT, date('Ymd'));
        $redis->multi()
            ->sAdd($key, $token)
            ->expire($key, ONE_DAY)
            ->exec();
        return sprintf($open_client_text_format, $token);
    }
}
