<?php

namespace app\components\auth;

use app\components\util\Equipment;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use yii\helpers\Json;
use yii\web\HttpException;
use Yii;

class AuthBiliBili
{
    const HTTP_METHOD_GET = 'GET';
    const HTTP_METHOD_POST = 'POST';

    const ERROR_CODE_NOT_EXISTS_OR_EXPIRED = -907;

    // Bilibili 支付风控业务方 ID
    const BILIBILI_RISK_CUSTOMER_ID = 10039;

    // Bilibili 支付风控域名地址
    const BILIBILI_RISK_GATEWAY = 'https://show.bilibili.com';
    const BILIBILI_RISK_GATEWAY_UAT = 'https://uat-show.bilibili.com';

    // Bilibili 支付异步风控接口，结果查询接口
    const BILIBILI_RISK_ASK_ASYNC = '/payplatform/shield/askasync';
    const BILIBILI_RISK_CHECK = '/payplatform/shield/check';

    /**
     * 校验 Bilibili 登录参数
     *
     * @param string $uid Bilibili 登录 uid
     * @param string $access_token 微博登录 token
     * @param integer $os 设备类型
     * @return bool|array
     */
    public function auth($uid, $access_token, $os)
    {
        try {
            $content = self::getUserInfo($access_token, $os);
            return [
                'nickname' => $content['name'],
                'iconurl' => $content['face'],
            ];
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 生成签名
     *
     * @param $params
     * @param $app_secret
     * @return string
     */
    private static function getSign($params, $app_secret)
    {
        ksort($params);
        $sign_calc = http_build_query($params);
        $str = $sign_calc . $app_secret;
        return md5($str);
    }

    private static function request($params, $uri, $os = Equipment::iOS, $method = self::HTTP_METHOD_GET)
    {
        try {
            if (Equipment::iOS === $os) {
                $appkey = BILIBILI_AUTH_IOS_ID;
                $appsecret = BILIBILI_AUTH_IOS_SECRET;
            } else {
                $appkey = BILIBILI_AUTH_ANDROID_ID;
                $appsecret = BILIBILI_AUTH_ANDROID_SECRET;
            }
            $params += [
                'appkey' => $appkey,
                'ts' => time(),
            ];
            $params['sign'] = self::getSign($params, $appsecret);

            $options['timeout'] = 15.0;
            if (self::HTTP_METHOD_GET === $method) {
                $options['query'] = $params;
            } else {
                $options['form_params'] = $params;
            }
            // Bilibili 正式环境进行安全性检查，其 UAT 环境域名证书未备案，忽略安全性检查
            $res = (new Client(['verify' => YII_ENV_PROD]))->request($method, $uri, $options);
            if (200 === $res->getStatusCode()) {
                $content = Json::decode($res->getBody());
                if (0 !== $content['code']) {
                    if (self::ERROR_CODE_NOT_EXISTS_OR_EXPIRED === $content['code']) {
                        throw new HttpException(400, '哔哩哔哩授权过期，请重新授权');
                    }
                    throw new \Exception($content['message'] ?? '服务器出差去了，请稍后重试');
                }
                return $content['data'];
            } else {
                return null;
            }
        } catch (ClientException $e) {
            return null;
        }
    }

    /**
     * 获取 Access Token
     *
     * @param string $code
     * @param int $os
     * @return null
     * @throws \Exception
     */
    public static function getAccessToken(string $code, int $os = Equipment::iOS)
    {
        return self::request([
            'grant_type' => 'authorization_code',
            'code' => $code,
        ], 'https://passport.bilibili.com/api/v2/oauth2/access_token', $os, self::HTTP_METHOD_GET);
    }

    /**
     * 获取用户信息
     *
     * @param $access_token
     * @param int $os
     * @return null
     * @throws \Exception
     */
    public static function getUserInfo($access_token, int $os = Equipment::iOS)
    {
        return self::request([
            'access_key' => $access_token,
        ], 'https://api.bilibili.com/x/member/thirdpart/info', $os, self::HTTP_METHOD_GET);
    }

    public static function payRskAsk(array $params)
    {
        $params['customerId'] = self::BILIBILI_RISK_CUSTOMER_ID;
        $params['timestamp'] = $_SERVER['REQUEST_TIME'];
        $params['uid'] = (string)Yii::$app->user->id;
        $params['userClientIp'] = Yii::$app->request->userIP;
        $params['deviceId'] = str_replace('-', '', Yii::$app->equip->getEquipId());
        $params['payChannel'] = 'IAP';
        try {
            return self::requestPayRisk(self::BILIBILI_RISK_ASK_ASYNC, $params);
        } catch (\Exception $e) {
            Yii::error("Bilibili 风控请求异常: {$e->getMessage()}");
            return null;
        }
    }

    public static function payRskCheck(array $params)
    {
        try {
            return self::requestPayRisk(self::BILIBILI_RISK_CHECK, $params);
        } catch (\Exception $e) {
            Yii::error("Bilibili 风控结果查询异常: {$e->getMessage()}");
            return null;
        }
    }

    /**
     * Bilibili 支付风控请求
     *
     * @link https://info.bilibili.co/pages/viewpage.action?pageId=11539392
     * @param string $api
     * @param array $params
     * @return mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function requestPayRisk(string $api, array $params)
    {
        $client = new Client([
            'base_uri' => YII_ENV_PROD ? self::BILIBILI_RISK_GATEWAY : self::BILIBILI_RISK_GATEWAY_UAT,
            'timeout' => 2,
            // Bilibili 正式环境进行安全性检查，其 UAT 环境域名证书未备案，忽略安全性检查
            'verify' => YII_ENV_PROD,
        ]);

        $body = Json::encode($params);
        try {
            $res = $client->request('POST', $api, [
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                    'User-Agent' => Yii::$app->name . '/' . Yii::$app->version,
                ],
                'body' => $body,
            ]);
            if (200 === $res->getStatusCode()) {
                $content = Json::decode($res->getBody());
                if (0 !== $content['errno']) {
                    throw new \Exception($content['showMsg']);
                }
                return $content['data'] ?? null;
            } else {
                Yii::error("Bilibili 风控请求异常: HTTP Code {$res->getStatusCode()}");
                return null;
            }
        } catch (ClientException $e) {
            Yii::error("Bilibili 风控请求异常: {$e->getMessage()}");
            return null;
        }
    }

}
