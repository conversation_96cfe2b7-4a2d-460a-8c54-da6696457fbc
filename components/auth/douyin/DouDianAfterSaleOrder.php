<?php

namespace app\components\auth\douyin;

class DouDianAfterSaleOrder
{
    // 退款状态：待退款
    const REFUND_STATUS_WAIT_TO_REFUND = 1;

    // 售后单状态：待商家处理
    const ORDER_STATUS_WAIT_MERCHANT_TO_PROCESS = 6;

    /**
     * @var string|null
     */
    private $_order_id;
    /**
     * @var int|null
     */
    private $_order_status;
    /**
     * @var int|null
     */
    private $_refund_status;

    public function __construct(array $after_sale_detail)
    {
        $this->_order_id = $after_sale_detail['process_info']['after_sale_info']['after_sale_id'];
        $this->_order_status = $after_sale_detail['process_info']['after_sale_info']['after_sale_status'];
        $this->_refund_status = $after_sale_detail['process_info']['after_sale_info']['refund_status'];
    }

    public function getOrderId()
    {
        return $this->_order_id;
    }

    public function getOrderStatus()
    {
        return $this->_order_status;
    }

    public function getRefundStatus()
    {
        return $this->_refund_status;
    }

    public function isWaitingToRefund()
    {
        return $this->getOrderStatus() === self::ORDER_STATUS_WAIT_MERCHANT_TO_PROCESS
            && $this->getRefundStatus() === self::REFUND_STATUS_WAIT_TO_REFUND;
    }

}
