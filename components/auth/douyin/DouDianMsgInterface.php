<?php

namespace app\components\auth\douyin;

use app\models\RechargeOrder;

interface DouDianMsgInterface
{
    public function getBiz(): int;

    public function getShopId(): string;

    public function getShopOrderId(): string;

    public function getShopOrder(): ?DouDianOrder;

    public function setShopOrder(DouDianOrder $order);

    public function getSellerOrder(): ?RechargeOrder;

    public function setSellerOrder(RechargeOrder $order);
}
