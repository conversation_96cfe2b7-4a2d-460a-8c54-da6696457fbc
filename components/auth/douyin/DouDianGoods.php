<?php

namespace app\components\auth\douyin;

class DouDianGoods
{
    const TYPE_DRAMA = 1;

    // C 端流量来源：0、unknown 2、精选联盟 8、小店自卖
    const CLIENT_BIZ_UNKNOWN = 0;
    const CLIENT_BIZ_ALLY = 2;
    const CLIENT_BIZ_SELF = 8;

    /**
     * @var int|null
     */
    private $_id;
    /**
     * @var int|null
     */
    private $_type;
    /**
     * @var int|null
     */
    private $_price_in_fen;
    /**
     * @var int|null
     */
    private $_num;
    /**
     * @var int
     */
    private $_c_biz;
    /**
     * C 端流量来源业务类型描述
     * @var string
     */
    private $_c_biz_desc;
    /**
     * 直播主播 id（达人）
     * @var int
     */
    private $_author_id;
    /**
     * 直播主播名称
     * @var string
     */
    private $_auth_name;

    public function __construct(array $sku)
    {
        // 在抖店后台填的自定义的 code 来做的商品区分，目前只支持广播剧的购买
        // https://op.jinritemai.com/docs/api-docs/15/1144
        // TODO: 后续若需售卖不同类型的商品，可对 code 进行约定来区分出不同商品的 type
        $this->_id = (int)$sku['code'];
        $this->_type = self::TYPE_DRAMA;
        $this->_price_in_fen = (int)$sku['pay_amount'];
        $this->_num = (int)$sku['item_num'];
        $this->_c_biz = (int)$sku['c_biz'];
        $this->_c_biz_desc = $sku['c_biz_desc'];
        $this->_author_id = (int)$sku['author_id'];
        $this->_auth_name = $sku['author_name'];
    }

    public function getId()
    {
        return $this->_id;
    }

    public function getType()
    {
        return $this->_type;
    }

    public function getPriceInFen()
    {
        return $this->_price_in_fen;
    }

    public function getNum()
    {
        return $this->_num;
    }

    public function getClientBizType()
    {
        return $this->_c_biz;
    }

    public function getClientBizDescription()
    {
        return $this->_c_biz_desc;
    }

    public function getAuthorId()
    {
        return $this->_author_id;
    }

    public function getAuthorName()
    {
        return $this->_auth_name;
    }

}
