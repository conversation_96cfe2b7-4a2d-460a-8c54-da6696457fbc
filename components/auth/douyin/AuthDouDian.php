<?php

namespace app\components\auth\douyin;

use Exception;
use Yii;
use yii\helpers\Json;

class AuthDouDian
{
    const GATEWAY = 'https://openapi-fxg.jinritemai.com';
    const API_VERSION = 2;
    const SIGN_METHOD = 'md5';

    const METHOD_TOKEN_CREATE = 'token.create';
    const METHOD_TOKEN_REFRESH = 'token.refresh';

    const METHOD_ORDER_DETAIL = 'order.orderDetail';
    const METHOD_ORDER_BATCH_DECRYPT = 'order.batchDecrypt';
    const METHOD_ORDER_DELIVER_GOODS = 'order.logisticsAdd';

    const METHOD_AFTER_SALE_DETAIL = 'afterSale.Detail';
    const METHOD_AFTER_SALE_OPERATE = 'afterSale.Operate';

    const METHOD_SYNC_TOPUP_RESULT = 'topup.result';

    // 发货后同意仅退款
    const AFTER_SALE_OPERATE_TYPE_AGREE_TO_REFUND = 201;

    // 商家状态（目前只用到 SUCCESS）
    const SELLER_ORDER_STATUS_SUCCESS = 'SUCCESS';
    const SELLER_ORDER_STATUS_FAILED = 'FAILED';
    const SELLER_ORDER_STATUS_IN_PROCESS = 'IN_PROCESS';

    // 充值业务类型
    const TOPUP_BIZ_MOBILE_TOPUP = 'ENTERTAINMENT';  // 生活娱乐充值，充值超时时间 12 小时，高峰期超时时间（月初/月末）12 小时

    // 错误码
    const ERR_CODE_INTERNAL_ERROR = '-1';
    const ERR_CODE_TRADE_ORDER_NO_ERROR = '1000';
    const ERR_CODE_STATUS_ERROR = '1001';
    const ERR_CODE_RECEIPT_ERROR = '1002';
    const ERR_CODE_PARAMS_ERROR = '1003';
    const ERR_CODE_SIGN_ERROR = '1004';
    const ERR_CODE_NOT_IN_PROCESS = '1005';

    const ERR_DESC = [
        self::ERR_CODE_INTERNAL_ERROR => '内部出错，请重试',
        self::ERR_CODE_TRADE_ORDER_NO_ERROR => 'trade_order_no 错误',
        self::ERR_CODE_STATUS_ERROR => '商家订单状态不符合预期',
        self::ERR_CODE_RECEIPT_ERROR => '没有对应的交易单据',
        self::ERR_CODE_PARAMS_ERROR => '参数校验失败',
        self::ERR_CODE_SIGN_ERROR => '签名失败',
        self::ERR_CODE_NOT_IN_PROCESS => '当前充值不是 IN_PROCESS 状态',
    ];

    const CODE_SUCCESS = 10000;

    /**
     * @var self|null
     */
    private static $_client;

    /**
     * @var string|null
     */
    private $app_key;
    /**
     * @var string|null
     */
    private $app_secret;
    /**
     * @var string|null
     */
    private $shop_id;

    private function __construct(string $app_key, string $app_secret, string $shop_id)
    {
        $this->app_key = $app_key;
        $this->app_secret = $app_secret;
        $this->shop_id = $shop_id;
    }

    public static function client()
    {
        if (!self::$_client) {
            $config = Yii::$app->params['service']['doudian'];
            self::$_client = new self($config['app_key'], $config['app_secret'], $config['shop_id']);
        }

        return self::$_client;
    }

    /**
     * 获取 access token
     *
     * @link https://op.jinritemai.com/docs/guide-docs/10/814
     * @link https://op.jinritemai.com/docs/guide-docs/token-tool Token 查询工具
     *
     * @return mixed
     * @throws Exception
     */
    public function getAccessToken()
    {
        $cache = Yii::$app->memcache;
        if ($access_token = $cache->get(KEY_DOUDIAN_ACCESS_TOKEN)) {
            return $access_token;
        }
        if ($refresh_token = $cache->get(KEY_DOUDIAN_REFRESH_TOKEN)) {
            $data = $this->refreshToken($refresh_token);
            $this->cacheToken($data);
            return $data['access_token'];
        }

        $params = [
            'code' => '',
            'grant_type' => 'authorization_self',
            'shop_id' => $this->shop_id,
        ];
        if (YII_ENV_DEV) {
            // 测试店铺: https://op.jinritemai.com/docs/guide-docs/10/209
            $params['test_shop'] = '1';
        }

        $data = $this->request(self::METHOD_TOKEN_CREATE, $params);
        $this->cacheToken($data);

        return $data['access_token'];
    }

    /**
     * @link https://op.jinritemai.com/docs/guide-docs/9/21
     *
     * @param array $token_info
     */
    private function cacheToken(array $token_info)
    {
        $ttl = $token_info['expires_in'] - ONE_HOUR;
        if ($ttl <= 0) {
            // 抖店文档上 access_token 有效期为 7 天，若发生变更则取其一半时长作为缓存有效期
            Yii::error(sprintf('doudian access_token expires_in is changed: %d', $token_info['expires_in']), __METHOD__);
            $ttl = intdiv($token_info['expires_in'], 2);
        }

        $cache = Yii::$app->memcache;
        $cache->set(KEY_DOUDIAN_ACCESS_TOKEN, $token_info['access_token'], $ttl);
        $cache->set(KEY_DOUDIAN_REFRESH_TOKEN, $token_info['refresh_token'], ONE_WEEK * 2 - ONE_HOUR);
    }

    /**
     * 刷新 token
     *
     * @link https://op.jinritemai.com/docs/guide-docs/9/21
     *
     * @param string $refresh_token
     * @return mixed
     * @throws Exception
     */
    private function refreshToken(string $refresh_token)
    {
        return $this->request(self::METHOD_TOKEN_REFRESH, [
            'grant_type' => 'refresh_token',
            'refresh_token' => $refresh_token,
        ]);
    }

    /**
     * 解密接口
     *
     * @deprecated 2023-09-01 及其之后无法在抖店云外调用解密接口
     *
     * @link https://op.jinritemai.com/docs/api-docs/15/982
     * @link https://op.jinritemai.com/docs/guide-docs/56/589
     * @link https://bytedance.feishu.cn/docs/doccnJNKML3jOzFgjJitzXy61lh
     *
     * @param array $auth_id_array
     * @param array $encrypted_str_array
     * @return mixed
     * @throws Exception
     */
    public function batchDecrypt(array $auth_id_array, array $encrypted_str_array)
    {
        if (count($auth_id_array) !== count($encrypted_str_array)) {
            throw new Exception('参数错误');
        }

        return $this->request(self::METHOD_ORDER_BATCH_DECRYPT, [
            'cipher_infos' => array_map(function ($auth_id, $encrypted_str) {
                return [
                    'auth_id' => $auth_id,
                    'cipher_text' => $encrypted_str,
                ];
            }, $auth_id_array, $encrypted_str_array),
        ], $this->getAccessToken());
    }

    public function decrypt(string $shop_order_id, string $encrypted_str): string
    {
        try {
            $resp = Yii::$app->serviceRpc->doudianDecrypt($this->app_key, $this->app_secret,
                $this->getAccessToken(), $shop_order_id, $encrypted_str);
        } catch (Exception $e) {
            Yii::error(sprintf('doudian decrypt error: %s', $e->getMessage()), __METHOD__);
            throw new Exception('服务器暂时维护中，请稍候再试');
        }

        return $resp['decrypted_text'];
    }

    /**
     * 获取抖店订单信息
     *
     * @link https://op.jinritemai.com/docs/api-docs/15/1144
     *
     * @param string $shop_order_id
     * @return DouDianOrder
     * @throws Exception
     */
    public function getShopOrder(string $shop_order_id): DouDianOrder
    {
        $order_detail = $this->request(self::METHOD_ORDER_DETAIL, [
            'shop_order_id' => $shop_order_id,
        ], $this->getAccessToken());
        return new DouDianOrder($order_detail, $this);
    }

    /**
     * 商品发货
     *
     * @link https://op.jinritemai.com/docs/api-docs/16/389
     *
     * @param $shop_order_id
     * @throws Exception
     */
    public function deliverGoods($shop_order_id): void
    {
        $this->request(self::METHOD_ORDER_DELIVER_GOODS, [
            'order_id' => $shop_order_id,
            'is_refund_reject' => true,
            'company_code' => 'qita',
        ], $this->getAccessToken());
    }

    /**
     * 售后审核：通过退款
     *
     * @link https://op.jinritemai.com/docs/api-docs/17/560
     *
     * @param string $after_sale_id
     * @return mixed
     * @throws Exception
     */
    public function agreeToRefund(string $after_sale_id): void
    {
        $data = $this->request(self::METHOD_AFTER_SALE_OPERATE, [
            'type' => self::AFTER_SALE_OPERATE_TYPE_AGREE_TO_REFUND,
            'items' => [
                [
                    'aftersale_id' => $after_sale_id,
                ],
            ],
        ], $this->getAccessToken());
        if ($data['items'][0]['status_code'] !== 0) {
            throw new Exception($data['items'][0]['status_msg']);
        }
    }

    /**
     * 获取售后单
     *
     * @link https://op.jinritemai.com/docs/api-docs/17/1095
     *
     * @param string $after_sale_id
     * @return DouDianAfterSaleOrder
     * @throws Exception
     */
    public function getAfterSaleOrder(string $after_sale_id): DouDianAfterSaleOrder
    {
        $after_sale_order = $this->request(self::METHOD_AFTER_SALE_DETAIL, [
            'after_sale_id' => $after_sale_id,
        ], $this->getAccessToken());
        return new DouDianAfterSaleOrder($after_sale_order);
    }

    /**
     * 将商家订单状态告知抖店
     * @link https://op.jinritemai.com/docs/api-docs/164/1639
     *
     * @param string $shop_order_id 抖店订单号
     * @param string $seller_order_id 商家自定义订单号
     * @param string $seller_order_status 商家状态订单状态
     * @param string $err_code 错误码
     * @return void
     * @throws Exception
     */
    public function syncOrderResultToDouDian(string $shop_order_id, string $seller_order_id,
            string $seller_order_status = self::SELLER_ORDER_STATUS_SUCCESS, ?string $err_code = null)
    {
        $params = [
            'trade_order_no' => $shop_order_id,
            'topup_biz' => self::TOPUP_BIZ_MOBILE_TOPUP,
            'seller_order_no' => $seller_order_id,
            'seller_order_status' => $seller_order_status,
        ];
        if (!is_null($err_code)) {
            $params['err_code'] = $err_code;
            $params['err_desc'] = self::ERR_DESC[$err_code] ?? '未知错误';
        }
        $data = $this->request(self::METHOD_SYNC_TOPUP_RESULT, $params, $this->getAccessToken());
        if ($data['result'] !== 'SUCCESS') {
            Yii::error('doudian mobile topup error: ' . Json::encode($data), __METHOD__);
            throw new DouDianException(500, '服务器暂时维护中，请稍候再试');
        }
    }

    /**
     * @link https://op.jinritemai.com/docs/guide-docs/9/21
     * @link https://op.jinritemai.com/docs/guide-docs/10/23
     * @link https://op.jinritemai.com/docs/guide-docs/api-test?apiType=0&interfaceId=2220 接口调试工具
     *
     * @param string $method
     * @param array $params
     * @param string|null $access_token
     * @return mixed
     * @throws Exception
     */
    private function request(string $method, array $params, ?string $access_token = null)
    {
        try {
            $body = $this->body($params);
            $timestamp = date('Y-m-d H:i:s', $_SERVER['REQUEST_TIME']);
            $resp = Yii::$app->tools->requestRemote(
                $this->getURL($method, $body, $timestamp, $access_token),
                [],
                'POST',
                $body
            );
            if ($resp['code'] !== self::CODE_SUCCESS) {
                throw new Exception(sprintf(
                    'code[%d], msg[%s], sub_code[%s], sub_msg[%s], log_id[%s]',
                    $resp['code'], $resp['msg'], $resp['sub_code'] ?? '', $resp['sub_msg'] ?? '', $resp['log_id'])
                );
            }
            return $resp['data'] ?? null;
        } catch (Exception $e) {
            Yii::error('doudian request error: ' . $e->getMessage(), __METHOD__);
        }

        throw new DouDianException(500, '服务器暂时维护中，请稍候再试');
    }

    private function getURL(string $method, string $body, string $timestamp, ?string $access_token): string
    {
        [$sign, $sign_method] = $this->sign($method, $body, $timestamp);
        $query_params = [
            'app_key' => $this->app_key,
            'method' => $method,
            'timestamp' => $timestamp,
            'v' => self::API_VERSION,
            'sign' => $sign,
            'sign_method' => $sign_method,
        ];
        if (!is_null($access_token)) {
            $query_params['access_token'] = $access_token;
        }
        return self::GATEWAY
            . '/' . str_replace('.', '/', $method)
            . '?' . http_build_query($query_params);
    }

    private function sign(string $method, string $body, string $timestamp): array
    {
        $param_pattern = "app_key{$this->app_key}method{$method}param_json{$body}timestamp{$timestamp}v2";
        return [
            md5($this->app_secret . $param_pattern . $this->app_secret),
            self::SIGN_METHOD,
        ];
    }

    private static function sortParams(array &$params): void
    {
        ksort($params);
        foreach ($params as $k => &$v) {
            if (is_array($v)) {
                self::sortParams($v);
            }
        }
    }

    private function body(array $params): string
    {
        self::sortParams($params);
        return json_encode($params, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    }

    public function isLegalPushMessage(string $app_key, string $event_sign, string $raw_body): bool
    {
        if ($app_key !== $this->app_key) {
            return false;
        }

        return $event_sign === md5($app_key . $raw_body . $this->app_secret);
    }

    public function isLegalShopId(string $shop_id): bool
    {
        return $shop_id === $this->shop_id;
    }

    /**
     * 获取 SPI 签名
     *
     * @link https://bytedance.feishu.cn/docs/doccnsslxlco3x7UsQAoFmdeQfb#
     *
     * @param array $params
     * @param string $app_key
     * @param string $timestamp
     * @param string $sign_method
     * @return string
     * @throws Exception
     */
    public function getSPISign(array $params, string $app_key, string $timestamp, string $sign_method)
    {
        $body = $this->body($params);
        $content = "app_key{$app_key}param_json{$body}timestamp{$timestamp}";
        $data = "{$this->app_secret}{$content}{$this->app_secret}";

        switch ($sign_method) {
            case 'md5':
                return md5($data);
            case 'hmac-sha256':
                return hash_hmac('sha256', $data, $this->app_secret);
            default:
                throw new Exception('unsupport sign method: ' . $sign_method);
        }
    }

}
