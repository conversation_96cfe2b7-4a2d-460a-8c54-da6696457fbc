<?php

namespace app\components\auth\douyin;

use yii\helpers\Json;

class DouDianPushMsgContent implements DouDianMsgInterface
{
    use DouDianMsgContentTrait;

    // 售后状态：待商家处理
    // https://op.jinritemai.com/docs/message-docs/31/115
    const AFTER_SALE_STATUS_WAITING_MERCHANT_PROCESS = 6;

    /**
     * @var int|null
     */
    public $after_sale_id;

    public function __construct(string $data)
    {
        $content = Json::decode($data);
        $this->shop_order_id = $content['p_id'];
        $this->shop_id = $content['shop_id'];
        if (array_key_exists('aftersale_id', $content)) {
            $this->after_sale_id = $content['aftersale_id'];
        }
    }

    public function getBiz(): int
    {
        return DouDianOrder::ORDER_BIZ_VIRTUAL;
    }

}
