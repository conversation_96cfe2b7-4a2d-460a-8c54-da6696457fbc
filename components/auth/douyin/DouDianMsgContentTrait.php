<?php

namespace app\components\auth\douyin;

use app\models\RechargeOrder;

trait DouDianMsgContentTrait
{
    /**
     * @var string|null
     */
    private $shop_id;
    /**
     * @var DouDianOrder|null
     */
    private $shop_order;
    /**
     * @var string
     */
    private $shop_order_id;
    /**
     * @var RechargeOrder|null
     */
    private $seller_order;

    public function getShopId(): string
    {
        return $this->shop_id;
    }

    public function getShopOrderId(): string
    {
        return (string)$this->shop_order_id;
    }

    public function setShopOrder(DouDianOrder $order)
    {
        $this->shop_order = $order;
    }

    public function getShopOrder(): ?DouDianOrder
    {
        return $this->shop_order;
    }

    public function setSellerOrder(RechargeOrder $order)
    {
        $this->seller_order = $order;
    }

    public function getSellerOrder(): ?RechargeOrder
    {
        return $this->seller_order;
    }

}
