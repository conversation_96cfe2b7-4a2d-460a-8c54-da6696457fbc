<?php

namespace app\components\auth\douyin;

use app\models\Balance;

class DouDianOrder
{
    // 订单状态
    const ORDER_STATUS_CREATE = 1;  // 在线支付订单待支付，或货到付款订单待确认
    const ORDER_STATUS_PAID_AND_READY_TO_DELIVER = 2;  // 已支付，备货中（只有此状态下，才可发货）
    const ORDER_STATUS_DELIVERED = 3;  // 已发货
    const ORDER_STATUS_CANCELED = 4;  // 已取消
    const ORDER_STATUS_FINISHED = 5;  // 已完成（货到付款订单，商家发货后，用户收货、拒收或者 15 天无物流更新；在线支付订单，用户确认收货）

    // 业务来源：5 虚拟、24 手机充值
    const ORDER_BIZ_VIRTUAL = 5;
    const ORDER_BIZ_TOPUP = 24;

    /**
     * @var string|null
     */
    private $_order_id;
    /**
     * @var int|null
     */
    private $_order_status;
    /**
     * @var int|null 订单原价（单位：分）
     */
    private $_original_price_in_fen;
    /**
     * @var int|null 订单实际支付的金额（单位：分）
     */
    private $_total_price_in_fen;
    /**
     * @var int|null 订单优惠总金额（店铺优惠金额+平台优惠金额+达人优惠金额）（单位：分）
     */
    private $_promotion_price_in_fen;
    /**
     * @var array|DouDianGoods[]
     */
    private $_goods = [];
    /**
     * @var string|null
     */
    private $_encrypted_buyer_tel;
    /**
     * @var int|null
     */
    private $_biz;
    /**
     * @var string|null
     */
    private $_biz_desc;
    /**
     * @var AuthDouDian|null
     */
    private $_doudian;

    /**
     * DouDianOrder constructor.
     * @param array $order
     * @param AuthDouDian $doudian
     */
    public function __construct(array $order, AuthDouDian $doudian)
    {
        $this->_order_id = $order['shop_order_detail']['order_id'];
        $this->_order_status = $order['shop_order_detail']['order_status'];
        $this->_original_price_in_fen = (int)$order['shop_order_detail']['order_amount'];
        $this->_total_price_in_fen = (int)$order['shop_order_detail']['pay_amount'];
        $this->_promotion_price_in_fen = (int)$order['shop_order_detail']['promotion_amount'];

        $this->_goods = array_map(function ($sku) {
            return new DouDianGoods($sku);
        }, $order['shop_order_detail']['sku_order_list']);

        $this->_encrypted_buyer_tel = $order['shop_order_detail']['encrypt_post_tel'];
        $this->_biz = (int)$order['shop_order_detail']['biz'];
        $this->_biz_desc = $order['shop_order_detail']['biz_desc'];
        $this->_doudian = $doudian;
    }

    public function getGoods()
    {
        return $this->_goods;
    }

    /**
     * 返回商品的总价（单位：分）
     * @return int
     */
    public function getSumPriceByGoods()
    {
        $prices = array_map(function ($goods) {
            /**
             * @var DouDianGoods $goods
             */
            return $goods->getPriceInFen();
        }, $this->_goods);
        return array_sum($prices);
    }

    public function getPriceInFen()
    {
        return $this->_total_price_in_fen;
    }

    public function getPriceInYuan()
    {
        return Balance::profitUnitConversion($this->getPriceInFen(), Balance::CONVERT_FEN_TO_YUAN);
    }

    public function getOriginalPriceInFen()
    {
        return $this->_original_price_in_fen;
    }

    public function getPromotionPriceInFen()
    {
        return $this->_promotion_price_in_fen;
    }

    public function getOrderId()
    {
        return $this->_order_id;
    }

    public function getStatus()
    {
        return $this->_order_status;
    }

    public function isReadyToPay()
    {
        return $this->getStatus() === self::ORDER_STATUS_CREATE;
    }

    public function isReadyToDeliveryGoods()
    {
        return $this->getStatus() === self::ORDER_STATUS_PAID_AND_READY_TO_DELIVER;
    }

    public function isDelivered()
    {
        return $this->getStatus() === self::ORDER_STATUS_DELIVERED;
    }

    public function isFinished()
    {
        return $this->getStatus() === self::ORDER_STATUS_FINISHED;
    }

    public function getBuyerTelephone()
    {
        // 返回的格式：手机号 ,,
        // 例：15237844228 ,,
        $result = $this->_doudian->decrypt($this->getOrderId(), $this->_encrypted_buyer_tel);
        return trim(explode(',', $result)[0]);
    }

    public function getBiz()
    {
        return $this->_biz;
    }

}
