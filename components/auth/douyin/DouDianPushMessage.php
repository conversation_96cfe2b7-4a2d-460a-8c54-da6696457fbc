<?php

namespace app\components\auth\douyin;

use yii\helpers\Json;

/**
 * @link https://op.jinritemai.com/docs/message-docs/30/109
 *
 * Class DouDianPushMessage
 * @package app\components\auth
 */
class DouDianPushMessage
{
    // 消息推送类型
    const TAG_PUSH_MESSAGE_OPEN = '0';  // 开启推送事件
    const TAG_ORDER_CREATE = '100';  // 创建订单
    const TAG_ORDER_PAID = '101';  // 订单支付
    const TAG_ORDER_REFUND_CREATE = '200';  // 发起退款

    /**
     * @var string|null
     */
    private $_tag;

    /**
     * @var string|null
     */
    private $_msg_id;
    /**
     * @var DouDianPushMsgContent|null
     */
    private $_content;

    public function __construct(array $msg)
    {
        $this->_tag = $msg['tag'];
        $this->_msg_id = $msg['msg_id'];
        $this->_content = new DouDianPushMsgContent($msg['data']);
    }

    public function getTag()
    {
        return $this->_tag;
    }

    public function getMsgId()
    {
        return $this->_msg_id;
    }

    /**
     * @return DouDianPushMsgContent
     */
    public function getContent()
    {
        return $this->_content;
    }

    public static function successfulResponse()
    {
        return Json::encode(['code' => 0, 'msg' => 'success']);
    }

}
