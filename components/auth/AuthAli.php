<?php
/**
 * Created by PhpStorm.
 * User: jiepe
 * Date: 2017/8/25
 * Time: 11:52
 */

namespace app\components\auth;

use AlipayTradeAppPayRequest;
use AlipayTradePagePayRequest;
use AlipayUserAgreementExecutionplanModifyRequest;
use AlipayUserAgreementQueryRequest;
use AlipayUserAgreementUnsignRequest;
use AlipayUserCertifyOpenCertifyRequest;
use AlipayUserCertifyOpenInitializeRequest;
use AlipayUserCertifyOpenQueryRequest;
use AopClient;
use app\components\util\Equipment;
use app\components\util\ModelTrait;
use app\components\util\MUtils;
use app\forms\UserContext;
use app\models\Balance;
use app\models\GuildLiveOrder;
use app\models\MUserVip;
use app\models\MVip;
use app\models\RechargeOrder;
use app\models\VipFeeDeductedRecord;
use app\models\VipSubscriptionSignAgreement;
use Exception;
use Yii;
use yii\helpers\Json;
use yii\web\HttpException;

class AuthAli
{
    use ModelTrait;

    private static $AOP;

    const CODE_SUCCESS = '10000';

    private $_notify_url = [];

    public function __construct()
    {
        if (!self::$AOP) {
            $aop = new AopClient();
            if (!$config = Yii::$app->params['service']['alipay'] ?? null) {
                throw new Exception('未找到支付宝配置');
            }
            [
                'gateway' => $aop->gatewayUrl,
                'app_id' => $aop->appId,
                'public_key' => $aop->alipayrsaPublicKey,
                'develop_private_key' => $aop->rsaPrivateKey,
                'notify_url' => $this->_notify_url,
            ] = $config;

            $aop->format='json';
            $aop->postCharset='UTF-8';
            $aop->signType = 'RSA2';
            $aop->apiVersion = '1.0';

            self::$AOP = $aop;
        }
    }

    public function getAopClient()
    {
        return self::$AOP;
    }

    /**
     * 支付宝身份认证初始化
     *
     * @link https://docs.open.alipay.com/api_2/alipay.user.certify.open.initialize/
     * @param string $real_name 真实姓名
     * @param string $id_no 身份证号
     * @return string
     * @throws Exception
     */
    public function alipayUserCertifyInit(string $real_name, string $id_no): string
    {
        $return_url = 'missevan://certificate';
        // WORKAROUND: iOS 新版本认证成功后使用 missevan://certificate 作为从支付宝回到 App 的地址，兼容老版本的处理
        if (Equipment::isAppOlderThan('4.3.6')) {
            $return_url = 'missevan://homepage';
        }

        $biz_content = [
            'outer_order_no' => sprintf('tid%s_%d', $id_no, $_SERVER['REQUEST_TIME']),
            'biz_code' => 'FACE',
            'identity_param' => [
                'identity_type' => 'CERT_INFO',
                'cert_type' => 'IDENTITY_CARD',
                'cert_name' => $real_name,
                'cert_no' => $id_no,
            ],
            'merchant_config' => [
                'return_url' => $return_url,
            ],
        ];

        $request = new AlipayUserCertifyOpenInitializeRequest();
        $request->setBizContent(json_encode($biz_content));
        $result = self::$AOP->execute($request);

        $node = str_replace('.', '_', $request->getApiMethodName()) . '_response';
        $response = $result->$node;
        if ($response->code !== '10000') {
            Yii::error(sprintf('身份认证出错：code[%s], msg[%s], sub_code[%s], sub_msg[%s], real_name[%s], id_no[%s]',
                $response->code, $response->msg, $response->sub_code ?? '', $response->sub_msg ?? '',
                MUtils::encrypt($real_name, SENSITIVE_FIXED_IV_KEY),
                MUtils::encrypt($id_no, SENSITIVE_FIXED_IV_KEY)), __METHOD__);
            throw new Exception('身份认证失败请稍后重试');
        }
        return $response->certify_id;
    }

    /**
     * 支付宝开始身份认证
     *
     * @link https://docs.open.alipay.com/api_2/alipay.user.certify.open.certify/
     * @param string $biz_no 申请操作的唯一标识
     * @return string
     * @throws Exception
     */
    public function alipayUserCertifyStart(string $biz_no): string
    {
        $request = new AlipayUserCertifyOpenCertifyRequest();
        $request->setBizContent(json_encode([
            'certify_id' => $biz_no,
        ]));
        $url = self::$AOP->pageExecute($request, 'GET');
        return $url;
    }

    /**
     * 检查支付宝身份认证是否通过
     *
     * @link https://docs.open.alipay.com/api_2/alipay.user.certify.open.query/
     * @param string $biz_no 申请操作的唯一标识
     * @return boolean
     * @throws Exception
     */
    public function alipayUserCertifyPass($biz_no): bool
    {
        $request = new AlipayUserCertifyOpenQueryRequest();
        $request->setBizContent(json_encode([
            'certify_id' => $biz_no,
        ]));
        $result = self::$AOP->execute($request);

        $node = str_replace('.', '_', $request->getApiMethodName()) . '_response';
        $response = $result->$node;
        if ($response->code !== '10000') {
            Yii::error(sprintf('身份认证出错：code[%s], msg[%s]', $response->code, $response->msg), __METHOD__);
            throw new Exception('身份认证失败请稍后重试');
        }
        return $response->passed === 'T';
    }

    /**
     * App 支付生成支付宝订单
     *
     * @link https://opendocs.alipay.com/open/02e7gq?scene=20
     *
     * @param RechargeOrder $order
     * @return string
     */
    public function tradeAppPay(RechargeOrder $order)
    {
        $request = new AlipayTradeAppPayRequest();
        $request->setNotifyUrl($this->_notify_url['default']);

        $content = [
            'body' => '钻石',
            'subject' => '购买钻石',
            'out_trade_no' => $order->getOrderId(),
            'timeout_express' => '30m',
            'total_amount' => $order->price,
            'product_code' => 'QUICK_MSECURITY_PAY',
            'business_params' => $this->getBusinessParams($order),
        ];
        $bizcontent = json_encode($content);
        $request->setBizContent($bizcontent);

        $response = self::$AOP->sdkExecute($request);
        return $response;
    }

    /**
     * 支付宝生成 PC 网站交易订单
     *
     * @link https://opendocs.alipay.com/open/028r8t?scene=22
     *
     * @param RechargeOrder $order
     * @return string
     * @throws Exception
     */
    public function tradePagePay(RechargeOrder $order)
    {
        $request = new \AlipayTradePagePayRequest();
        $order_id = $order->getOrderId();
        $request->setReturnUrl(Yii::$app->params['domainMissevan'] .
            sprintf('/mperson/wallet/topupresult?out_trade_no=%s', $order_id));
        $request->setNotifyUrl($this->_notify_url['default']);

        $content = [
            'body' => '钻石',
            'subject' => '购买钻石',
            'out_trade_no' => $order_id,
            'timeout_express' => '30m',
            'total_amount' => $order->price,
            'product_code' => 'FAST_INSTANT_TRADE_PAY',
            'business_params' => $this->getBusinessParams($order),
        ];
        $bizcontent = json_encode($content);
        $request->setBizContent($bizcontent);

        $result = self::$AOP->pageExecute($request);
        return $result;
    }

    /**
     * 支付宝生成手机网页交易订单
     *
     * @link https://opendocs.alipay.com/open/203/105285?pathHash=ada1de5b
     *
     * @param RechargeOrder $order
     * @return string
     * @throws Exception
     */
    public function tradeMobileWebPay(RechargeOrder $order)
    {
        $request = new \AlipayTradeWapPayRequest();
        $order_id = $order->getOrderId();
        $content = [
            'body' => '钻石',
            'subject' => '购买钻石',
            'out_trade_no' => $order_id,
            'timeout_express' => '30m',
            'total_amount' => $order->price,
            'product_code' => 'QUICK_WAP_WAY',
            'business_params' => $this->getBusinessParams($order),
        ];
        $bizcontent = json_encode($content);

        $request->setReturnUrl(Yii::$app->params['domainMobileWeb'] . "/wallet/topupresult?out_trade_no={$order_id}");
        $request->setNotifyUrl($this->_notify_url['default']);
        $request->setBizContent($bizcontent);

        $result = self::$AOP->pageExecute($request);
        return $result;
    }

    /**
     * 获取业务相关参数（用于风控）
     *
     * @link https://opendocs.alipay.com/open/01og7y?ref=api
     *
     * @param RechargeOrder $order
     * @return array
     */
    private function getBusinessParams(RechargeOrder $order): array
    {
        return [
            'mc_create_trade_ip' => $order->getDetail()['ip'] ?? Yii::$app->request->userIP,
        ];
    }

    /**
     * 获取业务相关参数（用于风控）
     *
     * @link https://opendocs.alipay.com/open/01og7y?ref=api
     *
     * @param UserContext|null $user_context
     * @return array
     */
    private function getBusinessParamsFromUserContext(UserContext|null $user_context): array
    {
        return [
            'mc_create_trade_ip' => $user_context?->ip ?? Yii::$app->request->userIP,
        ];
    }

    /**
     * 主播强制解约支付违约金订单
     *
     * @link https://opendocs.alipay.com/open/028r8t?scene=22
     *
     * @param GuildLiveOrder $order
     * @return string|\提交表单 HTML 文本
     * @throws HttpException
     */
    public function tradeLivePenaltyPay(GuildLiveOrder $order)
    {
        $request = new AlipayTradePagePayRequest();
        $order_id = $order->getOutTradeNo();
        $request->setReturnUrl(sprintf('%s/mperson/center/#/guild', Yii::$app->params['domainMissevan']));
        $request->setNotifyUrl($this->_notify_url['guild_live_order']);

        $content = [
            'body' => '公会主播支付',
            'subject' => '强制解约违约金',
            'out_trade_no' => $order_id,
            'timeout_express' => '30m',
            'total_amount' => (string)Balance::profitUnitConversion($order->price, Balance::CONVERT_FEN_TO_YUAN),
            'product_code' => 'FAST_INSTANT_TRADE_PAY',
        ];
        $bizcontent = Json::encode($content);
        $request->setBizContent($bizcontent);

        $result = self::$AOP->pageExecute($request, 'GET');
        return $result;
    }

    /**
     * App 开通点播会员下单
     *
     * @link https://opendocs.alipay.com/open/02fkau?scene=common
     * @link https://opendocs.alipay.com/open/06de8c?pathHash=654eb816
     * @notice 商家扣款不支持沙盒调试
     * @notice 支付成功但签约未成功可能原因：用户关闭签约自动续费、用户未通过支付宝免密自动扣费校验（如支付宝未绑定手机号，支付宝判定免密心智较弱暂不支持签约）
     *
     * @param VipFeeDeductedRecord $order 支付记录
     * @param VipSubscriptionSignAgreement|null $sign_agreement 签约记录（单次付费时为 null）
     * @param MVip $vip 价目信息
     * @return array 用于调用收银台 SDK 的字符串
     * @throws Exception
     */
    public function tradeVipAppPay(VipFeeDeductedRecord $order, VipSubscriptionSignAgreement|null $sign_agreement, MVip $vip): array
    {
        $request = new AlipayTradeAppPayRequest();
        $request->setNotifyUrl($this->_notify_url['default']);
        $order_subject = $vip->getOrderSubject();
        if (is_null($order_subject)) {
            $msg = sprintf('会员价目未配置订单标题，价目 ID: %d', $vip->id);
            Yii::error($msg, __METHOD__);
            throw new HttpException(500, '服务器内部错误');
        }
        $content = [
            'subject' => $order_subject,
            'out_trade_no' => $order->getOutTradeNo(),
            'timeout_express' => '30m',
            // 订单总金额（或周期扣款的首次支付金额，不算在周期扣总金额里面）
            'total_amount' => (string)Balance::profitUnitConversion($order->price, Balance::CONVERT_FEN_TO_YUAN), // 订单总金额（首次支付金额）
            'business_params' => $this->getBusinessParamsFromUserContext($order->getUserContext()),
        ];
        if ($vip->isSubscription()) {
            // 订阅付费
            if (is_null($sign_agreement)) {
                $msg = sprintf('会员签约记录为空，扣费记录 ID: %d', $order->id);
                Yii::error($msg, __METHOD__);
                throw new HttpException(500, '服务器内部错误');
            }
            $vip_period = $vip->getPeriod();
            if (is_null($vip_period)) {
                $msg = sprintf('会员价目未配置周期时间，价目 ID: %d', $vip->id);
                Yii::error($msg, __METHOD__);
                throw new HttpException(500, '服务器内部错误');
            }
            $sign_scene = $vip->getAlipaySignScene();
            if (is_null($sign_scene)) {
                Yii::error(sprintf('会员价目未配置支付宝场景码, 价目 ID: %d', $vip->id),
                    __METHOD__);
                throw new HttpException(500, '服务器内部错误');
            }
            // 实际调度时会按情况提前扣费，这里设置的是下次扣款的截止时间
            // https://opendocs.alipay.com/open/08bg92?pathHash=b655de17
            $execute_time = MUserVip::calculateEndTime($_SERVER['REQUEST_TIME'], $vip_period);
            $content['product_code'] = 'CYCLE_PAY_AUTH';
            $content['agreement_sign_params'] = [
                'personal_product_code' => 'CYCLE_PAY_AUTH_P',
                // 具体参数请商家完成产品签约后，根据业务场景或用户购买商品的差异性对应新增模版及场景码，签约接入时需要传入模版中实际填写的场景码
                // https://opendocs.alipay.com/open/08bg92?pathHash=b655de17
                'sign_scene' => $sign_scene,
                // 商家签约号，代扣协议中标示用户的唯一签约号（确保在商家系统中唯一）
                // 若签约时传入了签约号及场景参数，后续查询协议和解约时也必须传入。
                'external_agreement_no' => $sign_agreement->getOutAgreementNo(),
                // 签约成功异步通知地址
                'sign_notify_url' => $this->_notify_url['default'],
                'access_params' => [
                    'channel' => 'ALIPAYAPP',
                ],
                'period_rule_params' => [
                    'period_type' => 'DAY',
                    // 周期类型使用 DAY 的时候，周期数 period 不允许小于 7（包含 7），避免间隔日期太短，扣款过于频繁，没有周期限制的意义
                    // https://opendocs.alipay.com/open/08bpuc?pathHash=74036bac
                    'period' => (string)floor($vip_period / ONE_DAY),
                    // 发起扣款的时间需符合这里的扣款计划
                    // 下次扣款时间=支付并签约当笔支付时间加上扣款间隔，不以接口传入的时间为准。
                    // https://opendocs.alipay.com/open/06de8c?pathHash=654eb816
                    // https://opendocs.alipay.com/open/08bpuc?pathHash=74036bac
                    'execute_time' => date('Y-m-d', $execute_time),
                    // 每次发起扣款时限制的最大金额，单位为元。商家每次发起扣款都不允许大于此金额。
                    'single_amount' => (string)Balance::profitUnitConversion($vip->price, Balance::CONVERT_FEN_TO_YUAN),
                ],
            ];
        } else {
            // 单次付费
            $content['product_code'] = 'QUICK_MSECURITY_PAY';
        }
        $request->setBizContent(Json::encode($content));
        $alipay_body = self::$AOP->sdkExecute($request);
        return [
            'trade_no' => $order->getOutTradeNo(),
            'alipay_body' => $alipay_body
        ];
    }

    private static function getResponse(string $api_method_name, object $response_result)
    {
        $response_name = str_replace(".", "_", $api_method_name) . "_response";
        return $response_result->$response_name ?? null;
    }

    /**
     * 支付宝个人代扣协议查询
     *
     * @link https://opendocs.alipay.com/open/02fkao?scene=common&pathHash=ca39423f
     * @link https://opendocs.alipay.com/open/08bpuc?pathHash=74036bac
     *
     * @param string $agreement_no 支付宝签约协议号
     * @return array|null
     * 正常响应：
     * [
     *     'code' => '10000',
     *     'msg' => 'Success',
     *     'principal_id' => '2088101122675263',
     *     'principal_open_id' => '074a1CcTG1LelxKe4xQC0zgNdId0nxi95b5lsNpazWYoCo5',
     *     'valid_time' => '2017-05-24 15:00:40',
     *     'alipay_logon_id' => 'test***<EMAIL>',
     *     'invalid_time' => '2117-05-24 00:00:00',
     *     'pricipal_type' => 'CARD',
     *     'device_id' => 'RSED235F875932',
     *     'sign_scene' => 'INDUSTRY|CARRENTAL',
     *     'agreement_no' => '20170322450983769228',
     *     'third_party_type' => 'PARTNER',
     *     'status' => 'NORMAL',
     *     'sign_time' => '2017-05-24 15:00:40',
     *     'personal_product_code' => 'GENERAL_WITHHOLDING_P',
     *     'external_agreement_no' => 'test',
     *     'zm_open_id' => '268816057852461313538942792',
     *     'external_logon_id' => '2088101118392209',
     *     'credit_auth_mode' => 'DEDUCT_HUAZHI',
     *     'single_quota' => '100.00',
     *     'last_deduct_time' => '2022-05-15',
     *     'next_deduct_time' => '2022-06-15',
     *     'execution_plans' => [
     *         [
     *             'single_amount' => '100.00',
     *             'period_id' => '1',
     *             'execute_time' => '2024-04-29',
     *             'latest_execute_time' => '2024-05-30'
     *         ]
     *     ]
     * ]
     * 异常响应：
     * [
     *     'code' => '20000',
     *     'msg' => 'Service Currently Unavailable',
     *     'sub_code' => 'isp.unknow-error',
     *     'sub_msg' => '系统繁忙'
     * ]
     * @throws Exception
     */
    public function queryUserAgreement(string $agreement_no)
    {
        $request = new AlipayUserAgreementQueryRequest();
        $request->setBizContent(Json::encode([
            'agreement_no' => $agreement_no,
        ]));
        $response = self::$AOP->execute($request);
        return self::getResponse($request->getApiMethodName(), $response);
    }

    /**
     * 支付宝个人代扣协议解约
     *
     * @link https://opendocs.alipay.com/open/02fkap?scene=common&pathHash=47fbc0c5
     * @link https://opendocs.alipay.com/open/08bpuc?pathHash=74036bac
     *
     * @param string $agreement_no 支付宝签约协议号
     * @return array|null
     *   正常响应：
     *   [
     *       'code' => '10000',
     *       'msg' => 'Success'
     *   ]
     *   异常响应：
     *   [
     *       'code' => '20000',
     *       'msg' => 'Service Currently Unavailable',
     *       'sub_code' => 'isp.unknow-error',
     *       'sub_msg' => '系统繁忙'
     *   ]
     * @throws Exception
     */
    public function unSignUserAgreement(string $agreement_no)
    {
        $request = new AlipayUserAgreementUnsignRequest();
        $request->setBizContent(Json::encode([
            'agreement_no' => $agreement_no,
        ]));
        $response = self::$AOP->execute($request);
        return self::getResponse($request->getApiMethodName(), $response);
    }

    /**
     * 周期性扣款协议执行计划修改
     *
     * @link https://opendocs.alipay.com/open/02fkaq?scene=common&pathHash=a59f6784
     *
     * @param string $agreement_no 支付宝签约协议号
     * @param string $next_deduct_time 下次扣款时间（日期类型，例 2025-03-12）
     * @param string|null $memo 备注
     * @return array|null
     *   正常响应：
     *   [
     *       'code' => '10000',
     *       'msg' => 'Success',
     *       'agreement_no' => '20185909000458725113',
     *       'deduct_time' => '2020-05-12'
     *   ]
     *   异常响应：
     *   [
     *       'code' => '20000',
     *       'msg' => 'Service Currently Unavailable',
     *       'sub_code' => 'isp.unknow-error',
     *       'sub_msg' => '系统繁忙'
     *   ]
     * @throws Exception
     */
    public function modifyExecutionPlan(string $agreement_no, string $next_deduct_time, null|string $memo = null)
    {
        $request = new AlipayUserAgreementExecutionplanModifyRequest();
        $content = [
            'agreement_no' => $agreement_no,
            'deduct_time' => $next_deduct_time,
        ];
        if (is_null($memo)) {
            $content['memo'] = $memo;
        }
        $request->setBizContent(Json::encode($content));
        $response = self::$AOP->execute($request);
        return self::getResponse($request->getApiMethodName(), $response);
    }
}
