<?php

namespace app\components\auth\appleservernotification;

use stdClass;

/**
 * Class SignedRenewalInfo
 * @link https://developer.apple.com/help/app-store-connect/reference/auto-renewable-subscription-information 自动订阅的时长说明 \
 * 例：用户在某月 31 号开通 1 个月的订阅，则将在下个月的 31 号（如果没有对应日期，则在下个月的最后一天）续订，如 01-31 开通，则将在 02-28 或 02-29 续订，在 03-31、04-30 续订
 *
 * @property-read string $originalTransactionId 原始交易的 ID，例：720001565067608，在订阅时为最初订阅时的交易 ID
 * @property-read string $autoRenewProductId 下一个账单周期时需要续期的商品 ID
 * @property-read string $productId 内购商品的 ID，例：com.missevan.CatEarFM.vip000001
 * @property-read int $autoRenewStatus 自动续期商品的状态，例：1
 * @property-read int $renewalPrice 下一个账单周期的续期价格，例：300000
 * @property-read string $currency 交易使用的币种，ISO 4217 标准的货币代码（3 个字符）
 * @property-read int $signedDate 苹果加签成 JWS 报文的时间，毫秒级时间戳，例：1729514174000
 * @property-read string $environment 通知来源，Sandbox 或 Production
 * @property-read int $recentSubscriptionStartDate 自动续期订阅的最早开始日期，毫秒级时间戳，例：1729510574000
 * @property-read int $renewalDate 最近一次自动续期的订阅的到期时间，毫秒级时间戳，例：1729514174000
 * @property-read int $expirationIntent 订阅过期的原因，例：0
 * @property-read bool $isInBillingRetryPeriod 苹果是否在尝试自动续期已过期的订阅，例：false
 */
class SignedRenewalInfo
{
    use NotificationTrait;

    private $_data;

    public function __construct(stdClass $data)
    {
        $this->_data = $data;
    }

}
