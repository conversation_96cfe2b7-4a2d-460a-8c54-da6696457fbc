<?php

namespace app\components\auth\appleservernotification;

use Exception;
use stdClass;

/**
 * Class ServerNotificationData
 *
 * @property-read int $appAppleId App 的 ID，例 1148465254，用于下载 App，如 https://apps.apple.com/cn/app/id1148465254
 * @property-read string $bundleId App 包名，例 com.missevan.CatEarFM
 * @property-read string $bundleVersion App 包的版本号，例 115
 * @property-read string $environment 通知来源，Sandbox 或 Production
 * @property-read SignedTransactionInfo $signedTransactionInfo 交易相关信息
 * @property-read SignedRenewalInfo $signedRenewalInfo 自动续期的订阅相关信息
 * @property-read int $status 自动续期类型的订阅商品的状态，1 订阅有效，2 订阅已过期，3 订阅处于账单重试期，4 订阅账单重试失败，已过计费宽限期，5 订阅被撤销
 */
class ServerNotificationData
{
    use NotificationTrait;

    // https://developer.apple.com/documentation/appstoreservernotifications/environment
    const ENVIRONMENT_SANDBOX = 'Sandbox';
    const ENVIRONMENT_PRODUCTION = 'Production';

    // 自动续期类型的订阅商品的状态
    // https://developer.apple.com/documentation/appstoreservernotifications/status
    const STATUS_SUBSCRIPTION_VALID = 1;
    const STATUS_SUBSCRIPTION_EXPIRED = 2;
    const STATUS_SUBSCRIPTION_BILLING_RETRY = 3;
    const STATUS_SUBSCRIPTION_BILLING_RETRY_FAILED = 4;
    const STATUS_SUBSCRIPTION_REVOKED = 5;

    private $_data;

    public function __construct(stdClass $data)
    {
        if (isset($data->signedTransactionInfo)) {
            if (is_string($data->signedTransactionInfo)) {
                $signed_transaction_info = ServerNotification::parseSignedPayload($data->signedTransactionInfo);
                if (!$signed_transaction_info) {
                    throw new Exception('报文解析失败');
                }
            } elseif (is_array($data->signedTransactionInfo)) {
                $signed_transaction_info = (object)$data->signedTransactionInfo;
            } else {
                throw new Exception('未知的 signedTransactionInfo');
            }
            $data->signedTransactionInfo = new SignedTransactionInfo($signed_transaction_info);
        }

        if (isset($data->signedRenewalInfo)) {
            if (is_string($data->signedRenewalInfo)) {
                $signed_renewal_info = ServerNotification::parseSignedPayload($data->signedRenewalInfo);
                if (!$signed_renewal_info) {
                    throw new Exception('报文解析失败');
                }
            } elseif (is_array($data->signedRenewalInfo)) {
                $signed_renewal_info = (object)$data->signedRenewalInfo;
            } else {
                throw new Exception('未知的 signedRenewalInfo');
            }
            $data->signedRenewalInfo = new SignedRenewalInfo($signed_renewal_info);
        }

        $this->_data = $data;
    }

    public function isProductionEnv()
    {
        return $this->environment === self::ENVIRONMENT_PRODUCTION;
    }

}

