<?php

namespace app\components\auth\appleservernotification;

use Exception;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Yii;
use yii\helpers\Json;

/**
 * Class ServerNotification
 * @link https://developer.apple.com/documentation/appstoreservernotifications/responsebodyv2decodedpayload
 *
 * @property-read string $notificationType 通知类型，例：SUBSCRIBED
 * @property-read string $subtype 通知子类型，例：INITIAL_BUY
 * @property-read ServerNotificationData $data 包含 App 的原信息，及加签的订阅、交易相关信息
 * @property-read string $notificationUUID 唯一 ID，例：023bf16a-887a-4550-8004-6d94f4bb48a7
 * @property-read int $signedDate 苹果对数据加签成 JWS 的时间，毫秒级时间戳，例：1729510574000
 * @property-read string $version 通知的版本，例：2.0
 */
class ServerNotification
{
    use NotificationTrait;

    // 通知类型
    // https://info.missevan.com/pages/viewpage.action?pageId=118095008
    const NOTIFICATION_TYPE_LIST = [
        'SUBSCRIBED',  // 订阅
        'DID_CHANGE_RENEWAL_PREF',  // 变更订阅计划（升级/降级）
        'DID_CHANGE_RENEWAL_STATUS',  // 开启/关闭自动订阅
        'DID_RENEW',  // 续订

        'GRACE_PERIOD_EXPIRED',  // 计费宽限期已结束，但没有续订订阅
        'REVOKE',  // 用户通过家庭共享赋权的内购，不再可用
        'EXPIRED',  // 订阅过期

        'REFUND',  // 完成交易退款

        'DID_FAIL_TO_RENEW',  // 处理计费重试期中，由于账单问题订阅无法续订，订阅进行计费重试期
        'OFFER_REDEEMED',  // 兑换
        'ONE_TIME_CHARGE',  // 用户购买了消耗型/非消耗型/非续订型订阅，或通过家人共享获得对非消耗性的访问权限时，苹果会发送此通知。目前此通知仅在沙盒环境中可用
        'RENEWAL_EXTENSION',  // 正尝试延长您请求的订阅续订日期
        'RENEWAL_EXTENDED',  // 延长了特定订阅的订阅续订日期
        'PRICE_INCREASE',  // 表示系统已通知客户自动续期订阅价格上涨
        'CONSUMPTION_REQUEST',  // 用户对消耗型/自动续期订阅发起退款，Apple Store 要求提供消费数据
        'REFUND_DECLINED',  // 拒绝退款
        'REFUND_REVERSED',  // 由于争议撤销了之前通过的用户退款申请
        'EXTERNAL_PURCHASE_TOKEN',  // 苹果为您的应用创建了外部购买令牌，但未收到报告
        'TEST',  // 测试消息
    ];

    private $_data;

    public function __construct(?string $signed_payload = null)
    {
        if ($signed_payload) {
            $payload = self::parseSignedPayload($signed_payload);
            if (!$payload) {
                throw new Exception('报文解析失败');
            }
            $payload->data = new ServerNotificationData($payload->data);

            $this->_data = $payload;
        }
    }

    /**
     * 解析苹果服务端通知报文
     *
     * @link https://juejin.cn/post/7039970403770433544
     * @link https://sminrana.medium.com/app-store-server-notifications-v2-with-jwt-php-and-laravel-34b7d2e07012
     * @link https://www.toni-develops.com/2024/01/09/in-app-purchase-with-server-to-server-notifications/
     * @link https://www.apple.com/certificateauthority/
     * @link https://developer.apple.com/documentation/appstoreservernotifications/app_store_server_notifications_v2
     *
     * @param string $signed_payload
     * @return \stdClass|null
     */
    public static function parseSignedPayload(string $signed_payload)
    {
        [$header_base64encoded_str] = explode('.', $signed_payload);
        $header_json_str = base64_decode($header_base64encoded_str);

        $header = Json::decode($header_json_str);
        $algorithm = $header['alg'];
        $x5c_array = $header['x5c'];

        $certificates = array_map(function ($x5c) {
            $certificate =
                '-----BEGIN CERTIFICATE-----' . PHP_EOL
                . chunk_split($x5c, 64, PHP_EOL)
                . '-----END CERTIFICATE-----' . PHP_EOL;
            return openssl_x509_read($certificate);
        }, $x5c_array);

        // 第 2 个证书给第 1 证书验证的结果
        $verify_code_1 = openssl_x509_verify($certificates[0], $certificates[1]);
        if ($verify_code_1 !== 1) {
            Yii::error(sprintf('苹果回调报文验证证书失败：error[%s]', openssl_error_string()), __METHOD__);
            return null;
        }

        // 第 3 个证书给第 2 个证书验证的结果
        $verify_code_2 = openssl_x509_verify($certificates[1], $certificates[2]);
        if ($verify_code_2 !== 1) {
            Yii::error(sprintf('苹果回调报文验证证书失败：error[%s]', openssl_error_string()), __METHOD__);
            return null;
        }

        // 根证书验证结果
        $verify_code_3 = openssl_x509_verify($certificates[2], openssl_x509_read(Yii::$app->params['service']['apple']['apple_root_ca_g3']));
        if ($verify_code_3 !== 1) {
            Yii::error(sprintf('苹果回调报文验证证书失败：error[%s]', openssl_error_string()), __METHOD__);
            return null;
        }

        // 第 1 个证书是签署 JWS 的证书
        $pkey_object = openssl_pkey_get_public($certificates[0]);
        $pkey_array = openssl_pkey_get_details($pkey_object);
        $public_key = $pkey_array['key'];
        $decoded = JWT::decode($signed_payload, new Key($public_key, $algorithm));
        return $decoded;
    }

    public static function loadFromArray(array $data): self
    {
        $_data = new \stdClass();
        foreach ($data as $key => $value) {
            if ($key === 'data') {
                $value = new ServerNotificationData((object)$value);
            }
            $_data->$key = $value;
        }
        $obj = new self();
        $obj->_data = $_data;
        return $obj;
    }
}
