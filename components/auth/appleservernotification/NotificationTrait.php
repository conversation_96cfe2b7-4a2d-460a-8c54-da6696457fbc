<?php

namespace app\components\auth\appleservernotification;

trait NotificationTrait
{

    public function __get($name)
    {
        if (isset($this->_data->$name)) {
            return $this->_data->$name;
        }

        return null;
    }

    public function toArray(): array
    {
        $vars = get_object_vars($this->_data);
        foreach ($vars as $key => $value) {
            if (is_object($value)) {
                if (method_exists($value, 'toArray')) {
                    $vars[$key] = $value->toArray();
                } else {
                    $vars[$key] = get_object_vars($value);
                }
            }
        }
        return $vars;
    }

}
