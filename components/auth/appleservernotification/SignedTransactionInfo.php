<?php

namespace app\components\auth\appleservernotification;

use stdClass;

/**
 * Class SignedTransactionInfo
 *
 * @property-read string $transactionId 交易的唯一 ID，例：720001565067608
 * @property-read string $originalTransactionId 原始交易的 ID，例：720001565067608，在订阅时为最初订阅时的交易 ID
 * @property-read string $webOrderLineItemId 订阅购买/续期的唯一事件 ID，例：2000000078122249
 * @property-read string $bundleId App 包名，例：com.missevan.CatEarFM
 * @property-read string $productId 内购商品的 ID，例：com.missevan.CatEarFM.vip000001
 * @property-read string $subscriptionGroupIdentifier 订阅商品所属订阅组的 ID，例：21534909
 * @property-read int $purchaseDate 用户购买、恢复、续期内购商品的时间，毫秒级时间戳，例：1729510574000
 * @property-read int $originalPurchaseDate 原始交易的时间，毫秒级时间戳，例：1729510574000
 * @property-read int $expiresDate 订阅过期或重新续期的时间，例：1729514174000
 * @property-read int $quantity 消耗型商品购买的数量，例：1
 * @property-read string $type 内购商品的类型，例：Auto-Renewable Subscription
 * @property-read string $inAppOwnershipType 标识是用户自主购买，或者通过家庭共享而获得的标识，例：PURCHASED
 * @property-read int $signedDate 苹果加签成 JWS 报文的时间，毫秒级时间戳，例：1729510580907
 * @property-read int $revocationReason 苹果退款或者用户从家庭共享中被撤销的原因，例：0
 * @property-read int $revocationDate 苹果退款或者用户从家庭共享中被撤销的时间，毫秒级时间戳，例：1729558974000
 * @property-read string $environment 通知来源，Sandbox 或 Production
 * @property-read string $transactionReason 用户交易的原因，例：PURCHASE，交易可由用户发起或者系统发起
 * @property-read string $storefront 国家或区域（3 个字符），例：CHN
 * @property-read string $storefrontId 国家或区域 ID（由苹果定义），例：143465
 * @property-read int $price 在苹果后台中填写的内购商品价格乘以 1000，例：300000
 * @property-read string $currency 交易使用的币种，ISO 4217 标准的货币代码（3 个字符）
 * @property-read string $offerDiscountType 自动续期型订阅支付方式，见：https://developer.apple.com/documentation/appstoreserverapi/offerdiscounttype
 * @property-read int $offerType 订阅促销类型，见：https://developer.apple.com/documentation/appstoreserverapi/offertype
 */
class SignedTransactionInfo
{
    use NotificationTrait;

    // 内购商品类型
    const TYPE_AUTO_RENEWABLE_SUBSCRIPTION = 'Auto-Renewable Subscription';  // 自动续订型（如包月会员）
    const TYPE_NON_RENEWING_SUBSCRIPTION = 'Non-Renewing Subscription';  // 非自动续订型（如一年会员）
    const TYPE_NON_CONSUMABLE = 'Non-Consumable';  // 非消耗型（如永久装备）
    const TYPE_CONSUMABLE = 'Consumable';  // 消耗型（如钻石）

    // 优惠类型
    // https://developer.apple.com/documentation/appstoreserverapi/offertype
    const OFFER_TYPE_INTRODUCTORY = 1;  // 推介优惠
    const OFFER_TYPE_PROMOTION = 2;  // 促销优惠
    const OFFER_TYPE_CODE = 3;  // 优惠码
    const OFFER_TYPE_WIN_BACK = 4;  // 挽回客户优惠

    private $_data;

    public function __construct(stdClass $data)
    {
        $this->_data = $data;
    }

    public function isConsumableType()
    {
        return $this->type === self::TYPE_CONSUMABLE;
    }

    public function isAutoRenewableSubscriptionType()
    {
        return $this->type === self::TYPE_AUTO_RENEWABLE_SUBSCRIPTION;
    }

    public function isNonRenewingSubscriptionType()
    {
        return $this->type === self::TYPE_NON_RENEWING_SUBSCRIPTION;
    }

    public function isIntroductoryOffer()
    {
        return $this->offerType === self::OFFER_TYPE_INTRODUCTORY;
    }

    public function getPriceInFen()
    {
        return intval($this->price / 10);
    }

}
