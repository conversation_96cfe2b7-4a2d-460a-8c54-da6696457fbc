<?php

namespace app\components\auth;

use app\models\RechargeOrder;
use Yii;
use yii\helpers\Json;

/**
 * Class AuthJingDongV2
 * @package app\components\auth
 * @link https://info.missevan.com/pages/viewpage.action?pageId=109702879
 */
class AuthJingDongV2 implements AuthJingDongInterface
{
    // 订单状态
    const ORDER_STATUS_SUCCESS = '0';  // 充值成功
    const ORDER_STATUS_PROCESSING = '1';  // 充值中
    const ORDER_STATUS_FAILED = '2';  // 充值失败

    // 返回码
    const RET_CODE_SUCCESS = '100';  // 成功
    const RET_CODE_ORDER_NOT_EXISTS = '101';  // 订单不存在
    const RET_CODE_ORDER_NO_DUPLICATED = '102';  // 订单号不允许重复
    const RET_CODE_GAME_SERVER_ZONE_UNAVAILABLE = '103';  // 该游戏区服不存在或者区服已暂停
    const RET_CODE_ERROR_PARAMS = '104';  // 传入的参数有误
    const RET_CODE_ERROR_IP_ADDRESS = '105';  // IP 地址不符合要求
    const RET_CODE_ERROR_SIGN = '106';  // 验证摘要串验证失败
    const RET_CODE_GOODS_NOT_EXISTS = '107';  // 没有对应商品
    const RET_CODE_DATABASE_UNAVAILABLE = '108';  // 数据库繁忙，请稍后重试
    const RET_CODE_GOODS_OFF_THE_SHELF = '109';  // 本商品不可销售
    const RET_CODE_GOODS_SOLD_OUT = '110';  // 库存不足
    const RET_CODE_PRICE_NOT_CONSISTENT = '111';  // 商品价钱不一致
    const RET_CODE_ACCOUNT_BALANCE_NOT_ENOUGH = '112';  // 账户余额不足
    const RET_CODE_ACCOUNT_NOT_EXISTS = '113';  // 角色或账号不存在
    const RET_CODE_ACCOUNT_VALIDATION_FAILED = '114';  // 角色或账号验证失败
    const RET_CODE_GAME_BRAND_NOT_EXISTS = '115';  // 游戏品牌不存在
    const RET_CODE_SYSTEM_ERROR = '999';  // 系统错误

    // 直冲充值结果通知接口
    const GATEWAY_DIRECT_TOPUP_SYNC_ORDER_STATUS = 'https://card.jd.com/api/gameApi.action';

    // api 版本（京东不同类目 API 不同）
    const API_VERSION = 'v2';

    /**
     * @var string
     */
    private $customer_id;

    /**
     * @var string
     */
    private $private_key;

    public function __construct(array $config = [])
    {
        if (!$config) {
            $config = Yii::$app->params['service']['jingdong-v2'];
        }

        $this->customer_id = $config['customer_id'];
        $this->private_key = $config['private_key'];
    }

    public function getVersion(): string
    {
        return self::API_VERSION;
    }

    public static function formatTime(int $unix_timestamp): string
    {
        return date('YmdHis', $unix_timestamp);
    }

    public function makeSign(array $params): string
    {
        ksort($params);
        $str_array = [];
        foreach ($params as $k => $v) {
            if ($k === 'sign' || $v === '') {
                continue;
            }
            $pair_str = $k . '=' . $v;
            array_push($str_array, $pair_str);
        }

        array_push($str_array, $this->private_key);
        $joined_str = join('&', $str_array);
        return md5($joined_str, false);
    }

    public function isLegalVendorId(string $vendor_id): bool
    {
        return $this->customer_id === $vendor_id;
    }

    /**
     * 回调通知京东订单状态
     *
     * @param RechargeOrder $order
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function syncProductResult(RechargeOrder $order): void
    {
        switch ($order->status) {
            case RechargeOrder::STATUS_SUCCESS:
                $order_status = self::ORDER_STATUS_SUCCESS;
                break;
            case RechargeOrder::STATUS_CREATE:
                $order_status = self::ORDER_STATUS_PROCESSING;
                break;
            default:
                $order_status = self::ORDER_STATUS_FAILED;
        }

        $params = [
            'customerId' => $this->customer_id,
            'data' => base64_encode(Json::encode([
                'orderId' => $order->tid,
                'orderStatus' => $order_status,
            ])),
            'timestamp' => self::formatTime($_SERVER['REQUEST_TIME']),
        ];
        $params['sign'] = $this->makeSign($params);

        $resp = Yii::$app->tools->requestRemote(self::GATEWAY_DIRECT_TOPUP_SYNC_ORDER_STATUS, $params, 'POST', null, 0,
            ['Content-Type' => 'application/x-www-form-urlencoded']);
        if (self::RET_CODE_SUCCESS !== $resp['retCode']) {
            Yii::error(sprintf('京东旗舰店充值回调失败：code[%d], message[%s]', $resp['retCode'], $resp['retMessage']));
        }
    }

    public function renderResponse(string $ret_code, string $ret_message = '', string $order_status = self::ORDER_STATUS_FAILED): string
    {
        Yii::$app->response->headers->add('Content-Type', 'application/json; charset=GBK');
        return Yii::$app->response->data = iconv('UTF-8', 'GBK//TRANSLIT', Json::encode([
            'retCode' => $ret_code,
            'retMessage' => $ret_message,
            // NOTICE: 如果涉及中文需要转成 GBK
            'data' => base64_encode(Json::encode([
                'orderStatus' => $order_status,
            ])),
        ]));
    }

    public function getErrorCodeErrorAccount(): string
    {
        return self::RET_CODE_ACCOUNT_NOT_EXISTS;
    }

    public function getErrorCodeSystemError(): string
    {
        return self::RET_CODE_SYSTEM_ERROR;
    }

}
