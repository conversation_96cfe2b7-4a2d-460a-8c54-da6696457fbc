<?php
/**
 * Created by PhpStorm.
 * User: chenh
 * Date: 2019/4/3
 * Time: 16:47
 */

namespace app\components\auth;

use app\models\RechargeOrder;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use Redis;
use Yii;

class AuthPayPal
{
    const CURRENCY_USD = 'USD';
    const GATEWAY = 'https://www.paypal.com/cgi-bin/webscr';
    const GATEWAY_SANDBOX = 'https://www.sandbox.paypal.com/cgi-bin/webscr';

    const DAY_TOPUP_LIMIT = 500;
    const MONTH_TOPUP_LIMIT = 1000;

    const TOPUP_CHECK_STATUS_PASS = 0;
    const TOPUP_CHECK_STATUS_REACH_THRESHOLD = -1;
    const TOPUP_CHECK_STATUS_IN_BLACKLIST = -2;

    /**
     * 获取需要支付的费用
     *
     * @param $rmb_price
     * @param $num
     * @return array
     */
    private static function getFee($rmb_price, $num = 1)
    {
        // paypal 不支持人民币币种
        $currency = self::CURRENCY_USD;
        // 人民币换算为美元（保留两位小数：1.357 => 1.36，1.353 => 1.36）
        $price = ceil(($rmb_price / PAYPAL_USD_TO_CNY_RATE) * 100) / 100;
        // 确认税率（官方交易税费: 4.4% * 总价 + $0.3，同时提现时还有 $35 的固定费）
        // $tax = 0.046 * $subtotal + 0.32;
        // 0.046 和 0.32 是把税都转稼到用户身上的算法
        // P 商品价格, T 税费（用户要缴的税）
        // 定价 = P + T
        // paypal 扣税 = 定价 * 4.4% + 0.3 = (P + T) * 4.4% + 0.3 = P * 4.4% + T * 4.4% + 0.3 = T
        // T = (P * 4.4% + 0.3) / (1 - 95.6%) = 0.046 * P + 0.32

        // 总价（税费 + 商品总额），税费定为 0（用户不承担税费，设定最低充值限额）
        $total = $price * $num;
        return [
            'currency' => $currency,
            'total' => $total,
        ];
    }

    /**
     * 生成支付订单
     *
     * @param RechargeOrder $order
     * @param integer $origin
     * @return string
     */
    public function tradePay(RechargeOrder $order, $origin = RechargeOrder::ORIGIN_DESKTOP_WEB)
    {
        $order_id = $order->getOrderId();
        $fee = self::getFee($order->price);

        $PAYPAL_ACCOUNT = PAYPAL_ACCOUNT;
        $PAYPAL_CALLBACK = PAYPAL_CALLBACK;
        $GATEWAY = YII_ENV === 'dev' ? self::GATEWAY_SANDBOX : self::GATEWAY;

        switch ($origin) {
            // PayPal 充值后返回商家时若填写自定义协议地址（例 missevan://wallet）将跳转失败
            // 故对于 App 返回至 https://m.missevan.com/wallet/topupresult?out_trade_no=xxx&action=xxx 地址
            // 客户端解析 action 参数行为再做本地处理
            case RechargeOrder::ORIGIN_APP:
            case RechargeOrder::ORIGIN_MOBILE_WEB:
                $return_url = Yii::$app->params['domainMobileWeb']
                    . "/wallet/topupresult?out_trade_no={$order_id}&action=return";
                $cancel_return_url = Yii::$app->params['domainMobileWeb']
                    . "/wallet/topupresult?out_trade_no={$order_id}&action=cancel";
                break;
            default:
                $return_url = Yii::$app->params['domainMissevan']
                    . "/mperson/wallet/topupresult?out_trade_no={$order_id}&action=return";
                $cancel_return_url = Yii::$app->params['domainMissevan']
                    . "/mperson/wallet/topupresult?out_trade_no={$order_id}&action=cancel";
                break;
        }

        $form = <<<FORM
<form id="payform" name="payform" action="{$GATEWAY}" method="post">
    <input type="hidden" name="charset" value="utf-8">
    <input type="hidden" name="business" value="{$PAYPAL_ACCOUNT}">
    <!-- 自定义变量 paypal 原样返回 -->
    <input type="hidden" name="custom" value="{$order->price}">

    <input type="hidden" name="item_name" value="猫耳FM--购买钻石">
    <input type='hidden' name='item_number' value="{$order->num} 钻">
    <input type="hidden" name="amount" value="{$fee['total']}">
    <input type="hidden" name="currency_code" value="{$fee['currency']}">
    <input type="hidden" name="invoice" value="{$order_id}">
    <input type="hidden" name="no_shipping" value="1">

    <input type="hidden" name='cancel_return' value="{$cancel_return_url}">
    <input type="hidden" name="return" value="{$return_url}">
    <input type="hidden" name="notify_url" value="{$PAYPAL_CALLBACK}">
    <!-- 立即购买 -->
    <input type="hidden" name="cmd" value="_xclick">
    <input type="hidden" name="lc" value="en_US">
</form>
<script type="text/javascript">
(function (){
  document.payform.submit()
})()
</script>
FORM;
        return $form;
    }

    /**
     * 验证回调是否合法
     *
     * @param array $data
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function isLegalCallback(array $data)
    {
        if (PAYPAL_ACCOUNT !== $data['business']) {
            return false;
        }
        $data['cmd'] = '_notify-validate';
        try {
            $gateway = YII_ENV === 'dev' ? self::GATEWAY_SANDBOX : self::GATEWAY;

            if ($proxy = Yii::$app->params['service']['proxy']['http'] ?? null) {
                $client = new Client([
                    'proxy' => $proxy,
                ]);
            } else {
                $client = new Client();
            }
            $res = $client->request('POST', $gateway, [
                'form_params' => $data,
            ]);
            if (200 === $res->getStatusCode()) {
                $content = $res->getBody()->getContents();
            } else {
                $content = '';
            }
        } catch (ClientException $e) {
            Yii::error('PayPal 回调验证错误：' . $e->getMessage(), __METHOD__);
            $content = '';
        }
        return 'VERIFIED' === $content;
    }

    /**
     * 检查充值限制状态
     *
     * @param int $user_id
     * @return int -2 被列入黑名单，-1 充值已达最高限额，0 通过
     */
    public static function checkTopupLimit(int $user_id): int
    {
        [$is_inblacklist, $is_inwhitelist] = Yii::$app->redis->multi(Redis::PIPELINE)
            ->sIsMember(KEY_TOPUP_PAYPAL_BLACKLIST_USER_IDS, $user_id)
            ->sIsMember(KEY_TOPUP_PAYPAL_WHITELIST_USER_IDS, $user_id)
            ->exec();

        if ($is_inblacklist) {
            return self::TOPUP_CHECK_STATUS_IN_BLACKLIST;
        }

        if ($is_inwhitelist) {
            return self::TOPUP_CHECK_STATUS_PASS;
        }

        $topup_sum = RechargeOrder::getTodayAndCurrentMonthTopupSum($user_id, RechargeOrder::TYPE_PAYPAL);
        if ($topup_sum['day_topup'] >= self::DAY_TOPUP_LIMIT) {
            return self::TOPUP_CHECK_STATUS_REACH_THRESHOLD;
        } elseif ($topup_sum['month_topup'] >= self::MONTH_TOPUP_LIMIT) {
            return self::TOPUP_CHECK_STATUS_REACH_THRESHOLD;
        }

        return self::TOPUP_CHECK_STATUS_PASS;
    }

    /**
     * 判断是否为白名单用户
     *
     * @param int $user_id
     * @return bool
     */
    public static function inWhiteList(int $user_id): bool
    {
        return Yii::$app->redis->sIsMember(KEY_PAYPAL_WHITELIST_USER_IDS, $user_id);
    }
}
