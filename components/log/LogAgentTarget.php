<?php
/**
 * LogAgent 日志收集
 *
 * <AUTHOR>
 */

namespace app\components\log;

use Yii;
use yii\base\InvalidRouteException;
use yii\log\Logger;
use yii\helpers\VarDumper;
use yii\helpers\Json;
use yii\web\HttpException;
use index0h\log\LogstashTarget;

class LogAgentTarget extends LogstashTarget
{
    const DEFAULT_VERSION = '1';

    /** @var string App ID */
    public $app_id;
    /** @var string Hostname */
    public $host;
    /** @var string Instance ID */
    public $instance_id;

    /**
     * Returns the text display of the specified level.
     * @param int $level the message level, e.g. [[LEVEL_ERROR]], [[LEVEL_WARNING]].
     * @return string the text display of the level
     */
    public static function getLevelName($level)
    {
        // we accept upper case level only as LevelName is lower case in Yii
        // $level = Logger::getLevelName($level);
        static $levels = [
            Logger::LEVEL_ERROR => 'ERROR',
            Logger::LEVEL_WARNING => 'WARN',
            Logger::LEVEL_INFO => 'INFO',
            Logger::LEVEL_TRACE => 'DEBUG',
            Logger::LEVEL_PROFILE_BEGIN => 'DEBUG',
            Logger::LEVEL_PROFILE_END => 'DEBUG',
            Logger::LEVEL_PROFILE => 'DEBUG',
        ];

        return isset($levels[$level]) ? $levels[$level] : 'UNKNOWN';
    }

    /**
     * Formats a log message for display as a string.
     * @param array $message the log message to be formatted.
     * The message structure follows that in [[Logger::messages]].
     * @return string the formatted message
     */
    public function formatMessage($message)
    {
        list($text, $level, $category, $timestamp) = $message;
        $level = self::getLevelName($level);
        if (!is_string($text)) {
            // exceptions may not be serializable if in the call stack somewhere is a Closure
            if ($text instanceof \Throwable || $text instanceof \Exception) {
                $text = (string)$text;
            } else {
                $text = VarDumper::export($text);
            }
        }
        $traces = [];
        if (isset($message[4])) {
            foreach ($message[4] as $trace) {
                $traces[] = "in {$trace['file']}:{$trace['line']}";
            }
        }
        $prefix = $this->getMessagePrefix($message);
        return Json::encode([
            '@timestamp' => date('c', $timestamp),
            '@version' => self::DEFAULT_VERSION,
            'app_id' => $this->app_id,
            'host' => $this->host,
            'instance_id' => $this->instance_id,
            'level' => $level,
            'category' => $category,
            'message' => ($prefix ? $prefix . ' ' : '') . $text
                . (empty($traces) ? '' : "\n    " . implode("\n    ", $traces)),
        ]);
    }
}
