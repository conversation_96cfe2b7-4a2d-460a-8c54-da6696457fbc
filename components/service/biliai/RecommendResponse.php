<?php

namespace app\components\service\biliai;

class RecommendResponse
{
    private $_items;
    private $_user_feature;

    public function __construct(array $items, string $user_feature)
    {
        $this->_items = array_map(function ($item) {
            return new RecommendItem($item);
        }, $items);
        $this->_user_feature = $user_feature;
    }

    public function getUserFeature()
    {
        return $this->_user_feature;
    }

    public function getItems()
    {
        return $this->_items;
    }

}
