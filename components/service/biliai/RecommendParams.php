<?php

namespace app\components\service\biliai;

use app\components\util\Equipment;
use app\components\web\User;
use app\models\UserAddendum;
use Yii;
use yii\web\Request;

class RecommendParams
{
    /**
     * @var Equipment
     */
    private $_equip;

    /**
     * @var Request
     */
    private $_request;

    /**
     * @var User
     */
    private $_user;

    /**
     * @var int
     */
    private $_persona;

    /**
     * @var int
     */
    public $request_cnt = self::DEFAULT_REQUEST_NUM;

    /**
     * @var int
     */
    private $_network;
    /**
     * @var int
     */
    public $_refresh_type;
    /**
     * @var int
     */
    private $_refresh_num;

    const DEFAULT_REQUEST_NUM = 6;

    // 客户端上报的网络类型（同埋点上报参数约定值）
    const NETWORK_UNKNOWN = 0;
    const NETWORK_WIFI = 1;
    const NETWORK_CELLULAR = 2;
    const NETWORK_OFFLINE = 3;
    const NETWORK_OTHERNET = 4;

    // 刷新方式
    const REFRESH_TYPE_DEFAULT = 0;  // 旧版 App 默认值、或冷启动
    const REFRESH_TYPE_DRAG_DOWN = 1;  // 下拉刷新
    const REFRESH_TYPE_CLICK_CHANGE = 2;  // 点击换一批

    public function __construct(Request $request, Equipment $equip, ?User $user, int $persona, int $network, int $refresh_type, int $refresh_num, ?int $request_cnt = null)
    {
        $this->_request = $request;
        $this->_equip = $equip;
        $this->_user = $user;
        $this->_persona = $persona;

        $this->_network = $network;
        $this->_refresh_type = $refresh_type;
        $this->_refresh_num = $refresh_num;

        if ($request_cnt > 0) {
            $this->request_cnt = $request_cnt;
        }
    }

    public function getRequest()
    {
        return $this->_request;
    }

    public function getEquip()
    {
        return $this->_equip;
    }

    public function getUser()
    {
        return $this->_user;
    }

    public function getLocationInfo()
    {
        $ip = $this->_request->getUserIP();
        if (!$location_info = Yii::$app->serviceRpc->getLocationInfo($ip)) {
            $location_info = ['country_name' => '', 'country_code' => '', 'region_name' => '', 'city_name' => '', 'isp' => ''];
        }

        return $location_info;
    }

    public function getPersona()
    {
        return $this->_persona ?: TianMa::UNKNOWN_INT_VALUE;
    }

    public function getChid()
    {
        if ($this->getEquip()->isIOS()) {
            return TianMa::UNKNOWN_STRING_VALUE;
        }

        return $this->getEquip()->getChannel() ?: TianMa::UNKNOWN_STRING_VALUE;
    }

    public function getPlatform()
    {
        if ($this->getEquip()->isIOS()) {
            return TianMa::PLATFORM_IOS;
        }
        return TianMa::PLATFORM_ANDROID;
    }

    public function getFirstLoginTime()
    {
        return $this->getUser()->getRegisterAt() ?: TianMa::UNKNOWN_INT_VALUE;
    }

    public function getNetwork()
    {
        switch ($this->_network) {
            case self::NETWORK_WIFI:
                return TianMa::NETWORK_WIFI;
            case self::NETWORK_CELLULAR:
                return TianMa::NETWORK_CELLULAR;
            case self::NETWORK_OTHERNET:
                return TianMa::NETWORK_OTHERNET;
            case self::NETWORK_UNKNOWN:
            case self::NETWORK_OFFLINE:
                return TianMa::UNKNOWN_STRING_VALUE;
        }

        return TianMa::UNKNOWN_STRING_VALUE;
    }

    public function getFreshType()
    {
        return $this->_refresh_type;
    }

    public function getDisplayId()
    {
        return $this->_refresh_num ?: TianMa::UNKNOWN_INT_VALUE;
    }

    public function getMid()
    {
        return $this->getUser()->getId() ?: TianMa::UNKNOWN_INT_VALUE;
    }

    public function getBuvid()
    {
        return $this->getEquip()->getBuvid() ?: TianMa::UNKNOWN_STRING_VALUE;
    }

    public function getModel()
    {
        return $this->getEquip()->getPhone();
    }

    public function getVersion()
    {
        return $this->getEquip()->getAppVersion();
    }

    public function getSex()
    {
        switch ($this->_user->sex) {
            case UserAddendum::FEMALE:
                return TianMa::SEX_FEMALE;
            case UserAddendum::MALE:
                return TianMa::SEX_MALE;
        }

        return TianMa::UNKNOWN_INT_VALUE;
    }

}
