<?php

namespace app\components\service\biliai;

use Yii;
use yii\helpers\Json;

class TianMa
{
    const PROD_HOST = 'http://data.bilibili.co';
    const PRE_HOST = 'http://pre-data.bilibili.co';

    // https://info.bilibili.co/pages/viewpage.action?pageId=936796623
    const URL_RECOMMAND = '/recommand';

    // https://info.bilibili.co/pages/viewpage.action?pageId=827615706
    // https://info.missevan.com/pages/viewpage.action?pageId=126162784
    const CODE_OK = 0;  // 正常响应
    const CODE_BS_FALLBACK = 600;  // 触发天马推荐 bs 服务兜底
    const CODE_ROUTER_FALLBACK = 601;  // 触发天马推荐 router 服务兜底
    const CODE_ITEM_REQ_CNT_NOT_ENOUGH = -3;  // 返回 item 数量不足 req_cnt
    const CODE_NO_RESULT = -77;  // 不返回推荐结果
    const CODE_BAD_REQUEST = 400;  // 错误请求

    // 业务场景
    const SCENE_HOMEPAGE_RECOMMENDS = 'maoer_home';  // 首页猜你喜欢
    const SCENE_LIVE_HOMEPAGE = 'maoer_live';  // 直播首页

    // 用户性别
    const SEX_FEMALE = 0;
    const SEX_MALE = 1;

    // 平台
    const PLATFORM_IOS = 'ios';
    const PLATFORM_ANDROID = 'android';

    // 网络类型
    const NETWORK_WIFI = 'WIFI';
    const NETWORK_CELLULAR = 'CELLULAR';
    const NETWORK_OTHERNET = 'OTHERNET';

    // 下发的卡片类型
    const GOTO_SOUND = 'sound';
    const GOTO_DRAMA = 'drama';
    const GOTO_LIVE = 'live';

    // 整型、字符串类型的默认值
    const UNKNOWN_INT_VALUE = -1;
    const UNKNOWN_STRING_VALUE = '';

    public static function getHost()
    {
        if (getenv('DEPLOY_ENV', true) === 'prod') {
            return self::PROD_HOST;
        }

        // 推荐算法没有 UAT 接口，预发的数据不参与训练，不会影响算法模型
        return self::PRE_HOST;
    }

    /**
     * @link slb 内网接口，目前没有鉴权
     *
     * @param RecommendParams $params
     * @return RecommendResponse|null
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function getHomepageRecommends(RecommendParams $params): ?RecommendResponse
    {
        try {
            $location_info = $params->getLocationInfo();
            $status_code = 200;
            $result = Yii::$app->tools->requestRemote(self::getHost() . self::URL_RECOMMAND, [
                'cmd' => self::SCENE_HOMEPAGE_RECOMMENDS,
                'mid' => $params->getMid(),
                'buvid' => $params->getBuvid(),
                'request_cnt' => $params->request_cnt,
                'timeout' => 15000,  // 上游等待 ai 结果的时间，单位 ms
                'display_id' => $params->getDisplayId(),
                'fresh_type' => $params->getFreshType(),
                'chid' => $params->getChid(),
                'model' => $params->getModel(),
                'platform' => $params->getPlatform(),
                'version' => $params->getVersion(),
                'network' => $params->getNetwork(),
                'country' => $location_info['country_name'],
                'province' => $location_info['region_name'],
                'city' => $location_info['city_name'],
                'sex' => $params->getSex(),
                'persona' => $params->getPersona(),
                'first_logintime' => $params->getFirstLoginTime(),
                'ts' => $_SERVER['REQUEST_TIME'],
            ], 'GET', null, 0, [], $status_code);
            if ($status_code === 200 && $result && in_array($result['code'], [self::CODE_OK, self::CODE_BS_FALLBACK, self::CODE_ROUTER_FALLBACK, self::CODE_ITEM_REQ_CNT_NOT_ENOUGH, self::CODE_NO_RESULT])) {
                $items = [];
                if (array_key_exists('data', $result) && is_array($result['data'])) {
                    $items = $result['data'];
                }
                return new RecommendResponse($items, $result['user_feature']);
            } else {
                Yii::error(sprintf('获取猜你喜欢推荐下发音频失败：body[%s]', Json::encode($result)), __METHOD__);
            }
        } catch (\Exception $e) {
            Yii::error(sprintf('获取猜你喜欢推荐下发音频失败：error[%s]', $e->getMessage()), __METHOD__);
        }
        return null;
    }

}

