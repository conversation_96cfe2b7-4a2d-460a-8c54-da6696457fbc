<?php

namespace app\components\service\biliai;

class RecommendItem
{
    private $_id;
    private $_goto;
    private $_source;
    private $_trackid;
    private $_av_feature;

    public function __construct(array $body_item)
    {
        $this->_id = $body_item['id'];
        $this->_goto = $body_item['goto'];
        $this->_source = $body_item['source'];
        $this->_trackid = $body_item['trackid'];
        $this->_av_feature = $body_item['av_feature'];
    }

    /**
     * @return int
     */
    public function getId()
    {
        return $this->_id;
    }

    /**
     * @return string
     */
    public function getGoto()
    {
        return $this->_goto;
    }

    /**
     * @return string
     */
    public function getSource()
    {
        return $this->_source;
    }

    /**
     * @return string
     */
    public function getTrackId()
    {
        return $this->_trackid;
    }

    /**
     * @return string
     */
    public function getAvFeature()
    {
        return $this->_av_feature;
    }

    public function isSoundElementType()
    {
        return $this->getGoto() === TianMa::GOTO_SOUND;
    }

}
