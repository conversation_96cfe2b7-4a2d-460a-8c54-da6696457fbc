<?php

namespace app\components\service\bililargepay;

use app\models\RechargeOrder;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use yii\helpers\Json;
use Yii;

class BiliLargePayClient
{
    const GATEWAY_PROD = 'http://pay.bilibili.co';
    const GATEWAY_UAT = 'http://uat-pay.bilibili.co';

    const API_CREATE_ORDER = '/payplatform/pay/largePay';
    const API_QUERY_ORDER = '/payplatform/pay/query';

    // 支付设备渠道类型
    const DEVICE_TYPE_PC = 1;
    const DEVICE_TYPE_H5 = 2;
    const DEVICE_TYPE_APP = 3;
    const DEVICE_TYPE_PUBLIC_ACCOUNT = 4;
    const DEVICE_TYPE_PAYMENT_DEDUCTION = 5;
    const DEVICE_TYPE_WECHAT_MINI_PROGRAM = 6;
    const DEVICE_TYPE_JUHE_QCODE = 7;

    // 设备
    const DEVICE_WEB = 'WEB';
    const DEVICE_H5 = 'WAP';
    const DEVICE_IOS = 'IOS';
    const DEVICE_ANDROID = 'ANDROID';

    // 支付渠道 id，由支付平台分配：银行转账
    const PAY_CHANNEL_ID_BANK_TRANSFER = 10100;

    // 分配的支付场景值：大额支付
    const SERVICE_TYPE_LARGE_PAY = 40;

    // 货币类型
    const FEE_TYPE_CNY = 'CNY';

    // 回调响应
    const CALLBACK_RESP_SUCCESS = 'SUCCESS';  // 表示成功
    const CALLBACK_RESP_FAIL = 'FAIL';  // 表示失败，立即重试
    const CALLBACK_RESP_REPUBLISH = 'REPUBLISH';  // 表示稍后重试

    // 充值成功、失败消息模板
    const TOPUP_SUCCESS_NOTICE_MESSAGE_TPL = '亲爱的用户，您于 %s 发起的专属充值的 %d 钻石已到账，请注意查收。如有疑问，请联系您的专属充值客服。';
    const TOPUP_FAIL_NOTICE_MESSAGE_TPL = '亲爱的用户，您于 %s 发起的专属充值未查询到银行转账数据。建议您检查您的银行转账是否添加了我们的特殊备注，未备注的转账，将无法充值成功。<br/>如有任何疑问，请您联系您的专属充值客服。<br/>给您带来不便深感抱歉~';

    /**
     * 支付单状态查询接口
     * @link https://info.bilibili.co/pages/viewpage.action?pageId=102957977
     *
     * @param array $condition 例 ['txIds' => '3550233321537831217'] 或 ['orderIds' => '16606227890130000277155']
     * @return null
     * @throws \Exception
     */
    public function queryOrders(array $condition)
    {
        if (!array_key_exists('txIds', $condition) && !array_key_exists('orderIds', $condition)) {
            throw new Exception('缺少 txIds 或 orderIds 参数');
        }

        $condition['traceId'] = $this->getTraceId();
        return $this->request(self::API_QUERY_ORDER, $condition);
    }

    public function queryOrdersByOutTradeNo(string $out_trade_no)
    {
        return $this->queryOrders(['orderIds' => $out_trade_no]);
    }

    public function queryOrdersByTxnId(string $txn_id)
    {
        return $this->queryOrders(['txIds' => $txn_id]);
    }

    /**
     * 签名
     * @link https://info.bilibili.co/pages/viewpage.action?pageId=102957956
     *
     * @param array $params
     * @return string
     */
    public function makeSign(array $params)
    {
        ksort($params);
        $str_to_sign = '';
        foreach ($params as $k => $v) {
            if ($k === 'sign') {
                continue;
            }
            $str_to_sign .= "{$k}={$v}&";
        }
        return strtolower(md5($str_to_sign . 'token=' . BILIBILI_LARGE_PAY_TOKEN));
    }

    /**
     * 是否为合法签名
     * @param BiliLargePayCallbackMessage $msg
     * @return bool
     */
    public function isValidSign(BiliLargePayCallbackMessage $msg)
    {
        return $this->makeSign($msg->getBody()) === $msg->getSign();
    }

    /**
     * 大额充值下单接口
     * @link https://info.bilibili.co/pages/viewpage.action?pageId=253002356
     *
     * @param RechargeOrder $topup_order
     * @param string $username
     * @param string $device
     * @param int $device_type
     * @return array
     * @throws \Exception
     */
    public function createOrder(RechargeOrder $topup_order, string $username, string $device, int $device_type)
    {
        return $this->request(self::API_CREATE_ORDER, [
            'uid' => $topup_order->uid,
            'orderId' => $topup_order->getOrderId(),
            'orderCreateTime' => $topup_order->create_time * 1000,
            'feeType' => self::FEE_TYPE_CNY,
            'deviceType' => $device_type,
            'device' => $device,
            'notifyUrl' => BILIBILI_LARGE_PAY_NOTIFY_URL,
            'productId' => $topup_order->cid,
            'showTitle' => '支付交易记录',
            'payChannelId' => self::PAY_CHANNEL_ID_BANK_TRANSFER,
            'nickName' => $username,
        ]);
    }

    /**
     * 生成支付跳转地址
     * @param string $pay_channel_url
     * @param string $pay_channel_param
     * @return string
     */
    public function getRedirectUrl(string $pay_channel_url, string $pay_channel_param)
    {
        $tmp = '?';
        if (parse_url($pay_channel_url, PHP_URL_QUERY)) {
            $tmp = '&';
        }

        // WORKAROUND: 添加 2 次 urlencode 避免由于B站支付前端进行跳转时自动添加 pay-v2 的路由前缀，若B站支付前端解决了，可去除此兼容
        return $pay_channel_url . $tmp . 'payChannelParam=' . urlencode(urlencode($pay_channel_param));
    }

    private function getTraceId()
    {
        return 'trace_id_' . $_SERVER['REQUEST_TIME'] . '_' . uniqid();
    }

    private function request(string $uri, array $params)
    {
        try {
            $params['customerId'] = BILIBILI_LARGE_PAY_CUSTOMER_ID;
            $params['timestamp'] = $_SERVER['REQUEST_TIME'] * 1000;
            $params['version'] = '1.0';
            $params['signType'] = 'MD5';
            $params['sign'] = $this->makeSign($params);

            $url = (YII_ENV_PROD ? self::GATEWAY_PROD : self::GATEWAY_UAT) . $uri;
            $content = Yii::$app->tools->requestRemote($url, [], 'POST', Json::encode($params), 0, [
                'Content-Type' => 'application/json',
            ]);
            if (0 !== $content['errno']) {
                Yii::error(sprintf(
                    'B站大额充值错误：trace_id[%s], code[%d], msg[%s]',
                    $content['data']['traceId'] ?? '',
                    $content['errno'],
                    $content['showMsg'] . '|' . $content['msg']
                ), __METHOD__);
                throw new Exception($content['showMsg'] ?? '服务器出差去了，请稍后重试');
            }
            return $content['data'];
        } catch (Exception $e) {
            Yii::error(sprintf('B站大额充值错误: %s', $e->getMessage()), __METHOD__);
            throw $e;
        }
    }

}
