<?php

namespace app\components\service\bililargepay;

use yii\helpers\Json;

class BiliLargePayCallbackMessage
{
    /**
     * @var array
     */
    private $_body;

    public function __construct(string $msg_content)
    {
        $this->_body = Json::decode($msg_content);
    }

    public function getBody(): array
    {
        return $this->_body;
    }

    /**
     * @return string
     */
    public function getTraceId(): string
    {
        return $this->_body['traceId'];
    }

    /**
     * @return int
     */
    public function getCustomerId(): int
    {
        return (int)$this->_body['customerId'];
    }

    /**
     * @return int
     */
    public function getServiceType(): int
    {
        return (int)$this->_body['serviceType'];
    }

    /**
     * @return string
     */
    public function getPayStatus(): string
    {
        return $this->_body['payStatus'];
    }

    /**
     * @return int
     */
    public function getPayChannelId(): int
    {
        return $this->_body['payChannelId'];
    }

    /**
     * @return string
     */
    public function getFeeType(): string
    {
        return $this->_body['feeType'] ?? BiliLargePayClient::FEE_TYPE_CNY;
    }

    /**
     * @return string
     */
    public function getOrderId(): string
    {
        return $this->_body['orderId'];
    }

    /**
     * @return int
     */
    public function getPayAmountInFen(): int
    {
        return $this->_body['payAmount'];
    }

    /**
     * @return string
     */
    public function getTransactionId(): string
    {
        return $this->_body['txId'];
    }

    /**
     * @return string
     */
    public function getSign(): string
    {
        return $this->_body['sign'];
    }

    public function isValidPayChannelId(): bool
    {
        return $this->getPayChannelId() === BiliLargePayClient::PAY_CHANNEL_ID_BANK_TRANSFER;
    }

    public function isValidFeeType(): bool
    {
        return $this->getFeeType() === BiliLargePayClient::FEE_TYPE_CNY;
    }

    public function isValidCustomerId(): bool
    {
        return $this->getCustomerId() === BILIBILI_LARGE_PAY_CUSTOMER_ID;
    }

    public function isValidServiceType(): bool
    {
        return $this->getServiceType() === BiliLargePayClient::SERVICE_TYPE_LARGE_PAY;
    }
}
