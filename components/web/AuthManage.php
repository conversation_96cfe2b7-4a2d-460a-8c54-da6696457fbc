<?php

namespace app\components\web;

use Yii;
use yii\base\BaseObject;
use yii\db\Query;
use yii\di\Instance;
use yii\rbac\DbManager;

class AuthManage extends DbManager
{
    const LIVE_OPERATOR = 'liveoperator';
    const LIVE_ADMIN = 'liveadmin';
    const AUDIT_LIVE = 'audit_live';  // 审核 - 直播组

    public function init()
    {
        // 由于父类里强制要求使用 yii\db\Connection，而这里不做要求
        // 所以可直接使用 Yii::$app->getDb()
        // parent::init();
        // $this->db = Instance::ensure($this->db, Connection::className());
        // 防止祖父类 init 方法更新，调用祖父类的 init 方法
        BaseObject::init();
        $this->db = Yii::$app->getDb();
        if ($this->cache !== null) {
            $this->cache = Instance::ensure($this->cache, 'yii\caching\CacheInterface');
        }
    }

    /**
     * 判断用户角色（包含参数里任意一个角色就会返回 true）
     *
     * @param int $user_id 用户 ID
     * @param string ...$role_name 用户角色
     * @return bool
     */
    public function isRoleByUser(int $user_id, string ...$role_name)
    {
        if ($user_id <= 0 || empty($role_name)) {
            return false;
        }
        $row = (new Query())->from($this->assignmentTable)
            ->where(['userid' => (string)$user_id, 'itemname' => $role_name])
            ->exists($this->db);
        return $row;
    }
}
