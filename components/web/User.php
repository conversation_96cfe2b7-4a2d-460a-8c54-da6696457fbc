<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/13
 * Time: 16:15
 */

namespace app\components\web;

use app\components\util\Equipment;
use app\models\Blacklist;
use app\models\UserAddendum;
use Yii;
use yii\base\Component;
use yii\web\HttpException;
use yii\web\IdentityInterface;
use app\models\Mowangskuser;
use app\models\User as Identity;
use app\models\Token;

/**
 * Class User
 * @package app\components\web
 *
 * @property-read int|null $id
 * @property-read bool $isGuest
 * @property-read \app\models\User|null $identity
 * @property-read int|null $loginAt
 * @property-read string|null $name
 * @property-read false $isExam
 * @property-read int|null $sex
 * @property-read bool $isBindMobile
 * @property-read bool $isLimited
 */
class User extends Component
{
    public $identityClass = 'app\models\Mowangskuser';

    private $_identity = false;
    private $_isExam = NULL;
    private $_isLimited = NULL;
    private $_isBindMobile = NULL;
    /**
     * @var null|UserAddendum
     */
    private $_addendum = NULL;

    public $access_checker;
    private $_access = [];

    public function init()
    {
        parent::init();
    }

    public function getIsGuest()
    {
        return $this->getIdentity() === NULL;
    }

    public function login(IdentityInterface $identity)
    {
        $equip = Yii::$app->equip;
        if ($equip->isFromApp()) {
            $identity->renewal();
            return $identity;
        } else {
            throw new HttpException(400, '非法登录请求');
        }
    }

    public function logout()
    {
        $identity = $this->getIdentity();
        if ($identity !== null) {
            $identity->Logout();
            $this->setIdentity(NULL);
        }
        return $this->getIsGuest();
    }

    public function update($user)
    {
        $identity = $this->getIdentity();
        if ($identity) {
            $dirty_attributes = $user->getDirtyAttributes();
            foreach ($dirty_attributes as $key => $value) {
                if (isset($identity[$key])) {
                    $identity[$key] = $value;
                }
            }
            $status = Yii::$app->memcache->exists(Pre_Token . $identity->token);

            if ($status) {
                // 用户更新信息后，需重置缓存值
                $identity->renewal();
                return $identity;
            } else {
                throw new HttpException(400, '非法登录请求');
            }
        }
    }

    public function setIdentity($identity)
    {
        $this->_identity = $identity;
    }

    /**
     * @return \app\models\User|null;
     */
    public function getIdentity()
    {
        if ($this->_identity === false) {
            $this->renewAuthStatus();
        }
        return $this->_identity;
    }

    public function getId()
    {
        $identity = $this->getIdentity();
        return $identity ? $identity->getId() : NULL;
    }

    /**
     * 获取用户登录时间
     * 快捷调用：Yii::$app->user->loginAt
     *
     * @return null
     */
    public function getLoginAt()
    {
        $identity = $this->getIdentity();
        return $identity->login_at ?: null;
    }

    /**
     * 获取用户注册时间
     * Yii::$app->user->registerAt
     *
     * @return integer
     */
    public function getRegisterAt()
    {
        $identity = $this->getIdentity();
        return $identity ? (int)$identity->ctime : 0;
    }

    public function getName()
    {
        $identity = $this->getIdentity();
        return $identity ? $identity->username : NULL;
    }

    public function getIsExam()
    {
        // WORKAROUND: 由于没有开放答题功能，此处暂时直接返回 false
        return false;
        if ($this->_isExam === NULL) {
            if ($this->id
                    && Mowangskuser::find()->where(['id' => $this->id])->andWhere(['&', 'confirm', 1])->exists()) {
                $this->_isExam = true;
            } else {
                $this->_isExam = false;
            }
        }
        return $this->_isExam;
    }

    public function getSex()
    {
        if ($this->id) {
            if ($this->_addendum === NULL) {
                $this->_addendum = UserAddendum::getByPk($this->id);
            }
            return $this->_addendum->sex;
        }

        return null;
    }

    /**
     * 获取用户账号是否已绑定手机号
     *
     * @return boolean 返回 true 表示已绑定，false 表示未绑定
     */
    public function getIsBindMobile(): bool
    {
        if ($this->_isBindMobile === NULL) {
            if ($this->id) {
                $this->_isBindMobile = Yii::$app->sso->getUserAccount($this->id)['mobile'] ? true : false;
            } else {
                $this->_isBindMobile = false;
            }
        }
        return $this->_isBindMobile;
    }

    public function getIsLimited()
    {
        if ($this->_isLimited === NULL) {
            if (!$this->id || $this->id >= Mowangskuser::USER_ID_LIMIT || !$this->isExam) {
                $this->_isLimited = true;
            } else {
                $this->_isLimited = false;
            }
        }
        return $this->_isLimited;
    }

    /**
     * 新 Token 登录状态的检查逻辑：
     * 1. 如果是从 SSO 或 Token 表拿出的数据，则通过 renewal() 新建缓存
     * 2. 如果是从缓存拿出的数据，且缓存的生命周期最多剩余 10 分钟，则通过 renewal() 增加缓存存活时间
     * 3. 如果 Token 与缓存均失效，用户需重新登录
     *
     * 备注：新 Token 为 App#549 (2018.09.06) 后进行过登录的 Token
     */
    protected function renewAuthStatus()
    {
        // WORKAROUND: 针对 iOS 4.2.8 及其以上、安卓 5.1.8 及其以上，获取 Cookie 中的 token
        // 接口 sound/add-play-log, sound/add-play-times
        // 由于线上 iOS 老版本（4.2.7 及其以下）header 中未含 token，进行兼容处理
        // (Android 5.1.5 及其之后调用 sound/add-play-log 且 cookie 中已包含 token)
        $headers = Yii::$app->request->headers;
        $pathinfo_array = [
            'sound/add-play-log',
            'sound/add-play-times',
        ];
        if (!Equipment::isAppOlderThan('4.2.8', '5.1.8') || in_array(Yii::$app->request->pathInfo, $pathinfo_array)) {
            $token = $_COOKIE['token'] ?? null;
        } else {
            $token = $headers['token'] ?? null;
        }

        $identity = null;
        if ($token && $identity = Identity::findIdentity($token)) {

            // 若非从缓存中获取的用户信息，则构建缓存
            if ($identity->from !== Identity::FROM_CACHE) {
                $identity->renewal();
            }
        }
        $this->setIdentity($identity);
    }

    public function loginRequired()
    {
        throw new HttpException(401, Yii::t('yii', 'Login Required'), 100010006);
    }

    protected function getAuthManager()
    {
        return Yii::$app->getAuthManager();
    }

    protected function getAccessChecker()
    {
        return $this->access_checker !== null ? $this->access_checker : $this->getAuthManager();
    }

    public function can($permission_name, $params = [], $allow_caching = true)
    {
        if ($allow_caching && empty($params) && isset($this->_access[$permission_name])) {
            return $this->_access[$permission_name];
        }
        if (($access_checker = $this->getAccessChecker()) === null) {
            return false;
        }
        $access = $access_checker->checkAccess($this->getId(), $permission_name, $params);
        if ($allow_caching && empty($params)) {
            $this->_access[$permission_name] = $access;
        }

        return $access;
    }

    /**
     * 判断是否为黑名单用户
     *
     * @param int $type 可选，指定封禁类型（Blacklist::BAN_TIME_*）
     * @return bool 是否为黑名单用户
     * @throws \Exception
     */
    public function inBlacklist(int $type = Blacklist::BAN_TIME_ANY): bool
    {
        // 若用户未登录，视为非黑名单用户
        if (!$this->id) {
            return false;
        }
        return Blacklist::model()->hasUser($this->id, $type);
    }
}
