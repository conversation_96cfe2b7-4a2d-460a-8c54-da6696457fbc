<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yii message/extract' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE: this file must be saved in UTF-8 encoding.
 */
return [
    'Send successfully' => '发送成功',
    '[MissEvan] Verification Code' => '【猫耳FM】验证码',
    'You are registering an account' => '您正在注册账号',
    'You are binding a new phone number' => '您正在绑定手机号',
    'You are binding a new email address' => '您的账号正在绑定邮箱',
    'You are changing your password' => '您的账号正在修改密码',
    'You are binding the new phone number' => '您的账号正在进行绑定手机号的操作',
    'You are changing the bound email address' => '您的账号正在进行换绑邮箱的操作',
    'You are trying to reset your password' => '您的账号正在进行找回密码的操作',
    'Registration is successful' => '注册成功',
    'Sign out successfully' => '登出成功',
    'Change successfully' => '修改成功',
    'Verified' => '验证通过',
    'Comment succeeded' => '评论成功',
    'Cancel successfully' => '取消成功',
    'Successfully deleted' => '删除成功',
    'Send danmaku successfully' => '弹幕添加成功',
    'Like successfully' => '点赞成功',
    'Dislike successfully' => '点踩成功',
    'Like has been canceled' => '点赞已经取消了',
    'Report successfully' => '举报成功',
    'Reply successfully' => '回复成功',
    'Reward the drama--{drama_title}' => '打赏剧集--{drama_title}',
    'Purchase the drama--{drama_title}' => '剧集观赏券--{drama_title}',
    'Redeem the drama--{drama_title}' => '剧集兑换--{drama_title}',
    'Purchase the drama--{drama_title}--episode x{episode_num}' => '剧集观赏券--{drama_title}--单期购买x{episode_num}',
    'Purchase successfully' => '购买成功',
    'Reward successfully' => '打赏成功',
    'Reward message sent successfully' => '留言成功',
    'Reward with no message' => 'TA 傲娇地打赏了本剧…',
    'Drama reward agreement' => '用户须知',
];
