<?php

namespace tests\unit\components\util;

use app\components\util\Equipment;
use tests\components\UnitTestCase;
use Yii;
use yii\web\HttpException;

class EquipmentTest extends UnitTestCase
{
    const TEST_EQUIP_ID = '66d31e4c-5d31-4a6c-9261-c862fd5cf127';
    const TEST_EQUIP_ID_2 = '6ae93485-test-b665-9ada-a26e4dda4244';

    const TEST_DEVICE_TOKEN_EQUIP_ID = '6ae93485-test-b665-9ada-a26e4dda4245';
    const TEST_DEVICE_TOKEN = 'v1|1S5D8oIgiaDNvVIJm2PpkoxYWSUOuBqIMWMuVJcfpW1M0n7jdNAmgxv3pnocRI9oB';
    const TEST_DEVICE_TOKEN_ACTIVATE_TIME = 1712678400;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        Yii::$app->redis->sRem(KEY_BLACK_LIST_EQUIP_ID, self::TEST_EQUIP_ID);
    }

    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
        Yii::$app->redis->sRem(KEY_BLACK_LIST_EQUIP_ID, self::TEST_EQUIP_ID);
        self::setPrivateProperty(Yii::$app->equip, 'equip_id', parent::TEST_EQUIP_ID);
    }

    public function testIsBanVersion()
    {
        // 测试是否为 MiMi 禁止使用版本
        $this->assertFalse(Equipment::isBanVersion(true));

        // 测试为 iOS 禁止使用版本（4.6.5 之前版本）
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.6.3 (iOS;12.0;iPhone9,1)']);
        $this->assertTrue(Equipment::isBanVersion());

        // 测试不为 iOS 8 禁止使用版本（iOS 8 4.5.5 版本）
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.5.5 (iOS;8.0;iPhone9,1)']);
        $this->assertFalse(Equipment::isBanVersion());

        // 测试为 iOS 不禁止使用版本
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.6.5 (iOS;12.0;iPhone9,1)']);
        $this->assertFalse(Equipment::isBanVersion());

        // 测试为 Android 禁止使用版本（5.4.9 之前版本）
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/5.4.8 (Android;7.0;Meizu M6 M6)']);
        $this->assertTrue(Equipment::isBanVersion());

        // 测试为 Android 不禁止使用版本
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/5.4.9 (Android;7.0;Meizu M6 M6)']);
        $this->assertFalse(Equipment::isBanVersion());
    }

    public function testCheckEquipID()
    {
        $equip = Yii::$app->equip;

        // 错误的 equip_id
        self::setPrivateProperty($equip, 'equip_id', 'wrong_equip_id');
        $ret = self::invokePrivateMethod(get_class($equip), 'checkEquipID', null, $equip);
        $this->assertFalse($ret);

        // 合法的 equip_id
        self::setPrivateProperty($equip, 'equip_id', self::TEST_EQUIP_ID);
        $ret = self::invokePrivateMethod(get_class($equip), 'checkEquipID', null, $equip);
        $this->assertTrue($ret);

        // 合法的 equip_id 被加入黑名单
        Yii::$app->redis->sAdd(KEY_BLACK_LIST_EQUIP_ID, self::TEST_EQUIP_ID);
        $ret = self::invokePrivateMethod(get_class($equip), 'checkEquipID', null, $equip);
        $this->assertFalse($ret);
    }

    public function testCheckMac()
    {
        // 测试合法的 mac 地址
        $this->assertTrue(Equipment::checkMac('f2:b6:43:a2:50:40'));

        // 测试不合法的 mac 地址
        $this->assertFalse(Equipment::checkMac('f2:b6:43:asddawww'));
    }

    public function testIsFromTencentChannel()
    {
        Yii::$app->equip->init(['channel' => Equipment::CHANNEL_TENCENT]);
        $this->assertTrue(Equipment::isFromTencentChannel());

        Yii::$app->equip->init(['channel' => Equipment::CHANNEL_TENCENT_64BIT]);
        $this->assertTrue(Equipment::isFromTencentChannel());

        Yii::$app->equip->init(['channel' => 'missevan_vivo']);
        $this->assertFalse(Equipment::isFromTencentChannel());
        Yii::$app->equip->init(['channel' => 'missevan64_vivo']);
        $this->assertFalse(Equipment::isFromTencentChannel());
    }

    public function testIsFromYunYouXiChannel()
    {
        Yii::$app->equip->init(['channel' => Equipment::CHANNEL_YUNYOUXI]);
        $this->assertTrue(Equipment::isFromYunYouXiChannel());

        Yii::$app->equip->init(['channel' => Equipment::CHANNEL_YUNYOUXI_64BIT]);
        $this->assertTrue(Equipment::isFromYunYouXiChannel());

        Yii::$app->equip->init(['channel' => 'missevan_vivo']);
        $this->assertFalse(Equipment::isFromYunYouXiChannel());
        Yii::$app->equip->init(['channel' => 'missevan64_vivo']);
        $this->assertFalse(Equipment::isFromYunYouXiChannel());
    }

    public function testIsFromGoogleChannel()
    {
        Yii::$app->equip->init(['channel' => Equipment::CHANNEL_GOOGLE]);
        $this->assertTrue(Equipment::isFromGoogleChannel());

        Yii::$app->equip->init(['channel' => Equipment::CHANNEL_GOOGLE_64BIT]);
        $this->assertTrue(Equipment::isFromGoogleChannel());

        Yii::$app->equip->init(['channel' => 'missevan_vivo']);
        $this->assertFalse(Equipment::isFromGoogleChannel());
        Yii::$app->equip->init(['channel' => 'missevan64_vivo']);
        $this->assertFalse(Equipment::isFromGoogleChannel());
    }

    public function testParseUserAgent()
    {
        $equip = Yii::$app->equip;
        // 测试来自 iOS App 的请求
        $equip->parseUserAgent('MissEvanApp/4.9.2 (iOS;16.1.2;iPhone15,2)');
        $this->assertEquals(Equipment::iOS, $equip->getOs());
        $this->assertTrue($equip->isFromApp());

        // 测试来自 Android App 的请求
        $equip->parseUserAgent('MissEvanApp/5.7.6 (Android;12;HUAWEI MRX-AL19 HWMRX)');
        $this->assertEquals(Equipment::Android, $equip->getOs());
        $this->assertTrue($equip->isFromApp());

        // 测试来自 WebView 的请求
        $equip->parseUserAgent('Mozilla/5.0 (iPhone; CPU iPhone OS 16_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MissEvanApp/4.9.2 (Theme Dark; NetType 4G)');
        $this->assertEquals(Equipment::Web, $equip->getOs());
        $this->assertFalse($equip->isFromApp());

        $equip->parseUserAgent('Mozilla/5.0 (Linux; Android 13; V2304A Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.72 MQQBrowser/6.2 TBS/046249 Mobile Safari/537.36 MissEvanApp/6.0.6 (Theme Light; NetType Other; SafeArea 29,0) isNotchWindow/1 NotchHeight=29');
        $this->assertEquals(Equipment::Web, $equip->getOs());
        $this->assertFalse($equip->isFromApp());
    }

    public function testParseDeviceToken()
    {
        // 删除其他测试产生的脏数据
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_NEW_DEVICE_EQUIP_ID, self::TEST_DEVICE_TOKEN_EQUIP_ID);
        $redis->del($key);

        // 测试解析正常 device_token
        $equip = Yii::$app->equip;
        $equip->init([
            'user_agent' => 'MissEvanApp/6.1.7 (Android;7.0;Meizu M6 M6)',
            'Cookie' => 'equip_id=' . self::TEST_DEVICE_TOKEN_EQUIP_ID,
        ]);
        $equip->parseDeviceToken(self::TEST_DEVICE_TOKEN);
        // 验证解析成功，可获取到设备初次安装激活时间
        $this->assertEquals(self::TEST_DEVICE_TOKEN_ACTIVATE_TIME, $equip->getActivateTime());

        // 测试无 device_token 时的情况
        self::setPrivateProperty($equip, 'activate_time', null);
        $equip->parseDeviceToken(null);
        // 验证无法通过解析 device token 获取设备信息
        $this->assertEquals(0, $equip->getActivateTime());
    }

    public function testIsBeta()
    {
        $tests = [
            // 灰度
            [
                'args' => [
                    'Cookie' => 'equip_id=d5e57d48-25dd-493a-9c16-9f49cb6c4d16',
                    'ratio' => 10,
                ],
                'expected' => true,
            ],
            [
                'args' => [
                    'Cookie' => 'equip_id=' . self::TEST_EQUIP_ID_2,
                    'ratio' => 100,
                ],
                'expected' => true,
            ],
            // 非灰度
            [
                'args' => [
                    'Cookie' => 'equip_id=d5e57d48-25dd-493a-9c16-9f49cb6c4d16',
                    'ratio' => 0,
                ],
                'expected' => false,
            ],
            [
                'args' => [
                    'Cookie' => 'equip_id=' . self::TEST_EQUIP_ID_2,
                    'ratio' => 10,
                ],
                'expected' => false,
            ],
            [
                'args' => [
                    'Cookie' => 'equip_id=649e52c5-b70f-a4f7-d53e-69c591872138',  // CRC32(equip_id) % 100 = 0
                    'ratio' => 0,
                ],
                'expected' => false,
            ]
        ];
        foreach ($tests as $test) {
            Yii::$app->equip->init([
                'Cookie' => $test['args']['Cookie']
            ]);
            $res = Yii::$app->equip->isBeta($test['args']['ratio']);
            $this->assertEquals($test['expected'], $res);
        }

        // 测试 equip_id 为空的情况
        self::setPrivateProperty(Yii::$app->equip, 'equip_id', '');
        $this->assertFalse(Yii::$app->equip->isBeta(100));
    }

    public function testGetActivateTime()
    {
        Yii::$app->equip->init([
            'Cookie' => 'equip_id=' . self::TEST_EQUIP_ID_2,
            'user_agent' => 'MissEvanApp/6.0.9 (iOS;12.0;iPhone9,1)'
        ]);
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_NEW_DEVICE_EQUIP_ID, self::TEST_EQUIP_ID_2);
        $redis->del($key);
        $redis->setex($key, ONE_HOUR, $_SERVER['REQUEST_TIME']);

        $this->assertEquals($_SERVER['REQUEST_TIME'], Yii::$app->equip->getActivateTime());
    }

    public function testIsNewEquipment()
    {
        Yii::$app->equip->init([
            'Cookie' => 'equip_id=' . self::TEST_EQUIP_ID_2,
            'user_agent' => 'MissEvanApp/6.0.9 (iOS;12.0;iPhone9,1)'
        ]);
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_NEW_DEVICE_EQUIP_ID, self::TEST_EQUIP_ID_2);
        $redis->del($key);

        // 测试是新设备
        $now = $_SERVER['REQUEST_TIME'];
        $redis->setex($key, ONE_HOUR, $now - ONE_WEEK);
        $this->assertTrue(Yii::$app->equip->isNewEquipment());

        // 重置设备激活时间
        self::setPrivateProperty(Yii::$app->equip, 'activate_time', null);
        // 测试不是新设备
        $redis->setex($key, ONE_HOUR, $now - ONE_WEEK - ONE_MINUTE);
        $this->assertFalse(Yii::$app->equip->isNewEquipment());

        // 测试安卓 >= 6.1.7 版本，伪造 device_token 为新人，但是实际非新人的情况（无新设备锁）
        $device_token = Equipment::generateDeviceToken($now, self::TEST_DEVICE_TOKEN);
        Yii::$app->equip->init([
            'Cookie' => 'equip_id=' . self::TEST_DEVICE_TOKEN_EQUIP_ID . '; device_token=' . $device_token,
            'user_agent' => 'MissEvanApp/6.1.7 (Android;7.0;Meizu M6 M6)'
        ]);
        self::setPrivateProperty(Yii::$app->equip, 'activate_time', null);
        $key = $redis->generateKey(KEY_NEW_DEVICE_EQUIP_ID, self::TEST_DEVICE_TOKEN_EQUIP_ID);
        $redis->del($key);
        $this->assertFalse(Yii::$app->equip->isNewEquipment());

        // 测试安卓 >= 6.1.7 版本，device_token 为新人，实际也是新人的情况（有新设备锁）
        $device_token = Equipment::generateDeviceToken($_SERVER['REQUEST_TIME'], self::TEST_DEVICE_TOKEN);
        Yii::$app->equip->init([
            'Cookie' => 'equip_id=' . self::TEST_DEVICE_TOKEN_EQUIP_ID . '; device_token=' . $device_token,
            'user_agent' => 'MissEvanApp/6.1.7 (Android;7.0;Meizu M6 M6)'
        ]);
        self::setPrivateProperty(Yii::$app->equip, 'activate_time', null);
        $key = $redis->generateKey(KEY_NEW_DEVICE_EQUIP_ID, self::TEST_DEVICE_TOKEN_EQUIP_ID);
        $redis->set($key, $now);
        $this->assertTrue(Yii::$app->equip->isNewEquipment());
    }

    public function testCheckAndRemoveNewEquipmentFlag()
    {
        $test_equipment_id = '6ae93485-test-b665-9ada-a26e4dda4244';
        Yii::$app->equip->init([
            'Cookie' => 'equip_id=' . $test_equipment_id,
            'user_agent' => 'MissEvanApp/6.0.9 (iOS;12.0;iPhone9,1)'
        ]);
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_NEW_DEVICE_EQUIP_ID, $test_equipment_id);
        $redis->del($key);

        // 测试新设备登录了新用户（期望不删除新人锁）
        $redis->setex($key, ONE_HOUR, $_SERVER['REQUEST_TIME']);
        $this->loginByUserId(self::TEST_USER_ID);
        Yii::$app->equip->checkAndRemoveNewEquipmentFlag();
        $this->assertEquals($_SERVER['REQUEST_TIME'], (int)$redis->get($key));

        // 测试新设备登录了老用户（期望删除新人锁）
        $this->loginByUserId(self::TEST_USER_ID, ['ctime' => $_SERVER['REQUEST_TIME'] - ONE_WEEK - ONE_MINUTE]);
        Yii::$app->equip->checkAndRemoveNewEquipmentFlag();
        $this->assertFalse($redis->get($key));
    }

    public function testIsIOS11()
    {
        // 测试安卓系统
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.1.0 (Android;12;HUAWEI MRX-AL19 HWMRX)']);
        $this->assertFalse(Yii::$app->equip->isIOS11());

        // 测试 iOS 10 系统
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.1.0 (iOS;10.4;iPhone9,1)']);
        $this->assertFalse(Yii::$app->equip->isIOS11());

        // 测试 iOS 11 系统
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.1.0 (iOS;11.4;iPhone9,1)']);
        $this->assertTrue(Yii::$app->equip->isIOS11());

        // 测试 iOS 12 系统
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.1.0 (iOS;12.0;iPhone9,1)']);
        $this->assertFalse(Yii::$app->equip->isIOS11());
    }

    public function testGenerateDeviceToken()
    {
        $device_token = Equipment::generateDeviceToken(self::TEST_DEVICE_TOKEN_ACTIVATE_TIME,
            self::TEST_DEVICE_TOKEN_EQUIP_ID);
        $this->assertEquals(self::TEST_DEVICE_TOKEN, $device_token);
    }

    public function testGetDeviceInfoByToken()
    {
        // 测试正常获取设备信息的情况
        $equip = Yii::$app->equip;
        $equip_class = get_class($equip);
        self::setPrivateProperty($equip, 'equip_id', self::TEST_DEVICE_TOKEN_EQUIP_ID);
        $device_info = self::invokePrivateMethod($equip_class, 'getDeviceInfoByToken',
            self::TEST_DEVICE_TOKEN, $equip);

        $this->assertEquals([
            'activate_time' => self::TEST_DEVICE_TOKEN_ACTIVATE_TIME,
            'v' => Equipment::DEVICE_TOKEN_VERSION,
        ], $device_info);

        // 测试 device token 异常的情况
        $test_device_token = 'error';
        $device_info = self::invokePrivateMethod($equip_class, 'getDeviceInfoByToken',
            $test_device_token, $equip);
        $this->assertNull($device_info);
        $test_device_token = 'v2|MMINcB3WckDWyHRKLglm4T7Xzr64g0sAc40FEspeY2Qq4Z331/oUfZt01+kGskKr';
        $device_info = self::invokePrivateMethod($equip_class, 'getDeviceInfoByToken',
            $test_device_token, $equip);
        $this->assertNull($device_info);
    }

    public function testGetDeviceType()
    {
        // 测试来源 Android 手机
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.2.0 (Android;9;HUAWEI INE-AL00 HWINE)']);
        $this->assertEquals(Equipment::DEVICE_TYPE_PHONE, Yii::$app->equip->getDeviceType());

        // 测试来源 Android Pad
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.2.0 (Android;10;Lenovo Lenovo TB-J606F J606F)']);
        $this->assertEquals(Equipment::DEVICE_TYPE_PAD, Yii::$app->equip->getDeviceType());

        // 测试来源 iPhone
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.2.0 (iOS;17.7;iPhone14,5)']);
        $this->assertEquals(Equipment::DEVICE_TYPE_PHONE, Yii::$app->equip->getDeviceType());

        // 测试来源 iPad
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.2.0 (iOS;16.7.10;iPad6,11)']);
        $this->assertEquals(Equipment::DEVICE_TYPE_PAD, Yii::$app->equip->getDeviceType());

        // 测试来源其他
        Yii::$app->equip->init(['user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36']);
        $this->assertEquals(Equipment::DEVICE_TYPE_UNKNOWN, Yii::$app->equip->getDeviceType());
    }

    public function testIsAppOlderThanVipVersion()
    {
        // 测试不支持会员的版本（目前支持会员的版本号暂定 7.0.0）
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.9.9 (Android;9;HUAWEI INE-AL00 HWINE)']);
        $this->assertTrue(Equipment::isAppOlderThanVipVersion());

        // 测试支持会员的版本
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/7.0.0 (Android;9;HUAWEI INE-AL00 HWINE)']);
        $this->assertFalse(Equipment::isAppOlderThanVipVersion());
    }

    public function testGetIsValidEquipId()
    {
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.3.4 (iOS;12.0;iPhone9,1)']);
        $this->assertTrue(Yii::$app->equip->isValidEquipId);

        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/6.3.4 (Android;9;HUAWEI INE-AL00 HWINE)',
            'Cookie' => 'equip_id=abc-def-ghi-jkl',
        ]);
        $this->assertFalse(Yii::$app->equip->isValidEquipId);

        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/6.3.4 (Android;9;HUAWEI INE-AL00 HWINE)',
            'Cookie' => 'equip_id=680c7eb8-f302-c9f7-bccf-3067fb9371b0',
        ]);
        $this->assertTrue(Yii::$app->equip->isValidEquipId);
    }

    /**
     * @depends testGetIsValidEquipId
     */
    public function testAssertEquipIdValid()
    {
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.3.4 (iOS;12.0;iPhone9,1)']);
        $this->assertEmpty(Yii::$app->equip->assertEquipIdValid());

        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/6.3.4 (Android;9;HUAWEI INE-AL00 HWINE)',
            'Cookie' => 'equip_id=abc-def-ghi-jkl',
        ]);
        $this->assertThrowsWithMessage(HttpException::class, '需要更新 equip_id', function () {
            Yii::$app->equip->assertEquipIdValid();
        }, 400, 100010012);

        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/6.3.4 (Android;9;HUAWEI INE-AL00 HWINE)',
            'Cookie' => 'equip_id=680c7eb8-f302-c9f7-bccf-3067fb9371b0',
        ]);
        $this->assertEmpty(Yii::$app->equip->assertEquipIdValid());
    }
}
