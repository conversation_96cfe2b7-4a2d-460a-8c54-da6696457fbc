<?php

namespace tests\unit\components\auth\wechat;

use app\components\auth\wechat\WechatOffiaccount;
use app\models\MThirdPartyTask;
use app\models\MWechatOffiaccountReply;
use tests\components\UnitTestCase;
use Yii;

class WechatOffiaccountTest extends UnitTestCase
{
    private static $origin_db;
    private static $redis;

    private static function setOriginDbs(): void
    {
        self::$origin_db = Yii::$app->db;
        Yii::$app->set('db', Yii::$app->sqlitedb);
    }

    private static function resetOriginDbs(): void
    {
        Yii::$app->set('db', self::$origin_db);
    }

    protected function _before()
    {
        parent::_before();
        self::setOriginDbs();
    }

    protected function _after()
    {
        parent::_after();
        self::resetOriginDbs();
    }

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        self::setOriginDbs();
        self::$redis = Yii::$app->redis;
        self::clearData();
    }

    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
        self::setOriginDbs();
        self::clearData();
        self::resetOriginDbs();
    }

    private static function clearData()
    {
        $key = self::$redis->generateKey(KEY_THIRD_TASK_TOKEN, MThirdPartyTask::SCENE_WECHAT_OFFIACCOUNT,
            date('Ymd'));
        self::$redis->del($key);
    }

    public function testNewInstance()
    {
        $wechat_offiaccount = WechatOffiaccount::newInstance();
        $this->assertInstanceOf(WechatOffiaccount::class, $wechat_offiaccount);
        $this->assertEquals(Yii::$app->params['service']['wechat-offiaccount']['token'], $wechat_offiaccount->token);
    }

    public function testVerifySignature()
    {
        // 测试签名不通过
        $wechat_offiaccount = WechatOffiaccount::newInstance();
        Yii::$app->request->setQueryParams([
            'signature' => 'test_error_signature',
            'timestamp' => '**********',
            'nonce' => 'test_nonce',
        ]);
        $this->assertFalse($wechat_offiaccount->verifySignature(Yii::$app->request));

        // 测试签名通过
        Yii::$app->request->setQueryParams([
            'signature' => '18a836054387deadd3d7e4ef7e2b2a804809e0ca',
            'timestamp' => '**********',
            'nonce' => 'test_nonce',
        ]);
        $this->assertTrue($wechat_offiaccount->verifySignature(Yii::$app->request));
    }

    public function testGetMsgNoReplyResponse()
    {
        $response = WechatOffiaccount::getMsgNoReplyResponse();
        $this->assertEquals('success', $response);
    }

    public function testGetMessageInfo()
    {
        // 测试成功获取消息内容
        $wechat_offiaccount = WechatOffiaccount::newInstance();
        $xml = <<<XML
<xml>
  <ToUserName><![CDATA[test_to_user_id]]></ToUserName>
  <FromUserName><![CDATA[test_from_user_id]]></FromUserName>
  <CreateTime>**********</CreateTime>
  <MsgType><![CDATA[text]]></MsgType>
  <Content><![CDATA[this is a test]]></Content>
  <MsgId>**********123456</MsgId>
</xml>
XML;
        Yii::$app->request->setRawBody($xml);
        $message_info = $wechat_offiaccount->getMessageInfo(Yii::$app->request);
        $this->assertIsArray($message_info);
        $this->assertEquals('test_to_user_id', $message_info['to_user_name']);
        $this->assertEquals('test_from_user_id', $message_info['from_user_name']);
        $this->assertEquals(**********, $message_info['create_time']);
        $this->assertEquals('text', $message_info['msg_type']);
        $this->assertEquals('this is a test', $message_info['content']);
        $this->assertEquals(**********123456, $message_info['msg_id']);

        // 测试消息内容为空的情况
        Yii::$app->request->setRawBody('');
        $message_info = $wechat_offiaccount->getMessageInfo(Yii::$app->request);
        $this->assertNull($message_info);

        // 测试消息内容不合法的情况
        Yii::$app->request->setRawBody('<xml><ToUserName><![CDATA[]]></ToUserName></xml>');
        $message_info = $wechat_offiaccount->getMessageInfo(Yii::$app->request);
        $this->assertNull($message_info);
    }

    public function testGetEventInfo()
    {
        // 测试成功获取消息内容
        $wechat_offiaccount = WechatOffiaccount::newInstance();
        $xml = <<<XML
<xml>
  <ToUserName><![CDATA[test_to_user_id]]></ToUserName>
  <FromUserName><![CDATA[test_from_user_id]]></FromUserName>
  <CreateTime>*********</CreateTime>
  <MsgType><![CDATA[event]]></MsgType>
  <Event><![CDATA[CLICK]]></Event>
  <EventKey><![CDATA[test]]></EventKey>
</xml>
XML;
        Yii::$app->request->setRawBody($xml);
        $message_info = $wechat_offiaccount->getEventInfo(Yii::$app->request);
        $this->assertIsArray($message_info);
        $this->assertEquals('test_to_user_id', $message_info['to_user_name']);
        $this->assertEquals('test_from_user_id', $message_info['from_user_name']);
        $this->assertEquals(*********, $message_info['create_time']);
        $this->assertEquals('event', $message_info['msg_type']);
        $this->assertEquals('CLICK', $message_info['event']);
        $this->assertEquals('test', $message_info['event_key']);

        // 测试消息内容为空的情况
        Yii::$app->request->setRawBody('');
        $message_info = $wechat_offiaccount->getEventInfo(Yii::$app->request);
        $this->assertNull($message_info);

        // 测试消息内容不合法的情况
        Yii::$app->request->setRawBody('<xml><Event><![CDATA[]]></Event></xml>');
        $message_info = $wechat_offiaccount->getEventInfo(Yii::$app->request);
        $this->assertNull($message_info);
    }

    public function testHandleTextMessage()
    {
        // 测试回复内容不为鱼干任务关键字，也不为特定的自动回复关键字，回复默认内容
        $wechat_offiaccount = WechatOffiaccount::newInstance();
        $message = [
            'to_user_name' => 'test_to_user_id',
            'from_user_name' => 'test_from_user_id',
            'create_time' => **********,
            'msg_type' => WechatOffiaccount::MSG_TYPE_TEXT,
            'content' => 'test_content',
            'msg_id' => **********123456,
        ];
        $data = $wechat_offiaccount->handleTextMessage($message);
        $this->assertStringContainsString('<xml>', $data);
        $data_arr = (array)simplexml_load_string($data, 'SimpleXMLElement', LIBXML_NOCDATA);
        $this->assertArrayHasKeys(['ToUserName', 'FromUserName', 'CreateTime', 'MsgType', 'Content'], $data_arr);
        $this->assertEquals('test_from_user_id', $data_arr['ToUserName']);
        $this->assertEquals('test_to_user_id', $data_arr['FromUserName']);
        $this->assertEquals($_SERVER['REQUEST_TIME'], $data_arr['CreateTime']);
        $this->assertEquals(WechatOffiaccount::MSG_TYPE_TEXT, $data_arr['MsgType']);
        $this->assertEquals(MWechatOffiaccountReply::DEFAULT_TEXT_REPLY, $data_arr['Content']);

        // 测试关注公众号任务配置关键词时，回复正确的内容
        $message['content'] = '活动任务';
        $data = $wechat_offiaccount->handleTextMessage($message);
        $this->assertStringContainsString('<xml>', $data);
        $data_arr = (array)simplexml_load_string($data, 'SimpleXMLElement', LIBXML_NOCDATA);
        $this->assertArrayHasKeys(['ToUserName', 'FromUserName', 'CreateTime', 'MsgType', 'Content'], $data_arr);
        $this->assertEquals('test_from_user_id', $data_arr['ToUserName']);
        $this->assertEquals('test_to_user_id', $data_arr['FromUserName']);
        $this->assertEquals($_SERVER['REQUEST_TIME'], $data_arr['CreateTime']);
        $this->assertEquals(WechatOffiaccount::MSG_TYPE_TEXT, $data_arr['MsgType']);
        $this->assertStringContainsString('https://test.test.com/event?third_task_token=', $data_arr['Content']);

        // 测试非公众号任务配置关键字，但是为特定自动回复关键字，回复正确的内容
        $message['content'] = 'text test';
        $data = $wechat_offiaccount->handleTextMessage($message);
        $this->assertStringContainsString('<xml>', $data);
        $data_arr = (array)simplexml_load_string($data, 'SimpleXMLElement', LIBXML_NOCDATA);
        $this->assertArrayHasKeys(['ToUserName', 'FromUserName', 'CreateTime', 'MsgType', 'Content'], $data_arr);
        $this->assertEquals('test_from_user_id', $data_arr['ToUserName']);
        $this->assertEquals('test_to_user_id', $data_arr['FromUserName']);
        $this->assertEquals($_SERVER['REQUEST_TIME'], $data_arr['CreateTime']);
        $this->assertEquals(WechatOffiaccount::MSG_TYPE_TEXT, $data_arr['MsgType']);
        $this->assertEquals('test reply', $data_arr['Content']);
    }

    public function testHandleEventMessage()
    {
        // 测试为关注事件时，回复特定的语音内容
        $wechat_offiaccount = WechatOffiaccount::newInstance();
        $message = [
            'to_user_name' => 'test_to_user_id',
            'from_user_name' => 'test_from_user_id',
            'create_time' => **********,
            'msg_type' => WechatOffiaccount::MSG_TYPE_EVENT,
            'event' => WechatOffiaccount::EVENT_TYPE_SUBSCRIBE,
        ];
        $data = $wechat_offiaccount->handleEventMessage($message);
        $this->assertStringContainsString('<xml>', $data);
        $data_arr = (array)simplexml_load_string($data, 'SimpleXMLElement', LIBXML_NOCDATA);
        $this->assertArrayHasKeys(['ToUserName', 'FromUserName', 'CreateTime', 'MsgType', 'Voice'], $data_arr);
        $this->assertEquals('test_from_user_id', $data_arr['ToUserName']);
        $this->assertEquals('test_to_user_id', $data_arr['FromUserName']);
        $this->assertEquals($_SERVER['REQUEST_TIME'], $data_arr['CreateTime']);
        $this->assertEquals(WechatOffiaccount::MSG_TYPE_VOICE, $data_arr['MsgType']);
        $this->assertIsObject($data_arr['Voice']);
        $this->assertEquals('test_voice_medal_id', $data_arr['Voice']->MediaId);

        // 测试为其他事件时，回复默认内容
        $message['event'] = WechatOffiaccount::EVENT_TYPE_CLICK;
        $this->assertEquals('success', $wechat_offiaccount->handleTextMessage($message));
    }

    public function testGetPointTaskReply()
    {
        // 测试获取已配置的任务关键字的自动回复内容
        $wechat_offiaccount = WechatOffiaccount::newInstance();
        $reply = $wechat_offiaccount->getPointTaskReply('活动任务');
        $this->assertIsArray($reply);
        $this->assertArrayHasKeys(['MsgType', 'Content'], $reply);
        $this->assertEquals(WechatOffiaccount::MSG_TYPE_TEXT, $reply['MsgType']);
        $this->assertStringContainsString('https://test.test.com/event?third_task_token=', $reply['Content']);

        // 测试获取未配置关键字的自动回复内容
        $this->assertEquals('', $wechat_offiaccount->getPointTaskReply('not found'));
    }

    public function testGenerateTaskReply()
    {
        // 测试生成任务回复内容
        $open_client_text_format = '<a href="https://test.test.com/event?third_task_token=%s">活动任务</a>';
        $reply = self::invokePrivateMethod(WechatOffiaccount::class, 'generateTaskReply', $open_client_text_format);
        $this->assertStringContainsString('https://test.test.com/event?third_task_token=', $reply);
        $this->assertEquals(96, mb_strlen($reply));
    }
}
