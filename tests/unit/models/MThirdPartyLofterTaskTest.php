<?php

namespace tests\models;

use app\models\MThirdPartyLofterTask;
use tests\components\UnitTestCase;
use tests\components\util\Tools;

class MThirdPartyLofterTaskTest extends UnitTestCase
{
    public function testCallback()
    {
        Tools::registerRemoteApiResponseFunc(MThirdPartyLofterTask::LOFTER_CALLBACK_URL, function () {
            return ['code' => MThirdPartyLofterTask::LOFTER_REQUEST_SUCCESS_CODE];
        });

        // 测试回调成功
        $data = MThirdPartyLofterTask::callback('test');
        $this->assertTrue($data);
    }

    public function testBuildSign()
    {
        $test_params = [
            'token' => 'test',
            'platform' => 'maoer',
            'timestamp' => 1750649731000,
        ];
        $before_sign = 'platform=maoer&timestamp=1750649731000&token=test'
            . '&key=ME_8a8dd288-0826-4d12-ada8-aa0b8b3f90c2';
        $expected = md5($before_sign);

        $ad_track = new MThirdPartyLofterTask();
        $actual = self::invokePrivateMethod(MThirdPartyLofterTask::class, 'buildSign', $test_params, $ad_track);
        $this->assertEquals($expected, $actual);
    }
}
