<?php

namespace tests\models;

use app\models\MThirdPartyWeiboTask;
use tests\components\UnitTestCase;
use tests\components\util\Tools;

class MThirdPartyWeiboTaskTest extends UnitTestCase
{
    public function testCallback()
    {
        Tools::registerRemoteApiResponseFunc(MThirdPartyWeiboTask::CALLBACK_URL_WEIBO, function () {
            return ['status' => MThirdPartyWeiboTask::WEIBO_REQUEST_SUCCESS_CODE];
        });

        // 测试回调成功
        $data = MThirdPartyWeiboTask::callback('test');
        $this->assertTrue($data);
    }
}
