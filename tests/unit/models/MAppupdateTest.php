<?php

namespace tests\models;

use app\components\db\RedisConnection;
use app\models\MAppupdate;
use app\models\User;
use missevan\storage\StorageClient;
use Yii;
use yii\web\HttpException;
use ReflectionClass;
use tests\components\util\Equipment;
use tests\components\UnitTestCase;

class MAppupdateTest extends UnitTestCase
{
    /**
     * @var null|RedisConnection;
     */
    private static $redis = null;
    /**
     * @var string
     */
    private static $original_ios_version = '';

    const TEST_MAPPUPDATE_ID = 999;
    const TEST_MAPPUPDATE_VERSION = '999';

    private static $origin_db;

    protected function _before()
    {
        parent::_before();
        self::$origin_db = Yii::$app->db;
        Yii::$app->set('db', Yii::$app->sqlitedb);
    }

    protected function _after()
    {
        parent::_after();
        Yii::$app->set('db', self::$origin_db);
    }

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        self::$redis = Yii::$app->redis;
        self::$original_ios_version = self::$redis->get(KEY_IOS_VERSION);
        self::$redis->set(KEY_IOS_VERSION, '4.3.5');
    }

    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
        self::$redis->set(KEY_IOS_VERSION, self::$original_ios_version);
    }

    /**
     * 检查用户的 Beta 测试权限
     *
     * @throws \Exception
     */
    public function testGetIsBeta()
    {
        // Before Init Data
        $device = MAppupdate::DEVICE_ANDROID;
        $stub = new MAppupdate();
        $stub->attributes = [
            'id' => 999999,
            'title' => '测试版本',
            'version' => '99999',
            'intro' => 'emm',
            'changelog' => '更新了超多内容',
            'status' => MAppupdate::STATUS_BETA,
            'appurl' => 'https://www.missevan.com',
            'update_time' => 1552400000,
            'device' => $device,
            'size' => 1000.0,
            'force_download' => 0,
        ];

        $redis = Yii::$app->redis;
        $key_special = $redis->generateKey(KEY_BETA_RELEASE_SPECIAL_IDS, $device);
        // 重设灰度值
        $key_beta = $redis->generateKey(KEY_BETA_RELEASE, $device);
        $redis->del($key_beta);
        if (!$redis->lPush($key_beta, 65, 10)) {
            throw new \Exception('灰度测试的 Key 设置失败');
        }

        // Test Body
        $tests = [
            [
                'equipId' => 'testuuid1',  // crc32(equipId + slat) 值为 4084371964
                'device' => Equipment::Android,
                'checkUserID' => false,  // 【接收推送的特定用户列表】是否包含当前用户
                'expected' => 1,  // 1 表示具备资格，0 为不具备
                'alert' => '预期灰度用户具备测试资格',
            ], [
                'equipId' => 'testuuid1',
                'device' => Equipment::iOS,
                'checkUserID' => false,
                'expected' => 0,
                'alert' => '预期设备类型为 iOS 不具备测试资格',
            ], [
                'equipId' => 'testuuid2a',  // crc32(equipId + slat) 值为 657564568
                'device' => Equipment::Android,
                'checkUserID' => false,
                'expected' => 0,
                'alert' => '预期超出范围的 EquipID 不具备测试资格',
            ], [
                'equipId' => 'testuuid1',
                'device' => Equipment::Android,
                'checkUserID' => true,
                'expected' => 1,
                'alert' => '预期特定推送用户的具备测试资格',
            ], [
                'equipId' => 'testuuid2a',
                'device' => Equipment::Android,
                'checkUserID' => false,
                'expected' => 0,
                'alert' => '预期非特定推送用户不具备测试资格',
            ]
        ];

        // Assert Result
        $USER_LIST = [
            'join' => [2, self::TEST_USER_ID, 492],
            'absent' => [2, 492],
        ];
        $model = new MAppupdate();

        foreach ($tests as $test) {
            // 重设接收推送的特定用户列表
            $redis->del($key_special);
            $user_list = $test['checkUserID'] ? $USER_LIST['join'] : $USER_LIST['absent'];
            if (!$redis->sAdd($key_special, ...$user_list)) {
                throw new \Exception('请重试，参与测试的用户 ID 设置失败');
            }

            $result = $model->getIsBeta($stub, $test['device'], $test['equipId']);
            $this->assertEquals($test['expected'], $result, "Test failed: {$test['alert']}");
        }
    }

    /**
     * @depends testGetApp
     */
    public function testGetLastestApp()
    {
        $app = MAppupdate::getLastestApp(Equipment::Android);
        $this->assertNotEmpty($app);
        $this->assertInstanceOf(MAppupdate::class, $app);
        $this->assertContains($app->status, [MAppupdate::STATUS_BETA, MAppupdate::STATUS_PUBLISHED]);
    }

    /**
     * @depends testGetApp
     */
    public function testGetPublishedApp()
    {
        $app = MAppupdate::getPublishedApp(Equipment::Android);
        $this->assertNotEmpty($app);
        $this->assertInstanceOf(MAppupdate::class, $app);
        $this->assertEquals(MAppupdate::STATUS_PUBLISHED, $app->status);
    }

    public function testGetApp()
    {
        $this->assertThrowsWithMessage(HttpException::class, '该设备不能升级', function () {
            MAppupdate::getApp(Equipment::Web, MAppupdate::STATUS_PUBLISHED);
        });

        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.1.0 (iOS;10.4;iPhone9,1)']);
        $app = MAppupdate::getApp(Equipment::iOS, MAppupdate::STATUS_PUBLISHED);
        $this->assertNotEmpty($app);
        $this->assertInstanceOf(MAppupdate::class, $app);
        $this->assertEquals(MAppupdate::STATUS_PUBLISHED, $app->status);
        $this->assertEquals(MAppupdate::DEVICE_IOS, $app->device);

        // Android >= 5.7.4 下发最新版本
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/5.7.9 (Android;7.0;Meizu M6 M6)']);
        $app = MAppupdate::getApp(Equipment::Android, [MAppupdate::STATUS_BETA, MAppupdate::STATUS_PUBLISHED]);
        $this->assertNotEmpty($app);
        $this->assertInstanceOf(MAppupdate::class, $app);
        $this->assertContains($app->status, [MAppupdate::STATUS_BETA, MAppupdate::STATUS_PUBLISHED]);
        $this->assertEquals(MAppupdate::DEVICE_ANDROID, $app->device);
        $this->assertGreaterThan(MAppupdate::ANDROID_VERSION_ID_574, $app->id);

        // Android < 5.7.4 下发 5.7.4 版本
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/5.7.3 (Android;7.0;Meizu M6 M6)']);
        $app = MAppupdate::getApp(Equipment::Android, [MAppupdate::STATUS_BETA, MAppupdate::STATUS_PUBLISHED]);
        $this->assertNotEmpty($app);
        $this->assertInstanceOf(MAppupdate::class, $app);
        $this->assertContains($app->status, [MAppupdate::STATUS_BETA, MAppupdate::STATUS_PUBLISHED]);
        $this->assertEquals(MAppupdate::DEVICE_ANDROID, $app->device);
        $this->assertEquals(MAppupdate::ANDROID_VERSION_ID_574, $app->id);

        // 测试针对 iOS < 4.9.6、iOS 系统为 11.0 以下的客户端始终下发 4.9.5 版本
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.7.2 (iOS;10.4;iPhone8,2)']);
        $app = MAppupdate::getApp(Equipment::iOS, MAppupdate::STATUS_PUBLISHED);
        $this->assertNotEmpty($app);
        $this->assertInstanceOf(MAppupdate::class, $app);
        $this->assertEquals(MAppupdate::STATUS_PUBLISHED, $app->status);
        $this->assertEquals(MAppupdate::DEVICE_IOS, $app->device);
        $this->assertEquals(MAppupdate::IOS_VERSION_ID_495, $app->id);

        // 测试针对 iOS 11 系统且版本小于 6.1.2 的客户端始终下发 6.1.1 版本
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.1.0 (iOS;11.4;iPhone8,2)']);
        $app = MAppupdate::getApp(Equipment::iOS, MAppupdate::STATUS_PUBLISHED);
        $this->assertNotEmpty($app);
        $this->assertInstanceOf(MAppupdate::class, $app);
        $this->assertEquals(MAppupdate::STATUS_PUBLISHED, $app->status);
        $this->assertEquals(MAppupdate::DEVICE_IOS, $app->device);
        $this->assertEquals(MAppupdate::IOS_VERSION_ID_611, $app->id);
    }

    public function testGetIOSVersion()
    {
        $version = MAppupdate::getIOSVersion();
        $ok = fnmatch('*.*.*', $version);
        $this->assertTrue($ok);
    }

    public function testIsForceDownload()
    {
        $app = MAppupdate::find()
            ->where(['force_download' => MAppupdate::FORCE_DOWNLOAD_NO, 'device' => MAppupdate::DEVICE_ANDROID])
            ->one();
        MAppupdate::isForceDownload($app, '1.1.1');
        $this->assertEquals(MAppupdate::FORCE_DOWNLOAD_YES, $app->force_download);

        $app = MAppupdate::find()
            ->where(['force_download' => MAppupdate::FORCE_DOWNLOAD_NO, 'device' => MAppupdate::DEVICE_IOS])
            ->one();
        MAppupdate::isForceDownload($app, '1.1.1');
        $this->assertEquals(MAppupdate::FORCE_DOWNLOAD_YES, $app->force_download);
    }

    public function testProcessAppUrlAndSign()
    {
        // MiMi App
        Yii::$app->equip->init(['user_agent' => 'MiMiApp/1.0.5 (Android;7.0;Meizu M6 M6)']);
        $app = new MAppupdate([
            'device' => MAppupdate::DEVICE_ANDROID,
            'intro' => '1.0.7',
            'appurl' => 's3://app/202108/16/MissEvan.apk',
        ]);
        $app->afterFind();
        MAppupdate::processAppUrlAndSign($app);
        $this->assertEquals(StorageClient::getFileUrl('s3://app/202108/16/MissEvan.apk'), $app->appurl);

        // MissEvan App 非谷歌渠道
        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/5.7.9 (Android;7.0;Meizu M6 M6)',
            'channel' => Equipment::CHANNEL_OFFICIAL,
        ]);
        $app = new MAppupdate([
            'device' => MAppupdate::DEVICE_ANDROID,
            'intro' => '6.0.7',
            'appurl' => 'oss://app/202311/25/MissEvan.apk',
        ]);
        $app->afterFind();
        MAppupdate::processAppUrlAndSign($app);
        $this->assertEquals(StorageClient::getFileUrl('oss://app/202311/25/MissEvan.apk'), $app->appurl);

        // MissEvan App 谷歌渠道
        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/5.7.9 (Android;7.0;Meizu M6 M6)',
            'channel' => Equipment::CHANNEL_GOOGLE,
        ]);
        $app = new MAppupdate([
            'device' => MAppupdate::DEVICE_ANDROID,
            'intro' => '6.0.0',
            'appurl' => 'oss://app/202311/25/MissEvan.apk',
        ]);
        $app->afterFind();
        MAppupdate::processAppUrlAndSign($app);
        $this->assertEquals(MAppupdate::ANDROID_APP_GOOGLE_CHANNEL_LINK, $app->appurl);

        $app->intro = '6.0.7';
        MAppupdate::processAppUrlAndSign($app);
        $this->assertEquals(0, $app->version);
    }

    public function testGetUpdate()
    {
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/5.1.4 (Android;7.0;Meizu M6 M6)']);
        $data = MAppupdate::getUpdate(Yii::$app->equip);
        $this->assertArrayHasKey('new_version', $data);
        $this->assertNotNull($data['new_version']);
        $this->assertArrayNotHasKey('version', $data);

        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.2.5 (iOS;12.4;iPhone8,2)']);
        $data = MAppupdate::getUpdate(Yii::$app->equip, 1);
        $this->assertArrayHasKey('version', $data);

        // 测试云游戏渠道包不返回新版本信息
        Yii::$app->equip->init([
            'channel' => Equipment::CHANNEL_YUNYOUXI,
            'user_agent' => 'MissEvanApp/5.4.9 (Android;7.0;Meizu M6 M6)'
        ]);
        $data = MAppupdate::getUpdate(Yii::$app->equip, 1);
        $this->assertArrayHasKey('new_version', $data);
        $this->assertNull($data['new_version']);

        // 测试针对 iOS < 4.9.6、iOS 系统为 11.0 以下的客户端始终下发 4.9.5 版本
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.2.5 (iOS;10.4;iPhone8,2)']);
        $data = MAppupdate::getUpdate(Yii::$app->equip, 1);
        $this->assertArrayHasKey('new_version', $data);
        $this->assertNotEmpty($data['new_version']);
        $this->assertArrayHasKey('intro', $data['new_version']);
        $this->assertEquals('4.9.5', $data['new_version']['intro']);

        // 测试针对 iOS 11 系统且版本小于 6.1.2 的客户端始终下发 6.1.1 版本
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.2.5 (iOS;11.4;iPhone8,2)']);
        $data = MAppupdate::getUpdate(Yii::$app->equip, 1);
        $this->assertArrayHasKey('new_version', $data);
        $this->assertNotEmpty($data['new_version']);
        $this->assertArrayHasKey('intro', $data['new_version']);
        $this->assertEquals('6.1.1', $data['new_version']['intro']);

        // 测试 iOS >= 4.7.1 并且是 iOS 12 以上系统
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.7.1 (iOS;12.4;iPhone8,2)']);
        $data = MAppupdate::getUpdate(Yii::$app->equip);
        $this->assertTrue(version_compare('4.7.1', $data['new_version']['intro'], '<'));

        // 测试 Android supported_abis 不包含 arm64-v8a
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/5.1.4 (Android;7.0;Meizu M6 M6)']);
        $data = MAppupdate::getUpdate(Yii::$app->equip, 1, 'armeabi-v7a');
        $this->assertStringContainsString('MissEvan', $data['new_version']['download']);
        $this->assertStringNotContainsString('MissEvan_64', $data['new_version']['download']);

        // 测试 Android supported_abis 包含 arm64-v8a
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/5.1.4 (Android;7.0;Meizu M6 M6)']);
        $data = MAppupdate::getUpdate(Yii::$app->equip, 1, false, 'arm64-v8a,armeabi-v7a');
        $this->assertStringContainsString('MissEvan_64', $data['new_version']['download']);
        $this->assertIsValidHttpUrl($data['new_version']['download']);

        // 测试 launch 接口调用，推送版本号为空时（v = version）
        MAppupdate::updateAll(['push_version' => ''], 'id = :id', [':id' => self::TEST_MAPPUPDATE_ID]);
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.7.2 (iOS;12.4;iPhone8,2)']);
        $data = MAppupdate::getUpdate(Yii::$app->equip, 1, true, '');
        $this->assertIsArray($data);
        $this->assertEquals(self::TEST_MAPPUPDATE_VERSION, $data['new_version']['v']);

        // 测试 launch 接口调用，推送版本号不为空时（v = push_version）
        MAppupdate::updateAll(['push_version' => '89'], 'id = :id', [':id' => self::TEST_MAPPUPDATE_ID]);
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.7.2 (iOS;12.4;iPhone8,2)']);
        $data = MAppupdate::getUpdate(Yii::$app->equip, 1, true, '');
        $this->assertIsArray($data);
        $this->assertEquals('89', $data['new_version']['v']);

        // 测试 check-update 调用（v = version）
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.7.2 (iOS;12.4;iPhone8,2)']);
        $data = MAppupdate::getUpdate(Yii::$app->equip, 1);
        $this->assertIsArray($data);
        $this->assertEquals(self::TEST_MAPPUPDATE_VERSION, $data['new_version']['v']);

        // 测试 version_code 为 6000610 或 6000620 时，在启动 App 时强制更新
        MAppupdate::updateAll(['force_download' => MAppupdate::FORCE_DOWNLOAD_NO],
            'id = :id', [':id' => 3]);
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.0.5 (Android;4.4;Meizu M6 M6)']);
        $data = MAppupdate::getUpdate(Yii::$app->equip, 6000620, true);
        $this->assertEquals(MAppupdate::FORCE_DOWNLOAD_YES, $data['new_version']['force_download']);

        // 测试灰度用户，下发最新灰度版本
        $key_beta = self::$redis->generateKey(KEY_BETA_RELEASE, MAppupdate::DEVICE_ANDROID);
        self::$redis->del($key_beta);
        self::$redis->lPush($key_beta, 100, 0);
        $data = MAppupdate::getUpdate(Yii::$app->equip, 0);
        $this->assertIsArray($data);
        $this->assertEquals('6.1.2', $data['new_version']['intro']);
        $this->assertEquals('6010271', $data['new_version']['v']);
        $this->assertEquals(MAppupdate::FORCE_DOWNLOAD_NO, $data['new_version']['force_download']);

        // 测试安卓 6.1.0 的 6010081 和 6010082 版本埋点上报存在问题，需要提示更新到最新版本
        MAppupdate::updateAll([
            'intro' => '6.1.2',
            'version' => 6020001,
            'push_version' => 6010080,
            'device' => MAppupdate::DEVICE_ANDROID
        ], 'id = :id', [':id' => self::TEST_MAPPUPDATE_ID]);
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.1.0 (Android;7.0;Meizu M6 M6)']);
        $data = MAppupdate::getUpdate(Yii::$app->equip, 6010081, true);
        $this->assertIsArray($data);
        $this->assertEquals('6.1.2', $data['new_version']['intro']);
        $this->assertEquals(6020001, $data['new_version']['v']);
        $this->assertEquals(0, $data['new_version']['force_download']);

        // 测试安卓 6.1.0 非 6010081 和 6010082 版本，且小于 push_version，需要提示更新到 push_version 版本
        MAppupdate::updateAll(['intro' => '6.1.2', 'version' => 6020001, 'push_version' => 6010080],
            'id = :id', [':id' => self::TEST_MAPPUPDATE_ID]);
        $data = MAppupdate::getUpdate(Yii::$app->equip, 6010079, true);
        $this->assertIsArray($data);
        $this->assertEquals('6.1.2', $data['new_version']['intro']);
        $this->assertEquals(6010080, $data['new_version']['v']);
    }

    public function testGetPrivacyDataUrl()
    {
        // 测试获取普通版隐私协议文件
        $result = MAppupdate::getPrivacyDataUrl(Equipment::CHANNEL_OFFICIAL);
        $this->assertStringContainsString('/app/privacy.json', $result);

        Yii::$app->equip->init(['channel' => Equipment::CHANNEL_CONCEPT]);
        // 测试获取概念版隐私协议文件
        $result = MAppupdate::getPrivacyDataUrl(Equipment::CHANNEL_CONCEPT);
        $this->assertStringContainsString('/app/privacy/privacy-20211209.json', $result);

        // 测试获取 Google Play 隐私协议文件
        Yii::$app->equip->init(['channel' => '']);
        $result = MAppupdate::getPrivacyDataUrl(Equipment::CHANNEL_GOOGLE);
        $this->assertStringContainsString('/app/privacy-google.json', $result);
    }

    public function testIsLegacyAndroid()
    {
        // 测试 Android < 5.6.6、Android 系统为 4.x 的客户端
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/5.1.4 (Android;4.4;Meizu M6 M6)']);
        $this->assertTrue(MAppupdate::isLegacyAndroid());

        // 测试 Android = 5.6.6、Android 系统为 4.x 的客户端
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/5.6.6 (Android;4.4;Meizu M6 M6)']);
        $this->assertFalse(MAppupdate::isLegacyAndroid());

        // 测试 Android < 5.6.6、Android 系统为 5.x 的客户端
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/5.1.4 (Android;5.4;Meizu M6 M6)']);
        $this->assertFalse(MAppupdate::isLegacyAndroid());

        // 测试 iOS
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.7.2 (iOS;11.4;iPhone8,2)']);
        $this->assertFalse(MAppupdate::isLegacyAndroid());
    }

    public function testIsOlderThanAndroid6()
    {
        // 测试 Android 系统为 5.9 的客户端
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/5.6.6 (Android;5.9;Meizu M6 M6)']);
        $this->assertTrue(MAppupdate::isOlderThanAndroid6());

        // 测试 Android 系统为 6.x 的客户端
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/5.6.6 (Android;6.0;Meizu M6 M6)']);
        $this->assertFalse(MAppupdate::isOlderThanAndroid6());
    }

    public function testIsLegacyIOS()
    {
        // 测试 iOS 10 的系统
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.7.2 (iOS;10.4;iPhone8,2)']);
        $this->assertTrue(MAppupdate::isLegacyIOS());

        // 测试 iOS 11 的系统
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.7.2 (iOS;11.4;iPhone8,2)']);
        $this->assertFalse(MAppupdate::isLegacyIOS());

        // 测试 Android
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/5.1.4 (Android;5.4;Meizu M6 M6)']);
        $this->assertFalse(MAppupdate::isLegacyIOS());
    }

    public function testIsLegacyIOS11()
    {
        $ref_class = new ReflectionClass(MAppupdate::class);
        $instance = $ref_class->newInstance();

        // 测试 iOS 11 的系统
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.1.1 (iOS;11.4;iPhone8,2)']);
        $this->assertTrue(self::invokePrivateMethod($ref_class, 'isLegacyIOS11', null, $instance));

        // 测试 iOS 12 的系统
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.1.1 (iOS;12.4;iPhone8,2)']);
        $this->assertFalse(self::invokePrivateMethod($ref_class, 'isLegacyIOS11', null, $instance));

        // 测试 Android
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.1.1 (Android;5.4;Meizu M6 M6)']);
        $this->assertFalse(self::invokePrivateMethod($ref_class, 'isLegacyIOS11', null, $instance));
    }
}
