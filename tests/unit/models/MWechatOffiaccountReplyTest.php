<?php

namespace tests\models;

use app\components\auth\wechat\WechatOffiaccount;
use app\models\MWechatOffiaccountReply;
use tests\components\UnitTestCase;
use Yii;

class MWechatOffiaccountReplyTest extends UnitTestCase
{
    private static $origin_db;

    private static function setOriginDbs(): void
    {
        self::$origin_db = Yii::$app->db;
        Yii::$app->set('db', Yii::$app->sqlitedb);
    }

    private static function resetOriginDbs(): void
    {
        Yii::$app->set('db', self::$origin_db);
    }

    protected function _before()
    {
        parent::_before();
        self::setOriginDbs();
    }

    protected function _after()
    {
        parent::_after();
        self::resetOriginDbs();
    }

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        self::setOriginDbs();
    }

    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
        self::resetOriginDbs();
    }

    public function testGetReply()
    {
        // 测试表中无匹配数据时
        $this->assertNull(MWechatOffiaccountReply::getReply(MWechatOffiaccountReply::SCENE_MESSAGE,
            'not found'));

        // 测试表中有文本回复匹配数据时
        $reply = MWechatOffiaccountReply::getReply(MWechatOffiaccountReply::SCENE_MESSAGE, 'text test');
        $this->assertIsArray($reply);
        $this->assertArrayHasKeys(['MsgType', 'Content'], $reply);
        $this->assertEquals(WechatOffiaccount::MSG_TYPE_TEXT, $reply['MsgType']);
        $this->assertEquals('test reply', $reply['Content']);

        // 测试表中有图片回复匹配数据时
        $reply = MWechatOffiaccountReply::getReply(MWechatOffiaccountReply::SCENE_MESSAGE, 'image test');
        $this->assertIsArray($reply);
        $this->assertArrayHasKeys(['MsgType', 'Image'], $reply);
        $this->assertEquals(WechatOffiaccount::MSG_TYPE_IMAGE, $reply['MsgType']);
        $this->assertIsArray($reply['Image']);
        $this->assertArrayHasKey('MediaId', $reply['Image']);
        $this->assertEquals('test_medal_id', $reply['Image']['MediaId']);

        // 测试表中有语音回复匹配数据时
        $reply = MWechatOffiaccountReply::getReply(MWechatOffiaccountReply::SCENE_MESSAGE, 'voice test');
        $this->assertIsArray($reply);
        $this->assertArrayHasKeys(['MsgType', 'Voice'], $reply);
        $this->assertEquals(WechatOffiaccount::MSG_TYPE_VOICE, $reply['MsgType']);
        $this->assertIsArray($reply['Voice']);
        $this->assertArrayHasKey('MediaId', $reply['Voice']);
        $this->assertEquals('test_medal_id', $reply['Voice']['MediaId']);

        // 测试必须完全匹配
        $this->assertEmpty(MWechatOffiaccountReply::getReply(MWechatOffiaccountReply::SCENE_MESSAGE, 'te'));
    }

    public function testFormatTextReply()
    {
        $content = 'test content';
        $reply = MWechatOffiaccountReply::formatTextReply($content);
        $this->assertIsArray($reply);
        $this->assertArrayHasKeys(['MsgType', 'Content'], $reply);
        $this->assertEquals(WechatOffiaccount::MSG_TYPE_TEXT, $reply['MsgType']);
        $this->assertEquals($content, $reply['Content']);
    }

    public function testFormatImageReply()
    {
        $media_id = 'test_media_id';
        $reply = MWechatOffiaccountReply::formatImageReply($media_id);
        $this->assertIsArray($reply);
        $this->assertArrayHasKeys(['MsgType', 'Image'], $reply);
        $this->assertEquals(WechatOffiaccount::MSG_TYPE_IMAGE, $reply['MsgType']);
        $this->assertIsArray($reply['Image']);
        $this->assertArrayHasKey('MediaId', $reply['Image']);
        $this->assertEquals($media_id, $reply['Image']['MediaId']);
    }

    public function testFormatVoiceReply()
    {
        $media_id = 'test_voice_media_id';
        $reply = MWechatOffiaccountReply::formatVoiceReply($media_id);
        $this->assertIsArray($reply);
        $this->assertArrayHasKeys(['MsgType', 'Voice'], $reply);
        $this->assertEquals(WechatOffiaccount::MSG_TYPE_VOICE, $reply['MsgType']);
        $this->assertIsArray($reply['Voice']);
        $this->assertArrayHasKey('MediaId', $reply['Voice']);
        $this->assertEquals($media_id, $reply['Voice']['MediaId']);
    }
}
