<?php

namespace tests\models;

use app\components\util\MUtils;
use app\forms\UserContext;
use app\models\Balance;
use app\models\TopupMenu;
use app\models\MAdminLogger;
use app\models\PayAccount;
use app\models\PayBadDebt;
use app\models\MMessageAssign;
use app\models\RechargeOrder;
use app\models\RechargeOrderDetail;
use app\models\ReturnModel;
use Exception;
use tests\components\UnitTestCase;
use tests\components\util\Equipment;
use yii\helpers\Json;
use yii\web\HttpException;
use Yii;

class RechargeOrderTest extends UnitTestCase
{
    const CCY_ID_NOT_EXIST = *********;
    const TEST_ORDER_ID = 1000;
    const TEST_ORDER_UID = 2;
    const TEST_PAYPAL_ORDER_UID = 3;
    const TEST_CCY_ID = 15;
    const TEST_DIAMOND = 15;

    const TEST_USER_ID_IOS_REFUND_BUYER = 2991;
    const TEST_USER_ID_IOS_REFUND_BUYER2 = 2992;

    private static $origin_db;
    private static $origin_paydb;
    private static $origin_logdb;

    protected function _before()
    {
        parent::_before();
        self::$origin_db = Yii::$app->db;
        self::$origin_paydb = Yii::$app->paydb;
        self::$origin_logdb = Yii::$app->logdb;
        Yii::$app->set('db', Yii::$app->sqlitedb);
        Yii::$app->set('paydb', Yii::$app->sqlite_paydb);
        Yii::$app->set('logdb', Yii::$app->sqlite_logdb);
    }

    protected function _after()
    {
        parent::_after();
        Yii::$app->set('db', self::$origin_db);
        Yii::$app->set('paydb', self::$origin_paydb);
        Yii::$app->set('logdb', self::$origin_logdb);
    }

    public function testFindForUpdate()
    {
        $order1 = new RechargeOrder([
            'uid' => 2222,
            'tid' => uniqid(time()),
            'cid' => 1,
            'price' => 1,
            'num' => 1 * DIAMOND_EXRATE,
            'ccy' => TopupMenu::DIAMOND,
            'status' => RechargeOrder::STATUS_CREATE,
            'type' => RechargeOrder::TYPE_ALIPAY,
            'origin' => RechargeOrder::ORIGIN_MOBILE_WEB,
        ]);
        if (!$order1->save()) {
            throw new Exception(MUtils::getFirstError($order1));
        }
        $order2 = new RechargeOrder([
            'uid' => 3333,
            'tid' => uniqid(time()),
            'cid' => 1,
            'price' => 1,
            'num' => 1 * DIAMOND_EXRATE,
            'ccy' => TopupMenu::DIAMOND,
            'status' => RechargeOrder::STATUS_SUCCESS,
            'type' => RechargeOrder::TYPE_ALIPAY,
            'origin' => RechargeOrder::ORIGIN_MOBILE_WEB,
        ]);
        if (!$order2->save()) {
            throw new Exception(MUtils::getFirstError($order2));
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $result = RechargeOrder::findForUpdate($order1->id);
            $this->assertNotNull($result);
            $this->assertEquals($order1->id, $result->id);
        } finally {
            $transaction->rollBack();
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $result = RechargeOrder::findForUpdate(['uid' => 3333, 'status' => [0, 1]]);
            $this->assertNotNull($result);
            $this->assertEquals($order2->id, $result->id);
        } finally {
            $transaction->rollBack();
        }
    }

    public function testCreateOrder()
    {
        $_SERVER['HTTP_USER_AGENT'] = 'MissEvanApp/5.6.3 (Android;10;vivo V2002A PD2019)';

        $user_context = new UserContext(self::TEST_USERAGENT, '*************');
        // 用户不存在
        $this->assertThrowsWithMessage(HttpException::class, '用户不存在', function () use ($user_context) {
            RechargeOrder::createOrder(self::CCY_ID_NOT_EXIST, self::TEST_USER_ID_NOT_EXIST,
                Equipment::Android, $user_context);
        }, 404);

        // 虚拟货币充值货物不存在
        $this->assertThrowsWithMessage(HttpException::class, '该虚拟货币充值货物不存在', function () use ($user_context) {
            RechargeOrder::createOrder(self::CCY_ID_NOT_EXIST, 2992, Equipment::Android, $user_context);
        }, 404);

        $TEST_USER_ID = 2992;
        $ccy = new TopupMenu();
        $ccy->setAttributes([
            'price' => 0,
            'num' => 0,
            'ccy' => TopupMenu::DIAMOND,
            'device' => Equipment::Android,
        ]);
        if (!$ccy->save()) {
            throw new Exception(MUtils::getFirstError($ccy));
        }
        // 钻石数不合法
        $this->assertThrowsWithMessage(HttpException::class, '充值钻石数不合法', function () use ($user_context, $ccy, $TEST_USER_ID) {
            Yii::$app->request->setBodyParams([
                'diamond' => 99999999,
            ]);
            RechargeOrder::createOrder($ccy->id, 2992, Equipment::Android,
                $user_context, RechargeOrder::TYPE_QQPAY);
        }, 400);

        // PayPal 最低充值限制
        $this->assertThrowsWithMessage(HttpException::class, 'PayPal 最低充值金额需大于或等于 10 元', function () use ($user_context, $ccy, $TEST_USER_ID) {
            Yii::$app->request->setBodyParams([
                'diamond' => 30,
            ]);
            RechargeOrder::createOrder($ccy->id, $TEST_USER_ID, Equipment::Android,
                $user_context, RechargeOrder::TYPE_PAYPAL);
        }, 400);

        // 正常情况
        $TEST_DIAMOND = 107;
        Yii::$app->request->setBodyParams([
            'diamond' => $TEST_DIAMOND,
        ]);
        $order = RechargeOrder::createOrder($ccy->id, $TEST_USER_ID, Equipment::Android,
            $user_context, RechargeOrder::TYPE_PAYPAL);
        $this->assertInstanceOf(RechargeOrder::class, $order);
        $this->assertEquals($TEST_DIAMOND, $order->num);
        $this->assertIsString($order->price);
        $this->assertEquals((string)round(($TEST_DIAMOND * RechargeOrder::DIAMOND_EXCHANGE_PRICE_RATE), 1),
            $order->price);
        $this->assertEquals(RechargeOrder::TYPE_PAYPAL, $order->type);
        $this->assertEquals(RechargeOrder::STATUS_CREATE, $order->status);
        $this->assertTrue(RechargeOrderDetail::find()->where([
            'id' => $order->id,
            'real_price' => Balance::profitUnitConversion($order->price, Balance::CONVERT_YUAN_TO_FEN),
        ])->exists());
    }

    public function testGetList()
    {
        $order1 = new RechargeOrder([
            'uid' => 8888,
            'tid' => uniqid(time()),
            'cid' => 1,
            'price' => 100,
            'num' => 100 * DIAMOND_EXRATE,
            'ccy' => TopupMenu::DIAMOND,
            'status' => RechargeOrder::STATUS_SUCCESS,
            'type' => RechargeOrder::TYPE_APPLEPAY,
            'origin' => RechargeOrder::ORIGIN_MOBILE_WEB,
        ]);
        if (!$order1->save()) {
            throw new Exception(MUtils::getFirstError($order1));
        }
        $order2 = new RechargeOrder([
            'uid' => 8888,
            'tid' => uniqid(time()),
            'cid' => 1,
            'price' => 100,
            'num' => 100 * DIAMOND_EXRATE,
            'ccy' => TopupMenu::DIAMOND,
            'status' => RechargeOrder::STATUS_CREATE,
            'type' => RechargeOrder::TYPE_APPLEPAY,
            'origin' => RechargeOrder::ORIGIN_MOBILE_WEB,
        ]);
        if (!$order2->save()) {
            throw new Exception(MUtils::getFirstError($order2));
        }
        $order3 = new RechargeOrder([
            'uid' => 8888,
            'tid' => uniqid(time()),
            'cid' => TopupMenu::getCustomDiamondNumId(),
            'price' => 100,
            'num' => 100 * DIAMOND_EXRATE,
            'ccy' => TopupMenu::DIAMOND,
            'status' => RechargeOrder::STATUS_SUCCESS,
            'type' => RechargeOrder::TYPE_CASH,
            'origin' => RechargeOrder::ORIGIN_MOBILE_WEB,
        ]);
        if (!$order3->save()) {
            throw new Exception(MUtils::getFirstError($order3));
        }
        $detail = new RechargeOrderDetail([
            'id' => $order3->id,
            'ip' => '127.0.0.1',
            'os' => Equipment::Web,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.67 Safari/537.36',
            'real_price' => 100,
            'more' => ['event_id' => 2233],
        ]);
        if (!$detail->save()) {
            throw new Exception(MUtils::getFirstError($detail));
        }

        $list = RechargeOrder::getList(8888, RechargeOrder::STATUS_SUCCESS);
        $this->assertInstanceOf(ReturnModel::class, $list);
        $this->assertCount(2, $list->Datas);
        foreach ($list->Datas as $order) {
            $original_id = $order->getOldAttribute('id');
            $this->assertNotEquals($original_id, $order->id);
            $this->assertEquals($original_id, RechargeOrder::getRealOrderId($order->id));
            if ($order->cid === TopupMenu::getCustomDiamondNumId() && $original_id === 3) {
                $this->assertEquals('活动奖励', $order->title);
                $this->assertEquals('发放成功', $order->status_msg);
            }
        }
        $this->assertEquals(1, $order1->delete());
        $this->assertEquals(1, $order2->delete());
        $this->assertEquals(1, $order3->delete());
        $this->assertEquals(1, $detail->delete());
    }

    public function testGetTodayAndCurrentMonthTopupSum()
    {
        $model = new RechargeOrder();
        $model->setAttributes([
            'price' => 100,
            'num' => 100 * DIAMOND_EXRATE,
            'tid' => uniqid(),
            'cid' => 1,
            'ccy' => 1,
            'uid' => self::TEST_PAYPAL_ORDER_UID,
            'status' => RechargeOrder::STATUS_SUCCESS,
            'type' => RechargeOrder::TYPE_PAYPAL,
            'origin' => RechargeOrder::ORIGIN_APP,
        ]);
        if (!$model->save()) {
            throw new Exception(MUtils::getFirstError($model));
        }
        $model2 = clone $model;
        $model2->setAttributes([
            'id' => null,
            'price' => 200,
            'num' => 200 * DIAMOND_EXRATE,
            'tid' => uniqid(),
        ], false);
        $model2->setIsNewRecord(true);
        if (!$model2->save()) {
            throw new Exception(MUtils::getFirstError($model2));
        }
        $time = $_SERVER['REQUEST_TIME'];
        RechargeOrder::updateAll(['confirm_time' => strtotime(date('Y-m-01 00:00:01', $time))],
            'uid = :uid AND cid = :cid AND num = :num',
            [
                ':uid' => self::TEST_PAYPAL_ORDER_UID,
                ':cid' => 1,
                ':num' => 100 * DIAMOND_EXRATE,
            ]
        );
        RechargeOrder::updateAll(['confirm_time' => strtotime(date('Y-m-d 00:00:01', $time))],
            'uid = :uid AND cid = :cid AND num = :num',
            [
                ':uid' => self::TEST_PAYPAL_ORDER_UID,
                ':cid' => 1,
                ':num' => 200 * DIAMOND_EXRATE,
            ]
        );
        $summary = RechargeOrder::getTodayAndCurrentMonthTopupSum(
            self::TEST_PAYPAL_ORDER_UID,
            RechargeOrder::TYPE_PAYPAL
        );
        $this->assertEquals(300, $summary['month_topup']);
        if (1 === (int)date('j')) {
            // 1 号的时候当月与当天总额相同
            $this->assertEquals(300, $summary['day_topup']);
        } else {
            $this->assertEquals(200, $summary['day_topup']);
        }
    }

    public function testGetBalanceField()
    {
        $data = [
            RechargeOrder::TYPE_APPLEPAY => 'ios',
            RechargeOrder::TYPE_CASH => 'ios',
            RechargeOrder::TYPE_ALIPAY => 'android',
            RechargeOrder::TYPE_WECHATPAY => 'android',
            RechargeOrder::TYPE_QQPAY => 'android',
            RechargeOrder::TYPE_TMALL_ANDROID => 'android',
            RechargeOrder::TYPE_TMALL_IOS => 'tmallios',
            RechargeOrder::TYPE_PAYPAL => 'paypal',
            RechargeOrder::TYPE_GOOGLE_PAY => 'googlepay',
        ];
        $order = new RechargeOrder();
        foreach ($data as $type => $field) {
            $order->type = $type;
            $this->assertEquals($field, $order->getBalanceField());
        }

        // 参数错误
        $this->assertThrowsWithMessage(Exception::class, '参数错误', function () use ($order) {
            $order->type = 100;
            $order->getBalanceField();
        });
    }

    public function testRefundIOS()
    {
        // 钻石余额充足
        $order = new RechargeOrder([
            'uid' => 2993,
            'tid' => 'test_ios_refund1',
            'type' => RechargeOrder::TYPE_APPLEPAY,
            'status' => RechargeOrder::STATUS_SUCCESS,
            'cid' => 1,
            'price' => 50,
            'num' => 500,
            'ccy' => 1,
            'origin' => 1,
        ]);
        if (!$order->save()) {
            throw new Exception(MUtils::getFirstError($order));
        }
        Balance::getByPk($order->uid);
        Balance::updateByPk($order->uid, [
            'ios' => 100,
            'android' => 50,
            'tmallios' => 150,
            'paypal' => 200,
            'googlepay' => 200,
            'all_topup' => 1200,
            'all_coin' => 700,
        ]);

        $order->refundIOS();
        $buyer = Balance::getByPk($order->uid);
        $this->assertEquals(700, $buyer->all_topup);
        $this->assertEquals(200, $buyer->all_coin);
        $this->assertEquals(200, $buyer->getTotalBalance());
        $this->assertEquals(200, $buyer->googlepay);
        $logs = Yii::getLogger()->messages;
        $contents = array_column($logs, '0');
        $this->assertContains(
            sprintf('iOS 退款：用户 %d iOS 钻石退款 %d，订单 ID %d', $order->uid, $order->num, $order->id),
            $contents
        );
        $this->assertEquals(1, MMessageAssign::deleteAll(
            'recuid = :recuid AND title = :title AND status = :status
                AND content = :content AND time >= :time',
            [
                ':recuid' => $order->uid,
                ':title' => 'iOS 钻石充值退款通知',
                ':status' => MMessageAssign::NOT_READ,
                ':content' => "尊敬的用户，由于您的 iOS 钻石充值订单 {$order->getOrderId()} 已收到苹果商店的退款，"
                    . "平台需回收该笔订单对应的钻石数额：{$order->num} 钻。请前往钱包页确认余额。如有问题，请联系客服。",
                ':time' => $_SERVER['REQUEST_TIME'] - self::LOG_SEARCH_TIME,
            ]));
        $this->assertEquals(1, MAdminLogger::deleteAll(
            'catalog = :catalog AND channel_id = :channel_id AND user_id = :user_id AND create_time >= :create_time',
            [
                ':catalog' => MAdminLogger::CATALOG_USER_LAUNCH_IOS_REFUND_SUCCESS,
                ':channel_id' => $order->id,
                ':user_id' => $order->uid,
                ':create_time' => $_SERVER['REQUEST_TIME'] - self::LOG_SEARCH_TIME,
            ]));

        // 钻石余额不足
        $order2 = new RechargeOrder([
            'uid' => 2994,
            'tid' => 'test_ios_refund2',
            'type' => RechargeOrder::TYPE_APPLEPAY,
            'status' => RechargeOrder::STATUS_SUCCESS,
            'cid' => 1,
            'price' => 50,
            'num' => 500,
            'ccy' => 1,
            'origin' => 1,
        ]);
        if (!$order2->save()) {
            throw new Exception(MUtils::getFirstError($order2));
        }
        Balance::getByPk($order2->uid);
        Balance::updateByPk($order2->uid, [
            'ios' => 100,
            'all_topup' => 600,
            'all_coin' => 100,
        ]);

        $order2->refundIOS();
        $buyer2 = Balance::getByPk($order2->uid);
        $this->assertEquals(0, $buyer2->getTotalBalance());
        $this->assertEquals(100, $buyer2->all_topup);
        $this->assertEquals(0, $buyer2->all_coin);
        $this->assertEquals(1, PayBadDebt::deleteAll(
            'user_id = :user_id AND order_id = :order_id AND debt = :debt AND create_time >= :time',
            [
                ':user_id' => $order2->uid,
                ':order_id' => $order2->id,
                ':debt' => $order2->num - 100,
                ':time' => $_SERVER['REQUEST_TIME'] - self::LOG_SEARCH_TIME,
            ]));
        $this->assertEquals(1, MMessageAssign::deleteAll(
            'recuid = :recuid AND title = :title AND status = :status
                AND content = :content AND time >= :time',
            [
                ':recuid' => $order2->uid,
                ':title' => 'iOS 钻石充值退款通知',
                ':status' => MMessageAssign::NOT_READ,
                ':content' => "尊敬的用户，由于您的 iOS 钻石充值订单 {$order2->getOrderId()} 已收到苹果商店的退款，"
                    . "平台需回收该笔订单对应的钻石数额：{$order2->num} 钻。请前往钱包页确认余额。如有问题，请联系客服。",
                ':time' => $_SERVER['REQUEST_TIME'] - self::LOG_SEARCH_TIME,
            ]));
        $logs = Yii::getLogger()->messages;
        $contents = array_column($logs, '0');
        $this->assertContains(
            sprintf('iOS 退款：用户 %d 钻石余额不足，未能足额扣减（退款 %d 钻，扣减 %d 钻），订单 ID %d', $order2->uid, 500, 100, $order2->id),
            $contents
        );
        $this->assertEquals(1, MAdminLogger::deleteAll(
            'catalog = :catalog AND channel_id = :channel_id AND user_id = :user_id AND create_time >= :create_time',
            [
                ':catalog' => MAdminLogger::CATALOG_USER_LAUNCH_IOS_REFUND_SUCCESS,
                ':channel_id' => $order2->id,
                ':user_id' => $order2->uid,
                ':create_time' => $_SERVER['REQUEST_TIME'] - self::LOG_SEARCH_TIME,
            ]));
    }

    public function testBatchGenerateCashOrder()
    {
        $user_context = new UserContext(self::TEST_USERAGENT, '*************');
        // 测试不存在该充值货币类型
        $this->assertThrowsWithMessage(HttpException::class, '不存在该充值货币类型', function () use ($user_context) {
            RechargeOrder::batchGenerateCashOrder(self::TEST_CCY_ID, [346286], self::TEST_DIAMOND,
                $user_context, RechargeOrder::TYPE_CASH, 'ceshi');
        });

        // 测试用户 ID 不存在
        $this->assertThrowsWithMessage(HttpException::class, '用户 ID: 999999 不存在', function () use ($user_context) {
            RechargeOrder::batchGenerateCashOrder(self::TEST_CCY_ID, [346286, 999999], self::TEST_DIAMOND,
                $user_context, RechargeOrder::TYPE_CASH, PayAccount::COIN_FIELD_IOS);
        });

        // 测试存在重复的用户 ID
        $this->assertThrowsWithMessage(HttpException::class, '存在重复的用户 ID', function () use ($user_context) {
            RechargeOrder::batchGenerateCashOrder(self::TEST_CCY_ID, [346286, 346286, 999999], self::TEST_DIAMOND,
                $user_context, RechargeOrder::TYPE_CASH, PayAccount::COIN_FIELD_IOS);
        });

        // 测试充值成功
        $data = RechargeOrder::batchGenerateCashOrder(self::TEST_CCY_ID, [346286], self::TEST_DIAMOND,
            $user_context, RechargeOrder::TYPE_CASH, PayAccount::COIN_FIELD_IOS);
        $this->assertIsArray($data);
        $this->assertArrayHasKeys(['ids', 'num'], $data);
        $exists = RechargeOrderDetail::find()->where(['user_agent' => self::TEST_USERAGENT])->exists();
        $this->assertTrue($exists);

        // 测试写入额外信息
        $more = ['event_id' => 1];
        $data = RechargeOrder::batchGenerateCashOrder(self::TEST_CCY_ID, [346286], self::TEST_DIAMOND,
            $user_context, RechargeOrder::TYPE_CASH, PayAccount::COIN_FIELD_IOS, $more);
        $this->assertIsArray($data);
        $this->assertArrayHasKeys(['ids', 'num'], $data);
        $actual_more = RechargeOrderDetail::find()->select('more')
            ->where(['id' => $data['ids'][0]])->scalar();
        $this->assertEquals(Json::encode($more), $actual_more);
    }

    public function testIsSuccess()
    {
        $this->assertTrue((new RechargeOrder(['status' => RechargeOrder::STATUS_SUCCESS]))->isSuccess());
        $this->assertFalse((new RechargeOrder(['status' => RechargeOrder::STATUS_CREATE]))->isSuccess());
    }

    public function testIsProcessing()
    {
        $this->assertTrue((new RechargeOrder(['status' => RechargeOrder::STATUS_CREATE]))->isProcessing());
        $this->assertFalse((new RechargeOrder(['status' => RechargeOrder::STATUS_SUCCESS]))->isProcessing());
    }

    public function testQueryDetail()
    {
        $detail = new RechargeOrderDetail([
            'id' => ********,
            'ip' => '127.0.0.1',
            'os' => Equipment::Web,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.67 Safari/537.36',
            'real_price' => 100,
        ]);
        if (!$detail->save()) {
            throw new Exception(MUtils::getFirstError($detail));
        }
        $this->assertNull(RechargeOrder::queryDetail(99999999));
        $detail = RechargeOrder::queryDetail($detail->id);
        $this->assertNotNull($detail);
        $this->assertEquals(1, RechargeOrderDetail::deleteByPk($detail->id));
    }

    public function testGenerateDetail()
    {
        // 测试客户端创建充值订单
        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/5.7.9 (Android;7.0;Meizu M6 M6)',
            'channel' => Equipment::CHANNEL_GOOGLE,
        ]);
        $recharge_order = new RechargeOrder([
            'id' => 1,
            'price' => 10,
            'detail' => [
                'user_agent' => self::TEST_USERAGENT,
                'ip' => '*************',
            ]
        ]);
        $result = $recharge_order->generateDetail();
        $this->assertEquals(self::TEST_USERAGENT, $result->user_agent);
        $this->assertEquals('*************', $result->ip);
        $this->assertArrayHasKey('channel', $result->more);
        $this->assertEquals(Equipment::CHANNEL_GOOGLE, $result->more['channel']);
    }

    public function testGetNumberList()
    {
        // 测试 ID 值错误
        $this->assertThrowsWithMessage(Exception::class, 'ID 值错误', function () {
            self::invokePrivateMethod(RechargeOrder::class, 'getNumberList', [-1, 1], new RechargeOrder(), true);
        });

        $data = self::invokePrivateMethod(RechargeOrder::class, 'getNumberList', [1, 1], new RechargeOrder(), true);
        $this->assertEquals([1], $data);

        $data = self::invokePrivateMethod(RechargeOrder::class, 'getNumberList', [1, 3], new RechargeOrder(), true);
        $this->assertEquals([1, 2, 3], $data);
    }

    public function testPriceInFen()
    {
        $order = new RechargeOrder(['price' => 23.5]);
        $this->assertEquals(2350, $order->priceInFen());
    }

    public function testSetOrderTitleAndStatusMsg()
    {
        $ID = 111111;
        $order = new RechargeOrder();
        $order->id = $ID;
        $order->cid = TopupMenu::getCustomDiamondNumId();
        $order->more = ['event_id' => 2233];
        $order->status = RechargeOrder::STATUS_SUCCESS;
        $order->setOrderTitleAndStatusMsg();
        $this->assertEquals('活动奖励', $order->title);
        $this->assertEquals('发放成功', $order->status_msg);

        $order = new RechargeOrder();
        $order->type = RechargeOrder::TYPE_VIP;
        $order->status = RechargeOrder::STATUS_SUCCESS;
        $order->setOrderTitleAndStatusMsg();
        $this->assertEquals('会员免费领取钻石', $order->title);
        $this->assertEquals('领取成功', $order->status_msg);
        $order->id = $ID;
        $order->cid = TopupMenu::getCustomDiamondNumId();
        $order->more = ['event_id' => 792, 'title' => '故障补偿', 'status_title' => '发放'];
        $order->status = RechargeOrder::STATUS_SUCCESS;
        $order->setOrderTitleAndStatusMsg();
        $this->assertEquals('故障补偿', $order->title);
        $this->assertEquals('发放成功', $order->status_msg);

        $order = new RechargeOrder();
        $order->id = $ID;
        $order->status = RechargeOrder::STATUS_ERROR;
        $order->setOrderTitleAndStatusMsg();
        $this->assertEquals('购买钻石', $order->title);
        $this->assertEquals('购买失败', $order->status_msg);
    }
}
