<?php

namespace tests\models;

use app\models\MThirdPartyQidianTask;
use tests\components\UnitTestCase;
use tests\components\util\Tools;

class MThirdPartyQidianTaskTest extends UnitTestCase
{
    public function testCallback()
    {
        // 测试回调成功
        Tools::registerRemoteApiResponseFunc(MThirdPartyQidianTask::CALLBACK_URL, function () {
            return ['Result' => MThirdPartyQidianTask::SUCCESS_CODE];
        });
        $data = MThirdPartyQidianTask::callback('test|aa');
        $this->assertTrue($data);

        // 测试回调失败
        Tools::registerRemoteApiResponseFunc(MThirdPartyQidianTask::CALLBACK_URL, function () {
            return ['Result' => -1];
        });
        $data = MThirdPartyQidianTask::callback('test|aa');
        $this->assertFalse($data);

        // 测试参数错误
        Tools::registerRemoteApiResponseFunc(MThirdPartyQidianTask::CALLBACK_URL, function () {
            return ['Result' => MThirdPartyQidianTask::SUCCESS_CODE];
        });
        $data = MThirdPartyQidianTask::callback('testaa');
        $this->assertFalse($data);
    }

    public function testBuildSign()
    {
        $test_params = [
            'access_token' => 'sample_access_token',
            'task_id' => 'T20241001',
            'token' => 'sample_user_token',
            'request_id' => '20241001_sample_callback_request',
            'timestamp' => '1727712000000',
        ];
        $assert_sign = '2a93a55d6c7b34d2668401a66f950e31105fcbfe';

        $actual = self::invokePrivateMethod(MThirdPartyQidianTask::class, 'buildSign', $test_params, null, false);
        $this->assertEquals($assert_sign, $actual);
    }
}
