<?php

namespace tests\models;

use app\models\MThirdPartyQQMusicTask;
use tests\components\UnitTestCase;
use tests\components\util\Tools;

class MThirdPartyQQMusicTaskTest extends UnitTestCase
{
    public function testCallback()
    {
        Tools::registerRemoteApiResponseFunc(MThirdPartyQQMusicTask::CALLBACK_URL, function () {
            return ['code' => MThirdPartyQQMusicTask::TASK_SUCCESS_CODE];
        });

        // 测试回调成功
        $data = MThirdPartyQQMusicTask::callback('test');
        $this->assertTrue($data);
    }

    public function testBuildSign()
    {
        $test_params = [
            'Qy-Co-Api-Appid' => '123456789',
            'Qy-Co-Api-Nonce-Str' => 'nBXALNpRD9rWay3U5KT2wxxeeFlJi3mF',
            'Qy-Co-Api-Timestamp' => 1692254932,
        ];
        $test_sign_key = 'cjerlj9dqrqadk1vkjgg';
        $assert_sign = '545013C4E861CBD3BB72ADE1B450C9B92AF9000F7B7A795233764D6C7258BF70';

        $task = new MThirdPartyQQMusicTask();
        $actual = self::invokePrivateMethod(MThirdPartyQQMusicTask::class, 'buildSign', [$test_params, $test_sign_key], $task, true);
        $this->assertEquals($assert_sign, $actual);
    }
}
