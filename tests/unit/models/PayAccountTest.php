<?php

namespace tests\models;

use app\models\PayAccount;
use app\models\TransactionLog;
use Exception;
use missevan\util\MUtils;
use tests\components\UnitTestCase;
use Yii;

class PayAccountTest extends UnitTestCase
{
    const TEST_USER_ID = 987;
    private static $ACCOUNTS = [];

    private static $origin_db;
    private static $origin_paydb;

    protected function _before()
    {
        parent::_before();
        self::$origin_db = Yii::$app->db;
        self::$origin_paydb = Yii::$app->paydb;
        Yii::$app->set('db', Yii::$app->sqlitedb);
        Yii::$app->set('paydb', Yii::$app->sqlite_paydb);
    }

    protected function _after()
    {
        parent::_after();
        Yii::$app->set('db', self::$origin_db);
        Yii::$app->set('paydb', self::$origin_paydb);
    }

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        self::$ACCOUNTS = [
            new PayAccount(['id' => 5, 'balance' => 20, 'type' => 20]),
            new PayAccount(['id' => 4, 'balance' => 20, 'type' => 13]),
            new PayAccount(['id' => 3, 'balance' => 20, 'type' => 12]),
            new PayAccount(['id' => 2, 'balance' => 20, 'type' => 11]),
            new PayAccount(['id' => 1, 'balance' => 15, 'type' => 10]),
        ];

        Yii::$app->sqlite_paydb->createCommand(sprintf('
            INSERT INTO %s
            (user_id, tid, account_amount, balance, scope, type, withhold_order, expire_time, create_time, modified_time, status)
            VALUES
            (666, *********, 1000, 300, 1, 10, 1, UNIX_TIMESTAMP("2023-11-30 12:00:00"), UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1),
            (666, *********, 1000, 500, 1, 10, 1, UNIX_TIMESTAMP("2023-01-30 12:00:00"), UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1);
        ', PayAccount::tableName()))->execute();
    }

    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
        PayAccount::deleteAll(['user_id' => self::TEST_USER_ID]);
        Yii::$app->sqlite_paydb->createCommand(sprintf('DELETE FROM %s WHERE user_id = 666;', PayAccount::tableName()))->execute();
    }

    public function testGetBalance()
    {
        $test_cases = [
            [
                'accounts' => [
                    new PayAccount(['id' => 1, 'balance' => 20, 'type' => 10]),
                    new PayAccount(['id' => 2, 'balance' => 20, 'type' => 11]),
                    new PayAccount(['id' => 3, 'balance' => 20, 'type' => 12]),
                    new PayAccount(['id' => 4, 'balance' => 20, 'type' => 13]),
                    new PayAccount(['id' => 5, 'balance' => 15, 'type' => 20]),
                ],
                'balance' => 95,
            ], [
                'accounts' => [
                    new PayAccount(['id' => 1, 'balance' => 20, 'type' => 10]),
                ],
                'balance' => 20,
            ], [
                'accounts' => [
                    new PayAccount(['id' => 1, 'balance' => 20, 'type' => 10]),
                    new PayAccount(['id' => 2, 'balance' => 8, 'type' => 11]),
                ],
                'balance' => 28,
            ],
        ];

        foreach ($test_cases as $test_case) {
            $balance = PayAccount::getBalance($test_case['accounts']);
            $this->assertEquals($test_case['balance'], $balance);
        }
    }

    public function testChangeAccount()
    {
        $test_cases = [
            [
                'price' => 0,
                'expected' => [
                    'balances' => [],
                    'remaining' => 0,
                    'total_fee' => 0,
                    'income' => 0,
                ],
            ], [
                'price' => 50,
                'expected' => [
                    'balances' => [0, 0, 10],
                    'remaining' => 0,
                    'total_fee' => 0.75,
                    'income' => 4.25,
                ],
            ], [
                'price' => 100,
                'expected' => [
                    'balances' => [0, 0, 0, 0, 0],
                    'remaining' => 5,
                    'total_fee' => 0.9,
                    'income' => 8.6,
                ],
            ],
        ];

        foreach ($test_cases as $test_case) {
            /** @var $change_accounts PayAccount[] */
            [$change_accounts, $remaining] = PayAccount::getChangeAccount(self::$ACCOUNTS, $test_case['price']);
            $expected = $test_case['expected'];
            $balances = $expected['balances'];
            $this->assertEquals($expected['remaining'], $remaining);
            $this->assertEquals(count($balances), count($change_accounts));
            $this->assertLessThan(0.000001, abs((new TransactionLog(['type' => TransactionLog::TYPE_LIVE]))->calcTax($change_accounts) - $expected['total_fee']));
            foreach ($change_accounts as $i => $change_account) {
                $this->assertEquals($balances[$i], $change_account->getNewBalance());
            }
        }

        $this->assertThrowsWithMessage(Exception::class, '价格不能为负数', function () {
            PayAccount::getChangeAccount(self::$ACCOUNTS, -10);
        });
    }

    public function testGetRebateDetail()
    {
        $test_cases = [
            [
                'params' => [
                    // android, tmallios, paypal, ios, googpay 消费钻石
                    'currencies' => [10, 1, 500, 40, 0],
                    'rebate' => 0,
                ],
                'expected' => [0, 0, 0, 0, 0],
            ], [
                'params' => [
                    'currencies' => [1, 0, 0, 1199999, 0],
                    'rebate' => 240000,
                ],
                'expected' => [0, 0, 0, 240000, 0],
            ], [
                'params' => [
                    'currencies' => [1199999, 0, 0, 1, 0],
                    'rebate' => 240000,
                ],
                'expected' => [239999, 0, 0, 1, 0],
            ], [
                'params' => [
                    'currencies' => [0, 0, 0, 1200000, 0],
                    'rebate' => 240000,
                ],
                'expected' => [0, 0, 0, 240000, 0],
            ],
        ];
        foreach ($test_cases as $test_case) {
            ['currencies' => $currencies, 'rebate' => $rebate] = $test_case['params'];
            $expected = $test_case['expected'];
            $actual = PayAccount::getRebateDetail($currencies, $rebate);
            $this->assertEquals($expected, $actual);
            $this->assertEquals($rebate, array_sum($actual));
        }
    }

    public function testUpdateAccounts()
    {
        $nowstamp = $_SERVER['REQUEST_TIME'];
        $account1 = new PayAccount([
            'user_id' => self::TEST_USER_ID,
            'tid' => 9999,
            'account_amount' => 500,
            'balance' => 500,
            'scope' => PayAccount::SCOPE_LIVE,
            'type' => 0,
            'withhold_order' => 0,
            'expire_time' => $nowstamp + FIVE_MINUTE,
            'create_time' => $nowstamp,
            'modified_time' => $nowstamp,
        ]);
        if (!$account1->save()) {
            throw new Exception(MUtils::getFirstError($account1));
        }
        $account2 = new PayAccount([
            'user_id' => self::TEST_USER_ID,
            'tid' => 9999,
            'account_amount' => 1000,
            'balance' => 1000,
            'scope' => PayAccount::SCOPE_LIVE,
            'type' => 1,
            'withhold_order' => 1,
            'expire_time' => $nowstamp + FIVE_MINUTE,
            'create_time' => $nowstamp,
            'modified_time' => $nowstamp,
        ]);
        if (!$account2->save()) {
            throw new Exception(MUtils::getFirstError($account2));
        }

        $this->subTestUpdateAccountsCost($account1, $account2);
        $this->subTestUpdateAccountsRefund($account1, $account2);
    }

    private function subTestUpdateAccountsCost(PayAccount $account1, PayAccount $account2)
    {
        $account1->consume_amount = 500;
        $account2->consume_amount = 1200;

        $this->assertThrowsWithMessage(Exception::class, '同时有多个订单正在被处理', function () use ($account1, $account2) {
            $transaction = PayAccount::getDb()->beginTransaction();
            try {
                PayAccount::updateAccounts([$account1, $account2]);
                $transaction->commit();
            } catch (Exception $e) {
                $transaction->rollBack();
                throw $e;
            }
        });

        $account2->consume_amount = 700;
        $transaction = PayAccount::getDb()->beginTransaction();
        try {
            PayAccount::updateAccounts([$account1, $account2]);
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            throw $e;
        }

        $acc1 = PayAccount::findOne(['id' => $account1->id]);
        $this->assertNotNull($acc1);
        $this->assertEquals($account1->balance - $account1->consume_amount, $acc1->balance);

        $acc2 = PayAccount::findOne(['id' => $account2->id]);
        $this->assertNotNull($acc2);
        $this->assertEquals($account2->balance - $account2->consume_amount, $acc2->balance);

        $account1->balance = $acc1->balance;
        $account2->balance = $acc2->balance;
    }

    private function subTestUpdateAccountsRefund(PayAccount $account1, PayAccount $account2)
    {
        $account1->consume_amount = -800;
        $account2->consume_amount = -200;

        $transaction = PayAccount::getDb()->beginTransaction();
        try {
            PayAccount::updateAccounts([$account1, $account2]);
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            throw $e;
        }

        $acc1 = PayAccount::findOne(['id' => $account1->id]);
        $this->assertNotNull($acc1);
        $this->assertEquals($account1->balance + abs($account1->consume_amount), $acc1->balance);

        $acc2 = PayAccount::findOne(['id' => $account2->id]);
        $this->assertNotNull($acc2);
        $this->assertEquals($account2->balance + abs($account2->consume_amount), $acc2->balance);
    }

    public function testCalFee()
    {
        $this->assertEquals(3.26, PayAccount::calFee(100, 0.03257));
    }

    public function testGetCoinField()
    {
        $type_cointype_map = [
            PayAccount::ATTR_TYPE_OFFSET_MAP[PayAccount::ATTR_NOBLE_COIN] + PayAccount::TYPE_COIN_INDEX_ANDROID => 'noble_android',
            PayAccount::ATTR_TYPE_OFFSET_MAP[PayAccount::ATTR_NOBLE_COIN] + PayAccount::TYPE_COIN_INDEX_TMALL_IOS => 'noble_tmallios',
            PayAccount::ATTR_TYPE_OFFSET_MAP[PayAccount::ATTR_NOBLE_COIN] + PayAccount::TYPE_COIN_INDEX_PAYPAL => 'noble_paypal',
            PayAccount::ATTR_TYPE_OFFSET_MAP[PayAccount::ATTR_NOBLE_COIN] + PayAccount::TYPE_COIN_INDEX_IOS => 'noble_ios',
            PayAccount::ATTR_TYPE_OFFSET_MAP[PayAccount::ATTR_NOBLE_COIN] + PayAccount::TYPE_COIN_INDEX_GOOGLEPAY => 'noble_googlepay',
            PayAccount::ATTR_TYPE_OFFSET_MAP[PayAccount::ATTR_NOBLE_FREE_COIN] + PayAccount::TYPE_COIN_INDEX_ANDROID => 'noble_free_android',
            PayAccount::ATTR_TYPE_OFFSET_MAP[PayAccount::ATTR_NOBLE_FREE_COIN] + PayAccount::TYPE_COIN_INDEX_TMALL_IOS => 'noble_free_tmallios',
            PayAccount::ATTR_TYPE_OFFSET_MAP[PayAccount::ATTR_NOBLE_FREE_COIN] + PayAccount::TYPE_COIN_INDEX_PAYPAL => 'noble_free_paypal',
            PayAccount::ATTR_TYPE_OFFSET_MAP[PayAccount::ATTR_NOBLE_FREE_COIN] + PayAccount::TYPE_COIN_INDEX_IOS => 'noble_free_ios',
            PayAccount::ATTR_TYPE_OFFSET_MAP[PayAccount::ATTR_NOBLE_FREE_COIN] + PayAccount::TYPE_COIN_INDEX_GOOGLEPAY => 'noble_free_googlepay',
        ];
        foreach ($type_cointype_map as $type => $expected_cointype) {
            $pay_account = new PayAccount(['type' => $type]);
            $this->assertEquals($expected_cointype, $pay_account->getCoinField());
        }

        $this->assertThrowsWithMessage(Exception::class, 'wrong pay account: type[2], id[666]', function () {
            $pay_account = new PayAccount();
            $pay_account->setAttributes([
                'id' => 666,
                'type' => 2,
            ], false);
            $pay_account->getCoinField();
        });
    }

    public function testUpdateNobleCoinExpireTime()
    {
        $_SERVER['REQUEST_TIME'] = strtotime('2023-12-21 19:00:00');
        $expire_time = strtotime('2024-01-31 18:00:00');
        PayAccount::updateNobleCoinExpireTime(666, $expire_time);
        $this->assertEquals(1, PayAccount::find()->where(['user_id' => 666, 'expire_time' => $expire_time])->count());
    }

    public function testGetExpireBalanceByTime()
    {
        $_SERVER['REQUEST_TIME'] = strtotime('2023-12-24 16:30:00');
        $account = new PayAccount([
            'user_id' => 666666,
            'tid' => 111,
            'account_amount' => 1000,
            'balance' => 1000,
            'scope' => PayAccount::SCOPE_LIVE,
            'type' => 1,
            'withhold_order' => 1,
            'expire_time' => strtotime('2023-12-24 12:00:00'),
            'create_time' => $_SERVER['REQUEST_TIME'],
            'modified_time' => $_SERVER['REQUEST_TIME'],
        ]);
        if (!$account->save()) {
            throw new Exception(MUtils::getFirstError($account));
        }
        $account2 = new PayAccount([
            'user_id' => 777777,
            'tid' => 222,
            'account_amount' => 1000,
            'balance' => 500,
            'scope' => PayAccount::SCOPE_LIVE,
            'type' => 1,
            'withhold_order' => 2,
            'expire_time' => strtotime('2023-12-25 12:00:00'),
            'create_time' => $_SERVER['REQUEST_TIME'],
            'modified_time' => $_SERVER['REQUEST_TIME'],
        ]);
        if (!$account2->save()) {
            throw new Exception(MUtils::getFirstError($account2));
        }
        $account3 = new PayAccount([
            'user_id' => ********,
            'tid' => 333,
            'account_amount' => 2000,
            'balance' => 800,
            'scope' => PayAccount::SCOPE_LIVE,
            'type' => 1,
            'withhold_order' => 2,
            'expire_time' => $_SERVER['REQUEST_TIME'] - PayAccount::NOBLE_COIN_FROZEN_PERIOD - ONE_HOUR,
            'create_time' => $_SERVER['REQUEST_TIME'],
            'modified_time' => $_SERVER['REQUEST_TIME'],
        ]);
        if (!$account3->save()) {
            throw new Exception(MUtils::getFirstError($account3));
        }

        $result = PayAccount::getExpireBalanceByTime(strtotime('2023-12-24 23:59:59'), strtotime('2023-12-25 23:59:59'));
        $this->assertCount(1, $result);
        $this->assertArrayHasKeys(['user_id', 'total_balance'], $result[0]);
        $this->assertEquals(777777, $result[0]['user_id']);
        $this->assertEquals(500, $result[0]['total_balance']);

        $result = PayAccount::getExpireBalanceByTime(
            $_SERVER['REQUEST_TIME'] - ONE_DAY,
            $_SERVER['REQUEST_TIME']
            ,
            1
        );
        $this->assertCount(1, $result);
        $this->assertArrayHasKeys(['user_id', 'total_balance'], $result[0]);
        $this->assertEquals(********, $result[0]['user_id']);
        $this->assertEquals(800, $result[0]['total_balance']);
    }

}
