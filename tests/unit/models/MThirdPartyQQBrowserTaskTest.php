<?php

namespace tests\models;

use app\models\MThirdPartyQQBrowserTask;
use tests\components\UnitTestCase;
use tests\components\util\Tools;

class MThirdPartyQQBrowserTaskTest extends UnitTestCase
{
    public function testCallback()
    {
        Tools::registerRemoteApiResponseFunc(MThirdPartyQQBrowserTask::CALLBACK_URL_QQ_BROWSER, function () {
            return ['msg' => MThirdPartyQQBrowserTask::QQ_BROWSER_REQUEST_SUCCESS_MSG];
        });

        // 测试回调成功
        $data = MThirdPartyQQBrowserTask::callback('test');
        $this->assertTrue($data);
    }

    public function testEncryptToken()
    {
        $test_token = 'test';
        $ad_track = new MThirdPartyQQBrowserTask();
        $encrypt_token = self::invokePrivateMethod(MThirdPartyQQBrowserTask::class, 'encryptToken',
            $test_token, $ad_track);
        // 断言解密后是 test
        $encrypt_token .= str_repeat('=', (4 - strlen($encrypt_token) % 4) % 4);
        $base64_url_decode_token = base64_decode(strtr($encrypt_token, '-_', '+/'));
        $key_length = strlen(MThirdPartyQQBrowserTask::QQ_BROWSER_SIGN_KEY);
        $method = 'aes-' . ($key_length * 8) . '-cfb';
        $iv = substr($base64_url_decode_token, 0, MThirdPartyQQBrowserTask::AES_BLOCK_SIZE);
        $ciphertext = substr($base64_url_decode_token, MThirdPartyQQBrowserTask::AES_BLOCK_SIZE);
        $decrypt_token = openssl_decrypt($ciphertext, $method, MThirdPartyQQBrowserTask::QQ_BROWSER_SIGN_KEY,
            OPENSSL_RAW_DATA, $iv);
        $this->assertEquals($test_token, $decrypt_token);
    }
}
