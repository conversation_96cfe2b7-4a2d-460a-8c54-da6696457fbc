<?php

namespace tests\models;

use app\models\MThirdPartyQuarkTask;
use tests\components\UnitTestCase;
use tests\components\util\Tools;
use Yii;

class MThirdPartyQuarkTaskTest extends UnitTestCase
{
    public function testCallback()
    {
        Tools::registerRemoteApiResponseFunc(MThirdPartyQuarkTask::CALLBACK_URL, function () {
            return ['code' => MThirdPartyQuarkTask::SUCCESS_CODE];
        });

        // 测试回调成功
        $data = MThirdPartyQuarkTask::callback('test');
        $this->assertTrue($data);

        Tools::registerRemoteApiResponseFunc(MThirdPartyQuarkTask::CALLBACK_URL, function () {
            return ['code' => 'TASK_NOT_FOUND'];
        });

        // 测试回调失败
        $data = MThirdPartyQuarkTask::callback('test');
        $this->assertFalse($data);
    }
}
