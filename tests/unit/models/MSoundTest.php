<?php

namespace tests\models;

use app\components\util\Go;
use app\components\util\MUtils;
use app\forms\TmallPayForm;
use app\models\Catalog;
use app\models\Drama;
use app\models\MAlbum;
use app\models\MAllowAlbum;
use app\models\MLikeSound;
use app\models\Mowangskuser;
use app\models\MSound;
use app\models\MSoundAlbumMap;
use app\models\MTagSoundMap;
use app\models\MUserUnlockElement;
use app\models\MUserVip;
use app\models\PointDetailLog;
use app\models\SoundVideo;
use missevan\util\MUtils as MUtils2;
use tests\components\util\DataBus;
use tests\components\util\Tools;
use tests\components\UnitTestCase;
use tests\components\util\Data;
use tests\components\web\Request;
use Yii;
use yii\web\HttpException;

class MSoundTest extends UnitTestCase
{
    const TEST_SOUND_ID = 1;
    const SOUND_ID_NOT_EXIST = 999999;

    private static $origin_db;
    private static $origin_db1;
    private static $origin_paydb;
    private static $album;
    private static $normal_sound_id;
    private static $review_sound_id;
    private static $live_sound;
    private static $live_sound_id;
    private static $test_delete_album_id1;
    private static $test_delete_album_id2;
    private static $test_sound_video;
    private static $test_sound_1;
    private static $test_sound_2;
    private static $test_sound_video_1;
    private static $test_sound_id_no_video;
    private static $test_sound_id_has_video;

    private static function setOriginDbs(): void
    {
        self::$origin_db = Yii::$app->db;
        self::$origin_db1 = Yii::$app->db1;
        self::$origin_paydb = Yii::$app->paydb;
        Yii::$app->set('db', Yii::$app->sqlitedb);
        Yii::$app->set('db1', Yii::$app->sqlitedb1);
        Yii::$app->set('paydb', Yii::$app->sqlite_paydb);
    }

    private static function resetOriginDbs(): void
    {
        Yii::$app->set('db', self::$origin_db);
        Yii::$app->set('db1', self::$origin_db1);
        Yii::$app->set('paydb', self::$origin_paydb);
    }

    protected function _before()
    {
        parent::_before();
        self::setOriginDbs();
    }

    protected function _after()
    {
        parent::_after();
        self::resetOriginDbs();
    }

    /**
     * 创建测试相关数据
     */
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        self::setOriginDbs();
        self::clearData();

        // 创建普通测试音频
        $normal_sound = Data::createSound([
            'user_id' => self::TEST_USER2_ID,
            'type' => MSound::TYPE_NORMAL,
            'checked' => MSound::CHECKED_PASS
        ]);
        self::$normal_sound_id = (int)$normal_sound->id;

        $test_sound = Data::createSound([]);
        self::$test_sound_id_no_video = $test_sound->id;
        $test_sound = Data::createSound([]);
        self::$test_sound_id_has_video = $test_sound->id;
        Data::createSoundVideo([
            'sid' => self::$test_sound_id_has_video,
            'video_url' => 'oss://video/202110/15/test.mp4',
            'checked' => SoundVideo::CHECKED_PASS,
        ]);

        // 创建再审状态音频
        $review_sound = Data::createSound([
            'user_id' => self::TEST_USER2_ID,
            'type' => MSound::TYPE_NORMAL
        ]);
        self::$review_sound_id = (int)$review_sound->id;

        // 创建测试用音单
        self::$album = Data::createTestAlbum();
        Data::createSoundAlbumMap(self::$normal_sound_id, (int)self::$album->id);

        Data::createCheckedReviewSound([
            'sound_id' => self::$review_sound_id,
            'soundstr' => 'review_soundstr'
        ]);
        // 创建直播回放音频
        self::$live_sound = Data::createSound([
            'user_id' => self::TEST_USER2_ID,
            'type' => MSound::TYPE_LIVE
        ]);
        self::$live_sound_id = (int)self::$live_sound->id;
        // 创建用户喜欢的音频
        Data::createMLikeSound([
            'user_id' => self::TEST_USER_ID,
            'sound_id' => self::$review_sound_id
        ]);

        // 创建音频所绑定的视频测试数据
        self::$test_sound_video = Data::createSoundVideo([
            'sid' => 10,
            'checked' => SoundVideo::CHECKED_PASS,
            'video_url' => 'oss://video/test.mp4',
        ]);

        // 创建测试音频
        self::$test_sound_1 = Data::createSound([
            'user_id' => self::TEST_USER3_ID,
            'type' => MSound::TYPE_NORMAL,
            'checked' => MSound::CHECKED_PASS
        ]);
        self::$test_sound_2 = Data::createSound([
            'user_id' => self::TEST_USER3_ID,
            'type' => MSound::TYPE_NORMAL,
        ]);
        // 创建音频所绑定的视频测试数据
        self::$test_sound_video_1 = Data::createSoundVideo([
            'sid' => self::$test_sound_2->id,
            'checked' => SoundVideo::CHECKED_PASS,
            'video_url' => 'oss://video/test.mp4',
        ]);

        // 创建 8 条小鱼干投食记录，用于测试音频每获得 10 个投食 UP 主就会获得 5 个小鱼干的情况
        $NUM = 8;
        for ($i = 0; $i < $NUM; $i++) {
            Data::createMPointFeed(['sound_id' => self::$normal_sound_id]);
        }
    }

    /**
     * 删除测试中创建的相关数据
     */
    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
        self::setOriginDbs();
        self::clearData();
        self::resetOriginDbs();
    }

    /**
     * 清除测试中生成的数据
     */
    public static function clearData()
    {
        // 清除相似音频缓存数据
        $recommend_key = MUtils::generateCacheKey(KEY_SOUND_PLAY_RECOMMEND_STRATEGY_SOUNDS, 0,
            self::TEST_SOUND_ID);
        Yii::$app->memcache->delete($recommend_key);

        $catalog_key = MUtils::generateCacheKey(KEY_CATALOG_GLOBAL_SOUNDS, Catalog::CATALOG_ID_MUSIC, 0);
        Yii::$app->memcache->delete($catalog_key);

        $ts_lock_user1 = Yii::$app->redis->generateKey(TS_SOUND, self::$normal_sound_id, self::TEST_USER_ID);
        Yii::$app->redis->delete(TS_SOUND, $ts_lock_user1);
        $ts_lock_user3 = Yii::$app->redis->generateKey(TS_SOUND, self::$normal_sound_id, self::TEST_USER3_ID);
        Yii::$app->redis->delete(TS_SOUND, $ts_lock_user3);

        MSound::updateByPk(self::TEST_SOUND_ID, ['refined' => 0]);
        if (self::$album) {
            MAlbum::deleteAll(['id' => self::$album->id]);
            MSoundAlbumMap::deleteAll(['album_id' => self::$album->id]);
            MAllowAlbum::removeAlbum(self::$album->id);
        }
        $test_album_ids = [];
        if (self::$test_delete_album_id1) {
            $test_album_ids[] = self::$test_delete_album_id1;
        }
        if (self::$test_delete_album_id2) {
            $test_album_ids[] = self::$test_delete_album_id2;
        }
        if (!empty($test_album_ids)) {
            MAlbum::deleteAll(['id' => $test_album_ids]);
            MSoundAlbumMap::deleteAll(['album_id' => $test_album_ids]);
        }
        if (!empty($test_album_ids)) {
            MAlbum::deleteAll(['id' => $test_album_ids]);
        }
        // 删除测试用户的所有音频
        MSound::deleteAll(['user_id' => self::TEST_USER2_ID]);
        // 删除用户喜欢的音频
        MLikeSound::deleteAll(['sound_id' => self::$review_sound_id, 'user_id' => self::TEST_USER_ID]);

        // 删除音频测试数据
        if (self::$test_sound_1 || self::$test_sound_2) {
            MSound::deleteAll(['id' => array_filter(
                [self::$test_sound_1->id ?? null,
                    self::$test_sound_2->id ?? null]
            )]);
        }

        // 删除音频所绑定的视频测试数据
        if (self::$test_sound_video || self::$test_sound_video_1) {
            SoundVideo::deleteAll(['id' => array_filter(
                [self::$test_sound_video->id ?? null,
                    self::$test_sound_video_1->id ?? null]
            )]);
        }
        SoundVideo::deleteAll(['sid' => [10, self::$test_sound_id_has_video]]);
        SoundVideo::deleteAll(['id' => [self::$test_sound_id_no_video, self::$test_sound_id_has_video]]);

        // 删除小鱼干投食记录
        MTagSoundMap::deleteAll(['sound_id' => self::$normal_sound_id]);
        DataBus::clearQueue();
    }

    public function testGetSearchSuggest()
    {
        $data = MSound::getSearchSuggest('哈');
        $this->assertIsArray($data);
        $this->assertArrayHasKeys(['request_id', 'suggestions'], $data);
        $this->assertStringContainsString('哈', $data['suggestions'][0]);
    }

    public function testGetSearch()
    {
        Tools::registerDramaMockResponseFunc('/rpc/api/get-drama-paytype-by-sound', function () {
            return [
                '1' => [
                    'drama_id' => 1,
                    'pay_type' => Drama::DRAMA_FREE,
                    'is_interactive' => false,
                    'episode_vip' => Drama::EPISODE_VIP_LIMIT,
                ],
            ];
        });
        $free = 1;
        $return = MSound::getSearch('哈', 1, 20, self::TEST_USER_ID, 0, $free);
        $this->assertIsArray($return);
        $this->assertArrayHasKey('Datas', $return);
        $this->assertGreaterThanOrEqual(1, count($return['Datas']));
        $item = current($return['Datas']);
        $this->assertArrayNotHasKeys(['cover_image', 'soundurl', 'soundurl_128', 'soundurl_32', 'soundurl_64'], $item);
        $this->assertRegExp('/^https?:\/\/.+/', $item['front_cover']);
        $this->assertCount(1, array_unique(array_column($return['Datas'], 'pay_type')));
        $this->assertEquals(MSound::SOUND_FREE, (int)$item['pay_type']);

        $return = MSound::getSearch('哈', 1, 20, self::TEST_USER_ID);
        $this->assertIsArray($return);
        $this->assertArrayHasKey('Datas', $return);
        $this->assertGreaterThanOrEqual(1, count($return['Datas']));

        // 测试筛选分区
        $TEST_SEARCH_CATALOG_MUSIC_ID = 1;
        Yii::$app->memcache->delete(MUtils::generateCacheKey(KEY_SON_CATALOG, Catalog::CATALOG_SOUND_ID));
        Yii::$app->memcache->delete(MUtils::generateCacheKey(KEY_SON_CATALOG, Catalog::CATALOG_ID_DRAMA));
        $return = MSound::getSearch('测试', 1, 20, 0, 0, 0, 0, $TEST_SEARCH_CATALOG_MUSIC_ID);
        $this->assertIsArray($return);
        $this->assertArrayHasKeys(['Datas', 'pagination'], $return);
        foreach ($return['Datas'] as $data) {
            $this->assertEquals(Catalog::CATALOG_ID_STORY_SONG, $data['catalog_id']);
        }

        // 测试过滤会员音频
        Tools::registerRpcApiResponseFunc(Go::API_DISCOVERY_SEARCH, function () {
            return [
                'status' => Go::STATUS_SUCCESS,
                'code' => 0,
                'info' => [
                    'status' => 'OK',
                    'errors' => [],
                    'request_id' => '171021246816798189863775',
                    'result' => [
                        'total' => 2,
                        'num' => 2,
                        'viewtotal' => 1,
                        'items' => [
                            [
                                'id' => '1',
                                'background' => 'oss://search/topic/202212/13/a.png',
                                'color' => '#4D5577',
                                'cover' => 'oss://search/topic/202212/13/a.png',
                                'ip_id' => '0',
                                'more' => '',
                                'title' => '魔道祖师',
                                'url' => 'https://www.missevan.com/mtopic/341',
                                'create_time' => '1670912040',
                                'modified_time' => '1670912040',
                                'start_time' => '1670912100',
                                'type' => '1',
                                'index_name' => 'uat_special_search_items',
                            ],
                            [
                                'id' => '2',
                                'background' => 'oss://search/topic/202212/13/a.png',
                                'color' => '#4D5577',
                                'cover' => 'oss://search/topic/202212/13/a.png',
                                'ip_id' => '0',
                                'more' => '',
                                'title' => '魔道祖师',
                                'url' => 'https://www.missevan.com/mtopic/341',
                                'create_time' => '1670912040',
                                'modified_time' => '1670912040',
                                'start_time' => '1670912100',
                                'type' => '1',
                                'index_name' => 'uat_special_search_items',
                            ]
                        ]
                    ],
                    'ops_request_misc' => '%7B%22request%5Fid%22%3A%%22%7D',
                ]
            ];
        });
        $return = MSound::getSearch('test', 1, 20, self::TEST_USER_ID, 0, 1, 1);
        $this->assertCount(1, $return['Datas']);
        $this->assertEquals(2, $return['Datas'][0]['id']);
    }

    public function testHaveVideos()
    {
        // 对象的情形（自动调用 MSound::afterAllFind => MSound::haveVideos()）
        // 单个对象
        $model = MSound::findOne(self::$test_sound_id_no_video);
        $this->assertFalse($model->video);

        $model = MSound::findOne(self::$test_sound_id_has_video);
        $this->assertTrue($model->video);
        // 对象数组
        $sounds = MSound::findAll(['id' => [self::$test_sound_id_no_video, self::$test_sound_id_has_video]]);
        foreach ($sounds as $sound) {
            switch ($sound->id) {
                case self::$test_sound_id_no_video:
                    $this->assertFalse($sound->video, "{$sound->id} 音频 video 期望为 false");
                    break;
                case self::$test_sound_id_has_video:
                    $this->assertTrue($sound->video, "{$sound->id} 音频 video 期望为 true");
                    break;
            }
        }

        // 数组的情形
        // Map
        $arr = MSound::find()->where(['id' => self::$test_sound_id_no_video])->asArray()->one();
        MSound::haveVideos($arr);
        $this->assertFalse($arr['video']);

        $arr = MSound::find()->where(['id' => self::$test_sound_id_has_video])->asArray()->one();
        MSound::haveVideos($arr);
        $this->assertTrue($arr['video']);

        // Map 数组
        $sounds = MSound::find()
            ->where(['id' => [self::$test_sound_id_no_video, self::$test_sound_id_has_video]])
            ->asArray()
            ->all();
        MSound::haveVideos($sounds);
        foreach ($sounds as $sound) {
            switch ($sound['id']) {
                case self::$test_sound_id_no_video:
                    $this->assertFalse($sound['video'], "{$sound['id']} 音频 video 期望为 false");
                    break;
                case self::$test_sound_id_has_video:
                    $this->assertTrue($sound['video'], "{$sound['id']} 音频 video 期望为 true");
                    break;
            }
        }
    }

    public function testGetCatalogSounds()
    {
        // 测试正常获取音频
        $data = MSound::getCatalogSounds([Catalog::CATALOG_ID_MUSIC], 0, [], 10);
        $this->assertNotEmpty($data);

        // 测试获取不到报警音
        $police_sound_id = $data[0]['id'];
        MSound::updateByPk($police_sound_id, ['checked' => MSound::CHECKED_POLICE]);
        $data = MSound::getCatalogSounds([Catalog::CATALOG_ID_MUSIC], 0, [], 10);
        $this->assertNotEmpty($data);
        $sound_ids = array_column($data, 'id');
        $this->assertFalse(in_array($police_sound_id, $sound_ids));
    }

    public function testGetSoundsByCat()
    {
        // 测试没有推荐音频时正常获取不加精的音频
        $catalog_key = MUtils::generateCacheKey(KEY_CATALOG_GLOBAL_SOUNDS, Catalog::CATALOG_ID_MUSIC, 0);
        Yii::$app->memcache->delete($catalog_key);
        $data = MSound::getSoundsByCat(0, [Catalog::CATALOG_ID_MUSIC]);
        $this->assertNotEmpty($data);

        // 测试推荐音频数量满足当前页时正常获取推荐音频
        Yii::$app->memcache->delete($catalog_key);
        $ids = array_column($data, 'id');
        $catalog_sounds_key = MUtils::generateCacheKey(KEY_SOUNDS_CHECKED_CATALOG_ID, 0, Catalog::CATALOG_ID_MUSIC);
        $cache = array_reduce($ids, function ($sound_point, $sound_id) {
            $sound_point[$sound_id] = 1;
            return $sound_point;
        }, []);
        Yii::$app->memcache->set($catalog_sounds_key, $cache, FIVE_MINUTE);
        $data = MSound::getSoundsByCat(0, [Catalog::CATALOG_ID_MUSIC], 10);
        $this->assertNotEmpty($data);

        // 测试推荐音频数量不足时以不加精的音频补足后正常获取音频
        Yii::$app->memcache->delete($catalog_key);
        $data = MSound::getSoundsByCat(0, [Catalog::CATALOG_ID_MUSIC], 25);
        $this->assertNotEmpty($data);

        // 测试获取不到报警音
        Yii::$app->memcache->delete($catalog_key);
        Yii::$app->memcache->delete($catalog_sounds_key);
        $police_sound_id = $data[0]['id'];
        MSound::updateByPk($police_sound_id, ['checked' => MSound::CHECKED_POLICE]);
        $data = MSound::getSoundsByCat(0, [Catalog::CATALOG_ID_MUSIC]);
        $this->assertNotEmpty($data);
        $sound_ids = array_column($data, 'id');
        $this->assertFalse(in_array($police_sound_id, $sound_ids));
    }

    public function testGetCheckedSoundByIds()
    {
        // 没有符合条件的音频信息
        $sound_1 = Data::createSound([]);
        $sound_2 = Data::createSound([]);
        $result = self::invokePrivateMethod(MSound::class, 'getCheckedSoundByIds', [$sound_1->id, $sound_2->id]);
        $this->assertEquals([], $result);

        // 有符合条件的音频信息
        MSound::updateAll(['checked' => MSound::CHECKED_PASS], ['id' => [$sound_1->id, $sound_2->id]]);
        $result = self::invokePrivateMethod(MSound::class, 'getCheckedSoundByIds', [$sound_1->id, $sound_2->id]);
        $this->assertNotEmpty($result);
    }

    public function testIsForeignForbidden()
    {
        $IP_CHINA_MAINLAND = '*************';
        $IP_SINGAPORE = '************';

        // 1. 普通音频（非日抓）
        $sound = MSound::find()
            ->where('catalog_id <> :cat', [':cat' => Catalog::CATALOG_ID_JAPAN_DRAMA_OTOME])
            ->andWhere(['checked' => MSound::CHECKED_PASS])
            ->limit(1)->one();
        // 中国大陆 IP
        $_SERVER['REMOTE_ADDR'] = $IP_CHINA_MAINLAND;
        $this->assertFalse($sound->isForeignForbidden());
        self::resetLocation();

        // 海外 IP
        $_SERVER['REMOTE_ADDR'] = $IP_SINGAPORE;
        $this->assertFalse($sound->isForeignForbidden());

        // 2. 海外屏蔽的音频（日抓）
        $sound_forbidden = MSound::find()
            ->where(['catalog_id' => Catalog::CATALOG_ID_JAPAN_DRAMA_OTOME])
            ->andWhere(['checked' => MSound::CHECKED_PASS])
            ->limit(1)->one();
        // 清除测试数据
        Yii::$app->redis->sRem(KEY_FOREIGN_FORBIDDEN_SOUND_EXEMPTED_USER_ID, $sound_forbidden->user_id);
        self::resetLocation();

        // 中国大陆 IP
        $_SERVER['REMOTE_ADDR'] = $IP_CHINA_MAINLAND;
        $this->assertFalse($sound_forbidden->isForeignForbidden());
        self::resetLocation();

        // 海外 IP
        $_SERVER['REMOTE_ADDR'] = $IP_SINGAPORE;
        // 没有被豁免的情况
        $this->assertTrue($sound_forbidden->isForeignForbidden());
        // 被豁免的情况
        Yii::$app->redis->sAdd(KEY_FOREIGN_FORBIDDEN_SOUND_EXEMPTED_USER_ID, $sound_forbidden->user_id);
        $this->assertFalse($sound_forbidden->isForeignForbidden());
        // 重置 IP 位置
        self::resetLocation();
    }

    public function testGetSubscribedDrama()
    {
        Tools::registerRpcApiResponseFunc('/rpc/api/get-subscriptions', function ($data) {
            return [
                'status' => Go::STATUS_SUCCESS,
                'code' => 0,
                'info' => [
                    'Datas' => [
                        [
                            'id' => 1,
                            'name' => '测试剧集',
                            'type' => 7,
                            'serialize' => false,
                            'newest' => '不死者小剧场',
                            'pay_type' => 0,
                            'checked' => 1,
                            'cover' => 'https://static-test.maoercdn.com/test.jpg',
                            'cover_color' => 12434877,
                            'abstract' => '没加',
                            'price' => 0,
                            'discount' => null,
                        ]
                    ],
                    'pagination' => [
                        'p' => 1,
                        'count' => 0,
                        'maxpage' => 0,
                        'pagesize' => PAGE_SIZE_20
                    ]
                ],
            ];
        });
        Tools::registerRpcApiResponseFunc('/rpc/drama/get-corner-mark', function ($data) {
            return [
                'status' => Go::STATUS_SUCCESS,
                'code' => 0,
                'info' => [
                    '1' => [
                        'title' => '已购',
                        'text_color' => '#ffffff',
                        'bg_start_color' => '#e66465',
                        'bg_end_color' => '#e66465',
                        'left_icon_url' => 'https://static-test.maoercdn.com/cornermark/corner_mark.png',
                    ]
                ],
            ];
        });
        $TEST_USER_ID = 346287;
        $TEST_OWN_ID = 1;
        $data = MSound::getSubscribedDrama($TEST_USER_ID, $TEST_OWN_ID, 1, PAGE_SIZE_20);
        $this->assertIsObject($data);
        foreach ($data->Datas as $item) {
            // 他人主页，过滤掉了所有合约下架剧集
            $this->assertNotEquals(Drama::CHECKED_CONTRACT_EXPIRED, $item['checked']);
        }
        $data = MSound::getSubscribedDrama($TEST_USER_ID, $TEST_USER_ID, 1, PAGE_SIZE_20);
        $this->assertIsObject($data);
        foreach ($data->Datas as $item) {
            // 用户订阅的剧集目前只显示已购合约下架剧集，审核中剧集，过审剧集
            if ($item['checked'] === Drama::CHECKED_CONTRACT_EXPIRED) {
                // 合约下架剧集只显示用户已购的
                $this->assertEquals(Drama::DRAMA_PAID, $item['need_pay']);
            } else {
                $this->assertTrue(in_array($item['checked'], [Drama::CHECKED_NOT_VERIFY, Drama::CHECKED_PASS]));
            }
        }

        // 测试 iOS < 4.8.8
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.8.6 (iOS;12.0;iPhone9,1)']);
        $data = MSound::getSubscribedDrama($TEST_USER_ID, $TEST_USER_ID, 1, PAGE_SIZE_20);
        $this->assertIsObject($data);
        $this->assertArrayHasKey('need_pay', $data->Datas[0]);
        $this->assertArrayNotHasKeys(['corner_mark', 'discount'], $data->Datas[0]);

        // 测试 iOS >= 4.8.8，访问他人的个人主页
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.8.8 (iOS;12.0;iPhone9,1)']);
        $data = MSound::getSubscribedDrama($TEST_USER_ID, $TEST_OWN_ID, 1, PAGE_SIZE_20);
        $this->assertIsObject($data);
        $this->assertArrayNotHasKeys(['corner_mark', 'need_pay', 'discount'], $data->Datas[0]);

        // 测试 iOS >= 4.8.8，访问自己的个人主页
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.8.8 (iOS;12.0;iPhone9,1)']);
        $data = MSound::getSubscribedDrama($TEST_USER_ID, $TEST_USER_ID, 1, PAGE_SIZE_20);
        $this->assertIsObject($data);
        $this->assertArrayHasKey('corner_mark', $data->Datas[0]);
        $this->assertArrayNotHasKeys(['need_pay', 'discount'], $data->Datas[0]);
    }

    /**
     * 测试获取音频地址
     */
    public function testGetSoundUrl()
    {
        // 测试错误的音频地址
        $sound = new MSound();
        $sound->soundurl = '202002/21/test.mp3';
        $sound->soundurl_32 = '32BIT/202002/21/test.mp3';
        $sound->soundurl_64 = 'MP3/202002/21/test.mp3';
        $sound->soundurl_128 = '128BIT/202002/21/test.mp3';
        $data = MSound::getSoundUrl($sound);
        $this->assertNull($data);

        // 测试非 upos 协议地址转完整地址情况
        $sound->soundurl = '202002/21/test.mp3';
        $sound->soundurl_32 = 'sound://aod/202002/21/test-192k.m4a';
        $sound->soundurl_64 = 'sound://aod/202002/21/test.m4a';
        $sound->soundurl_128 = 'sound://aod/202002/21/test-128k.m4a';
        MSound::getSoundUrl($sound);
        $this->assertSoundUrl($sound, true);

        // 测试数组协议地址转完整地址情况
        $sound = [
            'soundurl' => 'upos://mefmboss/sound/202002/21/test.mp3',
            'soundurl_32' => 'sound://aod/202002/21/test-192k.m4a',
            'soundurl_64' => 'sound://aod/202002/21/test.m4a',
            'soundurl_128' => 'sound://aod/202002/21/test-128k.m4a',
        ];
        MSound::getSoundUrl($sound);
        $this->assertSoundUrl($sound, true);

        // 测试 upos 协议地址转完整地址情况
        $sound = new MSound();
        $sound->soundurl = 'upos://mefmboss/sound/202002/21/test.mp3';
        $sound->soundurl_32 = 'sound://aod/202002/21/test-192k.m4a';
        $sound->soundurl_64 = 'sound://aod/202002/21/test.m4a';
        $sound->soundurl_128 = 'sound://aod/202002/21/test-128k.m4a';
        MSound::getSoundUrl($sound);
        $this->assertSoundUrl($sound, true);
    }

    public function testGetSoundUrlByEquipment()
    {
        $public_url = Yii::$app->storage->publicUrl . '/';

        // 测试协议地址转完整地址情况
        $sounds = [
            [
                'path' => 'oss://aod/202002/21/test-128k.m4a',
                'field' => 'soundurl_128',
                'm4a_url' => "{$public_url}aod/202002/21/test-128k.m4a",
            ],
            [
                'path' => 'oss://aod/202002/21/test.m4a',
                'field' => 'soundurl_64',
                'm4a_url' => "{$public_url}aod/202002/21/test.m4a",
            ],
            [
                'path' => 'oss://aod/202002/21/test-192k.m4a',
                'field' => 'soundurl_32',
                'm4a_url' => "{$public_url}aod/202002/21/test-192k.m4a",
            ],
        ];
        foreach ($sounds as $sound) {
            $sound_url = MSound::GetSoundUrlByEquipment($sound['path'], $sound['field']);
            $this->assertEquals($sound['m4a_url'], $sound_url);
        }

        // 测试 flac 地址返回情况
        $sound = [
            'path' => 'oss://aod/202002/21/test.flac',
            'field' => 'soundurl_64',
            'flac_url' => "{$public_url}aod/202002/21/test.flac",
        ];
        $sound_url = MSound::GetSoundUrlByEquipment($sound['path'], $sound['field']);
        $this->assertEquals($sound['flac_url'], $sound_url);

        // 测试 iOS 11 以下版本 flac 地址返回情况
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.5.5 (iOS;10.2;iPhone9,1)']);
        $sound_url = MSound::GetSoundUrlByEquipment($sound['path'], $sound['field']);
        $this->assertEquals("{$public_url}aod/202002/21/test-192k.m4a", $sound_url);
    }

    public function testGetSoundPathByEquipment()
    {
        // 测试正常返回 sound path
        $this->assertEquals('upos://aod/202002/21/test-128k.m4a',
            MSound::getSoundPathByEquipment('upos://aod/202002/21/test-128k.m4a'));
        $this->assertEquals('upos://aod/202002/21/test.flac',
            MSound::getSoundPathByEquipment('upos://aod/202002/21/test.flac'));

        // 测试 iOS 11 以下的 4.9.5 之前版本 flac 地址返回情况
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.9.4 (iOS;10.2;iPhone9,1)']);
        $this->assertEquals('upos://aod/202002/21/test-128k.m4a',
            MSound::getSoundPathByEquipment('upos://aod/202002/21/test-128k.m4a'));
        $this->assertEquals('upos://aod/202002/21/test-192k.m4a',
            MSound::getSoundPathByEquipment('upos://aod/202002/21/test.flac'));
    }

    /**
     * 验证音频地址是否为完整地址
     *
     * @param MSound|array $sound
     * @param bool is_scheme 是否为协议地址
     */
    private function assertSoundUrl($sound, bool $is_scheme = false)
    {
        $public_url = Yii::$app->storage->publicUrl . '/';
        if ($is_scheme) {
            $this->assertEquals("{$public_url}aod/202002/21/test.m4a", $sound['soundurl']);
            $this->assertEquals("{$public_url}aod/202002/21/test.m4a", $sound['soundurl_64']);
            $this->assertEquals("{$public_url}aod/202002/21/test-128k.m4a", $sound['soundurl_128']);
            $this->assertEquals("{$public_url}aod/202002/21/test-192k.m4a", $sound['soundurl_32']);
        } else {
            $this->assertEquals("{$public_url}MP3/202002/21/test.mp3", $sound['soundurl']);
            $this->assertEquals("{$public_url}MP3/202002/21/test.mp3", $sound['soundurl_64']);
            $this->assertEquals("{$public_url}128BIT/202002/21/test.mp3", $sound['soundurl_128']);
            $this->assertEquals("{$public_url}32BIT/202002/21/test.mp3", $sound['soundurl_32']);
        }
    }

    public function testGetIsForbiddenInJapan()
    {
        $common_sound = new MSound();
        $common_sound->setAttributes([
            'id' => 11,
            'refined' => MSound::REFINED_BLOCK,
        ], false);
        $this->assertFalse($common_sound->isForbiddenInJapan);

        $forbidden_sound = new MSound();
        $forbidden_sound->setAttributes([
            'id' => 12,
            'refined' => MSound::REFINED_BLOCK | MSound::REFINED_FORBID_JAPAN,
        ], false);
        $this->assertTrue($forbidden_sound->isForbiddenInJapan);
    }

    public function testGetLikeMusic()
    {
        // 测试推荐音频正常返回
        $data = MSound::getLikeMusic(self::TEST_SOUND_ID, 4);
        $this->assertIsArray($data);
        $this->assertEquals(4, count($data));

        // 测试擦边球 1 音频被过滤掉
        $recommend_key = MUtils::generateCacheKey(KEY_SOUND_PLAY_RECOMMEND_STRATEGY_SOUNDS, self::TEST_SOUND_ID);
        $sound_like_ids_v1 = array_reduce(Yii::$app->memcache->get($recommend_key), function ($result, $value) {
            return array_merge($result, array_values($value));
        }, []);
        $id_condition = MUtils::generateIntegerIn('id', $sound_like_ids_v1);

        MSound::updateAll(['refined' => MSound::REFINED_POLICE], $id_condition);
        Yii::$app->memcache->delete($recommend_key);
        $data = MSound::getLikeMusic(self::TEST_SOUND_ID, 4);
        $this->assertIsArray($data);
        $this->assertEquals(4, count($data));
        // 验证含有策略 ID 字段
        $this->assertObjectHasAttribute('strategy_id', $data[0]);
        // 验证此时方法中生成的推荐音频 ID 中已不包含擦边球音频
        $sound_like_ids_v2 = array_reduce(Yii::$app->memcache->get($recommend_key), function ($result, $value) {
            return array_merge($result, array_values($value));
        }, []);
        $this->assertEmpty(array_intersect($sound_like_ids_v1, $sound_like_ids_v2));

        // 测试擦边球 2 音频被过滤掉
        MSound::updateAll(['refined' => MSound::REFINED_BLOCK], $id_condition);
        $data = MSound::getLikeMusic(self::TEST_SOUND_ID, 4);
        // 验证含有策略 ID 字段
        $this->assertObjectHasAttribute('strategy_id', $data[0]);
        $this->assertIsArray($data);
        $this->assertEquals(4, count($data));
        // 验证此时方法中生成的推荐音频 ID 中已不包含擦边球音频
        $sound_like_ids_v3 = array_reduce(Yii::$app->memcache->get($recommend_key), function ($result, $value) {
            return array_merge($result, array_values($value));
        }, []);
        $this->assertEmpty(array_intersect($sound_like_ids_v1, $sound_like_ids_v3));
        $this->assertEquals($sound_like_ids_v2, $sound_like_ids_v3);

        // 音频 refined 属性还原
        MSound::updateAll(['refined' => 0], $id_condition);

        // 测试报警音频被过滤掉
        Yii::$app->memcache->delete($recommend_key);
        MSound::updateAll(['checked' => MSound::CHECKED_POLICE], $id_condition);
        MSound::getLikeMusic(self::TEST_SOUND_ID, 4);
        MSound::updateAll(['checked' => MSound::CHECKED_PASS], $id_condition);
        // 验证此时方法中生成的推荐音频 ID 中已不包含报警音频
        $sound_like_ids_v3 = array_reduce(Yii::$app->memcache->get($recommend_key), function ($result, $value) {
            return array_merge($result, array_values($value));
        }, []);
        $this->assertEmpty(array_intersect($sound_like_ids_v1, $sound_like_ids_v3));
        $this->assertEquals($sound_like_ids_v2, $sound_like_ids_v3);
    }

    public function testAddPlayTimes()
    {
        $sound = new MSound();
        $sound->setAttributes([
            'id' => 998877,
        ], false);

        $_SERVER['REQUEST_TIME'] = strtotime('midnight');
        $VIEWS = 12;

        $redis_sound_view = Yii::$app->redis_sound_view;
        $time = MUtils::getUnitTime(ONE_MINUTE);
        $is_guest = Yii::$app->user->isGuest;
        $key = $redis_sound_view->generateKey(
            KEY_COUNTER_SOUND_VIEWS,
            $is_guest ? MSound::PLAY_SOURCE_GUEST : MSound::PLAY_SOURCE_USER,
            $time
        );
        $redis_sound_view->del($key);
        $redis_sound_view->hSet($key, $sound->id, 0);

        // 测试正常播放量增加
        $sound->addPlayTimes($VIEWS);
        $actual = (int)$redis_sound_view->hGet($key, $sound->id);
        $this->assertEquals($VIEWS, $actual);
        // 清理数据
        $redis_sound_view->del($key);

        $redis = Yii::$app->redis;
        $real_ip = MUtils2::getIPRange();
        $redis->sAdd(KEY_ABNORMAL_VIEWS_SOUND, $sound->id);
        $abnormal_key = $redis->generateKey(KEY_ABNORMAL_IP_SOUND_VIEWS, $sound->id);
        $redis->hSet($abnormal_key, $real_ip, MSound::LIMIT_ABNORMAL_SOUND_VIEWS_MAX_NUM);

        // 测试可疑刷播放量限制，达到限制时播放量不增加
        $sound->addPlayTimes(1);
        $actual = (int)$redis_sound_view->hGet($key, $sound->id);
        $this->assertEquals(0, $actual);
        // 清理数据
        $redis->del($abnormal_key);
        $redis->sRem(KEY_ABNORMAL_VIEWS_SOUND, $sound->id);
    }

    /**
     * 测试获取音单中音频方法
     */
    public function testGetAlbumSound()
    {
        // 更新测试再审音频状态为审核通过
        MSound::updateByPk(self::$normal_sound_id, ['checked' => MSound::CHECKED_PASS]);
        $album_id = (int)self::$album->id;
        // 测试音单不存在的情况
        $this->assertThrowsWithMessage(HttpException::class, '音单不存在',
            function () {
                MSound::getAlbumSound(9999999, self::TEST_USER_ID);
            });
        // 测试音单失效的情况（音单非创建者访问且为报警音单）
        MAlbum::updateAll(['checked' => MAlbum::CHECKED_POLICE], ['id' => self::$album->id]);
        $this->assertThrowsWithMessage(HttpException::class, '音单已失效',
            function () use ($album_id) {
                MSound::getAlbumSound($album_id, self::TEST_USER2_ID);
            });
        MAlbum::updateAll(['checked' => MAlbum::CHECKED_PASS], ['id' => self::$album->id]);

        // 测试创建者本人访问音单的情况
        $this->loginByUserId(self::TEST_USER_ID);
        $result = MSound::getAlbumSound($album_id, self::TEST_USER_ID);
        $this->assertEquals(1, $result['pagination']['count']);
        $this->assertEquals(1, count($result['Datas']));
        $this->assertObjectNotHasAttribute('soundurl', $result['Datas'][0]);
        $this->assertObjectNotHasAttribute('soundurl_64', $result['Datas'][0]);
        $this->assertObjectNotHasAttribute('soundurl_128', $result['Datas'][0]);

        // 测试音单中有擦边球音频的情况
        MSound::updateByPk(self::$normal_sound_id, ['refined' => MSound::REFINED_POLICE]);
        $result = MSound::getAlbumSound($album_id, self::TEST_USER_ID);
        $this->assertEquals(1, $result['pagination']['count']);
        $this->assertEquals(1, count($result['Datas']));
        $this->assertObjectNotHasAttribute('soundurl', $result['Datas'][0]);
        $this->assertObjectNotHasAttribute('soundurl_64', $result['Datas'][0]);
        $this->assertObjectNotHasAttribute('soundurl_128', $result['Datas'][0]);

        // 测试音单中有擦边球音频且为催眠专享获取音单中音频
        $result = MSound::getAlbumSound($album_id, self::TEST_USER_ID, false, true);
        $this->assertEquals(0, $result['pagination']['count']);
        $this->assertEmpty($result['Datas']);

        $this->loginByUserId(self::TEST_USER2_ID);
        // 测试音单加入白名单后不为创建者访问情况
        MSound::updateByPk(self::$normal_sound_id, ['refined' => MSound::REFINED_COMMON]);
        MAllowAlbum::addAlbum($album_id);
        $result = MSound::getAlbumSound($album_id, self::TEST_USER_ID);
        $this->assertEquals(1, $result['pagination']['count']);
        $this->assertEquals(1, count($result['Datas']));
        $this->assertObjectNotHasAttribute('soundurl', $result['Datas'][0]);
        $this->assertObjectNotHasAttribute('soundurl_64', $result['Datas'][0]);
        $this->assertObjectNotHasAttribute('soundurl_128', $result['Datas'][0]);

        // 测试音单中音频为付费音的情况
        MSound::updateByPk(self::$normal_sound_id, ['pay_type' => MSound::PAY_BY_SOUND]);
        $result = MSound::getAlbumSound($album_id, self::TEST_USER_ID);
        $this->assertEquals(1, $result['pagination']['count']);
        $this->assertEquals(1, count($result['Datas']));
        $this->assertObjectNotHasAttribute('soundurl', $result['Datas'][0]);
        $this->assertObjectNotHasAttribute('soundurl_64', $result['Datas'][0]);
        $this->assertObjectNotHasAttribute('soundurl_128', $result['Datas'][0]);
    }

    public function testGetUserSound()
    {
        Tools::registerDramaMockResponseFunc('/rpc/api/get-drama-paytype-by-sound', function () {
            return [
                self::$review_sound_id => [
                    'drama_id' => 1,
                    'pay_type' => Drama::PAY_TYPE_DRAMA,
                    'is_interactive' => false,
                    'episode_vip' => Drama::EPISODE_VIP_LIMIT,
                ],
            ];
        });
        // 更新测试再审音频状态为审核通过
        MSound::updateByPk(self::$review_sound_id, ['checked' => MSound::CHECKED_PASS]);
        // 更新测试再审音频状态为审核中
        MSound::updateByPk(self::$normal_sound_id, ['checked' => MSound::CHECKED_UNPASS]);
        // 测试他人视角
        $result = MSound::getUserSound(MSound::USER_SOUND_TYPE_OWN, self::TEST_USER2_ID);
        $this->assertIsObject($result);
        $this->assertObjectHasAttribute('Datas', $result);
        $this->assertNotEmpty($result->Datas);
        // 断言只能看到审核通过的测试再审音频
        $this->assertEquals(1, count($result->Datas));
        $this->assertEquals(self::$review_sound_id, $result->Datas[0]['id']);
        $this->assertEquals(MSound::CHECKED_PASS, $result->Datas[0]['checked']);
        // 断言他人视角看到的是修改前的信息
        $this->assertEquals('test_sound', $result->Datas[0]['soundstr']);
        $this->assertObjectHasAttribute('pagination', $result);
        // 断言 episode_vip
        $this->assertEquals(Drama::EPISODE_VIP_LIMIT, $result->Datas[0]['episode_vip']);

        // 登录
        $this->loginByUserId(self::TEST_USER2_ID);
        // 测试 UP 主视角
        // 测试获取用户普通音频
        $result = MSound::getUserSound(MSound::USER_SOUND_TYPE_OWN, self::TEST_USER2_ID);
        $this->assertIsObject($result);
        $this->assertObjectHasAttribute('Datas', $result);
        $this->assertNotEmpty($result->Datas);
        // 断言只能看到审核通过的测试再审音频和审核中的音频
        $EXPECTED = 2;
        $this->assertEquals($EXPECTED, count($result->Datas));
        // 断言他人视角看到的是修改后的信息
        $actual_sounds = array_column($result->Datas, null, 'id');
        $this->assertEquals('review_soundstr', $actual_sounds[self::$review_sound_id]['soundstr']);
        $this->assertEquals(MSound::CHECKED_UNPASS, $actual_sounds[self::$review_sound_id]['checked']);
        $this->assertObjectHasAttribute('pagination', $result);

        // 测试获取用户直播回放音频
        $result = MSound::getUserSound(MSound::USER_SOUND_TYPE_LIVE, self::TEST_USER2_ID);
        $this->assertIsObject($result);
        $this->assertObjectHasAttribute('Datas', $result);
        $this->assertNotEmpty($result->Datas);
        // 断言只能看到直播回放音频
        $this->assertEquals(1, count($result->Datas));
        $this->assertEquals(self::$live_sound_id, $result->Datas[0]['id']);
        $this->assertObjectHasAttribute('pagination', $result);

        // 测试获取用户喜欢的音频
        // 登录
        $this->loginByUserId(self::TEST_USER_ID);
        $result = MSound::getUserSound(MSound::USER_SOUND_TYPE_LIKED, self::TEST_USER_ID);
        $this->assertIsObject($result);
        $this->assertObjectHasAttribute('Datas', $result);
        $this->assertNotEmpty($result->Datas);
        // 断言有 checked, soundstr, cover_image, intro, download 字段
        foreach ($result->Datas as $data) {
            $this->assertArrayHasKey('checked', $data);
            $this->assertArrayHasKey('soundstr', $data);
            $this->assertArrayHasKey('cover_image', $data);
            $this->assertArrayHasKey('intro', $data);
            $this->assertArrayHasKey('download', $data);
        }

        // 测试参数错误
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () {
            MSound::getUserSound(MSound::USER_SOUND_TYPE_LIKED, self::TEST_USER_ID, -1);
        });

        // 测试按照最热方式排序
        $result = MSound::getUserSound(MSound::USER_SOUND_TYPE_LIKED, self::TEST_USER_ID, 2);
        $this->assertIsObject($result);
        $this->assertObjectHasAttribute('Datas', $result);
        $this->assertNotEmpty($result->Datas);
    }

    public function testGetRingtone()
    {
        // 测试不显示设置铃声按钮与不下载完整音频地址情况
        $sound = new MSound();
        $sound->catalog_id = 1;
        $sound->type = MSound::TYPE_NORMAL;
        $this->assertEquals(0, MSound::getRingtone($sound));

        // 测试显示铃声按钮情况
        $sound->catalog_id = Yii::$app->params['ringtone_catalog_ids'][0];
        $this->assertEquals(1, MSound::getRingtone($sound));

        // 测试下载完整音频的情况
        $sound->catalog_id = Catalog::CATALOG_ID_RINGTONE_CLOCK;
        $this->assertEquals(3, MSound::getRingtone($sound));

        // 测试互动剧不显示铃声按钮情况
        $sound->type = MSound::TYPE_INTERACTIVE;
        $this->assertEquals(2, MSound::getRingtone($sound));
    }

    public function testTs()
    {
        // 测试暂不能对当前内容进行此操作
        $this->assertThrowsWithMessage(HttpException::class, '暂不能对当前内容进行此操作', function () {
            MSound::Ts(self::SOUND_ID_NOT_EXIST);
        });

        self::loginByUserId();

        // 测试投食成功：音频获得的小鱼干不为 10 的倍数
        DataBus::clearQueue();
        MSound::updateByPk(self::$normal_sound_id, ['checked' => MSound::CHECKED_PASS, 'point' => 8]);
        Mowangskuser::updateAll(['point' => 1], ['id' => self::TEST_USER_ID]);
        $this->assertEquals(9, MSound::Ts(self::$normal_sound_id));
        $databus_queue = DataBus::getQueue();
        $this->assertIsArray($databus_queue[0]['msg']);
        $this->assertArrayHasKeys(['num', 'type', 'user_id', 'origin', 'create_time', 'more'],
            $databus_queue[0]['msg']);
        $this->assertLessThanOrEqual($_SERVER['REQUEST_TIME'], $databus_queue[0]['msg']['create_time']);
        unset($databus_queue[0]['msg']['create_time']);
        $except_queue = [
            [
                'key' => 'user_point_detail_log:' . self::TEST_USER_ID,
                'msg' => [
                    'num' => -PointDetailLog::POINT_SOUND_TS,
                    'type' => PointDetailLog::TYPE_SOUND_TS,
                    'user_id' => self::TEST_USER_ID,
                    'origin' => PointDetailLog::ORIGIN_APP,
                    'more' => [
                        'sound_id' => self::$normal_sound_id,
                    ],
                ],
            ],
        ];
        $this->assertEquals($except_queue, $databus_queue);

        // 测试投食成功：音频获得的小鱼干为 10 的倍数
        self::loginByUserId(self::TEST_USER3_ID);
        DataBus::clearQueue();
        Mowangskuser::updateAll(['point' => 1], ['id' => self::TEST_USER3_ID]);
        $this->assertEquals(10, MSound::Ts(self::$normal_sound_id));
        $databus_queue = DataBus::getQueue();
        $this->assertEquals(2, count($databus_queue));
        foreach ($databus_queue as $key => $log) {
            $this->assertArrayHasKeys(['num', 'type', 'user_id', 'origin', 'create_time', 'more'], $log['msg']);
            $this->assertLessThanOrEqual($_SERVER['REQUEST_TIME'], $log['msg']['create_time']);
            unset($databus_queue[$key]['msg']['create_time']);
        }
        $except_queue = [
            [
                'key' => 'user_point_detail_log:' . self::TEST_USER2_ID,
                'msg' => [
                    'num' => PointDetailLog::POINT_SOUND_TS_UP_GET,
                    'type' => PointDetailLog::TYPE_SOUND_GAIN_TS,
                    'user_id' => self::TEST_USER2_ID,
                    'origin' => PointDetailLog::ORIGIN_APP,
                    'more' => [
                        'sound_id' => self::$normal_sound_id,
                    ],
                ],
            ],
            [
                'key' => 'user_point_detail_log:' . self::TEST_USER3_ID,
                'msg' => [
                    'num' => -PointDetailLog::POINT_SOUND_TS,
                    'type' => PointDetailLog::TYPE_SOUND_TS,
                    'user_id' => self::TEST_USER3_ID,
                    'origin' => PointDetailLog::ORIGIN_APP,
                    'more' => [
                        'sound_id' => self::$normal_sound_id,
                    ],
                ],
            ],
        ];
        $this->assertEquals($except_queue, $databus_queue);

        // 小鱼干不足
        $this->assertThrowsWithMessage(HttpException::class, '没有小鱼干可以投食了 T_T', function () {
            MSound::Ts(self::$normal_sound_id);
        });

        // 测试五分钟只能投一次小鱼干
        Mowangskuser::updateAll(['point' => 1], ['id' => self::TEST_USER3_ID]);
        $this->assertThrowsWithMessage(HttpException::class, '五分钟只能投一次小鱼干', function () {
            MSound::Ts(self::$normal_sound_id);
        });
    }

    private static function resetMSound(&$sound, $not_null_128 = true)
    {
        $sound = new MSound();
        $sound->setOldAttribute(MSound::KEY_SOUNDURL_32, 'sound://aod/test-32.m4a');
        $sound->setOldAttribute(MSound::KEY_SOUNDURL_64, 'sound://aod/test-64.m4a');
        $soundurl_128 = $not_null_128 ? 'sound://aod/test-128.m4a' : '';
        $sound->setOldAttribute(MSound::KEY_SOUNDURL_128, $soundurl_128);
        $sound->setAttribute(MSound::KEY_SOUNDURL, 'https://static-test.missevan.com/aod/test_64.m4a');
        $sound->setAttribute(MSound::KEY_SOUNDURL_64, 'https://static-test.missevan.com/aod/test_64.m4a');
        $soundurl_128 = $not_null_128 ? 'https://static-test.missevan.com/aod/test_128k.m4a' : '';
        $sound->setAttribute(MSound::KEY_SOUNDURL_128, $soundurl_128);
    }

    public function testGetSoundSignUrls()
    {
        // 测试高版本（Android >= 5.7.3, iOS >= 4.7.4）正常获取音频的签名地址：获取签名地址列表为 1 个的情况
        self::resetMSound($sound);
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.0.7 (Android;7.0;Meizu M6 M6)']);
        MSound::getSoundSignUrls($sound);
        $key_soundurl = MSound::KEY_SOUNDURL;
        $pre = 'http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/';
        $this->assertEquals($pre . 'test-64.m4a?sign=test0', $sound->$key_soundurl);
        $key_soundurl_list = $key_soundurl . '_list';
        $this->assertEquals([$pre . 'test-64.m4a?sign=test0'], $sound->$key_soundurl_list);
        $key_soundurl_32 = MSound::KEY_SOUNDURL_32;
        $this->assertEquals($pre . 'test-32.m4a?sign=test0', $sound->$key_soundurl_32);
        $key_soundurl_128 = MSound::KEY_SOUNDURL_128;
        $this->assertEquals($pre . 'test-128.m4a?sign=test0', $sound->$key_soundurl_128);
        $key_soundurl_128_list = $key_soundurl_128 . '_list';
        $this->assertEquals([$pre . 'test-128.m4a?sign=test0'], $sound->$key_soundurl_128_list);

        // 测试高版本（Android >= 5.7.3, iOS >= 4.7.4）正常获取音频的签名地址：获取签名地址列表超出 1 个的情况
        self::resetMSound($sound);
        MSound::getSoundSignUrls($sound, true, 1);
        $this->assertIsArray($sound->$key_soundurl_128_list);
        $soundurl_128_list = [
            $pre . 'test-128.m4a?sign=test0',
            $pre . 'test-128.m4a?sign=test1',
        ];
        $this->assertEquals($soundurl_128_list, $sound->$key_soundurl_128_list);
        $this->assertIsArray($sound->$key_soundurl_list);
        $soundurl_list = [
            $pre . 'test-64.m4a?sign=test0',
            $pre . 'test-64.m4a?sign=test1',
        ];
        $this->assertEquals($soundurl_list, $sound->$key_soundurl_list);

        // 测试低版本（Android < 5.7.3, iOS < 4.7.4）等原因无法正常获取音频 bvc 签名播放地址列表信息的情况
        self::resetMSound($sound);
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/5.7.2 (Android;7.0;Meizu M6 M6)']);
        MSound::getSoundSignUrls($sound, true, 1);
        $this->assertEquals('https://static-test.missevan.com/aod/test_64.m4a', $sound->$key_soundurl);
        $this->assertEquals(['https://static-test.missevan.com/aod/test_64.m4a'], $sound->$key_soundurl_list);
        $this->assertEquals('https://static-test.missevan.com/aod/test_128k.m4a', $sound->$key_soundurl_128);
        $this->assertEquals(['https://static-test.missevan.com/aod/test_128k.m4a'], $sound->$key_soundurl_128_list);

        // 测试 soundurl_128 为空的情况
        self::resetMSound($sound, false);
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.0.7 (Android;7.0;Meizu M6 M6)']);
        MSound::getSoundSignUrls($sound, true, 1);
        $this->assertEquals($pre . 'test-64.m4a?sign=test0', $sound->$key_soundurl);
        $this->assertEquals([$pre . 'test-64.m4a?sign=test0', $pre . 'test-64.m4a?sign=test1'],
            $sound->$key_soundurl_list);
        $this->assertEquals('', $sound->$key_soundurl_128);
        $this->assertNull($sound->$key_soundurl_128_list);
    }

    public function testGetSignUrls()
    {
        // 测试安卓请求下发 http 播放地址
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.0.7 (Android;7.0;Meizu M6 M6)']);
        $upos_uris = [
            'upos://mefmxcodeboss/aod/test-32.m4a',
            'upos://mefmxcodeboss/aod/test-64.m4a',
            'upos://mefmxcodeboss/aod/test-128.m4a'
        ];
        $sound = MSound::getSignUrls($upos_uris);
        $this->assertEquals('http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test-32.m4a?sign=test0',
            $sound[0][0]);
        $this->assertEquals('http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test-64.m4a?sign=test0',
            $sound[1][0]);
        $this->assertEquals('http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test-128.m4a?sign=test0',
            $sound[2][0]);

        // 测试 Google Play 渠道安装的客户端，下发 https 播放地址
        Yii::$app->equip->init(['channel' => 'missevan_google']);
        $sound = MSound::getSignUrls($upos_uris);
        $this->assertEquals('https://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test-32.m4a?sign=test0',
            $sound[0][0]);
        $this->assertEquals('https://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test-64.m4a?sign=test0',
            $sound[1][0]);
        $this->assertEquals('https://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test-128.m4a?sign=test0',
            $sound[2][0]);

        // 测试海外 Android 系统 < 7.1.1 的设备下发 http 播放地址
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.0.7 (Android;7.1;Meizu M6 M6)']);
        self::setLocation(['ip' => Data::TEST_JAPAN_IP,
            'geoip_record' => ['country_code' => 'JP', 'region_name' => 'Japan']]);
        $sound = MSound::getSignUrls($upos_uris);
        $this->assertEquals('http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test-32.m4a?sign=test0',
            $sound[0][0]);
        $this->assertEquals('http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test-64.m4a?sign=test0',
            $sound[1][0]);
        $this->assertEquals('http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test-128.m4a?sign=test0',
            $sound[2][0]);
    }

    public function testIsBVCSoundCDN()
    {
        // 测试视频云白名单用户返回 playurl 签名地址
        MSound::$is_upos_user = null;
        self::loginByUserId();
        $redis = Yii::$app->redis;
        $redis->sAdd(KEY_UPOS_USER_IDS_WHITE_LIST, self::TEST_USER_ID);
        $this->assertTrue(MSound::isBVCSoundCDN());
        $redis->sRem(KEY_UPOS_USER_IDS_WHITE_LIST, self::TEST_USER_ID);

        MSound::$is_upos_user = false;
        $env = getenv('DEPLOY_ENV', true) ?: '';
        // 测试 prod 环境，全量返回 bvc cdn 时，返回 playurl 签名地址
        putenv('DEPLOY_ENV=prod');
        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/4.8.7 (iOS;12.0;iPhone9,1)',
            'Cookie' => 'equip_id=0aff70c6-3e29-4475-81f8-f38bc38ded71'
        ]);
        $this->assertTrue(MSound::isBVCSoundCDN(true));

        // 测试 prod 环境，非灰度设备时，不返回 playurl 签名地址
        $this->assertFalse(MSound::isBVCSoundCDN());

        // 测试 prod 环境，为灰度设备时，返回 playurl 签名地址
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.8.7 (iOS;12.0;iPhone9,1)']);
        $this->assertTrue(MSound::isBVCSoundCDN());

        // 测试非 prod 环境不区分区域都返回 playurl 签名地址
        putenv('DEPLOY_ENV=pre');
        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/4.8.7 (iOS;12.0;iPhone9,1)',
            'Cookie' => 'equip_id=0aff70c6-3e29-4475-81f8-f38bc38ded71'
        ]);
        $this->assertTrue(MSound::isBVCSoundCDN());

        // 测试 iOS 4.8.7 之前版本不返回 playurl 签名地址
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.8.6 (iOS;12.0;iPhone9,1)']);
        $this->assertFalse(MSound::isBVCSoundCDN());

        // 还原环境变量
        putenv('DEPLOY_ENV=' . $env);
    }

    public function testIsUposUser()
    {
        // 测试参数为 null 的情况
        MSound::$is_upos_user = null;
        $this->assertFalse(MSound::isUposUser());

        // 测试白名单用户下发视频云音频签名地址
        MSound::$is_upos_user = null;
        self::loginByUserId(self::TEST_USER_ID);
        $redis = Yii::$app->redis;
        $redis->sAdd(KEY_UPOS_USER_IDS_WHITE_LIST, self::TEST_USER_ID);
        $this->assertTrue(MSound::isUposUser());

        // 测试非白名单用户不下发视频云音频签名地址
        MSound::$is_upos_user = null;
        $redis->sRem(KEY_UPOS_USER_IDS_WHITE_LIST, self::TEST_USER_ID);
        $this->assertFalse(MSound::isUposUser());
    }

    public function testUpdateSoundFavoriteCount()
    {
        // 测试需要批量更新的音频收藏数一致
        $test_album1 = Data::createTestAlbum();
        self::$test_delete_album_id1 = $test_album1->id;
        MSoundAlbumMap::collectSoundOrNot([self::$normal_sound_id], $test_album1, 1);
        // 获取音频原来的收藏数
        $origin_collected_count = (int)MSound::find()->select('favorite_count')
            ->where(['id' => self::$normal_sound_id])->scalar();
        // 更新音频的收藏数
        $data = MSound::updateSoundFavoriteCount([self::$normal_sound_id => 1]);
        // 断言受影响的行数
        $this->assertEquals(1, $data);
        // 断言音频现在的收藏数
        $now_collected_count = (int)MSound::find()->select('favorite_count')
            ->where(['id' => self::$normal_sound_id])->scalar();
        $this->assertEquals($origin_collected_count - 1, $now_collected_count);

        // 测试需要批量更新的音频收藏数都不一致
        $test_album1 = Data::createTestAlbum();
        self::$test_delete_album_id1 = $test_album1->id;
        MSoundAlbumMap::collectSoundOrNot([self::$normal_sound_id], $test_album1, 1);
        $test_album2 = Data::createTestAlbum();
        self::$test_delete_album_id2 = $test_album2->id;
        MSoundAlbumMap::collectSoundOrNot([self::$normal_sound_id, self::$live_sound_id], $test_album2, 1);
        // 获取音频原来的收藏数
        $origin_collected_info = MSound::find()->select('favorite_count')
            ->where(['id' => [self::$normal_sound_id, self::$live_sound_id]])->indexBy('id')->column();
        $data = MSound::updateSoundFavoriteCount([self::$normal_sound_id => 2, self::$live_sound_id => 1]);
        $now_collected_info = MSound::find()->select('favorite_count')
            ->where(['id' => [self::$normal_sound_id, self::$live_sound_id]])->indexBy('id')->column();
        // 断言受影响的行数
        $EXPECTED_AFFECTED_ROW = 2;
        $this->assertEquals($EXPECTED_AFFECTED_ROW, $data);
        $this->assertEquals((int)$origin_collected_info[self::$normal_sound_id] - $EXPECTED_AFFECTED_ROW,
            (int)$now_collected_info[self::$normal_sound_id]);
        $this->assertEquals((int)$origin_collected_info[self::$live_sound_id] - 1,
            (int)$now_collected_count[self::$live_sound_id]);
    }

    public function testGetRecommendedSounds()
    {
        $res = MSound::getRecommendedSounds([self::$test_sound_1->id, self::$test_sound_2->id]);
        $this->assertIsArray($res);
        $this->assertCount(2, $res);
        $this->assertArrayHasKeys(['id', 'front_cover', 'soundstr', 'intro', 'view_count', 'comment_count', 'all_comments', 'user_id', 'username'],
            $res[0]);
        $this->assertArrayNotHasKey('video', $res[0]);
        $this->assertEquals(self::$test_sound_1->id, $res[0]['id']);
        $this->assertArrayHasKeys(['id', 'front_cover', 'soundstr', 'intro', 'view_count', 'comment_count', 'all_comments', 'user_id', 'username', 'video'],
            $res[1]);
        $this->assertEquals(self::$test_sound_2->id, $res[1]['id']);
    }

    public function testSingleSound()
    {
        Tools::registerDramaMockResponseFunc('/rpc/api/get-drama-price-by-sound', function ($params) {
            switch ($params['sound_id']) {
                case 1:
                    return [
                        'drama_id' => 1,
                        'price' => 0,
                        'pay_type' => Drama::PAY_TYPE_FREE,
                        'episode' => [
                            'vip' => Drama::EPISODE_VIP_LIMIT,
                        ]
                    ];
                case 3:
                    return [
                        'drama_id' => 3,
                        'price' => 199,
                        'pay_type' => Drama::PAY_TYPE_EPISODE,
                        'episode' => [
                            'vip' => Drama::EPISODE_VIP_LIMIT,
                        ]
                    ];
                default:
                    return [
                        'drama_id' => 2,
                        'price' => 199,
                        'pay_type' => Drama::PAY_TYPE_DRAMA,
                        'vip_discount' => [
                            'rate' => 0.8,
                            'price' => 287,
                        ],
                        'episode' => [
                            'vip' => Drama::EPISODE_VIP_LIMIT,
                        ]
                    ];
            }
        });

        $expected_return = [
            'drama' => [
                'id' => 1,
                'name' => '剧集名称',
                'price' => 199,
            ],
            'current_id' => 2,
            'current_name' => '单集名称',
            'current_order' => 3,
        ];
        Tools::registerDramaMockResponseFunc('/rpc/api/get-drama-details-by-sound', function ($params) use ($expected_return) {
            if ($params['sound_id'] === 5) {
                return null;
            }
            if ($params['sound_id'] === 6) {
                throw new HttpException(404, '单集不存在');
            }
            return $expected_return;
        });

        // 测试未登录用户
        // 下载免费剧集下的音频
        $download = 1;
        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/7.0.1 (Android;7.0;Meizu M6 M6)',
            'equip_id' => self::TEST_EQUIP_ID,
        ]);
        $data = MSound::singleSound(1, $download);
        $this->assertEquals(MSound::SOUND_FREE, $data->need_pay);

        // 下载付费剧集下的免费音频
        $data = MSound::singleSound(4, $download);
        $this->assertEquals(MSound::SOUND_FREE, $data->need_pay);

        // 下载单集付费剧集下的付费音频
        $data = MSound::singleSound(2, $download);
        $this->assertEquals(MSound::SOUND_UNPAID, $data->need_pay);

        // 下载整剧付费音频
        $data = MSound::singleSound(3, $download);
        $this->assertEquals(MSound::SOUND_UNPAID, $data->need_pay);

        // 登录用户（会员用户）：用户购买了单集付费剧集（ID: 3）
        Tools::registerDramaMockResponseFunc('/rpc/api/get-drama-price-by-sound', function ($params) {
            switch ($params['sound_id']) {
                case 1:
                    return [
                        'drama_id' => 1,
                        'price' => 0,
                        'pay_type' => Drama::PAY_TYPE_FREE,
                        'episode' => [
                            'vip' => Drama::EPISODE_VIP_LIMIT,
                        ]
                    ];
                case 3:
                    return [
                        'drama_id' => 3,
                        'price' => 199,
                        'pay_type' => Drama::PAY_TYPE_EPISODE,
                        'episode' => [
                            'vip' => Drama::EPISODE_VIP_LIMIT,
                        ]
                    ];
                default:
                    return [
                        'drama_id' => 2,
                        'price' => 199,
                        'pay_type' => Drama::PAY_TYPE_DRAMA,
                        'vip_discount' => [
                            'rate' => 0.8,
                            'price' => 287,
                        ],
                        'episode' => [
                            'vip' => Drama::EPISODE_VIP_NOT,
                        ]
                    ];
            }
        });
        self::loginByUserId(2);
        // 免费剧下的音频
        $data = MSound::singleSound(1);
        // 断言返回 UP 主认证信息、音频标签、音频分类、音频插图
        $this->assertEquals(Mowangskuser::CONFIRM_GOLDEN_VIP, $data->authenticated);
        $this->assertNotEmpty($data->tags);
        $this->assertNotNull($data->catalog);
        $this->assertNotEmpty($data->pics);
        $this->assertEquals(MSound::SOUND_FREE, $data->need_pay);

        // 付费音频未付费
        $data = MSound::singleSound(2);
        // 断言返回 UP 主认证信息、音频标签、音频分类
        $this->assertEquals(Mowangskuser::CONFIRM_GOLDEN_VIP, $data->authenticated);
        $this->assertNotEmpty($data->tags);
        $this->assertNotNull($data->catalog);
        // 断言未返回音频插图
        $this->assertEmpty($data->pics);
        $this->assertEquals(MSound::SOUND_UNPAID, $data->need_pay);

        // 付费音频已付费
        $data = MSound::singleSound(3);
        // 断言返回 UP 主认证信息、音频标签、音频分类、音频插图
        $this->assertEquals(Mowangskuser::CONFIRM_GOLDEN_VIP, $data->authenticated);
        $this->assertNotEmpty($data->tags);
        $this->assertNotNull($data->catalog);
        $this->assertNotEmpty($data->pics);
        $this->assertEquals(MSound::SOUND_PAID, $data->need_pay);

        // 下载免费剧下的音频
        $data = MSound::singleSound(1, $download);
        $this->assertEquals(MSound::SOUND_FREE, $data->need_pay);

        // 下载整剧付费音频：用户未付费
        $data = MSound::singleSound(2, $download);
        $this->assertEquals(MSound::SOUND_UNPAID, $data->need_pay);
        // 下载其中免费音频
        $data = MSound::singleSound(4, $download);
        $this->assertEquals(MSound::SOUND_FREE, $data->need_pay);

        // 下载单集付费音频：用户已付费
        $data = MSound::singleSound(3, $download);
        $this->assertEquals(MSound::SOUND_PAID, $data->need_pay);

        $redis = Yii::$app->redis;
        $equip_play_key = $redis->generateKey(KEY_EQUIP_PLAY_USER_ID, 1);
        $redis->del($equip_play_key);
        // 登录用户（会员用户）：用户购买了整剧付费剧集（ID: 2）
        self::loginByUserId(1);
        // 下载整剧付费音频：用户已付费
        // 下载其中付费音频
        $data = MSound::singleSound(2, $download);
        $this->assertEquals(MSound::SOUND_PAID, $data->need_pay);
        $this->assertTrue($data->isDramaPaid);
        // 下载其中免费音频
        $data = MSound::singleSound(4, $download);
        $this->assertEquals(MSound::SOUND_FREE, $data->need_pay);
        $this->assertTrue($data->isDramaPaid);

        // 下载单集付费音频：用户未付费
        $data = MSound::singleSound(3, $download);
        $this->assertEquals(MSound::SOUND_UNPAID, $data->need_pay);
        // 验证音频所属剧集和单集的信息
        $this->assertNotNull($data->drama_id);
        $this->assertEquals($expected_return['drama']['id'], $data->drama_id);
        $this->assertNotNull($data->episode);
        $this->assertIsArray($data->episode);
        $this->assertNotEmpty($data->episode);
        $this->assertEquals($expected_return['current_id'], $data->episode['id']);
        $this->assertEquals($expected_return['current_name'], $data->episode['name']);
        $this->assertEquals($expected_return['current_order'], $data->episode['order']);
        $this->assertEquals($expected_return['drama']['id'], $data->episode['drama_id']);
        $this->assertEquals($expected_return['drama']['name'], $data->episode['drama_name']);

        // 测试单集不存在
        $data = MSound::singleSound(5, $download);
        $this->assertNull($data->drama_id);
        $this->assertNull($data->episode);

        // 测试 drama rpc 出错时
        $data = MSound::singleSound(6, $download);
        $this->assertNull($data->drama_id);
        $this->assertNull($data->episode);

        Tools::registerDramaMockResponseFunc('/rpc/api/get-drama-price-by-sound', function ($params) {
            switch ($params['sound_id']) {
                case 1:
                    return [
                        'drama_id' => 1,
                        'price' => 0,
                        'pay_type' => Drama::PAY_TYPE_FREE,
                        'episode' => [
                            'vip' => Drama::EPISODE_VIP_LIMIT,
                        ]
                    ];
                case 3:
                    return [
                        'drama_id' => 3,
                        'price' => 199,
                        'pay_type' => Drama::PAY_TYPE_EPISODE,
                        'episode' => [
                            'vip' => Drama::EPISODE_VIP_LIMIT,
                        ]
                    ];
                default:
                    return [
                        'drama_id' => 2,
                        'price' => 199,
                        'pay_type' => Drama::PAY_TYPE_DRAMA,
                        'vip_discount' => [
                            'rate' => 0.8,
                            'price' => 287,
                        ],
                        'episode' => [
                            'vip' => Drama::EPISODE_VIP_LIMIT,
                        ]
                    ];
            }
        });
        // 测试 Android 6.3.6 客户端版本向账号播放设备缓存集合中添加元素
        $equip_play_key = $redis->generateKey(KEY_EQUIP_PLAY_USER_ID, 1);
        $redis->del($equip_play_key);
        $redis->del(KEY_EQUIP_PLAY_LIMIT_USER_IDS_ALLOW_LIST);
        $TEST_EQUIP_ID = '228236f1-test-test-test-test2e6f8bc5';
        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/6.3.6 (Android;7.0;Meizu M6 M6)',
            'equip_id' => $TEST_EQUIP_ID,
        ]);
        $data = MSound::singleSound(2);
        $this->assertNull($data->limit_type);
        // 断言有播放地址相关字段
        $this->assertNotNull($data->soundurl);
        $this->assertNotNull($data->soundurl_32);
        $this->assertNotNull($data->soundurl_64);
        $this->assertNotNull($data->soundurl_128);
        // 断言元素
        $data = $redis->zRange($equip_play_key, 0, 1);
        $this->assertEquals(1, count($data));
        $this->assertEquals($TEST_EQUIP_ID, $data[0]);

        // 测试 Android 6.3.5 客户端版本触发播放设备量上限跳转【同时播放设备超限】提示音
        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/6.3.5 (Android;7.0;Meizu M6 M6)',
            'equip_id' => self::TEST_EQUIP_ID,
        ]);
        $data = MSound::singleSound(2);
        $this->assertEquals(Yii::$app->params['sounds_for_notice']['equip_play_num_limit'], $data);

        // 测试 Android 6.3.6 客户端版本触发播放设备量上限，下发限制播放字段
        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/6.3.6 (Android;7.0;Meizu M6 M6)',
            'equip_id' => self::TEST_EQUIP_ID,
        ]);
        $data = MSound::singleSound(2);
        $this->assertEquals(MSound::LIMIT_TYPE_EQUIP_PLAY, $data->limit_type);
        // 断言没有播放地址相关字段
        $this->assertNull($data->soundurl);
        $this->assertNull($data->soundurl_32);
        $this->assertNull($data->soundurl_64);
        $this->assertNull($data->soundurl_128);

        // 测试未购付费音下载不会受限
        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/6.3.6 (Android;7.0;Meizu M6 M6)',
            'equip_id' => self::TEST_EQUIP_ID,
        ]);
        $data = MSound::singleSound(3);
        $this->assertEquals(MSound::SOUND_UNPAID, $data->need_pay);
        $this->assertEquals(MSound::LIMIT_TYPE_EQUIP_PLAY, $data->limit_type);

        // 测试下发剧集折扣信息
        $data = MSound::singleSound(2);
        $this->assertEquals(0.8, $data->vip_discount['rate']);
        $this->assertEquals(287, $data->vip_discount['price']);

        // 测试未下发会员收听限制
        $equip_play_key = $redis->generateKey(KEY_EQUIP_PLAY_USER_ID, 1);
        // 删除播放受限
        $redis->del($equip_play_key);
        $data = MSound::singleSound(2);
        // 断言没有任何限制播放类型
        $this->assertNull($data->limit_type);
        // 断言有播放地址相关字段
        $this->assertNotNull($data->soundurl);
        $this->assertNotNull($data->soundurl_32);
        $this->assertNotNull($data->soundurl_64);
        $this->assertNotNull($data->soundurl_128);

        // 测试传入一个参数后，App 播放时抹掉其他端的播放缓存
        $redis->del($equip_play_key);
        $redis->zAdd($equip_play_key, $_SERVER['REQUEST_TIME'], '228236f1-test-test-test-test2e6f8bc5');
        $this->assertTrue($redis->zScore($equip_play_key, '228236f1-test-test-test-test2e6f8bc5') > 0);
        $data = MSound::singleSound(2, 0, false, 2);
        // 断言没有任何限制播放类型
        $this->assertNull($data->limit_type);
        // 断言有播放地址相关字段
        $this->assertNotNull($data->soundurl);
        $this->assertNotNull($data->soundurl_32);
        $this->assertNotNull($data->soundurl_64);
        $this->assertNotNull($data->soundurl_128);
        // 断言 App 播放时其他端的播放缓存被抹除
        $this->assertFalse($redis->zScore($equip_play_key, '228236f1-test-test-test-test2e6f8bc5'));

        // 测试支持会员的客户端版本触发会员收听限制，下发会员收听限制字段
        $NOT_VIP_USER_ID = 999;  // 非会员用户
        self::loginByUserId($NOT_VIP_USER_ID);
        $data = MSound::singleSound(6);  // 音频所属剧集属于会员专享剧
        $this->assertEquals(MSound::LIMIT_TYPE_VIP_PLAY, $data->limit_type);
        // 断言没有播放地址相关字段
        $this->assertNull($data->soundurl);
        $this->assertNull($data->soundurl_32);
        $this->assertNull($data->soundurl_64);
        $this->assertNull($data->soundurl_128);

        // 测试 Android 6.3.5 客户端版本触发会员收听限制，跳转升级提示音
        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/6.3.5 (Android;7.0;Meizu M6 M6)',
            'equip_id' => self::TEST_EQUIP_ID,
        ]);
        $data = MSound::singleSound(6);
        $this->assertEquals(Yii::$app->params['sounds_for_notice']['upgrade_app'], $data);
    }

    public function testUnlockFree()
    {
        // 测试用户未登录的情况
        $test_sound_id = 234234;
        $sound = new MSound();
        $sound->id = $test_sound_id;
        $sound->limit_type = MSound::LIMIT_TYPE_VIP_PLAY;
        $sound->episode_vip = Drama::EPISODE_VIP_LIMIT;
        $sound->unlockFree(0);
        $this->assertEquals(MSound::LIMIT_TYPE_VIP_PLAY, $sound->limit_type);
        $this->assertEquals(Drama::EPISODE_VIP_LIMIT, $sound->episode_vip);

        // 测试会员专享剧集限时解锁，用户已登录，未解锁的情况
        MUserUnlockElement::deleteAll(['element_id' => $test_sound_id]);
        $test_user_id = 2333;
        $sound->unlockFree($test_user_id);
        $this->assertEquals(MSound::LIMIT_TYPE_VIP_PLAY, $sound->limit_type);
        $this->assertEquals(Drama::EPISODE_VIP_LIMIT, $sound->episode_vip);

        // 测试会员专享剧集限时解锁，用户已登录，已解锁的情况
        Data::createMUserUnlockElement([
            'user_id' => $test_user_id,
            'element_id' => $test_sound_id,
        ]);
        $sound->unlockFree($test_user_id);
        $this->assertNull($sound->limit_type);
        $this->assertEquals(Drama::EPISODE_VIP_NOT_LIMIT, $sound->episode_vip);

        // 测试付费剧集限时解锁，用户已登录，已解锁的情况
        $sound = new MSound();
        $sound->id = $test_sound_id;
        $sound->pay_type = MSound::PAY_BY_DRAMA;
        $sound->need_pay = MSound::SOUND_UNPAID;
        $sound->limit_type = MSound::LIMIT_TYPE_DRAMA;
        $sound->unlockFree($test_user_id);
        $this->assertNull($sound->limit_type);
        $this->assertEquals(MSound::SOUND_FREE, $sound->pay_type);
        $this->assertEquals(MSound::SOUND_FREE, $sound->need_pay);
        $this->assertNull($sound->limit_type);

        // 测试付费剧集限时解锁，用户已登录，已解锁的情况
        $sound = new MSound();
        $sound->id = $test_sound_id;
        $sound->pay_type = MSound::PAY_BY_DRAMA;
        $sound->need_pay = MSound::SOUND_UNPAID;
        $sound->episode_vip = Drama::EPISODE_VIP_LIMIT;
        $sound->limit_type = MSound::LIMIT_TYPE_DRAMA;
        $sound->unlockFree($test_user_id);
        $this->assertNull($sound->limit_type);
        $this->assertEquals(MSound::SOUND_FREE, $sound->pay_type);
        $this->assertEquals(MSound::SOUND_FREE, $sound->need_pay);
    }

    public function testFillSoundDramaInfo()
    {
        $expected_return = [
            'drama' => [
                'id' => 1,
                'name' => '剧集名称',
                'price' => 199,
            ],
            'current_id' => 1,
            'current_name' => '单集名称',
            'current_order' => 3,
        ];
        Tools::registerDramaMockResponseFunc('/rpc/api/get-drama-details-by-sound', function ($params) use ($expected_return) {
            if ($params['sound_id'] === 2) {
                return null;
            }
            if ($params['sound_id'] === 3) {
                throw new HttpException(404, '剧集不存在');
            }
            return $expected_return;
        });

        $sound = new MSound();
        // 测试补充音频所属剧集和单集的信息
        MSound::fillSoundDramaInfo($sound, 1, 1);
        $this->assertNotNull($sound->drama_id);
        $this->assertEquals($expected_return['drama']['id'], $sound->drama_id);
        $this->assertNotNull($sound->episode);
        $this->assertIsArray($sound->episode);
        $this->assertNotEmpty($sound->episode);
        $this->assertEquals($expected_return['current_id'], $sound->episode['id']);
        $this->assertEquals($expected_return['current_name'], $sound->episode['name']);
        $this->assertEquals($expected_return['current_order'], $sound->episode['order']);
        $this->assertEquals($expected_return['drama']['id'], $sound->episode['drama_id']);
        $this->assertEquals($expected_return['drama']['name'], $sound->episode['drama_name']);

        // 测试单集不存在时
        $sound = new MSound();
        MSound::fillSoundDramaInfo($sound, 2, 1);
        $this->assertNull($sound->drama_id);
        $this->assertNull($sound->episode);

        // 测试 drama rpc 出错时
        $sound = new MSound();
        MSound::fillSoundDramaInfo($sound, 3, 1);
        $this->assertNull($sound->drama_id);
        $this->assertNull($sound->episode);
    }

    public function testCheckEquipPlayNumLimit()
    {
        self::loginByUserId();

        // 测试未登录用户不受限制
        $sound = new MSound();
        $this->assertFalse(MSound::checkEquipPlayNumLimit($sound, 0, '', true, 0));

        // 测试下载不受限制
        $sound->pay_type = MSound::PAY_BY_DRAMA;
        $sound->episode_vip = Drama::EPISODE_VIP_NOT;
        $this->assertFalse(MSound::checkEquipPlayNumLimit($sound, self::TEST_USER_ID,
            '', false, 1));

        // 测试非会员剧的免费音不受限制
        $sound->pay_type = MSound::SOUND_FREE;
        $sound->episode_vip = Drama::EPISODE_VIP_NOT;
        $this->assertFalse(MSound::checkEquipPlayNumLimit($sound, self::TEST_USER_ID,
            '', true, 0));

        // 测试会员剧（会员专享剧、双模式剧）的试听音不受限制
        $sound->pay_type = MSound::SOUND_FREE;
        $sound->episode_vip = Drama::EPISODE_VIP_NOT_LIMIT;
        $this->assertFalse(MSound::checkEquipPlayNumLimit($sound, self::TEST_USER_ID,
            '', true, 0));

        $env = getenv('DEPLOY_ENV', true) ?: '';
        // 设置内网环境
        putenv('DEPLOY_ENV=pre');
        // 测试内网不受限制
        $sound->episode_vip = Drama::EPISODE_VIP_LIMIT;
        $this->assertFalse(MSound::checkEquipPlayNumLimit($sound, self::TEST_USER_ID,
            '', true, 0));
        putenv('DEPLOY_ENV=' . $env);

        // 测试用户白名单不受限制
        $redis = Yii::$app->redis;
        $redis->sAdd(KEY_EQUIP_PLAY_LIMIT_USER_IDS_ALLOW_LIST, self::TEST_USER_ID);
        $this->assertFalse(MSound::checkEquipPlayNumLimit($sound, self::TEST_USER_ID,
            '', true, 0));
        $redis->sRem(KEY_EQUIP_PLAY_LIMIT_USER_IDS_ALLOW_LIST, self::TEST_USER_ID);

        // 更新用户为蓝 V
        $user_confirm = (int)Mowangskuser::find()->select('confirm')
            ->where(['id' => self::TEST_USER_ID])
            ->scalar();
        $update_confirm = Mowangskuser::CONFIRM_BLUE_VIP << Mowangskuser::VIP_BIT_OFFSET_MASK[0];
        Mowangskuser::updateByPk(self::TEST_USER_ID, ['confirm' => $update_confirm]);
        // 测试蓝 V 用户不受限制
        $this->assertFalse(MSound::checkEquipPlayNumLimit($sound, self::TEST_USER_ID,
            '', true, 0));
        Mowangskuser::updateByPk(self::TEST_USER_ID, ['confirm' => $user_confirm]);

        // 测试向账号播放设备缓存集合中添加元素
        $equip_play_key = $redis->generateKey(KEY_EQUIP_PLAY_USER_ID, self::TEST_USER_ID);
        $redis->del($equip_play_key);
        $this->assertFalse(MSound::checkEquipPlayNumLimit($sound, self::TEST_USER_ID,
            self::TEST_EQUIP_ID, false, 0));
        // 断言元素
        $data = $redis->zRange($equip_play_key, 0, 1);
        $this->assertEquals(1, count($data));
        $this->assertEquals(self::TEST_EQUIP_ID, $data[0]);

        // 测试另一台设备受限制
        $this->assertTrue(MSound::checkEquipPlayNumLimit($sound, self::TEST_USER_ID,
            '228236f1-test-test-test-test2e6f8bc5', false, 0));
        // 断言元素
        $data = $redis->zRange($equip_play_key, 0, 1);
        $this->assertEquals(1, count($data));
        $this->assertEquals(self::TEST_EQUIP_ID, $data[0]);
    }

    public function testFillLimitType()
    {
        // 测试没有限制
        $sound = new MSound();
        $sound->pay_type = MSound::SOUND_FREE;
        $sound->fillLimitType(0, 0);  // 0 表示用户未登录
        $this->assertNull($sound->limit_type);

        // 测试非会员剧下发整剧付费限制
        $sound = new MSound();
        $sound->pay_type = MSound::PAY_BY_DRAMA;
        $sound->need_pay = MSound::SOUND_UNPAID;
        $sound->fillLimitType(0, 0);  // 0 表示用户未登录
        $this->assertEquals(MSound::LIMIT_TYPE_DRAMA, $sound->limit_type);

        // 测试非会员剧下发单集付费限制
        $sound = new MSound();
        $sound->pay_type = MSound::PAY_BY_SOUND;
        $sound->need_pay = MSound::SOUND_UNPAID;
        $sound->fillLimitType(0, 0);
        $this->assertEquals(MSound::LIMIT_TYPE_EPISODE, $sound->limit_type);

        // 测试双模式剧、用户不是会员、未购买剧集，下发会员收听限制
        $NOT_VIP_USER_ID = 3;
        MUserVip::deleteAll(['user_id' => $NOT_VIP_USER_ID]);
        self::loginByUserId($NOT_VIP_USER_ID);
        $sound = new MSound();
        $sound->pay_type = MSound::PAY_BY_DRAMA;
        $sound->episode_vip = Drama::EPISODE_VIP_LIMIT;
        $sound->need_pay = MSound::SOUND_UNPAID;
        $sound->fillLimitType($NOT_VIP_USER_ID, 0);
        $this->assertEquals(MSound::LIMIT_TYPE_VIP_PLAY, $sound->limit_type);

        // 测试双模式剧、用户不是会员、未购买单集，下发会员收听限制
        $sound = new MSound();
        $sound->pay_type = MSound::PAY_BY_SOUND;
        $sound->episode_vip = Drama::EPISODE_VIP_LIMIT;
        $sound->need_pay = MSound::SOUND_UNPAID;
        $sound->fillLimitType($NOT_VIP_USER_ID, 0);
        $this->assertEquals(MSound::LIMIT_TYPE_VIP_PLAY, $sound->limit_type);

        // 测试双模式剧、用户不是会员、已购买剧集，不下发会员收听限制
        $sound = new MSound();
        $sound->pay_type = MSound::PAY_BY_DRAMA;
        $sound->episode_vip = Drama::EPISODE_VIP_LIMIT;
        $sound->need_pay = MSound::SOUND_PAID;
        $sound->fillLimitType($NOT_VIP_USER_ID, 0);
        $this->assertNull($sound->limit_type);

        // 测试会员专享剧、用户不是会员，下发会员收听限制
        $sound = new MSound();
        $sound->episode_vip = Drama::EPISODE_VIP_LIMIT;
        $sound->fillLimitType($NOT_VIP_USER_ID, 0);
        $this->assertEquals(MSound::LIMIT_TYPE_VIP_PLAY, $sound->limit_type);

        // 测试双模式剧、用户是会员、未购买剧集，下发播放受限
        $VIP_USER_ID = 1;
        self::loginByUserId($VIP_USER_ID);
        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/6.3.6 (Android;7.0;Meizu M6 M6)',
            'equip_id' => self::TEST_EQUIP_ID,
        ]);
        $redis = Yii::$app->redis;
        $equip_play_key = $redis->generateKey(KEY_EQUIP_PLAY_USER_ID, $VIP_USER_ID);
        $redis->del($equip_play_key);
        $redis->zAdd($equip_play_key, $_SERVER['REQUEST_TIME'], '228236f1-test-test-test-test2e6f8bc5');
        $sound = new MSound();
        $sound->pay_type = MSound::PAY_BY_DRAMA;
        $sound->episode_vip = Drama::EPISODE_VIP_LIMIT;
        $sound->need_pay = MSound::SOUND_UNPAID;
        $sound->fillLimitType($VIP_USER_ID, 0);
        $this->assertEquals(MSound::LIMIT_TYPE_EQUIP_PLAY, $sound->limit_type);

        // 测试双模式剧、用户是会员、已购买剧集，下发播放受限
        $redis->del($equip_play_key);
        $redis->zAdd($equip_play_key, $_SERVER['REQUEST_TIME'], '228236f1-test-test-test-test2e6f8bc5');
        $sound = new MSound();
        $sound->pay_type = MSound::PAY_BY_DRAMA;
        $sound->episode_vip = Drama::EPISODE_VIP_LIMIT;
        $sound->need_pay = MSound::SOUND_PAID;
        $sound->fillLimitType($VIP_USER_ID, 0);
        $this->assertEquals(MSound::LIMIT_TYPE_EQUIP_PLAY, $sound->limit_type);

        // 测试会员专享剧、用户是会员，下发播放受限
        $redis->del($equip_play_key);
        $redis->zAdd($equip_play_key, $_SERVER['REQUEST_TIME'], '228236f1-test-test-test-test2e6f8bc5');
        $sound = new MSound();
        $sound->episode_vip = Drama::EPISODE_VIP_LIMIT;
        $sound->fillLimitType($VIP_USER_ID, 0);
        $this->assertEquals(MSound::LIMIT_TYPE_EQUIP_PLAY, $sound->limit_type);

        // 测试会员专享剧、用户是会员，下载时不受限
        $sound = new MSound();
        $sound->episode_vip = Drama::EPISODE_VIP_LIMIT;
        $sound->fillLimitType($VIP_USER_ID, 1);
        $this->assertNull($sound->limit_type);
    }
}
