<?php

namespace tests\forms;

use app\forms\GoodsForm;
use app\forms\TransactionForm;
use app\forms\TransactionFormFukubukuro;
use app\forms\TransactionFormRebateGift;
use app\models\Balance;
use app\models\PayAccount;
use app\models\TransactionItemsLog;
use app\models\TransactionLog;
use Exception;
use tests\components\UnitTestCase;
use Yii;
use yii\helpers\Json;

class TransactionFormRebateGiftTest extends UnitTestCase
{
    /**
     * @var null|TransactionLog
     */
    private static $transaction_log_fukubukuro = null;

    /**
     * @var null|TransactionFormRebateGift
     */
    private static $send_gift_with_context_form = null;
    /**
     * @var null|TransactionFormRebateGift
     */
    private static $send_gift_with_no_context_form = null;

    private static $origin_db;
    private static $origin_paydb;

    private static function setOriginDbs(): void
    {
        self::$origin_db = Yii::$app->db;
        self::$origin_paydb = Yii::$app->paydb;
        Yii::$app->set('db', Yii::$app->sqlitedb);
        Yii::$app->set('paydb', Yii::$app->sqlite_paydb);
    }

    private static function resetOriginDbs(): void
    {
        Yii::$app->set('db', self::$origin_db);
        Yii::$app->set('paydb', self::$origin_paydb);
    }

    protected function _before()
    {
        parent::_before();
        self::setOriginDbs();
    }

    protected function _after()
    {
        parent::_after();
        self::resetOriginDbs();
    }

    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
        self::setOriginDbs();
        if (!is_null(self::$transaction_log_fukubukuro)) {
            self::$transaction_log_fukubukuro->delete();
        }
        self::resetOriginDbs();
    }

    /**
     * @var array
     */
    private static $gifts_with_no_context;
    /**
     * @var array
     */
    private static $gifts_with_context;

    public function testScenarios()
    {
        $form = new TransactionFormRebateGift();
        $scenarios = $form->scenarios();
        $this->assertGreaterThanOrEqual(1, $scenarios);
        $this->assertArrayHasKey(TransactionFormRebateGift::SCENARIO_BUY, $scenarios);
    }

    public function testRules()
    {
        $form = new TransactionFormRebateGift();
        $rules = $form->rules();
        $this->assertGreaterThanOrEqual(2, $rules);
    }

    public function testAttributeLabels()
    {
        $form = new TransactionFormRebateGift();
        $labels = $form->attributeLabels();
        $this->assertGreaterThanOrEqual(1, $labels);
    }

    public function testCheckGifts()
    {
        $form = new TransactionFormRebateGift();
        $form->checkGifts();
        $this->assertEquals('没有要送出的礼物', $form->getFirstError('gifts'));
        $form->clearErrors();

        $form = new TransactionFormFukubukuro(TransactionFormFukubukuro::SCENARIO_BUY);
        $buyer = Balance::getByPk(self::TEST_USER_ID);
        $buyer->updateCounters(['googlepay' => 1200, 'all_coin' => 1200, 'all_topup' => 1200]);
        $form->load([
            'from_id' => self::TEST_USER_ID,
            'gift_id' => 3,
            'price' => 1200,
            'title' => '福袋',
            'noble' => 0,
        ], '');
        $ret = $form->buy();
        $this->assertIsArray($ret);
        $this->assertArrayHasKeys(['transaction_id', 'balance', 'live_noble_balance', 'price', 'context'], $ret);

        self::$transaction_log_fukubukuro = TransactionLog::findOne(['id' => $ret['transaction_id']]);
        $this->assertNotNull(self::$transaction_log_fukubukuro);

        $common_coins = [];
        foreach (Balance::balanceFields() as $field) {
            $coin_num = self::$transaction_log_fukubukuro->{$field . '_coin'};
            if ($coin_num > 0) {
                $common_coins[$field] = $coin_num;
            }
        }
        self::$gifts_with_context = [
            [
                'id' => 3,
                'title' => '福袋礼物 A',
                'price' => 50,
                'num' => 2,
                'context' => Json::encode([
                    'type' => self::$transaction_log_fukubukuro->type,
                    'attr' => self::$transaction_log_fukubukuro->attr,
                    'transaction_id' => self::$transaction_log_fukubukuro->id,
                    'tax' => self::$transaction_log_fukubukuro->tax,
                    'common_coins' => $common_coins,
                    'price' => self::$transaction_log_fukubukuro->all_coin,
                ]),
            ],
            [
                'id' => 3,
                'title' => '福袋礼物 B',
                'price' => 100,
                'num' => 1,
                'context' => Json::encode([
                    'type' => self::$transaction_log_fukubukuro->type,
                    'attr' => self::$transaction_log_fukubukuro->attr,
                    'transaction_id' => self::$transaction_log_fukubukuro->id,
                    'tax' => self::$transaction_log_fukubukuro->tax,
                    'common_coins' => $common_coins,
                    'price' => self::$transaction_log_fukubukuro->all_coin,
                ]),
            ],
        ];
        $form = new TransactionFormRebateGift();
        $form->load([
            'from_id' => self::TEST_USER_ID,
            'to_id' => 2,
            'gifts' => self::$gifts_with_context,
        ], '');
        $form->checkGifts();
        $this->assertEmpty($form->getErrors());
        foreach ($form->gifts as $gift) {
            $this->assertInstanceOf(GoodsForm::class, $gift);
        }
        self::$send_gift_with_context_form = $form;

        // 不带 context 的白给礼物
        self::$gifts_with_no_context = [
            [
                'id' => 99,
                'title' => '普通白给礼物',
                'price' => 100,
                'num' => 2,
            ],
        ];
        $form = new TransactionFormRebateGift();
        $form->load([
            'from_id' => self::TEST_USER_ID,
            'to_id' => 2,
            'gifts' => self::$gifts_with_no_context,
        ], '');
        $form->checkGifts();
        $this->assertEmpty($form->getErrors());
        foreach ($form->gifts as $gift) {
            $this->assertInstanceOf(GoodsForm::class, $gift);
        }
        self::$send_gift_with_no_context_form = $form;
    }

    /**
     * @depends testCheckGifts
     */
    public function testSendGift()
    {
        self::$send_gift_with_context_form->gifts = self::$gifts_with_context;
        $ret = self::$send_gift_with_context_form->sendGift();
        $this->assertIsArray($ret);
        $this->assertArrayHasKeys(['transaction_id', 'balance', 'live_noble_balance'], $ret);
        $rebate_gift = TransactionLog::findOne(['id' => $ret['transaction_id']]);
        $this->assertNotNull($rebate_gift);
        $this->assertEquals(20, $rebate_gift->income);
        $this->assertEquals(3, $rebate_gift->num);
        $this->assertEquals('福袋礼物 A', $rebate_gift->title);

        $transaction_items_logs = TransactionItemsLog::find()->where([
            'tid' => $rebate_gift->id,
            'status' => TransactionLog::STATUS_SUCCESS,
        ])->all();
        $this->assertCount(2, $transaction_items_logs);
        $this->assertEquals($rebate_gift->all_coin, array_sum(array_column($transaction_items_logs, 'price')));
        $sum_income = $sum_tax = 0;
        foreach ($transaction_items_logs as $item) {
            /**
             * @var TransactionItemsLog $item
             */
            $this->assertArrayHasKeys(['income', 'tax', 'context_transaction_id', 'context_attr', 'context_type'], $item->more_detail);
            $this->assertEquals(self::$transaction_log_fukubukuro->attr, $item->more_detail['context_attr']);
            $this->assertEquals(self::$transaction_log_fukubukuro->type, $item->more_detail['context_type']);
            $sum_income += $item->more_detail['income'];
            $sum_tax += $item->more_detail['tax'];
        }
        $this->assertEquals($rebate_gift->income, $sum_income);
        $this->assertEquals($rebate_gift->tax, $sum_tax);

        // 不带 context 的白给礼物
        self::$send_gift_with_no_context_form->gifts = self::$gifts_with_no_context;
        $ret = self::$send_gift_with_no_context_form->sendGift();
        $this->assertIsArray($ret);
        $this->assertArrayHasKeys(['transaction_id', 'balance', 'live_noble_balance'], $ret);
        $rebate_gift = TransactionLog::findOne(['id' => $ret['transaction_id']]);
        $this->assertNotNull($rebate_gift);
        $this->assertEquals(20, $rebate_gift->income);
        $this->assertEquals($rebate_gift->calcTax([PayAccount::COIN_FIELD_ANDROID => 200]), $rebate_gift->tax);
        $this->assertEquals(2, $rebate_gift->num);
        $this->assertEquals('普通白给礼物', $rebate_gift->title);
    }

}
