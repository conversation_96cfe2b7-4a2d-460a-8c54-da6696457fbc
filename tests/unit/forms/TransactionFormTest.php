<?php

namespace tests\forms;

use app\forms\TransactionForm;
use app\forms\UserContext;
use app\models\Balance;
use app\models\Drama;
use app\models\Gift;
use app\models\GuildBalance;
use app\models\GuildLiveContract;
use app\models\Live;
use app\models\LuckyGift;
use app\models\PayAccount;
use app\models\Mowangskuser;
use app\models\TransactionLog;
use Exception;
use missevan\util\MUtils as MUtils2;
use tests\components\UnitTestCase;
use yii\web\HttpException;
use yii\db\Expression;
use Yii;

class TransactionFormTest extends UnitTestCase
{
    // 提问者悬赏金额（单位：钻石）
    const TEST_PRICE = 50000;
    // 直播礼物价格
    const GIFT_PRICE = 6;

    // 幸运签开箱花费钻石
    const LUCK_GIFT_PRICE = 300;
    // 幸运签宝箱礼物价值（钻）
    const LUCK_GIFT_REAL_PRICE = 160;

    // 接收白给礼物的主播 ID
    const TEST_LIVE_ID_RECEIVE_REBATE_GIFT = 1;
    // 接收幸运签礼物的主播 ID
    const TEST_LIVE_ID_RECEIVE_LUCKY_GIFT = 111;

    const TEST_USER_ID_NORMAL = 1;
    const TEST_USER_ID_BAN_TOPUP_AND_CONFIRM = 2;
    const TEST_USER_ID_BUY_LUCKY_GIFT = 980;

    /**
     * @var Gift|null
     */
    private static $rebate_gift = null;

    private static $origin_db;
    private static $origin_paydb;

    private static function setOriginDbs(): void
    {
        self::$origin_db = Yii::$app->db;
        self::$origin_paydb = Yii::$app->paydb;
        Yii::$app->set('db', Yii::$app->sqlitedb);
        Yii::$app->set('paydb', Yii::$app->sqlite_paydb);
    }

    private static function resetOriginDbs(): void
    {
        Yii::$app->set('db', self::$origin_db);
        Yii::$app->set('paydb', self::$origin_paydb);
    }

    protected function _before()
    {
        parent::_before();
        self::setOriginDbs();
    }

    protected function _after()
    {
        parent::_after();
        self::resetOriginDbs();
    }

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        self::setOriginDbs();
        self::cleanTestData();
        self::createTestData();
    }

    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
        self::setOriginDbs();
        self::cleanTestData();
        self::resetOriginDbs();
    }

    private static function createTestData()
    {
        Mowangskuser::updateAll(
            ['confirm' => new Expression('confirm &~ :confirm')],
            'id = :id',
            [
                ':id' => self::TEST_USER_ID_NORMAL,
                ':confirm' => Mowangskuser::CONFIRM_BAN_TOPUP_AND_CONSUME,
            ]
        );

        Mowangskuser::updateAll(
            ['confirm' => new Expression('confirm | :confirm')],
            'id = :id',
            [
                ':id' => self::TEST_USER_ID_BAN_TOPUP_AND_CONFIRM,
                ':confirm' => Mowangskuser::CONFIRM_BAN_TOPUP_AND_CONSUME,
            ]
        );
        $rebate_gift = new Gift([
            'name' => '测试的白给礼物',
            'type' => Gift::TYPE_LIVE_REBATE_GIFT,
            'price' => 1000,
        ]);
        if (!$rebate_gift->save()) {
            throw new Exception(MUtils2::getFirstError($rebate_gift));
        }
        self::$rebate_gift = $rebate_gift;

        Balance::updateByPk([self::TEST_LIVE_ID_RECEIVE_REBATE_GIFT, self::TEST_USER_ID_BUY_LUCKY_GIFT], [
            'in_ios' => 0,
            'new_live_profit' => 0,
            'new_all_live_profit' => 0,
        ]);
        GuildLiveContract::deleteAll([
            'live_id' => [self::TEST_LIVE_ID_RECEIVE_REBATE_GIFT, self::TEST_USER_ID_BUY_LUCKY_GIFT],
            'status' => GuildLiveContract::STATUS_CONTRACTION,
        ]);
    }

    private static function cleanTestData()
    {
        Mowangskuser::updateAll(
            ['confirm' => new Expression('confirm &~ :confirm')],
            [
                'id' => [
                    self::TEST_USER_ID_NORMAL,
                    self::TEST_USER_ID_BAN_TOPUP_AND_CONFIRM,
                ]
            ],
            [
                ':confirm' => Mowangskuser::CONFIRM_BAN_TOPUP_AND_CONSUME,
            ]
        );
        if (!is_null(self::$rebate_gift)) {
            Gift::deleteByPk(self::$rebate_gift->id);
            TransactionLog::deleteAll([
                'from_id' => self::TEST_USER_ID,
                'to_id' => self::TEST_LIVE_ID_RECEIVE_REBATE_GIFT,
                'gift_id' => self::$rebate_gift->id,
                'status' => TransactionLog::STATUS_SUCCESS,
                'attr' => TransactionLog::ATTR_LIVE_REBATE_GIFT,
            ]);
            TransactionLog::deleteAll([
                'from_id' => self::TEST_USER_ID,
                'to_id' => self::TEST_LIVE_ID_RECEIVE_LUCKY_GIFT,
                'status' => TransactionLog::STATUS_SUCCESS,
                'attr' => TransactionLog::ATTR_LIVE_LUCKY_GIFT,
            ]);
        }
    }

    public function testCheckPrice()
    {
        // 测试付费问答最少需付 30 个钻石
        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_FREEZE]);
        $form->price = 29;
        $form->checkPrice('price');
        $error = $form->getFirstError('price');
        $this->assertEquals('付费问答最少需付 30 个钻石', $error);

        // 测试打赏价格传入负数的情况
        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_THINGS]);
        $form->price = -10;
        $form->checkPrice('price');
        $error = $form->getFirstError('price');
        $this->assertEquals('价格不能小于 1', $error);
    }

    public function testCheckBuyer()
    {
        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_FREEZE]);
        // 用户不存在
        $form->from_id = self::TEST_USER_ID_NOT_EXIST;
        $form->checkBuyer('from_id');
        $error = $form->getFirstError('from_id');
        $this->assertEquals('用户不存在', $error);
        $form->clearErrors();

        // 正常情况
        $form->from_id = self::TEST_USER_ID_NORMAL;
        $form->checkBuyer('from_id');
        $error = $form->getFirstError('from_id');
        $this->assertNull($error);
        $form->clearErrors();

        // 账号被系统停封
        $form->from_id = self::TEST_USER_ID_BAN_TOPUP_AND_CONFIRM;
        $form->checkBuyer('from_id');
        $error = $form->getFirstError('from_id');
        $this->assertEquals('您的账号暂被系统停封，不可进行充值消费操作', $error);
    }

    public function testFreeze()
    {
        // 测试非付费问答场景抛出异常
        $this->assertThrowsWithMessage(Exception::class, '场景错误', function () {
            $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_CONFIRM]);
            $form->freeze();
        });

        // 测试付费问答最少需付 30 个钻石
        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_FREEZE]);
        $form->from_id = self::TEST_USER_ID;
        $form->to_id = 1;
        $form->price = 29;
        $data = $form->freeze();
        $this->assertFalse($data);

        // 给测试用户指定钻石数量
        Balance::updateAll(['android' => self::TEST_PRICE], ['id' => self::TEST_USER_ID]);
        // 测试付费问答成功
        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_FREEZE]);
        $form->from_id = self::TEST_USER_ID;
        $form->to_id = 1;
        $form->price = 30;
        $data = $form->freeze();
        $this->assertIsArray($data);
        $this->assertEquals($form->price, $data['price']);
        // 删除交易记录
        TransactionLog::deleteAll('id = :id', [':id' => $data['transaction_id']]);
    }

    public function testGenerateTransaction()
    {
        $this->subTestNormalTransaction();
        $this->subTestLuckyGiftTransaction();
    }

    private function subTestNormalTransaction()
    {
        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_GIFT]);
        $form->to_id = self::TEST_USER_ID;
        $form->from_id = self::TEST_USER2_ID;
        $form->price = self::GIFT_PRICE;
        $form->type = TransactionLog::TYPE_LIVE;
        $form->gift_id = 1;

        // 测试方法必须需在事务中调用
        $transaction = Yii::$app->db->beginTransaction();
        try {
            // 测试用户从未充值的情况
            Balance::deleteAll('id = :id', [':id' => self::TEST_USER3_ID]);
            $this->assertThrowsWithMessage(Exception::class, '您的余额不足，请充值后购买',
                function () use ($form) {
                    // 该方法需要在事务中调用
                    $form->generateTransaction(self::GIFT_PRICE, 1, TransactionLog::STATUS_SUCCESS);
                });

            // 测试用户余额不足的情况
            $balance = Balance::getByPk(self::TEST_USER2_ID);
            $form->price = $price = $balance->getTotalBalance() + self::GIFT_PRICE;
            $this->assertThrowsWithMessage(Exception::class, '您的余额不足，请充值后购买',
                function () use ($form, $price) {
                    // 该方法需要在事务中调用
                    $form->generateTransaction($price, 1, TransactionLog::STATUS_SUCCESS);
                });

            // 给测试用户指定钻石数量
            Balance::updateAll(
                ['android' => self::TEST_PRICE, 'all_coin' => self::TEST_PRICE],
                ['id' => self::TEST_USER2_ID]
            );
            [$tid, $balance, $tax, $costs] = $form->generateTransaction(self::GIFT_PRICE, 1, TransactionLog::STATUS_SUCCESS);
            $this->assertEquals(self::TEST_PRICE - self::GIFT_PRICE, $balance);
            $transaction_log = TransactionLog::findOne(['id' => $tid]);
            $this->assertNotNull($transaction_log);
            $this->assertEquals(self::GIFT_PRICE, $costs['android']);
            $this->assertEquals(self::GIFT_PRICE / DIAMOND_EXRATE, $transaction_log->income);
        } finally {
            // 测试数据不提交
            $transaction->rollBack();
        }
    }

    private function subTestLuckyGiftTransaction()
    {
        $luck_gift = LuckyGift::newInstance(
            221, 30, 10,
            Balance::profitUnitConversion(160, Balance::CONVERT_YUAN_TO_FEN)
        );

        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_LUCKY_GIFT]);
        $form->to_id = self::TEST_USER_ID;
        $form->from_id = self::TEST_USER_ID_BUY_LUCKY_GIFT;
        $form->price = $luck_gift->getTotalPrice();
        $form->type = TransactionLog::TYPE_LIVE;
        $form->attr = TransactionLog::ATTR_LIVE_LUCKY_GIFT;
        self::setPrivateProperty(
            $form,
            'luck_gift',
            $luck_gift
        );
        $form->gift_id = $luck_gift->getId();

        $transaction = Yii::$app->db->beginTransaction();
        try {
            // 给测试用户指定钻石数量
            $buyer = Balance::getByPk(self::TEST_USER_ID_BUY_LUCKY_GIFT);
            $buyer->setAttributes([
                'googlepay' => 100,
                'ios' => 50,
                'paypal' => 50,
                'tmallios' => 50,
                'android' => 70,
                'all_coin' => 320,
            ], false);
            if (!$buyer->save()) {
                throw new Exception(MUtils2::getFirstError($buyer));
            }
            [$tid, $balance, $tax, $costs] = $form->generateTransaction($form->price, 0.3, TransactionLog::STATUS_SUCCESS);
            $this->assertEquals($buyer->all_coin - $form->price, $balance);
            $transaction_log = TransactionLog::findOne(['id' => $tid]);
            $this->assertNotNull($transaction_log);
            $this->assertEquals(
                Balance::profitUnitConversion($luck_gift->getCreatorIncome(), Balance::CONVERT_FEN_TO_YUAN),
                $transaction_log->income
            );
        } finally {
            // 测试数据不提交
            $transaction->rollBack();
        }
    }

    /**
     * @depends testGenerateTransaction
     */
    public function testBuyLuckyGift()
    {
        $this->assertThrowsWithMessage(Exception::class, '场景错误', function () {
            $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_GIFT]);
            $form->buyLuckyGift();
        });

        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_LUCKY_GIFT]);
        $form->setAttributes([
            'from_id' => 888,
            'to_id' => 888,
            'price' => 30,
            'num' => -1,
            'gift_id' => 986,
            'title' => '风钤（幸运签）',
            'noble' => 1,
            'live_open_log_id' => '900150983cd24fb0d6963f7d28e',
        ]);
        $ret = $form->buyLuckyGift();
        $this->assertIsBool($ret);
        $this->assertFalse($ret);
        $this->assertEquals('数量必须为正整数', $form->getFirstError('num'));

        $form->num = 100;
        $this->assertThrowsWithMessage(HttpException::class, '不可以打赏自己', function () use ($form) {
            $form->buyLuckyGift();
        }, 403, 200020002);

        $buyer = Balance::getByPk(self::TEST_USER_ID_BUY_LUCKY_GIFT);
        $buyer->setAttributes([
            'ios' => 5000,
            'all_coin' => 5000,
        ], false);
        if (!$buyer->save()) {
            throw new Exception(MUtils2::getFirstError($buyer));
        }
        Balance::deleteAll(['id' => self::TEST_LIVE_ID_RECEIVE_LUCKY_GIFT]);

        $income = Balance::profitUnitConversion(120, Balance::CONVERT_YUAN_TO_FEN);
        Yii::$app->request->setBodyParams(['income' => $income]);
        $form->setAttributes([
            'from_id' => self::TEST_USER_ID_BUY_LUCKY_GIFT,
            'to_id' => self::TEST_LIVE_ID_RECEIVE_LUCKY_GIFT,
            'gift_id' => 986,
            'price' => 30,
            'num' => 100,
            'title' => '风钤（幸运签）',
        ]);
        $ret = $form->buyLuckyGift();
        $this->assertIsArray($ret);
        $this->assertArrayHasKeys(['transaction_id', 'price'], $ret);

        $this->assertEquals($form->price * $form->num, $ret['price']);
        $tradelog = TransactionLog::findOne(['id' => $ret['transaction_id']]);
        $this->assertNotNull($tradelog);
        $this->assertEquals(TransactionLog::TYPE_LIVE, $tradelog->type);
        $this->assertEquals(TransactionLog::ATTR_LIVE_LUCKY_GIFT, $tradelog->attr);
        $balance = Balance::getByPk(self::TEST_LIVE_ID_RECEIVE_LUCKY_GIFT);
        $live_profit = (Balance::profitUnitConversion($income, Balance::CONVERT_FEN_TO_YUAN) - $tradelog->tax)
            * Live::getRate(self::TEST_LIVE_ID_RECEIVE_LUCKY_GIFT);
        $this->assertEquals(
            $balance->new_live_profit,
            Balance::profitUnitConversion($live_profit, Balance::CONVERT_YUAN_TO_FEN)
        );
    }

    public function testGetErrorMessage()
    {
        $form = new TransactionForm();
        // 单集已购
        $form->type = TransactionLog::TYPE_SOUND;
        $ret = self::invokePrivateMethod(get_class($form), 'getErrorMessage', null, $form);
        $this->assertEquals(Yii::t('app/error', 'You have purchased this episode'), $ret);

        // 单集已购且退过款
        self::setPrivateProperty($form, 'is_refund', true);
        $ret = self::invokePrivateMethod(get_class($form), 'getErrorMessage', null, $form);
        $this->assertEquals(Yii::t('app/error', 'You have received refund for this drama, and the drama could not be purchased twice'), $ret);

        // 整剧已购且退过款
        $form->type = TransactionLog::TYPE_DRAMA;
        $ret = self::invokePrivateMethod(get_class($form), 'getErrorMessage', null, $form);
        $this->assertEquals(Yii::t('app/error', 'You have received refund for this drama, and the drama could not be purchased twice'), $ret);

        // 整剧已购
        self::setPrivateProperty($form, 'is_refund', false);
        $ret = self::invokePrivateMethod(get_class($form), 'getErrorMessage', null, $form);
        $this->assertEquals(Yii::t('app/error', 'You have purchased this drama'), $ret);

        // 猫耳男友已购
        $form->type = TransactionLog::TYPE_BOYFRIEND;
        $ret = self::invokePrivateMethod(get_class($form), 'getErrorMessage', null, $form);
        $this->assertEquals('您已经购买过该男友', $ret);

        // 语音季包已购
        $form->type = TransactionLog::TYPE_CARD_PACKAGE;
        $ret = self::invokePrivateMethod(get_class($form), 'getErrorMessage', null, $form);
        $this->assertEquals('您已经购买过该季包', $ret);

        // 其它
        $form->type = TransactionLog::TYPE_OMIKUJI;
        $ret = self::invokePrivateMethod(get_class($form), 'getErrorMessage', null, $form);
        $this->assertEquals(Yii::t('app/error', 'You have purchased this goods'), $ret);
    }

    public function testCalculateLiveRevenue()
    {
        /**
         * @var Balance $seller
         */
        $seller = Balance::find()->where('id < 100')->limit(1)->one();
        $original_revenue = [
            'in_ios' => $seller->in_ios,
            'in_android' => $seller->in_android,
            'new_live_profit' => $seller->new_live_profit,
            'new_all_live_profit' => $seller->new_all_live_profit,
        ];
        $PRICE = 500;
        $buyer_coin_costs = ['ios' => 150, 'android' => 350];
        $TAX = 50;
        $REVENUE_RATE = 0.2;

        $form = new TransactionForm();
        self::invokePrivateMethod(TransactionForm::class, 'calculateLiveRevenue',
            [$seller, $PRICE, $buyer_coin_costs, $TAX, $REVENUE_RATE], $form, true);
        $seller = Balance::findOne(['id' => $seller->id]);
        $profit_in_fen = Balance::profitUnitConversion(($PRICE / DIAMOND_EXRATE - $TAX) * $REVENUE_RATE, 1);
        $this->assertEquals($original_revenue, [
            'in_ios' => $seller->in_ios - $buyer_coin_costs['ios'],
            'in_android' => $seller->in_android - $buyer_coin_costs['android'],
            'new_live_profit' => $seller->new_live_profit - $profit_in_fen,
            'new_all_live_profit' => $seller->new_all_live_profit - $profit_in_fen,
        ]);
        $seller->updateAttributes($original_revenue);
    }

    public function testIsBuyerAndSellerSame()
    {
        $form = new TransactionForm();
        $form->load(['from_id' => 999, 'to_id' => 666], '');
        $this->assertFalse($form->isBuyerAndSellerSame());

        $form->load(['from_id' => 888, 'to_id' => 888], '');
        $this->assertTrue($form->isBuyerAndSellerSame());
    }

    public function testGetCreatorTradeInfo()
    {
        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_NOBLE]);

        $result = self::invokePrivateMethod(get_class($form), 'getCreatorTradeInfo', 0, $form);
        $this->assertIsArray($result);
        $this->assertCount(4, $result);
        $this->assertArrayHasKeys(['seller', 'rate', 'type', 'guild_id'], $result);
        $this->assertEquals(0, $result['rate']);
        $this->assertEquals(0, $result['guild_id']);
        $this->assertEquals(TransactionLog::TYPE_LIVE, $result['type']);

        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_BUY]);
        $result = self::invokePrivateMethod(get_class($form), 'getCreatorTradeInfo', 1, $form);
        $this->assertCount(4, $result);
        $this->assertArrayHasKeys(['seller', 'rate', 'type', 'guild_id'], $result);
        $this->assertEquals(1, $result['seller']['id']);
        $this->assertEquals(Live::LIVE_RATE, $result['rate']);
        $this->assertEquals(0, $result['guild_id']);
        $this->assertEquals(TransactionLog::TYPE_LIVE, $result['type']);

        $contract = GuildLiveContract::findOne(['status' => GuildLiveContract::CONTRACT_EFFECTIVE]);
        if (!$contract) {
            $contract = new GuildLiveContract([
                'guild_id' => 9090,
                'live_id' => 999000,
                'contract_start' => $_SERVER['REQUEST_TIME'],
                'contract_end' => $_SERVER['REQUEST_TIME'] + ONE_DAY,
                'rate' => 45,
                'kpi' => '',
                'type' => 1,
                'status' => GuildLiveContract::STATUS_CONTRACTION,
                'guild_owner' => 111,
                'guild_name' => '测试公会-111',
            ]);
            if (!$contract->save()) {
                throw new Exception(MUtils2::getFirstError($contract));
            }
        }

        $result = self::invokePrivateMethod(get_class($form), 'getCreatorTradeInfo', $contract->live_id, $form);
        $this->assertCount(4, $result);
        $this->assertArrayHasKeys(['seller', 'rate', 'type', 'guild_id'], $result);
        $this->assertEquals($contract->guild_id, $result['seller']['id']);
        $seller = GuildBalance::findOne(['id' => $contract->guild_id]);
        $this->assertEquals($seller->rate, $result['rate']);
        $this->assertEquals($contract->guild_id, $result['guild_id']);
        $this->assertEquals(TransactionLog::TYPE_GUILD_LIVE, $result['type']);
    }

    public function testHasVipDiscount()
    {
        // 测试不是会员
        $this->assertFalse(TransactionForm::hasVipDiscount(false, 10, ['rate' => 0.5]));

        // 测试是会员，但价格低于最低会员折扣价格
        $this->assertFalse(TransactionForm::hasVipDiscount(true, 4, ['rate' => 0.5]));

        // 测试是会员，价格高于最低会员折扣价格，剧集没有折扣信息
        $this->assertFalse(TransactionForm::hasVipDiscount(true, 10, null));

        // 测试是会员，价格高于最低会员折扣价格，剧集有折扣信息
        $this->assertTrue(TransactionForm::hasVipDiscount(true, 10, ['rate' => 0.5]));
    }

    public function testGetDramaPrice()
    {
        // 测试是会员但剧集没有折扣价格时
        $price = TransactionForm::getDramaPrice(true, Drama::PAY_TYPE_EPISODE, 10, null);
        $this->assertEquals(10, $price);

        // 测试是会员但总价小于 5 钻时
        $price = TransactionForm::getDramaPrice(true, Drama::PAY_TYPE_EPISODE, 4, ['rate' => 0.5]);
        $this->assertEquals(4, $price);

        // 测试是会员，总价大于等于 5 钻，剧集有折扣时（单集付费）
        $price = TransactionForm::getDramaPrice(true, Drama::PAY_TYPE_EPISODE, 5, ['rate' => 0.5]);
        $this->assertEquals(2, $price);

        // 测试是会员，总价大于等于 5 钻，剧集有折扣时（整剧付费）
        $price = TransactionForm::getDramaPrice(true, Drama::PAY_TYPE_EPISODE, 6,
            ['rate' => 0.5, 'price' => 3]);
        $this->assertEquals(3, $price);

        // 测试未知的付费类型
        $this->assertThrowsWithMessage(Exception::class, '未知的付费方式', function () {
            TransactionForm::getDramaPrice(true, 4, 10, ['rate' => 0.5, 'price' => 3]);
        });
    }

    public function testGetDramaEpisodesVipPrice()
    {
        // 测试正常情况
        $this->assertEquals(7, TransactionForm::getDramaEpisodesVipPrice(9, 0.8));

        // 测试取整后为 0 的情况，保底 1 钻
        $this->assertEquals(1, TransactionForm::getDramaEpisodesVipPrice(5, 0.1));
    }

    public function testSetTransactionLogDetail()
    {
        $tradelog = new TransactionLog();
        $tradelog->trade_detail = [
            'foo' => 'bar',
        ];
        $form = new TransactionForm();
        $form->user_context = new UserContext('Mozilla/5', '127.0.0.1');
        $form->live_open_log_id = 'test_live_open_log_id';
        $form->more = ['abc' => 'cde'];
        self::invokePrivateMethod(get_class($form), 'setTransactionLogDetail', $tradelog, $form);
        $this->assertArrayHasKeys(['foo', 'user_agent', 'ip', 'more'], $tradelog->trade_detail);
        $this->assertEquals([
            'foo' => 'bar',
            'user_agent' => 'Mozilla/5',
            'ip' => '127.0.0.1',
            'more' => [
                'abc' => 'cde',
                'live_open_log_id' => 'test_live_open_log_id',
            ],
        ], $tradelog->trade_detail);
    }
}
