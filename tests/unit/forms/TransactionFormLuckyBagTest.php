<?php

namespace tests\forms;

use app\forms\GoodsForm;
use app\forms\TransactionFormLuckyBag;
use app\models\Balance;
use app\models\Drama;
use app\models\Live;
use app\models\PayAccount;
use app\models\TransactionItemsLog;
use app\models\TransactionLog;
use Exception;
use missevan\util\MUtils as MUtils2;
use tests\components\util\Tools;
use tests\components\UnitTestCase;

class TransactionFormLuckyBagTest extends UnitTestCase
{
    /**
     * @var Balance
     */
    private static $original_balance;

    /**
     * @var int
     */
    private static $tid_to_delete = 0;

    /**
     * @var int
     */
    private static $test_transaction_id = 0;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        self::$original_balance = Balance::getByPk(self::TEST_USER_ID);
        $tradelog = new TransactionLog([
            'from_id' => self::TEST_USER_ID,
            'to_id' => 0,
            'gift_id' => 6,
            'title' => '广播剧福袋--魔道祖师第一季',
            'ios_coin' => 500,
            'android_coin' => 18,
            'all_coin' => 518,
            'income' => 51.8,
            'rate' => Live::LIVE_RATE,
            'attr' => TransactionLog::ATTR_LIVE_BUY_LUCKY_BAG,
            'type' => TransactionLog::TYPE_LIVE,
            'status' => TransactionLog::STATUS_SUCCESS,
            'num' => 2,
        ]);
        $tradelog->tax = $tradelog->calcTax(['ios' => 500, 'android' => 18]);
        if (!$tradelog->save()) {
            throw new Exception(MUtils2::getFirstError($tradelog));
        }
        self::$test_transaction_id = $tradelog->id;
        $itemslog = new TransactionItemsLog([
            'goods_id' => 15861,
            'goods_title' => '魔道祖师第一季',
            'goods_price' => 259,
            'goods_num' => 2,
            'user_id' => self::TEST_USER_ID,
            'status' => TransactionLog::STATUS_SUCCESS,
            'type' => TransactionLog::TYPE_LIVE,
            'tid' => $tradelog->id,
        ]);
        $itemslog->more_detail = [
            'transaction_type' => GoodsForm::TRANSACTION_TYPE_DRAMA,
            'common_coins' => ['ios' => 500, 'android' => 18],
            'successful_goods_num' => 2,
        ];
        if (!$itemslog->save()) {
            throw new Exception(MUtils2::getFirstError($itemslog));
        }
    }

    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
        $update_columns = [];
        foreach (Balance::balanceFields() as $column) {
            $update_columns[$column] = self::$original_balance->$column;
        }
        self::$original_balance->updateAttributes($update_columns);
        if (self::$tid_to_delete) {
            TransactionLog::deleteAll(['id' => self::$tid_to_delete]);
            TransactionItemsLog::deleteAll(['tid' => self::$tid_to_delete]);
        }
        if (self::$test_transaction_id) {
            TransactionLog::deleteAll(['id' => self::$test_transaction_id]);
            TransactionItemsLog::deleteAll(['tid' => self::$test_transaction_id]);
        }
    }

    public function testScenarios()
    {
        $form = new TransactionFormLuckyBag(TransactionFormLuckyBag::SCENARIO_BUY);
        $scenarios = $form->scenarios();
        $this->assertArrayHasKey(TransactionFormLuckyBag::SCENARIO_BUY, $scenarios);
        $this->assertEquals(['from_id', 'live_open_log_id', 'package_info', 'goods'], $scenarios[TransactionFormLuckyBag::SCENARIO_BUY]);
    }

    public function testRules()
    {
        $form = new TransactionFormLuckyBag(TransactionFormLuckyBag::SCENARIO_BUY);
        $rules = $form->rules();
        $this->assertGreaterThanOrEqual(3, count($rules));

        $rule = array_pop($rules);
        $this->assertEquals([['transaction_id'], 'integer'], $rule);

        $rule = array_pop($rules);
        $this->assertEquals(['goods', 'checkGoods'], $rule);

        $rule = array_pop($rules);
        $this->assertEquals(['package_info', 'checkPackageInfo'], $rule);
    }

    public function testCheckPackageInfo()
    {
        $form = new TransactionFormLuckyBag(TransactionFormLuckyBag::SCENARIO_BUY);
        $form->package_info = [
            'id' => 999,
            'title' => '广播剧福袋',
            'price' => 500,
            'num' => -1,
        ];
        $form->checkPackageInfo();
        $first_error = MUtils2::getFirstError($form);
        $this->assertEquals('商品数量的值必须不小于1。', $first_error);

        $form->clearErrors();

        $form->package_info['num'] = 2;
        $form->checkPackageInfo();
        $first_error = MUtils2::getFirstError($form);
        $this->assertEquals('单次只支持发送一个福袋', $first_error);
    }

    private static function mockDramaApi()
    {
        Tools::registerRpcApiResponseFunc('/rpc/api/get-drama-price', function ($params) {
            switch ($params['drama_id']) {
                case 9888:
                    return [
                        'code' => 0,
                        'info' => [
                            'drama_id' => $params['drama_id'],
                            'user_id' => 2,
                            'price' => 299,
                            'rate' => 0.35,
                            'name' => '杀破狼第一季',
                            'type' => Drama::PAY_TYPE_DRAMA,
                            'is_lossless' => false,
                        ],
                    ];
                case 15861:
                    return [
                        'code' => 0,
                        'info' => [
                            'drama_id' => $params['drama_id'],
                            'user_id' => 3,
                            'price' => 259,
                            'rate' => 0.35,
                            'name' => '魔道祖师第一季',
                            'type' => Drama::PAY_TYPE_DRAMA,
                            'is_lossless' => false,
                        ],
                    ];
            }
        });
    }

    public function testCheckGoods()
    {
        self::mockDramaApi();

        $form = new TransactionFormLuckyBag(TransactionFormLuckyBag::SCENARIO_BUY);
        $form->from_id = 346286;
        $form->goods = [];
        $form->checkGoods('goods');
        $this->assertEquals('福袋中没有对应的商品', MUtils2::getFirstError($form));

        $form->clearErrors();
        $form->goods = [
            ['id' => 15861, 'title' => '魔道祖师第一季', 'price' => 259, 'num' => 1, 'transaction_type' => GoodsForm::TRANSACTION_TYPE_DRAMA],
            ['id' => 15861, 'title' => '魔道祖师第一季', 'price' => 259, 'num' => 1, 'transaction_type' => GoodsForm::TRANSACTION_TYPE_DRAMA],
            ['id' => 9888, 'title' => '杀破狼第一季', 'price' => 299, 'num' => 1, 'transaction_type' => GoodsForm::TRANSACTION_TYPE_DRAMA],
        ];
        $form->checkGoods('goods');
        $this->assertEmpty(MUtils2::getFirstError($form));
        $this->assertCount(2, $form->goods);
        $goods_id_num_map = array_column($form->goods, 'num', 'id');
        $this->assertArrayHasKey(15861, $goods_id_num_map);
        $this->assertEquals(2, $goods_id_num_map[15861]);
        $this->assertArrayHasKey(9888, $goods_id_num_map);
        $this->assertEquals(1, $goods_id_num_map[9888]);
    }

    public function testIsNeedCalculcateCreatorRevenue()
    {
        $form = new TransactionFormLuckyBag(TransactionFormLuckyBag::SCENARIO_BUY);
        $this->assertFalse(self::invokePrivateMethod(get_class($form), 'isNeedCalculcateCreatorRevenue', null, $form));
    }

    public function testBeforeBuy()
    {
        self::mockDramaApi();

        $form = new TransactionFormLuckyBag(TransactionFormLuckyBag::SCENARIO_BUY);
        $form->from_id = 346286;
        $form->goods = [
            ['id' => 15861, 'title' => '魔道祖师第一季', 'price' => 259, 'num' => 1, 'transaction_type' => GoodsForm::TRANSACTION_TYPE_DRAMA],
            ['id' => 9888, 'title' => '杀破狼第一季', 'price' => 299, 'num' => 1, 'transaction_type' => GoodsForm::TRANSACTION_TYPE_DRAMA],
        ];
        $form->package_info = [
            'id' => 29,
            'title' => '广播剧福袋',
            'price' => 500,
            'num' => 1,
        ];
        $this->assertFalse($form->beforeBuy());
        $this->assertEquals('商品价格总额不一致', MUtils2::getFirstError($form));

        $form2 = new TransactionFormLuckyBag(TransactionFormLuckyBag::SCENARIO_BUY);
        $form2->from_id = 346286;
        $form2->goods = [
            ['id' => 15861, 'title' => '魔道祖师第一季', 'price' => 259, 'num' => 1, 'transaction_type' => GoodsForm::TRANSACTION_TYPE_DRAMA],
            ['id' => 9888, 'title' => '杀破狼第一季', 'price' => 299, 'num' => 1, 'transaction_type' => GoodsForm::TRANSACTION_TYPE_DRAMA],
        ];
        $form2->package_info = [
            'id' => 29,
            'title' => '广播剧福袋',
            'price' => 558,
            'num' => 1,
        ];
        $this->assertTrue($form2->beforeBuy());
        $this->assertEmpty(MUtils2::getFirstError($form2));
    }

    public function testGetOrderTitle()
    {
        $form = new TransactionFormLuckyBag(TransactionFormLuckyBag::SCENARIO_BUY);

        $goods1 = new GoodsForm();
        $goods1->title = '魔道祖师第一季';
        $goods1->num = 3;

        $goods2 = new GoodsForm();
        $goods2->title = '魔道祖师第一季';
        $goods2->num = 2;
        $form->goods = [$goods1, $goods2];

        $package = new GoodsForm();
        $package->title = '广播剧福袋';
        $form->package_info = $package;

        $order_title = self::invokePrivateMethod(get_class($form), 'getOrderTitle', null, $form);
        $this->assertEquals('广播剧福袋--魔道祖师第一季', $order_title);
    }

    public function testGetSuccessfulGoodsNum()
    {
        $item1 = new TransactionItemsLog([
            'more_detail' => ['successful_goods_num' => 50],
        ]);
        $item2 = new TransactionItemsLog([
            'more_detail' => ['successful_goods_num' => 30],
        ]);
        $items = [$item1, $item2];

        $form = new TransactionFormLuckyBag();
        $goods_num = self::invokePrivateMethod(get_class($form), 'getSuccessfulGoodsNum', $items, $form);
        $this->assertEquals(80, $goods_num);
    }

    public function testBuy()
    {
        self::mockDramaApi();
        self::$original_balance->updateAttributes(['ios' => 500, 'android' => 500, 'tmallios' => 0, 'paypal' => 0, 'googlepay' => 0]);

        $form = new TransactionFormLuckyBag(TransactionFormLuckyBag::SCENARIO_BUY);
        $form->from_id = self::$original_balance->id;
        $form->live_open_log_id = 'test-live-open-log-id';
        $form->goods = [
            ['id' => 15861, 'title' => '魔道祖师第一季', 'price' => 259, 'num' => 1, 'transaction_type' => GoodsForm::TRANSACTION_TYPE_DRAMA],
            ['id' => 9888, 'title' => '杀破狼第一季', 'price' => 299, 'num' => 2, 'transaction_type' => GoodsForm::TRANSACTION_TYPE_DRAMA],
        ];
        $form->package_info = [
            'id' => 29,
            'title' => '广播剧福袋',
            'price' => 857,
            'num' => 1,
        ];
        $result = $form->buy();
        $this->assertIsArray($result);
        $this->assertArrayHasKeys(['transaction_id', 'balance', 'price', 'context'], $result);

        self::$tid_to_delete = $result['transaction_id'];
        $tradelog = TransactionLog::findOne(['id' => $result['transaction_id']]);
        $this->assertNotNull($tradelog);
        $this->assertEquals(500, $tradelog->ios_coin);
        $this->assertEquals(357, $tradelog->android_coin);
        $this->assertEquals(857, $tradelog->all_coin);
        $this->assertEquals(85.7, $tradelog->income);
        $this->assertEquals($tradelog->calcTax(['ios' => 500, 'android' => 357]), $tradelog->tax);
        $this->assertEquals($form->from_id, $tradelog->from_id);
        $this->assertEquals(0, $tradelog->to_id);
        $this->assertEquals(29, $tradelog->gift_id);
        $this->assertEquals(3, $tradelog->num);
        $this->assertEquals(Live::LIVE_RATE, $tradelog->rate);
        $this->assertEquals(TransactionLog::TYPE_LIVE, $tradelog->type);
        $this->assertEquals(TransactionLog::ATTR_LIVE_BUY_LUCKY_BAG, $tradelog->attr);
        $this->assertEquals(TransactionLog::STATUS_SUCCESS, $tradelog->status);

        $balance = Balance::getByPk($form->from_id);
        $this->assertEquals(0, $balance->ios);
        $this->assertEquals(143, $balance->android);

        /**
         * @var array|[]TransactionItemsLog
         */
        $itemslog_list = TransactionItemsLog::find()->where(['tid' => $tradelog->id])->indexBy('goods_id')->all();
        $this->assertCount(2, $itemslog_list);
        $this->assertArrayHasKeys([9888, 15861], $itemslog_list);

        $this->assertEquals(2, $itemslog_list[9888]->goods_num);
        $this->assertEquals(299, $itemslog_list[9888]->goods_price);
        $this->assertEquals($form->from_id, $itemslog_list[9888]->user_id);
        $this->assertEquals(TransactionLog::STATUS_SUCCESS, $itemslog_list[9888]->status);
        $this->assertEquals(GoodsForm::TRANSACTION_TYPE_DRAMA, $itemslog_list[9888]->more_detail['transaction_type']);
        $this->assertEquals(2, $itemslog_list[9888]->more_detail['successful_goods_num']);
        $this->assertEquals(['ios' => 241, 'android' => 357], $itemslog_list[9888]->more_detail['common_coins']);

        $this->assertEquals(1, $itemslog_list[15861]->goods_num);
        $this->assertEquals(259, $itemslog_list[15861]->goods_price);
        $this->assertEquals($form->from_id, $itemslog_list[15861]->user_id);
        $this->assertEquals(TransactionLog::STATUS_SUCCESS, $itemslog_list[15861]->status);
        $this->assertEquals(GoodsForm::TRANSACTION_TYPE_DRAMA, $itemslog_list[15861]->more_detail['transaction_type']);
        $this->assertEquals(1, $itemslog_list[15861]->more_detail['successful_goods_num']);
        $this->assertEquals(['ios' => 259], $itemslog_list[15861]->more_detail['common_coins']);
    }

    public function testRefund()
    {
        // 第一次退款
        $original_balance = Balance::getByPk(self::TEST_USER_ID);

        $form = new TransactionFormLuckyBag(TransactionFormLuckyBag::SCENARIO_REFUND);
        $form->transaction_id = self::$test_transaction_id;
        $form->goods = [
            ['id' => 15861, 'title' => '魔道祖师第一季', 'price' => 259, 'num' => 1, 'transaction_type' => GoodsForm::TRANSACTION_TYPE_DRAMA],
        ];

        $result = $form->refund();
        $this->assertEmpty(MUtils2::getFirstError($form));
        $this->assertIsArray($result);
        $this->assertArrayHasKeys(['transaction_id', 'balance'], $result);

        // 交易记录
        $tradelog = TransactionLog::findOne(['id' => $result['transaction_id']]);
        $this->assertNotNull($tradelog);
        $this->assertEquals(259, $tradelog->ios_coin);
        $this->assertEquals(0, $tradelog->android_coin);
        $this->assertEquals(259, $tradelog->all_coin);
        $this->assertEquals(25.9, $tradelog->income);
        $this->assertEquals(1, $tradelog->num);
        $this->assertEquals($tradelog->calcTax(['ios' => 259, 'android' => 0]), $tradelog->tax);
        $this->assertEquals(TransactionLog::STATUS_SUCCESS, $tradelog->status);

        // 余额
        $new_balance = Balance::getByPk(self::TEST_USER_ID);
        $this->assertEquals(241, $new_balance->ios - $original_balance->ios);
        $this->assertEquals(18, $new_balance->android - $original_balance->android);

        // 交易明细
        $itemslog_list = TransactionItemsLog::findAll(['tid' => self::$test_transaction_id]);
        $this->assertCount(1, $itemslog_list);
        $this->assertEquals(1, $itemslog_list[0]->goods_num);
        $this->assertEquals(['ios' => 259, 'android' => 0], $itemslog_list[0]->more_detail['common_coins']);
        $this->assertEquals(['ios' => 241, 'android' => 18], $itemslog_list[0]->more_detail['common_coins_refund']);
        $this->assertEquals(1, $itemslog_list[0]->more_detail['refund_goods_num']);
        $this->assertEquals(1, $itemslog_list[0]->more_detail['successful_goods_num']);

        // 第二次退款
        $original_balance = Balance::getByPk(self::TEST_USER_ID);

        $form = new TransactionFormLuckyBag(TransactionFormLuckyBag::SCENARIO_REFUND);
        $form->transaction_id = self::$test_transaction_id;
        $form->goods = [
            ['id' => 15861, 'title' => '魔道祖师第一季', 'price' => 259, 'num' => 1, 'transaction_type' => GoodsForm::TRANSACTION_TYPE_DRAMA],
        ];
        $result = $form->refund();
        $this->assertIsArray($result);
        $this->assertArrayHasKeys(['transaction_id', 'balance'], $result);

        // 交易记录
        $tradelog = TransactionLog::findOne(['id' => $result['transaction_id']]);
        $this->assertNotNull($tradelog);
        $this->assertEquals(0, $tradelog->ios_coin);
        $this->assertEquals(0, $tradelog->android_coin);
        $this->assertEquals(0, $tradelog->all_coin);
        $this->assertEquals(0, $tradelog->income);
        $this->assertEquals(0, $tradelog->tax);
        $this->assertEquals(0, $tradelog->num);
        $this->assertEquals(TransactionLog::STATUS_REFUND_DIAMOND, $tradelog->status);

        // 余额
        $new_balance = Balance::getByPk(self::TEST_USER_ID);
        $this->assertEquals(259, $new_balance->ios - $original_balance->ios);
        $this->assertEquals(0, $new_balance->android - $original_balance->android);

        // 交易明细
        $itemslog_list = TransactionItemsLog::findAll(['tid' => self::$test_transaction_id]);
        $this->assertCount(1, $itemslog_list);
        $this->assertEquals(0, $itemslog_list[0]->goods_num);
        $this->assertEquals(['ios' => 0, 'android' => 0], $itemslog_list[0]->more_detail['common_coins']);
        $this->assertEquals(['ios' => 500, 'android' => 18], $itemslog_list[0]->more_detail['common_coins_refund']);
        $this->assertEquals(2, $itemslog_list[0]->more_detail['refund_goods_num']);
        $this->assertEquals(0, $itemslog_list[0]->more_detail['successful_goods_num']);
        $this->assertEquals(TransactionLog::STATUS_REFUND_DIAMOND, $itemslog_list[0]->status);
    }

}
