<?php

namespace app\tests\unit\forms;

use app\components\auth\douyin\AuthDouDian;
use app\components\auth\douyin\DouDianGoods;
use app\components\auth\douyin\DouDianOrder;
use app\components\auth\douyin\DouDianPushMsgContent;
use app\components\auth\douyin\DouDianSPIMsgContent;
use app\forms\DouDianGoodsForm;
use app\forms\DouDianTransactionForm;
use app\forms\UserContext;
use app\models\Balance;
use app\models\Drama;
use app\models\Mowangskuser;
use app\models\PayAccount;
use app\models\PayAccountPurchaseDetail;
use app\models\RechargeOrder;
use app\models\RechargeOrderDetail;
use app\models\TransactionLog;
use app\tests\components\util\SSOUtil;
use Exception;
use missevan\util\MUtils as MUtils2;
use tests\components\UnitTestCase;
use tests\components\util\Tools;
use yii\db\Expression;
use yii\helpers\Json;
use Yii;

class DouDianTransactionFormTest extends UnitTestCase
{
    const TEST_USER_ID_NEW_BUYER = 666;

    const TEST_BUYER_MOBILE = '***********';
    const TEST_BUYER_MOBILE2 = '***********';
    const TEST_DRAMA_ID = ********;

    const TEST_DOUDIAN_SHOP_ORDER_ID = 'test-doudian-shop-order-id';
    const TEST_DOUDIAN_SHOP_ORDER_ID2 = 'test-doudian-shop-order-id-2';

    /**
     * @var AuthDouDian
     */
    private static $client_doudian;
    /**
     * @var string
     */
    private static $shop_id;
    /**
     * @var DouDianTransactionForm
     */
    private static $form;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        self::cleanTestData();

        self::$client_doudian = AuthDouDian::client();
        self::$shop_id = self::getPrivateProperty(self::$client_doudian, 'shop_id');
        self::$form = new DouDianTransactionForm(self::$client_doudian);
    }

    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
        self::cleanTestData();
    }

    public static function cleanTestData()
    {
        Balance::deleteAll(['id' => self::TEST_USER_ID_NEW_BUYER]);
        Mowangskuser::deleteAll(['mobile' => self::TEST_BUYER_MOBILE]);
        SSOUtil::executeSQL('DELETE FROM m_user WHERE mobile = :mobile', [':mobile' => self::TEST_BUYER_MOBILE]);

        PayAccount::deleteAll(['scope' => PayAccount::SCOPE_COMMON]);
        $trade_ids = TransactionLog::find()->select('id')
            ->where(['type' => TransactionLog::TYPE_DRAMA, 'gift_id' => self::TEST_DRAMA_ID])
            ->column();
        TransactionLog::deleteAll(['id' => $trade_ids]);
        PayAccount::deleteAll(['tid' => $trade_ids]);
        PayAccountPurchaseDetail::deleteAll(['tid' => $trade_ids]);
        $orders_ids = RechargeOrder::find()->select('id')->where([
            'type' => RechargeOrder::TYPE_DOUDIAN,
            'tid' => [self::TEST_DOUDIAN_SHOP_ORDER_ID, self::TEST_DOUDIAN_SHOP_ORDER_ID2],
        ])->column();
        RechargeOrder::deleteAll(['id' => $orders_ids]);
        RechargeOrderDetail::deleteAll(['id' => $orders_ids]);
    }

    public function testIsLegalShopId()
    {
        $is_legal_shop_id = self::invokePrivateMethod(
            DouDianTransactionForm::class,
            'isLegalShopId',
            new DouDianPushMsgContent(Json::encode(['p_id' => 'test-order-id', 'shop_id' => self::$shop_id])),
            self::$form
        );
        $this->assertTrue($is_legal_shop_id);

        $is_legal_shop_id = self::invokePrivateMethod(
            DouDianTransactionForm::class,
            'isLegalShopId',
            new DouDianPushMsgContent(Json::encode(['p_id' => 'test-order-id', 'shop_id' => 'wrong-shop-id'])),
            self::$form
        );
        $this->assertFalse($is_legal_shop_id);
    }

    public function createOrderDataProvider()
    {
        Tools::registerDouDianMockResponseFunc(AuthDouDian::METHOD_ORDER_DETAIL, function ($body) {
            return [
                'data' => [
                    'shop_order_detail' => [
                        'order_id' => self::TEST_DOUDIAN_SHOP_ORDER_ID,
                        'order_status' => DouDianOrder::ORDER_STATUS_CREATE,
                        'order_amount' => 1990,
                        'pay_amount' => 1990,
                        'promotion_amount' => 0,
                        'sku_order_list' => [
                            [
                                'code' => self::TEST_DRAMA_ID,
                                'pay_amount' => 1990,
                                'item_num' => 1,
                                'c_biz' => DouDianGoods::CLIENT_BIZ_ALLY,
                                'c_biz_desc' => '精选联盟',
                                'author_id' => ****************,
                                'author_name' => '喵喵广播站(每周一三五晚七点直播）',
                            ]
                        ],
                        'encrypt_post_tel' => '...encrypt_post_tel...',
                        'biz' => DouDianOrder::ORDER_BIZ_VIRTUAL,
                        'biz_desc' => '虚拟',
                    ],
                ],
                'err_no' => 0,
                'log_id' => 'doudian-test-log-id',
                'message' => 'success',
            ];
        });

        Tools::registerDouDianMockResponseFunc(AuthDouDian::METHOD_ORDER_BATCH_DECRYPT, function ($body) {
            return [
                'data' => [
                    'custom_err' => [
                        'err_code' => 0,
                        'err_msg' => '',
                    ],
                    'decrypt_infos' => [
                        [
                            'auth_id' => '111111',
                            'cipher_text' => 'xxxxxxxxx',
                            'decrypt_text' => self::TEST_BUYER_MOBILE,
                            'err_msg' => '',
                            'err_no' => 0,
                        ],
                    ],
                ],
                'err_no' => 0,
                'log_id' => 'doudian-test-log-id',
                'message' => 'success',
            ];
        });
        Tools::registerDramaMockResponseFunc('/rpc/api/get-drama-details-by-id', function ($params) {
            return [
                'drama' => [
                    'id' => self::TEST_DRAMA_ID,
                    'name' => '在抖店销售的广播剧',
                    'price' => 199,
                    'pay_type' => Drama::PAY_TYPE_DRAMA,
                ],
            ];
        });

        return [
            [
                'msg' => new DouDianPushMsgContent(Json::encode([
                    'p_id' => self::TEST_DOUDIAN_SHOP_ORDER_ID,
                    'shop_id' => '123456',
                ])),
                'user_context' => new UserContext(self::TEST_USERAGENT, '175.45.20.138')
            ],
        ];
    }

    /**
     * @depends testIsLegalShopId
     * @depends testGetBuyer
     * @depends testGenerateTopupOrder
     * @dataProvider createOrderDataProvider
     *
     * @param DouDianPushMsgContent $msg
     * @param UserContext $user_context
     */
    public function testCreateOrder(DouDianPushMsgContent $msg, UserContext $user_context)
    {
        $this->assertEmpty(self::$form->createOrder($msg, $user_context));
        $order = RechargeOrder::find()
            ->where(['tid' => $msg->getShopOrderId(), 'status' => RechargeOrder::STATUS_CREATE])
            ->andWhere('create_time > :create_time', [':create_time' => $_SERVER['REQUEST_TIME'] - TEN_MINUTE])
            ->limit(1)
            ->one();
        $this->assertNotNull($order);
        $this->assertEquals(19.9, (float)$order->price);
        $this->assertNotNull(RechargeOrderDetail::findOne(['id' => $order->id, 'real_price' => Balance::profitUnitConversion(19.9, Balance::CONVERT_YUAN_TO_FEN)]));
    }

    public function testGetBuyer()
    {
        [$user, $is_new] = self::invokePrivateMethod(DouDianTransactionForm::class, 'getBuyer', self::TEST_BUYER_MOBILE, self::$form);
        $this->assertTrue($is_new);
        $this->assertInstanceOf(Mowangskuser::class, $user);

        [$user, $is_new] = self::invokePrivateMethod(DouDianTransactionForm::class, 'getBuyer', self::TEST_BUYER_MOBILE2, self::$form);
        $this->assertFalse($is_new);
        $this->assertInstanceOf(Mowangskuser::class, $user);
    }

    public function testGenerateTopupOrder()
    {
        $user_context = new UserContext(self::TEST_USERAGENT, '175.45.20.138');
        /**
         * @var $topup_order RechargeOrder
         */
        $topup_order = self::invokePrivateMethod(
            DouDianTransactionForm::class,
            'generateTopupOrder',
            ['test-shop-order-id', self::TEST_USER_ID, 9888, 5, $user_context],
            self::$form,
            true
        );
        $this->assertInstanceOf(RechargeOrder::class, $topup_order);
        $this->assertEquals(50, $topup_order->num);
        $this->assertEquals(5, $topup_order->price);

        $topup_order = self::invokePrivateMethod(
            DouDianTransactionForm::class,
            'generateTopupOrder',
            ['test-shop-order-id', self::TEST_USER_ID, 9888, 0.01, $user_context],
            self::$form,
            true
        );
        $this->assertInstanceOf(RechargeOrder::class, $topup_order);
        $this->assertEquals(1, $topup_order->num);
        $this->assertEquals(0.01, $topup_order->price);
    }

    public function testTopupAndConsume()
    {
        $buyer = Balance::getByPk(self::TEST_USER_ID_NEW_BUYER);
        $order = new RechargeOrder([
            'uid' => self::TEST_USER_ID_NEW_BUYER,
            'cid' => self::TEST_DRAMA_ID,
            'tid' => self::TEST_DOUDIAN_SHOP_ORDER_ID2,
            'ccy' => 0,
            'num' => 129,
            'price' => 12.9,
            'status' => RechargeOrder::STATUS_CREATE,
            'type' => RechargeOrder::TYPE_DOUDIAN,
            'origin' => RechargeOrder::TYPE_DOUDIAN,
        ]);
        if (!$order->save()) {
            throw new Exception(MUtils2::getFirstError($order));
        }
        $shop_order = new DouDianOrder([
            'shop_order_detail' => [
                'order_id' => 'test-order-id',
                'order_status' => DouDianOrder::ORDER_STATUS_DELIVERED,
                'order_amount' => 1290,
                'pay_amount' => 1290,
                'promotion_amount' => 0,
                'sku_order_list' => [
                    [
                        'code' => self::TEST_DRAMA_ID,
                        'order_amount' => 1290,
                        'pay_amount' => 1290,
                        'item_num' => 1,
                        'c_biz' => DouDianGoods::CLIENT_BIZ_ALLY,
                        'c_biz_desc' => '精选联盟',
                        'author_id' => ****************,
                        'author_name' => '喵喵广播站(每周一三五晚七点直播）',
                    ],
                ],
                'encrypt_post_tel' => 'xxxxx',
                'biz' => DouDianOrder::ORDER_BIZ_VIRTUAL,
                'biz_desc' => '虚拟',
            ],
        ], self::$client_doudian);
        $accounts = self::invokePrivateMethod(DouDianTransactionForm::class, 'topup', [$buyer, $order, $shop_order], self::$client_doudian, true);
        $goods = DouDianGoodsForm::initiateByDramaId($order->cid, self::TEST_USER_ID_NEW_BUYER);
        self::invokePrivateMethod(DouDianTransactionForm::class, 'consume', [$buyer, $order, $goods, $accounts], self::$client_doudian, true);

        $buyer = Balance::getByPk($order->uid);
        $this->assertEquals(129, $buyer->all_topup);
        $this->assertEquals(129, $buyer->all_consumption);
        $this->assertEquals(0, $buyer->all_coin);
        $this->assertTrue(TransactionLog::find()->where(['gift_id' => $order->cid, 'from_id' => $buyer->id, 'status' => TransactionLog::STATUS_SUCCESS])->exists());
    }

    public function payDataProvider()
    {
        Tools::registerDouDianMockResponseFunc(AuthDouDian::METHOD_ORDER_DETAIL, function ($body) {
            return [
                'data' => [
                    'shop_order_detail' => [
                        'order_id' => self::TEST_DOUDIAN_SHOP_ORDER_ID,
                        'order_status' => DouDianOrder::ORDER_STATUS_DELIVERED,
                        'order_amount' => 1990,
                        'pay_amount' => 1990,
                        'promotion_amount' => 0,
                        'sku_order_list' => [
                            [
                                'code' => self::TEST_DRAMA_ID,
                                'pay_amount' => 1990,
                                'item_num' => 1,
                                'c_biz' => DouDianGoods::CLIENT_BIZ_ALLY,
                                'c_biz_desc' => '精选联盟',
                                'author_id' => ****************,
                                'author_name' => '喵喵广播站(每周一三五晚七点直播）',
                            ],
                        ],
                        'encrypt_post_tel' => '...encrypt_post_tel...',
                        'biz' => DouDianOrder::ORDER_BIZ_VIRTUAL,
                        'biz_desc' => '虚拟',
                    ],
                ],
                'err_no' => 0,
                'log_id' => 'doudian-test-log-id',
                'message' => 'success',
            ];
        });
        Tools::registerDramaMockResponseFunc('/rpc/api/get-drama-price', function ($params) {
            return [
                'drama_id' => self::TEST_DRAMA_ID,
                'name' => '在抖店销售的广播剧',
                'price' => 199,
                'pay_type' => Drama::PAY_TYPE_DRAMA,
                'rate' => 0.5,
                'user_id' => *********,
            ];
        });

        return [
            [
                'msg' => new DouDianPushMsgContent(Json::encode([
                    'p_id' => self::TEST_DOUDIAN_SHOP_ORDER_ID,
                    'shop_id' => '123456',
                ])),
            ],
        ];
    }

    /**
     * @depends testIsLegalShopId
     * @depends testCreateOrder
     * @dataProvider payDataProvider
     *
     * @param DouDianPushMsgContent $msg
     */
    public function testPay(DouDianPushMsgContent $msg)
    {
        $this->assertEmpty(self::$form->pay($msg));
        $order = RechargeOrder::find()
            ->where(['tid' => $msg->getShopOrderId(), 'status' => RechargeOrder::STATUS_SUCCESS])
            ->andWhere('modified_time > :modified_time', [':modified_time' => $_SERVER['REQUEST_TIME'] - TEN_MINUTE])
            ->one();
        $this->assertNotNull($order);
        /**
         * @var TransactionLog $tradelog
         */
        $tradelog = TransactionLog::find()
            ->select('id, from_id, all_coin, income, tax')
            ->where([
                'gift_id' => self::TEST_DRAMA_ID,
                'from_id' => $order->uid,
                'to_id' => *********,
                'type' => TransactionLog::TYPE_DRAMA,
                'status' => TransactionLog::STATUS_SUCCESS,
            ])->one();
        $this->assertNotNull($tradelog);
        $this->assertGreaterThan(0, $tradelog->id);
        $this->assertEquals(299, $tradelog->all_coin);
        $this->assertEquals(29.9, $tradelog->income);

        $this->assertTrue(PayAccount::find()->where(['tid' => $order->id, 'scope' => PayAccount::SCOPE_COMMON])->exists());
        $this->assertTrue(PayAccountPurchaseDetail::find()->where(['tid' => $tradelog->id, 'status' => PayAccountPurchaseDetail::STATUS_CONFIRM])->exists());

        $balance = Balance::getByPk($tradelog->from_id);
        $this->assertEquals(299, $balance->all_consumption);
        $this->assertEquals(299, $balance->all_topup);
    }

    private function buyOnce(string $shop_order_id, Balance $buyer, int $drama_id, int $price_in_fen)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $price_in_yuan = Balance::profitUnitConversion($price_in_fen, Balance::CONVERT_FEN_TO_YUAN);
            $diamond_num = Balance::profitUnitConversion($price_in_yuan, Balance::CONVERT_YUAN_TO_DIAMOND);
            $topup_order = new RechargeOrder([
                'uid' => $buyer->id,
                'tid' => $shop_order_id,
                'cid' => $drama_id,
                'price' => $price_in_yuan,
                'num' => $diamond_num,
                'ccy' => 0,
                'status' => RechargeOrder::STATUS_SUCCESS,
                'type' => RechargeOrder::TYPE_DOUDIAN,
                'origin' => RechargeOrder::ORIGIN_DOUDIAN,
            ]);
            if (!$topup_order->save()) {
                throw new Exception(MUtils2::getFirstError($topup_order));
            }

            $pay_account = new PayAccount([
                'user_id' => $buyer->id,
                'tid' => $topup_order->id,
                'account_amount' => $topup_order->num,
                'balance' => 0,
                'scope' => PayAccount::SCOPE_COMMON,
                'type' => PayAccount::TYPE_COIN_INDEX_DOUDIAN,
                'withhold_order' => PayAccount::TYPE_COIN_INDEX_DOUDIAN,
                'expire_time' => $_SERVER['REQUEST_TIME'],
            ]);
            if (!$pay_account->save()) {
                throw new Exception(MUtils2::getFirstError($pay_account));
            }

            $tradelog = new TransactionLog([
                'from_id' => $buyer->id,
                'to_id' => 1,
                'type' => TransactionLog::TYPE_DRAMA,
                'status' => TransactionLog::STATUS_SUCCESS,
                'gift_id' => $drama_id,
                'title' => '测试剧集：' . $drama_id,
                'all_coin' => $diamond_num,
                'income' => $price_in_yuan,
                'rate' => 0.5,
                'attr' => TransactionLog::ATTR_DRAMA_DOUDIAN,
            ]);
            $tradelog->tax = $pay_account->getFee($tradelog);
            if (!$tradelog->save()) {
                throw new Exception(MUtils2::getFirstError($tradelog));
            }

            $purchase_detail = new PayAccountPurchaseDetail([
                'account_id' => $pay_account->id,
                'tid' => $tradelog->id,
                'purchase_amount' => $pay_account->account_amount,
                'fee_rate' => $pay_account->getFeeRate($tradelog),
                'status' => PayAccountPurchaseDetail::STATUS_CONFIRM,
            ]);
            if (!$purchase_detail->save()) {
                throw new Exception(MUtils2::getFirstError($purchase_detail));
            }

            $buyer->updateAttributes([
                'all_coin' => new Expression('all_coin + :coin', [':coin' => $topup_order->num]),
                'all_topup' => new Expression('all_topup + :coin', [':coin' => $topup_order->num]),
            ]);

            $transaction->commit();

            return [
                $topup_order,
                $pay_account,
                $tradelog,
                $purchase_detail,
            ];
        } catch (Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    private function buyTwice(string $shop_order_id, Balance $buyer, int $drama_id, int $price_in_fen)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $price_in_yuan = Balance::profitUnitConversion($price_in_fen, Balance::CONVERT_FEN_TO_YUAN);
            $diamond_num = Balance::profitUnitConversion($price_in_yuan, Balance::CONVERT_YUAN_TO_DIAMOND);
            $topup_order = new RechargeOrder([
                'uid' => $buyer->id,
                'tid' => $shop_order_id,
                'cid' => $drama_id,
                'price' => $price_in_yuan,
                'num' => $diamond_num,
                'ccy' => 0,
                'status' => RechargeOrder::STATUS_SUCCESS,
                'type' => RechargeOrder::TYPE_DOUDIAN,
                'origin' => RechargeOrder::ORIGIN_DOUDIAN,
            ]);
            if (!$topup_order->save()) {
                throw new Exception(MUtils2::getFirstError($topup_order));
            }

            $buyer->updateAttributes([
                'android' => new Expression('android + :coin', [':coin' => $topup_order->num]),
                'all_coin' => new Expression('all_coin + :coin', [':coin' => $topup_order->num]),
                'all_topup' => new Expression('all_topup + :coin', [':coin' => $topup_order->num]),
            ]);

            $transaction->commit();

            return [
                $topup_order,
            ];
        } catch (Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    public function testRefund()
    {
        RechargeOrder::deleteAll(['tid' => ['test-mobile-topup-shop-id-001', 'test-mobile-topup-shop-id-002']]);
        $TEST_BUYER_ID = 2;
        $buyer = Balance::getByPk($TEST_BUYER_ID);

        // 首次购买退款
        [$topup_order, $pay_account, $tradelog, $purchase_detail] = $this->buyOnce('test-mobile-topup-shop-id-001', $buyer, 9777, 1990);

        $balance_before = Balance::getByPk($buyer->id);
        $now = $_SERVER['REQUEST_TIME'];
        $_SERVER['REQUEST_TIME'] = $now + 1;
        $msg = new DouDianSPIMsgContent([
            'trade_order_no' => $topup_order->tid,
            'shop_id' => '123456',
        ]);
        $msg->setSellerOrder($topup_order);
        $this->assertEmpty(self::$form->refund($msg));

        $row_num = RechargeOrder::deleteAll(['id' => $topup_order->id, 'status' => RechargeOrder::STATUS_CANCELED])
            + PayAccount::deleteAll(['id' => $pay_account->id, 'balance' => 0])
            + TransactionLog::deleteAll(['id' => $tradelog->id, 'status' => TransactionLog::STATUS_REFUND])
            + PayAccountPurchaseDetail::deleteAll(['id' => $purchase_detail->id, 'status' => PayAccountPurchaseDetail::STATUS_CANCEL]);
        $this->assertEquals(4, $row_num);

        $buyer = Balance::getByPk($buyer->id);
        $this->assertEquals(max($balance_before->all_topup - $topup_order->num, 0), $buyer->all_topup);
        $this->assertEquals(max($balance_before->all_consumption - $topup_order->num, 0), $buyer->all_consumption);
        $this->assertEquals($balance_before->all_coin, $buyer->all_coin);
        $this->assertEquals($balance_before->android, $buyer->android);

        // 重复购买退款
        [$topup_order2] = $this->buyTwice('test-mobile-topup-shop-id-002', $buyer, 9777, 1990);
        $_SERVER['REQUEST_TIME'] = $now + 2;
        $balance_before = Balance::getByPk($buyer->id);
        $msg = new DouDianSPIMsgContent([
            'trade_order_no' => $topup_order2->tid,
            'shop_id' => '123456',
        ]);
        $msg->setSellerOrder($topup_order2);
        $this->assertEmpty(self::$form->refund($msg));

        $row_num = RechargeOrder::deleteAll(['id' => $topup_order2->id, 'status' => RechargeOrder::STATUS_CANCELED]);
        $this->assertEquals(1, $row_num);

        $buyer = Balance::getByPk($buyer->id);
        $this->assertEquals(max($balance_before->all_topup - $topup_order2->num, 0), $buyer->all_topup);
        $this->assertEquals($balance_before->all_consumption, $buyer->all_consumption);
        $this->assertEquals(max($balance_before->all_coin - $topup_order2->num, 0), $buyer->all_coin);
        $this->assertEquals(max($balance_before->android - $topup_order2->num, 0), $buyer->android);
    }

}
