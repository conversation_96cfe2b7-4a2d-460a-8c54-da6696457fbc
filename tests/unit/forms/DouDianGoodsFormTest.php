<?php

namespace app\tests\unit\forms;

use app\forms\DouDianGoodsForm;
use app\models\Drama;
use tests\components\UnitTestCase;
use tests\components\util\Tools;
use app\models\TransactionLog;

class DouDianGoodsFormTest extends UnitTestCase
{

    /**
     * @var DouDianGoodsForm
     */
    private static $goods;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        Tools::registerDramaMockResponseFunc('/rpc/api/get-drama-price', function ($params) {
            return [
                'drama_id' => 5678,
                'name' => '在抖店销售的广播剧',
                'price' => 199,
                'pay_type' => Drama::PAY_TYPE_DRAMA,
                'rate' => 0.55,
                'user_id' => 234,
            ];
        });
    }

    public function testInitiateByDramaId()
    {
        $goods = DouDianGoodsForm::initiateByDramaId(5678, self::TEST_USER_ID);
        $this->assertInstanceOf(DouDianGoodsForm::class, $goods);
        self::$goods = $goods;
    }

    public function testGetGoodsID()
    {
        $this->assertEquals(5678, self::$goods->getGoodsID());
    }

    public function testGetGoodsTitle()
    {
        $this->assertEquals('在抖店销售的广播剧', self::$goods->getGoodsTitle());
    }

    public function testGetOwnerID()
    {
        $this->assertEquals(234, self::$goods->getOwnerID());
    }

    public function testGetPrice()
    {
        $this->assertEquals(199, self::$goods->getPrice());
    }

    public function testGetRevenueRate()
    {
        $this->assertEquals(0.55, self::$goods->getRevenueRate());
    }

    public function testGetTransactionLogType()
    {
        $this->assertEquals(TransactionLog::TYPE_DRAMA, self::$goods->getTransactionLogType());
    }

    public function testGetTransactionLogAttr()
    {
        $this->assertEquals(TransactionLog::ATTR_DRAMA_DOUDIAN, self::$goods->getTransactionLogAttr());
    }

}
