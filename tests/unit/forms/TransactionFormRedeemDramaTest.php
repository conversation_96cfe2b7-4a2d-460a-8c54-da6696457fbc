<?php

namespace tests\forms;

use app\forms\GoodsForm;
use app\forms\TransactionFormRedeemDrama;
use app\models\Balance;
use app\models\Drama;
use app\models\Live;
use app\models\PayAccount;
use app\models\TransactionItemsLog;
use app\models\TransactionLog;
use app\models\TransactionLogDetail;
use Exception;
use missevan\util\MUtils as MUtils2;
use tests\components\UnitTestCase;
use tests\components\util\Tools;
use yii\web\HttpException;

class TransactionFormRedeemDramaTest extends UnitTestCase
{
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
    }

    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
    }

    public function testScenarios()
    {
        $form = new TransactionFormRedeemDrama(['scenario' => TransactionFormRedeemDrama::SCENARIO_DEFAULT]);
        $scenarios = $form->scenarios();
        $this->assertCount(2, $scenarios);
        $this->assertArrayHasKeys([TransactionFormRedeemDrama::SCENARIO_DEFAULT, TransactionFormRedeemDrama::SCENARIO_LUCKY_BAG], $scenarios);
        $this->assertEquals(['from_id', 'gift_id', 'operator_id'], $scenarios[TransactionFormRedeemDrama::SCENARIO_DEFAULT]);
        $this->assertEquals(['from_id', 'gift_id', 'operator_id', 'context_transaction_id'], $scenarios[TransactionFormRedeemDrama::SCENARIO_LUCKY_BAG]);
    }

    public function testRules()
    {
        $form = new TransactionFormRedeemDrama(['scenario' => TransactionFormRedeemDrama::SCENARIO_DEFAULT]);
        $rules = $form->rules();
        $this->assertGreaterThanOrEqual(2, $rules);
        $this->assertEquals(['context_transaction_id', 'checkContextTransaction', 'on' => TransactionFormRedeemDrama::SCENARIO_LUCKY_BAG], array_pop($rules));
        $this->assertEquals(['operator_id', 'integer'], array_pop($rules));
    }

    public function testCheckGoods()
    {
        self::mockDramaApi();

        $form = new TransactionFormRedeemDrama(['scenario' => TransactionFormRedeemDrama::SCENARIO_DEFAULT]);
        $form->gift_id = 15861;
        $form->checkGoods('gift_id');
        $this->assertEquals('该剧为非可兑换剧集', MUtils2::getFirstError($form));

        $form = new TransactionFormRedeemDrama(['scenario' => TransactionFormRedeemDrama::SCENARIO_LUCKY_BAG]);
        $form->gift_id = 333;
        $form->checkGoods('gift_id');
        $this->assertEquals('剧集非整剧付费类型', MUtils2::getFirstError($form));

        $form = new TransactionFormRedeemDrama(['scenario' => TransactionFormRedeemDrama::SCENARIO_LUCKY_BAG]);
        $form->gift_id = 15861;
        $form->checkGoods('gift_id');
        $this->assertEmpty(MUtils2::getFirstError($form));
        $this->assertNotNull(self::getPrivateProperty($form, 'drama_info'));
    }

    private function prepareCheckContextTransaction()
    {
        $tradelog = new TransactionLog([
            'from_id' => self::TEST_USER2_ID,
            'to_id' => 0,
            'gift_id' => 6,
            'title' => '广播剧福袋--魔道祖师第一季',
            'ios_coin' => 500,
            'android_coin' => 18,
            'all_coin' => 518,
            'income' => 51.8,
            'rate' => Live::LIVE_RATE,
            'attr' => TransactionLog::ATTR_LIVE_BUY_LUCKY_BAG,
            'type' => TransactionLog::TYPE_LIVE,
            'status' => TransactionLog::STATUS_SUCCESS,
            'num' => 2,
        ]);
        $tradelog->tax = $tradelog->calcTax(['ios' => 500, 'android' => 18]);
        if (!$tradelog->save()) {
            throw new Exception(MUtils2::getFirstError($tradelog));
        }

        $cleanup = function () use ($tradelog) {
            TransactionLog::deleteAll(['id' => $tradelog->id]);
        };
        return [$tradelog->id, $cleanup];
    }

    public function testCheckContextTransaction()
    {
        [$transaction_id, $cleanup] = $this->prepareCheckContextTransaction();
        try {
            $form = new TransactionFormRedeemDrama(['scenario' => TransactionFormRedeemDrama::SCENARIO_LUCKY_BAG]);
            $form->context_transaction_id = $transaction_id;
            $form->checkContextTransaction('context_transaction_id');
            $this->assertNotNull(self::getPrivateProperty($form, 'context_transaction_log'));
            $this->assertEmpty(MUtils2::getFirstError($form));
        } finally {
            $cleanup();
        }
    }

    private static function mockDramaApi()
    {
        Tools::registerDramaMockResponseFunc('/rpc/api/get-drama-price', function ($params) {
            switch ($params['drama_id']) {
                case 9888:
                    return [
                        'drama_id' => $params['drama_id'],
                        'user_id' => 2,
                        'price' => 299,
                        'rate' => 0.35,
                        'name' => '杀破狼第一季',
                        'type' => Drama::PAY_TYPE_DRAMA,
                        'is_lossless' => false,
                    ];
                case 15861:
                    return [
                        'drama_id' => $params['drama_id'],
                        'user_id' => 3,
                        'price' => 259,
                        'rate' => 0.35,
                        'name' => '魔道祖师第一季',
                        'type' => Drama::PAY_TYPE_DRAMA,
                        'is_lossless' => false,
                    ];
                case 333:
                    return [
                        'drama_id' => $params['drama_id'],
                        'user_id' => 3,
                        'price' => 9,
                        'rate' => 0.35,
                        'name' => '测试单集付费剧集',
                        'type' => Drama::PAY_TYPE_EPISODE,
                        'is_lossless' => false,
                    ];
            }
        });
        Tools::registerDramaMockResponseFunc('/rpc/drama/get-dramas', function ($params) {
            $drama_id = $params['drama_id'];
            switch ($drama_id) {
                case 9888:
                    return [
                        ['id' => $drama_id, 'user_id' => 2, 'name' => '杀破狼第一季', 'refined' => Drama::REFINED_SPECIAL],
                    ];
                case 15861:
                    return [
                        ['id' => $drama_id, 'user_id' => 3, 'name' => '魔道祖师第一季', 'refined' => 0],
                    ];
                case 333:
                    return [
                        ['id' => $drama_id, 'user_id' => 3, 'name' => '测试单集付费剧集', 'refined' => Drama::REFINED_SPECIAL],
                    ];
            }
            return [];
        });
    }

    private function prepareRedeemCase1()
    {
        TransactionLog::deleteAll(['from_id' => self::TEST_USER_ID, 'gift_id' => 9888, 'type' => TransactionLog::TYPE_DRAMA]);
        TransactionItemsLog::deleteAll(['user_id' => self::TEST_USER_ID, 'goods_id' => 9888, 'type' => TransactionLog::TYPE_DRAMA]);
        return function () {
            TransactionLog::deleteAll(['from_id' => self::TEST_USER_ID, 'gift_id' => 9888, 'type' => TransactionLog::TYPE_DRAMA]);
            TransactionItemsLog::deleteAll(['user_id' => self::TEST_USER_ID, 'goods_id' => 9888, 'type' => TransactionLog::TYPE_DRAMA]);
        };
    }

    private function prepareRedeemCase2()
    {
        $tradelog = new TransactionLog([
            'from_id' => self::TEST_USER_ID,
            'to_id' => 0,
            'gift_id' => 6,
            'title' => '广播剧福袋--魔道祖师第一季',
            'ios_coin' => 500,
            'android_coin' => 18,
            'all_coin' => 518,
            'income' => 51.8,
            'rate' => Live::LIVE_RATE,
            'attr' => TransactionLog::ATTR_LIVE_BUY_LUCKY_BAG,
            'type' => TransactionLog::TYPE_LIVE,
            'status' => TransactionLog::STATUS_SUCCESS,
            'num' => 2,
        ]);
        $tradelog->tax = $tradelog->calcTax(['ios' => 500, 'android' => 18]);
        if (!$tradelog->save()) {
            throw new Exception(MUtils2::getFirstError($tradelog));
        }
        $itemslog = new TransactionItemsLog([
            'goods_id' => 15861,
            'goods_title' => '魔道祖师第一季',
            'goods_price' => 259,
            'goods_num' => 2,
            'user_id' => self::TEST_USER_ID,
            'status' => TransactionLog::STATUS_SUCCESS,
            'type' => TransactionLog::TYPE_LIVE,
            'tid' => $tradelog->id,
        ]);
        $itemslog->more_detail = [
            'transaction_type' => GoodsForm::TRANSACTION_TYPE_DRAMA,
            'common_coins' => ['ios' => 500, 'android' => 18],
            'successful_goods_num' => 2,
        ];
        if (!$itemslog->save()) {
            throw new Exception(MUtils2::getFirstError($itemslog));
        }

        $cleanup = function () use ($tradelog) {
            TransactionLog::deleteAll(['id' => $tradelog->id]);
            TransactionItemsLog::deleteAll(['tid' => $tradelog->id]);
            TransactionLog::deleteAll(['from_id' => self::TEST_USER_ID, 'gift_id' => 15861, 'type' => TransactionLog::TYPE_DRAMA]);
        };
        return [$tradelog->id, $cleanup];
    }

    public function testRedeem()
    {
        self::mockDramaApi();
        // 普通兑换
        $cleanup = $this->prepareRedeemCase1();

        try {
            $form = new TransactionFormRedeemDrama(['scenario' => TransactionFormRedeemDrama::SCENARIO_DEFAULT]);
            $form->from_id = self::TEST_USER_ID;
            $form->operator_id = 999;
            $form->gift_id = 9888;
            $transaction_id = $form->redeem();

            $this->assertIsInt($transaction_id);
            $tradelog = TransactionLog::findOne([
                'id' => $transaction_id,
                'from_id' => $form->from_id,
                'to_id' => 2,
                'gift_id' => 9888,
                'type' => TransactionLog::TYPE_DRAMA,
                'status' => TransactionLog::STATUS_SUCCESS,
            ]);
            $this->assertNotNull($tradelog);
            $this->assertEquals(0, $tradelog->income);
            $this->assertEquals(0, $tradelog->tax);
            $this->assertEquals(0, $tradelog->all_coin);
            $this->assertEquals(0, $tradelog->rate);
            $this->assertEquals(TransactionLog::ATTR_DRAMA_REDEEM, $tradelog->attr);

            $tradelog_detail = TransactionLogDetail::findOne(['id' => $tradelog->id]);
            $this->assertNotNull($tradelog_detail);
            $this->assertArrayHasKey('operator_id', $tradelog_detail->more);

            $this->assertThrowsWithMessage(HttpException::class, '该M号已拥有本剧，无法兑换！', function () use ($form) {
                $form->redeem();
            }, 403);
        } finally {
            $cleanup();
        }

        // 福袋广播剧兑换
        [$context_transaction_id, $cleanup] = $this->prepareRedeemCase2();
        try {
            $form = new TransactionFormRedeemDrama(['scenario' => TransactionFormRedeemDrama::SCENARIO_LUCKY_BAG]);
            $form->from_id = self::TEST_USER_ID;
            $form->operator_id = 999;
            $form->gift_id = 15861;
            $form->context_transaction_id = $context_transaction_id;
            $transaction_id = $form->redeem();

            $tradelog = TransactionLog::findOne([
                'id' => $transaction_id,
                'from_id' => $form->from_id,
                'to_id' => 3,
                'gift_id' => 15861,
                'type' => TransactionLog::TYPE_DRAMA,
                'status' => TransactionLog::STATUS_SUCCESS,
            ]);
            $this->assertNotNull($tradelog);
            $this->assertEquals(25.9, $tradelog->income);
            $this->assertEquals($tradelog->calcTax(['ios' => 259]), $tradelog->tax);
            $this->assertEquals(0, $tradelog->all_coin);
            $this->assertEquals(0.35, $tradelog->rate);
            $this->assertEquals(TransactionLog::ATTR_DRAMA_LUCKY_BAG, $tradelog->attr);
            $this->assertEquals(1, $tradelog->num);

            $tradelog_detail = TransactionLogDetail::findOne(['id' => $tradelog->id]);
            $this->assertNotNull($tradelog_detail);
            $this->assertArrayHasKeys(['operator_id', 'context_transaction_id', 'context_transaction_items_log_id', 'common_coins'], $tradelog_detail->more);
            $this->assertEquals($context_transaction_id, $tradelog_detail->more['context_transaction_id']);
            $this->assertEquals(999, $tradelog_detail->more['operator_id']);
            $this->assertEquals(['ios' => 259], $tradelog_detail->more['common_coins']);

            $itemslog_list = TransactionItemsLog::findAll(['tid' => $context_transaction_id]);
            $this->assertCount(1, $itemslog_list);
            $this->assertEquals($itemslog_list[0]->id, $tradelog_detail->more['context_transaction_items_log_id']);

            $this->assertEquals(1, $itemslog_list[0]->goods_num);
            $this->assertContains($tradelog->id, $itemslog_list[0]->more_detail['redeem_transaction_ids']);
            $this->assertEquals(1, $itemslog_list[0]->more_detail['redeem_goods_num']);
            $this->assertEquals(['ios' => 241, 'android' => 18], $itemslog_list[0]->more_detail['common_coins']);
            $this->assertEquals(['ios' => 259], $itemslog_list[0]->more_detail['common_coins_redeem']);
        } finally {
            $cleanup();
        }
    }
}
