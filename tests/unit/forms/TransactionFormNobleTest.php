<?php

namespace app\tests\unit\forms;

use app\forms\TransactionForm;
use app\forms\TransactionFormNoble;
use app\forms\UserContext;
use app\models\Balance;
use app\models\PayAccount;
use app\models\PayAccounts;
use app\models\TransactionLog;
use tests\components\UnitTestCase;
use Yii;
use yii\web\HttpException;

class TransactionFormNobleTest extends UnitTestCase
{
    private static $origin_db;
    private static $origin_paydb;

    protected function _before()
    {
        parent::_before();
        self::$origin_db = Yii::$app->db;
        self::$origin_paydb = Yii::$app->paydb;
        Yii::$app->set('db', Yii::$app->sqlitedb);
        Yii::$app->set('paydb', Yii::$app->sqlite_paydb);
    }

    protected function _after()
    {
        parent::_after();
        Yii::$app->set('db', self::$origin_db);
        Yii::$app->set('paydb', self::$origin_paydb);
    }

    public function testRules()
    {
        $form = new TransactionFormNoble();
        $rules = $form->rules();
        $this->assertGreaterThan(0, count($rules));
        $rule = array_pop($rules);
        $this->assertEquals(['noble', 'setAttrByNoble'], $rule);
    }

    public function testCheckPrice()
    {
        $form = new TransactionFormNoble();

        $form->price = 100;
        $form->checkPrice('price');
        $error = $form->getFirstError('price');
        $this->assertNull($error);

        $form->price = 0;
        $form->checkPrice('price');
        $error = $form->getFirstError('price');
        $this->assertEquals('价格不能小于 1', $error);

        $form->is_trial = true;
        $form->checkPrice('price');
        $error = $form->getFirstError('price');
        $this->assertNull($error);

        $form->price = -1;
        $form->checkPrice('price');
        $error = $form->getFirstError('price');
        $this->assertEquals('价格不能小于 0', $error);
    }

    /**
     * @depends testRules
     */
    public function testSetAttrByNoble()
    {
        $form = new TransactionFormNoble();
        $form->noble = true;
        $form->setAttrByNoble();
        $this->assertEquals(TransactionLog::ATTR_LIVE_REGISTER_NOBLE, $form->attr);

        $form->noble = false;
        $form->setAttrByNoble();
        $this->assertEquals(TransactionLog::ATTR_LIVE_RENEWAL_NOBLE, $form->attr);
    }

    public function testScenarios()
    {
        $form = new TransactionFormNoble();
        $scenarios = $form->scenarios();
        $this->assertGreaterThan(0, count($scenarios));
        $this->assertArrayHasKey(TransactionFormNoble::SCENARIO_NOBLE, $scenarios);
        $this->assertEquals([
            'from_id',
            'to_id',
            'price',
            'rebate',
            'gift_id',
            'noble',
            'title',
            'live_open_log_id',
            'max_expire_time',
        ], $scenarios[TransactionFormNoble::SCENARIO_NOBLE]);
    }

    public function testGetSellerAndRate()
    {
        $form = new TransactionFormNoble();
        $form->to_id = 999;

        // 贵族开通
        $form->noble = true;
        [$seller, $rate] = self::invokePrivateMethod(TransactionFormNoble::class, 'getSellerAndRate', null, $form);
        $this->assertInstanceOf(Balance::class, $seller);
        $this->assertEquals(999, $seller->id);
        $this->assertEquals(0.24, $rate);

        // 贵族续费
        $form->noble = false;
        [$seller, $rate] = self::invokePrivateMethod(TransactionFormNoble::class, 'getSellerAndRate', null, $form);
        $this->assertInstanceOf(Balance::class, $seller);
        $this->assertEquals(999, $seller->id);
        $this->assertEquals(0, $rate);
    }

    /**
     * @depends testRules
     * @depends testScenarios
     * @depends testGetSellerAndRate
     * @depends testSetAttrByNoble
     */
    public function testBuyNoble()
    {
        $BUYER_ID = 666;
        $SELLER_ID = 999;
        $NOBLE_PRICE = 50000;

        PayAccount::deleteAll(['user_id' => $BUYER_ID]);
        $buyer = Balance::getByPk($BUYER_ID);
        $buyer->updateAttributes(['ios' => $NOBLE_PRICE, 'all_coin' => $NOBLE_PRICE]);

        $_SERVER['REQUEST_TIME'] = strtotime('2023-12-23 12:00:00');
        $user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.78 Safari/537.36';
        $ip = '*******';
        $form = new TransactionFormNoble();
        $form->load([
            'from_id' => $BUYER_ID,
            'to_id' => $SELLER_ID,
            'price' => $NOBLE_PRICE,
            'rebate' => 10000,
            'title' => '大伽',
            'gift_id' => 4,
            'noble' => 1,
            'max_expire_time' => strtotime('2024-06-01 18:00:00'),
            'live_open_log_id' => uniqid(),
            'user_agent' => $user_agent,
            'ip' => $ip,
        ], '');
        $form->user_context = new UserContext($user_agent, $ip);
        $this->assertTrue($form->validate());
        $result = $form->buyNoble();
        $this->assertArrayHasKeys(['transaction_id', 'balance', 'live_noble_balance', 'price'], $result);

        $tradelog = TransactionLog::findOne(['id' => $result['transaction_id']]);
        $this->assertEquals($NOBLE_PRICE / DIAMOND_EXRATE, $tradelog->income);
        $this->assertEquals(TransactionLog::ATTR_LIVE_REGISTER_NOBLE, $tradelog->attr);
        $this->assertEquals(TransactionLog::TYPE_LIVE, $tradelog->type);
        $seller = Balance::getByPk($SELLER_ID);
        $this->assertEquals(
            Balance::profitUnitConversion(($tradelog->income - $tradelog->tax) * 0.24, Balance::CONVERT_YUAN_TO_FEN),
            $seller->new_live_profit
        );

        $record_count = PayAccount::find()->where([
            'scope' => PayAccount::SCOPE_LIVE,
            'user_id' => $BUYER_ID,
            'type' => PayAccount::ATTR_TYPE_OFFSET_MAP[PayAccount::ATTR_NOBLE_COIN] + PayAccount::TYPE_COIN_INDEX_IOS,
            'expire_time' => strtotime('2024-06-01 18:00:00'),
            'create_time' => $_SERVER['REQUEST_TIME'],
        ])->count();
        $this->assertEquals(1, $record_count);
    }

    public function testBuyTrialNoble()
    {
        $balance = Balance::getByPk(self::TEST_USER_ID)->getTotalBalance();
        $live_noble_balance = PayAccounts::getAccounts(self::TEST_USER_ID, PayAccount::SCOPE_LIVE)->getTotalBalance();
        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_NOBLE]);
        $form->load([
            'from_id' => self::TEST_USER_ID,
            'price' => 0,
            'rebate' => 0,
            'gift_id' => 1,
            'title' => '测试贵族',
            'num' => 10,
            'noble' => 1,
            'is_trial' => 1,
        ], '');

        $form->to_id = self::TEST_USER_ID;
        $this->assertThrowsWithMessage(
            HttpException::class,
            '不可以在自己的房间开通贵族',
            function () use ($form) {
                $form->buyTrialNoble();
            }
        );

        // 测试正常使用贵族体验卡
        $form->to_id = self::TEST_USER2_ID;
        $ret = $form->buyTrialNoble();
        $this->assertEquals($balance, $ret['balance']);
        $this->assertEquals($live_noble_balance, $ret['live_noble_balance']);
        $this->assertEquals(0, $ret['price']);
        $transaction_log = TransactionLog::findOne(['id' => $ret['transaction_id']]);
        $this->assertEquals(self::TEST_USER_ID, $transaction_log->from_id);
        $this->assertEquals(self::TEST_USER2_ID, $transaction_log->to_id);
        $this->assertEquals(1, $transaction_log->gift_id);
        $this->assertEquals('测试贵族', $transaction_log->title);
        $this->assertEquals(0, $transaction_log->all_coin);
        $this->assertEquals(0, $transaction_log->income);
        $this->assertEquals(0, $transaction_log->tax);
        $this->assertEquals(0, $transaction_log->rate);
        $this->assertEquals(10, $transaction_log->num);
        $this->assertEquals(TransactionLog::STATUS_SUCCESS, $transaction_log->status);
        $this->assertEquals(TransactionLog::TYPE_LIVE, $transaction_log->type);
        $this->assertEquals(TransactionLog::ATTR_LIVE_REGISTER_NOBLE_TRIAL, $transaction_log->attr);

        // 测试贵族体验卡价格超过 0
        $form->price = 100;
        $form->rebate = 100;
        $ret = $form->buyTrialNoble();
        $logs = Yii::getLogger()->messages;
        $contents = array_column($logs, '0');
        $this->assertContains('贵族体验卡价格必须为 0', $contents);
        $this->assertContains('贵族体验卡返钻必须为 0', $contents);
        $this->assertEquals($balance, $ret['balance']);
        $this->assertEquals($live_noble_balance, $ret['live_noble_balance']);
        $this->assertEquals(0, $ret['price']);
        $transaction_log = TransactionLog::findOne(['id' => $ret['transaction_id']]);
        $this->assertEquals(0, $transaction_log->all_coin);
        $this->assertEquals(0, $transaction_log->income);
        $this->assertEquals(0, $transaction_log->tax);
        $this->assertEquals(0, $transaction_log->rate);
    }
}
