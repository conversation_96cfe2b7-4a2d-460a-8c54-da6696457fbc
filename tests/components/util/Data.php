<?php

namespace tests\components\util;

use app\components\util\MUtils;
use app\models\AccountInfo;
use app\models\AdTrack;
use app\models\AdTrackBilibili;
use app\models\AnFeedback;
use app\models\AnFeedbackTicket;
use app\models\AnMsg;
use app\models\Balance;
use app\models\Card;
use app\models\Catalog;
use app\models\TopupMenu;
use app\models\Certification;
use app\models\CommentLike;
use app\models\Commentnotice;
use app\models\Drama;
use app\models\EventVote;
use app\models\GetCard;
use app\models\InstallBuvid;
use app\models\MAlbum;
use app\models\MAppIcon;
use app\models\MAttentionUser;
use app\models\MCheckedSoundReview;
use app\models\MCollectAlbum;
use app\models\MDramaRewardRanks;
use app\models\MEvent;
use app\models\MGameCenter;
use app\models\MGameElement;
use app\models\MHomepageIcon;
use app\models\MHomepageRank;
use app\models\MLaunch;
use app\models\MLikeSound;
use app\models\MMessageAssign;
use app\models\Mowangsksoundseiy;
use app\models\Mowangskuser;
use app\models\MPersonaCollection;
use app\models\MPersonaModuleElement;
use app\models\MPersonaRank;
use app\models\MPointFeed;
use app\models\MPersonHomePage;
use app\models\MPowerSound;
use app\models\MPowerSoundIp;
use app\models\MRadioSound;
use app\models\MRecommendedElements;
use app\models\MSobotUser;
use app\models\MSound;
use app\models\MSoundAlbumMap;
use app\models\MSoundComment;
use app\models\MSoundNode;
use app\models\MTab;
use app\models\MTabBarPackage;
use app\models\MTag;
use app\models\MTagAlbumMap;
use app\models\MTheatrePickedComment;
use app\models\MTheatreBlindBox;
use app\models\MUserConfig;
use app\models\MUserCover;
use app\models\MUserLikeDanmaku;
use app\models\MUserLikeElement;
use app\models\MUserNodeLog;
use app\models\MUserUnlockElement;
use app\models\MVideoCard;
use app\models\MVip;
use app\models\Persona;
use app\models\RewardMessage;
use app\models\SkinPackage;
use app\models\SoundComment;
use app\models\SoundSubComment;
use app\models\SoundVideo;
use app\models\Topic;
use app\models\TransactionLog;
use app\models\TransactionSoundLog;
use app\models\UserAddendum;
use app\models\UserCertification;
use app\models\VipFeeDeductedRecord;
use app\models\VipReceiveCoinLog;
use app\models\VipSubscriptionSignAgreement;
use app\models\WithdrawalRecord;
use app\models\Work;
use app\models\Live;
use app\models\YouMightLikeModule;
use Exception;
use missevan\util\MUtils as MUtils2;
use tests\components\UnitTestCase;
use Yii;
use yii\db\Expression;
use yii\db\IntegrityException;
use yii\helpers\Json;

class Data extends UnitTestCase
{
    // 测试用互动广播剧音频 ID、剧集 ID 及价格（暂时使用固定数据）
    const TEST_INTERACTIVE_SOUND_ID = 3675;
    const TEST_INTERACTIVE_DRAMA_ID = 1201;
    const TEST_INTERACTIVE_SOUND_ID1 = 3676;
    const TEST_INTERACTIVE_DRAMA_ID1 = 1202;
    const TEST_INTERACTIVE_DRAMA_PRICE = 1;
    const TEST_NODE_ID = 233;
    const TEST_SON_NODE_ID = 234;
    const TEST_NODE_ID1 = 235;
    const TEST_SON_NODE_ID1 = 236;

    const TEST_IMAGE_FILE = __DIR__ . '/../../_data/testimages/test_feedback.jpg';

    const TEST_MAINLAND_CHINA_IP = '***************';
    const TEST_JAPAN_IP = '**************';
    const TEST_HK_IP = '*************';
    const TEST_EXCEPTION_IP = '**************';

    // 剧集信息表
    const DRAMA_TABLE = 'app_missevan_radio_drama.radio_drama_dramainfo';
    // 剧集订阅表
    const DRAMA_SUBSCRIBE_TABLE = 'app_missevan_radio_drama.radio_drama_subscription';

    // 用于测试获取购买剧集的剧集 ID
    const TEST_DRAMA_ID1 = 1;
    const TEST_DRAMA_ID2 = 2;
    const TEST_DRAMA_ID3 = 3;

    // 未绑定邮箱及第三方账号用户 ID
    const TEST_USER_ID_NOT_BIND = 12333;
    // 绑定了邮箱及第三方账号用户 ID
    const TEST_USER_ID_BIND = 12334;

    /**
     * 创建消费（订单）记录
     *
     * @param array $attrs 订单相关数据组成的数组，键名对应字段名，必须包含订单类型
     * @return TransactionLog
     * @throws Exception 报错失败时抛出异常
     */
    public static function createTransactionLog(array $attrs)
    {
        if (!key_exists('from_id', $attrs)) {
            throw new \Exception('创建订单时需指定 from_id');
        }
        if (!key_exists('type', $attrs)) {
            throw new \Exception('创建订单时需指定 type');
        }
        $default_data = [
            'income' => 1,
        ];
        $default_data = array_merge($default_data, $attrs);
        $transaction_log = new TransactionLog();
        $transaction_log->from_id = $attrs['from_id'];
        $transaction_log->c_time = $_SERVER['REQUEST_TIME'];
        $transaction_log->to_id = $attrs['to_id'] ?? 0;
        $transaction_log->gift_id = $attrs['gift_id'] ?? 0;
        $transaction_log->title = $attrs['title'] ?? '测试数据';
        $transaction_log->status = $attrs['status'] ?? TransactionLog::STATUS_SUCCESS;
        $transaction_log->type = $attrs['type'];
        $transaction_log->num = $attrs['num'] ?? 1;
        $transaction_log->attr = $attrs['attr'] ?? TransactionLog::ATTR_COMMON;
        $transaction_log->suborders_num = $attrs['suborders_num'] ?? 1;
        $transaction_log->ios_coin = $attrs['ios_coin'] ?? 0;
        $transaction_log->android_coin = $attrs['android_coin'] ?? 0;
        $transaction_log->paypal_coin = $attrs['paypal_coin'] ?? 0;
        $transaction_log->tmallios_coin = $attrs['tmallios_coin'] ?? 0;
        $transaction_log->googlepay_coin = $attrs['googlepay_coin'] ?? 0;
        $transaction_log->load($default_data);
        if (!$transaction_log->save()) {
            throw new Exception(MUtils::getFirstError($transaction_log));
        }
        return $transaction_log;
    }

    /**
     * 创建单集消费（订单）记录
     *
     * @param array $attrs 订单相关数据组成的数组，键名对应字段名
     * @return TransactionSoundLog
     * @throws Exception 报错失败时抛出异常
     */
    public static function createTransactionSoundLog(array $attrs)
    {
        if (!key_exists('sound_id', $attrs) || !key_exists('drama_id', $attrs)) {
            throw new Exception('创建订单时需指定 sound_id 和 drama_id');
        }
        $transaction_sound_log = new TransactionSoundLog();
        $transaction_sound_log->sound_id = $attrs['sound_id'];
        $transaction_sound_log->user_id = $attrs['user_id'] ?? self::TEST_USER_ID;
        $transaction_sound_log->drama_id = $attrs['drama_id'];
        $transaction_sound_log->tid = $attrs['tid'] ?? 0;
        $transaction_sound_log->status = $attrs['status'] ?? TransactionLog::STATUS_SUCCESS;
        $transaction_sound_log->attr = $attrs['attr'] ?? TransactionLog::ATTR_COMMON;
        if (!$transaction_sound_log->save()) {
            throw new Exception(MUtils::getFirstError($transaction_sound_log));
        }
        return $transaction_sound_log;
    }

    /**
     * 创建互动广播剧测试节点
     *
     * @return int
     */

    /**
     * @param array $attrs
     * @return int
     */
    public static function createTestNode(array $attrs = [])
    {
        $now = $_SERVER['REQUEST_TIME'];
        $node = $son_node = [
            'id' => $attrs['id'] ?? self::TEST_NODE_ID,
            'sound_id' => $attrs['sound_id'] ?? self::TEST_INTERACTIVE_SOUND_ID,
            'node_type' => MSoundNode::TYPE_ROOT,
            'next_ids' => $attrs['next_ids'] ?? '[' . self::TEST_SON_NODE_ID . ']',
            'stay_duration' => -1,
            'title' => '加特林大战余额宝',
            'question' => '加特林打的过余额宝吗',
            'option' => '',
            'soundurl_user' => 'sound://sound/test.m4a',
            'soundurl' => 'sound://aod/test.m4a',
            'soundurl_128' => 'sound://aod/test-128k.m4a',
            'duration' => 1,
            'checked' => MSoundNode::CHECKED_PASS,
            'create_time' => $now,
            'modified_time' => $now,
        ];
        $son_node['id'] = $attrs['son_id'] ?? self::TEST_SON_NODE_ID;
        $son_node['option'] = '测试选项 - 默认名称';
        $son_node['node_type'] = MSoundNode::TYPE_SON;
        $son_node['next_ids'] = '';
        return Yii::$app->db->createCommand()
            ->batchInsert(MSoundNode::tableName(), array_keys($node), [$node, $son_node])->execute();
    }

    /**
     * 创建互动广播剧节点播放日志
     *
     * @return MUserNodeLog
     */
    public static function createTestMUserNodeLog(array $attrs): MUserNodeLog
    {
        $now = $_SERVER['REQUEST_TIME'];
        $node_log = new MUserNodeLog([
            'user_id' => $attrs['user_id'] ?? self::TEST_USER_ID,
            'sound_id' => $attrs['sound_id'],
            'equip_id' => $attrs['equip_id'] ?? self::TEST_EQUIP_ID,
            'buvid' => $attrs['buvid'] ?? 0,
            'node_ids' => $attrs['node_ids'] ?? '1',
            'current_node_id' => $attrs['current_node_id'] ?? 1,
            'create_time' => $attrs['create_time'] ?? $now,
            'modified_time' => $attrs['modified_time'] ?? $now,
        ]);
        if (!$node_log->save()) {
            throw new Exception('创建测试音单失败：' . MUtils::getFirstError($node_log));
        }
        return $node_log;
    }

    /**
     * 创建测试音单
     *
     * @param array $attrs 音单属性数组，[column => value]
     * @return MAlbum
     * @throws Exception 创建失败时抛出异常
     */
    public static function createTestAlbum(array $attrs = [])
    {
        $album = new MAlbum([
            'title' => $attrs['title'] ?? '测试音单_' . MUtils::randomKeys(4, 2),
            'intro' => $attrs['intro'] ?? 'test album',
            'catalog_id' => $attrs['catalog_id'] ?? Catalog::CATALOG_ID_SOUND_LOVER,
            'user_id' => $attrs['user_id'] ?? self::TEST_USER_ID,
            'username' => $attrs['username'] ?? '加特林',
            'cover_image' => $attrs['cover_image'] ?? 'oss://nocover.png',
            'checked' => $attrs['checked'] ?? MAlbum::CHECKED_PASS,
            'refined' => $attrs['refined'] ?? 0,
        ]);
        if (!$album->save()) {
            throw new Exception('创建测试音单失败：' . MUtils::getFirstError($album));
        }
        return $album;
    }

    /**
     * 创建催眠电台分类
     *
     * @return int
     */
    public static function createRadioCatalog()
    {
        $now = $_SERVER['REQUEST_TIME'];
        $test_radio_asmr_catalog = $test_radio_asmr_son_catalog = $test_radio_new_asmr_catalog = $test_radio_new_asmr_sub_catalog = [
            'id' => Catalog::CATALOG_RADIO_ASMR,
            'parent_id' => 0,
            'catalog_name' => '催眠专享',
            'content' => '',
            'create_time' => $now,
            'modified_time' => $now,
        ];
        $test_radio_asmr_son_catalog['id'] = Catalog::CATALOG_RADIO_ASMR_CV_MAN;
        $test_radio_asmr_son_catalog['parent_id'] = Catalog::CATALOG_RADIO_ASMR;
        $test_radio_asmr_son_catalog['catalog_name'] = '催眠专享 - 测试分类';
        // 新的催眠专享一级分类数据
        $test_radio_new_asmr_catalog['id'] = Catalog::CATALOG_ID_RADIO_COAX_SLEEP;
        $test_radio_new_asmr_catalog['parent_id'] = Catalog::CATALOG_RADIO_ASMR;
        $test_radio_new_asmr_catalog['content'] = Json::encode([
            'share_title' => '分享标题',
            'share_url' => 'http://www.test.com',
            'icon_url' => 'oss://test/test.png',
        ]);
        $test_radio_new_asmr_catalog['catalog_name'] = '新版催眠专享 - 测试分类';
        // 新的催眠专享二级分类数据
        $test_radio_new_asmr_sub_catalog['id'] = Catalog::CATALOG_ID_RADIO_COAX_SLEEP_BOY;
        $test_radio_new_asmr_sub_catalog['parent_id'] = Catalog::CATALOG_ID_RADIO_COAX_SLEEP;
        $test_radio_new_asmr_sub_catalog['catalog_name'] = '新版催眠专享 - 测试二级分类';

        return Yii::$app->db->createCommand()
            ->batchInsert(Catalog::tableName(),
                ['id', 'parent_id', 'catalog_name', 'content', 'create_time', 'last_update_time'],
                [$test_radio_asmr_catalog, $test_radio_asmr_son_catalog, $test_radio_new_asmr_catalog, $test_radio_new_asmr_sub_catalog])
            ->execute();
    }

    /**
     * 创建音单收藏音频记录
     *
     * @param int $sound_id 音频 ID
     * @param int $album_id 音单 ID
     * @throws Exception 创建失败时抛出异常
     */
    public static function createSoundAlbumMap(int $sound_id, int $album_id)
    {
        $collect = new MSoundAlbumMap([
            'sound_id' => $sound_id,
            'album_id' => $album_id,
            'sort' => 0,
            'time' => $_SERVER['REQUEST_TIME'],
        ]);
        if (!$collect->save()) {
            throw new Exception('创建测试音单绑定音频数据失败：' . MUtils::getFirstError($collect));
        }
    }

    /**
     * 创建评论点赞或点踩记录
     *
     * @param array $attrs [column => value]
     * @return CommentLike
     * @throws Exception 创建失败时抛出异常
     */
    public static function createCommentLike(array $attrs): CommentLike
    {
        $comment_like = new CommentLike();
        $comment_like->cid = $attrs['cid'];
        $comment_like->sub = $attrs['sub'] ?? CommentLike::IS_NOT_SUB;
        $comment_like->userid = $attrs['user_id'] ?? self::TEST_USER_ID;
        $comment_like->type = $attrs['type'];
        if (!$comment_like->save()) {
            throw new Exception('创建测试点赞或点踩数据失败：' . MUtils::getFirstError($comment_like));
        }
        return $comment_like;
    }

    /**
     * 创建评论数据
     *
     * @param array $attrs [column => value]
     * @return SoundComment
     * @throws Exception 创建失败时抛出异常
     */
    public static function createComment(array $attrs): SoundComment
    {
        if (!array_key_exists('element_id', $attrs) || !array_key_exists('c_type', $attrs)) {
            throw new Exception('缺少评论对象 ID 和评论对象类型');
        }
        $comment = new SoundComment();
        $comment->comment_content = $attrs['comment_content'] ?? '单元测试评论';
        $comment->c_type = $attrs['c_type'] ?: SoundComment::TYPE_SOUND;
        $comment->element_id = $attrs['element_id'];
        if (!$comment->save()) {
            throw new Exception('创建测试评论失败：' . MUtils::getFirstError($comment));
        }
        return $comment;
    }

    /**
     * 创建点赞单音数据
     *
     * @param array $attrs [column => value]
     * @return MLikeSound
     * @throws Exception 创建失败时抛出异常
     */
    public static function createMLikeSound(array $attrs): MLikeSound
    {
        $m_like_sound = new MLikeSound();
        $m_like_sound->user_id = $attrs['user_id'] ?? self::TEST_USER_ID;
        $m_like_sound->sound_id = $attrs['sound_id'];
        $m_like_sound->ctime = $attrs['ctime'] ?? $_SERVER['REQUEST_TIME'];
        if (!$m_like_sound->save()) {
            throw new Exception(MUtils::getFirstError($m_like_sound));
        }
        return $m_like_sound;
    }

    /**
     * 创建测试弹幕
     *
     * @param array $attrs 创建弹幕需要的相关数据
     * @return MSoundComment 新建弹幕
     * @throws Exception 保存失败时抛出异常
     */
    public static function createMSoundComment(array $attrs = []): MSoundComment
    {
        $danmaku = new MSoundComment();
        $danmaku->sound_id = $attrs['sound_id'] ?? self::TEST_SOUND_ID;
        $danmaku->user_id = $attrs['user_id'] ?? self::TEST_USER_ID;
        $danmaku->text = $attrs['text'] ?? '测试弹幕';
        $danmaku->stime = $attrs['stime'] ?? '1';
        $danmaku->mode = $attrs['mode'] ?? MSoundComment::MODE_SLIDE;
        $danmaku->pool = $attrs['pool'] ?? MSoundComment::POOL_NORMAL;
        $danmaku->date = $attrs['date'] ?? $_SERVER['REQUEST_TIME'];
        if (!$danmaku->save()) {
            throw new Exception(MUtils::getFirstError($danmaku));
        }
        $danmaku->id = (int)$danmaku->id;
        return $danmaku;
    }

    public static function createSound(array $attrs = []): MSound
    {
        $sound = new MSound();
        if ($attrs['id'] ?? 0) {
            $sound->id = $attrs['id'];
        }
        $sound->catalog_id = $attrs['catalog_id'] ?? Catalog::CATALOG_ID_MUSIC;
        $sound->user_id = $attrs['user_id'] ?? self::TEST_USER_ID;
        $sound->username = $attrs['username'] ?? self::TEST_USERNAME;
        $sound->soundstr = $attrs['soundstr'] ?? 'test_sound';
        $sound->intro = $attrs['intro'] ?? 'test_intro';
        $sound->soundurl = $attrs['soundurl'] ?? 'upos://mefmboss/sound/201202/03/test.mp3';
        $sound->duration = $attrs['duration'] ?? 0;
        $sound->cover_image = $attrs['cover_image'] ?? 'test_cover_image';
        $sound->animationid = $attrs['animationid'] ?? 0;
        $sound->characterid = $attrs['characterid'] ?? 0;
        $sound->seiyid = $attrs['seiyid'] ?? 0;
        $sound->checked = $attrs['checked'] ?? 0;
        $sound->type = $attrs['type'] ?? MSound::TYPE_NORMAL;
        if (!$sound->save()) {
            throw new Exception(MUtils::getFirstError($sound));
        }
        // 对默认属性进行修改
        if (key_exists('soundurl_64', $attrs)) {
            $sound->soundurl_64 = $attrs['soundurl_64'];
            $sound->update(false);
        }
        return $sound;
    }

    /**
     * 创建测试 Tab
     *
     * @param array $attrs 创建 Tab 需要的数据信息
     * @return int
     * @throws
     */
    public static function createTab(array $attrs = []): int
    {
        $tab = new MTab();
        if (isset($attrs['id'])) {
            $tab->id = $attrs['id'];
        }
        $time = $_SERVER['REQUEST_TIME'];
        $tab->title = $attrs['title'] ?? 'test_tab';
        $tab->url = $attrs['url'] ?? 'missevan://test';
        $tab->sort = $attrs['sort'] ?? 0;
        $tab->active = $attrs['active'] ?? MTab::INACTIVE;
        $tab->archive = $attrs['archive'] ?? MTab::ARCHIVE_ONLINE;
        $tab->position = $attrs['position'] ?? MTab::POSITION_APP_HOMEPAGE_TOP;
        $tab->create_time = $attrs['create_time'] ?? $time;
        $tab->modified_time = $attrs['create_time'] ?? $time;
        if (!$tab->save()) {
            throw new Exception(MUtils::getFirstError($tab));
        }
        return (int)$tab->id;
    }

    /**
     * 创建测试 Icon
     *
     * @param array $attrs 创建 Icon 需要的数据信息
     * @return int
     * @throws
     */
    public static function createIcon(array $attrs = []): int
    {
        $icon = new MHomepageIcon();
        if (isset($attrs['id'])) {
            $icon->id = $attrs['id'];
        }
        $time = $_SERVER['REQUEST_TIME'];
        $icon->tab_id = $attrs['tab_id'] ?? 0;
        $icon->title = $attrs['title'] ?? 'test_icon';
        $icon->url = $attrs['url'] ?? 'missevan://test';
        $icon->sort = $attrs['sort'] ?? 0;
        $icon->icon = $attrs['icon'] ?? 'oss://mimages/202004/23/2bd0d87e8bb960046b71f12aa534a673171552.png';
        $icon->dark_icon = $attrs['dark_icon'] ?? 'oss://mimages/202004/23/2bd0d87e8bb960046b71f12aa534a673171552.png';
        $icon->type = $attrs['type'] ?? MHomepageIcon::TYPE_ICON_TAB;
        $icon->archive = $attrs['archive'] ?? MHomepageIcon::ARCHIVE_ONLINE;
        $icon->create_time = $attrs['create_time'] ?? $time;
        $icon->modified_time = $attrs['create_time'] ?? $time;
        $icon->anchor_name = $attrs['anchor_name'] ?? '';
        if (!$icon->save()) {
            throw new Exception(MUtils::getFirstError($icon));
        }
        return (int)$icon->id;
    }

    /**
     * 创建测试皮肤包
     *
     * @param array $attrs
     * @return SkinPackage
     * @throws
     */
    public static function createSkinPackage(array $attrs = []): SkinPackage
    {
        $skin_package = new SkinPackage();
        $time = $_SERVER['REQUEST_TIME'];
        $skin_package->url = $attrs['url'] ?? 'oss://voice/work/10/skin/2019-09-24/os-2-3x-5d89d9a7ab3cd/skin.zip';
        $skin_package->screen = $attrs['screen'] ?? SkinPackage::ANDROID_SCREEN_ALL;
        $skin_package->os = $attrs['os'] ?? Equipment::Android;
        $skin_package->work_id = $attrs['work_id'] ?? Work::ID_JIANWANG3;
        $skin_package->create_time = $attrs['create_time'] ?? $time;
        $skin_package->modified_time = $attrs['create_time'] ?? $time;
        if (!$skin_package->save()) {
            throw new Exception(MUtils::getFirstError($skin_package));
        }
        return $skin_package;
    }

    public static function createCheckedReviewSound(array $attrs = []): int
    {
        if ($attrs['sound_id']) {
            $time = $_SERVER['REQUEST_TIME'];
            $review_sound = new MCheckedSoundReview();
            $review_sound->sound_id = $attrs['sound_id'];
            $review_sound->user_id = $attrs['user_id'] ?? self::TEST_USER_ID;
            $review_sound->soundstr = $attrs['soundstr'] ?? 'test_sound';
            $review_sound->intro = $attrs['intro'] ?? 'test_intro';
            $review_sound->cover_image = $attrs['cover_image'] ?? 'test_cover_image';
            $review_sound->source = $attrs['source'] ?? 0;
            $review_sound->download = $attrs['download'] ?? 0;
            $review_sound->catalog_id = $attrs['catalog_id'] ?? Catalog::CATALOG_ID_MUSIC;
            $review_sound->animationid = $attrs['animationid'] ?? 0;
            $review_sound->characterid = $attrs['characterid'] ?? 0;
            $review_sound->seiyid = $attrs['seiyid'] ?? 0;
            $review_sound->tags = $attrs['seiyid'] ?? '';
            $review_sound->create_time = $attrs['create_time'] ?? $time;
            $review_sound->last_update_time = $attrs['last_update_time'] ?? $time;
            if (!$review_sound->save()) {
                throw new Exception('创建失败，原因：' . MUtils::getFirstError($review_sound));
            }
            return $review_sound->id;
        }
    }

    /**
     * 创建测试系统通知
     *
     * @param array $attrs 创建数据信息
     * @return MMessageAssign
     * @throws
     */
    public static function createMMessageAssign(array $attrs = []): MMessageAssign
    {
        $msg = new MMessageAssign();
        $msg->recuid = $attrs['recuid'] ?? self::TEST_USER_ID;
        $msg->send_uid = $attrs['send_uid'] ?? self::TEST_USER2_ID;
        $msg->title = $attrs['title'] ?? '测试通知标题';
        $msg->content = $attrs['content'] ?? '测试通知内容';
        $msg->status = $attrs['status'] ?? MMessageAssign::NOT_READ;
        $msg->time = $attrs['time'] ?? $_SERVER['REQUEST_TIME'];
        if (!$msg->save()) {
            throw new Exception(MUtils::getFirstError($msg));
        }
        return $msg;
    }

    /**
     * 创建画像记录
     *
     * @param array $attrs [column => value]
     * @return Persona
     * @throws Exception 创建失败时抛出异常
     */
    public static function createPersona(array $attrs): Persona
    {
        $persona = new Persona();
        $persona->equip_id = $attrs['equip_id'] ?? null;
        $persona->persona = $attrs['persona'] ?? Persona::TYPE_GIRL;
        $persona->user_id = $attrs['user_id'] ?? null;
        $persona->favor_tags = $attrs['favor_tags'] ?? '';
        if (!$persona->save()) {
            throw new Exception('创建测试画像失败：' . MUtils::getFirstError($persona));
        }
        $persona->id = (int)$persona->id;
        return $persona;
    }

    /**
     * 创建广告点击记录
     *
     * @param array $attrs 创建数据信息
     * @param string $class_name
     * @return AdTrack
     * @throws
     */
    public static function createAdTrack(array $attrs, string $class_name): AdTrack
    {
        $ad_track = new $class_name();
        $ad_track->track_id = $attrs['track_id'] ?? '';
        $ad_track->creative_id = $attrs['creative_id'] ?? '';
        $ad_track->project_id = $attrs['project_id'] ?? '';
        $ad_track->group_id = $attrs['group_id'] ?? '';
        $ad_track->equip_id = $attrs['equip_id'] ?? self::TEST_EQUIP_ID;
        $ad_track->idfa = $attrs['idfa'] ?? null;
        $ad_track->android_id_md5 = md5($attrs['android_id_md5'] ?? null);
        $ad_track->imei_md5 = md5($attrs['imei_md5'] ?? null);
        $ad_track->mac_md5 = md5($attrs['mac_md5'] ?? null);
        $ad_track->ua = $attrs['ua'] ?? self::TEST_USERAGENT;
        $ad_track->ip = $attrs['ip'] ?? Yii::$app->request->userIP;
        $ad_track->os = $attrs['os'] ?? Equipment::Android;
        $ad_track->click_time = intval(microtime(true) * 1000);
        $ad_track->vendor = $attrs['vendor'] ?? AdTrack::VENDOR_BILIBILI;
        $ad_track->more = $attrs['more'] ?? null;
        $ad_track->ad_from = $attrs['ad_from'] ?? AdTrackBilibili::FROM_BILIBILI_COMMON_AD;
        $ad_track->bili_buvid = $attrs['bili_buvid'] ?? '';
        if (!$ad_track->save()) {
            throw new Exception('创建广告点击记录失败：' . MUtils::getFirstError($ad_track));
        }
        return $ad_track;
    }

    /**
     * 创建测试关注用户
     *
     * @param array $attrs
     * @return MAttentionUser
     * @throws Exception
     */
    public static function createMAttentionUser(array $attrs = []): MAttentionUser
    {
        if (!array_key_exists('user_active', $attrs) || !array_key_exists('user_passtive', $attrs)) {
            throw new Exception('创建测试关注用户缺少 user_active 或 user_passtive 值');
        }
        $m_attention_user = new MAttentionUser();
        $m_attention_user->user_active = $attrs['user_active'];
        $m_attention_user->user_passtive = $attrs['user_passtive'];
        $m_attention_user->time = $attrs['time'] ?? $_SERVER['REQUEST_TIME'];
        if (!$m_attention_user->save()) {
            throw new Exception('创建关注用户失败：' . MUtils::getFirstError($m_attention_user));
        }
        return $m_attention_user;
    }

    public static function createDrama(array $attrs = [])
    {
        if (!array_key_exists('id', $attrs)) {
            throw new Exception('创建剧集缺少剧集 ID 字段');
        }
        Yii::$app->db->createCommand()->Insert(self::DRAMA_TABLE, [
            'id' => $attrs['id'],
            'name' => $attrs['name'] ?? 'test_drama_name',
            'integrity' => $attrs['integrity'] ?? Drama::INTEGRITY_NAME_SERIALIZING,
            'age' => $attrs['age'] ?? 1,
            'type' => $attrs['type'] ?? Drama::TYPE_BL,
            'user_id' => $attrs['user_id'] ?? self::TEST_USER_ID,
            'username' => $attrs['username'] ?? self::TEST_USERNAME,
            'create_time' => $attrs['create_time'] ?? $_SERVER['REQUEST_TIME'],
            'catalog' => $attrs['catalog'] ?? Drama::DRAMA_CATALOG_ID_CN_RADIO_DRAMA,
            'checked' => $attrs['checked'] ?? Drama::CHECKED_PASS,
            'pay_type' => $attrs['pay_type'] ?? Drama::PAY_TYPE_FREE,
            'name_letters' => $attrs['name_letters'] ?? 'testdramaname'
        ])->execute();
        return $attrs['id'];
    }

    /**
     * 创建剧集订阅记录
     *
     * @param array $attrs
     * @return int
     * @throws \yii\db\Exception
     */
    public static function createDramaSubscribe(array $attrs = [])
    {
        $now = $_SERVER['REQUEST_TIME'];
        return Yii::$app->db->createCommand()->insert(self::DRAMA_SUBSCRIBE_TABLE, [
            'user_id' => $attrs['user_id'] ?? self::TEST_USER_ID,
            'drama_id' => $attrs['drama_id'] ?? 0,
            'create_time' => $attrs['create_time'] ?? $now,
            'update_time' => $attrs['update_time'] ?? $now,
            'saw_episode' => $attrs['saw_episode'] ?? 0,
            'is_saw' => $attrs['is_saw'] ?? 0,
            'saw_episode_id' => $attrs['saw_episode_id'] ?? 0,
        ])->execute();
    }

    /**
     * 创建语音作品测试数据
     *
     * @param array $attrs
     * @return Work
     * @throws Exception
     */
    public static function createWork($attrs = [])
    {
        // 创建作品
        $work = new Work();
        if (isset($attrs['id'])) {
            $work->id = $attrs['id'];
        }
        $work->type = $attrs['type'] ?? Work::TYPE_OMIKUJI;
        $work->title = $attrs['title'] ?? '测试作品';
        $work->coupon_name = $attrs['coupon_name'] ?? '';
        $work->rank = $attrs['rank'] ?? 0;
        $work->icon = $attrs['icon'] ?? 'http://static-test.missevan.com/test_icon.png';
        $work->dark_icon = $attrs['dark_icon'] ?? 'http://static-test.missevan.com/test_dark_icon.png';
        $work->homepage_banners = $attrs['homepage_banners'] ?? '';
        $work->episode_banner = $attrs['episode_banner'] ?? '';
        $work->episode_cover = $attrs['episode_cover'] ?? '';
        $work->episode_icon = $attrs['episode_icon'] ?? '';
        $work->drawpage_banner = $attrs['drawpage_banner'] ?? '';
        $work->welfare_episode_banner = $attrs['welfare_episode_banner'] ?? '';
        $work->welfare_episode_cover = $attrs['welfare_episode_cover'] ?? '';
        $work->welfare_episode_icon = $attrs['welfare_episode_icon'] ?? '';
        $work->skin = $attrs['skin'] ?? '';
        $work->create_time = $_SERVER['REQUEST_TIME'];
        $work->modified_time = $_SERVER['REQUEST_TIME'];
        if (!$work->save()) {
            throw new Exception('创建测试作品失败，原因：' . MUtils::getFirstError($work));
        }
        return $work;
    }

    /**
     * 创建测试充值选项数据
     *
     * @param array $attrs
     * @return TopupMenu
     * @throws Exception
     */
    public static function createCcy(array $attrs = []): TopupMenu
    {
        $ccy = new TopupMenu();
        $ccy->price = $attrs['price'] ?? 0;
        $ccy->num = $attrs['num'] ?? 0;
        $ccy->ccy = $attrs['ccy'] ?? TopupMenu::DIAMOND;
        $ccy->device = $attrs['device'] ?? Equipment::Android;
        $ccy->scope = $attrs['scope'] ?? TopupMenu::SCOPE_ALL_USER;
        if (!$ccy->save()) {
            throw new Exception(MUtils::getFirstError($ccy));
        }
        return $ccy;
    }

    /**
     * 创建评论提醒测试数据
     *
     * @param array $attrs
     * @return Commentnotice
     * @throws Exception
     */
    public static function createCommentNotice(array $attrs = []): Commentnotice
    {
        if (!array_key_exists('comment_id', $attrs) || !array_key_exists('sub', $attrs)) {
            throw new Exception('没有对应的评论 ID');
        }
        $comment_notice = new Commentnotice();
        $comment_notice->c_user_id = $attrs['c_user_id'] ?? self::TEST_USER2_ID;
        $comment_notice->c_user_name = $attrs['c_user_id'] ?? '测试 @ 用户';
        $comment_notice->a_user_id = $attrs['a_user_id'] ?? self::TEST_USER_ID;
        $comment_notice->a_user_name = $attrs['a_user_name'] ?? '测试被 @ 用户';
        $comment_notice->notice_type = $attrs['notice_type'] ?? Commentnotice::NOTICE_TYPE_COMMENT;
        $comment_notice->type = $attrs['notice_type'] ?? SoundComment::TYPE_SOUND;
        $comment_notice->eId = $attrs['eId'] ?? self::TEST_SOUND_ID;
        $comment_notice->title = $attrs['title'] ?? '单元测试';
        $comment_notice->sub = $attrs['sub'];
        $comment_notice->comment_id = $attrs['comment_id'];
        $comment_notice->isread = Commentnotice::NOT_READ;
        if (!$comment_notice->save()) {
            throw new Exception(MUtils::getFirstError($comment_notice));
        }
        return $comment_notice;
    }

    /**
     * 创建测试活动
     *
     * @param array $attrs 测试数据
     * @return MEvent
     * @throws Exception
     */
    public static function createEvent(array $attrs = []): MEvent
    {
        $time = $_SERVER['REQUEST_TIME'];
        $event = new MEvent();
        if (isset($attrs['id'])) {
            $event->id = $attrs['id'];
        }
        $event->title = $attrs['title'] ?? '测试活动';
        $event->main_cover = $attrs['main_cover'] ?? '测试图片';
        $event->mini_cover = $attrs['mini_cover'] ?? '测试图片';
        $event->share_cover = $attrs['share_cover'] ?? '测试图片';
        $event->intro = $attrs['intro'] ?? '测试简介';
        $event->tag = $attrs['tag'] ?? '测试';
        $event->type = $attrs['type'] ?? Mevent::TYPE_SOUND;
        $create_time = $attrs['create_time'] ?? $time;
        $event->create_time = $event->start_time = $create_time;
        $event->vote_start_time = $event->draw_start_time = $create_time;
        $event->end_time = $event->vote_end_time = $event->draw_end_time = $attrs['end_time'] ?? $time + ONE_DAY;
        $event->status = $attrs['status'] ?? Mevent::STATUS_WEB;
        $event->attr = $attrs['attr'] ?? Mevent::ATTR_VOTE;
        $event->extended_fields = $attrs['extended_fields'] ?? null;
        if (!$event->save()) {
            throw new Exception('创建失败，原因：' . MUtils::getFirstError($event));
        }
        return $event;
    }

    /**
     * 创建测试专题
     *
     * @param array $attrs
     * @return Topic
     * @throws Exception
     */
    public static function createTopic(array $attrs = []): Topic
    {
        $time = $_SERVER['REQUEST_TIME'];
        $topic = new Topic();
        $topic->title = $attrs['title'] ?? '测试专题';
        $topic->pic_url = $attrs['pic_url'] ?? 'oss://test.png';
        $topic->mobile_pic_url = $attrs['mobile_pic_url'] ?? 'oss://test.png';
        $topic->share_pic_url = $attrs['share_pic_url'] ?? 'oss://test.png';
        $topic->html_url = $attrs['html_url'] ?? 'oss://topic/202001/07/143727/index.html';
        $topic->status = $attrs['status'] ?? Topic::STATUS_PUBLISHED;
        $topic->create_time = $attrs['create_time'] ?? $time;
        $topic->modified_time = $attrs['modified_time'] ?? $time;
        if (!$topic->save()) {
            throw new Exception('创建专题失败，原因：' . MUtils::getFirstError($topic));
        }
        return $topic;
    }

    /**
     * 创建启动数据
     *
     * @param array $attrs
     * @return MLaunch
     * @throws Exception
     */
    public static function createMLaunch(array $attrs = []): MLaunch
    {
        $time = $_SERVER['REQUEST_TIME'];
        $m_launch = new MLaunch();
        $m_launch->element_type = MLaunch::ELEMENT_TYPE_LAUNCH_PIC;
        $m_launch->title = $attrs['title'] ?? '测试数据';
        $m_launch->url = $attrs['url'] ?? '201906/10/4f815b856150e6c71091027afbb7a575150923.jpg';
        $m_launch->status = $attrs['status'] ?? MLaunch::STATUS_PUBLISHED;
        $m_launch->start_time = $attrs['start_time'] ?? $time;
        $m_launch->end_time = $attrs['end_time'] ?? $time + ONE_MINUTE;
        $m_launch->redirect_url = $attrs['redirect_url'] ?? '';
        $m_launch->label_str = $attrs['label'] ?? '';
        $m_launch->attr = $attrs['attr'] ?? 0;
        $m_launch->message = $attrs['message'] ?? '';
        $m_launch->create_time = $attrs['create_time'] ?? $time;
        $m_launch->modified_time = $attrs['modified_time'] ?? $time;
        if (!$m_launch->save()) {
            throw new Exception(MUtils::getFirstError($m_launch));
        }
        return $m_launch;
    }

    /**
     * 创建用户数据
     *
     * @param array $data
     * @return Mowangskuser
     * @throws Exception
     */
    public static function createMowangskuser(array $data = []): Mowangskuser
    {
        $mowangskuser = new Mowangskuser();
        if (isset($data['id'])) {
            $mowangskuser->id = $data['id'];
        }
        $mowangskuser->username = $data['username'] ?? self::TEST_USERNAME;
        $mowangskuser->iconid = $data['iconid'] ?? 0;
        $mowangskuser->iconurl = $data['iconurl'] ?? 'http://host.com/foo.png';
        $mowangskuser->iconcolor = $data['iconcolor'] ?? '#91c0edm#cde1edm#709cc9m#5079c9m#709cc9';
        $mowangskuser->teamid = $data['teamid'] ?? 0;
        $mowangskuser->teamname = $data['teamname'] ?? '测试';
        $mowangskuser->subtitle = $data['subtitle'] ?? '测试';
        $mowangskuser->confirm = $data['confirm'] ?? 0;
        $mowangskuser->ctime = $data['ctime'] ?? $_SERVER['REQUEST_TIME'];
        $mowangskuser->utime = $data['utime'] ?? $_SERVER['REQUEST_TIME'];
        $mowangskuser->cip = $data['cip'] ?? '127.0.0.1';
        $mowangskuser->uip = $data['uip'] ?? '127.0.0.1';
        $mowangskuser->quanxian = $data['quanxian'] ?? 'B';
        $mowangskuser->boardiconurl = $data['boardiconurl'] ?? 'test.jpg';
        if (!$mowangskuser->save()) {
            throw new Exception(MUtils::getFirstError($mowangskuser));
        }
        return $mowangskuser;
    }

    /**
     * @param array $data
     * @return AnFeedback
     * @throws Exception
     */
    public static function createAnFeedback(array $data = []): AnFeedback
    {
        $feedback = new AnFeedback();
        $feedback->status = $data['status'] ?? AnFeedback::STATUS_STAFF_UNREAD;
        $feedback->content = $data['content'] ?? '测试创建意见反馈';
        $feedback->equip_id = $data['equip_id'] ?? self::TEST_EQUIP_ID;
        $feedback->client = $data['client'] ?? Equipment::iOS;
        $feedback->create_time = $data['create_time'] ?? time();
        $feedback->type = $data['type'] ?? AnFeedback::TYPE_FEEDBACK_DIRECT;
        $feedback->ticket_id = $data['ticket_id'] ?? AnFeedback::FEEDBACK_DIRECT;
        $feedback->ip = $data['ip'] ?? Yii::$app->request->userIP;
        if (!$feedback->save()) {
            throw new Exception(MUtils::getFirstError($feedback));
        }
        return $feedback;
    }

    /**
     * @param array $attrs
     * @return EventVote
     * @throws Exception
     */
    public static function createEventVote(array $attrs = []): EventVote
    {
        $event_vote = new EventVote();
        $event_vote->event_id = $attrs['event_id'];
        $event_vote->eid = $attrs['eid'] ?? 0;
        $event_vote->vote_num = $attrs['vote_num'] ?? 0;
        $event_vote->category = $attrs['category'] ?? EventVote::CATEGORY_SOUND;
        $event_vote->reset_vote = $attrs['reset_vote'] ?? 0;
        if (!$event_vote->save()) {
            throw new Exception(MUtils::getFirstError($event_vote));
        }
        return $event_vote;
    }

    /**
     * 创建用户配置数据
     *
     * @param array $attrs
     * @return MUserConfig
     * @throws Exception
     */
    public static function createMUserConfig(array $attrs = []): MUserConfig
    {
        $user_config = new MUserConfig();
        $user_config->user_id = $attrs['user_id'] ?? 0;
        $user_config->buvid = $attrs['buvid'] ?? self::TEST_BUVID;
        $user_config->app_config = $attrs['app_config'] ?? MUserConfig::getDefaultAppConfig($user_config->user_id);
        if (!$user_config->save()) {
            throw new Exception(MUtils::getFirstError($user_config));
        }
        return $user_config;
    }

    /**
     * 创建用户封面图测试数据
     *
     * @param array $attrs
     * @return MUserCover
     * @throws Exception
     */
    public static function createMUserCover(array $attrs = []): MUserCover
    {
        $user_cover = new MUserCover();
        $user_cover->user_id = $attrs['user_id'] ?? self::TEST_USER_ID;
        $user_cover->reason = $attrs['reason'] ?? '';
        $user_cover->cover = $attrs['cover'] ?? 'oss://usercover/test.png';
        $user_cover->checked = $attrs['checked'] ?? MUserCover::CHECKED_PASS;
        if (!$user_cover->save()) {
            throw new Exception(MUtils::getFirstError($user_cover));
        }
        return $user_cover;
    }

    /**
     * 创建弹幕点赞记录
     *
     * @param array $attrs [column => value]
     * @return MUserLikeDanmaku
     * @throws Exception 创建失败时抛出异常
     */
    public static function createMUserLikeDanmaku(array $attrs): MUserLikeDanmaku
    {
        $model = new MUserLikeDanmaku();
        $model->user_id = $attrs['user_id'] ?? self::TEST_USER_ID;
        $model->element_id = $attrs['sound_id'] ?? self::TEST_SOUND_ID;
        $model->element_type = $attrs['element_type'] ?? MUserLikeDanmaku::ELEMENT_TYPE_SOUND;
        $model->danmaku_id = $attrs['danmaku_id'];
        $now = $_SERVER['REQUEST_TIME'];
        $model->create_time = $attrs['create_time'] ?? $now;
        $model->modified_time = $attrs['modified_time'] ?? $now;
        $model->delete_time = $attrs['delete_time'] ?? 0;
        if (!$model->save()) {
            throw new Exception('创建测试画像失败：' . MUtils::getFirstError($model));
        }
        $model->id = (int)$model->id;
        return $model;
    }

    /**
     * 创建子评论
     *
     * @param array $attrs 属性信息
     * @return SoundSubComment
     * @throws Exception
     */
    public static function createSoundSubComment(array $attrs = []): SoundSubComment
    {
        if (!array_key_exists('comment_id', $attrs)) {
            throw new Exception('缺少父评论 ID');
        }
        $sound_sub_comment = new SoundSubComment();
        $sound_sub_comment->comment_content = $attrs['comment_content'] ?? 'test';
        $sound_sub_comment->comment_id = $attrs['comment_id'];
        $sound_sub_comment->ctime = $_SERVER['REQUEST_TIME'];
        if (!$sound_sub_comment->save()) {
            throw new Exception('子评论创建失败，原因：' . MUtils::getFirstError($sound_sub_comment));
        }
        return $sound_sub_comment;
    }

    /**
     * 创建启动音数据
     *
     * @param array $attrs
     * @return MPowerSound
     * @throws Exception
     */
    public static function createMPowerSound(array $attrs = []): MPowerSound
    {
        $power_sound = new MPowerSound();
        $power_sound->sound_id = $attrs['sound_id'];
        $power_sound->cv = $attrs['cv'] ?? 'test_cv';
        $power_sound->role_name = $attrs['role_name'] ?? 'test_role';
        $power_sound->ip_id = $attrs['ip_id'] ?? 0;
        $power_sound->cover = $attrs['cover'] ?? 'oss://test_cover.png';
        $power_sound->icon = $attrs['icon'] ?? 'oss://test_icon.png';
        $power_sound->intro = $attrs['intro'] ?? 'test_intro';
        $power_sound->sort_order = $attrs['sort_order'] ?? 0;
        $power_sound->archive = $attrs['archive'] ?? MPowerSound::ARCHIVE_ONLINE;
        $now = $_SERVER['REQUEST_TIME'];
        $power_sound->create_time = $attrs['create_time'] ?? $now;
        $power_sound->modified_time = $attrs['modified_time'] ?? $now;
        $power_sound->playurl = $attrs['playurl'] ?? '';
        if (!$power_sound->save()) {
            throw new Exception(MUtils::getFirstError($power_sound));
        }
        return $power_sound;
    }

    /**
     * 创建启动音 IP 数据
     *
     * @param array $attrs
     * @return MPowerSoundIp
     * @throws Exception
     */
    public static function createMPowerSoundIp(array $attrs = []): MPowerSoundIp
    {
        $power_sound_ip = new MPowerSoundIp();
        $power_sound_ip->ip_name = $attrs['ip_name'] ?? 'test_ip';
        $power_sound_ip->sound_num = $attrs['sound_num'] ?? 1;
        $power_sound_ip->sort_order = $attrs['sort_order'] ?? 0;
        $power_sound_ip->archive = $attrs['archive'] ?? MPowerSoundIp::ARCHIVE_ONLINE;
        $now = $_SERVER['REQUEST_TIME'];
        $power_sound_ip->create_time = $attrs['create_time'] ?? $now;
        $power_sound_ip->modified_time = $attrs['modified_time'] ?? $now;
        if (!$power_sound_ip->save()) {
            throw new Exception(MUtils::getFirstError($power_sound_ip));
        }
        return $power_sound_ip;

    }

    /**
     * 创建直播间
     *
     * @param array $attrs
     * @return Live
     * @throws Exception
     */
    public static function createLive(array $attrs = []): Live
    {
        $live = new Live();
        $live->id = $attrs['id'] ?? self::TEST_USER_ID;
        $live->room_id = $attrs['room_id'] ?? 1;
        $live->catalog_id = $attrs['catalog_id'] ?? 0;
        $live->title = $attrs['title'] ?? '测试直播间';
        $live->intro = $attrs['intro'] ?? '测试简介';
        $live->cover = $attrs['cover'] ?? 'oss://live/test.png';
        $live->status = $attrs['status'] ?? Live::STATUS_CLOSE;
        $live->live_start_time = $attrs['live_start_time'] ?? 0;
        $live->contract_id = $attrs['contract_id'] ?? 1;
        $live->create_time = $attrs['create_time'] ?? $_SERVER['REQUEST_TIME'];
        $live->modified_time = $attrs['modified_time'] ?? $_SERVER['REQUEST_TIME'];
        $live->user_id = $attrs['user_id'] ?? self::TEST_USER_ID;
        $live->score = $attrs['score'] ?? 0;
        if (!$live->save()) {
            throw new Exception(MUtils::getFirstError($live));
        }
        return $live;
    }

    /**
     * 创建用户额外信息
     *
     * @param array $attrs
     * @return UserAddendum
     * @throws Exception
     */
    public static function createUserAddendum(array $attrs = []): UserAddendum
    {
        $user_addendum = new UserAddendum();
        $user_addendum->id = $attrs['id'] ?? self::TEST_USER_ID;
        $user_addendum->sex = $attrs['sex'] ?? UserAddendum::MALE;
        $user_addendum->birthday = $attrs['birthday'] ?? '2000-01-01';
        $user_addendum->qq = $attrs['qq'] ?? null;
        $user_addendum->weibo = $attrs['weibo'] ?? null;
        $user_addendum->wechat = $attrs['wechat'] ?? null;
        $user_addendum->apple = $attrs['apple'] ?? null;
        $user_addendum->message_config = $attrs['status'] ?? '{"receive":0,"fold":0}';
        $user_addendum->sobot = $attrs['sobot'] ?? 0;
        $user_addendum->ip_detail = $attrs['ip_detail'] ?? null;
        if (!$user_addendum->save()) {
            throw new Exception(MUtils2::getFirstError($user_addendum));
        }
        return $user_addendum;
    }

    public static function createTag(array $attrs = []): MTag
    {
        $tag = new MTag();
        $tag->name = $attrs['name'] ?? '测试';
        $tag->icon = $attrs['icon'] ?? '';
        $tag->cover = $attrs['cover'] ?? '';
        $tag->userid = $attrs['userid'] ?? 0;
        $tag->recommended = $attrs['recommended'] ?? 0;
        $tag->album_num = $attrs['album_num'] ?? 0;
        if (!$tag->save()) {
            throw new Exception(MUtils2::getFirstError($tag));
        }
        return $tag;
    }

    /**
     * 创建音频绑定的视频数据
     *
     * @param array $attrs
     * @return SoundVideo
     * @throws Exception
     */
    public static function createSoundVideo(array $attrs = []): SoundVideo
    {
        $sound_video = new SoundVideo();
        $sound_video->sid = $attrs['sid'];
        $sound_video->video_url = $attrs['video_url'];
        $time = $_SERVER['REQUEST_TIME'];
        $sound_video->create_time = $attrs['create_time'] ?? $time;
        $sound_video->modified_time = $attrs['modified_time'] ?? $time;
        $sound_video->videourl_360 = $attrs['videourl_360'] ?? '';
        $sound_video->videourl_480 = $attrs['videourl_480'] ?? '';
        $sound_video->videourl_720 = $attrs['videourl_720'] ?? '';
        $sound_video->videourl_1080 = $attrs['videourl_1080'] ?? '';
        $sound_video->checked = $attrs['checked'] ?? SoundVideo::CHECKED_UNPASS;
        $sound_video->attr = $attrs['attr'] ?? 0;
        $sound_video->more = $attrs['more'] ?? '';
        if (!$sound_video->save()) {
            throw new Exception(MUtils::getFirstError($sound_video));
        }
        return $sound_video;
    }

    /**
     * 创建测试收藏音单
     *
     * @param array $attrs
     * @return MCollectAlbum
     * @throws Exception
     */
    public static function createCollectAlbum(array $attrs = []): MCollectAlbum
    {
        if (!array_key_exists('album_id', $attrs) || !array_key_exists('user_id', $attrs)) {
            throw new Exception('缺少音单 ID 或用户 ID');
        }
        $collect_album = new MCollectAlbum();
        $collect_album->user_id = $attrs['user_id'];
        $collect_album->album_id = $attrs['album_id'];
        $collect_album->time = $_SERVER['REQUEST_TIME'];
        if (!$collect_album->save()) {
            throw new Exception(MUtils2::getFirstError($collect_album));
        }
        return $collect_album;
    }

    /**
     * 关注
     * 目前关注接口迁移至 missevan-go 项目，此函数创建用户关注相关数据，用于进行关注相关的测试业务
     *
     * @param int $fans_id 粉丝用户 ID
     * @param int $follow_id 被关注者用户 ID
     * @return bool
     * @throws \Exception
     */
    public static function follow(int $fans_id, int $follow_id): bool
    {
        if ($fans_id <= 0 || $follow_id <= 0) {
            throw new Exception('参数错误');
        }
        try {
            if (!MAttentionUser::findOne(['user_active' => $fans_id, 'user_passtive' => $follow_id])) {
                $model = new MAttentionUser;
                $model->user_passtive = $follow_id;
                $model->user_active = $fans_id;
                $model->time = $_SERVER['REQUEST_TIME'];
                if (!$model->save()) {
                    throw new Exception(MUtils2::getFirstError($model));
                }
                Mowangskuser::updateAllCounters(['fansnum' => 1], ['id' => $follow_id]);
                Mowangskuser::updateAllCounters(['follownum' => 1], ['id' => $fans_id]);
            }
        } catch (IntegrityException $e) {
            if (!MUtils2::isUniqueError($e, MAttentionUser::getDb())) {
                // 抛出除唯一索引以外的异常
                throw $e;
            }
        }
        return true;
    }

    /**
     * 取消关注
     * 目前取消关注接口迁移至 missevan-go 项目，此函数创建用户取消关注相关数据，用于进行取消关注相关的测试业务
     *
     * @param int $fans_id 粉丝用户 ID
     * @param int $follow_id 被关注者用户 ID
     * @return bool
     * @throws \Exception
     */
    public static function unfollow(int $fans_id, int $follow_id): bool
    {
        if ($fans_id <= 0 || $follow_id <= 0) {
            throw new Exception('参数错误');
        }
        $fans_attention_follow = MAttentionUser::find()
            ->where('user_active = :user_active AND user_passtive = :user_passtive', [
                ':user_active' => $fans_id,
                ':user_passtive' => $follow_id,
            ])
            ->one();

        // 取消关注
        if ($fans_attention_follow && $fans_attention_follow->delete()) {
            // 更新粉丝数和关注数
            $fans_expression = new Expression('GREATEST(fansnum, 1) - 1');
            $follow_expression = new Expression('GREATEST(follownum, 1) - 1');
            Mowangskuser::updateAll(['fansnum' => $fans_expression], ['id' => $follow_id]);
            Mowangskuser::updateAll(['follownum' => $follow_expression], ['id' => $fans_id]);
        }
        return true;
    }

    /**
     * 创建测试用户画像和专辑流映射数据
     *
     * @param array $attrs
     * @return MPersonaCollection
     * @throws Exception
     */
    public static function createMPersonaCollection(array $attrs = []): MPersonaCollection
    {
        if (!array_key_exists('persona_id', $attrs) || !array_key_exists('elem_id', $attrs)) {
            throw new Exception('缺少画像 ID 或元素 ID');
        }
        $model = new MPersonaCollection();
        if (array_key_exists('id', $attrs)) {
            $model->id = $attrs['id'];
        }
        $model->persona_id = $attrs['persona_id'];
        $model->elem_type = $attrs['elem_type'] ?? MPersonaCollection::TYPE_ALBUM;
        $model->elem_id = $attrs['elem_id'];
        $model->pace = $attrs['pace'] ?? 1;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建音单标签关联数据
     *
     * @param int $tag_id 标签 ID
     * @param int $album_id 音单 ID
     * @throws Exception 创建失败时抛出异常
     */
    public static function createTagAlbumMap(int $tag_id, int $album_id)
    {
        $model = new MTagAlbumMap([
            'tag_id' => $tag_id,
            'album_id' => $album_id,
        ]);
        if (!$model->save()) {
            throw new Exception('创建测试音单绑定标签数据失败：' . MUtils2::getFirstError($model));
        }
    }

    /**
     * 创建测试声优信息
     *
     * @param array $attr
     * @return Mowangsksoundseiy
     * @throws Exception
     */
    public static function createMowangskSoundSeiy(array $attr = []): Mowangsksoundseiy
    {
        $seiy = new Mowangsksoundseiy();
        $seiy->name = $attr['name'] ?? 'a_test_seiy';
        $seiy->icon = $attr['icon'] ?? 'test_icon';
        $seiy->profile = $attr['icon'] ?? 'test_profile';
        $seiy->birthyear = $attr['birthyear'] ?? date('Y');
        $seiy->mid = $attr['mid'] ?? 0;
        $seiy->birthmonth = $attr['birthmonth'] ?? date('m');
        $seiy->birthday = $attr['birthday'] ?? date('d');
        $seiy->bloodtype = $attr['bloodtype'] ?? 0;
        $seiy->weibo = $attr['weibo'] ?? 'test_weibo';
        $seiy->weiboname = $attr['weiboname'] ?? 'test_weiboname';
        $seiy->baike = $attr['baike'] ?? 'test_baike';
        $seiy->baikename = $attr['baikename'] ?? 'test_baikename';
        $seiy->gender = $attr['gender'] ?? 0;
        $seiy->initial = $attr['initial'] ?? 1;
        $seiy->career = $attr['career'] ?? 0;
        $seiy->group = $attr['group'] ?? 'test_organization';
        $seiy->soundline1 = $attr['soundline1'] ?? 0;
        $seiy->soundline2 = $attr['soundline2'] ?? 0;
        $seiy->soundline3 = $attr['soundline3'] ?? 0;
        if (!$seiy->save()) {
            throw new Exception('创建声优信息失败，原因：' . MUtils2::getFirstError($seiy));
        }
        return $seiy;
    }

    /**
     * 创建测试私信
     *
     * @param array $attr
     * @return AnMsg
     * @throws Exception
     */
    public static function createAnMsg(array $attr = []): AnMsg
    {
        if (!array_key_exists('small_id', $attr) || !array_key_exists('big_id', $attr)) {
            throw new Exception('缺少互相私信的用户 ID');
        }
        $msg = new AnMsg();
        $msg->small_id = $attr['small_id'];
        $msg->big_id = $attr['big_id'];
        $msg->msg = $attr['msg'] ?? '测试私信';
        $msg->status = $attr['status'] ?? AnMsg::STATUS_UNREAD;
        $msg->post_name = $attr['post_name'] ?? self::TEST_USERNAME;
        $msg->post_icon = $attr['post_icon'] ?? 'icon01.png';
        $msg->post_color = $attr['post_color'] ?? 'test';
        $msg->type = $attr['type'] ?? AnMsg::MSG_TYPE_TEXT;
        $msg->ctime = $_SERVER['REQUEST_TIME'];
        if (!$msg->save()) {
            throw new Exception('创建失败，原因：' . MUtils::getFirstError($msg));
        }
        return $msg;
    }

    /**
     * 创建测试推荐模块
     *
     * @param array $attrs
     * @return YouMightLikeModule
     * @throws Exception
     */
    public static function createYouMightLikeModule(array $attrs = []): YouMightLikeModule
    {
        $model = new YouMightLikeModule();
        if (key_exists('id', $attrs)) {
            $model->id = $attrs['id'];
        }
        $model->title = $attrs['title'] ?? '测试推荐模块';
        $model->creator_id = $attrs['creator_id'] ?? self::TEST_USER_ID;
        $model->element_type = $attrs['element_type'] ?? YouMightLikeModule::TYPE_DRAMA;
        $model->element_attr = $attrs['element_attr'] ?? 0;
        $model->element_style = $attrs['element_style'] ?? MPersonaModuleElement::MODULE_STYLE_DEFAULT;
        $now = $_SERVER['REQUEST_TIME'];
        $model->create_time = $now;
        $model->modified_time = $now;
        $model->more = $attrs['more'] ?? null;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建模块元素数据
     *
     * @param array $attrs
     * @return MPersonaModuleElement
     * @throws Exception
     */
    public static function createMPersonaModuleElement($attrs = []): MPersonaModuleElement
    {
        $model = new MPersonaModuleElement();
        $model->module_id = $attrs['module_id'];
        $model->persona_id = $attrs['persona_id'] ?? Persona::TYPE_GIRL;
        $model->element_id = $attrs['element_id'] ?? 0;
        $model->element_type = $attrs['element_type'] ?? 0;
        $model->sort = $attrs['sort'] ?? 1;
        $model->summary = $attrs['summary'] ?? 'test';
        $now = $_SERVER['REQUEST_TIME'];
        $model->create_time = $now;
        $model->modified_time = $now;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建测试盲盒剧场精选评论信息
     *
     * @param array $attr
     * @return MTheatrePickedComment
     * @throws Exception
     */
    public static function createMTheatrePickedComment(array $attr = []): MTheatrePickedComment
    {
        $now = $_SERVER['REQUEST_TIME'];
        $picked_comment = new MTheatrePickedComment();
        $picked_comment->create_time = $attr['create_time'] ?? $now;
        $picked_comment->modified_time = $attr['modified_time'] ?? $now;
        $picked_comment->comment_id = $attr['comment_id'] ?? 1;
        $picked_comment->comment_content = $attr['comment_content'] ?? '测试评论内容';
        $picked_comment->drama_id = $attr['drama_id'] ?? self::TEST_DRAMA_ID1;
        $picked_comment->drama_name = $attr['drama_name'] ?? '测试剧集名称';
        $picked_comment->sound_id = $attr['sound_id'] ?? self::TEST_SOUND_ID;
        $picked_comment->user_id = $attr['user_id'] ?? self::TEST_USER_ID;
        $picked_comment->sort = $attr['sort'] ?? 0;
        if (!$picked_comment->save()) {
            throw new Exception('创建创建测试盲盒剧场信息失败，原因：' . MUtils2::getFirstError($picked_comment));
        }
        return $picked_comment;
    }

    /**
     * 创建推荐模块元素
     *
     * @param array $attrs
     * @return MRecommendedElements
     * @throws Exception
     */
    public static function createMRecommendedElement(array $attrs = []): MRecommendedElements
    {
        $now = $_SERVER['REQUEST_TIME'];
        $model = new MRecommendedElements();
        $model->client = $attrs['client'] ?? MRecommendedElements::CLIENT_APP_ALL;
        $model->module_id = $attrs['module_id'] ?? 0;
        $model->module_type = $attrs['module_type'] ?? MRecommendedElements::MODULE_TYPE_NONE;
        $model->element_type = $attrs['element_type'] ?? 0;
        $model->element_id = $attrs['element_id'] ?? MRecommendedElements::ELEMENT_ID_NONE;
        $model->cover = $attrs['cover'] ?? 'oss://missevan-test/test/cover.png';
        $model->url = $attrs['url'] ?? 'missevan://test';
        $model->sort = $attrs['sort'] ?? 0;
        $model->create_time = $now;
        $model->update_time = $now;
        $model->start_time = $attrs['start_time'] ?? 0;
        $model->end_time = $attrs['end_time'] ?? $now + ONE_DAY;
        $model->archive = $attrs['archive'] ?? MRecommendedElements::ARCHIVE_ONLINE;
        $model->more = $attrs['more'] ?? null;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建抽取盲盒音频数据
     *
     * @param array $attrs
     * @return MTheatreBlindBox
     * @throws Exception
     */
    public static function createMTheatreSound(array $attrs = []): MTheatreBlindBox
    {
        $model = new MTheatreBlindBox();
        $model->sound_id = $attrs['sound_id'];
        $model->drama_id = $attrs['drama_id'];
        $now = $_SERVER['REQUEST_TIME'];
        $model->create_time = $now;
        $model->modified_time = $now;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建测试实名认证信息
     *
     * @param array $attr
     * @return Certification
     * @throws Exception
     */
    public static function createCertification(array $attr = []): Certification
    {
        $certification = new Certification();
        if (!array_key_exists('id_number', $attr)) {
            throw new Exception('缺少身份证号');
        }
        if (array_key_exists('id', $attr)) {
            $certification->id = $attr['id'];
        }
        $certification->user_id = $attr['user_id'] ?? self::TEST_USER_ID;
        $certification->user_name = $attr['user_name'] ?? self::TEST_USERNAME;
        $certification->real_name = $attr['real_name'] ?? 'test_real_name';
        $certification->id_number = $attr['id_number'];
        $certification->id_people = $attr['id_people'] ?? 'test_id_people';
        $certification->method = $attr['method'] ?? Certification::METHOD_ALIPAY_ZHIMA;
        $certification->checked = $attr['checked'] ?? Certification::CHECKED_CREATE;
        if (!$certification->save()) {
            throw new Exception('创建测试实名认证信息失败，原因：' . MUtils2::getFirstError($certification));
        }
        return $certification;
    }

    /**
     * 创建测试催眠专享音频
     *
     * @param array $attr
     * @return MRadioSound
     * @throws Exception
     */
    public static function createMRadioSound(array $attr = []): MRadioSound
    {
        $m_radio_sound = new MRadioSound();
        $now = $_SERVER['REQUEST_TIME'];
        $m_radio_sound->create_time = $now;
        $m_radio_sound->modified_time = $now;
        $m_radio_sound->catalog_id = $attr['catalog_id'];
        $m_radio_sound->sound_id = $attr['sound_id'];
        $m_radio_sound->title = $attr['title'] ?? 'test_title';
        $m_radio_sound->cover = $attr['cover'] ?? 'oss://test/front_cover.png';
        $m_radio_sound->background_cover = $attr['background_cover'] ?? 'oss://test/background_cover.png';
        $m_radio_sound->background_video = $attr['background_video'] ?? 'oss://test/background_video.mp4';
        $m_radio_sound->more = $attr['more'] ?? null;
        if (!$m_radio_sound->save()) {
            throw new Exception(MUtils2::getFirstError($m_radio_sound));
        }
        return $m_radio_sound;
    }

    /**
     * 创建用户喜欢元素记录
     *
     * @param array $attr
     * @return MUserLikeElement
     * @throws Exception
     */
    public static function createMUserLikeElement(array $attr = []): MUserLikeElement
    {
        $model = new MUserLikeElement();
        $now = $_SERVER['REQUEST_TIME'];
        $model->create_time = $now;
        $model->modified_time = $now;
        $model->user_id = $attr['user_id'] ?? self::TEST_USER_ID;
        $model->element_type = $attr['element_type'];
        $model->element_id = $attr['element_id'];
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建反馈信息
     *
     * @param array $attr
     * @return AnFeedbackTicket
     * @throws Exception
     */
    public static function createAnFeedbackTicket(array $attr = []): AnFeedbackTicket
    {
        $model = new AnFeedbackTicket();
        if (key_exists('id', $attr)) {
            $model->id = $attr['id'];
        }
        $model->equip_id = $attr['equip_id'] ?? self::TEST_EQUIP_ID;
        $model->buvid = $attr['buvid'] ?? self::TEST_BUVID;
        $model->content = $attr['content'] ?? 'test content';
        $model->client = $attr['client'] ?? Equipment::Android;
        $model->user_id = $attr['user_id'] ?? self::TEST_USER_ID;
        $model->status = $attr['status'] ?? AnFeedbackTicket::STATUS_NOTICE_STAFF_UNREAD;
        $model->type = $attr['type'] ?? AnFeedback::TYPE_FEEDBACK_OTHER;
        $now = $_SERVER['REQUEST_TIME'];
        $model->create_time = $now;
        $model->modified_time = $now;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建视频大卡记录
     *
     * @param array $attr
     * @return MVideoCard
     * @throws Exception
     */
    public static function createMVideoCard(array $attr = []): MVideoCard
    {
        $model = new MVideoCard();
        $now = $_SERVER['REQUEST_TIME'];
        $model->title = $attr['title'] ?? '测试标题';
        $model->sound_id = $attr['sound_id'] ?? self::TEST_SOUND_ID;
        $model->url = $attr['url'] ?? '';
        $model->cover = $attr['cover'] ?? 'oss://test/cover.png';
        $model->start_time = $attr['start_time'] ?? $now;
        $model->end_time = $attr['end_time'] ?? $now;
        $model->more = $attr['more'] ?? ['persona_ids' => [Persona::TYPE_GIRL, Persona::TYPE_OTOME]];
        $model->delete_time = $attr['delete_time'] ?? 0;
        $model->type = $attr['type'] ?? MVideoCard::TYPE_RECOMMEND;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建剧集打赏榜数据
     *
     * @param array $attr
     * @return MDramaRewardRanks
     * @throws Exception
     */
    public static function createMDramaRewardRanks(array $attr = []): MDramaRewardRanks
    {
        $model = new MDramaRewardRanks();
        $model->drama_id = $attr['drama_id'] ?? self::TEST_DRAMA_ID1;
        $model->type = $attr['type'] ?? MDramaRewardRanks::TYPE_RANK_ALL;
        $model->total_coin = $attr['total_coin'] ?? 1;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建语音卡/签
     *
     * @param array $data
     * @return Card
     * @throws Exception
     */
    public static function createCard(array $data = []): Card
    {
        $now = $_SERVER['REQUEST_TIME'];
        // 创建求签包下语音签
        $card = new Card();
        $card->work_id = $data['work_id'];
        $card->title = $data['title'] ?? '测试语音';
        $card->intro = $data['intro'] ?? 'test';
        $card->special = $data['special'] ?? Card::SPECIAL_NORMAL;
        $card->rank = $data['rank'] ?? 0;
        $card->push = $data['push'] ?? 0;
        $card->coupon = $data['coupon'] ?? 0;
        $card->price = $data['price'] ?? 0;
        $card->level = $data['level'] ?? 0;
        $card->role_id = $data['role_id'] ?? 0;
        $card->is_online = $data['is_online'] ?? Card::ONLINE;
        $card->card_package_id = $data['card_package_id'] ?? 0;
        $card->pics = $data['pics'] ?? '';  // 默认无插图
        $card->duration = $data['duration'] ?? 0;
        $card->icon = $data['icon'] ?? 'oss://image/cards/201810/26/test.png';
        $card->cover = $data['cover'] ?? 'oss://image/cards/201810/26/test.png';
        $card->play_cover = $data['play_cover'] ?? 'oss://image/cards/201810/26/test.png';
        $card->subtitles = $data['subtitles'] ?? 'voice://text/cards/201810/26/test.lrc';
        $card->voice = $data['voice'] ?? 'voice://audio/cards/201810/26/test.mp3';
        $card->voice_source = $data['voice_source'] ?? 'voice://audio/cards/201810/26/test.mp3';
        $card->create_time = $now;
        $card->modified_time = $now;
        if (!$card->save()) {
            throw new Exception(MUtils::getFirstError($card));
        }
        return $card;
    }

    /**
     * 创建语音卡/签获取记录
     *
     * @param array $data
     * @return GetCard
     * @throws Exception
     */
    public static function createGetCard(array $data = []): GetCard
    {
        $now = $_SERVER['REQUEST_TIME'];
        // 创建求签包下语音签
        $get_card = new GetCard();
        $get_card->create_time = $now;
        $get_card->modified_time = $now;
        $get_card->appear_time = $data['appear_time'] ?? 0;
        $get_card->user_id = $data['user_id'] ?? self::TEST_USER_ID;
        $get_card->card_id = $data['card_id'];
        $get_card->work_id = $data['work_id'];
        $get_card->role_id = $data['role_id'] ?? 0;
        $get_card->level = $data['level'] ?? Card::LEVEL_N;
        $get_card->status = $data['status'] ?? GetCard::SHOW_NOTICE;
        $get_card->special = $data['special'] ?? Card::SPECIAL_NORMAL;
        $get_card->card_package_id = $data['card_package_id'];
        $get_card->interval = $data['interval'] ?? 0;
        if (!$get_card->save()) {
            throw new Exception(MUtils::getFirstError($get_card));
        }
        return $get_card;
    }

    /**
     * 创建测试用的游戏
     *
     * @param array $data
     * @return MGameCenter
     * @throws Exception
     */
    public static function createMGameCenter(array $data = []): MGameCenter
    {
        $model = new MGameCenter();
        $model->event_id = $data['event_id'] ?? 0;
        $model->url = $data['url'] ?? 'http://test/game';
        $model->cover = $data['cover'] ?? 'oss://test/cover.jpg';
        $model->icon = $data['icon'] ?? 'oss://test/icon.jpg';
        $model->name = $data['name'] ?? '测试游戏';
        $model->tag = $data['tag'] ?? 'xxx';
        $model->intro = $data['intro'] ?? 'xxx';
        $model->extended_fields = $data['extended_fields'] ?? null;
        $model->sort = $data['sort'] ?? 1;
        if (!$model->save()) {
            throw new Exception(MUtils::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建测试用的游戏关联数据
     *
     * @param array $data
     * @return MGameElement
     * @throws Exception
     */
    public static function createMGameElement(array $data): MGameElement
    {
        if (!key_exists('game_id', $data) || !key_exists('element_id', $data)) {
            throw new Exception('创建游戏关联数据参数错误');
        }
        $model = new MGameElement();
        $model->game_id = $data['game_id'];
        $model->element_type = $data['element_type'] ?? MGameElement::ELEMENT_TYPE_SOUND;
        $model->element_id = $data['element_id'];
        if (!$model->save()) {
            throw new Exception(MUtils::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建用户画像与榜单关联数据
     *
     * @param array $attrs
     * @return MPersonaRank
     * @throws Exception
     */
    public static function createMPersonaRank(array $attrs = []): MPersonaRank
    {
        $model = new MPersonaRank();
        $model->persona_id = $attrs['persona_id'] ?? Persona::TYPE_GIRL;
        $model->rank_type = $attrs['rank_type'] ?? MHomepageRank::TYPE_RANK_DRAMA_NEW;
        $model->sort = $attrs['sort'] ?? 0;
        $model->name = $attrs['name'] ?? '新品榜';
        $model->active = $attrs['active'] ?? MPersonaRank::ACTIVE_OFF;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建榜单数据
     *
     * @param array $attrs
     * @return MHomepageRank
     * @throws Exception
     */
    public static function createMHomepageRank(array $attrs = []): MHomepageRank
    {
        $model = new MHomepageRank();
        $model->bizdate = $attrs['bizdate'] ?? date('Y-m-d', $_SERVER['REQUEST_TIME']);
        $model->type = $attrs['type'] ?? MHomepageRank::TYPE_RANK_DRAMA_NEW;
        $model->sub_type = $attrs['sub_type'] ?? MHomepageRank::SUB_TYPE_RANK_DAY;
        $model->data = $attrs['data'] ?? null;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建小鱼干投食记录
     *
     * @param array $attrs
     * @return MPointFeed
     * @throws Exception
     */
    public static function createMPointFeed(array $attrs = []): MPointFeed
    {
        $model = new MPointFeed();
        $model->sound_id = $attrs['sound_id'];
        $model->user_id = $attrs['user_id'] ?? self::TEST_USER_ID;
        $model->create_time = $_SERVER['REQUEST_TIME'];
        $model->num = $attrs['num'] ?? 1;
        $model->catalog_id = $attrs['catalog_id'] ?? Catalog::CATALOG_ID_DRAMA;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建打赏留言数据
     *
     * @param array $attrs
     * @return RewardMessage
     * @throws Exception
     */
    public static function createRewardMessage(array $attrs = []): RewardMessage
    {
        $model = new RewardMessage();
        $model->drama_id = $attrs['drama_id'] ?? self::TEST_DRAMA_ID1;
        $model->user_id = $attrs['user_id'] ?? self::TEST_USER_ID;
        $model->transaction_id = $attrs['transaction_id'] ?? 0;
        $model->message = $attrs['message'] ?? '测试打赏留言';
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建智齿访客信息数据
     *
     * @param array $attrs
     * @return MSobotUser
     * @throws Exception
     */
    public static function createMSobotUser(array $attrs = []): MSobotUser
    {
        $model = new MSobotUser();
        $model->user_id = $attrs['user_id'] ?? self::TEST_USER_ID;
        $model->visitor_id = $attrs['visitor_id'] ?? 'test_visitor_id';
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建用户头衔认证信息
     *
     * @param array $attrs
     * @return UserCertification
     * @throws Exception
     */
    public static function createUserCertification(array $attrs = []): UserCertification
    {
        $model = new UserCertification();
        $time = $_SERVER['REQUEST_TIME'];
        $model->create_time = $time;
        $model->modified_time = $time;
        $model->user_id = $attrs['user_id'];
        $model->subtitle = $attrs['subtitle'];
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建关注的用户的动态
     *
     * @param array $attrs
     * @return MPersonHomePage
     * @throws Exception
     */
    public static function createMPersonHomePage(array $attrs = []): MPersonHomePage
    {
        if (!key_exists('recid', $attrs) || !key_exists('soundid', $attrs)) {
            throw new Exception('缺少用户 ID 或音频 ID');
        }

        $model = new MPersonHomePage();
        $model->recid = $attrs['recid'];
        $model->gmt_create = $attrs['gmt_create'] ?? date('Y-m-d H:i:s');
        $model->soundid = $attrs['soundid'];
        $model->status = $attrs['status'] ?? 0;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建测试用的应用图标
     *
     * @param array $data
     * @return MAppIcon
     * @throws Exception
     */
    public static function createAppIcon(array $data = []): MAppIcon
    {
        $model = new MAppIcon();
        $time = $_SERVER['REQUEST_TIME'];
        $model->create_time = $time;
        $model->modified_time = $time;
        $model->icon = $data['icon'] ?? 'oss://test/icon.jpg';
        $model->start_time = $data['start_time'] ?? 0;
        $model->end_time = $data['end_time'] ?? MAppIcon::END_TIME_FOREVER_EFFECTIVE;
        $model->supported_version = $data['supported_version'] ?? ['ios' => '6.1.3'];
        if (!$model->save()) {
            throw new Exception(MUtils::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建测试用的应用底部图标
     *
     * @param array $data
     * @return MTabBarPackage
     * @throws Exception
     */
    public static function createTabBarPackage(array $data = []): MTabBarPackage
    {
        $model = new MTabBarPackage();
        $time = $_SERVER['REQUEST_TIME'];
        $model->create_time = $time;
        $model->modified_time = $time;
        $model->package = $data['package'] ?? 'oss://test/tab_bar_package.zip';
        $model->start_time = $data['start_time'] ?? 0;
        $model->end_time = $data['end_time'] ?? MTabBarPackage::END_TIME_FOREVER_EFFECTIVE;
        if (!$model->save()) {
            throw new Exception(MUtils::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建提现记录
     *
     * @param array $data
     * @return WithdrawalRecord
     * @throws Exception
     */
    public static function createWithdrawalRecord(array $data = []): WithdrawalRecord
    {
        $time = $_SERVER['REQUEST_TIME'];
        $model = new WithdrawalRecord();
        $model->user_id = $data['user_id'] ?? self::TEST_USER_ID;
        $model->account_id = $data['account_id'] ?? 1;
        $model->profit = $data['profit'] ?? 1;
        $model->create_time = $time;
        $model->status = $data['status'] ?? WithdrawalRecord::STATUS_CREATE;
        $model->type = $data['type'] ?? WithdrawalRecord::TYPE_WITHDRAW_LIVE_NEW;
        if (!$model->save()) {
            throw new Exception(MUtils::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建提现账号信息
     *
     * @param array $data
     * @return AccountInfo
     * @throws Exception
     */
    public static function createAccountInfo(array $data = []): AccountInfo
    {
        if (!key_exists('user_id', $data)) {
            throw new Exception('缺少用户 ID');
        }

        $model = new AccountInfo();
        $time = $_SERVER['REQUEST_TIME'];
        if (key_exists('id', $data)) {
            $model->id = $data['id'];
        }
        $model->create_time = $time;
        $model->user_id = $data['user_id'];
        $model->real_name = $data['real_name'] ?? 'test';
        $model->account = $data['account'] ?? '***********';
        $model->mobile = $data['mobile'] ?? '***********';
        $model->id_number = $data['id_number'] ?? '231025222222222222';
        $model->bank = $data['bank'] ?? 'test';
        $model->bank_account = $data['bank_account'] ?? '6215811111111111111';
        $model->bank_branch = $data['bank_branch'] ?? 'test';
        $model->type = $data['type'] ?? AccountInfo::TYPE_BANK;
        $model->status = $data['status'] ?? AccountInfo::STATUS_CONFIRM;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建用户订阅会员周期扣款签约协议
     *
     * @param array $data
     * @return VipSubscriptionSignAgreement
     * @throws Exception
     */
    public static function createVipSubscriptionSignAgreement(array $data = []): VipSubscriptionSignAgreement
    {
        $time = $_SERVER['REQUEST_TIME'];
        $model = new VipSubscriptionSignAgreement();
        $model->user_id = $data['user_id'] ?? self::TEST_USER_ID;
        $model->vip_id = $data['vip_id'] ?? 1;
        $model->pay_type = $data['pay_type'] ?? VipSubscriptionSignAgreement::PAY_TYPE_WECHAT;
        $model->status = $data['status'] ?? VipSubscriptionSignAgreement::STATUS_PENDING;
        $model->start_time = $data['start_time'] ?? $time;
        $model->expire_time = $data['expire_time'] ?? $time + ONE_DAY;
        $model->agreement_no = $data['agreement_no'] ?? '';
        $model->more = $data['more'] ?? null;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建会员扣费记录
     *
     * @param array $data
     * @return VipFeeDeductedRecord
     * @throws Exception
     */
    public static function createVipFeeDeductedRecord(array $data = []): VipFeeDeductedRecord
    {
        $model = new VipFeeDeductedRecord();
        $model->user_id = $data['user_id'] ?? self::TEST_USER_ID;
        $model->vip_id = $data['vip_id'] ?? 1;
        $model->sign_agreement_id = $data['sign_agreement_id'] ?? 0;
        $model->pay_type = $data['pay_type'] ?? VipFeeDeductedRecord::PAY_TYPE_WECHAT;
        $model->price = $data['price'] ?? 1;
        $model->status = $data['status'] ?? VipFeeDeductedRecord::STATUS_PENDING;
        $model->next_deduct_time = $data['next_deduct_time'] ?? 0;
        $model->transaction_id = $data['transaction_id'] ?? '';
        $model->tax = $data['tax'] ?? 0;
        $model->more = $data['more'] ?? null;
        $model->confirm_time = $data['confirm_time'] ?? 0;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建领取会员福利钻石信息
     *
     * @param array $data
     * @return VipReceiveCoinLog
     * @throws Exception
     */
    public static function createVipReceiveCoinLog(array $data = []): VipReceiveCoinLog
    {
        $model = new VipReceiveCoinLog();
        $time = $_SERVER['REQUEST_TIME'];
        $model->create_time = $time;
        $model->modified_time = $time;
        $model->user_id = $data['user_id'];
        $model->vip_id = $data['vip_id'] ?? 1;
        $model->coin_num = $data['coin_num'] ?? 1;
        $model->receive_time = $time;
        $model->more = $data['more'] ?? null;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建用户解锁资源记录
     *
     * @param array $data
     * @return MUserUnlockElement
     * @throws Exception
     */
    public static function createMUserUnlockElement(array $data = []): MUserUnlockElement
    {
        $model = new MUserUnlockElement();
        $time = $_SERVER['REQUEST_TIME'];
        $model->create_time = $time;
        $model->modified_time = $time;
        $model->user_id = $data['user_id'];
        $model->element_id = $data['element_id'];
        $model->element_type = $data['element_type'] ?? MUserUnlockElement::ELEMENT_TYPE_SOUND;
        $model->unlock_time = $time;
        $model->start_time = $data['start_time'] ?? $time;
        $model->end_time = $data['end_time'] ?? $time + ONE_DAY;
        $model->more = $data['more'] ?? null;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }

    /**
     * 创建用户钱包信息
     *
     * @param array $data
     * @return Balance
     * @throws Exception
     */
    public static function createBalance(array $data = []): Balance
    {
        if (!key_exists('id', $data)) {
            throw new Exception('缺少用户 ID');
        }
        $model = new Balance();
        $model->id = $data['id'];
        $model->ios = $data['ios'] ?? 0;
        $model->android = $data['android'] ?? 0;
        $model->paypal = $data['paypal'] ?? 0;
        $model->tmallios = $data['tmallios'] ?? 0;
        $model->googlepay = $data['googlepay'] ?? 0;
        $model->in_ios = $data['in_ios'] ?? 0;
        $model->in_android = $data['in_android'] ?? 0;
        $model->in_paypal = $data['in_paypal'] ?? 0;
        $model->in_tmallios = $data['in_tmallios'] ?? 0;
        $model->in_googlepay = $data['in_googlepay'] ?? 0;
        $model->new_all_live_profit = $data['new_all_live_profit'] ?? 0;
        $model->new_live_profit = $data['new_live_profit'] ?? 0;
        $model->all_live_profit = $data['all_live_profit'] ?? 0;
        $model->live_profit = $data['live_profit'] ?? 0;
        $model->profit = $data['profit'] ?? 0;
        $model->drama_reward_profit = $data['drama_reward_profit'] ?? 0;
        $model->drama_buy_profit = $data['drama_buy_profit'] ?? 0;
        $model->other_profit = $data['other_profit'] ?? 0;
        $model->all_drama_buy_profit = $data['all_drama_buy_profit'] ?? 0;
        $model->all_drama_reward_profit = $data['all_drama_reward_profit'] ?? 0;
        $model->all_other_profit = $data['all_other_profit'] ?? 0;
        $model->all_consumption = $data['all_consumption'] ?? 0;
        $model->all_topup = $data['all_topup'] ?? 0;
        $model->all_coin = $data['all_coin'] ?? 0;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }
        return $model;
    }
}
