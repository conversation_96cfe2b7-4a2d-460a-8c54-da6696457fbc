<?php

return [
    'drama' => [
        'url' => 'http://missevan-drama.srv.maoer.co:8080',
        'key' => 'testkey'
    ],
    'pushserver' => [
        'url' => 'http://www.uat.missevan.com:8098/',
        'key' => 'testkey'
    ],
    'iap-gateway' => [
        'url' => 'http://172.16.10.20:8090',
    ],
    'bilibili-flow' => [
        'url' => 'http://app.bilibili.com/',
        // Bilibili 接入联通 API 用
        'cpid' => 'bilibl',
        'cpkey' => 'xxxxxxxx',
        // 免流规则
        'rules' => 'https://bili-static.acgvideo.com/static.missevan.com/app/flow-rules.json',
    ],
    'go' => [
        'url' => 'http://missevan-go.srv.maoer.co:3032/',
    ],
    'audio-chatroom' => [
        'url' => 'http://audio-chatroom.srv.maoer.co',
        'key' => 'xxxxxxxxxxxxxxxxxxxxxxxx'
    ],
    'live' => [
        'url' => 'http://live-service.srv.maoer.co:3013',
        'key' => 'testkey',
    ],
    'apple' => [
        'apple_search_ads' => [
            '12345' => [
                'client_id' => 'test_client_id',
                'team_id' => 'test_team_id',
                'key_id' => 'test_key_id',
                'private_key' => "-----BEGIN EC PRIVATE KEY-----\ntest....test\n-----END EC PRIVATE KEY-----",
            ],
        ],
        'apple_shared_secret' => 'xxxxxx',
        // https://www.apple.com/certificateauthority/
        'apple_root_ca_g3' => "-----BEGIN CERTIFICATE-----
MIICQzCCAcmgAwIBAgIILcX8iNLFS5UwCgYIKoZIzj0EAwMwZzEbMBkGA1UEAwwS
QXBwbGUgUm9vdCBDQSAtIEczMSYwJAYDVQQLDB1BcHBsZSBDZXJ0aWZpY2F0aW9u
IEF1dGhvcml0eTETMBEGA1UECgwKQXBwbGUgSW5jLjELMAkGA1UEBhMCVVMwHhcN
MTQwNDMwMTgxOTA2WhcNMzkwNDMwMTgxOTA2WjBnMRswGQYDVQQDDBJBcHBsZSBS
b290IENBIC0gRzMxJjAkBgNVBAsMHUFwcGxlIENlcnRpZmljYXRpb24gQXV0aG9y
aXR5MRMwEQYDVQQKDApBcHBsZSBJbmMuMQswCQYDVQQGEwJVUzB2MBAGByqGSM49
AgEGBSuBBAAiA2IABJjpLz1AcqTtkyJygRMc3RCV8cWjTnHcFBbZDuWmBSp3ZHtf
TjjTuxxEtX/1H7YyYl3J6YRbTzBPEVoA/VhYDKX1DyxNB0cTddqXl5dvMVztK517
IDvYuVTZXpmkOlEKMaNCMEAwHQYDVR0OBBYEFLuw3qFYM4iapIqZ3r6966/ayySr
MA8GA1UdEwEB/wQFMAMBAf8wDgYDVR0PAQH/BAQDAgEGMAoGCCqGSM49BAMDA2gA
MGUCMQCD6cHEFl4aXTQY2e3v9GwOAEZLuN+yRhHFD/3meoyhpmvOwgPUnPWTxnS4
at+qIxUCMG1mihDK1A3UT82NQz60imOlM27jbdoXt2QfyFMm+YhidDkLF1vLUagM
6BgD56KyKA==
-----END CERTIFICATE-----
",
    ],
    'doudian' => [
        'app_key' => '111111111',
        'app_secret' => 'abc-cde-efg-hij-klmn',
        'shop_id' => '123456',
        'normal_fee_rate_author_ids' => [
            2683221693703400,
        ],
    ],
    'ad' => [
        'wangyiyun' => [
            '123456' => [
                'app_key' => 'xxxxxxxxxxxxxxxxxxxxxxxx',
                'secret_key' => 'xxxxxxxxxxxxxxxxxxxxxxxx',
                'source' => 1
            ],
        ],
        'vivo' => [
            // advertise_id
            '234324343' => [
                'client_id' => 'test_client_id',  // 应用 ID
                'client_secret' => 'test_client_secret',  // 应用密钥
                'src_id' => 'test_src_id',  // 数据源 ID
            ],
        ],
    ],
    'httpdns' => [
        'aliyun_service_url' => 'http://203.107.1.33/191607/ss',
    ],
    'user-growth' => [
        'url' => 'http://user-growth:3003',
        'key' => 'testkey',
    ],
    'jingdong' => [
        'key' => 'test-key',
        'vendor_id' => 12345,
    ],
    'jingdong-v2' => [
        'private_key' => 'test-private-key',
        'customer_id' => '54321',
    ],
    'wechatpay' => [
        // https://info.missevan.com/pages/viewpage.action?pageId=97887348
        'jsapi' => [
            'app_id' => '123456',  // 应用 ID
            'app_secret' => 'abcdefghijklmn',  // 应用秘钥
            'apiv3_key' => 'test-key',  // APIv3 Key
            'merchant_id' => '987654321',  // 商户号
            'merchant_serial_no' => '1234567890',  // 商户 API 私钥的证书序列号
            'platform_cert' => [  // 微信平台证书
                // 证书序列号 => 证书（新旧证书更新的过渡期间，两个证书都在使用）
                '2222' => [
                    'content' => "-----BEGIN CERTIFICATE-----\ntest certificate2\n-----END CERTIFICATE-----",
                    'latest' => true,  // 是否为最新证书
                ],
                '1111' => [
                    'content' => "-----BEGIN CERTIFICATE-----\ntest certificate1\n-----END CERTIFICATE-----",
                    'latest' => false,
                ],
            ],
            'apiclient_key' => "-----BEGIN PRIVATE KEY-----\ntest private key\n-----END PRIVATE KEY-----",
            'authorize_redirect_url' => 'https://www.missevan.com/member/wechatpubaccauthorize?backurl=https%3A%2F%2Fm.missevan.com%2Fwallet',  // 授权后的跳回地址
            'notify_url' => 'https://app.missevan.com/callback/wechatpay-v3',  // 支付回调地址
        ],
    ],
    'wechat-offiaccount' => [
        // https://developers.weixin.qq.com/doc/offiaccount/Getting_Started/Getting_Started_Guide.html
        // 'app_id' => 'test_app_id',  // 应用 ID，暂不需要配置
        // 'app_secret' => 'test_app_secret',  // 应用秘钥，暂不需要配置
        // 'url' => 'https://api.weixin.qq.com',  // 请求微信接口，暂不需要配置
        'token' => 'test_token',  // 微信公众号的 Token
    ],
    'openapi_key_secret_map' => [
        'abcde' => '12345',
    ],
    // 微博开发平台授权，到端任务换量接口相关
    'weibo_third_party_task' => [
        'app_key' => 'test',
        'access_token' => 'test',  // 申请时间：2025-04-16 17:00，有效期 5 年
    ]
];
