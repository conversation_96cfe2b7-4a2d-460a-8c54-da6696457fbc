CREATE TABLE IF NOT EXISTS `catalog` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '上级分类',
  `catalog_name` varchar(100) NOT NULL COMMENT '名称',
  `catalog_name_second` varchar(100) DEFAULT '' COMMENT '副名称',
  `catalog_name_alias` varchar(100) NOT NULL DEFAULT '' COMMENT '别名',
  `content` text COMMENT '详细介绍',
  `seo_title` varchar(100) NOT NULL DEFAULT '' COMMENT 'seo 标题',
  `seo_keywords` varchar(255) NOT NULL DEFAULT '' COMMENT 'seo 关键字',
  `seo_description` text COMMENT 'seo 描述',
  `attach_file` varchar(100) DEFAULT '' COMMENT '附件',
  `attach_thumb` varchar(100) DEFAULT '' COMMENT '缩略图',
  `sort_order` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `data_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数据量',
  `page_size` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '每页显示数量',
  `status_is`  NOT NULL DEFAULT 'Y' COMMENT '状态',
  `menu_is` varchar(16) DEFAULT 'N' COMMENT '是否导航显示',
  `redirect_url` varchar(255) NOT NULL DEFAULT '' COMMENT '跳转地址',
  `display_type` varchar(16) NOT NULL DEFAULT 'list' COMMENT '显示方式',
  `template_list` varchar(100) NOT NULL DEFAULT '' COMMENT '列表模板',
  `template_page` varchar(100) NOT NULL DEFAULT '' COMMENT '单页模板',
  `template_show` varchar(100) NOT NULL DEFAULT '' COMMENT '内容页模板',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '录入时间',
  `last_update_time` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='全局分类';

INSERT INTO `catalog` (`id`, `parent_id`, `catalog_name`) VALUES
  (1, 0, '音频')
  ,(5, 1, '广播剧')
  ,(8, 1, '音乐')
  ,(18, 5, '言情')
  ,(65, 1, '铃声')
  ,(108, 1, '声音恋人')
;

CREATE TABLE IF NOT EXISTS `m_appupdate` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(32) NOT NULL COMMENT 'app名称',
  `version` varchar(32) NOT NULL COMMENT '版本号',
  `intro` text NOT NULL COMMENT '介绍',
  `changelog` text NOT NULL COMMENT '变更日志',
  `status` tinyint(3) unsigned NOT NULL COMMENT '发布状态',
  `appurl` varchar(100) NOT NULL COMMENT 'app下载地址',
  `update_time` int(10) unsigned NOT NULL COMMENT '更新时间',
  `device` tinyint(4) DEFAULT '0' COMMENT '设备类型，0：Android，1：iOS，2：Windows',
  `size` int(10) unsigned NOT NULL COMMENT 'app大小(M)',  -- 暂不支持 float 类型，单元测试先用 int 替代
  `force_download` int(11) NOT NULL DEFAULT '0' COMMENT '是否强制更新',
  `developer` varchar(255) NOT NULL DEFAULT '河北宅喵网络科技有限公司' COMMENT '开发者名称',
  `privacy_url` varchar(255) NOT NULL DEFAULT '' COMMENT '隐私政策地址',
  `permission_url` varchar(255) NOT NULL DEFAULT '' COMMENT '权限用途地址',
  `appurl2` varchar(100) NOT NULL DEFAULT '' COMMENT 'app 下载地址（Android: 64 位包，Windows: zip 包）',
  `push_version` varchar(32) NOT NULL DEFAULT '' COMMENT '推送版本号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `m_appupdate`
  (`id`, `title`, `version`, `intro`, `changelog`, `status`, `appurl`, `update_time`, `device`, `size`, `force_download`, `developer`, `privacy_url`, `permission_url`, `appurl2`, `push_version`)
VALUES
  -- testGetUpdate
  -- testGetApp
  (1, '猫耳FM', '6000620', '6.0.6', '优化用户体验', 1, 'oss://app/202311/24/MissEvan.apk', UNIX_TIMESTAMP(), 0, '1', 0, 'xx有限公司', 'https://link.test.com/rule/privacy', 'https://link.test.com/rule/app-permission-android', 'oss://app/202311/24/MissEvan_64.apk', '6000620'),
  (2, '猫耳FM', '6000630', '6.0.6', '优化用户体验', 2, 'oss://app/202311/24/MissEvan.apk', UNIX_TIMESTAMP(), 0, '1', 0, 'xx有限公司', 'https://link.test.com/rule/privacy', 'https://link.test.com/rule/app-permission-android', 'oss://app/202311/24/MissEvan_64.apk', '6000630'),
  (3, '猫耳FM', '6000631', '6.0.7', '优化用户体验', 2, 'oss://app/202311/24/MissEvan.apk', UNIX_TIMESTAMP(), 0, '1', 1, 'xx有限公司', 'https://link.test.com/rule/privacy', 'https://link.test.com/rule/app-permission-android', 'oss://app/202311/24/MissEvan_64.apk', '6000630'),
  (4, '猫耳FM(M站) - 让广播剧流行起来', '85', '6.0.5', "1. 优化用户体验\n2. 修复已知 bug", 1, '', UNIX_TIMESTAMP(), 1, '0', 1, 'xx有限公司', 'https://link.test.com/rule/privacy', 'https://link.test.com/rule/app-permission-android', '', '85'),
  (5, '猫耳FM(M站) - 让广播剧流行起来', '86', '6.0.6', "1. 优化用户体验\n2. 修复已知 bug", 1, '', UNIX_TIMESTAMP(), 1, '0', 0, 'xx有限公司', 'https://link.test.com/rule/privacy', 'https://link.test.com/rule/app-permission-android', '', '86'),
  (292, '猫耳FM(M站) - 让广播剧流行起来', '292', '5.7.4', "1. 优化用户体验\n2. 修复已知 bug", 1, 'oss://app/202311/24/MissEvan.apk', UNIX_TIMESTAMP(), 0, '0', 0, 'xx有限公司', 'https://link.test.com/rule/privacy', 'https://link.test.com/rule/app-permission-android', 'oss://app/202311/24/MissEvan_64.apk', '292'),
  (309, '猫耳FM(M站) - 让广播剧流行起来', '63', '4.9.5', "1. 优化用户体验\n2. 修复已知 bug", 1, '', UNIX_TIMESTAMP(), 1, '0', 0, 'xx有限公司', '', '', '', '63'),
  (339, '猫耳FM', '6010271', '6.1.2', "1. 优化用户体验\n2. 修复已知 bug", 1, 'oss://app/202405/18/MissEvan.apk', 1715967801, 0, '86.71', 0, '河北宅喵网络科技有限公司', 'https://link.missevan.com/rule/privacy', 'https://link.missevan.com/rule/app-permission-android', 'oss://app/202405/18/MissEvan_64.apk', '6010060'),
  (341, '猫耳FM(M站) - 让广播剧流行起来', '108', '6.1.1', "1. 优化用户体验\n2. 修复已知 bug", 1, '', UNIX_TIMESTAMP(), 1, '0', 0, 'xx有限公司', '', '', '', '108'),
  -- testGetUpdate 测试最新的 iOS 版本
  (999, '猫耳FM(M站) - 让广播剧流行起来', '999', '9.9.9', "1. 优化用户体验\n2. 修复已知 bug", 1, '', UNIX_TIMESTAMP(), 1, '0', 0, 'xx有限公司', '', '', '', '999');

CREATE TABLE IF NOT EXISTS `mowangskuser` (
  `id` bigint NOT NULL COMMENT '用户 ID',
  `confirm` int unsigned NOT NULL DEFAULT '0' COMMENT '用户权限或身份标识',
  `username` varchar(20) NOT NULL,
  `cip` varchar(50) NOT NULL DEFAULT '',
  `uip` varchar(50) NOT NULL,
  `ctime` int unsigned NOT NULL,
  `utime` int unsigned NOT NULL,
  `quanxian` varchar(5) NOT NULL DEFAULT '',
  `teamid` int unsigned NOT NULL DEFAULT '1',
  `teamname` varchar(20) NOT NULL DEFAULT 'Drrr',
  `ban` tinyint unsigned NOT NULL DEFAULT '0',
  `ustr` int unsigned NOT NULL DEFAULT '0',
  `uint` int unsigned NOT NULL DEFAULT '0',
  `uagi` int unsigned NOT NULL DEFAULT '0',
  `point` int unsigned NOT NULL DEFAULT '50',
  `nowsound` int unsigned NOT NULL DEFAULT '0' COMMENT '记录用户当前M音时长',
  `iconid` int unsigned NOT NULL DEFAULT '0',
  `iconurl` varchar(60) NOT NULL DEFAULT '',
  `iconcolor` varchar(50) NOT NULL DEFAULT '',
  `subtitle` varchar(10) NOT NULL DEFAULT '',
  `boardiconid` int unsigned NOT NULL DEFAULT '0',
  `boardiconurl` varchar(60) NOT NULL DEFAULT '',
  `boardiconcolor` varchar(50) NOT NULL DEFAULT '#B1B1B1m#CECECEm#B1B1B1m#6A6A6Am#B1B1B1',
  `coverid` int DEFAULT NULL COMMENT '封面图id',
  `coverurl` varchar(60) DEFAULT NULL COMMENT '封面图',
  `isnewmsg` tinyint unsigned NOT NULL DEFAULT '0',
  `userintro` text,
  `userintro_audio` int unsigned DEFAULT NULL,
  `likenum` int unsigned DEFAULT '0' COMMENT '点赞数',
  `fansnum` int unsigned DEFAULT '0' COMMENT '粉丝数',
  `follownum` int unsigned DEFAULT '0' COMMENT '关注数',
  `soundnum` int unsigned DEFAULT '0' COMMENT '个人语音数',
  `albumnum` int unsigned DEFAULT '0' COMMENT '个人专辑数',
  `imagenum` int unsigned DEFAULT '0' COMMENT '个人图片数',
  `feednum` int DEFAULT '0' COMMENT 'feed流未读信息',
  `soundnumchecked` int unsigned DEFAULT '0' COMMENT '审核通过的声音',
  `imagenumchecked` int unsigned DEFAULT '0' COMMENT '审核通过的图片',
  `mlevel` tinyint unsigned DEFAULT '1' COMMENT '用户当前等级',
  `avatar` varchar(100) DEFAULT NULL COMMENT '三次元头像',
  `icontype` int DEFAULT '1',
  `coverurl_new` varchar(100) NOT NULL DEFAULT '' COMMENT '用户上传的封面图协议地址',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO mowangskuser
  (id, username, confirm, cip, uip, ctime, utime)
  VALUES
  -- testCheckBuyer
  (1, 'test-user-1', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(2, 'test-user-2', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(666, 'test-user-666', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  -- testBuyGift2
  ,(13, 'test-user-13', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(14, 'test-user-14', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(50, 'test-user-50', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(51, 'test-user-51', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  -- testBuyLuckyGift
  ,(111, 'test-user-111', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(677, 'test-user-677', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(678, 'test-user-678', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(679, 'test-user-679', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(888, 'test-user-888', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(980, 'test-user-988', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(999, 'test-user-999', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(2991, 'test-user-2991', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(2992, 'test-user-2992', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(2993, 'test-user-2993', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(2994, 'test-user-2994', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(346286, 'test-user-346286', 33554432, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  -- testShowNewUserTopup
  ,(988, 'test-user-988', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(3457182, 'test-user-3457182', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  -- testBuy
  ,(333, 'test-user-333', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(444, 'test-user-444', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  -- testBuyWishGoods
  ,(401, 'test-user-401', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  -- testBuyGashapon
  ,(555, 'test-user-555', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  -- testBuyGashapon
  ,(556, 'test-user-556', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(557, 'test-user-557', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  -- testActionCreateWechatPayOrder
  ,(558, 'test-user-558', 1024, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  -- testActionReport
  ,(3013620, 'test-user-557', 0, '127.0.0.1', '127.0.0.1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
;

CREATE TABLE IF NOT EXISTS `m_message_assign` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `recuid` bigint(20) NOT NULL COMMENT '接收者用户 ID',
  `send_uid` bigint(20) NOT NULL COMMENT '发送者用户 ID',
  `title` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '标题',
  `content` varchar(5000) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '正文',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0未读1已读',
  `time` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `guild_live_contract` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guild_id` int unsigned NOT NULL COMMENT '公会 ID',
  `live_id` bigint NOT NULL COMMENT '主播 ID',
  `contract_start` int unsigned NOT NULL COMMENT '合约起始时间',
  `contract_end` int unsigned NOT NULL COMMENT '合约结束时间',
  `contract_duration` int unsigned NOT NULL DEFAULT '0' COMMENT '合约时长：1. 6 个月；2. 1 年；3. 2 年',
  `rate` int unsigned NOT NULL COMMENT '主播所占分成比例',
  `kpi` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '指标',
  `status` int NOT NULL COMMENT '合约状态，-3：已解约；-2：已失效；-1：已拒绝；0：申请/邀约中；1：合约生效中（主播申请入会通过或公会邀约通过）； 2：合约生效中（主播申请退会或公会发起解约）',
  `type` tinyint NOT NULL COMMENT '签约类型（1 主播发起，2 公会发起）',
  `guild_owner` bigint NOT NULL COMMENT '公会会长 ID',
  `guild_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公会名称',
  `create_time` int unsigned NOT NULL COMMENT '创建时间',
  `modified_time` int unsigned NOT NULL COMMENT '修改时间',
  `attr` int unsigned NOT NULL DEFAULT '0' COMMENT '按位运算：第 1 位表示主播在合约期内申请过协商解约；第 2 位表示主播有未处理的降薪申请',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='公会与主播关系表';

INSERT INTO `guild_live_contract`
  (`id`, `guild_id`, `live_id`, `contract_start`, `contract_end`, `contract_duration`, `rate`, `kpi`, `status`, `type`, `guild_owner`, `guild_name`, `create_time`, `modified_time`)
VALUES
  -- testBuyGift2
  (1, 1000007, 51, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 0, '', 1, 1, 51, '测试公会（匆删）', UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
;

CREATE TABLE IF NOT EXISTS `m_image` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户 ID',
  `username` varchar(20) NOT NULL COMMENT ' 用户名',
  `catalog_id` bigint unsigned NOT NULL COMMENT '类别 ID',
  `title` varchar(255) NOT NULL COMMENT '标题',
  `save_name` varchar(255) NOT NULL COMMENT '带路径文件名',
  `file_ext` char(5) NOT NULL DEFAULT 'jpg' COMMENT '扩展名称',
  `file_size` int unsigned NOT NULL DEFAULT '0' COMMENT '文件大小',
  `width` smallint unsigned NOT NULL COMMENT '长度',
  `height` smallint unsigned NOT NULL COMMENT '高度',
  `view_count` int unsigned NOT NULL DEFAULT '0' COMMENT '查看数',
  `comment_count` int unsigned NOT NULL DEFAULT '0' COMMENT '评论数',
  `favorite_count` int unsigned NOT NULL DEFAULT '0' COMMENT '收藏数',
  `access` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '图片属性',
  `status_is` varchar(2) NOT NULL DEFAULT 'Y' COMMENT '状态',
  `sort_order` smallint NOT NULL DEFAULT '0' COMMENT '排序',
  `uptimes` int unsigned NOT NULL DEFAULT '0' COMMENT '被赞次数',
  `checked` tinyint NOT NULL DEFAULT '0' COMMENT '审核状态',
  `source` tinyint NOT NULL DEFAULT '0' COMMENT '来源',
  `create_time` int unsigned NOT NULL DEFAULT '0' COMMENT '上传时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='图片信息表';

INSERT INTO m_image
  (id, user_id, username, catalog_id, title, save_name, width, height)
VALUES
  -- testSingleSound
  (1, 346286, 'InVinCiblezzz', 1, 'test_image', 'test_save_name', 0, 0);

CREATE TABLE IF NOT EXISTS `m_sound` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `catalog_id` bigint unsigned NOT NULL COMMENT '分类 ID',
  `create_time` bigint unsigned NOT NULL COMMENT '创建时间',
  `last_update_time` bigint unsigned NOT NULL,
  `duration` bigint NOT NULL DEFAULT '0' COMMENT '持续时间',
  `user_id` bigint NOT NULL COMMENT '用户 ID',
  `username` varchar(20) NOT NULL,
  `cover_image` varchar(255) NOT NULL COMMENT '封面图片',
  `animationid` int unsigned NOT NULL DEFAULT 0,
  `characterid` int unsigned NOT NULL DEFAULT 0,
  `seiyid` bigint unsigned NOT NULL DEFAULT 0,
  `soundstr` varchar(100) NOT NULL,
  `intro` text NOT NULL COMMENT '简介',
  `soundurl` varchar(100) NOT NULL,
  `soundurl_32` varchar(100) NOT NULL,
  `soundurl_64` varchar(100) NOT NULL,
  `soundurl_128` varchar(100) NOT NULL,
  `downtimes` int unsigned NOT NULL DEFAULT '0' COMMENT '下载次数',
  `uptimes` int unsigned NOT NULL DEFAULT '0' COMMENT '被赞次数',
  `checked` tinyint NOT NULL DEFAULT '0' COMMENT '音频状态',
  `source` tinyint NOT NULL DEFAULT '0' COMMENT '来源',
  `download` tinyint NOT NULL DEFAULT '0' COMMENT '是否允许下载',
  `view_count` int unsigned NOT NULL DEFAULT '0' COMMENT '查看数',
  `comment_count` int unsigned NOT NULL DEFAULT '0' COMMENT '弹幕数',
  `favorite_count` int unsigned NOT NULL DEFAULT '0' COMMENT '收藏数',
  `point` int NOT NULL DEFAULT '0' COMMENT '猫耳数',
  `push` tinyint NOT NULL DEFAULT '0' COMMENT '是否推送',
  `refined` tinyint NOT NULL DEFAULT '0' COMMENT '音频属性',
  `comments_count` int unsigned NOT NULL DEFAULT '0' COMMENT '评论数',
  `sub_comments_count` int unsigned NOT NULL DEFAULT '0' COMMENT '子评论数',
  `pay_type` tinyint DEFAULT '0',
  `type` int DEFAULT '0' COMMENT '音频类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='音频信息表';

INSERT INTO m_sound
  (id, catalog_id, create_time, last_update_time, duration, user_id, username, cover_image, soundstr, intro, checked, soundurl, soundurl_32, soundurl_64, soundurl_128, pay_type)
VALUES
  -- testSingleSound
  (1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 346286, 'InVinCiblezzz', 'test_cover_image', 'test', 'test', 1, 'upos://mefmboss/sound/201202/03/test.mp3', '', '', '', 0)
  ,(2, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 346286, 'InVinCiblezzz', 'test_cover_image', 'test', 'test', 1, 'upos://mefmboss/sound/201202/03/test.mp3', '', '', '', 1)
  ,(4, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 346286, 'InVinCiblezzz', 'test_cover_image', 'test', 'test', 1, 'upos://mefmboss/sound/201202/03/test.mp3', '', '', '', 0)
  ,(5, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 346286, 'InVinCiblezzz', 'test_cover_image', 'test', 'test', 1, 'upos://mefmboss/sound/201202/03/test.mp3', '', '', '', 0)
  ,(6, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 346286, 'InVinCiblezzz', 'test_cover_image', 'test', 'test', 1, 'upos://mefmboss/sound/201202/03/test.mp3', '', '', '', 0)
  -- testGetRecommends
  ,(7, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 346286, 'InVinCiblezzz', 'test_cover_image', 'test', 'test', 1, 'upos://mefmboss/sound/201202/03/test.mp3', '', '', '', 0)
  ,(8, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 346286, 'InVinCiblezzz', 'test_cover_image', 'test', 'test', 1, 'upos://mefmboss/sound/201202/03/test.mp3', '', '', '', 0)
  ,(9, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 346286, 'InVinCiblezzz', 'test_cover_image', 'test', 'test', 1, 'upos://mefmboss/sound/201202/03/test.mp3', '', '', '', 0)
  ,(10, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 346286, 'InVinCiblezzz', 'test_cover_image', 'test', 'test', 1, 'upos://mefmboss/sound/201202/03/test.mp3', '', '', '', 0)
  ,(11, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 346286, 'InVinCiblezzz', 'test_cover_image', 'test', 'test', 1, 'upos://mefmboss/sound/201202/03/test.mp3', '', '', '', 0)
  ,(12, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 346286, 'InVinCiblezzz', 'test_cover_image', 'test', 'test', 1, 'upos://mefmboss/sound/201202/03/test.mp3', '', '', '', 0)
  -- testGetPersonInfo
  ,(3, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 346286, 'InVinCiblezzz', 'test_cover_image', 'test', 'test', 1, 'upos://mefmboss/sound/201202/03/test.mp3', 'upos://mefmboss/sound/201202/03/test.mp3', 'upos://mefmboss/sound/201202/03/test.mp3', 'upos://mefmboss/sound/201202/03/test.mp3', 1)
  -- 默认头像音
  ,(14299, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 9613, 346286, 'InVinCiblezzz', 'test_cover_image', 'test', 'test', 1, 'upos://mefmboss/sound/201202/03/test.mp3', 'upos://mefmboss/sound/201202/03/test.mp3', 'upos://mefmboss/sound/201202/03/test.mp3', 'upos://mefmboss/sound/201202/03/test.mp3', 1)
;

CREATE TABLE IF NOT EXISTS `sound_video`(
  `id` bigint NOT NULL AUTO_INCREMENT,
  `sid` bigint NOT NULL COMMENT '音频 ID',
  `video_url` varchar(255) NOT NULL COMMENT '视频地址',
  `create_time` int NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modified_time` int NOT NULL DEFAULT '0' COMMENT '修改时间',
  `videourl_360` varchar(125) NOT NULL DEFAULT '' COMMENT '360P 视频地址',
  `videourl_480` varchar(125) NOT NULL DEFAULT '' COMMENT '480P 视频地址',
  `videourl_720` varchar(125) NOT NULL DEFAULT '' COMMENT '720P 视频地址',
  `videourl_1080` varchar(125) NOT NULL DEFAULT '' COMMENT '1080P 视频地址',
  `attr` int NOT NULL DEFAULT '0' COMMENT '视频属性，比特位第一位为 1 时表示优先播放',
  `source` tinyint NOT NULL DEFAULT '0' COMMENT '视频来源，1：后台绑定；2：配音秀',
  `checked` tinyint NOT NULL DEFAULT '-1' COMMENT '视频审核状态，-1：待转码；0：待审核；1：审核通过',
  `more` text NOT NULL COMMENT '视频额外信息，格式为 json 字符串，size 字段存放不同视频大小，单位 Bytes',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='音频视频关联表';

CREATE TABLE IF NOT EXISTS `m_sound_image_map` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `sound_id` bigint unsigned NOT NULL DEFAULT '0',
  `image_id` bigint unsigned NOT NULL DEFAULT '0',
  `user_id` bigint NOT NULL DEFAULT '0' COMMENT '用户 ID',
  `stime` varchar(11) NOT NULL DEFAULT '0',
  `size` tinyint unsigned NOT NULL DEFAULT '0',
  `color` int unsigned NOT NULL DEFAULT '0',
  `mode` tinyint unsigned NOT NULL DEFAULT '0',
  `date` int unsigned NOT NULL DEFAULT '0',
  `pool` tinyint unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='音频图片关联表';

INSERT INTO m_sound_image_map
  (id, sound_id, image_id, user_id)
VALUES
  -- testSingleSound
  (1, 1, 1, 346286)
  ,(2, 2, 1, 346286)
  ,(3, 3, 1, 346286);

CREATE TABLE IF NOT EXISTS `m_tag` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '标签 ID',
  `name` varchar(64) NOT NULL COMMENT '标签名称',
  `icon` varchar(100) NOT NULL DEFAULT '' COMMENT '标签图标',
  `cover` varchar(120) DEFAULT '' COMMENT '频道背景图',
  `sintro` varchar(200) NOT NULL DEFAULT '' COMMENT '一句话简介',
  `intro` text COMMENT '标签简介',
  `sound_num` int unsigned NOT NULL DEFAULT '0' COMMENT '声音引用数',
  `image_num` int unsigned NOT NULL DEFAULT '0' COMMENT '图片引用数',
  `album_num` int unsigned NOT NULL DEFAULT '0' COMMENT '专辑引用数',
  `follow_num` int unsigned NOT NULL DEFAULT '0' COMMENT '标签订阅人数',
  `userid` bigint NOT NULL DEFAULT '0' COMMENT '绑定用户 ID',
  `seiyid` bigint unsigned NOT NULL DEFAULT '0' COMMENT '绑定声优 ID',
  `characterid` bigint unsigned NOT NULL DEFAULT '0' COMMENT '绑定角色 ID',
  `animationid` bigint unsigned NOT NULL DEFAULT '0' COMMENT '绑定作品 ID',
  `recommended` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否上首页',
  `sort_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '排序方式',
  `sort_channel` tinyint NOT NULL DEFAULT '0' COMMENT '频道排序',
  `catalogid` bigint unsigned NOT NULL DEFAULT '0' COMMENT '分类 ID',
  `last_upload_time` bigint unsigned NOT NULL DEFAULT '0' COMMENT '最后上传时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='标签表';

INSERT INTO m_tag
  (id, `name`, intro)
VALUES
  -- testSingleSound
  (1, 'test_sound_tag', 'test_sound_tag');

CREATE TABLE IF NOT EXISTS `m_tag_sound_map` (
  `tag_id` bigint unsigned NOT NULL COMMENT '标签 ID',
  `sound_id` bigint unsigned NOT NULL COMMENT '声音 ID',
  PRIMARY KEY (`tag_id`,`sound_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='音频标签关联表';

INSERT INTO m_tag_sound_map
  (tag_id, sound_id)
VALUES
  -- testSingleSound
  (1, 1)
  ,(1, 2)
  ,(1, 3);

CREATE TABLE IF NOT EXISTS `m_catalog_tags` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `catalog_id` bigint unsigned NOT NULL COMMENT '分类 ID',
  `catalog_name_alias` varchar(64) NOT NULL COMMENT '分类名',
  `tags` varchar(64) NOT NULL COMMENT '推荐标签',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分类和标签关联表';

INSERT INTO m_catalog_tags
  (id, catalog_id, catalog_name_alias, tags)
VALUES
  -- testSingleSound
  (1, 1, '测试分类', '测试标签');

CREATE TABLE IF NOT EXISTS `m_user_node_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NOT NULL COMMENT '用户 ID',
  `sound_id` bigint NOT NULL COMMENT '音频 ID',
  `equip_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '设备号',
  `node_ids` varchar(225) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '故事线节点 ID，以半角逗号分隔，如 1,2',
  `current_node_id` int unsigned NOT NULL COMMENT '用户当前选择的节点 ID',
  `create_time` bigint NOT NULL COMMENT '创建时间',
  `modified_time` bigint NOT NULL COMMENT '修改时间',
  `buvid` varchar(64) NOT NULL DEFAULT '' COMMENT 'buvid',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户选择故事线节点表';

INSERT INTO m_user_node_log
(id, user_id, sound_id, equip_id, node_ids, current_node_id, create_time, modified_time, buvid)
VALUES
  -- testFindUserLog
  (1, 346286, 3675, '5ae93485-test-b665-9ada-a26e4dda48ae', '1,2', 123, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '123');

CREATE TABLE IF NOT EXISTS `m_sound_node` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sound_id` bigint NOT NULL COMMENT '音频 ID',
  `node_type` tinyint unsigned NOT NULL COMMENT '节点类型，1 为根节点；2 为子节点；3 为子叶节点',
  `next_ids` varchar(255) NOT NULL COMMENT '子节点 ID，值为包含多个子节点 ID 的 json 字符串',
  `attr` int unsigned NOT NULL DEFAULT '0' COMMENT '节点属性。比特位第一位为 1 表示默认选项',
  `stay_duration` int NOT NULL COMMENT '选择页面停留时长，单位毫秒， -1 为未做选择前一直停留',
  `title` varchar(30) NOT NULL COMMENT '节点标题',
  `question` varchar(60) NOT NULL DEFAULT '' COMMENT '问题',
  `option` varchar(30) NOT NULL DEFAULT '' COMMENT '选项描述',
  `soundurl_user` varchar(125) NOT NULL COMMENT '原音频地址',
  `soundurl` varchar(125) NOT NULL DEFAULT '' COMMENT '原音质音频地址',
  `soundurl_128` varchar(125) NOT NULL DEFAULT '' COMMENT '128K 比特率音质音频地址',
  `soundurl_192` varchar(125) NOT NULL DEFAULT '' COMMENT '192K 比特率音质音频地址',
  `cover` varchar(125) NOT NULL DEFAULT '' COMMENT '封面地址',
  `duration` int unsigned NOT NULL COMMENT '音频时长，单位毫秒',
  `pay_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '付费类型，0：免费；1：单集付费；2：整剧付费',
  `score_threshold` int unsigned NOT NULL DEFAULT '0' COMMENT '可选择时需要达到的分数阈值',
  `button_color` int NOT NULL DEFAULT '16777215' COMMENT '按钮颜色',
  `button_image` varchar(125) NOT NULL DEFAULT '' COMMENT '按钮背景图',
  `checked` tinyint NOT NULL DEFAULT '-1' COMMENT '状态，-1：待转码；0：待审核；1：审核通过；2：报警；3：下架',
  `create_time` bigint NOT NULL COMMENT '创建时间',
  `modified_time` bigint NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='互动剧音频节点表';

CREATE TABLE IF NOT EXISTS `m_event` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(256) NOT NULL COMMENT '活动名称',
  `mobile_cover` varchar(255) DEFAULT NULL COMMENT '手机端封面',
  `main_cover` varchar(256) NOT NULL COMMENT '活动封面',
  `share_cover` varchar(255) NOT NULL DEFAULT '' COMMENT '分享封面图',
  `intro` text NOT NULL COMMENT '活动介绍',
  `short_intro` varchar(255) DEFAULT NULL COMMENT '活动短介绍',
  `bilibili_url_pc` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT 'B站配置的 PC url',
  `bilibili_url_h5` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT 'B站配置的 H5 url',
  `tag_id` int(11) DEFAULT NULL,
  `tag` varchar(64) NOT NULL COMMENT '活动标签',
  `type` tinyint(3) unsigned NOT NULL COMMENT '活动上传类型 0 是音频，1 是图片',
  `vote_start_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '投票开始时间',
  `vote_end_time` int(10) unsigned NOT NULL COMMENT '投票结束时间',
  `draw_start_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '抽奖开始时间',
  `draw_end_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '抽奖结束时间',
  `create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '活动创建时间',
  `end_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '活动结束时间',
  `head` varchar(255) DEFAULT NULL COMMENT '音频头',
  `tail` varchar(255) DEFAULT NULL COMMENT '音频尾',
  `extended_fields` text COMMENT '额外数据，JSON format',
  `status` tinyint(4) DEFAULT NULL COMMENT '手机端是否可见',
  `limit` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '活动每日投票上限',
  `mini_cover` varchar(255) DEFAULT NULL COMMENT '290图片',
  `limit_work` tinyint(4) unsigned DEFAULT '0' COMMENT '每日可投票作品数量限制',
  `limit_vote` tinyint(4) unsigned DEFAULT '0' COMMENT '每日每作品可投票数量限制',
  `do_comment` tinyint(1) unsigned DEFAULT '0' COMMENT '活动是否支持评论 0：否；1：是',
  `attr` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '活动属性',
  `start_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '活动开始时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `special_event_elem` (
  `event_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '活动 ID',
  `elem_id` int(10) unsigned NOT NULL COMMENT '元素 ID',
  `elem_type` tinyint(3) unsigned NOT NULL COMMENT '元素类型（1 为剧集）',
  `intro` varchar(255) NOT NULL DEFAULT '' COMMENT '宣传介绍',
  `url` varchar(255) NOT NULL DEFAULT '' COMMENT '入口链接',
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  `modified_time` int(10) unsigned NOT NULL COMMENT '修改时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='特殊活动与元素对应关联表';

CREATE TABLE IF NOT EXISTS `event_vote` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `event_id` int(10) unsigned NOT NULL COMMENT '活动 id',
  `eid` int(10) unsigned NOT NULL COMMENT '资源 id',
  `vote_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '投票数',
  `category` tinyint(3) unsigned NOT NULL COMMENT '资源类型 0: 单音 1: 图片',
  `reset_vote` int(10) NOT NULL DEFAULT '0' COMMENT '扣除的票数',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `event_vote_detail` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL COMMENT '用户 ID',
  `eid` int(10) unsigned NOT NULL COMMENT '资源 id',
  `event_id` int(10) unsigned NOT NULL COMMENT '活动 id',
  `time` int(10) unsigned NOT NULL COMMENT '投票时间',
  `ip` varchar(50) NOT NULL COMMENT '用户 IP',
  `env` tinyint(3) unsigned NOT NULL COMMENT '1web端2安卓3ios4h5',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- GRANT SELECT ON app_missevan.live TO 'missevan_app'@'%';
CREATE TABLE IF NOT EXISTS `live` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `room_id` int(10) unsigned NOT NULL COMMENT '直播间房间 ID',
  `catalog_id` int(10) unsigned NOT NULL DEFAULT '0',
  `title` varchar(30) NOT NULL DEFAULT '' COMMENT '直播间名称',
  `intro` varchar(160) NOT NULL DEFAULT '' COMMENT '直播间简介',
  `status` tinyint(4) DEFAULT '0' COMMENT '直播间状态，-1：用户注销（搜索隐藏）；0：没有开启房间；1：房间开启',
  `live_start_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '开播时间',
  `contract_id` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '主播合约 ID',
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  `modified_time` int(10) unsigned NOT NULL COMMENT '修改时间',
  `user_id` bigint(20) NOT NULL COMMENT '主播 ID',
  `cover` varchar(255) NOT NULL DEFAULT '' COMMENT '直播间封面',
  `score` bigint(20) NOT NULL DEFAULT '0' COMMENT '直播间热度',
  `username` varchar(20) DEFAULT '' COMMENT '主播昵称，冗余字段',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO live
  (id, room_id, status, live_start_time, create_time, modified_time, user_id)
VALUES
  -- testGetFeedNotice、testCheckOpen
  (1, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1);

-- GRANT SELECT ON app_missevan.m_attention_user TO 'missevan_app'@'%';
CREATE TABLE IF NOT EXISTS `m_attention_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_active` bigint(20) NOT NULL COMMENT '关注者 ID',
  `user_passtive` bigint(20) NOT NULL COMMENT '被关注者 ID',
  `time` int(11) NOT NULL COMMENT '关注时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='关注';

INSERT INTO m_attention_user
  (id, user_active, user_passtive, `time`)
VALUES
  -- testGetFeedNotice、testCheckOpen
  (1, 346286, 1, UNIX_TIMESTAMP());

CREATE TABLE IF NOT EXISTS `m_person_home_page` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `recid` bigint NOT NULL COMMENT '用户 ID',
  `soundid` bigint NOT NULL COMMENT '音频 ID',
  `status` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`,`gmt_create`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户 feed 流表';

-- GRANT SELECT, DELETE ON `comment_like` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `comment_like` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `cid` int(10) unsigned NOT NULL,
  `sub` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT '1：子评论；0：评论',
  `userId` bigint(20) NOT NULL COMMENT '用户 ID',
  `type` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '类型，1：点赞；2：点踩',
  `create_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modified_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- GRANT SELECT, UPDATE, DELETE ON `commentnotice` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `commentnotice` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `c_user_id` bigint(20) NOT NULL COMMENT '评论用户',
  `c_user_name` varchar(20) NOT NULL COMMENT '评论用户名',
  `a_user_id` bigint(20) NOT NULL COMMENT '被 at 用户的用户 ID',
  `a_user_name` varchar(20) NOT NULL COMMENT '被 at 用户的用户名',
  `type` int(2) NOT NULL COMMENT '种类，1：音频；2：专辑；3：新闻',
  `eId` int(11) NOT NULL COMMENT '对应 ID',
  `title` varchar(100) NOT NULL COMMENT '被评论的 title',
  `comment_id` int(11) NOT NULL COMMENT '评论 ID',
  `sub` int(2) unsigned NOT NULL COMMENT '是否为子评论，0：否；1：是',
  `isread` int(2) unsigned NOT NULL COMMENT '是否已读',
  `notice_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否是被 @ 消息，0：评论提醒；1：被 @ 消息',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- GRANT SELECT, INSERT, UPDATE ON `m_user_cover` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `m_user_cover` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` bigint(20) NOT NULL COMMENT '创建时间',
  `modified_time` bigint(20) NOT NULL COMMENT '修改时间',
  `user_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户 ID',
  `cover` varchar(100) NOT NULL DEFAULT '' COMMENT '封面图',
  `checked` tinyint(4) NOT NULL DEFAULT '0' COMMENT '审核状态，0: 未审核；1: 已通过；2: 已拒绝；3: 已失效',
  `reason` varchar(255) NOT NULL DEFAULT '' COMMENT '拒绝原因',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户上传封面图审核表';

-- GRANT SELECT ON `mowangsksoundseiy` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `mowangsksoundseiy` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(40) CHARACTER SET utf8mb4 NOT NULL COMMENT '声优名称',
  `icon` varchar(100) NOT NULL,
  `profile` text NOT NULL,
  `gender` tinyint(1) unsigned NOT NULL,
  `initial` tinyint(3) unsigned NOT NULL COMMENT '首字母，0：其他；1 - 26：26 个字母',
  `birthyear` smallint(4) unsigned NOT NULL,
  `birthmonth` smallint(2) NOT NULL,
  `birthday` smallint(2) NOT NULL,
  `birthmonthday` smallint(6) DEFAULT '0',
  `bloodtype` tinyint(1) NOT NULL COMMENT '血型',
  `career` tinyint(1) NOT NULL COMMENT '职业',
  `group` varchar(32) NOT NULL COMMENT '社团',
  `weibo` varchar(64) NOT NULL COMMENT '微博',
  `weiboname` varchar(64) NOT NULL COMMENT '微博名称',
  `baike` varchar(64) NOT NULL COMMENT '百科',
  `baikename` varchar(64) NOT NULL COMMENT '百科名称',
  `mid` bigint(20) DEFAULT '0' COMMENT '用户 ID',
  `checked` smallint(6) DEFAULT '0',
  `soundline1` int(11) NOT NULL COMMENT '声线 1',
  `soundline2` int(11) NOT NULL COMMENT '声线 2',
  `soundline3` int(11) NOT NULL COMMENT '声线 3',
  `seiyalias` varchar(60) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- GRANT SELECT ON m_homepage_icon TO `missevan_app`@`%`
CREATE TABLE IF NOT EXISTS `m_homepage_icon` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键'
  ,`sort` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '排序'
  ,`title` varchar(20) NOT NULL COMMENT '图标名'
  ,`icon` varchar(120) NOT NULL COMMENT '简洁白模式的入口图标'
  ,`dark_icon` varchar(120) NOT NULL COMMENT '黑夜模式的入口图标'
  ,`url` varchar(255) NOT NULL COMMENT '地址'
  ,`create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间'
  ,`modified_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间'
  ,`tab_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '对应 Tab ID'
  ,`type` int(11) NOT NULL DEFAULT '0' COMMENT '图标类型：1：我的页图标；2：首页 tab 所属图标'
  ,`archive` int(11) NOT NULL DEFAULT '0' COMMENT '是否为历史归档 0 ：否， 1 ：是（归档）'
  ,`anchor_name` varchar(20) NOT NULL DEFAULT '' COMMENT '图标名称标识（只有提示红点的图标设置此字段）'
  ,PRIMARY KEY (`id`)
  -- ,KEY `idx_type_archive` (`type`,`archive`)
  -- ,KEY `idx_tabid` (`tab_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='我的页图标'
;

CREATE INDEX idx_type_archive ON `m_homepage_icon` (`type`, `archive`);
CREATE INDEX idx_tabid ON `m_homepage_icon` (`tab_id`);

INSERT INTO `m_homepage_icon`
  (`id`, `sort`, `title`, `icon`, `dark_icon`, `url`, `create_time`, `modified_time`, `tab_id`, `type`, `archive`, `anchor_name`)
VALUES
  -- testActionHomepageIcons、testGetIcons
  (8, 8, '我的消息', 'oss://test.jpg', 'oss://test.jpg', 'missevan://message', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 0, 'message')
  ,(9, 9, '我的钱包', 'oss://test.jpg', 'oss://test.jpg', 'missevan://wallet', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 0, 'wallet')
  ,(10, 10, '测试未填写 anchor_name', 'oss://test.jpg', 'oss://test.jpg', 'missevan://wallet', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 0, '')
  ,(11, 11, '专题', 'oss://test.jpg', 'oss://test.jpg', 'missevan://topic', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 0, '')
  ,(12, 12, '活动', 'oss://test.jpg', 'oss://test.jpg', 'missevan://event', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 0, '')
  ,(13, 13, '声音恋人', 'oss://test.jpg', 'oss://test.jpg', 'https://www.test.com/mgame/lovegame?webview=1', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 0, '')
  ,(14, 0, '测试', 'http://static.missevan.com/profile/icon01.png', 'http://static.missevan.com/profile/icon01.png', 'missevan://mall/bilibili', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 0, 'test')
  -- testGetHomepageTabs
  ,(201, 1, '索引', 'http://static.missevan.com/profile/icon01.png', 'http://static.missevan.com/profile/icon01.png', 'missevan://drama/filter', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 101, 2, 0, '')
  ,(202, 0, '时间表', 'http://static.missevan.com/profile/icon01.png', 'http://static.missevan.com/profile/icon01.png', 'missevan://drama/timeline', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 101, 2, 0, '')
;

-- GRANT SELECT, INSERT, UPDATE ON `persona` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `persona` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `uuid` char(36) NOT NULL DEFAULT '' COMMENT '设备 ID',
  `equip_id` char(36) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '设备号',
  `persona` int unsigned DEFAULT '1' COMMENT '用户画像：1-8 位存模块画像（1.大众 2. 普通男 3. 普通女 4. 腐女）；9-16 位猜你喜欢音推荐策略',
  `user_id` bigint DEFAULT NULL COMMENT '用户 ID',
  `create_time` bigint NOT NULL COMMENT '创建时间',
  `modified_time` bigint NOT NULL COMMENT '修改时间',
  `favor_tags` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '用户选择的偏好标签',
  `buvid` varchar(64) NOT NULL DEFAULT '' COMMENT 'buvid',
  `points` varchar(255) NOT NULL DEFAULT '' COMMENT 'JSON 字符串，存储画像分数，如：{"7":100,"9":1}',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='用户画像';

-- GRANT SELECT, INSERT, UPDATE ON `user_addendum` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `user_addendum` (
  `id` bigint NOT NULL COMMENT '用户 ID',
  `sex` tinyint DEFAULT NULL COMMENT '性别（0 未知，1 为男，2 为女）',
  `birthday` date DEFAULT NULL,
  `qq` varchar(32) DEFAULT NULL COMMENT 'QQ 昵称',
  `weibo` varchar(32) DEFAULT NULL COMMENT '微博昵称',
  `wechat` varchar(32) DEFAULT NULL COMMENT '微信昵称',
  `bilibili` varchar(32) DEFAULT NULL COMMENT 'Bilibili 昵称',
  `apple` varchar(32) DEFAULT NULL COMMENT 'Apple 昵称',
  `message_config` varchar(255) DEFAULT NULL COMMENT '消息设置',
  `ip` varchar(50) DEFAULT NULL COMMENT '用户 IP',
  `ip_detail` text COMMENT '用户 IP 详情',
  `sobot` bigint NOT NULL DEFAULT 0 COMMENT '用户智齿客服 partnerId 版本，0：新版；-1：旧版；> 0：旧版',
  `sign` varchar(255) DEFAULT NULL COMMENT '签到信息，e.g. {"continuous_days":97,"continuous_days_end_time":1733983331}',
  `more` varchar(1024) DEFAULT NULL COMMENT '更多详情',
  `birthdate_mmdd` char(4) DEFAULT NULL COMMENT '用于检索的生日月日（例如 12 月 26 日生日为 1226）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- GRANT SELECT ON `m_persona_module_element` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `m_persona_module_element` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `module_id` bigint(20) NOT NULL COMMENT '模块 ID',
  `persona_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '用户画像 ID',
  `element_type` tinyint(3) unsigned DEFAULT '0' COMMENT '元素类型，1：音单；2：剧集',
  `element_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '元素 ID',
  `summary` varchar(255) DEFAULT NULL COMMENT '剧集或音单元素摘要介绍',
  `cover` varchar(255) NOT NULL DEFAULT '' COMMENT '剧集或音单自定义封面图',
  `sort` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '0 为隐藏元素，正整数为元素排序顺序，由小到大排序',
  `create_time` bigint(20) NOT NULL COMMENT '创建时间',
  `modified_time` bigint(20) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- GRANT SELECT ON `you_might_like_module` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `you_might_like_module` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(100) NOT NULL COMMENT '模块名称',
  `creator_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '负责人用户 ID',
  `element_type` tinyint(3) unsigned NOT NULL COMMENT '模块类型，1：音单模块；2：剧集模块；3：音频模块；5：直播模块',
  `element_attr` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '模块的属性，比特位第 3 位为 1 时表示封面图样式为堆叠样式，为 0 时表示封面图样式为扁平样式',
  `weekly_task_target` int(10) NOT NULL DEFAULT '0' COMMENT '每周更新音频要求的数量',
  `skipped` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '比特位为 1 -- 跳过对应类型检查，0 -- 不跳过；第一位 -- weekly-module，第二位 -- 以后用到的请向这里添加，依此类推',
  `update_type` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '模块更新类型，1：主题模块；2：轮换模块；3：换血模块',
  `create_time` bigint(20) NOT NULL COMMENT '创建时间',
  `modified_time` bigint(20) NOT NULL COMMENT '更新时间',
  `more` text COMMENT '额外信息，格式为 json 字符串，详情文档：https://info.missevan.com/pages/viewpage.action?pageId=97891396',
  `element_style` tinyint(4) NOT NULL DEFAULT '0' COMMENT '模块样式，0：竖版；1：横版；2：排行榜；3：滑动',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- GRANT SELECT ON `m_recommended_elements` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `m_recommended_elements` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `client` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '平台（0 安卓或 iOS，1 安卓，2 iOS）',
  `module_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '模块类型，1：版头图模块；2：精品必听、活动等四个小图标模块；3：猜你喜欢音模块；4：精品周更模块；5：推荐剧集或音单模块；6：今日推荐音模块',
  `module_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '模块 ID',
  `element_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '元素类型，0：其它；1：音单；2：剧集；3：单音；4：活动',
  `element_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '元素 ID',
  `summary` varchar(255) NOT NULL DEFAULT '' COMMENT '简介',
  `cover` varchar(255) NOT NULL DEFAULT '' COMMENT '封面',
  `url` varchar(255) NOT NULL DEFAULT '' COMMENT '原始链接',
  `sort` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '排序值，0、1、2... 越小越靠前，由程序控制',
  `creator_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '负责人用户 ID',
  `start_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '自动上线时间',
  `end_time` bigint(20) NOT NULL DEFAULT '0' COMMENT '自动下线时间',
  `archive` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否为历史归档，0：否， 1：是（即被删去的）',
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL COMMENT '更新时间',
  `more` json DEFAULT NULL COMMENT '更多详情',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='推荐位表';

-- GRANT SELECT, INSERT, UPDATE, DELETE ON `m_album` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `m_album` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 NOT NULL COMMENT '专辑标题',
  `intro` text COMMENT '专辑介绍',
  `catalog_id` smallint(5) unsigned NOT NULL DEFAULT '0' COMMENT '分类 ID',
  `create_time` int(10) unsigned NOT NULL COMMENT '创建时间',
  `last_update_time` int(10) unsigned DEFAULT NULL COMMENT '最后更新',
  `user_id` bigint(20) NOT NULL COMMENT '用户 ID',
  `username` varchar(20) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '作者名称',
  `cover_image` varchar(255) NOT NULL DEFAULT '' COMMENT '封面图片',
  `uptimes` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '被赞次数',
  `refined` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '音单属性。比特位第 1 位为 1 表示加精；第 2 位为 1 表示音单封面图未设置；比特位第 5 位为 1 表示私有音单。',
  `checked` tinyint(4) NOT NULL DEFAULT '0' COMMENT '审核状态，0：未审核；1：已审核通过；2：报警音单；3：下架',
  `source` tinyint(4) NOT NULL DEFAULT '0' COMMENT '来源',
  `view_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '查看数',
  `comment_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '评论数',
  `favorite_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '收藏数',
  `music_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '音乐数',
  `sort` bigint(20) NOT NULL DEFAULT '0' COMMENT '自建音单排序值',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- GRANT SELECT, INSERT ON `m_point_feed` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `m_point_feed` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sound_id` bigint unsigned NOT NULL COMMENT '音频 ID',
  `user_id` bigint NOT NULL COMMENT '投食者 ID',
  `create_time` bigint unsigned NOT NULL COMMENT '投食时间',
  `num` smallint unsigned NOT NULL COMMENT '投食鱼干数量',
  `catalog_id` bigint NOT NULL COMMENT '音频分类 ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- GRANT SELECT ON `dub` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `dub` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(255) NOT NULL COMMENT '素材名',
  `video_url` varchar(255) NOT NULL COMMENT '视频地址',
  `cover_url` varchar(255) NOT NULL COMMENT '封面图地址',
  `from` varchar(100) NOT NULL COMMENT '来自',
  `duration` int(11) NOT NULL,
  `eid` int(11) NOT NULL COMMENT '活动id',
  `more` text COMMENT '更多信息',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- GRANT SELECT ON `subtitle` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `subtitle` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `context` varchar(255) NOT NULL,
  `stime` int(11) NOT NULL COMMENT '字幕起始时间，单位为毫秒',
  `etime` int(11) NOT NULL COMMENT '字幕结束时间，单位为毫秒',
  `did` int(11) NOT NULL COMMENT '配音id',
  `role` varchar(10) NOT NULL COMMENT '人物',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- GRANT SELECT ON `m_persona_collection` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `m_persona_collection` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `persona_id` bigint NOT NULL COMMENT '画像 ID',
  `elem_type` tinyint unsigned NOT NULL COMMENT '元素类型： 1 为音单，2 为剧集',
  `elem_id` bigint NOT NULL COMMENT '元素 ID（音单或剧集）',
  `pace` int unsigned NOT NULL DEFAULT '1' COMMENT '步长（取音单或剧集中取出的单音个数）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `m_persona_collection`
  (`id`, `persona_id`, `elem_type`, `elem_id`, `pace`)
VALUES
  (1, 2, 1, 1, 1)
  -- testGetRecommends
  ,(2, 3, 1, 2, 6)
;

-- GRANT SELECT, INSERT, UPDATE, DELETE ON `m_sound_album_map` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `m_sound_album_map` (
  `id` int NOT NULL AUTO_INCREMENT,
  `album_id` int unsigned NOT NULL COMMENT '专辑 ID',
  `sound_id` int unsigned NOT NULL COMMENT '声音 ID',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `hot` int NOT NULL DEFAULT '0' COMMENT '热度',
  `time` int NOT NULL COMMENT '添加时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `m_sound_album_map`
  (`id`, `album_id`, `sound_id`, `sort`, `hot`, `time`)
VALUES
  (1, 1, 1, 0, 0, UNIX_TIMESTAMP())
  -- testGetRecommends
  ,(2, 2, 7, 0, 0, UNIX_TIMESTAMP())
  ,(3, 2, 8, 1, 0, UNIX_TIMESTAMP())
  ,(4, 2, 9, 2, 0, UNIX_TIMESTAMP())
  ,(5, 2, 10, 3, 0, UNIX_TIMESTAMP())
  ,(6, 2, 11, 4, 0, UNIX_TIMESTAMP())
  ,(7, 2, 12, 5, 0, UNIX_TIMESTAMP())
;

-- GRANT SELECT ON `m_recommend_popup` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `m_recommend_popup` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `creative_id` char(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创意 ID，用于广告主与自己平台上的创意进行映射',
  `gender` tinyint NOT NULL DEFAULT '0' COMMENT '用户画像（0 未知，1 为普通男，2 为普通女，3为乙女',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主题',
  `url` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '弹窗链接',
  `vendor` tinyint(1) NOT NULL DEFAULT '0' COMMENT '渠道（0：Bilibili）',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态 -1：软删除；0：已停止；1：生效中',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `project_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '广告计划 ID',
  `group_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '广告组 ID',
  `tab` tinyint NOT NULL DEFAULT '1' COMMENT '首次默认 tab 1：推荐；2：直播',
  `element_id` bigint NOT NULL DEFAULT '0' COMMENT '元素 ID',
  `more` json DEFAULT NULL COMMENT '更多详情',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='推荐弹窗';

-- GRANT SELECT ON `app_missevan`.`gift` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `gift` (
  `id` int(11) NOT NULL AUTO_INCREMENT
  ,`name` varchar(255) DEFAULT NULL
  ,`price` int(11) DEFAULT NULL
  ,`type` tinyint(4) DEFAULT '1' COMMENT '1 为直播间普通；4 为猫耳男友；7 为剧集打赏；8 为直播间白给礼物'
  ,PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `gift`
  (`id`, `name`, `price`, `type`)
VALUES
  -- testBuyGift2
  (1, 'test_gift', 6, 1)
  -- testSendRebateGifts
  ,(2, 'test_gift', 6, 8)
;

-- GRANT SELECT ON `app_missevan`.`guild` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `guild` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键'
  ,`name` varchar(50) CHARACTER SET utf8mb4 NOT NULL COMMENT '公会名称'
  ,`intro` varchar(255) NOT NULL DEFAULT '' COMMENT '公会简介'
  ,`owner_name` varchar(255) NOT NULL COMMENT '法人代表姓名'
  ,`owner_id_number` varchar(255) NOT NULL COMMENT '法人代表身份证号'
  ,`owner_id_people` varchar(255) NOT NULL COMMENT '法人代表手持身份证正面照'
  ,`owner_backcover` varchar(255) NOT NULL COMMENT '法人代表身份证背面'
  ,`mobile` varchar(255) NOT NULL COMMENT '法人代表手机号'
  ,`email` varchar(255) NOT NULL COMMENT '邮箱'
  ,`qq` varchar(20) NOT NULL DEFAULT '' COMMENT 'QQ 号'
  ,`corporation_name` varchar(255) NOT NULL COMMENT '公司名称'
  ,`corporation_address` varchar(255) NOT NULL COMMENT '公司地址'
  ,`corporation_phone` varchar(255) NOT NULL COMMENT '公司电话'
  ,`business_license_number` varchar(255) NOT NULL COMMENT '营业执照号'
  ,`business_license_frontcover` varchar(255) NOT NULL COMMENT '营业执照扫描件'
  ,`tax_account` varchar(255) NOT NULL COMMENT '纳税人识别号'
  ,`bank_account` varchar(255) NOT NULL COMMENT '银行卡号'
  ,`bank_account_name` varchar(20) NOT NULL COMMENT '银行开户名'
  ,`bank` varchar(20) NOT NULL COMMENT '开户行'
  ,`bank_address` varchar(255) NOT NULL COMMENT '开户行所在地'
  ,`bank_branch` varchar(255) NOT NULL COMMENT '开户支行'
  ,`invoice_rate` tinyint(4) NOT NULL DEFAULT '-2' COMMENT '发票税率（-2 税率未知，-1 不开具发票）'
  ,`type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '公会信息类型 1: 加密'
  ,`checked` tinyint(3) NOT NULL DEFAULT '0' COMMENT '公会状态（-1 审核驳回，0 审核中，1 审核通过，2 解散）'
  ,`user_id` bigint(20) NOT NULL COMMENT '公会创建人用户 ID'
  ,`apply_time` int(10) unsigned NOT NULL COMMENT '申请时间'
  ,`create_time` int(10) unsigned NOT NULL COMMENT '创建时间'
  ,`modified_time` int(10) unsigned NOT NULL COMMENT '修改时间'
  ,`live_num` int unsigned NOT NULL DEFAULT '0' COMMENT '签约主播数'
  ,PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COMMENT='公会表';

INSERT INTO `guild`
  (`id`, `name`, `intro`, `owner_name`, `owner_id_number`, `owner_id_people`, `owner_backcover`, `mobile`, `email`, `qq`, `corporation_name`, `corporation_address`, `corporation_phone`, `business_license_number`, `business_license_frontcover`, `tax_account`, `bank_account`, `bank_account_name`, `bank`, `bank_address`, `bank_branch`, `invoice_rate`, `type`, `checked`, `user_id`, `apply_time`, `create_time`, `modified_time`, `live_num`)
VALUES
  -- testGetCreatorTradeInfo
  (9090, '测试公会（匆删）', '简介3', '法人', '123456', 'oss://icon01.png', 'oss://icon01.png', '123', '123', '', '123', '132', '12', '12', 'oss://icon01.png', '12', '121', '21', '2', '12', '12', -1, 0, 1, 12, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 2767)
;

-- GRANT SELECT ON `app_missevan`.`authassignment` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `authassignment` (
  `itemname` varchar(64) NOT NULL
  ,`userid` varchar(64) NOT NULL
  ,`bizrule` text
  ,`data` text
  ,PRIMARY KEY (`itemname`,`userid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- GRANT SELECT, INSERT, DELETE ON `m_like_sound` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `m_like_sound` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint(20) NOT NULL COMMENT '点赞者用户 ID',
  `sound_id` int(11) NOT NULL COMMENT '点赞的声音 ID',
  `ctime` int(11) NOT NULL DEFAULT '0' COMMENT '点赞时间，0 为没有设置时间时，用户点赞数据',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='声音点赞';

-- GRANT SELECT ON `m_checked_sound_review` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `m_checked_sound_review` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `sound_id` int(10) NOT NULL COMMENT '单音 ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户 ID',
  `soundstr` varchar(100) NOT NULL COMMENT '单音标题',
  `intro` text NOT NULL COMMENT '简介',
  `cover_image` varchar(255) NOT NULL COMMENT '封面图片',
  `source` tinyint(4) NOT NULL DEFAULT '0' COMMENT '来源（原创 1 或搬运 0）',
  `download` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否允许下载（1 为禁止，0 为允许）',
  `catalog_id` smallint(5) unsigned NOT NULL COMMENT '分类 ID',
  `animationid` int(10) unsigned NOT NULL COMMENT '作品 ID',
  `characterid` int(10) unsigned NOT NULL COMMENT '角色 ID',
  `seiyid` int(10) unsigned NOT NULL COMMENT '声优 ID',
  `tags` varchar(255) NOT NULL DEFAULT '' COMMENT '标签',
  `create_time` int(10) unsigned NOT NULL,
  `last_update_time` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='UP 主修改已过审单音的临时信息';

-- GRANT SELECT ON `m_vip` TO 'missevan_app'@'%';
CREATE TABLE IF NOT EXISTS `m_vip` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键'
  ,`create_time` bigint NOT NULL COMMENT '创建时间（秒级时间戳）'
  ,`modified_time` bigint NOT NULL COMMENT '修改时间（秒级时间戳）'
  ,`delete_time` bigint NOT NULL DEFAULT 0 COMMENT '删除时间（秒级时间戳）'
  ,`deduct_fee_schedule` bigint NOT NULL COMMENT '付费周期：1 单次付费、2 连续包月、3 连续包季'
  ,`sort` int NOT NULL COMMENT '展示顺序'
  ,`price` int NOT NULL COMMENT '价格（单位：分）'
  ,`platform` tinyint NOT NULL COMMENT '平台：1 iOS、2 Android（非 Google 渠道）、3 Google Play'
  ,`more` JSON COMMENT '更多详情，e.g. { "first_subscribe_discount_price": 9900 }'
  ,PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会员价目';

INSERT INTO m_vip
  (id, create_time, modified_time, delete_time, deduct_fee_schedule, sort, price, platform, more)
VALUES
  -- testActionCreateWechatPayOrder
  -- testUpdateOrder
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 2, 1, 1900, 1, '{"title":"连续包月","first_title":"连续包月首月","order_subject":"连续包月猫耳FM会员","first_subscribe_discount_price":990,"original_price":2500,"first_original_price":1900,"description":"最多可领 155 钻石\\n每月续费 ¥19，可随时取消","first_description":"最多可领 155 钻石\\n每月续费 ¥19，可随时取消","period":2678400,"active":1,"corner_mark_text":"限时"}')
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 3, 2, 5300, 1, '{"title":"连续包季","order_subject":"连续包季猫耳FM会员","original_price":7500,"description":"最多可领 465 钻石\\n每季续费 ¥53，可随时取消","period":8035200}')
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 3, 22800, 1, '{"title":"年费","order_subject":"12 个月猫耳FM会员","original_price":30000,"description":"最多可领 1825 钻石","period":31622400}')
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 4, 6800, 1, '{"title":"3 个月","order_subject":"3 个月猫耳FM会员","original_price":7500,"description":"最多可领 465 钻石","period":8035200}')
  ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 5, 2500, 1, '{"title":"1 个月","order_subject":"1 个月猫耳FM会员","description":"最多可领 155 钻石","period":2678400}')
  ,(6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 2, 1, 1500, 2, '{"title":"连续包月","first_title":"连续包月首月","order_subject":"连续包月猫耳FM会员","first_subscribe_discount_price":990,"original_price":1800,"first_original_price":1500,"description":"最多可领 155 钻石\\n每月续费 ¥15，可随时取消","first_description":"最多可领 155 钻石\\n每月续费 ¥15，可随时取消","period":2678400,"active":1,"corner_mark_text":"限时","wechat_plan_id":1}')
  ,(7, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 3, 2, 3900, 2, '{"title":"连续包季","order_subject":"连续包季猫耳FM会员","original_price":5400,"description":"最多可领 465 钻石\\n每季续费 ¥39，可随时取消","period":8035200,"wechat_plan_id":2}')
  ,(8, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 3, 16800, 2, '{"title":"年费","order_subject":"12 个月猫耳FM会员","original_price":21600,"description":"最多可领 1825 钻石","period":31622400}')
  ,(9, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 4, 4900, 2, '{"title":"3 个月","order_subject":"3 个月猫耳FM会员","original_price":5400,"description":"最多可领 465 钻石","period":8035200}')
  ,(10, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 5, 1800, 2, '{"title":"1 个月","order_subject":"1 个月猫耳FM会员","description":"最多可领 155 钻石","period":2678400}')
  ,(11, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 2, 1, 1500, 3, '{"title":"连续包月","first_title":"连续包月首月","order_subject":"连续包月猫耳FM会员","first_subscribe_discount_price":990,"original_price":1800,"first_original_price":1500,"description":"最多可领 155 钻石\\n每月续费 ¥15，可随时取消","first_description":"最多可领 155 钻石\\n每月续费 ¥15，可随时取消","period":2678400,"active":1,"corner_mark_text":"限时"}')
  ,(12, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 3, 2, 3900, 3, '{"title":"连续包季","order_subject":"连续包季猫耳FM会员","original_price":5400,"description":"最多可领 465 钻石\\n每季续费 ¥39，可随时取消","period":8035200}')
  ,(13, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 3, 16800, 3, '{"title":"年费","order_subject":"12 个月猫耳FM会员","original_price":21600,"description":"最多可领 1825 钻石","period":31622400}')
  ,(14, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 4, 4900, 3, '{"title":"3 个月","order_subject":"3 个月猫耳FM会员","original_price":5400,"description":"最多可领 465 钻石","period":8035200}')
  ,(15, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 5, 1800, 3, '{"title":"1 个月","order_subject":"1 个月猫耳FM会员","description":"最多可领 155 钻石","period":2678400}')
;

-- GRANT SELECT, INSERT, UPDATE ON `m_user_vip` TO 'missevan_app'@'%';
CREATE TABLE IF NOT EXISTS `m_user_vip` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键'
  ,`create_time` bigint NOT NULL COMMENT '创建时间（秒级时间戳）'
  ,`modified_time` bigint NOT NULL COMMENT '修改时间（秒级时间戳）'
  ,`vip_id` bigint NOT NULL COMMENT 'vip id'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`type` tinyint unsigned NOT NULL COMMENT 'vip 类型，4：点播会员'
  ,`start_time` bigint NOT NULL COMMENT '开始时间（秒级时间戳）'
  ,`end_time` bigint NOT NULL COMMENT '过期时间（秒级时间戳）'
  ,`deduct_record_id` BIGINT NOT NULL DEFAULT 0 COMMENT '扣费记录 ID'
  ,PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户的会员状态';

INSERT INTO m_user_vip
  (id, create_time, modified_time, vip_id, user_id, type, start_time, end_time)
VALUES
  -- testSingleSound、testIsVip、testGetUserVipInfo、testGetVipUserIds
  -- 为了确保单测运行的时候当前时间是在会员有效期内的，会员有效期起止时间设置为固定值
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 1, 4, 1733205636, 2048738436)
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 2, 4, 1733205636, 2048738436)
  -- testActionGetDramaBySoundId、testGetDramaDetail
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 3, 4, 0, 2048738436)
  -- testActionClaimDiamonds
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 3013097, 4, UNIX_TIMESTAMP() - 100, UNIX_TIMESTAMP() + 100)
  -- testGetUserVipInfo
  ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 484564, 4, UNIX_TIMESTAMP() - 100, UNIX_TIMESTAMP() - 50)
;

-- GRANT SELECT ON `m_launch` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `m_launch` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `element_id` bigint DEFAULT NULL COMMENT '猜你喜欢广告位音频 ID',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '链接地址',
  `element_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '元素类型 1：启动图；2：启动音；3：猜你喜欢广告位',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态 -1：已删除；0：未发布；1：已发布',
  `start_time` bigint NOT NULL COMMENT '上线时间',
  `end_time` bigint NOT NULL COMMENT '下线时间',
  `create_time` bigint NOT NULL COMMENT '创建时间',
  `modified_time` bigint NOT NULL COMMENT '更新时间',
  `redirect_url` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '跳转地址',
  `title` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '闪屏名称',
  `label_str` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '认证标志',
  `attr` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '闪屏属性',
  `message` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '跳转按钮文案',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='启动图，启动音，猜你喜欢广告位';

-- GRANT SELECT, INSERT ON `vip_receive_coin_log` TO 'missevan_app'@'%'
CREATE TABLE `vip_receive_coin_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_time` bigint NOT NULL COMMENT '创建时间，单位：秒',
  `modified_time` bigint NOT NULL COMMENT '修改时间，单位：秒',
  `user_id` bigint NOT NULL COMMENT '用户 ID',
  `vip_id` bigint NOT NULL COMMENT '领取时的会员 ID',
  `coin_num` bigint NOT NULL COMMENT '领取的钻石数',
  `receive_time` bigint NOT NULL COMMENT '领取的时间，单位：秒',
  `more` JSON DEFAULT NULL COMMENT '更新详情',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员钻石领取记录表';

-- GRANT SELECT, INSERT, UPDATE ON `m_third_party_task` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `m_third_party_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` bigint NOT NULL COMMENT '创建时间戳，单位：秒',
  `modified_time` bigint NOT NULL COMMENT '最后修改时间戳，单位：秒',
  `task_time` bigint NOT NULL COMMENT '任务发起时间戳，单位：秒',
  `user_id` bigint NOT NULL COMMENT '任务用户 ID',
  `token` VARCHAR NOT NULL COMMENT '任务 token',
  `scene` int NOT NULL COMMENT '第三方场景 1：百度；2：携程；3：大众点评',
  `status` int NOT NULL DEFAULT 0 COMMENT '任务状态 0：未完成；1：已完成未领取奖励；2：已完成已领取奖励',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='三方导流用户任务详情';

INSERT INTO m_third_party_task
  (id, create_time, modified_time, task_time, user_id, token, scene, status)
VALUES
  -- testUpdateTaskStatus
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, 'test', 1, 0)
  -- testTaskAndTaskStatus
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 3013620, 'test', 9, 1)
;

-- GRANT SELECT ON `reward_drama_ranks` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `reward_drama_ranks` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` int unsigned NOT NULL COMMENT '创建时间',
  `modified_time` int unsigned NOT NULL COMMENT '修改时间',
  `drama_id` int unsigned NOT NULL COMMENT '剧集 ID',
  `abstract` varchar(255) NOT NULL DEFAULT '' COMMENT '剧集简介',
  `cover` varchar(255) NOT NULL DEFAULT '' COMMENT '剧集封面',
  `rank` tinyint unsigned NOT NULL COMMENT '剧集排名',
  `type` tinyint unsigned NOT NULL COMMENT '榜单类型（1: 周榜；2: 月榜）',
  PRIMARY KEY (`id`)
  -- KEY `IDX_TYPE_DRAMA_ID` (`type`,`drama_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='剧集势力榜排名归档表';

-- GRANT SELECT ON `m_appearance` TO 'missevan_app'@'%';
CREATE TABLE IF NOT EXISTS `m_appearance` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键'
  ,`create_time` bigint NOT NULL COMMENT '创建时间，单位：秒'
  ,`modified_time` bigint NOT NULL COMMENT '最后修改时间，单位：秒'
  ,`name` varchar(50) NOT NULL COMMENT '名称'
  ,`intro` varchar(125) NOT NULL COMMENT '简介'
  ,`vip` tinyint(2) NOT NULL DEFAULT '0' COMMENT '是否会员免费。0：否；1：是'
  ,`pay` tinyint(2) NOT NULL DEFAULT '0' COMMENT '付费类型。0：非付费使用；1：付费使用'
  ,`price` bigint NOT NULL DEFAULT '0' COMMENT '价格（单位：分）'
  ,`archive` tinyint(2) NOT NULL DEFAULT '0' COMMENT '归档状态 0：未归档；1：已归档'
  ,`appearance`json NOT NULL COMMENT '套装信息'
  ,`more` json NOT NULL COMMENT '更多信息'
  ,PRIMARY KEY (`id`)
  -- ,KEY `idx_modifiedtime` (`modified_time`)
  -- ,KEY `idx_archive` (`archive`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='外观套装详情表';

-- GRANT SELECT, INSERT ON `m_user_appearance` TO 'missevan_app'@'%';
CREATE TABLE IF NOT EXISTS `m_user_appearance` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键'
  ,`create_time` bigint NOT NULL COMMENT '创建时间（秒级时间戳）'
  ,`modified_time` bigint NOT NULL COMMENT '修改时间（秒级时间戳）'
  ,`appearance_id` bigint NOT NULL COMMENT '外观套装 ID'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`start_time` bigint NOT NULL COMMENT '开始时间（秒级时间戳）'
  ,`end_time` bigint NOT NULL COMMENT '过期时间（秒级时间戳）'
  ,`deduct_record_id` bigint DEFAULT '0' COMMENT '扣费记录 ID'
  ,PRIMARY KEY (`id`)
  -- ,KEY `idx_modifiedtime` (`modified_time`)
  -- ,KEY `idx_userid_appearanceid_endtime` (`user_id`, `appearance_id`, `end_time`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COMMENT='用户拥有的外观套装';

-- GRANT SELECT ON `m_tab` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `m_tab` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Tab ID 1：推荐；2：分类；3：直播；100 以内的 ID 是固定不变的',
  `title` varchar(20) COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Tab 名称',
  `url` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '链接',
  `cover` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'Tab 花体字图片',
  `sort` tinyint NOT NULL DEFAULT '0' COMMENT '排序',
  `active` tinyint NOT NULL DEFAULT '0' COMMENT '默认选中 0：否；1：是',
  `archive` tinyint NOT NULL DEFAULT '0' COMMENT '是否为历史归档 0：否；1：是（归档）',
  `position` tinyint NOT NULL COMMENT 'Tab 位置 1：App 首页顶部',
  `create_time` bigint NOT NULL COMMENT '创建时间',
  `modified_time` bigint NOT NULL COMMENT '更新时间',
  `more` json NULL COMMENT '额外信息',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `m_tab`
  (`id`,`title`,`url`,`cover`,`sort`,`active`,`archive`,`position`,`create_time`,`modified_time`, `more`)
VALUES
  -- testGetHomepageTabs
  -- testActionTabs
  (1, '推荐', 'missevan://homepage', null, 2, 1, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '{"page_mark":"missevan://homepage"}')
  ,(2, '分类', 'missevan://catalogs', null, 3, 0, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '')
  ,(3, '直播', 'missevan://live', null, 1, 0, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '{"icon":"oss://icon/a.png","dark_icon":"oss://icon/b.png","page_mark":""}')
  ,(4, '广播剧', 'missevan://catalog/drama/89?homepage=1', null, 4, 0, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '{"page_mark":"drama.drama_homepage"}')
  ,(5, '听书', 'missevan://catalog/drama/86?homepage=1', null, 5, 0, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '{"page_mark":"drama.catalog_86_homepage"}')
  ,(6, '声音恋人', 'missevan://catalog/sound/108?homepage=1', null, 6, 0, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '{"page_mark":"main.voicelover"}')
  ,(7, '天官赐福', 'https://testabc.com', null, 7, 0, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '{"icon":"oss://icon/c.png","dark_icon":"oss://icon/d.png","page_mark":"786_homepage"}')
  ,(8, '新人', 'missevan://homepage/newuser', null, 8, 0, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), '{"icon":"oss://icon/e.png","dark_icon":"oss://icon/f.png","page_mark":"main.new_user"}')
;

-- GRANT INSERT ON `m_report` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `m_report` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键'
  ,`target` tinyint unsigned NOT NULL COMMENT '举报类型，1：单音；2：图片；3：音频弹幕；4：直播间用户；5：直播；6：评论；7：子评论；8：私信；9：音单；10：互动剧节点弹幕；11：个人信息'
  ,`source_id` bigint NOT NULL COMMENT '举报资源的 ID'
  ,`user_id` bigint NOT NULL COMMENT '举报人用户 ID'
  ,`reason` tinyint unsigned NOT NULL COMMENT '举报原因。同 m_report_reason 表 id'
  ,`content` varchar(512) NOT NULL DEFAULT '' COMMENT '举报详细原因'
  ,`status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否受理，0：未处理；1：已处理'
  ,`serious` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否严重违规'
  ,`create_time` int NOT NULL COMMENT '举报时间'
  ,`valid` tinyint NOT NULL DEFAULT 0 COMMENT '举报是否有效。0: 未处理；1: 有效举报；2: 无效举报'
  ,PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='举报表';

-- GRANT SELECT ON `m_report_reason` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `m_report_reason` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间。单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间。单位：秒'
  ,`parent_id` bigint NOT NULL COMMENT '上级分类，无上级分类时为 0'
  ,`name` varchar(125) NOT NULL COMMENT '名称'
  ,`sort` bigint DEFAULT '0' COMMENT '举报原因顺序'
  ,`scene` tinyint DEFAULT '0' COMMENT '举报场景，使用比特位标识场景。第 1 位: 稿件举报；第 2 位: 互动举报；第 3 位: 直播举报'
  ,PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='举报原因分类表';

INSERT INTO m_report_reason
  (id, create_time, modified_time, parent_id, name, sort, scene)
VALUES
  -- testActionReport
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 6, '音频', 1, 1)
  ,(5, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 6, '其他', 1, 1)
;

-- GRANT SELECT ON `m_top_notification` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `m_top_notification` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键 ID'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间戳，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '最后更新时间戳，单位：秒'
  ,`notification_type` tinyint NOT NULL COMMENT '通知类型，1: iOS 充值页面通知'
  ,`title` varchar(20) NOT NULL COMMENT '通知栏标题'
  ,`subtitle` varchar(50) NOT NULL COMMENT '通知栏副标题'
  ,`button_text` varchar(10) NOT NULL  DEFAULT '' COMMENT '通知栏按钮文案'
  ,`link` varchar(255) NOT NULL  DEFAULT '' COMMENT '通知栏跳转链接'
  ,`icon` varchar(255) NOT NULL COMMENT '通知栏图片'
  ,`more` json DEFAULT NULL COMMENT '更多详情'
  ,PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='顶部横幅通知表';

INSERT INTO m_top_notification
  (id, create_time, modified_time, notification_type, title, subtitle, button_text, link, icon, `more`)
VALUES
  -- testTopupMenu、testCreateNotificationLog
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1, '关注猫耳FM公众号有惊喜', '资讯不迷路，听剧更省心', '立即前往', 'https://m.uat.missevan.com/app_notification', 'oss://test/test.png', '{"system_notification":{"title":"关注猫耳FM公众号有惊喜","content":"资讯轻触即达，听剧悄然无忧~ <a href=\"https://m.uat.com\" target=\"_blank\">立即前往 >></a>"}}')
;

-- GRANT INSERT ON `m_top_notification_log` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `m_top_notification_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键'
  ,`create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间戳，单位：秒'
  ,`modified_time` bigint NOT NULL DEFAULT '0' COMMENT '最后更新时间戳，单位：秒'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`notification_id` bigint NOT NULL COMMENT '通知 ID，1: iOS 充值页面通知'
  ,PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='顶部横幅通知记录表';

CREATE UNIQUE INDEX IF NOT EXISTS `uk_userid_notificationid` ON `m_top_notification_log` (`user_id`,`notification_id`);

-- GRANT SELECT ON `m_allow_album` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `m_allow_album` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `album_id` bigint NOT NULL COMMENT '音单 ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='音单白名单';

-- GRANT SELECT ON `m_wechat_offiaccount_reply` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `m_wechat_offiaccount_reply` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` bigint NOT NULL COMMENT '创建时间戳，单位：秒',
  `modified_time` bigint NOT NULL COMMENT '最后修改时间戳，单位：秒',
  `delete_time` bigint NOT NULL COMMENT '删除时间戳，单位：秒',
  `scene` tinyint NOT NULL COMMENT '场景。1：消息回复；2：事件回复',
  `reply_type` tinyint NOT NULL COMMENT '回复的消息类型。1：文本；2：图片；3：语音；4：视频；5：音乐；6：图文',
  `keywords` json NOT NULL COMMENT '自动回复匹配的关键字数组，如 ["word1","word2"]',
  `reply` json NOT NULL COMMENT '回复的内容',
  PRIMARY KEY (`id`)
  -- KEY `idx_modifiedtime` (`modified_time`),
  -- KEY `idx_scene` (`scene`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信公众号自动回复配置表';

INSERT INTO m_wechat_offiaccount_reply
  (id, create_time, modified_time, delete_time, scene, reply_type, keywords, reply)
VALUES
  -- testGetReply、testHandleTextMessage
  (1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 1, '["text test","233"]', '{"content":"test reply"}')
  ,(2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 2, '["image test"]', '{"image":{"media_id":"test_medal_id"}}')
  ,(3, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 1, 3, '["voice test"]', '{"voice":{"media_id":"test_medal_id"}}')
  -- testHandleEventMessage
  ,(4, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 0, 2, 3, '["subscribe_wechat_offiaccount"]', '{"voice":{"media_id":"test_voice_medal_id"}}')
;
