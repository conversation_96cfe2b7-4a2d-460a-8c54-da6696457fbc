CREATE TABLE IF NOT EXISTS `topup_menu` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `price` decimal(7,2) NOT NULL COMMENT '价格（单位：元），为 0 时代表自定义充值项',
  `num` int NOT NULL COMMENT '虚拟货币数量',
  `ccy` tinyint NOT NULL COMMENT '虚拟货币种类\n1. 钻石',
  `device` tinyint NOT NULL COMMENT '哪些场景可见\n1. 安卓机（包括微信和支付宝）\n2. iOS\n3. 网页\n4. 后台充值\n5. 天猫充值\n6. 抖店\n7. 京东\n8. B站大额充值\n9 Google Pay',
  `scope` int NOT NULL DEFAULT 1 COMMENT '应用范围',
  `delete_time` bigint NOT NULL DEFAULT '0' COMMENT '删除时间',
  -- SQLite 不支持 JSON 类型，使用 text 代替
  `more` text DEFAULT NULL COMMENT '更多详情',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO topup_menu
  (id, price, num, ccy, device, scope, more)
  VALUES
  (1, 6, 60, 1, 2, 2, '{}'),
  (2, 18, 180, 1, 2, 1, '{}'),
  (3, 50, 500, 1, 2, 1, '{}'),
  (4, 128, 1280, 1, 2, 1, '{}'),
  (5, 258, 2580, 1, 2, 1, '{}'),
  (6, 518, 5180, 1, 2, 1, '{}'),
  (7, 10, 100, 1, 1, 1, '{}'),
  (8, 25, 250, 1, 1, 1, '{}'),
  (9, 50, 500, 1, 1, 1, '{}'),
  (10, 100, 1000, 1, 1, 1, '{}'),
  (11, 200, 2000, 1, 1, 1, '{}'),
  (12, 500, 5000, 1, 1, 1, '{}'),
  (13, 10, 100, 1, 3, 1, '{}'),
  (14, 25, 250, 1, 3, 1, '{}'),
  (15, 50, 500, 1, 3, 1, '{}'),
  (16, 100, 1000, 1, 3, 1, '{}'),
  (17, 200, 2000, 1, 3, 1, '{}'),
  (18, 500, 5000, 1, 3, 1, '{}'),
  (20, 20, 200, 1, 3, 1, '{}'),
  (21, 30, 300, 1, 3, 1, '{}'),
  (22, 19.9, 199, 1, 4, 1, '{}'),
  (23, 0, 0, 1, 1, 1, '{}'),
  (24, 0, 0, 1, 3, 1, '{}'),
  (25, 1, 10, 1, 4, 1, '{}'),
  (26, 1, 10, 1, 5, 1, '{}'),
  (27, 10, 100, 1, 5, 1, '{}'),
  (28, 20, 200, 1, 5, 1, '{}'),
  (29, 50, 500, 1, 5, 1, '{}'),
  (30, 100, 1000, 1, 5, 1, '{}'),
  (31, 200, 2000, 1, 5, 1, '{}'),
  (32, 500, 5000, 1, 5, 1, '{}'),
  (33, 1000, 10000, 1, 5, 1, '{}'),
  (34, 5000, 50000, 1, 5, 1, '{}'),
  (35, 180, 2020, 1, 5, 1, '{}'),
  (36, 10000, 100000, 1, 5, 1, '{}'),
  (37, 2, 20, 1, 3, 1, '{}'),
  (38, 180, 2021, 1, 5, 1, '{}'),
  (39, 180, 2022, 1, 5, 1, '{}'),
  (40, 50000, 500000, 1, 5, 1, '{}'),
  (41, 50, 500, 1, 7, 1, '{}'),
  (42, 100, 1000, 1, 7, 1, '{}'),
  (43, 200, 2000, 1, 7, 1, '{}'),
  (44, 500, 5000, 1, 7, 1, '{}'),
  (45, 1000, 10000, 1, 7, 1, '{}'),
  (46, 10000, 100000, 1, 7, 1, '{}'),
  (47, 50000, 500000, 1, 7, 1, '{}'),
  (48, 180, 2023, 1, 5, 1, '{}'),
  (49, 180, 2023, 1, 5, 1, '{}'),
  (50, 10, 100, 1, 9, 1, '{}'),
  (51, 25, 250, 1, 9, 1, '{}'),
  (52, 50, 500, 1, 9, 1, '{}'),
  (53, 100, 1000, 1, 9, 1, '{}'),
  (54, 200, 2000, 1, 9, 1, '{}'),
  (55, 500, 5000, 1, 9, 1, '{}'),
  -- iOS 首充福利
  (56, 6, 66, 1, 2, 4, '{"corner_mark": "首充加赠 10%", "original_num": 60}'),
  -- Android 首充福利
  (57, 6, 66, 1, 1, 4, '{"corner_mark": "首充加赠 10%", "original_num": 60}');

CREATE TABLE IF NOT EXISTS `recharge_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `uid` bigint NOT NULL COMMENT '用户 ID',
  `tid` varchar(32) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '各平台订单号',
  `ctime` int NOT NULL COMMENT '创建时间',
  `cid` int NOT NULL COMMENT '当 type 为 11 时为商品 ID（当前为剧集 ID）；其它为 ccy 表 ID',
  `price` decimal(10,2) NOT NULL COMMENT '购买金额',
  `num` int NOT NULL COMMENT '币种数量',
  `ccy` tinyint NOT NULL COMMENT '币种\n1 钻石',
  `status` tinyint NOT NULL COMMENT '订单状态\n1 成功\n0 创建\n-1 取消（已退款）\n-2 错误',
  `type` tinyint DEFAULT NULL COMMENT '充值平台\n0 苹果充值\n1 支付宝\n2 微信充值\n3 现金\n4 QQ 钱包\n5 天猫（iOS）\n6 天猫（安卓）\n7 PayPal\n8 Google Pay\n9 iOS 补单\n10 公对公\n11 抖店',
  `origin` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '设备来源\n0 Web\n1 手机网页\n2 App\n3 天猫',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modified_time` bigint NOT NULL DEFAULT '0' COMMENT '最后修改时间',
  `confirm_time` bigint NOT NULL DEFAULT '0' COMMENT '交易确认时间，退款、取消订单不更新此字段',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='充值订单';

CREATE TABLE IF NOT EXISTS `recharge_order_detail` (
  `id` bigint NOT NULL COMMENT 'recharge_order.id',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `ip` varchar(50) NOT NULL COMMENT 'IP 地址',
  `os` tinyint NOT NULL COMMENT '设备类型: 1 为 Android; 2 为 iOS；3 为 Web',
  `user_agent` varchar(500) NOT NULL DEFAULT '' COMMENT 'User-Agent',
  `equip_id` char(36) NOT NULL DEFAULT '' COMMENT '设备号',
  `buvid` varchar(64) NOT NULL DEFAULT '' COMMENT '唯一设备标识',
  `real_price` bigint NOT NULL COMMENT '实际支付金额（单位：分）',
  `more` json DEFAULT NULL COMMENT '更多详情',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='充值订单明细';

CREATE TABLE IF NOT EXISTS `balance` (
  `id` bigint NOT NULL COMMENT '主键，用户 ID',
  `ios` int unsigned DEFAULT '0' COMMENT 'iOS 充值余额',
  `android` int unsigned DEFAULT '0' COMMENT 'Android 充值余额',
  `paypal` int unsigned DEFAULT '0' COMMENT 'PayPal 余额',
  `tmallios` int NOT NULL DEFAULT '0' COMMENT '天猫 iOS 充值余额（单位：钻）',
  `googlepay` int NOT NULL DEFAULT '0' COMMENT 'Google Pay 充值余额（单位：钻）',
  `in_ios` int unsigned DEFAULT '0' COMMENT 'iOS 收益余额',
  `in_android` int unsigned DEFAULT '0' COMMENT 'Android 收益余额',
  `in_paypal` int unsigned DEFAULT '0' COMMENT 'PayPal 收入',
  `in_tmallios` int NOT NULL DEFAULT '0' COMMENT '天猫 iOS 收益余额（单位：钻）',
  `in_googlepay` int NOT NULL DEFAULT '0' COMMENT 'Google Pay 收益余额（单位：钻）',
  `new_all_live_profit` int unsigned NOT NULL DEFAULT '0' COMMENT '直播间散人总收益（2020 年 6 月 1 日后）单位 ：分',
  `new_live_profit` int unsigned NOT NULL DEFAULT '0' COMMENT '直播间散人可提现收益（2020 年 6 月 1 日后）单位 ：分',
  `all_live_profit` int unsigned NOT NULL DEFAULT '0' COMMENT '直播间散人总收益（2020 年 6 月 1 日前）单位 ：分',
  `live_profit` int unsigned NOT NULL DEFAULT '0' COMMENT '直播间散人可提现收益（2020 年 6 月 1 日前）单位 ：分',
  `profit` double unsigned DEFAULT '0' COMMENT '总收益余额（可提现）',
  `drama_reward_profit` int unsigned DEFAULT '0' COMMENT '剧集打赏总收益余 \n额',
  `drama_buy_profit` int unsigned DEFAULT '0' COMMENT '剧集购买总收益余额',
  `other_profit` int unsigned DEFAULT '0' COMMENT '其他总收益余额',
  `all_drama_buy_profit` int DEFAULT '0' COMMENT '剧集累计购买收益',
  `all_drama_reward_profit` int unsigned DEFAULT '0' COMMENT '剧集累计打赏收益',
  `all_other_profit` int unsigned DEFAULT '0' COMMENT '其他累计收益',
  `all_consumption` bigint DEFAULT '0' COMMENT '用户总消费（单位：钻），不包含贵族钻石消费',
  `all_topup` bigint NOT NULL DEFAULT '0' COMMENT '总充值额（单位：普通钻石）',
  `all_coin` int NOT NULL DEFAULT '0' COMMENT '普通钻石余额（单位：钻）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO balance
  (id)
  VALUES
  -- testGetCreatorTradeInfo
  (0)
  -- testGenerateTransaction
  ,(1)
  -- testBuyGift2、testBuyGift
  ,(13)
  ,(14)
  ,(50)
  ,(51)
  ,(111)
  ,(980)
  ,(3013620)
  ,(666)
  ,(999)
  -- testShowNewUserTopup
  ,(988)
  -- testFreeze、testBuyGift
  ,(346286)
  -- testBuyWishGoods
  ,(401)
  -- testBuy
  ,(333)
  ,(444)
  -- testBuy
  ,(556)
  -- testActionRequestTopupOrderRefund
  ,(677)
  -- testActionRequestConsumeOrderRefund
  ,(678)
  -- testActionDeductCreatorBalance
  ,(679)
;

CREATE TABLE IF NOT EXISTS `pay_bad_debt` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NOT NULL COMMENT '用户 ID',
  `order_id` bigint NOT NULL DEFAULT '0' COMMENT '订单 ID',
  `debt` int NOT NULL COMMENT '坏账值（单位为普通钻）',
  `type` tinyint NOT NULL DEFAULT '1' COMMENT '坏账类型（1: iOS 退款）',
  `reason` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '原因',
  `create_time` bigint NOT NULL COMMENT '创建时间',
  `modified_time` bigint NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='坏账表';

CREATE TABLE IF NOT EXISTS `pay_account` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint DEFAULT NULL COMMENT '用户 ID',
  `tid` int unsigned DEFAULT NULL COMMENT 'transaction_log 表 ID',
  `account_amount` int unsigned DEFAULT NULL COMMENT '账户金额（钻石，0.1 元）',
  `balance` int unsigned DEFAULT NULL COMMENT '余额（钻石 0.1 元）',
  `scope` int DEFAULT NULL COMMENT '适用范围（按位计算）：\n0 为普通钻石；\n第 1 位为直播间（含公会主播）',
  `type` int DEFAULT NULL COMMENT '账户类型，其中个位数用来判断费率：\n5 为抖店费率，\n4 为 Google Pay 费率，\n3 为 iOS 费率，\n2 为 paypal 费率，\n1 为天猫 iOS 费率，\n0 为安卓通用费率；\n\n普通钻石 0X，\n贵族普通钻石 1X，\n贵族免费钻石 2X',
  `withhold_order` int DEFAULT NULL COMMENT '扣款顺序，按从大到小',
  `expire_time` bigint DEFAULT NULL COMMENT '过期时间（秒级时间戳）该秒尚在有效期。判断只能使用大于',
  `create_time` bigint DEFAULT NULL COMMENT '创建时间（秒级时间戳）',
  `modified_time` bigint DEFAULT NULL COMMENT '修改时间（秒级时间戳）',
  `status` int NOT NULL DEFAULT '1' COMMENT 'pay_account 状态：\n1 为可用。\n-3 为退钱。\n-4 为退钻。\n-5 为代充取消。\n-6 为未成年退款。',
  `more` json DEFAULT NULL COMMENT '更多详情',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `pay_account`
  (`id`, `user_id`, `tid`, `account_amount`, `balance`, `scope`, `type`, `withhold_order`, `expire_time`, `create_time`, `modified_time`, `status`)
  VALUES
  -- testGetExpireBalanceByTime
  (1, 7188032, *********, 30000, 100, 1, 10, 10, UNIX_TIMESTAMP() - 91*86400, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1)
  ,(2, 3457181, *********, 30000, 100, 1, 10, 10, UNIX_TIMESTAMP() - 91*86400, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1)
;

-- GRANT SELECT, INSERT, UPDATE ON `transaction_log` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `transaction_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `from_id` bigint NOT NULL,
  `to_id` bigint NOT NULL,
  `c_time` int NOT NULL,
  `gift_id` int NOT NULL COMMENT '0 为知识问答 正整数为正常礼物',
  `title` varchar(255) DEFAULT NULL,
  `ios_coin` int DEFAULT '0',
  `android_coin` int DEFAULT '0',
  `paypal_coin` int DEFAULT '0',
  `tmallios_coin` int NOT NULL DEFAULT '0' COMMENT '天猫 iOS 收入（单元：钻）',
  `googlepay_coin` int NOT NULL DEFAULT '0' COMMENT 'Google Pay 收入（单元：钻）',
  `all_coin` int NOT NULL DEFAULT '0' COMMENT '钻石总和（花费的普通钻石与贵族钻石的总和）',
  `revenue` double NOT NULL DEFAULT '0' COMMENT '分成后收益',
  `income` double DEFAULT NULL,
  `tax` double DEFAULT NULL,
  `rate` double DEFAULT NULL,
  `num` int NOT NULL DEFAULT '1' COMMENT '直播时购买礼物数量；购买语音包时存储季度',
  `status` tinyint DEFAULT '0' COMMENT '-5 代充取消交易记录\n-4 已退款（钻石）\n-3 已退款（现金）\n-2 是直播问答取消\n-1 未完成（目前仅用作“直播问答提问中”）\n1 交易成功',
  `type` tinyint NOT NULL DEFAULT '1' COMMENT '1：直播间礼物；2：剧集单集购买；3：剧集购买；4：微信男友购买；5：全职抽卡；6：全职季包；7：剧集打赏；8: 求签；9：公会直播收益；',
  `suborders_num` int unsigned NOT NULL DEFAULT '1' COMMENT '购买剧集单集时存储子订单数量（本次购买的单集数）；购买语音包时存储作品 ID',
  `attr` int unsigned NOT NULL DEFAULT '0' COMMENT '12 位及其之前为具体的商品属性：type 为 1 或 9 时，attr 为 1 表示直播续费贵族，为 2 表示直播开通贵族，为 3 表示直播间白给礼物，为 4 表示幸运签开箱礼物，为 5 表示开通超粉，为 6 表示续费超粉，为 7 表示为购买福袋，为 8 表示购买超能魔盒，为 9 表示超能魔盒礼物。type 为 2 或 3 时，attr 1 表示特殊途径购买，2 表示剧集兑换，3 为购自抖店；type 为 4 时，attr 0 表示微信男友，1 表示掌心男友；13 位及其之后按位处理',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modified_time` bigint NOT NULL DEFAULT '0' COMMENT '最后修改时间',
  `confirm_time` bigint NOT NULL DEFAULT '0' COMMENT '交易确认时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `transaction_log`
  (`id`, `from_id`, `to_id`, `c_time`, `gift_id`, `title`, `ios_coin`, `android_coin`, `paypal_coin`, `tmallios_coin`, `googlepay_coin`, `all_coin`, `revenue`, `income`, `tax`, `rate`, `num`, `status`, `type`, `suborders_num`, `attr`, `create_time`, `modified_time`, `confirm_time`)
  VALUES
  -- testSingleSound
  (1, 1, 1, UNIX_TIMESTAMP(), 2, '断弦', 1, 0, 0, 0, 0, 1, 0, 0.1, 0.04, 0, 1, 1, 3, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
;

CREATE TABLE IF NOT EXISTS `transaction_log_detail` (
  `id` bigint NOT NULL COMMENT 'transaction_log.id',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `ip` varchar(50) NOT NULL COMMENT 'IP 地址',
  `os` tinyint NOT NULL COMMENT '设备类型: 1 为 Android; 2 为 iOS；3 为 Web',
  `user_agent` varchar(500) NOT NULL DEFAULT '' COMMENT 'User-Agent',
  `equip_id` char(36) NOT NULL DEFAULT '' COMMENT '设备号',
  `buvid` varchar(64) NOT NULL DEFAULT '' COMMENT '唯一设备标识',
  `more` text DEFAULT NULL COMMENT '更多详情',
  `user_hide_time` bigint DEFAULT 0 COMMENT '已购剧集隐藏时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE IF NOT EXISTS `transaction_sound_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `sound_id` int unsigned NOT NULL COMMENT '单音 ID',
  `user_id` bigint NOT NULL COMMENT '用户 ID',
  `drama_id` int unsigned NOT NULL COMMENT '剧集 ID',
  `tid` int NOT NULL COMMENT 'transaction_log 关联表 ID',
  `status` int NOT NULL DEFAULT '0' COMMENT '订单状态（同 transaction_log.status）',
  `attr` int unsigned DEFAULT '0' COMMENT '订单属性（同 transaction_log.attr）',
  `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间',
  `modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='音频购买详情表';

INSERT INTO transaction_sound_log
  (id, sound_id, user_id, drama_id, tid, status, attr, create_time, modified_time)
VALUES
  -- testSingleSound
  (1, 3, 2, 3, 1, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- GRANT SELECT ON missevan_pay.drama_reward_menu TO 'missevan_app'@'%';
CREATE TABLE IF NOT EXISTS `drama_reward_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
  `create_time` bigint NOT NULL COMMENT '创建时间',
  `modified_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间',
  `name` varchar(30) NOT NULL DEFAULT '' COMMENT '价目名称',
  `price` int NOT NULL COMMENT '钻石数',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `drama_reward_menu`
  (`id`, `create_time`, `name`, `price`)
  VALUES
  (1, UNIX_TIMESTAMP(), '50 钻', 50),
  (2, UNIX_TIMESTAMP(), '100 钻', 100),
  (3, UNIX_TIMESTAMP(), '500 钻', 500),
  (4, UNIX_TIMESTAMP(), '1000 钻', 1000),
  (5, UNIX_TIMESTAMP(), '自定义', 0);

-- GRANT SELECT, INSERT, UPDATE ON `missevan_pay`.`guild_balance` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `guild_balance` (
  `id`               int unsigned    NOT NULL AUTO_INCREMENT COMMENT '主键，公会 ID'
  ,`in_ios`          int unsigned    NOT NULL DEFAULT '0' COMMENT 'iOS 收益余额'
  ,`in_android`      int unsigned    NOT NULL DEFAULT '0' COMMENT 'Android 收益余额'
  ,`in_paypal`       int unsigned    NOT NULL DEFAULT '0' COMMENT 'PayPal 收益余额'
  ,`in_googlepay`    int             NOT NULL DEFAULT '0' COMMENT 'Google Pay 收益余额（单位：钻）'
  ,`in_tmallios`     int             NOT NULL DEFAULT '0' COMMENT '天猫 iOS 收益余额（单位：钻）'
  ,`live_profit`     double unsigned NOT NULL DEFAULT '0' COMMENT '直播总收益余额（可提现）'
  ,`all_live_profit` double unsigned NOT NULL DEFAULT '0' COMMENT '直播累计收益'
  ,`rate`            double unsigned NOT NULL DEFAULT '0.5' COMMENT '公会分成比例'
  ,`create_time`     int unsigned    NOT NULL COMMENT '创建时间'
  ,`modified_time`   int unsigned    NOT NULL COMMENT '修改时间'
  ,PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO `guild_balance`
  (`id`, `in_ios`, `in_android`, `in_paypal`, `in_googlepay`, `in_tmallios`, `live_profit`, `all_live_profit`, `rate`, `create_time`, `modified_time`)
  VALUES
  (3, 0, 0, 0, 0, 0, 0, 0, 0.6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
  ,(1000007, 36, 36, 36, 0, 0, 0, 0, 0.6, UNIX_TIMESTAMP(), UNIX_TIMESTAMP())
;

-- GRANT SELECT, INSERT, UPDATE ON `missevan_pay`.`pay_account_purchase_detail` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `pay_account_purchase_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键'
  ,`account_id` bigint COMMENT 'pay_account 表 ID'
  ,`tid` bigint COMMENT 'transaction_log 表 ID'
  ,`purchase_amount` bigint COMMENT '销售金额（钻石，价值 0.1 元）'
  ,`fee_rate` double COMMENT '交易时的费率'
  ,`status` int COMMENT '单笔交易类型 -1. 交易已取消 0. 交易尚未完成，资金暂时冻结 1. 已经和收款方结算'
  ,`create_time` bigint COMMENT '创建时间（秒级时间戳）'
  ,`modified_time` bigint COMMENT '修改时间（秒级时间戳）'
  ,PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- GRANT SELECT, INSERT, UPDATE ON `missevan_pay`.`transaction_items_log` TO 'missevan_app'@'%'
CREATE TABLE IF NOT EXISTS `transaction_items_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键'
  ,`create_time` bigint NOT NULL COMMENT '创建时间'
  ,`modified_time` bigint NOT NULL COMMENT '修改时间'
  ,`goods_id` bigint NOT NULL COMMENT '商品 ID'
  ,`goods_title` varchar(255) COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品标题'
  ,`goods_price` bigint NOT NULL COMMENT '商品价格（单位：钻石）'
  ,`goods_num` int NOT NULL DEFAULT '1' COMMENT '商品数量'
  ,`tid` bigint NOT NULL COMMENT 'transaction_log 主键'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`status` tinyint NOT NULL COMMENT '订单状态，同 transaction_log.status'
  ,`type` tinyint NOT NULL COMMENT '订单类型，同 transaction_log.type'
  ,`more` text COLLATE utf8mb4_general_ci COMMENT '更多详情'
  ,PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

-- GRANT INSERT, SELECT, UPDATE ON missevan_pay.balance_settlement_change_log TO 'missevan_app'@'%';
CREATE TABLE IF NOT EXISTS `balance_settlement_change_log`  (
  id BIGINT AUTO_INCREMENT COMMENT '主键'
  ,create_time BIGINT NOT NULL COMMENT '创建时间'
  ,modified_time BIGINT NOT NULL COMMENT '修改时间'
  ,type TINYINT NOT NULL COMMENT '类型：1 充值订单退款，2 消费订单退款，3 素人主播扣减收益余额'
  ,status TINYINT NOT NULL COMMENT '状态：-1 失败，0 创建（处理中），1 成功'
  ,user_id BIGINT NOT NULL COMMENT '用户 ID'
  ,detail JSON COMMENT '详情'
  ,note VARCHAR(255) NOT NULL COMMENT '备注'
  ,PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- GRANT SELECT, INSERT, UPDATE ON missevan_pay.vip_subscription_sign_agreement TO 'missevan_app'@'%';
CREATE TABLE IF NOT EXISTS `vip_subscription_sign_agreement` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键'
  ,`create_time` bigint NOT NULL COMMENT '创建时间（秒级时间戳）'
  ,`modified_time` bigint NOT NULL COMMENT '修改时间（秒级时间戳）'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`pay_type` tinyint NOT NULL COMMENT '付费方式：1 iOS、2 微信、3 支付宝、4 Google Play'
  ,`vip_id` bigint NOT NULL COMMENT '会员套餐价目 ID'
  ,`status` bigint NOT NULL COMMENT '协议签约状态：1 待签约，2 签约生效中，3 签约到期'
  ,`start_time` bigint NOT NULL COMMENT '协议生效时间（秒级时间戳）'
  ,`expire_time` bigint NOT NULL COMMENT '协议失效时间（秒级时间戳），含此时刻，待签约和签约生效中时为 0'
  ,`agreement_no` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '协议编号'
  ,`more` JSON COMMENT '更多详情，e.g. { "alipay_agreement_no": "0123456789", "alipay_open_id": "0123456789" }'
  ,PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户订阅会员周期扣款签约协议';

-- GRANT SELECT, INSERT, UPDATE ON missevan_pay.vip_fee_deducted_record TO `missevan_app`@`%`;
CREATE TABLE IF NOT EXISTS `vip_fee_deducted_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键'
  ,`create_time` bigint NOT NULL COMMENT '创建时间（秒级时间戳）'
  ,`modified_time` bigint NOT NULL COMMENT '修改时间（秒级时间戳）'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`vip_id` bigint NOT NULL COMMENT '会员价目 ID'
  ,`sign_agreement_id` bigint NOT NULL COMMENT '签约协议 ID（0 为单次付费）'
  ,`pay_type` tinyint NOT NULL COMMENT '付费方式：1 iOS、2 微信、3 支付宝、4 Google Play'
  ,`price` bigint NOT NULL COMMENT '本次扣款金额（单位：分）'
  ,`status` bigint NOT NULL COMMENT '本次扣款状态：1 待扣款，2 扣款成功，3 扣款失败'
  ,`next_deduct_time` bigint NOT NULL COMMENT '下次扣款时间（秒级时间戳，0 为单次付费）'
  ,`transaction_id` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '交易票据 ID'
  ,`tax` bigint NOT NULL DEFAULT 0 COMMENT '渠道费（单位：分）'
  ,`more` JSON COMMENT '更多详情，e.g. { "is_first_topup_discount": true }'
  ,`confirm_time` bigint DEFAULT NULL COMMENT '交易确认时间（秒级时间戳）'
  ,PRIMARY KEY (`id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='会员扣费记录';

-- GRANT SELECT, UPDATE ON missevan_pay.withdrawal_record TO 'missevan_app'@'%';
CREATE TABLE IF NOT EXISTS `withdrawal_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户 ID',
  `account_id` bigint NOT NULL COMMENT '账号 ID',
  `profit` double NOT NULL COMMENT '兑换金额',
  `create_time` bigint NOT NULL COMMENT '创建时间戳，单位：秒',
  `modified_time` bigint NOT NULL DEFAULT '0' COMMENT '更新时间戳，单位：秒',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '申请状态 1 为申请中，2 为确认打款，3 为拒绝打款',
  `type` tinyint NOT NULL DEFAULT '1' COMMENT '提现的收益类型：1：其他收益 ( 微信男友 + 全职语音包 )；2：旧直播间收益（2020 年 6 月 1 日前）；3：剧集购买收益；4：剧集打赏收益；5：新直播间收益（2020 年 6 月 1 日后）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- GRANT SELECT ON missevan_pay.account_info TO 'missevan_app'@'%';
CREATE TABLE IF NOT EXISTS `account_info` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户 ID',
  `real_name` varchar(20) NOT NULL COMMENT '真实姓名',
  `account` varchar(255) DEFAULT NULL COMMENT '提现账号',
  `mobile` varchar(255) NOT NULL COMMENT '手机号',
  `id_number` varchar(44) NOT NULL COMMENT '身份证号',
  `bank` varchar(255) NOT NULL COMMENT '开户银行',
  `bank_branch` varchar(255) NOT NULL COMMENT '支行信息',
  `bank_account` varchar(44) NOT NULL COMMENT '银行账号',
  `create_time` bigint NOT NULL COMMENT '创建时间',
  `modified_time` bigint DEFAULT NULL COMMENT '修改时间',
  `type` tinyint NOT NULL DEFAULT '0' COMMENT '0 为支付宝账户, 1 银行卡账户',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '旧版本定义（-1 历史保存 0 保存 1 已确认）：2019-10-17 16:17:00 线上代码版本定义（-1 为失效账号）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- GRANT SELECT, INSERT, UPDATE ON `m_appearance_fee_deducted_record` TO 'missevan_app'@'%';
CREATE TABLE IF NOT EXISTS `m_appearance_fee_deducted_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键'
  ,`create_time` bigint NOT NULL COMMENT '创建时间（秒级时间戳）'
  ,`modified_time` bigint NOT NULL COMMENT '修改时间（秒级时间戳）'
  ,`user_id` bigint NOT NULL COMMENT '用户 ID'
  ,`appearance_id` bigint NOT NULL COMMENT '外观套装 ID'
  ,`appearance_name` varchar(50) NOT NULL COMMENT '外观套装名称'
  ,`num` int NOT NULL DEFAULT '1' COMMENT '购买的月份数量'
  ,`pay_type` tinyint NOT NULL COMMENT '付费方式 1：钻石；2：微信；3：支付宝'
  ,`price` bigint NOT NULL COMMENT '本次扣款金额（单位：分）'
  ,`status` bigint NOT NULL COMMENT '本次扣款状态：1 待扣款，2 扣款成功，3 扣款失败，4 重复支付，5 已退款（现金）'
  ,`transaction_id` varchar(50) DEFAULT '' COMMENT '交易票据 ID（微信或支付宝交易时记录）'
  ,`tax` bigint DEFAULT '0' COMMENT '渠道费（单位：分）'
  ,`more` json DEFAULT NULL COMMENT '更多详情'
  ,PRIMARY KEY (`id`)
  -- ,KEY `idx_modifiedtime` (`modified_time`),
  -- ,KEY `idx_userid_appearanceid` (`user_id`, `appearance_id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COMMENT='外观套装交易记录';
