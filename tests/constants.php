<?php

// 项目所在路径
define('ROOT', dirname(__DIR__));
// 读写分离延时（秒）
define('MASTER_SLAVE_DELAY_TIME', 3);
// 配置路径
defined('CONFIG_PATH') or define('CONFIG_PATH', __DIR__ . '/config');

// ********** 基础配置 **********
$files = glob(ROOT . '/constants/*.php');
foreach ($files as $file) {
    $file = basename($file);
    if (strpos($file, '_bac') !== false
            || $file === 'env.php' || $file === 'key.php' || $file === 'index.php') {
        continue;
    }
    require(ROOT . '/constants/' . $file);
}

// ********** 自定义配置 **********
// 环境设置
defined('YII_DEBUG') or define('YII_DEBUG', true);
defined('YII_ENV') or define('YII_ENV', 'test');

// 滑动验证开关设置
defined('ENABLE_NOCAPTCHA') or define('ENABLE_NOCAPTCHA', false);

// 是否开启手机号绑定与否检查
defined('ENABLE_MOBILE_CHECK') or define('ENABLE_MOBILE_CHECK', true);

// 是否走 IAP 代理访问苹果 IAP 服务器
defined('ENABLE_IAP_GATEWAY') or define('ENABLE_IAP_GATEWAY', false);

// 是否开启 B 站风控检查
defined('ENABLE_BILIBILI_RISK_CHECK') or define('ENABLE_BILIBILI_RISK_CHECK', true);

// 开启以每分钟进行播放量持久化
defined('ENABLE_ADD_PLAY_TIMES_PER_MINUTE') or define('ENABLE_ADD_PLAY_TIMES_PER_MINUTE', true);

// 是否开启压力测试
defined('ENABLE_LOAD_TEST') or define('ENABLE_LOAD_TEST', true);

// M站内网签名私钥
define('MISSEVAN_PRIVATE_KEY', 'testkey');

define('FFMPEG', '');

// 使用 HD 交换密钥的 P、G 值
define('REQUEST_SIGN_DH_P', '1741150997');
define('REQUEST_SIGN_DH_G', '695065453');

// API 签名密钥
define('APP_API_SIGN_KEY', 'testkey');

// 天猫充值相关
define('TMALL_APPKEY', 'test');
define('TMALL_APPSECRET', 'test');
define('TMALL_COOPID', 'test');

// QQ 钱包支付相关
define('QQPAY_APP_ID', '');  // 应用 ID
define('QQPAY_MCH_ID', '');  // 商户号
define('QQPAY_KEY', '');  // 密钥
define('QQPAY_CALLBACK', 'http://127.0.0.1:8017/callback/qqpay');  // 支付回调
define('QQPAY_APP_SIGN', '');  // 应用签名

// 微信支付相关
define('WECHATPAY_APP_ID', 'test');  // 应用 ID
define('WECHATPAY_MCH_ID', 'test');  // 商户号
define('WECHATPAY_APP_SIGN', 'test');  // 应用签名
define('WECHATPAY_KEY', 'test');
// 支付回调地址
define('WECHATPAY_CALLBACK', 'http://127.0.0.1:8017/callback/wechatpay');
// 会员签约结果回调接口地址
define('WECHATPAY_SIGN_VIP_CALLBACK', 'http://127.0.0.1:8017/callback/wechatpay-sign');

// 猫耳概念版微信支付配置
define('WECHATPAY_APP_ID_CONCEPT_CHANNEL', 'test');
define('WECHATPAY_APP_SIGN_CONCEPT_CHANNEL', 'test');

// PayPal 支付相关
define('PAYPAL_ACCOUNT', '');
define('PAYPAL_CALLBACK', 'http://127.0.0.1:8017/callback/paypal');
define('PAYPAL_RETURN_URL', 'http://127.0.0.1:8017/callback/paypalreturn');
define('PAYPAL_CLIENT_ID', '');
define('PAYPAL_CLIENT_SECRET', '');
// TODO: 单位美元转人民币汇率调整成取实时汇率
define('PAYPAL_USD_TO_CNY_RATE', 6.00);

define('BILIBILI_LARGE_PAY_CUSTOMER_ID', 123456);
define('BILIBILI_LARGE_PAY_TOKEN', 'xxxxxx');
define('BILIBILI_LARGE_PAY_NOTIFY_URL', 'http://127.0.0.1:8017/callback/bili-large-pay');

// QQ 或微博授权登录相关
define('QQ_AUTH_ANDROID', '**********');
define('QQ_AUTH_IOS', '**********');
define('WEIBO_AUTH_ANDROID', '*********');
define('WEIBO_AUTH_IOS', '**********');

// 应用包名
define('APP_PACKAGE_NAMES', [
    'missevan_ios' => 'com.missevan.CatEarFM',
    'missevan_android' => 'cn.missevan',
    'missevan_concept' => 'com.missevan.conceptapp',
    'mimi' => 'jp.mimifm.app',
]);

// 内购商品标识
define('IAP_PRODUCT_IDS', [
    'missevan_ios' => 'com.missevan.CatEarFM',  // Apple Store
    'missevan_android' => 'cn.missevan.item',  // Google Pay
    'missevan_concept' => '',
    'mimi' => 'jp.mimifm.app.item',
]);

// 单位货币可兑换的钻石
define('DIAMOND_EXRATE', 10);

// Google Pay 认证密钥配置
define('GOOGLE_PAY_AUTH_CONFIG', [
    'type' => 'service_account',
    'project_id' => 'test',
    'private_key_id' => 'test',
    'private_key' => 'test',
    'client_email' => 'test',
    'client_id' => 'test',
    'auth_uri' => 'https://accounts.google.com/o/oauth2/auth',
    'token_uri' => 'https://oauth2.googleapis.com/token',
    'auth_provider_x509_cert_url' => 'https://www.googleapis.com/oauth2/v1/certs',
    'client_x509_cert_url' => 'https://www.googleapis.com/robot/v1/metadata/x509/xxxx',
]);

define('SENSITIVE_INFORMATION_KEY', 'openssl_aes_256_cbc_testpassword');
// 加密 IV 初始化向量值，在需要数据库进行查询的地方使用到这个固定常量
define('SENSITIVE_FIXED_IV_KEY', 'testiv');

// 下发视频云签名 CDN 开关，true: 开启；false: 关闭；默认开启
defined('ENABLE_SOUND_BVC_CDN') or define('ENABLE_SOUND_BVC_CDN', true);
// 触发音频下发视频云签名 CDN 地址概率，取值范围 0 ~ 100，即 0% ~ 100%
defined('SOUND_BVC_CDN_TEST_RATIO') or define('SOUND_BVC_CDN_TEST_RATIO', 20);

// 用户智齿客服 partnerId 加密参数
define('USER_SOBOT_PARTNERID_SECRET_KEY', 'test');

// device token 签名密钥
define('DEVICE_TOKEN_KEY', 'testkey');

// 音频同时播放限制开关，true: 开启；false: 关闭；默认开启
defined('ENABLE_EQUIP_PLAY_LIMIT') or define('ENABLE_EQUIP_PLAY_LIMIT', true);

// 启用 tab bar 直播按钮的概率（未启用时为"发现"按钮），取值范围 0 ~ 100，即 0% ~ 100%
// 需求文档：https://info.missevan.com/pages/viewpage.action?pageId=118093283
defined('ENABLE_TAB_BAR_LIVE_RATIO') or define('ENABLE_TAB_BAR_LIVE_RATIO', 20);

// 第三方到端回调 API 签名密钥
define('APP_THIRD_PARTY_TASK_API_SIGN_KEY', [
    'baidu' => 'baidu_test_key',
    'ctrip' => 'ctrip_test_key',
    'dianping' => 'dianping_test_key',
]);
