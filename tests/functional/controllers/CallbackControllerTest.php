<?php

namespace tests\controllers;

use app\components\auth\AuthJingDong;
use app\components\auth\AuthJingDongV2;
use app\components\auth\AuthWechat;
use app\components\auth\douyin\AuthDouDian;
use app\components\auth\douyin\DouDianException;
use app\components\auth\douyin\DouDianGoods;
use app\components\auth\douyin\DouDianOrder;
use app\components\auth\douyin\DouDianPushMessage;
use app\components\auth\wechat\WechatOffiaccount;
use app\components\service\bililargepay\BiliLargePayClient;
use app\components\util\MUtils;
use app\controllers\CallbackController;
use app\forms\TmallPayForm;
use app\forms\UserContext;
use app\models\AdTrack;
use app\models\Balance;
use app\models\MWechatOffiaccountReply;
use app\models\TopupMenu;
use app\models\Drama;
use app\models\InstallLog;
use app\models\IosReceipt;
use app\models\MMessageAssign;
use app\models\MThirdPartyTask;
use app\models\RechargeOrder;
use app\models\VipFeeDeductedRecord;
use app\models\VipSubscriptionSignAgreement;
use app\models\ThirdPartyTaskUtil;
use Exception;
use missevan\util\MUtils as MUtils2;
use tests\components\UnitTestCase;
use tests\components\util\Data;
use tests\components\util\Equipment;
use tests\components\util\SSOClient;
use tests\components\util\Tools;
use tests\components\web\Request;
use tests\unit\components\util\EquipmentTest;
use Yii;
use yii\helpers\Json;
use yii\web\HttpException;

class CallbackControllerTest extends UnitTestCase
{
    const TEST_USER_ID_IOS_IOP_REFUND = 2993;
    const TEST_TRANSACTION_ID = '**************';
    const APPSFLYER_TRACK_ID = '*************-5309999';
    const JINGDONG_TRANSACTION_ID = '100005';
    const JINGDONG_V2_TRANSACTION_ID = '100008';
    const BILI_LARGE_PAY_TRANSACTION_ID = '3550613824602600000';

    private static $vip_subscription_sign_agreement;
    private static $vip_fee_deducted_record;

    private static $origin_db;
    private static $origin_paydb;
    private static $origin_growthdb;

    private static function setOriginDbs(): void
    {
        self::$origin_db = Yii::$app->db;
        self::$origin_paydb = Yii::$app->paydb;
        self::$origin_growthdb = Yii::$app->growthdb;
        Yii::$app->set('db', Yii::$app->sqlitedb);
        Yii::$app->set('paydb', Yii::$app->sqlite_paydb);
        Yii::$app->set('growthdb', Yii::$app->sqlite_growthdb);
    }

    private static function resetOriginDbs(): void
    {
        Yii::$app->set('db', self::$origin_db);
        Yii::$app->set('paydb', self::$origin_paydb);
        Yii::$app->set('growthdb', self::$origin_growthdb);
    }

    protected function _before()
    {
        parent::_before();
        self::setOriginDbs();
    }

    protected function _after()
    {
        parent::_after();
        self::resetOriginDbs();
    }

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        self::setOriginDbs();
        self::createTestData();
    }

    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
        self::setOriginDbs();
        self::cleanData();
        self::resetOriginDbs();
    }

    private static function cleanData()
    {
        Yii::$app->request->setRawBody(null);
        IosReceipt::deleteAll(['user_id' => self::TEST_USER_ID_IOS_IOP_REFUND, 'transaction_id' => self::TEST_TRANSACTION_ID]);
        RechargeOrder::deleteAll(['uid' => self::TEST_USER_ID_IOS_IOP_REFUND, 'tid' => self::TEST_TRANSACTION_ID]);
        AdTrack::deleteAll(['track_id' => self::APPSFLYER_TRACK_ID]);
        RechargeOrder::deleteAll(['tid' => [self::JINGDONG_TRANSACTION_ID, self::JINGDONG_V2_TRANSACTION_ID, self::BILI_LARGE_PAY_TRANSACTION_ID]]);

        if (self::$vip_subscription_sign_agreement) {
            VipSubscriptionSignAgreement::deleteAll(['id' => self::$vip_subscription_sign_agreement->id]);
        }
        if (self::$vip_fee_deducted_record) {
            VipFeeDeductedRecord::deleteAll(['id' => self::$vip_fee_deducted_record->id]);
        }
    }

    private static function createTestData()
    {
        $receipt = new IosReceipt([
            'user_id' => self::TEST_USER_ID_IOS_IOP_REFUND,
            'product_id' => 'com.missevan.CatEarFM0003',
            'transaction_id' => self::TEST_TRANSACTION_ID,
            'status' => IosReceipt::STATUS_SUCCESS,
        ]);
        if (!$receipt->save()) {
            throw new Exception(MUtils::getFirstError($receipt));
        }
        $order = new RechargeOrder([
            'uid' => self::TEST_USER_ID_IOS_IOP_REFUND,
            'tid' => self::TEST_TRANSACTION_ID,
            'cid' => 3,
            'price' => 50,
            'ccy' => TopupMenu::DIAMOND,
            'num' => 500,
            'status' => RechargeOrder::STATUS_SUCCESS,
            'type' => RechargeOrder::TYPE_APPLEPAY,
            'origin' => RechargeOrder::ORIGIN_APP,
        ]);
        if (!$order->save()) {
            throw new Exception(MUtils::getFirstError($order));
        }
        $buyer = Balance::getByPk($order->uid);
        $buyer->updateAttributes([
            'ios' => 600,
            'all_coin' => 600,
            'all_topup' => 600,
        ]);
        Yii::$app->request->setRawBody(null);

        self::$vip_subscription_sign_agreement = Data::createVipSubscriptionSignAgreement([
            'user_id' => self::TEST_USER_ID,
            'pay_type' => VipSubscriptionSignAgreement::PAY_TYPE_WECHAT,
            'vip_id' => 6,
            'status' => VipSubscriptionSignAgreement::STATUS_PENDING,
            'start_time' => $_SERVER['REQUEST_TIME'],
            'expire_time' => 0,
        ]);

        self::$vip_fee_deducted_record = Data::createVipFeeDeductedRecord([
            'user_id' => self::TEST_USER_ID,
            'vip_id' => 6,
            'sign_agreement_id' => self::$vip_subscription_sign_agreement->id,
            'pay_type' => VipFeeDeductedRecord::PAY_TYPE_WECHAT,
            'price' => 9900,
            'status' => VipFeeDeductedRecord::STATUS_PENDING,
            'next_deduct_time' => 0,
        ]);
    }

    public function testBeforeAction()
    {
        try {
            Request::api('/callback/alipay', $_POST = [
                'cc' => 'dd'
            ]);
        } catch (Exception $e) {
            // PASS
        }
        try {
            Request::api('/callback/guild-live-alipay', $_POST = [
                'kk' => 'll'
            ]);
        } catch (Exception $e) {
            // PASS
        }
        try {
            Yii::$app->request->setRawBody('<xml>ee</xml>');
            Request::api('/callback/wechatpay');
        } catch (Exception $e) {
            // PASS
        }
        try {
            Yii::$app->request->setRawBody('<xml>ff</xml>');
            Request::api('/callback/qqpay');
        } catch (Exception $e) {
            // PASS
        }
        try {
            Yii::$app->request->setRawBody('gg=hh&ii=jj');
            Request::api('/callback/paypal');
        } catch (Exception $e) {
            // PASS
        }
        try {
            Request::api('/callback/thirdparty-task-notify', $_POST = [
                'aa' => 'bb'
            ]);
        } catch (Exception $e) {
            // PASS
        }

        $logs = Yii::getLogger()->messages;
        $contents = array_column($logs, '0');
        $this->assertContains('callback alipay message: {"cc":"dd"}', $contents);
        $this->assertContains('callback guild-live-alipay message: {"kk":"ll"}', $contents);
        $this->assertContains('callback wechatpay message: <xml>ee</xml>', $contents);
        $this->assertContains('callback qqpay message: <xml>ff</xml>', $contents);
        $this->assertContains('callback paypal message: gg=hh&ii=jj', $contents);
        $this->assertContains('callback thirdparty-task-notify message: {"aa":"bb"}', $contents);
    }

    public function testTmallpay()
    {
        $_GET = [
            'app_key' => TMALL_APPKEY,
            'method' => TmallPayForm::TMALL_METHOD_QUERY,
            'timestamp' => '2019-05-17 16:18:14',
            'sign' => 'DFE51E03EA5A9DEDBCBBCABC6CEEB9B0',
            'tbOrderNo' => '1093225859150728',
        ];

        // 测试加锁失败（并发）
        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_GENERATE_ORDER, $_GET['tbOrderNo']);
        $redis->lock($lock, ONE_MINUTE);
        $data = Request::api('/callback/tmallpay', $_GET);
        $data = $this->processTmallResponse($data);
        $this->assertEquals('UNDERWAY', $data['coopOrderStatus']);
        $redis->unlock($lock);

        // 签名失败
        $data = Request::api('/callback/tmallpay', $_GET = [
            'app_key' => TMALL_APPKEY,
            'method' => TmallPayForm::TMALL_METHOD_QUERY,
            'timestamp' => '2019-05-17 16:18:14',
            'sign' => 'Wrong Sign',
            'tbOrderNo' => '1093225859150728',
            'coopId' => TMALL_COOPID,
            'version' => '1.0.0',
        ]);
        $data = $this->processTmallResponse($data);
        $this->assertEquals('GENERAL_ERROR', $data['coopOrderStatus']);
        $this->assertEquals('0102', $data['failedCode']);
        $this->assertEquals('签名失败', $data['failedReason']);
        $redis->unlock($lock);

        // 非法请求
        $this->assertThrowsWithMessage(HttpException::class, '非法请求', function () {
            Request::api('/callback/tmallpay', $_GET = [
                'app_key' => TMALL_APPKEY,
                'method' => 'wrong method',
                'timestamp' => '2019-05-17 16:18:14',
                'sign' => '343BA47F15F106BA229C3F352AF40F5B',
                'tbOrderNo' => '1093225859150728',
                'coopId' => TMALL_COOPID,
                'version' => '1.0.0',
            ]);
        });
        $redis->unlock($lock);

        // 正常情况
        // 测试过程中签名的 key 非线上使用，这里只验证计算签名的过程没有问题
        $data = Request::api('/callback/tmallpay', $_GET = [
            'app_key' => TMALL_APPKEY,
            'method' => TmallPayForm::TMALL_METHOD_QUERY,
            'timestamp' => '2019-05-17 16:18:14',
            'sign' => '6E173DB4DC0226544066BC26EDBE1262',
            'tbOrderNo' => '1093225859150728',
            'coopId' => TMALL_COOPID,
            'version' => '1.0.0',
        ]);
        $data = $this->processTmallResponse($data);
        $this->assertEquals('REQUEST_FAILED', $data['coopOrderStatus']);
        $this->assertEquals('0104', $data['failedCode']);
        $this->assertEquals('订单未创建', $data['failedReason']);
        $redis->unlock($lock);
    }

    private function processTmallResponse($data)
    {
        $data = iconv('GBK', 'UTF-8', $data);
        $array = MUtils::XMLToArray($data);
        return $array;
    }

    public function testIosIap()
    {
        $this->assertThrowsWithMessage(HttpException::class, '非法请求', function () {
            Yii::$app->request->setRawBody(Json::encode([
                'notification_type' => 'REFUND',
                'environment' => 'PROD',
                'bid' => 'com.missevan.CatEarFM999999999',
            ]));
            Request::api('/callback/ios-iap', [], Request::POST);
        }, 403);

        $this->assertThrowsWithMessage(HttpException::class, '不合法的 iOS 小票', function () {
            Yii::$app->request->setRawBody(Json::encode([
                'notification_type' => 'REFUND',
                'environment' => 'PROD',
                'bid' => 'com.missevan.CatEarFM',
                'latest_receipt' => 'wrong_receipt',
            ]));
            Request::api('/callback/ios-iap', [], Request::POST);
        }, 400);

        // 正常情况
        Yii::$app->request->setRawBody(Json::encode([
            'notification_type' => 'REFUND',
            'environment' => 'PROD',
            'latest_receipt' => 'test_ios_refund_receipt',
            'latest_receipt_info' => [
                'cancellation_reason' => '1',
                'is_trial_period' => 'false',
                'is_in_intro_offer_period' => 'false',
                'unique_identifier' => 'd0aade83a6673ac06a8fbf7f39599da7df87b469',
                'unique_vendor_identifier' => '81B8CC35-F251-4E10-B9F3-2A1EF21BBA4A',
                'cancellation_date' => '2020-08-23 23:08:46 Etc/GMT',
                'cancellation_date_ms' => '1598224126000',
                'cancellation_date_pst' => '2020-08-23 16:08:46 America/Los_Angeles',
                'purchase_date' => '2020-08-23 01:43:09 Etc/GMT',
                'purchase_date_ms' => '1598146989000',
                'purchase_date_pst' => '2020-08-22 18:43:09 America/Los_Angeles',
                'original_purchase_date' => '2020-08-23 01:43:09 Etc/GMT',
                'original_purchase_date_ms' => '1598146989000',
                'original_purchase_date_pst' => '2020-08-22 18:43:09 America/Los_Angeles',
                'item_id' => '1265411383',
                'app_item_id' => '1148465254',
                'version_external_identifier' => '837421099',
                'bid' => 'com.missevan.CatEarFM',
                'product_id' => 'com.missevan.CatEarFM0003',
                'transaction_id' => self::TEST_TRANSACTION_ID,
                'original_transaction_id' => self::TEST_TRANSACTION_ID,
                'quantity' => '1',
                'bvrs' => '2',
            ],
            'unified_receipt' => [
                'status' => 0,
                'environment' => 'Production',
                'latest_receipt_info' => [
                    [
                        'quantity' => '1',
                        'product_id' => 'com.missevan.CatEarFM0003',
                        'transaction_id' => self::TEST_TRANSACTION_ID,
                        'purchase_date' => '2020-08-23 01:43:09 Etc/GMT',
                        'purchase_date_ms' => '1598146989000',
                        'purchase_date_pst' => '2020-08-22 18:43:09 America/Los_Angeles',
                        'original_purchase_date' => '2020-08-23 01:43:09 Etc/GMT',
                        'original_purchase_date_ms' => '1598146989000',
                        'original_purchase_date_pst' => '2020-08-22 18:43:09 America/Los_Angeles',
                        'is_trial_period' => 'false',
                        'original_transaction_id' => self::TEST_TRANSACTION_ID,
                        'cancellation_date' => '2020-08-23 23:08:46 Etc/GMT',
                        'cancellation_date_ms' => '1598224126000',
                        'cancellation_date_pst' => '2020-08-23 16:08:46 America/Los_Angeles',
                        'cancellation_reason' => '1',
                    ],
                ],
                'latest_receipt' => 'test_ios_refund_receipt',
            ],
            'bid' => 'com.missevan.CatEarFM',
            'bvrs' => '2',
        ]));
        Request::api('/callback/ios-iap', [], Request::POST);
        $buyer = Balance::getByPk(self::TEST_USER_ID_IOS_IOP_REFUND);
        $this->assertEquals(100, $buyer->ios);
    }

    public function doudianDataProvider()
    {
        Tools::registerDouDianMockResponseFunc(AuthDouDian::METHOD_ORDER_DETAIL, function ($body) {
            return [
                'data' => [
                    'shop_order_detail' => [
                        'order_id' => 'test-callback-order-id',
                        'order_status' => DouDianOrder::ORDER_STATUS_DELIVERED,
                        'order_amount' => 1990,
                        'pay_amount' => 1990,
                        'promotion_amount' => 0,
                        'sku_order_list' => [
                            [
                                'code' => 9999,
                                'pay_amount' => 1990,
                                'item_num' => 1,
                                'c_biz' => DouDianGoods::CLIENT_BIZ_ALLY,
                                'c_biz_desc' => '精选联盟',
                                'author_id' => 2683221693703400,
                                'author_name' => '喵喵广播站(每周一三五晚七点直播）',
                            ],
                        ],
                        'encrypt_post_tel' => '...encrypt_post_tel...',
                        'biz' => DouDianOrder::ORDER_BIZ_VIRTUAL,
                        'biz_desc' => '虚拟',
                    ],
                ],
                'err_no' => 0,
                'log_id' => 'doudian-test-log-id',
                'message' => 'success',
            ];
        });

        return [
            [
                'raw_body' => Json::encode([
                    [
                        'tag' => DouDianPushMessage::TAG_ORDER_REFUND_CREATE,
                        'msg_id' => 'test-msg-id',
                        'data' => Json::encode([
                            'p_id' => 'test-callback-order-id',
                            'shop_id' => '123456',
                            'aftersale_id' => 'test-callback-after-sale-id',
                        ]),
                    ]
                ]),
            ],
        ];
    }

    /**
     * @dataProvider doudianDataProvider
     *
     * @param $raw_body
     */
    public function testDouDianVirtual($raw_body)
    {
        $config = Yii::$app->params['service']['doudian'];
        $event_sign = md5($config['app_key'] . $raw_body . $config['app_secret']);

        $request = Yii::$app->request;
        $request->headers->add('event-sign', $event_sign);
        $request->headers->add('app-id', $config['app_key']);
        $request->setRawBody($raw_body);

        $resp = Request::api('/callback/doudian', [], Request::POST);
        $this->assertEquals(DouDianPushMessage::successfulResponse(), $resp);
    }

    private function setDouDianMobileTopupRawBody(array $params)
    {
        $config = Yii::$app->params['service']['doudian'];
        $_GET = [
            'app_key' => $config['app_key'],
            'timestamp' => date('Y-m-d H:i:s'),
            'sign_method' => 'md5',
            'sign' => '',
        ];

        $params['shop_id'] = $config['shop_id'];
        $raw_body = Json::encode($params);
        $client = AuthDouDian::client();
        $_GET['sign'] = $client->getSPISign($params, $config['app_key'], $_GET['timestamp'], $_GET['sign_method']);
        Yii::$app->request->setRawBody($raw_body);
    }

    public function doudianNotifyDataProvider()
    {
        Tools::registerDouDianMockResponseFunc(AuthDouDian::METHOD_ORDER_DETAIL, function ($body) {
            return [
                'data' => [
                    'shop_order_detail' => [
                        'order_id' => 'test-mobile-topup-order-no',
                        'order_status' => DouDianOrder::ORDER_STATUS_DELIVERED,
                        'order_amount' => 1990,
                        'pay_amount' => 1990,
                        'promotion_amount' => 0,
                        'sku_order_list' => [
                            [
                                'code' => 666777,
                                'pay_amount' => 1990,
                                'item_num' => 1,
                                'c_biz' => DouDianGoods::CLIENT_BIZ_ALLY,
                                'c_biz_desc' => '精选联盟',
                                'author_id' => 2683221693703400,
                                'author_name' => '喵喵广播站(每周一三五晚七点直播）',
                            ],
                        ],
                        'encrypt_post_tel' => '...encrypt_post_tel...',
                        'biz' => DouDianOrder::ORDER_BIZ_TOPUP,
                        'biz_desc' => '手机充值',
                    ],
                ],
                'err_no' => 0,
                'log_id' => 'doudian-test-log-id',
                'message' => 'success',
            ];
        });
        Tools::registerDramaMockResponseFunc('/rpc/api/get-drama-details-by-id', function ($params) {
            return [
                'drama' => [
                    'id' => 666777,
                    'name' => '在抖店销售的广播剧',
                    'price' => 199,
                    'pay_type' => Drama::PAY_TYPE_DRAMA,
                ],
            ];
        });
        Tools::registerDramaMockResponseFunc('/rpc/api/get-drama-price', function ($params) {
            return [
                'drama_id' => 666777,
                'name' => '在抖店销售的广播剧',
                'price' => 199,
                'pay_type' => Drama::PAY_TYPE_DRAMA,
                'rate' => 0.5,
                'user_id' => 333222111,
            ];
        });

        return [[]];
    }

    /**
     * @dataProvider doudianNotifyDataProvider
     */
    public function testDouDianNotify()
    {
        $params = [
            'trade_order_no' => 'test-mobile-topup-order-no',
            'topup_biz' => AuthDouDian::TOPUP_BIZ_MOBILE_TOPUP,
            'time_start' => '**************',
            'time_limit' => '10800',
            'buy_num' => '1',
            'amount_unit' => '10800',
            'sku_id' => '*********',
            'shop_id' => '',
            'account_list' => [
                [
                    'account_val' => '***********',
                    'account_type' => 'MOBILE',
                    'account_name' => '绑定手机号',
                ],
            ],
            'code' => '26',
            'pay_amount' => '10000',
        ];
        $this->setDouDianMobileTopupRawBody($params);
        RechargeOrder::deleteAll(['tid' => $params['trade_order_no']]);

        $data = Request::api('/callback/doudian-notify', [], Request::POST);
        $this->assertArrayHasKeys(['data', 'code', 'message'], $data);
        $this->assertEquals('success', $data['message']);
        $this->assertEquals(0, $data['code']);
        $this->assertArrayHasKeys(['trade_order_no', 'topup_biz', 'seller_order_no', 'seller_order_status', 'err_code', 'err_desc'], $data['data']);
        $this->assertEquals(AuthDouDian::SELLER_ORDER_STATUS_SUCCESS, $data['data']['seller_order_status']);
    }

    /**
     * @depends testDouDianNotify
     */
    public function testDouDianQuery()
    {
        $this->setDouDianMobileTopupRawBody([
            'trade_order_no' => 'test-mobile-topup-order-no',
            'topup_biz' => AuthDouDian::TOPUP_BIZ_MOBILE_TOPUP,
            'shop_id' => '',
        ]);

        $data = Request::api('/callback/doudian-query', [], Request::POST);
        $this->assertArrayHasKeys(['data', 'code', 'message'], $data);
        $this->assertEquals(0, $data['code']);
        $this->assertEquals(AuthDouDian::SELLER_ORDER_STATUS_SUCCESS, $data['data']['seller_order_status']);
    }

    /**
     * @depends testDouDianQuery
     */
    public function testDouDianCancel()
    {
        $this->setDouDianMobileTopupRawBody([
            'trade_order_no' => 'test-mobile-topup-order-no',
            'topup_biz' => AuthDouDian::TOPUP_BIZ_MOBILE_TOPUP,
            'shop_id' => '',
        ]);

        $data = Request::api('/callback/doudian-cancel', [], Request::POST);
        $this->assertArrayHasKeys(['data', 'code', 'message'], $data);
        $this->assertEquals(0, $data['code']);
        $this->assertEquals(AuthDouDian::SELLER_ORDER_STATUS_SUCCESS, $data['data']['seller_order_status']);
    }

    public function testAppsflyer()
    {
        $nowstamp = $_SERVER['REQUEST_TIME'];
        $ymd = date('Ym', $nowstamp);
        $sql = <<<SQL
REPLACE INTO `install_log_{$ymd}`
(`id`, `create_time`, `modified_time`, `equip_id`, `device_type`, `is_root`, `adid`, `idfv`, `user_agent`, `ip`, `buvid`, `version`)
VALUES
(333, UNIX_TIMESTAMP() - 60, UNIX_TIMESTAMP() - 60, 'dev-equip-id', 2, 0, 'dev-idfa', 'test-idfv', 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_5_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148', '*******', 'dev-buvid', '4.7.4');
SQL;
        InstallLog::getDb()->createCommand($sql)->execute();

        $raw_body = Json::encode([
            'platform' => 'ios',
            'conversion_type' => 'Install',
            'custom_data' => Json::encode([
                'equip_id' => 'dev-equip-id',
                'buvid' => 'dev-buvid',
            ]),
            'appsflyer_id' => self::APPSFLYER_TRACK_ID,
            'af_ad' => '广告名',
            'af_ad_id' => '1111',
            'af_adset' => '广告组名',
            'af_adset_id' => '222',
            'af_c_id' => '333',
            'campaign' => '广告计划名',
            'media_source' => 'Non-Organic',
            'af_channel' => 'YouTube',
            'event_time' => date('Y-m-d H:i:s.000', $nowstamp - 8 * 3600),
            'selected_timezone' => 'Asia/Shanghai',
            'event_time_selected_timezone' => date('Y-m-d H:i:s.000+0800', $nowstamp),
        ]);
        Yii::$app->request->setRawBody($raw_body);
        Request::api('/callback/appsflyer', [], Request::POST);
        $record = AdTrack::findOne(['track_id' => self::APPSFLYER_TRACK_ID, 'converted' => AdTrack::CONVERTED]);
        $this->assertNotNull($record);
    }

    public function testJdTopup()
    {
        $_POST = [
            'signType' => AuthJingDong::SIGN_TYPE_DEFAULT,
            'vendorId' => Yii::$app->params['service']['jingdong']['vendor_id'],
            'produceAccount' => self::TEST_USER2_MOBILE,
            'jdOrderNo' => self::JINGDONG_TRANSACTION_ID,
            'totalPrice' => 50000,
            'quantity' => 1,
            'wareNo' => 50,
        ];
        if (!$ccy = TopupMenu::findOne(['id' => $_POST['wareNo']])) {
            $ccy = new TopupMenu();
            $ccy->setAttributes([
                'id' => $_POST['wareNo'],
                'num' => 5000,
                'price' => 500,
                'ccy' => TopupMenu::DIAMOND,
                'device' => TopupMenu::DEVICE_JINGDONG,
            ], false);
            if (!$ccy->save()) {
                throw new Exception(MUtils2::getFirstError($ccy));
            }
        }

        $jd_client = new AuthJingDong();
        $_POST['sign'] = $jd_client->makeSign($_POST);

        $old_balance = Balance::findOne(self::TEST_USER2_ID);
        $resp = Request::api('/callback/jd-topup', $_POST, Request::POST);
        $this->assertArrayHasKeys(['agentOrderNo', 'code', 'jdOrderNo', 'produceStatus', 'signType', 'timestamp'], $resp);
        $this->assertEquals(AuthJingDong::PRODUCE_CODE_SUCCESS, $resp['code']);
        $this->assertEquals(AuthJingDong::PRODUCE_STATUS_SUCCESS, $resp['produceStatus']);

        $new_balance = Balance::findOne(self::TEST_USER2_ID);
        $this->assertEquals($old_balance->android + 5000, $new_balance->android);
        $this->assertEquals($old_balance->all_topup + 5000, $new_balance->all_topup);
        $this->assertEquals($old_balance->all_coin + 5000, $new_balance->all_coin);
    }

    /**
     * @depends testJdTopup
     */
    public function testJdQueryOrder()
    {
        $_POST = [
            'signType' => AuthJingDong::SIGN_TYPE_DEFAULT,
            'jdOrderNo' => self::JINGDONG_TRANSACTION_ID,
        ];
        $jd_client = new AuthJingDong();
        $_POST['sign'] = $jd_client->makeSign($_POST);

        $resp = Request::api('/callback/jd-query-order', $_POST, Request::POST);
        $this->assertArrayHasKeys(['agentOrderNo', 'code', 'jdOrderNo', 'produceStatus', 'signType', 'timestamp'], $resp);
        $this->assertEquals(AuthJingDong::PRODUCE_CODE_SUCCESS, $resp['code']);
        $this->assertEquals(AuthJingDong::PRODUCE_STATUS_SUCCESS, $resp['produceStatus']);
    }

    public function testActionJdTopupV2()
    {
        $_POST = [
            'customerId' => Yii::$app->params['service']['jingdong-v2']['customer_id'],
            'timestamp' => AuthJingDongV2::formatTime($_SERVER['REQUEST_TIME']),
            'data' => base64_encode(Json::encode([
                'orderId' => self::JINGDONG_V2_TRANSACTION_ID,
                'buyNum' => 2,
                'totalPrice' => 1000,
                'gameAccount' => self::TEST_USER2_MOBILE,
                'outId' => 50,
            ])),
        ];
        $jd_client = new AuthJingDongV2();

        $_POST['sign'] = 'wrong sign';
        $resp = Request::api('/callback/jd-topup-v2', $_POST, Request::POST);
        $resp = Json::decode(iconv('GBK', 'UTF-8//IGNORE', $resp));
        $this->assertArrayHasKeys(['retCode', 'retMessage'], $resp);
        $this->assertEquals(AuthJingDongV2::RET_CODE_ERROR_SIGN, $resp['retCode']);
        $this->assertEquals('签名错误', $resp['retMessage']);

        if (!$ccy = TopupMenu::findOne(['id' => 50])) {
            $ccy = new TopupMenu();
            $ccy->setAttributes([
                'id' => 50,
                'num' => 5000,
                'price' => 500,
                'ccy' => TopupMenu::DIAMOND,
                'device' => TopupMenu::DEVICE_JINGDONG,
            ], false);
            if (!$ccy->save()) {
                throw new Exception(MUtils2::getFirstError($ccy));
            }
        }

        $_POST['sign'] = $jd_client->makeSign($_POST);
        $old_balance = Balance::findOne(self::TEST_USER2_ID);
        $resp = Request::api('/callback/jd-topup-v2', $_POST, Request::POST);
        $resp = Json::decode(iconv('GBK', 'UTF-8//IGNORE', $resp));
        $this->assertArrayHasKeys(['retCode', 'retMessage'], $resp);
        $this->assertEquals(AuthJingDongV2::RET_CODE_SUCCESS, $resp['retCode']);
        $this->assertEquals('成功', $resp['retMessage']);

        $new_balance = Balance::findOne(self::TEST_USER2_ID);
        $this->assertEquals($old_balance->android + 10000, $new_balance->android);
        $this->assertEquals($old_balance->all_topup + 10000, $new_balance->all_topup);
        $this->assertEquals($old_balance->all_coin + 10000, $new_balance->all_coin);
    }

    /**
     * @depends testActionJdTopupV2
     */
    public function testActionJdQueryOrderV2()
    {
        $_POST = [
            'customerId' => Yii::$app->params['service']['jingdong-v2']['customer_id'],
            'timestamp' => AuthJingDongV2::formatTime($_SERVER['REQUEST_TIME']),
            'data' => base64_encode(Json::encode([
                'orderId' => self::JINGDONG_V2_TRANSACTION_ID,
            ])),
        ];
        $jd_client = new AuthJingDongV2();
        $_POST['sign'] = $jd_client->makeSign($_POST);

        $resp = Request::api('/callback/jd-query-order-v2', $_POST, Request::POST);
        $resp = Json::decode(iconv('GBK', 'UTF-8//IGNORE', $resp));
        $this->assertArrayHasKeys(['retCode', 'retMessage'], $resp);
        $this->assertEquals(AuthJingDongV2::RET_CODE_SUCCESS, $resp['retCode']);
        $this->assertEquals('成功', $resp['retMessage']);
    }

    public function testBiliLargePay()
    {
        $order = new RechargeOrder([
            'uid' => self::TEST_USER2_ID,
            'type' => RechargeOrder::TYPE_BILI_LARGE_PAY,
            'cid' => 0,
            'price' => 0,
            'num' => 0,
            'ccy' => TopupMenu::DIAMOND,
            'status' => RechargeOrder::STATUS_CREATE,
            'origin' => RechargeOrder::ORIGIN_DESKTOP_WEB,
            'detail' => UserContext::fromUser(Yii::$app->request)->toArray(),
        ]);
        if (!$order->save()) {
            throw new Exception(MUtils2::getFirstError($order));
        }

        $balance = Balance::getByPk($order->uid);
        $PAY_AMOUNT_IN_FEN = 50000;
        $params = [
            'customerId' => BILIBILI_LARGE_PAY_CUSTOMER_ID,
            'serviceType' => BiliLargePayClient::SERVICE_TYPE_LARGE_PAY,
            'txId' => self::BILI_LARGE_PAY_TRANSACTION_ID,
            'orderId' => $order->getOrderId(),
            'feeType' => BiliLargePayClient::FEE_TYPE_CNY,
            'payStatus' => 'SUCCESS',
            'payChannel' => 'cmb_transfer',
            'payChannelName' => '银行转帐',
            'payChannelId' => BiliLargePayClient::PAY_CHANNEL_ID_BANK_TRANSFER,
            'payAmount' => $PAY_AMOUNT_IN_FEN,
            'payMsgContent' => '{"payCounponAmount":0,"payBpAmount":0,"defaultBpAmount":0,"iosBpAmount":0,"productId":"0","uid":"346286","failReason":""}',
            'payAccountId' => '',
            'payAccount' => '',
            'payBank' => '',
            'deviceType' => 1,
            'timestamp' => $_SERVER['REQUEST_TIME'],
            'traceId' => '3550613824602636322',
            'extData' => '{}',
            'signType' => 'MD5',
            'expiredTime' => 0
        ];
        $client = new BiliLargePayClient();
        $params['sign'] = $client->makeSign($params);
        $_GET['msgContent'] = Json::encode($params);

        $resp = Request::api('/callback/bili-large-pay', $_GET);
        $this->assertEquals(BiliLargePayClient::CALLBACK_RESP_SUCCESS, $resp);
        $order = RechargeOrder::findOne(['id' => $order->id]);
        $this->assertTrue($order->isSuccess());
        $this->assertEquals(self::BILI_LARGE_PAY_TRANSACTION_ID, $order->tid);
        $this->assertEquals(Balance::profitUnitConversion($PAY_AMOUNT_IN_FEN, Balance::CONVERT_FEN_TO_YUAN), $order->price);
        $this->assertEquals(Balance::profitUnitConversion($order->price, Balance::CONVERT_YUAN_TO_DIAMOND), $order->num);
        $balance_new = Balance::getByPk(self::TEST_USER2_ID);
        $this->assertEquals($balance->android + $order->num, $balance_new->android);
        $this->assertEquals($balance->all_coin + $order->num, $balance_new->all_coin);
    }

    public function testActionWechatpaySign()
    {
        $api = '/callback/wechatpay-sign';
        $contract_code = self::$vip_subscription_sign_agreement->getAgreementNo();
        $openid = 'onqOjjmM1tad-3ROpncN-yUfa6ua';
        $operate_time = date('Y-m-d H:i:s', $_SERVER['REQUEST_TIME']);
        $contract_id = 'Wx15463511252015071056489715';
        $change_type = AuthWechat::AGREEMENT_CHANGE_TYPE_ADD;

        // 测试非法请求（没有签名）
        $raw_body = <<<XML
<xml>
  <return_code><![CDATA[SUCCESS]]></return_code>
  <result_code><![CDATA[SUCCESS]]></result_code>
  <mch_id>test</mch_id>
  <contract_code>$contract_code</contract_code>
  <openid><![CDATA[$openid]]></openid>
  <plan_id><![CDATA[1]]></plan_id>
  <change_type><![CDATA[$change_type]]></change_type>
  <operate_time><![CDATA[$operate_time]]></operate_time>
  <contract_id><![CDATA[$contract_id]]></contract_id>
</xml>
XML;
        Yii::$app->request->setRawBody($raw_body);
        $this->assertThrowsWithMessage(HttpException::class, '非法请求', function () use ($api) {
            Request::api($api, [], Request::POST);
        }, 403);

        // 测试签名错误
        $raw_body = <<<XML
<xml>
  <return_code><![CDATA[SUCCESS]]></return_code>
  <result_code><![CDATA[SUCCESS]]></result_code>
  <sign><![CDATA[error_sign]]></sign>
  <mch_id>test</mch_id>
  <contract_code>$contract_code</contract_code>
  <openid><![CDATA[$openid]]></openid>
  <plan_id><![CDATA[1]]></plan_id>
  <change_type><![CDATA[$change_type]]></change_type>
  <operate_time><![CDATA[$operate_time]]></operate_time>
  <contract_id><![CDATA[$contract_id]]></contract_id>
</xml>
XML;
        Yii::$app->request->setRawBody($raw_body);
        $res = Request::api($api, [], Request::POST);
        $this->assertEquals(AuthWechat::getWechatPayFailCallbackResponse(), $res);

        // 测试正常签约
        $arr = [
            'return_code' => 'SUCCESS',
            'result_code' => 'SUCCESS',
            'mch_id' => 'test',
            'contract_code' => $contract_code,
            'plan_id' => 1,
            'openid' => $openid,
            'change_type' => $change_type,
            'operate_time' => $operate_time,
            'contract_id' => $contract_id,
        ];
        $sign = AuthWechat::getWechatSign($arr);
        $raw_body = <<<XML
<xml>
  <return_code><![CDATA[SUCCESS]]></return_code>
  <result_code><![CDATA[SUCCESS]]></result_code>
  <sign><![CDATA[$sign]]></sign>
  <mch_id>test</mch_id>
  <contract_code>$contract_code</contract_code>
  <openid><![CDATA[$openid]]></openid>
  <plan_id><![CDATA[1]]></plan_id>
  <change_type><![CDATA[$change_type]]></change_type>
  <operate_time><![CDATA[$operate_time]]></operate_time>
  <contract_id><![CDATA[$contract_id]]></contract_id>
</xml>
XML;
        Yii::$app->request->setRawBody($raw_body);
        $res = Request::api($api, [], Request::POST);
        $this->assertEquals(AuthWechat::getWechatPaySuccessCallbackResponse(), $res);
        // 验证签约记录已更新
        $sign_termination_time = $_SERVER['REQUEST_TIME'];
        $sign_agreement = VipSubscriptionSignAgreement::findOne(['id' => self::$vip_subscription_sign_agreement->id]);
        $this->assertNotNull($sign_agreement);
        $this->assertEquals(VipSubscriptionSignAgreement::STATUS_ACTIVE, $sign_agreement->status);
        $this->assertEquals($sign_termination_time, $sign_agreement->start_time);
        $this->assertNotEmpty($sign_agreement->more);
        $this->assertArrayHasKeys(['wechat_openid', 'agreement_no', 'contract_id'], $sign_agreement->more);
        $this->assertEquals($openid, $sign_agreement->more['wechat_openid']);
        $this->assertEquals($contract_code, $sign_agreement->more['agreement_no']);
        $this->assertEquals($contract_id, $sign_agreement->more['contract_id']);

        // 测试正常解约
        $CONTRACT_TERMINATION_MODE = 3;
        $operate_time_1 = date('Y-m-d H:i:s', $_SERVER['REQUEST_TIME']);
        $change_type = AuthWechat::AGREEMENT_CHANGE_TYPE_DELETE;
        $arr = [
            'return_code' => 'SUCCESS',
            'result_code' => 'SUCCESS',
            'mch_id' => 'test',
            'contract_code' => $contract_code,
            'plan_id' => 1,
            'openid' => $openid,
            'change_type' => $change_type,
            'operate_time' => $operate_time_1,
            'contract_id' => $contract_id,
            'contract_termination_mode' => $CONTRACT_TERMINATION_MODE,
        ];
        $sign = AuthWechat::getWechatSign($arr);
        $raw_body = <<<XML
<xml>
  <return_code><![CDATA[SUCCESS]]></return_code>
  <result_code><![CDATA[SUCCESS]]></result_code>
  <sign><![CDATA[$sign]]></sign>
  <mch_id>test</mch_id>
  <contract_code>$contract_code</contract_code>
  <openid><![CDATA[$openid]]></openid>
  <plan_id><![CDATA[1]]></plan_id>
  <change_type><![CDATA[$change_type]]></change_type>
  <operate_time><![CDATA[$operate_time_1]]></operate_time>
  <contract_id><![CDATA[$contract_id]]></contract_id>
  <contract_termination_mode><![CDATA[$CONTRACT_TERMINATION_MODE]]></contract_termination_mode>
</xml>
XML;
        Yii::$app->request->setRawBody($raw_body);
        $res = Request::api($api, [], Request::POST);
        $this->assertEquals(AuthWechat::getWechatPaySuccessCallbackResponse(), $res);
        // 验证签约记录已更新为
        $sign_termination_time_1 = $_SERVER['REQUEST_TIME'];
        $sign_agreement = VipSubscriptionSignAgreement::findOne(['id' => self::$vip_subscription_sign_agreement->id]);
        $this->assertNotNull($sign_agreement);
        $this->assertEquals(VipSubscriptionSignAgreement::STATUS_TERMINATED, $sign_agreement->status);
        $this->assertEquals($sign_termination_time, $sign_agreement->start_time);
        $this->assertEquals($sign_termination_time_1, $sign_agreement->expire_time);
        $this->assertNotEmpty($sign_agreement->more);
        $this->assertArrayHasKeys(['wechat_openid', 'agreement_no', 'contract_id', 'wechat_contract_termination_mode'],
            $sign_agreement->more);

        // 测试解约成功后再次解约
        Yii::$app->request->setRawBody($raw_body);
        $res = Request::api($api, [], Request::POST);
        $this->assertEquals(AuthWechat::getWechatPaySuccessCallbackResponse(), $res);
    }

    public function testActionThirdpartyTaskNotify()
    {
        Yii::$app->set('db', Yii::$app->sqlitedb);
        MThirdPartyTask::updateAll(['status' => MThirdPartyTask::TASK_STATUS_ON_GOING], 'id = :id', [':id' => 1]);

        // 测试参数为空
        $res = Request::api('/callback/thirdparty-task-notify', $_POST = [
            'request_id' => null,
            'maoer_task_token' => '',
            'scene' => '',
            'ts' => 0,
            'sign' => '',
        ], Request::POST);
        $data = Json::decode($res);
        $this->assertArrayHasKeys(['code', 'message', 'data'], $data);
        $this->assertNotEquals(CallbackController::CODE_SUCCESS, $data['code']);
        $this->assertEquals('参数错误', $data['message']);
        $this->assertNull($data['data']);

        $now = $_SERVER['REQUEST_TIME'];
        // 测试不支持的第三方
        $res = Request::api('/callback/thirdparty-task-notify', $_POST = [
            'request_id' => 'test',
            'maoer_task_token' => 'test',
            'scene' => 'test',
            'ts' => $now,
            'sign' => 'test',
        ], Request::POST);
        $data = Json::decode($res);
        $this->assertArrayHasKeys(['code', 'message', 'data'], $data);
        $this->assertNotEquals(CallbackController::CODE_SUCCESS, $data['code']);
        $this->assertEquals('不支持的第三方', $data['message']);
        $this->assertNull($data['data']);

        // 测试验签失败
        $res = Request::api('/callback/thirdparty-task-notify', $_POST = [
            'request_id' => 'test',
            'maoer_task_token' => 'test',
            'scene' => ThirdPartyTaskUtil::SCENE_CTRIP,
            'ts' => $now,
            'sign' => 'test',
        ], Request::POST);
        $data = Json::decode($res);
        $this->assertArrayHasKeys(['code', 'message', 'data'], $data);
        $this->assertNotEquals(CallbackController::CODE_SUCCESS, $data['code']);
        $this->assertEquals('验签失败', $data['message']);
        $this->assertNull($data['data']);

        // 测试奖励发放成功
        $param = [
            'request_id' => 'test',
            'maoer_task_token' => 'test',
            'scene' => ThirdPartyTaskUtil::SCENE_BAIDU,
            'ts' => $now,
            'sign' => 'test'
        ];
        $sign = ThirdPartyTaskUtil::buildSign($param);
        $param['sign'] = $sign;
        $res = Request::api('/callback/thirdparty-task-notify', $_POST = $param, Request::POST);
        $data = Json::decode($res);
        $this->assertArrayHasKeys(['code', 'message', 'data'], $data);
        $this->assertEquals(CallbackController::CODE_SUCCESS, $data['code']);
        $this->assertEquals('', $data['message']);
        $this->assertNotNull($data['data']);
        $this->assertArrayHasKey('status', $data['data']);
        $this->assertEquals(CallbackController::THIRD_PARTY_TASK_RESPONSE_STATUS_SUCCESS, $data['data']['status']);

        // 测试奖励发放失败
        $param = [
            'request_id' => 'test',
            'maoer_task_token' => 'test',
            'scene' => ThirdPartyTaskUtil::SCENE_BAIDU,
            'ts' => $now,
            'sign' => 'test'
        ];
        $sign = ThirdPartyTaskUtil::buildSign($param);
        $param['sign'] = $sign;
        $res = Request::api('/callback/thirdparty-task-notify', $_POST = $param, Request::POST);
        $data = Json::decode($res);
        $this->assertArrayHasKeys(['code', 'message', 'data'], $data);
        $this->assertEquals(CallbackController::CODE_SUCCESS, $data['code']);
        $this->assertEquals('', $data['message']);
        $this->assertNotNull($data['data']);
        $this->assertArrayHasKey('status', $data['data']);
        $this->assertEquals(CallbackController::THIRD_PARTY_TASK_RESPONSE_STATUS_FAILED, $data['data']['status']);
    }

    public function testActionWechatOffiaccountReplay()
    {
        // 测试非法请求
        $api = '/callback/wechat-offiaccount-replay';
        $this->assertThrowsWithMessage(HttpException::class, '非法请求', function () use ($api) {
            Request::api($api, [], Request::POST);
        }, 400);

        // 测试正常的 GET 请求验证
        $query_param = [
            'signature' => '18a836054387deadd3d7e4ef7e2b2a804809e0ca',
            'timestamp' => '**********',
            'nonce' => 'test_nonce',
            'echostr' => 'this is a test echostr',
        ];
        $resp = Request::api($api, $query_param, Request::GET);
        $this->assertEquals($query_param['echostr'], $resp);

        // 测试正常的 POST 请求，对于非任务关键词回复，回复固定内容
        unset($query_param['echostr']);
        // TODO: 后续应该考虑在 Request::api() 中，支持 POST 请求时设置 raw body
        $raw_body = <<<XML
<xml>
  <ToUserName><![CDATA[test_to_user_id]]></ToUserName>
  <FromUserName><![CDATA[test_from_user_id]]></FromUserName>
  <CreateTime>**********</CreateTime>
  <MsgType><![CDATA[text]]></MsgType>
  <Content><![CDATA[this is a test]]></Content>
  <MsgId>**********123456</MsgId>
</xml>
XML;
        Yii::$app->request->setRawBody($raw_body);
        $api .= '?' . http_build_query($query_param);
        $resp = Request::api($api, [], Request::POST);
        $data_arr = (array)simplexml_load_string($resp, 'SimpleXMLElement', LIBXML_NOCDATA);
        $this->assertArrayHasKeys(['ToUserName', 'FromUserName', 'CreateTime', 'MsgType', 'Content'], $data_arr);
        $this->assertEquals('test_from_user_id', $data_arr['ToUserName']);
        $this->assertEquals('test_to_user_id', $data_arr['FromUserName']);
        $this->assertEquals($_SERVER['REQUEST_TIME'], $data_arr['CreateTime']);
        $this->assertEquals(WechatOffiaccount::MSG_TYPE_TEXT, $data_arr['MsgType']);
        $this->assertEquals(MWechatOffiaccountReply::DEFAULT_TEXT_REPLY, $data_arr['Content']);

        // 测试正常的 POST 请求，对于任务关键词回复，回复任务相关信息
        $raw_body = <<<XML
<xml>
  <ToUserName><![CDATA[test_to_user_id]]></ToUserName>
  <FromUserName><![CDATA[test_from_user_id]]></FromUserName>
  <CreateTime>**********</CreateTime>
  <MsgType><![CDATA[text]]></MsgType>
  <Content><![CDATA[活动任务]]></Content>
  <MsgId>**********123456</MsgId>
</xml>
XML;
        Yii::$app->request->setRawBody($raw_body);
        $api .= '?' . http_build_query($query_param);
        $resp = Request::api($api, [], Request::POST);
        $this->assertStringContainsString('<xml>', $resp);
        $data_arr = (array)simplexml_load_string($resp, 'SimpleXMLElement', LIBXML_NOCDATA);
        $this->assertArrayHasKeys(['ToUserName', 'FromUserName', 'CreateTime', 'MsgType', 'Content'], $data_arr);
        $this->assertEquals('test_from_user_id', $data_arr['ToUserName']);
        $this->assertEquals('test_to_user_id', $data_arr['FromUserName']);
        $this->assertEquals($_SERVER['REQUEST_TIME'], $data_arr['CreateTime']);
        $this->assertEquals(WechatOffiaccount::MSG_TYPE_TEXT, $data_arr['MsgType']);
        $this->assertStringContainsString('https://test.test.com/event?third_task_token=', $data_arr['Content']);

        // 测试关注事件，回复特定语音
        $raw_body = <<<XML
<xml>
  <ToUserName><![CDATA[test_to_user_id]]></ToUserName>
  <FromUserName><![CDATA[test_from_user_id]]></FromUserName>
  <CreateTime>*********</CreateTime>
  <MsgType><![CDATA[event]]></MsgType>
  <Event><![CDATA[subscribe]]></Event>
</xml>
XML;
        Yii::$app->request->setRawBody($raw_body);
        $api .= '?' . http_build_query($query_param);
        $resp = Request::api($api, [], Request::POST);
        $this->assertStringContainsString('<xml>', $resp);
        $data_arr = (array)simplexml_load_string($resp, 'SimpleXMLElement', LIBXML_NOCDATA);
        $this->assertArrayHasKeys(['ToUserName', 'FromUserName', 'CreateTime', 'MsgType', 'Voice'], $data_arr);
        $this->assertEquals('test_from_user_id', $data_arr['ToUserName']);
        $this->assertEquals('test_to_user_id', $data_arr['FromUserName']);
        $this->assertEquals($_SERVER['REQUEST_TIME'], $data_arr['CreateTime']);
        $this->assertEquals(WechatOffiaccount::MSG_TYPE_VOICE, $data_arr['MsgType']);
        $this->assertIsObject($data_arr['Voice']);
        $this->assertEquals('test_voice_medal_id', $data_arr['Voice']->MediaId);

        // 测试非关注事件，无回复
        $raw_body = <<<XML
<xml>
  <ToUserName><![CDATA[test_to_user_id]]></ToUserName>
  <FromUserName><![CDATA[test_from_user_id]]></FromUserName>
  <CreateTime>*********</CreateTime>
  <MsgType><![CDATA[event]]></MsgType>
  <Event><![CDATA[CLICK]]></Event>
  <EventKey><![CDATA[CLICK]]></EventKey>
</xml>
XML;
        Yii::$app->request->setRawBody($raw_body);
        $api .= '?' . http_build_query($query_param);
        $resp = Request::api($api, [], Request::POST);
        $this->assertEquals('success', $resp);
    }
}
