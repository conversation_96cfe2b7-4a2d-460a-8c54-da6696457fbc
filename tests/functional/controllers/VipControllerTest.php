<?php

namespace tests\controllers;

use app\components\auth\AuthWechat;
use app\components\util\SuccessResponseWithMessage;
use app\models\Ccy;
use app\models\MUserVip;
use app\models\TopupMenu;
use app\models\VipFeeDeductedRecord;
use app\models\VipReceiveCoinLog;
use app\models\VipSubscriptionSignAgreement;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Response;
use tests\components\UnitTestCase;
use tests\components\util\Data;
use tests\components\web\Request;
use Yii;
use yii\helpers\Json;
use yii\web\HttpException;

class VipControllerTest extends UnitTestCase
{
    private static $origin_db;
    private static $origin_paydb;

    private static $vip_subscription_sign_agreement;
    private static $vip_fee_deducted_record_1;
    private static $vip_fee_deducted_record_2;

    private static function setOriginDbs(): void
    {
        self::$origin_db = Yii::$app->db;
        self::$origin_paydb = Yii::$app->paydb;
        Yii::$app->set('db', Yii::$app->sqlitedb);
        Yii::$app->set('paydb', Yii::$app->sqlite_paydb);
    }

    private static function resetOriginDbs(): void
    {
        Yii::$app->set('db', self::$origin_db);
        Yii::$app->set('paydb', self::$origin_paydb);
    }

    protected function _before()
    {
        parent::_before();
        self::setOriginDbs();
    }

    protected function _after()
    {
        parent::_after();
        self::resetOriginDbs();
    }

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        self::setOriginDbs();
        self::cleanTestData();
        self::createTestData();
    }

    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
        self::setOriginDbs();
        self::cleanTestData();
        self::resetOriginDbs();
    }

    private static function createTestData()
    {
        self::$vip_subscription_sign_agreement = Data::createVipSubscriptionSignAgreement([
            'user_id' => self::TEST_USER_ID,
            'pay_type' => VipSubscriptionSignAgreement::PAY_TYPE_WECHAT,
            'vip_id' => 6,
            'status' => VipSubscriptionSignAgreement::STATUS_PENDING,
            'start_time' => $_SERVER['REQUEST_TIME'],
            'expire_time' => 0,
        ]);

        self::$vip_fee_deducted_record_1 = Data::createVipFeeDeductedRecord([
            'user_id' => self::TEST_USER_ID,
            'vip_id' => 6,
            'sign_agreement_id' => self::$vip_subscription_sign_agreement->id,
            'pay_type' => VipFeeDeductedRecord::PAY_TYPE_WECHAT,
            'price' => 9900,
            'status' => VipFeeDeductedRecord::STATUS_PENDING,
            'next_deduct_time' => 0,
        ]);

        self::$vip_fee_deducted_record_2 = Data::createVipFeeDeductedRecord([
            'user_id' => self::TEST_USER_ID,
            'vip_id' => 8,
            'sign_agreement_id' => 0,
            'pay_type' => VipFeeDeductedRecord::PAY_TYPE_WECHAT,
            'price' => 16800,
            'status' => VipFeeDeductedRecord::STATUS_SUCCESS,
            'next_deduct_time' => 0,
        ]);
    }

    private static function cleanTestData()
    {
        if (self::$vip_subscription_sign_agreement) {
            VipSubscriptionSignAgreement::deleteAll(['id' => self::$vip_subscription_sign_agreement->id]);
        }
        if (self::$vip_fee_deducted_record_1 && self::$vip_fee_deducted_record_2) {
            VipFeeDeductedRecord::deleteAll(['id' => [self::$vip_fee_deducted_record_1->id,
                self::$vip_fee_deducted_record_2->id]]);
        }
    }

    public function testActionCreateWechatOrder()
    {
        $arr = [
            'return_code' => 'SUCCESS',
            'result_code' => 'SUCCESS',
            'appid' => 'test',
            'mch_id' => 'test',
            'prepay_id' => 'wx201410272009395522657a690389285100',
            'trade_type' => 'APP',
            'plan_id' => '123',
            'out_trade_no' => '123456'
        ];
        $sign = AuthWechat::getWechatSign($arr);
        $xml = <<<XML
<xml>
 <return_code><![CDATA[SUCCESS]]></return_code>
 <result_code><![CDATA[SUCCESS]]></result_code>
 <appid><![CDATA[test]]></appid>
 <mch_id><![CDATA[test]]></mch_id>
 <prepay_id><![CDATA[wx201410272009395522657a690389285100]]></prepay_id>
 <sign><![CDATA[$sign]]></sign>
 <trade_type><![CDATA[APP]]></trade_type>
 <plan_id><![CDATA[123]]></plan_id>
 <out_trade_no><![CDATA[123456]]></out_trade_no>
</xml>
XML;
        Client::registerMockResponseFunc('pay/contractorder', function ($method, $options) use ($xml) {
            return new Response(200, [], $xml);
        });

        $api = '/vip/create-wechat-order';

        // 测试用户禁止充值和消费
        $param = ['vip_id' => 1];
        self::loginByUserId(558);
        $this->assertThrowsWithMessage(HttpException::class, '您的账号暂被系统停封，不可进行充值消费操作',
            function () use ($api, $param) {
                Request::api($api, $param, Request::POST, [
                    'Cookie' => 'equip_id=' . self::TEST_EQUIP_ID,
                    'user_agent' => 'MissEvanApp/6.2.4 (Android;7.0;Meizu M6 M6)'
                ]);
            },
            403
        );

        // 测试价目 ID 是 iOS 的价目 ID 时
        $param = ['product_id' => 1];
        self::loginByUserId(self::TEST_USER_ID);
        $this->assertThrowsWithMessage(HttpException::class, '会员价目不存在',
            function () use ($api, $param) {
                Request::api($api, $param, Request::POST, [
                    'Cookie' => 'equip_id=' . self::TEST_EQUIP_ID,
                    'user_agent' => 'MissEvanApp/6.2.4 (Android;7.0;Meizu M6 M6)'
                ]);
            },
            404
        );

        // 测试正常情况
        $param = ['vip_id' => 6];
        self::loginByUserId(self::TEST_USER_ID);
        $result = Request::api($api, $param, Request::POST, [
            'Cookie' => 'equip_id=' . self::TEST_EQUIP_ID,
            'user_agent' => 'MissEvanApp/6.2.4 (Android;7.0;Meizu M6 M6)'
        ]);
        $this->assertIsArray($result);
        $this->assertArrayHasKeys(['trade_no', 'wechatpay_body'], $result);
        $this->assertNotEmpty($result['trade_no']);
        $this->assertNotEmpty($result['wechatpay_body']);
        $this->assertArrayHasKeys(['appid', 'partnerid', 'prepayid', 'package', 'noncestr', 'timestamp', 'sign'],
            $result['wechatpay_body']);
        $this->assertEquals($arr['appid'], $result['wechatpay_body']['appid']);
        $this->assertEquals($arr['mch_id'], $result['wechatpay_body']['partnerid']);
        $this->assertEquals($arr['prepay_id'], $result['wechatpay_body']['prepayid']);
        $this->assertEquals('Sign=WXPay', $result['wechatpay_body']['package']);
        $this->assertNotEmpty($result['wechatpay_body']['noncestr']);
        $this->assertNotEmpty($result['wechatpay_body']['timestamp']);
        $this->assertNotEmpty($result['wechatpay_body']['sign']);
    }

    public function testActionOrderDetail()
    {
        $api = '/vip/order-detail';
        self::loginByUserId(self::TEST_USER_ID);

        // 测试参数错误
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () use ($api) {
            Request::api($api, ['trade_no' => 'test']);
        }, 400);

        // 测试连续订阅订单详情
        $result = Request::api($api, ['trade_no' => self::$vip_fee_deducted_record_1->getTradeNo()]);
        $this->assertIsArray($result);
        $this->assertArrayHasKeys(['deducted_record', 'sign_agreement'], $result);
        $this->assertIsArray($result['deducted_record']);
        $this->assertArrayHasKeys(['id', 'create_time', 'modified_time', 'user_id', 'vip_id', 'sign_agreement_id', 'pay_type', 'price', 'status', 'next_deduct_time'],
            $result['deducted_record']);
        $this->assertArrayHasKeys(['id', 'create_time', 'modified_time', 'user_id', 'vip_id', 'pay_type', 'status', 'start_time', 'expire_time'],
            $result['sign_agreement']);

        // 测试单次付费订单详情
        $result = Request::api($api, ['trade_no' => self::$vip_fee_deducted_record_2->getTradeNo()]);
        $this->assertIsArray($result);
        $this->assertArrayHasKey('deducted_record', $result);
        $this->assertArrayNotHasKey('sign_agreement', $result);
        $this->assertIsArray($result['deducted_record']);
        $this->assertArrayHasKeys(['id', 'create_time', 'modified_time', 'user_id', 'vip_id', 'sign_agreement_id', 'pay_type', 'price', 'status', 'next_deduct_time'],
            $result['deducted_record']);
    }

    public function testActionClaimDiamonds()
    {
        TopupMenu::deleteAll(['device' => TopupMenu::DEVICE_VIP, 'scope' => TopupMenu::SCOPE_VIP]);
        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_CLAIM_VIP_DIAMONDS, self::TEST_USER3_ID, date('Ymd'));
        $redis->unlock($lock);
        $equip_id = Yii::$app->equip->getEquipId();
        $equip_key = $redis->generateKey(COUNTER_CLAIM_VIP_DIAMONDS_EQUIP, $equip_id, date('Ymd'));
        $ip = Yii::$app->request->userIP;
        $ip_key = $redis->generateKey(COUNTER_CLAIM_VIP_DIAMONDS_IP, $ip, date('Ymd'));
        $redis->unlink($equip_key, $ip_key);

        $api = '/vip/claim-diamonds';
        self::loginByUserId(self::TEST_USER2_ID);

        // 测试用户非会员无法领取
        $this->assertThrowsWithMessage(HttpException::class, '开通/续费会员享受专属权益',
            function () use ($api) {
                Request::api($api, [], Request::POST);
            });

        // 测试用户正常领取会员福利钻石
        Data::createCcy([
            'device' => TopupMenu::DEVICE_VIP,
            'scope' => TopupMenu::SCOPE_VIP,
            'num' => 5,
        ]);
        self::loginByUserId(self::TEST_USER3_ID);
        $return = Request::api($api, [], Request::POST);
        $this->assertIsObject($return);
        $this->assertEquals(new SuccessResponseWithMessage(null, '领取成功'), $return);

        // 测试无法重复领取
        $this->assertThrowsWithMessage(HttpException::class, '今天已经领过了',
            function () use ($api) {
                Request::api($api, [], Request::POST);
            });

        // 测试设备领取次数达到上限
        $redis->unlock($lock);
        VipReceiveCoinLog::deleteAll(['user_id' => self::TEST_USER3_ID]);
        $return = Request::api($api, [], Request::POST);
        $this->assertIsObject($return);
        $this->assertEquals(new SuccessResponseWithMessage(null, '领取成功'), $return);

        $redis->unlock($lock);
        VipReceiveCoinLog::deleteAll(['user_id' => self::TEST_USER3_ID]);
        $this->assertThrowsWithMessage(HttpException::class, '已达到每日领取上限',
            function () use ($api) {
                Request::api($api, [], Request::POST);
            });
    }
}
