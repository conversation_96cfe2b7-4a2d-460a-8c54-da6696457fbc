<?php

namespace tests\controllers;

use app\components\util\Go;
use app\components\util\MUtils;
use app\controllers\PersonController;
use app\models\Drama;
use app\models\Feed;
use app\models\Live;
use app\models\MAlbum;
use app\models\MAttentionUser;
use app\models\MThirdPartyTask;
use app\models\UserCertification;
use app\models\MLikeSound;
use app\models\Mowangskuser;
use app\models\MPointFeed;
use app\models\MReport;
use app\models\MSobotUser;
use app\models\MSound;
use app\models\MHomepageIcon;
use app\models\UserAddendum;
use app\models\UserSignHistory;
use missevan\rpc\LiveRpc;
use missevan\rpc\Rpc;
use missevan\rpc\RpcNetworkException;
use missevan\rpc\RpcResult;
use missevan\rpc\ServiceRpc;
use tests\components\UnitTestCase;
use tests\components\util\Data;
use tests\components\util\DataBus;
use tests\components\util\Equipment;
use tests\components\web\Request;
use Yii;
use yii\db\Exception;
use yii\db\Expression;
use yii\helpers\Json;
use yii\web\HttpException;
use yii\web\MethodNotAllowedHttpException;
use ReflectionClass;

class PersonControllerTest extends UnitTestCase
{
    // 我的页图标，测试 equip id
    const TEST_HOMEPAGE_ICONS_EQUIP_ID = '5ae93485-test-b665-9ada-a44e4dda48ae';
    const TEST_VISITOR_ID_1 = 'test_visitor_id_1';
    const TEST_VISITOR_ID_2 = 'test_visitor_id_2';
    const TEST_VISITOR_ID_3 = 'test_visitor_id_3';

    public static $test_album_id;
    private static $test_sound_id;

    private static $memcache;
    private static $redis;

    private static $origin_db;
    private static $origin_maindb;
    private static $origin_paydb;

    protected function _before()
    {
        parent::_before();
        self::$origin_db = Yii::$app->db;
        self::$origin_maindb = Yii::$app->db1;
        self::$origin_paydb = Yii::$app->paydb;
        Yii::$app->set('db1', Yii::$app->sqlitedb1);
    }

    protected function _after()
    {
        parent::_after();
        Yii::$app->set('db', self::$origin_db);
        Yii::$app->set('db1', self::$origin_maindb);
        Yii::$app->set('paydb', self::$origin_paydb);
    }

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        self::$memcache = Yii::$app->memcache;
        self::$redis = Yii::$app->redis;
        self::cleanData();

        $confirm = Mowangskuser::CONFIRM_GOLDEN_VIP << Mowangskuser::VIP_BIT_OFFSET_MASK[0];
        Mowangskuser::updateAll(['confirm' => new Expression('confirm | :confirm',
            [':confirm' => $confirm])], ['id' => self::TEST_USER2_ID]);
    }

    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
        self::cleanData();
    }

    public static function cleanData()
    {
        MSound::deleteAll(['user_id' => self::TEST_USER2_ID]);
        // 恢复用户注销之前状态
        Mowangskuser::updateAll(['confirm' => 0, 'follownum' => 0], 'id = :id', [':id' => self::TEST_USER_ID]);
        // 清理测试用户的关注数据
        MAttentionUser::deleteAll('user_active = :user_active OR user_passtive = :user_passtive', [
            ':user_active' => self::TEST_USER_ID,
            ':user_passtive' => self::TEST_USER2_ID
        ]);
        Mowangskuser::updateAll(['fansnum' => 0], 'id = :id', [':id' => self::TEST_USER2_ID]);

        self::$redis->del(self::$redis->generateKey(LOCK_USER_ID_ATTENTION_ID, self::TEST_USER_ID,
            self::TEST_USER2_ID));

        if (self::$test_album_id) {
            MAlbum::deleteAll(['id' => self::$test_album_id]);
        }
        // 删除用户直播间
        $ROOM_ID = 989898;
        Live::deleteAll(['user_id' => self::TEST_USER3_ID, 'room_id' => $ROOM_ID]);
        // 删除测试音频
        if (self::$test_sound_id) {
            MSound::deleteAll(['id' => self::$test_sound_id]);
        }
        MLikeSound::deleteAll(['user_id' => self::TEST_USER2_ID, 'sound_id' => self::$test_sound_id]);

        UserAddendum::deleteAll(['id' => [self::TEST_USER_ID, self::TEST_USER3_ID]]);
        Mowangskuser::updateAll(['point' => 350], ['id' => self::TEST_USER3_ID]);
        UserSignHistory::deleteAll(['user_id' => [self::TEST_USER_ID, self::TEST_USER3_ID]]);

        $key = self::$redis->generateKey(TASK, self::TEST_USER3_ID);
        self::$redis->unlock($key);

        $confirm = Mowangskuser::CONFIRM_GOLDEN_VIP << Mowangskuser::VIP_BIT_OFFSET_MASK[0];
        Mowangskuser::updateAll(['confirm' => new Expression('confirm &~ :confirm',
            [':confirm' => $confirm])], ['id' => self::TEST_USER2_ID]);
        UserCertification::deleteAll(['user_id' => self::TEST_USER2_ID]);

        self::$redis->del(
            self::$redis->generateKey(KEY_NEW_DEVICE_EQUIP_ID, self::TEST_HOMEPAGE_ICONS_EQUIP_ID)
        );
    }

    public function testUserFeed()
    {
        $this->login();
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () {
            Request::api('/person/user-feed', ['feed_type' => 9]);
        });

        $data = Request::api('/person/user-feed', ['feed_type' => Feed::FEED_TYPE_USER_CHANNEL_DRAMA]);
        $this->assertArrayHasKey('pagination', $data);
        $this->assertArrayHasKey('has_more', $data['pagination']);
        // 测试 iOS 4.8.5，安卓 5.7.2 以下的版本下发 Datas 兼容老的数据类型
        $this->assertArrayHasKey('Datas', $data);
        // 测试是否存入 memcache，且缓存存的是 data
        $memcache = Yii::$app->memcache;
        $user_feed_key = MUtils::generateCacheKey(KEY_USER_FEED, self::TEST_USER_ID,
            Feed::FEED_TYPE_USER_CHANNEL_DRAMA, 0);
        $data['data'] = $data['Datas'];
        unset($data['Datas']);
        $this->assertEquals($data, Json::decode($memcache->get($user_feed_key)));

        // 测试直播动态
        $data = Request::api('/person/user-feed', ['feed_type' => Feed::FEED_TYPE_USER_CHANNEL_DRAMA],
            Request::GET, ['User-Agent' => 'MissEvanApp/4.8.5 (iOS;12.1.4;iPhone10,3)']);
        $this->assertNotEmpty($data);
        $this->assertEquals('live', $data['data'][0]['type']);
        $this->assertEquals(1, $data['data'][0]['user_id']);
    }

    private function getSignature(string $str)
    {
        return hash_hmac('sha1', $str, Yii::$app->params['feedBackKey'], true);
    }

    public function testFeedBack()
    {
        $test_equip_id = self::TEST_EQUIP_ID;
        $params = [
            'timestamp' => time(),
            'content' => '测试'
        ];
        // 测试安卓 5.1.8 版本
        $str = $test_equip_id . $params['timestamp'] . Equipment::Android . $params['content'];
        $params['signature'] = base64_encode($this->getSignature($str));
        $data = Request::api('/person/feed-back', $params, Request::POST, [
            'Cookie' => 'equip_id=' . $test_equip_id,
            'User-Agent' => 'MissEvanApp/5.1.8 (Android;7.0;Meizu M6 M6)',
        ]);
        $this->assertEquals('我们已经收到你的意见', $data);
        // 测试 iOS 4.2.7 版本
        $str = $test_equip_id . $params['timestamp'] . Equipment::iOS . $params['content'];
        $params['signature'] = base64_encode($this->getSignature($str));
        $data = Request::api('/person/feed-back', $params, Request::POST, [
            'Cookie' => 'equip_id=' . $test_equip_id,
            'User-Agent' => 'MissEvanApp/4.2.7 (iOS;12.1.4;iPhone10,3)',
        ]);
        $this->assertEquals('我们已经收到你的意见', $data);
        // 测试验证错误
        $params['signature'] = null;
        $this->assertThrowsWithMessage(HttpException::class, '验证错误', function () use ($params, $test_equip_id) {
            Request::api('/person/feed-back', $params, Request::POST, [
                'Cookie' => 'equip_id=' . $test_equip_id,
                'User-Agent' => 'MissEvanApp/4.2.7 (iOS;12.1.4;iPhone10,3)',
            ]);
        });
    }

    /**
     * 获取用户信息
     *
     * @throws \yii\base\InvalidRouteException
     * @throws \yii\console\Exception
     * @throws \Exception
     */
    public function testActionGetUserInfo()
    {
        Rpc::registerRpcApiResponseFunc(ServiceRpc::API_LIST_AVATAR_FRAME, function () {
            return new RpcResult([]);
        });

        // 预期未登录时，返回需要登录
        $this->assertThrowsWithMessage(HttpException::class, '需要登录', function () {
            Request::api('/person/get-user-info');
        });

        // 登录时获取登录用户信息
        self::loginByUserId();
        $data = Request::api('/person/get-user-info');
        $this->assertArrayHasKey('ip_location', $data);
        $this->assertEquals(self::TEST_USER_ID, $data['id'], '预期不传参时，获取用户本人信息');

        // 传递 user_id 参数时获取他人用户信息
        Data::createUserCertification([
            'user_id' => self::TEST_USER2_ID,
            'subtitle' => '2023 百大 UP 主',
        ]);
        $data = Request::api('/person/get-user-info', ['user_id' => self::TEST_USER2_ID], Request::GET, [
            'User-Agent' => 'MissEvanApp/6.0.7 (iOS;12.1.4;iPhone10,3)'
        ]);
        $this->assertEquals(self::TEST_USER2_ID, $data['id'], '预期获取特定用户的信息');
        // 断言低于 6.0.8 版本会返回 authenticated、title 字段
        $this->assertArrayHasKeys(['authenticated', 'title', 'auth_info', 'live', 'ip_location'], $data);
        $except_auth_info = [
            'type' => Mowangskuser::CONFIRM_GOLDEN_VIP,
            'title' => '个人认证',
            'subtitle' => '2023 百大 UP 主',
        ];
        $this->assertEquals($except_auth_info, $data['auth_info']);

        // 测试 6.0.8 之后版本不会返回 authenticated、title 字段：用户有认证头衔
        $data = Request::api('/person/get-user-info', ['user_id' => self::TEST_USER2_ID], Request::GET, [
            'User-Agent' => 'MissEvanApp/6.0.8 (Android;7.0;Meizu M6 M6)'
        ]);
        $this->assertArrayHasKeys(['auth_info', 'live', 'ip_location'], $data);
        $this->assertArrayNotHasKeys(['authenticated', 'title', 'avatar_frame_url', 'avatar_frame'], $data);
        $this->assertEquals($except_auth_info, $data['auth_info']);

        // 测试无头像框的情况
        $data = Request::api('/person/get-user-info', ['user_id' => self::TEST_USER2_ID], Request::GET, [
            'User-Agent' => 'MissEvanApp/6.1.4 (Android;7.0;Meizu M6 M6)'
        ]);
        $this->assertArrayNotHasKeys(['avatar_frame_url', 'avatar_frame'], $data);

        // 测试用户有头像框的情况
        Rpc::registerRpcApiResponseFunc(ServiceRpc::API_LIST_AVATAR_FRAME, function ($data) {
            $res = [];
            foreach ($data['user_ids'] as $k => $user_id) {
                $res[$user_id] = [
                    'id' => $k + 1,
                    'name' => "头像框名称 {$k}",
                    'avatar_frame_url' => 'https://test.com/test.png',
                ];
            }
            return new RpcResult($res);
        });
        // 6.1.4 版本以下仅下发头像框地址字段
        $data = Request::api('/person/get-user-info', ['user_id' => self::TEST_USER2_ID], Request::GET, [
            'User-Agent' => 'MissEvanApp/6.1.3 (Android;7.0;Meizu M6 M6)'
        ]);
        $this->assertArrayHasKey('avatar_frame_url', $data);
        $this->assertArrayNotHasKey('avatar_frame', $data);
        // 6.1.4 及以上版本下发头像框信息字段
        $data = Request::api('/person/get-user-info', ['user_id' => self::TEST_USER2_ID], Request::GET, [
            'User-Agent' => 'MissEvanApp/6.1.4 (Android;7.0;Meizu M6 M6)'
        ]);
        $this->assertArrayHasKey('avatar_frame', $data);
        $this->assertArrayNotHasKey('avatar_frame_url', $data);

        // 测试 6.0.8 之后版本不会返回 authenticated、title 字段：用户无认证头衔
        UserCertification::deleteAll(['user_id' => self::TEST_USER2_ID]);
        $data = Request::api('/person/get-user-info', ['user_id' => self::TEST_USER2_ID], Request::GET, [
            'User-Agent' => 'MissEvanApp/6.0.8 (Android;7.0;Meizu M6 M6)'
        ]);
        $this->assertArrayHasKeys(['auth_info', 'live', 'ip_location'], $data);
        $this->assertArrayNotHasKeys(['authenticated', 'title'], $data);
        $except_auth_info = [
            'type' => Mowangskuser::CONFIRM_GOLDEN_VIP,
            'title' => 'UP 主认证',
        ];
        $this->assertEquals($except_auth_info, $data['auth_info']);

        // 测试用户被注销情况
        // 设置用户为已注销
        Mowangskuser::updateAll(['confirm' => new Expression('confirm | :confirm',
            [':confirm' => Mowangskuser::CONFIRM_DELETED])], 'id = :id', [':id' => self::TEST_USER_ID]);
        $this->assertThrowsWithMessage(HttpException::class, '账号已注销', function () {
            Request::api('/person/get-user-info', ['user_id' => self::TEST_USER_ID], Request::GET, [
                'User-Agent' => 'MissEvanApp/4.5.9 (iOS;12.1.4;iPhone10,3)'
            ]);
        }, 403, 300010005);

        // 恢复用户注销之前状态
        Mowangskuser::updateAll(['confirm' => new Expression('confirm &~ :confirm',
            [':confirm' => Mowangskuser::CONFIRM_DELETED])], 'id = :id', [':id' => self::TEST_USER_ID]);
    }

    public function testGetUserPoint()
    {
        // 未登录
        $this->assertThrowsWithMessage(HttpException::class, '需要登录', function () {
            Request::api('/person/get-user-point');
        });

        // 正常返回
        self::loginByUserId(self::TEST_USER_ID);
        $data = Request::api('/person/get-user-point');
        $this->assertIsArray($data);
        $this->assertArrayHasKey('point', $data);
        $this->assertIsInt($data['point']);
    }

    public function testGetSobotUser()
    {
        // 未登录
        $this->assertThrowsWithMessage(HttpException::class, '需要登录', function () {
            Request::api('/person/get-sobot-user');
        });

        // 获取到旧的 partnerId
        self::loginByUserId();
        Data::createUserAddendum(['id' => Data::TEST_USER_ID, 'sobot' => $_SERVER['REQUEST_TIME'] - ONE_WEEK]);
        $data = Request::api('/person/get-sobot-user');
        $this->assertIsArray($data);
        $this->assertEquals(['partner_id' => self::TEST_USER_ID], $data);

        // 获取到新的 partnerId
        $sobot = $_SERVER['REQUEST_TIME'] - THIRTY_DAYS - ONE_WEEK;
        UserAddendum::updateAll(['sobot' => $sobot], ['id' => self::TEST_USER_ID]);
        $data = Request::api('/person/get-sobot-user');
        $this->assertIsArray($data);
        $this->assertEquals(['partner_id' => '34283928dd7cf47d75cb7267953ccc13'], $data);
    }

    public function testFollow()
    {
        $TEST_ATTENTION_TYPE = 1;
        $TEST_CANCEL_TYPE = 0;

        self::loginByUserId(self::TEST_USER_ID);
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.7.8 (iOS;13.5;iPhone9,1)']);

        // 测试关注
        $data = Request::api('/person/follow', ['type' => $TEST_ATTENTION_TYPE, 'user_id' => self::TEST_USER2_ID],
            Request::POST);
        $this->assertIsArray($data);
        $this->assertEquals('关注成功', $data['msg']);
        $this->assertTrue($data['attention']);
        // 测试取消关注
        $data = Request::api('/person/follow', ['type' => $TEST_CANCEL_TYPE, 'user_id' => self::TEST_USER2_ID],
            Request::POST);
        $this->assertIsArray($data);
        $this->assertEquals('取消关注成功', $data['msg']);
        $this->assertFalse($data['attention']);

        // 测试参数错误
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () {
            $TEST_ERROR_TYPE = 3;
            Request::api('/person/follow', ['type' => $TEST_ERROR_TYPE, 'user_id' => self::TEST_USER2_ID],
                Request::POST);
        });
        // 测试关注自己
        $this->assertThrowsWithMessage(HttpException::class, '不能关注自己',
            function () use ($TEST_ATTENTION_TYPE) {
                Request::api('/person/follow', ['type' => $TEST_ATTENTION_TYPE, 'user_id' => self::TEST_USER_ID],
                    Request::POST);
            }
        );

        // 测试兼容当前 rpc 返回数据类型为 string
        // 关注
        $data = Request::api('/person/follow', ['type' => $TEST_ATTENTION_TYPE, 'user_id' => 1],
            Request::POST);
        $this->assertIsArray($data);
        $this->assertEquals('关注成功', $data['msg']);
        $this->assertTrue($data['attention']);

        // 取关
        $data = Request::api('/person/follow', ['type' => $TEST_CANCEL_TYPE, 'user_id' => 1],
            Request::POST);
        $this->assertIsArray($data);
        $this->assertEquals('取消关注成功', $data['msg']);
        $this->assertFalse($data['attention']);
    }

    /**
     * 用户举报
     *
     * @throws \Throwable
     * @throws \yii\base\InvalidRouteException
     * @throws \yii\console\Exception
     * @throws \yii\db\StaleObjectException
     */
    public function testActionReport()
    {
        Yii::$app->set('db', Yii::$app->sqlitedb);

        // 期望出现请求不是 POST 的提示
        $this->assertThrowsWithMessage(
            MethodNotAllowedHttpException::class,
            'Method Not Allowed. This URL can only handle the following request methods: POST.',
            function () {
                Request::api('/person/report', ['type' => 3, 'id' => 2, 'reason' => 1]);
            });

        $this->assertThrowsWithMessage(HttpException::class, '需要登录',
            function () {
                Request::api('/person/report', ['type' => 3, 'id' => 2, 'reason' => 1], Request::POST);
            });

        $this->loginByUserId();
        // 测试举报的音单不存在
        $msg = Yii::t('app/error', 'Report content does not exist');
        $this->assertThrowsWithMessage(HttpException::class, $msg, function () {
            Request::api('/person/report', [
                'type' => MReport::TARGET_ALBUM,
                'id' => 999999999,
                'reason' => MReport::REASON_AD,
                'content' => '测试'
            ], Request::POST);
        });
        // 测试举报音单成功
        $album = Data::createTestAlbum();
        self::$test_album_id = $album->id;
        $data = Request::api('/person/report', [
            'type' => MReport::TARGET_ALBUM,
            'id' => self::$test_album_id,
            'reason' => MReport::REASON_AD,
            'content' => '测试'
        ], Request::POST);
        $this->assertEquals(Yii::t('app/base', 'Report successfully'), $data);

        // 测试不能举报自己
        // 创建用户直播间
        $ROOM_ID = 989898;
        $this->loginByUserId(self::TEST_USER3_ID);
        Data::createLive(['id' => self::TEST_USER3_ID, 'user_id' => self::TEST_USER3_ID, 'room_id' => $ROOM_ID,
            'status' => Live::STATUS_OPEN]);
        $this->assertThrowsWithMessage(HttpException::class, '不能举报自己哦',
            function () use ($ROOM_ID) {
                Request::api('/person/report', [
                    'type' => MReport::TARGET_LIVE,
                    'id' => $ROOM_ID,
                    'reason' => MReport::REASON_AD,
                    'content' => '不能举报自己哦'
                ], Request::POST);
            });
        // 删除用户直播间
        Live::deleteAll(['user_id' => self::TEST_USER3_ID, 'room_id' => $ROOM_ID]);

        // 测试个人信息举报（举报自己）
        $this->assertThrowsWithMessage(HttpException::class, '不能举报自己哦',
            function () {
                Request::api('/person/report', [
                    'type' => MReport::TARGET_USER_INFO,
                    'id' => self::TEST_USER3_ID,
                    'reason' => MReport::REASON_OTHER,
                    'content' => '头像违规；昵称违规；签名违规'
                ], Request::POST);
            });

        // 测试个人信息举报（举报内容为空）
        $this->assertThrowsWithMessage(HttpException::class, '请选择举报内容',
            function () {
                Request::api('/person/report', [
                    'type' => MReport::TARGET_USER_INFO,
                    'id' => self::TEST_USER2_ID,
                    'reason' => MReport::REASON_OTHER,
                    'content' => ''
                ], Request::POST);
            });

        // 测试个人信息举报成功
        $data = Request::api('/person/report', [
            'type' => MReport::TARGET_USER_INFO,
            'id' => self::TEST_USER2_ID,
            'reason' => MReport::REASON_OTHER,
            'content' => '头像违规；昵称违规；签名违规'
        ], Request::POST);
        $this->assertEquals(Yii::t('app/base', 'Report successfully'), $data);
    }

    public function testGetRewardDiamondNum()
    {
        $redis = Yii::$app->redis;
        $key_tb = $redis->generateKey(COUNTER_ADDITIONAL_TASK_FINISHED, date('Ymd'), PersonController::TASK_TYPE_TB);
        $key_live = $redis->generateKey(COUNTER_LIVE_TASK_DIAMOND, date('Ymd'));
        $redis->del($key_tb, $key_live);

        // 测试任务正常配置随机奖励
        $ref_class = new ReflectionClass(PersonController::class);
        $instance = $ref_class->newInstance('person', 'test');
        $param = [7, ['weights' => [10 => 1, 20 => 5], 'random_limit' => 1], 2];
        $num = self::invokePrivateMethod($ref_class, 'getRewardDiamondNum', $param, $instance, true);
        // 验证随机奖励范围
        $this->assertTrue(in_array($num, [10, 20]));
        // 发放计数累加
        $redis_num = $redis->get($key_tb);
        $this->assertEquals(1, $redis_num);

        // 测试达到限制后下发指定数量奖励
        $num = self::invokePrivateMethod($ref_class, 'getRewardDiamondNum', $param, $instance, true);
        $this->assertEquals(2, $num);
        // 发放计数仍然累加
        $redis_num = $redis->get($key_tb);
        $this->assertEquals(2, $redis_num);

        // 测试无随机奖励配置
        $param = [7, null, 2];
        $num = self::invokePrivateMethod($ref_class, 'getRewardDiamondNum', $param, $instance, true);
        $this->assertEquals(2, $num);
        // 发放计数未累加
        $redis_num = $redis->get($key_tb);
        $this->assertEquals(2, $redis_num);

        // 测试直播任务正常配置随机奖励
        $param = [PersonController::TASK_TYPE_LIVE_LISTEN_NEW_USER, ['weights' => [10 => 1, 20 => 5], 'random_limit' => 20], 2];
        $num = self::invokePrivateMethod($ref_class, 'getRewardDiamondNum', $param, $instance, true);
        // 验证随机奖励范围
        $this->assertTrue(in_array($num, [10, 20]));
        // 发放计数累加
        $redis_num = $redis->get($key_live);
        $this->assertEquals($num, $redis_num);

        // 测试直播任务达到限制后下发指定数量奖励
        $param = [PersonController::TASK_TYPE_LIVE_LISTEN_NEW_USER, ['weights' => [10 => 1, 20 => 5], 'random_limit' => 10], 2];
        $num2 = self::invokePrivateMethod($ref_class, 'getRewardDiamondNum', $param, $instance, true);
        $this->assertEquals(2, $num2);
        // 发放计数仍然累加
        $redis_num = $redis->get($key_live);
        $this->assertEquals($num + $num2, $redis_num);

        // 测试直播任务无随机奖励配置
        $param = [PersonController::TASK_TYPE_LIVE_LISTEN_NEW_USER, null, 2];
        $num3 = self::invokePrivateMethod($ref_class, 'getRewardDiamondNum', $param, $instance, true);
        $this->assertEquals(2, $num3);
        // 发放钻石计数未累加
        $redis_num = $redis->get($key_live);
        $this->assertEquals($num + $num2, $redis_num);
    }

    public function testGetRewardPointInfo()
    {
        // 随机规则为空
        $random_rule = [];
        $param = [$random_rule, 2, '好幸运，摸到了 %d 个小鱼干~'];
        $ref_class = new ReflectionClass(PersonController::class);
        $instance = $ref_class->newInstance('person', 'test');
        for ($i = 0; $i <= 10; $i++) {
            $return = self::invokePrivateMethod($ref_class, 'getRewardPointInfo', $param, $instance, true);
            $this->assertEquals(['num' => 2, 'finished_info' => '好幸运，摸到了 2 个小鱼干~'], $return);
        }

        // 随机规则非空
        $random_rule = [
            'weights' => [
                20 => 5,
                50 => 65,
            ],
            'finished_info_format' => [
                50 => '运气爆棚！摸到 %d 个小鱼干！',
            ]
        ];
        $param = [$random_rule, 2, '好幸运，摸到了 %d 个小鱼干'];
        $expected_1 = ['num' => 20, 'finished_info' => '好幸运，摸到了 20 个小鱼干'];
        $expected_2 = ['num' => 50, 'finished_info' => '运气爆棚！摸到 50 个小鱼干！'];
        $expected_array = [$expected_1, $expected_2];
        for ($i = 0; $i <= 10; $i++) {
            $return = self::invokePrivateMethod($ref_class, 'getRewardPointInfo', $param, $instance, true);
            $this->assertTrue(in_array($return, $expected_array));
        }
    }

    public function testIsFinishedLiveTask()
    {
        // 测试进入直播间任务完成
        $live_task = ['gtype' => PersonController::TASK_TYPE_LIVE_VISIT];
        $user_task = ['today_listen_duration' => 100];
        $ref_class = new ReflectionClass(PersonController::class);
        $instance = $ref_class->newInstance('person', 'test');
        $param = [$live_task, $user_task];
        $is_finished = self::invokePrivateMethod($ref_class, 'isFinishedLiveTask', $param, $instance, true);
        $this->assertTrue($is_finished);

        // 测试进入直播间任务未完成
        $param = [$live_task, []];
        $is_finished = self::invokePrivateMethod($ref_class, 'isFinishedLiveTask', $param, $instance, true);
        $this->assertFalse($is_finished);

        // 测试收听直播时长任务完成的情况
        $live_task = [
            'gtype' => PersonController::TASK_TYPE_LIVE_LISTEN_NEW_USER,
            'task_value' => 100
        ];
        $param = [$live_task, $user_task];
        $is_finished = self::invokePrivateMethod($ref_class, 'isFinishedLiveTask', $param, $instance, true);
        $this->assertTrue($is_finished);

        // 测试收听直播时长任务未完成
        $live_task['task_value'] = 101;
        $param = [$live_task, $user_task];
        $is_finished = self::invokePrivateMethod($ref_class, 'isFinishedLiveTask', $param, $instance, true);
        $this->assertFalse($is_finished);

        // 测试不存在的任务
        $live_task['gtype'] = 2333;
        $param = [$live_task, $user_task];
        $is_finished = self::invokePrivateMethod($ref_class, 'isFinishedLiveTask', $param, $instance, true);
        $this->assertFalse($is_finished);
    }

    public function testBuildThirdPartyUrlQueryToken()
    {
        $tests = [
            // 测试百度
            [
                'args' => [
                    'urls' => [
                        'native_url' => 'tbopen://m.baidu.com/index.html?a=1!(token)',
                        'web_url' => 'https://market.m.test.com/xx?token=!(token)'
                    ],
                    'token' => 'test',
                    'scene' => MThirdPartyTask::SCENE_BAIDU
                ],
                'expected' => [
                    'native_url' => 'tbopen://m.baidu.com/index.html?a=1test',
                    'web_url' => 'https://market.m.test.com/xx?token=test'
                ]
            ],
            // 测试携程
            [
                'args' => [
                    'urls' => [
                        'native_url' => 'tbopen://m.ctrip.com/index.html?a=1',
                        'web_url' => 'https://market.m.test.com/xx'
                    ],
                    'token' => 'test',
                    'scene' => MThirdPartyTask::SCENE_CTRIP
                ],
                'expected' => [
                    'native_url' => 'tbopen://m.ctrip.com/index.html?a=1&token=test',
                    'web_url' => 'https://market.m.test.com/xx?token=test'
                ]
            ],
            // 测试百度地图
            [
                'args' => [
                    'urls' => [
                        'native_url' => 'baidumap://map/index.html?a=1&user_token=${token}',
                        'web_url' => 'https://map.baidu.com/xx?token=${token}'
                    ],
                    'token' => 'test',
                    'scene' => MThirdPartyTask::SCENE_BAIDUMAP
                ],
                'expected' => [
                    'native_url' => 'baidumap://map/index.html?a=1&user_token=test',
                    'web_url' => 'https://map.baidu.com/xx?token=test'
                ]
            ],
            // 测试优酷
            [
                'args' => [
                    'urls' => [
                        'native_url' => 'youku://unic??a=1&token=${token}',
                        'web_url' => 'https://o.youku.com/m/fz8kgdflqu?unic_co=pha&token=${token}'
                    ],
                    'token' => 'test',
                    'scene' => MThirdPartyTask::SCENE_YOUKU
                ],
                'expected' => [
                    'native_url' => 'youku://unic??a=1&token=test',
                    'web_url' => 'https://o.youku.com/m/fz8kgdflqu?unic_co=pha&token=test'
                ]
            ],
            // 测试 QQ 浏览器
            [
                'args' => [
                    'urls' => [
                        'native_url' => 'mttbrowser://url=qb://ext/rn?otoken%3D${token} ,ChannelID=11001452,PosID=93127',
                        'web_url' => 'https://quickstart.imtt.qq.com/qb/din/qb/QBforNEWuser-missevan/index.html'
                    ],
                    'token' => 'test',
                    'scene' => MThirdPartyTask::SCENE_QQ_BROWSER
                ],
                'expected' => [
                    'native_url' => 'mttbrowser://url=qb://ext/rn?otoken%3Dtest ,ChannelID=11001452,PosID=93127',
                    'web_url' => 'https://quickstart.imtt.qq.com/qb/din/qb/QBforNEWuser-missevan/index.html'
                ]
            ],
            // 测试微博无特殊字符
            [
                'args' => [
                    'urls' => [
                        'native_url' => 'sinaweibo://browser?luicode=10000360&tToken%3D${token}%26luicode%3D10000360',
                        'web_url' => 'https://m.weibo.cn/feature/openapp?tToken%253D${token}'
                    ],
                    'token' => 'test',
                    'scene' => MThirdPartyTask::SCENE_WEIBO
                ],
                'expected' => [
                    'native_url' => 'sinaweibo://browser?luicode=10000360&tToken%3Dtest%26luicode%3D10000360',
                    'web_url' => 'https://m.weibo.cn/feature/openapp?tToken%253Dtest'
                ]
            ],
            // 测试微博有特殊字符
            [
                'args' => [
                    'urls' => [
                        'native_url' => 'sinaweibo://browser?luicode=10000360&tToken%3D${token}%26luicode%3D10000360',
                        'web_url' => 'https://m.weibo.cn/feature/openapp?tToken%253D${token}'
                    ],
                    'token' => 'tes t',
                    'scene' => MThirdPartyTask::SCENE_WEIBO
                ],
                'expected' => [
                    'native_url' => 'sinaweibo://browser?luicode=10000360&tToken%3Dtes%2Bt%26luicode%3D10000360',
                    'web_url' => 'https://m.weibo.cn/feature/openapp?tToken%253Dtes%252Bt'
                ]
            ],
        ];
        $ref_class = new ReflectionClass(PersonController::class);
        $instance = $ref_class->newInstance('person', 'test');
        foreach ($tests as $test) {
            $data = self::invokePrivateMethod($ref_class, 'buildThirdPartyUrlQueryToken', array_values($test['args']),
                $instance, true);
            $this->assertEquals($test['expected'], $data);
        }
    }

    public function testAddQueryParamsToUrl()
    {
        $tests = [
            // 测试已有 query 参数
            [
                'args' => ['url' => 'tbopen://m.baidu.com/index.html?a=1', 'param' => ['token' => 'test']],
                'expected' => 'tbopen://m.baidu.com/index.html?a=1&token=test'
            ],
            // 测试没有 query 参数
            [
                'args' => ['url' => 'tbopen://m.baidu.com/tbopen/index.html', 'param' => ['token' => 'test']],
                'expected' => 'tbopen://m.baidu.com/tbopen/index.html?token=test'
            ],
        ];
        $ref_class = new ReflectionClass(PersonController::class);
        $instance = $ref_class->newInstance('person', 'test');
        foreach ($tests as $test) {
            $data = self::invokePrivateMethod($ref_class, 'addQueryParamsToUrl', array_values($test['args']),
                $instance, true);
            $this->assertEquals($test['expected'], $data);
        }
    }

    public function testFilterAdditionalTaskResp()
    {
        $task = [
            'gtype' => 7,
            'name' => '免费领钻石',
            'reward_type' => 2,
            'reward_num' => 5,
            'info' => '每天完成从猫耳FM进入淘宝农场，必得 5 钻',
            'finished_info_format' => '恭喜获得 %d 钻',
            'icon_url' => 'https://abc.png',
            'dark_icon_url' => 'https://abc.png',
            'button_text' => '去完成',
            'native_url' => 'tbopen://m.taobao.com/tbopen/index.html?foo=bar',
            'web_url' => 'https://market.m.test.com/xx?foo=bar',
            'event_id' => 799,
            'diamond_random_rule' => [
                'random_limit' => 100000,
                'weights' => [
                    1 => 3470,
                    2 => 5000,
                    3 => 1000,
                    4 => 200,
                    5 => 100,
                    6 => 100,
                    7 => 60,
                    8 => 40,
                    9 => 20,
                    10 => 10,
                ],
            ],
            'finish' => 1,
            'to_third_party' => false,
        ];
        $to_third_party_task = [
            'gtype' => 7,
            'name' => '免费领钻石',
            'reward_type' => 2,
            'reward_num' => 5,
            'info' => '每天完成从猫耳FM进入淘宝农场，必得 5 钻',
            'finished_info_format' => '恭喜获得 %d 钻',
            'icon_url' => 'https://abc.png',
            'dark_icon_url' => 'https://abc.png',
            'button_text' => '去完成',
            'native_url' => 'tbopen://m.taobao.com/tbopen/index.html?foo=bar',
            'web_url' => 'https://market.m.test.com/xx?foo=bar',
            'event_id' => 799,
            'diamond_random_rule' => [
                'random_limit' => 100000,
                'weights' => [
                    1 => 3470,
                    2 => 5000,
                    3 => 1000,
                    4 => 200,
                    5 => 100,
                    6 => 100,
                    7 => 60,
                    8 => 40,
                    9 => 20,
                    10 => 10,
                ],
            ],
            'finish' => 1,
            'button_texts' => [
                '去完成',
                '领钻石',
                '已完成',
            ],
            'to_third_party' => true,
        ];
        // 测试 iOS ≥ 6.2.7 Android ≥ 6.2.7 非到端任务
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.2.7 (Android;9.0;Meizu M6)']);
        $ref_class = new ReflectionClass(PersonController::class);
        $instance = $ref_class->newInstance('person', 'test');
        $param = [$task, false];
        $filter_task = self::invokePrivateMethod($ref_class, 'filterAdditionalTaskResp', $param, $instance, true);
        $expect_fields = [
            'gtype', 'name', 'info', 'reward_type',
            'icon_url', 'button_text', 'native_url',
            'web_url', 'finish', 'dark_icon_url', 'to_third_party'
        ];
        $this->assertEquals($expect_fields, array_keys($filter_task));

        // 测试 iOS ≥ 6.2.7 Android ≥ 6.2.7 到端任务
        $ref_class = new ReflectionClass(PersonController::class);
        $instance = $ref_class->newInstance('person', 'test');
        $param = [$to_third_party_task, false];
        $filter_task = self::invokePrivateMethod($ref_class, 'filterAdditionalTaskResp', $param, $instance, true);
        $this->assertNotNull($filter_task);
        $expect_fields = [
            'gtype', 'name', 'info', 'reward_type',
            'icon_url', 'button_text', 'finish', 'dark_icon_url', 'to_third_party',
        ];
        $this->assertEquals($expect_fields, array_keys($filter_task));

        // 测试 iOS < 6.2.7 Android < 6.2.7 非到端任务
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.2.6 (iOS;13.5;iPhone9,1)']);
        $ref_class = new ReflectionClass(PersonController::class);
        $instance = $ref_class->newInstance('person', 'test');
        $param = [$task, true];
        $filter_task = self::invokePrivateMethod($ref_class, 'filterAdditionalTaskResp', $param, $instance, true);
        $expect_fields = [
            'gtype', 'name', 'info', 'reward_type',
            'icon_url', 'button_text', 'native_url',
            'web_url', 'finish', 'dark_icon_url',
        ];
        $this->assertEquals($expect_fields, array_keys($filter_task));

        // 测试 iOS < 6.2.7 Android < 6.2.7 到端任务
        $ref_class = new ReflectionClass(PersonController::class);
        $instance = $ref_class->newInstance('person', 'test');
        $param = [$to_third_party_task, true];
        $filter_task = self::invokePrivateMethod($ref_class, 'filterAdditionalTaskResp', $param, $instance, true);
        $this->assertEquals($expect_fields, array_keys($filter_task));

        // 缺少配置
        unset($task['name']);
        $task = self::invokePrivateMethod($ref_class, 'filterAdditionalTaskResp', $task, $instance);
        $this->assertNull($task);
    }

    public function testFilterLiveTaskResp()
    {
        $tasks = [
            [
                'gtype' => 12,  // 任务类型
                'type' => PersonController::LIVE_TASK_TYPE_NORMAL,  // 直播任务类型 1：普通直播任务，2：直播发消息任务
                'name' => '【惊喜任务】收听直播',
                'reward_type' => 2,  // 奖励类型。1：小鱼干，2：钻石
                'reward_num' => 1,  // 每次完成任务可获得奖励数量
                'task_value' => 300000, // 收听直播 5 分钟，单位：毫秒
                'info' => '收听直播满 5min 可随机抽取 1-10 钻石哦~',  // 任务说明
                'finished_info_format' => '恭喜获得 %d 钻',  // 完成钻石任务获取奖励的提示。%d 为占位符，不要替换为具体数字
                'icon_url' => 'https://abc.png',  // 任务图标
                'dark_icon_url' => 'https://abc.png',  // 夜间模式任务图标
                'icon_url_scene_live' => 'https://yy.png',  // 任务图标，在直播间半窗下发
                'button_texts' => [
                    0 => '去完成',  // 未完成
                    1 => '抽钻石',  // 待领奖
                    2 => '已完成',  // 已领奖
                ],
                'button_texts_scene_live' => [
                    0 => '去完成',  // 未完成
                    1 => '抽钻石',  // 待领奖
                    2 => '已完成',  // 已领奖
                ],
                'event_id' => 801,  // 用于统计该任务每日发放钻石总量推送的活动 ID, 当不需要推送时可删除该配置或设置为 0
                'diamond_random_rule' => [  // 随机奖励发放规则，仅对钻石奖励生效。当不配置或数组为空时表示不支持随机奖励
                    'random_limit' => 100000,  // 随机钻石数量限制。当每自然日发放随机钻石数超出该配置时，固定发放 reward_num 个奖励，不配置或值为 0 时无限制
                    'weights' => [  // 奖励数量及其权重。key 为奖励数量，value 为该奖励的随机权重
                        1 => 3470,
                        2 => 5000,
                        3 => 1000,
                        4 => 200,
                        5 => 100,
                        6 => 100,
                        7 => 60,
                        8 => 40,
                        9 => 20,
                        10 => 10,
                    ],
                ],
                'finish' => 1,
                'url' => 'missevan://live/9075111'
            ],
            [
                'gtype' => 15,  // 任务类型
                'type' => PersonController::LIVE_TASK_TYPE_SEND_MESSAGE,  // 直播任务类型 1：普通直播任务，2：直播发消息任务
                'name' => '发消息',
                'reward_type' => 2,  // 奖励类型。1：小鱼干，2：钻石
                'info' => '每天进入直播间发 3 条消息可抽 1-10 钻',  // 任务说明
                'task_value' => 3,  // 任务达标值
                'progress' => 4,  // 任务达标值
                'icon_url' => 'https://abc.png',  // 任务图标
                'dark_icon_url' => 'https://abc.png',  // 夜间模式任务图标
                'icon_url_scene_live' => 'https://aa.png',  // 任务图标，在直播间半窗下发
                'button_texts' => [
                    0 => '去完成',  // 未完成
                    1 => '抽钻石',  // 待领奖
                    2 => '已完成',  // 已领奖
                ],
                'button_texts_scene_live' => [
                    0 => '去完成',  // 未完成
                    1 => '抽钻石',  // 待领奖
                    2 => '已完成',  // 已领奖
                ],
                'diamond_random_rule' => [  // 随机奖励发放规则，仅对钻石奖励生效。当不配置或数组为空时表示不支持随机奖励
                    'random_limit' => 100000,  // 随机钻石数量限制。当每自然日发放随机钻石数超出该配置时，固定发放 reward_num 个奖励，不配置或值为 0 时无限制
                    'weights' => [  // 奖励数量及其权重。key 为奖励数量，value 为该奖励的随机权重
                        1 => 3470,
                        2 => 5000,
                        3 => 1000,
                        4 => 200,
                        5 => 100,
                        6 => 100,
                        7 => 60,
                        8 => 40,
                        9 => 20,
                        10 => 10,
                    ],
                ],
                'finish' => 1,
                'url' => 'missevan://live/9075111'
            ]
        ];
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.3.5 (Android;9.0;Meizu M6)']);
        $ref_class = new ReflectionClass(PersonController::class);
        $instance = $ref_class->newInstance('person', 'test');
        $filter_tasks = self::invokePrivateMethod($ref_class, 'filterLiveTaskResp', $tasks, $instance);
        $this->assertNotEmpty($tasks);
        $this->assertCount(2, $filter_tasks);
        $expected = ['gtype', 'type', 'name', 'reward_type', 'info', 'icon_url', 'dark_icon_url', 'button_text', 'finish', 'url'];
        $this->assertArrayHasKeys($expected, $filter_tasks[0]);
        $this->assertArrayHasKeys($expected, $filter_tasks[1]);
        $this->assertEquals('发消息（3/3）', $filter_tasks[1]['name']);

        // 测试 scene
        $filter_tasks = self::invokePrivateMethod($ref_class, 'filterLiveTaskResp', [$tasks, 'live'], $instance, true);
        $this->assertNotEmpty($tasks);
        $this->assertCount(2, $filter_tasks);
        $this->assertArrayHasKeys($expected, $filter_tasks[0]);
        $this->assertArrayHasKeys($expected, $filter_tasks[1]);

        // 测试 iOS < 6.3.4 不下发直播发消息任务，不下发 type 字段
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.3.3 (iOS;12.2;iPhone9,1)']);
        $filter_tasks = self::invokePrivateMethod($ref_class, 'filterLiveTaskResp', [$tasks, 'live'], $instance, true);
        $this->assertNotEmpty($tasks);
        $this->assertCount(1, $filter_tasks);
        $expected = ['gtype', 'name', 'reward_type', 'info', 'icon_url', 'dark_icon_url', 'button_text', 'finish', 'url'];
        $this->assertArrayHasKeys($expected, $filter_tasks[0]);
        $this->assertArrayNotHasKey('type', $filter_tasks[0]);

        // 测试 Android < 6.3.4 不下发直播消息任务，下发 type 字段
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.3.3 (Android;9.0;Meizu M6)']);
        $filter_tasks = self::invokePrivateMethod($ref_class, 'filterLiveTaskResp', [$tasks, 'live'], $instance, true);
        $this->assertNotEmpty($tasks);
        $this->assertCount(1, $filter_tasks);
        $this->assertArrayHasKey('type', $filter_tasks[0]);

        // 测试 harmony < 6.2.6 不下发直播消息任务，下发 type 字段
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.2.5 (HarmonyOS;5.0.0;HUAWEI BRA-AL00 HL1FLSM)']);
        $filter_tasks = self::invokePrivateMethod($ref_class, 'filterLiveTaskResp', [$tasks, 'live'], $instance, true);
        $this->assertNotEmpty($tasks);
        $this->assertCount(1, $filter_tasks);
        $this->assertArrayHasKey('type', $filter_tasks[0]);

        // 缺少配置
        unset($tasks[0]['name'], $tasks[1]['name']);
        $filter_tasks = self::invokePrivateMethod($ref_class, 'filterLiveTaskResp', $tasks, $instance);
        $this->assertNotNull($filter_tasks);
        $this->assertEmpty($filter_tasks);
    }

    /**
     * 小鱼干任务状态与奖励的联测
     *
     * @throws \yii\base\InvalidRouteException
     * @throws \yii\console\Exception
     */
    public function testTaskAndTaskStatus()
    {
        Rpc::registerRpcApiResponseFunc(LiveRpc::API_LIVE_DAILY_TASK, function () {
            return new RpcResult([
                'open_room_url' => 'missevan://live/9075111',
                'is_new' => true,
                'today_listen_duration' => 200000,
                'today_message_count' => 1,
            ]);
        });
        Yii::$app->set('db', Yii::$app->sqlitedb);
        Yii::$app->set('paydb', Yii::$app->sqlite_paydb);
        UserAddendum::deleteAll(['id' => self::TEST_USER_ID]);
        UserSignHistory::deleteAll(['user_id' => self::TEST_USER_ID]);
        self::loginByUserId();

        $redis = Yii::$app->redis;

        $result = Request::api('/person/task', ['gtype' => PersonController::TASK_TYPE_SIGN, 'type' => 1],
            Request::GET, ['User-Agent' => 'MissEvanApp/4.9.0 (iOS;12.0;iPhone9,1)']);
        $key = $redis->generateKey(TASK, self::TEST_USER_ID);
        $redis->unlock($key);
        $this->assertEquals(1, $result['is_signed'], '预期签到抽奖成功');

        // 验证数据写入成功
        $time = strtotime('today', $_SERVER['REQUEST_TIME']);
        $exists = UserSignHistory::find()->where([
            'user_id' => self::TEST_USER_ID,
            'sign_time' => $time,
            'type' => UserSignHistory::TYPE_SIGN,
        ])->exists();
        $this->assertTrue($exists);

        $TEST_TASK_TYPE_ZFB = 8;
        $key = $redis->generateKey(EARS, PersonController::TASK_TYPE_TB, self::TEST_USER_ID);
        $redis->unlock($key);
        $key = $redis->generateKey(EARS, $TEST_TASK_TYPE_ZFB, self::TEST_USER_ID);
        $redis->unlock($key);
        $date = date('Ymd');
        $key = $redis->generateKey(COUNTER_ADDITIONAL_TASK_FINISHED, $date, $TEST_TASK_TYPE_ZFB);
        $key2 = $redis->generateKey(COUNTER_ADDITIONAL_TASK_FINISHED, $date, PersonController::TASK_TYPE_TB);
        $redis->del($key, $key2);
        $tests = [
            [
                'alert' => '预期摸鱼抽奖成功',
                'gtype' => PersonController::TASK_TYPE_GET_POINT,
            ], [
                'alert' => '预期投食三次抽小鱼干的抽奖成功',
                'gtype' => PersonController::TASK_TYPE_TS,
                'preset' => 'setPointFeed',  // 若存在多种预设，可使用 call_user_func
            ], [
                'alert' => '预期为分享三次抽小鱼干的抽奖成功',
                'gtype' => PersonController::TASK_TYPE_SHARE,
            ], [
                'alert' => '预期为评论三次抽小鱼干的抽奖成功',
                'gtype' => PersonController::TASK_TYPE_COMMENT,
            ], [
                'alert' => '预期为访问淘宝农场成功',
                'gtype' => PersonController::TASK_TYPE_TB,
            ], [
                'alert' => '预期为访问支付宝农场成功',
                'gtype' => $TEST_TASK_TYPE_ZFB,
            ],
        ];

        $lock = $redis->generateKey(KEY_LOCK_USER_COMMENT, self::TEST_USER_ID);
        $fx_key = $redis->generateKey(SHARE, self::TEST_USER_ID, strtotime(date('Ymd')));
        foreach ($tests as $test) {
            $redis->set($lock, PersonController::TASK_COMMENT_TIMES_LIMIT);
            $redis->set($fx_key, PersonController::TASK_SHARE_TIMES_LIMIT);
            $has_preset = isset($test['preset']);
            if ($has_preset) {
                $this->setPointFeed(self::TEST_USER_ID, 3);
            }

            // 测试不低于 6.2.3 客户端下发的任务信息
            $result = Request::api('/person/task', ['gtype' => $test['gtype'], 'type' => 1], Request::GET, [
                'User-Agent' => 'MissEvanApp/6.2.3 (iOS;12.1.4;iPhone10,3)',
            ]);
            $result_status = Request::api('/person/task-status', [], Request::GET, [
                'User-Agent' => 'MissEvanApp/6.2.3 (iOS;12.1.4;iPhone10,3)',
            ]);
            // 先解锁，后断言，避免断言失败阻碍重复测试
            $key = $redis->generateKey(EARS, $test['gtype'], self::TEST_USER_ID);
            $redis->unlock($key);
            if ($has_preset) {
                $this->setPointFeed(self::TEST_USER_ID, 0, false);
            }

            $this->assertIsArray($result, $test['alert']);
            $this->assertIsNumeric($result['nums'], $test['alert']);
            $this->assertIsArray($result_status, $test['alert']);
            $this->assertArrayHasKeys(['point', 'tasks', 'banner'], $result_status);
            $except_task_list = [
                [
                    'gtype' => 7,
                    'name' => '免费领钻石',
                    'reward_type' => 2,
                    'info' => '每天完成从猫耳FM进入淘宝农场，必得 5 钻',
                    'icon_url' => 'https://abc.png',
                    'dark_icon_url' => 'https://abc.png',
                    'button_text' => '已完成',
                    'native_url' => 'tbopen://ios.taobao.com/tbopen/index.html?foo=bar',
                    'web_url' => 'https://market.m.test.com/xx?foo=bar',
                    'finish' => 1,
                ],
                [
                    'gtype' => $TEST_TASK_TYPE_ZFB,
                    'name' => '每日抽钻石',
                    'reward_type' => 2,
                    'info' => '每天完成从猫耳FM进入支付宝农场，必得 4 钻',
                    'icon_url' => 'https://abc.png',
                    'dark_icon_url' => 'https://abc.png',
                    'button_text' => '去完成',
                    'native_url' => 'tbopen://m.taobao.com/tbopen/index.html?foo=bar',
                    'web_url' => 'https://market.m.test.com/xx?foo=bar',
                    'finish' => 1,
                ]
            ];
            if ($test['gtype'] >= PersonController::TASK_TYPE_TB) {
                $this->assertArrayHasKey('additional_task_list', $result_status);
                if ($test['gtype'] === PersonController::TASK_TYPE_TB) {
                    $except_task_list[1]['finish'] = 0;
                    $this->assertEquals($except_task_list, $result_status['additional_task_list']);
                } elseif ($test['gtype'] === $TEST_TASK_TYPE_ZFB) {
                    $except_task_list[0]['finish'] = 0;
                    $except_task_list[0]['button_text'] = '去完成';
                    $except_task_list[1]['button_text'] = '已完成';
                    $this->assertEquals($except_task_list, $result_status['additional_task_list']);
                }
            } else {
                $this->assertEquals(1, $result_status['tasks'][$test['gtype']]['finish'], $test['alert']);
            }
            $except_msg = [
                'tip' => '连签解锁新人专属头像框！了解详情',
                'url' => 'https://www.test.com/blackboard/bm6w9Tih14rVeq0v.html',
            ];
            $this->assertEquals($except_msg, $result_status['banner']);

            // 测试低于 6.1.4 客户端下发的任务信息
            $result_status = Request::api('/person/task-status', null, Request::POST, [
                'User-Agent' => 'MissEvanApp/6.1.3 (iOS;12.1.4;iPhone10,3)',
            ]);
            $redis->unlock($key);
            $this->assertArrayHasKeys(['1', '4', '5', '6'], $result_status);
        }
        $redis->set($lock, PersonController::TASK_COMMENT_TIMES_LIMIT);
        $redis->set($fx_key, 0);

        // 测试 6.2.2 客户端下发的任务信息
        $key = $redis->generateKey(EARS, PersonController::TASK_TYPE_TB, self::TEST_USER_ID);
        $redis->unlock($key);
        $result = Request::api('/person/task', ['gtype' => PersonController::TASK_TYPE_TB], Request::GET, [
            'User-Agent' => 'MissEvanApp/6.2.2 (iOS;12.1.4;iPhone10,3)',
        ]);
        $this->assertIsArray($result);
        $this->assertTrue(in_array($result['nums'], [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]));
        $this->assertEquals('恭喜获得 ' . $result['nums'] . ' 钻', $result['info']);
        $result_status = Request::api('/person/task-status', [], Request::GET, [
            'User-Agent' => 'MissEvanApp/6.2.2 (iOS;12.1.4;iPhone10,3)',
        ]);
        // 先解锁，后断言，避免断言失败阻碍重复测试
        $redis->unlock($key);
        if ($has_preset) {
            $this->setPointFeed(self::TEST_USER_ID, 0, false);
        }
        $this->assertArrayHasKey('additional_task_list', $result_status);
        // 断言淘宝农场任务已完成
        $this->assertEquals(1, $result_status['additional_task_list'][0]['finish']);

        // 测试低于 6.2.2 客户端下发的任务信息
        $key = $redis->generateKey(EARS, (string)PersonController::TASK_TYPE_TB . '_old', self::TEST_USER_ID);
        $redis->unlock($key);
        $result = Request::api('/person/task', ['gtype' => PersonController::TASK_TYPE_TB], Request::GET, [
            'User-Agent' => 'MissEvanApp/6.2.1 (iOS;12.1.4;iPhone10,3)',
        ]);
        $this->assertIsArray($result);
        $this->assertEquals(['info' => '恭喜获得 10 条小鱼干', 'nums' => 10], $result);
        $redis->unlock($key);
        $this->assertArrayHasKey('additional_task_list', $result_status);
        // 断言淘宝农场任务已完成
        $this->assertEquals(1, $result_status['additional_task_list'][0]['finish']);

        // 测试低于 6.2.3 版本完成支付宝任务受限
        $key = $redis->generateKey(EARS, $TEST_TASK_TYPE_ZFB, self::TEST_USER_ID);
        $redis->unlock($key);
        $this->assertThrowsWithMessage(HttpException::class, '该任务尚未开放', function () use ($TEST_TASK_TYPE_ZFB) {
            Request::api('/person/task', ['gtype' => $TEST_TASK_TYPE_ZFB], Request::GET, [
                'User-Agent' => 'MissEvanApp/6.2.2 (iOS;12.1.4;iPhone10,3)',
            ]);
        });

        // 测试不低于 6.2.5 版本成功完成支付宝任务
        $redis->unlock($key);
        $result = Request::api('/person/task?gtype=' . $TEST_TASK_TYPE_ZFB, null, Request::POST, [
            'User-Agent' => 'MissEvanApp/6.2.5 (iOS;12.1.4;iPhone10,3)',
        ]);
        $this->assertIsArray($result);
        $this->assertTrue(in_array($result['nums'], [1, 2]));
        $this->assertEquals('恭喜获得 ' . $result['nums'] . ' 钻', $result['info']);

        // 测试成功领取支付宝任务钻石奖励人次达到上限后，本次获取到固定数量钻石奖励
        $redis->unlock($key);
        $result = Request::api('/person/task?gtype=' . $TEST_TASK_TYPE_ZFB, null, Request::POST, [
            'User-Agent' => 'MissEvanApp/6.2.5 (iOS;12.1.4;iPhone10,3)',
        ]);
        $this->assertIsArray($result);
        $this->assertEquals(['info' => '恭喜获得 4 钻', 'nums' => 4], $result);

        // 测试不低于 6.2.5 版本通过 GET 方式传参完成支付宝任务受限
        $redis->unlock($key);
        $this->assertThrowsWithMessage(HttpException::class, '非法请求', function () use ($TEST_TASK_TYPE_ZFB) {
            Request::api('/person/task', ['gtype' => $TEST_TASK_TYPE_ZFB], Request::GET, [
                'User-Agent' => 'MissEvanApp/6.2.5 (iOS;12.1.4;iPhone10,3)',
            ]);
        });

        // 测试获取直播任务
        $key_visit = $redis->generateKey(EARS, PersonController::TASK_TYPE_LIVE_VISIT, self::TEST_USER_ID);
        $redis->unlock($key_visit);
        $key_new_user = $redis->generateKey(EARS, PersonController::TASK_TYPE_LIVE_LISTEN_NEW_USER, self::TEST_USER_ID);
        $redis->unlock($key_new_user);
        $except_task_list = [
            [
                'gtype' => 10,
                'type' => PersonController::LIVE_TASK_TYPE_NORMAL,
                'name' => '进入直播间',
                'reward_type' => 1,
                'info' => '每天进入直播间可摸鱼 1 次',
                'icon_url' => 'https://abc.png',
                'dark_icon_url' => 'https://abc.png',
                'button_text' => '摸鱼',
                'url' => 'missevan://live/9075111',
                'finish' => 1,
            ],
            [
                'gtype' => 11,
                'type' => PersonController::LIVE_TASK_TYPE_NORMAL,
                'name' => '【惊喜任务】收听直播',
                'reward_type' => 2,
                'info' => '收听直播满 3min 可随机抽取 1-10 钻石哦~',
                'icon_url' => 'https://abc.png',
                'dark_icon_url' => 'https://abc.png',
                'button_text' => '抽钻石',
                'url' => 'missevan://live/9075111',
                'finish' => 1,
            ],
            [
                'gtype' => 15,
                'type' => PersonController::LIVE_TASK_TYPE_SEND_MESSAGE,
                'name' => '发消息（1/3）',
                'reward_type' => 2,
                'info' => '每天进入直播间发 3 条消息可抽 1-10 钻',
                'icon_url' => 'https://abc.png',
                'dark_icon_url' => 'https://abc.png',
                'button_text' => '去完成',
                'url' => 'missevan://live/9075111',
                'finish' => 0,
            ]
        ];
        $result_status = Request::api('/person/task-status', [], Request::GET, [
            'User-Agent' => 'MissEvanApp/6.3.5 (iOS;12.1.4;iPhone10,3)',
        ]);
        $this->assertIsArray($result_status);
        $this->assertArrayHasKeys(['live_task_list'], $result_status);
        $this->assertIsArray($result_status['live_task_list']);
        $this->assertCount(3, $result_status['live_task_list']);
        $tasks = $result_status['live_task_list'];
        $this->assertEquals($except_task_list, $tasks);

        // 测试直播任务抽奖错误
        $TASK_TYPE_LIVE_LISTEN = 12;
        $this->assertThrowsWithMessage(HttpException::class, '未满足收听时长', function () use ($TASK_TYPE_LIVE_LISTEN) {
            Request::api('/person/task?gtype=' . $TASK_TYPE_LIVE_LISTEN, null, Request::POST);
        });

        // 测试正常抽奖
        $result = Request::api('/person/task?gtype=' . PersonController::TASK_TYPE_LIVE_LISTEN_NEW_USER,
            null, Request::POST);
        $this->assertIsArray($result);
        $this->assertArrayHasKeys(['info', 'nums', 'reward_icon_url'], $result);
        $this->assertEquals('https://ff.png', $result['reward_icon_url']);

        // 测试 iOS = 6.2.5 版本完成携程任务返回已领取奖励状态
        $except_ctrip_task = [
            'gtype' => PersonController::TASK_TYPE_CTRIP,
            'name' => '每日抽钻石',
            'reward_type' => 2,
            'info' => '携程引流任务',
            'icon_url' => 'https://abc.png',
            'dark_icon_url' => 'https://abc.png',
            'button_text' => '已完成',
            'finish' => PersonController::TASK_AWARDED,
            'web_url' => 'https://ctrip.m.test.com/xx?foo=bar',
            'native_url' => 'tbopen://m.ctrip.com/tbopen/index.html?foo=bar',
        ];
        $key = $redis->generateKey(EARS, PersonController::TASK_TYPE_CTRIP, self::TEST_USER_ID);
        $redis->lockAt($key, $_SERVER['REQUEST_TIME'] + HALF_MINUTE);
        $result = Request::api('/person/task-status', [], Request::GET, [
            'User-Agent' => 'MissEvanApp/6.2.5 (iOS;12.1.4;iPhone10,3)',
        ]);
        $this->assertIsArray($result);
        $this->assertArrayHasKeys(['additional_task_list'], $result);
        $ctrip_task = array_filter($result['additional_task_list'], function ($task) {
            return $task['gtype'] === PersonController::TASK_TYPE_CTRIP;
        });
        $this->assertEquals($except_ctrip_task, reset($ctrip_task));

        // 测试只返回直播任务
        $result = Request::api('/person/task-status', ['scene' => 'live'], Request::GET, [
            'User-Agent' => 'MissEvanApp/6.4.1 (iOS;12.1.4;iPhone10,3)',
        ]);
        $this->assertIsArray($result);
        $this->assertArrayHasKey('live_task_list', $result);
        $this->assertEquals(3, count($result['live_task_list']));
        $this->assertEquals(1, $result['live_task_list'][0]['finish']);
        $this->assertEquals('https://kk.png', $result['live_task_list'][0]['icon_url']);
        $this->assertEquals(0, $result['live_task_list'][1]['finish']);
        $this->assertEquals('https://aa.png', $result['live_task_list'][1]['icon_url']);
        $this->assertEquals(2, $result['live_task_list'][2]['finish']);
        $this->assertEquals('https://mmm.png', $result['live_task_list'][2]['icon_url']);

        // 测试非新人用户，直播间任务下发
        Rpc::registerRpcApiResponseFunc(LiveRpc::API_LIVE_DAILY_TASK, function () {
            return new RpcResult([
                'open_room_url' => 'missevan://live/9075111',
                'is_new' => false,
                'today_listen_duration' => 200000,
                'today_message_count' => 1,
            ]);
        });
        $result = Request::api('/person/task-status', ['scene' => 'live'], Request::GET, [
            'User-Agent' => 'MissEvanApp/6.4.1 (iOS;12.1.4;iPhone10,3)',
        ]);
        $this->assertIsArray($result);
        $this->assertArrayHasKey('live_task_list', $result);
        $this->assertCount(2, $result['live_task_list']);
        $this->assertEquals(PersonController::TASK_TYPE_LIVE_VISIT, $result['live_task_list'][0]['gtype']);
        $this->assertEquals(PersonController::TASK_TYPE_LIVE_LISTEN, $result['live_task_list'][1]['gtype']);

        // 测试获取微信公众号任务
        $key_visit = $redis->generateKey(EARS, PersonController::TASK_TYPE_WECHAT_OFFIACCOUNT, self::TEST_USER_ID);
        $redis->unlock($key_visit);
        // 未完成任务直接领奖
        $this->assertThrowsWithMessage(HttpException::class, '尚未完成任务', function () use ($TASK_TYPE_LIVE_LISTEN) {
            Request::api('/person/task?gtype=' . PersonController::TASK_TYPE_WECHAT_OFFIACCOUNT, null, Request::POST, [
                'User-Agent' => 'MissEvanApp/6.4.2 (iOS;12.1.4;iPhone10,3)',
            ]);
        });
        // >= 6.4.2 版本客户端单独下发关注公众号任务
        $expect_wechat_offiaccount_task = [
            'gtype' => 19,
            'name' => '关注猫耳FM微信公众号',
            'info' => '在公众号发送暗号完成任务，最多摸到 88 小鱼干',
            'reward_type' => 1,
            'icon_url' => 'https://abc.png',
            'web_url' => 'https://test.wechat.com/xx?foo=bar',
            'dark_icon_url' => 'https://abc.png',
            'to_third_party' => false,
            'button_text' => '去完成',
            'finish' => 0,
        ];
        $list = Request::api('/person/task-status', null, Request::POST, [
            'User-Agent' => 'MissEvanApp/6.4.2 (iOS;12.1.4;iPhone10,3)',
        ]);
        $this->assertIsArray($list);
        $this->assertArrayHasKey('wechat_offiaccount_task', $list);
        $this->assertEquals($list['wechat_offiaccount_task'], $expect_wechat_offiaccount_task);

        // 低于 6.4.2 版本在限时任务列表中下发关注公众号任务
        $list = Request::api('/person/task-status', null, Request::POST, [
            'User-Agent' => 'MissEvanApp/6.4.1 (iOS;12.1.4;iPhone10,3)',
        ]);
        $this->assertIsArray($list);
        $this->assertArrayNotHasKey('wechat_offiaccount_task', $list);
        $this->assertArrayHasKey('additional_task_list', $list);
        $this->assertEquals($list['additional_task_list'][0], $expect_wechat_offiaccount_task);

        // 完成任务后领奖
        $key_visit = $redis->generateKey(EARS, PersonController::TASK_TYPE_WECHAT_OFFIACCOUNT, self::TEST_USER2_ID);
        $redis->unlock($key_visit);
        self::loginByUserId(self::TEST_USER2_ID);
        $result = Request::api('/person/task?gtype=' . PersonController::TASK_TYPE_WECHAT_OFFIACCOUNT,
            null, Request::POST, [
                'User-Agent' => 'MissEvanApp/6.4.1 (iOS;12.1.4;iPhone10,3)',
            ]);
        $this->assertIsArray($result);
        $this->assertArrayHasKeys(['info', 'nums'], $result);
        // 任务列表
        $list = Request::api('/person/task-status', null, Request::POST, [
            'User-Agent' => 'MissEvanApp/6.4.1 (iOS;12.1.4;iPhone10,3)',
        ]);
        $this->assertIsArray($list);
        $this->assertArrayNotHasKey('wechat_offiaccount_task', $list);
    }

    public function testGetAdditionalTaskList()
    {
        // 测试 6.2.3 以下版本返回配置任务列表为空
        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/6.2.2 (Android;7.0;Meizu M6 M6)'
        ]);
        $class = PersonController::class;
        $result = self::invokePrivateMethod($class, 'getAdditionalTaskList');
        $this->assertEmpty($result);

        // 测试 6.2.4 以下版本不返回百度引流任务
        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/6.2.3 (Android;7.0;Meizu M6 M6)'
        ]);
        $result = self::invokePrivateMethod($class, 'getAdditionalTaskList');
        $this->assertNotEmpty($result);
        $task_types = array_column($result, 'gtype');
        $this->assertNotContains(PersonController::TASK_TYPE_BAIDU, $task_types);
        $this->assertNotContains(PersonController::TASK_TYPE_CTRIP, $task_types);
        // 验证返回的 native_url 为 OS 对应的 native_url
        $this->assertEquals('tbopen://android.taobao.com/tbopen/index.html?foo=bar', $result[0]['native_url']);

        // 测试 6.2.4 版本返回结果有百度引流任务、测试任务在非开放时间时不返回
        $_SERVER['REQUEST_TIME'] = 1;
        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/6.2.4 (iOS;11.4.1;iPhone8,4)',
        ]);
        $result = self::invokePrivateMethod($class, 'getAdditionalTaskList');
        $task_types = array_column($result, 'gtype');
        $this->assertContains(PersonController::TASK_TYPE_BAIDU, $task_types);
        $this->assertNotContains(PersonController::TASK_TYPE_CTRIP, $task_types);
        // 验证返回的 native_url 为 OS 对应的 native_url
        $this->assertEquals('tbopen://ios.taobao.com/tbopen/index.html?foo=bar', $result[0]['native_url']);

        // 测试 native_url 不区分设备下发时（native_url 直接配置为字符串）
        $_SERVER['REQUEST_TIME'] = 2;
        $result = self::invokePrivateMethod($class, 'getAdditionalTaskList');
        $task_types = array_column($result, 'gtype');
        $this->assertContains(PersonController::TASK_TYPE_BAIDU, $task_types);
        $this->assertContains(PersonController::TASK_TYPE_CTRIP, $task_types);
        // 验证返回的 native_url 正确
        $this->assertEquals('tbopen://m.ctrip.com/tbopen/index.html?foo=bar', $result[3]['native_url']);

        // 测试百度和携程限时任务支持根据版本下发不同任务
        $older_than_627 = false;
        $result = self::invokePrivateMethod($class, 'getAdditionalTaskList', $older_than_627);
        $result_map = array_column($result, null, 'gtype');
        $this->assertTrue(array_key_exists(PersonController::TASK_TYPE_BAIDU, $result_map));
        $this->assertTrue(array_key_exists(PersonController::TASK_TYPE_CTRIP, $result_map));
        // 断言百度新版本下发到端钻石任务
        $baidu_task = $result_map[PersonController::TASK_TYPE_BAIDU];
        $this->assertArrayHasKeys(['name', 'info', 'reward_type', 'button_texts', 'to_third_party'], $baidu_task);
        $this->assertEquals('每日抽钻石', $baidu_task['name']);
        $this->assertEquals('test', $baidu_task['info']);
        $this->assertEquals(2, $baidu_task['reward_type']);
        $this->assertEquals(['去完成', '领钻石', '已完成'], $baidu_task['button_texts']);
        $this->assertTrue($baidu_task['to_third_party']);
        // 断言携程新版本下发非到端钻石任务
        $ctrip_task = $result_map[PersonController::TASK_TYPE_CTRIP];
        $this->assertArrayHasKeys(['name', 'info', 'reward_type', 'button_texts', 'to_third_party'], $ctrip_task);
        $this->assertEquals('每日抽钻石', $ctrip_task['name']);
        $this->assertEquals('携程引流任务', $ctrip_task['info']);
        $this->assertEquals(2, $ctrip_task['reward_type']);
        $this->assertEquals(['去完成', '已完成'], $ctrip_task['button_texts']);
        $this->assertFalse($ctrip_task['to_third_party']);
        // 测试旧版本
        $older_than_627 = true;
        $result = self::invokePrivateMethod($class, 'getAdditionalTaskList', $older_than_627);
        $result_map = array_column($result, null, 'gtype');
        $this->assertTrue(array_key_exists(PersonController::TASK_TYPE_BAIDU, $result_map));
        $this->assertTrue(array_key_exists(PersonController::TASK_TYPE_CTRIP, $result_map));
        // 断言百度旧版本下发小鱼干任务
        $baidu_task = $result_map[PersonController::TASK_TYPE_BAIDU];
        $this->assertArrayHasKeys(['name', 'info', 'reward_type', 'button_texts', 'to_third_party'], $baidu_task);
        $this->assertEquals('百度极速版', $baidu_task['name']);
        $this->assertEquals('点击前往百度极速版，必得 10 条小鱼干（更新新版本奖励升级，随机获取 1-10 钻）', $baidu_task['info']);
        $this->assertEquals(1, $baidu_task['reward_type']);
        $this->assertEquals(['去完成', '已完成'], $baidu_task['button_texts']);
        $this->assertFalse($baidu_task['to_third_party']);
        // 断言携程旧版本下发非到端钻石任务
        $ctrip_task = $result_map[PersonController::TASK_TYPE_CTRIP];
        $this->assertArrayHasKeys(['name', 'info', 'reward_type', 'button_texts', 'to_third_party'], $ctrip_task);
        $this->assertEquals('每日抽钻石', $ctrip_task['name']);
        $this->assertEquals('携程引流任务', $ctrip_task['info']);
        $this->assertEquals(2, $ctrip_task['reward_type']);
        $this->assertEquals(['去完成', '已完成'], $ctrip_task['button_texts']);
        $this->assertFalse($ctrip_task['to_third_party']);
    }

    public function testActionSign()
    {
        $time = strtotime('today', $_SERVER['REQUEST_TIME']);
        $one_week_ago = $time - ONE_WEEK;
        self::loginByUserId(self::TEST_USER3_ID, ['ctime' => $one_week_ago]);
        UserSignHistory::deleteAll(['user_id' => self::TEST_USER3_ID]);
        $key = self::$redis->generateKey(TASK, self::TEST_USER3_ID);
        self::$redis->unlock($key);

        // 测试异常情况
        $error_test_cases = [
            [
                'params' => [
                    'sign_date' => date('Y-m-d', $time),
                ],
                'want' => '非法请求',
            ],
            [
                'params' => [
                    'type' => 1,
                    'sign_date' => date('Y-m-d', $time),
                ],
                'want' => '当前日期不在可补签范围内',
            ],
            [
                'params' => [
                    'type' => 1,
                    'sign_date' => date('Y-m-d', $one_week_ago - ONE_DAY),
                ],
                'want' => '无法补签注册日之前的日期哦 >_<',
            ],
            [
                'params' => [],
                'want' => '当天已经签到',
            ],
            [
                'params' => [
                    'type' => 1,
                    'sign_date' => date('Y-m-d', $time - ONE_DAY),
                ],
                'want' => '小鱼干余额不足 T_T',
            ],
        ];

        foreach ($error_test_cases as $key => $error) {
            if ($key === 3) {
                UserSignHistory::addSignHistory(self::TEST_USER3_ID, $time);
            } elseif ($key === 4) {
                UserSignHistory::deleteAll(['user_id' => self::TEST_USER3_ID]);
                Mowangskuser::updateAll(['point' => 29], ['id' => self::TEST_USER3_ID]);
            }
            $this->assertThrowsWithMessage(HttpException::class, $error['want'], function () use ($error, $key) {
                $version = $key ? '5.7.9' : '5.7.6';
                $user_agent = 'MissEvanApp/' . $version . ' (Android;7.0;Meizu M6 M6)';
                Request::api('/person/sign', $error['params'], Request::POST, ['User-Agent' => $user_agent]);
            });
        }

        // 测试正常签到
        Mowangskuser::updateAll(['point' => 3497], ['id' => self::TEST_USER3_ID]);
        UserSignHistory::deleteAll(['user_id' => self::TEST_USER3_ID]);
        UserAddendum::deleteAll(['id' => self::TEST_USER3_ID]);
        $params = [
            'sign_date' => date('Y-m-d', $time),
        ];
        $result = Request::api('/person/sign', $params, Request::POST,
            ['User-Agent' => 'MissEvanApp/5.8.8 (Android;7.0;Meizu M6 M6)']);
        $expect = [
            'continuous_sign_days' => 1,
            'history_data' => 0b1,
            'msg' => '签到成功',
            'user_point' => 3500,
            'patch_required_point' => 30,
            'reward_point' => 3,
            'ts' => $_SERVER['REQUEST_TIME'],
        ];
        $this->assertEquals($expect, $result);

        // 测试正常补签
        self::loginByUserId(self::TEST_USER3_ID, ['ctime' => $time - THIRTY_DAYS - ONE_WEEK]);
        $success_patch_date = [2, 1, 5, 4, 9, 11, 30, 3, 7, 6, 8, 10, 29, 28, 26, 12, 13, 15, 16, 17, 18, 19, 20, 21, 23, 14, 22, 24, 27, 25];
        $success_patch_want = [
            [1, 0b0000000000000000000000000000101],
            [3, 0b0000000000000000000000000000111],
            [3, 0b00000000000000000000000000100111],
            [3, 0b0000000000000000000000000110111],
            [3, 0b0000000000000000000001000110111],
            [3, 0b0000000000000000000101000110111],
            [3, 0b1000000000000000000101000110111],
            [6, 0b1000000000000000000101000111111],
            [6, 0b1000000000000000000101010111111],
            [8, 0b1000000000000000000101011111111],
            [10, 0b1000000000000000000101111111111],
            [12, 0b1000000000000000000111111111111],
            [12, 0b1100000000000000000111111111111],
            [12, 0b1110000000000000000111111111111],
            [12, 0b1110100000000000000111111111111],
            [13, 0b1110100000000000001111111111111],
            [14, 0b1110100000000000011111111111111],
            [14, 0b1110100000000001011111111111111],
            [14, 0b1110100000000011011111111111111],
            [14, 0b1110100000000111011111111111111],
            [14, 0b1110100000001111011111111111111],
            [14, 0b1110100000011111011111111111111],
            [14, 0b1110100000111111011111111111111],
            [14, 0b1110100001111111011111111111111],
            [14, 0b1110100101111111011111111111111],
            [22, 0b1110100101111111111111111111111],
            [24, 0b1110100111111111111111111111111],
            [25, 0b1110101111111111111111111111111],
            [25, 0b1111101111111111111111111111111],
            [31, 0b1111111111111111111111111111111],
        ];

        $init_point = $expect['user_point'];
        foreach ($success_patch_date as $key => $date) {
            $params = [
                'type' => 1,
                'sign_date' => date('Y-m-d', $time - $date * ONE_DAY),
            ];
            $result = Request::api('/person/sign', $params, Request::POST,
                ['User-Agent' => 'MissEvanApp/6.2.5 (Android;9.0;Meizu M6 M6)']);
            $want = [
                'msg' => '补签成功',
                'patch_required_point' => 30,
                'reward_point' => 3,
                // 每次补签可以获取 3 积分，同时需要消耗 30 积分
                'user_point' => $init_point + ($key + 1) * (3 - 30),
                'ts' => $_SERVER['REQUEST_TIME'],
                'continuous_sign_days' => $success_patch_want[$key][0],
                'history_data' => $success_patch_want[$key][1],
            ];
            $this->assertEquals($want, $result);
        }

        // 测试无法补签一个月前
        $this->assertThrowsWithMessage(HttpException::class, '当前日期不在可补签范围内', function () use ($error, $key, $time) {
            $params = [
                'type' => 1,
                'sign_date' => date('Y-m-d', $time - THIRTY_DAYS - ONE_DAY),
            ];
            $user_agent = 'MissEvanApp/6.2.5 (Android;9.0;Meizu M6 M6)';
            Request::api('/person/sign', $params, Request::POST, ['User-Agent' => $user_agent]);
        });

        // 测试已经加锁成功后不能重复签到
        UserSignHistory::deleteAll(['user_id' => self::TEST_USER3_ID]);
        $this->assertThrowsWithMessage(HttpException::class, '签到失败', function () use ($error, $key) {
            $user_agent = 'MissEvanApp/5.7.9 (Android;7.0;Meizu M6 M6)';
            Request::api('/person/sign', [], Request::POST, ['User-Agent' => $user_agent]);
        });
    }

    public function testSignHistory()
    {
        self::loginByUserId(self::TEST_USER3_ID);
        UserSignHistory::deleteAll(['user_id' => self::TEST_USER3_ID]);
        UserAddendum::deleteAll(['id' => self::TEST_USER3_ID]);
        Mowangskuser::updateAll(['point' => 299], ['id' => self::TEST_USER3_ID]);
        $key = self::$redis->generateKey(TASK, self::TEST_USER3_ID);
        self::$redis->unlock($key);

        // 用户无签到记录
        $result = Request::api('/person/sign-history');
        $data = [
            'ts' => $_SERVER['REQUEST_TIME'],
            'continuous_sign_days' => 0,
            'patch_required_point' => 30,
            'reward_point' => 3,
            'user_point' => 299,
            'history_data' => 0,
        ];
        $this->assertEquals($data, $result);

        // 用户有签到记录
        Request::api('/person/sign', [], Request::POST,
            ['User-Agent' => 'MissEvanApp/5.8.8 (Android;7.0;Meizu M6 M6)']);
        $result = Request::api('/person/sign-history');

        $data = [
            'ts' => $_SERVER['REQUEST_TIME'],
            'continuous_sign_days' => 1,
            'patch_required_point' => 30,
            'reward_point' => 3,
            'user_point' => 302,
            'history_data' => 1,
        ];
        $this->assertEquals($data, $result);
    }

    /**
     * 预置投食数据
     *
     * @param int $user_id
     * @param int $item_num
     * @param bool $create
     */
    protected function setPointFeed(int $user_id, int $item_num, bool $create = true)
    {
        if ($create) {
            $timestamp = time();
            foreach (range($item_num, 1) as $index => $value) {
                $pf = new MPointFeed();
                $pf->sound_id = 1;
                $pf->catalog_id = 1;
                $pf->create_time = $timestamp - $value;
                $pf->user_id = $user_id;
                $pf->num = 1;
                $pf->save();
            }
        } else {
            MPointFeed::deleteAll(['user_id' => $user_id, 'sound_id' => 1, 'catalog_id' => 1]);
        }
    }

    public function testGetUserSound()
    {
        $TEST_PAGE_SIZE = 1;
        // 登录
        $this->login();
        $sound = MSound::find()
            ->where('user_id = :user_id', [':user_id' => self::TEST_USER_ID])
            ->orderBy('id DESC')->limit(1)->one();
        $origin_checked = $sound->checked;
        $origin_not_police = false;
        if ($sound->checked !== MSound::CHECKED_POLICE) {
            // 更新成报警音
            // 因为 model 里的 afterFind 方法会给 soundurl 字段加上域名
            // 所以 save 时要存储为原数据
            $sound->soundurl = $sound->oldAttributes['soundurl'];
            $sound->checked = MSound::CHECKED_POLICE;
            if (!$sound->save(false, ['checked'])) {
                throw new Exception(MUtils::getFirstError($sound));
            }
            $origin_not_police = true;
        }
        $params = [
            'user_id' => self::TEST_USER_ID,
            'page_size' => $TEST_PAGE_SIZE,
        ];
        // 获取自己的音频
        $data = Request::api('/person/get-user-sound', $params);
        $this->assertIsObject($data);
        $first_sound = current($data->Datas);
        // 可以获取到报警音
        $this->assertEquals($sound->id, $first_sound['id']);
        $this->loginByUserId(self::TEST_USER2_ID);
        // 获取他人的音频
        $data = Request::api('/person/get-user-sound', $params);
        $this->assertIsObject($data);
        $first_sound = current($data->Datas);
        // 获取不到报警音
        $this->assertNotEquals($sound->id, $first_sound['id']);
        // 还原数据
        if ($origin_not_police && $first_sound['checked'] !== $origin_checked) {
            $sound->checked = $origin_checked;
            if (!$sound->save()) {
                throw new Exception(MUtils::getFirstError($sound));
            }
        }
        // 获取用户非直播回放音频（普通音频，音乐集音频，互动广播剧音频）
        // 创建测试音频
        $expected_sound_ids = [];
        $expected_sound1 = Data::createSound([
            'user_id' => self::TEST_USER2_ID,
            'type' => MSound::TYPE_NORMAL
        ]);
        $expected_sound_ids[] = (int)$expected_sound1->id;

        $expected_sound2 = Data::createSound([
            'user_id' => self::TEST_USER2_ID,
            'type' => MSound::TYPE_MUSIC
        ]);
        $expected_sound_ids[] = (int)$expected_sound2->id;

        $expected_sound3 = Data::createSound([
            'user_id' => self::TEST_USER2_ID,
            'type' => MSound::TYPE_INTERACTIVE
        ]);
        $expected_sound_ids[] = (int)$expected_sound3->id;

        $expected_sound4 = Data::createSound([
            'user_id' => self::TEST_USER2_ID,
            'type' => MSound::TYPE_LIVE
        ]);
        $expected_sound_ids[] = (int)$expected_sound4->id;

        $params['user_id'] = self::TEST_USER2_ID;
        $params['type'] = MSound::USER_SOUND_TYPE_OWN;
        $params['page_size'] = PAGE_SIZE_20;
        $data = Request::api('/person/get-user-sound', $params);
        $this->assertNotEmpty($data->Datas);
        $actual_sound_ids = array_column($data->Datas, 'id');
        $expected_sound_ids = array_reverse($expected_sound_ids);
        $this->assertEquals($expected_sound_ids, $actual_sound_ids);

        // 获取用户直播回放音频
        $params['type'] = MSound::USER_SOUND_TYPE_LIVE;
        $data = Request::api('/person/get-user-sound', $params);
        $this->assertNotEmpty($data->Datas);
        $actual_sound_ids = array_column($data->Datas, 'id');
        $this->assertEquals((int)$expected_sound4->id, $actual_sound_ids[0]);

        // 测试 iOS 4.5.8 版本返回的页数
        $data = Request::api('/person/get-user-sound', $params, Request::GET, [
            'User-Agent' => 'MissEvanApp/4.5.8 (iOS;11.4.1;iPhone8,4)',
        ]);
        $this->assertNotEmpty($data);
        $this->assertObjectHasAttribute('Datas', $data);
        $this->assertNotEmpty($data->Datas);
        $this->assertObjectHasAttribute('pagination', $data);
        $EXPECTED = 200;
        $this->assertEquals($EXPECTED, $data->pagination['pagesize']);

        // 测试 iOS 其他版本返回的页数
        $data = Request::api('/person/get-user-sound', $params, Request::GET, [
            'User-Agent' => 'MissEvanApp/4.5.9 (iOS;11.4.1;iPhone8,4)',
        ]);
        $this->assertNotEmpty($data);
        $this->assertObjectHasAttribute('Datas', $data);
        $this->assertNotEmpty($data->Datas);
        $this->assertObjectHasAttribute('pagination', $data);
        $this->assertEquals(PAGE_SIZE_20, $data->pagination['pagesize']);

        // 测试安卓返回的页数
        $data = Request::api('/person/get-user-sound', $params, Request::GET, [
            'User-Agent' => 'MissEvanApp/5.4.8 (Android;6.0.1;OPPO OPPO R9sk R9sk)',
        ]);
        $this->assertNotEmpty($data);
        $this->assertObjectHasAttribute('Datas', $data);
        $this->assertNotEmpty($data->Datas);
        $this->assertObjectHasAttribute('pagination', $data);
        $this->assertEquals(PAGE_SIZE_20, $data->pagination['pagesize']);
    }

    public function testGetSubscribedDrama()
    {
        $TEST_USER_ID = 346287;
        $params = [
            'user_id' => $TEST_USER_ID,
            'page' => 1,
            'page_size' => PAGE_SIZE_20,
        ];
        // 他人主页
        $this->login();
        $data = Request::api('/person/get-subscribed-drama', $params, Request::GET, [
            'Cookie' => 'token=' . $_COOKIE['token'] . ';equip_id=' . self::TEST_EQUIP_ID,
            'User-Agent' => self::TEST_IOS_USERAGENT,
        ]);
        $this->assertIsObject($data);
        foreach ($data->Datas as $item) {
            // 他人主页，过滤掉了所有合约下架剧集
            $this->assertNotEquals(Drama::CHECKED_CONTRACT_EXPIRED, $item['checked']);
        }
        // 个人主页
        $this->login('<EMAIL>', '1234567');
        $data = Request::api('/person/get-subscribed-drama', $params, Request::GET, [
            'Cookie' => 'token=' . $_COOKIE['token'] . ';equip_id=' . self::TEST_EQUIP_ID,
            'User-Agent' => self::TEST_IOS_USERAGENT,
        ]);
        $this->assertIsObject($data);
        foreach ($data->Datas as $item) {
            // 用户订阅的剧集目前只显示已购合约下架剧集，审核中剧集，过审剧集
            if ($item['checked'] === Drama::CHECKED_CONTRACT_EXPIRED) {
                // 合约下架剧集只显示用户已购的
                $this->assertEquals(Drama::DRAMA_PAID, $item['need_pay']);
            } else {
                $this->assertTrue(in_array($item['checked'], [Drama::CHECKED_NOT_VERIFY, Drama::CHECKED_PASS]));
            }
        }
    }

    public function testDramaFeed()
    {
        $this->login();
        // 测试参数错误
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () {
            Request::api('/person/drama-feed', ['type' => 0]);
        });
        // 测试正常返回
        $data = Request::api('/person/drama-feed', []);
        $this->assertArrayHasKey('Datas', $data);
        $this->assertArrayHasKey('pagination', $data);
    }

    /**
     * 测试获取默认（可选）头像音接口
     */
    public function testDefaultHeadSound()
    {
        $tests = [
            // 测试 iOS 4.7.8 之前版本返回数据情况
            ['is_old' => true, 'ua' => 'MissEvanApp/4.7.7 (iOS;12.2;iPhone9,1)'],
            // 测试 iOS 4.8.2，Android 5.7.0 版本
            ['is_old' => false, 'ua' => 'MissEvanApp/5.7.0 (Android;7.0;Meizu M6 M6)'],
            ['is_old' => false, 'ua' => 'MissEvanApp/4.8.2 (iOS;12.2;iPhone9,1)'],
        ];
        foreach ($tests as $test) {
            $data = Request::api('/person/default-head-sound', [], Request::GET, [
                'User-Agent' => $test['ua'],
            ]);
            $this->assertIsArray($data);
            if ($test['is_old']) {
                // 验证返回的数据与配置相匹配
                $this->assertEquals(self::TEST_SOUND_ID, $data[0]['id']);
                // 验证返回的音频地址为不完整地址
                $this->assertFalse(MUtils::hasHttpScheme($data[2]['soundurl']));
            } else {
                // 验证返回的数据与配置相匹配
                $this->assertEquals(self::TEST_SOUND_ID, $data[0]['id']);
                // 验证返回的音频地址为完整地址
                $this->assertTrue(MUtils::hasHttpScheme($data[0]['soundurl']));
            }
        }
    }

    /**
     * 测试我的页图标
     */
    public function testActionHomepageIcons()
    {
        Yii::$app->set('db', Yii::$app->sqlitedb);
        $url = '/person/homepage-icons';
        // 老设备
        $result = Request::api($url, [], Request::GET, [
            'Cookie' => 'equip_id=' . self::TEST_HOMEPAGE_ICONS_EQUIP_ID,
            'user_agent' => 'MissEvanApp/6.1.0 (iOS;12.0;iPhone9,1)'
        ]);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $found_msg = false;
        $found_wallet = false;
        $exist_test_title_name = false;
        $exist_name = false;
        foreach ($result as $v) {
            if (array_key_exists('name', $v) && $v['name'] === 'message') {
                $found_msg = true;
                $this->assertEquals('missevan://msg', $v['url']);
            }

            if (array_key_exists('name', $v) && $v['name'] === 'wallet') {
                $found_wallet = true;
                $this->assertArrayNotHasKey('is_new_user', $v);
            }

            if ($v['title'] === '测试未填写 anchor_name') {
                $exist_test_title_name = true;
                $exist_name = array_key_exists('name', $v);
            }
        }
        $this->assertTrue($found_msg);
        $this->assertTrue($found_wallet);
        $this->assertTrue($exist_test_title_name);
        $this->assertFalse($exist_name);

        // 新设备
        Yii::$app->redis->setex(
            Yii::$app->redis->generateKey(KEY_NEW_DEVICE_EQUIP_ID, self::TEST_HOMEPAGE_ICONS_EQUIP_ID),
            ONE_WEEK, $_SERVER['REQUEST_TIME']
        );
        // 重置设备激活时间
        self::setPrivateProperty(Yii::$app->equip, 'activate_time', null);
        $result = Request::api($url, [], Request::GET, [
            'Cookie' => 'equip_id=' . self::TEST_HOMEPAGE_ICONS_EQUIP_ID,
            'user_agent' => 'MissEvanApp/6.1.0 (iOS;12.0;iPhone9,1)'
        ]);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $found_wallet = false;
        foreach ($result as $v) {
            if (array_key_exists('name', $v) && $v['name'] === 'wallet') {
                $found_wallet = true;
                $this->assertArrayHasKey('is_new_user', $v);
                $this->assertTrue($v['is_new_user']);
            }
        }
        $this->assertTrue($found_wallet);
    }

    public function testCanViewUser()
    {
        // 测试参数错误
        $ref_class = new ReflectionClass(PersonController::class);
        $instance = $ref_class->newInstance('person', 'test');
        $this->assertThrowsWithMessage(HttpException::class, '参数错误',
            function () use ($ref_class, $instance) {
                self::invokePrivateMethod($ref_class, 'canViewUser', 0, $instance);
            });
        // 测试用户不存在
        $this->assertThrowsWithMessage(HttpException::class, '用户不存在',
            function () use ($ref_class, $instance) {
                self::invokePrivateMethod($ref_class, 'canViewUser', 99999999, $instance);
            });
        // 测试账号已注销
        // 创建注销用户
        Mowangskuser::updateAll(['confirm' => new Expression('confirm | :confirm',
            [':confirm' => Mowangskuser::CONFIRM_DELETED])], 'id = :id', [':id' => self::TEST_USER_ID]);
        $this->assertThrowsWithMessage(HttpException::class, '账号已注销',
            function () use ($ref_class, $instance) {
                $config = [
                    'user_agent' => 'MissEvanApp/4.5.8 (iOS;11.4.1;iPhone8,4)',
                ];
                Yii::$app->equip->init($config);
                self::invokePrivateMethod($ref_class, 'canViewUser', self::TEST_USER_ID, $instance);
            });
        // 恢复正常用户
        Mowangskuser::updateAll(['confirm' => new Expression('confirm &~ :confirm',
            [':confirm' => Mowangskuser::CONFIRM_DELETED])], 'id = :id', [':id' => self::TEST_USER_ID]);

        // 测试 UP 主设置了隐私，无法访问
        // 创建隐私设置用户
        Mowangskuser::updateAll(['confirm' => new Expression('confirm | :confirm',
            [':confirm' => Mowangskuser::CONFIRM_PRIVACY])], 'id = :id', [':id' => self::TEST_USER_ID]);
        $this->assertThrowsWithMessage(HttpException::class, 'UP 主设置了隐私，无法访问',
            function () use ($ref_class, $instance) {
                self::invokePrivateMethod($ref_class, 'canViewUser', self::TEST_USER_ID, $instance);
            });
        // 恢复为正常用户
        Mowangskuser::updateAll(['confirm' => new Expression('confirm &~ :confirm',
            [':confirm' => Mowangskuser::CONFIRM_PRIVACY])], 'id = :id', [':id' => self::TEST_USER_ID]);
    }

    public function testHomepage()
    {
        // 测试正常返回
        $result = Request::api('/person/homepage', ['user_id' => self::TEST_USER_ID, 'position' => '3']);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $data = $result[0];
        $this->assertArrayHasKey('module_id', $data);
        $this->assertArrayHasKey('sort', $data);
        $this->assertArrayHasKey('title', $data);
        $this->assertArrayHasKey('type', $data);
        $this->assertArrayHasKey('elements_num', $data);
        $this->assertArrayHasKey('more', $data);
        $this->assertArrayHasKey('elements', $data);
        $this->assertNotEmpty($data['elements']);

        // 测试版本兼容
        // 测试 iOS 4.5.8 不单独返回其他模块数据
        $params = ['user_id' => self::TEST_USER_ID, 'position' => '4,5'];
        $result = Request::api('/person/homepage', $params, Request::GET, [
            'User-Agent' => 'MissEvanApp/4.5.8 (iOS;11.4.1;iPhone8,4)',
        ]);
        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // 测试 iOS 4.5.9 不单独返回其他模块数据
        $params = ['user_id' => self::TEST_USER_ID, 'position' => '4,5'];
        $result = Request::api('/person/homepage', $params, Request::GET, [
            'User-Agent' => 'MissEvanApp/4.5.9 (iOS;11.4.1;iPhone8,4)',
        ]);
        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // 测试 iOS 4.5.8 返回全部数据
        $params = ['user_id' => self::TEST_USER_ID, 'position' => '1,2,3'];
        $result = Request::api('/person/homepage', $params, Request::GET, [
            'User-Agent' => 'MissEvanApp/4.5.8 (iOS;11.4.1;iPhone8,4)',
        ]);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $EXPECTED = 7;
        $this->assertEquals($EXPECTED, count($result));

        // 测试 iOS 4.5.9 返回全部数据
        $params = ['user_id' => self::TEST_USER_ID, 'position' => '1,2,3'];
        $result = Request::api('/person/homepage', $params, Request::GET, [
            'User-Agent' => 'MissEvanApp/4.5.9 (iOS;11.4.1;iPhone8,4)',
        ]);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $EXPECTED = 7;
        $this->assertEquals($EXPECTED, count($result));

        // 测试 iOS 4.6.0 不返回个人主页模块
        $params = ['user_id' => self::TEST_USER_ID];
        $result = Request::api('/person/homepage', $params, Request::GET, [
            'User-Agent' => 'MissEvanApp/4.6.0 (iOS;11.4.1;iPhone8,4)',
        ]);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $EXPECTED = 6;
        $this->assertEquals($EXPECTED, count($result));
        // 断言没有个人主页模块
        $actual_module_types = array_column($result, 'module_type');
        foreach ($actual_module_types as $module_type) {
            $this->assertNotEquals(PersonController::MODULE_TYPE_USER_INFO, $module_type);
        }

        // 测试 Android 5.4.8 不单独返回其他模块数据
        $params = ['user_id' => self::TEST_USER_ID, 'position' => '4,5'];
        $result = Request::api('/person/homepage', $params, Request::GET, [
            'User-Agent' => 'MissEvanApp/5.4.8 (Android;6.0.1;OPPO OPPO R9sk R9sk)',
        ]);
        $this->assertIsArray($result);
        $this->assertEmpty($result);

        // 测试 Android 5.4.8 返回全部数据
        $params = ['user_id' => self::TEST_USER_ID, 'position' => '1,2,3'];
        $result = Request::api('/person/homepage', $params, Request::GET, [
            'User-Agent' => 'MissEvanApp/5.4.8 (Android;6.0.1;OPPO OPPO R9sk R9sk)',
        ]);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $EXPECTED = 7;
        $this->assertEquals($EXPECTED, count($result));

        // 测试 Android 5.4.9 不返回个人主页模块
        $params = ['user_id' => self::TEST_USER_ID];
        $result = Request::api('/person/homepage', $params, Request::GET, [
            'User-Agent' => 'MissEvanApp/5.4.9 (Android;6.0.1;OPPO OPPO R9sk R9sk)',
        ]);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $EXPECTED = 6;
        $this->assertEquals($EXPECTED, count($result));
        // 断言没有个人主页模块
        $actual_module_types = array_column($result, 'module_type');
        foreach ($actual_module_types as $module_type) {
            $this->assertNotEquals(PersonController::MODULE_TYPE_USER_INFO, $module_type);
        }
    }

    public function testGetHomepageModule()
    {
        // 删除缓存
        $key = MUtils::generateCacheKey(KEY_PERSON_HOMEPAGE, self::TEST_USER_ID);
        Yii::$app->memcache->delete($key);
        // 断言缓存不存在
        $this->assertFalse(Yii::$app->memcache->get($key));
        // 测试缓存不存在时，正常返回
        $ref_class = new ReflectionClass(PersonController::class);
        $instance = $ref_class->newInstance('person', 'test');
        // 他人查看 UP 主个人主页，并且 UP 主追剧和收藏是开启状态
        $result = self::invokePrivateMethod($ref_class, 'getHomepageModule',
            self::TEST_USER_ID, $instance);
        $this->assertNotEmpty($result);
        // 断言第三个模块是追剧模块
        $this->assertEquals(5, $result[3]['module_id']);
        $this->assertArrayNotHasKey('is_hidden', $result[3]);
        // 断言第四个模块是收藏模块
        $this->assertEquals(6, $result[4]['module_id']);
        $this->assertArrayNotHasKey('is_hidden', $result[4]);

        // 断言缓存存在
        $this->assertNotFalse(Yii::$app->memcache->get($key));
        // 测试缓存存在时，正常返回
        $result = self::invokePrivateMethod($ref_class, 'getHomepageModule', self::TEST_USER_ID, $instance);
        $this->assertNotEmpty($result);
    }

    public function testGetUserDrama()
    {
        $data = Request::api('/person/get-user-drama', ['user_id' => self::TEST_USER_ID]);
        $this->assertIsArray($data);
        $this->assertArrayHasKey('Datas', $data);
        $this->assertNotEmpty($data['Datas']);
        $this->assertArrayHasKey('pagination', $data);
    }

    public function testSearchUserDrama()
    {
        // 测试关键字为空的情况
        $this->assertThrowsWithMessage(HttpException::class, '搜索关键字不可为空', function () {
            Request::api('/person/search-user-drama', ['q' => '', 'user_id' => 0]);
        });

        // 测试关键字包含 emoji 的情况
        $this->assertThrowsWithMessage(HttpException::class, '搜索关键字不可为空', function () {
            Request::api('/person/search-user-drama', ['q' => ' 🌀😀🚀🌀 ', 'user_id' => 0]);
        });

        // 测试参数错误的情况
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () {
            Request::api('/person/search-user-drama', ['q' => '测试', 'user_id' => 0]);
        });

        // 测试正常返回
        $data = Request::api('/person/search-user-drama', ['user_id' => self::TEST_USER_ID, 'q' => '测试']);
        $this->assertIsArray($data);
        $this->assertArrayHasKey('Datas', $data);
        $this->assertNotEmpty($data['Datas']);
        $this->assertArrayHasKey('pagination', $data);
    }

    public function testSearchUserSound()
    {
        // 测试关键字为空的情况
        $this->assertThrowsWithMessage(HttpException::class, '搜索关键词不可为空', function () {
            Request::api('/person/search-user-sound', ['s' => '', 'user_id' => self::TEST_USER_ID]);
        });

        // 测试正常返回
        $data = Request::api('/person/search-user-sound', ['s' => '测试', 'user_id' => self::TEST_USER_ID]);
        $this->assertIsArray($data);
        $this->assertArrayHasKey('Datas', $data);
        $this->assertArrayHasKey('pagination', $data);
    }

    public function testGetUserAttention()
    {
        // 清理脏数据
        self::cleanData();
        // 用户 346286 关注用户 3013097
        Data::createMAttentionUser([
            'user_active' => self::TEST_USER_ID,
            'user_passtive' => self::TEST_USER2_ID,
        ]);
        // 更新用户粉丝数量，避免由于数据缺失导致获取不到数据的情况
        Mowangskuser::updateAllCounters(['follownum' => 1], ['id' => self::TEST_USER_ID]);

        $data = Request::api('/person/get-user-attention', ['user_id' => self::TEST_USER_ID]);
        $this->assertIsObject($data);
        $this->assertObjectHasAttribute('Datas', $data);
        $this->assertNotEmpty($data->Datas);
        $this->assertObjectHasAttribute('pagination', $data);
    }

    /**
     * @depends testGetUserAttention
     */
    public function testSearchAttentionUsers()
    {
        $this->loginByUserId(self::TEST_USER_ID);
        // 测试不输入搜索关键字
        $this->assertThrowsWithMessage(HttpException::class, '什么都没有找到', function () {
            Request::api('/person/search-attention-users', ['s' => '']);
        });

        // 测试正常返回
        $data = Request::api('/person/search-attention-users', ['s' => self::TEST_USERNAME2]);
        $this->assertObjectHasAttribute('Datas', $data);
        $this->assertNotEmpty($data->Datas);
        $this->assertObjectHasAttribute('pagination', $data);
    }

    public function testGetUserEntrance()
    {
        // MiMi App
        Yii::$app->equip->init(['user_agent' => 'MiMiApp/1.0.5 (Android;7.0;Meizu M6 M6)']);
        // 获取 mimi 追剧入口
        $result = PersonController::getUserEntrance(self::TEST_USER_ID, 'subscription');
        $this->assertEquals('mimi://user/346286/subscription', $result);

        // MissEvan App
        Yii::$app->equip->init(['user_agent' => self::TEST_USERAGENT]);
        // 获取 非 mimi 追剧入口
        $result = PersonController::getUserEntrance(self::TEST_USER_ID, 'subscription');
        $this->assertEquals('missevan://user/346286/subscription', $result);
    }

    public function testGetCollectAlbum()
    {
        $this->loginByUserId(self::TEST_USER_ID);
        // 测试 iOS < 4.7.8 Android < 5.6.7
        $result = Request::api('/person/get-collect-album', ['user_id' => self::TEST_USER_ID], Request::GET,
            ['User-Agent' => 'MissEvanApp/5.6.6 (Android;6.0.1;OPPO OPPO R9sk R9sk)']);
        $this->assertNotNull($result);
        $this->assertObjectHasAttribute('Datas', $result);
        $this->assertObjectHasAttribute('pagination', $result);

        // 测试 iOS >= 4.7.8 Android >= 5.6.7
        $result = Request::api('/person/get-collect-album', ['user_id' => self::TEST_USER_ID], Request::GET,
            ['User-Agent' => 'MissEvanApp/5.6.7 (Android;6.0.1;OPPO OPPO R9sk R9sk)']);
        $this->assertNotNull($result);
    }

    public function testGetUserAlbum()
    {
        $this->loginByUserId(self::TEST_USER_ID);
        // 测试 iOS < 4.7.8 Android < 5.6.7
        $result = Request::api('/person/get-user-album', ['user_id' => self::TEST_USER_ID], Request::GET,
            ['User-Agent' => 'MissEvanApp/5.6.6 (Android;6.0.1;OPPO OPPO R9sk R9sk)']);
        $this->assertNotNull($result);
        $this->assertObjectHasAttribute('Datas', $result);
        $this->assertObjectHasAttribute('pagination', $result);

        // 测试 iOS >= 4.7.8 Android >= 5.6.7
        $result = Request::api('/person/get-user-album', ['user_id' => self::TEST_USER_ID], Request::GET,
            ['User-Agent' => 'MissEvanApp/5.6.7 (Android;6.0.1;OPPO OPPO R9sk R9sk)']);
        $this->assertNotEmpty($result);
        $this->assertArrayHasKey('data', $result);
    }

    public function testSearchFavoriteSound()
    {
        // 创建测试音频
        $test_sound = Data::createSound([
            'soundstr' => '测试搜索我喜欢的音频',
            'user_id' => self::TEST_USER3_ID,
        ]);
        self::$test_sound_id = $test_sound->id;
        // 创建喜欢音频数据
        Data::createMLikeSound([
            'user_id' => self::TEST_USER2_ID,
            'sound_id' => self::$test_sound_id
        ]);
        $this->loginByUserId(self::TEST_USER2_ID);
        // 测试搜索关键字为空
        $this->assertThrowsWithMessage(HttpException::class, '什么都没有找到呢~', function () {
            Request::api('/person/search-favorite-sound', ['q' => '']);
        });

        // 测试搜索有结果
        $data = Request::api('/person/search-favorite-sound', ['q' => '搜索']);
        $this->assertNotEmpty($data);
        $this->assertObjectHasAttribute('Datas', $data);
        $this->assertObjectHasAttribute('pagination', $data);
        $this->assertNotEmpty($data->Datas);
        foreach ($data->Datas as $item) {
            $this->assertArrayHasKeys([
                'id', 'soundstr', 'duration', 'view_count', 'pay_type', 'username', 'front_cover', 'need_pay'
            ], $item);
        }
    }

    public function testShare()
    {
        self::loginByUserId();
        $TYPE_LIVE = 5;
        $data = Request::api('/person/share', ['type' => $TYPE_LIVE, 'element_id' => 123, 'url' => 'xxxx']);
        $this->assertEquals('分享成功', $data);
        $time = $_SERVER['REQUEST_TIME'];
        // 验证消息被添加
        $expected = [
            'type' => $TYPE_LIVE,
            'user_id' => self::TEST_USER_ID,
            'element_id' => 123,
            'url' => 'xxxx',
            'create_time' => $time,
        ];
        $databus_msg_key = 'share_detail_log:' . $time;
        $databus_queue = DataBus::getQueue();
        $this->assertEquals(1, count($databus_queue));
        $this->assertEquals($databus_msg_key, current($databus_queue)['key']);
        $this->assertEquals($expected, current($databus_queue)['msg']);

        DataBus::clearQueue();
        // 测试分享活动消息被成功写入
        $params = [
            'type' => 0,
            'element_id' => 0,
            'url' => 'https://www.fat.missevan.com/mevent/351?share_channel=wechat',
        ];
        $data = Request::api('/person/share', $params);
        $this->assertEquals('分享成功', $data);
        $time = $_SERVER['REQUEST_TIME'];
        // 验证消息被添加
        $expected = [
            'type' => 4,
            'user_id' => self::TEST_USER_ID,
            'element_id' => 351,
            'url' => 'https://www.fat.missevan.com/mevent/351?share_channel=wechat',
            'create_time' => $time,
        ];
        $databus_msg_key = 'share_detail_log:' . $time;
        $databus_queue = DataBus::getQueue();
        $this->assertEquals(1, count($databus_queue));
        $this->assertEquals($databus_msg_key, current($databus_queue)['key']);
        $this->assertEquals($expected, current($databus_queue)['msg']);
    }

    public function testDramaBought()
    {
        self::loginByUserId(self::TEST_USER2_ID);
        // 测试 Android < 5.7.3
        $data = Request::api('/person/drama-bought', [], 'GET',
            ['User-Agent' => 'MissEvanApp/5.7.1 (Android;7.0;Meizu M6 M6)']);
        $this->assertIsObject($data);
        $this->assertObjectHasAttribute('Datas', $data);

        // 测试 Android >= 5.7.3
        $data = Request::api('/person/drama-bought', [], 'GET',
            ['User-Agent' => 'MissEvanApp/5.7.3 (Android;7.0;Meizu M6 M6)']);
        $this->assertIsArray($data);
        $this->assertArrayHasKeys(['data', 'pagination', 'delete_count'], $data);
    }

    public function testDramaRecover()
    {
        self::loginByUserId(self::TEST_USER2_ID);
        $data = Request::api('/person/drama-recover', [], 'GET',
            ['User-Agent' => 'MissEvanApp/5.7.3 (Android;7.0;Meizu M6 M6)']);
        $this->assertIsArray($data);
        $this->assertArrayHasKeys(['data', 'pagination'], $data);
    }

    public function testHideDramaPurchaseOrder()
    {
        self::loginByUserId(self::TEST_USER2_ID);
        // 测试参数错误
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () {
            Request::api('/person/hide-drama-purchase-order', [], 'post',
                ['User-Agent' => 'MissEvanApp/5.7.3 (Android;7.0;Meizu M6 M6)']);
        });

        // 测试删除成功
        $data = Request::api('/person/hide-drama-purchase-order', ['ids' => [Data::TEST_DRAMA_ID1]], 'post',
            ['User-Agent' => 'MissEvanApp/5.7.3 (Android;7.0;Meizu M6 M6)']);
        $this->assertEquals('删除成功', $data);
    }

    public function testRecoverDramaPurchaseOrder()
    {
        self::loginByUserId(self::TEST_USER2_ID);
        // 测试参数错误
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () {
            Request::api('/person/recover-drama-purchase-order', [], 'post',
                ['User-Agent' => 'MissEvanApp/5.7.3 (Android;7.0;Meizu M6 M6)']);
        });

        // 测试恢复成功
        $data = Request::api('/person/recover-drama-purchase-order', ['ids' => [Data::TEST_DRAMA_ID1]], 'post',
            ['User-Agent' => 'MissEvanApp/5.7.3 (Android;7.0;Meizu M6 M6)']);
        $this->assertEquals('恢复成功', $data);
    }

    public function testDelHistory()
    {
        self::loginByUserId(self::TEST_USER2_ID);

        // 测试参数错误
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () {
            Request::api('/person/del-history', ['type' => 9, 'ids' => [1, 2]], Request::POST,
                ['User-Agent' => 'MissEvanApp/5.7.3 (Android;7.0;Meizu M6 M6)']);
        });

        Rpc::registerRpcApiResponseFunc(ServiceRpc::API_DEL_PLAY_HISTORY, function () {
            return new RpcResult('删除成功');
        });
        // 测试通过 ID 删除
        $params = ['type' => PersonController::DELETE_HISTORY_TYPE_DEL, 'ids' => [1, 2]];
        $data = Request::api('/person/del-history', $params, Request::POST,
            ['User-Agent' => 'MissEvanApp/5.7.3 (Android;7.0;Meizu M6 M6)']);
        $this->assertEquals('删除成功', $data);

        Rpc::registerRpcApiResponseFunc(ServiceRpc::API_CLEAR_PLAY_HISTORY, function () {
            return new RpcResult('删除成功');
        });
        // 测试全部删除
        $params = ['type' => PersonController::DELETE_HISTORY_TYPE_CLEAR, 'ids' => [1, 2]];
        $data = Request::api('/person/del-history', $params, Request::POST,
            ['User-Agent' => 'MissEvanApp/5.7.3 (Android;7.0;Meizu M6 M6)']);
        $this->assertEquals('删除成功', $data);
    }

    public function testCountAction()
    {
        self::loginByUserId(self::TEST_USER_ID);
        // 删除缓存数据
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_USER_FEED_LAST_REQUEST_TIME, self::TEST_USER_ID);
        $redis->del($key);

        $_SERVER['REQUEST_TIME'] = 1683561600;
        $res = Request::api('/person/count-action', [], Request::GET);
        $this->assertNotEmpty($res);
        $this->assertArrayHasKeys(['collection_count', 'history_count', 'drama_bought_count'], $res);
        // 断言已存储访问时间
        $data = $redis->get($key);
        $this->assertNotFalse($data);
        $this->assertEquals($_SERVER['REQUEST_TIME'], (int)$data);
        // 断言过期时间大于 0
        $expire_duration = $redis->ttl($key);
        $this->assertGreaterThan(0, $expire_duration);

        // 更新访问时间后，断言过期时间大于 0
        $_SERVER['REQUEST_TIME'] = 1683648000;
        $res = Request::api('/person/count-action', [], Request::GET);
        $this->assertNotEmpty($res);
        $this->assertArrayHasKeys(['collection_count', 'history_count', 'drama_bought_count'], $res);
        $data = $redis->get($key);
        $this->assertNotFalse($data);
        $this->assertEquals($_SERVER['REQUEST_TIME'], (int)$data);
        $expire_duration = $redis->ttl($key);
        $this->assertGreaterThan(0, $expire_duration);
    }

    public function testActionSobotOfflineMsgCount()
    {
        Rpc::registerRpcApiResponseFunc(ServiceRpc::API_SOBOT_GET_NOTICE, function ($params) {
            if ($params['visitor_id'] === self::TEST_VISITOR_ID_1) {
                throw new RpcNetworkException('网络错误');
            }
            if ($params['visitor_id'] === self::TEST_VISITOR_ID_2) {
                return new RpcResult(['unread_count' => 4]);
            }
            return new RpcResult(['unread_count' => 2]);
        });

        $api = '/person/sobot-offline-msg-count';

        // 测试安卓 < 6.1.5 版本时（期望直接返回 count 为 0）
        $headers = ['User-Agent' => 'MissEvanApp/6.1.4 (Android;6.0.1;OPPO OPPO R9sk R9sk)'];
        $res = Request::api($api, ['sobot_visitor_id' => self::TEST_VISITOR_ID_2], Request::GET, $headers);
        $this->assertIsArray($res);
        $this->assertNotEmpty($res);
        $this->assertArrayHasKeys(['count'], $res);
        $this->assertEquals(0, $res['count']);

        // 测试 6.1.5 及以上版本
        $headers = ['User-Agent' => 'MissEvanApp/6.1.5 (Android;6.0.1;OPPO OPPO R9sk R9sk)'];
        // 测试未登录，没有传 sobot_visitor_id 时
        $res = Request::api($api, [], Request::GET, $headers);
        $this->assertIsArray($res);
        $this->assertNotEmpty($res);
        $this->assertArrayHasKeys(['count'], $res);
        $this->assertEquals(0, $res['count']);

        // 测试未登录，RPC 请求出错时
        $res = Request::api($api, ['sobot_visitor_id' => self::TEST_VISITOR_ID_1], Request::GET, $headers);
        $this->assertIsArray($res);
        $this->assertNotEmpty($res);
        $this->assertArrayHasKeys(['count'], $res);
        $this->assertEquals(0, $res['count']);

        // 测试未登录，请求成功
        $res = Request::api($api, ['sobot_visitor_id' => self::TEST_VISITOR_ID_2], Request::GET, $headers);
        $this->assertIsArray($res);
        $this->assertNotEmpty($res);
        $this->assertArrayHasKeys(['count'], $res);
        $this->assertEquals(4, $res['count']);

        // 删除智齿访客信息数据
        MSobotUser::deleteAll(['user_id' => self::TEST_USER_ID]);
        self::loginByUserId(self::TEST_USER_ID);
        // 测试已登录，没有传 sobot_visitor_id 时 (数据库中没有登录用户的 sobot_visitor_id 时)
        $res = Request::api($api, [], Request::GET, $headers);
        $this->assertIsArray($res);
        $this->assertNotEmpty($res);
        $this->assertArrayHasKeys(['count'], $res);
        $this->assertEquals(0, $res['count']);

        // 测试已登录，RPC 请求出错时
        $res = Request::api($api, ['sobot_visitor_id' => self::TEST_VISITOR_ID_1], Request::GET, $headers);
        $this->assertIsArray($res);
        $this->assertNotEmpty($res);
        $this->assertArrayHasKeys(['count'], $res);
        $this->assertEquals(0, $res['count']);

        // 删除智齿访客信息数据
        MSobotUser::deleteAll(['user_id' => self::TEST_USER2_ID]);
        // 创建智齿访客信息数据
        Data::createMSobotUser(['user_id' => self::TEST_USER2_ID, 'visitor_id' => self::TEST_VISITOR_ID_3]);

        self::loginByUserId(self::TEST_USER2_ID);
        // 测试已登录，没有传 sobot_visitor_id 时，使用数据库存储的 sobot_visitor_id
        $res = Request::api($api, ['sobot_visitor_id' => self::TEST_VISITOR_ID_1], Request::GET, $headers);
        $this->assertIsArray($res);
        $this->assertNotEmpty($res);
        $this->assertArrayHasKeys(['count'], $res);
        $this->assertEquals(2, $res['count']);
    }

    public function testActionUpdateSobotStatus()
    {
        Rpc::registerRpcApiResponseFunc(ServiceRpc::API_SOBOT_UPDATE_STATUS, function ($params) {
            if ($params['visitor_id'] === self::TEST_VISITOR_ID_1) {
                throw new RpcNetworkException('网络错误');
            }
            return new RpcResult(['msg' => '更新成功']);
        });

        $api = '/person/update-sobot-status';

        // 测试安卓 < 6.1.5 版本时（期望直接返回更新成功）
        $headers = ['User-Agent' => 'MissEvanApp/6.1.4 (Android;6.0.1;OPPO OPPO R9sk R9sk)'];
        $res = Request::api($api, [], Request::POST, $headers);
        $this->assertEquals('更新成功', $res);

        // 测试 6.1.5 及以上版本
        $headers = ['User-Agent' => 'MissEvanApp/6.1.5 (Android;6.0.1;OPPO OPPO R9sk R9sk)'];
        // 测试更新智齿未读消息状态参数错误
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () use ($api, $headers) {
            Request::api($api,
                [
                    'type' => PersonController::UPDATE_SOBOT_TYPE_MSG_STATUS,
                    'partner_id' => '',
                    'sobot_visitor_id' => '',
                ],
                Request::POST, $headers
            );
        });

        // 测试更新智齿未读消息状态失败
        $res = Request::api($api,
            [
                'type' => PersonController::UPDATE_SOBOT_TYPE_MSG_STATUS,
                'partner_id' => '',
                'sobot_visitor_id' => self::TEST_VISITOR_ID_1,
            ],
            Request::POST, $headers);
        $this->assertEquals('更新失败', $res);

        // 测试更新智齿未读消息状态成功
        $res = Request::api($api,
            [
                'type' => PersonController::UPDATE_SOBOT_TYPE_MSG_STATUS,
                'partner_id' => '',
                'sobot_visitor_id' => self::TEST_VISITOR_ID_2,
            ],
            Request::POST, $headers);
        $this->assertEquals('更新成功', $res);

        self::loginByUserId(self::TEST_USER2_ID);
        $partner_id = UserAddendum::getSobotPartnerId(self::TEST_USER2_ID);

        // 测试已登录，更新登录用户的智齿 visitor_id 时 partner_id 参数错误
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () use ($api, $headers) {
            Request::api($api,
                [
                    'type' => PersonController::UPDATE_SOBOT_TYPE_MSG_STATUS,
                    'partner_id' => '123',
                    'sobot_visitor_id' => self::TEST_VISITOR_ID_2,
                ],
                Request::POST, $headers);
        });

        // 测试已登录，更新智齿未读消息状态时传入的 visitor_id 参数与数据库中的 visitor_id 不一致时
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () use ($api, $partner_id, $headers) {
            Request::api($api,
                [
                    'type' => PersonController::UPDATE_SOBOT_TYPE_MSG_STATUS,
                    'partner_id' => $partner_id,
                    'sobot_visitor_id' => self::TEST_VISITOR_ID_2,
                ],
                Request::POST, $headers
            );
        });

        // 测试已登录，更新智齿未读消息状态时传入的 visitor_id 参数与数据库中的 visitor_id 一致时
        $res = Request::api($api,
            [
                'type' => PersonController::UPDATE_SOBOT_TYPE_MSG_STATUS,
                'partner_id' => $partner_id,
                'sobot_visitor_id' => self::TEST_VISITOR_ID_3,
            ],
            Request::POST, $headers
        );
        $this->assertEquals('更新成功', $res);

        // 测试已登录，更新登录用户的智齿 visitor_id 时 partner_id 参数错误
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () use ($api, $headers) {
            Request::api($api,
                [
                    'type' => PersonController::UPDATE_SOBOT_TYPE_VISITOR_ID,
                    'partner_id' => '123',
                    'sobot_visitor_id' => self::TEST_VISITOR_ID_2,
                ],
                Request::POST, $headers);
        });

        // 测试已登录，更新登录用户的智齿 visitor_id 时更新成功
        $res = Request::api($api,
            [
                'type' => PersonController::UPDATE_SOBOT_TYPE_VISITOR_ID,
                'partner_id' => $partner_id,
                'sobot_visitor_id' => self::TEST_VISITOR_ID_2,
            ],
            Request::POST, $headers);
        $this->assertEquals('更新成功', $res);

        // 验证数据库中保存的 visitor_id
        $m_sobot_user = MSobotUser::findOne(['user_id' => self::TEST_USER2_ID]);
        $this->assertEquals(self::TEST_VISITOR_ID_2, $m_sobot_user->visitor_id);
    }
}
