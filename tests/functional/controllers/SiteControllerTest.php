<?php

namespace tests\controllers;

use app\components\db\RedisConnection;
use app\components\util\MUtils;
use app\components\util\SSOClient;
use app\controllers\SiteController;
use app\models\AdTrack;
use app\models\AdTrackBilibili;
use app\models\Blacklist;
use app\models\Catalog;
use app\models\Drama;
use app\models\InstallBuvid;
use app\models\InstallLog;
use app\models\LaunchReportLog;
use app\models\Live;
use app\models\MAppIcon;
use app\models\MAppupdate;
use app\models\MHomepageIcon;
use app\models\MLaunch;
use app\models\Mowangskuser;
use app\models\MPersonaModuleElement;
use app\models\MPowerSoundIp;
use app\models\MRecommendedElements;
use app\models\MSound;
use app\models\MTab;
use app\models\MPowerSound;
use app\models\MTabBarPackage;
use app\models\MUserConfig;
use app\models\Persona;
use app\models\UserAddendum;
use app\models\YouMightLikeModule;
use missevan\rpc\LiveRpc;
use missevan\rpc\Rpc;
use missevan\rpc\RpcNetworkException;
use missevan\rpc\RpcResult;
use missevan\rpc\ServiceRpc;
use missevan\util\MUtils as MUtils2;
use ReflectionClass;
use Exception;
use tests\components\util\Data;
use tests\components\util\Equipment;
use tests\components\web\Request;
use tests\components\UnitTestCase;
use yii\helpers\Json;
use yii\web\HttpException;
use Yii;

class SiteControllerTest extends UnitTestCase
{
    const CONFIG_OMIKUJI_SHARE_TEXT_CONTENT = '{"2":{"平":"#撒野# 1111","末吉":"#撒野# 1111"}';
    const CONFIG_HTTPDNS = '{"hosts":["app.uat.missevan.com","fm.uat.missevan.com","static-test.missevan.com"]}';
    // 测试 Tab ID
    const TEST_ICON_TAB_ID = 101;
    const TEST_EQUIP_ID2 = '01e1b22d-b49d-4d27-fce5-90e085714cd5';
    const TEST_EQUIP_ID3 = '01e1b22d-b49d-4d27-fce5-90e085714cd6';
    const TEST_BUVID = 'XYDC0C4D4DCA12FA9C23A4266D50C1FC3E2E2';
    const TEST_BUVID2 = UnitTestCase::TEST_BUVID;
    const TEST_IP_NAME = '大家好';
    const TEST_POWER_SOUND_ID = 1;
    const TEST_IP_ID = 2;
    const NEWUSER_TAB_USER_ALLOWLIST_USER_ID = 1234;

    /**
     * @var null|RedisConnection
     */
    private static $redis = null;
    private static $memcache = null;

    private static $old_icons_data;
    private static $m_launch;

    private static $test_you_might_like_module_1;
    private static $test_you_might_like_module_2;
    private static $test_m_persona_module_element_1;
    private static $test_m_persona_module_element_2;
    private static $test_m_recommended_element_1;
    private static $test_m_recommended_element_2;
    private static $test_m_recommended_element_3;
    private static $test_m_recommended_element_4;
    private static $test_sound_1;
    private static $test_sound_2;
    private static $test_live_1;
    private static $test_live_2;
    private static $test_new_user_modules_key;
    private static $test_new_user_modules_key_old;
    private static $banner_config;

    private static $origin_db;
    private static $origin_db1;
    private static $origin_growthdb;
    private static $origin_logdb;
    private static function setOriginDbs(): void
    {
        self::$origin_db = Yii::$app->db;
        Yii::$app->set('db', Yii::$app->sqlitedb);
        self::$origin_db1 = Yii::$app->db1;
        Yii::$app->set('db1', Yii::$app->sqlitedb1);
        self::$origin_growthdb = Yii::$app->growthdb;
        Yii::$app->set('growthdb', Yii::$app->sqlite_growthdb);
        self::$origin_logdb = Yii::$app->logdb;
        Yii::$app->set('logdb', Yii::$app->sqlite_logdb);
    }

    private static function resetOriginDbs(): void
    {
        Yii::$app->set('db', self::$origin_db);
        Yii::$app->set('db1', self::$origin_db1);
        Yii::$app->set('growthdb', self::$origin_growthdb);
        Yii::$app->set('logdb', self::$origin_logdb);
    }

    protected function _before()
    {
        parent::_before();
        self::setOriginDbs();
    }

    protected function _after()
    {
        parent::_after();
        Yii::$app->set('db', self::$origin_db);
        self::resetOriginDbs();
    }

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        self::setOriginDbs();
        self::$banner_config = Yii::$app->params['new_user']['banner'];
        self::$redis = Yii::$app->redis;
        self::$memcache = Yii::$app->memcache;
        // 保存历史首页 icon 数据（测试 /site/icons 接口时此值会被改变）
        self::$old_icons_data = self::$redis->get(KEY_APP_HOMEPAGE_ICONS);
        self::clearData();

        // 设置下发配置测试数据
        $key_ios_config = self::$redis->generateKey(KEY_CONFIG_CLIENT, Equipment::iOS);
        $key_android_config = self::$redis->generateKey(KEY_CONFIG_CLIENT, Equipment::Android);
        self::$redis->hSet($key_ios_config, 'omikuji_share_text', self::CONFIG_OMIKUJI_SHARE_TEXT_CONTENT);
        self::$redis->hSet($key_android_config, 'code_test', '@Yii::$app->equip->isFromMiMiApp()');
        self::$redis->hSet($key_android_config, 'error_code_test', '@Yii::$app->');
        self::$redis->hSet($key_android_config, 'murmurhash_test', '@missevan\util\MUtils::murmurHash3_32("example_buvidpriority_1710746757747") % 100');
        self::$redis->hSet($key_android_config, 'omikuji_share_text', self::CONFIG_OMIKUJI_SHARE_TEXT_CONTENT);
        self::$redis->hSet($key_android_config, 'httpdns', self::CONFIG_HTTPDNS);
        // 关闭青少年状态
        Mowangskuser::updateAll(['confirm' => 2], ['id' => self::TEST_USER_ID]);
        // 创建 Tab 测试数据
        $time = $_SERVER['REQUEST_TIME'];
        Data::createTab([
            'id' => 1,
            'title' => '推荐',
            'url' => 'missevan://homepage',
            'sort' => 1,
            'active' => MTab::ACTIVE,
            'position' => MTab::POSITION_APP_HOMEPAGE_TOP,
            'create_time' => $time,
            'modified_time' => $time,
        ]);
        Data::createTab([
            'id' => 2,
            'title' => '分类',
            'url' => 'missevan://catalogs',
            'sort' => 2,
            'active' => MTab::INACTIVE,
            'position' => MTab::POSITION_APP_HOMEPAGE_TOP,
            'create_time' => $time,
            'modified_time' => $time,
        ]);
        Data::createTab([
            'id' => 3,
            'title' => '直播',
            'url' => 'missevan://live',
            'sort' => 0,
            'active' => MTab::INACTIVE,
            'position' => MTab::POSITION_APP_HOMEPAGE_TOP,
            'create_time' => $time,
            'modified_time' => $time,
        ]);
        // 创建 Icon 测试数据
        Data::createIcon([
            'id' => 1,
            'tab_id' => self::TEST_ICON_TAB_ID,
            'title' => '索引',
            'url' => 'missevan://drama/filter',
            'icon' => 'http://static.missevan.com/profile/icon01.png',
            'dark_icon' => 'http://static.missevan.com/profile/icon01.png',
            'type' => MHomepageIcon::TYPE_ICON_TAB,
            'sort' => 1,
            'create_time' => $time,
            'modified_time' => $time,
        ]);
        Data::createIcon([
            'id' => 2,
            'tab_id' => self::TEST_ICON_TAB_ID,
            'title' => '时间表',
            'url' => 'missevan://drama/timeline',
            'icon' => 'http://static.missevan.com/profile/icon01.png',
            'dark_icon' => 'http://static.missevan.com/profile/icon01.png',
            'type' => MHomepageIcon::TYPE_ICON_TAB,
            'sort' => 0,
            'create_time' => $time,
            'modified_time' => $time,
        ]);

        // 添加测试启动音 IP
        $model = new MPowerSoundIp();
        $model->id = self::TEST_IP_ID;
        $model->archive = MPowerSoundIp::ARCHIVE_ONLINE;
        $model->ip_name = '测试';
        $model->sort_order = 0;
        if (!$model->save()) {
            throw new Exception(MUtils::getFirstError($model));
        }

        // 添加测试启动音
        $test_sound = Data::createSound();
        $test_sound->soundurl_64 = 'oss://aod/test.m4a';
        $test_sound->checked = MSound::CHECKED_PASS;
        if (!$test_sound->save()) {
            throw new Exception('创建测试音频失败');
        }
        $sound_data = [
            'icon' =>
                'oss://system/app/powerSound/202102/20/673bfe2b2cc419af4b176b4297fe0896105132.png',
            'cover' =>
                'oss://launch/images/202102/20/8f02ca74a7843bc1e8cf30fcb3ca5f56105207.png',
            'sound_id' => $test_sound->id,
            'intro' => '大家好，这是测试数据',
            'archive' => MPowerSound::ARCHIVE_ONLINE,
            'cv' => '测试 CV',
            'role_name' => '测试角色',
            'sort_order' => 0,
            'ip_id' => self::TEST_IP_ID,
            'create_time' => $_SERVER['REQUEST_TIME'],
            'modified_time' => $_SERVER['REQUEST_TIME'],
            'playurl' => 'upos://mefmexboss/test.m4a'
        ];
        $model = new MPowerSound();
        $model->attributes = $sound_data;
        $model->id = self::TEST_POWER_SOUND_ID;
        if (!$model->save()) {
            throw new Exception(MUtils::getFirstError($model));
        }
        self::$m_launch = Data::createMLaunch([
            'redirect_url' => Yii::$app->params['domainMissevan'] . '/mtopic/131',
            'message' => '跳转地址',
        ]);
        $live_module = Data::createYouMightLikeModule([
            'title' => '正在直播',
            'element_type' => MPersonaModuleElement::ELEMENT_TYPE_LIVING,
        ]);
        if (!$live_module) {
            throw new Exception(MUtils2::getFirstError($live_module));
        }
        $live_element = Data::createMPersonaModuleElement([
            'persona_id' => Persona::TYPE_BOY,
            'module_id' => $live_module->id,
            'summary' => 'live_test',
            'sort' => 1,
        ]);
        if (!$live_element) {
            throw new Exception(MUtils2::getFirstError($live_element));
        }

        // 设置渠道数据
        $redis = Yii::$app->redis;
        $recommend_popup_key = $redis->generateKey(KEY_RECOMMEND_POPUP_EQUIP, self::TEST_EQUIP_ID3);
        $redis->set($recommend_popup_key, 'tab:live');
    }

    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
        self::setOriginDbs();
        self::clearData();
        self::resetOriginDbs();

        // 恢复广告条配置
        Yii::$app->params['new_user']['banner'] = self::$banner_config;
        // 恢复历史首页 icon 数据
        self::$redis->set(KEY_APP_HOMEPAGE_ICONS, self::$old_icons_data);
        // 还原原有 equip_id
        self::resetEquipID();
        // 清理安装记录
        InstallBuvid::deleteAll(['buvid' => self::TEST_BUVID]);
        if (self::$m_launch) {
            MLaunch::deleteAll('id = :id', [':id' => self::$m_launch->id]);
        }
    }

    private static function clearData()
    {
        // 清除渠道数据
        Blacklist::model()->removeChannel(Blacklist::TYPE_CHANNEL_AUDIT_SENSITIVE_HIDE,
            'missevan_huawei', '5.3.0');
        // 清除配置下发的测试数据
        $key_ios_config = self::$redis->generateKey(KEY_CONFIG_CLIENT, Equipment::iOS);
        $key_android_config = self::$redis->generateKey(KEY_CONFIG_CLIENT, Equipment::Android);
        self::$redis->del($key_ios_config, $key_android_config);
        Mowangskuser::updateAll(['confirm' => 2], ['id' => self::TEST_USER_ID]);
        // 清除首页 Tab 缓存数据
        Yii::$app->memcache->delete(KEY_SITE_TAB_LIST);
        // 清除 Icon 缓存数据
        Yii::$app->memcache->delete(MUtils::generateCacheKey(KEY_TAB_ICON_LIST, self::TEST_ICON_TAB_ID));
        // 清除 Tab 数据
        $TEST_TAB_ID1 = 1;
        $TEST_TAB_ID2 = 2;
        $TEST_TAB_ID3 = 3;
        MTab::deleteAll(['id' => [$TEST_TAB_ID1, $TEST_TAB_ID2, $TEST_TAB_ID3]]);
        // 清除 Icon 数据
        $TEST_ICON_ID1 = 1;
        $TEST_ICON_ID2 = 2;
        MHomepageIcon::deleteAll(['id' => [$TEST_ICON_ID1, $TEST_ICON_ID2]]);

        // 清理启动音数据
        MPowerSoundIp::deleteAll('ip_name = :ip_name', [':ip_name' => self::TEST_IP_NAME]);
        MPowerSoundIp::deleteAll('id = :id', [':id' => self::TEST_IP_ID]);
        MPowerSound::deleteAll('id = :id', [':id' => self::TEST_POWER_SOUND_ID]);

        // 清理用户配置数据
        MUserConfig::deleteAll('user_id IN (:user_id_1, :user_id_2) OR buvid = :buvid', [
            ':user_id_1' => self::TEST_USER_ID,
            ':user_id_2' => self::TEST_USER2_ID,
            ':buvid' => self::TEST_BUVID2,
        ]);
        // 清理“正在直播”模块数据
        YouMightLikeModule::deleteAll('element_type = :element_type',
            [':element_type' => MPersonaModuleElement::ELEMENT_TYPE_LIVING]);
        MPersonaModuleElement::deleteAll('summary = :summary', [':summary' => 'live_test']);

        $key = MUtils::generateCacheKey(KEY_NEW_POWER_SOUND, ...[1, MPowerSound::DEFAULT_PAGE_SIZE]);
        self::$memcache->delete($key);
        self::$memcache->delete(KEY_APP_LAUNCH_SPLASH);
        self::$memcache->delete(MUtils2::generateCacheKey(KEY_ALIYUN_SERVICE_IP, 'hk'));
        self::$memcache->delete(KEY_APPLE_SEARCH_ADS_ACCESS_TOKEN);
        self::$memcache->delete(KEY_APPLE_SEARCH_ADS_ACL_DATA);

        // 删除渠道数据
        $redis = Yii::$app->redis;
        $recommend_popup_key = $redis->generateKey(KEY_RECOMMEND_POPUP_EQUIP, self::TEST_EQUIP_ID3);
        $redis->del($recommend_popup_key);

        // 删除新人测试数据
        self::$memcache->delete(self::$test_new_user_modules_key);
        if (self::$test_you_might_like_module_1 || self::$test_you_might_like_module_2) {
            YouMightLikeModule::deleteAll(['id' => array_filter(
                [self::$test_you_might_like_module_1->id ?? null,
                    self::$test_you_might_like_module_2->id ?? null]
            )]);
        }
        MPersonaModuleElement::deleteAll(['persona_id' => MPersonaModuleElement::PERSONA_ID_NEW_USER]);
        if (self::$test_m_persona_module_element_1 || self::$test_m_persona_module_element_2) {
            MPersonaModuleElement::deleteAll(['id' => array_filter(
                [self::$test_m_persona_module_element_1->id ?? null,
                    self::$test_m_persona_module_element_2->id ?? null]
            )]);
        }
        if (self::$test_m_recommended_element_1
                || self::$test_m_recommended_element_2
                || self::$test_m_recommended_element_3
                || self::$test_m_recommended_element_4) {
            MRecommendedElements::deleteAll(['id' => array_filter(
                [self::$test_m_recommended_element_1->id ?? null,
                    self::$test_m_recommended_element_2->id ?? null,
                    self::$test_m_recommended_element_3->id ?? null,
                    self::$test_m_recommended_element_4->id ?? null]
            )]);
        }
        if (self::$test_sound_1 || self::$test_sound_2) {
            MSound::deleteAll(['id' => array_filter(
                [self::$test_sound_1->id ?? null,
                    self::$test_sound_2->id ?? null]
            )]);
        }
        // 删除测试直播间
        Live::deleteAll(['id' => [self::TEST_USER_ID, self::TEST_USER2_ID]]);

        // 删除测试应用图标
        MAppIcon::deleteAll();
        // 删除测试应用底部图标
        MTabBarPackage::deleteAll();
    }

    public function testActionLaunch()
    {
        $expected_data = [
            'theme_skin' => [
                'id' => 1,
                'package_url' => 'https://static-test.maoercdn.com/app/themeskin/202412/12/tcu8461ccs2497829dbe9541b55bdc8d170256.zip',
            ],
        ];
        Rpc::registerRpcApiResponseFunc(ServiceRpc::API_GET_THEME_SKIN,
            function ($data) use ($expected_data) {
                if ($data['user_id'] === 0) {
                    return ['theme_skin' => null];
                } elseif ($data['user_id'] === self::TEST_USER2_ID) {
                    throw new RpcNetworkException('网络错误');
                }
                return new RpcResult($expected_data);
            });

        // 测试未登录时获取用户 ID
        $data = Request::api('/site/launch');
        $this->assertIsArray($data);
        $this->assertArrayHasKey('user_id', $data, '预期返回值包含用户状态');
        $this->assertEquals(null, $data['user_id'], '预期用户状态为登录失效');
        $this->assertEquals(-1, $data['teenager_status']);
        $this->assertNull($data['theme_skin']);

        // 预期返回值包含用户状态且为 true
        self::loginByUserID();
        $data = Request::api('/site/launch');
        $this->assertIsArray($data);
        $this->assertArrayHasKey('user_id', $data, '预期返回值包含用户状态');
        $this->assertIsNumeric($data['user_id'], '预期用户状态为登录');
        $this->assertEquals($expected_data['theme_skin'], $data['theme_skin']);

        // 测试青少年是否获取正确
        $this->assertEquals(SSOClient::TEENAGER_STATUS_CLOSE, $data['teenager_status']);
        Mowangskuser::updateAll(['confirm' => Mowangskuser::CONFIRM_TEENAGER], ['id' => self::TEST_USER_ID]);
        $data = Request::api('/site/launch');
        $this->assertEquals(SSOClient::TEENAGER_STATUS_OPEN, $data['teenager_status']);

        $data = Request::api('/site/launch');
        $this->assertIsArray($data);
        $this->assertArrayHasKey('new_version', $data);
        $this->assertArrayHasKey('sound_url', $data);
        $this->assertArrayHasKey('pic_url', $data);
        $this->assertArrayHasKey('privacy_data', $data);

        // 测试 Android 小于 5.1.0 版本时，提示强制下载
        $data = Request::api('/site/launch', [], 'GET',
            ['User-Agent' => 'MissEvanApp/5.0.9 (Android;7.0;Meizu M6 M6)']);
        $this->assertEquals(MAppupdate::FORCE_DOWNLOAD_YES, $data['new_version']['force_download']
            , '预期版本过低时，强制下载');
        // 测试 iOS 小于 4.2.4 版本时，提示下载
        $data = Request::api('/site/launch', [], 'GET',
            ['User-Agent' => 'MissEvanApp/4.2.3 (iOS;12.2;iPhone9,1)']);
        $this->assertEquals(MAppupdate::FORCE_DOWNLOAD_YES, $data['new_version']['force_download']
            , '预期版本过低时，强制下载');

        // 测试 Android < 5.5.5 版本时
        Yii::$app->memcache->delete(KEY_APP_LAUNCH_SPLASH);
        $data = Request::api('/site/launch', [], 'GET',
            ['User-Agent' => 'MissEvanApp/5.5.4 (Android;7.0;Meizu M6 M6)']);
        $this->assertIsArray($data);
        $this->assertArrayHasKeys(['pic_url', 'sound_url'], $data);

        // 测试 Android >= 5.5.6 版本时
        Yii::$app->memcache->delete(KEY_APP_LAUNCH_SPLASH);
        $data = Request::api('/site/launch', [], 'GET',
            ['User-Agent' => 'MissEvanApp/5.5.6 (Android;7.0;Meizu M6 M6)']);
        $this->assertIsArray($data);
        $this->assertArrayHasKeys(['splash', 'sound_url'], $data);
        $this->assertArrayHasKeys(['id', 'pic_url', 'redirect_url', 'label', 'no_skip', 'message', 'launch_type'],
            $data['splash']);
        $this->assertArrayNotHasKey('pic_url', $data);

        // 测试 Android >= 5.6.0 版本时
        Yii::$app->memcache->delete(KEY_APP_LAUNCH_SPLASH);
        $data = Request::api('/site/launch', [], 'GET',
            ['User-Agent' => 'MissEvanApp/5.6.0 (Android;7.0;Meizu M6 M6)']);
        $this->assertIsArray($data);
        $this->assertIsArray($data['splash']);
        $this->assertArrayHasKeys(['splash', 'sound_url', 'splash_ready', 'season'], $data);
        $this->assertArrayHasKeys(['id', 'pic_url', 'redirect_url', 'label', 'no_skip', 'message', 'launch_type'],
            $data['splash'][0]);
        $this->assertArrayNotHasKey('pic_url', $data);

        // 测试 iOS >= 6.1.3 版本时下发应用图标和应用底部图标
        Data::createAppIcon([
            'end_time' => $_SERVER['REQUEST_TIME'] + ONE_HOUR,
        ]);
        Data::createTabBarPackage([
            'end_time' => $_SERVER['REQUEST_TIME'] + ONE_HOUR,
        ]);
        Yii::$app->memcache->delete(KEY_APP_LAUNCH_SPLASH);
        Yii::$app->memcache->delete(KEY_APP_ICON);
        Yii::$app->memcache->delete(KEY_TAB_BAR_PACKAGE);
        $data = Request::api('/site/launch', [], 'GET',
            ['User-Agent' => 'MissEvanApp/6.1.3 (iOS;12.2;iPhone9,1)']);
        $this->assertIsArray($data);
        $this->assertArrayHasKeys(['app_icon', 'tab_bar_package'], $data);
        $this->assertIsArray($data['app_icon']);
        $this->assertArrayHasKeys(['id', 'expire_duration'], $data['app_icon']);
        $this->assertIsArray($data['tab_bar_package']);
        $this->assertArrayHasKeys(['id', 'expire_duration'], $data['tab_bar_package']);

        // 测试 Android >= 6.1.3 版本时下发应用底部图标
        Yii::$app->memcache->delete(KEY_APP_LAUNCH_SPLASH);
        $data = Request::api('/site/launch', [], 'GET',
            ['User-Agent' => 'MissEvanApp/6.1.3 (Android;7.0;Meizu M6 M6)']);
        $this->assertIsArray($data);
        $this->assertArrayNotHasKey('app_icon', $data);
        $this->assertArrayHasKey('tab_bar_package', $data);
        $this->assertIsArray($data['tab_bar_package']);
        $this->assertArrayHasKeys(['id', 'expire_duration'], $data['tab_bar_package']);

        // 测试登录时获取主题皮肤失败
        self::loginByUserID(self::TEST_USER2_ID);
        $data = Request::api('/site/launch');
        $this->assertIsArray($data);
        $this->assertNull($data['theme_skin']);
    }

    public function testCheckUpdate()
    {
        $data = Request::api('/site/check-update');
        $this->assertIsArray($data);
    }

    public function testLimitBanners()
    {
        $env = getenv('DEPLOY_ENV', true) ?: '';
        // 测试 prod 环境
        putenv('DEPLOY_ENV=prod');

        $links = [
            [
                'element_type' => 1,
                'element_id' => 1,
                'pic' => 'https://www.test/test/1.jpg',
                'url' => 'https://www.test/mdrama/1',
                'more' => ['limit' => 1],
                'trace' => '{"original_url":"https://www.test/mdrama/1"}',
            ],
        ];

        // 不属于特定地区，过滤展示
        self::setLocation(['ip' => Data::TEST_JAPAN_IP,
            'geoip_record' => ['country_code' => 'JP', 'region_name' => 'Japan']]);
        $return = self::invokePrivateMethod(SiteController::class, 'limitBanners', $links);
        $this->assertEquals([], $return);

        // 属于特定地区，保留展示
        self::setLocation(['ip' => Data::TEST_MAINLAND_CHINA_IP,
            'geoip_record' => ['country_code' => 'CN', 'region_name' => 'Guangdong', 'city_name' => 'Shenzhen']]);
        $return = self::invokePrivateMethod(SiteController::class, 'limitBanners', $links);
        $this->assertEquals($links, $return);

        self::resetLocation();
        // 还原环境变量
        putenv('DEPLOY_ENV=' . $env);
    }

    public function testUserBanners()
    {
        self::loginByUserId();
        $memcache = Yii::$app->memcache;
        $key = MUtils::generateCacheKey(KEY_USER_DRAMA_EPISODE, Yii::$app->user->id, Json::encode([2, 39, 4]));
        $memcache->delete($key);

        Rpc::registerRpcApiResponseFunc(ServiceRpc::API_GET_USER_LASTVIEWED, function ($param) {
            if ($param['drama_ids'][0] === 2) {
                return new RpcResult([
                    'dramas' => [
                        '4' => [
                            'sound_id' => 101,
                        ],
                        '2' => null,
                        '39' => [
                            'sound_id' => 1039,
                        ],
                    ],
                ]);
            } elseif ($param['drama_ids'][0] === 3) {
                return new RpcResult([
                    'dramas' => [],
                ]);
            } else {
                throw new RpcNetworkException('网络错误');
            }
        });

        // 测试正常获取到收听进度
        $links = [
            [
                'element_type' => 1,
                'element_id' => 1,
                'pic' => 'https://www.test/test/1.jpg',
                'url' => 'https://www.test/mdrama/1',
                'trace' => '{"original_url":"https://www.test/mdrama/1"}',
            ],
            [
                'element_type' => MRecommendedElements::ELEMENT_TYPE_DRAMA,
                'element_id' => 2,
                'pic' => 'https://www.test/test/2.jpg',
                'url' => 'https://www.test/mdrama/2?pay_type=1',
                'more' => ['lastviewed' => 1],
                'trace' => '{"original_url":"https://www.test/mdrama/2?pay_type=1"}',
            ],
            [
                'element_type' => MRecommendedElements::ELEMENT_TYPE_DRAMA,
                'element_id' => 39,
                'pic' => 'https://www.test/test/39.jpg',
                'url' => 'https://www.test/mdrama/39?pay_type=1',
                'more' => ['lastviewed' => 1],
                'trace' => '{"original_url":"https://www.test/mdrama/39?pay_type=1"}',
            ],
            [
                'element_type' => MRecommendedElements::ELEMENT_TYPE_DRAMA,
                'element_id' => 102,
                'pic' => 'https://www.test/test/3.jpg',
                'url' => 'https://www.test/mdrama/102',
                'trace' => '{"original_url":"https://www.test/mdrama/102"}',
            ],
            [
                'element_type' => MRecommendedElements::ELEMENT_TYPE_DRAMA,
                'element_id' => 4,
                'pic' => 'https://www.test/test/4.jpg',
                'url' => 'https://www.test/mdrama/4',
                'more' => ['lastviewed' => 1],
                'trace' => '{"original_url":"https://www.test/mdrama/4"}',
            ],
        ];
        $expect_links = [
            [
                'element_type' => 1,
                'element_id' => 1,
                'pic' => 'https://www.test/test/1.jpg',
                'url' => 'https://www.test/mdrama/1',
                'trace' => '{"original_url":"https://www.test/mdrama/1"}',
            ],
            [
                'element_type' => MRecommendedElements::ELEMENT_TYPE_DRAMA,
                'element_id' => 39,
                'pic' => 'https://www.test/test/39.jpg',
                // url 为用户收听到的单集音频
                'url' => Yii::$app->params['domainMissevan'] . '/sound/1039',
                'more' => ['lastviewed' => 1],
                'trace' => '{"original_url":"https://www.test/mdrama/39?pay_type=1"}',
            ],
            [
                'element_type' => MRecommendedElements::ELEMENT_TYPE_DRAMA,
                'element_id' => 102,
                'pic' => 'https://www.test/test/3.jpg',
                'url' => 'https://www.test/mdrama/102',
                'trace' => '{"original_url":"https://www.test/mdrama/102"}',
            ],
            [
                'element_type' => MRecommendedElements::ELEMENT_TYPE_DRAMA,
                'element_id' => 4,
                'pic' => 'https://www.test/test/4.jpg',
                // url 为用户收听到的单集音频
                'url' => Yii::$app->params['domainMissevan'] . '/sound/101',
                'trace' => '{"original_url":"https://www.test/mdrama/4"}',
                'more' => ['lastviewed' => 1],
            ],
        ];
        $return = self::invokePrivateMethod(SiteController::class, 'userBanners', $links);
        $this->assertEquals($expect_links, $return);

        // 测试有缓存时成功获取收听进度
        $return = self::invokePrivateMethod(SiteController::class, 'userBanners', $links);
        $this->assertEquals($expect_links, $return);
        $memcache->delete($key);

        // 测试接口返回剧集下单集信息为空
        $key = MUtils::generateCacheKey(KEY_USER_DRAMA_EPISODE, Yii::$app->user->id, Json::encode([3]));
        $memcache->delete($key);
        $links = [
            [
                'element_type' => MRecommendedElements::ELEMENT_TYPE_DRAMA,
                'element_id' => 3,
                'pic' => 'https://www.test/test/1.jpg',
                'url' => 'https://www.test/mdrama/1?pay_type=1',
                'more' => ['lastviewed' => 1],
                'trace' => '{"original_url":"https://www.test/mdrama/1?pay_type=1"}',
            ]
        ];
        $return = self::invokePrivateMethod(SiteController::class, 'userBanners', $links);
        $this->assertEmpty($return);
        $memcache->delete($key);

        // 测试接口请求错误
        $key = MUtils::generateCacheKey(KEY_USER_DRAMA_EPISODE, Yii::$app->user->id, Json::encode([1]));
        $memcache->delete($key);
        $links = [
            [
                'element_type' => MRecommendedElements::ELEMENT_TYPE_DRAMA,
                'element_id' => 1,
                'pic' => 'https://www.test/test/1.jpg',
                'url' => 'https://www.test/mdrama/1?pay_type=1',
                'more' => ['lastviewed' => 1],
                'trace' => '{"original_url":"https://www.test/mdrama/1?pay_type=1"}',
            ]
        ];
        $return = self::invokePrivateMethod(SiteController::class, 'userBanners', $links);
        $this->assertEquals($links, $return);
    }

    public function testBanners()
    {
        self::loginByUserId();
        $memcache = Yii::$app->memcache;
        $key1 = MUtils::generateCacheKey(KEY_APP_HOMEPAGE_BANNER_CLIENT, Equipment::Android);
        $key2 = MUtils::generateCacheKey(KEY_USER_DRAMA_EPISODE, Yii::$app->user->id, Json::encode([2, 39, 4]));
        $memcache->delete($key1, $key2);

        Rpc::registerRpcApiResponseFunc(ServiceRpc::API_GET_USER_LASTVIEWED, function () {
            return new RpcResult([
                'dramas' => [
                    '4' => null,
                ],
            ]);
        });

        // 测试无缓存时正常获取到 banner
        MRecommendedElements::deleteAll(['client' => MRecommendedElements::CLIENT_APP_ANDROID]);
        Data::createMRecommendedElement([
            'client' => MRecommendedElements::CLIENT_APP_ANDROID,
            'module_type' => MRecommendedElements::MODULE_TYPE_BANNER,
            'element_type' => 2,
            'element_id' => 4,
            'url' => 'https://www.test/mdrama/4',
        ]);

        $expect_banner = [
            [
                'pic' => 'https://static-test.missevan.com/test/cover.png',
                'url' => 'https://www.test/mdrama/4',
                'trace' => '{"original_url":"https://www.test/mdrama/4"}',
            ],
        ];
        $return = self::invokePrivateMethod(SiteController::class, 'banners');
        $this->assertEquals($expect_banner, $return);
        // 验证正常生成缓存
        $cache_data = Json::decode($memcache->get($key1));
        $this->assertIsArray($cache_data);
        $this->assertNotEmpty($cache_data);

        // 测试有缓存时正常获取到 banner
        $return = self::invokePrivateMethod(SiteController::class, 'banners');
        $this->assertEquals($expect_banner, $return);
    }

    /**
     * 客户端首页获取顶部轮播图、轮播通栏、直播间推荐、打赏榜、精品周更的接口测试
     */
    public function testActionGetTop()
    {
        $memcache = Yii::$app->memcache;
        $banner_android_key = MUtils::generateCacheKey(KEY_APP_HOMEPAGE_BANNER_CLIENT,
            Equipment::Android);
        $memcache->delete($banner_android_key);
        $banner_ios_key = MUtils::generateCacheKey(KEY_APP_HOMEPAGE_BANNER_CLIENT,
            Equipment::iOS);
        $memcache->delete($banner_ios_key);

        $extra_banner_android_key = MUtils::generateCacheKey(KEY_APP_HOMEPAGE_EXTRA_BANNER_CLIENT,
            Equipment::Android);
        $memcache->delete($extra_banner_android_key);
        $extra_banner_ios_key = MUtils::generateCacheKey(KEY_APP_HOMEPAGE_EXTRA_BANNER_CLIENT,
            Equipment::iOS);
        $memcache->delete($extra_banner_ios_key);

        Rpc::registerRpcApiResponseFunc(ServiceRpc::API_GET_PERSONA, function () {
            return new RpcResult(['persona' => Persona::TYPE_GIRL]);
        });
        Rpc::registerRpcApiResponseFunc(LiveRpc::API_LIVE_EXTRA_BANNERS, function ($params) {
            if (isset($params['user_id']) && $params['user_id'] === self::TEST_USER_ID) {
                return new RpcResult(['data' => []]);
            }
            return new RpcResult([
                'data' => [
                    '1' => [
                        [
                            'pic' => 'https://static-test.maoercdn.com/test/test.png',
                            'url' => 'https://www.uat.missevan.com/live/665152'
                        ],
                    ],
                ],
            ]);
        });
        self::delMRecommendedElements();
        // 构造首页顶部轮播图数据
        $ELEMENT_ID1 = 875095;
        $ELEMENT_ID2 = 875096;
        $EXPIRATION1 = 10;
        $EXPIRATION2 = 20;
        $time = time();
        $banner_arr = [
            ['client' => Equipment::Android,
                'element_id' => $ELEMENT_ID1,
                'cover' => 'oss://app/homepage/banners/202006/01/e533c57d3f9919e4c4b505f8c9165d32183647.jpeg',
                'url' => 'missevan://omikuji/draw?work_id=2&season_id=1',
                'end_time' => $EXPIRATION1],
            ['client' => Equipment::iOS,
                'element_id' => $ELEMENT_ID2,
                'cover' => 'oss://app/homepage/banners/202006/01/e533c57d3f9919e4c4b505f8c9165d32183647.jpeg',
                'url' => 'missevan://omikuji/draw?work_id=2&season_id=1',
                'end_time' => $EXPIRATION2]
        ];
        foreach ($banner_arr as $k => $v) {
            $banner = new MRecommendedElements();
            $banner->client = $v['client'];
            $banner->module_type = MRecommendedElements::MODULE_TYPE_BANNER;
            $banner->element_type = 0;
            $banner->element_id = $v['element_id'];
            $banner->cover = $v['cover'];
            $banner->url = $v['url'];
            $banner->sort = $k;
            $banner->create_time = $_SERVER['REQUEST_TIME'];
            $banner->update_time = $_SERVER['REQUEST_TIME'];
            $banner->end_time = $time + $v['end_time'];
            $banner->start_time = $time;
            if (!$banner->save()) {
                throw new Exception(MUtils::getFirstError($banner));
            }
        }
        // 构造首页轮播通栏数据
        $extra_banner_arr = [
            ['client' => Equipment::Android,
                'element_id' => $ELEMENT_ID1,
                'module_id' => MRecommendedElements::MODULE_POSITION_EXTRA_BANNER_1,  // 位置 1
                'cover' => 'oss://app/homepage/banners/202006/01/e533c57d3f9919e4c4b505f8c9165d32183647.jpeg',
                'url' => 'missevan://omikuji/draw?work_id=2&season_id=1',
                'end_time' => $EXPIRATION2],
            ['client' => Equipment::Android,
                'element_id' => $ELEMENT_ID1,
                'module_id' => MRecommendedElements::MODULE_POSITION_EXTRA_BANNER_2,  // 位置 2
                'cover' => 'oss://app/homepage/banners/202006/01/e533c57d3f9919e4c4b505f8c9165d32183647.jpeg',
                'url' => 'missevan://omikuji/draw?work_id=2&season_id=1',
                'end_time' => $EXPIRATION1],
            ['client' => Equipment::Android,
                'element_id' => $ELEMENT_ID2,
                'module_id' => MRecommendedElements::MODULE_POSITION_EXTRA_BANNER_3,  // 位置 3
                'cover' => 'oss://app/homepage/banners/202006/01/e533c57d3f9919e4c4b505f8c9165d32183647.jpeg',
                'url' => 'missevan://omikuji/draw?work_id=2&season_id=1',
                'end_time' => $EXPIRATION2],
            ['client' => Equipment::Android,
                'element_id' => $ELEMENT_ID2,
                'module_id' => MRecommendedElements::MODULE_POSITION_EXTRA_BANNER_4,  // 位置 4
                'cover' => 'oss://app/homepage/banners/202006/01/e533c57d3f9919e4c4b505f8c9165d32183647.jpeg',
                'url' => 'missevan://omikuji/draw?work_id=2&season_id=1',
                'end_time' => $EXPIRATION1],
        ];
        foreach ($extra_banner_arr as $k => $v) {
            $banner = new MRecommendedElements();
            $banner->client = $v['client'];
            $banner->module_type = MRecommendedElements::MODULE_TYPE_EXTRA_BANNER;
            $banner->module_id = $v['module_id'];
            $banner->element_type = 0;
            $banner->element_id = $v['element_id'];
            $banner->cover = $v['cover'];
            $banner->url = $v['url'];
            $banner->sort = $k;
            $banner->create_time = $_SERVER['REQUEST_TIME'];
            $banner->update_time = $_SERVER['REQUEST_TIME'];
            $banner->end_time = $time + $v['end_time'];
            $banner->start_time = $time;
            if (!$banner->save()) {
                throw new Exception(MUtils::getFirstError($banner));
            }
        }

        // 测试安卓
        $data_android = Request::api('/site/get-top', [], Request::GET,
            ['User-Agent' => self::TEST_USERAGENT], ['request_time' => $time]);
        $this->assertArrayHasKeys(['search_words', 'banners', 'extra_banners', 'weeklydrama'], $data_android);
        $this->assertEquals(1, count($data_android['banners']));
        $this->assertEquals(4, count($data_android['extra_banners']));
        $this->assertEquals(2, count($data_android['extra_banners'][MRecommendedElements::MODULE_POSITION_EXTRA_BANNER_1]));
        // 直播通栏放在其它通栏前
        $this->assertEquals('https://www.uat.missevan.com/live/665152', $data_android['extra_banners'][MRecommendedElements::MODULE_POSITION_EXTRA_BANNER_1][0]['url']);

        // 断言是否包含关键字段
        $first_extra_banners = current($data_android['extra_banners']);
        $first_banner = current($first_extra_banners);
        $this->assertArrayHasKeys(['url', 'pic'], $first_banner);

        // 测试 iOS
        self::loginByUserId();
        $data_ios = Request::api('/site/get-top', [], Request::GET,
            ['User-Agent' => self::TEST_IOS_USERAGENT], ['request_time' => $time]);
        $this->assertArrayHasKeys(['search_words', 'banners', 'extra_banners', 'weeklydrama'], $data_ios);
        $this->assertEquals(1, count($data_android['banners']));
        // 断言为空对象
        $expected = new \stdClass();
        $this->assertEquals($expected, $data_ios['extra_banners']);

        // 测试缓存数据是否生成
        $data = Json::decode($memcache->get($banner_android_key));
        $this->assertNotEmpty($data);
        $data = Json::decode($memcache->get($banner_ios_key));
        $this->assertNotEmpty($data);
        $data = Json::decode($memcache->get($extra_banner_android_key));
        $this->assertNotEmpty($data);
        $data = Json::decode($memcache->get($extra_banner_ios_key));
        $this->assertIsArray($data);
        $this->assertEmpty($data);
        self::delMRecommendedElements();
    }

    // 删除版头图测试数据
    private static function delMRecommendedElements()
    {
        $memcache = Yii::$app->memcache;
        $banner_android_key = MUtils::generateCacheKey(KEY_APP_HOMEPAGE_BANNER_CLIENT,
            Equipment::Android);
        $memcache->delete($banner_android_key);
        $banner_ios_key = MUtils::generateCacheKey(KEY_APP_HOMEPAGE_BANNER_CLIENT,
            Equipment::iOS);
        $memcache->delete($banner_ios_key);
        $extra_banner_android_key = MUtils::generateCacheKey(KEY_APP_HOMEPAGE_EXTRA_BANNER_CLIENT,
            Equipment::Android);
        $memcache->delete($extra_banner_android_key);
        $extra_banner_ios_key = MUtils::generateCacheKey(KEY_APP_HOMEPAGE_EXTRA_BANNER_CLIENT,
            Equipment::iOS);
        $memcache->delete($extra_banner_ios_key);
        MRecommendedElements::updateAll(['archive' => MRecommendedElements::ARCHIVE_HISTORY],
            ['archive' => MRecommendedElements::ARCHIVE_ONLINE,
                'module_type' => [MRecommendedElements::MODULE_TYPE_BANNER,
                    MRecommendedElements::MODULE_TYPE_EXTRA_BANNER],
                'client' => [Equipment::Android, Equipment::iOS]]);
    }

    /**
     * 测试获取首页 icon 数据接口
     */
    public function testActionIcons()
    {
        $summer_drama_link = Yii::$app->params['domainMobileWeb'] . '/summerdrama';
        $classics_sound_icon = [
            'id' => 2,
            'title' => '经典必听',
            'pic' => Yii::$app->params['static_domain'] . 'system/app/icons/normal/64c8187e7ddb17498c769a190dacac0a.png',
            'url' => 'missevan://classic',
            'select' => 3,
            'normal' => '64c8187e7ddb17498c769a190dacac0a',
            'dark' => '39283ee451e6e75de3ff53404bd33f94',
        ];
        $live_icon = [
            'id' => 5,
            'title' => '直播',
            'pic' => Yii::$app->params['static_domain'] . 'system/app/icons/normal/2557cd34590b7991c8e7ac342945dfa7.png',
            'url' => 'missevan://live',
            'select' => 3,
            'normal' => '2557cd34590b7991c8e7ac342945dfa7',
            'dark' => '2557cd34590b7991c8e7ac342945dfa7',
        ];
        $mall_icon = [
            'id' => 6,
            'title' => '猫耳商城',
            'pic' => Yii::$app->params['static_domain'] . 'system/app/icons/normal/41da81f8f3af0bc84e38f402d16fb6e2.png',
            'url' => 'missevan://mall/homepage',
            'select' => 3,
            'normal' => '41da81f8f3af0bc84e38f402d16fb6e2',
            'dark' => '41da81f8f3af0bc84e38f402d16fb6e2',
        ];
        $drama_icon = [
            'id' => 1,
            'title' => '广播剧',
            'pic' => Yii::$app->params['static_domain'] . 'system/app/icons/normal/46ccce40f5d2184e2f93505ac30c8203.png',
            'url' => 'missevan://drama',
            'select' => 3,
            'normal' => '46ccce40f5d2184e2f93505ac30c8203.webp',
            'dark' => '46ccce40f5d2184e2f93505ac30c8203.webp',
        ];
        $summer_drama_icon = [
            'id' => 4,
            'title' => '精品周更',
            'pic' => Yii::$app->params['static_domain'] . 'system/app/icons/normal/8b94b29cbeba372f9f85c7e198f32a6e.png',
            'url' => $summer_drama_link,
            'select' => 3,
            'normal' => '8b94b29cbeba372f9f85c7e198f32a6e',
            'dark' => '6228375ec07ccd7c92e751b505c4efd3',
        ];
        $power_sound_icon = [
            'id' => 3,
            'title' => '启动音',
            'pic' => Yii::$app->params['static_domain'] . 'system/app/icons/normal/2843b7f645c2d1812ae5914087c79f74.png',
            'url' => 'missevan://powersound',
            'select' => 3,
            'normal' => '2843b7f645c2d1812ae5914087c79f74',
            'dark' => '2843b7f645c2d1812ae5914087c79f74',
        ];
        $game_center_icon = [
            'id' => 7,
            'title' => '游戏中心',
            'pic' => Yii::$app->params['static_domain'] . 'system/app/icons/normal/1403912eb9e70108d3a4acf9423fc39c.png',
            'url' => 'missevan://game/center',
            'select' => 1,
            'normal' => '1403912eb9e70108d3a4acf9423fc39c',
            'dark' => '1403912eb9e70108d3a4acf9423fc39c',
        ];
        $rank_link = Yii::$app->params['web_links']['homepage_rank_details'];
        $homepage_rank_icon = [
            'id' => 10,
            'title' => '排行榜',
            'pic' => Yii::$app->params['static_domain'] . 'system/app/icons/normal/rank.png',
            'url' => "{$rank_link}?type=1&navhide=1",
            'select' => 2,
            'normal' => 'test',
            'dark' => 'test',
        ];

        $icons = [
            $classics_sound_icon,
            $live_icon,
            $mall_icon,
            $drama_icon,
            $game_center_icon,
            $summer_drama_icon,
            $power_sound_icon,
            $homepage_rank_icon,
        ];
        // 设置 icons
        Yii::$app->redis->set(KEY_APP_HOMEPAGE_ICONS, Json::encode($icons));
        Rpc::registerRpcApiResponseFunc(ServiceRpc::API_GET_PERSONA, function () {
            return new RpcResult(['persona' => Persona::TYPE_GENERAL]);
        });

        // 测试直播 icon 正常显示
        $data = Request::api('/site/icons', [], 'GET',
            ['User-Agent' => 'MissEvanApp/5.4.9 (Android;7.0;Meizu M6 M6)']);
        $this->assertStringContainsString('.webp', $data['icons'][3]['icon']);

        // iOS 不显示游戏中心入口
        $data = Request::api('/site/icons', [], Request::GET,
            ['User-Agent' => self::TEST_IOS_USERAGENT]);
        $urls = array_column($data['icons'], 'url');
        $this->assertNotContains('missevan://game/center', $urls);

        // 测试 Android 5.4.9 华为渠道隐藏游戏中心入口
        // 设置渠道客户端隐藏游戏中心入口
        Blacklist::model()->addChannel(Blacklist::TYPE_CHANNEL_AUDIT_SENSITIVE_HIDE,
            'missevan_huawei', '5.4.9');
        $data = Request::api('/site/icons', [], 'GET',
            ['User-Agent' => 'MissEvanApp/5.4.9 (Android;7.0;Meizu M6 M6)', 'Channel' => 'missevan_huawei']);
        $urls = array_column($data['icons'], 'url');
        // 测试隐藏游戏中心入口
        $this->assertNotContains('missevan://game/center', $urls);

        // Android 5.4.9 华为渠道未隐藏游戏中心入口
        // 清除渠道数据为了测试有游戏中心入口
        Blacklist::model()->removeChannel(Blacklist::TYPE_CHANNEL_AUDIT_SENSITIVE_HIDE,
            'missevan_huawei', '5.4.9');
        $data = Request::api('/site/icons', [], 'GET',
            ['User-Agent' => 'MissEvanApp/5.4.9 (Android;7.0;Meizu M6 M6)', 'Channel' => 'missevan_huawei']);
        $urls = array_column($data['icons'], 'url');
        $this->assertContains('missevan://game/center', $urls);

        // 测试 Android 5.4.9 华为渠道隐藏盲盒剧场入口
        // 设置渠道客户端隐藏盲盒剧场入口
        Blacklist::model()->addChannel(Blacklist::TYPE_CHANNEL_AUDIT_SENSITIVE_HIDE,
            'missevan_huawei', '5.4.9');
        $data = Request::api('/site/icons', [], 'GET',
            ['User-Agent' => 'MissEvanApp/5.4.9 (Android;7.0;Meizu M6 M6)', 'Channel' => 'missevan_huawei']);
        $urls = array_column($data['icons'], 'url');
        $this->assertNotContains('missevan://theatre', $urls);

        // 测试出现“正在直播”模块信息
        $data = Request::api('/site/icons', ['persona_id' => Persona::TYPE_BOY], 'GET',
            ['User-Agent' => 'MissEvanApp/5.4.9 (Android;7.0;Meizu M6 M6)']);
        $this->assertArrayHasKey('live_module', $data);
        $this->assertEquals('正在直播', $data['live_module']['title']);

        // 测试“正在直播”模块隐藏返回的模块数据为 null
        MPersonaModuleElement::updateAll(['sort' => MPersonaModuleElement::MODULE_HIDDEN],
            ['summary' => 'live_test']);
        $data = Request::api('/site/icons', ['persona_id' => Persona::TYPE_BOY], 'GET',
            ['User-Agent' => 'MissEvanApp/5.4.9 (iOS;12.0;iPhone9,1)']);
        $this->assertArrayHasKey('live_module', $data);
        $this->assertNull($data['live_module']);

        // 测试不返回特效图标
        $data = Request::api('/site/icons', [], Request::GET,
            ['User-Agent' => 'MissEvanApp/4.9.5 (iOS;12.0;iPhone9,1)']);
        $this->assertIsArray($data);
        $icon = current($data['icons']);
        $this->assertArrayHasKey('icon', $icon);

        // 测试安卓 Google 渠道包不显示游戏中心入口
        Blacklist::model()->addChannel(Blacklist::TYPE_CHANNEL_AUDIT_SENSITIVE_HIDE,
            Equipment::CHANNEL_GOOGLE, '0');
        $data = Request::api('/site/icons', [], Request::GET, [
            'User-Agent' => 'MissEvanApp/5.4.9 (Android;7.0;Meizu M6 M6)',
            'channel' => Equipment::CHANNEL_GOOGLE,
        ]);
        $this->assertIsArray($data);
        $urls = array_column($data['icons'], 'url');
        $this->assertNotContains('missevan://game/center', $urls);
        Blacklist::model()->removeChannel(Blacklist::TYPE_CHANNEL_AUDIT_SENSITIVE_HIDE,
            Equipment::CHANNEL_GOOGLE, '0');

        // 测试传了 persona_id 参数时
        $persona_id = Persona::TYPE_GIRL;
        $data = Request::api('/site/icons', ['persona_id' => $persona_id], Request::GET,
            ['User-Agent' => 'MissEvanApp/4.9.6 (iOS;12.0;iPhone9,1)']);
        $this->assertIsArray($data);
        $urls = array_column($data['icons'], 'url');
        $this->assertContains("{$homepage_rank_icon['url']}&persona_id={$persona_id}", $urls);

        // 测试没有传 persona_id 参数时
        $data = Request::api('/site/icons', [], Request::GET,
            ['User-Agent' => 'MissEvanApp/4.9.5 (iOS;12.0;iPhone9,1)']);
        $this->assertIsArray($data);
        $present_persona_id = (Persona::getPersonaType()) & Persona::PERSONA_MODULE_MASK;
        $urls = array_column($data['icons'], 'url');
        $this->assertContains("{$homepage_rank_icon['url']}&persona_id={$present_persona_id}", $urls);

        // 测试 persona_id 参数为 0 时
        $data = Request::api('/site/icons', [], Request::GET,
            ['User-Agent' => 'MissEvanApp/4.9.5 (iOS;12.0;iPhone9,1)']);
        $this->assertIsArray($data);
        $urls = array_column($data['icons'], 'url');
        $this->assertContains("{$homepage_rank_icon['url']}&persona_id={$present_persona_id}", $urls);
    }

    public function testGetIconTitle()
    {
        // 测试获取正常的 icon 标题
        $icon = [
            'title' => '标题',
            'url' => 'missevan://test/test'
        ];
        $title = self::invokePrivateMethod(SiteController::class, 'getIconTitle', $icon);
        $this->assertEquals($icon['title'], $title);

        // 测试 icon 标题未配置
        $icon = [
            'url' => 'missevan://test/test'
        ];
        $title = self::invokePrivateMethod(SiteController::class, 'getIconTitle', $icon);
        $this->assertEquals('', $title);
    }

    /**
     * 验证客户端首页数据是否合法
     *
     * @param $data
     */
    private function validateGetTopData($data)
    {
        foreach ($data as $key => $value) {
            $this->assertIsArray($value, 'Failed key is: ' . $key);
        }
    }

    public function testConfig()
    {
        // 测试 iOS 的下发配置
        $data = Request::api('/site/config', [], Request::GET,
            ['User-Agent' => self::TEST_IOS_USERAGENT]);
        $this->assertArrayHasKey('omikuji_share_text', $data);
        $this->assertIsString($data['omikuji_share_text']);

        // 测试安卓的下发配置
        self::resetLocation();
        $_SERVER['REMOTE_ADDR'] = Data::TEST_MAINLAND_CHINA_IP;
        $data = Request::api('/site/config', [], Request::GET,
            ['User-Agent' => self::TEST_USERAGENT]);
        $this->assertArrayHasKeys(['omikuji_share_text', 'httpdns'], $data);
        $this->assertIsString($data['omikuji_share_text']);
        $this->assertArrayNotHasKeys(['service_ip', 'provider'], Json::decode($data['httpdns']));

        // 测试下发可执行代码
        $data = Request::api('/site/config', [], Request::GET, ['User-Agent' => self::TEST_USERAGENT]);
        $this->assertArrayHasKey('code_test', $data);
        $this->assertFalse($data['code_test']);

        // 测试下发可执行代码中有语法错误
        $data = Request::api('/site/config', [], Request::GET, ['User-Agent' => self::TEST_USERAGENT]);
        $this->assertArrayNotHasKey('error_code_test', $data);

        // 测试下发 murmurhash 实验分组代码
        $data = Request::api('/site/config', [], Request::GET, ['User-Agent' => self::TEST_USERAGENT]);
        $this->assertArrayHasKey('murmurhash_test', $data);
        $this->assertEquals(17, $data['murmurhash_test']);

        $key = MUtils2::generateCacheKey(KEY_ALIYUN_SERVICE_IP, 'hk');
        // 测试其它海外地区
        self::resetLocation();
        Rpc::registerRpcApiResponseFunc(ServiceRpc::API_UTIL_GEOIP, function ($data) {
            if ($data['ip'] === Data::TEST_JAPAN_IP) {
                return new RpcResult(['country_code' => 'JP', 'region_name' => 'Japan']);
            }
            return new RpcResult(['country_code' => 'CN', 'region_name' => '']);
        });
        $_SERVER['REMOTE_ADDR'] = Data::TEST_JAPAN_IP;
        $data = Request::api('/site/config', [], Request::GET,
            ['User-Agent' => self::TEST_USERAGENT]);
        $this->assertArrayHasKeys(['omikuji_share_text', 'httpdns'], $data);
        $httpdns = Json::decode($data['httpdns']);
        $this->assertArrayHasKeys(['service_ip', 'provider'], $httpdns);
        $this->assertEmpty($httpdns['service_ip']);
        $this->assertEmpty(self::$memcache->get($key));

        // 测试香港地区
        self::resetLocation();
        Rpc::registerRpcApiResponseFunc(ServiceRpc::API_UTIL_GEOIP, function ($data) {
            if ($data['ip'] === Data::TEST_HK_IP) {
                return new RpcResult(['country_code' => 'HK', 'region_name' => 'Hong Kong']);
            }
            return new RpcResult(['country_code' => 'CN', 'region_name' => '']);
        });
        $_SERVER['REMOTE_ADDR'] = Data::TEST_HK_IP;
        $data = Request::api('/site/config', [], Request::GET,
            ['User-Agent' => self::TEST_USERAGENT]);
        $this->assertArrayHasKeys(['omikuji_share_text', 'httpdns'], $data);
        $httpdns = Json::decode($data['httpdns']);
        $this->assertArrayHasKey('service_ip', $httpdns);
        $this->assertArrayNotHasKey('provider', $httpdns);
        $this->assertNotEmpty($httpdns['service_ip']);
        $this->assertNotEmpty(self::$memcache->get($key));

        // 测试无缓存数据时
        self::$redis->del(self::$redis->generateKey(KEY_CONFIG_CLIENT, Equipment::Android));
        $data = Request::api('/site/config', [], Request::GET, ['User-Agent' => self::TEST_USERAGENT]);
        $this->assertNull($data);

        // 重置 IP 位置
        self::resetLocation();
    }

    public function testCatalogRoot()
    {
        $catalog_icons = [
            [
                'name' => '直播',
                'path' => 'missevan://live',
                'sort' => 1,
                'icon' => 'http://static.missevan.com/coversmini/nocover.png',
                'icon_night' => 'http://static.missevan.com/coversmini/nocover.png',
            ],
            [
                'name' => '活动',
                'path' => 'missevan://event',
                'sort' => 2,
                'icon' => 'http://static.missevan.com/coversmini/nocover.png',
                'icon_night' => 'http://static.missevan.com/coversmini/nocover.png',
            ],
            [
                'name' => '专题',
                'path' => 'missevan://topic',
                'sort' => 3,
                'icon' => 'http://static.missevan.com/coversmini/nocover.png',
                'icon_night' => 'http://static.missevan.com/coversmini/nocover.png',
            ],
            [
                'name' => '测试隐藏用',
                'path' => 'missevan://topic',
                'sort' => 3,
                'icon' => 'http://static.missevan.com/coversmini/nocover.png',
                'icon_night' => 'http://static.missevan.com/coversmini/nocover.png',
                'disabled' => false,
            ],
        ];
        // 设置分类额外图标数据
        self::$redis->set(CATALOG_ICONS, serialize($catalog_icons));

        // 不含额外图标（直播、活动、新闻）的情况
        $data = Request::api('/site/catalog-root', ['dark' => 0, 'extra' => 0], Request::GET,
            ['User-Agent' => 'MissEvanApp/5.2.3 (Android;7.0;Meizu M6 M6)']);

        $this->assertIsArray($data);
        $this->assertGreaterThanOrEqual(1, count($data));
        $types = array_unique(array_column($data, 'type'));
        $this->assertCount(1, $types);
        $this->assertEquals(0, $types[0]);

        // 含额外图标的情况
        // Android 旧版本不含专题图标
        $data = Request::api('/site/catalog-root', ['dark' => 0, 'extra' => 1], Request::GET,
            ['User-Agent' => 'MissEvanApp/5.2.9 (Android;7.0;Meizu M6 M6)']);
        $this->assertIsArray($data);
        $this->assertGreaterThanOrEqual(1, count($data));
        $links = array_column($data, 'id');
        $this->assertNotContains('missevan://topic', $links);

        // Android 新版本含专题图标
        $data = Request::api('/site/catalog-root', ['dark' => 0, 'extra' => 1], Request::GET,
            ['User-Agent' => 'MissEvanApp/5.3.0 (Android;7.0;Meizu M6 M6)']);
        $this->assertIsArray($data);
        $this->assertGreaterThanOrEqual(1, count($data));
        $links = array_column($data, 'id');
        $this->assertContains('missevan://topic', $links);

        // 获取额外图标
        $data = Request::api('/site/catalog-root', ['dark' => 0, 'extra' => 1], Request::GET);
        $show_data_count = count($data);
        // 额外图标设置为隐藏
        $catalog_icons[3]['disabled'] = true;
        self::$redis->set(CATALOG_ICONS, serialize($catalog_icons));
        $data = Request::api('/site/catalog-root', ['dark' => 0, 'extra' => 1], Request::GET);
        $this->assertIsArray($data);
        $this->assertEquals($show_data_count - 1, count($data));
        // 额外图标设置为显示
        $catalog_icons[3]['disabled'] = false;
        self::$redis->set(CATALOG_ICONS, serialize($catalog_icons));
        $data = Request::api('/site/catalog-root', ['dark' => 0, 'extra' => 1], Request::GET);
        $this->assertIsArray($data);
        $this->assertEquals($show_data_count, count($data));

        // 测试不同 IP 地区
        $IP_CHINA_MAINLAND = '*************';
        $IP_SINGAPORE = '************';
        self::resetLocation();
        // 中国大陆地区（含日抓分类）
        $_SERVER['REMOTE_ADDR'] = $IP_CHINA_MAINLAND;
        $data = Request::api('/site/catalog-root', ['dark' => 0, 'extra' => 1], Request::GET,
            ['User-Agent' => 'MissEvanApp/5.2.3 (Android;7.0;Meizu M6 M6)']);
        $this->assertIsArray($data);
        $this->assertGreaterThanOrEqual(1, count($data));
        $types = array_unique(array_column($data, 'type'));
        $this->assertCount(2, $types);
        $ids = array_column($data, 'id');
        $this->assertContains(Catalog::CATALOG_ID_JAPAN_DRAMA, $ids);
        self::resetLocation();

        // 中国大陆以外地区（不含日抓分类）
        $_SERVER['REMOTE_ADDR'] = $IP_SINGAPORE;
        $data = Request::api('/site/catalog-root', ['dark' => 0, 'extra' => 1], Request::GET,
            ['User-Agent' => 'MissEvanApp/5.2.3 (Android;7.0;Meizu M6 M6)']);
        $this->assertIsArray($data);
        $this->assertGreaterThanOrEqual(1, count($data));
        $ids = array_column($data, 'id');
        $this->assertNotContains(Catalog::CATALOG_ID_JAPAN_DRAMA, $ids);
        // 重置 IP 位置
        self::resetLocation();

        // 返回 正常和黑夜图标
        $data = Request::api('/site/catalog-root', ['dark' => 0, 'extra' => 1], Request::GET);
        $this->assertIsArray($data);
        $this->assertArrayNotHasKey('dark_icon', current($data));
        // 返回 正常和黑夜图标
        $data = Request::api('/site/catalog-root', ['dark' => 0, 'extra' => 1], Request::GET,
            ['User-Agent' => 'MissEvanApp/5.4.0 (Android;7.0;Meizu M6 M6)']);
        $this->assertIsArray($data);
        $this->assertArrayHasKey('dark_icon', current($data));
    }

    public function testRingingSound()
    {
        // 设置测试数据
        self::$redis->set(RINGING_1, '1,2,3');

        // 测试低版本返回音频地址信息
        $data = Request::api('/site/ringing-sound', [], Request::GET,
            ['User-Agent' => 'MissEvanApp/5.7.1 (Android;7.0;Meizu M6 M6)']);
        $this->assertNotEmpty($data);
        $sound = current($data);
        $this->assertNotEmpty($sound);
        $this->assertArrayHasKeys(['id', 'soundstr', 'user_id', 'username', 'front_cover', 'iconurl', 'soundurl', 'soundurl_32', 'soundurl_64', 'soundurl_128'], $sound);
        $this->assertArrayNotHasKey('cover_image', $sound);
        $this->assertFalse($sound['soundurl_32'] === $sound['soundurl_64']);

        // 测试高版本不再返回音频地址信息
        $data = Request::api('/site/ringing-sound', [], Request::GET,
            ['User-Agent' => 'MissEvanApp/5.8.1 (Android;7.0;Meizu M6 M6)']);
        $this->assertNotEmpty($data);
        $sound = current($data);
        $this->assertNotEmpty($sound);
        $this->assertArrayHasKeys(['id', 'soundstr', 'user_id', 'username', 'front_cover', 'iconurl'], $sound);
        $this->assertArrayNotHasKeys(['cover_image', 'soundurl', 'soundurl_32', 'soundurl_64', 'soundurl_128'], $sound);
    }

    public function testSetMessageConfig()
    {
        // 登录
        $this->login();
        // 先断言默认设置为接收并收起未关注人消息
        $user_info = UserAddendum::getByPk(self::TEST_USER_ID);
        $user_info->message_config = null;
        if (!$user_info->save(false, ['message_config'])) {
            throw new Exception('更新失败，原因：' . MUtils::getFirstError($user_info));
        }
        // 设置屏蔽未关注人消息
        $data = Request::api('/site/set-message-config', [
            'type' => UserAddendum::MSG_CFG_TYPE_RECEIVE,
            'value' => 0,
        ], Request::POST);
        $this->assertEquals(0, $data);
        $user_info = UserAddendum::getByPk(self::TEST_USER_ID);
        $message_config = $user_info->message_config;
        $this->assertEquals(UserAddendum::MESSAGE_REJECT, $message_config[UserAddendum::MSG_CFG_TYPE_RECEIVE]);
        $this->assertEquals(UserAddendum::MESSAGE_EXPAND, $message_config[UserAddendum::MSG_CFG_TYPE_FOLD]);
        // 设置接收未关注人消息
        $data = Request::api('/site/set-message-config', [
            'type' => UserAddendum::MSG_CFG_TYPE_RECEIVE,
            'value' => 1,
        ], Request::POST);
        $this->assertEquals(1, $data);
        $user_info = UserAddendum::getByPk(self::TEST_USER_ID);
        $message_config = $user_info->message_config;
        $this->assertEquals(UserAddendum::MESSAGE_RECEIVE, $message_config[UserAddendum::MSG_CFG_TYPE_RECEIVE]);
        $this->assertEquals(UserAddendum::MESSAGE_EXPAND, $message_config[UserAddendum::MSG_CFG_TYPE_FOLD]);
        // 设置展开未关注人消息
        $data = Request::api('/site/set-message-config', [
            'type' => UserAddendum::MSG_CFG_TYPE_FOLD,
            'value' => 0,
        ], Request::POST);
        $this->assertEquals(0, $data);
        $user_info = UserAddendum::getByPk(self::TEST_USER_ID);
        $message_config = $user_info->message_config;
        $this->assertEquals(UserAddendum::MESSAGE_RECEIVE, $message_config[UserAddendum::MSG_CFG_TYPE_RECEIVE]);
        $this->assertEquals(UserAddendum::MESSAGE_EXPAND, $message_config[UserAddendum::MSG_CFG_TYPE_FOLD]);
        // 设置收起未关注人消息
        $data = Request::api('/site/set-message-config', [
            'type' => UserAddendum::MSG_CFG_TYPE_FOLD,
            'value' => 1,
        ], Request::POST);
        $this->assertEquals(1, $data);
        $user_info = UserAddendum::getByPk(self::TEST_USER_ID);
        $message_config = $user_info->message_config;
        $this->assertEquals(UserAddendum::MESSAGE_RECEIVE, $message_config[UserAddendum::MSG_CFG_TYPE_RECEIVE]);
        $this->assertEquals(UserAddendum::MESSAGE_FOLD, $message_config[UserAddendum::MSG_CFG_TYPE_FOLD]);
        // 还原数据
        $user_info->message_config = null;
        if (!$user_info->save(false, ['message_config'])) {
            throw new Exception('更新失败，原因：' . MUtils::getFirstError($user_info));
        }
    }

    public function testGetMessageConfig()
    {
        $user_info = UserAddendum::getByPk(self::TEST_USER2_ID);
        $user_info->message_config = null;
        if (!$user_info->save(false, ['message_config'])) {
            throw new Exception('更新失败，原因：' . MUtils::getFirstError($user_info));
        }
        // 测试默认设置
        $this->login(self::TEST_USER2_ACCOUNT2, self::TEST_USER2_PASSWORD);
        $data = Request::api('/site/get-message-config');
        $this->assertEquals(UserAddendum::MESSAGE_RECEIVE, $data[UserAddendum::MSG_CFG_TYPE_RECEIVE]);
        $this->assertEquals(UserAddendum::MESSAGE_EXPAND, $data[UserAddendum::MSG_CFG_TYPE_FOLD]);
        // 测试获取已经设置的值
        Request::api('/site/set-message-config', [
            'type' => UserAddendum::MSG_CFG_TYPE_FOLD,
            'value' => 1,
        ], Request::POST);
        $data = Request::api('/site/get-message-config');
        $this->assertEquals(UserAddendum::MESSAGE_RECEIVE, $data[UserAddendum::MSG_CFG_TYPE_RECEIVE]);
        $this->assertEquals(UserAddendum::MESSAGE_FOLD, $data[UserAddendum::MSG_CFG_TYPE_FOLD]);
        // 还原数据
        $user_info->message_config = null;
        if (!$user_info->save(false, ['message_config'])) {
            throw new Exception('更新失败，原因：' . MUtils::getFirstError($user_info));
        }
    }

    public function testCatalogTabs()
    {
        // 测试分类不存在
        $this->assertThrowsWithMessage(HttpException::class, '分类不存在', function () {
            Request::api('/site/catalog-tabs', ['catalog_id' => -1]);
        });
        // 测试 iOS 4.7.6 中文广播剧正常返回
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.7.6 (iOS;12.0;iPhone9,1)']);
        $data = Request::api('/site/catalog-tabs', ['catalog_id' => Drama::DRAMA_CATALOG_ID_CN_RADIO_DRAMA]);
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);

        // 测试 iOS 4.7.6 以下中文广播剧正常返回
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.7.5 (iOS;12.0;iPhone9,1)']);
        $data = Request::api('/site/catalog-tabs', ['catalog_id' => Drama::DRAMA_CATALOG_ID_CN_RADIO_DRAMA]);
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);

        // 测试 iOS 4.7.6 日抓正常返回
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.7.6 (iOS;12.0;iPhone9,1)']);
        $data = Request::api('/site/catalog-tabs', ['catalog_id' => Drama::DRAMA_CATALOG_ID_JAPAN_RADIO_DRAMA]);
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);
        $this->assertArrayHasKey('type', $data[0]);

        // 测试 iOS 4.7.6 以下日抓正常返回
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/4.7.5 (iOS;12.0;iPhone9,1)']);
        $data = Request::api('/site/catalog-tabs', ['catalog_id' => Drama::DRAMA_CATALOG_ID_JAPAN_RADIO_DRAMA]);
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);
        $this->assertArrayHasKey('type', $data[0]);
    }

    public function testInstallEquipInfo()
    {
        $ref_class = new ReflectionClass(SiteController::class);
        $instance = $ref_class->newInstance('site', 'test');
        self::setPrivateProperty(Yii::$app->equip, 'buvid', null);

        // 测试没有 buvid 的设备
        $this->assertEquals([true, 0],
            self::invokePrivateMethod($ref_class, 'installEquipInfo', Yii::$app->equip, $instance));
        self::setPrivateProperty(Yii::$app->equip, 'buvid', self::TEST_BUVID);

        // 测试未安装的设备
        $this->assertEquals([false, $_SERVER['REQUEST_TIME']],
            self::invokePrivateMethod($ref_class, 'installEquipInfo', Yii::$app->equip, $instance));

        // 测试已安装的设备
        $this->assertEquals([true, $_SERVER['REQUEST_TIME']],
            self::invokePrivateMethod($ref_class, 'installEquipInfo', Yii::$app->equip, $instance));
    }

    private static function resetEquipID()
    {
        self::setPrivateProperty(Yii::$app->equip, 'equip_id', self::TEST_EQUIP_ID);
    }

    public function testActionTabs()
    {
        // 测试参数错误
        $api = '/site/tabs';
        $params = ['boot' => -1];
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () use ($params, $api) {
            Request::api($api, $params, Request::GET, ['User-Agent' => 'MissEvanApp/6.0.3 (Android;7.0;Meizu M6 M6)']);
        });

        // 测试低版本客户端 h5 tab 被屏蔽
        $tab_live = [
            'id' => 3,
            'title' => '直播',
            'url' => 'missevan://live',
            'icon' => 'https://static-test.missevan.com/icon/a.png',
            'dark_icon' => 'https://static-test.missevan.com/icon/b.png',
        ];
        $tab_homepage = [
            'id' => 1,
            'title' => '推荐',
            'url' => 'missevan://homepage',
            'active' => 1,
            'page_mark' => 'missevan://homepage',
        ];
        $tab_catalogs = [
            'id' => 2,
            'title' => '分类',
            'url' => 'missevan://catalogs',
        ];
        $tab_drama = [
            'id' => 4,
            'title' => '广播剧',
            'url' => 'missevan://catalog/drama/89?homepage=1',
            'page_mark' => 'drama.drama_homepage',
        ];
        $tab_book = [
            'id' => 5,
            'title' => '听书',
            'url' => 'missevan://catalog/drama/86?homepage=1',
            'page_mark' => 'drama.catalog_86_homepage',
        ];
        $tab_love = [
            'id' => 6,
            'title' => '声音恋人',
            'url' => 'missevan://catalog/sound/108?homepage=1',
            'page_mark' => 'main.voicelover',
        ];
        $tab_h5 = [
            'id' => 7,
            'title' => '天官赐福',
            'url' => 'https://testabc.com',
            'icon' => 'https://static-test.missevan.com/icon/c.png',
            'dark_icon' => 'https://static-test.missevan.com/icon/d.png',
            'page_mark' => '786_homepage',
        ];
        $tab_newuser = [
            'id' => 8,
            'title' => '新人',
            'url' => 'missevan://homepage/newuser',
            'icon' => 'https://static-test.missevan.com/icon/e.png',
            'dark_icon' => 'https://static-test.missevan.com/icon/f.png',
            'page_mark' => 'main.new_user',
        ];
        $expected_tab_list = [
            'tabs' => [
                $tab_live, $tab_homepage, $tab_catalogs, $tab_drama, $tab_book, $tab_love, $tab_h5, $tab_newuser,
            ],
        ];
        $memcache = Yii::$app->memcache;
        $memcache->set(KEY_SITE_TAB_LIST, Json::encode($expected_tab_list));
        $result = Request::api($api, [], Request::GET,
            ['User-Agent' => 'MissEvanApp/5.2.9 (iOS;12.0;iPhone9,1)']);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertArrayHasKey('tabs', $result);
        $actual_tabs = $result['tabs'];
        $this->assertNotEmpty($actual_tabs);
        $expected_tabs = [
            $tab_live, $tab_homepage, $tab_catalogs, $tab_drama, $tab_book, $tab_love
        ];
        $this->assertEquals($expected_tabs, $actual_tabs);

        // 测试云游戏渠道 App 默认显示直播 Tab
        $result = Request::api($api, [], Request::GET, [
            'channel' => Equipment::CHANNEL_YUNYOUXI
        ]);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertArrayHasKey('tabs', $result);
        $actual_tabs = $result['tabs'];
        $this->assertNotEmpty($actual_tabs);
        $tab_live['active'] = 1;
        unset($tab_homepage['active']);
        $expected_tabs = [
            $tab_live, $tab_homepage, $tab_catalogs, $tab_drama, $tab_book, $tab_love
        ];
        $this->assertEquals($expected_tabs, $actual_tabs);

        // 测试新人 Tab 页
        // 测试 iOS >= 6.0.3 时游客新设备激活后 30s 内启动显示新人 Tab 且默认定位到新人 Tab（新人 Tab 位置在第 2 位）
        $test_ios_equip_id = '5ae93485-test-b665-9ada-a26e4dda4233';
        $key = self::$redis->generateKey(KEY_NEW_DEVICE_EQUIP_ID, $test_ios_equip_id);
        self::$redis->del($key);
        self::$redis->setex($key, ONE_HOUR, $_SERVER['REQUEST_TIME'] - HALF_MINUTE);
        self::logout();
        self::setPrivateProperty(Yii::$app->equip, 'activate_time', null);
        $result = Request::api($api, ['boot' => SiteController::BOOT], Request::GET, [
            'User-Agent' => 'MissEvanApp/6.0.3 (iOS;12.0;iPhone10,1)',
            'Cookie' => 'equip_id=' . $test_ios_equip_id,
        ], ['request_time' => $_SERVER['REQUEST_TIME']]);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertArrayHasKey('tabs', $result);
        $actual_tabs = $result['tabs'];
        $this->assertNotEmpty($actual_tabs);
        $tab_newuser['active'] = 1;
        unset($tab_live['active']);
        $expected_tabs_new = [
            $tab_live, $tab_newuser, $tab_homepage, $tab_catalogs, $tab_drama, $tab_book, $tab_love
        ];
        $this->assertEquals($expected_tabs_new, $actual_tabs);

        // 测试 iOS >= 6.0.3 时新用户显示新人 Tab 但不定位到新人 Tab（新人 Tab 位置在第 1 位）
        self::loginByUserId(self::TEST_USER_ID);
        $result = Request::api($api, ['boot' => SiteController::BOOT], Request::GET,
            ['User-Agent' => 'MissEvanApp/6.0.3 (iOS;12.0;iPhone9,1)']);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertArrayHasKey('tabs', $result);
        $actual_tabs = $result['tabs'];
        $tab_homepage['active'] = 1;
        unset($tab_newuser['active']);
        $expected_tabs = [
            $tab_live, $tab_homepage, $tab_catalogs, $tab_drama, $tab_book, $tab_love, $tab_newuser,
        ];
        $this->assertNotEmpty($actual_tabs);
        $this->assertEquals($expected_tabs, $actual_tabs);

        // 测试配置了渠道 Tab（渠道承接位置为直播时，默认定位到直播 Tab）
        $expected_tabs[1]['active'] = 1;
        unset($expected_tabs[2]['active']);
        $result = Request::api($api, ['boot' => SiteController::BOOT], Request::GET, [
            'User-Agent' => 'MissEvanApp/6.0.3 (Android;7.0;Meizu M6 M6)',
            'Cookie' => 'equip_id=' . self::TEST_EQUIP_ID3,
        ]);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertArrayHasKey('tabs', $result);
        $actual_tabs = $result['tabs'];
        $this->assertNotEmpty($actual_tabs);
        $tab_live['active'] = 1;
        unset($tab_homepage['active']);
        $expected_tabs = [
            $tab_live, $tab_homepage, $tab_catalogs, $tab_drama, $tab_book, $tab_love, $tab_newuser,
        ];
        $this->assertEquals($expected_tabs, $actual_tabs);

        // 测试 Android < 6.0.3 时不显示新人 Tab 页
        $result = Request::api($api, ['boot' => SiteController::BOOT], Request::GET,
            ['User-Agent' => 'MissEvanApp/6.0.2 (Android;7.0;Meizu M6 M6)']);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertArrayHasKey('tabs', $result);
        $actual_tabs = $result['tabs'];
        $this->assertNotEmpty($actual_tabs);
        $tab_homepage['active'] = 1;
        unset($tab_live['active']);
        $expected_tabs = [
            $tab_live, $tab_homepage, $tab_catalogs, $tab_drama, $tab_book, $tab_love,
        ];
        $this->assertEquals($expected_tabs, $actual_tabs);

        // 测试老用户不显示新人 Tab 页
        self::loginByUserId(self::TEST_USER_ID, ['ctime' => $_SERVER['REQUEST_TIME'] - 3 * ONE_WEEK]);
        $result = Request::api($api, ['boot' => SiteController::BOOT], Request::GET,
            ['User-Agent' => 'MissEvanApp/6.0.3 (iOS;12.0;iPhone9,1)']);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertArrayHasKey('tabs', $result);
        $actual_tabs = $result['tabs'];
        $this->assertNotEmpty($actual_tabs);
        $this->assertEquals($expected_tabs, $actual_tabs);

        // 测试客户端 >= 6.2.2 版本时，当设备未启用 tab bar 直播按钮，则顶部推荐 tab 标题不被改为"发现"
        $result = Request::api($api, ['boot' => SiteController::BOOT], Request::GET,
            [
                'Cookie' => 'equip_id=01e1b22d-b49d-4d27-fce5-90e085714cd5',
                'User-Agent' => 'MissEvanApp/6.2.2 (Android;7.0;Meizu M6 M6)',
            ]);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertArrayHasKey('tabs', $result);
        $actual_tabs = $result['tabs'];
        $this->assertNotEmpty($actual_tabs);
        $this->assertEquals($expected_tabs, $actual_tabs);

        // 测试客户端 >= 6.2.2 版本时，当设备启用了 tab bar 直播按钮，则顶部推荐 tab 标题改为"发现"
        $result = Request::api($api, ['boot' => SiteController::BOOT], Request::GET,
            [
                'Cookie' => 'equip_id=d12cbc21-c41d-1c17-11e1-22e085c14c11',
                'User-Agent' => 'MissEvanApp/6.2.2 (Android;7.0;Meizu M6 M6)',
            ]);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertArrayHasKey('tabs', $result);
        $actual_tabs = $result['tabs'];
        $this->assertNotEmpty($actual_tabs);
        $tab_homepage['title'] = '发现';
        $expected_tabs = [
            $tab_live, $tab_homepage, $tab_catalogs, $tab_drama, $tab_book, $tab_love
        ];
        $this->assertEquals($expected_tabs, $actual_tabs);

        // 测试客户端 < 6.3.1 版本时，不展示 h5 tab
        $result = Request::api($api, [], Request::GET,
            ['User-Agent' => 'MissEvanApp/6.3.0 (iOS;12.0;iPhone9,1)']);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertArrayHasKey('tabs', $result);
        $actual_tabs = $result['tabs'];
        $this->assertNotEmpty($actual_tabs);
        $this->assertEquals($expected_tabs, $actual_tabs);

        // 测试客户端 >= 6.3.1 版本时，展示 h5 tab
        $result = Request::api($api, [], Request::GET,
            ['User-Agent' => 'MissEvanApp/6.3.1 (iOS;12.0;iPhone9,1)']);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertArrayHasKey('tabs', $result);
        $actual_tabs = $result['tabs'];
        $this->assertNotEmpty($actual_tabs);
        $expected_tabs = [
            $tab_live, $tab_homepage, $tab_catalogs, $tab_drama, $tab_book, $tab_love, $tab_h5,
        ];
        $this->assertEquals($expected_tabs, $actual_tabs);
    }

    public function testActionInstall()
    {
        Yii::$app->set('growthdb', Yii::$app->sqlite_growthdb);
        $test_ios_equip_id = '5ae93485-test-b665-9ada-a26e4dda4233';
        $object_key = self::$redis->generateKey(KEY_NEW_DEVICE_EQUIP_ID, $test_ios_equip_id);
        self::$redis->del($object_key);
        InstallBuvid::deleteAll('buvid = :buvid', [':buvid' => 'test_ios_buvid']);
        $result = Request::api('/site/install', [
                'type' => InstallLog::TYPE_NEW,
                'buvid' => 'test_ios_buvid',
                'user_agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 10_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
            ], Request::POST, [
                'Cookie' => 'buvid=test_ios_buvid;equip_id=' . $test_ios_equip_id,
                'User-Agent' => 'MissEvanApp/6.1.8 (iOS;12.1.4;iPhone10,3)',
            ]);
        $this->assertArrayHasKeys(['msg', 'device_token'], $result);
        $this->assertEquals('安装成功', $result['msg']);
        $install_log = InstallLog::find()->select('id')
            ->where(['equip_id' => $test_ios_equip_id, 'device_type' => Equipment::iOS])
            ->orderBy('id DESC')
            ->limit(1)
            ->one();
        $this->assertNotNull($install_log);
        InstallLog::deleteAll(['id' => $install_log->id]);
        $this->assertEquals($_SERVER['REQUEST_TIME'], self::$redis->get($object_key));

        $query = http_build_query($_GET = [
            'ct' => 'missevan_vivo',
            'buvid' => 'test_android_buvid',
            'drm_id' => 'test_drm_id',
        ]);
        $test_android_equip_id = '6247cf87-3340-0aba-b2df-fe09c0960dbd';
        $object_key = self::$redis->generateKey(KEY_NEW_DEVICE_EQUIP_ID, $test_android_equip_id);
        self::$redis->del($object_key);
        InstallBuvid::deleteAll('buvid = :buvid', [':buvid' => 'test_android_buvid']);
        $result = Request::api('/site/install?' . $query,
            [
                'user_agent' => 'Mozilla/5.0 (Linux; Android 11; V2134A Build/RP1A.200720.012; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/99.0.4844.73 Mobile Safari/537.36',
                'screen_dpr' => '1.756',
                'screen_resolution' => '1080x1920',
                'screen_native_resolution' => '1080x2340',
            ],
            Request::POST,
            [
                'Cookie' => 'buvid=test_android_buvid;equip_id=' . $test_android_equip_id,
                'User-Agent' => 'MissEvanApp/6.1.7 (Android;11;vivo V2134A PD2134)',
            ]);
        $this->assertArrayHasKeys(['msg', 'device_token'], $result);
        $this->assertEquals('安装成功', $result['msg']);
        $install_log = InstallLog::find()->select('id, more')
            ->where(['equip_id' => $test_android_equip_id, 'device_type' => Equipment::Android])
            ->orderBy('id DESC')
            ->limit(1)
            ->one();
        $this->assertNotNull($install_log);
        $this->assertNotEmpty($install_log->more);
        $this->assertArrayHasKeys(['drm_id', 'screen_resolution', 'screen_dpr', 'device_info', 'screen_native_resolution'], $install_log->more);
        $this->assertEquals('1080x1920', $install_log->more['screen_resolution']);
        $this->assertEquals('1080x2340', $install_log->more['screen_native_resolution']);
        // 测试设备像素比四舍五入保留两位小数
        $this->assertEquals('1.76', $install_log->more['screen_dpr']);
        $this->assertEquals('test_drm_id', $install_log->more['drm_id']);
        InstallLog::deleteAll(['id' => $install_log->id]);
        $this->assertEquals($_SERVER['REQUEST_TIME'], self::$redis->get($object_key));
    }

    public function testTabIcons()
    {
        $memcache = Yii::$app->memcache;
        $TEST_ICON = 'https://static-test.missevan.com/mimages/202004/23/2bd0d87e8bb960046b71f12aa534a673171552.png';
        $expected_icon_list = [
            'icons' => [
                [
                    'id' => 2,
                    'title' => '时间表',
                    'url' => 'missevan://drama/timeline',
                    'icon' => $TEST_ICON,
                    'dark_icon' => $TEST_ICON,
                ],
                [
                    'id' => 1,
                    'title' => '索引',
                    'url' => 'missevan://drama/filter',
                    'icon' => $TEST_ICON,
                    'dark_icon' => $TEST_ICON,
                ],
            ]
        ];
        $expected_icons = $expected_icon_list['icons'];

        $key = MUtils::generateCacheKey(KEY_TAB_ICON_LIST, self::TEST_ICON_TAB_ID);
        $memcache->set($key, Json::encode($expected_icon_list));
        $result = Request::api('/site/tab-icons', ['tab_id' => self::TEST_ICON_TAB_ID], Request::GET);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertArrayHasKey('icons', $result);
        $actual_icons = $result['icons'];
        $this->assertNotEmpty($actual_icons);
        $this->assertEquals(count($expected_icons), count($actual_icons));
        foreach ($actual_icons as $key => $icon) {
            $expected = $expected_icons[$key];
            $expected['icon'] .= MHomepageIcon::ICON_WEBP_PARAM;
            $expected['dark_icon'] .= MHomepageIcon::ICON_WEBP_PARAM;
            $this->assertEquals($expected, $icon);
        }

        // 测试 iOS 没有拼接 webp 参数
        $result = Request::api('/site/tab-icons', ['tab_id' => self::TEST_ICON_TAB_ID], Request::GET, [
            'User-Agent' => 'MissEvanApp/4.5.2 (iOS;13.5.1;iPhone10,3)'
        ]);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertArrayHasKey('icons', $result);
        $actual_icons = $result['icons'];
        $this->assertNotEmpty($actual_icons);
        // 断言没有拼接 webp 参数
        $actual_icons = array_column($actual_icons, 'icon');
        foreach ($actual_icons as $icon) {
            $this->assertEquals($TEST_ICON, $icon);
        }
        $actual_dark_icons = array_column($actual_icons, 'dark_icon');
        foreach ($actual_dark_icons as $dark_icon) {
            $this->assertEquals($TEST_ICON, $dark_icon);
        }

        // 测试安卓 < 5.5.5 版本拼接了 webp 参数
        $result = Request::api('/site/tab-icons', ['tab_id' => self::TEST_ICON_TAB_ID], Request::GET, [
            'User-Agent' => 'MissEvanApp/5.5.3 (Android;7.0;Meizu M6 M6)'
        ]);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertArrayHasKey('icons', $result);
        $actual_icons = $result['icons'];
        $this->assertNotEmpty($actual_icons);
        // 断言拼接 webp 参数
        $actual_icons = array_column($actual_icons, 'icon');
        foreach ($actual_icons as $icon) {
            $this->assertEquals($TEST_ICON . MHomepageIcon::ICON_WEBP_PARAM, $icon);
        }
        $actual_dark_icons = array_column($actual_icons, 'dark_icon');
        foreach ($actual_dark_icons as $dark_icon) {
            $this->assertEquals($TEST_ICON . MHomepageIcon::ICON_WEBP_PARAM, $dark_icon);
        }

        // 测试安卓 >= 5.5.3 版本没有拼接 webp 参数
        $result = Request::api('/site/tab-icons', ['tab_id' => self::TEST_ICON_TAB_ID], Request::GET, [
            'User-Agent' => 'MissEvanApp/5.5.5 (Android;7.0;Meizu M6 M6)'
        ]);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertArrayHasKey('icons', $result);
        $actual_icons = $result['icons'];
        $this->assertNotEmpty($actual_icons);
        // 断言没有拼接 webp 参数
        $actual_icons = array_column($actual_icons, 'icon');
        foreach ($actual_icons as $icon) {
            $this->assertEquals($TEST_ICON, $icon);
        }
        $actual_dark_icons = array_column($actual_icons, 'dark_icon');
        foreach ($actual_dark_icons as $dark_icon) {
            $this->assertEquals($TEST_ICON, $dark_icon);
        }
    }

    public function testSupportCountry()
    {
        $arr = range('A', 'Z');
        $common_countries = [
            'CN', 'HK', 'MO', 'TW', 'US', 'BE', 'AU', 'FR', 'CA', 'JP', 'SG', 'KR', 'MY', 'GB', 'IT', 'DE', 'RU', 'NZ'
        ];
        // 测试安卓 < 5.8.2 时
        $result = Request::api('/site/support-country', [], Request::GET,
            ['User-Agent' => 'MissEvanApp/5.8.1 (Android;7.0;Meizu M6 M6)']);
        $this->assertIsArray($result);
        $this->assertArrayNotHasKey('#', $result);
        $this->assertArrayHasKeys($arr, $result);
        $this->assertIsArray($result['A']);
        $this->assertArrayHasKey('is_common', $result['A'][0]);
        $this->assertEquals($arr, array_keys($result));

        // 测试 iOS < 4.9.9 时
        $result = Request::api('/site/support-country', [], Request::GET,
            ['User-Agent' => 'MissEvanApp/4.9.8 (iOS;11.4.1;iPhone8,4)']);
        $this->assertIsArray($result);
        $this->assertArrayNotHasKey('#', $result);
        $this->assertArrayHasKeys($arr, $result);
        $this->assertIsArray($result['A']);
        $this->assertArrayHasKey('is_common', $result['A'][0]);
        $this->assertEquals($arr, array_keys($result));

        // 测试安卓 >= 5.8.2 时
        $result = Request::api('/site/support-country', [], Request::GET,
            ['User-Agent' => 'MissEvanApp/5.8.2 (Android;7.0;Meizu M6 M6)']);
        $this->assertIsArray($result);
        $this->assertArrayHasKey('#', $result);
        $this->assertArrayHasKeys($arr, $result);
        $this->assertIsArray($result['A']);
        $this->assertArrayNotHasKey('is_common', $result['A'][0]);
        $this->assertIsArray($result['#']);
        $this->assertArrayNotHasKey('is_common', $result['#'][0]);
        array_unshift($arr, '#');
        $this->assertEquals($arr, array_keys($result));
        $this->assertEquals($common_countries, array_column($result['#'], 'code'));

        // 测试 iOS >= 4.9.9 时
        $result = Request::api('/site/support-country', [], Request::GET,
            ['User-Agent' => 'MissEvanApp/4.9.9 (iOS;11.4.1;iPhone8,4)']);
        $this->assertIsArray($result);
        $this->assertArrayHasKey('#', $result);
        $this->assertArrayHasKeys($arr, $result);
        $this->assertIsArray($result['A']);
        $this->assertArrayNotHasKey('is_common', $result['A'][0]);
        $this->assertIsArray($result['#']);
        $this->assertArrayNotHasKey('is_common', $result['#'][0]);
        $this->assertEquals($arr, array_keys($result));
        $this->assertEquals($common_countries, array_column($result['#'], 'code'));
    }

    // 测试获取启动音
    public function testPowerSound()
    {
        $this->loginByUserId(self::TEST_USER_ID);

        // 测试获取启动音（下发 m4a）
        $_SERVER['REMOTE_ADDR'] = Data::TEST_JAPAN_IP;
        $data = Request::api('/site/power-sound');
        $this->assertNotEmpty($data);
        $count = (int)MPowerSound::find()->where(['archive' => MPowerSound::ARCHIVE_ONLINE])->count();
        $this->assertEquals($count, count($data));
        $this->assertArrayHasKeys(['eid', 'surl', 'purl', 'intro', 'iconurl'], $data[0]);
        $this->assertEquals('https://static-test.missevan.com/aod/test.m4a', $data[0]['surl']);

        // 测试获取启动音（下发 mp3）
        self::setPrivateProperty(MSound::class, 'is_mp3_user', null);
        $data = Request::api('/site/power-sound', [], Request::GET, [
            'User-Agent' => 'MissEvanApp/5.6.1 (Android;6.0;HUAWEI M6 M6)',
        ]);
        $this->assertNotEmpty($data);
        $this->assertArrayHasKeys(['eid', 'surl', 'purl', 'intro', 'iconurl'], $data[0]);
        $this->assertEquals('https://static-test.missevan.com/MP3/test.mp3', $data[0]['surl']);
    }

    // 测试获取启动音
    public function testActionNewPowerSound()
    {
        self::loginByUserId(self::TEST_USER_ID);

        // 测试获取启动音
        $key = MUtils::generateCacheKey(KEY_NEW_POWER_SOUND, ...[1, MPowerSound::DEFAULT_PAGE_SIZE]);
        Yii::$app->memcache->delete($key);
        $data = Request::api('/site/new-power-sound', ['keyword' => '大家好，这是测试数据']);
        $this->assertEquals(1, count($data->Datas));
        $this->assertIsValidHttpUrl($data->Datas[0]['list'][0]['soundurl']);
        // 断言 IPR 支持默认选中
        $this->assertTrue($data->Datas[0]['support_default_selection']);

        // 有缓存的情况
        $data = Request::api('/site/new-power-sound');
        $this->assertEquals(1, count($data->Datas));
        $this->assertIsValidHttpUrl($data->Datas[0]['list'][0]['soundurl']);

        // 测试 IPR 不支持默认选中
        MPowerSoundIp::updateAll(['attr' => MPowerSoundIp::ATTR_UNSUPPORTED_DEFAULT_SELECTION],
            'ip_name = :name', [':name' => $data->Datas[0]['ip_name']]);
        $data = Request::api('/site/new-power-sound', ['keyword' => '大家好，这是测试数据']);
        $this->assertFalse($data->Datas[0]['support_default_selection']);

        // 设置为删除
        if (!MPowerSound::updateByPk(self::TEST_POWER_SOUND_ID, ['archive' => MPowerSound::ARCHIVE_ARCHIVED])) {
            throw new Exception('设置为删除失败');
        }
        $data = Request::api('/site/new-power-sound', ['keyword' => '大家好，这是测试数据']);
        $this->assertEquals(0, count($data->Datas));
    }

    /**
     * 测试用户 APP 配置项设置接口
     */
    public function testSetUserConfig()
    {
        MUserConfig::deleteAll(['user_id' => self::TEST_USER_ID]);
        $api = '/site/set-user-config';
        // 测试参数错误的情况
        // type 不在允许值之内
        $params = ['type' => 'test', 'value' => MUserConfig::APP_CONF_ENABLE];
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () use ($params, $api) {
            Request::api($api, $params, Request::POST);
        });

        // 不能单独设置推送消息子项
        $params = [
            'type' => MUserConfig::MESSAGE_NOTIFICATION_AT_ME,
            'value' => MUserConfig::APP_CONF_ENABLE,
        ];
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () use ($params, $api) {
            Request::api($api, $params, Request::POST);
        });

        // 推送消息对应的 vaule 非 JSON 字符串
        $params = [
            'type' => MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION,
            'value' => '{abc}',
        ];
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () use ($params, $api) {
            Request::api($api, $params, Request::POST);
        });

        // 推送消息对应的 vaule 值错误
        $params = [
            'type' => MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION,
            'value' => MUserConfig::APP_CONF_ENABLE,
        ];
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () use ($params, $api) {
            Request::api($api, $params, Request::POST);
        });

        // 推送消息对应的子项中 value 值错误
        $value = [
            MUserConfig::MESSAGE_NOTIFICATION_AT_ME => '1',
            MUserConfig::MESSAGE_NOTIFICATION_LIKE => '0',
            MUserConfig::MESSAGE_NOTIFICATION_COMMENT => '1',
            MUserConfig::MESSAGE_NOTIFICATION_PRIVATE_MESSAGE => '0',
            MUserConfig::MESSAGE_NOTIFICATION_LIVE => '2',
            MUserConfig::MESSAGE_NOTIFICATION_INTEREST_RECOMMEND => '1',
        ];
        $value = Json::encode($value);
        $params = ['type' => MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION, 'value' => $value];
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () use ($params, $api) {
            Request::api($api, $params, Request::POST);
        });

        // 测试无记录时设置用户选项配置
        $params = [
            'type' => MUserConfig::APP_CONF_TYPE_PERSONALIZED_RECOMMEND,
            'value' => '1',
        ];
        $result = Request::api($api, $params, Request::POST);
        $this->assertEquals(MUserConfig::APP_CONF_ENABLE, $result);
        // 验证已创建记录
        $this->assertTrue(MUserConfig::find()
            ->where(['buvid' => self::TEST_BUVID2])->exists());

        // 测试有记录时设置用户选项配置（选项值不变）
        $result = Request::api($api, $params, Request::POST);
        // 验证接口保持幂等性
        $this->assertEquals(MUserConfig::APP_CONF_ENABLE, $result);

        // 测试有记录时修改设置用户选项配置
        $params['value'] = MUserConfig::APP_CONF_DISABLE;
        $result = Request::api($api, $params, Request::POST);
        $this->assertEquals(MUserConfig::APP_CONF_DISABLE, $result);

        // 测试未登录时不能设置推送消息
        $value = Json::encode(MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION_CONF);
        $params = ['type' => 'message_notification', 'value' => $value];
        $this->assertThrowsWithMessage(HttpException::class, '请先登录', function () use ($params, $api) {
            Request::api($api, $params, Request::POST);
        });

        // 测试登录时设置用户选项配置
        self::loginByUserId();
        $result = Request::api($api, $params, Request::POST);
        $this->assertEquals($value, $result);
        $this->assertTrue(MUserConfig::find()
            ->where(['user_id' => self::TEST_USER_ID, 'buvid' => ''])->exists());

        // 测试无记录时设置在个人主页公开“我的追剧”
        $params = [
            'type' => MUserConfig::APP_CONF_TYPE_SHOW_SUBSCRIBE_DRAMA,
            'value' => MUserConfig::APP_CONF_ENABLE,
        ];
        $result = Request::api($api, $params, Request::POST);
        $this->assertEquals(MUserConfig::APP_CONF_ENABLE, $result);
        // 验证已创建记录
        $this->assertTrue(MUserConfig::find()
            ->where(['buvid' => self::TEST_BUVID2])->exists());

        // 测试有记录时设置在个人主页公开“我的追剧”（选项值不变）
        $result = Request::api($api, $params, Request::POST);
        // 验证接口保持幂等性
        $this->assertEquals(MUserConfig::APP_CONF_ENABLE, $result);

        // 测试有记录时修改设置个人主页公开“我的追剧”
        $params['value'] = MUserConfig::APP_CONF_DISABLE;
        $result = Request::api($api, $params, Request::POST);
        $this->assertEquals(MUserConfig::APP_CONF_DISABLE, $result);

        // 测试登录时设置个人主页公开“我的追剧”
        self::loginByUserId();
        $params['value'] = MUserConfig::APP_CONF_ENABLE;
        $result = Request::api($api, $params, Request::POST);
        $this->assertEquals(MUserConfig::APP_CONF_ENABLE, $result);
        $this->assertTrue(MUserConfig::find()
            ->where(['user_id' => self::TEST_USER_ID, 'buvid' => ''])->exists());

        // 测试登录时成功设置推送消息
        $value = Json::encode([
            MUserConfig::MESSAGE_NOTIFICATION_LIVE => '1',
            MUserConfig::MESSAGE_NOTIFICATION_INTEREST_RECOMMEND => '1',
            MUserConfig::MESSAGE_NOTIFICATION_AT_ME => '1',
            MUserConfig::MESSAGE_NOTIFICATION_LIKE => '0',
            MUserConfig::MESSAGE_NOTIFICATION_PRIVATE_MESSAGE => '0',
            MUserConfig::MESSAGE_NOTIFICATION_COMMENT => '1',
        ]);
        $params = ['type' => MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION, 'value' => $value];
        $result = Request::api($api, $params, Request::POST);
        $this->assertEquals($value, $result);
        $config = MUserConfig::find()
            ->select('app_config')
            ->where(['user_id' => self::TEST_USER_ID])->scalar();
        $this->assertNotNull($config);
        $config = Json::decode($config);
        $this->assertArrayHasKey(MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION, $config);
        $notification_config = $config[MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION];
        $this->assertArrayHasKeys([
            MUserConfig::MESSAGE_NOTIFICATION_AT_ME,
            MUserConfig::MESSAGE_NOTIFICATION_LIKE,
            MUserConfig::MESSAGE_NOTIFICATION_COMMENT,
            MUserConfig::MESSAGE_NOTIFICATION_PRIVATE_MESSAGE,
            MUserConfig::MESSAGE_NOTIFICATION_LIVE,
            MUserConfig::MESSAGE_NOTIFICATION_INTEREST_RECOMMEND,
        ], $notification_config);
        $want = [
            MUserConfig::MESSAGE_NOTIFICATION_AT_ME => MUserConfig::APP_CONF_ENABLE,
            MUserConfig::MESSAGE_NOTIFICATION_LIKE => MUserConfig::APP_CONF_DISABLE,
            MUserConfig::MESSAGE_NOTIFICATION_COMMENT => MUserConfig::APP_CONF_ENABLE,
            MUserConfig::MESSAGE_NOTIFICATION_PRIVATE_MESSAGE => MUserConfig::APP_CONF_DISABLE,
            MUserConfig::MESSAGE_NOTIFICATION_LIVE => MUserConfig::APP_CONF_ENABLE,
            MUserConfig::MESSAGE_NOTIFICATION_INTEREST_RECOMMEND => MUserConfig::APP_CONF_ENABLE,
        ];
        $this->assertEquals($want, $notification_config);
        // 验证数据库存储的值
        $app_config = MUserConfig::find()
            ->select('app_config')
            ->where(['user_id' => self::TEST_USER_ID])->scalar();
        $app_config = Json::decode($app_config);
        $this->assertArrayHasKey(MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION, $config);
        $this->assertEquals($want, $app_config[MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION]);
    }

    /**
     * 测试获取用户 APP 选项配置信息接口
     */
    public function testGetUserConfig()
    {
        // 清理测试数据
        MUserConfig::deleteAll('user_id IN (:user_id_1, :user_id_2) OR buvid = :buvid', [
            ':user_id_1' => self::TEST_USER_ID,
            ':user_id_2' => self::TEST_USER2_ID,
            ':buvid' => self::TEST_BUVID2,
        ]);

        // 测试无记录时获取用户选项配置
        $api = '/site/get-user-config';
        $result = Request::api($api);
        $this->assertEquals(MUserConfig::APP_DEFAULT_EQUIP_CONF, $result);

        // 测试未登录，设备有记录时获取用户选项配置
        $equip_config = Data::createMUserConfig(['user_id' => 0, 'buvid' => self::TEST_BUVID2]);
        $result = Request::api($api);
        $this->assertEquals(MUserConfig::APP_DEFAULT_EQUIP_CONF, $result);

        // 测试关闭选项时可获得正确值
        $equip_config->app_config = [
            MUserConfig::APP_CONF_TYPE_PERSONALIZED_RECOMMEND => MUserConfig::APP_CONF_DISABLE,
        ];
        $this->assertTrue($equip_config->save());
        $result = Request::api($api);
        $this->assertEquals(MUserConfig::APP_CONF_DISABLE,
            $result[MUserConfig::APP_CONF_TYPE_PERSONALIZED_RECOMMEND]);

        // 测试登录时，用户无记录，设备有记录的情况
        self::loginByUserId();
        $result = Request::api($api);
        $app_config = MUserConfig::APP_DEFAULT_USER_CONF;
        $app_config[MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION][MUserConfig::MESSAGE_NOTIFICATION_PRIVATE_MESSAGE]
            = MUserConfig::APP_CONF_DISABLE;
        $app_config[MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION] = Json::encode($app_config[MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION]);
        $this->assertEquals(array_merge($app_config, MUserConfig::APP_DEFAULT_EQUIP_CONF), $result);

        // 测试登录时，用户有记录的情况
        Data::createMUserConfig([
            'user_id' => self::TEST_USER_ID,
            'buvid' => '',
            'app_config' => [MUserConfig::APP_CONF_TYPE_PERSONALIZED_RECOMMEND => MUserConfig::APP_CONF_DISABLE,
                MUserConfig::APP_CONF_TYPE_SHOW_SUBSCRIBE_DRAMA => MUserConfig::APP_CONF_DISABLE,
                MUserConfig::APP_CONF_TYPE_SHOW_USER_COLLECT => MUserConfig::APP_CONF_DISABLE]
        ]);
        $result = Request::api($api, [], Request::POST, ['User-Agent' => 'MissEvanApp/4.9.6 (iOS;12.2;iPhone9,1)']);
        $this->assertArrayHasKeys([
            MUserConfig::APP_CONF_TYPE_PERSONALIZED_RECOMMEND,
            MUserConfig::APP_CONF_TYPE_SHOW_SUBSCRIBE_DRAMA,
            MUserConfig::APP_CONF_TYPE_SHOW_USER_COLLECT,
            MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION,
        ], $result);
        $this->assertEquals(MUserConfig::APP_CONF_DISABLE,
            $result[MUserConfig::APP_CONF_TYPE_PERSONALIZED_RECOMMEND]);
        $this->assertEquals(MUserConfig::APP_CONF_DISABLE,
            $result[MUserConfig::APP_CONF_TYPE_SHOW_SUBSCRIBE_DRAMA]);
        $this->assertEquals(MUserConfig::APP_CONF_DISABLE,
            $result[MUserConfig::APP_CONF_TYPE_SHOW_USER_COLLECT]);
        $notification_config = Json::decode($result[MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION]);

        $this->assertArrayHasKeys([
            MUserConfig::MESSAGE_NOTIFICATION_AT_ME,
            MUserConfig::MESSAGE_NOTIFICATION_LIKE,
            MUserConfig::MESSAGE_NOTIFICATION_COMMENT,
            MUserConfig::MESSAGE_NOTIFICATION_PRIVATE_MESSAGE,
            MUserConfig::MESSAGE_NOTIFICATION_LIVE,
            MUserConfig::MESSAGE_NOTIFICATION_INTEREST_RECOMMEND,
        ], $notification_config);
        $this->assertEquals(MUserConfig::APP_CONF_DISABLE, $notification_config[MUserConfig::MESSAGE_NOTIFICATION_PRIVATE_MESSAGE]);

        // 测试注册时间 >= 2023-04-30 00:00:00 且 iOS 版本号 >= 4.9.8 时保持开启私信推送
        $time = $_SERVER['REQUEST_TIME'];
        $_SERVER['REQUEST_TIME'] = MUserConfig::MESSAGE_NOTIFICATION_PRIVATE_MESSAGE_ENABLE_TIME;
        self::loginByUserId(self::TEST_USER2_ID);
        $result = Request::api($api, [], Request::POST, ['User-Agent' => 'MissEvanApp/4.9.8 (iOS;12.2;iPhone9,1)']);
        $this->assertArrayHasKey(MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION, $result);
        $notification = Json::decode($result[MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION]);
        $this->assertArrayHasKey(MUserConfig::MESSAGE_NOTIFICATION_PRIVATE_MESSAGE, $notification);
        $this->assertEquals(MUserConfig::APP_CONF_ENABLE, $notification[MUserConfig::MESSAGE_NOTIFICATION_PRIVATE_MESSAGE]);

        // 测试注册时间 >= 2023-04-30 00:00:00 且 iOS 版本号 < 4.9.8 时关闭私信推送
        $result = Request::api($api, [], Request::POST, ['User-Agent' => 'MissEvanApp/4.9.7 (iOS;12.2;iPhone9,1)']);
        $this->assertArrayHasKey(MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION, $result);
        $notification = Json::decode($result[MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION]);
        $this->assertArrayHasKey(MUserConfig::MESSAGE_NOTIFICATION_PRIVATE_MESSAGE, $notification);
        $this->assertEquals(MUserConfig::APP_CONF_DISABLE, $notification[MUserConfig::MESSAGE_NOTIFICATION_PRIVATE_MESSAGE]);
        $_SERVER['REQUEST_TIME'] = $time;
    }

    public function testGetEmote()
    {
        // 测试不支持专属表情包的客户端获取表情包地址
        $data = Request::api('/site/get-emote', [], Request::GET,
            ['User-Agent' => 'MissEvanApp/4.9.8 (iOS;12.2;iPhone9,1)']);
        $this->assertIsValidHttpUrl($data['package_url']);
        $this->assertContains('emote/no_exclusive_emote.zip', $data['package_url']);

        // 测试支持专属表情包的客户端获取表情包地址
        $data = Request::api('/site/get-emote', [], Request::GET,
            ['User-Agent' => 'MissEvanApp/4.9.9 (iOS;12.2;iPhone9,1)']);
        $this->assertIsValidHttpUrl($data['package_url']);
        $this->assertContains('emote/emote.zip', $data['package_url']);
    }

    public function testTrackConsumePay()
    {
        // 测试消费关键行为回传
        $resp = Request::api('/site/track-consume-pay',
            ['diamond' => 100, 'type' => AdTrack::CONVERSION_TRANSACTION_TYPE_CONSUME, 'event_id' => 'live.live_room.tab_bar.gifts.click', 'status' => 1],
            Request::POST);
        $this->assertTrue($resp);

        // 测试 event_id 参数错误
        $this->assertThrowsWithMessage(Exception::class, '参数错误',
            function () {
                Request::api('/site/track-consume-pay',
                    ['diamond' => 100, 'type' => AdTrack::CONVERSION_TRANSACTION_TYPE_CONSUME, 'event_id' => '', 'status' => 1],
                    Request::POST);
            });

        // 测试 diamond 参数错误
        $this->assertThrowsWithMessage(Exception::class, '参数错误',
            function () {
                Request::api('/site/track-consume-pay',
                    ['diamond' => -1, 'type' => AdTrack::CONVERSION_TRANSACTION_TYPE_PAY, 'event_id' => 'live.live_room.tab_bar.gifts.click', 'status' => 1],
                    Request::POST);
            });

        // 测试 type 参数错误
        $this->assertThrowsWithMessage(Exception::class, '参数错误',
            function () {
                Request::api('/site/track-consume-pay',
                    ['diamond' => 100, 'type' => 3, 'event_id' => 'live.live_room.tab_bar.gifts.click', 'status' => 1],
                    Request::POST);
            });

        // 测试 status 参数错误
        $this->assertThrowsWithMessage(Exception::class, '参数错误',
            function () {
                Request::api('/site/track-consume-pay',
                    ['diamond' => 100, 'status' => 3, 'event_id' => 'live.live_room.tab_bar.gifts.click'],
                    Request::POST);
            });

        // 测试充值关键行为回传
        $resp = Request::api('/site/track-consume-pay',
            ['diamond' => 100, 'type' => AdTrack::CONVERSION_TRANSACTION_TYPE_PAY, 'event_id' => 'live.live_room.tab_bar.gifts.click', 'status' => 1],
            Request::POST);
        $this->assertTrue($resp);
    }

    public function testActionAdTrack()
    {
        // 删除测试数据
        InstallBuvid::deleteAll('buvid = :buvid', [':buvid' => 'test_android_buvid_1']);
        AdTrack::deleteAll('bili_buvid = :bili_buvid AND converted = :converted',
            [':bili_buvid' => 'test_android_buvid_1', ':converted' => AdTrack::NOT_CONVERTED]);

        // 测试促活广告归因
        // 测试没有 track_from_url 参数
        $this->assertThrowsWithMessage(Exception::class, '非法请求',
            function () {
                Request::api('/site/ad-track', [], Request::POST);
            });

        Yii::$app->request->setRawBody(Json::encode([
            'android_id' => 'd517f85fbf127a96',
            'buvid' => 'test_android_buvid_1',
            'ct' => 'missevan',
            'is_root' => 0,
            'mac' => 'f2:b6:43:a2:50:40',
            'oaid' => 'ef7e2efb-fcf9-90b3-d42f-feffe95706f2',
            'adid' => 'test_android_adid',
            'user_agent' => 'MissEvanApp/5.7.0 (Android;11;vivo V2134A PD2134)',
            'track_from_url' => 'missevan://drama/34632?ad_source_from=bili&app_key=appKey_1380&track_id=iPwdshurWNG3y6MXHD7WIhpkcy-Eps',
        ]));

        // 测试没有安装记录时
        $resp = Request::api('/site/ad-track', [], Request::POST, [
            'Cookie' => 'buvid=test_android_buvid_1;equip_id=6247cf87-3340-0aba-b2df-fe09c0960dbd',
        ]);
        $this->assertFalse($resp['ad_tracked']);

        // 生成安装记录
        $model = new InstallBuvid();
        $model->buvid = 'test_android_buvid_1';
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }

        // 测试有安装记录，没有广告点击记录时
        $resp = Request::api('/site/ad-track', [], Request::POST, [
            'Cookie' => 'buvid=test_android_buvid_1;equip_id=6247cf87-3340-0aba-b2df-fe09c0960dbd',
        ]);
        $this->assertFalse($resp['ad_tracked']);

        // 生成促活广告点击记录
        $more = [
            'bili_buvid' => 'test_android_buvid_1',
            'android_id' => 'd517f85fbf127a96',
            'imei' => '',
            'stage' => 0,
            'vendor' => AdTrack::VENDOR_BILIBILI,
            'ad_event_type' => AdTrack::AD_EVENT_TYPE_CALLUP,
        ];
        $ad_track = Data::createAdTrack([
            'more' => Json::encode($more),
            'bili_buvid' => 'test_android_buvid_1',
        ], AdTrackBilibili::class);

        // 测试广告促活回传正常
        $resp = Request::api('/site/ad-track', [], Request::POST, [
            'Cookie' => 'buvid=test_android_buvid_1;equip_id=6247cf87-3340-0aba-b2df-fe09c0960dbd',
        ]);
        $this->assertTrue($resp['ad_tracked']);
        $ad_track = AdTrack::findOne(['id' => $ad_track->id]);
        $this->assertEquals($ad_track->converted, AdTrack::CONVERTED);

        // 测试广告促活上报没有 track_id
        Yii::$app->request->setRawBody(Json::encode([
            'android_id' => 'd517f85fbf127a96',
            'buvid' => 'test_android_buvid_1',
            'ct' => 'missevan',
            'is_root' => 0,
            'mac' => 'f2:b6:43:a2:50:40',
            'oaid' => 'ef7e2efb-fcf9-90b3-d42f-feffe95706f2',
            'adid' => 'test_android_adid',
            'user_agent' => 'MissEvanApp/5.7.0 (Android;11;vivo V2134A PD2134)',
            'track_from_url' => 'missevan://drama/34632?ad_source_from=bili&app_key=appKey_1380',
        ]));
        $resp = Request::api('/site/ad-track', [], Request::POST);
        $this->assertFalse($resp['ad_tracked']);
    }

    public function testAppCallupAttribution()
    {
        $buvid = 'test_android_buvid';
        // 删除测试数据
        InstallBuvid::deleteAll('buvid = :buvid', [':buvid' => $buvid]);
        AdTrack::deleteAll('bili_buvid = :bili_buvid AND converted = :converted',
            [':bili_buvid' => $buvid, ':converted' => AdTrack::NOT_CONVERTED]);

        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/5.7.0 (Android;11;vivo V2134A PD2134)',
            'Cookie' => 'equip_id=0aff70c6-3e29-4475-81f8-f38bc38ded73;buvid=' . $buvid,
        ]);
        $ref_class = new ReflectionClass(SiteController::class);
        $instance = $ref_class->newInstance('site', 'test');
        $rawbody_data = [
            'android_id' => 'd517f85fbf127a96',
            'buvid' => $buvid,
            'ct' => 'missevan',
            'is_root' => 0,
            'mac' => 'f2:b6:43:a2:50:40',
            'oaid' => 'ef7e2efb-fcf9-90b3-d42f-feffe95706f2',
            'adid' => 'test_android_adid',
            'user_agent' => 'MissEvanApp/5.7.0 (Android;11;vivo V2134A PD2134)',
            'track_from_url' => 'missevan://drama/34632?ad_source_from=bili&app_key=appKey_1380&track_id=...',
        ];

        // 测试没有安装记录也没有广告点击记录时
        $res = self::invokePrivateMethod($ref_class, 'appCallupAttribution', $rawbody_data, $instance);
        $this->assertNotEmpty($res);
        $this->assertFalse($res['ad_tracked']);

        // 生成安装记录
        $model = new InstallBuvid();
        $model->buvid = $buvid;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }

        // 测试有安装记录没有广告点击记录时
        $res = self::invokePrivateMethod($ref_class, 'appCallupAttribution', $rawbody_data, $instance);
        $this->assertNotEmpty($res);
        $this->assertFalse($res['ad_tracked']);

        // 生成促活广告点击记录
        $more = [
            'bili_buvid' => $buvid,
            'android_id' => 'd517f85fbf127a96',
            'imei' => '',
            'stage' => 0,
            'vendor' => AdTrack::VENDOR_BILIBILI,
            'ad_event_type' => AdTrack::AD_EVENT_TYPE_CALLUP,
        ];
        Data::createAdTrack(['more' => Json::encode($more)], AdTrackBilibili::class);

        // 测试安装记录和广告点击记录都有时
        $res = self::invokePrivateMethod($ref_class, 'appCallupAttribution', $rawbody_data, $instance);
        $this->assertNotEmpty($res);
        $this->assertTrue($res['ad_tracked']);
    }

    public function testAppleAdAttribution()
    {
        $buvid = 'test_ios_buvid';
        Yii::$app->equip->init([
            'user_agent' => 'MissEvanApp/4.6.8 (iOS;12.0.1;iPhone8,2)',
            'Cookie' => 'equip_id=0aff70c6-3e29-4475-81f8-f38bc38ded71;buvid=' . $buvid,
        ]);
        // 删除测试数据
        Yii::$app->memcache->delete(KEY_APPLE_SEARCH_ADS_CLIENT_SECRET);
        InstallBuvid::deleteAll('buvid = :buvid', [':buvid' => $buvid]);
        AdTrack::deleteAll('buvid = :buvid AND converted = :converted',
            [':buvid' => $buvid, ':converted' => AdTrack::CONVERTED]);

        $ref_class = new ReflectionClass(SiteController::class);
        $instance = $ref_class->newInstance('site', 'test');
        $rawbody_data = [
            'idfa' => 'f1828e51-4701-41b6-8ad5-29a7a4478fce',
            'buvid' => $buvid,
            'user_agent' => 'MissEvanApp/4.3.0 (iOS;12.2;iPhone9,1)',
            'attribution_data' => [
                'Version3.1' => [
                    'iad-adgroup-id' => 1234567890,
                    'iad-adgroup-name' => 'AdGroupName',
                    'iad-attribution' => 'true',
                    'iad-campaign-id' => 1234567890,
                    'iad-campaign-name' => 'CampaignName',
                    'iad-click-date' => '2021-08-05T11:25:44Z',
                    'iad-conversion-date' => '2021-08-05T11:25:44Z',
                    'iad-conversion-type' => 'Download',
                    'iad-country-or-region' => 'US',
                    'iad-creativeset-id' => 1234567890,
                    'iad-creativeset-name' => 'CreativeSetName',
                    'iad-keyword' => 'Keyword',
                    'iad-keyword-id' => 12323222,
                    'iad-keyword-matchtype' => 'Broad',
                    'iad-lineitem-id' => 1234567890,
                    'iad-lineitem-name' => 'LineName',
                    'iad-org-id' => 1234567890,
                    'iad-org-name' => 'OrgName',
                    'iad-purchase-date' => '2021-08-05T11:25:44Z',
                ]
            ]
        ];
        Yii::$app->memcache->set(KEY_APPLE_SEARCH_ADS_ACCESS_TOKEN, 'eyJhbGc8SHw983jha38iq13a01JH3e4H', FIVE_MINUTE);
        Yii::$app->memcache->set(KEY_APPLE_SEARCH_ADS_ACL_DATA,
            '{"data":[{"orgId":2825450,"orgName":"猫耳FM"}]}', FIVE_MINUTE);
        // 测试没有归因过的设备
        Yii::$app->memcache->set(KEY_APPLE_SEARCH_ADS_CLIENT_SECRET, 'test_apple_search_ads_client_secret');
        $res = self::invokePrivateMethod($ref_class, 'appleAdAttribution', $rawbody_data, $instance);
        $this->assertNotEmpty($res);
        $this->assertTrue($res['new_equip']);
        $this->assertTrue($res['ad_tracked']);

        // 测试已经归因过的设备
        $res = self::invokePrivateMethod($ref_class, 'appleAdAttribution', $rawbody_data, $instance);
        $this->assertNotEmpty($res);
        $this->assertFalse($res['new_equip']);
        $this->assertTrue($res['ad_tracked']);

        // 生成安装记录
        $model = new InstallBuvid();
        $model->buvid = $buvid;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }

        // 修改安装记录创建时间（30 天以前）
        $model->create_time = $_SERVER['REQUEST_TIME'] - THIRTY_DAYS - 1;
        if (!$model->save()) {
            throw new Exception(MUtils2::getFirstError($model));
        }

        // 测试已经安装超过 30 天，不进行归因
        $res = self::invokePrivateMethod($ref_class, 'appleAdAttribution', $rawbody_data, $instance);
        $this->assertNotEmpty($res);
        $this->assertFalse($res['new_equip']);
        $this->assertFalse($res['ad_tracked']);
    }

    public function testGetIpDetail()
    {
        // mock 数据
        Rpc::registerRpcApiResponseFunc(ServiceRpc::API_UTIL_GEOIP, function ($data) {
            if ($data['ip'] === '**************') {
                return new RpcResult([
                    'city_name' => '北京',
                    'country_code' => 'CN',
                    'country_name' => '中国',
                    'isp' => '电信',
                    'region_name' => '北京',
                ]);
            } elseif ($data['ip'] === '*************') {
                return new RpcResult([
                    'city_name' => '',
                    'country_code' => 'HK',
                    'country_name' => '中国',
                    'isp' => 'hkbn.net',
                    'region_name' => '中国香港',
                ]);
            }
            return new RpcResult([
                'city_name' => '',
                'country_code' => '',
                'country_name' => '',
                'isp' => '',
                'region_name' => '',
            ]);
        });
        $data = Request::api('/site/get-ip-detail', [], Request::GET, ['X-Forwarded-For' => '**************']);
        $this->assertIsArray($data);
        $this->assertEquals('**************', $data['ip']);
        $this->assertEquals('北京', $data['ip_location']);
        $this->assertEquals('电信', $data['isp']);

        $data = Request::api('/site/get-ip-detail', [], Request::GET, ['X-Forwarded-For' => '*************']);
        $this->assertIsArray($data);
        $this->assertEquals('*************', $data['ip']);
        $this->assertEquals('中国香港', $data['ip_location']);
        $this->assertEquals('hkbn.net', $data['isp']);
    }

    public function testShowNewUserTab()
    {
        $test_equip_id = '5ae93485-test-b665-9ada-a26e4dda4233';
        Yii::$app->equip->init([
            'Cookie' => 'equip_id=' . $test_equip_id,
            'user_agent' => 'MissEvanApp/6.0.3 (iOS;12.0;iPhone9,1)'
        ]);
        $key = self::$redis->generateKey(KEY_NEW_DEVICE_EQUIP_ID, $test_equip_id);
        self::$redis->del($key);
        $ref_class = new ReflectionClass(SiteController::class);
        $instance = $ref_class->newInstance('site', 'test');

        // 测试游客状态下客户端启动时请求且当前设备为老设备，首页不显示新人 Tab 页
        $this->assertEquals([false, false], self::invokePrivateMethod($ref_class, 'showNewUserTab', SiteController::BOOT, $instance));

        // 测试游客状态下新设备 30s 内启动，首页显示新人 Tab, 默认定位到新人 Tab 页
        self::$redis->setex($key, ONE_HOUR, $_SERVER['REQUEST_TIME'] - HALF_MINUTE);
        // 重置设备激活时间
        self::setPrivateProperty(Yii::$app->equip, 'activate_time', null);
        $this->assertEquals([true, true],
            self::invokePrivateMethod($ref_class, 'showNewUserTab', SiteController::BOOT, $instance));

        // 测试游客状态下新设备非 30s 内启动，首页显示新人 Tab, 不默认定位到新人 Tab 页
        self::$redis->setex($key, ONE_HOUR, $_SERVER['REQUEST_TIME'] - ONE_MINUTE);
        // 重置设备激活时间
        self::setPrivateProperty(Yii::$app->equip, 'activate_time', null);
        $this->assertEquals([true, false],
            self::invokePrivateMethod($ref_class, 'showNewUserTab', SiteController::BOOT, $instance));

        // 测试游客状态下客户端非启动时请求且当前设备为新设备，首页显示新人 Tab, 不默认定位到新人 Tab 页
        $this->assertEquals([true, false], self::invokePrivateMethod($ref_class, 'showNewUserTab', SiteController::NOT_BOOT, $instance));

        // 测试客户端非启动时请求 Tab 页，当前设备为新设备且用户为新用户，首页显示新人 Tab, 不默认定位到新人 Tab 页
        self::loginByUserId(self::TEST_USER_ID, ['ctime' => $_SERVER['REQUEST_TIME'] - ONE_WEEK]);
        $this->assertEquals([true, false], self::invokePrivateMethod($ref_class, 'showNewUserTab', SiteController::NOT_BOOT, $instance));

        // 测试客户端启动时请求 Tab 页，当前设备为新设备且用户为老用户，首页不显示新人 Tab
        self::loginByUserId(self::TEST_USER_ID, ['ctime' => $_SERVER['REQUEST_TIME'] - (ONE_WEEK + ONE_DAY)]);
        $this->assertEquals([false, false], self::invokePrivateMethod($ref_class, 'showNewUserTab', SiteController::BOOT, $instance));
        $this->assertFalse(self::$redis->get($key));

        // 测试客户端启动时请求 Tab 页，当前设备为老设备且用户为新用户，首页显示新人 Tab, 不默认定位到新人 Tab 页
        self::loginByUserId(self::TEST_USER_ID, ['ctime' => $_SERVER['REQUEST_TIME'] - 4 * ONE_DAY]);
        $this->assertEquals([true, false],
            self::invokePrivateMethod($ref_class, 'showNewUserTab', SiteController::BOOT, $instance));

        // 测试用户在新人 Tab 页白名单中，首页显示新人 Tab, 不默认定位到新人 Tab 页
        self::loginByUserId(self::NEWUSER_TAB_USER_ALLOWLIST_USER_ID, ['ctime' => $_SERVER['REQUEST_TIME'] - 2 * ONE_WEEK]);
        $this->assertEquals([true, false], self::invokePrivateMethod($ref_class, 'showNewUserTab', SiteController::BOOT, $instance));
    }

    public function testActionNewUser()
    {
        Yii::$app->set('db', Yii::$app->sqlitedb);

        Rpc::registerRpcApiResponseFunc(LiveRpc::API_LIVE_RECOMMEND_INFO, function () {
            return new RpcResult([
                'data' => [
                    (string)self::TEST_USER_ID => [
                        'room_id' => 12202121,
                        'title' => '直播间标题',
                        'cover_url' => Yii::$app->params['coverUrl'] . 'fmcovers/icon01.png',
                        'status' => Live::STATUS_CLOSE,
                        'catalog_id' => 233,
                        'catalog_name' => '配音',
                        'catalog_color' => '#FFFFFF',
                        'custom_tag' => [
                            'tag_id' => 1000001,
                            'tag_name' => '腹黑青叔',
                        ],
                        'user_id' => self::TEST_USER_ID,
                        'username' => '主播昵称',
                        'iconurl' => Yii::$app->params['defaultAvatarUrl'],
                    ],
                    (string)self::TEST_USER2_ID => [
                        'room_id' => 12202122,
                        'title' => '直播间标题 2',
                        'cover_url' => Yii::$app->params['coverUrl'] . 'fmcovers/icon02.png',
                        'status' => Live::STATUS_OPEN,
                        'catalog_id' => 233,
                        'catalog_name' => '配音',
                        'catalog_color' => '#FFFFFF',
                        'user_id' => self::TEST_USER2_ID,
                        'username' => '主播昵称 2',
                        'iconurl' => Yii::$app->params['defaultAvatarUrl'],
                    ],
                ]
            ]);
        });

        // 创建测试推荐模块
        self::$test_you_might_like_module_1 = Data::createYouMightLikeModule([
            'title' => '测试音频类型推荐位',
            'element_type' => MPersonaModuleElement::ELEMENT_TYPE_SOUND,
            'element_style' => MPersonaModuleElement::MODULE_STYLE_SLIDE,
        ]);
        // 创建直播类型推荐模块
        self::$test_you_might_like_module_2 = Data::createYouMightLikeModule([
            'title' => '测试直播类型推荐位',
            'element_type' => MPersonaModuleElement::ELEMENT_TYPE_LIVE,
            'element_style' => MPersonaModuleElement::MODULE_STYLE_DEFAULT,
        ]);

        // 创建测试音频
        self::$test_sound_1 = Data::createSound([
            'user_id' => self::TEST_USER3_ID,
            'type' => MSound::TYPE_NORMAL,
            'checked' => MSound::CHECKED_PASS,
        ]);
        self::$test_sound_2 = Data::createSound([
            'user_id' => self::TEST_USER3_ID,
            'type' => MSound::TYPE_NORMAL,
            'checked' => MSound::CHECKED_PASS,
        ]);

        // 创建主播数据
        self::$test_live_1 = Data::createLive([
            'id' => self::TEST_USER_ID,
            'user_id' => self::TEST_USER_ID,
            'title' => '测试 CV 主播 1',
        ]);
        self::$test_live_2 = Data::createLive([
            'id' => self::TEST_USER2_ID,
            'user_id' => self::TEST_USER2_ID,
            'title' => '测试 CV 主播 2',
            'status' => Live::STATUS_OPEN,
        ]);

        // 创建测试模块元素数据
        self::$test_m_persona_module_element_1 = Data::createMPersonaModuleElement([
            'module_id' => self::$test_you_might_like_module_1->id,
            'persona_id' => MPersonaModuleElement::PERSONA_ID_NEW_USER,
            'element_type' => MPersonaModuleElement::ELEMENT_TYPE_SOUND,
            'sort' => 1,
        ]);
        self::$test_m_persona_module_element_2 = Data::createMPersonaModuleElement([
            'module_id' => self::$test_you_might_like_module_2->id,
            'persona_id' => MPersonaModuleElement::PERSONA_ID_NEW_USER,
            'element_type' => MPersonaModuleElement::ELEMENT_TYPE_LIVE,
            'sort' => 2,
        ]);

        // 创建推荐模块元素
        self::$test_m_recommended_element_1 = Data::createMRecommendedElement([
            'module_id' => self::$test_you_might_like_module_1->id,
            'module_type' => MRecommendedElements::MODULE_TYPE_LIKE_DRAMA_ALBUM,
            'element_id' => self::$test_sound_1->id,
            'element_type' => MPersonaModuleElement::ELEMENT_TYPE_SOUND,
            'sort' => 1,
        ]);
        self::$test_m_recommended_element_2 = Data::createMRecommendedElement([
            'module_id' => self::$test_you_might_like_module_1->id,
            'module_type' => MRecommendedElements::MODULE_TYPE_LIKE_DRAMA_ALBUM,
            'element_id' => self::$test_sound_2->id,
            'element_type' => MPersonaModuleElement::ELEMENT_TYPE_SOUND,
            'sort' => 2,
        ]);
        self::$test_m_recommended_element_3 = Data::createMRecommendedElement([
            'module_id' => self::$test_you_might_like_module_2->id,
            'module_type' => MRecommendedElements::MODULE_TYPE_LIKE_DRAMA_ALBUM,
            'element_id' => self::$test_live_1->id,
            'element_type' => MPersonaModuleElement::ELEMENT_TYPE_LIVE,
            'sort' => 1,
        ]);
        self::$test_m_recommended_element_4 = Data::createMRecommendedElement([
            'module_id' => self::$test_you_might_like_module_2->id,
            'module_type' => MRecommendedElements::MODULE_TYPE_LIKE_DRAMA_ALBUM,
            'element_id' => self::$test_live_2->id,
            'element_type' => MPersonaModuleElement::ELEMENT_TYPE_LIVE,
            'sort' => 2,
        ]);
        self::$test_new_user_modules_key = MUtils2::generateCacheKey(KEY_GUESSYOURLIKES_MODULES,
            MPersonaModuleElement::PERSONA_ID_NEW_USER);
        self::$test_new_user_modules_key_old = MUtils2::generateCacheKey(KEY_GUESSYOURLIKES_MODULES_OLD,
            MPersonaModuleElement::PERSONA_ID_NEW_USER);
        // 删除新人 Tab 缓存
        self::$memcache->delete(self::$test_new_user_modules_key);
        self::$memcache->delete(self::$test_new_user_modules_key_old);

        // 测试未登录（无缓存）时
        $api = '/site/new-user';
        $data = Request::api($api, [], Request::GET, ['User-Agent' => 'MissEvanApp/6.0.9 (iOS;11.4.1;iPhone8,4)']);
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);
        $this->assertArrayHasKey('blocks', $data);
        $this->assertCount(3, $data['blocks']);
        // 验证游客广告条
        $this->assertIsArray($data['blocks'][0]);
        $this->assertNotEmpty($data['blocks'][0]);
        $this->assertArrayHasKeys(['block_type', 'intro'], $data['blocks'][0]);
        $this->assertEquals(SiteController::NEW_USER_BLOCK_TYPE_GUEST_BANNER, $data['blocks'][0]['block_type']);

        // 验证音频模块数据
        $this->assertIsArray($data['blocks'][1]);
        $this->assertNotEmpty($data['blocks'][1]);
        $this->assertArrayHasKeys(['module_id', 'title', 'type', 'style', 'elements'], $data['blocks'][1]);
        $this->assertEquals(self::$test_you_might_like_module_1->id, $data['blocks'][1]['module_id']);
        $this->assertEquals(SiteController::NEW_USER_BLOCK_TYPE_CUSTOM_MODULE, $data['blocks'][1]['block_type']);
        // 验证模块内容
        $this->assertIsArray($data['blocks'][1]['elements']);
        $this->assertNotEmpty($data['blocks'][1]['elements']);
        $this->assertCount(2, $data['blocks'][1]['elements']);
        $this->assertArrayHasKeys(['id', 'front_cover', 'soundstr', 'intro', 'view_count', 'comment_count', 'all_comments', 'user_id', 'username'],
            $data['blocks'][1]['elements'][0]);
        $this->assertEquals(self::$test_sound_1->id, $data['blocks'][1]['elements'][0]['id']);
        $this->assertEquals(self::$test_sound_2->id, $data['blocks'][1]['elements'][1]['id']);

        // 验证直播模块数据
        $this->assertIsArray($data['blocks'][2]);
        $this->assertNotEmpty($data['blocks'][2]);
        $this->assertArrayHasKeys(['module_id', 'title', 'type', 'style', 'elements'], $data['blocks'][2]);
        $this->assertEquals(self::$test_you_might_like_module_2->id, $data['blocks'][2]['module_id']);
        $this->assertEquals(SiteController::NEW_USER_BLOCK_TYPE_CUSTOM_MODULE, $data['blocks'][2]['block_type']);
        // 验证模块内容
        $this->assertIsArray($data['blocks'][2]['elements']);
        $this->assertNotEmpty($data['blocks'][2]['elements']);
        $this->assertCount(2, $data['blocks'][2]['elements']);
        $this->assertArrayHasKeys(['room_id', 'title', 'cover_url', 'catalog_id', 'catalog_name', 'catalog_color', 'status', 'user_id', 'username', 'iconurl', 'status'],
            $data['blocks'][2]['elements'][0]);
        $this->assertEquals(self::TEST_USER2_ID, $data['blocks'][2]['elements'][0]['user_id']);
        $this->assertArrayHasKeys(['room_id', 'title', 'cover_url', 'catalog_id', 'catalog_name', 'catalog_color', 'status', 'user_id', 'username', 'iconurl', 'status', 'custom_tag'],
            $data['blocks'][2]['elements'][1]);
        $this->assertEquals(self::TEST_USER_ID, $data['blocks'][2]['elements'][1]['user_id']);
        $this->assertEquals(['tag_id' => 1000001, 'tag_name' => '腹黑青叔'], $data['blocks'][2]['elements'][1]['custom_tag']);
        // 验证缓存数据生成成功
        $this->assertNotFalse(self::$memcache->get(self::$test_new_user_modules_key));

        // 测试未登录（有缓存）时
        $data1 = Request::api($api, [], Request::GET, ['User-Agent' => 'MissEvanApp/6.0.9 (iOS;11.4.1;iPhone8,4)']);
        $this->assertIsArray($data1);
        $this->assertNotEmpty($data1);
        $this->assertEquals($data, $data1);

        // 删除新人 Tab 缓存
        self::$memcache->delete(self::$test_new_user_modules_key);

        // 测试已登录（无缓存）且版本号大于等于 6.0.4 时
        self::loginByUserId(self::TEST_USER_ID);
        $data2 = Request::api($api, [], Request::GET,
            ['User-Agent' => 'MissEvanApp/6.0.4 (iOS;12.2;iPhone9,1)']);
        $this->assertIsArray($data2);
        $this->assertNotEmpty($data2);
        $this->assertArrayHasKey('blocks', $data2);
        $this->assertCount(3, $data2['blocks']);
        // 验证登录用户广告条
        $this->assertIsArray($data2['blocks'][0]);
        $this->assertNotEmpty($data2['blocks'][0]);
        $this->assertArrayHasKeys(['block_type', 'title', 'intro', 'btn_title', 'btn_url'], $data2['blocks'][0]);
        $this->assertEquals(SiteController::NEW_USER_BLOCK_TYPE_USER_BANNER, $data2['blocks'][0]['block_type']);

        // 验证音频模块数据
        $this->assertIsArray($data2['blocks'][1]);
        $this->assertNotEmpty($data2['blocks'][1]);
        $this->assertArrayHasKeys(['module_id', 'title', 'type', 'style', 'elements'], $data2['blocks'][1]);
        $this->assertEquals(self::$test_you_might_like_module_1->id, $data2['blocks'][1]['module_id']);
        $this->assertEquals(SiteController::NEW_USER_BLOCK_TYPE_CUSTOM_MODULE, $data2['blocks'][1]['block_type']);
        // 验证模块内容
        $this->assertIsArray($data2['blocks'][1]['elements']);
        $this->assertNotEmpty($data2['blocks'][1]['elements']);
        $this->assertCount(2, $data2['blocks'][1]['elements']);
        $this->assertArrayHasKeys(['id', 'front_cover', 'soundstr', 'intro', 'view_count', 'comment_count', 'all_comments', 'user_id', 'username'],
            $data2['blocks'][1]['elements'][0]);
        $this->assertEquals(self::$test_sound_1->id, $data2['blocks'][1]['elements'][0]['id']);
        $this->assertEquals(self::$test_sound_2->id, $data2['blocks'][1]['elements'][1]['id']);

        // 验证直播模块数据
        $this->assertIsArray($data2['blocks'][2]);
        $this->assertNotEmpty($data2['blocks'][2]);
        $this->assertArrayHasKeys(['module_id', 'title', 'type', 'style', 'elements'], $data2['blocks'][2]);
        $this->assertEquals(self::$test_you_might_like_module_2->id, $data2['blocks'][2]['module_id']);
        $this->assertEquals(SiteController::NEW_USER_BLOCK_TYPE_CUSTOM_MODULE, $data2['blocks'][2]['block_type']);
        // 验证模块内容
        $this->assertIsArray($data2['blocks'][2]['elements']);
        $this->assertNotEmpty($data2['blocks'][2]['elements']);
        $this->assertCount(2, $data2['blocks'][2]['elements']);
        $this->assertArrayHasKeys(['room_id', 'title', 'cover_url', 'catalog_id', 'catalog_name', 'catalog_color', 'status', 'user_id', 'username', 'iconurl', 'status'],
            $data2['blocks'][2]['elements'][0]);
        $this->assertEquals(self::TEST_USER2_ID, $data2['blocks'][2]['elements'][0]['user_id']);
        $this->assertArrayHasKeys(['room_id', 'title', 'cover_url', 'catalog_id', 'catalog_name', 'catalog_color', 'status', 'user_id', 'username', 'iconurl', 'status', 'custom_tag'],
            $data2['blocks'][2]['elements'][1]);
        $this->assertEquals(self::TEST_USER_ID, $data2['blocks'][2]['elements'][1]['user_id']);
        $this->assertEquals(['tag_id' => 1000001, 'tag_name' => '腹黑青叔'], $data2['blocks'][2]['elements'][1]['custom_tag']);
        // 验证缓存数据生成成功
        $this->assertNotFalse(self::$memcache->get(self::$test_new_user_modules_key_old));

        // 测试已登录（有缓存）且版本号小于 6.0.4 时
        $data3 = Request::api($api, [], Request::GET,
            ['User-Agent' => 'MissEvanApp/6.0.3 (iOS;12.2;iPhone9,1)']);
        $this->assertIsArray($data3);
        $this->assertNotEmpty($data3);
        $this->assertArrayHasKey('blocks', $data3);
        $this->assertCount(2, $data3['blocks']);
        $this->assertNotEquals(SiteController::NEW_USER_BLOCK_TYPE_USER_BANNER, $data3['blocks'][0]['block_type']);

        // 测试已登录（有缓存）版本号大于等于 6.0.4 且是谷歌渠道时
        $data4 = Request::api($api, [], Request::GET,
            ['User-Agent' => 'MissEvanApp/6.0.4 (iOS;12.2;iPhone9,1)', 'channel' => Equipment::CHANNEL_GOOGLE]);
        $this->assertIsArray($data4);
        $this->assertNotEmpty($data4);
        $this->assertEquals($data3, $data4);

        // 测试未配置广告条时不下发广告
        Yii::$app->params['new_user']['banner'] = [];
        $data5 = Request::api($api);
        $this->assertIsArray($data5);
        $this->assertNotEmpty($data5);
        $this->assertArrayHasKey('blocks', $data5);
        $this->assertCount(2, $data5['blocks']);
        $this->assertIsArray($data5['blocks'][0]);
        $this->assertNotEmpty($data5['blocks'][0]);
        $this->assertNotEquals(SiteController::NEW_USER_BLOCK_TYPE_USER_BANNER, $data5['blocks'][0]['block_type']);
        $this->assertEquals(SiteController::NEW_USER_BLOCK_TYPE_CUSTOM_MODULE, $data5['blocks'][0]['block_type']);
    }

    public function testGetNewUserLuckybagsModule()
    {
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.1.8 (Android;7.0;Meizu M6 M6)']);
        // 测试 rpc 请求出错的情况
        Rpc::registerRpcApiResponseFunc(LiveRpc::API_LUCKYBAG_DRAMA_LIST, function () {
            throw new RpcNetworkException('网络错误');
        });
        $ref_class = new ReflectionClass(SiteController::class);
        $module = self::invokePrivateMethod($ref_class, 'getNewUserLuckybagsModule', null, null);
        $this->assertNull($module);

        // 测试无福袋的情况
        Rpc::registerRpcApiResponseFunc(LiveRpc::API_LUCKYBAG_DRAMA_LIST, function () {
            return new RpcResult([
                'has_more' => false,
                'data' => [],
            ]);
        });
        $module = self::invokePrivateMethod($ref_class, 'getNewUserLuckybagsModule');
        $this->assertNull($module);

        // 测试有福袋，但是没有"更多"的情况
        $luckybag_data = [[
            'ipr_id' => 11,
            'ipr_name' => '魔道祖师',
            'num' => 5,
            'cover_url' => 'https://static.test.com/test.png',
            'rooms' => [[
                'room_id' => 100003,
                'creator_id' => 13,
                'creator_iconurl' => 'https://static-test.maoercdn.com/icon03.png',
            ]],
            'trace' => "{\"drama_id\":0,\"ipr_id\":11,\"num\":5,\"enter_room_id\":100003}",
        ]];
        Rpc::registerRpcApiResponseFunc(LiveRpc::API_LUCKYBAG_DRAMA_LIST, function () use ($luckybag_data) {
            return new RpcResult([
                'has_more' => false,
                'data' => $luckybag_data,
            ]);
        });
        $module = self::invokePrivateMethod($ref_class, 'getNewUserLuckybagsModule');
        $this->assertEquals([
            'block_type' => 4,
            'title' => '好剧免费送',
            'data' => $luckybag_data,
        ], $module);

        // 测试有福袋，而且有"更多"的情况
        Rpc::registerRpcApiResponseFunc(LiveRpc::API_LUCKYBAG_DRAMA_LIST, function () use ($luckybag_data) {
            return new RpcResult([
                'has_more' => true,
                'data' => $luckybag_data,
            ]);
        });
        $module = self::invokePrivateMethod($ref_class, 'getNewUserLuckybagsModule');
        $this->assertEquals([
            'block_type' => 4,
            'title' => '好剧免费送',
            'more' => ['url' => 'https://www.test.com/test'],
            'data' => $luckybag_data,
        ], $module);

        // 测试 Android 6.1.7 客户端版本不下发福袋模块
        Yii::$app->equip->init(['user_agent' => 'MissEvanApp/6.1.7 (Android;7.0;Meizu M6 M6)']);
        $module = self::invokePrivateMethod($ref_class, 'getNewUserLuckybagsModule');
        $this->assertNull($module);
    }

    public function testGetAppCallupVendor()
    {
        $ref_class = new ReflectionClass(SiteController::class);
        $instance = $ref_class->newInstance('site', 'test');
        $this->assertEquals(AdTrack::VENDOR_BILIBILI,
            self::invokePrivateMethod($ref_class, 'getAppCallupVendor', 'bili', $instance));

        $this->assertEquals(AdTrack::VENDOR_DOUYIN,
            self::invokePrivateMethod($ref_class, 'getAppCallupVendor', 'douyin', $instance));

        $this->assertEquals(AdTrack::VENDOR_TENGXUN,
            self::invokePrivateMethod($ref_class, 'getAppCallupVendor', 'tengxun', $instance));

        $this->assertThrowsWithMessage(HttpException::class, '非法请求', function () use ($ref_class, $instance) {
            self::invokePrivateMethod($ref_class, 'getAppCallupVendor', '', $instance);
        }, 403);
    }

    public function testActionLaunchReport()
    {
        $location_info = [
            'city' => 'Beijing',
            'country_code' => 'CN',
            'region_code' => 'BJ',
        ];
        Rpc::registerRpcApiResponseFunc(ServiceRpc::API_UTIL_GEOIP, function ($data) use ($location_info) {
            return new RpcResult($location_info);
        });

        // 测试安卓上报
        $test_android_buvid = 'test_android_buvid';
        LaunchReportLog::deleteAll('buvid = :buvid', [':buvid' => $test_android_buvid]);

        $api = '/site/launch-report';
        $test_android_equip_id = '6247cf87-3340-0aba-b2df-fe09c0960dbd';
        $headers = [
            'Cookie' => "buvid={$test_android_buvid};equip_id={$test_android_equip_id}",
            'User-Agent' => 'MissEvanApp/6.1.7 (Android;7.1.2;vivo X9i Build/N2G47H)',
            'X-Forwarded-For' => '**************',
        ];

        // 测试 data 参数错误
        $this->assertThrowsWithMessage(HttpException::class, '参数错误',
            function () use ($api, $headers) {
                Request::api($api, [], Request::POST, $headers);
            }, 400
        );

        // 测试 is_root 参数错误
        $data = [
            'is_root' => 3,
            'ct' => 'missevan',
            'mac' => 'f2:b6:43:a2:50:40',
            'imei' => '865224033616913',
            'android_id' => 'd517f85fbf127a96',
            'adid' => '',
            'buvid' => $test_android_buvid,
            'oaid' => 'ef7e2efb-fcf9-90b3-d42f-feffe95706f2',
            'drm_id' => '8f16443fb54c6c9e98a45e6daf685bd3',
            'user_agent' => 'Mozilla/5.0 (Linux; Android 7.1.2; vivo X9i Build/N2G47H; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/55.0.2883.91 Mobile Safari/537.36',
            'screen_dpr' => '1.756',
            'screen_resolution' => '1080x1920',
            'screen_native_resolution' => '1080x2340',
        ];
        $params = ['data' => base64_encode(Json::encode($data))];
        $this->assertThrowsWithMessage(HttpException::class, '参数错误',
            function () use ($api, $params, $headers) {
                Request::api($api, $params, Request::POST, $headers);
            }, 400
        );

        // 测试正常请求
        $data['is_root'] = LaunchReportLog::IS_NOT_ROOT;
        $params = ['data' => base64_encode(Json::encode($data))];
        $result = Request::api($api, $params, Request::POST, $headers);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertArrayHasKey('msg', $result);
        // 验证保存的数据
        $launch_report_log = LaunchReportLog::findOne(['buvid' => $test_android_buvid]);
        $this->assertNotNull($launch_report_log);
        $this->assertEquals($_SERVER['REQUEST_TIME'], $launch_report_log->create_time);
        $this->assertEquals($_SERVER['REQUEST_TIME'], $launch_report_log->modified_time);
        $this->assertEquals(LaunchReportLog::IS_NOT_ROOT, $launch_report_log->is_root);
        $this->assertEquals(Equipment::Android, $launch_report_log->device_type);
        $this->assertEquals($test_android_equip_id, $launch_report_log->equip_id);
        $this->assertEquals($data['user_agent'], $launch_report_log->user_agent);
        $this->assertEquals(Yii::$app->request->userIP, $launch_report_log->ip);
        $this->assertEquals($test_android_buvid, $launch_report_log->buvid);
        $this->assertEquals('6.1.7', $launch_report_log->version);
        $this->assertEquals($data['ct'], $launch_report_log->ct);
        $this->assertEquals($data['adid'], $launch_report_log->adid);
        $this->assertEquals(strtoupper($data['mac']), $launch_report_log->mac);
        $this->assertEquals($data['imei'], $launch_report_log->imei);
        $this->assertEquals($data['android_id'], $launch_report_log->android_id);
        $this->assertEquals($data['oaid'], $launch_report_log->oaid);
        $this->assertNotNull($launch_report_log->more);
        $this->assertIsArray($launch_report_log->more);
        $this->assertNotEmpty($launch_report_log->more);
        $this->assertArrayHasKeys(['mac_md5', 'imei_md5', 'android_id_md5', 'oaid_md5', 'drm_id', 'location_info', 'screen_resolution', 'screen_dpr', 'device_info', 'screen_resolution'],
            $launch_report_log->more);
        $this->assertEquals(md5(strtoupper($data['mac'])), $launch_report_log->more['mac_md5']);
        $this->assertEquals(md5($data['imei']), $launch_report_log->more['imei_md5']);
        $this->assertEquals(md5($data['android_id']), $launch_report_log->more['android_id_md5']);
        $this->assertEquals(md5($data['oaid']), $launch_report_log->more['oaid_md5']);
        $this->assertEquals($data['drm_id'], $launch_report_log->more['drm_id']);
        $this->assertEquals($location_info, $launch_report_log->more['location_info']);
        $this->assertEquals($data['screen_resolution'], $launch_report_log->more['screen_resolution']);
        $this->assertEquals('1080x2340', $launch_report_log->more['screen_native_resolution']);
        // 测试设备像素比四舍五入保留两位小数
        $this->assertEquals('1.76', $launch_report_log->more['screen_dpr']);
        $device_info = $launch_report_log->more['device_info'];
        $this->assertArrayHasKeys(['bot_info', 'client', 'os', 'brand_name', 'device', 'device_name', 'model'], $device_info);
        $this->assertEquals('Vivo', $device_info['brand_name']);
        $this->assertEquals('X9i', $device_info['model']);

        // 测试 iOS 上报
        $test_ios_buvid = 'test_ios_buvid';
        LaunchReportLog::deleteAll('buvid = :buvid', [':buvid' => $test_ios_buvid]);

        $rpc_result = [
            'caid' => [
                '20230330' => 'a9baa7af0a832a751b13fe6b52747f23',
                '20220111' => '7d67d07677e46c347e848dd8b38b1d58',
                '20211207' => '6a1f799a7677d39178ef4f7824a58363',
                '20201230' => 'c1cec971df9af1dae3cea41000cf2452',
            ],
            'remaining_days' => 341,
        ];
        Rpc::registerRpcApiResponseFunc(ServiceRpc::API_DEVICE_CAID, function () use ($rpc_result) {
            return new RpcResult($rpc_result);
        });

        $data = [
            'idfa' => '2cc60d08-d8b2-4052-b781-2bb73f99fa31',
            'idfv' => 'eb20c9be-ab73-4df3-999e-856a6ce57719',
            'buvid' => $test_ios_buvid,
            'user_agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_7_8 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
            'caid' => '{"carrierInfo":"中国联通","machine":"iPhone10,3","sysFileTime":"1613802564.660653","countryCode":"CN","deviceName":"8CA05995D3EF703B596A39D76CD489F6","systemVersion":"14.0","language":"zh-Hans-CN","memory":"2964537344","disk":"63887757312","bootTimeInSec":"1628493190","timeZone":"28800","model":"D22AP"}',
            'screen_dpr' => '1.756',
            'screen_resolution' => '1080x1920',
        ];
        $params = ['data' => base64_encode(Json::encode($data))];
        $test_ios_equip_id = '5ae93485-test-b665-9ada-a26e4dda4233';
        $headers = [
            'Cookie' => "buvid={$test_ios_buvid};equip_id={$test_ios_equip_id}",
            'User-Agent' => 'MissEvanApp/6.1.7 (iOS;16.7;iPhone9,1)',
            'X-Forwarded-For' => '**************',
        ];
        $result = Request::api($api, $params, Request::POST, $headers);
        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertArrayHasKey('msg', $result);
        // 验证保存的数据
        $launch_report_log = LaunchReportLog::findOne(['buvid' => $test_ios_buvid]);
        $this->assertNotNull($launch_report_log);
        $this->assertEquals($_SERVER['REQUEST_TIME'], $launch_report_log->create_time);
        $this->assertEquals($_SERVER['REQUEST_TIME'], $launch_report_log->modified_time);
        $this->assertEquals(LaunchReportLog::IS_NOT_ROOT, $launch_report_log->is_root);
        $this->assertEquals(Equipment::iOS, $launch_report_log->device_type);
        $this->assertEquals($test_ios_equip_id, $launch_report_log->equip_id);
        $this->assertEquals($data['user_agent'], $launch_report_log->user_agent);
        $this->assertEquals(Yii::$app->request->userIP, $launch_report_log->ip);
        $this->assertEquals($test_ios_buvid, $launch_report_log->buvid);
        $this->assertEquals('6.1.7', $launch_report_log->version);
        $this->assertEquals($data['idfa'], $launch_report_log->idfa);
        $this->assertEquals($data['idfv'], $launch_report_log->idfv);
        $this->assertNotNull($launch_report_log->more);
        $this->assertIsArray($launch_report_log->more);
        $this->assertNotEmpty($launch_report_log->more);
        $this->assertArrayHasKeys(['caid', 'location_info', 'screen_resolution', 'screen_dpr', 'device_info'], $launch_report_log->more);
        $this->assertEquals($rpc_result['caid'], $launch_report_log->more['caid']);
        $this->assertEquals($location_info, $launch_report_log->more['location_info']);
        $this->assertEquals($data['screen_resolution'], $launch_report_log->more['screen_resolution']);
        // 测试设备像素比四舍五入保留两位小数
        $this->assertEquals('1.76', $launch_report_log->more['screen_dpr']);
        $device_info = $launch_report_log->more['device_info'];
        $this->assertArrayHasKeys(['bot_info', 'client', 'os', 'brand_name', 'device', 'device_name', 'model'], $device_info);
        $this->assertEquals('Apple', $device_info['brand_name']);
        $this->assertEquals('iPhone', $device_info['model']);

        // 测试不支持的系统类型
        $headers = [
            'Cookie' => 'equip_id=01e1b22d-b49d-4d27-fce5-90e085714cd5',
            'User-Agent' => 'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.75 Safari/537.36',
        ];
        $params = ['data' => base64_encode(Json::encode($data))];
        $this->assertThrowsWithMessage(HttpException::class, '不支持的系统类型',
            function () use ($api, $params, $headers) {
                Request::api($api, $params, Request::POST, $headers);
            }, 400
        );
    }

    public function testParseTrackFromUrl()
    {
        $ref_class = new ReflectionClass(SiteController::class);
        $instance = $ref_class->newInstance('site', 'test');
        // 测试域名开头为：missevan://?
        $data = self::invokePrivateMethod($ref_class, 'parseTrackFromUrl',
            'missevan://?ad_source_from=ctrip_task&track_id=test', $instance);
        $this->assertArrayHasKeys(['ad_source_from', 'track_id'], $data);

        // 测试域名开头为：missevan://xxxx?
        $data = self::invokePrivateMethod($ref_class, 'parseTrackFromUrl',
            'missevan://xxxxx?ad_source_from=ctrip_task&track_id=test', $instance);
        $this->assertArrayHasKeys(['ad_source_from', 'track_id'], $data);
    }

    public function testActionCustomCatalog()
    {
        Yii::$app->set('db', Yii::$app->sqlitedb);
        self::loginByUserId(self::TEST_USER_ID);

        $redis = Yii::$app->redis;
        $custom_key = $redis->generateKey(CUSTOM, self::TEST_USER_ID);
        $redis->del($custom_key);
        $data = $redis->get($custom_key);
        $this->assertFalse($data);
        $block_list_key = $redis->generateKey(KEY_CHANNEL_BLACK_LIST, Blacklist::TYPE_CHANNEL_AUDIT_SENSITIVE_HIDE);
        $value = 'missevan_huawei:6.2.7';
        $redis->sAdd($block_list_key, $value);
        $this->assertTrue($redis->sIsMember($block_list_key, $value));

        $api = '/site/custom-catalog';

        // 测试缓存不存在 GET 请求
        $res = Request::api($api, [], Request::GET);
        $this->assertNull($res);

        // 测试缓存不存在 POST 请求
        Yii::$app->equip->init([
            'channel' => Equipment::CHANNEL_HUAWEI,
            'User-Agent' => 'MissEvanApp/6.2.7 (Android;7.0;Meizu M6 M6)',
        ]);
        $res = Request::api($api, ['classfications' => '8,65,108'], Request::POST, [
            'channel' => Equipment::CHANNEL_HUAWEI,
            'User-Agent' => 'MissEvanApp/6.2.7 (Android;7.0;Meizu M6 M6)',
        ]);
        $expected = (string)Catalog::CATALOG_ID_SOUND_LOVER;
        // 断言没有声音恋人模块
        $this->assertFalse(in_array($expected, $res));
        // 断言缓存存在
        $data = $redis->get($custom_key);
        $this->assertNotFalse($data);

        // 测试缓存存在
        Yii::$app->equip->init([
            'channel' => Equipment::CHANNEL_HUAWEI,
            'User-Agent' => 'MissEvanApp/6.2.7 (Android;7.0;Meizu M6 M6)',
        ]);
        $res = Request::api($api, ['classfications' => '8,65,108'], Request::POST, [
            'channel' => Equipment::CHANNEL_HUAWEI,
            'User-Agent' => 'MissEvanApp/6.2.7 (Android;7.0;Meizu M6 M6)',
        ]);
        // 断言没有声音恋人模块
        $this->assertFalse(in_array($expected, $res));

        // 测试非华为渠道
        Yii::$app->equip->init([
            'channel' => Equipment::CHANNEL_VIVO,
            'User-Agent' => 'MissEvanApp/6.2.7 (Android;7.0;Meizu M6 M6)',
        ]);
        $res = Request::api($api, ['classfications' => '8,65,108'], Request::POST, [
            'channel' => Equipment::CHANNEL_VIVO,
            'User-Agent' => 'MissEvanApp/6.2.7 (Android;7.0;Meizu M6 M6)',
        ]);
        // 断言有声音恋人模块
        $this->assertTrue(in_array($expected, $res));

        // 测试非黑名单渠道
        Yii::$app->equip->init([
            'channel' => Equipment::CHANNEL_HUAWEI,
            'User-Agent' => 'MissEvanApp/6.2.6 (Android;7.0;Meizu M6 M6)',
        ]);
        $res = Request::api($api, ['classfications' => '8,65,108'], Request::POST, [
            'channel' => Equipment::CHANNEL_HUAWEI,
            'User-Agent' => 'MissEvanApp/6.2.6 (Android;7.0;Meizu M6 M6)',
        ]);
        // 断言有声音恋人模块
        $this->assertTrue(in_array($expected, $res));
    }
}
