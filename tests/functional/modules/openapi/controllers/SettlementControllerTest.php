<?php

namespace tests\modules\openapi\controllers;

use app\models\Balance;
use app\models\BalanceSettlementChangeLog;
use app\models\TopupMenu;
use app\models\Guild;
use app\models\MMessageAssign;
use app\models\PayAccount;
use app\models\RechargeOrder;
use app\models\TransactionLog;
use app\models\WithdrawalRecord;
use app\modules\openapi\controllers\SettlementController;
use Exception;
use missevan\util\MUtils as MUtils2;
use ReflectionClass;
use tests\components\UnitTestCase;
use tests\components\util\Data;
use tests\components\util\Tools;
use tests\components\web\Request;
use Yii;
use yii\helpers\Html;
use yii\web\HttpException;

class SettlementControllerTest extends UnitTestCase
{
    private static $origin_db;
    private static $origin_paydb;

    private static $test_guild_id = 0;

    const TEST_USER_ID_REQUEST_TOPUP_ORDER_REFUND = 677;
    const TEST_USER_ID_REQUEST_CONSUME_ORDER_REFUND = 678;
    const TEST_USER_ID_QUERY_ORDER_STATUS_OR_DEDUCT_CREATOR_BALANCE = 679;

    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        self::$origin_db = Yii::$app->db;
        self::$origin_paydb = Yii::$app->paydb;
        self::setSqliteDB();

        self::cleanTestData();
    }

    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
        self::resetDB();
        self::cleanTestData();
    }

    protected function _before()
    {
        parent::_before();
        self::setSqliteDB();
    }

    protected function _after()
    {
        parent::_after();
        self::resetDB();
    }

    private static function setSqliteDB()
    {
        Yii::$app->set('db', Yii::$app->sqlitedb);
        Yii::$app->set('paydb', Yii::$app->sqlite_paydb);
    }

    private static function resetDB()
    {
        Yii::$app->set('db', Yii::$app->db);
        Yii::$app->set('paydb', Yii::$app->paydb);
    }

    private static function cleanTestData()
    {
        BalanceSettlementChangeLog::deleteAll(['user_id' => [
            self::TEST_USER_ID_REQUEST_TOPUP_ORDER_REFUND,
            self::TEST_USER_ID_REQUEST_CONSUME_ORDER_REFUND,
            self::TEST_USER_ID_QUERY_ORDER_STATUS_OR_DEDUCT_CREATOR_BALANCE,
        ]]);
        if (self::$test_guild_id) {
            Guild::deleteAll(['id' => self::$test_guild_id]);
        }
    }

    private function prepareRequestTopupOrderRefund()
    {
        $balance = Balance::getByPk(self::TEST_USER_ID_REQUEST_TOPUP_ORDER_REFUND);
        $balance->android = 60;
        if (!$balance->save()) {
            throw new Exception(MUtils2::getFirstError($balance));
        }

        $order = new RechargeOrder([
            'uid' => $balance->id,
            'status' => RechargeOrder::STATUS_SUCCESS,
            'type' => RechargeOrder::TYPE_ALIPAY,
            'price' => 10,
            'num' => 100,
            'tid' => 'tid:' . $_SERVER['REQUEST_TIME'],
            'cid' => 24,
            'ccy' => TopupMenu::DIAMOND,
            'origin' => RechargeOrder::ORIGIN_APP,
            'confirm_time' => $_SERVER['REQUEST_TIME'],
        ]);
        if (!$order->save()) {
            throw new Exception(MUtils2::getFirstError($order));
        }

        return [$order, function () use ($balance, $order) {
            Balance::deleteAll(['id' => $balance->id]);
            RechargeOrder::deleteAll(['id' => $order->id]);
        }];
    }

    private function assertOpenApiResponse(array $resp)
    {
        $this->assertIsArray($resp);
        $this->assertCount(3, $resp);
        $this->assertArrayHasKeys(['code', 'message', 'data'], $resp);
    }

    public function testActionRequestTopupOrderRefund()
    {
        [$order, $cleanup] = $this->prepareRequestTopupOrderRefund();
        try {
            $result = Request::openapi('/openapi/settlement/request-topup-order-refund', $_POST = [
                'order_ids' => $order->id,
                'user_id' => $order->uid,
                'note' => '充值订单退款处理',
                'request_id' => $_SERVER['REQUEST_TIME'] . rand(1, 100),
            ], Request::POST);
            $this->assertOpenApiResponse($result);
            $this->assertEquals(0, $result['code']);
            $this->assertEquals('success', $result['message']);
            $this->assertIsArray($result['data']);
            $this->assertArrayHasKey('task_id', $result['data']);

            ['id' => $id, 'type' => $type] = BalanceSettlementChangeLog::parseTaskId($result['data']['task_id']);
            $log = BalanceSettlementChangeLog::findOne(['id' => $id, 'type' => $type, 'user_id' => $order->uid]);
            $this->assertNotNull($log);
            $this->assertEquals(BalanceSettlementChangeLog::STATUS_SUCCESS, $log->status);
            $this->assertEquals('充值订单退款处理', $log->note);
            $this->assertIsArray($log->detail);
            $this->assertEquals($_POST['request_id'], $log->detail['task']['request_id']);
            $this->assertEquals(60, $log->detail['balance']['old']['android']);
            $this->assertEquals(0, $log->detail['balance']['new']['android']);
            $this->assertCount(1, $log->detail['coin_changes']);
            $this->assertEquals($order->id, $log->detail['coin_changes'][0]['order_id']);
            $this->assertEquals(-100, $log->detail['coin_changes'][0]['coin']);
            $this->assertEquals(-60, $log->detail['coin_changes'][0]['coin_changed']);
        } finally {
            $cleanup();
        }
    }

    private function prepareRequestConsumeOrderRefund()
    {
        $balance = Balance::getByPk(self::TEST_USER_ID_REQUEST_CONSUME_ORDER_REFUND);
        $balance->ios = 10;
        $balance->android = 20;
        if (!$balance->save()) {
            throw new Exception(MUtils2::getFirstError($balance));
        }

        $order = new TransactionLog([
            'from_id' => $balance->id,
            'to_id' => 123,
            'gift_id' => 9888,
            'title' => '杀破狼',
            'ios_coin' => 90,
            'android_coin' => 80,
            'all_coin' => 170,
            'income' => 17,
            'rate' => 0.6,
            'type' => TransactionLog::TYPE_DRAMA,
            'status' => TransactionLog::STATUS_SUCCESS,
            'confirm_time' => $_SERVER['REQUEST_TIME'],
        ]);
        $order->tax = $order->calcTax(['ios' => 90, 'android' => 80]);
        if (!$order->save()) {
            throw new Exception(MUtils2::getFirstError($order));
        }

        return [$order, function () use ($balance, $order) {
            Balance::deleteAll(['id' => $balance->id]);
            TransactionLog::deleteAll(['id' => $order->id]);
        }];
    }

    public function testActionRequestConsumeOrderRefund()
    {
        [$order, $cleanup] = $this->prepareRequestConsumeOrderRefund();

        try {
            $result = Request::openapi('/openapi/settlement/request-consume-order-refund', $_POST = [
                'order_ids' => $order->id,
                'user_id' => $order->from_id,
                'note' => '消费订单退款处理',
                'request_id' => $_SERVER['REQUEST_TIME'] . rand(1, 100),
            ], Request::POST);
            $this->assertOpenApiResponse($result);
            $this->assertEquals(0, $result['code']);
            $this->assertEquals('success', $result['message']);
            $this->assertIsArray($result['data']);
            $this->assertArrayHasKey('task_id', $result['data']);

            ['id' => $id, 'type' => $type] = BalanceSettlementChangeLog::parseTaskId($result['data']['task_id']);
            $log = BalanceSettlementChangeLog::findOne(['id' => $id, 'type' => $type, 'user_id' => $order->from_id]);
            $this->assertNotNull($log);
            $this->assertEquals(BalanceSettlementChangeLog::STATUS_SUCCESS, $log->status);
            $this->assertEquals('消费订单退款处理', $log->note);
            $this->assertIsArray($log->detail);
            $this->assertEquals($_POST['request_id'], $log->detail['task']['request_id']);
            $this->assertEquals(10, $log->detail['balance']['old']['ios']);
            $this->assertEquals(20, $log->detail['balance']['old']['android']);
            $this->assertEquals(100, $log->detail['balance']['new']['ios']);
            $this->assertEquals(100, $log->detail['balance']['new']['android']);
            $this->assertCount(1, $log->detail['coin_changes']);
            $this->assertEquals($order->id, $log->detail['coin_changes'][0]['order_id']);
            $this->assertEquals(170, $log->detail['coin_changes'][0]['coin']);
            $this->assertEquals(170, $log->detail['coin_changes'][0]['coin_changed']);
        } finally {
            $cleanup();
        }
    }

    public function testActionQueryOrderRefundStatus()
    {
        $this->assertThrowsWithMessage(HttpException::class, '该任务不存在', function () {
            Request::openapi('/openapi/settlement/query-order-refund-status', $_POST = [
                'task_id' => '12345678901',
                'request_id' => $_SERVER['REQUEST_TIME'] . rand(1, 100),
            ], Request::POST);
        }, 404);

        $log = BalanceSettlementChangeLog::newLog(BalanceSettlementChangeLog::TYPE_TOPUP_ORDER_REFUND,
            self::TEST_USER_ID_QUERY_ORDER_STATUS_OR_DEDUCT_CREATOR_BALANCE, '充值订单退款');
        if (!$log->save()) {
            throw new Exception(MUtils2::getFirstError($log));
        }
        $task_id = $log->taskId();

        // 处理中
        $result = Request::openapi('/openapi/settlement/query-order-refund-status', $_POST = [
            'task_id' => $task_id,
            'request_id' => $_SERVER['REQUEST_TIME'] . rand(1, 100),
        ], Request::POST);
        $this->assertOpenApiResponse($result);
        $this->assertEquals(0, $result['code']);
        $this->assertEquals('success', $result['message']);
        $this->assertIsArray($result['data']);
        $this->assertArrayHasKeys(['task_id', 'order_status'], $result['data']);
        $this->assertEquals(BalanceSettlementChangeLog::STATUS_CREATE, $result['data']['order_status']);

        // 成功
        $log->status = BalanceSettlementChangeLog::STATUS_SUCCESS;
        $log->detail = [
            'coin_changes' => [
                ['order_id' => 999, 'coin' => -30, 'coin_changed' => -10],
            ],
        ];
        if (!$log->save()) {
            throw new Exception(MUtils2::getFirstError($log));
        }
        $result = Request::openapi('/openapi/settlement/query-order-refund-status', $_POST = [
            'task_id' => $task_id,
            'request_id' => $_SERVER['REQUEST_TIME'] . rand(1, 100),
        ], Request::POST);
        $this->assertOpenApiResponse($result);
        $this->assertEquals(0, $result['code']);
        $this->assertEquals('success', $result['message']);
        $this->assertIsArray($result['data']);
        $this->assertArrayHasKeys(['task_id', 'order_status', 'coin_changes'], $result['data']);
        $this->assertEquals(BalanceSettlementChangeLog::STATUS_SUCCESS, $result['data']['order_status']);
        $this->assertCount(1, $result['data']['coin_changes']);
        $this->assertEquals(999, $result['data']['coin_changes'][0]['order_id']);
        $this->assertEquals(-30, $result['data']['coin_changes'][0]['coin']);
        $this->assertEquals(-10, $result['data']['coin_changes'][0]['coin_changed']);

        // 失败
        $log->status = BalanceSettlementChangeLog::STATUS_FAILED;
        $log->detail = [
            'task' => [
                'fail_reason' => '部分订单已退款',
            ],
        ];
        if (!$log->save()) {
            throw new Exception(MUtils2::getFirstError($log));
        }
        $result = Request::openapi('/openapi/settlement/query-order-refund-status', $_POST = [
            'task_id' => $task_id,
            'request_id' => $_SERVER['REQUEST_TIME'] . rand(1, 100),
        ], Request::POST);
        $this->assertOpenApiResponse($result);
        $this->assertEquals(0, $result['code']);
        $this->assertEquals('success', $result['message']);
        $this->assertIsArray($result['data']);
        $this->assertArrayHasKeys(['task_id', 'order_status', 'fail_reason'], $result['data']);
        $this->assertEquals(BalanceSettlementChangeLog::STATUS_FAILED, $result['data']['order_status']);
        $this->assertEquals('部分订单已退款', $result['data']['fail_reason']);
    }

    private function prepareDeductCreatorBalance()
    {
        $balance = Balance::getByPk(self::TEST_USER_ID_QUERY_ORDER_STATUS_OR_DEDUCT_CREATOR_BALANCE);
        $balance->new_live_profit = 50000;
        $balance->new_all_live_profit = 80000;
        if (!$balance->save()) {
            throw new Exception(MUtils2::getFirstError($balance));
        }

        return [$balance->id, function () use ($balance) {
            Balance::deleteAll(['id' => $balance->id]);
        }];
    }

    public function testActionDeductCreatorBalance()
    {
        [$creator_id, $cleanup] = $this->prepareDeductCreatorBalance();
        try {
            $result = Request::openapi('/openapi/settlement/deduct-creator-balance', $_POST = [
                'creator_type' => 1,
                'creator_id' => $creator_id,
                'deduct_amount' => 30000,
                'note' => '主播余额扣减',
                'request_id' => 'test-request-id',
            ], Request::POST);
            $this->assertOpenApiResponse($result);
            $this->assertEquals(0, $result['code']);
            $this->assertEquals('success', $result['message']);
            $this->assertIsArray($result['data']);
            $this->assertArrayHasKeys(['revenue_change', 'balance'], $result['data']);
            $this->assertEquals(-30000, $result['data']['revenue_change']['revenue']);
            $this->assertEquals(-30000, $result['data']['revenue_change']['revenue_changed']);
            $this->assertEquals(20000, $result['data']['balance']);

            $creator = Balance::getByPk($creator_id);
            $this->assertEquals(20000, $creator->new_live_profit);
            $this->assertEquals(50000, $creator->new_all_live_profit);

            $log = BalanceSettlementChangeLog::findOne(['user_id' => $creator_id, 'type' => BalanceSettlementChangeLog::TYPE_SINGLE_CREATOR_DEDUCT_BALANCE]);
            $this->assertNotNull($log);
            $this->assertEquals(BalanceSettlementChangeLog::STATUS_SUCCESS, $log->status);
            $this->assertEquals('主播余额扣减', $log->note);
            $this->assertArrayHasKeys(['task', 'balance', 'revenue_change'], $log->detail);
            $this->assertEquals('test-request-id', $log->detail['task']['request_id']);
            $this->assertEquals(50000, $log->detail['balance']['old']['new_live_profit']);
            $this->assertEquals(80000, $log->detail['balance']['old']['new_all_live_profit']);
            $this->assertEquals(20000, $log->detail['balance']['new']['new_live_profit']);
            $this->assertEquals(50000, $log->detail['balance']['new']['new_all_live_profit']);
            $this->assertEquals(-30000, $log->detail['revenue_change']['revenue']);
            $this->assertEquals(-30000, $log->detail['revenue_change']['revenue_changed']);
        } finally {
            $cleanup();
        }
    }

    public function testActionSendSystemMsg()
    {
        $TEST_USER_ID = 1;
        $TEST_TEMPLATE_CODE = 'sys_deduction_ios_1';
        // 测试 request_id 不可为空
        $this->assertThrowsWithMessage(HttpException::class, 'request_id 不可为空',
            function () use ($TEST_USER_ID, $TEST_TEMPLATE_CODE) {
                Request::openapi('/openapi/settlement/send-system-msg', $_POST = [
                    'user_id' => $TEST_USER_ID,
                    'template_code' => $TEST_TEMPLATE_CODE,
                    'template_params' => ['user_id' => self::TEST_USER_ID, 'username' => self::TEST_USERNAME],
                    'request_id' => '',
                ], Request::POST);
            });

        // 测试主播 ID 错误
        $this->assertThrowsWithMessage(HttpException::class, '主播 ID 错误',
            function () use ($TEST_TEMPLATE_CODE) {
                Request::openapi('/openapi/settlement/send-system-msg', $_POST = [
                    'user_id' => 0,
                    'template_code' => $TEST_TEMPLATE_CODE,
                    'template_params' => ['user_id' => self::TEST_USER_ID, 'username' => self::TEST_USERNAME],
                    'request_id' => 'test_request_id',
                ], Request::POST);
            });

        // 测试系统消息模板编号错误
        $this->assertThrowsWithMessage(HttpException::class, '系统消息模板编号错误',
            function () use ($TEST_USER_ID) {
                Request::openapi('/openapi/settlement/send-system-msg', $_POST = [
                    'user_id' => $TEST_USER_ID,
                    'template_code' => '',
                    'template_params' => ['user_id' => self::TEST_USER_ID, 'username' => self::TEST_USERNAME],
                    'request_id' => 'test_request_id',
                ], Request::POST);
            });

        // 测试模板信息错误
        $this->assertThrowsWithMessage(HttpException::class, '模板信息错误',
            function () use ($TEST_USER_ID, $TEST_TEMPLATE_CODE) {
                Request::openapi('/openapi/settlement/send-system-msg', $_POST = [
                    'user_id' => $TEST_USER_ID,
                    'template_code' => $TEST_TEMPLATE_CODE,
                    'template_params' => [],
                    'request_id' => 'test_request_id',
                ], Request::POST);
            });

        // 测试主播不存在
        $this->assertThrowsWithMessage(HttpException::class, '主播不存在',
            function () use ($TEST_TEMPLATE_CODE) {
                Request::openapi('/openapi/settlement/send-system-msg', $_POST = [
                    'user_id' => 999999,
                    'template_code' => $TEST_TEMPLATE_CODE,
                    'template_params' => ['user_id' => self::TEST_USER_ID, 'username' => self::TEST_USERNAME],
                    'request_id' => 'test_request_id',
                ], Request::POST);
            });

        // 测试正常发送系统消息
        $res = Request::openapi('/openapi/settlement/send-system-msg', $_POST = [
            'user_id' => $TEST_USER_ID,
            'template_code' => $TEST_TEMPLATE_CODE,
            'template_params' => ['user_id' => self::TEST_USER_ID, 'username' => self::TEST_USERNAME],
            'request_id' => 'test_request_id',
        ], Request::POST);
        $this->assertOpenApiResponse($res);
        $this->assertEquals(0, $res['code']);
        $this->assertEquals('success', $res['message']);
        $this->assertNull($res['data']);
        // 断言系统消息存在
        $data = MMessageAssign::find()
            ->where(['recuid' => $TEST_USER_ID, 'status' => MMessageAssign::NOT_READ])
            ->one();
        $expected = Yii::$app->params['templates']['system_msg_templates'][$TEST_TEMPLATE_CODE];
        $expected['content'] = str_replace(['{{user_id}}', '{{username|html}}'],
            [self::TEST_USER_ID, self::TEST_USERNAME], $expected['content']);
        $this->assertEquals($expected['title'], $data['title']);
        $this->assertEquals($expected['content'], $data['content']);

        // 测试发送素人提现成功通知 - 不带模板参数
        $TEST_USER_ID = 2;
        $TEST_TEMPLATE_CODE = 'sys_withdrawal_success';
        $res = Request::openapi('/openapi/settlement/send-system-msg', $_POST = [
            'user_id' => $TEST_USER_ID,
            'template_code' => $TEST_TEMPLATE_CODE,
            'request_id' => 'test_request_id',
        ], Request::POST);
        $this->assertOpenApiResponse($res);
        $this->assertEquals(0, $res['code']);
        $this->assertEquals('success', $res['message']);
        $this->assertNull($res['data']);
        // 断言系统消息存在
        $data = MMessageAssign::find()
            ->where(['recuid' => $TEST_USER_ID, 'status' => MMessageAssign::NOT_READ])
            ->one();
        $expected = Yii::$app->params['templates']['system_msg_templates'][$TEST_TEMPLATE_CODE];
        $this->assertEquals($expected['title'], $data['title']);
        $this->assertEquals($expected['content'], $data['content']);
    }

    public function testActionSendEmail()
    {
        // 测试参数错误
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () {
            Request::openapi('/openapi/settlement/send-email', $_POST = [
                'to' => '',
                'cc' => '',
                'template_code' => '',
                'template_params' => [],
                'request_id' => '',
            ], Request::POST);
        });

        // 测试不存在的 template_code
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () {
            Request::openapi('/openapi/settlement/send-email', $_POST = [
                'to' => '<EMAIL>',
                'cc' => '<EMAIL>',
                'template_code' => '',
                'template_params' => [
                    'guild_name' => '测试',
                    'billing_month' => '2024-09',
                    'signing_deadline_date' => '2024-09-10',
                    'invoice_return_deadline_date' => '2024-09-10',
                    'download_url' => 'http://test.com/xxx',
                ],
                'request_id' => 'test',
            ], Request::POST);
        });

        // 测试正常发送
        Tools::registerRpcApiResponseFunc('/api/email', function () {
            return ['code' => Tools::CODE_SUCCESS];
        });
        $res = Request::openapi('/openapi/settlement/send-email', $_POST = [
            'to' => '<EMAIL>',
            'cc' => '<EMAIL>',
            'template_code' => SettlementController::TEMPLATE_CODE_GUILD_SETTLEMENT_MONTHLY,
            'template_params' => [
                'guild_name' => '测试',
                'billing_month' => '2024-09',
                'signing_deadline_date' => '2024-09-10',
                'invoice_return_deadline_date' => '2024-09-10',
                'download_url' => 'http://test.com/xxx',
            ],
            'request_id' => 'test',
        ], Request::POST);
        $this->assertOpenApiResponse($res);
        $this->assertEquals(0, $res['code']);
        $this->assertEquals('success', $res['message']);
        $this->assertNull($res['data']);

        // 测试合作方账单邮件发送成功
        $res = Request::openapi('/openapi/settlement/send-email', $_POST = [
            'to' => '<EMAIL>',
            'cc' => '<EMAIL>',
            'template_code' => SettlementController::TEMPLATE_CODE_BEIJING_PARTNER_SETTLEMENT_QUARTER,
            'template_params' => [
                'partner_name' => '合作方公司名称',
                'enterprise_name' => '北京喵斯拉',
                'title' => '剧集',
                'billing_year' => '2024',
                'billing_quarter' => 'Q3',
                'download_url' => 'http://test.com/xxx',
            ],
            'request_id' => 'test',
        ], Request::POST);
        $this->assertOpenApiResponse($res);
        $this->assertEquals(0, $res['code']);
        $this->assertEquals('success', $res['message']);
        $this->assertNull($res['data']);

        // 测试公对私账单邮件发送成功
        $res = Request::openapi('/openapi/settlement/send-email', $_POST = [
            'to' => '<EMAIL>',
            'cc' => '<EMAIL>',
            'template_code' => SettlementController::TEMPLATE_CODE_PERSONAL_SETTLEMENT_MONTHLY,
            'template_params' => [
                'username' => '测试',
                'billing_month' => '2025-01',
                'signing_deadline_date' => '2025-01-14',
                'download_url' => 'http://test.com/xxx',
            ],
            'request_id' => 'test',
        ], Request::POST);
        $this->assertOpenApiResponse($res);
        $this->assertEquals(0, $res['code']);
        $this->assertEquals('success', $res['message']);
        $this->assertNull($res['data']);
    }

    public function testReplaceTemplateParams()
    {
        // 测试正常返回
        $ref_class = new ReflectionClass(SettlementController::class);
        $instance = $ref_class->newInstance('settlement', 'test');
        $result = self::invokePrivateMethod($ref_class, 'replaceTemplateParams', [['aa' => 'bb'], '你好 {{aa}}'], $instance, true);
        $this->assertEquals('你好 bb', $result);

        // 测试模板信息参数数量错误
        $this->assertThrowsWithMessage(HttpException::class, '模板信息参数数量错误',
            function () use ($ref_class, $instance) {
                $content = Yii::$app->params['templates']['system_msg_templates']['sys_deduction_ios_1']['content'];
                self::invokePrivateMethod($ref_class, 'replaceTemplateParams', [['username' => 'bb'], $content],
                    $instance, true);
            });

        // 测试正常替换
        $content = Yii::$app->params['templates']['system_msg_templates']['sys_deduction_ios_1']['content'];
        $params = [['user_id' => 1234, 'username' => 'bb'], $content];
        $result = self::invokePrivateMethod($ref_class, 'replaceTemplateParams', $params, $instance, true);
        $expected = str_replace(['{{user_id}}', '{{username|html}}'], array_values($params[0]), $content);
        $this->assertEquals($expected, $result);

        // 测试需要替换的参数为 null
        $content = Yii::$app->params['templates']['system_msg_templates']['sys_withdrawal_success']['content'];
        $params = [null, $content];
        $result = self::invokePrivateMethod($ref_class, 'replaceTemplateParams', $params, $instance, true);
        $this->assertEquals($content, $result);

        // 测试 0 个需要替换的参数
        $content = Yii::$app->params['templates']['system_msg_templates']['sys_withdrawal_success']['content'];
        $result = self::invokePrivateMethod($ref_class, 'replaceTemplateParams', [[], $content], $instance, true);
        $this->assertEquals($content, $result);

        // 测试有重复命名的模板变量
        $params = $params = [['aa' => 'test'], '你好 {{aa}} {{aa}}', true];
        $result = self::invokePrivateMethod($ref_class, 'replaceTemplateParams', $params, $instance, true);
        $this->assertEquals('你好 test test', $result);
        $params = $params = [['aa' => '<test>'], '你好 {{aa}} {{aa|html}}', true];
        $result = self::invokePrivateMethod($ref_class, 'replaceTemplateParams', $params, $instance, true);
        $this->assertEquals('你好 <test> ' . Html::encode($params[0]['aa']), $result);

        // 测试进行 html 转义
        $params = [['aa' => '<a href="http://test.com">test</a>'], '你好 {{aa|html}}', true];
        $result = self::invokePrivateMethod($ref_class, 'replaceTemplateParams', $params, $instance, true);
        $this->assertEquals('你好 ' . Html::encode($params[0]['aa']), $result);

        // 测试不进行 html 转义
        $params = [['aa' => '<a href="http://test.com">test</a>'], '你好 {{aa}}', false];
        $result = self::invokePrivateMethod($ref_class, 'replaceTemplateParams', $params, $instance, true);
        $this->assertEquals('你好 ' . $params[0]['aa'], $result);
    }

    public function testFormatGuildSettlementMonthly()
    {
        $ref_class = new ReflectionClass(SettlementController::class);
        $instance = $ref_class->newInstance('settlement', 'test');

        $this->assertThrowsWithMessage(HttpException::class, '参数不可为空',
            function () use ($ref_class, $instance) {
                self::invokePrivateMethod($ref_class, 'formatGuildSettlementMonthly', [], $instance);
            }
        );

        $param = [
            'guild_name' => '公会名称',
            'download_url' => '',
        ];
        $this->assertThrowsWithMessage(HttpException::class, 'guild_name OR download_url 不可为空',
            function () use ($ref_class, $instance, $param) {
                self::invokePrivateMethod($ref_class, 'formatGuildSettlementMonthly', $param, $instance);
            }
        );

        $param['download_url'] = 'https://baidu.com/xx.xlsx';
        $param['billing_month'] = '2024-01-02';
        $this->assertThrowsWithMessage(HttpException::class, '账单月份不正确',
            function () use ($ref_class, $instance, $param) {
                self::invokePrivateMethod($ref_class, 'formatGuildSettlementMonthly', $param, $instance);
            }
        );

        $param['billing_month'] = '2024-01';
        $param['signing_deadline_date'] = '2024-01';
        $this->assertThrowsWithMessage(HttpException::class, '签署截止日期不正确',
            function () use ($ref_class, $instance, $param) {
                self::invokePrivateMethod($ref_class, 'formatGuildSettlementMonthly', $param, $instance);
            }
        );

        $param['signing_deadline_date'] = '2024-01-01';
        $param['invoice_return_deadline_date'] = '2024-01';
        $this->assertThrowsWithMessage(HttpException::class, '发票接收截止日期不正确',
            function () use ($ref_class, $instance, $param) {
                self::invokePrivateMethod($ref_class, 'formatGuildSettlementMonthly', $param, $instance);
            }
        );

        $param = [
            'guild_name' => '公会名称',
            'download_url' => 'https://baidu.com/xx.xlsx',
            'billing_month' => '2024-01',
            'signing_deadline_date' => date_timestamp_set(date_create(), $_SERVER['REQUEST_TIME'])->format('Y-m-20'),
            'invoice_return_deadline_date' => '2024-02-20',
        ];
        list($subject, $body) = self::invokePrivateMethod($ref_class, 'formatGuildSettlementMonthly', $param, $instance);
        $this->assertNotEmpty($subject);
        $this->assertEquals('01', $subject['billing_month']);
        $this->assertNotEmpty($body);
        $this->assertEquals('公会名称', $body['guild_name']);
        $this->assertEquals('01', $body['billing_month']);
        $this->assertEquals('本月 20 日', $body['signing_deadline_date']);
        $this->assertEquals('02 月 20 日', $body['invoice_return_deadline_date']);
        $this->assertEquals('https://link.missevan.com/help/live-guild-invoice', $body['help_url']);
        $this->assertEquals('https://baidu.com/xx.xlsx', $body['download_url']);

        // 本地时间和签署年月对不上
        $param['signing_deadline_date'] = '2024-01-20';
        list($subject, $body) = self::invokePrivateMethod($ref_class, 'formatGuildSettlementMonthly', $param, $instance);
        $this->assertNotEmpty($subject);
        $this->assertNotEmpty($body);
        $this->assertEquals(' 01 月 20 日', $body['signing_deadline_date']);
    }

    public function testFormatPartnerSettlementQuarter()
    {
        $ref_class = new ReflectionClass(SettlementController::class);
        $instance = $ref_class->newInstance('settlement', 'test');

        $this->assertThrowsWithMessage(HttpException::class, '参数不可为空',
            function () use ($ref_class, $instance) {
                self::invokePrivateMethod($ref_class, 'formatPartnerSettlementQuarter', [], $instance);
            }
        );

        // 测试 partner_name 参数不正确
        $param = [
            'partner_name' => '',
        ];
        $this->assertThrowsWithMessage(HttpException::class, 'partner_name 参数不正确',
            function () use ($ref_class, $instance, $param) {
                self::invokePrivateMethod($ref_class, 'formatPartnerSettlementQuarter', $param, $instance);
            }
        );

        // 测试 enterprise_name 参数不正确
        $param = [
            'partner_name' => '合作方公司名称',
            'enterprise_name' => '',
        ];
        $this->assertThrowsWithMessage(HttpException::class, 'enterprise_name 参数不正确',
            function () use ($ref_class, $instance, $param) {
                self::invokePrivateMethod($ref_class, 'formatPartnerSettlementQuarter', $param, $instance);
            }
        );

        // 测试 title 参数不正确
        $param = [
            'partner_name' => '合作方公司名称',
            'enterprise_name' => '主体公司名称',
            'title' => '',
        ];
        $this->assertThrowsWithMessage(HttpException::class, 'title 参数不正确',
            function () use ($ref_class, $instance, $param) {
                self::invokePrivateMethod($ref_class, 'formatPartnerSettlementQuarter', $param, $instance);
            }
        );

        // 测试 billing_year 参数不正确
        $param = [
            'partner_name' => '合作方公司名称',
            'enterprise_name' => '主体公司名称',
            'title' => '语音包',
            'billing_year' => '',
        ];
        $this->assertThrowsWithMessage(HttpException::class, 'billing_year 参数不正确',
            function () use ($ref_class, $instance, $param) {
                self::invokePrivateMethod($ref_class, 'formatPartnerSettlementQuarter', $param, $instance);
            }
        );

        // 测试 billing_quarter 参数不正确
        $param = [
            'partner_name' => '合作方公司名称',
            'enterprise_name' => '主体公司名称',
            'title' => '语音包',
            'billing_year' => '2024',
            'billing_quarter' => '',
        ];
        $this->assertThrowsWithMessage(HttpException::class, 'billing_quarter 参数不正确',
            function () use ($ref_class, $instance, $param) {
                self::invokePrivateMethod($ref_class, 'formatPartnerSettlementQuarter', $param, $instance);
            }
        );

        // 测试 billing_quarter 参数不正确
        $param = [
            'partner_name' => '合作方公司名称',
            'enterprise_name' => '主体公司名称',
            'title' => '语音包',
            'billing_year' => '2024',
            'billing_quarter' => 'Q3',
            'download_url' => ''
        ];
        $this->assertThrowsWithMessage(HttpException::class, 'download_url 参数不正确',
            function () use ($ref_class, $instance, $param) {
                self::invokePrivateMethod($ref_class, 'formatPartnerSettlementQuarter', $param, $instance);
            }
        );

        // 测试正常返回
        $param['download_url'] = 'https://static-test.maoercdn.com/xx.xlsx';
        list($subject, $body) = self::invokePrivateMethod($ref_class, 'formatPartnerSettlementQuarter', $param, $instance);
        $this->assertNotEmpty($subject);
        $this->assertCount(5, $subject);
        $this->assertEquals($param['partner_name'], $subject['partner_name']);
        $this->assertEquals($param['enterprise_name'], $subject['enterprise_name']);
        $this->assertEquals($param['title'], $subject['title']);
        $this->assertEquals($param['billing_year'], $subject['billing_year']);
        $this->assertEquals($param['billing_quarter'], $subject['billing_quarter']);
        $this->assertNotEmpty($body);
        $this->assertCount(3, $body);
        $this->assertEquals($param['billing_year'], $body['billing_year']);
        $this->assertEquals($param['billing_quarter'], $body['billing_quarter']);
        $this->assertEquals($param['download_url'], $body['download_url']);
    }

    public function testFormatPersonSettlementMonthly()
    {
        $ref_class = new ReflectionClass(SettlementController::class);
        $instance = $ref_class->newInstance('settlement', 'test');

        $this->assertThrowsWithMessage(HttpException::class, '参数不可为空',
            function () use ($ref_class, $instance) {
                self::invokePrivateMethod($ref_class, 'formatPersonSettlementMonthly', [], $instance);
            }
        );

        // 测试参数错误
        $param = [
            'username' => '',
            'billing_month' => '',
            'signing_deadline_date' => '',
            'download_url' => '',
        ];
        $this->assertThrowsWithMessage(HttpException::class, '参数错误',
            function () use ($ref_class, $instance, $param) {
                self::invokePrivateMethod($ref_class, 'formatPersonSettlementMonthly', $param, $instance);
            }
        );

        // 测试账单月份不正确
        $param = [
            'username' => '测试',
            'billing_month' => '2025-01-14',
            'signing_deadline_date' => '2025-01',
            'download_url' => 'https://test.com/xx.xlsx',
        ];
        $this->assertThrowsWithMessage(HttpException::class, '账单月份不正确',
            function () use ($ref_class, $instance, $param) {
                self::invokePrivateMethod($ref_class, 'formatPersonSettlementMonthly', $param, $instance);
            }
        );

        // 测试签署截止日期不正确
        $now = $_SERVER['REQUEST_TIME'];
        $test_month = date('Y-m', $now);
        $param = [
            'username' => '测试',
            'billing_month' => $test_month,
            'signing_deadline_date' => '2025-01',
            'download_url' => 'https://test.com/xx.xlsx',
        ];
        $this->assertThrowsWithMessage(HttpException::class, '签署截止日期不正确',
            function () use ($ref_class, $instance, $param) {
                self::invokePrivateMethod($ref_class, 'formatPersonSettlementMonthly', $param, $instance);
            }
        );

        // 测试正常返回
        $test_date = date('Y-m-d', $now);
        $param['signing_deadline_date'] = $test_date;
        list($subject, $body) = self::invokePrivateMethod($ref_class, 'formatPersonSettlementMonthly', $param, $instance);
        $this->assertNotEmpty($subject);
        $this->assertCount(1, $subject);
        $this->assertEquals(date('m', $now), $subject['billing_month']);
        $this->assertNotEmpty($body);
        $this->assertCount(4, $body);
        $this->assertEquals($param['username'], $body['username']);
        $this->assertEquals(date('m', $now), $body['billing_month']);
        $this->assertEquals('本月 ' . date('d', $now) . ' 日', $body['signing_deadline_date']);
        $this->assertEquals($param['download_url'], $body['download_url']);
    }

    public function testActionWithdrawalConfirm()
    {
        $TEST_PROFIT = 100;
        $record1 = Data::createWithdrawalRecord([
            'user_id' => self::TEST_USER_ID,
            'profit' => $TEST_PROFIT,
            'status' => WithdrawalRecord::STATUS_CREATE,
            'type' => WithdrawalRecord::TYPE_WITHDRAW_LIVE_NEW,
        ]);
        $record2 = Data::createWithdrawalRecord([
            'user_id' => self::TEST_USER_ID,
            'profit' => $TEST_PROFIT,
            'status' => WithdrawalRecord::STATUS_CREATE,
            'type' => WithdrawalRecord::TYPE_WITHDRAW_LIVE_NEW,
        ]);
        $record_ids = [$record1->id, $record2->id];
        $record_ids_str = implode(',', $record_ids);
        // 测试 request_id 错误
        $this->assertThrowsWithMessage(HttpException::class, 'request_id 不可为空',
            function () use ($record_ids_str) {
                Request::openapi('/openapi/settlement/withdrawal-confirm', $_POST = [
                    'record_ids' => $record_ids_str,
                    'user_id' => self::TEST_USER_ID,
                    'withdrawal_status' => 1,
                    'request_id' => ''
                ], Request::POST);
            });

        // 测试参数错误
        $this->assertThrowsWithMessage(HttpException::class, '参数错误',
            function () use ($record_ids_str) {
                Request::openapi('/openapi/settlement/withdrawal-confirm', $_POST = [
                    'record_ids' => $record_ids_str,
                    'user_id' => 0,
                    'withdrawal_status' => 9999999,
                    'request_id' => 'test'
                ], Request::POST);
            });

        // 测试 record_ids 信息不可为空
        $this->assertThrowsWithMessage(HttpException::class, 'record_ids 信息不可为空',
            function () use ($record_ids_str) {
                Request::openapi('/openapi/settlement/withdrawal-confirm', $_POST = [
                    'record_ids' => '',
                    'user_id' => self::TEST_USER_ID,
                    'withdrawal_status' => 1,
                    'request_id' => 'test'
                ], Request::POST);
            });

        // 测试 record_ids 元素需为整型
        $this->assertThrowsWithMessage(HttpException::class, 'record_ids 元素需为整型',
            function () use ($record_ids_str) {
                Request::openapi('/openapi/settlement/withdrawal-confirm', $_POST = [
                    'record_ids' => 'a,1',
                    'user_id' => self::TEST_USER_ID,
                    'withdrawal_status' => 1,
                    'request_id' => 'test'
                ], Request::POST);
            });

        // 测试 record_ids 数量上限
        $this->assertThrowsWithMessage(HttpException::class, 'record_ids 最大数量为 50',
            function () {
                Request::openapi('/openapi/settlement/withdrawal-confirm', $_POST = [
                    'record_ids' => '1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30' .
                        ',31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51',
                    'user_id' => self::TEST_USER_ID,
                    'withdrawal_status' => 1,
                    'request_id' => 'test'
                ], Request::POST);
            });

        // 测试提现失败原因不能为空
        $this->assertThrowsWithMessage(HttpException::class, '提现失败原因不能为空',
            function () use ($record_ids_str) {
                Request::openapi('/openapi/settlement/withdrawal-confirm', $_POST = [
                    'record_ids' => $record_ids_str,
                    'user_id' => self::TEST_USER_ID,
                    'withdrawal_status' => 2,
                    'failed_reason' => '',
                    'request_id' => 'test'
                ], Request::POST);
            });

        // 测试订单不存在
        $this->assertThrowsWithMessage(HttpException::class, '提现记录不存在',
            function () {
                Request::openapi('/openapi/settlement/withdrawal-confirm', $_POST = [
                    'record_ids' => '99998,99999',
                    'user_id' => self::TEST_USER_ID,
                    'withdrawal_status' => 1,
                    'request_id' => 'test'
                ], Request::POST);
            });

        // 测试提现成功
        WithdrawalRecord::updateAll(['status' => WithdrawalRecord::STATUS_CREATE, 'modified_time' => 0],
            ['id' => $record_ids]);
        $res = Request::openapi('/openapi/settlement/withdrawal-confirm', $_POST = [
            'record_ids' => $record_ids_str,
            'user_id' => self::TEST_USER_ID,
            'withdrawal_status' => 1,
            'request_id' => 'test'
        ], Request::POST);
        $this->assertOpenApiResponse($res);
        $this->assertEquals(0, $res['code']);
        $this->assertEquals('success', $res['message']);
        $this->assertNull($res['data']);
        // 断言提现记录状态
        $actual_record_map = WithdrawalRecord::find()->select('id, status, modified_time')
            ->where(['id' => $record_ids])->indexBy('id')->all();
        $this->assertEquals(WithdrawalRecord::STATUS_CONFIRM, $actual_record_map[$record1->id]->status);
        $this->assertTrue($actual_record_map[$record1->id]->modified_time > 0);
        $this->assertEquals(WithdrawalRecord::STATUS_CONFIRM, $actual_record_map[$record2->id]->status);
        $this->assertTrue($actual_record_map[$record2->id]->modified_time > 0);

        // 测试提现失败
        WithdrawalRecord::updateAll(['status' => WithdrawalRecord::STATUS_CREATE, 'modified_time' => 0],
            ['id' => $record_ids]);
        Balance::updateByPk(self::TEST_USER_ID, ['new_live_profit' => 0]);
        $res = Request::openapi('/openapi/settlement/withdrawal-confirm', $_POST = [
            'record_ids' => $record_ids_str,
            'user_id' => self::TEST_USER_ID,
            'withdrawal_status' => 2,
            'failed_reason' => 'test',
            'request_id' => 'test'
        ], Request::POST);
        $this->assertOpenApiResponse($res);
        $this->assertEquals(0, $res['code']);
        $this->assertEquals('success', $res['message']);
        $this->assertNull($res['data']);
        // 断言提现记录状态
        $actual_record_map = WithdrawalRecord::find()->select('id, status, modified_time')
            ->where(['id' => $record_ids])->indexBy('id')->all();
        $this->assertEquals(WithdrawalRecord::STATUS_INVALID, $actual_record_map[$record1->id]->status);
        $this->assertTrue($actual_record_map[$record1->id]->modified_time > 0);
        $this->assertEquals(WithdrawalRecord::STATUS_INVALID, $actual_record_map[$record2->id]->status);
        $this->assertTrue($actual_record_map[$record2->id]->modified_time > 0);
        // 断言收益已退回
        $actual = (int)Balance::find()->select('new_live_profit')->where(['id' => self::TEST_USER_ID])->scalar();
        $expected = Balance::profitUnitConversion($record1->profit + $record2->profit,
            Balance::CONVERT_YUAN_TO_FEN);
        $this->assertEquals($expected, $actual);
    }

    public function testActionGetAccountInfo()
    {
        // 测试 request_id 为空
        $this->assertThrowsWithMessage(HttpException::class, 'request_id 不可为空', function () {
            Request::openapi('/openapi/settlement/get-account-info', $_POST = [
                'account_ids' => '1,2',
                'request_id' => '',
            ], Request::POST);
        });

        // 测试 account_ids 为空
        $this->assertThrowsWithMessage(HttpException::class, 'account_ids 信息不可为空', function () {
            Request::openapi('/openapi/settlement/get-account-info', $_POST = [
                'account_ids' => '',
                'request_id' => 'test',
            ], Request::POST);
        });

        // 测试 account_ids 参数不合法
        $this->assertThrowsWithMessage(HttpException::class, 'account_ids 元素需为整型', function () {
            Request::openapi('/openapi/settlement/get-account-info', $_POST = [
                'account_ids' => 'a,2',
                'request_id' => 'test',
            ], Request::POST);
        });

        // 测试 account_ids 数量上限
        $this->assertThrowsWithMessage(HttpException::class, 'account_ids 最大数量为 100', function () {
            Request::openapi('/openapi/settlement/get-account-info', $_POST = [
                'account_ids' => '1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30' .
                    ',31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61' .
                    ',62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92' .
                    ',93,94,95,96,97,98,99,100,101',
                'request_id' => 'test',
            ], Request::POST);
        });

        // 测试查询不到相关信息
        $result = Request::openapi('/openapi/settlement/get-account-info', $_POST = [
            'account_ids' => '999998,999999',
            'request_id' => 'test',
        ], Request::POST);
        $this->assertEmpty($result['data']['account_info']);

        // 测试正常返回
        Data::createAccountInfo(['id' => 1, 'user_id' => 1]);
        Data::createAccountInfo(['id' => 2, 'user_id' => 2]);
        $result = Request::openapi('/openapi/settlement/get-account-info', $_POST = [
            'account_ids' => '1,2',
            'request_id' => 'test',
        ], Request::POST);
        $this->assertNotEmpty($result['data']['account_info']);
    }

    public function testActionGetGuildInfo()
    {
        // 参数不合法
        // request_id 为空
        $this->assertThrowsWithMessage(HttpException::class, 'request_id 不可为空', function () {
            Request::openapi('/openapi/settlement/get-guild-info', $_POST = [
                'guild_ids' => '1,2',
                'request_id' => '',
            ], Request::POST);
        });

        // guild_ids 为空
        $this->assertThrowsWithMessage(HttpException::class, 'guild_ids 信息不可为空', function () {
            Request::openapi('/openapi/settlement/get-guild-info', $_POST = [
                'guild_ids' => '',
                'request_id' => 'test',
            ], Request::POST);
        });

        // guild_ids 参数不合法
        $this->assertThrowsWithMessage(HttpException::class, 'guild_ids 元素需为整型', function () {
            Request::openapi('/openapi/settlement/get-guild-info', $_POST = [
                'guild_ids' => 'a,2',
                'request_id' => 'test',
            ], Request::POST);
        });

        // 正常返回
        $now = $_SERVER['REQUEST_TIME'];
        $guild = new Guild();
        $guild->user_id = 12345679;
        $guild->name = uniqid('name_');
        $guild->intro = 'xxx';
        $guild->owner_name = MUtils2::encrypt('xxx', $now, SENSITIVE_INFORMATION_KEY);
        $guild->owner_id_number = MUtils2::encrypt('xxx', $now, SENSITIVE_INFORMATION_KEY);
        $guild->owner_id_people = 'oss://image/guild/201805/22/762f97d2f6cf4e328d27c6462a8f1b35145512.png';
        $guild->owner_backcover = 'oss://image/guild/201805/22/762f97d2f6cf4e328d27c6462a8f1b35142221.png';
        $guild->mobile = MUtils2::encrypt('xxx', $now, SENSITIVE_INFORMATION_KEY);
        $guild->email = MUtils2::encrypt('xxx', $now, SENSITIVE_INFORMATION_KEY);
        $guild->corporation_name = MUtils2::encrypt('test-corporation-name', $now, SENSITIVE_INFORMATION_KEY);
        $guild->corporation_address = MUtils2::encrypt('xxx', $now, SENSITIVE_INFORMATION_KEY);
        $guild->corporation_phone = MUtils2::encrypt('xxx', $now, SENSITIVE_INFORMATION_KEY);
        $guild->business_license_number = MUtils2::encrypt('xxx', $now, SENSITIVE_INFORMATION_KEY);
        $guild->business_license_frontcover = 'oss://image/guild/201805/22/762f97d2f6cf4e328d27c6462a.png';
        $guild->tax_account = MUtils2::encrypt('xxx', $now, SENSITIVE_INFORMATION_KEY);
        $guild->bank = 'xxx';
        $guild->bank_address = 'xxx';
        $guild->bank_branch = 'xxx';
        $guild->checked = Guild::CHECKED_PASS;
        $guild->bank_account = MUtils2::encrypt('xxx', $now, SENSITIVE_INFORMATION_KEY);
        $guild->bank_account_name = 'xxx';
        $guild->apply_time = $now;
        $guild->create_time = $now;
        $guild->modified_time = $now;
        if (!$guild->save()) {
            throw new Exception(MUtils2::getFirstError($guild));
        }
        self::$test_guild_id = $guild->id;

        $result = Request::openapi('/openapi/settlement/get-guild-info', $_POST = [
            'guild_ids' => '777,888',
            'request_id' => 'test',
        ], Request::POST);
        $this->assertEmpty($result['data']['guild_info']);

        $result = Request::openapi('/openapi/settlement/get-guild-info', $_POST = [
            'guild_ids' => implode(',', [888, self::$test_guild_id]),
            'request_id' => 'test',
        ], Request::POST);
        $this->assertNotEmpty($result['data']['guild_info']);
        $this->assertCount(1, $result['data']['guild_info']);
        $this->assertArrayHasKeys(['id', 'corporation_name'], $result['data']['guild_info'][0]);
        $this->assertEquals(self::$test_guild_id, $result['data']['guild_info'][0]['id']);
        $this->assertEquals('test-corporation-name', $result['data']['guild_info'][0]['corporation_name']);
    }

}
