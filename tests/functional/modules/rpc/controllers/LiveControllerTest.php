<?php

namespace tests\modules\rpc\controllers;

use app\forms\GoodsForm;
use app\forms\TransactionFormLive;
use app\models\Balance;
use app\models\BlackUser;
use app\models\Drama;
use app\models\Gift;
use app\models\Guild;
use app\models\GuildBalance;
use app\models\GuildLiveContract;
use app\models\Live;
use app\models\MAttentionUser;
use app\models\Mowangskuser;
use app\models\MSound;
use app\models\PayAccount;
use app\models\PayAccounts;
use app\models\TransactionItemsLog;
use app\models\TransactionLog;
use missevan\util\MUtils as MUtils2;
use PHPUnit\Runner\Exception;
use tests\components\util\Data;
use tests\components\util\Tools;
use tests\components\web\Request;
use tests\components\UnitTestCase;
use Yii;
use yii\helpers\Json;
use yii\web\HttpException;

class LiveControllerTest extends UnitTestCase
{
    // 礼物 ID（药丸）
    const TEST_GIFT_ID = 1;
    // 礼物价格（6 钻石）
    const TEST_GIFT_PRICE = 6;
    // 直播提问价格（30 钻石）
    const TEST_ASK_PRICE = 30;
    // 练习生 VIP 价格
    const TEST_VIP1_PRICE = 160;

    const TEST_WISH_GOODS_BUYER_ID = 401;
    const TEST_GASHAPON_BUYER_ID = 555;
    const TEST_GASHAPON_SELLER_ID = 666;
    const TEST_BUY_GOODS_USER_ID = 777;

    // 测试用公会 ID
    private static $guild_id;
    // 主播不属于公会时，直播提问交易记录 ID
    private static $ask_transaction_id;
    // 主播属于公会时，直播提问交易记录 ID
    private static $ask_guild_transaction_id;

    private static $origin_db;
    private static $origin_paydb;
    private static $origin_messagedb;
    private static $origin_readonly_messagedb;

    private static function setOriginDbs(): void
    {
        self::$origin_db = Yii::$app->db;
        self::$origin_paydb = Yii::$app->paydb;
        self::$origin_messagedb = Yii::$app->messagedb;
        self::$origin_readonly_messagedb = Yii::$app->readonly_messagedb;
        Yii::$app->set('db', Yii::$app->sqlitedb);
        Yii::$app->set('paydb', Yii::$app->sqlite_paydb);
        Yii::$app->set('messagedb', Yii::$app->sqlite_messagedb);
        Yii::$app->set('readonly_messagedb', Yii::$app->sqlite_readonly_messagedb);
    }

    private static function resetOriginDbs(): void
    {
        Yii::$app->set('db', self::$origin_db);
        Yii::$app->set('paydb', self::$origin_paydb);
        Yii::$app->set('messagedb', self::$origin_messagedb);
        Yii::$app->set('readonly_messagedb', self::$origin_readonly_messagedb);
    }

    protected function _before()
    {
        parent::_before();
        self::setOriginDbs();
    }

    protected function _after()
    {
        parent::_after();
        self::resetOriginDbs();
    }

    /**
     * 创建测试相关数据
     */
    public static function setUpBeforeClass(): void
    {
        parent::setUpBeforeClass();
        self::setOriginDbs();
        self::cleanData();

        $now = $_SERVER['REQUEST_TIME'];
        GuildBalance::updateAll([
            'in_ios' => 0,
            'in_android' => 0,
            'in_tmallios' => 0,
            'in_paypal' => 0,
            'live_profit' => 0,
            'all_live_profit' => 0,
        ], 'id = 3');

        // 创建测试用直播公会
        $guild = new Guild();
        $guild->user_id = self::TEST_USER_ID;
        $guild->name = '三体协会';
        $guild->intro = '世界属于三体';
        $guild->owner_name = 'mowang';
        $guild->owner_id_number = '5225271200001010101';
        $guild->owner_id_people = 'oss://image/guild/201805/22/762f97d2f6cf4e328d27c6462a8f1b35145512.png';
        $guild->owner_backcover = 'oss://image/guild/201805/22/762f97d2f6cf4e328d27c6462a8f1b35142221.png';
        $guild->mobile = '***********';
        $guild->email = '<EMAIL>';
        $guild->corporation_name = '三体';
        $guild->corporation_address = '三体星';
        $guild->corporation_phone = '**********';
        $guild->business_license_number = '****************';
        $guild->business_license_frontcover = 'oss://image/guild/201805/22/762f97d2f6cf4e328d27c6462a.png';
        $guild->tax_account = '****************';
        $guild->bank = '三体银行';
        $guild->bank_address = '三体星';
        $guild->bank_branch = '三体银行地球支行';
        $guild->checked = Guild::CHECKED_PASS;
        $guild->bank_account = '1111000011110000111';
        $guild->bank_account_name = '三体协会';
        $guild->apply_time = $now;
        $guild->create_time = $now;
        $guild->modified_time = $now;
        if (!$guild->save()) {
            throw new Exception(MUtils2::getFirstError($guild));
        }
        self::$guild_id = $guild->id;
        // 删除历史测试数据
        TransactionLog::deleteAll(['to_id' => self::TEST_USER_ID, 'from_id' => self::TEST_USER2_ID]);
        Balance::updateAll([
            'live_profit' => 0,
            'all_live_profit' => 0,
            'new_live_profit' => 0,
            'new_all_live_profit' => 0,
        ], ['id' => self::TEST_USER_ID]);
    }

    /**
     * 删除测试中创建的相关数据
     */
    public static function tearDownAfterClass(): void
    {
        parent::tearDownAfterClass();
        self::setOriginDbs();
        self::cleanData();
        self::resetOriginDbs();
    }

    public static function cleanData()
    {
        // 清理脏数据
        Balance::deleteAll(['id' => self::TEST_GASHAPON_BUYER_ID]);
        TransactionLog::deleteAll(['from_id' => self::TEST_GASHAPON_BUYER_ID]);
        Guild::deleteAll(['id' => self::$guild_id]);
        GuildLiveContract::deleteAll(['guild_id' => self::$guild_id]);
        GuildBalance::deleteAll(['id' => self::$guild_id]);
        MAttentionUser::deleteAll([
            'OR',
            ['IN', 'user_active', [self::TEST_USER_ID, self::TEST_USER4_ID]],
            ['IN', 'user_passtive', [self::TEST_USER_ID, self::TEST_USER4_ID]]
        ]);
        Mowangskuser::updateAll(['fansnum' => 0, 'follownum' => 0], 'id = :id', [':id' => self::TEST_USER_ID]);
        Mowangskuser::updateAll(['fansnum' => 0, 'follownum' => 0], 'id = :id', [':id' => self::TEST_USER4_ID]);

        MSound::deleteAll('user_id = :user_id AND soundstr = :soundstr', [
            ':user_id' => self::TEST_USER_ID,
            ':soundstr' => '直播回放测试标题',
        ]);
        Yii::$app->redis->del(KEY_LIVE_VIDEOS);
    }

    public function testBuyGift2()
    {
        Balance::updateAll(
            [
                'ios' => 25,
                'android' => 15,
                'paypal' => 10,
                'tmallios' => 12,
                'googlepay' => 0,
                'all_coin' => 62,
                'all_topup' => 62,
            ],
            ['id' => 13]
        );

        Balance::updateAll(
            [
                'ios' => 812,
                'android' => 11,
                'paypal' => 24,
                'tmallios' => 15,
                'googlepay' => 0,
                'all_coin' => 862,
                'all_topup' => 862,
            ],
            ['id' => 14]
        );

        PayAccount::deleteAll(['user_id' => 14]);

        $api = '/rpc/live/buy-vip';
        $params = [
            'from_id' => 14,
            'to_id' => 51,
            'price' => 800,
            'rebate' => 160,
            'gift_id' => 1,
            'noble' => 1,
            'title' => '练习生',
        ];
        $data = Request::rpc($api, $params);

        Balance::updateAll([
            'in_ios' => 0,
            'in_android' => 0,
            'in_paypal' => 0,
            'in_tmallios' => 0,
            'in_googlepay' => 0,
            'live_profit' => 0,
            'all_live_profit' => 0,
            'new_live_profit' => 0,
            'new_all_live_profit' => 0,
        ], ['id' => 50]);

        GuildBalance::updateAll([
            'in_ios' => 0,
            'in_android' => 0,
            'in_paypal' => 0,
            'in_tmallios' => 0,
            'in_googlepay' => 0,
            'live_profit' => 0,
            'all_live_profit' => 0,
        ], ['id' => 1000007]);

        $test_cases = [
            [
                'params' => [
                    'from_id' => 13,
                    'to_id' => 50,
                    'gift_id' => 1,
                    'num' => 4,
                    'noble' => 0,
                ],
                'expected' => [
                    'balance' => 38,
                    'income' => 24,
                ],
            ], [
                'params' => [
                    'from_id' => 13,
                    'to_id' => 51,
                    'gift_id' => 1,
                    'num' => 6,
                    'noble' => 0,
                ],
                'expected' => [
                    'balance' => 2,
                    'income' => 36,
                ],
            ], [
                'params' => [
                    'from_id' => 14,
                    'to_id' => 50,
                    'gift_id' => 1,
                    'num' => 8,
                    'noble' => 1,
                ],
                'expected' => [
                    'balance' => 62,
                    'income' => 24,
                ],
            ], [
                'params' => [
                    'from_id' => 14,
                    'to_id' => 51,
                    'gift_id' => 1,
                    'num' => 2,
                    'noble' => 1,
                ],
                'expected' => [
                    'balance' => 62,
                    'income' => 36,
                ],
            ],
        ];

        $api = '/rpc/live/buy-gift';
        foreach ($test_cases as $index => $test_case) {
            $params = $test_case['params'];
            $data = Request::rpc($api, $params);
            $expected = $test_case['expected'];
            $this->assertEquals($data['info']['balance'], $expected['balance']);
            if ($params['to_id'] === 51) {
                $income = GuildBalance::findOne(1000007)->getTotalIncome();
            } else {
                $income = Balance::findOne($params['to_id'])->getTotalIncome();
            }
            $this->assertEquals($income, $expected['income'], "第 $index 个测试用例出错");
        }
    }

    /**
     * 购买赠送直播礼物接口测试
     */
    public function testBuyGift()
    {
        $api = '/rpc/live/buy-gift';
        // 将主播直播相关收益清零
        self::clearLiveProfit(self::TEST_USER_ID);
        // 给测试用户充值测试所需钻石数量
        Balance::updateAll(
            [
                'ios' => 0,
                'android' => self::TEST_GIFT_PRICE * 2,
                'paypal' => 0,
                'tmallios' => 0,
                'googlepay' => 0,
                'all_coin' => self::TEST_GIFT_PRICE * 2,
                'all_topup' => self::TEST_GIFT_PRICE * 2,
            ],
            ['id' => self::TEST_USER2_ID]
        );
        $params = [
            'from_id' => self::TEST_USER2_ID,
            'to_id' => self::TEST_USER_ID,
            'gift_id' => self::TEST_GIFT_ID,
            'num' => 1,
            'noble' => 0,
        ];

        // 测试主播不属于公会时，赠送礼物
        GuildLiveContract::deleteAll(['live_id' => self::TEST_USER_ID]);

        $user_balance = Balance::findOne(['id' => self::TEST_USER2_ID]);
        $all_consumption = $user_balance->all_consumption;

        $data = Request::rpc($api, $params);
        // 验证接口返回值是否正常
        $this->assertEquals(self::TEST_GIFT_PRICE, $data['info']['price']);

        // 测试总消费增加
        $user_balance = Balance::findOne(['id' => self::TEST_USER2_ID]);
        $this->assertEquals($all_consumption + self::TEST_GIFT_PRICE, $user_balance->all_consumption);

        // 验证主播是否获得收益
        $this->validateProfit(self::TEST_GIFT_PRICE);

        // 测试主播属于公会时，赠送礼物
        // 将主播加入公会
        $this->joinGuild(self::TEST_USER_ID, self::$guild_id);
        $data = Request::rpc($api, $params);
        // 验证接口返回值是否正常
        $this->assertEquals(self::TEST_GIFT_PRICE, $data['info']['price']);
        // 验证公会是否获得收益
        $this->validateProfit(self::TEST_GIFT_PRICE, true);
    }

    /**
     * 测试直播提问接口
     *
     * @depends testBuyGift
     */
    public function testAsk()
    {
        $api = '/rpc/live/ask';
        // 给测试用户充值测试所需钻石数量
        Balance::updateAll(
            [
                'ios' => 0,
                'android' => self::TEST_ASK_PRICE * 2,
                'paypal' => 0,
                'tmallios' => 0,
                'googlepay' => 0,
                'all_coin' => self::TEST_ASK_PRICE * 2,
                'all_topup' => self::TEST_ASK_PRICE * 2,
            ],
            ['id' => self::TEST_USER2_ID]
        );
        $params = [
            'from_id' => self::TEST_USER2_ID,
            'to_id' => self::TEST_USER_ID,
            'price' => self::TEST_ASK_PRICE,
            'noble' => 0,
        ];

        // 测试主播不属于公会时，进行提问
        GuildLiveContract::deleteAll(['live_id' => self::TEST_USER_ID]);

        $user_balance = Balance::findOne(['id' => self::TEST_USER2_ID]);
        $all_consumption = $user_balance->all_consumption;
        $data = Request::rpc($api, $params);
        // 验证接口返回值是否正常
        $transaction_id = $data['info']['transaction_id'];
        $this->assertIsInt($transaction_id);
        // 测试总消费不变
        $user_balance = Balance::findOne(['id' => self::TEST_USER2_ID]);
        $this->assertEquals($all_consumption, $user_balance->all_consumption);

        self::$ask_transaction_id = $transaction_id;

        // 测试主播属于公会时，进行提问
        $this->joinGuild(self::TEST_USER_ID, self::$guild_id);
        $data = Request::rpc($api, $params);
        // 验证接口返回值是否正常
        $transaction_id = $data['info']['transaction_id'];
        $this->assertIsInt($transaction_id);
        self::$ask_guild_transaction_id = $transaction_id;
    }

    /**
     * 测试直播付费问答确认接口
     *
     * @depends testAsk
     */
    public function testConfirmAsk()
    {
        // 将主播直播相关收益清零
        self::clearLiveProfit(self::TEST_USER_ID);
        $api = '/rpc/live/confirm-ask';
        $params = [
            'transaction_id' => self::$ask_transaction_id,
            'to_id' => self::TEST_USER_ID,
            'sure' => 1,
        ];

        // 获取提问者的钻石总消耗
        $user_balance = Balance::findOne(['id' => self::TEST_USER2_ID]);
        $all_consumption = $user_balance->all_consumption;

        // 测试主播不属于公会时，回答提问
        $data = Request::rpc($api, $params);
        // 验证接口返回值是否正常
        $this->assertEquals(self::TEST_ASK_PRICE, $data['info']['price']);
        // 验证主播是否获得收益
        $this->validateProfit(self::TEST_ASK_PRICE);
        // 验证用户总消耗增加
        $user_balance = Balance::findOne(['id' => self::TEST_USER2_ID]);
        $this->assertEquals($all_consumption + self::TEST_ASK_PRICE, $user_balance->all_consumption);

        // 测试主播属于公会时，回答提问
        $params['transaction_id'] = self::$ask_guild_transaction_id;
        // 将公会直播相关收益清零
        self::clearLiveProfit(self::$guild_id, true);
        $data = Request::rpc($api, $params);
        // 验证接口返回值是否正常
        $this->assertEquals(self::TEST_ASK_PRICE, $data['info']['price']);
        // 验证公会是否获得收益
        $this->validateProfit(self::TEST_ASK_PRICE, true);
    }

    /**
     * 测试提问取消接口
     */
    public function testCancelAsks()
    {
        $api = '/rpc/live/cancel-asks';

        // 给测试用户充值测试所需钻石数量
        Balance::updateAll(
            [
                'ios' => 0,
                'android' => self::TEST_ASK_PRICE * 2,
                'paypal' => 0,
                'tmallios' => 0,
                'googlepay' => 0,
                'all_coin' => self::TEST_ASK_PRICE * 2,
                'all_topup' => self::TEST_ASK_PRICE * 2,
            ],
            ['id' => self::TEST_USER2_ID]
        );

        $cancel_ask_params = [
            'transaction_ids' => [],
            'to_id' => self::TEST_USER_ID
        ];

        $create_ask_params = [
            'from_id' => self::TEST_USER2_ID,
            'to_id' => self::TEST_USER_ID,
            'price' => self::TEST_ASK_PRICE,
            'noble' => 0,
        ];

        $data = Request::rpc('/rpc/live/ask', $create_ask_params);
        $cancel_ask_params['transaction_ids'] = [$data['info']['transaction_id']];
        $user_balance = Balance::findOne(['id' => self::TEST_USER2_ID]);
        $all_consumption = $user_balance->all_consumption;
        $data = Request::rpc($api, $cancel_ask_params);
        // 验证接口返回值是否正常
        $this->assertIsArray($data);
        // 测试总消费不变
        $user_balance = Balance::findOne(['id' => self::TEST_USER2_ID]);
        $this->assertEquals($all_consumption, $user_balance->all_consumption);

        // 加入公会，测试取消提问
        $this->joinGuild(self::TEST_USER_ID, self::$guild_id);
        $data = Request::rpc('/rpc/live/ask', $create_ask_params);
        $cancel_ask_params['transaction_ids'] = [$data['info']['transaction_id']];
        $user_balance = Balance::findOne(['id' => self::TEST_USER2_ID]);
        $all_consumption = $user_balance->all_consumption;
        $data = Request::rpc($api, $cancel_ask_params);
        // 验证接口返回值是否正常
        $this->assertIsArray($data);
        // 测试总消费不变
        $user_balance = Balance::findOne(['id' => self::TEST_USER2_ID]);
        $this->assertEquals($all_consumption, $user_balance->all_consumption);
    }

    /**
     * 将用户加入公会
     *
     * @param int $user_id
     * @param int $guild_id
     * @throws Exception 加入公会失败时抛出异常
     */
    private function joinGuild(int $user_id, int $guild_id)
    {
        $now = $_SERVER['REQUEST_TIME'];
        $guild_live_contract = new GuildLiveContract();
        $guild_live_contract->guild_id = $guild_id;
        $guild_live_contract->live_id = $user_id;
        $guild_live_contract->contract_start = 0;  // 合同开始默认为 0
        $guild_live_contract->contract_end = 0;  // 合同结束默认为 0
        $guild_live_contract->rate = GuildBalance::RATE;  // 分成比例默认 0.5
        $guild_live_contract->kpi = '';  // kpi 默认无
        $guild_live_contract->type = GuildLiveContract::TYPE_APPLY;
        $guild_live_contract->status = GuildLiveContract::STATUS_CONTRACTION;
        $guild_live_contract->guild_owner = self::TEST_USER_ID;
        $guild_live_contract->guild_name = '三体公会';
        $guild_live_contract->create_time = $now;
        $guild_live_contract->modified_time = $now;
        if (!$guild_live_contract->save()) {
            throw new Exception(MUtils2::getFirstError($guild_live_contract));
        }
    }

    /**
     * 验证直播送礼或提问后，主播/公会收益是否正确
     *
     * @param int $price 礼物/提问价格（单位：钻石）
     * @param bool $is_join_guild 主播是否加入了公会
     */
    private function validateProfit(int $price, $is_join_guild = false)
    {
        if ($is_join_guild) {
            $balance = GuildBalance::getByPk(self::$guild_id);
            $this->assertEquals(0, $balance->live_profit);
            $this->assertEquals(0, $balance->all_live_profit);
        } else {
            // 验证主播是否获得收益
            $rate = Live::getRate(self::TEST_USER_ID);
            $tax = $price * 0.001;
            $profit = ($price / 10 - $tax) * $rate;
            // 收益换算成分为单位
            $profit_in_fen = Balance::profitUnitConversion($profit, Balance::CONVERT_YUAN_TO_FEN);
            $balance = Balance::getByPk(self::TEST_USER_ID);
            if ($_SERVER['REQUEST_TIME'] >= Live::TIMESTAMP_NEW_RATE) {
                $this->assertEquals($profit_in_fen, $balance->new_live_profit);
                $this->assertEquals($profit_in_fen, $balance->new_all_live_profit);
            } else {
                $this->assertEquals($profit_in_fen, $balance->live_profit);
                $this->assertEquals($profit_in_fen, $balance->all_live_profit);
            }
        }
    }

    /**
     * 清除主播直播相关收益
     * 用于送礼或回答提问后方便验证收益
     *
     * @param int $id 主播或公会 ID
     * @param bool $is_guild 是否为公会
     * @throws Exception 清除失败时抛出异常
     */
    private static function clearLiveProfit(int $id, bool $is_guild = false)
    {
        $balance = $is_guild ? GuildBalance::getByPk($id) : Balance::getByPk($id);
        $balance->setAttributes([
            'live_profit' => 0,
            'all_live_profit' => 0,
        ], false);
        if (!$is_guild) {
            $balance->setAttributes([
                'new_live_profit' => 0,
                'new_all_live_profit' => 0,
            ], false);
        }
        if (!$balance->save()) {
            throw new Exception(MUtils2::getFirstError($balance));
        }
    }

    private function assertFollowFansNum()
    {
        // 确认关注数量
        $follow_count = MAttentionUser::find()->where(['user_active' => self::TEST_USER_ID])->count();
        $user_info = Mowangskuser::find()->select('follownum')
            ->where('id = :id', [':id' => self::TEST_USER_ID])->one();
        $this->assertEquals($follow_count, $user_info->follownum);
        // 确认粉丝数量
        $user2_info = Mowangskuser::find()->select('fansnum')
            ->where('id = :id', [':id' => self::TEST_USER4_ID])->one();
        $fans_count = MAttentionUser::find()->where(['user_passtive' => self::TEST_USER4_ID])->count();
        $this->assertEquals($fans_count, $user2_info->fansnum);
    }

    /**
     * 购买赠送直播礼物接口测试
     */
    public function testBuyVip()
    {
        $api = '/rpc/live/buy-vip';
        // 给测试用户充值测试所需钻石数量
        Balance::updateAll(
            [
                'ios' => 0,
                'android' => 10000,
                'paypal' => 0,
                'tmallios' => 0,
                'googlepay' => 0,
                'all_topup' => 10000,
                'all_coin' => 10000,
            ],
            ['id' => self::TEST_USER2_ID]
        );

        $params = [
            'from_id' => self::TEST_USER2_ID,
            'to_id' => self::TEST_USER_ID,
            'gift_id' => self::TEST_GIFT_ID,
            'noble' => 0,
            'title' => '练习生',
            'rebate' => self::TEST_VIP1_PRICE,
            'price' => self::TEST_VIP1_PRICE,
        ];
        $user_balance = Balance::findOne(['id' => self::TEST_USER2_ID]);
        $all_consumption = $user_balance->all_consumption;
        $data = Request::rpc($api, $params);
        $this->assertEquals(0, $data['code']);

        // 测试总消费增加
        $user_balance = Balance::findOne(['id' => self::TEST_USER2_ID]);
        $this->assertEquals($all_consumption + self::TEST_VIP1_PRICE, $user_balance->all_consumption);

        // 测试使用贵族体验卡（开通体验贵族）
        $params = [
            'from_id' => self::TEST_USER2_ID,
            'to_id' => self::TEST_USER_ID,
            'gift_id' => self::TEST_GIFT_ID,
            'noble' => 1,
            'title' => '练习生',
            'rebate' => 0,
            'price' => 0,
            'is_trial' => 1,
        ];
        $user_balance = Balance::findOne(['id' => self::TEST_USER2_ID])->getTotalBalance();
        $noble_balance = PayAccounts::getAccounts(self::TEST_USER2_ID, PayAccount::SCOPE_LIVE)->getTotalBalance();
        $data = Request::rpc($api, $params);
        $this->assertEquals(0, $data['code']);
        $this->assertEquals(0, $data['info']['price']);
        $this->assertEquals($user_balance, $data['info']['balance']);
        $this->assertEquals($noble_balance, $data['info']['live_noble_balance']);
    }

    public function testBuyLuckyGift()
    {
        // 给测试用户充值测试所需钻石数量
        Balance::updateAll(
            [
                'ios' => 0,
                'android' => 3500,
                'paypal' => 0,
                'tmallios' => 0,
                'googlepay' => 0,
                'all_coin' => 3500,
                'all_topup' => 3500,
            ],
            ['id' => self::TEST_USER2_ID]
        );
        $data = Request::rpc('/rpc/live/buy-lucky-gift', [
            'from_id' => self::TEST_USER2_ID,
            'to_id' => self::TEST_USER_ID,
            'gift_id' => 999,
            'price' => 30,
            'num' => 100,
            'title' => '魔法王冠（幸运签）',
            'income' => Balance::profitUnitConversion(300, Balance::CONVERT_YUAN_TO_FEN),
        ]);
        $this->assertEquals(0, $data['code']);
        $this->assertIsArray($data['info']);
        $this->assertIsArray($data['info']);
        $this->assertArrayHasKeys(['transaction_id', 'balance', 'live_noble_balance', 'price'], $data['info']);
        $this->assertEquals(500, $data['info']['balance']);
        $tradelog = TransactionLog::findOne(['id' => $data['info']['transaction_id']]);
        $this->assertEquals(300, $tradelog->income);
    }

    public function testGetExpireBalanceByTime()
    {
        $params = [
            'start_time' => 0,
        ];
        $TEST_API = '/rpc/live/get-expire-balance-by-time';
        $this->assertThrowsWithMessage(HttpException::class, '参数错误',
            function () use ($TEST_API, $params) {
                Request::rpc($TEST_API, $params);
            }
        );

        $params['end_time'] = $_SERVER['REQUEST_TIME'];
        $data = Request::rpc($TEST_API, $params);
        $this->assertArrayHasKeys(['code', 'info'], $data);
        $this->assertEquals(0, $data['code']);
        $this->assertNotEmpty($data['info']);
        $this->assertArrayHasKeys(['user_id', 'total_balance'], $data['info'][0]);
    }

    /**
     * 测试直播回放处理接口
     */
    public function testVideo()
    {
        // 测试参数错误的情况
        $params = [
            'cover' => '',
            'create_time' => 233,
            'description' => '',
            'playback_id' => 'test',
            'record' => '',
            'title' => '',
            'user_id' => '',
            'username' => '',
        ];
        $this->assertThrowsWithMessage(HttpException::class, '参数错误',
            function () use ($params) {
                Request::rpc('/rpc/live/video', $params);
            }
        );

        // 测试非 UPOS 地址
        $params = [
            'cover' => 'testdata/test.jpg',
            'create_time' => 233,
            'description' => '',
            'playback_id' => 'test',
            'record' => 'test.flv',
            'title' => '测试音频标题',
            'user_id' => self::TEST_USER_ID,
            'username' => '测试用户昵称',
        ];
        $redis = Yii::$app->redis;
        $redis->del(KEY_LIVE_VIDEOS);
        $data = Request::rpc('/rpc/live/video', $params);
        $this->assertEquals(0, $data['code']);
        $this->assertEquals(0, $data['info']['sound_id']);
        // 验证数据是否正常
        $sound_data = $redis->lPop(KEY_LIVE_VIDEOS);
        $this->assertEquals(Json::encode($params), $sound_data);

        // 测试 UPOS 地址
        $params = [
            'cover' => 'testdata/test.jpg',
            'create_time' => 233,
            'description' => '直播回放',
            'playback_id' => 'test',
            'record' => 'upos://test/test.m4a',
            'title' => '直播回放测试标题',
            'user_id' => self::TEST_USER_ID,
            'username' => '测试用户昵称',
        ];
        $data = Request::rpc('/rpc/live/video', $params);
        $this->assertEquals(0, $data['code']);
        $this->assertGreaterThan(0, $data['info']['sound_id']);
        // 验证数据是否正常
        $this->assertFalse($redis->lPop(KEY_LIVE_VIDEOS));
        $sound = MSound::find()->where('user_id = :user_id AND soundstr = :soundstr', [
            ':user_id' => self::TEST_USER_ID,
            ':soundstr' => '直播回放测试标题',
        ])->one();
        $this->assertNotNull($sound);
        $this->assertEquals($params['create_time'], $sound->create_time);
        $this->assertEquals(MSound::CHECKED_SOUND_TRANSCODE, $sound->checked);
        $this->assertEquals($params['record'], $sound->oldAttributes['soundurl']);
    }

    public function testBuyGashapon()
    {
        $to_id = self::TEST_GASHAPON_SELLER_ID;
        $buyer = Balance::getByPk(self::TEST_GASHAPON_BUYER_ID);
        $buyer->updateAttributes(['ios' => 1000, 'all_topup' => 1000, 'all_coin' => 1000]);

        $data = Request::rpc('/rpc/live/buy-gashapon', [
            'from_id' => $buyer->id,
            'to_id' => $to_id,
            'goods' => [
                'id' => 22,
                'title' => '超能魔盒 100 连',
                'total_price' => 1000,
                'num' => 100,
            ],
            'gifts' => [
                ['id' => 111, 'title' => '礼物 111', 'price' => 10, 'num' => 30],
                ['id' => 222, 'title' => '礼物 222', 'price' => 20, 'num' => 20],
                ['id' => 333, 'title' => '礼物 333', 'price' => 30, 'num' => 10],
            ],
        ]);
        $this->assertEquals(0, $data['code']);
        $this->assertArrayHasKeys(['balance', 'transaction_id', 'live_noble_balance', 'price'], $data['info']);
    }

    public function testAddBlocklist()
    {
        // 清除黑名单相关数据
        BlackUser::deleteAll('small_id = :small_id AND big_id = :big_id', [
            ':small_id' => Data::TEST_USER_ID,
            ':big_id' => Data::TEST_USER2_ID,
        ]);

        // 测试参数错误
        $api = '/rpc/live/add-blocklist';
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () use ($api) {
            Request::rpc($api, ['user_id' => 0, 'block_user_id' => 0]);
        });

        // 测试拉黑官方账号
        $this->assertThrowsWithMessage(HttpException::class, 'M娘开挂中，地球人无法拉黑她！',
            function () use ($api) {
                Request::rpc($api, [
                    'user_id' => Data::TEST_USER_ID,
                    'block_user_id' => Mowangskuser::OFFICIAL_M_GIRL_USER_ID,
                ]);
            });

        // 测试加入黑名单的用户不存在
        $this->assertThrowsWithMessage(HttpException::class, '用户不存在', function () use ($api) {
            Request::rpc($api, ['user_id' => Data::TEST_USER_ID, 'block_user_id' => 99999999999]);
        });

        // 测试把自己加入自己的黑名单
        $this->assertThrowsWithMessage(HttpException::class, '我不允许你把自己拉黑！',
            function () use ($api) {
                Request::rpc($api, [
                    'user_id' => Data::TEST_USER_ID,
                    'block_user_id' => Data::TEST_USER_ID,
                ]);
            });

        // 测试加入黑名单正常返回
        $params = [
            'user_id' => Data::TEST_USER_ID,
            'block_user_id' => Data::TEST_USER2_ID,
        ];
        $data = Request::rpc($api, $params);
        $this->assertEquals('成功加入黑名单', $data['info']);

        // 测试接口幂等性
        $params = [
            'user_id' => Data::TEST_USER_ID,
            'block_user_id' => Data::TEST_USER2_ID,
        ];
        $data = Request::rpc($api, $params);
        $this->assertEquals('成功加入黑名单', $data['info']);
    }

    public function testRemoveBlocklist()
    {
        // 清除黑名单相关数据
        BlackUser::deleteAll('small_id = :small_id AND big_id = :big_id', [
            ':small_id' => Data::TEST_USER_ID,
            ':big_id' => Data::TEST_USER2_ID,
        ]);

        // 测试参数错误
        $api = '/rpc/live/remove-blocklist';
        $this->assertThrowsWithMessage(HttpException::class, '参数错误', function () use ($api) {
            Request::rpc($api, ['user_id' => 0, 'block_user_id' => 0]);
        });

        // 测试移除黑名单的用户不存在
        $this->assertThrowsWithMessage(HttpException::class, '用户不存在', function () use ($api) {
            Request::rpc($api, ['user_id' => Data::TEST_USER_ID, 'block_user_id' => 99999999999]);
        });

        // 测试把自己加入自己的黑名单
        $this->assertThrowsWithMessage(HttpException::class, '不允许取消拉黑自己',
            function () use ($api) {
                Request::rpc($api, [
                    'user_id' => Data::TEST_USER_ID,
                    'block_user_id' => Data::TEST_USER_ID,
                ]);
            });

        // 测试移除黑名单正常返回
        $params = [
            'user_id' => Data::TEST_USER_ID,
            'block_user_id' => Data::TEST_USER2_ID,
        ];
        $data = Request::rpc($api, $params);
        $this->assertEquals('移除黑名单成功', $data['info']);

        // 测试接口幂等性
        $params = [
            'user_id' => Data::TEST_USER_ID,
            'block_user_id' => Data::TEST_USER2_ID,
        ];
        $data = Request::rpc($api, $params);
        $this->assertEquals('移除黑名单成功', $data['info']);
    }

    public function testBuyWishGoods()
    {
        $buyer = Balance::getByPk(self::TEST_WISH_GOODS_BUYER_ID);
        $buyer->updateAttributes(['ios' => 998, 'all_topup' => 998, 'all_coin' => 998]);

        $data = Request::rpc('/rpc/live/buy-wish-goods', [
            'from_id' => $buyer->id,
            'gift_id' => 3,
            'price' => 998,
            'title' => '测试许愿池',
            'num' => 2,
            'noble' => 0,
        ]);
        $this->assertEquals(0, $data['code']);
        $this->assertArrayHasKeys(['transaction_id', 'balance', 'live_noble_balance', 'price', 'context'], $data['info']);
    }

    public function testSendRebateGifts()
    {
        // 测试新参数格式
        $data = Request::rpc('/rpc/live/send-rebate-gifts', [
            'from_id' => self::TEST_USER_ID,
            'to_id' => 1,
            'gifts' => [
                [
                    'id' => 1,
                    'title' => '星之辉',
                    'price' => 5,
                    'num' => 1,
                ],
            ],
            'user_agent' => 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36',
            'ip' => '240e:398:6580:1560:3957:7ac5:f984:fa53',
            'live_open_log_id' => '637f0610b9baab39e538c6ed',
        ]);
        $this->assertEquals(0, $data['code']);
        $this->assertArrayHasKeys(['transaction_id', 'balance', 'live_noble_balance'], $data['info']);

        // 测试兼容老的参数格式
        $gift = Gift::findOne(['type' => Gift::TYPE_LIVE_REBATE_GIFT]);
        $this->assertNotNull($gift);
        $data = Request::rpc('/rpc/live/send-rebate-gifts', [
            'from_id' => self::TEST_USER_ID,
            'to_id' => 1,
            'gift_id' => $gift->id,
            'num' => 1,
            'user_agent' => 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36',
            'ip' => '240e:398:6580:1560:3957:7ac5:f984:fa53',
            'live_open_log_id' => '637f0610b9baab39e538c6ed',
        ]);
        $this->assertEquals(0, $data['code']);
        $this->assertArrayHasKeys(['transaction_id', 'balance', 'live_noble_balance'], $data['info']);
    }

    private static function mockDramaApi()
    {
        Tools::registerRpcApiResponseFunc('/rpc/api/get-drama-price', function ($params) {
            switch ($params['drama_id']) {
                case 9888:
                    return [
                        'code' => 0,
                        'info' => [
                            'drama_id' => $params['drama_id'],
                            'user_id' => 2,
                            'price' => 299,
                            'rate' => 0.35,
                            'name' => '杀破狼第一季',
                            'type' => Drama::PAY_TYPE_DRAMA,
                            'is_lossless' => false,
                        ],
                    ];
                case 15861:
                    return [
                        'code' => 0,
                        'info' => [
                            'drama_id' => $params['drama_id'],
                            'user_id' => 3,
                            'price' => 259,
                            'rate' => 0.35,
                            'name' => '魔道祖师第一季',
                            'type' => Drama::PAY_TYPE_DRAMA,
                            'is_lossless' => false,
                        ],
                    ];
            }
        });
    }

    public function testBuyGoods()
    {
        $buyer = Balance::getByPk(self::TEST_USER_ID);
        $buyer->updateAttributes(['ios' => 998, 'all_topup' => 998, 'all_coin' => 998]);

        $data = Request::rpc('/rpc/live/buy-goods', [
            'buyer_id' => $buyer->id,
            'receiver_id' => 1,
            'goods_type' => TransactionFormLive::GOODS_TYPE_RED_PACKAGE,
            'goods' => [
                [
                    'id' => 3,
                    'title' => '测试礼物红包',
                    'price' => 997,
                    'num' => 1,
                ],
            ],
            'noble' => 0,
            'user_agent' => 'MissEvanApp/4.8.9 (iOS;14.8.1;iPhone13,4)',
            'ip' => '************',
            'live_open_log_id' => '636a12e7b9baab6194293c65',
        ]);
        $this->assertEquals(0, $data['code']);
        $this->assertArrayHasKeys(['transaction_id', 'balance', 'live_noble_balance', 'price', 'context'], $data['info']);

        self::mockDramaApi();
        $buyer->updateAttributes(['ios' => 299, 'all_topup' => 0, 'all_coin' => 0]);
        $data = Request::rpc('/rpc/live/buy-goods', [
            'buyer_id' => $buyer->id,
            'receiver_id' => 0,
            'goods_type' => TransactionFormLive::GOODS_TYPE_LUCKY_BAG,
            'package_info' => [
                'id' => 36,
                'title' => '广播剧福袋',
                'price' => 299,
                'num' => 1,
            ],
            'goods' => [
                [
                    'id' => 9888,
                    'title' => '杀破狼第一季',
                    'price' => 299,
                    'transaction_type' => GoodsForm::TRANSACTION_TYPE_DRAMA,
                    'num' => 1,
                ],
            ],
            'user_agent' => 'MissEvanApp/4.8.9 (iOS;14.8.1;iPhone13,4)',
            'ip' => '************',
            'live_open_log_id' => '636a12e7b9baab6194293c65',
        ]);
        $this->assertEquals(0, $data['code']);
        $this->assertArrayHasKeys(['transaction_id', 'balance', 'price', 'context'], $data['info']);
    }

    private function prepareRefundGoods()
    {
        $tradelog = new TransactionLog([
            'from_id' => self::TEST_USER2_ID,
            'to_id' => 0,
            'gift_id' => 6,
            'title' => '广播剧福袋--杀破狼 × 1',
            'ios_coin' => 299,
            'all_coin' => 299,
            'income' => 29.9,
            'rate' => Live::LIVE_RATE,
            'attr' => TransactionLog::ATTR_LIVE_BUY_LUCKY_BAG,
            'type' => TransactionLog::TYPE_LIVE,
            'status' => TransactionLog::STATUS_SUCCESS,
            'num' => 1,
        ]);
        $tradelog->tax = $tradelog->calcTax(['ios' => 299]);
        if (!$tradelog->save()) {
            throw new Exception(MUtils2::getFirstError($tradelog));
        }
        $itemslog = new TransactionItemsLog([
            'goods_id' => 9888,
            'goods_title' => '杀破狼第一季',
            'goods_price' => 299,
            'goods_num' => 1,
            'user_id' => self::TEST_USER2_ID,
            'status' => TransactionLog::STATUS_SUCCESS,
            'type' => TransactionLog::TYPE_LIVE,
            'tid' => $tradelog->id,
        ]);
        $itemslog->more_detail = [
            'transaction_type' => GoodsForm::TRANSACTION_TYPE_DRAMA,
            'common_coins' => ['ios' => 299],
            'successful_goods_num' => 1,
        ];
        if (!$itemslog->save()) {
            throw new Exception(MUtils2::getFirstError($itemslog));
        }

        return $tradelog->id;
    }

    private function cleanupRefundGoods(int $transaction_id)
    {
        TransactionLog::deleteAll(['id' => $transaction_id]);
        TransactionItemsLog::deleteAll(['tid' => $transaction_id]);
    }

    public function testRefundGoods()
    {
        $transaction_id = $this->prepareRefundGoods();
        try {
            $data = Request::rpc('/rpc/live/refund-goods', [
                'transaction_id' => $transaction_id,
                'goods_type' => TransactionFormLive::GOODS_TYPE_LUCKY_BAG,
                'goods' => [
                    [
                        'id' => 9888,
                        'title' => '杀破狼第一季',
                        'price' => 299,
                        'num' => 1,
                        'transaction_type' => GoodsForm::TRANSACTION_TYPE_DRAMA,
                    ],
                ],
            ]);
            $this->assertEquals(0, $data['code']);
            $this->assertArrayHasKeys(['transaction_id', 'balance'], $data['info']);
        } finally {
            $this->cleanupRefundGoods($transaction_id);
        }
    }
}
