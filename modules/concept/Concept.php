<?php

namespace app\modules\concept;

use app\components\util\Equipment;
use yii\web\HttpException;
use yii\web\Response;
use Yii;

class Concept extends \yii\base\Module
{
    public $controllerNamespace = 'app\modules\concept\controllers';

    public function init()
    {
        parent::init();

        // custom initialization code goes here
    }

    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }
        if (!Equipment::isConceptVersion()) {
            // 仅概念版可访问该模块下接口
            throw new HttpException(400, '非法请求');
        }
        return true;
    }
}
