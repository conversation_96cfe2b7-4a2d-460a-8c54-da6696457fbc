<?php

namespace app\modules\concept\controllers;

use app\components\util\Equipment;
use app\components\util\MUtils;
use app\middlewares\Controller;
use app\models\Blacklist;
use app\models\Drama;
use app\models\MPersonaModuleElement;
use app\models\MTab;
use app\models\Persona;
use app\models\YouMightLikeModule;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\helpers\Json;

class SiteController extends Controller
{
    // 白天或黑夜样式 0 为白天，1 为黑夜
    const ICON_DARK = 1;
    const ICON_NORMAL = 0;

    public function init()
    {
        parent::init();
    }

    /**
     * @api {get} /concept/site/icons 概念版首页小图标
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/concept/site/icons
     * @apiSampleRequest /concept/site/icons
     * @apiVersion 0.1.0
     * @apiName icons
     * @apiGroup concept/site
     * @apiParam {Number} [persona_id=3] 用户画像
     *
     * @apiSuccess {Boolean} success 请求状态
     * @apiSuccess {Number} code 状态码
     * @apiSuccess {Object} info  请求数据详情
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "live_module": {  // 模块被隐藏或不存在时返回 null
     *           "title": "正在直播"
     *         },
     *         "icons": [
     *           {
     *             "id": 3,
     *             "url": "missevan://event",
     *             "title": "活动",
     *             "icon": "http://static.missevan.com/profile/icon01.png",  // 日间图标
     *             "dark_icon": "http://static.missevan.com/profile/icon01.png"  // 黑夜图标
     *           },
     *           {
     *             "id": 4,
     *             "url": "https://m.missevan.com/summerdrama",
     *             "title": "精品周更",
     *             "icon": "http://static.missevan.com/profile/icon01.png",
     *             "dark_icon": "http://static.missevan.com/profile/icon01.png"
     *           },
     *           {
     *             "id": 1,
     *             "url": "missevan://drama",
     *             "title": "广播剧",
     *             "icon": "http://static.missevan.com/profile/icon01.png",
     *             "dark_icon": "http://static.missevan.com/profile/icon01.png"
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionIcons(int $persona_id = Persona::TYPE_GIRL)
    {
        $equipment = Yii::$app->equip;
        $os = Equipment::iOS;
        if ($equipment->isAndroidOrHarmonyOS()) {
            $os = Equipment::Android;
        }
        $icons = Yii::$app->redis->get(KEY_APP_CONCEPT_HOMEPAGE_ICONS);
        $icons = $icons ? Json::decode($icons) : [];
        if (!empty($icons)) {
            $icons = array_reduce($icons, function ($ret, $icon) use ($os, $equipment) {
                $icon['icon'] = self::getHomepageIcon($icon, self::ICON_NORMAL);
                $icon['dark_icon'] = self::getHomepageIcon($icon, self::ICON_DARK);
                $select = $icon['select'];
                unset($icon['select'], $icon['dark'], $icon['normal']);
                $icon['url'] = MUtils::getUsableAppLink($icon['url']);
                if (self::isEquipMatchedWithIcon($select, $os)) {
                    $ret[] = $icon;
                }
                return $ret;
            }, []);
        }
        $is_game_center_blocked = Blacklist::model()->isGameCenterBlocked();
        $icons = array_reduce($icons, function ($ret, $icon) use ($is_game_center_blocked) {
            if ($is_game_center_blocked && preg_match('/^missevan:\/\/game\/.*/', $icon['url'])) {
                // 隐藏对应渠道的“游戏中心”图标
                return $ret;
            }
            $ret[] = $icon;
            return $ret;
        }, []);
        // 接口返回信息
        $info = ['icons' => $icons];
        $persona_module = $persona_id & Persona::PERSONA_MODULE_MASK;
        $live_module = MPersonaModuleElement::getLiveModule($persona_module);
        $live_module_info = null;
        if ($live_module) {
            $live_module_info = ['title' => $live_module->title ?: '正在直播'];
        }
        $info['live_module'] = $live_module_info;
        return $info;
    }

    /**
     * @param $icon
     * @param integer $dark 黑夜或白天模式（0 白天，1 为黑夜）
     *
     * @return string
     */
    private static function getHomepageIcon($icon, $dark): string
    {
        $hash_key = $dark ? 'dark' : 'normal';
        return Yii::$app->storage->getUrl(OSS_DIR_APP_HOMEPAGE_ICONS,
            "{$hash_key}/{$icon[$hash_key]}");
    }

    /**
     * 设备是否与图标匹配
     *
     * @param integer $icon_select 图标标志（1 位属于安卓，2 位属于 iOS）
     * @param integer $os 设备类型（1 安卓，2 iOS）
     *
     * @return boolean
     */
    private static function isEquipMatchedWithIcon($icon_select, $os)
    {
        // icon_select 的 1 位为安卓图标（icon_select & 1 != 0），2 位为 iOS 图标（icon_select & 2 != 0）
        if ($os <= 0) {
            return false;
        }
        return ($icon_select & (1 << ($os - 1))) !== 0;
    }

    /**
     * @api {get} /concept/site/popularity-rank 人气月榜
     * @apiDescription 使用女性画像推荐模块人气月榜数据
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/concept/site/popularity-rank
     * @apiSampleRequest concept/site/popularity-rank
     *
     * @apiVersion 0.1.0
     * @apiName popularity-rank
     * @apiGroup concept/site
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "module_id": 8,
     *         "title": "燃爆全场",
     *         "type": 2,  // 元素类型，1：音单；2：剧集；3：单音
     *         "elements": [
     *           {
     *             "id": 142732,
     *             "title": "加特林",
     *             "intro": "加特林吃饭",
     *             "front_cover": "http://www.test.com/mimages/201711/15/test.jpg",
     *             "type": 1,
     *             "music_count": 16,
     *             "view_count": 135718,
     *             "sort": 1
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionPopularityRank()
    {
        // 使用女性画像下推荐模块中人气月榜模块的数据
        $persona = Persona::TYPE_GIRL;
        [$modules, $from_cache] = YouMightLikeModule::getPersonModules($persona);
        $rank_module = null;
        foreach ($modules as $module) {
            if ($module['style'] === MPersonaModuleElement::MODULE_STYLE_TOP_PLAY_STYLE) {
                $rank_module = $module;
                break;
            }
        }
        if ($from_cache && $rank_module && MPersonaModuleElement::MODULE_TYPE_DRAMA === $rank_module['type']) {
            // 排行榜为剧集时获取实时用户剧集购买的状态
            Drama::checkNeedPay($rank_module['elements'], Yii::$app->user->id);
        }
        return $rank_module;
    }

    /**
     * @api {get} /concept/site/tabs 获取 App 首页 tabs
     * @apiDescription 概念版 App 首页目前仅返回直播、推荐、广播剧 3 个 Tab \
     * 推荐（ID = 1），直播（ID = 3） 的 ID 是固定的 \
     * 当接口返回值里，多个 Tab 字段里有 active = 1（默认选择）的情况，客户端需选取最后一个作为默认显示
     *
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/concept/site/tabs
     * @apiSampleRequest concept/site/tabs
     *
     * @apiVersion 0.1.0
     * @apiName tabs
     * @apiGroup concept/site
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "tabs": [
     *           {
     *             "id": 3,
     *             "title": "直播",
     *             "url": "missevan://live"
     *           },
     *           {
     *             "id": 1,
     *             "title": "推荐",
     *             "active": 1,
     *             "url": "missevan://homepage"
     *           },
     *           {
     *             "id": 101,
     *             "title": "广播剧",
     *             "url": "missevan://catalog/drama/89"
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionTabs()
    {
        $data = MTab::getHomepageTabs();
        // NOTICE: 概念版仅返回直播、推荐、广播剧 3 个 Tab，因广播剧 tab id 可能产生变化，故用 title 做匹配过滤
        // TODO: 之后应考虑在 m_tab 表使用不同的 position 来区分并获取概念版首页 tab
        $need_tab_titles = ['直播', '推荐', '广播剧'];
        $concept_tabs = array_reduce($data['tabs'], function ($ret, $tab) use ($need_tab_titles) {
            if (in_array($tab['title'], $need_tab_titles)) {
                $ret[] = $tab;
            }
            return $ret;
        }, []);
        if (empty($concept_tabs)) {
            Yii::error('概念版 App 首页 Tab 数据异常', __METHOD__);
            // PASS: 客户端做了保底，返回 tabs 为空数组用户可正常使用
        }
        return ['tabs' => $concept_tabs];
    }

    /**
     * @api {get} /concept/site/weekly-dramas 获取精品周更数据
     * @apiDescription 首页最多获取 20 条数据，具体数量看后台设置了多少剧集
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/concept/site/weekly-dramas
     * @apiSampleRequest concept/site/weekly-dramas
     *
     * @apiVersion 0.1.0
     * @apiName weekly-dramas
     * @apiGroup concept/site
     *
     * @apiSuccess {Boolean} success true or false
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "info": [
     *         {
     *           "id": 9888,
     *           "name": "杀破狼",
     *           "cover": "http://www.test.com/dramacoversmini/201604/15/233.jpg",
     *           "author": "799",
     *           "catalog": "1233",
     *           "pay_type": 2,
     *           "date": 1
     *         },
     *         {
     *           "id": 8540,
     *           "name": "加特林",
     *           "cover": "http://www.test.com/mimages/201712/12/233.jpg",
     *           "author": "达咩",
     *           "catalog": "灵异",
     *           "pay_type": 2,
     *           "date": 2
     *         }
     *       ]
     *     }
     */
    public function actionWeeklyDramas()
    {
        $dramas = Drama::getWeeklyDrama();
        if (empty($dramas)) {
            return [];
        }
        // 首页最多可获取 20 条数据
        return array_slice($dramas, 0, PAGE_SIZE_20);
    }
}
