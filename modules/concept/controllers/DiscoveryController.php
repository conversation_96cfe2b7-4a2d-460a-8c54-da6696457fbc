<?php

namespace app\modules\concept\controllers;

use app\middlewares\Controller;
use app\models\Discovery;

class DiscoveryController extends Controller
{
    public function init()
    {
        parent::init();
    }

    /**
     * @api {get} /concept/discovery/list 发现页面
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/concept/discovery/list
     * @apiSampleRequest /concept/discovery/list
     * @apiVersion 0.1.0
     * @apiName list
     * @apiGroup /concept/discovery
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code Error code
     * @apiSuccess {Object} info Result of request
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         [{
     *           "id": 12,
     *           "title": "直播间",
     *           "icon": "http://www.test.com/profile/icon01.png",  // 日间图标
     *           "dark_icon": "http://www.test.com/profile/icon01.png",  // 黑夜图标
     *           "intro": "M娘正在直播，快去围观吧",
     *           "link": {
     *             "scene": 3,
     *             "url": "missevan://live"
     *           }
     *         }],
     *         [{
     *           "id": 3,
     *           "title": "广播剧",
     *           "icon": "http://www.test.com/profile/icon01.png",
     *           "dark_icon": "http://www.test.com/profile/icon01.png",
     *           "intro": "",
     *           "link": {
     *             "scene": 1,
     *             "url": "missevan://catalog/drama/89"
     *           }
     *         }, {
     *           "id": 4,
     *           "title": "鱼干任务",
     *           "icon": "http://www.test.com/profile/icon01.png",
     *           "dark_icon": "http://www.test.com/profile/icon01.png",
     *           "intro": "",
     *           "link": {
     *             "scene": 3,
     *             "url": "missevan://task"
     *           }
     *         }]
     *       ]
     *     }
     */
    public function actionList()
    {
        $icon_groups = Discovery::getIconsGroup();
        // 去除指定的入口
        $filter_icon_titles = ['新闻站', '手机恋人', '免流服务'];
        $result = [];
        foreach ($icon_groups as $icon_group) {
            $icon_group = array_reduce($icon_group, function ($ret, $icon) use ($filter_icon_titles) {
                if (!in_array($icon['title'], $filter_icon_titles)) {
                    $ret[] = $icon;
                }
                return $ret;
            }, []);
            if (!empty($icon_group)) {
                $result[] = $icon_group;
            }
        }
        return $result;
    }
}
