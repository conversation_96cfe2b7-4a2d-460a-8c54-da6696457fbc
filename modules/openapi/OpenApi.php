<?php

namespace app\modules\openapi;

use Yii;
use yii\base\Module;
use yii\web\HttpException;

class OpenApi extends Module
{
    public $controllerNamespace = 'app\modules\openapi\controllers';

    public function init()
    {
        parent::init();

        // custom initialization code goes here
        Yii::$app->set('errorHandler', [
            'class' => ErrorHandler::class,
        ]);
        Yii::$app->errorHandler->register();
        Yii::$app->request->enableCsrfValidation = false;
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
    }

    private static function getSecret(string $appkey)
    {
        return Yii::$app->params['service']['openapi_key_secret_map'][$appkey] ?? '';
    }

    private static function buildSign(array $params, string $secret)
    {
        unset($params['sign']);

        ksort($params);
        $str_to_sign = http_build_query($params, '', null, PHP_QUERY_RFC3986);

        return md5($str_to_sign . $secret);
    }

    /**
     * @link https://info.bilibili.co/pages/viewpage.action?pageId=378303862 Open 业务接入说明
     * @link https://info.bilibili.co/pages/viewpage.action?pageId=378304293 Open Sign 算法
     * @link https://git.bilibili.co/platform/go-common/-/blob/master/library/net/http/blademaster/middleware/verify/verify.go
     *
     * @return bool
     */
    private static function isValidSign()
    {
        if (Yii::$app->request->getIsPost() && Yii::$app->request->getQueryParam('sign', '') === '') {
            $params = Yii::$app->request->getBodyParams();
        } else {
            $params = Yii::$app->request->getQueryParams();
        }

        if (($params['sign'] ?? '') === ''
                || ($params['appkey'] ?? '') === ''
                || ($params['ts'] ?? '') === ''  // TODO: check time
        ) {
            return false;
        }

        $secret = self::getSecret($params['appkey']);
        if (!$secret) {
            return false;
        }

        return self::buildSign($params, $secret) === $params['sign'];
    }

    public function afterAction($action, $result)
    {
        $result = parent::afterAction($action, $result);
        return [
            'code' => 0,
            'message' => 'success',
            'data' => $result,
        ];
    }

    public function createController($route)
    {
        if (!self::isValidSign()) {
            throw new HttpException(403, '请求不合法');
        }
        return parent::createController($route);
    }

}
