<?php

namespace app\modules\openapi\controllers;

use app\components\util\SSOClient;
use app\models\BiliVipMaoerBenefitMenu;
use app\models\BiliVipMaoerUserBenefit;
use app\models\MobileNumber;
use app\models\Mowangskuser;
use Exception;
use libphonenumber\PhoneNumberUtil;
use missevan\util\MUtils;
use Yii;
use yii\db\Transaction;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use yii\web\Controller;
use yii\web\HttpException;
use yii\web\Request;

class BiliVipController extends Controller
{
    const CODE_SUCCESS = 0;  // 成功
    const CODE_WRONG_SIGN = -1;  // 签名错误
    const CODE_ERROR_PARAMS = -2;  // 参数错误
    const CODE_MOBILE_NOT_REGISTERED = -3;  // 手机号未注册
    const CODE_ALREADY_DELIVERED = -4;  // 权益已领取
    const CODE_NOT_QUALIFIED = -5;  // 不满足领取条件
    const CODE_STOCK_NOT_ENOUGH = -6;  // 库存不足
    const CODE_REQUEST_TOO_FAST = -7;  // 操作过快
    const CODE_DELIVER_FAILED = -8;  // 权益发放失败
    const CODE_DUPLICATE_REQUEST_ID = -9;  // request_id 已存在

    // 用户属性
    const USER_ATTR_QUALIFIED = 1;  // 用户满足领取条件
    const USER_ATTR_BANNED = 2;  // 用户已被封禁（不可领取权益）

    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'check-mobile' => ['post'],
                'check-stock' => ['post'],
                'deliver-benefit' => ['post'],
            ],
        ];

        return $behaviors;
    }

    /**
     * @api {post} /openapi/bili-vip/check-mobile 检查手机号是否已注册猫耳
     *
     * @apiName check-mobile
     * @apiGroup /openapi/bili-vip/
     *
     * @apiParam {String} mobile 手机号，例 13913903851、4387958768
     * @apiParam {String} region 手机国际区号，例 +86、+1
     * @apiParam {String} request_id 请求唯一标识（不超过 32 个字符）
     *
     * @apiSuccess {Number} code 返回码
     * @apiSuccess {String} message 成功返回成功，若是失败则返回错误信息
     * @apiSuccess {Object} data 具体数据响应
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200
     *     {
     *       "code": 0,
     *       "message": "success",
     *       "data": {
     *         "registered": true,  // 是否已注册（true 已注册、false 未注册）
     *         "attr": 1,  // 注册用户的属性：1 满足领取条件，2 用户被封禁（不可领取权益）
     *       }
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 400
     *     {
     *       "code": -1,  // 签名错误
     *       "message": "请求不合法",
     *       "data": null
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 400
     *     {
     *       "code": -2,
     *       "message": "不合法的手机号",
     *       "data": null
     *     }
     */
    public function actionCheckMobile()
    {
        [, $user] = self::getUser(Yii::$app->request);
        if (!$user) {
            return ['registered' => false];
        }
        return [
            'registered' => true,
            'attr' => ($user->isBanLogin() || $user->isBanTopupAndConsume())
                ? self::USER_ATTR_BANNED
                : self::USER_ATTR_QUALIFIED,
        ];
    }

    /**
     * @api {post} /openapi/bili-vip/check-stock 检查权益库存
     *
     * @apiName check-stock
     * @apiGroup /openapi/bili-vip/
     *
     * @apiParam {number=1} benefit_type 权益类型（1 有赞商城优惠券）
     * @apiParam {Number} benefit_id 权益项 ID
     * @apiParam {String} request_id 请求唯一标识（不超过 32 个字符）
     *
     * @apiSuccess {Number} code 返回码
     * @apiSuccess {String} message 成功返回成功，若是失败则返回错误信息
     * @apiSuccess {Object} data 具体数据响应
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200
     *     {
     *       "code": 0,
     *       "message": "success",
     *       "data": {
     *         "daily_limit": 29888,  // 日库存限制（-1 表示没有库存限制）
     *         "stock": 10568  // 剩余可用库存（-1 表示没有库存限制）
     *       }
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 400
     *     {
     *       "code": -1,  // 签名错误
     *       "message": "请求不合法",
     *       "data": null
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 400
     *     {
     *       "code": -2,
     *       "message": "未知的权益项",
     *       "data": null
     *     }
     */
    public function actionCheckStock()
    {
        $benefit_type = (int)Yii::$app->request->post('benefit_type');
        $benefit_id = (int)Yii::$app->request->post('benefit_id');

        $item = BiliVipMaoerBenefitMenu::getBenefitItem($benefit_type, $benefit_id);
        if (!$item) {
            throw new HttpException(400, '未知的权益项', self::CODE_ERROR_PARAMS);
        }

        return [
            'daily_limit' => $item->daily_stock_limit,
            'stock' => $item->getStock(),
        ];
    }

    /**
     * @api {post} /openapi/bili-vip/deliver-benefit 发放权益
     *
     * @apiName deliver-benefit
     * @apiGroup /openapi/bili-vip/
     *
     * @apiParam {Number} bili_uid B 站用户 UID
     * @apiParam {String} mobile 手机号，例 13913903851、4387958768
     * @apiParam {String} region 手机国际区号，例 +86、+1
     * @apiParam {number=1} benefit_type 权益类型（1 有赞商城优惠券）
     * @apiParam {Number} benefit_id 权益项 ID
     * @apiParam {String} request_id 请求唯一标识（不超过 32 个字符）
     * @apiParam {String} [extra] 额外信息（JSON 格式字符串），例 {"bili_vip_period_start":1747742400, "bili_vip_period_end":1750435200}
     *
     * @apiSuccess {Number} code 返回码
     * @apiSuccess {String} message 成功返回成功，若是失败则返回错误信息
     * @apiSuccess {Object} data 具体数据响应
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200
     *     {
     *       "code": 0,
     *       "message": "success",
     *       "data": {
     *         "maoer_uid": 3857777,  // 发放的猫耳用户 UID
     *         "deliver_record_id": 43504  // 权益发放记录 ID
     *       }
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 400
     *     {
     *       "code": -1,  // 签名错误
     *       "message": "请求不合法",
     *       "data": null
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 400
     *     {
     *       "code": -2,
     *       "message": "不合法的手机号",
     *       "data": null
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 400
     *     {
     *       "code": -2,
     *       "message": "未知的权益项",
     *       "data": null
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 403
     *     {
     *       "code": -3,
     *       "message": "当前手机号未注册猫耳",
     *       "data": null
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 403
     *     {
     *       "code": -4,
     *       "message": "不可重复领取",
     *       "data": null
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 403
     *     {
     *       "code": -5,
     *       "message": "不满足领取条件",  // 例猫耳用户已被封禁
     *       "data": null
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 403
     *     {
     *       "code": -6,
     *       "message": "库存不足",
     *       "data": null
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 403
     *     {
     *       "code": -7,
     *       "message": "操作过快，请稍候重试",
     *       "data": null
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 200 约定返回 HTTP 200，避免不断重试
     *     {
     *       "code": -8,
     *       "message": "权益发放失败，请稍候重试",
     *       "data": null
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 400
     *     {
     *       "code": -9,
     *       "message": "request_id 已存在",
     *       "data": null
     *     }
     */
    public function actionDeliverBenefit()
    {
        $request = Yii::$app->request;
        $request_id = trim(Yii::$app->request->post('request_id'));
        $benefit_type = (int)Yii::$app->request->post('benefit_type');
        $benefit_id = (int)Yii::$app->request->post('benefit_id');
        $bili_uid = trim($request->post('bili_uid'));
        $extra = Json::decode(trim($request->post('extra'))) ?: [];

        if (!$bili_uid) {
            throw new HttpException(400, 'B站用户 ID 不能为空', self::CODE_ERROR_PARAMS);
        }
        if (!$extra || !array_key_exists('bili_vip_period_start', $extra) || !array_key_exists('bili_vip_period_end', $extra)
                || $extra['bili_vip_period_end'] <= $extra['bili_vip_period_start']
                // 只允许当前生效的大会员进行发放权益，如果大会员侧需要补发历史权益，需要走工单方式
                || $extra['bili_vip_period_end'] <= $_SERVER['REQUEST_TIME'] || $extra['bili_vip_period_start'] > $_SERVER['REQUEST_TIME']) {
            throw new HttpException(400, 'extra 不合法', self::CODE_ERROR_PARAMS);
        }

        if (!$request_id || strlen($request_id) > 32 || !ctype_alnum($request_id)) {
            throw new HttpException(400, 'request_id 不合法', self::CODE_ERROR_PARAMS);
        }
        if (!$item = BiliVipMaoerBenefitMenu::getBenefitItem($benefit_type, $benefit_id)) {
            throw new HttpException(400, '未知的权益项', self::CODE_ERROR_PARAMS);
        }
        if ($item->isOutOfStock()) {
            throw new HttpException(403, '库存不足', self::CODE_STOCK_NOT_ENOUGH);
        }

        /**
         * @var MobileNumber $mobile_info
         * @var Mowangskuser $user
         */
        [$mobile_info, $user] = self::getUser($request);
        if (!$user) {
            throw new HttpException(403, '当前手机号未注册猫耳', self::CODE_MOBILE_NOT_REGISTERED);
        }
        if ($user->isBanLogin() || $user->isBanTopupAndConsume()) {
            throw new HttpException(403, '不满足领取条件', self::CODE_NOT_QUALIFIED);
        }

        $benefit = BiliVipMaoerUserBenefit::findOne(['request_id' => $request_id]);
        if ($benefit) {
            switch ($benefit->status) {
                case BiliVipMaoerUserBenefit::STATUS_SUCCESS:
                case BiliVipMaoerUserBenefit::STATUS_EXPIRED:
                    return [
                        'maoer_uid' => $benefit->maoer_uid,
                        'deliver_record_id' => $benefit->id,
                    ];
                case BiliVipMaoerUserBenefit::STATUS_FAILED:
                    throw new HttpException(200, '权益发放失败', self::CODE_DELIVER_FAILED);
            }
        } else {
            $benefit = BiliVipMaoerUserBenefit::initiate($item, $mobile_info, $user->id, $bili_uid, [
                'request_id' => $request_id,
                'bili_vip_period_start' => $extra['bili_vip_period_start'],
                'bili_vip_period_end' => $extra['bili_vip_period_end'],
            ]);
            if (!$benefit->save()) {
                throw new Exception(MUtils::getFirstError($benefit));
            }
        }
        if (BiliVipMaoerUserBenefit::checkIsDelivered($item, $benefit)) {
            throw new HttpException(403, '不可重复领取', self::CODE_ALREADY_DELIVERED);
        }

        /**
         * @var Transaction $transaction
         */
        $transaction = null;
        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_BILI_VIP_MAOER_BENEFIT_DELIVERY, $item->benefit_type, $item->id, $user->id);
        try {
            if (!$redis->lock($lock, HALF_MINUTE)) {
                throw new HttpException(403, '操作过快，请稍候重试', self::CODE_REQUEST_TOO_FAST);
            }
            $transaction = BiliVipMaoerUserBenefit::getDb()->beginTransaction();
            $benefit->deliver($item, function (BiliVipMaoerUserBenefit $benefit, string|null $error_msg) use ($item) {
                if ($error_msg) {
                    throw new Exception($error_msg);
                }
                $benefit->status = BiliVipMaoerUserBenefit::STATUS_SUCCESS;
                if (!$benefit->save()) {
                    Yii::error(get_class($benefit) . ' save error: ' . MUtils::getFirstError($benefit), __METHOD__);
                    // PASS
                }
            });
            $transaction->commit();
            $transaction = null;

            $item->deductStock()->sendDeliverNotice($benefit);
            return [
                'maoer_uid' => $benefit->maoer_uid,
                'deliver_record_id' => $benefit->id,
            ];
        } catch (Exception $e) {
            if ($transaction) {
                $transaction->rollBack();
            }
            if ($e->getCode() === SSOClient::CODE_YOUZAN_ACCOUNT_NOT_EXIST) {
                // 有赞账号不存在，需要用户在进入有赞商城时才进行权益发放
                Yii::warning("有赞账号不存在: user_id={$benefit->maoer_uid}", __METHOD__);
                return [
                    'maoer_uid' => $benefit->maoer_uid,
                    'deliver_record_id' => $benefit->id,
                ];
            }

            if (($e instanceof HttpException && $e->statusCode >= 500) || !($e instanceof HttpException)) {
                Yii::error("大会员x猫耳权益发放异常: {$e->getMessage()}", __METHOD__);
                // 权益已发放完毕（可能为扣减库存/发放通知异常）
                if (!$transaction) {
                    return [
                        'maoer_uid' => $benefit->maoer_uid,
                        'deliver_record_id' => $benefit->id,
                    ];
                }
                // 其它异常
                $benefit->more += ['error' => $e->getMessage()];
                $benefit->status = BiliVipMaoerUserBenefit::STATUS_FAILED;
                if (!$benefit->save()) {
                    Yii::error(get_class($benefit) . ' save error: ' . MUtils::getFirstError($benefit), __METHOD__);
                    // PASS
                }
                // 与大会员侧约定，返回 HTTP 200，避免 HTTP 500 时大会员侧会不断重试
                throw new HttpException(200, '权益发放失败', self::CODE_DELIVER_FAILED);
            }
            throw $e;
        } finally {
            $redis->unlock($lock);
        }
    }

    public static function getUser(Request $request)
    {
        $mobile = trim($request->post('mobile'));
        $region = trim($request->post('region'));
        if (!$mobile || !$region) {
            throw new HttpException(400, '手机号或国际区号不能为空', self::CODE_ERROR_PARAMS);
        }

        $national_mobile = $country_code = null;
        try {
            $phone_util = PhoneNumberUtil::getInstance();
            $num = $phone_util->parse($region . $mobile, 'CN');
            if ($phone_util->isValidNumber($num)) {
                [$national_mobile, $country_code] = [$num->getNationalNumber(), $num->getCountryCode()];
            }
        } catch (Exception $e) {
            Yii::error("解析手机号失败: {$e->getMessage()}", __METHOD__);
        }
        if (!$national_mobile) {
            throw new HttpException(400, '不合法的手机号', self::CODE_ERROR_PARAMS);
        }

        $mobile_encrypted = MUtils::encrypt($national_mobile, SENSITIVE_FIXED_IV_KEY, SENSITIVE_INFORMATION_KEY);
        $mobile_info = new MobileNumber($mobile_encrypted, $country_code);

        $user_info = Yii::$app->sso->getUserInfoByMobile($national_mobile, $country_code);
        if (!$user_info) {
            return [$mobile_info, null];
        }

        $user = new Mowangskuser();
        $user->setAttributes(['id' => $user_info['id'], 'confirm' => $user_info['confirm']], false);
        return [$mobile_info, $user];
    }

}
