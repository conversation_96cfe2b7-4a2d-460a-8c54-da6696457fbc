<?php

namespace app\modules\openapi\controllers;

use app\components\util\Tools;
use app\models\AccountInfo;
use app\models\Balance;
use app\models\BalanceSettlementChangeLog;
use app\models\Guild;
use app\models\MAdminLogger;
use app\models\MMessageAssign;
use app\models\Mowangskuser;
use app\models\PayAccount;
use app\models\PayAccountPurchaseDetail;
use app\models\RechargeOrder;
use app\models\TransactionLog;
use app\models\WithdrawalRecord;
use Exception;
use missevan\util\MUtils as MUtils2;
use missevan\util\MUtils;
use Yii;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\web\Controller;
use yii\web\HttpException;
use yii\web\Request;

class SettlementController extends Controller
{
    const TEMPLATE_CODE_GUILD_SETTLEMENT_MONTHLY = 'guild_settlement_monthly';
    const TEMPLATE_CODE_BEIJING_PARTNER_SETTLEMENT_QUARTER = 'beijing_partner_settlement_quarter';
    const TEMPLATE_CODE_CHONGQING_PARTNER_SETTLEMENT_QUARTER = 'chongqing_partner_settlement_quarter';
    const TEMPLATE_CODE_PERSONAL_SETTLEMENT_MONTHLY = 'personal_settlement_monthly';

    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'request-topup-order-refund' => ['post'],
                'request-consume-order-refund' => ['post'],
                'query-order-refund-status' => ['post'],
                'deduct-creator-balance' => ['post'],
                'send-system-msg' => ['post'],
                'send-email' => ['post'],
                'withdrawal-confirm' => ['post'],
                'get-account-info' => ['post'],
                'get-guild-info' => ['post'],
            ],
        ];

        return $behaviors;
    }

    /**
     * @api {post} /openapi/settlement/request-topup-order-refund 发起充值订单退款
     * @apiDescription 注：若有退款（处理充值订单）过程中有涉及退钻（处理消费订单）场景，需要先调用处理消费订单退钻，再调用处理充值订单退款
     *
     * @apiName request-topup-order-refund
     * @apiGroup /openapi/settlement/
     *
     * @apiParam {String} order_ids 订单 ID（多个订单用半角逗号隔，例 "111,222,333"，订单 ID 数不可超过 100）
     * @apiParam {Number} user_id 订单所属的用户 ID
     * @apiParam {String} note 备注
     * @apiParam {String} request_id 请求唯一标识
     *
     * @apiSuccess {Number} code 返回码
     * @apiSuccess {String} message 成功返回成功，若是失败则返回错误信息
     * @apiSuccess {Object} data 具体数据响应
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "code": 0,  // 成功
     *       "message": "success",
     *       "data": {
     *         "task_id": "xxxx"  // 本次处理任务标识，用于后续查询处理状态
     *       }
     *     }
     */
    public function actionRequestTopupOrderRefund()
    {
        [
            'order_ids' => $order_ids,
            'user_id' => $user_id,
            'note' => $note,
            'request_id' => $request_id,
            'orders' => $orders,
        ] = $this->checkRequestOrderRefundParams(Yii::$app->request, BalanceSettlementChangeLog::TYPE_TOPUP_ORDER_REFUND);

        $log = BalanceSettlementChangeLog::newLog(BalanceSettlementChangeLog::TYPE_TOPUP_ORDER_REFUND, $user_id, $note,
            ['task' => ['request_id' => $request_id, 'order_ids' => $order_ids]]);
        $log->ignoreExceptionSave();

        $this->processOrderRefund($log, function ($buyer, $settlement_detail) use ($orders) {
            /**
             * @var Balance $buyer
             * @var RechargeOrder $order
             */
            $settlement_detail['coin_changes'] = [];
            foreach ($orders as $order) {
                if (!$balance_field = $order->getBalanceField()) {
                    continue;
                }
                $coin_changed = -min($buyer->{$balance_field}, $order->num);

                $settlement_detail['coin_changes'][] = [
                    'order_id' => $order->id,
                    'coin' => -$order->num,
                    'coin_changed' => $coin_changed,  // 实扣
                ];

                PayAccount::generateCommonCoin([array_flip(PayAccount::TYPE_INDEX_COIN_FIELD_MAP)[$balance_field] => $order->num], $order->id, $order->uid);
                $buyer->all_coin = $buyer->all_coin + $coin_changed;
                $buyer->all_topup = max($buyer->all_topup - $order->num, 0);
            }
            if (!$buyer->save()) {
                throw new Exception(MUtils2::getFirstError($buyer));
            }

            return $settlement_detail;
        });

        return [
            'task_id' => $log->taskId(),
        ];
    }

    /**
     * @api {post} /openapi/settlement/request-consume-order-refund 发起消费订单退款
     *
     * @apiName request-consume-order-refund
     * @apiGroup /openapi/settlement/
     *
     * @apiParam {String} order_ids 订单 ID（多个订单用半角逗号隔，例 "111,222,333"，订单 ID 数不可超过 100）
     * @apiParam {Number} user_id 订单所属的用户 ID
     * @apiParam {String} note 备注
     * @apiParam {String} request_id 请求唯一标识
     *
     * @apiSuccess {Number} code 返回码
     * @apiSuccess {String} message 成功返回成功，若是失败则返回错误信息
     * @apiSuccess {Object} data 具体数据响应
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "code": 0,  // 成功
     *       "message": "success",
     *       "data": {
     *         "task_id": "xxxx"  // 本次处理任务标识，用于后续查询处理状态
     *       }
     *     }
     */
    public function actionRequestConsumeOrderRefund()
    {
        [
            'order_ids' => $order_ids,
            'user_id' => $user_id,
            'note' => $note,
            'request_id' => $request_id,
            'orders' => $orders,
        ] = $this->checkRequestOrderRefundParams(Yii::$app->request, BalanceSettlementChangeLog::TYPE_CONSUME_ORDER_REFUND);

        $log = BalanceSettlementChangeLog::newLog(BalanceSettlementChangeLog::TYPE_CONSUME_ORDER_REFUND, $user_id, $note,
            ['task' => ['request_id' => $request_id, 'order_ids' => $order_ids]]);
        $log->ignoreExceptionSave();

        $this->processOrderRefund($log, function ($buyer, $settlement_detail) use ($orders) {
            $tid_common_coin_map = [];
            if (count($orders) > 0) {
                $tid_common_coin_map = PayAccountPurchaseDetail::find()
                    ->alias('t1')
                    ->select('t1.tid, SUM(t1.purchase_amount) AS purchase_amount')
                    ->innerJoin(PayAccount::tableName() . ' AS t2', 't2.id = t1.account_id')
                    ->where([
                        't1.tid' => array_column($orders, 'id'),
                        't1.status' => PayAccountPurchaseDetail::STATUS_CONFIRM,
                        't2.scope' => PayAccount::SCOPE_COMMON,
                        't2.status' => PayAccount::STATUS_SUCCESS,
                    ])->groupBy('tid')->indexBy('tid')->asArray()->all();
            }

            /**
             * @var Balance $buyer
             * @var TransactionLog $order
             */
            $settlement_detail['coin_changes'] = [];
            foreach ($orders as $order) {
                $common_coin = $order->getLegacyCommonCoin();
                if (array_key_exists($order->id, $tid_common_coin_map)) {
                    $common_coin += $tid_common_coin_map[$order->id]['purchase_amount'];
                }
                $settlement_detail['coin_changes'][] = [
                    'order_id' => $order->id,
                    'coin' => $common_coin,
                    'coin_changed' => $common_coin,
                ];

                PayAccountPurchaseDetail::proceed($order, PayAccountPurchaseDetail::STATUS_REFUND, PayAccountPurchaseDetail::STATUS_CONFIRM);
            }

            return $settlement_detail;
        });
        self::writeRefundIdToDatabus($user_id, $order_ids);
        return [
            'task_id' => $log->taskId(),
        ];
    }

    /**
     * 将消费退款 ID 写入 databus
     *
     * @param int $user_id 用户ID
     * @param array $order_ids 用户的订单号
     */
    public static function writeRefundIdToDatabus(int $user_id, array $order_ids)
    {
        try {
            $data = [
                'from_id' => $user_id,
                'order_ids' => $order_ids,
                'create_time' => $_SERVER['REQUEST_TIME'],
            ];
            Yii::$app->paydatabus->pub($data, 'live_consume_order_refund_log:from_id:' . $user_id);
        } catch (Exception $e) {
            Yii::error('databus 出错：' . $e->getMessage() . ', 当前 log: ' . Json::encode($data), __METHOD__);
            // PASS: databus 出错记录错误并忽略异常
        }
    }

    /**
     * @api {post} /openapi/settlement/query-order-refund-status 订单退款状态查询接口
     *
     * @apiName query-order-refund-status
     * @apiGroup /openapi/settlement/
     *
     * @apiParam {String} task_id 发起退款请求返回的任务 ID
     * @apiParam {String} request_id 请求唯一标识
     *
     * @apiSuccess {String} code 返回码
     * @apiSuccess {String} message 成功返回成功，若是失败则返回错误信息
     * @apiSuccess {Object} data 具体数据响应
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "code": 0,
     *       "message": "success",
     *       "data": {
     *         "task_id": "xxxx",  // 处理任务标识
     *         "order_status": 1,  // -1 退款失败，0 处理中，1 已退款
     *         "fail_reason": "",  // 失败原因（退款失败时返回）
     *         "coin_changes": [
     *           {
     *             "order_id": 13579,
     *             "coin": -500,  // 充值订单时为应扣的钻石数（负值），消费订单时为应返还的钻石数（正值）
     *             "coin_changed": -300  // 实扣钻石数或实际返还的钻石数
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionQueryOrderRefundStatus()
    {
        $task_id = trim(Yii::$app->request->post('task_id'));
        if ($task_id === '') {
            throw new HttpException(400, 'task_id 不能为空');
        }

        ['id' => $log_id, 'type' => $type] = BalanceSettlementChangeLog::parseTaskId($task_id);
        $log = BalanceSettlementChangeLog::findOne(['id' => $log_id, 'type' => $type]);
        if (!$log) {
            throw new HttpException(404, '该任务不存在');
        }

        $query_result = [
            'task_id' => $task_id,
            'order_status' => $log->status,
        ];
        switch ($log->status) {
            case BalanceSettlementChangeLog::STATUS_CREATE:
                return $query_result;
            case BalanceSettlementChangeLog::STATUS_FAILED:
                $query_result['fail_reason'] = $log->detail['task']['fail_reason'];
                return $query_result;
            case BalanceSettlementChangeLog::STATUS_SUCCESS:
                $query_result['coin_changes'] = $log->detail['coin_changes'];
                return $query_result;
        }
    }

    /**
     * @api {post} /openapi/settlement/deduct-creator-balance 扣减主播余额
     *
     * @apiName deduct-creator-balance
     * @apiGroup /openapi/settlement/
     *
     * @apiParam {Number} creator_type 主播类型（1 素人主播）
     * @apiParam {Number} creator_id 主播 ID
     * @apiParam {Number} deduct_amount 扣除金额（新收益），单位为分，注：旧收益的扣减走线下处理
     * @apiParam {String} note 备注
     * @apiParam {String} request_id 请求唯一标识
     *
     * @apiSuccess {String} code 返回码
     * @apiSuccess {String} message 成功返回成功，若是失败则返回错误信息
     * @apiSuccess {Object} data 具体数据响应
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "code": 0,
     *       "message": "success",
     *       "data": {
     *         "revenue_change": {
     *           "revenue": -3909,  // 应扣的金额（单位：分）
     *           "revenue_changed": -1909  // 实扣的金额（单位：分）
     *         },
     *         "balance": 0  // 主播余额（单位：分）
     *       }
     *     }
     */
    public function actionDeductCreatorBalance()
    {
        $creator_type = (int)Yii::$app->request->post('creator_type');
        $creator_id = (int)Yii::$app->request->post('creator_id');
        $deduct_amount = (int)Yii::$app->request->post('deduct_amount');
        $note = trim(Yii::$app->request->post('note'));
        $request_id = trim(Yii::$app->request->post('request_id'));

        if ($creator_type !== 1) {
            throw new HttpException(400, '暂时只支持素人主播进行余额扣减');
        }
        if ($note === '') {
            throw new HttpException(400, '备注不可为空');
        }
        if ($deduct_amount <= 0) {
            throw new HttpException(400, '扣减的余额需要大于 0');
        }
        if ($request_id === '') {
            throw new HttpException(400, 'request_id 不可为空');
        }
        if ($creator_id <= 0) {
            throw new HttpException(400, 'creator_id 错误');
        }
        if (BalanceSettlementChangeLog::isRequestIdExists($creator_id, BalanceSettlementChangeLog::TYPE_SINGLE_CREATOR_DEDUCT_BALANCE, $request_id)) {
            throw new HttpException(400, 'request_id 已存在');
        }

        $detail = ['task' => ['request_id' => $request_id]];
        $log = BalanceSettlementChangeLog::newLog(BalanceSettlementChangeLog::TYPE_SINGLE_CREATOR_DEDUCT_BALANCE, $creator_id, $note, $detail);
        $log->ignoreExceptionSave();

        $transaction = Balance::getDb()->beginTransaction();
        try {
            $creator = Balance::findOneForUpdate('id = :id', [':id' => $creator_id]);
            $num = min($creator->new_live_profit, $deduct_amount);

            $detail['balance'] = [
                'old' => [
                    'new_live_profit' => $creator->new_live_profit,
                    'new_all_live_profit' => $creator->new_all_live_profit,
                ],
                'new' => [
                    'new_live_profit' => $creator->new_live_profit - $num,
                    'new_all_live_profit' => $creator->new_all_live_profit - $num,
                ],
            ];
            $detail['revenue_change'] = [
                'revenue' => -$deduct_amount,
                'revenue_changed' => -$num,
            ];
            $detail['task']['request_id'] = $request_id;
            if (!BalanceSettlementChangeLog::updateByPk($log->id, [
                'detail' => $detail,
                'status' => BalanceSettlementChangeLog::STATUS_SUCCESS,
                'modified_time' => $_SERVER['REQUEST_TIME'],
            ])) {
                throw new Exception('结算日志保存失败');
            }

            $creator->updateCounters([
                'new_live_profit' => -$num,
                'new_all_live_profit' => -$num,
            ]);
            $transaction->commit();
            $transaction = null;

            return [
                'revenue_change' => $detail['revenue_change'],
                'balance' => $creator->new_live_profit,
            ];
        } catch (Exception $e) {
            if ($transaction) {
                $transaction->rollBack();
            }
            Yii::error(
                sprintf('扣减素人主播余额失败：creator_id[%d], deduct_amount[%d], error[%s], request_id[%s], note[%s]',
                    $creator_id, $deduct_amount, $e->getMessage(), $request_id, $note),
                __METHOD__
            );
            $log->saveWhenFailed($e->getMessage());
            throw new HttpException(500, '扣减素人主播余额失败：' . $e->getMessage());
        }
    }

    /**
     * 检查退款请求的参数
     *
     * @param Request $request
     * @param int $type
     * @throws HttpException
     */
    private function checkRequestOrderRefundParams(Request $request, int $type)
    {
        $order_ids = explode(',', $request->post('order_ids'));
        $user_id = (int)$request->post('user_id');
        $note = trim($request->post('note'));
        $request_id = trim($request->post('request_id'));

        if (!MUtils2::isUintArr($order_ids)) {
            throw new HttpException(400, 'order_ids 需为整型数组');
        }
        if (count($order_ids) > 100) {
            throw new HttpException(400, 'order_ids 最大长度为 100');
        }
        $order_ids = array_unique($order_ids);
        $order_ids = array_map('intval', $order_ids);

        if ($user_id <= 0) {
            throw new HttpException(400, 'user_id 错误');
        }
        if ($note === '') {
            throw new HttpException(400, '备注不可为空');
        }
        if ($request_id === '') {
            throw new HttpException(400, 'request_id 不可为空');
        }
        if (BalanceSettlementChangeLog::isRequestIdExists($user_id, $type, $request_id)) {
            throw new HttpException(400, 'request_id 已存在');
        }

        switch ($type) {
            case BalanceSettlementChangeLog::TYPE_TOPUP_ORDER_REFUND:
                $order_class = RechargeOrder::class;
                $condition = ['id' => $order_ids, 'uid' => $user_id, 'status' => RechargeOrder::STATUS_SUCCESS];
                break;
            case BalanceSettlementChangeLog::TYPE_CONSUME_ORDER_REFUND:
                $order_class = TransactionLog::class;
                $condition = ['id' => $order_ids, 'from_id' => $user_id, 'status' => TransactionLog::STATUS_SUCCESS];
                break;
            default:
                throw new Exception('参数错误');
        }

        $orders = $order_class::findAll($condition);
        if (count($orders) !== count($order_ids)) {
            throw new HttpException(400, '部分订单已退款或者非用户所有');
        }

        return [
            'order_ids' => $order_ids,
            'user_id' => $user_id,
            'note' => $note,
            'request_id' => $request_id,
            'orders' => $orders,
        ];
    }

    /**
     * 处理订单退款
     *
     * @param BalanceSettlementChangeLog $settlement_change_log 结算变更日志
     * @param callable $func 回调处理
     */
    private function processOrderRefund(BalanceSettlementChangeLog $settlement_change_log, callable $func)
    {
        $transaction = Balance::getDb()->beginTransaction();
        $error_message = null;
        $settlement_detail = $settlement_change_log->detail;
        try {
            $buyer = Balance::findOneForUpdate('id = :id', [':id' => $settlement_change_log->user_id]);

            $settlement_detail['balance']['old'] = $buyer->balanceSnapshot();
            $settlement_detail = $func($buyer, $settlement_detail);
            $settlement_detail['balance']['new'] = $buyer->balanceSnapshot();

            switch ($settlement_change_log->type) {
                case BalanceSettlementChangeLog::TYPE_TOPUP_ORDER_REFUND:
                    $order_num_affected = RechargeOrder::updateAll(
                        ['status' => RechargeOrder::STATUS_CANCELED, 'modified_time' => $_SERVER['REQUEST_TIME']],
                        ['id' => $settlement_detail['task']['order_ids'], 'status' => RechargeOrder::STATUS_SUCCESS]
                    );
                    break;
                case BalanceSettlementChangeLog::TYPE_CONSUME_ORDER_REFUND:
                    $order_num_affected = TransactionLog::updateAll(
                        ['status' => TransactionLog::STATUS_REFUND_DIAMOND, 'modified_time' => $_SERVER['REQUEST_TIME']],
                        ['id' => $settlement_detail['task']['order_ids'], 'status' => TransactionLog::STATUS_SUCCESS]
                    );
                    break;
                default:
                    throw new Exception('参数错误');
            }
            if ($order_num_affected < count($settlement_detail['task']['order_ids'])) {
                throw new HttpException(400, '部分订单已退款');
            }

            $settlement_change_log->setAttributes([
                'detail' => $settlement_detail,
                'status' => BalanceSettlementChangeLog::STATUS_SUCCESS,
            ]);
            if (!$settlement_change_log->save()) {
                Yii::error(sprintf('结算日志保存失败：id[%d], error[%s]', $settlement_change_log->id, MUtils2::getFirstError($settlement_change_log)), __METHOD__);
            }

            $transaction->commit();
        } catch (Exception $e) {
            if ($transaction) {
                $transaction->rollBack();
            }
            Yii::error(sprintf('订单退款处理失败：user_id[%d], error[%s]', $settlement_change_log->user_id, $e->getMessage()), __METHOD__);

            $settlement_change_log->saveWhenFailed($e->getMessage());
        }
    }

    /**
     * @api {post} /openapi/settlement/send-system-msg 推送系统消息
     *
     * @apiName send-system-msg
     * @apiGroup /openapi/settlement/
     *
     * @apiParam {Number} user_id 接收系统消息用户 ID
     * @apiParam {String} template_code 系统消息模板编号
     * - 扣款消息模板
     *   + iOS 退款：
     *     - 素人主播：
     *       + 应扣款：sys_deduction_ios_1
     *       + 未扣款、部分扣款：sys_deduction_ios_2
     *     - 公会主播：
     *       + 应扣款：sys_deduction_ios_3
     *       + 未扣款、部分扣款：sys_deduction_ios_4
     *   + 红包异常：sys_deduction_lucky_bag_1
     *   + 交易异常：
     *     - 素人主播：
     *       + 扣款：sys_deduction_transaction_abnormal_1
     *       + 未扣款、部分扣款：sys_deduction_transaction_abnormal_2
     *     - 公会主播：
     *       + 扣款：sys_deduction_transaction_abnormal_3
     *       + 未扣款、部分扣款：sys_deduction_transaction_abnormal_4
     *   + 未成年退款：
     *     - 素人主播：
     *       + 扣款：sys_deduction_teenager_1
     *       + 未扣款、部分扣款：sys_deduction_teenager_2
     *     - 公会主播：
     *       + 扣款：sys_deduction_teenager_3
     *       + 未扣款、部分扣款：sys_deduction_teenager_4
     * - 追款消息模板
     *   + iOS 退款：
     *     - 素人主播：
     *       + 未追回应扣款：sys_reminder_ios_1
     *       + 追回应扣款：sys_reminder_ios_2
     *     - 公会主播：
     *       + 未追回应扣款：sys_reminder_ios_3
     *       + 追回应扣款：sys_reminder_ios_4
     *   + 红包异常：sys_reminder_lucky_bag_1
     *   + 交易异常：
     *     - 素人主播：
     *       + 未追回应扣款：sys_reminder_transaction_abnormal_1
     *       + 追回应扣款：sys_reminder_transaction_abnormal_2
     *     - 公会主播：
     *       + 未追回应扣款：sys_reminder_transaction_abnormal_3
     *       + 追回应扣款：sys_reminder_transaction_abnormal_4
     *   + 未成年退款：
     *     - 素人主播：
     *       + 未追回应扣款：sys_reminder_teenager_1
     *       + 追回应扣款：sys_reminder_teenager_2
     *     - 公会主播：
     *       + 未追回应扣款：sys_reminder_teenager_3
     *       + 追回应扣款：sys_reminder_teenager_4
     * - 提现消息模板
     *   + 素人主播：
     *     - 提现成功：sys_withdrawal_success
     *     - 提现失败：sys_withdrawal_failed
     * @apiParam {Object} template_params 模板信息（模板参数可约定）
     * @apiParam {String} request_id 请求唯一标识
     *
     * @apiParamExample {application/x-www-form-urlencoded} Request-Example: 公会主播 iOS 退款应扣款系统消息
     *     {
     *       "user_id": 1,
     *       "template_code": "sys_deduction_ios_3",
     *       "template_params": {
     *         "user_id": 2,  // 用户 ID
     *         "username": "test"  // 用户昵称
     *       },
     *       "request_id": "abc"
     *     }
     * @apiParamExample {application/x-www-form-urlencoded} Request-Example: 素人主播提现成功系统消息（无模板参数）
     *     {
     *       "user_id": 1,
     *       "template_code": "sys_withdrawal_success",
     *       "request_id": "abc"
     *     }
     * @apiParamExample {application/x-www-form-urlencoded} Request-Example: 素人主播提现失败系统消息
     *     {
     *       "user_id": 1,
     *       "template_code": "sys_withdrawal_failed",
     *       "template_params": {
     *         "reason": "test"  // 提现失败原因
     *       },
     *       "request_id": "abc"
     *     }
     *
     * @apiSuccess {Number} code 返回码
     * @apiSuccess {String} message 成功返回成功，若是失败则返回错误信息
     * @apiSuccess {Object} data 具体数据响应
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "code": 0,  // 成功
     *       "message": "success",
     *       "data": null
     *     }
     */
    public function actionSendSystemMsg()
    {
        $user_id = (int)Yii::$app->request->post('user_id');
        $template_code = trim(Yii::$app->request->post('template_code'));
        $template_params = Yii::$app->request->post('template_params');
        $request_id = trim(Yii::$app->request->post('request_id'));

        // TODO: request_id 暂时不做其他验证，后续需要保存日志时再做处理
        if ($request_id === '') {
            throw new HttpException(400, 'request_id 不可为空');
        }
        if ($user_id <= 0) {
            throw new HttpException(400, '主播 ID 错误');
        }
        $system_msg_templates = Yii::$app->params['templates']['system_msg_templates'];
        if (!array_key_exists($template_code, $system_msg_templates)) {
            throw new HttpException(400, '系统消息模板编号错误');
        }
        // template_params 参数可以是 null 或者非 list 类型的数组
        if (!(is_null($template_params) || ArrayHelper::isAssociative($template_params))) {
            throw new HttpException(400, '模板信息错误');
        }

        if (!Mowangskuser::find()->where('id = :id', [':id' => $user_id])->exists()) {
            throw new HttpException(404, '主播不存在');
        }

        ['title' => $title, 'content' => $content] = $system_msg_templates[$template_code];
        $content = self::replaceTemplateParams($template_params, $content);
        $res = MMessageAssign::sendSysMsg($user_id, $title, $content);
        if (!$res) {
            throw new HttpException(500, '推送系统消息异常，请联系工作人员');
        }
        return null;
    }

    /**
     * @api {post} /openapi/settlement/send-email 发送邮件
     *
     * @apiName send-email
     * @apiGroup /openapi/settlement/
     *
     * @apiParam {String} request_id 请求唯一标识
     * @apiParam {String} to 收件人邮箱（多个邮箱使用 , 拼接）
     * @apiParam {String} cc 抄送人邮箱（多个邮箱使用 , 拼接）
     * @apiParam {String} template_code 系统消息模板编号
     * + 每月公会结算单：guild_settlement_monthly
     * + 每季度合作方结算单（北京主体）：beijing_partner_settlement_quarter
     * + 每季度合作方结算单（重庆主体）：chongqing_partner_settlement_quarter
     * + 每月公对私结算单：personal_settlement_monthly
     * @apiParam {Object} template_params 模板信息（模板参数按示例填写）
     *
     * @apiParamExample {application/x-www-form-urlencoded} Request-Example: 公会每月结算单
     *      {
     *        "request_id": "abc",
     *        "to": "<EMAIL>,<EMAIL>", // 收件人邮箱（多个邮箱使用 , 拼接）
     *        "cc": "<EMAIL>,<EMAIL>", // 抄送人邮箱（没有可不填写；多个邮箱使用 , 拼接）
     *        "template_code": "guild_settlement_monthly",
     *        "template_params" : {
     *          "guild_name": "公会名称", // 公会名称
     *          "billing_month": "2024-09", // 账单月份
     *          "signing_deadline_date": "2024-09-10", // 签署截止日期
     *          "invoice_return_deadline_date": "2024-10-10", // 发票接收截止日期
     *          "download_url": "https://baidu.com/xxxx.xlsx" // 结算单下载链接
     *        }
     *      }
     * @apiParamExample {application/x-www-form-urlencoded} Request-Example: 合作方每季度结算单（北京主体 | 重庆主体）
     *      {
     *        "request_id": "abc",
     *        "to": "<EMAIL>,<EMAIL>", // 收件人邮箱（多个邮箱使用 , 拼接）
     *        "cc": "<EMAIL>,<EMAIL>", // 抄送人邮箱（没有可不填写；多个邮箱使用 , 拼接）
     *        "template_code": "beijing_partner_settlement_quarter",
     *        "template_params" : {
     *          "partner_name": "合作方公司名称", // 合作方公司名称
     *          "enterprise_name": "北京喵斯拉", // 主体公司名称
     *          "title": "剧集", // 结算单类型名称
     *          "billing_year": "2024", // 账单年份
     *          "billing_quarter": "Q3", // 账单季度
     *          "download_url": "https://static-test.maoercdn.com/xxxx.xlsx" // 结算单下载链接
     *        }
     *      }
     * @apiParamExample {application/x-www-form-urlencoded} Request-Example: 每月公对私结算单
     *      {
     *        "request_id": "abc",
     *        "to": "<EMAIL>,<EMAIL>", // 收件人邮箱（多个邮箱使用 , 拼接）
     *        "cc": "<EMAIL>,<EMAIL>", // 抄送人邮箱（没有可不填写；多个邮箱使用 , 拼接）
     *        "template_code": "personal_settlement_monthly",
     *        "template_params" : {
     *          "username": "用户昵称", // 用户昵称
     *          "billing_month": "2024-09", // 账单月份
     *          "signing_deadline_date": "2024-09-10", // 签署截止日期
     *          "download_url": "https://baidu.com/xxxx.xlsx" // 结算单下载链接
     *        }
     *      }
     *
     * @apiSuccess {Number} code 返回码
     * @apiSuccess {String} message 成功返回 success
     * @apiSuccess {Object} data 具体数据响应
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "code": 0,
     *       "message": "success",
     *       "data": null
     *     }
     */
    public function actionSendEmail()
    {
        $request_id = trim(Yii::$app->request->post('request_id'));
        $to = trim(Yii::$app->request->post('to'));
        $cc = trim(Yii::$app->request->post('cc'));
        $template_code = trim(Yii::$app->request->post('template_code'));
        $template_params = Yii::$app->request->post('template_params');

        if (!$request_id || !$template_code || !ArrayHelper::isAssociative($template_params) || !$to) {
            throw new HttpException(400, '参数错误');
        }

        $email_templates = Yii::$app->params['templates']['email_templates'];
        switch ($template_code) {
            case self::TEMPLATE_CODE_GUILD_SETTLEMENT_MONTHLY:
                // 每月公会结算单
                [
                    'title' => $subject,
                    'content' => $body,
                ] = $email_templates[self::TEMPLATE_CODE_GUILD_SETTLEMENT_MONTHLY];
                [$subject_params, $body_params] = self::formatGuildSettlementMonthly($template_params);
                break;
            case self::TEMPLATE_CODE_BEIJING_PARTNER_SETTLEMENT_QUARTER:
            case self::TEMPLATE_CODE_CHONGQING_PARTNER_SETTLEMENT_QUARTER:
                // 每季度合作方结算单
                [
                    'title' => $subject,
                    'content' => $body,
                ] = $email_templates[$template_code];
                [$subject_params, $body_params] = self::formatPartnerSettlementQuarter($template_params);
                break;
            case self::TEMPLATE_CODE_PERSONAL_SETTLEMENT_MONTHLY:
                [
                    'title' => $subject,
                    'content' => $body,
                ] = $email_templates[$template_code];
                [$subject_params, $body_params] = self::formatPersonSettlementMonthly($template_params);
                break;
            default:
                throw new HttpException(403, '不存在的 template_code');
        }

        $subject = self::replaceTemplateParams($subject_params, $subject);
        $body = self::replaceTemplateParams($body_params, $body);
        try {
            $emails = [
                'to' => $to,
                'cc' => $cc,
                'subject' => $subject,
                'body' => $body,
            ];
            Yii::$app->tools->sendNotification($emails, Tools::SEND_EMAIL);
            return null;
        } catch (Exception $e) {
            Yii::error(sprintf('发送结算单邮件失败：%s', $e->getMessage()), __METHOD__);
            throw new HttpException(500, '发送失败');
        }
    }

    /**
     * 替换模板变量
     *
     * @param array|null $params 模板变量值 e.g. ['username' => 'test', 'user_id' => '123']
     * @param string $content 模板内容
     * @return string 替换后的模板内容
     * @throws HttpException
     */
    private static function replaceTemplateParams(?array $params, string $content): string
    {
        if (!$params) {
            return $content;
        }
        $pattern = '/\{\{([a-zA-Z_\|]+)\}\}/';
        // out 举例：[['{{username|html}}', '{{user_id}}'], ['username|html', 'user_id']]
        $res = preg_match_all($pattern, $content, $out);
        if ($res === false) {
            throw new HttpException(400, '模板内容错误');
        }
        $flag = '|html';
        $keys_map = [];
        $html_encode_keys = [];
        foreach ($out[1] as $item) {
            if (str_ends_with($item, $flag) && !in_array($item, $html_encode_keys)) {
                // 需要 html 转义的模板变量
                $html_encode_keys[] = $item;
            }
            // keys_map 举例：['user_id' => 'user_id', 'username|html' => username]
            $keys_map[$item] = explode('|', $item)[0];
        }
        $match_count = count(array_unique(array_values($keys_map)));
        if ($match_count === 0) {
            return $content;
        }
        if ($match_count !== count($params)) {
            throw new HttpException(400, '模板信息参数数量错误');
        }

        $search = [];
        $replace = [];
        foreach ($keys_map as $template_key => $params_key) {
            if (!array_key_exists($params_key, $params)) {
                continue;
            }
            // 模板中需要 html 转义的变量格式为 {{xxx|html}}，不需要转义的格式为 {{xxx}}
            $search[] = '{{' . $template_key . '}}';
            if (in_array($template_key, $html_encode_keys)) {
                $val = Html::encode($params[$params_key]);
            } else {
                $val = $params[$params_key];
            }
            $replace[] = $val;
        }
        return str_replace($search, $replace, $content);
    }

    private static function formatGuildSettlementMonthly($data): array
    {
        if (!$data) {
            throw new HttpException(400, '参数不可为空');
        }

        $guild_name = trim($data['guild_name'] ?? '');
        $download_url = trim($data['download_url'] ?? '');
        if ($guild_name === '' || $download_url === '') {
            throw new HttpException(400, 'guild_name OR download_url 不可为空');
        }

        $billing_month = trim($data['billing_month'] ?? '');
        $billing_month = date_create_from_format('Y-m', $billing_month);
        if (!$billing_month) {
            throw new HttpException(400, '账单月份不正确');
        }

        $signing_deadline_date = trim($data['signing_deadline_date'] ?? '');
        $signing_deadline_date = date_create_from_format('Y-m-d', $signing_deadline_date);
        if (!$signing_deadline_date) {
            throw new HttpException(400, '签署截止日期不正确');
        }

        $invoice_return_deadline_date = trim($data['invoice_return_deadline_date'] ?? '');
        $invoice_return_deadline_date = date_create_from_format('Y-m-d', $invoice_return_deadline_date);
        if (!$invoice_return_deadline_date) {
            throw new HttpException(400, '发票接收截止日期不正确');
        }

        $dt = date_timestamp_set(date_create(), $_SERVER['REQUEST_TIME']);
        if ($dt->format('Ym') === $signing_deadline_date->format('Ym')) {
            $signing_deadline_date_str = '本月 ' . $signing_deadline_date->format('d') . ' 日';
        } else {
            $signing_deadline_date_str = $signing_deadline_date->format(' m 月 d 日');
        }

        $subject = [
            'billing_month' => $billing_month->format('m'),
        ];
        $body = [
            'guild_name' => $guild_name,
            'billing_month' => $billing_month->format('m'),
            'signing_deadline_date' => $signing_deadline_date_str,
            'invoice_return_deadline_date' => $invoice_return_deadline_date->format('m 月 d 日'),
            'help_url' => Yii::$app->params['help_links']['send_guild_email'],
            'download_url' => $download_url
        ];
        return [$subject, $body];
    }

    private static function formatPartnerSettlementQuarter($data): array
    {
        if (!$data) {
            throw new HttpException(400, '参数不可为空');
        }

        $partner_name = trim($data['partner_name'] ?? '');
        $enterprise_name = trim($data['enterprise_name'] ?? '');
        $title = trim($data['title'] ?? '');
        $billing_year = trim($data['billing_year'] ?? '');
        $billing_quarter = trim($data['billing_quarter'] ?? '');
        $download_url = trim($data['download_url'] ?? '');
        if (!$partner_name) {
            throw new HttpException(400, 'partner_name 参数不正确');
        }

        if (!$enterprise_name) {
            throw new HttpException(400, 'enterprise_name 参数不正确');
        }

        if (!$title) {
            throw new HttpException(400, 'title 参数不正确');
        }

        if (!$billing_year) {
            throw new HttpException(400, 'billing_year 参数不正确');
        }

        if (!$billing_quarter) {
            throw new HttpException(400, 'billing_quarter 参数不正确');
        }

        if (!$download_url) {
            throw new HttpException(400, 'download_url 参数不正确');
        }

        $subject = [
            'partner_name' => $partner_name,
            'enterprise_name' => $enterprise_name,
            'title' => $title,
            'billing_year' => $billing_year,
            'billing_quarter' => $billing_quarter,
        ];
        $body = [
            'billing_year' => $billing_year,
            'billing_quarter' => $billing_quarter,
            'download_url' => $download_url,
        ];
        return [$subject, $body];
    }

    private static function formatPersonSettlementMonthly($data): array
    {
        if (!$data) {
            throw new HttpException(400, '参数不可为空');
        }

        $username = trim($data['username'] ?? '');
        $download_url = trim($data['download_url'] ?? '');
        $billing_month = trim($data['billing_month'] ?? '');
        $signing_deadline_date = trim($data['signing_deadline_date'] ?? '');
        if (!$username || !$download_url || !$billing_month || !$signing_deadline_date) {
            throw new HttpException(400, '参数错误');
        }

        $billing_month = date_create_from_format('Y-m', $billing_month);
        if (!$billing_month) {
            throw new HttpException(400, '账单月份不正确');
        }

        $signing_deadline_date = date_create_from_format('Y-m-d', $signing_deadline_date);
        if (!$signing_deadline_date) {
            throw new HttpException(400, '签署截止日期不正确');
        }
        $dt = date_timestamp_set(date_create(), $_SERVER['REQUEST_TIME']);
        if ($dt->format('Ym') === $signing_deadline_date->format('Ym')) {
            $signing_deadline_date_str = '本月 ' . $signing_deadline_date->format('d') . ' 日';
        } else {
            $signing_deadline_date_str = $signing_deadline_date->format(' m 月 d 日');
        }

        $subject = [
            'billing_month' => $billing_month->format('m'),
        ];
        $body = [
            'username' => $username,
            'billing_month' => $billing_month->format('m'),
            'signing_deadline_date' => $signing_deadline_date_str,
            'download_url' => $download_url
        ];
        return [$subject, $body];
    }

    /**
     * @api {post} /openapi/settlement/withdrawal-confirm 提现状态变更
     * @apiDescription 结算系统素人主播提现使用
     * @apiName withdrawal-confirm
     * @apiGroup /openapi/settlement/
     *
     * @apiParam {String} record_ids 提现记录 IDs（多个用半角逗号分隔，例 "111,222,333"，ID 数不可超过 50）
     * @apiParam {Number} user_id 主播 ID
     * @apiParam {Number} withdrawal_status 提现状态 1：成功；2：失败
     * @apiParam {String} [failed_reason] 提现失败原因，提现状态为失败时传递，用于记录到操作日志中
     * @apiParam {String} request_id 请求唯一标识
     *
     * @apiParamExample {application/x-www-form-urlencoded} Request-Example: 提现成功
     *     {
     *       "record_ids": "1,2,3",
     *       "user_id": 1,
     *       "withdrawal_status": 1,
     *       "request_id": "abc"
     *     }
     * @apiParamExample {application/x-www-form-urlencoded} Request-Example: 提现失败
     *     {
     *       "record_ids": "1,2,3",
     *       "user_id": 1,
     *       "withdrawal_status": 2,
     *       "failed_reason": "test",
     *       "request_id": "abc"
     *     }
     *
     * @apiSuccess {Number} code 返回码
     * @apiSuccess {String} message 成功返回成功，若是失败则返回错误信息
     * @apiSuccess {Object} data 具体数据响应
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "code": 0,  // 成功
     *       "message": "success",
     *       "data": null
     *     }
     */
    public function actionWithdrawalConfirm()
    {
        $record_ids_str = trim(Yii::$app->request->post('record_ids'));
        $user_id = (int)Yii::$app->request->post('user_id');
        $withdrawal_status = (int)Yii::$app->request->post('withdrawal_status');
        $failed_reason = trim(Yii::$app->request->post('failed_reason'));
        $request_id = trim(Yii::$app->request->post('request_id'));

        // TODO: request_id 暂时不做其他验证，后续需要保存日志时再做处理
        if ($request_id === '') {
            throw new HttpException(400, 'request_id 不可为空');
        }
        $STATUS_SUCCESS = 1;
        $STATUS_FAILED = 2;
        if ($user_id <= 0 || !in_array($withdrawal_status, [$STATUS_SUCCESS, $STATUS_FAILED])) {
            throw new HttpException(400, '参数错误');
        }
        if (!$record_ids_str) {
            throw new HttpException(400, 'record_ids 信息不可为空');
        }
        $record_ids = explode(',', $record_ids_str);
        if (!MUtils2::isUintArr($record_ids)) {
            throw new HttpException(400, 'record_ids 元素需为整型');
        }
        $LIMIT_ACCOUNT_NUM = 50;
        if (count($record_ids) > $LIMIT_ACCOUNT_NUM) {
            throw new HttpException(400, 'record_ids 最大数量为 ' . $LIMIT_ACCOUNT_NUM);
        }
        if ($STATUS_FAILED === $withdrawal_status && !$failed_reason) {
            throw new HttpException(400, '提现失败原因不能为空');
        }
        $record_ids = array_unique($record_ids);
        $record_unique_ids_str = implode(',', $record_ids);
        $transaction = WithdrawalRecord::getDb()->beginTransaction();
        try {
            $condition = MUtils::generateSqlIntegerIn('id', $record_ids) .
                ' AND user_id = :user_id AND status = :status AND type = :type';
            $params = [
                ':user_id' => $user_id,
                ':status' => WithdrawalRecord::STATUS_CREATE,
                ':type' => WithdrawalRecord::TYPE_WITHDRAW_LIVE_NEW,
            ];
            $records = WithdrawalRecord::findAllForUpdate($condition, $params);
            if (empty($records) || count($record_ids) !== count($records)) {
                throw new HttpException(404, '提现记录不存在');
            }
            // 收益换算成分为单位
            $profits = array_map(function ($item) {
                return Balance::profitUnitConversion($item->profit, Balance::CONVERT_YUAN_TO_FEN);
            }, $records);
            $profit_sum = array_sum($profits);
            if ($STATUS_SUCCESS === $withdrawal_status) {
                // 提现成功
                $column_status = WithdrawalRecord::STATUS_CONFIRM;
            } else {
                // 提现失败，退回待提现的余额
                $column_status = WithdrawalRecord::STATUS_INVALID;
                Balance::updateAllCounters(['new_live_profit' => $profit_sum], 'id = :id', [':id' => $user_id]);
            }
            $time = $_SERVER['REQUEST_TIME'];
            WithdrawalRecord::updateAll(['status' => $column_status, 'modified_time' => $time], $condition, $params);
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            throw new HttpException(500, $e->getMessage());
        }
        // 记录日志
        $profit = Balance::profitUnitConversion($profit_sum, Balance::CONVERT_FEN_TO_YUAN);
        $intro = $STATUS_SUCCESS === $withdrawal_status
            ? sprintf('提现成功，提现记录 ID：%s，提现金额：%f 元', $record_unique_ids_str, $profit)
            : sprintf('提现失败，原因：%s。提现记录 ID：%s，提现金额：%f 元', $failed_reason, $record_unique_ids_str,
                $profit);
        MAdminLogger::addOne([
            'intro' => $intro,
            'catalog' => MAdminLogger::CATALOG_SYSTEM_WITHDRAWAL_CHECK,
            'channel_id' => $user_id,
        ]);
        return null;
    }

    /**
     * @api {post} /openapi/settlement/get-account-info 获取用户提现账号信息
     *
     * @apiName get-account-info
     * @apiGroup /openapi/settlement/
     *
     * @apiParam {String} account_ids 提现账号记录 IDs（多个用半角逗号隔，例 "111,222,333"，ID 数不可超过 100）
     * @apiParam {String} request_id 请求唯一标识
     *
     * @apiParamExample {application/x-www-form-urlencoded} Request-Example:
     *     {
     *       "account_id": "111,222",
     *       "request_id": "abc"
     *     }
     *
     * @apiSuccess {Number} code 返回码
     * @apiSuccess {String} message 成功返回成功，若是失败则返回错误信息
     * @apiSuccess {Object} data 具体数据响应
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "code": 0,  // 成功
     *       "message": "success",
     *       "data": {
     *         "account_info": [  // 单个查询不到记录的不返回，全部查询不到的情况下返回空数组
     *           {
     *             "id": 111,
     *             "user_id": 1,
     *             "real_name": "姓名",  // 真实姓名
     *             "account": "",  // 提现支付宝账号
     *             "mobile": "***********",  // 手机号
     *             "id_number": "231025111111111111",  // 身份证号
     *             "bank": "邮储银行",  // 开户银行
     *             "bank_branch": "湖北省武汉市藏龙岛分行",  // 支行信息
     *             "bank_account": "6215811111111111111",  // 银行账号
     *             "status": 1,  // 状态 -1：失效账号；0：保存；1：已确认
     *             "type": 1  // 账户类型 0：支付宝账户 1：银行账户
     *           },
     *           {
     *             "id": 222,
     *             "user_id": 2,
     *             "real_name": "姓名",  // 真实姓名
     *             "account": "***********",  // 提现支付宝账号
     *             "mobile": "***********",  // 手机号
     *             "id_number": "231025222222222222",  // 身份证号
     *             "bank": "",  // 开户银行
     *             "bank_branch": "",  // 支行信息
     *             "bank_account": "",  // 银行账号
     *             "status": -1,  // 状态 -1：失效账号；0：保存；1：已确认
     *             "type": 0  // 账户类型 0：支付宝账户 1：银行账户
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionGetAccountInfo()
    {
        $account_ids_str = trim(Yii::$app->request->post('account_ids'));
        $request_id = trim(Yii::$app->request->post('request_id'));

        // TODO: request_id 暂时不做其他验证，后续需要保存日志时再做处理
        if ($request_id === '') {
            throw new HttpException(400, 'request_id 不可为空');
        }
        if (!$account_ids_str) {
            throw new HttpException(400, 'account_ids 信息不可为空');
        }
        $account_ids = explode(',', $account_ids_str);
        if (!MUtils2::isUintArr($account_ids)) {
            throw new HttpException(400, 'account_ids 元素需为整型');
        }
        $LIMIT_ACCOUNT_NUM = 100;
        if (count($account_ids) > $LIMIT_ACCOUNT_NUM) {
            throw new HttpException(400, 'account_ids 最大数量为 ' . $LIMIT_ACCOUNT_NUM);
        }
        $account_ids = array_unique($account_ids);
        $account_ids = array_map('intval', $account_ids);

        $accounts = AccountInfo::find()
            ->where(['id' => $account_ids])
            ->all();
        if (empty($accounts)) {
            return ['account_info' => []];
        }
        $res = array_map(function ($item) {
            return [
                'id' => (int)$item->id,
                'user_id' => (int)$item->user_id,
                'real_name' => $item->real_name,
                'account' => $item->getDecryptAccount(),
                'mobile' => $item->getDecryptMobile(),
                'id_number' => $item->getDecryptIdNumber(),
                'bank' => $item->bank,
                'bank_branch' => $item->bank_branch,
                'bank_account' => $item->getDecryptBankAccount(),
                'status' => (int)$item->status,
                'type' => (int)$item->type,
            ];
        }, $accounts);
        return ['account_info' => $res];
    }

    /**
     * @api {post} /openapi/settlement/get-guild-info 获取公会信息
     *
     * @apiName get-guild-info
     * @apiGroup /openapi/settlement/
     *
     * @apiParam {String} guild_ids 公会 IDs（多个用半角逗号隔，例 "111,222,333"，ID 数不可超过 100）
     * @apiParam {String} request_id 请求唯一标识
     *
     * @apiParamExample {application/x-www-form-urlencoded} Request-Example:
     *     {
     *       "guild_ids": "111,222",
     *       "request_id": "abc"
     *     }
     *
     * @apiSuccess {Number} code 返回码
     * @apiSuccess {String} message 成功返回成功，若是失败则返回错误信息
     * @apiSuccess {Object} data 具体数据响应
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "code": 0,  // 成功
     *       "message": "success",
     *       "data": {
     *         "guild_info": [  // 单个查询不到记录的不返回，全部查询不到的情况下返回空数组
     *           {
     *             "id": 111,
     *             "corporation_name": "北京xxxx有限公司"
     *           },
     *           {
     *             "id": 222,
     *             "corporation_name": "深圳xxxx有限公司"
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionGetGuildInfo()
    {
        $ids = trim(Yii::$app->request->post('guild_ids'));
        $request_id = trim(Yii::$app->request->post('request_id'));

        if ($request_id === '') {
            throw new HttpException(400, 'request_id 不可为空');
        }
        if (!$ids) {
            throw new HttpException(400, 'guild_ids 信息不可为空');
        }
        $ids = explode(',', $ids);
        if (!MUtils2::isUintArr($ids)) {
            throw new HttpException(400, 'guild_ids 元素需为整型');
        }
        $LIMIT_ACCOUNT_NUM = 100;
        if (count($ids) > $LIMIT_ACCOUNT_NUM) {
            throw new HttpException(400, 'guild_ids 最大数量为 ' . $LIMIT_ACCOUNT_NUM);
        }

        $ids = array_map('intval', array_unique($ids));
        $result = Guild::find()
            ->select('id, create_time, corporation_name')
            ->where(['id' => $ids])
            ->all();
        $res = array_map(function ($item) {
            /**
             * @var Guild $item
             */
            return [
                'id' => $item->id,
                'corporation_name' => $item->getDecryptCorporationName(),
            ];
        }, $result);

        return [
            'guild_info' => $res,
        ];
    }

}
