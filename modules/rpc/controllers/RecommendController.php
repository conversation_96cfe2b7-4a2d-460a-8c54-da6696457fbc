<?php

namespace app\modules\rpc\controllers;

use app\components\util\Equipment;
use app\models\MRecommendPopup;
use Yii;
use yii\web\Controller;

class RecommendController extends Controller
{
    /**
     * @api {post} /rpc/recommend/get-popup 获取推荐弹窗和跳转链接
     * @apiName get-popup
     * @apiGroup Recommend
     *
     * @apiParam {String} channel 渠道标识
     * @apiParam {String} buvid 唯一设备标识
     * @apiParam {String} equip_id 设备 ID
     *
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "popup_url": "http://static.missevan.com/standalone/app/test/index.html", // 无推荐弹窗的情况下不含该参数
     *         "open_url": "missevan://live/463640018?from=ad.0.1.80000_0_0_0&event_id_from=ad.0.1.80000_0_0_0" // 无跳转地址时不含该参数
     *       }
     *     }
     */
    public function actionGetPopup()
    {
        $channel = Yii::$app->request->post('channel');
        $buvid = Yii::$app->request->post('buvid');
        $equip_id = Yii::$app->request->post('equip_id');
        $return = MRecommendPopup::getPopupUrls($channel, $buvid, $equip_id);
        return $return ?: new \stdClass();
    }
}
