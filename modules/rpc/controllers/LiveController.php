<?php
/**
 * Created by PhpS<PERSON><PERSON>.
 * User: tomcao
 * Date: 2017/7/28
 * Time: 17:53
 */

namespace app\modules\rpc\controllers;

use app\components\util\MUtils;
use app\forms\TransactionForm;
use app\forms\TransactionFormGashapon;
use app\forms\TransactionFormFukubukuro;
use app\forms\TransactionFormNoble;
use app\forms\TransactionFormRebateGift;
use app\forms\TransactionFormLive;
use app\forms\TransactionFormSuperFan;
use app\forms\TransactionFormWishGoods;
use app\forms\UserContext;
use app\models\Balance;
use app\models\BlackUser;
use app\models\Catalog;
use app\models\Gift;
use app\models\Mowangskuser;
use app\models\MSound;
use app\models\MUserHistory;
use app\models\PayAccount;
use app\models\UserNoble;
use missevan\util\MUtils as MUtils2;
use yii\helpers\Json;
use yii\web\Controller;
use yii\web\HttpException;
use Yii;

class LiveController extends Controller
{
    /**
     * @api {post} /rpc/live/buy-gift 购买礼物
     * @apiName buy-gift
     * @apiGroup live
     *
     * @apiParam {Number} from_id 购买者 ID
     * @apiParam {Number} to_id 收礼者 ID
     * @apiParam {Number} gift_id 礼物 ID
     * @apiParam {Number} num 礼物数量
     * @apiParam {number=0,1} noble 是否可以使用贵族钻石
     *
     * @apiParam {String} live_open_log_id 直播场次
     * @apiParam {String} user_agent 设备 User-Agent
     * @apiParam {String} ip 终端 IP
     * @apiParam {String} [equip_id] 设备 equip_id
     * @apiParam {String} [buvid] 设备 buvid
     *
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "transaction_id": 56711318,
     *         "balance": 560,
     *         "live_noble_balance": 2655,
     *         "price": 200
     *       }
     *     }
     *
     */
    public function actionBuyGift()
    {
        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_GIFT]);
        $form->load(Yii::$app->request->post(), '');
        $form->user_context = UserContext::fromRpc(Yii::$app->request);
        if ($msg = $form->buyGift()) {
            return $msg;
        }
        throw new HttpException(400, MUtils::getFirstError($form));
    }

    /**
     * @apiDeprecated 使用新接口 /rpc/live/send-rebate-gifts
     * @api {post} /rpc/live/send-rebate-gift 赠送白给礼物
     * @apiName send-rebate-gift
     * @apiGroup live
     *
     * @apiParam {Number} from_id 购买者 ID
     * @apiParam {Number} to_id 收礼者 ID
     * @apiParam {Number} gift_id 礼物 ID
     * @apiParam {Number} num 礼物数量
     * @apiParam {String} context 礼物信息 context
     *
     * @apiParam {String} live_open_log_id 直播场次
     * @apiParam {String} user_agent 设备 User-Agent
     * @apiParam {String} ip 终端 IP
     * @apiParam {String} [equip_id] 设备 equip_id
     * @apiParam {String} [buvid] 设备 buvid
     *
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "transaction_id": 83452
     *       }
     *     }
     *
     * @apiError (400) {Number} code *********
     * @apiError (400) {String} info 参数错误
     *
     * @apiError (403) {Number} code 101010003
     * @apiError (403) {String} info 活动未开始
     */
    public function actionSendRebateGift()
    {
        return $this->actionSendRebateGifts();
    }

    /**
     * @api {post} /rpc/live/buy-vip 购买贵族
     * @apiName buy-vip
     * @apiGroup live
     *
     * @apiParam {Number} from_id 购买者 ID
     * @apiParam {Number} to_id 收礼者 ID
     * @apiParam {Number} price 贵族价格
     * @apiParam {Number} rebate 返回贵族钻石数
     * @apiParam {Number} gift_id 贵族 ID
     * @apiParam {String} title 贵族名
     * @apiParam {Number} num 数量（体验卡使用数量）
     * @apiParam {Number=0,1} noble 是否为开通，1 为开通，0 为续费
     * @apiParam {Number} max_expire_time 贵族最大的有效期截止时间点（含），秒级时间戳
     * @apiParam {Number=0,1} [is_trial] 是否为贵族体验卡，1 为是，0 为否
     *
     * @apiParam {String} live_open_log_id 直播场次
     * @apiParam {String} user_agent 设备 User-Agent
     * @apiParam {String} ip 终端 IP
     * @apiParam {String} [equip_id] 设备 equip_id
     * @apiParam {String} [buvid] 设备 buvid
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "transaction_id": 83452,
     *         "balance": 3000,
     *         "live_noble_balance": 500,
     *         "price": 200
     *       }
     *     }
     *
     * @apiError (400) {Number} code *********
     * @apiError (400) {String} info 参数错误
     *
     * @throws HttpException
     */
    public function actionBuyVip()
    {
        $form = new TransactionFormNoble();
        $form->load(Yii::$app->request->post(), '');
        $form->user_context = UserContext::fromRpc(Yii::$app->request);
        if ($form->validate()) {
            $msg = $form->is_trial ? $form->buyTrialNoble() : $form->buyNoble();
            if ($msg) {
                return $msg;
            }
        }
        throw new HttpException(400, MUtils::getFirstError($form));
    }

    /**
     * @api {post} /rpc/live/update-noble-coin-expire-time 更新贵族钻石有效期
     * @apiDescription 调用场景：开通/续费上神贵族、体验贵族、手动设置贵族有效期 \
     * https://info.missevan.com/pages/viewpage.action?pageId=90790348
     * @apiName update-noble-coin-expire-time
     * @apiGroup live
     *
     * @apiParam {Number} user_id 用户 ID
     * @apiParam {Number} expire_time 贵族有效期截止时间点（含），秒级时间戳
     * @apiParam {Number} [is_trial=0] 是否是体验贵族（0 否，1 是）
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "msg": "更新成功"
     *       }
     *     }
     */
    public function actionUpdateNobleCoinExpireTime()
    {
        $user_id = (int)Yii::$app->request->post('user_id');
        $expire_time = (int)Yii::$app->request->post('expire_time');
        $is_trial = (bool)Yii::$app->request->post('is_trial');
        if ($is_trial) {
            $user_vip_list = Yii::$app->sso->getUserVipList(
                [UserNoble::TYPE_LIVE_VIP_NOBLE, UserNoble::TYPE_LIVE_VIP_HIGHNESS],
                $user_id, 1
            );
            // 如果当前没有处于有效期的贵族，则直接返回，否则更新贵族钻石有效期
            if (empty($user_vip_list)) {
                return [
                    'msg' => '更新成功',
                ];
            }
        }

        PayAccount::updateNobleCoinExpireTime($user_id, $expire_time, $is_trial);
        return [
            'msg' => '更新成功',
        ];
    }

    /**
     * @api {post} /rpc/live/buy-super-fan 购买超粉
     * @apiName buy-super-fan
     * @apiGroup live
     *
     * @apiParam {Number} from_id 购买者用户 ID
     * @apiParam {Number} to_id 主播用户 ID
     * @apiParam {Number} gift_id 超粉价目 ID
     * @apiParam {Number} price 总价格（钻石）
     * @apiParam {String} title 超粉价目名称
     * @apiParam {number=0,1} [renew=0] 是否为续费，0 为开通，1 为续费
     * @apiParam {Number} [num=1] 购买的数量
     *
     * @apiParam {String} live_open_log_id 直播场次
     * @apiParam {String} user_agent 设备 User-Agent
     * @apiParam {String} ip 终端 IP
     * @apiParam {String} [equip_id] 设备 equip_id
     * @apiParam {String} [buvid] 设备 buvid
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "transaction_id": 83452,
     *         "balance": 3000,
     *         "live_noble_balance": 1000,
     *         "price": 200
     *       }
     *     }
     *
     * @apiError (400) {Number} code *********
     * @apiError (400) {String} info 参数错误
     *
     * @apiError (403) {Number} code 200020002
     * @apiError (403) {String} info 不能开通自己房间的超粉哦~
     */
    public function actionBuySuperFan()
    {
        $form = new TransactionFormSuperFan();
        $form->load(Yii::$app->request->post(), '');
        $form->user_context = UserContext::fromRpc(Yii::$app->request);
        if ($msg = $form->buy()) {
            return $msg;
        }

        throw new HttpException(400, MUtils::getFirstError($form));
    }

    /**
     * @api {post} /rpc/live/buy-fukubukuro 购买福袋
     * @apiName buy-fukubukuro
     * @apiGroup live
     *
     * @apiParam {Number} from_id 购买者 ID
     * @apiParam {Number} gift_id 福袋商品 ID
     * @apiParam {Number} price 价格（钻石）
     * @apiParam {String} title 福袋名称
     * @apiParam {number=0,1} noble 是否可以使用贵族钻石
     *
     * @apiParam {String} live_open_log_id 直播场次
     * @apiParam {String} user_agent 设备 User-Agent
     * @apiParam {String} ip 终端 IP
     * @apiParam {String} [equip_id] 设备 equip_id
     * @apiParam {String} [buvid] 设备 buvid
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "transaction_id": 10095893,
     *         "balance": 22000,
     *         "live_noble_balance": 0,
     *         "price": 3000,
     *         "context": "{\"transaction_id\":10095893,\"tax\":12.5,\"price\":2000,\"attr\":7,\"type\":9,\"common_coins\":{\"ios\":400,\"android\":500,\"tmallios\":100}}"
     *       }
     *     }
     *
     * @apiError (400) {Number} code *********
     * @apiError (400) {String} info 参数错误
     */
    public function actionBuyFukubukuro()
    {
        $form = new TransactionFormFukubukuro(TransactionFormFukubukuro::SCENARIO_BUY);
        $form->load(Yii::$app->request->post(), '');
        $form->user_context = UserContext::fromRpc(Yii::$app->request);
        if ($msg = $form->buy()) {
            return $msg;
        }

        throw new HttpException(400, MUtils::getFirstError($form));
    }

    /**
     * @api {post} /rpc/live/send-rebate-gifts 赠送白给礼物
     * @apiName send-rebate-gifts
     * @apiGroup live
     *
     * @apiParam {Number} from_id 购买者 ID
     * @apiParam {Number} to_id 收礼者 ID
     * @apiParam {Object[]} gifts 礼物信息
     * @apiParam (gifts[Number]) {Number} id 礼物 ID
     * @apiParam (gifts[Number]) {String} title 礼物标题
     * @apiParam (gifts[Number]) {Number} price 礼物单价（钻石）
     * @apiParam (gifts[Number]) {Number} num 礼物数量
     * @apiParam (gifts[Number]) {String} context 礼物信息 context
     *
     * @apiParam {String} live_open_log_id 直播场次
     * @apiParam {String} user_agent 设备 User-Agent
     * @apiParam {String} ip 终端 IP
     * @apiParam {String} [equip_id] 设备 equip_id
     * @apiParam {String} [buvid] 设备 buvid
     *
     * @apiParamExample {json} Request-Example:
     *     {
     *       "from_id": 4711,
     *       "to_id": 24,
     *       "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
     *       "ip": "************",
     *       "live_open_log_id": "900150983cd24fb0d6963f7d28e",
     *       "gifts": [
     *         {
     *           "id": 54,
     *           "title": "福袋礼物 A",
     *           "price": 200,
     *           "num": 1,
     *           "context": "{\"transaction_id\":247005,\"tax\":12.5,\"price\":2000,\"common_coins\":{\"ios\":400,\"android\":500,\"tmallios\":100}}"
     *         },
     *         {
     *           "id": 45,
     *           "title": "福袋礼物 A",
     *           "price": 500,
     *           "num": 2,
     *           "context": "{\"transaction_id\":247173,\"tax\":6.24,\"price\":1200,\"common_coins\":{\"ios\":1000}}"
     *         }
     *       ]
     *     }
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "transaction_id": 10095893,
     *         "balance": 22000,
     *         "live_noble_balance": 0
     *       }
     *     }
     *
     * @apiError (400) {Number} code *********
     * @apiError (400) {String} info 参数错误
     */
    public function actionSendRebateGifts()
    {
        $data = Yii::$app->request->post();
        // WORKAROUND: 兼容 /rpc/live/send-rebate-gift 参数，业务不再调用 /rpc/live/send-rebate-gift 后去除此兼容
        if (!array_key_exists('gifts', $data)) {
            $gift = Gift::findOne(['id' => $data['gift_id'], 'type' => Gift::TYPE_LIVE_REBATE_GIFT]);
            if (!$gift) {
                throw new HttpException(404, '该礼物不存在', *********);
            }

            $data['gifts'] = [
                [
                    'id' => $gift->id,
                    'title' => $gift->name,
                    'price' => $gift->price,
                    'num' => $data['num'] ?? 1,
                    'context' => $data['context'] ?? null,
                ],
            ];
            unset($data['gift_id'], $data['num'], $data['context']);
        }

        $form = new TransactionFormRebateGift();
        $form->load($data, '');
        $form->user_context = UserContext::fromRpc(Yii::$app->request);
        if ($msg = $form->sendGift()) {
            return $msg;
        }

        throw new HttpException(400, MUtils::getFirstError($form));
    }

    /**
     * @api {post} /rpc/live/buy-lucky-gift 赠送随机礼物
     * @apiName buy-lucky-gift
     * @apiGroup live
     *
     * @apiParam {Number} from_id 购买者 ID
     * @apiParam {Number} to_id 收礼者 ID
     * @apiParam {Number} gift_id 幸运签 ID
     * @apiParam {Number} price 幸运签价格（钻石）
     * @apiParam {Number} num 幸运签数量
     * @apiParam {String} title 宝箱礼物名称，例：魔法王冠（幸运签）
     * @apiParam {Number} income 主播收入（单位：分）
     * @apiParam {number=0,1} noble 是否可以使用贵族钻石
     * @apiParam {Object} more 额外信息
     *
     * @apiParam {String} live_open_log_id 直播场次
     * @apiParam {String} user_agent 设备 User-Agent
     * @apiParam {String} ip 终端 IP
     * @apiParam {String} [equip_id] 设备 equip_id
     * @apiParam {String} [buvid] 设备 buvid
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "transaction_id": 10095893,
     *         "balance": 22000,
     *         "live_noble_balance": 0,
     *         "price": 3000
     *       }
     *     }
     *
     * @apiError (400) {Number} code *********
     * @apiError (400) {String} info 参数错误
     */
    public function actionBuyLuckyGift()
    {
        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_LUCKY_GIFT]);
        $form->load(Yii::$app->request->post(), '');
        $form->user_context = UserContext::fromRpc(Yii::$app->request);
        if ($msg = $form->buyLuckyGift()) {
            return $msg;
        }

        throw new HttpException(400, MUtils::getFirstError($form));
    }

    /**
     * @api {post} /rpc/live/buy-gashapon 购买超能魔盒
     * @apiName buy-gashapon
     * @apiGroup live
     *
     * @apiParam {Number} from_id 购买者 ID
     * @apiParam {Number} to_id 收礼者 ID
     * @apiParam {number=0,1} noble 是否可以使用贵族钻石
     *
     * @apiParam {Object} goods 超能魔盒
     * @apiParam (goods) {Number} id 商品 ID
     * @apiParam (goods) {String} title 标题
     * @apiParam (goods) {Number} total_price 总价（钻石）
     * @apiParam (goods) {Number} num 数量（连抽数量）
     *
     * @apiParam {Object[]} gifts 礼物信息
     * @apiParam (gifts[]) {Number} id 礼物 ID
     * @apiParam (gifts[]) {String} title 标题
     * @apiParam (gifts[]) {Number} price 礼物单价（钻石）
     * @apiParam (gifts[]) {Number} num 礼物数量
     *
     * @apiParam {String} live_open_log_id 直播场次
     * @apiParam {String} user_agent 设备 User-Agent
     * @apiParam {String} ip 终端 IP
     * @apiParam {String} [equip_id] 设备 equip_id
     * @apiParam {String} [buvid] 设备 buvid
     *
     * @apiParamExample {json} Request-Example:
     *     {
     *       "from_id": 24711,
     *       "to_id": 346286,
     *       "noble": 1,
     *       "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
     *       "ip": "************",
     *       "live_open_log_id": "900150983cd24fb0d6963f7d28e",
     *       "goods": {
     *         "id": 6,
     *         "title": "超能魔盒 100 连",
     *         "total_price": 1000,
     *         "num": 100,
     *       },
     *       "gifts": [
     *         {
     *           "id": 54,
     *           "title": "凤凰",
     *           "price": 2000,
     *           "num": 1
     *         },
     *         {
     *           "id": 45,
     *           "title": "情书",
     *           "price": 10,
     *           "num": 2
     *         }
     *       ]
     *     }
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "transaction_id": 10095893,
     *         "balance": 22000,
     *         "live_noble_balance": 0,
     *         "price": 1000
     *       }
     *     }
     *
     * @apiError (400) {Number} code *********
     * @apiError (400) {String} info 参数错误
     */
    public function actionBuyGashapon()
    {
        $form = new TransactionFormGashapon(['scenario' => TransactionFormGashapon::SCENARIO_BUY]);
        $form->load(Yii::$app->request->post(), '');
        $form->user_context = UserContext::fromRpc(Yii::$app->request);
        if ($msg = $form->buy()) {
            return $msg;
        }

        throw new HttpException(400, MUtils::getFirstError($form));
    }

    /**
     * @api {post} /rpc/live/buy-goods 购买商品
     * @apiName buy-goods
     * @apiGroup live
     * @@apiDescription 对于购买可退回的商品（如飞镖），需要调用 refund-goods 接口后才会确认订单并完成钻石扣费，否则钻石处于冻结状态
     *
     * @apiParam {Number} buyer_id 购买者 ID
     * @apiParam {Number} receiver_id 收礼者 ID
     *
     * @apiParam {Object} package_info 商品组合信息
     * @apiParam (package_info) {Number} id 商品组合 ID
     * @apiParam (package_info) {String} title 商品组合标题
     * @apiParam (package_info) {Number} price 商品组合价格（单位：钻石）
     * @apiParam (package_info) {Number} num 数量
     *
     * @apiParam {number=1,2,3,4,5,6} goods_type 商品大类：1 礼物红包，2 许愿池，3 直播付费弹幕，4 福袋，5 宝盒，6 可退回的商品（如飞镖）
     * @apiParam {Object[]} goods 商品
     * @apiParam (goods) {Number} id 商品 ID
     * @apiParam (goods) {String} title 标题
     * @apiParam (goods) {Number} price 礼物单价（单位：钻石）
     * @apiParam (goods) {Number} num 数量
     * @apiParam (goods) {number=1,2} [transaction_type=1] 交易类型（1 直播间，2 剧集）
     *
     * @apiParam {number=0,1} noble 是否可以使用贵族钻石
     *
     * @apiParam {String} [live_open_log_id] 直播场次
     * @apiParam {String} user_agent 设备 User-Agent
     * @apiParam {String} ip 终端 IP
     * @apiParam {String} [equip_id] 设备 equip_id
     * @apiParam {String} [buvid] 设备 buvid
     *
     * @apiParamExample {json} Request-Example: 礼物红包
     *     {
     *       "buyer_id": 24711,
     *       "receiver_id": 346286,
     *       "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
     *       "ip": "************",
     *       "live_open_log_id": "900150983cd24fb0d6963f7d28e",
     *       "noble": 0,
     *       "goods_type": 1,
     *       "goods": [{
     *         "id": 39,
     *         "title": "恋爱告急",
     *         "price": 500,
     *         "num": 1
     *       }]
     *     }
     *
     * @apiParamExample {json} Request-Example: 许愿池
     *     {
     *       "buyer_id": 24711,
     *       "receiver_id": 0,
     *       "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
     *       "ip": "************",
     *       "live_open_log_id": "900150983cd24fb0d6963f7d28e",
     *       "noble": 1,
     *       "goods_type": 2,
     *       "goods": [{
     *         "id": 62,
     *         "title": "梦墟感应",
     *         "price": 100,
     *         "num": 1
     *       }]
     *     }
     *
     * @apiParamExample {json} Request-Example: 直播付费弹幕
     *     {
     *       "buyer_id": 24711,
     *       "receiver_id": 346286,
     *       "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
     *       "ip": "************",
     *       "live_open_log_id": "900150983cd24fb0d6963f7d28e",
     *       "noble": 1,
     *       "goods_type": 3,
     *       "goods": [{
     *         "id": 77,
     *         "title": "弹幕",
     *         "price": 10,
     *         "num": 1
     *       }]
     *     }
     *
     * @apiParamExample {json} Request-Example: 购买福袋
     *     {
     *       "buyer_id": 24711,
     *       "receiver_id": 0,
     *       "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
     *       "ip": "************",
     *       "live_open_log_id": "900150983cd24fb0d6963f7d28e",
     *       "goods_type": 4,
     *       "package_info": {
     *         "id": 6,  // 福袋 ID
     *         "title": "广播剧福袋",
     *         "price": 777,
     *         "num": 1
     *       },
     *       "goods": [{  // 目前只支持同一种剧集
     *         "id": 15861,
     *         "title": "魔道祖师第一季",
     *         "price": 259,
     *         "num": 2,
     *         "transaction_type": 1
     *       },
     *       {
     *         "id": 19059,
     *         "title": "魔道祖师第一季",
     *         "price": 259,
     *         "num": 1,
     *         "transaction_type": 1
     *       }]
     *     }
     *
     * @apiParamExample {json} Request-Example: 购买宝盒
     *     {
     *       "buyer_id": 24711,
     *       "receiver_id": 346286,
     *       "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
     *       "ip": "************",
     *       "live_open_log_id": "900150983cd24fb0d6963f7d28e",
     *       "goods_type": 5,
     *       "goods": [{
     *         "id": 15861,
     *         "title": "宝盒名称",
     *         "price": 259,
     *         "num": 1
     *       }]
     *     }
     *
     * @apiParamExample {json} Request-Example: 购买可退回商品
     *     {
     *       "buyer_id": 24711,
     *       "user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15",
     *       "ip": "************",
     *       "live_open_log_id": "900150983cd24fb0d6963f7d28e",  // 可选
     *       "goods_type": 6,
     *       "noble": 1,
     *       "goods": [{
     *         "id": 11,  // 商品 ID，值由猫耳业务侧提供
     *         "title": "飞镖",  // 商品标题，值由猫耳业务侧提供
     *         "price": 20,  // 商品单价，值由猫耳业务侧提供
     *         "num": 5  // 商品购买数量
     *       }]
     *     }
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "transaction_id": 10095893,
     *         "balance": 22000,
     *         "live_noble_balance": 0,
     *         "price": 3000,
     *         "context": "{\"transaction_id\":10095893,\"tax\":12.5,\"price\":2000,\"attr\":11,\"type\":9,\"common_coins\":{\"ios\":400,\"android\":500,\"tmallios\":100}}"
     *       }
     *     }
     *
     * @apiError (400) {Number} code *********
     * @apiError (400) {String} info 参数错误
     */
    public function actionBuyGoods()
    {
        $buyer_id = (int)Yii::$app->request->post('buyer_id');
        $receiver_id = (int)Yii::$app->request->post('receiver_id');
        $goods_type = (int)Yii::$app->request->post('goods_type');
        $goods = Yii::$app->request->post('goods');
        $live_open_log_id = Yii::$app->request->post('live_open_log_id', null);
        $noble = (int)Yii::$app->request->post('noble');

        $form = TransactionFormLive::newTransactionForm($goods_type);
        $form->user_context = UserContext::fromRpc(Yii::$app->request);
        if ($goods_type === TransactionFormLive::GOODS_TYPE_LUCKY_BAG) {
            // 不支持使用贵族钻石消费
            $params = [
                'from_id' => $buyer_id,
                'to_id' => $receiver_id,
                'package_info' => Yii::$app->request->post('package_info'),
                'goods' => Yii::$app->request->post('goods'),
            ];
        } else {
            if (count($goods) !== 1) {
                throw new HttpException(400, '暂时只支持送出单个礼物');
            }
            // 支持使用贵族钻石消费
            $params = [
                'from_id' => $buyer_id,
                'to_id' => $receiver_id,
                'gift_id' => $goods[0]['id'],
                'price' => $goods[0]['price'] * $goods[0]['num'],
                'title' => $goods[0]['title'],
                'num' => $goods[0]['num'],
                'noble' => $noble,
            ];
        }
        if (!is_null($live_open_log_id)) {
            $params['live_open_log_id'] = $live_open_log_id;
        }

        $form->load($params, '');
        if (!$msg = $form->buy()) {
            throw new HttpException(400, MUtils::getFirstError($form));
        }

        return $msg;
    }

    /**
     * @api {post} /rpc/live/refund-goods 商品退款
     * @apiName refund-goods
     * @apiGroup live
     *
     * @apiParam {Number} [transaction_id] 交易 ID
     * @apiParam {Number[]} [transaction_ids] 交易 ID 列表
     * @apiParam {number=4,6} goods_type 商品大类：4 福袋、6 可退回商品（如飞镖）
     * @apiParam {number=0,1} noble 是否可以使用贵族钻石
     *
     * @apiParam {Object[]} goods 需要退款的商品
     * @apiParam (goods) {Number} id 商品 ID
     * @apiParam (goods) {String} title 标题
     * @apiParam (goods) {Number} price 礼物单价（钻石）
     * @apiParam (goods) {Number} num 数量
     * @apiParam (goods) {number=1,2} transaction_type 交易类型（1 直播间，2 剧集）
     *
     * @apiParamExample {json} Request-Example: 剧集福袋退钻参数
     *     {
     *       "transaction_id": 247154381,
     *       "goods_type": 4,
     *       "goods": [{
     *         "id": 15861,
     *         "title": "魔道祖师第一季",
     *         "price": 259,
     *         "num": 1,
     *         "transaction_type": 2
     *       },
     *       {
     *         "id": 19059,
     *         "title": "魔道祖师第二季",
     *         "price": 339,
     *         "num": 1,
     *         "transaction_type": 2
     *       }]
     *     }
     * @apiSuccessExample Success-Response: 剧集福袋退钻响应
     *     {
     *       "code": 0,
     *       "info": {
     *         "transaction_id": 10095893,
     *         "balance": 22000
     *       }
     *     }
     *
     * @apiParamExample {json} Request-Example: 可退回商品退钻或确认
     *     {
     *       "transaction_ids": [247154381, 230153274],
     *       "noble": 1,
     *       "goods_type": 6,
     *       "goods": [{
     *         "id": 11,  // 商品 ID，值由猫耳业务侧提供
     *         "title": "飞镖",  // 商品标题，值由猫耳业务侧提供
     *         "price": 20,  // 商品单价，值由猫耳业务侧提供
     *         "num": 3  // 退回的数量，没有需要退钻的商品时，传 0 用于确认订单
     *       }]
     *     }
     *
     * @apiSuccessExample Success-Response: 可退回商品退钻或确认响应
     *     {
     *       "code": 0,
     *       "info": {
     *         "balance": 22000,  // 普通钻石余额
     *         "live_noble_balance": 5000,  // 贵族钻石余额
     *         "cancel_order_ids": [189093, 19982],  // 退款的订单 ID
     *         "confirm_order_ids": [20934]  // 确认的订单 ID
     *       }
     *     }
     */
    public function actionRefundGoods()
    {
        $goods_type = (int)Yii::$app->request->post('goods_type');
        $goods = Yii::$app->request->post('goods');
        $noble = (int)Yii::$app->request->post('noble');

        switch ($goods_type) {
            case TransactionFormLive::GOODS_TYPE_LUCKY_BAG:
                $params = [
                    'transaction_id' => (int)Yii::$app->request->post('transaction_id'),
                    'goods' => $goods,
                ];
                break;
            case TransactionFormLive::GOODS_TYPE_LIVE_TOKEN:
                $params = [
                    'transaction_ids' => Yii::$app->request->post('transaction_ids'),
                    'goods' => $goods,
                    'noble' => $noble,
                ];
                break;
            default:
                throw new HttpException(400, '参数错误');
        }

        $form = TransactionFormLive::newTransactionForm($goods_type, TransactionFormLive::SCENARIO_REFUND);
        $form->load($params, '');
        if (!$msg = $form->refund()) {
            throw new HttpException(400, MUtils2::getFirstError($form));
        }

        return $msg;
    }

    /**
     * @api {post} /rpc/live/ask 付费问答
     * @apiName ask
     * @apiGroup live
     *
     * @apiParam {Number} from_id 提问者 ID
     * @apiParam {Number} to_id 回答者 ID
     * @apiParam {Number} price 提问者悬赏金额
     * @apiParam {number=0,1} noble 是否可以使用贵族金额
     *
     * @apiParam {String} live_open_log_id 直播场次
     * @apiParam {String} user_agent 设备 User-Agent
     * @apiParam {String} ip 终端 IP
     * @apiParam {String} [equip_id] 设备 equip_id
     * @apiParam {String} [buvid] 设备 buvid
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "transaction_id": 10095893,
     *         "balance": 22000,
     *         "live_noble_balance": 0,
     *         "price": 1000
     *       }
     *     }
     *
     */
    public function actionAsk()
    {
        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_FREEZE]);
        $form->load(Yii::$app->request->post(), '');
        $form->user_context = UserContext::fromRpc(Yii::$app->request);
        if ($msg = $form->freeze()) {
            return $msg;
        }
        throw new HttpException(400, MUtils::getFirstError($form));
    }

    /**
     * @api {post} /rpc/live/confirm-ask 付费问答确认
     * @apiName confirm-ask
     * @apiGroup live
     *
     * @apiParam {Number} transaction_id 回答订单 id
     * @apiParam {Number} to_id 回答者 id
     * @apiParam {boolean} sure 回答或取消
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "transaction_id": 10095893,
     *         "price": 1000
     *       }
     *     }
     */
    public function actionConfirmAsk()
    {
        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_CONFIRM]);
        $form->load(Yii::$app->request->post(), '');
        $form->transaction_id = (int)$form->transaction_id;
        if ($msg = $form->confirmTransaction()) {
            return $msg;
        }
        throw new HttpException(400, MUtils::getFirstError($form));
    }

    /**
     * @api {post} /rpc/live/cancel-asks 付费问答批量取消
     * @apiName cancel-asks
     * @apiGroup live
     *
     * @apiParam {Number[]} transaction_ids 回答订单 id 数组
     * @apiParam {Number} to_id 回答者 id
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "transactions": [{
     *           "transaction_id": 866115,
     *           "price": 400
     *         }],
     *         "balance": 22000
     *       }
     *     }
     */
    public function actionCancelAsks()
    {
        $transaction_ids = Yii::$app->request->post('transaction_ids');
        $transaction_ids = is_array($transaction_ids) ? $transaction_ids : [];
        $to_id = (int)Yii::$app->request->post('to_id');
        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_CONFIRM]);
        $form->sure = false;
        $form->to_id = $to_id;
        $msgs = false;
        foreach ($transaction_ids as $key => $transaction_id) {
            $form->transaction_id = (int)$transaction_id;
            try {
                if ($msg = $form->confirmTransaction()) {
                    $msgs['transactions'][$key]['transaction_id'] = $msg['transaction_id'];
                    $msgs['transactions'][$key]['price'] = $msg['price'];
                }
            } catch (\Exception $e) {
                $msgs['transactions'][$key]['error'] = $e->getMessage();
            }
        }
        if ($msgs) {
            $msgs['balance'] = Balance::getByPk($to_id)->getTotalBalance();
        }
        return $msgs;
    }

    /**
     * @api {post} /rpc/live/buy-wish-goods 许愿池
     * @apiName buy-wish-goods
     * @apiGroup live
     *
     * @apiParam {Number} from_id 购买者 ID
     * @apiParam {Number} gift_id 礼物 ID
     * @apiParam {Number} price 总价（钻石）
     * @apiParam {Number} num 数量
     * @apiParam {String} title 礼物名称
     * @apiParam {number=0,1} noble 是否可以使用贵族钻石
     *
     * @apiParam {String} live_open_log_id 直播场次
     * @apiParam {String} user_agent 设备 User-Agent
     * @apiParam {String} ip 终端 IP
     * @apiParam {String} [equip_id] 设备 equip_id
     * @apiParam {String} [buvid] 设备 buvid
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "transaction_id": 10095893,
     *         "balance": 22000,
     *         "live_noble_balance": 0,
     *         "price": 3000,
     *         "context": "{\"transaction_id\":10095893,\"tax\":12.5,\"price\":2000,\"common_coins\":{\"ios\":400,\"android\":500,\"tmallios\":100}}"
     *       }
     *     }
     *
     * @apiError (400) {Number} code *********
     * @apiError (400) {String} info 参数错误
     */
    public function actionBuyWishGoods()
    {
        $form = new TransactionFormWishGoods(TransactionFormWishGoods::SCENARIO_BUY);
        $form->load(Yii::$app->request->post(), '');
        $form->user_context = UserContext::fromRpc(Yii::$app->request);
        if (!$msg = $form->buy()) {
            throw new HttpException(400, MUtils::getFirstError($form));
        }

        return $msg;
    }

    /**
     * @deprecated 新版本 Android >= 6.0.7, iOS >= 6.0.7 不再显示贵族钻石持有明细页面
     * @api {post} /rpc/live/balance-details 获取贵族钻石持有明细
     * @apiName balance-details
     * @apiGroup live
     *
     * @apiParam {Number} user_id 回答者 id
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": [{
     *         "expire_date": "2022-04-30",
     *         "balance": 432,
     *         "has_expired": false
     *       }]
     *     }
     */
    public function actionBalanceDetails()
    {
        $user_id = (int)Yii::$app->request->post('user_id');
        return PayAccount::getNobleBalanceDetails($user_id);
    }

    /**
     * @api {post} /rpc/live/get-expire-balance-by-time 获取贵族在给定时间段到期钻石总额
     *
     * @apiVersion 0.1.0
     * @apiName get-expire-balance-by-time
     * @apiGroup rpc/live
     *
     * @apiParam {Number} start_time 开始时间戳（单位：秒）
     * @apiParam {Number} end_time 结束时间戳（单位：秒）
     * @apiParam {Number} type 类型（0 取过期的钻石，1 取清零的钻石，即超过贵族钻石冻结期的钻石）
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": [{
     *         "user_id": 12,
     *         "total_balance": 1000
     *       }]
     *     }
     */
    public function actionGetExpireBalanceByTime()
    {
        $start_time = (int)Yii::$app->request->post('start_time');
        $end_time = (int)Yii::$app->request->post('end_time');
        $type = (int)Yii::$app->request->post('type');
        if ($end_time <= $start_time) {
            throw new HttpException(400, '参数错误');
        }
        return PayAccount::getExpireBalanceByTime($start_time, $end_time, $type);
    }

    /**
     * @api {post} /rpc/live/video
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/rpc/live/video
     * @apiSampleRequest /rpc/live/video
     *
     * @apiVersion 0.1.0
     * @apiName video
     * @apiGroup rpc/live
     *
     * @apiParam {String} playback_id
     * @apiParam {String} title 音频标题
     * @apiParam {String} description 音频简介
     * @apiParam {String} cover 音频封面（OSS）地址
     * @apiParam {String} record 音频地址，可能为 OSS 地址（不带协议链接）或 UPOS 地址
     * @apiParam {Number} create_time 创建时间
     * @apiParam {Number} user_id 用户 ID
     * @apiParam {String} username 用户昵称
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "sound_id": 1  // 音频 ID，若为 0 表示该任务进入了视频转音频队列（record 非 UPOS 地址时）
     *       }
     *     }
     */
    public function actionVideo()
    {
        $post = Yii::$app->request->post();
        $sound_url = trim($post['record']);
        $user_id = (int)$post['user_id'];
        if (!$sound_url || !$user_id || !$post['cover'] || !$post['record'] || !$post['title']
                || !$post['playback_id']) {
            Yii::error('保存直播回放参数错误：' . Json::encode($post), __METHOD__);
            throw new HttpException(400, '参数错误');
        }
        if (MUtils::isUposUrl($sound_url)) {
            // 若为 UPOS 音频地址，则直接保存为音频
            $now = $_SERVER['REQUEST_TIME'];
            $username = Mowangskuser::find()->select('username')->where('id = :id', [':id' => $user_id])->scalar();
            $new_sound = new MSound();
            $new_sound->user_id = $user_id;
            $new_sound->username = $username ?? $post['username'];
            $new_sound->catalog_id = Catalog::CATALOG_ID_LIVE;
            $new_sound->soundstr = mb_substr($post['title'], 0, MSound::MAX_TITLE_LENGTH);
            $new_sound->intro = $post['description'];
            $new_sound->soundurl = $sound_url;
            $new_sound->type = MSound::TYPE_LIVE;
            $new_sound->create_time = $post['create_time'] ?? $now;
            $new_sound->last_update_time = $post['create_time'] ?? $now;
            // 图片地址
            $cover_img = trim($post['cover'] ?? '');
            if ($cover_img) {
                $download_path = MUtils2::makeLocalFilesDir();
                $img_path = $download_path . md5($cover_img . $now) . '.jpg';
                Yii::$app->storage->download($cover_img, $img_path);
                $new_sound->uploadCoverImage(7, $img_path, 'jpg');
            }
            if (!$new_sound->save()) {
                $error_info = MUtils::getFirstError($new_sound);
                Yii::error('直播回放音频保存失败：' . $error_info, __METHOD__);
                throw new HttpException(400, $error_info);
            }
            // 回调直播相关接口完成此次任务
            $sound_id = (int)$new_sound->id;
            $params = [
                'type' => 'playback',
                'event' => 'updated',
                'playback' => [
                    'playback_id' => $post['playback_id'],
                    'sound_id' => $sound_id,
                ]
            ];
            $service = Yii::$app->params['service'];
            try {
                Yii::$app->tools->requestApi('/api/channel/playback/notify', $params,
                    $service['audio-chatroom']['url'], $service['audio-chatroom']['key']);
            } catch (\Exception $e) {
                Yii::error("Request audio-chatroom rpc api fail: {$e->getMessage()}", __METHOD__);
                // PASS
            }
            return ['sound_id' => $sound_id];
        } else {
            $redis = Yii::$app->redis;
            $redis->lpush(KEY_LIVE_VIDEOS, Json::encode($post));
        }
        return ['sound_id' => 0];
    }

    /**
     * @api {post} /rpc/live/get-forbidden-keywords 获取违禁词列表
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/rpc/live/get-forbidden-keywords
     * @apiSampleRequest /rpc/live/get-forbidden-keywords
     * @apiDescription 获取违禁词列表
     * @apiVersion 0.1.0
     * @apiName get-forbidden-keywords
     * @apiGroup /rpc/live/
     *
     * @apiParam {number=0,1} [type=0] 屏蔽词类型 0：搜索屏蔽词；1：音单屏蔽词
     *
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "code": 0,
     *       "info": {
     *         "keywords": [
     *             "测试1",
     *             "测试2"
     *         ]
     *       }
     *     }
     */
    public function actionGetForbiddenKeywords()
    {
        $type = (int)Yii::$app->request->post('type', MUtils::FORBIDDEN_WORD_TYPE_SEARCH);
        $result = [
            'keywords' => MUtils::getForbiddenKeywords($type),
        ];
        return $result;
    }

    /**
     * @api {post} /rpc/live/add-listen-history 添加收听记录
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/rpc/live/add-listen-history
     * @apiSampleRequest /rpc/live/add-listen-history
     *
     * @apiVersion 0.1.0
     * @apiName add-listen-history
     * @apiGroup /rpc/live/
     *
     * @apiParam {Number} user_id 用户 ID
     * @apiParam {Number} room_id 房间 ID
     * @apiParam {Number} access_time 访问时间（毫秒级时间戳）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Boolean} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": true
     *     }
     */
    public function actionAddListenHistory()
    {
        $user_id = (int)Yii::$app->request->post('user_id');
        $room_id = (int)Yii::$app->request->post('room_id');
        $access_time = (int)Yii::$app->request->post('access_time');
        if ($user_id <= 0 || $room_id <= 0 || $access_time <= 0) {
            throw new HttpException(400, '参数错误');
        }

        MUserHistory::newLiveRoom($user_id, $access_time, $room_id);
        return true;
    }

    /**
     * @api {post} /rpc/live/add-blocklist 加入黑名单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/rpc/live/add-blocklist
     *
     * @apiVersion 0.1.0
     * @apiName add-blocklist
     * @apiGroup rpc/live
     *
     * @apiParam {Number} user_id 拉黑用户 ID
     * @apiParam {Number} block_user_id 被拉黑用户 ID
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": "成功加入黑名单"
     *     }
     */
    public function actionAddBlocklist()
    {
        // TODO: 迁移至 missevan-go rpc
        $user_id = (int)Yii::$app->request->post('user_id');
        $block_user_id = (int)Yii::$app->request->post('block_user_id');
        if ($user_id <= 0 || $block_user_id <= 0) {
            throw new HttpException(400, '参数错误');
        }
        BlackUser::checkSetBlacklist($block_user_id, $user_id);
        BlackUser::blacklistOrNot($user_id, $block_user_id, false);
        return '成功加入黑名单';
    }

    /**
     * @api {post} /rpc/live/remove-blocklist 移除黑名单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/rpc/live/remove-blocklist
     *
     * @apiVersion 0.1.0
     * @apiName remove-blocklist
     * @apiGroup rpc/live
     *
     * @apiParam {Number} user_id 拉黑用户 ID
     * @apiParam {Number} block_user_id 被拉黑用户 ID
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": "移除黑名单成功"
     *     }
     */
    public function actionRemoveBlocklist()
    {
        // TODO: 迁移至 missevan-go rpc
        $user_id = (int)Yii::$app->request->post('user_id');
        $block_user_id = (int)Yii::$app->request->post('block_user_id');
        if ($user_id <= 0 || $block_user_id <= 0) {
            throw new HttpException(400, '参数错误');
        }
        if ($user_id === $block_user_id) {
            throw new HttpException(403, '不允许取消拉黑自己');
        }
        if ((int)MowangskUser::find()->where(['id' => [$user_id, $block_user_id]])->count() !== 2) {
            throw new HttpException(404, '用户不存在');
        }
        BlackUser::blacklistOrNot($user_id, $block_user_id, true);
        return '移除黑名单成功';
    }
}
