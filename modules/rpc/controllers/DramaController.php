<?php

namespace app\modules\rpc\controllers;

use app\components\util\Equipment;
use app\components\util\MUtils;
use app\forms\TransactionFormRedeemDrama;
use app\forms\UserContext;
use app\models\Drama;
use app\models\DramaBoughtDetailLog;
use app\models\TransactionLog;
use app\forms\TransactionForm;
use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\web\Controller;
use yii\web\HttpException;

class DramaController extends Controller
{
    /**
     * @api {post} /rpc/drama/buy-drama 购买剧集
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/rpc/drama/buy-drama
     * @apiSampleRequest /rpc/drama/buy-drama
     * @apiDescription 购买剧集
     * @apiVersion 0.1.0
     * @apiName buy-drama
     * @apiGroup /rpc/drama/
     *
     * @apiParam {Number} drama_id 剧集 ID
     * @apiParam {Number} user_id 用户 ID
     * @apiParam {String} user_agent 设备 User-Agent
     * @apiParam {String} ip 终端 IP
     * @apiParam {Number} [origin=2] 请求来源 1: App; 2: Web
     *
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "code": 0,
     *       "info": {
     *         "transaction_id": 426244,
     *         "balance": 551,
     *         "price": 199
     *       }
     *     }
     */
    public function actionBuyDrama()
    {
        $drama_id = (int)Yii::$app->request->post('drama_id');
        $origin = (int)Yii::$app->request->post('origin', DramaBoughtDetailLog::ORIGIN_WEB);

        if ($drama_id <= 0
                || !in_array($origin, [DramaBoughtDetailLog::ORIGIN_WEB, DramaBoughtDetailLog::ORIGIN_APP])) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        if (MUtils::isJapan()
                && Drama::checkDramaRefined($drama_id, Drama::REFINED_NO_JAPAN_SALE)) {
            throw new HttpException(403, '該当地域でのご購入はできません');
        }
        $user_id = (int)Yii::$app->request->post('user_id');

        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_BUY]);
        $form->gift_id = $drama_id;
        $form->from_id = $user_id;
        $form->type = TransactionLog::TYPE_DRAMA;
        $form->user_context = UserContext::fromRpc(Yii::$app->request);

        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_USER_BUY_DRAMA, $user_id, $drama_id);
        if (!$redis->lock($lock, HALF_MINUTE)) {
            throw new HttpException(400, '操作过快，请稍候重试');
        }
        try {
            if ($form->validate() && $msg = $form->buyDrama($origin, false)) {
                // 购买成功后剧集自动加入追剧
                Drama::subscribe($drama_id, $user_id, 1);

                return $msg;
            }
            throw new HttpException(400, MUtils::getFirstError($form));
        } catch (Exception $e) {
            throw $e;
        } finally {
            $redis->unlock($lock);
        }
    }

    /**
     * @api {post} /rpc/drama/drama-bought 用户是否已购买过该剧集
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/rpc/drama/drama-bought
     * @apiSampleRequest /rpc/drama/drama-bought
     * @apiDescription 用户是否已购买过该剧集
     * @apiVersion 0.1.0
     * @apiName drama-bought
     * @apiGroup /rpc/drama/
     *
     * @apiParam {Number} drama_id 剧集 ID
     * @apiParam {Number} user_id 用户 ID
     *
     * @apiSuccess {Number} code
     * @apiSuccess {Boolean} info true or false
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "code": 0,
     *       "info": true
     *     }
     */
    public function actionDramaBought()
    {
        $drama_id = (int)Yii::$app->request->post('drama_id');
        // TODO: 由 Cookie 中的 token 获取 user_id
        $user_id = (int)Yii::$app->request->post('user_id');

        if (TransactionLog::find()->where([
            'from_id' => $user_id,
            'gift_id' => $drama_id,
            'type' => TransactionLog::TYPE_DRAMA,
            'status' => TransactionLog::STATUS_SUCCESS
        ])->exists()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @api {post} /rpc/drama/buy-drama-episodes 购买单集
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/rpc/drama/buy-drama-episodes
     * @apiSampleRequest /rpc/drama/buy-drama-episodes
     * @apiDescription 购买单集
     * @apiVersion 0.1.0
     * @apiName buy-drama-episodes
     * @apiGroup /rpc/drama/
     *
     * @apiParam {String} sound_ids 音频 IDs 每个音频 ID 用半角逗号分隔，例：1,2,3
     * @apiParam {Number} drama_id 剧集 ID
     * @apiParam {Number} user_id 用户 ID
     * @apiParam {String} user_agent 设备 User-Agent
     * @apiParam {String} ip 终端 IP
     * @apiParam {Number} [origin=2] 请求来源 1: App; 2: Web
     *
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "code": 0,
     *       "info": {
     *         "transaction_id": 212,
     *         "balance": 1002,
     *         "price": 20
     *       }
     *     }
     */
    public function actionBuyDramaEpisodes()
    {
        $sound_ids = Yii::$app->request->post('sound_ids');
        $origin = (int)Yii::$app->request->post('origin', DramaBoughtDetailLog::ORIGIN_WEB);

        if (!in_array($origin, [DramaBoughtDetailLog::ORIGIN_WEB, DramaBoughtDetailLog::ORIGIN_APP])) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        if (is_string($sound_ids)) {
            // WORKAROUND: 兼容传递数组和字符串两种类型，前端调整类型后可删除判断
            $sound_ids = explode(',', trim($sound_ids));
        }
        $drama_id = (int)Yii::$app->request->post('drama_id');
        $user_id = (int)Yii::$app->request->post('user_id');

        if (empty($sound_ids) || !$drama_id) {
            throw new HttpException(400, '请选择需要购买的单集或剧集');
        }
        if ($drama_id <= 0 || !MUtils2::isUintArr($sound_ids)) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        if (MUtils::isJapan()
                && Drama::checkDramaRefined($drama_id, Drama::REFINED_NO_JAPAN_SALE)) {
            throw new HttpException(403, '該当地域でのご購入はできません');
        }

        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_BUY]);
        $form->gift_id = $drama_id;
        $form->from_id = $user_id;
        $form->type = TransactionLog::TYPE_SOUND;
        $form->user_context = UserContext::fromRpc(Yii::$app->request);
        $sound_ids = array_values(array_unique(array_map('intval', $sound_ids)));

        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_USER_BUY_DRAMA, $user_id, $drama_id);
        if (!$redis->lock($lock, HALF_MINUTE)) {
            throw new HttpException(400, '操作过快，请稍候重试');
        }
        try {
            if ($form->validate() && $msg = $form->buyDramaEpisodes($sound_ids, $origin)) {
                // 购买成功后剧集自动加入追剧
                Drama::subscribe($drama_id, $user_id, 1);

                // 购买单集剧集时，当存在隐藏的单集剧集购买订单，需要把该剧集的所有单集剧集从隐藏列表中移除
                $exists = TransactionLog::hasHideDramaPurchaseOrder($user_id, $drama_id);
                if ($exists) {
                    TransactionLog::recoverDramaPurchaseOrder($user_id, [$drama_id]);
                }

                return $msg;
            }
            throw new HttpException(400, MUtils::getFirstError($form));
        } catch (Exception $e) {
            throw $e;
        } finally {
            $redis->unlock($lock);
        }
    }

    /**
     * @api {post} /rpc/drama/get-paid-dramas 获取用户已购买的剧集
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/rpc/drama/get-paid-dramas
     * @apiSampleRequest /rpc/drama/get-paid-dramas
     * @apiDescription 获取用户已购买的剧集
     * @apiVersion 0.1.0
     * @apiName get-paid-dramas
     * @apiGroup /rpc/drama/
     *
     * @apiParam {Number} user_id 用户 ID
     *
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "code": 0,
     *       "info": [
     *         9888
     *       ]
     *     }
     */
    public function actionGetPaidDramas()
    {
        $user_id = (int)Yii::$app->request->post('user_id');
        if (!$user_id) {
            throw new HttpException(400, '参数错误');
        }
        $paid_drama_ids = TransactionLog::find()
            ->select('DISTINCT(gift_id)')
            ->where([
                'type' => [TransactionLog::TYPE_SOUND, TransactionLog::TYPE_DRAMA],
                'from_id' => $user_id,
                'status' => TransactionLog::STATUS_SUCCESS,
            ])
            ->column();
        $paid_drama_ids = array_map('intval', $paid_drama_ids);
        return $paid_drama_ids;
    }

    /**
     * @api {post} /rpc/drama/add-drama-transaction-record 添加剧集券 0 元消费记录
     * @apiVersion 0.1.0
     * @apiName add-drama-transaction-record
     * @apiGroup /rpc/drama/
     *
     * @apiParam {Number} user_id 用户 ID（接收剧集的用户）
     * @apiParam {Number} operator_id 发起兑换的用户 ID
     * @apiParam {Number[]} drama_ids 剧集 IDs
     * @apiParam {number=0,1} scene 场景（0 普通兑换码兑换，1 福袋广播剧兑换）
     * @apiParam {Number} context_transaction_id 福袋广播剧兑换时传购买福袋的交易记录 ID
     * @apiParam {String} user_agent 设备 User-Agent
     * @apiParam {String} ip 终端 IP
     * @apiParam {String} [equip_id] 设备 equip_id
     * @apiParam {String} [buvid] 设备 buvid
     *
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "transaction_ids": [648290]
     *       }
     *     }
     */
    public function actionAddDramaTransactionRecord()
    {
        $user_id = (int)Yii::$app->request->post('user_id');
        $operator_id = (int)Yii::$app->request->post('operator_id');
        $drama_ids = Yii::$app->request->post('drama_ids');
        $scene = (int)Yii::$app->request->post('scene');
        $context_transaction_id = (int)Yii::$app->request->post('context_transaction_id');

        if ($user_id <= 0 || !MUtils2::isUintArr($drama_ids)) {
            throw new HttpException(400, '参数错误');
        }

        $redeem_type = 0;
        switch ($scene) {
            case 0:
                $scenario = TransactionFormRedeemDrama::SCENARIO_DEFAULT;
                $redeem_type = DramaBoughtDetailLog::REDEEM_TYPE_NORMAL;
                break;
            case 1:
                $scenario = TransactionFormRedeemDrama::SCENARIO_LUCKY_BAG;
                $redeem_type = DramaBoughtDetailLog::REDEEM_TYPE_LUCKY_BAG;
                break;
            default:
                throw new HttpException(400, '参数错误');
        }
        $form = new TransactionFormRedeemDrama(['scenario' => $scenario]);

        $transaction = TransactionLog::getDb()->beginTransaction();
        try {
            $form->user_context = UserContext::fromRpc(Yii::$app->request);
            $form->context_transaction_id = $context_transaction_id;
            $form->from_id = $user_id;
            $form->operator_id = $operator_id;
            // TODO: 存在一次兑换多个剧集情形，后续优化为一次调用
            // NOTICE: 兑换多个剧集时，只能一次性全部兑换成功
            $transaction_ids = [];
            foreach ($drama_ids as $drama_id) {
                $form->gift_id = $drama_id;
                if (!$transaction_id = $form->redeem()) {
                    throw new HttpException(400, MUtils::getFirstError($form));
                }
                $transaction_ids[] = $transaction_id;
            }
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            throw $e;
        }

        // 兑奖成功写入 databus
        $origin = DramaBoughtDetailLog::ORIGIN_WEB;
        try {
            $equip = new Equipment();
            $equip->parseUserAgent($form->user_context->user_agent);
            if ($equip->isFromApp()) {
                $origin = DramaBoughtDetailLog::ORIGIN_APP;
            }
        } catch (Exception $e) {
            // PASS: 不直接抛出异常，将错误记录到日志中
            Yii::error($e->getMessage() . '，User-Agent：' . $form->user_context->user_agent, __METHOD__);
        }
        // NOTICE: 现有的兑换剧集业务，只允许兑换整剧付费剧集
        foreach ($drama_ids as $drama_id) {
            DramaBoughtDetailLog::addLog($form->from_id, $drama_id, $origin, Drama::PAY_TYPE_DRAMA,
                ['redeem_type' => $redeem_type]);
        }

        return [
            'transaction_ids' => $transaction_ids,
        ];
    }

    /**
     * @api {post} /rpc/drama/get-paid-drama-ids 获取用户已购的剧集 IDs
     * @apiDescription 获取用户已购的剧集 IDs
     * @apiVersion 0.1.0
     * @apiName get-paid-drama-ids
     * @apiGroup /rpc/drama/
     *
     * @apiParam {Number[]} drama_ids 剧集 IDs
     * @apiParam {Number} user_id 用户 ID
     *
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "code": 0,
     *       "info": {
     *         "drama_ids": [9888]
     *       }
     *     }
     */
    public function actionGetPaidDramaIds()
    {
        $user_id = (int)Yii::$app->request->post('user_id');
        $drama_ids = Yii::$app->request->post('drama_ids');

        if ($user_id <= 0 || !MUtils2::isUintArr($drama_ids)) {
            throw new HttpException(400, '参数错误');
        }
        $paid_drama_ids = TransactionLog::getPaidDramaIds($user_id, $drama_ids);
        return ['drama_ids' => $paid_drama_ids];
    }
}
