<?php

namespace app\modules\rpc\controllers;

use app\components\auth\AuthWechat;
use app\models\UserAddendum;
use yii\web\Controller;
use Yii;
use yii\web\HttpException;

class UserController extends Controller
{

    /**
     * @api {post} /rpc/user/get-wechat-pub-acc-authorize-request 获取微信授权 URL（静默授权）
     * @apiName get-wechat-pub-acc-authorize-request
     * @apiGroup User
     *
     * @apiParam {Number} user_id 用户 ID
     *
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "authorize_url": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=...."
     *       }
     *     }
     */
    public function actionGetWechatPubAccAuthorizeRequest()
    {
        $user_id = (int)Yii::$app->request->post('user_id');
        return [
            'authorize_url' => AuthWechat::getSilentAuthorizeUrl($user_id),
        ];
    }

    /**
     * @api {post} /rpc/user/wechat-pub-acc-authorize 微信公众号授权
     * @apiName wechat-pub-acc-authorize
     * @apiGroup User
     *
     * @apiParam {Number} user_id 用户 ID
     * @apiParam {String} state 自定义携带的参数
     * @apiParam {String} code 授权码
     *
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "backurl": "https://m.missevan.com/wallet",
     *         "openid": "2a3492r4e4kdfadkf"
     *       }
     *     }
     */
    public function actionWechatPubAccAuthorize()
    {
        $user_id = (int)Yii::$app->request->post('user_id');
        $code = trim(Yii::$app->request->post('code'));
        $state = trim(Yii::$app->request->post('state'));

        if (!AuthWechat::validateAuthorizeUrlState($user_id, $state)) {
            throw new HttpException(403, '请重新授权');
        }

        $user = UserAddendum::getByPk($user_id);
        $result = AuthWechat::authorizeCodeToAccessToken($code, $state);
        $user->saveWechatPubAccOpenId($result['openid']);

        return [
            'backurl' => Yii::$app->params['domainMobileWeb'] . '/wallet',
            'openid' => $result['openid'],
        ];
    }

}
