<?php

namespace app\modules\rpc\controllers;

use app\components\auth\appleservernotification\ServerNotification;
use app\components\auth\AuthPayPal;
use app\components\service\bililargepay\BiliLargePayClient;
use app\components\util\MUtils;
use app\forms\RechargeForm;
use app\forms\UserContext;
use app\forms\VipIosSubscriptionForm;
use app\models\GuildLiveContractApplyment;
use app\models\GuildLiveOrder;
use app\models\Mowangskuser;
use app\models\PayAccount;
use app\models\PayAccountPurchaseDetail;
use app\models\UserAddendum;
use Yii;
use yii\db\Exception;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\web\Controller;
use yii\web\HttpException;
use app\components\util\Equipment;
use app\forms\TransactionForm;
use app\models\TopupMenu;
use app\models\Balance;
use app\models\AccountInfo;
use app\models\RechargeOrder;
use app\models\TransactionLog;
use app\models\WithdrawalRecord;
use missevan\util\MUtils as MUtils2;

class FinancialController extends Controller
{
    /**
     * @api {post} /rpc/financial/topup-menu
     * @apiName 钻石价目及消息提示
     * @apiGroup Financial
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code Error code
     * @apiSuccess {Object} info Result of request
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "tip": {
     *           "msg": "近期出现了大量第三方代充『猫耳FM』钻石的行为。目前...",
     *           "url": "",
     *           "bg_color" : "#fff0d9",
     *           "font_color": "#fcb651"
     *         },
     *         "menu": [
     *           {
     *             "id": 1,
     *             "num": 60,
     *             "price": "6.00",
     *             "checked": 1  // 默认选中
     *           },
     *           {
     *             "id": 2,
     *             "num": 180,
     *             "price": "18.00"
     *           },
     *           {
     *             "id": 3,
     *             "num": 500,
     *             "price": "50.00"
     *           },
     *           {
     *             "id": 4,
     *             "num": 1280,
     *             "price": "128.00"
     *           },
     *           {
     *             "id": 5,
     *             "num": 2580,
     *             "price": "258.00"
     *           },
     *           {
     *             "id": 6,
     *             "num": 5180,
     *             "price": "518.00"
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionTopupMenu()
    {
        $device = (int)Yii::$app->request->post('equip', Equipment::Web);
        $redis = Yii::$app->redis;
        $tip_key = $redis->generateKey(KEY_TOPUP_TIP, $device);
        $tip = json_decode($redis->get($tip_key), true) ?: null;
        return [
            'tip' => $tip,
            'menu' => TopupMenu::getCoinPrice($device, TopupMenu::getScope()),
        ];
    }

    /**
     * @api {post} /rpc/financial/create-alipay-page-order 创建网页支付宝订单
     * @apiName create-alipay-page-order
     * @apiGroup Financial
     *
     * @apiParam {Number} [origin=0] 请求来源类型：0 桌面 Web，1 为手机 Web
     * @apiParam {Number} user_id 用户 ID
     * @apiParam {Number} cid 钻石价目表 ccy 的主键
     * @apiParam {Number} diamond 自定义充值时钻石数目（当 cid 对应 model 价格为 0 时，使用自定义充值时钻石数目）
     * @apiParam {String} user_agent 用户代理
     * @apiParam {String} ip IP 地址
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "order": {
     *           "uid": 346286,
     *           "ctime": 1533616031,
     *           "cid": 18,
     *           "price": 0.1,
     *           "ccy": 1,
     *           "num": 5000,
     *           "status": 0,
     *           "type": 2,
     *           "id": 12603
     *         },
     *         "form": "<form id='alipaysubmit' name='alipaysubmit'>...</form>"
     *       }
     *     }
     */
    public function actionCreateAlipayPageOrder()
    {
        $origin = (int)Yii::$app->request->post('origin');
        $user_id = (int)Yii::$app->request->post('user_id');
        $cid = (int)Yii::$app->request->post('cid');
        $order = RechargeOrder::generateAlipayOrder($cid, $user_id, $origin,
            UserContext::fromRpc(Yii::$app->request));
        return $order;
    }

    /**
     * @api {post} /rpc/financial/create-wechat-page-order 创建网页微信支付订单
     * @apiName create-wechat-page-order
     * @apiGroup Financial
     *
     * @apiParam {Number} [origin=0] 请求来源类型：0 桌面 Web，1 为手机 Web
     * @apiParam {Number} user_id 用户 ID
     * @apiParam {Number} cid 钻石价目表 CCY 的 ID
     * @apiParam {Number} diamond 自定义充值时钻石数目（当 cid 对应 model 价格为 0 时，使用自定义充值时钻石数目）
     * @apiParam {String} user_agent 用户代理
     * @apiParam {String} ip IP 地址
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "order": {
     *           "uid": 346286,
     *           "ctime": 1533616031,
     *           "cid": 18,
     *           "price": 0.1,
     *           "ccy": 1,
     *           "num": 5000,
     *           "status": 0,
     *           "type": 2,
     *           "id": 12603
     *         },
     *         "code_url": "",
     *         "mweb_url": "https:\/\/wx.tenpay.com\/cgi-bin\/mmpayweb-bin\/checkmweb",
     *         "order_id": "dev15336156830020000012602"
     *       }
     *     }
     */
    public function actionCreateWechatPageOrder()
    {
        $origin = (int)Yii::$app->request->post('origin');
        $user_id = (int)Yii::$app->request->post('user_id');
        $cid = (int)Yii::$app->request->post('cid');
        $order = RechargeOrder::generateWechatpayOrder($cid, $user_id, $origin,
            UserContext::fromRpc(Yii::$app->request));
        return $order;
    }

    /**
     * @api {post} /rpc/financial/create-wechat-jsapi-order 创建微信公众号 JSAPI 支付订单
     * @apiName create-wechat-jsapi-order
     * @apiGroup financial
     *
     * @apiParam {Number} user_id 用户 ID
     * @apiParam {Number} cid 钻石价目表 CCY 的 ID
     * @apiParam {Number} diamond 自定义充值时钻石数目（当 cid 对应 model 价格为 0 时，使用自定义充值时钻石数目）
     * @apiParam {String} user_agent 用户代理
     * @apiParam {String} ip IP 地址
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "order": {
     *           "uid": 2985440,
     *           "cid": 24,
     *           "price": "0.1",
     *           "ccy": 1,
     *           "num": 1,
     *           "status": 0,
     *           "type": 2,
     *           "origin": 6,
     *           "ctime": 1686731447,
     *           "create_time": 1686731447,
     *           "modified_time": 1686731447,
     *           "id": 384456
     *         },
     *         "invoke_body": {
     *           "appId": "wx888888888888888",
     *           "timeStamp": "1686731447",
     *           "nonceStr": "Vtuz023OkTG5v75J7jFg0sSA2JMUmMvC",
     *           "package": "prepay_id=wx14163047422456c5ff6d43fa3743660000",
     *           "paySign": "TJp5JuJ/LB60nbQDqgiSGZYWie43CIHjgSb0Vw9kNwxsr5FMdZIL603G56A76MRmjb3ezhQ==",
     *           "signType": "RSA"
     *         },
     *         "order_id": "dev16867314470020000384456"
     *       }
     *     }
     */
    public function actionCreateWechatJsapiOrder()
    {
        $user_id = (int)Yii::$app->request->post('user_id');
        $cid = (int)Yii::$app->request->post('cid');
        $user = UserAddendum::getByPk($user_id);
        if (!$openid = $user->getWechatPubAccOpenId()) {
            Yii::error(sprintf('用户 %d 未找到对应 openid', $user->id), __METHOD__);
            throw new HttpException(403, '请重新进行微信公众号授权');
        }

        $order = RechargeOrder::generateWechatpayOrder($cid, $user_id, RechargeOrder::ORIGIN_WECHAT_PUBLIC_ACCOUNT,
            UserContext::fromRpc(Yii::$app->request), $openid);
        return $order;
    }

    /**
     * @api {post} /rpc/financial/create-qqpay-page-order 创建 QQ 网页支付订单
     * @apiName create-qqpay-page-order
     * @apiGroup financial
     *
     * @apiParam {Number} cid 充值类型 ID
     * @apiParam {Number} [origin=0] 请求来源类型（0 桌面 Web，1 为手机 Web）
     * @apiParam {Number} diamond 自定义充值时钻石数目（当 cid 对应 model 价格为 0 时，使用自定义充值时钻石数目）
     * @apiParam {String} user_agent 用户代理
     * @apiParam {String} ip IP 地址
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response: 手机网页版响应
     *     {
     *       "code": 0,
     *       "info": {
     *         "order": {
     *           "uid": 346286,
     *           "ctime": **********,
     *           "cid": 8,
     *           "price": "18.00",
     *           "ccy": 1,
     *           "num": 180,
     *           "status": 0,
     *           "type": 4,
     *           "id": 54128
     *         },
     *         "orderString": {
     *           "appid": "1103599281",
     *           "bargainorId": "1529373471",
     *           "pubAcc": ""
     *         },
     *         "redirect_url": "https://m.missevan.com/wallet/topupresult?out_trade_no=32155645"
     *       }
     *     }
     *
     * @apiSuccessExample Success-Response: 电脑网页版响应
     *     {
     *       "code": 0,
     *       "info": {
     *         "order": {
     *           "uid": 346286,
     *           "ctime": **********,
     *           "cid": 8,
     *           "price": "18.00",
     *           "ccy": 1,
     *           "num": 180,
     *           "status": 0,
     *           "type": 4,
     *           "id": 54128
     *         },
     *         "code_url": "https://qpay.qq.com/qr/6040c008",
     *         "redirect_url": "https://www.missevan.com/mperson/wallet/topupresult?out_trade_no=32155645"
     *       }
     *     }
     */
    public function actionCreateQqpayPageOrder()
    {
        $cid = (int)Yii::$app->request->post('cid');
        $origin = (int)Yii::$app->request->post('origin');
        $user_id = (int)Yii::$app->request->post('user_id');
        $order = RechargeOrder::generateQQPayOrder($cid, $user_id, $origin,
            UserContext::fromRpc(Yii::$app->request));

        return $order;
    }

    /**
     * @api {post} /rpc/financial/create-paypal-page-order 创建 Paypal 网页支付订单
     * @apiName create-paypal-page-order
     * @apiGroup financial
     *
     * @apiParam {Number} cid 充值类型 ID
     * @apiParam {Number} [origin=0] 请求来源类型（0 桌面 Web，1 为手机 Web）
     * @apiParam {Number} diamond 自定义充值时钻石数目（当 cid 对应 model 价格为 0 时，使用自定义充值时钻石数目）
     * @apiParam {String} user_agent 用户代理
     * @apiParam {String} ip IP 地址
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response: 电脑网页版响应
     *     {
     *       "code": 0,
     *       "info": {
     *         "order": {
     *           "uid": 346286,
     *           "ctime": **********,
     *           "cid": 8,
     *           "price": "18.00",
     *           "ccy": 1,
     *           "num": 180,
     *           "status": 0,
     *           "type": 4,
     *           "id": 54128
     *         },
     *         "form": "<form id="payform" name="payform">...</form>"
     *       }
     *     }
     */
    public function actionCreatePaypalPageOrder()
    {
        // 默认为自定义充值的选项
        $cid = (int)Yii::$app->request->post('cid', TopupMenu::getCustomDiamondNumId());
        $origin = (int)Yii::$app->request->post('origin');
        $user_id = (int)Yii::$app->request->post('user_id');
        // 检查是否是白名单用户
        if (!AuthPayPal::inWhiteList($user_id)) {
            throw new HttpException(403, '暂时无法使用 PayPal 充值哦');
        }
        // WORKAROUND: 之后可能会有充值最高额的限制，所以暂时注释掉
        /* if (AuthPayPal::checkTopupLimit($user_id) !== AuthPayPal::TOPUP_CHECK_STATUS_PASS) {
            throw new HttpException(403, '充值已达最高限额，如有疑问请联系客服');
        } */
        $order = RechargeOrder::generatePayPalOrder($cid, $user_id, $origin,
            UserContext::fromRpc(Yii::$app->request));
        return $order;
    }

    /**
     * @api {post} /rpc/financial/batch-system-topup
     * @apiName 批量给用户充值钻石
     * @apiDescription 充值的钻石默认为使用 iOS 渠道进行充值，实现利益最大化
     * @apiGroup Financial
     *
     * @apiVersion 0.1.0
     *
     * @apiParam {Number} [cid=0] 充值（价格）类型 ID
     * @apiParam {Number[]} user_ids 用户 IDs
     * @apiParam {Number} diamond 自定义充值时钻石数目
     * @apiParam {number=3,9,10} [type=3] 充值平台（3 现金充值、9 iOS 补单、10 公对公）
     * @apiParam {String} [coin_type="ios"] 充值货币类型
     * @apiParam {String} user_agent 用户代理
     * @apiParam {String} ip IP 地址
     *
     * @apiParam {Object} [more] 额外信息
     * @apiParam (more) {Number} event_id 活动 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "ids": [3],
     *         "num": 1000
     *       }
     *     }
     */
    public function actionBatchSystemTopup()
    {
        // (只有超管才能给用户充值钻石)
        $cid = (int)Yii::$app->request->post('cid', 0);
        $user_ids = Yii::$app->request->post('user_ids');
        $diamond = (int)Yii::$app->request->post('diamond');
        $type = (int)Yii::$app->request->post('type', RechargeOrder::TYPE_CASH);
        $coin_type = trim(Yii::$app->request->post('coin_type', PayAccount::COIN_FIELD_IOS));
        $more = Yii::$app->request->post('more', null);

        if (empty($user_ids) || !MUtils2::isUintArr($user_ids)) {
            throw new HttpException(400, '参数错误');
        }

        if ($more && !ArrayHelper::isAssociative($more)) {
            throw new HttpException(400, '额外信息格式错误');
        }

        $data = RechargeOrder::batchGenerateCashOrder($cid, $user_ids, $diamond,
            UserContext::fromRpc(Yii::$app->request), $type, $coin_type, $more ?: []);
        return $data;
    }

    /**
     * @api {post} /rpc/financial/recharge-detail
     * @apiName 获取充值详情
     * @apiGroup Financial
     *
     * @apiParam {Number} order_id 交易记录 ID
     * @apiParam {Number} user_id 用户 ID
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "id": 48207,
     *         "uid": 9,
     *         "tid": "4200000018201712154689395054",
     *         "ctime": 1513332895,
     *         "cid": 14,
     *         "price": "30.00",
     *         "num": 300,
     *         "ccy": 1,
     *         "status": 1,
     *         "type": 2
     *       }
     *     }
     */
    public function actionRechargeDetail()
    {
        $user_id = (int)Yii::$app->request->post('user_id');
        $order_id = (int)Yii::$app->request->post('order_id');
        $order = RechargeOrder::findOne(['uid' => $user_id, 'id' => $order_id]);
        if (!$order) {
            throw new HttpException(404, '订单不存在');
        }
        return $order;
    }

    /**
     * @api {post} /rpc/financial/balance
     * @apiName 获取充值余额或主播收益
     * @apiGroup Financial
     *
     * @apiParam {Number} user_id 用户 ID
     * @apiParam {Number} [type=1] 1 为充值余额，2 为直播收益
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "balance": 60,
     *         "live_noble_balance": 60,
     *         "live_noble_balance_status": 1
     *       }
     *     }
     */
    public function actionBalance()
    {
        $user_id = (int)Yii::$app->request->post('user_id');
        $type = (int)Yii::$app->request->post('type', Balance::BALANCE_TYPE_TOPUP);
        $user = Balance::getByPk($user_id);
        return $user->getBalanceDetail($type, true);
    }

    /**
     * @api {post} /rpc/financial/withdrawal
     * @apiName 申请提现
     * @apiGroup Financial
     *
     * @apiParam {Number} user_id 用户 ID
     * @apiParam {Number} account_id 账户 ID
     * @apiParam {Number} price 提现数额（元）
     * @apiParam {number=2,5} [withdraw_type=2] 提现的收益类型（2 为旧直播收益提现，5 为新直播收益提现）
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "id": 54,
     *         "msg": "提现申请已提交"
     *       }
     *     }
     */
    public function actionWithdrawal()
    {
        // TODO: 由 Cookie 中的 token 获取 user_id
        $user_id = (int)Yii::$app->request->post('user_id');
        $account_id = (int)Yii::$app->request->post('account_id');
        $price = (double)Yii::$app->request->post('price');
        $withdraw_type = (int)Yii::$app->request->post('withdraw_type',
            WithdrawalRecord::TYPE_WITHDRAW_LIVE);

        if ($price <= 0) {
            throw new HttpException(400, '参数有误');
        }
        if (!in_array($withdraw_type,
                [WithdrawalRecord::TYPE_WITHDRAW_LIVE, WithdrawalRecord::TYPE_WITHDRAW_LIVE_NEW])) {
            throw new HttpException(400, '参数有误');
        }

        $account = AccountInfo::findOne($account_id);
        if (!$account || $account->type === AccountInfo::STATUS_SAVED) {
            throw new HttpException(404, '该账户不存在');
        }
        if ($account->user_id !== $user_id) {
            throw new HttpException(403, '该账号不属于该用户');
        }
        $balance = Balance::getByPk($user_id);
        $profit = Balance::profitUnitConversion($price, Balance::CONVERT_YUAN_TO_FEN);
        // 判断用户需要提现的收益和用户当前可提现的收益
        $balance->checkWithdrawProfit($withdraw_type, $profit);

        $transaction = WithdrawalRecord::getDb()->beginTransaction();
        try {
            $record = new WithdrawalRecord([
                'user_id' => $user_id,
                'account_id' => $account_id,
                'profit' => $price,
                'create_time' => $_SERVER['REQUEST_TIME'],
                'status' => WithdrawalRecord::STATUS_CREATE,
                'type' => $withdraw_type
            ]);
            $record->save();
            if ($withdraw_type === WithdrawalRecord::TYPE_WITHDRAW_LIVE_NEW) {
                $balance->updateCounters(['new_live_profit' => -1 * $profit]);
            } else {
                $balance->updateCounters(['live_profit' => -1 * $profit]);
            }
            $transaction->commit();
            return [
                'id' => $record->id,
                'msg' => '提现申请已提交',
            ];
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * @api {post} /rpc/financial/withdrawal-sure
     * @apiName 处理提现申请
     * @apiGroup Financial
     *
     * @apiParam {Number} record_id 提现记录 ID
     * @apiParam {Number} sure 账户 ID
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "msg": "通过审核！",
     *         "updated": 1
     *       }
     *     }
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "msg": "已拒绝",
     *         "updated": 1
     *       }
     *     }
     */
    public function actionWithdrawalSure()
    {
        // TODO: 由 Cookie 中的 token 获取 user_id 判断用户身份
        $record_id = (int)Yii::$app->request->post('record_id');
        $sure = (bool)Yii::$app->request->post('sure');
        $transaction = WithdrawalRecord::getDb()->beginTransaction();
        try {
            // 加行锁，防止并发情况
            $record = WithdrawalRecord::findBySql('SELECT * FROM ' . WithdrawalRecord::tableName() .
                ' WHERE id = :id FOR UPDATE')->addParams([':id' => $record_id])->one();
            if (!$record) {
                throw new HttpException(404, '该订单不存在');
            }
            if (WithdrawalRecord::STATUS_CREATE < $record->status) {
                throw new HttpException(400, '已被审核过，无需再次审核');
            }
            if ($sure) {
                $record->status = WithdrawalRecord::STATUS_CONFIRM;
                if (!$record->save(false, ['status'])) {
                    throw new Exception(MUtils::getFirstError($record));
                }
                $return = [
                    'msg' => '通过审核！',
                    'updated' => 1,
                ];
            } else {
                $record->status = WithdrawalRecord::STATUS_INVALID;
                // 收益换算成分为单位
                $profit = Balance::profitUnitConversion($record->profit, Balance::CONVERT_YUAN_TO_FEN);
                if ($record->type === WithdrawalRecord::TYPE_WITHDRAW_LIVE_NEW) {
                    $updated = Balance::updateAllCounters(['new_live_profit' => $profit], 'id = :id',
                        [':id' => $record->user_id]);
                } else {
                    $updated = Balance::updateAllCounters(['live_profit' => $profit], 'id = :id',
                        [':id' => $record->user_id]);
                }
                if (!$record->save(false, ['status'])) {
                    throw new Exception(MUtils::getFirstError($record));
                }
                $return = [
                    'msg' => '已拒绝',
                    'updated' => $updated,
                ];
            }
            $transaction->commit();
            return $return;
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * @api {post} /rpc/financial/buy-things
     * @apiName 购买东西
     * @apiGroup financial
     *
     * @apiParam {Number} from_id 购买者 ID
     * @apiParam {Number} title 物品标题
     * @apiParam {Number} price 物品价格钻石数
     * @apiParam {Number} type 物品类型（4：手机恋人；10：喵喵周卡活动；11：魔力赏活动）
     * @apiParam {Number} gift_id 主题 ID 或角色 ID
     * @apiParam {Number} attr 手机恋人 IP（0 微信男友，1 掌心男友；当 type = 11 时，0：抽卡；1：运费）
     * @apiParam {Number} suborders_num 订单数量（当 type = 10 时，为 m_weekly_memberships.id；type = 11 时，为 game_magic_user_award.id）
     * @apiParam {String} user_agent 设备 User-Agent
     * @apiParam {String} equip_id 设备 equip_id
     * @apiParam {String} buvid
     * @apiParam {String} ip 终端 IP
     *
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "transaction_id": 56711318,
     *         "balance": 560,
     *         "price": 200
     *       }
     *     }
     */
    public function actionBuyThings()
    {
        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_THINGS]);
        $form->load(Yii::$app->request->post(), '');
        $form->user_context = UserContext::fromRpc(Yii::$app->request);
        if ($form->validate() && $msg = $form->buyThings()) {
            return $msg;
        }
        throw new HttpException(400, MUtils::getFirstError($form));
    }

    /**
     * 创建主播强制解约违约金支付订单
     *
     * @api {post} /rpc/financial/create-live-penalty-order
     * @apiName create-live-penalty-order
     * @apiGroup financial
     *
     * @apiParam {Number} applyment_id 合约申请 ID
     * @apiParam {Number} price 违约金额（单位：分）
     *
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "price": "17094.20",
     *         "order": {
     *           "price": 1709420,
     *           "live_id": 473927,
     *           "guild_id": 15,
     *           "applyment_id": 5,
     *           "status": 0,
     *           "type": 1,
     *           "create_time": 1575099188,
     *           "modified_time": 1575099188,
     *           "id": 5
     *         },
     *         "link": "https://openapi.alipaydev.com/gateway.do?alipay_sdk=alipay-sdk-php-20161101&app_id=2016080..."
     *       }
     *     }
     */
    public function actionCreateLivePenaltyOrder()
    {
        $applyment_id = (int)Yii::$app->request->post('applyment_id');
        $price = (int)Yii::$app->request->post('price', 0);
        if ($applyment_id <= 0 || $price <= 0) {
            throw new HttpException(400, '参数错误');
        }
        $applyment = GuildLiveContractApplyment::find()
            ->where(['id' => $applyment_id, 'status' => GuildLiveContractApplyment::STATUS_PENDING])
            ->one();
        if (!$applyment) {
            throw new HttpException(404, '合约申请不存在');
        }
        return GuildLiveOrder::createLivePenaltyOrder($applyment, $price);
    }

    /**
     * @api {post} /rpc/financial/purchase-history 购买历史
     * @apiName purchase-history
     * @apiGroup financial
     *
     * @apiParam {Number} user_id 用户 ID
     * @apiParam {Number} [page=1] 页数
     * @apiParam {Number} [page_size=30] 页数
     * @apiParam {number=0,1,2} [type=0] 类型（0 剧集，1 直播，2 其他）
     * @apiParam {Number} [from_time=0] 起始时间戳
     * @apiParam {Number} [to_time=0] 截止时间戳（不含此刻）
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "Datas": [{
     *           "transaction_id": "15767414120030000001703",
     *           "detail": "礼物--喵头虎脑 × 1",
     *           "status_msg": "购买成功",
     *           "coin": 336,
     *           "confirm_time": 1646901420
     *         }],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 1,
     *           "count": 28,
     *           "pagesize": 30
     *         },
     *         "tabs": [{
     *           "title": "剧集",
     *           "type": 0
     *         },
     *         {
     *           "title": "直播",
     *           "type": 1,
     *           "no_more_tip": "仅展示最近三个月记录"
     *         },
     *         {
     *           "title": "其他",
     *           "type": 2
     *         }]
     *       }
     *     }
     */
    public function actionPurchaseHistory()
    {
        $user_id = (int)Yii::$app->request->post('user_id');
        $page = (int)Yii::$app->request->post('page', 1);
        $page_size = (int)Yii::$app->request->post('page_size', DEFAULT_PAGE_SIZE);
        $type = (int)Yii::$app->request->post('type');
        $from_time = (int)Yii::$app->request->post('from_time');
        $to_time = (int)Yii::$app->request->post('to_time');

        if ($user_id <= 0) {
            throw new HttpException(400, '参数错误');
        }
        switch ($type) {
            case 0:
                $resp = TransactionLog::newDramaPurchaseHistory($user_id, $page, $page_size, $from_time, $to_time);
                break;
            case 1:
                $resp = TransactionLog::newLivePurchaseHistory($user_id, $page, $page_size, $from_time, $to_time);
                break;
            case 2:
                $resp = TransactionLog::newOtherPurchaseHistory($user_id, $page, $page_size, $from_time, $to_time);
                break;
            default:
                throw new HttpException(400, '参数错误');
        }

        $resp->tabs = [
            [
                'title' => '剧集',
                'type' => 0,
            ],
            [
                'title' => '直播',
                'type' => 1,
                'no_more_tip' => '仅展示最近三个月记录',
            ],
            [
                'title' => '其他',
                'type' => 2,
            ],
        ];
        unset($resp->has_more);

        return (array)$resp;
    }

    /**
     * @api {post} /rpc/financial/create-bili-large-pay-order 创建B站大额充值订单
     * @apiName create-bili-large-pay-order
     * @apiGroup financial
     *
     * @apiParam {Number} user_id 用户 ID
     * @apiParam {number=0,1} origin 来源
     * @apiParam {String} user_agent 用户代理
     * @apiParam {String} ip IP 地址
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "url": "https://m.uat.missevan.com/wallet/large-pay?payChannelParam=%7B%22customer..."
     *       }
     *     }
     */
    public function actionCreateBiliLargePayOrder()
    {
        $user_id = (int)Yii::$app->request->post('user_id');
        $origin = (int)Yii::$app->request->post('origin');
        switch ($origin) {
            case RechargeOrder::ORIGIN_DESKTOP_WEB:
                $device = BiliLargePayClient::DEVICE_WEB;
                $device_type = BiliLargePayClient::DEVICE_TYPE_PC;
                break;
            case RechargeOrder::ORIGIN_MOBILE_WEB:
                $device = BiliLargePayClient::DEVICE_H5;
                $device_type = BiliLargePayClient::DEVICE_TYPE_H5;
                break;
            default:
                throw new HttpException(400, '参数错误');
        }

        $topup_order = new RechargeOrder([
            'uid' => $user_id,
            'type' => RechargeOrder::TYPE_BILI_LARGE_PAY,
            'cid' => 0,
            'price' => 0,
            'num' => 0,
            'ccy' => TopupMenu::DIAMOND,
            'status' => RechargeOrder::STATUS_CREATE,
            'origin' => $origin,
            'detail' => UserContext::fromUser(Yii::$app->request)->toArray(),
        ]);
        if (!$topup_order->save()) {
            throw new \Exception(MUtils2::getFirstError($topup_order));
        }

        $username = Mowangskuser::getUsernameById($topup_order->uid);
        $pay = new BiliLargePayClient();
        $resp = $pay->createOrder($topup_order, $username, $device, $device_type);

        return [
            'url' => $pay->getRedirectUrl($resp['payChannelUrl'], $resp['payChannelParam']),
        ];
    }

    /**
     * @api {post} /rpc/financial/update-ios-topup-whitelist 更新 iOS 充值白名单
     * @apiName update-ios-topup-whitelist
     * @apiGroup financial
     *
     * @apiParam {Number} user_id 用户 ID
     * @apiParam {number=0,1} [type=0] 操作类型（0 添加，1 删除）
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "ok": 1
     *       }
     *     }
     */
    public function actionUpdateIosTopupWhitelist()
    {
        $user_id = (int)Yii::$app->request->post('user_id');
        $type = (int)Yii::$app->request->post('type');

        $redis = Yii::$app->redis;
        if ($type) {
            $ok = $redis->sRem(KEY_IOS_DEVICE_TOPUP_USER_ID_WHITELIST, $user_id);
        } else {
            $ok = $redis->sAdd(KEY_IOS_DEVICE_TOPUP_USER_ID_WHITELIST, $user_id);
        }

        return [
            'ok' => $ok,
        ];
    }

    /**
     * @api {post} /rpc/financial/ios-iap-manual-notify iOS 内购手动通知处理（用于丢单失败等）
     * @apiName ios-iap-manual-notify
     * @apiGroup financial
     *
     * @apiParam {String} raw 解析后的 v2 报文（见 /callback/ios-iap）
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "ok": 1
     *       }
     *     }
     */
    public function actionIosIapManualNotify()
    {
        $raw = Yii::$app->request->post('raw');
        $message = ServerNotification::loadFromArray(Json::decode($raw));
        if (!RechargeForm::isValidAppPackageName($message->data->bundleId)) {
            Yii::error(sprintf('苹果退款通知回调报文异常：bundleId[%s]', $message->data->bundleId), __METHOD__);
            throw new HttpException(403, '非法请求');
        }
        if (YII_ENV_PROD && !$message->data->isProductionEnv()) {
            Yii::error(sprintf('苹果退款通知回调报文异常：environment[%s]', $message->data->environment), __METHOD__);
            throw new HttpException(403, '非法请求');
        }
        if (!$message->data->signedTransactionInfo->isAutoRenewableSubscriptionType() && !$message->data->signedTransactionInfo->isNonRenewingSubscriptionType()) {
            throw new HttpException(403, '只处理订阅（自动续期、非自动续期）型商品');
        }
        try {
            $form = new VipIosSubscriptionForm($message);
            $form->processNotification();
            return ['ok' => 1];
        } catch (Exception $e) {
            Yii::error(sprintf('iOS 回调通知手动处理失败：%s', $e->getMessage()), __METHOD__);
            throw $e;
        }
    }

    /**
     * @api {post} /rpc/financial/consume-order-refund 消费退款（钻石退回钱包）
     * @apiName consume-order-refund
     * @apiGroup financial
     *
     * @apiParam {Number} user_id 用户 ID
     * @apiParam {Number} type 购买商品类型，对应 transaction_log.type
     * @apiParam {Number} order_id 订单 ID
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "message": "退款成功"
     *       }
     *     }
     */
    public function actionConsumeOrderRefund()
    {
        $user_id = (int)Yii::$app->request->post('user_id');
        $order_id = (int)Yii::$app->request->post('order_id');
        $type = (int)Yii::$app->request->post('type');

        if ($user_id <= 0 || $order_id <= 0 || $type !== TransactionLog::TYPE_MAGIC) {
            throw new HttpException(400, '参数错误');
        }
        $order = TransactionLog::find()
            ->where([
                'type' => $type,
                'from_id' => $user_id,
                'status' => TransactionLog::STATUS_SUCCESS,
                'id' => $order_id,
            ])
            ->one();
        if (!$order) {
            throw new HttpException(404, '订单不存在或状态异常');
        }
        try {
            $transaction = TransactionLog::getDb()->beginTransaction();
            PayAccountPurchaseDetail::proceed($order, PayAccountPurchaseDetail::STATUS_REFUND, PayAccountPurchaseDetail::STATUS_CONFIRM);
            $rows = TransactionLog::updateAll(['status' => TransactionLog::STATUS_REFUND_DIAMOND, 'modified_time' => $_SERVER['REQUEST_TIME']],
                ['id' => $order_id, 'status' => TransactionLog::STATUS_SUCCESS]);
            if ($rows === 0) {
                throw new HttpException(403, '订单状态异常，退款失败');
            }
            $transaction->commit();
        } catch (Exception $e) {
            Yii::error("用户 {$user_id} 订单 {$order_id} 消费退款失败：" . $e->getMessage(), __METHOD__);
            throw $e;
        }
        return ['message' => '退款成功'];
    }
}
