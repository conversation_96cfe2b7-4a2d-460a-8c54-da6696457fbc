<?php

namespace app\modules\rpc\controllers;

use app\models\Goods;
use Yii;
use yii\web\Controller;
use yii\web\HttpException;

class MallController extends Controller
{
    /**
     * @api {post} /rpc/mall/goods-detail 获取商品详情
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/rpc/mall/goods-detail
     * @apiSampleRequest /rpc/mall/goods-detail
     * @apiDescription 购买剧集
     * @apiVersion 0.1.0
     * @apiName goods-detail
     * @apiGroup /rpc/mall
     *
     * @apiParam {Number} id 商品 ID
     *
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "code": 0,
     *       "info": {
     *         "id": 1,
     *         "title": "金坷垃",
     *         "intro": "不要打架不要打架",
     *         "cover": "oss://maoershop/images/201907/08/test.jpg",
     *         ...
     *       }
     *     }
     */

    public function actionGoodsDetail()
    {
        $id = (int)Yii::$app->request->post('id');
        if ($id <= 0) {
            throw new HttpException(400, '参数错误');
        }
        $goods = Goods::findOne($id);
        if (!$goods) {
            throw new HttpException(400, '商品不存在');
        }
        // 返回数据库原始数据而非查询出来后处理过的数据
        return $goods->oldAttributes;
    }
}
