<?php
/**
 * RPC 来源类
 *
 * Created by PhpStorm.
 * User: summer
 * Date: 19/1/9
 * Time: 下午2:56
 */

namespace app\modules\rpc\middlewares;

class RpcSource
{
    /**
     * @var string 项目名
     */
    private $name;

    /**
     * @var string 版本号
     */
    private $version;

    public function __construct($user_agent)
    {
        $name = $version = null;
        if (is_array($user_agent)) {
            [$name, $version] = array_values($user_agent);
        } elseif (is_string($user_agent)) {
            [$name, $version] = explode('/', $user_agent);
        }
        $this->name = $name;
        $this->version = $version;
    }

    /**
     * 根据 UA 进行版本比较
     *
     * @param string|array $name 项目名或者是一个数组
     * 类似 [[name => 'missevan-web', version => '1.0.6'], [name => 'missevan-app', version => '1.0.7']]
     * @param string $version 版本号
     * @return bool 比较的结果
     */
    public function isOlderThan($name, $version = null)
    {
        if (is_array($name)) {
            foreach ($name as $value) {
                if ($value['name'] === $this->name &&
                        version_compare($this->version, $value['version'], '<')) {
                    return true;
                }
            }
        } elseif (is_string($name)) {
            if ($name === $this->name &&
                    version_compare($this->version, $version, '<')) {
                return true;
            }
        }
        return false;
    }
}
