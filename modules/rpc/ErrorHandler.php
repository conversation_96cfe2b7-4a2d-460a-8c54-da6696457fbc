<?php

namespace app\modules\rpc;

use Yii;
use yii\web\HttpException;
use yii\web\Response;

class ErrorCode {
    // 参数错误
    const ERROR_PARAM = 101010000;
    // 签名校验失败
    const ERROR_SIGNATURE = 101010001;
    // 无权执行该操作
    const ERROR_FORBIDDEN = 101010003;
    // 无法找到对应资源
    const ERROR_NOTFOUND = 101010004;
    // 服务器内部错误
    const ERROR_INTERNAL = 101010004;
}

class ErrorHandler extends yii\web\ErrorHandler {

    const ERROR_CODE = [
        400 => ErrorCode::ERROR_PARAM,
        401 => ErrorCode::ERROR_SIGNATURE,
        403 => ErrorCode::ERROR_FORBIDDEN,
        404 => ErrorCode::ERROR_NOTFOUND,
        500 => ErrorCode::ERROR_INTERNAL,
    ];

    /**
     * Renders the exception.
     * @param \Exception|\Error $exception the exception to be rendered.
     */
    public function renderException($exception)
    {
        if (Yii::$app->has('response')) {
            $response = Yii::$app->getResponse();
            // reset parameters of response to avoid interference with partially created response data
            // in case the error occurred while sending the response.
            $response->isSent = false;
            $response->stream = null;
            $response->data = null;
            $response->content = null;
        } else {
            $response = new Response();
        }
        $response->setStatusCodeByException($exception);
        $response->format = Response::FORMAT_JSON;

        if ($exception instanceof HttpException) {
            $code = self::ERROR_CODE[400];

            // using ReflectionClass to get protected property
            $reflection = new \ReflectionClass($exception);
            $property = $reflection->getProperty('code');
            $property->setAccessible(true);
            $expcode = $property->getValue($exception);

            if ($expcode) {
                $code = $expcode;
            } elseif (!$expcode && isset(self::ERROR_CODE[$exception->statusCode])) {
                $code = self::ERROR_CODE[$exception->statusCode];
            }
            $response->data = [
                'code' => $code,
                'info' => $exception->getMessage(),
            ];
        } else {
            $response->data = [
                'code' => self::ERROR_CODE[500],
                'info' => $exception->getMessage(),
            ];
        }
        $response->send();
    }

}
