<?php

namespace app\modules\rpc;

use Yii;
use ReflectionClass;
use app\components\util\Equipment;
use app\modules\rpc\middlewares\RpcSource;
use app\models\User;
use yii\helpers\Json;
use yii\web\HttpException;
use yii\web\Request;

class Rpc extends \yii\base\Module
{
    const TIMEOUT = 120;
    const BODY_LIST_COUNT = 3;

    private $_source = null;

    // 路由映射表
    const FORWARD_LIST = [
        'reward' => ['drama', 'app\controllers'],
        'voice' => ['voice', 'app\controllers'],
        'you-might-like' => ['you-might-like', 'app\controllers'],
        'theatre' => ['theatre', 'app\controllers'],
        'radio' => ['radio', 'app\controllers'],
    ];

    public $controllerNamespace = 'app\modules\rpc\controllers';

    // 路由是否需要映射的标识，默认不需要映射
    private $is_forward = false;

    public function init()
    {
        parent::init();

        // custom initialization code goes here
        Yii::$app->set('errorHandler', [
            'class' => 'app\modules\rpc\ErrorHandler',
        ]);
        Yii::$app->errorHandler->register();
        Yii::$app->request->enableCsrfValidation = false;
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
    }

    private function setData()
    {
        if (Yii::$app->request->isPost) {
            $this->parseBody(Yii::$app->request);
        } else {
            throw new HttpException(400, '请求不合法');
        }
    }

    private function parseBody(Request $request)
    {
        $datas = explode(' ', $request->getRawBody());
        if (count($datas) !== self::BODY_LIST_COUNT) {
            throw new HttpException(400, '请求不合法');
        }
        list($msg, $sign, $timestamp) = $datas;
        if (!YII_DEBUG && abs(time() - $timestamp) > self::TIMEOUT) {
            throw new HttpException(408, '请求超时', ErrorCode::ERROR_SIGNATURE);
        }
        $rpc_key = MISSEVAN_PRIVATE_KEY;
        if ($x_appkey = $request->headers['x-appkey']) {
            $rpc_key = Yii::$app->params['bili_app_key'][$x_appkey] ?? null;
            if (!$rpc_key) {
                throw new HttpException(403, '未知的 X-AppKey');
            }
        }

        $text = "$msg $timestamp";
        $check_sign = hash_hmac('sha256', $text, $rpc_key);
        if ($check_sign === $sign) {
            try {
                $_POST = Json::decode(base64_decode($msg));
            } catch (\Exception $e) {
                throw new HttpException(400, '请求不合法');
            }
        } else {
            throw new HttpException(403, '您没有访问权限');
        }
    }

    public function afterAction($action, $result)
    {
        if ($this->is_forward) {
            return $result;
        }
        $result = parent::afterAction($action, $result);
        // finish request
        return [
            'code' => 0,
            'info' => $result,
        ];
    }

    /**
     * 继承自父类（base\Module）
     * 改动：
     *  - 增加「路由映射」
     * 如果是转发请求，则将路由地址映射到新的路由地址
     *
     * @param string $route
     * @return array|bool
     * @throws InvalidConfigException
     */
    public function createController($route)
    {
        $this->setData();
        $route_map = $route;
        [$id, $action_id] = explode('/', $route_map, 2);

        // 判断是否需要路由映射
        if (array_key_exists($id, self::FORWARD_LIST)) {
            // 获取路由映射地址
            $route_map = $this->getForwardRoute($id, $action_id);
            // 初始化信息
            $this->initForwardInfo();
        }
        $parent = parent::createController($route_map);
        if ($parent && $this->is_forward) {
            // 反注册的目的是为了过滤掉抛出异常的数据又做了一层数据结构嵌套的情况
            Yii::$app->errorHandler->unregister();
        }
        return $parent;
    }

    /**
     * 获取路由映射地址
     *
     * @param string $id 控制器 ID
     * @param string $action_id 方法 ID
     * @return string 路由映射地址
     */
    public function getForwardRoute($id, $action_id)
    {
        [$controller_id, $controller_name_space] = self::FORWARD_LIST[$id];
        // 路由映射标识
        $this->is_forward = true;
        // 映射到的命名空间
        $this->controllerNamespace = $controller_name_space;
        // 路由映射地址
        $route = "{$controller_id}/{$action_id}";
        return $route;
    }

    public function initForwardInfo()
    {
        // set equipment
        $reflector = new ReflectionClass(get_class(Yii::$app->equip));
        $prop = $reflector->getProperty('os');
        $prop->setAccessible(true);
        $prop->setValue(Yii::$app->equip, Equipment::Web);
        $prop = $reflector->getProperty('from_app');
        $prop->setAccessible(true);
        $prop->setValue(Yii::$app->equip, true);

        $method_param = Yii::$app->request->methodParam;
        if (!array_key_exists($method_param, $_POST)) {
            throw new HttpException(400, '非法请求');
        }

        $user_id = $_POST['user_id'] ?? null;
        unset($_POST['user_id']);

        // overwrite request method
        $method = strtoupper($_POST[$method_param]);
        unset($_POST[$method_param]);

        switch ($method) {
            case 'POST':
                break;
            case 'GET':
                $_GET = $_POST;
                $_POST = [$method_param => $method];
                break;
            default:
                throw new HttpException(400, '非法请求');
                break;
        }
        // set user identity
        $user = new User();
        $user->user_id = $user_id;
        Yii::$app->user->setIdentity($user);
    }

    /**
     * 继承自父类（base\Module）
     * 改动：
     *  - 改变根路径
     * 如果是转发请求，则将路径改成项目的根路径
     *
     * @return 返回根路径
     */
    public function getBasePath()
    {
        // TODO: 根据要转发的路径做操作
        if ($this->is_forward) {
            return __DIR__ . '/../../';
        }
        return parent::getBasePath();
    }

    /**
     * 继承自父类（yii\di\ServiceLocator）
     * 改动：
     *  - 实例化类，懒加载来源对象
     * 如果访问的私有属性为 source，则实例化 RpcSource 类并且懒加载，否则直接调用父类的魔术方法 __get()
     *
     * @param string $name 属性
     * @return object|mixed 来源对象或者是属性的值
     */
    public function __get($name)
    {
        if ($name === 'source') {
            if ($this->_source === null) {
                $this->_source = new RpcSource(Yii::$app->request->userAgent);
            }
            return $this->_source;
        }
        return parent::__get($name);
    }
}
