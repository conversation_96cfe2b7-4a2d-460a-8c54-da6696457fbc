<?php

namespace app\models;

use Yii;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "m_appearance".
 *
 * @property integer $id 主键
 * @property integer $create_time 创建时间（秒级时间戳）
 * @property integer $modified_time 最后修改时间（秒级时间戳）
 * @property string $name 名称
 * @property string $intro 简介
 * @property integer $vip 是否会员免费。0：否；1：是
 * @property integer $pay 付费类型。0：非付费使用；1：付费使用
 * @property integer $price 价格（单位：分）
 * @property integer $archive 归档状态。0：未归档；1：已归档
 * @property array $appearance 套装信息
 * @property array $more 更多信息
 */
class MAppearance extends ActiveRecord
{
    use ActiveRecordTrait;

    const VIP_FREE_NO = 0;  // 非会员免费
    const VIP_FREE_YES = 1;  // 会员免费

    const PAY_FREE = 0;  // 非付费使用
    const PAY_PAID = 1;  // 付费使用

    const ARCHIVE_NO = 0;  // 未归档
    const ARCHIVE_YES = 1;  // 已归档

    /**
     * @return Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->db;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_appearance';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name', 'vip', 'pay'], 'required'],
            [['create_time', 'modified_time', 'vip', 'pay', 'archive'], 'integer'],
            [['vip'], 'in', 'range' => [
                self::VIP_FREE_NO,
                self::VIP_FREE_YES,
            ]],
            [['pay'], 'in', 'range' => [
                self::PAY_FREE,
                self::PAY_PAID,
            ]],
            [['archive'], 'in', 'range' => [
                self::ARCHIVE_NO,
                self::ARCHIVE_YES,
            ]],
            [['appearance', 'more'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'name' => '名称',
            'intro' => '简介',
            'vip' => '是否会员免费',  // 0：否；1：是
            'pay' => '付费类型',  // 0：非付费使用；1：付费使用
            'price' => '价格',  // 单位：分
            'archive' => '归档状态',  // 0：未归档；1：已归档
            'appearance' => '套装信息',
            'more' => '更多信息',
        ];
    }

}
