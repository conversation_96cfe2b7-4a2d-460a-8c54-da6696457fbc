<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "dub".
 *
 * @property integer $id
 * @property string $title
 * @property string $video_url
 * @property string $cover_url
 * @property string $from
 * @property integer $duration
 * @property integer $eid
 * @property string $more
 */
class Dub extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'dub';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['title', 'video_url', 'cover_url', 'from', 'duration', 'eid'], 'required'],
            [['duration', 'eid'], 'integer'],
            [['title', 'video_url', 'cover_url'], 'string', 'max' => 255],
            [['from'], 'string', 'max' => 100],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'title' => '素材名',
            'video_url' => '视频地址',
            'cover_url' => '封面图地址',
            'from' => '来自',
            'duration' => 'Duration',
            'eid' => '活动id',
            'more' => '更多信息',
        ];
    }
}
