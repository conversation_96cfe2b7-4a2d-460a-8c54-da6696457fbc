<?php

namespace app\models;

use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;

/**
 * This is the model class for table "user_sign_history".
 *
 * @property integer $id 记录 ID
 * @property integer $create_time 创建时间，单位：秒
 * @property integer $modified_time 修改时间，单位：秒
 * @property integer $sign_time 签到日期，所签到（补签）的日期零点时间戳，单位：秒。注：不是指进行签到（补签）操作的当前日期
 * @property integer $type 签到类型，1：签到；2：补签
 * @property integer $user_id 用户 ID
 */
class UserSignHistory extends ActiveRecord
{
    // 签到类型
    // 签到
    const TYPE_SIGN = 1;
    // 补签
    const TYPE_PATCH_SIGN = 2;

    /**
     * @inheritdoc
     */
    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'user_sign_history';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['sign_time', 'type', 'user_id'], 'required'],
            [['create_time', 'modified_time', 'sign_time', 'type', 'user_id'], 'integer'],
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 写入签到记录
     *
     * @param int $user_id 用户 ID
     * @param int $sign_time 签到日期，所签到（补签）的日期零点时间戳，单位：秒。注：不是指进行签到（补签）操作的当前日期
     * @param int $type 签到类型，1：签到；2：补签
     * @return bool 是否写入成功
     * @throws Exception
     */
    public static function addSignHistory(int $user_id, int $sign_time, int $type = self::TYPE_SIGN): bool
    {
        $sign = new UserSignHistory();
        $sign->sign_time = $sign_time;
        $sign->type = $type;
        $sign->user_id = $user_id;
        if (!$sign->save()) {
            throw new Exception(MUtils2::getFirstError($sign));
        }
        return true;
    }

    /**
     * 获取用户近一个月（包含今天一共 31 天）签到记录
     *
     * @param int $user_id 用户 ID
     * @return array 用户近一个月签到记录
     */
    public static function getUserSignHistoryList(int $user_id): array
    {
        return self::find()
            ->select('sign_time')
            ->where(['user_id' => $user_id])
            ->andWhere(['>=', 'sign_time', strtotime('today', $_SERVER['REQUEST_TIME']) - THIRTY_DAYS])
            ->column();
    }

    /**
     * 检查用户某天是否已经签到
     *
     * @param int $user_id 用户 ID
     * @param int $sign_time 所检查日期当天零点时间戳，单位：秒
     * @return bool 是否签到
     */
    public static function isSigned(int $user_id, int $sign_time)
    {
        return UserSignHistory::find()
            ->where(['user_id' => $user_id, 'sign_time' => $sign_time])
            ->exists();
    }

    /**
     * 获取指定日期之间用户是否达到指定连续签到天数
     *
     * @param int $user_id 用户 ID
     * @param int $start_time 开始时间戳，单位：秒
     * @param int $end_time 结束时间戳，单位：秒
     * @param int $target_day 目标天数
     * @return bool
     * @throws Exception
     */
    public static function isFinishedContinuousSign(int $user_id, int $start_time, int $end_time, int $target_day): bool
    {
        if ($target_day <= 0 || $start_time >= $end_time || ($end_time - $start_time) / ONE_DAY < $target_day) {
            throw new Exception('参数错误');
        }
        $sign_list = self::find()
            ->select('sign_time')
            ->where(['user_id' => $user_id])
            ->andWhere('sign_time >= :start_time')
            ->andWhere('sign_time < :end_time')
            ->params([':start_time' => $start_time, ':end_time' => $end_time])
            ->orderBy('sign_time ASC')
            ->column();
        if (empty($sign_list) || count($sign_list) < $target_day) {
            return false;
        }
        $continuous_days = 1;
        foreach ($sign_list as $key => $sign_log) {
            if ($key >= 1 && ($sign_list[$key] - $sign_list[$key - 1]) === ONE_DAY) {
                $continuous_days++;
            } else {
                $continuous_days = 1;
            }
            if ($continuous_days >= $target_day) {
                return true;
            }
        }
        return false;
    }
}
