<?php

namespace app\models;

use app\components\util\MUtils;
use missevan\util\MUtils as MUtils2;
use yii\db\Exception;
use yii\db\Expression;

/**
 * This is the model class for table "m_user_node_log".
 *
 * @property int $id 主键
 * @property int $user_id 用户 ID
 * @property int $sound_id 音频 ID
 * @property string $equip_id 设备号
 * @property string $buvid BUVID
 * @property string $node_ids 故事线节点 ID，以半角逗号分隔，如 1,2
 * @property int $current_node_id 用户当前选择的节点 ID
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 */
class MUserNodeLog extends ActiveRecord
{
    // 场景：更新用户故事线数据
    const SCENARIO_UPDATE = 'update';

    public $is_user;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_user_node_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'sound_id', 'current_node_id'], 'required'],
            [['user_id', 'sound_id', 'current_node_id', 'create_time', 'modified_time'], 'integer'],
            [['node_ids'], 'string', 'max' => 225],
            [['equip_id'], 'string', 'max' => 50],
            ['node_ids', 'checkStoryLine', 'on' => self::SCENARIO_UPDATE],
        ];
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios[self::SCENARIO_UPDATE] = ['user_id', 'sound_id', 'equip_id', 'node_ids'];
        return $scenarios;
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => '用户 ID',
            'sound_id' => '音频 ID',
            'equip_id' => '设备号',
            'node_ids' => '故事线节点 ID 组',
            'current_node_id' => '用户当前选择的节点 ID',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'buvid' => 'BUVID',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($this->isNewRecord) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 检测故事线是否合法
     *
     * @param string $attribute 验证的属性
     * @param mixed[] $params 其他自定义参数
     * @todo 需要验证节点组成的故事线是否合法
     */
    public function checkStoryLine($attribute, $params)
    {
        $node_ids = explode(',', $this->$attribute);
        if (!MUtils2::isUintArr($node_ids)) {
            // 故事线中节点 ID 必须都为非负整数
            return $this->addError($attribute, '互动广播剧用户故事线节点数据错误');
        }
        if (!in_array($this->current_node_id, $node_ids)) {
            return $this->addError($attribute,
                "当前节点不在故事线节点中，node_id：{$this->current_node_id}，node_list：{$this->$attribute}");
        }
        $root_node_id = MSoundNode::getRootNodeId($this->sound_id);
        if ((int)$node_ids[0] !== $root_node_id) {
            // 若故事线起始节点不为互动剧根节点，则报错
            return $this->addError($attribute, '互动广播剧用户故事线起始节点数据错误');
        }
    }

    /**
     * 新增或更新用户用户互动剧集故事线信息
     *
     * @param int $node_id 用户当前选择的节点 ID
     * @param int $user_id 用户 ID
     * @param int $sound_id 节点所属音频 ID
     * @param string $equip_id 设备号
     * @param string $buvid 唯一设备标识
     * @param array $node_ids 故事线中的节点 ID，以半角逗号分隔，如：1,2
     * @throws Exception
     */
    public static function saveStoryList(int $node_id, int $user_id, int $sound_id, string $equip_id, string $buvid,
            array $node_ids = [])
    {
        $condition = [];
        if ($user_id) {
            // 存在用户 ID 时，以用户 ID 和设备 ID 作为查询条件
            $condition = [
                'sound_id' => $sound_id,
                'equip_id' => $equip_id,
                'user_id' => $user_id,
            ];
        } else {
            $condition = [
                'sound_id' => $sound_id,
                'equip_id' => $equip_id,
                'user_id' => 0,
            ];
        }
        $user_node_log = self::find()->where($condition)->orderBy('modified_time DESC')
            ->limit(1)->one();
        if (!$user_node_log) {
            $user_node_log = new self(['scenario' => self::SCENARIO_UPDATE]);
        }
        $user_node_log->user_id = $user_id;
        $user_node_log->sound_id = $sound_id;
        $user_node_log->equip_id = $equip_id;
        $user_node_log->buvid = $buvid;
        $node_ids_str = implode(',', $node_ids);
        $user_node_log->node_ids = $node_ids_str;
        $user_node_log->current_node_id = $node_id;
        if (!$user_node_log->save()) {
            throw new Exception(MUtils::getFirstError($user_node_log));
        }
    }

    /**
     * 获取用户互动剧历史故事线节点数据
     *
     * @param int $user_id 用户 ID
     * @param int $sound_id 互动广播剧关联音频 ID
     * @param string $equip_id 设备号，若用户 ID 不为 0，则该值不作为查询条件
     * @return array
     * @throws \Exception 若传入用户 ID 为 0 且为设备号为空字符串时抛出参数错误的异常
     */
    public static function getStoryList(int $user_id, int $sound_id, string $equip_id): array
    {
        $user_node_log = self::findUserLog($user_id, $sound_id, $equip_id);
        $story_node_ids = [];
        if ($user_node_log) {
            $story_node_ids = explode(',', $user_node_log->node_ids);
        }
        $root_node_id = MSoundNode::getRootNodeId($sound_id);
        if (empty($story_node_ids) || (int)$story_node_ids[0] !== $root_node_id) {
            // 若用户无历史故事线或故事线开始节点不为根节点（数据错误情况下）
            // 返回根节点信息，避免用户无法回溯到故事开始节点
            $story_node_ids = [$root_node_id];
        }
        $story_node_ids = array_map('intval', $story_node_ids);
        $story_nodes = MSoundNode::find()
            ->select('id, cover, title, duration, node_type')
            ->allByColumnValues('id', $story_node_ids);
        return array_map(function ($story_node) {
            return [
                'id' => $story_node->id,
                'title' => $story_node->title,
                'front_cover' => $story_node->front_cover,
                'duration' => $story_node->duration,
                'node_type' => $story_node->node_type,
            ];
        }, $story_nodes);
    }

    /**
     * 获取用户故事线最后选择的节点 ID
     *
     * @param int $user_id 用户 ID
     * @param int $sound_id 互动广播剧关联音频 ID
     * @param string $equip_id 设备号，若用户 ID 不为 0，则该值不作为查询条件
     * @return int 节点 ID
     * @throws \Exception 参数错误时抛出异常
     */
    public static function getUserCurrentNodeID(int $user_id, int $sound_id, string $equip_id): int
    {
        $story_log = self::findUserLog($user_id, $sound_id, $equip_id);
        if ($story_log) {
            $story_node_ids = $story_log->node_ids ? explode(',', $story_log->node_ids) : [];
            if (in_array($story_log->current_node_id, $story_node_ids)) {
                // 若存在历史故事线记录，则返回用户最后一个选择的节点
                return $story_log->current_node_id;
            }
        }
        // 若无历史记录，则返回互动广播剧根节点返回
        return MSoundNode::getRootNodeId($sound_id);
    }

    /**
     * 获取用户故事线记录
     *
     * @param int $user_id 用户 ID
     * @param int $sound_id 音频 ID
     * @param string $equip_id 设备号
     * @return null|self
     * @throws \Exception 参数错误时抛出异常
     */
    public static function findUserLog(int $user_id, int $sound_id, string $equip_id): ?self
    {
        if (!$sound_id || (!$user_id && !$equip_id)) {
            throw new \Exception('获取用户互动广播剧故事线参数错误');
        }
        if ($user_id) {
            // 存在用户 ID 时，优先以用户 ID 作为查询条件
            $user_condition = ['sound_id' => $sound_id, 'user_id' => $user_id];
            $user_result = self::find()
                ->where($user_condition)
                ->orderBy('modified_time DESC')
                ->limit(1)
                ->one();
            if ($user_result) {
                return $user_result;
            }
        }
        $condition = ['sound_id' => $sound_id, 'equip_id' => $equip_id];
        return self::find()
            ->where($condition)
            ->orderBy('modified_time DESC')
            ->limit(1)
            ->one();
    }
}
