<?php

namespace app\models;

use app\forms\RechargeForm;
use Exception;
use yii\helpers\Json;
use Yii;

/**
 * This is the model class for table "ios_receipt".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 * @property int $user_id 用户 ID
 * @property int $status 状态
 * @property string $transaction_id 交易 ID
 * @property string $product_id 产品 ID
 */
class IosReceipt extends ActiveRecord
{
    const STATUS_SUSPICIOUS_DOWNLOAD_ID = -6;
    const STATUS_SUSPICIOUS_RISK = -5;
    const STATUS_SUSPICIOUS_POLICY = -4;
    const STATUS_SUSPICIOUS = -3;
    const STATUS_NETWORK_ERROR = -2;
    const STATUS_INVALID = -1;
    const STATUS_PENDING = 0;
    const STATUS_PASS = 1;
    const STATUS_SUCCESS = 2;
    const STATUS_FAILED = 3;
    const STATUS_REFUND = 4;

    const GATEWAY_IOS_VERIFY_RECEIPT = 'https://buy.itunes.apple.com/verifyReceipt';
    const GATEWAY_IOS_VERIFY_RECEIPT_DEV = 'https://sandbox.itunes.apple.com/verifyReceipt';

    /**
     * @var IosReceiptDetail|null
     */
    public $detail = null;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'ios_receipt';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id'], 'required'],
            [['transaction_id'], 'safe'],
        ];
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('logdb');
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'user_id' => '用户 ID',
            'status' => '交易状态',
            'transaction_id' => '交易 ID',
            'product_id' => '产品 ID',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 以忽略异常的方式初始化对象并返回
     * Yii 模型对象初次给数据库字段属性赋值时，会连接数据库，若连接出现异常，则记录到日志
     *
     * @param int $user_id
     * @param string $transaction_id
     * @param string $receipt_data
     *
     * @return self|null iOS 充值日志模型对象
     */
    public static function newRecord(int $user_id, string $transaction_id, string $receipt_data): ?self
    {
        $receiptlog = new self([
            'user_id' => $user_id,
            'status' => self::STATUS_PENDING,
            'transaction_id' => $transaction_id,
        ]);
        $receiptlog->detail = new IosReceiptDetail([
            'buvid' => Yii::$app->equip->getBuvid() ?? '',
            'equip_id' => Yii::$app->equip->getEquipId(),
            'ua' => Yii::$app->request->headers['User-Agent'],
            'ip' => Yii::$app->request->userIP,
            'receipt_data' => $receipt_data,
            'download_id' => 0,
        ]);

        return $receiptlog;
    }

    /**
     * @inheritdoc
     */
    public function ignoreExceptionSave(bool $run_validation = true, string $errlog_prefix = ''): bool
    {
        try {
            $is_new = $this->isNewRecord;
            $ret = parent::ignoreExceptionSave($run_validation, $errlog_prefix);
            if ($ret && $is_new) {
                $this->detail->id = $this->id;
                $this->detail->ignoreExceptionSave($run_validation, $errlog_prefix);
            }

            return $ret;
        } catch (Exception $e) {
            Yii::error(($errlog_prefix === '' ? static::class : $errlog_prefix) . ": {$e->getMessage()}", __METHOD__);
            return false;
        }
    }

    public function setStatusFromLevel($suspicious_level)
    {
        switch ($suspicious_level) {
            case RechargeForm::SUSPICIOUS_LEVEL_SUSPECTED_IOS8:
                $this->status = IosReceipt::STATUS_SUSPICIOUS_POLICY;
                break;
            case RechargeForm::SUSPICIOUS_LEVEL_BILIBILI_RISK:
                $this->status = IosReceipt::STATUS_SUSPICIOUS_RISK;
                break;
            case RechargeForm::SUSPICIOUS_LEVEL_DOWNLOAD_ID:
                $this->status = IosReceipt::STATUS_SUSPICIOUS_DOWNLOAD_ID;
                break;
            case RechargeForm::SUSPICIOUS_LEVEL_SUSPECTED:
                $this->status = IosReceipt::STATUS_SUSPICIOUS;
                break;
            default:
                $this->status = IosReceipt::STATUS_PASS;
        }

        return $this;
    }

    public function isSuspicious()
    {
        return $this->status === self::STATUS_SUSPICIOUS
            || $this->status === self::STATUS_SUSPICIOUS_RISK
            || $this->status === self::STATUS_SUSPICIOUS_DOWNLOAD_ID;
    }

    /**
     * 验证苹果小票
     *
     * @link https://developer.apple.com/documentation/appstorereceipts/verifyreceipt
     * @link https://developer.apple.com/documentation/appstorereceipts/status
     * @param string $receipt
     * @return mixed|null
     */
    public static function verifyReceipt(string $receipt)
    {
        if (YII_ENV_PROD) {
            if (ENABLE_IAP_GATEWAY) {
                $url = Yii::$app->params['service']['iap-gateway']['url'] . '/verifyReceipt';
            } else {
                $url = self::GATEWAY_IOS_VERIFY_RECEIPT;
            }
        } else {
            $url = self::GATEWAY_IOS_VERIFY_RECEIPT_DEV;
        }
        $body = null;
        // https://developer.apple.com/documentation/appstorereceipts/requestbody
        $raw_receipt = Json::encode([
            'receipt-data' => $receipt,
            // 对于订阅类商品，密钥必传
            // 对于其他情形，苹果也建议传（实际测试下来对于消耗型商品的新的小票，密钥也必须传才能正常响应，旧的小票不影响，可不传）
            'password' => Yii::$app->params['service']['apple']['apple_shared_secret'],
        ]);
        try {
            $body = Yii::$app->tools->requestRemote($url, [], 'POST', $raw_receipt, 10);
        } catch (Exception $e) {
            Yii::error('iOS 验证小票失败: ' . $e->getMessage(), __METHOD__);
            // PASS: 下面将再尝试直接访问重试一次（使用降级的网关地址）
            $url = YII_ENV_PROD ? self::GATEWAY_IOS_VERIFY_RECEIPT : self::GATEWAY_IOS_VERIFY_RECEIPT_DEV;
        }

        if (!$body) {
            try {
                $body = Yii::$app->tools->requestRemote($url, [], 'POST', $raw_receipt, ONE_MINUTE);
            } catch (Exception $e) {
                Yii::error('iOS 验证小票失败: ' . $e->getMessage(), __METHOD__);
                return null;
            }
        }

        return $body;
    }

}
