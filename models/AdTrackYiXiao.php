<?php

namespace app\models;

use Exception;
use Yii;

class AdTrackYi<PERSON>ia<PERSON> extends AdTrack implements AdTrackInterface
{
    const CALLBACK_EVENT_TYPE_MAP = [
        self::CALLBACK_EVENT_ACTIVATE => self::CALLBACK_EVENT_TYPE_ACTIVATE,
        self::CALLBACK_EVENT_ONE_DAY_RETENTION => self::CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION,
        self::CALLBACK_EVENT_PAY => self::CALLBACK_EVENT_TYPE_PAY,
        self::CALLBACK_EVENT_REGISTER => self::CALLBACK_EVENT_TYPE_REGISTER,
        self::CALLBACK_EVENT_KEY_ACTION => null,
        self::CALLBACK_EVENT_TRANSACTION => null,  // 无消费 / 充值关键行为
    ];

    // 激活
    const CALLBACK_EVENT_TYPE_ACTIVATE = 'actived';
    // 付费
    const CALLBACK_EVENT_TYPE_PAY = 'pay';
    // 次日留存
    const CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION = 'retained';
    // 注册
    const CALLBACK_EVENT_TYPE_REGISTER = 'register';

    /**
     * 转化事件回调
     *
     * @param string $event_type 事件类型
     * @param mixed $arg 支付金额（单位：元），关键行为参数
     */
    public function callback(string $event_type, $arg = 0)
    {
        try {
            if (!array_key_exists($event_type, self::CALLBACK_EVENT_TYPE_MAP)) {
                throw new Exception('易效点击回传事件错误：' . $event_type);
            }
            $event = self::CALLBACK_EVENT_TYPE_MAP[$event_type];
            if (is_null($event)) {
                return true;
            }
            $data = Yii::$app->tools->requestRemote($this->getCallbackUrl($event, $_SERVER['REQUEST_TIME'] * 1000));
            if (!($data && $data['status'] === 'SUCCESSED')) {
                throw new Exception(sprintf('易效点击回传失败：code[%d], msg[%s]', $data['status'], $data['msg']));
            }
            return true;
        } catch (Exception $e) {
            Yii::error('yixiao ad error: ' . $e->getMessage(), __METHOD__);
        }
        return false;
    }

    /**
     * 转化事件回调
     *
     * @param string $callback_event 事件类型
     * @param int $event_time 转化发生时间
     * @return string
     */
    private function getCallbackUrl(string $callback_event, int $event_time)
    {
        return sprintf('%s&t=%d&conv_type=%s', $this->track_id, $event_time, $callback_event);
    }
}
