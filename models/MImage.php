<?php

namespace app\models;

use app\components\util\Go;
use app\components\util\Image;
use app\components\util\MUtils;
use Exception;
use Imagick;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "m_image".
 *
 * @property integer $id
 * @property integer $user_id
 * @property string $username
 * @property integer $catalog_id
 * @property string $title
 * @property string $save_name
 * @property string $file_ext
 * @property integer $file_size
 * @property integer $width
 * @property integer $height
 * @property string $show_type
 * @property integer $view_count
 * @property integer $comment_count
 * @property integer $favorite_count
 * @property string $access
 * @property string $status_is
 * @property integer $sort_order
 * @property integer $uptimes
 * @property integer $checked
 * @property integer $source
 * @property integer $create_time
 */
class MImage extends ActiveRecord
{
    // 图片审核状态 0：未审核通过；1：审核通过
    const CHECKED_UNPASS = 0;
    const CHECKED_PASS = 1;

    public $banner_pic;
    public $front_cover;
    public $have;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_image';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'username', 'catalog_id', 'title', 'save_name', 'width', 'height'], 'required'],
            [['user_id', 'catalog_id', 'file_size', 'width', 'height', 'view_count', 'comment_count', 'favorite_count', 'sort_order', 'uptimes', 'checked', 'source', 'create_time'], 'integer'],
            [['status_is'], 'string'],
            [['username'], 'string', 'max' => 20],
            [['title', 'save_name', 'access'], 'string', 'max' => 255],
            [['file_ext'], 'string', 'max' => 5],
            [['show_type'], 'string', 'max' => 32],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'username' => 'Username',
            'catalog_id' => 'Catalog ID',
            'title' => 'Title',
            'save_name' => 'Save Name',
            'file_ext' => 'File Ext',
            'file_size' => 'File Size',
            'width' => 'Width',
            'height' => 'Height',
            'show_type' => 'Show Type',
            'view_count' => 'View Count',
            'comment_count' => 'Comment Count',
            'favorite_count' => 'Favorite Count',
            'access' => 'Access',
            'status_is' => 'Status Is',
            'sort_order' => 'Sort Order',
            'uptimes' => 'Uptimes',
            'checked' => 'Checked',
            'source' => 'Source',
            'create_time' => 'Create Time',
        ];
    }

    public function afterFind()
    {
        if (!$this->save_name) {
            $this->front_cover = Yii::$app->params['defaultCoverUrl'];
        } else {
            $this->front_cover = Yii::$app->params["mimagesUrl"] . $this->save_name;
        }
        $this->banner_pic = Yii::$app->params['mimagesbigUrl'] . $this->save_name;
        parent::afterFind();
    }

    public static function getUserPics(int $user_id, int $page_size)
    {
        $query = self::find()->select('id, save_name')->orderBy(['id' => SORT_DESC])
            ->where('user_id = :user_id', [':user_id' => $user_id]);
        if ($user_id !== Yii::$app->user->id) {
            $query->andWhere('checked = :checked', [':checked' => self::CHECKED_PASS]);
        } else {
            $query->andWhere('checked >= :checked', [':checked' => self::CHECKED_UNPASS]);
        }

        return MUtils::getPaginationModels($query, $page_size);
    }

    public static function cropCenter($src)
    {
        $imagick = new Imagick($src);
        $geo = $imagick->getImageGeometry();
        $border = $geo['height'] > $geo['width'] ?
            $geo['width'] : $geo['height'];
        $y = abs(($geo['width'] - $border) / 2);
        $x = abs(($geo['height'] - $border) / 2);

        $imagick->cropImage($border, $border, $y, $x);
        // $imagick->cropThumbnailImage($border, $border);

        $tempextensionstr = '.' . pathinfo($src, PATHINFO_EXTENSION);
        $filename = str_replace($tempextensionstr, "", $src);

        $extension = Image::getImageExtension($imagick->getImageFormat());
        $cover_name = $filename . '_cover' . $extension;
        $imagick->writeImage($cover_name);
        $imagick->clear();

        return $cover_name;
    }

    /**
     * 生成马赛克图
     */
    public static function mosaic(string $image_path): string
    {
        $imagick = new Imagick($image_path);
        $size = $imagick->getImageGeometry();
        $height = intval(1240 * $size['height'] / $size['width']);
        $imagick->thumbnailImage(1240, $height, true);
        $imagick->cropImage(1240, 400, 0, 0);

        $imagick->blurImage(150, 50);

        $imagick->sampleImage(25, 8);
        $imagick->sampleImage(1250, 400);

        $imagick->cropImage(1240, 400, 0, 0);

        $tempextensionstr = '.' . pathinfo($image_path, PATHINFO_EXTENSION);
        $filename = str_replace($tempextensionstr, "", $image_path);
        $extension = Image::getImageExtension($imagick->getImageFormat());
        $cover_name = $filename . '_mosaic' . $extension;
        register_shutdown_function([self::class, 'deleteUploadFile'], $cover_name);

        $imagick->writeImage($cover_name);

        $imagick->clear();
        return $cover_name;
    }

    //上传用户自定义的封面
    public static function uploadCover($src)
    {
        if (!isset($src['tmp_name'])) throw new \Exception('文件缺失');
        if (!is_uploaded_file($src['tmp_name'])) throw new \Exception('必须通过 HTTP POST 上传');
        $extension = MUtils::getFileExtension($src['tmp_name']);
        if (!preg_match('/^(jpe?g|png)$/i', $extension)) {
            // WORKAROUND: 对非正常上传行为进行记录，用于观察是否为恶意攻击
            Yii::warning('Abnormal cover upload', __METHOD__);
            throw new HttpException(400, '封面仅允许上传 jpg 或 png 文件');
        }
        // 文件存放路径
        $upload_path = MUtils2::makeLocalFilesDir();
        $image_file = $upload_path . md5($src['name']) . date('His') . '.' . $extension;
        $image_file = urldecode($image_file);
        // 移动文件
        move_uploaded_file($src['tmp_name'], $image_file);
        // 编辑封面图
        $image_file = self::editCoverImage($image_file);
        // storage 存放地址
        $save_name = MUtils::generateFilePath($image_file);
        if (!$save_name) {
            throw new \Exception('上传的图片不存在');
        }
        try {
            // storage 上传原图
            Yii::$app->storage->upload($image_file, OSS_DIR_USER_COVER . '/' . $save_name, true);
        } catch (Exception $e) {
            throw new \Exception('上传失败，如果多次失败请联系管理员');
        }
        // 删除原图
        is_file($image_file) && unlink($image_file);
        // 封面图违规检测
        $storage_img_url = Yii::$app->params['userCoverUrl'] . $save_name;
        $result = Yii::$app->go->checkImage($storage_img_url, Go::SCENE_USER_INFO);
        if ($result && !$result['pass']) {
            // TODO: 若检测成功并且图片违规，删除该图片并提示用户封面图违规，检测失败的情况暂时不处理
            Yii::$app->storage->deleteFile(OSS_DIR_USER_COVER . '/' . $save_name);
            throw new HttpException(400, '封面图违规喔~');
        }
        return $save_name;
    }

    /**
     * 编辑封面图（gif 的图转 jpg 加白色背景, png 的图加白色背景）
     * gif 图只取第一帧，当为透明背景时做背景白色处理
     *
     * @param string $image_file 图片地址
     * @return string
     * @throws \ImagickException
     */
    public static function editCoverImage(string $image_file)
    {
        $path_info = pathinfo($image_file);
        if (!is_file($image_file) || !isset($path_info['dirname']) || !isset($path_info['filename'])) {
            throw new Exception('图片文件路径错误');
        }
        $imagick = new Imagick($image_file);
        $base_image = null;
        $format = $imagick->getImageFormat();
        $extension = Image::getImageExtension($format);

        $filename = $path_info['dirname'] . '/' . $path_info['filename'];
        $cover_name = $filename . '_cover' . $extension;
        $size = $imagick->getImagePage();
        $image_width = $image_height = $size['width'] > $size['height'] ? $size['height'] : $size['width'];
        $target_w = $image_width;
        $target_h = $image_height;
        $source_w = $size['width'];
        $source_h = $size['height'];

        // 计算裁剪的位置
        $judge = ($source_w / $source_h) > ($target_w / $target_h);
        if ($judge) {
            $start_x = (int)(($source_w - $target_w) / 2);
            $start_y = 0;
        } else {
            $start_x = 0;
            $start_y = (int)(($source_h - $target_h) / 2);
        }
        $TARG_W = $TARG_H = 500;
        try {
            if ($format === Image::FORMAT_GIF) {
                $extension = Image::getImageExtension(Image::FORMAT_JPEG);
                $cover_name = $filename . '_cover' . $extension;
                $base_image = $imagick->coalesceImages();
                $base_image->cropImage($target_w, $target_h, $start_x, $start_y);
                if ($image_width > $TARG_W) {
                    $base_image->thumbnailImage($TARG_W, $TARG_H);
                }
                // TODO: 优化写法，$base_image 直接赋值 $imagick 统一处理会导致单元测试 Segmentation fault
                // 上传图片背景为透明改为白色
                if ($base_image->getImageAlphaChannel()) {
                    $base_image->setImageBackgroundColor('white');
                    $base_image->setImageAlphaChannel(Imagick::ALPHACHANNEL_REMOVE);
                }
                $base_image->setImageFormat(Image::FORMAT_JPEG);
                $base_image->writeImage($cover_name);
            } else {
                $imagick->stripImage();
                $imagick->cropImage($target_w, $target_h, $start_x, $start_y);
                if ($image_width > $TARG_W) {
                    $imagick->thumbnailImage($TARG_W, $TARG_H);
                }
                // 上传图片背景为透明改为白色
                if ($imagick->getImageAlphaChannel()) {
                    $imagick->setImageBackgroundColor('white');
                    $imagick->setImageAlphaChannel(Imagick::ALPHACHANNEL_REMOVE);
                }
                $imagick->writeImage($cover_name);
            }
            // TODO: 该函数需调整单元测试（当前单元测试结束后执行）
            register_shutdown_function([self::class, 'deleteUploadFile'], $cover_name);
            // TODO: 如果是 APNG，只保留第一帧
            return $cover_name;
        } catch (Exception $e) {
            throw $e;
        } finally {
            $imagick->clear();
            if ($base_image) {
                $base_image->clear();
            }
        }
    }

    /**
     * 删除新生成的图片地址
     *
     * @param string $image_file 新生成的图片地址
     */
    public static function deleteUploadFile(string $image_file)
    {
        @unlink($image_file);
    }
}
