<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "pay_bad_debt".
 *
 * @property int $id 主键
 * @property int $user_id 用户 ID
 * @property int $order_id 订单 ID
 * @property int $debt 坏账值（单位为普通钻）
 * @property int $type 坏账类型（1: iOS 退款）
 * @property string $reason 原因
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 */
class PayBadDebt extends ActiveRecord
{
    // 坏账类型：1 iOS 退款，2 天猫充值退款
    const TYPE_IOS_REFUND = 1;
    const TYPE_TMALL_REFUND = 2;

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->paydb;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'pay_bad_debt';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'debt'], 'required'],
            [['user_id', 'order_id', 'debt', 'type', 'create_time', 'modified_time'], 'integer'],
            [['reason'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'user_id' => '用户 ID',
            'order_id' => '订单 ID',
            'debt' => '坏账值',  // 单位为普通钻
            'type' => '坏账类型',  // 1: iOS 退款
            'reason' => '原因',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

}
