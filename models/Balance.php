<?php

namespace app\models;

use app\components\models\BalanceInterface;
use app\components\models\traits\UserTrait;
use yii\web\HttpException;
use Yii;

/**
 * This is the model class for table "balance".
 *
 * @property integer $id
 * @property integer $ios
 * @property integer $android
 * @property integer $paypal
 * @property integer $tmallios
 * @property integer $googlepay
 * @property integer $in_ios
 * @property integer $in_android
 * @property integer $in_paypal
 * @property integer $in_tmallios
 * @property integer $drama_buy_profit
 * @property integer $drama_reward_profit
 * @property integer $live_profit
 * @property integer $other_profit
 * @property integer $all_drama_buy_profit
 * @property integer $all_drama_reward_profit
 * @property integer $all_live_profit
 * @property integer $all_other_profit
 * @property integer $all_consumption
 * @property integer $all_topup
 * @property integer $all_coin
 * @property integer $new_live_profit
 * @property integer $new_all_live_profit
 */
class Balance extends ActiveRecord implements BalanceInterface
{
    use UserTrait;

    const BALANCE_TYPE_TOPUP = 1;
    const BALANCE_TYPE_REWARD = 2;

    // 单位换算：1：“分”换成“元”；2：“元”换成“分”；3：“钻石”换成货币；“钻石”换成“分”
    const CONVERT_FEN_TO_YUAN = 1;
    const CONVERT_YUAN_TO_FEN = 2;
    const CONVERT_DIAMOND_TO_YUAN = 3;
    const CONVERT_DIAMOND_TO_FEN = 4;
    const CONVERT_YUAN_TO_DIAMOND = 5;
    const CONVERT_FEN_TO_DIAMOND = 6;

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->paydb;
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'balance';
    }

    /**
     * 返回和用户余额相关的字段
     *
     * @return array
     */
    public static function balanceFields(): array
    {
        return ['googlepay', 'ios', 'paypal', 'tmallios', 'android'];
    }

    public function balanceSnapshot()
    {
        $balance = [];
        foreach (self::balanceFields() as $field) {
            $balance[$field] = $this->{$field};
        }

        return $balance;
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id'], 'required'],
            [
                array_merge(['id'], self::balanceFields(), array_map(function ($field) {
                    return 'in_' . $field;
                }, self::balanceFields())),
                'integer',
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        $result = [];
        foreach (self::balanceFields() as $field) {
            $result[$field] = $field . ' 余额';
            $result['in_' . $field] = $field . ' 收入';
        }

        return array_merge(['id' => '用户 ID'], $result, [
            'drama_buy_profit' => '剧集购买收益余额',
            'drama_reward_profit' => '剧集打赏收益余额',
            'live_profit' => '直播总收益余额',  // 2020.06.01 00:00:00 前
            'other_profit' => '其他总收益余额', // 微信男友 + 全职语音包
            'all_drama_buy_profit' => '累计剧集购买收益',
            'all_drama_reward_profit' => '累计剧集打赏收益',
            'all_live_profit' => '直播累计收益',  // 2020.06.01 00:00:00 前
            'all_other_profit' => '其他累计收益',
            'all_consumption' => '用户总消费',
            'all_topup' => '用户总充值额',
            'all_coin' => '用户余额总数', // 用于支持各个子项为负数, 之前是通过非负来保障并发消费不透支，需要该字段来判断
            'new_live_profit' => '新直播收益余额',  // 2020.06.01 00:00:00 起
            'new_all_live_profit' => '新直播收益总额',  // 2020.06.01 00:00:00 起
        ]);
    }

    /**
     * 获取充值或收益余额
     *
     * @param int $type 余额类型，1：收益；2：充值
     * @param bool $get_noble_balance 当 type 为 2 时，是否获取直播贵族钻石余额
     * @return array
     */
    public function getBalanceDetail(int $type, bool $get_noble_balance = false)
    {
        switch ($type) {
            case self::BALANCE_TYPE_REWARD:
                return [
                    'income' => $this->getTotalIncome(),
                    'live_profit' => $this->new_live_profit,
                ];
            default:
                $balances = [
                    'balance' => PayAccounts::getAccounts($this->id, PayAccount::SCOPE_COMMON, -1, $this)->getTotalBalance(),
                ];
                if ($get_noble_balance) {
                    $balances += UserNoble::getBalance($this->id);
                }
                return $balances;
        }
    }

    /**
     * 返回用户总余额
     *
     * @return integer
     */
    public function getTotalBalance()
    {
        return $this->getBalanceDetail(self::BALANCE_TYPE_TOPUP)['balance'];
    }

    /**
     * 返回用户总收益
     * @deprecated
     *
     * @return integer
     */
    public function getTotalIncome()
    {
        $count = 0;
        foreach (self::balanceFields() as $field) {
            $count += $this->{'in_' . $field};
        }
        return $count;
    }

    /**
     * 收益单位元和单位分的互相换算
     *
     * @param float|int $profit 收益
     * @param int $type 换算类型 1：“分”换成“元”；2：“元”换成“分”；3：“钻石”换成“元”
     *
     * @return float|int 换算成功的收益
     * @throws
     */
    public static function profitUnitConversion($profit, int $type)
    {
        $KEY_PROFIT_UNIT = 100;

        switch ($type) {
            case self::CONVERT_FEN_TO_YUAN:
                // 省略分位后的数（98.8 分 => 0.98 元）
                return floor($profit) / $KEY_PROFIT_UNIT;
            case self::CONVERT_YUAN_TO_FEN:
                // 收益换算成分为单位，(int)round 是预防精度损失
                // 备注：对 0.57 元直接运算 intval(0.57 * 100)，会得到 56（分）的结果。
                return (int)round($profit * $KEY_PROFIT_UNIT, 1);
            case self::CONVERT_DIAMOND_TO_YUAN:
                return floor($profit) / DIAMOND_EXRATE;
            case self::CONVERT_DIAMOND_TO_FEN:
                return $profit * 10;
            case self::CONVERT_YUAN_TO_DIAMOND:
                return intval($profit * DIAMOND_EXRATE);
            case self::CONVERT_FEN_TO_DIAMOND:
                return intval($profit / 10);
            default:
                throw new HttpException(404, Yii::t('app/error', 'params error'));
        }
    }

    /**
     * 判断用户需要提现的收益和用户当前可提现的收益
     *
     * @param int $withdraw_type 收益类型
     * @param float|int $withdraw_profit
     *
     * @return bool 用户需要提现的收益小于用户当前可提现的收益
     * @throws HttpException
     */
    public function checkWithdrawProfit($withdraw_type, $withdraw_profit)
    {
        switch ($withdraw_type) {
            case WithdrawalRecord::TYPE_WITHDRAW_LIVE_NEW:
                if ($this->new_live_profit < $withdraw_profit) {
                    throw new HttpException(403, '您的直播收益可提现余额不足');
                }
                break;
            case WithdrawalRecord::TYPE_WITHDRAW_LIVE:
                if ($this->live_profit < $withdraw_profit) {
                    throw new HttpException(403, '您的直播收益可提现余额不足');
                }
                break;
            case WithdrawalRecord::TYPE_WITHDRAW_DRAMA_BUY:
                if ($this->drama_buy_profit < $withdraw_profit) {
                    throw new HttpException(403, '您的剧集购买收益可提现余额不足');
                }
                break;
            case WithdrawalRecord::TYPE_WITHDRAW_DRAMA_REWARD:
                if ($this->drama_reward_profit < $withdraw_profit) {
                    throw new HttpException(403, '您的剧集打赏收益可提现余额不足');
                }
                break;
            case WithdrawalRecord::TYPE_WITHDRAW_OTHER:
                if ($this->other_profit < $withdraw_profit) {
                    throw new HttpException(403, '您的其他收益可提现余额不足');
                }
                break;
            default:
                throw new HttpException(404, '该收益类型不存在');
        }
        return true;
    }

    public function isGuild()
    {
        return false;
    }

    public function isNeverTopup()
    {
        return !RechargeOrder::hasTopuped($this->id);
    }

    /**
     * @param \app\components\web\User|Mowangskuser $user
     * @return bool
     */
    public function hasNewUserTopupDiscount($user)
    {
        return ($_SERVER['REQUEST_TIME'] - $user->getRegisterAt() < 2 * ONE_WEEK) && $this->isNeverTopup();
    }

}
