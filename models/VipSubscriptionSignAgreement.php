<?php

namespace app\models;

use app\components\auth\AuthAli;
use app\components\auth\AuthWechat;
use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "vip_subscription_sign_agreement".
 *
 * @property integer $id 主键
 * @property integer $create_time 创建时间（秒级时间戳）
 * @property integer $modified_time 修改时间（秒级时间戳）
 * @property integer $user_id 用户 ID
 * @property integer $pay_type 付费方式：1 iOS、2 微信、3 支付宝、4 Google Play
 * @property integer $vip_id 会员套餐价目 ID
 * @property integer $status 协议签约状态：1 待签约，2 签约生效中，3 签约到期
 * @property integer $start_time 协议生效时间（秒级时间戳）
 * @property integer $expire_time 协议失效时间（秒级时间戳），含此时刻，待签约和签约生效中时为 0
 * @property string $agreement_no 第三方平台协议编号
 * @property array $more 更多详情，e.g. {"alipay_open_id": "0123456789" }
 */
class VipSubscriptionSignAgreement extends ActiveRecord
{
    use ActiveRecordTrait;

    const AGREEMENT_NO_PREFIX = 'vip_agreement_';

    // 协议签约状态
    const STATUS_PENDING = 1; // 待签约
    const STATUS_ACTIVE = 2; // 签约生效中
    const STATUS_TERMINATED = 3; // 已解约

    // 付费方式（与 VipFeeDeductedRecord 中的同名常量一致）
    const PAY_TYPE_IOS = 1;
    const PAY_TYPE_WECHAT = 2;
    const PAY_TYPE_ALIPAY = 3;
    const PAY_TYPE_GOOGLE_PLAY = 4;

    // 操作类型
    const OPERATION_TYPE_ADD = 1;  // 签约
    const OPERATION_TYPE_DELETE = 2;  // 解约

    // 生成协议编号时各参数的所占位数长度
    const AGREEMENT_NO_PAY_TYPE_PAD_LENGTH = 3;  // 支付方式所占位数长度
    const AGREEMENT_NO_ID_PAD_LENGTH = 10;  // 协议 ID 所占位数长度

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->paydb;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'vip_subscription_sign_agreement';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'pay_type', 'vip_id', 'status', 'start_time', 'expire_time'], 'required'],
            [['create_time', 'modified_time', 'user_id', 'pay_type', 'vip_id', 'status', 'start_time', 'expire_time'], 'integer'],
            [['pay_type'], 'in', 'range' => [
                self::PAY_TYPE_IOS,
                self::PAY_TYPE_WECHAT,
                self::PAY_TYPE_ALIPAY,
                self::PAY_TYPE_GOOGLE_PLAY,
            ]],
            [['status'], 'in', 'range' => [
                self::STATUS_PENDING,
                self::STATUS_ACTIVE,
                self::STATUS_TERMINATED,
            ]],
            [['agreement_no', 'more'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'user_id' => '用户 ID',
            'pay_type' => '付费方式',
            'vip_id' => '会员套餐价目 ID',
            'status' => '协议签约状态',
            'start_time' => '协议生效时间',
            'expire_time' => '协议失效时间',
            // 对于 iOS，同一 Apple 账号生成续订订单的原始交易 ID（original_transaction_id）一致，使用原始交易 ID 作为订阅协议编号
            'agreement_no' => '协议编号',
            'more' => '更多详情',
        ];
    }

    /**
     * 获取协议编号
     *
     * @return string
     */
    public function getOutAgreementNo(): string
    {
        return (YII_ENV_PROD ?  '' : 'dev_') .
            self::AGREEMENT_NO_PREFIX .
            $this->create_time .
            str_pad($this->pay_type, self::AGREEMENT_NO_PAY_TYPE_PAD_LENGTH, '0', STR_PAD_LEFT) .
            str_pad($this->id, self::AGREEMENT_NO_ID_PAD_LENGTH, '0', STR_PAD_LEFT);
    }

    /**
     * 根据协议编号获取实际 ID
     *
     * @param string $agreement_no 协议编号
     * @return int
     */
    public static function getRealId(string $agreement_no): int
    {
        return (int)substr($agreement_no, -self::AGREEMENT_NO_ID_PAD_LENGTH);
    }

    /**
     * 解析协议编号，获取实际 ID 和支付方式
     *
     * @param string $out_agreement_no
     * @return array|null 如果协议编号无效，返回 null
     */
    public static function parseOutAgreementNo(string $out_agreement_no): ?array
    {
        $prefix = (YII_ENV_PROD ? '' : 'dev_') . self::AGREEMENT_NO_PREFIX;
        if (!str_starts_with($out_agreement_no, $prefix)) {
            return null;
        }

        $id = self::getRealId($out_agreement_no);

        $pay_type = (int)substr($out_agreement_no,
            -(self::AGREEMENT_NO_PAY_TYPE_PAD_LENGTH + self::AGREEMENT_NO_ID_PAD_LENGTH),
            self::AGREEMENT_NO_PAY_TYPE_PAD_LENGTH);
        if (!in_array($pay_type, [
            self::PAY_TYPE_IOS,
            self::PAY_TYPE_WECHAT,
            self::PAY_TYPE_ALIPAY,
            self::PAY_TYPE_GOOGLE_PLAY,
        ])) {
            return null;
        }

        return [
            'id' => $id,
            'pay_type' => $pay_type,
        ];
    }

    /**
     * 更新协议（签约或解约）
     *
     * @param int $operation_type 操作类型（1: 签约；2: 解约）
     * @param int $pay_type 付费方式（1: iOS；2: 微信；3: 支付宝；4: Google Play）
     * @param string $out_agreement_no 协议编号
     * @param string|null $agreement_no 支付平台的协议 ID
     * @param callable|null $func
     * @return bool
     */
    public static function updateAgreement(int $operation_type, int $pay_type, string $out_agreement_no, ?string $agreement_no, callable $func = null)
    {
        if (!in_array($operation_type, [self::OPERATION_TYPE_ADD, self::OPERATION_TYPE_DELETE])) {
            return false;
        }
        $agreement_id = self::getRealId($out_agreement_no);
        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_SIGN_TERMINATION_VIP_AGREEMENT, $agreement_id);
        if (!$redis->lock($lock, ONE_MINUTE)) {
            return false;
        }
        try {
            $transaction = self::getDb()->beginTransaction();
            $fee_record = VipFeeDeductedRecord::findOne(['sign_agreement_id' => $agreement_id, 'pay_type' => $pay_type]);
            if (!$fee_record) {
                throw new Exception('扣费记录不存在');
            }
            if ($fee_record->is_first_topup_discount
                    && VipFeeDeductedRecord::hasSubscribedPromotional($fee_record->user_id, $fee_record->id)) {
                // 如果本次开通的扣费记录是连续包月首次优惠，并且用户已经享受过除了本次以外的连续包月首次优惠，说明用户有薅羊毛嫌疑，此时不予签约成功
                // 用户未达到享受首次开通优惠的条件。比如创建多个首次充值并且待支付的订单，然后支付其中一个订单，剩余的其他订单如果再次完成支付会进入此分支
                // 这里我们不自动退款，需要找客服退款或延长会员时长
                throw new Exception(sprintf('用户 %d 未达到享受折扣福利的条件，扣款记录 ID: %d', $fee_record->user_id, $fee_record->id));
            }
            $record = self::findOneForUpdate('id = :id AND pay_type = :pay_type',
                [':id' => $agreement_id, ':pay_type' => $pay_type]);
            if (!$record) {
                throw new Exception('签约记录不存在');
            }
            $more = $record->more ?? [];
            switch ($operation_type) {
                case self::OPERATION_TYPE_ADD:
                    if ($record->status === self::STATUS_ACTIVE) {
                        // 签约时若当前状态为已签约，直接返回成功
                        return true;
                    }
                    if ($record->status !== self::STATUS_PENDING) {
                        throw new Exception(sprintf('签约时签约协议状态错误：%d', $record->status));
                    }
                    break;
                case self::OPERATION_TYPE_DELETE:
                    if ($record->status === self::STATUS_TERMINATED) {
                        // 解约时若当前状态为已解约，直接返回成功
                        return true;
                    }
                    if ($record->status !== self::STATUS_ACTIVE) {
                        throw new Exception(sprintf('解约时签约协议状态错误：%d', $record->status));
                    }
                    $active_agreement_no = $record->agreement_no;
                    if (!$active_agreement_no || $active_agreement_no !== $agreement_no) {
                        throw new Exception(sprintf('解约时委托代扣协议 ID 不一致：%s, %s', $active_agreement_no, $agreement_no));
                    }
                    break;
                default:
                    throw new Exception('操作类型错误');
            }

            $vip = MVip::findOne(['id' => $record->vip_id]);
            if (!$vip) {
                throw new Exception('会员价目不存在');
            }
            $now_time = $_SERVER['REQUEST_TIME'];
            if ($operation_type === self::OPERATION_TYPE_ADD) {
                $record->status = self::STATUS_ACTIVE;
                $record->start_time = $now_time;
                $record->agreement_no = $agreement_no;
                $more['out_agreement_no'] = $out_agreement_no;
            } else {
                $record->status = self::STATUS_TERMINATED;
                $record->expire_time = $now_time;
            }
            $record->more = $more;
            if (is_callable($func)) {
                $func($record, $fee_record, $vip);
            }
            if (!$record->save()) {
                throw new Exception('签约协议记录更新失败: ' . MUtils2::getFirstError($record));
            }
            $transaction->commit();
            $transaction = null;
            if ($operation_type === self::OPERATION_TYPE_ADD && $record->status === self::STATUS_ACTIVE) {
                // 当前已是自动续费会员时，在相同付款渠道签约【自动续费档位】，则老协议解约，后续按最新签约的【自动续费档位】进行续费
                // 当前已是自动续费会员时，在其他付款渠道签约【自动续费档位】，前置平台不主动解约
                // 根据以上产品需求，新协议签约成功后，解除用户本次签约所使用的付费平台上生效中的协议
                self::terminateOtherActiveAgreement($record);
            }
            return true;
        } catch (Exception $e) {
            if ($transaction) {
                $transaction->rollBack();
            }
            Yii::error(sprintf('更新签约协议出错：%s，签约协议记录 ID: %d', $e->getMessage(), $agreement_id), __METHOD__);
            return false;
        } finally {
            // 解锁
            $redis->unlock($lock);
        }
    }

    /**
     * 获取支付宝用户登录 ID
     *
     * @return string|null null 表示没有支付宝用户登录 ID
     */
    public function getAlipayLogonId(): ?string
    {
        return $this->more['alipay_logon_id'] ?? null;
    }

    /**
     * 解除本次成功签约的签约记录对应的支付方式上生效中的协议（排除本次签约记录）
     *
     * @param self $agreement_record 本次成功签约的签约记录
     * @return bool
     */
    public static function terminateOtherActiveAgreement(self $agreement_record)
    {
        try {
            /**
             * @var $agreement VipSubscriptionSignAgreement
             */
            $agreement = self::find()
                ->where('id <> :id AND user_id = :user_id AND status = :status AND pay_type = :pay_type',
                    [
                        // 排除本次签约记录
                        ':id' => $agreement_record->id,
                        ':user_id' => $agreement_record->user_id,
                        ':status' => self::STATUS_ACTIVE,
                        ':pay_type' => $agreement_record->pay_type,
                    ]
                )->one();
            if (!$agreement) {
                return false;
            }
            switch ($agreement->pay_type) {
                case self::PAY_TYPE_WECHAT:
                    if (!$agreement->agreement_no) {
                        throw new Exception(sprintf('签约协议中支付平台协议 ID 为空，协议记录 ID: %d', $agreement->id));
                    }
                    $reason = "新协议签约成功，解除旧协议，新协议编号为: {$agreement_record->getOutAgreementNo()}";
                    $wechat = new AuthWechat();
                    $wechat->deleteContract($agreement->agreement_no, $reason);
                    break;
                case self::PAY_TYPE_ALIPAY:
                    $alipay = new AuthAli();
                    $res = $alipay->unSignUserAgreement($agreement->agreement_no);
                    if (!$res || !isset($res->code) || $res->code !== AuthAli::CODE_SUCCESS) {
                        throw new Exception(sprintf('支付宝解约失败，协议记录 ID: %d，接口响应: %s', $agreement->id, Json::encode($res)));
                    }
                    break;
                case self::PAY_TYPE_GOOGLE_PLAY:
                    // TODO: 其他平台的解约逻辑待补充
                    break;
                default:
                    throw new Exception(sprintf('未知的支付方式：%d，协议记录 ID: %d', $agreement->pay_type, $agreement->id));
            }
            return true;
        } catch (Exception $e) {
            Yii::error(sprintf('解除用户生效中的协议出错：%s，%s', $agreement_record->user_id, $e->getMessage()), __METHOD__);
            return false;
        }
    }

    /**
     * @param string $agreement_no
     * @param int $pay_type
     * @param int $status
     * @return self|null|\yii\db\ActiveRecord
     */
    public static function getAgreementByNo(string $agreement_no, int $pay_type, $status = -1)
    {
        $query = self::find()->where([
            'pay_type' => $pay_type,
            'agreement_no' => $agreement_no,
        ]);
        if ($status !== -1) {
            $query->andWhere(['status' => $status]);
        }
        return $query->orderBy('id DESC')->limit(1)->one();
    }

    /**
     * @param int $user_id
     * @param int $pay_type
     * @return self|null|\yii\db\ActiveRecord
     */
    public static function getActiveAgreement(int $user_id, int $pay_type)
    {
        return self::find()
            ->where([
                'status' => self::STATUS_ACTIVE,
                'user_id' => $user_id,
                'pay_type' => $pay_type,
            ])->one();
    }

    public function getDeductFeeSchedule(): int
    {
        return (int)MVip::find()
            ->select('deduct_fee_schedule')
            ->where(['id' => $this->vip_id])
            ->scalar();
    }

    public function isMonthlyDeductFeeSchedule(): bool
    {
        return $this->getDeductFeeSchedule() === MVip::DEDUCT_FEE_SCHEDULE_MONTHLY;
    }

    public function isQuarterlyDeductFeeSchedule(): bool
    {
        return $this->getDeductFeeSchedule() === MVip::DEDUCT_FEE_SCHEDULE_MONTHLY;
    }

    public function markExpired()
    {
        $this->status = self::STATUS_TERMINATED;
        $this->expire_time = $_SERVER['REQUEST_TIME'];
    }

    public function expireNow()
    {
        $this->markExpired();
        if (!$this->save()) {
            throw new Exception(MUtils2::getFirstError($this));
        }
    }

    public function isPending()
    {
        return $this->status === self::STATUS_PENDING;
    }

    public function isActive()
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    public function isExpired()
    {
        return $this->status === self::STATUS_TERMINATED;
    }

    public static function newAgreement(string $agreement_no, int $user_id, int $vip_id, int $pay_type, int $status)
    {
        return new self([
            'user_id' => $user_id,
            'vip_id' => $vip_id,
            'status' => $status,
            'pay_type' => $pay_type,
            'agreement_no' => $agreement_no,
            'start_time' => $_SERVER['REQUEST_TIME'],
            'expire_time' => 0,
        ]);
    }

}
