<?php

namespace app\models;

use app\components\util\Equipment;
use app\components\util\MUtils;
use Yii;
use yii\data\Pagination;
use yii\db\Expression;
use yii\db\Query;
use yii\web\HttpException;

/**
 * This is the model class for table "m_message_assign".
 *
 * @property integer $id
 * @property integer $recuid
 * @property integer $send_uid
 * @property string $title
 * @property string $content
 * @property integer $status
 * @property integer $time
 */
class MMessageAssign extends ActiveRecord
{
    const NOT_READ = 0;
    const ALREADY_READ = 1;

    // 通知类型，1：全局通知；2：个人专属通知
    const TYPE_PUBLIC = 1;
    const TYPE_PRIVATE = 2;

    // 获取通知状态的类型，0：获取所有；1：获取未读；2：获取已读
    const TYPE_GET_ALL = 0;
    const TYPE_GET_UNREAD = 1;
    const TYPE_GET_READ = 2;

    public static function tableName()
    {
        return 'm_message_assign';
    }

    public function rules()
    {
        return [
            [['id', 'recuid', 'send_uid', 'status', 'time'], 'integer'],
            [['title', 'content'], 'string'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'recuid' => '收信人 ID',
            'send_uid' => '发信人 ID ', // 0 为系统消息
            'title' => '标题',
            'content' => '内容',
            'status' => '是否已读', // 0 未读、1 已读
            'time' => '发信时间',
        ];
    }

    /**
     * 获取系统通知
     *
     * @param int $user_id 用户 ID
     * @param int $type 获取的状态类型，0：全部；1：未读；2：已读
     * @param int $page_size 每页通知条数
     * @param int $page 要获取第几页的数据
     * @return ReturnModel 通知消息与分页信息组成的数组
     * @throws Exception 获取通知状态的参数错误时，抛出异常
     */
    public static function getSysMsg(int $user_id, int $type, int $page_size, int $page)
    {
        switch ($type) {
            case self::TYPE_GET_ALL:
                // 所有（包括已读和未读）
                $status = [self::NOT_READ, self::ALREADY_READ];
                break;
            case self::TYPE_GET_UNREAD:
                // 未读
                $status = [self::NOT_READ];
                break;
            case self::TYPE_GET_READ:
                // 已读
                $status = [self::ALREADY_READ];
                break;
            default:
                throw new \Exception('获取通知状态的参数错误');
        }
        // 查询出来的全局通知状态标识
        $STATUS_PUBLIC = -1;
        $status[] = $STATUS_PUBLIC;
        // 通知类型
        $TYPE_PUBLIC = self::TYPE_PUBLIC;
        $TYPE_PRIVATE = self::TYPE_PRIVATE;
        $query1 = (new Query())
            ->select("id, {$TYPE_PRIVATE} AS `type`, title, content, `time`, `status` AS isread")
            ->from(MMessageAssign::tableName())
            ->where(['status' => $status, 'recuid' => $user_id]);
        $os_arr = [Equipment::All, Yii::$app->equip->getOs()];
        // 存在从老鸿蒙升级到纯血鸿蒙用户，需要添加 Android 的消息，避免读取不到以前的消息
        if (Yii::$app->equip->isHarmonyOS()) {
            $os_arr[] = Equipment::Android;
        }
        $query2 = (new Query())
            ->select("id, {$TYPE_PUBLIC} AS `type`, title, content, `time`")
            ->addSelect(new Expression("{$STATUS_PUBLIC} AS isread"))
            ->from(MMessageText::tableName())
            ->where(['os' => $os_arr])
            ->andWhere('`time` > :register_time', [':register_time' => Yii::$app->user->registerAt]);

        $query = new Query();
        $query->from(['t' => $query1->union($query2)]);
        // 获得总的数据条数
        $total_count = (int)$query->count();
        // 获得当页起始位置
        $offset = ($page - 1) * $page_size;
        if ($offset >= $total_count) {
            return ReturnModel::empty($page, $page_size);
        }
        $messages = $query->orderBy('`time` DESC')->offset($offset)->limit($page_size)->all();
        // 获取全局通知的 ID
        $global_ids = [];
        foreach ($messages as &$msg) {
            $msg['id'] = (int)$msg['id'];
            $msg['type'] = (int)$msg['type'];
            $msg['time'] = (int)$msg['time'];
            $msg['isread'] = (int)$msg['isread'];
            $msg['content'] = MUtils::formatContentUrl($msg['content']);
            if ($msg['type'] === $TYPE_PUBLIC) {
                $global_ids[] = $msg['id'];
            }
        }
        if ($global_ids) {
            // 若存在全局通知，为其附上正确的状态值
            $msg_users = MMessageUserMap::findAll(['recuid' => $user_id, 'messageid' => $global_ids]);
            if ($msg_users) {
                $msg_ids = array_column($msg_users, 'messageid');
                foreach ($messages as &$msg) {
                    if ($msg['type'] === $TYPE_PUBLIC) {
                        $msg['isread'] = self::NOT_READ;
                        if (in_array($msg['id'], $msg_ids)) {
                            // 若为全局通知并且在中间表中有记录时，设置状态为已读
                            $msg['isread'] = self::ALREADY_READ;
                        }
                    }
                }
            } else {
                foreach ($messages as &$msg) {
                    if ($msg['type'] === $TYPE_PUBLIC) {
                        // 若为全局通知并且在中间表中有记录时，设置状态为已读
                        $msg['isread'] = self::NOT_READ;
                    }
                }
            }
        }
        return ReturnModel::getPaginationData($messages, $total_count, $page, $page_size);
    }

    /**
     * 修改通知的状态
     *
     * @param int $user_id 用户 ID
     */
    public static function updateMsgStatus(int $user_id)
    {
        $now = $_SERVER['REQUEST_TIME'] ?? time();
        // 更新未读公共系统通知为已读
        $public_msg_ids = MMessageText::getUnreadMsgIds($user_id);
        $rows = [];
        foreach ($public_msg_ids as $msg_id) {
            $rows[] = [
                'recuid' => $user_id,
                'messageid' => $msg_id,
                'statue' => self::ALREADY_READ,
                'time' => $now,
            ];
        }
        if ($rows) {
            $command = Yii::$app->db->createCommand();
            $command->batchInsert(MMessageUserMap::tableName(), array_keys(current($rows)), $rows)
                ->execute();
        }
        // 更新未读个人系统通知为已读
        MMessageAssign::updateAll(
            ['status' => self::ALREADY_READ],
            'recuid = :user_id AND status = :status',
            [':user_id' => $user_id, ':status' => self::NOT_READ]
        );
    }

    /**
     * 发送系统消息
     *
     * @param $user_id
     * @param $title
     * @param $content
     * @param int $send_id
     * @return bool
     */
    public static function sendSysMsg($user_id, $title, $content, $send_id = 0): bool
    {
        $model = new self();
        $model->setAttributes([
            'recuid' => $user_id,
            'title' => $title,
            'content' => $content,
            'status' => self::NOT_READ,
            'send_uid' => $send_id,
            'time' => $_SERVER['REQUEST_TIME'],
        ]);
        if (!$model->save()) {
            Yii::error(sprintf('系统消息发送失败: %s', MUtils::getFirstError($model)));
            return false;
        }

        return true;
    }

    /**
     * 获取用户未读系统消息数量
     *
     * @param int $user_id
     * @return int
     */
    public static function getUserSysNoticeCount(int $user_id): int
    {
        if (!$user_id) {
            return 0;
        }
        // 系统通知未读提醒
        $sys_num = MMessageAssign::find()->where('recuid = :user_id AND status = :status AND time > 0')
            ->params([':user_id' => $user_id, ':status' => self::NOT_READ])->count();
        // 全局系统通知数量
        $public_msg_num = MMessageText::getTotalMsgCount();
        if ($public_msg_num) {
            // 若存在全局系统通知，则查询其中未读的数量
            // TODO: 之后条件中的“statue”字段应更正为“status”或者去掉（暂未发现其意义）
            $public_read_num = MMessageUserMap::find()
                ->where('recuid = :recuid AND statue = :status', [
                    ':recuid' => $user_id, ':status' => MMessageUserMap::STATUS_UNREAD
                ])->count();
            if ($public_msg_num > $public_read_num) {
                $sys_num += $public_msg_num - $public_read_num;
            }
        }
        return $sys_num;
    }
}
