<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "av_attention_tag".
 *
 * @property integer $id
 * @property integer $user_id
 * @property integer $tag_id
 * @property integer $time
 */
class AvAttentionTag extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'av_attention_tag';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'tag_id'], 'required'],
            [['user_id', 'tag_id', 'time'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'User ID',
            'tag_id' => 'Tag ID',
            'time' => 'Time',
        ];
    }
}
