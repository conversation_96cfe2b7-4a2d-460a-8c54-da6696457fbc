<?php

namespace app\models;

use app\components\util\Equipment;
use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "install_log".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 * @property string $equip_id 设备号
 * @property int $device_type 设备类型
 * @property int $is_root 是否越狱，0：未越狱；1：越狱
 * @property string $adid 广告 ID
 * @property string $idfv iOS IDFV
 * @property string $imei Android 设备 IMEI
 * @property string $imei_md5 Android IMEI (MD5 格式)
 * @property string $android_id Android 设备 ID
 * @property string $android_id_md5 Android ID (MD5 格式)
 * @property string $mac Android 设备 MAC 地址
 * @property string $mac_md5 Android MAC 地址 (MD5 格式)
 * @property string $user_agent 用户代理
 * @property string $ip 客户端 IP
 * @property string $buvid 唯一设备标识
 * @property string $ct 渠道标识
 * @property int $track_type 跟踪类型
 * @property string $track_id 跟踪 ID
 * @property int $converted 是否转化
 * @property string $oaid Android 匿名设备标识符
 * @property string $version App 版本
 * @property string $oaid_md5 Android 匿名设备标识符 (MD5 格式)
 * @property array $more 更多详情
 *
 * @property DeviceCAIDInfo $caid_info
 */
class InstallLog extends ActiveRecord
{
    // 安装类型，1：新装；2：重装
    const TYPE_NEW = 1;
    const TYPE_REPORT = 2;

    // 未越狱
    const IS_NOT_ROOT = 0;
    // 越狱
    const IS_ROOT = 1;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        $ym = date('Ym', $_SERVER['REQUEST_TIME']);
        return 'install_log_' . $ym;
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->growthdb;
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['device_type'], 'required'],
            [['create_time', 'modified_time', 'is_root'], 'integer'],
            [['device_type', 'track_type', 'converted'], 'integer', 'max' => 9],
            [['equip_id', 'adid', 'idfv'], 'string', 'max' => 36],
            [['imei'], 'string', 'max' => 20],
            [['android_id'], 'string', 'max' => 50],
            [['imei_md5', 'android_id_md5', 'mac_md5', 'oaid_md5'], 'string', 'max' => 32],
            [['mac'], 'string', 'max' => 17],
            [['user_agent', 'oaid'], 'string', 'max' => 255],
            [['ip'], 'string', 'max' => MUtils2::IP_MAX_LENGTH],
            [['ct'], 'string', 'max' => 50],
            [['buvid'], 'string', 'max' => 64],
            [['track_id'], 'string', 'max' => 300],
            [['version'], 'string', 'max' => 20],
            [['more'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'equip_id' => '设备号',
            'device_type' => '设备类型', // 1: Android, 2: iOS, 6: HarmonyOS
            'is_root' => '是否越狱',
            'adid' => '广告 ID',
            'idfv' => 'iOS IDFV',
            'imei' => 'Android 设备 IMEI',
            'imei_md5' => 'Android IMEI (MD5 格式)',
            'android_id' => 'Android 设备 ID',
            'android_id_md5' => 'Android ID (MD5 格式)',
            'mac' => 'Android 设备 MAC 地址', // 大写保留冒号分隔符
            'mac_md5' => 'Android MAC 地址 (MD5 格式)', // 以大写保留冒号分隔符的格式进行 MD5 加密
            'user_agent' => '用户代理',
            'ip' => '客户端 IP',
            'buvid' => '唯一设备标识',  // 用于广告归因等，Buvid 设计：https://info.bilibili.co/pages/viewpage.action?pageId=23120800
            'ct' => '渠道标识',
            'track_type' => '跟踪类型',
            'track_id' => '跟踪 ID',
            'converted' => '是否转化',
            'oaid' => 'Android 匿名设备标识符',
            'version' => '客户端版本号',
            'oaid_md5' => 'Android 匿名设备标识符 (MD5 格式)',
            'more' => '更多详情',  // json 格式
        ];
    }

    /**
     * 入库前自动处理
     */
    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    public function isIOSApp()
    {
        return $this->device_type === Equipment::iOS;
    }

    /**
     * 加入次日留存的列表中
     *
     * @param string $unique_tracked_id
     */
    private function addToOneDayRetentionPool(string $unique_tracked_id)
    {
        $tomorrow = date('Y-m-d', strtotime('+1 day', $this->create_time ?: $_SERVER['REQUEST_TIME']));
        $key = Yii::$app->redis->generateKey(
            KEY_AD_TRACKED_EQUIP_BUVID_LIST_ONE_DAY_RETENTION,
            $tomorrow
        );
        $expire_at = strtotime('+2 day midnight', $_SERVER['REQUEST_TIME']) + TEN_MINUTE;
        $this->addToAdTrackCallbackPool($unique_tracked_id, $key, $expire_at);
    }

    public function addToConvertedPool($unique_tracked_id)
    {
        if (!$this->buvid) {
            return false;
        }

        $redis = Yii::$app->redis;
        // buvid 记录用于：注册、关键行为的回传
        $key = $redis->generateKey(KEY_AD_TRACKED_BUVID, $this->buvid);
        $ttl = strtotime('+8 day midnight', $_SERVER['REQUEST_TIME']) + ONE_MINUTE - $_SERVER['REQUEST_TIME'];
        $redis->set($key, $unique_tracked_id, ['nx', 'ex' => $ttl]);

        // 次日留存
        $this->addToOneDayRetentionPool($unique_tracked_id);
    }

    private function addToAdTrackCallbackPool(string $unique_tracked_id, string $redis_key, int $expire_at)
    {
        if (!$this->buvid) {
            return;
        }
        Yii::$app->redis
            ->multi()
            ->hSet($redis_key, $this->buvid, $unique_tracked_id)
            ->expireAt($redis_key, $expire_at)
            ->exec();
    }

    /**
     * 是否在次日留存的列表中
     *
     * @param string|null $buvid
     * @return array
     */
    public static function isInOneDayRetentionPool(?string $buvid)
    {
        if (!$buvid) {
            return [false, ''];
        }

        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_AD_TRACKED_EQUIP_BUVID_LIST_ONE_DAY_RETENTION, date('Y-m-d', $_SERVER['REQUEST_TIME']));

        $unique_tracked_id = $redis->hGet($key, $buvid);
        if ($unique_tracked_id) {
            $redis->hDel($key, $buvid);
            return [true, $unique_tracked_id];
        }

        return [false, ''];
    }

    /**
     * @param Equipment $equip
     * @return InstallLogAndroid|InstallLogIOS
     * @throws Exception
     */
    public static function newInstance(Equipment $equip)
    {
        switch ($equip->getOs()) {
            case Equipment::Android:
                return new InstallLogAndroid();
            case Equipment::iOS:
                return new InstallLogIOS();
            case Equipment::HarmonyOS:
                return new InstallLogHarmonyOS();
            default:
                throw new Exception('不支持的系统类型');
        }
    }

}
