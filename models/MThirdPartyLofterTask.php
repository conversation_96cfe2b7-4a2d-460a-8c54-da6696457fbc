<?php

namespace app\models;

use Exception;
use Yii;

class MThirdPartyLofterTask
{
    const LOFTER_CALLBACK_URL = 'https://aso.lofter.com/exchange/callback';
    const LOFTER_APP_KEY = 'maoer';
    const LOFTER_SIGN_KEY = 'ME_8a8dd288-0826-4d12-ada8-aa0b8b3f90c2';
    const LOFTER_REQUEST_SUCCESS_CODE = 0;
    const LOFTER_TASK_TYPE = 'read_10s';

    public static function callback(string $track_id): bool
    {
        $params = [
            'token' => $track_id,
            'platform' => self::LOFTER_APP_KEY,
            'timestamp' => intval(microtime(true) * 1000),
        ];
        $params['sign'] = self::buildSign($params);
        $params['taskType'] = self::LOFTER_TASK_TYPE;

        try {
            $data = Yii::$app->tools->requestRemote(self::LOFTER_CALLBACK_URL, $params, 'POST', null, 0, [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ]);
            if (!$data) {
                throw new Exception('LOFTER 点击回传失败，返回值为空');
            }
            if ($data['code'] !== self::LOFTER_REQUEST_SUCCESS_CODE) {
                throw new Exception(sprintf('code[%d],msg[%s]', $data['code'], $data['msg']));
            }
        } catch (Exception $e) {
            Yii::error(sprintf('LOFTER 点击回传失败，参数 = %s，原因 = %s', json_encode($params), $e->getMessage()), __METHOD__);
            return false;
        }
        return true;
    }

    private static function buildSign(array $params): string
    {
        ksort($params);
        $str_to_sign = '';
        foreach ($params as $k => $v) {
            $str_to_sign .= "{$k}={$v}&";
        }
        $str_to_sign .= 'key=' . self::LOFTER_SIGN_KEY;
        return md5($str_to_sign);
    }
}
