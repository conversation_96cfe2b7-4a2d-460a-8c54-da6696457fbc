<?php

namespace app\models;

use app\components\util\Equipment;
use app\components\util\MUtils;
use Exception;
use missevan\storage\StorageClient;
use missevan\storage\UposClient;
use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;

/**
 * This is the model class for table "m_sound_node".
 *
 * @property int $id 主键
 * @property int $sound_id 音频 ID
 * @property int $node_type 节点类型，1 为根节点；2 为子节点；3 为子叶节点
 * @property string $next_ids 子节点 ID，值为包含多个子节点 ID 的 json 字符串
 * @property int $attr 节点属性。比特位第一位为 1 表示默认选项
 * @property int $stay_duration 选择页面停留时长，单位毫秒，-1 为未做选择前一直停留
 * @property string $title 节点标题
 * @property string $question 问题
 * @property string $option 选项描述
 * @property string $soundurl_user 原音频地址（用户上传）
 * @property string $soundurl 原音质音频地址
 * @property string $soundurl_128 128K 比特率音质音频地址
 * @property string $soundurl_192 192K 比特率音质音频地址
 * @property string $cover 封面地址
 * @property int $duration 音频时长，单位毫秒
 * @property int $pay_type 付费类型，0：免费；1：单集付费；2：整剧付费
 * @property int $score_threshold 可选择时需要达到的分数阈值
 * @property int $button_color 按钮颜色
 * @property string $button_image 按钮背景图
 * @property int $checked 状态，-3：转码失败；-1：待转码；0：待审核；1：审核通过；2：报警；3：下架
 * @property int $create_time 创建时间
 * @property int modified_time 修改时间
 *
 * @property string $soundurl_64 原音质音频地址
 * @property int $need_pay 付费状态
 * @property string $front_cover 封面完整地址
 */
class MSoundNode extends ActiveRecord
{
    // 节点类型
    const TYPE_ROOT = 1;  // 根节点
    const TYPE_SON = 2;  // 子节点
    const TYPE_LEAF = 3;  // 子叶节点

    // 音频过审状态（转码失败，待转码，审核中，已过审，报警，下架，合约期满）
    const CHECKED_TRANSCODE_FAILED = -3;
    const CHECKED_SOUND_TRANSCODE = -1;
    const CHECKED_UNPASS = 0;
    const CHECKED_PASS = 1;
    const CHECKED_POLICE = 2;
    const CHECKED_DISCONTINUED = 3;

    // 付费状态
    const NODE_FREE = 0;
    const NODE_UNPAID = 1;
    const NODE_PAID = 2;

    // 付费类型
    const PAY_TYPE_FREE = 0;  // 免费节点
    const PAY_TYPE_NODE = 1;  // 单节点付费，预留值，暂未使用
    const PAY_TYPE_DRAMA = 2;  // 整剧付费

    // 节点选项（按钮）加锁状态
    const LOCK_NO = 0;  // 未加锁
    const LOCK_UNPAID = 1;  // 未支付加锁
    const LOCK_SCORE = 2;  // 分数未达标加锁，预留值，暂未使用

    // 是否未默认选项
    const ATTR_DEFAULT_OPTION = 1 << 0;

    // 默认文本颜色
    const DEFAULT_TEXT_COLOR = 16777215;

    // TODO: 原音质音频地址，与音频播放接口逻辑保持一致返回该属性，之后需要去掉
    public $soundurl_64;

    // 封面完整 url 地址
    public $front_cover;
    // 支付状态
    public $need_pay;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_sound_node';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['sound_id', 'node_type', 'stay_duration', 'title', 'soundurl_user'], 'required'],
            [
                ['sound_id', 'node_type', 'attr', 'stay_duration', 'pay_type', 'score_threshold', 'button_color'],
                'integer'
            ],
            [['title'], 'string', 'max' => 30],
            [['option'], 'string', 'max' => 30],
            [['question'], 'string', 'max' => 60],
            [['soundurl', 'soundurl_128', 'button_image'], 'string', 'max' => 125],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'sound_id' => '音频 ID',
            'node_type' => '节点类型',  // 1 为根节点；2 为子节点；3 为子叶节点
            'next_ids' => '子节点 ID',  // 值为包含多个子节点 ID 的 json 字符串，如 [1, 2]
            'attr' => '节点属性',  // 比特位第一位为 1 表示默认选项
            'stay_duration' => '选择页面停留时长',  // 单位毫秒，-1 为未做选择前一直停留
            'title' => '标题',
            'question' => '问题',
            'option' => '选项描述',
            'soundurl_user' => '原音频地址', // 用户上传的音频地址
            'soundurl' => '原音质音频地址',
            'soundurl_128' => '128K 比特率音质音频地址',
            'soundurl_192' => '192K 比特率音质音频地址',
            'cover' => '节点封面',
            'duration' => '音频时长，单位毫秒',
            'pay_type' => '付费类型',  // 0：免费；1：单集付费；2：整剧付费
            'score_threshold' => '可选择时需要达到的分数阈值',
            'button_color' => '按钮颜色',
            'button_image' => '按钮背景图',
            'checked' => '节点音频状态',  // -3：转码失败；-1：待转码；0：待审核；1：审核通过；2：报警；3：下架
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($this->isNewRecord) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    public function afterFind()
    {
        if ($this->cover) {
            $this->front_cover = StorageClient::getFileUrl($this->cover);
        }
        if (!is_null($this->next_ids)) {
            // TODO: MySQL 数据库版本升级为支持 json 字段后，此步骤可省略
            // TODO: 当前 next_ids 存在两种格式，如 [1,2,3] 或 [{id: 1, option: "选项 1"},{id: 2, option: "选项 2"}]
            // 之后需要都调整为后者的格式
            $this->next_ids = $this->next_ids ? Json::decode($this->next_ids) : [];
        }
    }

    /**
     * 获取节点
     *
     * @param int $sound_id
     * @param int $node_id
     * @return self|null
     */
    public static function getNode(int $sound_id, int $node_id): ?self
    {
        return self::find()->where('id = :id AND sound_id = :sound_id AND checked = :checked', [
            ':id' => $node_id,
            ':sound_id' => $sound_id,
            ':checked' => self::CHECKED_PASS,
        ])->one();
    }

    /**
     * 获取节点选项信息
     *
     * @param self $node
     * @return array 子节点选项信息组成的数组
     */
    public static function getChoices(self $node): array
    {
        if (empty($node->next_ids)) {
            return [];
        }
        $son_node_ids = $node->next_ids;
        if ($has_option = is_array($son_node_ids[0])) {
            // WORKAROUND: 当前 next_ids 存在两种格式，
            // 如 [1,2,3] 或 [{"id": 1, option: "选项 1"}, {"id": 2, option: "选项 2"}]
            // 获取子节点 ID 时需要进行兼容处理，统一数据格式后可去掉此兼容
            // 若为包含 option 信息的数据，则取出 ID
            $son_node_ids = array_column($son_node_ids, 'id');
        }
        $unique_son_node_ids = array_unique($son_node_ids);
        // 查询未被下线的子节点
        // TODO: 待 next_ids 字段老格式数据统一后，可直接使用 all() 查询，代码中已经做了排序
        $son_nodes = self::find()->where(['checked' => self::CHECKED_PASS])
            ->allByColumnValues('id', $unique_son_node_ids);
        if (count($son_nodes) !== count($unique_son_node_ids)) {
            // 若有节点不存在或下线，记录一条错误日志用于及时发现和修复数据问题
            Yii::error("互动剧节点（{$node->id}）下存在下线或不存在的子节点", __METHOD__);
            // PASS
        }
        if ($has_option) {
            $son_id_nodes = array_column($son_nodes, null, 'id');
            // 若包含 option 信息，给 option 赋值，可能出现多个选项指向同一个节点，允许返回子节点 ID 重复的选项信息
            $son_node_info = MUtils::groupArray($node->next_ids, 'id');
            $son_nodes = $repeat_node_num = [];
            foreach ($son_node_ids as $son_node_id) {
                if (!key_exists($son_node_id, $son_id_nodes)) {
                    continue;
                }
                $son_node = [
                    'id' => $son_id_nodes[$son_node_id]->id,
                    'option' => $son_id_nodes[$son_node_id]->option,
                    'attr' => $son_id_nodes[$son_node_id]->attr,
                    'pay_type' => $son_id_nodes[$son_node_id]->pay_type,
                ];
                // 若存在重复节点，则重复节点数量 + 1，以数量做索引，保证取相同节点选项的时候可以获取到正确的选项名称
                $repeat_node_num[$son_node_id] = key_exists($son_node_id, $repeat_node_num)
                    ? $repeat_node_num[$son_node_id] + 1 : 0;
                // 父节点中包含子节点选项名称时使用该名称，否则使用子节点默认选项名称
                $son_node['option'] = $son_node_info[$son_node_id][$repeat_node_num[$son_node_id]]['option'];
                $son_nodes[] = $son_node;
            }
        } else {
            $son_nodes = array_map(function ($son_node) {
                return [
                    'id' => $son_node->id,
                    'option' => $son_node->option,
                    'attr' => $son_node->attr,
                    'pay_type' => $son_node->pay_type,
                ];
            }, $son_nodes);
        }
        return $son_nodes;
    }

    /**
     * 获取节点所属剧集付费状态
     * 仅支持整剧付费情况
     *
     * @param int $sound_id 节点所属音频 ID
     * @param int $user_id 用户 ID
     * @return array 剧集价格及付费状态
     * @throws \Exception 节点所属剧集付费类型错误时抛出异常
     */
    public static function dramaPayInfo(int $sound_id, int $user_id): array
    {
        // 获取节点所属音频剧集收费情况
        $drama_pay_info = Drama::rpc('api/get-drama-price-by-sound',
            ['sound_id' => $sound_id, 'user_id' => $user_id]);
        $drama_id = $drama_pay_info['drama_id'];
        $pay_info = self::NODE_FREE;
        switch ($drama_pay_info['pay_type']) {
            case Drama::PAY_TYPE_FREE:
                break;
            case Drama::PAY_TYPE_DRAMA:
                if ($user_id && TransactionLog::find()->where([
                    'from_id' => $user_id,
                    'gift_id' => $drama_id,
                    'type' => TransactionLog::TYPE_DRAMA,
                    'status' => TransactionLog::STATUS_SUCCESS
                ])->exists()) {
                    $pay_info = self::NODE_PAID;
                } else {
                    // 在未登录或者用户未购买剧集时，need_pay 为用户未购买
                    $pay_info = self::NODE_UNPAID;
                }
                break;
            default:
                // 当前互动广播剧付费类型仅支持免费或整剧付费
                Yii::error("互动广播剧付费类型设置错误：{$drama_id}", __METHOD__);
                throw new \Exception('互动广播剧内容错误，请联系管理员');
        }
        return [$drama_pay_info['price'], $pay_info];
    }

    /**
     * 获取互动广播剧皮肤
     *
     * @param int $sound_id 节点所属音频 ID
     * @return array 皮肤信息
     */
    public static function getSkin(int $sound_id): array
    {
        // TODO: 当前返回默认皮肤，后续根据需求完善该方法
        return [
            'title_text_color' => self::DEFAULT_TEXT_COLOR,
            'button_text_color' => self::DEFAULT_TEXT_COLOR,
            'button_image' => '',
        ];
    }

    /**
     * 获取互动广播剧节点插图
     *
     * @param int $node_id 节点 ID
     * @return array 剧集付费状态
     */
    public static function getPics(int $node_id): array
    {
        $images = MNodeImageMap::find()
            ->alias('t')
            ->select('t.stime, t1.save_name AS img_url, t1.width AS img_width,t1.height AS img_height,'
                . 't1.file_size AS size')
            ->leftJoin(MImage::tableName() . ' AS t1', 't.image_id = t1.id')
            ->where('t.node_id = :id', [':id' => $node_id])->all();
        if (empty($images)) {
            return [];
        }
        $pics = [];
        $equip = Yii::$app->equip;
        foreach ($images as $image) {
            // WORKAROUND: Android 版本存在大尺寸插图导致闪退的问题，需要将图片进行裁剪
            // 裁剪成最长边 2000，等比缩放，在安卓客户端显示效果较好且不闪退
            $image['img_url'] = Yii::$app->params['mimagesbigUrl'] . $image['img_url'];
            if (($image['img_width'] > 2000 || $image['img_height'] > 2000)
                    && $equip->isAndroidOrHarmonyOS()) {
                $image['img_url'] .= '?x-oss-process=image/resize,l_2000';
            }
            $pics[($image->stime * 1000)] = [
                'stime' => (string)($image->stime / 1000),  // 客户端与音频插图兼容同一套方案，此处返回秒级单位
                'img_url' => $image->img_url,
                'img_width' => (int)$image->img_width,
                'img_height' => (int)$image->img_height,
                'size' => (int)$image->size,
            ];
        }
        ksort($pics);
        $pics = array_values($pics);
        return $pics;
    }

    /**
     * 获取互动剧根节点
     *
     * @param int $sound_id 互动剧绑定的音频 ID
     * @return int 根节点 ID，若音频不为互动剧，返回 0
     */
    public static function getRootNodeId(int $sound_id): int
    {
        return (int)MSoundNode::find()->select('id')->where([
            'sound_id' => $sound_id,
            'node_type' => MSoundNode::TYPE_ROOT,
        ])->scalar();
    }

    /**
     * 是否为互动剧可用版本
     *
     * @return bool
     */
    public static function isUsableVersion(): bool
    {
        return !Equipment::isAppOlderThan('4.5.1', '5.4.1');
    }

    /**
     * 获取互动剧音频 playurl 签名地址
     *
     * @param array $sound 音频信息
     */
    public static function getSoundSignUrls(&$sound)
    {
        $handled = false;
        $url_keys = [MSound::KEY_SOUNDURL, MSound::KEY_SOUNDURL_128];
        try {
            if (MSound::isBVCSoundCDN(true)) {
                // 如果是视频云的音频 CDN 允许的灰度地区则下发 upos 签名地址
                $upos_uris = [];
                $keys = [];
                foreach ($url_keys as $key) {
                    if (!isset($sound[$key]) || !$sound[$key]) {
                        continue;
                    }
                    $sound[$key] = MSound::getSoundPathByEquipment($sound[$key]);
                    if (!$sound[$key]) {
                        continue;
                    }
                    $upos_uris[] = MSound::getUposURI($sound[$key]);
                    $keys[] = $key;
                }
                $sound_sign_urls = MSound::getSignUrls($upos_uris, 1);
                if (!empty($sound_sign_urls)) {
                    foreach ($url_keys as $key) {
                        $sound[$key . '_list'] = null;
                    }
                    foreach ($keys as $i => $key) {
                        $sound[$key] = $sound_sign_urls[$i][0];
                        $sound[$key . '_list'] = $sound_sign_urls[$i];
                    }
                    $handled = true;
                }
            }
        } catch (Exception $e) {
            // 若请求出错，记录错误日志
            Yii::error('获取互动剧音频 playurl 签名地址错误：' . $e->getMessage(), __METHOD__);
            // PASS
        }
        if (!$handled) {
            MSound::getSoundUrl($sound, false);
            foreach ($url_keys as $key) {
                $sound[$key . '_list'] = $sound[$key] ? [$sound[$key]] : null;
            }
        }
    }
}
