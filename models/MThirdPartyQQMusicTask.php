<?php

namespace app\models;

use Exception;
use missevan\util\MUtils;
use Yii;
use yii\helpers\Json;

class MThirdPartyQQMusicTask
{
    const CALLBACK_URL = 'https://tj.y.qq.com/channel/task/api/report';
    const APPID = '1750925597';
    const SIGN_KEY = 'd1eg2ll6a5r18hfb4g00';
    const TASK_TID = 1;
    const TASK_TYPE = 1;
    const TASK_SUCCESS_CODE = 0;

    public static function callback(string $track_id): bool
    {
        $params = [
            'qmid' => $track_id,
            'tid' => self::TASK_TID,
            'type' => self::TASK_TYPE,
        ];

        $headers = [
            'Qy-Co-Api-Appid' => self::APPID,
            'Qy-Co-Api-Nonce-Str' => MUtils::randomKeys(32, 7),
            'Qy-Co-Api-Timestamp' => intval(microtime(true)),
        ];
        $sign = self::buildSign($headers, self::SIGN_KEY);
        $headers['Qy-Co-Api-Sign'] = $sign;
        $headers['Content-Type'] = 'application/json';

        try {
            $data = Yii::$app->tools->requestRemote(self::CALLBACK_URL, [], 'POST', Json::encode($params), 0, $headers);
            if (!$data) {
                throw new Exception('返回值为空');
            }
            if ($data['code'] !== self::TASK_SUCCESS_CODE) {
                // token 验证失败时 msg 会返回 fail
                throw new Exception(sprintf('code[%d] msg[%s]', $data['code'], $data['msg']));
            }
        } catch (Exception $e) {
            Yii::error('QQMusic 点击回传失败，原因：' . $e->getMessage(), __METHOD__);
            return false;
        }
        return true;
    }

    private static function buildSign(array $params, string $sign_key): string
    {
        ksort($params);
        $str = urldecode(http_build_query($params)) . "&key={$sign_key}";
        return strtoupper(hash_hmac('sha256', $str, $sign_key));
    }
}
