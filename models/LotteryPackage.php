<?php

namespace app\models;

use app\components\util\MUtils;
use yii\web\HttpException;
use app\components\random\Lottery;
use app\components\random\PackageInterface;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "lottery_package".
 *
 * @property int $id 主键
 * @property int $work_id 作品 id，work 表主键
 * @property int $season 季度
 * @property string $rule 抽奖规则，为 json 字符串
 *   {
 *     "special": true,
 *     "times": 10,
 *     "rules": [
 *       {
 *         "times": 9,
 *         "probability": {
 *           "package_key1": 25,
 *           "package_key2": 60,
 *           "package_key3": 10,
 *           "package_key4": 5
 *         }
 *       },
 *       {
 *         "times": 1,
 *         "probability": {
 *           "package_key3": 95,
 *           "package_key4": 5,
 *         }
 *       }
 *     ],
 *     "special_rules": {
 *       "triggle_condition": {
 *         "times": 30,
 *         "without": 4, // LEVEL_SSR
 *       },
 *       "rule": {
 *         "times": 1,
 *         "probability": {
 *           "package_key4": 100,
 *         }
 *       }
 *     }
 *   }
 * @property int $price 抽奖价格
 * @property int $given_coupon 抽奖价格
 * @property int $start_time 开始时间
 * @property int $end_time 结束时间
 * @property int $mark 位置
 * @property int $priority 优先级
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 */
class LotteryPackage extends ActiveRecord implements PackageInterface
{
    // 卡包被点击位置，1：单抽；2：十连抽，3：新用户免费卡包，4：求签
    const MARK_ONE_DRAW = 1;
    const MARK_TEN_DRAW = 2;
    const MARK_FREE_DRAW = 3;
    const MARK_OMIKUJI_DRAW = 4;

    // TODO: 求签语音连续获得此数量重复语音卡时，必定获得新卡，之后考虑放入 rule 字段存储
    const OMIKUJI_GUARANTEED_NUM = 2;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'lottery_package';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db2');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['create_time', 'modified_time', 'work_id', 'price', 'mark'], 'required'],
            [
                [
                    'create_time',
                    'modified_time',
                    'work_id',
                    'priority',
                    'price',
                    'start_time',
                    'end_time',
                    'given_coupon',
                    'mark'
                ],
                'integer'
            ],
            [['rule'], 'string', 'max' => 1024],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'work_id' => '作品 ID',
            'season' => '季度',
            'rule' => '抽卡规则',
            'price' => '价格',
            'given_coupon' => '抽卡赠送荣耀点',
            'start_time' => '开始时间',
            'end_time' => '结束时间',
            'mark' => '抽卡对应位置',
            'priority' => '优先级',
            'create_time' => '创建时间',
            'modified_time' => '更改时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 通过卡包位置、作品 ID 和季度获得卡包
     *
     * @param int $mark 卡包位置
     * @param int $work_id 作品 ID
     * @param int $season 所属季度
     * @param bool $free 是否包含免费卡包
     * @return LotteryPackage 卡包对象
     */
    public static function findByMark(int $mark, int $work_id, int $season, bool $free = true)
    {
        $time = $_SERVER['REQUEST_TIME'] ?? time();
        $query = self::find()
            ->where(['mark' => $mark, 'work_id' => $work_id, 'season' => $season])
            ->andWhere(':time BETWEEN start_time AND end_time', [':time' => $time]);
        if (!$free) {
            $query->andWhere('price <> 0');
        }
        $lottery = $query->orderBy(['priority' => SORT_DESC])->limit(1)->one();
        return $lottery;
    }

    /**
     * 通过作品及季度获取对应求签包
     *
     * @param int $work_id 作品 ID
     * @param int $season 所属季度
     * @param bool $is_online 是否上线
     * @return LotteryPackage 卡包对象
     */
    public static function getOmikujiPackage(int $work_id, int $season, bool $is_online = true)
    {
        $query = self::find()
            ->where(['mark' => self::MARK_OMIKUJI_DRAW, 'work_id' => $work_id, 'season' => $season]);
        if ($is_online) {
            $time = $_SERVER['REQUEST_TIME'];
            $query->andWhere(':time BETWEEN start_time AND end_time', [':time' => $time]);
        }
        return $query->one();
    }

    /**
     * 根据卡包规则获得卡片
     *
     * @return array 卡片对象组成的数组
     */
    public function getCards()
    {
        $cards = [];
        $lottery_rule = $this->getRules();
        if ($lottery_rule) {
            $special = $lottery_rule['special'] ?? false;
            $rules = $lottery_rule['rules'] ?? [];
            if ($special) {
                $special_rule = $lottery_rule['special_rules'];
                $all_times = $lottery_rule['times'];
                $condition = $special_rule['triggle_condition'];
                $triggle_times = $condition['times'] ?? -1;
                if ($all_times > $triggle_times && $triggle_times <= 0) {
                    $special = false;
                }
                $level = $condition['without'];
                if ($triggle_times > 0) {
                    $user_id = Yii::$app->user->id;
                    $limit = $triggle_times - $all_times;
                    $levels = array_column(GetCardLog::find()
                        ->select('level')
                        ->where(['card_package_id' => $this->id, 'user_id' => $user_id])
                        ->orderBy(['create_time' => SORT_DESC])
                        ->limit($limit)
                        ->all(), 'level');
                    // 用户在此卡包中抽卡次数
                    $draw_num = GetCardLog::find()
                        ->where(['card_package_id' => $this->id, 'user_id' => $user_id])
                        ->count();
                    if (in_array($level, $levels) || $draw_num < $limit) {
                        $special = false;
                    }
                } else {
                    $special = false;
                }
            }
            foreach ($rules as $rule) {
                $this_times = $rule['times'] ?? 0;
                if ($special && ($all_times -= $this_times) <= 0) {
                    $rule['times'] = abs($all_times);
                }

                $cards = array_merge($cards, $this->drawCardsByRule($rule));
                if ($special && in_array($level, array_column($cards, 'level'))) {
                    $special = false;
                }
            }
            if ($special) {
                $special_rule = $special_rule['rule'];
                $cards = array_merge($cards, $this->drawCardsByRule($special_rule));
            }
        }
        return $cards;
    }

    /**
     * 根据规则获取语音卡
     *
     * @param array $rule 抽卡规则
     * @return array 抽到的语音卡
     */
    private function drawCardsByRule(array $rule)
    {
        $times = $rule['times'] ?? 0;
        $probability = $rule['probability'] ?? [];
        if (!$times || !$probability) return [];
        $lottery = new Lottery(new Card(), $probability);
        return $lottery->getItems($times);
    }

    public function getRules()
    {
        return Json::decode($this->rule) ?: [];
    }

    public function getSeasonTitle()
    {
        if (($rule = $this->getRules()) && $title = $rule['title'] ?? '') {
            return $title;
        }
        return sprintf('第%s季', MUtils::numToChinese($this->season));
    }

    public function getPushRules():array
    {
        return [];
    }

    public static function getObtainingMethod()
    {
        return GetCardLog::TYPE_DRAW;
    }

    /**
     * 获取抽卡赠送的荣耀点数量
     *
     * @return int 赠送的荣耀点数量
     */
    public function getGiveCoupon():int
    {
        return $this->given_coupon;
    }

    /**
     * 获取抽卡次数
     *
     * @return int 抽卡次数
     */
    public function getDrawTimes(): int
    {
        $rules = $this->getRules();
        if ($rules) {
            return $rules['times'] ?? 0;
        }
        return 0;
    }

    /**
     * 是否已过期（下架）
     *
     * @return bool
     */
    public function isExpired(): bool
    {
        return $this->end_time <= $_SERVER['REQUEST_TIME'];
    }

    /**
     * 去除已下架的签筒（若用户已购则进行显示）
     *
     * @param self[] $packages
     * @param int|null $user_id
     * @param array|int $work_ids
     * @return mixed
     */
    public static function processExpiredPackages(array $packages, ?int $user_id, $work_ids)
    {
        // 对于已下架的签筒，用户若消费过则进行显示
        $expired_lottery_package_ids = array_reduce($packages, function ($ret, $item) {
            /**
             * @var LotteryPackage $item
             */
            if ($item->isExpired()) {
                $ret[] = $item->id;
            }
            return $ret;
        }, []);
        if (!empty($expired_lottery_package_ids)) {
            $paid_expired_lottery_package_ids = [];
            if ($user_id) {
                $paid_expired_lottery_package_ids = TransactionLog::getPaidLotteryPackages(
                    $user_id,
                    $expired_lottery_package_ids,
                    $work_ids
                );
            }
            $packages = array_reduce($packages, function ($ret, $item) use ($paid_expired_lottery_package_ids) {
                /**
                 * @var LotteryPackage $item
                 */
                if (!$item->isExpired() || in_array($item->id, $paid_expired_lottery_package_ids)) {
                    $ret[] = $item;
                }
                return $ret;
            }, []);
        }

        return $packages;
    }

}
