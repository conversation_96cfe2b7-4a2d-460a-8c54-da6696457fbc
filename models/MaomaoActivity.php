<?php
/**
 * 该 Model 是和猫猫星球活动相关的 Model，没有和数据库相关联
 * @deprecated 活动已结束，可删除该 Model
 */

namespace app\models;

use Exception;
use yii\web\HttpException;
use Yii;

class MaomaoActivity
{
    // 剧集消费满足条件送的积分数量
    const DRAMA_CONSUME_POINT = 100;
    // 剧集消费需要达到 300 钻石
    const DRAMA_CONSUME_PRICE = 300;
    // 剧集消费送积分最大可送次数
    const DRAMA_CONSUME_LIMIT = 20;

    /**
     * 更新用户抽奖积分
     *
     * @param integer $user_id 用户 ID
     * @param integer $point 新增积分
     * @throws HttpException
     * @return integer 更新后用户总积分
     */
    public static function updateEventDrawPoint(int $user_id, int $point): int
    {
        return Yii::$app->go->updateEventDrawPoint(MEvent::EVENT_ID_MAOMAO, $user_id, $point);
    }

    /**
     * 剧集消费送积分
     * 规则：活动期间，用户剧集消费每满 300 钻，增加 100 积分，当完成次数达到 20 次时，积分和完成次数不增长，只有消费增长
     *
     * @param integer $user_id 用户 ID
     * @param integer $price 剧集消费的价格（剧集购买或剧集打赏的价格）
     */
    public static function addDramaConsumePoint(int $user_id, int $price)
    {
        try {
            if (!$user_id || !$price) {
                return;
            }

            $event = MEvent::findOne(MEvent::EVENT_ID_MAOMAO);
            if (!$event) {
                return;
            }

            $time = $_SERVER['REQUEST_TIME'] + ($event->extended_fields['time_offset'] ?? 0);
            $draw_point_start_time = $event->extended_fields['draw_point_start_time'] ?? 0;
            $draw_point_end_time = $event->extended_fields['draw_point_end_time'] ?? 0;
            if ($time < $draw_point_start_time || $time >= $draw_point_end_time) {
                return;
            }

            $redis = Yii::$app->redis;
            $key = $redis->generateKey(KEY_MAOMAO_POINT_USER_ID, $user_id);
            // 判断当前完成次数
            $drama_consume_num = $redis->hGet($key, 'drama_consume_num') ?: 0;
            $drama_consume_total_balance = $redis->hGet($key, 'drama_consume_total_balance') ?: 0;
            // 当完成次数达到 20 次时，积分和完成次数不增长，只有消费增长
            if ($drama_consume_num >= self::DRAMA_CONSUME_LIMIT) {
                $redis->hIncrBy($key, 'drama_consume_total_balance', $price);
                return;
            }

            $drama_consume_total_balance += $price;
            // 用户每消费 300 钻石时，增加 100 积分
            $num = min(floor($drama_consume_total_balance / self::DRAMA_CONSUME_PRICE),
                self::DRAMA_CONSUME_LIMIT);
            // 新增完成次数 = 新的累计次数 - 上次计算次数
            $add_num = $num - $drama_consume_num;
            $multi = $redis->multi();
            if ($add_num > 0) {
                $multi->hIncrBy($key, 'drama_consume_num', $add_num);
            }
            $multi->hIncrBy($key, 'drama_consume_total_balance', $price)
                // WORKAROUND: 过期时间为活动结束时间加上 30 天
                ->expireAt($key, $event->end_time + THIRTY_DAYS)
                ->exec();
            if ($add_num > 0) {
                // 给用户添加积分
                self::updateEventDrawPoint($user_id, self::DRAMA_CONSUME_POINT * $add_num);
            }
        } catch (Exception $e) {
            Yii::error('在剧集消费送积分活动，数据处理异常，错误信息：' . $e->getMessage(),
                __METHOD__);
            // PASS: 加积分出错不能影响正常消费流程，记录错误并忽略异常
        }
    }
}
