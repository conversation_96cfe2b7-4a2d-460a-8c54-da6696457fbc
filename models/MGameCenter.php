<?php

namespace app\models;

use app\components\util\MUtils;
use Exception;
use yii\helpers\Json;
use yii\web\HttpException;
use Yii;

/**
 * This is the model class for table "m_game_center".
 *
 * @property integer $id
 * @property string $url
 * @property string $cover
 * @property string $icon
 * @property string $name
 * @property string $tag
 * @property string $intro
 * @property string $extended_fields 额外信息，e.g.
 * {
 *   "download_open_time": 1635838250,  // 开放下载时间戳，单位：秒
 *   "download_url": "oss://game/202111/02/test.apk",  // Android 安装包下载地址
 *   "package_name": "com.rastar.sndwz.maoer",  // Android 安装包名
 *   "package_version_code": 3017,  // Android 安装包版本
 *   "sys_notice":{
 *     "title": "《加特林的王座》预约成功",  // 预约成功时发送的系统通知标题
 *     "content": "恭喜您成功预约《加特林的王座》！游戏正式上线后会及时通知您"  // 预约成功时发送的系统通知内容
 *   },
 *   "vendor": "加特林互动娱乐有限公司",  // 开发商
 *   "permission": "允许重启程序\n允许检索当前运行的应用程序",  // 游玩所需权限说明，使用 \n 换行
 *   "privacy_url": "https://sy.test.com/private.html",  // 隐私协议地址
 *   "update_date": "2021-11-02",  // 更新日期
 *   "card": {  // 预约卡片信息
 *     "cover": "oss://game/202111/02/cover.jpg",  // 背景图
 *     "dark_cover": "oss://game/202111/02/dark_cover.jpg",  // 黑夜模式背景图
 *     "btn_color": "#000000",  // 预约按钮颜色
 *     "dark_btn_color": "#ffffff"  // 黑夜模式预约按钮颜色
 *   },
 *   "next_stage": {  // 新安装包信息
 *     "time": 1635838251,  // 新包开放下载时间戳，单位：秒
 *     "extended_fields": {
 *       ...
 *     }
 *   }
 * }
 * @property integer $sort
 * @property integer $create_time 创建时间，单位：秒
 * @property integer $modified_time 修改时间，单位：秒
 * @property integer $publish_time 上架时间，单位：秒
 * @property integer $shutdown_time 下架时间，单位：秒
 */
class MGameCenter extends ActiveRecord
{
    // 未预约
    const STATUS_UNSUBSCRIBED = 1;
    // 已预约
    const STATUS_SUBSCRIBED = 2;
    // 开放下载
    const STATUS_OPEN_DOWNLOAD = 3;

    // 未下架
    const SHUTDOWN_TIME_NONE = 0;

    // 预约游戏的用户
    public $subscribed_user_id;

    const TYPE_GAME_SUBSCRIBE = 1;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_game_center';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                ['url', 'cover', 'icon', 'name', 'tag', 'intro', 'extended_fields'],
                'required'
            ],
            [['sort', 'create_time', 'modified_time', 'publish_time', 'shutdown_time'], 'integer'],
            [['name'], 'string', 'max' => 50],
            [['tag'], 'string', 'max' => 100],
            [['cover', 'icon'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'url' => '跳转链接', // 详情页
            'cover' => '封面图',
            'icon' => '图标',
            'name' => '游戏名',
            'tag' => '标签名', // 用半角逗号分隔
            'intro' => '简介',
            'extended_fields' => '额外数据', // JSON format
            'sort' => '排序',
            'create_time' => '创建时间',  // 单位：秒
            'modified_time' => '修改时间',  // 单位：秒
            'publish_time' => '上架时间',  // 单位：秒
            'shutdown_time' => '下架时间',  // 单位：秒
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->extended_fields) {
            $this->extended_fields = Json::decode($this->extended_fields);
            if (isset($this->extended_fields['next_stage']['time'])
                && $_SERVER['REQUEST_TIME'] >= $this->extended_fields['next_stage']['time']) {
                // 若存在需要定时更新的资源包信息且更新时间小于等于当前时间，则使用最新的资源包信息覆盖旧信息
                // NOTICE: next_stage.extended_fields 里没有开发厂商、权限用途、预约卡片等字段值，要使用 merge，不能直接赋值
                $this->extended_fields = array_merge($this->extended_fields,
                    $this->extended_fields['next_stage']['extended_fields']);
            }
        }
    }

    /**
     * 游戏预约
     *
     * @param int $user_id 用户 ID
     * @return boolean
     */
    public function subscribe(int $user_id)
    {
        if (MGameSubscribe::find()->where(['game_id' => $this->id, 'user_id' => $user_id, 'delete_time' => 0])
                ->limit(1)->exists()) {
            throw new HttpException(403, '您已经预约过了哦~');
        }

        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(KEY_GAME_SUBSCRIBE_LOCK, $this->id, $user_id);
        if (!$redis->lock($lock, ONE_MINUTE)) {
            throw new HttpException(400, '操作过于频繁，请稍候再试');
        }
        try {
            // 预约
            $subscribe = new MGameSubscribe();
            $subscribe->setAttributes([
                'game_id' => $this->id,
                'user_id' => $user_id,
            ]);
            if (!$subscribe->save()) {
                Yii::error(sprintf('用户 %d 游戏预约失败: %s', $user_id, MUtils::getFirstError($subscribe)));
                throw new Exception('预约失败，请重试');
            }
            // 预约成功后若有通知的设置则进行通知
            if ($this->extended_fields) {
                $sys_notice = $this->extended_fields['sys_notice'] ?? [];
                if (!$sys_notice) {
                    return true;
                }
                $msg = new MMessageAssign();
                $msg->setAttributes([
                    'recuid' => $user_id,
                    'send_uid' => 0,
                    'status' => MMessageAssign::NOT_READ,
                    'title' => $sys_notice['title'],
                    'content' => $sys_notice['content'],
                    'time' => $_SERVER['REQUEST_TIME'],
                ]);
                if (!$msg->save()) {
                    Yii::error(sprintf('游戏预约向用户 %d 发送通知失败',
                        $user_id, MUtils::getFirstError($msg)));
                }
            }
            return true;
        } catch (Exception $e) {
            throw $e;
        } finally {
            $redis->unlock($lock);
        }
    }

    /**
     * 获取元素关联的游戏信息
     *
     * @param int $element_id 元素 ID
     * @param int $element_type 元素类型
     * @return self|null
     * @throws Exception
     */
    public static function getGameByElement(int $element_type, int $element_id): ?self
    {
        if ($element_id <= 0 || !in_array($element_type, [MGameElement::ELEMENT_TYPE_SOUND, MGameElement::ELEMENT_TYPE_DRAMA])) {
            throw new Exception("参数错误，element_id: $element_id, element_type: $element_type");
        }
        return self::find()
            ->alias('t1')
            ->leftJoin(MGameElement::tableName() . ' AS t2', 't1.id = t2.game_id')
            ->select('t1.id, t1.url, t1.cover, t1.icon, t1.name, t1.tag, t1.intro, t1.extended_fields')
            ->where('t2.element_type = :element_type AND t2.element_id = :element_id',
                [':element_type' => $element_type, ':element_id' => $element_id])
            ->one();
    }

    /**
     * 获取游戏预约、下载状态
     *
     * @param int $user_id 用户 ID
     * @return int 游戏状态
     */
    public function getCardStatus(int $user_id): int
    {
        $status = MGameCenter::STATUS_UNSUBSCRIBED;
        $download_open_time = $this->extended_fields['download_open_time'] ?? 0;
        if ($download_open_time && $_SERVER['REQUEST_TIME'] >= $download_open_time) {
            // 设置了开放下载时间并且当前时间超过该时间时，状态为开放下载
            $status = MGameCenter::STATUS_OPEN_DOWNLOAD;
        } elseif ($user_id) {
            // 登录用户查询其是否已预约
            $isSubscribed = MGameSubscribe::find()->where('game_id = :game_id AND user_id = :user_id AND delete_time = :delete_time',
                [':game_id' => $this->id, ':user_id' => $user_id, ':delete_time' => 0])->exists();
            if ($isSubscribed) {
                $status = MGameCenter::STATUS_SUBSCRIBED;
            }
        }
        return $status;
    }
}
