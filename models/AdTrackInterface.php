<?php

namespace app\models;

interface AdTrackInterface
{
    const CALLBACK_EVENT_ACTIVATE = 'activate';
    const CALLBACK_EVENT_ONE_DAY_RETENTION = 'one_day_retention';
    const CALLBACK_EVENT_PAY = 'pay';
    const CALLBACK_EVENT_REGISTER = 'register';
    const CALLBACK_EVENT_KEY_ACTION = 'key_action';
    const CALLBACK_EVENT_TRANSACTION = 'transaction';
    const CALLBACK_EVENT_APP_CALLUP = 'app_callup';

    public function callback(string $event_type, $arg);
}
