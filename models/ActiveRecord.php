<?php

namespace app\models;

use app\components\base\db\ActiveQuery;
use app\components\base\db\BaseActiveRecordTrait;
use app\components\base\ModelTrait;
use app\components\util\MUtils;
use missevan\util\MUtils as MUtils2;
use Exception;
use Yii;

/**
 * ActiveRecord is the base class for classes representing relational data in terms of objects.
 **/
class ActiveRecord extends \yii\db\ActiveRecord
{
    use BaseActiveRecordTrait;
    use ModelTrait;

    /**
     * 重写 \yii\db\ActiveRecord::find()
     *
     * {@inheritdoc}
     */
    public static function find()
    {
        // 使用 \app\components\db\ActiveQuery 替换 \yii\db\ActiveQuery
        return Yii::createObject(ActiveQuery::class, [get_called_class()]);
    }

    public function fields()
    {
        $fields = parent::fields();
        $new_fields = MUtils::getNotNullObjectFields($this);
        $fields = array_merge($fields, $new_fields);
        return $fields;
    }

    /**
     * 通过主键更新数据
     *
     * @param $pk int|int[] 主键整型值或整型数组
     * @param $attributes array 待更新字段键值对
     * @param $params array 绑定的参数键值对
     * @return int 受影响的行数
     * @throws Exception
     */
    public static function updateByPk($pk, $attributes, $params = [])
    {
        $primary_keys = static::primaryKey();
        if (empty($primary_keys) || !isset($primary_keys[0])) {
            throw new Exception(sprintf('Class :%s must have a primary key', static::class));
        }
        if (!(is_integer($pk) || MUtils2::isUintArr($pk))) {
            throw new Exception('参数错误');
        }
        return static::updateAll($attributes, [$primary_keys[0] => $pk], $params);
    }

    /**
     * 以忽略异常的方式保存数据，若出现异常，则记录到日志
     *
     * @param bool $run_validation 是否验证参数
     * @param string $errlog_prefix 错误日志前缀
     * @return bool 是否保存成功
     */
    public function ignoreExceptionSave(bool $run_validation = true, string $errlog_prefix = ''): bool
    {
        try {
            if (!$this->save($run_validation)) {
                throw new Exception(MUtils2::getFirstError($this));
            }
            return true;
        } catch (Exception $e) {
            Yii::error(
                sprintf(
                    '%s: %s',
                    $errlog_prefix === '' ? static::class : $errlog_prefix,
                    $e->getMessage()
                ),
                __METHOD__
            );
        }
        return false;
    }

    /**
     * 查询一条记录并加锁
     * （注：需要在事务中执行）
     *
     * @param string $condition
     * @param array $params
     * @return array|\yii\db\ActiveRecord|static|null
     */
    public static function findOneForUpdate(string $condition, array $params = [])
    {
        $query = static::findBySql(sprintf('SELECT * FROM %s WHERE %s FOR UPDATE', static::tableName(), $condition));
        if (!empty($params)) {
            $query->addParams($params);
        }
        return $query->one();
    }

    /**
     * 查询多条记录并加锁
     * （注：需要在事务中执行）
     *
     * @param string $condition
     * @param array $params
     * @return array|\yii\db\ActiveRecord|static|null
     */
    public static function findAllForUpdate(string $condition, array $params = [])
    {
        $query = static::findBySql(sprintf('SELECT * FROM %s WHERE %s FOR UPDATE', static::tableName(), $condition));
        if (!empty($params)) {
            $query->addParams($params);
        }
        return $query->all();
    }
}
