<?php

namespace app\models;

use app\components\util\Go;
use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "comment_like".
 *
 * @property integer $id ID
 * @property integer $cid 评论 ID
 * @property integer $sub 是否为子评论
 * @property integer $userid 用户 ID
 * @property integer $type 类型（1：点赞；2：点踩）
 */
class CommentLike extends ActiveRecord
{
    // 是否为子评论的点赞，0：非子评论点赞；1：子评论点赞；
    const IS_NOT_SUB = 0;
    const IS_SUB = 1;

    // 类型
    const TYPE_LIKE = 1;  // 点赞
    const TYPE_DISLIKE = 2;  // 点踩

    const IS_NOT_LIKE = 0;
    const IS_LIKE = 1;
    const IS_NOT_DISLIKE = 0;
    const IS_DISLIKE = 1;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'comment_like';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['cid', 'userid', 'type'], 'required'],
            [['cid', 'sub', 'userid', 'type'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'cid' => '评论 ID',
            'sub' => '是否为子评论',  // 0：不为子评论；1：子评论
            'userid' => '点赞人 ID',
            'type' => '类型',  // 1：点赞；2：点踩
        ];
    }

    /**
     * 评论点赞或取消点赞
     *
     * @param int $comment_id 评论 ID
     * @param int $user_id 用户 ID
     * @param int $sub 是否为子评论，0：非子评论，1：子评论
     * @param int $action 行为，0：取消点赞；1：点赞
     * @return bool 是否成功
     * @throws \Exception 点赞（或取消点赞）失败时抛出异常
     */
    public static function commentLike(int $comment_id, int $user_id, int $sub, int $action): bool
    {
        $res = Yii::$app->go->rpc('/rpc/message/like', [
            'comment_id' => $comment_id,
            'sub' => $sub,
            'user_id' => $user_id,
            'action' => $action
        ]);
        if (is_null($res)) {
            // 若请求失败，则抛出异常
            throw new \Exception('网络错误，请稍后再试');
        }
        if ($res['code'] !== Go::CODE_SUCCESS) {
            throw new HttpException($res['status'], $res['info'], $res['code']);
        }
        return $res['info']['status'];
    }

    /**
     * 评论点踩或取消点踩
     *
     * @param int $comment_id 评论 ID
     * @param int $user_id 用户 ID
     * @param int $sub 是否为子评论，0：非子评论，1：子评论
     * @param int $action 行为，0：取消点踩；1：点踩
     * @return bool 是否成功
     * @throws \Exception 点踩（或取消点踩）失败时抛出异常
     */
    public static function commentDislike(int $comment_id, int $user_id, int $sub, int $action): bool
    {
        $res = Yii::$app->go->rpc('/rpc/message/dislike', [
            'comment_id' => $comment_id,
            'sub' => $sub,
            'user_id' => $user_id,
            'action' => $action
        ]);
        if (is_null($res)) {
            // 若请求失败，则抛出异常
            throw new \Exception('网络错误，请稍后再试');
        }
        if ($res['code'] !== Go::CODE_SUCCESS) {
            throw new HttpException($res['status'], $res['info'], $res['code']);
        }
        return $res['info']['status'];
    }

    /**
     * 设置评论点赞或点踩状态
     *
     * @param SoundComment|SoundComment[]|SoundSubComment|SoundSubComment[] $models 评论数据
     * @param int|null $user_id 用户 ID
     * @param bool $sub 是否为子评论
     */
    public static function DoYouLike(&$models, ?int $user_id, bool $sub)
    {
        if (!$models || !$user_id) {
            return;
        }
        if (is_array($models)) {
            $comment_ids = array_column($models, 'id');
            $condition = ['userid' => $user_id, 'sub' => self::IS_NOT_SUB, 'cid' => $comment_ids];
            if ($sub) {
                $condition['sub'] = self::IS_SUB;
            }
            $comment_likes = self::find()->select('cid, type')->where($condition)->all();
            foreach ($models as &$model) {
                $model['liked'] = self::IS_NOT_LIKE;
                $model['disliked'] = self::IS_NOT_DISLIKE;
                foreach ($comment_likes as $comment_like) {
                    if ($model->id === $comment_like->cid) {
                        // 已被用户点赞，属性 liked 赋值为 1，disliked 赋值为 0
                        // 不使用 break 跳出循环，避免同时请求热评和普通评论时出现重复评论不能正确赋值的情况
                        if ($comment_like->type === self::TYPE_LIKE) {
                            $model['liked'] = self::IS_LIKE;
                            $model['disliked'] = self::IS_NOT_DISLIKE;
                        } else {
                            $model['disliked'] = self::IS_DISLIKE;
                            $model['liked'] = self::IS_NOT_LIKE;
                        }
                    }
                }
            }
        } else {
            $condition = ['userid' => $user_id, 'sub' => self::IS_NOT_SUB, 'cid' => $models->id];
            if ($sub) {
                $condition['sub'] = self::IS_SUB;
            }
            $comment_like = self::find()->select('cid, type')->where($condition)->one();
            if (!$comment_like) {
                return;
            }
            if ($comment_like->type === self::TYPE_LIKE) {
                $models->liked = 1;
                $models->disliked = 0;
            } else {
                $models->disliked = 1;
                $models->liked = 0;
            }
        }
    }
}
