<?php

namespace app\models;

use missevan\storage\StorageClient;
use Yii;

/**
 * This is the model class for table "goods_specification".
 *
 * @property int $id 主键
 * @property string $title 规格名称
 * @property int $price 价格（单位：分）
 * @property string $cover 封面
 * @property string $url 跳转地址
 * @property int $status 状态（-2 删除归档，-1 下架，0 保留，1 在售）
 * @property int $sort 展示顺序
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 * @property int $goods_id 商品主键
 */
class GoodsSpecification extends ActiveRecord
{
    // 商品规格状态（-2 删除归档、-1 下架、1 在售）
    public const STATUS_ARCHIVED = -2;
    public const STATUS_OFF_THE_SHELVE = -1;
    public const STATUS_SALE = 1;

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('malldb');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'goods_specification';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title', 'price', 'url', 'create_time', 'modified_time', 'goods_id'], 'required'],
            [['price'], 'number'],
            [['status', 'sort', 'create_time', 'modified_time', 'goods_id'], 'integer'],
            [['title'], 'string', 'max' => 30],
            [['cover', 'url'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'title' => '规格名称',
            'price' => '价格',
            'cover' => '封面',
            'url' => '跳转地址',
            'status' => '状态',  // -2 删除归档，-1 下架，0 保留，1 在售
            'sort' => '展示顺序',  // 数值越大排位越靠前
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'goods_id' => '商品主键',
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        $this->cover = StorageClient::getFileUrl($this->cover) ?: Yii::$app->params['defaultCoverUrl'];
        $this->price = (string)Balance::profitUnitConversion($this->price, Balance::CONVERT_FEN_TO_YUAN);
    }

}
