<?php

namespace app\models;

use app\components\util\MUtils;

/**
 * This is the model class for table "m_persona_collection".
 *
 * @property string $id
 * @property string $persona_id
 * @property integer $elem_type
 * @property string $elem_id
 * @property integer $pace
 */
class MPersonaCollection extends ActiveRecord
{
    const TYPE_ALBUM = 1;
    const TYPE_DRAMA = 2;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_persona_collection';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['persona_id', 'elem_type', 'elem_id', 'pace'], 'required'],
            [['id', 'persona_id', 'elem_type', 'elem_id', 'pace'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'persona_id' => 'Persona ID',
            'elem_type' => 'Elem Type',
            'elem_id' => 'Elem ID',
            'pace' => 'Pace'
        ];
    }

    // 获取用户画像对应的专辑
    public static function getFeed(int $persona_id)
    {
        $elements = self::find()->select('elem_type, elem_id, pace')
            ->where('persona_id = :persona_id')
            ->params([':persona_id' => $persona_id])
            ->asArray()->all();

        $elem_group = MUtils::groupArray($elements, 'elem_type');

        return $elem_group;
    }

}
