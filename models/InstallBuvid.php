<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "install_buvid".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 * @property string $buvid buvid
 */
class InstallBuvid extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'install_buvid';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->growthdb;
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['create_time', 'modified_time'], 'integer'],
            [['buvid'], 'string', 'max' => 64],
            [['buvid'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'buvid' => 'buvid',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

}
