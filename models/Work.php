<?php

namespace app\models;

use app\components\util\Equipment;
use app\components\util\MUtils;
use missevan\storage\StorageClient;
use Yii;
use yii\helpers\Json;
use yii\web\HttpException;

/**
 * This is the model class for table "work".
 *
 * @property int $id 主键
 * @property string $title 作品名
 * @property string $type 作品类型
 * @property string $rank 作品排序
 * @property string $icon 白天模式的入口图标
 * @property string $dark_icon 黑夜模式的入口图标
 * @property string $homepage_banners 首页 banner 图
 * @property string $episode_banner 小剧场 banner 图
 * @property string $episode_icon 小剧场头像
 * @property string $episode_cover 小剧场入在角色列表的入口图
 * @property string $drawpage_banner 抽卡背景图
 * @property string $welfare_episode_banner 福利剧场背景图
 * @property string $welfare_episode_cover 人物介绍页福利剧场 cover 图
 * @property string $welfare_episode_icon 福利剧场头像
 * @property string $skin 皮肤详情
 * @property string $coupon_name 经验值名称
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 */
class Work extends ActiveRecord
{

    // 全职高手 ID、标题及图标
    const ID_QUANZHI = 1;
    const TITLE_QUANZHI = '全职高手';
    const ICON_QUANZHI = 'oss://image/works/201805/22/762f97d2f6cf4e328d27c6462a8f1b35145512.png';
    const DARK_ICON_QUANZHI = 'oss://image/works/201805/22/cc45f51c0153ef4f5371a32a61d0e26b145512.png';

    // 求签皮肤包作品 ID
    const OMIKUJI_SKIN_WORK_ID = 0;

    // 迪奥先生作品 ID
    const ID_DIAOXIANSHENG = 7;

    // 剑网 3 ID 及其评论提醒显示图标
    const ID_JIANWANG3 = 5;
    const ICON_COMMENT_NOTICE_JIANWANG3 = 'oss://image/works/201911/04/59e4b265092f7aedced125389e9caa66121354.jpg';

    // IP 类别，1：语音包；2：求签
    const TYPE_VOICE = 1;
    const TYPE_OMIKUJI = 2;

    // TODO: 求签包字体下载 storage 协议地址，因基本不变动，暂时以常量方式写死
    const OMIKUJI_FONT_OSS_URL = 'voice://font/201908/21/omikuji-voice-v3.zip';

    const TYPE_HOMEPAGE_BANNERS = 0;
    const TYPE_DRAWPAGE_BANNER = 1;
    const TYPE_EPISODE_BANNER = 2;
    const TYPE_EPISODE_ICON = 3;
    const TYPE_EPISODE_COVER = 4;
    const TYPE_WELFARE_BANNER = 5;
    const TYPE_WELFARE_ICON = 6;
    const TYPE_WELFARE_COVER = 7;

    const TYPE_PIC_FILEDS = [
        self::TYPE_HOMEPAGE_BANNERS => 'homepage_banners',
        self::TYPE_DRAWPAGE_BANNER => 'drawpage_banner',
        self::TYPE_EPISODE_BANNER => 'episode_banner',
        self::TYPE_EPISODE_ICON => 'episode_icon',
        self::TYPE_EPISODE_COVER => 'episode_cover',
        self::TYPE_WELFARE_BANNER => 'welfare_episode_banner',
        self::TYPE_WELFARE_ICON => 'welfare_episode_icon',
        self::TYPE_WELFARE_COVER => 'welfare_episode_cover',
    ];

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'work';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db2');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['title', 'icon', 'dark_icon'], 'required'],
            [['create_time', 'modified_time'], 'integer'],
            [['title'], 'string', 'max' => 20],
            [['homepage_banners', 'skin', 'coupon_name'], 'string'],
            [
                ['icon', 'dark_icon', 'episode_banner', 'episode_icon', 'drawpage_banner', 'episode_cover', 'welfare_episode_banner', 'welfare_episode_cover', 'welfare_episode_icon'],
                'string',
                'max' => 120
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'title' => '作品名',
            'icon' => '白天模式的入口图标',
            'dark_icon' => '黑夜模式的入口图标',
            'homepage_banners' => '首页 banner 图',
            'episode_banner' => '小剧场 banner 图',
            'episode_icon' => '小剧场头像',
            'drawpage_banner' => '抽卡背景图',
            'welfare_episode_banner' => '福利剧场背景图',
            'welfare_episode_cover' => '人物介绍页福利剧场 cover 图',
            'welfare_episode_icon' => '福利剧场头像',
            'skin' => '皮肤详情',
            'coupon_name' => '经验值名称',
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->icon) $this->icon = StorageClient::getFileUrl($this->icon);
        if ($this->dark_icon) $this->dark_icon = StorageClient::getFileUrl($this->dark_icon);
        if ($this->homepage_banners = json_decode($this->homepage_banners, true) ?: []) {
            $this->homepage_banners = array_map(function ($item) {
                $item['pic'] = StorageClient::getFileUrl($item['pic']);
                return $item;
            }, $this->homepage_banners);
        }
        if ($this->episode_banner) $this->episode_banner = StorageClient::getFileUrl($this->episode_banner);
        if ($this->episode_icon) $this->episode_icon = StorageClient::getFileUrl($this->episode_icon);

        if ($this->drawpage_banner = json_decode($this->drawpage_banner, true) ?: []) {
            $this->drawpage_banner = array_map(function ($item) {
                return StorageClient::getFileUrl($item);
            }, $this->drawpage_banner);
        }
        if ($this->welfare_episode_banner) {
            $this->welfare_episode_banner = StorageClient::getFileUrl($this->welfare_episode_banner);
        }
        if ($this->welfare_episode_cover) {
            $this->welfare_episode_cover = StorageClient::getFileUrl($this->welfare_episode_cover);
        }
        if ($this->welfare_episode_icon) {
            $this->welfare_episode_icon = StorageClient::getFileUrl($this->welfare_episode_icon);
        }
    }

    /**
     * 获取某类型下的所有作品的信息
     *
     * @param int $type
     * @return array|ActiveRecord[]
     * @todo 目前作品（IP）数量较少，限制最多查询 1000 条避免一次查询过多，之后可以考虑分页操作
     */
    public static function getWorksByType(int $type)
    {
        return self::find()
            ->select('id, title, rank, drawpage_banner')
            ->where('type = :type AND id <> :id', [':type' => $type, ':id' => self::OMIKUJI_SKIN_WORK_ID])
            ->limit(1000)
            ->orderBy('rank ASC, create_time DESC')
            ->all();
    }

    /**
     * 获取季度名
     * 目前暂定名称为“第x季”，其中 x 为季度对应的中文数字
     *
     * @param int $season 季度
     * @return string
     */
    public static function getSeasonSubject(int $season): string
    {
        $char_season = MUtils::numToChinese($season);
        return "第{$char_season}季";
    }

    public static function getWorkPic($work_id, $type, $season = 1)
    {
        $pic = null;
        if (in_array($type, array_keys(self::TYPE_PIC_FILEDS))) {
            $pic = self::find()->select(self::TYPE_PIC_FILEDS[$type])->where(['id' => $work_id])->scalar();
            if (self::TYPE_HOMEPAGE_BANNERS === $type) {
                $pic = array_map(function ($item) {
                    $item['pic'] = StorageClient::getFileUrl($item['pic']);
                    return $item;
                }, json_decode($pic, true) ?: []);
            } elseif (self::TYPE_DRAWPAGE_BANNER === $type) {
                // 若为获取抽卡页背景图，按照相应季度取出背景图片
                $covers_arr = json_decode($pic, true);
                $pic = isset($covers_arr[$season]) ? StorageClient::getFileUrl($covers_arr[$season]) : '';
            } else {
                $pic = StorageClient::getFileUrl($pic);
            }
        }
        return $pic;
    }

    public static function checkWork($work_id, int $type = self::TYPE_VOICE)
    {
        if (!$work_id) return false;
        return self::find()->where(['id' => $work_id, 'type' => $type])->exists();
    }

    public static function getWork($work_id, int $type = self::TYPE_VOICE)
    {
        if (!$work_id) return null;
        return self::find()->where(['id' => $work_id, 'type' => $type])->one();
    }

    /**
     * 获取作品下季度
     *
     * @param int $work_id 作品 ID
     * @param bool $is_online 是否只获取上线的季度
     * @return array 季度 ID 组成的数组
     */
    public static function getSeasons(int $work_id, bool $is_online = true): array
    {
        $condition = ['work_id' => $work_id];
        if ($is_online) {
            // 若存在上线的季包，则视其所属季度已上线
            $condition['is_online'] = CardPackage::ONLINE;
        }
        // 季度按新旧顺序排行
        $season_ids = CardPackage::find()
            ->select('season')
            ->distinct()
            ->where($condition)
            ->orderBy('season DESC')
            ->column();
        if ($season_ids) {
            $season_ids = array_map('intval', $season_ids);
        }
        return $season_ids;
    }

    /**
     * 获取求签作品下的季度
     *
     * @param int $work_id 作品 ID
     * @param int|null $user_id 用户 ID
     * @return array 季度 ID 组成的数组
     */
    public static function getOmikujiSeasons(int $work_id, ?int $user_id): array
    {
        // 季度按新旧顺序排行
        $packages = LotteryPackage::find()
            ->select('id, season, end_time')
            ->where(['work_id' => $work_id])
            ->andWhere(':now >= start_time', [':now' => $_SERVER['REQUEST_TIME']])
            ->orderBy('season ASC')
            ->all();
        $packages = LotteryPackage::processExpiredPackages($packages, $user_id, $work_id);
        $season_ids = array_unique(array_column($packages, 'season'));
        sort($season_ids);

        return $season_ids;
    }

    /**
     * 获得某作品下已解锁的热度卡片数量
     *
     * @param int $work_id 作品 ID
     * @return int 作品下已解锁的热度卡片数量（包含下架语音卡）
     */
    public static function getUnlockHotCardNum(int $work_id): int
    {
        // 当 push 值大于 0 时，可看作卡片已解锁；即使下架也可以看到该卡片
        return (int)Card::find()
            ->where('special = :special AND work_id = :work_id AND push > 0', [
                ':special' => Card::SPECIAL_HOTCARD,
                ':work_id' => $work_id,
            ])
            ->count();
    }

    /**
     * 获取全职公告
     *
     * @param integer $work_id 作品 ID
     * @param integer $os 设备类型（1 为 Android、2 为 iOS）
     * @param string $version 版本号（支持通配符 "*"，例："4.2.*"、"5.*.*"）
     * @return array 公告内容（例：["open" => 0, "alert" => ""]）
     */
    public static function getAnnouncement($work_id, $os, $version)
    {
        $announcement = ['open' => 0, 'alert' => ''];
        $redis = Yii::$app->redis;
        $data = $redis->get($redis->generateKey(KEY_VOICE_ICON_GO, $work_id));
        $voice = $data ? Json::decode($data) : null;
        if (!$voice) return $announcement;

        if ($os === Equipment::Web && ($voice[$os] ?? null)) {
            return $voice[$os];
        }

        if ($voice = $voice[$os] ?? null) {
            if (array_key_exists($version, $voice)) {
                $announcement = $voice[$version];
            } else {
                // 匹配版本公告：如 4.*.*、4.2.* 的公告都属于 4.2.3 的公告，但以 4.2.* 为优先
                $match = null;
                krsort($voice);
                foreach ($voice as $pattern => $item) {
                    if (fnmatch($pattern, $version)) {
                        $match = $pattern;
                        break;
                    }
                }
                if ($match) {
                    $announcement = $voice[$match];
                }
            }
        }
        return $announcement;
    }

    /**
     * 检查某作品下是否包含小剧场
     *
     * @param integer $work_id
     * @return boolean
     */
    public static function hasEpisode(int $work_id)
    {
        return Card::find()->where([
            'special' => Card::SPECIAL_EPISODE,
            'work_id' => $work_id,
        ])->exists();
    }

    /**
     * 获取作品的经验值名称
     *
     * @param integer $work_id
     * @return string
     */
    public static function getCouponName(int $work_id)
    {
        return self::find()->select('coupon_name')->where(['id' => $work_id])->scalar();
    }

    /**
     * 获取作品列表
     *
     * @param integer $os 设备类型
     * @param string $resolution 设备分辨率（例 1080x1920）
     * @return array
     */
    public static function getAllWorks(int $os, ?string $resolution = null)
    {
        $works = self::find()->select('id, title')
            ->where(['type' => self::TYPE_VOICE])->orderBy('rank ASC')
            ->asArray()->all();
        $work_ids = array_map('intval', array_column($works, 'id'));
        $skin_packages = SkinPackage::getSkinPackages($work_ids, $os, $resolution);
        return array_map(function ($work) use ($skin_packages) {
            return [
                'id' => (int)$work['id'],
                'title' => $work['title'],
                'skin' => $skin_packages[$work['id']] ?? null,
            ];
        }, $works);
    }

    /**
     * 获取语音包图标
     *
     * @param $dark
     * @return string
     */
    public function getVoiceIcon($dark)
    {
        return $dark ? $this->dark_icon : $this->icon;
    }

    /**
     * 是否有热度福利剧场卡
     *
     * @param $work_id
     * @return bool
     */
    public static function hasHotCard($work_id)
    {
        return Card::find()->where([
            'work_id' => $work_id,
            'special' => Card::SPECIAL_HOTCARD,
            'is_online' => Card::ONLINE,
        ])->limit(1)->exists();
    }

    /**
     * 获取经验值获取方式
     *
     * @param $work_id
     * @return array 例 ['text' => '* 荣耀点不足，荣耀点可通过抽语音获得', attr => 4]
     */
    public static function getCouponInfo($work_id)
    {
        $attr_array = CardPackage::find()->select('attr')
            ->where(['work_id' => $work_id, 'is_online' => CardPackage::ONLINE])
            ->column();
        $final_attr = 0;
        $is_purchasable = $is_drawable = false;
        foreach ($attr_array as $attr) {
            $attr = (int)$attr;
            if (!(CardPackage::ATTR_NOT_PURCHASABLE & $attr)) {
                $is_purchasable = true;
            }
            if (!(CardPackage::ATTR_NOT_DRAWABLE & $attr === 0)) {
                $is_drawable = true;
            }
            $final_attr |= (int)$attr;
        }
        if ($is_purchasable) {
            $final_attr &= ~CardPackage::ATTR_NOT_PURCHASABLE;
        }
        if ($is_drawable) {
            $final_attr &= ~CardPackage::ATTR_NOT_DRAWABLE;
        }
        $coupon_name = self::find()->select('coupon_name')->where(['id' => $work_id])->scalar();
        if ($final_attr & CardPackage::ATTR_NOT_PURCHASABLE) {
            // 不能买（只能抽/兑换）
            $text_insufficient = sprintf('* %s不足，%s可通过抽语音获得', $coupon_name, $coupon_name);
        } elseif ($final_attr & CardPackage::ATTR_NOT_DRAWABLE) {
            // 不能抽（只能买/兑换）
            $text_insufficient = sprintf('* %s不足，%s可通过购买季包获得', $coupon_name, $coupon_name);
        } else {
            $text_insufficient = sprintf('* %s不足，抽语音或购买季包可得', $coupon_name);
        }

        return [
            'attr' => $final_attr,
            'text_insufficient' => $text_insufficient,
            'text_sufficient' => '* 点击兑换后可获得该语音',
        ];
    }

    /**
     * 获取运势作品的签筒
     *
     * @param int|int[] $work_ids 作品 ID
     * @param int|null $user_id 用户 ID
     * @return LotteryPackage[]
     */
    public static function getLotteryPackages($work_ids, ?int $user_id): array
    {
        // 查询作品下的上线季度（按季度先后顺序排行）
        $packages = LotteryPackage::find()
            ->select('id, end_time, work_id, season')
            ->where(['work_id' => $work_ids])
            ->andWhere(':now >= start_time', [':now' => $_SERVER['REQUEST_TIME']])
            ->orderBy('season ASC')
            ->all();
        return LotteryPackage::processExpiredPackages($packages, $user_id, $work_ids);
    }

}
