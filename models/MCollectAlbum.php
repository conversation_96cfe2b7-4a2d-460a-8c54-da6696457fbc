<?php

namespace app\models;

use app\components\models\traits\AlbumTrait;
use app\components\util\MUtils;
use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\db\Expression;
use yii\web\HttpException;

/**
 * This is the model class for table "m_collect_album".
 *
 * @property integer $id
 * @property integer $user_id
 * @property integer $album_id
 * @property integer $type
 * @property integer $time
 * @property integer $sort
 */
class MCollectAlbum extends ActiveRecord
{
    use AlbumTrait;
    // 音单 ID 字段名赋值给常量，用于 Trait 中自定义排序函数
    const ALBUM_ID_COLUMN = 'album_id';

    // 排序值偏移量 2^20 (1048576)
    const SORT_INTERVAL = 1048576;
    // 排序最大绝对阈值（绝对值）2^52
    const SORT_MAX = 4503599627370496;

    // 用户可收藏的最大音单数量
    const MAX_COLLECT_ALBUM_NUM = 1000;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_collect_album';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'album_id', 'time'], 'required'],
            [['user_id', 'album_id', 'type', 'time'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键 ID',
            'user_id' => '用户 ID',
            'album_id' => '音单 ID',
            'type' => '音单类型',  // 0：普通音单，1：点赞音单
            'time' => '收藏时间',
            'sort' => '收藏音单排序值',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->time = $time;
            // 新收藏的音单排在首位
            $first_album_sort = self::getFirstAlbumSort($this->user_id);
            $this->sort = $first_album_sort - self::SORT_INTERVAL;
        }
        return true;
    }

    /**
     * 批量收藏或批量取消收藏音单
     *
     * @param array $album_ids 音单 IDs
     * @param int $user_id 用户 ID
     * @param int $type 操作类型 0：取消收藏；1：收藏
     * @return string
     * @throws HttpException
     * @throws Exception
     */
    public static function batchCollectOrNot(array $album_ids, int $user_id, int $type = MUtils::ACTION_DO): string
    {
        if ($user_id <= 0 || empty($album_ids) || !MUtils2::isUintArr($album_ids)) {
            throw new Exception('参数错误');
        }
        // 加锁
        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_ALBUM_COLLECT, $user_id);
        if (!$redis->lock($lock, ONE_MINUTE)) {
            throw new HttpException(400, '操作过于频繁，请稍候再试');
        }
        $transaction = null;
        try {
            $album_info = MAlbum::find()
                ->select('id, user_id')
                ->where(['id' => $album_ids])
                ->indexBy('id')
                ->asArray()
                ->all();
            if (empty($album_info)) {
                throw new HttpException(404, '所选音单不存在', 200120001);
            }
            $exist_album_ids = array_map('intval', array_keys($album_info));
            $album_user_ids = array_map('intval', array_column($album_info, 'user_id'));
            if (in_array($user_id, $album_user_ids)) {
                throw new HttpException(400, '不能收藏自己的音单', 200120002);
            }
            $collected_album_ids = self::find()
                ->select('album_id')
                ->where(['user_id' => $user_id, 'album_id' => $exist_album_ids])
                ->column();
            switch ($type) {
                case MUtils::ACTION_DO:
                    // 收藏音单
                    $msg = '收藏音单成功';
                    // 过滤未收藏的音单
                    $not_collect_album_ids = array_diff($exist_album_ids, $collected_album_ids);
                    if (empty($not_collect_album_ids)) {
                        // 所选音单中，没有未收藏的音单，则直接返回收藏成功
                        return $msg;
                    }
                    // 只能收藏审核通过的音单
                    $checked_album_count = (int)MAlbum::find()
                        ->where(['checked' => MAlbum::CHECKED_PASS, 'id' => $not_collect_album_ids])->count();
                    if ($checked_album_count !== count($not_collect_album_ids)) {
                        throw new HttpException(404, '所选音单不存在', 200120001);
                    }
                    // 判断收藏音单数量上限
                    $collected_album_count = (int)MCollectAlbum::find()->where(['user_id' => $user_id])->count();
                    if ($collected_album_count + count($not_collect_album_ids) > self::MAX_COLLECT_ALBUM_NUM) {
                        throw new HttpException(403, '(′・_・`) 您收藏的音单数量已达上限', 200120002);
                    }
                    $transaction = Yii::$app->db->beginTransaction();
                    self::batchInsertCollectAlbums($not_collect_album_ids, $user_id);
                    // 音单收藏数冗余字段 + 1
                    MAlbum::updateAllCounters(['favorite_count' => 1], ['id' => $not_collect_album_ids]);
                    break;
                case MUtils::ACTION_UNDO:
                    // 取消收藏音单
                    $msg = '取消收藏音单成功';
                    if (empty($collected_album_ids)) {
                        // 所选音单中，没有已收藏的音单，则直接返回取消收藏成功
                        return $msg;
                    }
                    $transaction = Yii::$app->db->beginTransaction();
                    self::deleteAll(['user_id' => $user_id, 'album_id' => $collected_album_ids]);
                    // 音单收藏数冗余字段 - 1
                    $favorite_count_expression = new Expression('GREATEST(favorite_count, 1) - 1');
                    MAlbum::updateAll(['favorite_count' => $favorite_count_expression],
                        ['id' => $collected_album_ids]);
                    break;
                default:
                    throw new Exception('参数错误');
            }
            $transaction->commit();
            return $msg;
        } catch (Exception $e) {
            if (!$transaction) {
                throw $e;
            }
            $transaction->rollBack();
            // 记录错误日志
            Yii::error('批量收藏或取消收藏音单失败，原因：' . $e->getMessage(), __METHOD__);
            $msg = $type ? '收藏音单失败' : '取消收藏音单失败';
            throw new HttpException(500, $msg);
        } finally {
            // 解锁
            $redis->unlock($lock);
        }
    }

    /**
     * 批量写入音单收藏表
     *
     * @param array $album_ids 音单 IDs
     * @param int $user_id 用户 ID
     * @return int
     * @throws Exception
     */
    public static function batchInsertCollectAlbums(array $album_ids, int $user_id): int
    {
        if ($user_id <= 0 || empty($album_ids) || !MUtils2::isUintArr($album_ids)) {
            throw new Exception('参数错误');
        }
        $collect_func = function ($model, $album_ids, $user_id) {
            $insert_datas = [];
            $now = $_SERVER['REQUEST_TIME'];
            // 获取排在首位的收藏音单排序值
            $sort = $model::getFirstAlbumSort($user_id);
            foreach ($album_ids as $album_id) {
                $sort -= self::SORT_INTERVAL;
                $insert_datas[] = [$user_id, $album_id, MAlbum::ALBUM_COMMON, $now, $sort];
            }
            $res = self::getDb()->createCommand()
                ->batchInsert($model::tableName(), ['user_id', 'album_id', '`type`', 'time', 'sort'], $insert_datas)
                ->execute();
            $need_reorder = $sort < -self::SORT_MAX;
            if ($need_reorder) {
                self::reorderAlbums($user_id);
            }
            return $res;
        };
        return MUtils::ensureDbTransaction(self::class, $collect_func, $album_ids, $user_id);
    }
}
