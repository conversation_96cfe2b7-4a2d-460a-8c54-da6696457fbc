<?php

namespace app\models;

use Exception;
use Yii;
use yii\helpers\Json;

/**
 * Class AdTrackTengXun
 * @package app\models
 */
class AdTrackTengXun extends AdTrack implements AdTrackInterface
{

    const CALLBACK_EVENT_TYPE_MAP = [
        self::CALLBACK_EVENT_ACTIVATE => self::CALLBACK_EVENT_TYPE_ACTIVATE,
        self::CALLBACK_EVENT_ONE_DAY_RETENTION => self::CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION,
        self::CALLBACK_EVENT_PAY => self::CALLBACK_EVENT_TYPE_PAY,
        self::CALLBACK_EVENT_REGISTER => self::CALLBACK_EVENT_TYPE_REGISTER,
        // TODO: 收听 5 个音的关键行为已调整为消费 / 充值关键行为，之后可删除此类型
        self::CALLBACK_EVENT_KEY_ACTION => self::CALLBACK_EVENT_TYPE_VIEW_CONTENT,
        self::CALLBACK_EVENT_TRANSACTION => self::CALLBACK_EVENT_TYPE_VIEW_CONTENT,  // 消费 / 充值关键行为
        self::CALLBACK_EVENT_APP_CALLUP => self::CALLBACK_EVENT_TYPE_VIEW_CONTENT,  // 唤起 APP (促活行为)
    ];

    const CALLBACK_EVENT_TYPE_ACTIVATE = 'ACTIVATE_APP';  // 激活
    const CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION = 'START_APP';  // 次日留存
    const CALLBACK_EVENT_TYPE_PAY = 'PURCHASE';  // 付费
    const CALLBACK_EVENT_TYPE_REGISTER = 'REGISTER';  // 注册
    const CALLBACK_EVENT_TYPE_VIEW_CONTENT = 'VIEW_CONTENT';  // 关键页面浏览量

    /**
     * @link https://developers.e.qq.com/docs/guide/conversion/new_version/APP_api
     * @param string $event_type 事件类型
     * @param mixed $arg 付费金额（单位：元），关键行为参数
     */
    public function callback(string $event_type, $arg = 0)
    {
        try {
            if (!array_key_exists($event_type, self::CALLBACK_EVENT_TYPE_MAP)) {
                throw new Exception('腾讯广告点击回传事件错误：' . $event_type);
            }
            $event = self::CALLBACK_EVENT_TYPE_MAP[$event_type];
            if (is_null($event)) {
                return true;
            }
            $data = Yii::$app->tools->requestRemote($this->getCallbackUrl(), null, 'POST',
                $this->getCallbackBody($event, $arg), 0, ['Content-Type' => 'application/json']);
            if (!($data && $data['code'] === 0)) {
                throw new Exception(sprintf('腾讯广告点击回传失败：code[%d], msg[%s]', $data['code'], $data['message'] ?? ''));
            }
            return true;
        } catch (Exception $e) {
            Yii::error('tengxun ad error: ' . $e->getMessage(), __METHOD__);
        }
        return false;
    }

    private function getCallbackUrl()
    {
        return $this->track_id;
    }

    private function getCallbackBody(string $callback_event, $arg = 0)
    {
        $body = [
            'actions' => [
                [
                    // 选填：客户唯一行为 ID（若上报可能有重复请填写该 ID，系统会根据该 ID 进行去重）
                    'outer_action_id' => $callback_event
                        . $_SERVER['REQUEST_TIME']
                        . str_pad(rand(1, 100), 3, '0', STR_PAD_LEFT),
                    'action_time' => $_SERVER['REQUEST_TIME'],
                    // 必填：可采集到的设备标示
                    'user_id' => $this->getCallbackUserIdParam(),
                    // 必填：行为类型
                    'action_type' => $callback_event,
                ]
            ]
        ];
        if ($action_param = $this->getCallbackActionParam($callback_event, $arg)) {
            $body['actions'][0]['action_param'] = $action_param;
        }

        return Json::encode($body);
    }

    private function getCallbackUserIdParam()
    {
        $user_id_param = [];

        if ($this->isAndroid()) {
            if ($this->imei_md5) {
                $user_id_param['hash_imei'] = $this->imei_md5;
            }
            if ($this->oaid) {
                $user_id_param['oaid'] = $this->oaid;
                $user_id_param['hash_oaid'] = md5($this->oaid);
            }
            if ($this->android_id_md5) {
                if (strlen($this->android_id_md5) === 32) {
                    $user_id_param['hash_android_id'] = $this->android_id_md5;
                } else {
                    // PASS: android_id_md5 为腾讯广点通回传的值，回传时为选填，值有误的话就不回传该字段
                    Yii::error(sprintf('腾讯广告 android_id_md5 错误：android_id_md5[%s], ad_track.id[%d]', $this->android_id_md5, $this->id), __METHOD__);
                }
            }
        } elseif ($this->isIOS()) {
            $user_id_param['hash_idfa'] = $this->idfa_md5;
        }

        return $user_id_param;
    }

    /**
     * @param string $callback_event 事件类型
     * @param mixed $arg 付费金额（单位：元），或关键行为参数
     * @return array
     * @throws \yii\web\HttpException
     */
    private function getCallbackActionParam(string $callback_event, $arg = 0)
    {
        $action_param = [];
        if ($callback_event === self::CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION) {
            $action_param['length_of_stay'] = 1;
        }
        if ($callback_event === self::CALLBACK_EVENT_TYPE_PAY) {
            $action_param['value'] = $this->getCallbackPayAmount($arg);
        }
        return $action_param;
    }

}
