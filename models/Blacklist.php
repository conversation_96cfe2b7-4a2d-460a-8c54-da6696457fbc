<?php

namespace app\models;

use app\components\util\Equipment;
use Redis;
use Yii;

/**
 * 封装与黑名单相关操作的类
 *
 * @property \app\components\db\RedisConnection $redis Redis 对象
 * @static Blacklist $blacklist 此类实例化的一个对象
 *
 * <AUTHOR> <<EMAIL>>
 */
class Blacklist
{
    // 封禁标识
    const BAN_TIME_ANY = 0;  // 任意
    const BAN_TIME_TEMP = 1;  // 临时
    const BAN_TIME_FOREVER = -1;  // 永远（拒绝发短信、弹幕）
    const BAN_TIME_FOREVER_LISTEN = -2;  // 永远（拒绝发短信、弹幕与听音）
    const BAN_TIME_FOREVER_LOGIN = -3;  // 永远（禁止登录）
    const BAN_TIME_FOREVER_TOPUP_AND_CONSUME = -4;  // 永远（禁止充值及消费）

    // 隐藏送审时涉及的敏感项内容
    const TYPE_CHANNEL_AUDIT_SENSITIVE_HIDE = 1;

    private $redis;
    private static $blacklist;

    public function __construct()
    {
        $this->redis = Yii::$app->redis;
    }

    public static function model()
    {
        if (self::$blacklist === null) {
            self::$blacklist = new self();
        }
        return self::$blacklist;
    }

    /**
     * 检查 IP 是否处于黑名单中
     *
     * @param string $ip 要检查的 IP
     * @return bool IP 是否处于黑名单中
     */
    public function hasIP(string $ip): bool
    {
        $expire = $this->getIPExpire($ip);
        $now = $_SERVER['REQUEST_TIME'];
        if ($expire === false || ($expire !== self::BAN_TIME_FOREVER && $expire < $now)) {
            // 若 IP 不存在于黑名单中或非永封且封禁时间已到，则不视为在屏蔽名单中
            return false;
        }
        return true;
    }

    /**
     * 检查用户是否处于黑名单中
     * 参数 $type 为默认值时，同时检测“临时封禁”和“永久封禁”；
     * 可指定 $type，对各级永封进行单独检测。
     *
     * @param int $user_id 要检查的用户的 ID
     * @param int $type 针对性检查的封禁类型，默认值 0 为“在任意封禁状态中”
     * @return bool 用户是否处于黑名单中
     * @throws \Exception
     */
    public function hasUser(int $user_id, int $type = self::BAN_TIME_ANY): bool
    {
        $expire = $this->getUserExpire($user_id);
        if ($expire === false) {
            return false;
        }

        $forever_list = [
            self::BAN_TIME_FOREVER,
            self::BAN_TIME_FOREVER_LISTEN,
        ];

        if ($type === self::BAN_TIME_ANY) {
            $now = $_SERVER['REQUEST_TIME'];
            // “若存在过期时间 && 封禁时间已到”表示不在封禁状态
            if ($expire >= self::BAN_TIME_TEMP && $expire < $now) {
                return false;
            }
        } elseif (in_array($type, $forever_list)) {
            if ($expire !== $type) {
                return false;
            }
        } else {
            throw new \Exception('意外的检查类型');
        }
        return true;
    }

    /**
     * 封禁用户
     *
     * @param int $user_id 要封禁用户的 ID
     * @param int $time 封禁时间，若传入 -1 表示永久封禁
     * @return int 解封时间的时间戳
     */
    public function addUser(int $user_id, int $time): int
    {
        $expire = $time;
        if ($time > self::BAN_TIME_FOREVER) {
            $expire += $_SERVER['REQUEST_TIME'];
        }
        // 加入屏蔽黑名单或更新其屏蔽解封时间
        $this->redis->zAdd(KEY_BLACK_LIST, $expire, $user_id);

        return $expire;
    }

    /**
     * 封禁 IP
     *
     * @param string $ip 要封禁 IP
     * @param int $time 封禁时间，单位为秒，若传入 -1 表示永久封禁
     * @return int 解封时间的时间戳
     */
    public function addIP(string $ip, int $time): int
    {
        $expire = $time;
        if ($time !== self::BAN_TIME_FOREVER) {
            $expire += $_SERVER['REQUEST_TIME'];
        }
        // 加入屏蔽黑名单或更新其屏蔽解封时间
        $this->redis->zAdd(KEY_BLACK_LIST_IP, $expire, $ip);
        return $expire;
    }

    /**
     * 将 IP 解封
     *
     * @param string $ip 要从黑名单中移除的 IP
     * @return bool 是否解封成功
     */
    public function removeIP(string $ip): bool
    {
        return $this->redis->zRem(KEY_BLACK_LIST_IP, $ip);
    }

    /**
     * 将用户解封
     *
     * @param int $user_id 要从黑名单中移除的用户的 ID
     * @return bool 是否解封成功
     */
    public function removeUser(int $user_id): bool
    {
        return $this->redis->zRem(KEY_BLACK_LIST, $user_id);
    }

    /**
     * 获得 IP 解封时间
     *
     * @param string $ip IP
     * @return bool|int 解封时间的时间戳，若为 false 表示未被封，若为 -1 表示永封
     */
    public function getIPExpire(string $ip)
    {
        $expire = $this->redis->zScore(KEY_BLACK_LIST_IP, $ip);
        if ($expire === false) return $expire;
        return (int)$expire;
    }

    /**
     * 获得用户解封时间
     *
     * @param int $user_id 用户的 ID
     * @return bool|int 解封时间的时间戳，若为 false 表示未被封，小于 0 则为永封
     */
    public function getUserExpire(int $user_id)
    {
        $expire = $this->redis->zScore(KEY_BLACK_LIST, $user_id);
        if ($expire === false) return $expire;
        return (int)$expire;
    }

    /**
     * 检查渠道是否处于限制列表中
     *
     * @param integer $type 类型
     * @param string $channel 渠道
     * @param string $version 版本号
     * @return boolean 若为 true 表示已限制，false 表示未限制
     */
    public function hasChannel(int $type, string $channel, string $version): bool
    {
        $key = $this->getChannelKey($type);
        $value = $channel . ':' . $version;
        [$is_black_channel_version, $is_black_channel_all_version] = $this->redis->multi(Redis::PIPELINE)
            ->sIsMember($key, $value)
            // 0 表示渠道内所有版本都处于限制列表中
            ->sIsMember($key, $channel . ':0')
            ->exec();
        return $is_black_channel_version || $is_black_channel_all_version;
    }

    /**
     * 限制渠道
     *
     * @param integer $type 类型
     * @param string $channel 渠道
     * @param string $version 版本号
     * @return boolean 若为 true 表示限制成功，false 表示限制失败
     */
    public function addChannel(int $type, string $channel, string $version): bool
    {
        $key = $this->getChannelKey($type);
        $value = $channel . ':' . $version;
        return (bool)$this->redis->sAdd($key, $value);
    }

    /**
     * 取消限制渠道
     *
     * @param integer $type 类型
     * @param string $channel 渠道
     * @param string $version 版本号
     * @return boolean 若为 true 表示取消限制成功，false 表示取消限制失败
     */
    public function removeChannel(int $type, string $channel, string $version): bool
    {
        $key = $this->getChannelKey($type);
        $value = $channel . ':' . $version;
        return (bool)$this->redis->sRem($key, $value);
    }

    /**
     * 检查是否需要限制游戏中心入口
     *
     * @return bool 若为 true 表示需要限制游戏中心入口，false 表示不需要限制游戏中心入口
     */
    public function isGameCenterBlocked(): bool
    {
        $os = Yii::$app->equip->getOs();
        switch ($os) {
            case Equipment::Android:
                $channel = Yii::$app->equip->getChannel();
                if (!$channel) {
                    return false;
                }
                if ($channel === Equipment::CHANNEL_GOOGLE) {
                    return true;
                }

                $app_version = Yii::$app->equip->getAppVersion();
                return self::model()->hasChannel(self::TYPE_CHANNEL_AUDIT_SENSITIVE_HIDE, $channel, $app_version);
            case Equipment::iOS:
            case Equipment::HarmonyOS:
                return true;
            default:
                Yii::error('暂时不支持的系统类型：' . $os, __METHOD__);
                return true;
        }
    }

    /**
     * 是否属于黑名单渠道
     *
     * @param int $type
     * @return bool
     */
    public function isBlackChannel(int $type = self::TYPE_CHANNEL_AUDIT_SENSITIVE_HIDE)
    {
        if (!Yii::$app->equip->isAndroid()) {
            return false;
        }

        $channel = Yii::$app->equip->getChannel();
        if (!$channel) {
            return false;
        }

        $app_version = Yii::$app->equip->getAppVersion();
        return self::model()->hasChannel($type, $channel, $app_version);
    }

    /**
     * 获取渠道的 redis key
     *
     * @param integer $type 类型
     * @return string 获取到的渠道 redis key
     */
    public function getChannelKey(int $type)
    {
        return $this->redis->generateKey(KEY_CHANNEL_BLACK_LIST, $type);
    }

    /**
     * 判断指定渠道是否隐藏某个模块
     *
     * @param string $channel 渠道名
     * @param string $title 模块标题
     * @return bool 若为 true 表示需要隐藏，false 表示不需要
     */
    public static function isChannelRecommendModuleBlocked(string $channel, string $title): bool
    {
        if ($channel !== Equipment::CHANNEL_HUAWEI) {
            return false;
        }
        return str_contains($title, '恋人') || str_contains($title, '女友');
    }
}
