<?php

namespace app\models;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "m_person_drama_page".
 *
 * @property integer $recid
 * @property integer $soundid
 * @property integer $dramaid
 * @property string $drama_name
 * @property string $drama_cover
 * @property string $episode_name
 */
class MPersonDramaPage extends Feed
{
    const DRAMA_FREE = 0;
    const DRAMA_UNPAID = 1;
    const DRAMA_PAID = 2;
    const DRAMA_PAY_BY_SOUND = 1;
    const DRAMA_PAY_BY_DRAMA = 2;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_person_drama_page';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['recid', 'soundid', 'dramaid'], 'integer'],
            [['drama_name', 'episode_name'], 'string', 'max' => 30],
            [['drama_cover'], 'string', 'max' => 255],
            [['recid', 'soundid'], 'unique', 'targetAttribute' => ['recid', 'soundid'], 'message' => 'The combination of Recid and Soundid has already been taken.'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'recid' => 'Recid',
            'soundid' => 'Soundid',
            'dramaid' => 'Dramaid',
            'drama_name' => 'Drama Name',
            'drama_cover' => 'Drama Cover',
            'episode_name' => 'Episode Name',
        ];
    }

    /**
     * 获取追剧的动态
     *
     * @param string|array $condition
     * @param string|array $order
     * @param integer $limit
     * @return array
     */
    public static function getFeed($condition, $order, $limit): array
    {

        $select = 't.id, t.create_time, t.duration, t.soundstr, t.cover_image, t.view_count, 
            t1.dramaid AS drama_id, t1.drama_name, t1.drama_cover, t1.episode_name';

        $feed3 = MSound::find()->alias('t')->select($select)->innerJoin('m_person_drama_page t1', 't1.soundid = t.id')
            ->where($condition)->orderBy($order)->limit($limit)->asArray()->all();
        if (!empty($feed3)) {
            $feed3 = array_map(function ($item) {
                $item['id'] = (int)$item['id'];
                // WORKAROUND: 兼容 timing_task 项目 BUG 导致的剧集及单集名称被单引号包裹的 Feed 流数据
                $item['drama_name'] = trim($item['drama_name'], "'");
                $item['episode_name'] = trim($item['episode_name'], "'");
                $item['create_time'] = (int)$item['create_time'];
                $item['duration'] = (int)$item['duration'];
                $item['view_count'] = (int)$item['view_count'];
                $item['front_cover'] = MSound::getFrontCoverUrl($item['cover_image']);
                $item['drama_id'] = (int)$item['drama_id'];
                $item['drama_cover'] = Drama::getCoverUrl($item['drama_cover']);
                $item['type'] = 'drama';

                unset($item['cover_image']);
                return $item;
            }, $feed3);
        }
        return $feed3;
    }

    /**
     * 获取用户订阅的剧集动态
     *
     * @param integer $user_id 用户 ID
     * @param integer $type 排序方式 1：最近更新；2：最新追剧；3：最近收听
     * @param integer $page 第几页
     * @param integer $page_size 每页个数
     * @return array
     */
    public static function getDramaFeed($user_id, $type = 1, $page = 1, $page_size = PAGE_SIZE_20)
    {
        $dramas = Drama::rpc('drama/get-feed', [
            'user_id' => $user_id,
            'type' => $type,
            'page' => $page,
            'page_size' => $page_size
        ]);
        if (!empty($dramas['Datas'])) {
            Drama::compatibleFillCornerMark($dramas['Datas'], $user_id);
        }
        return $dramas;
    }

    /**
     * 获取追剧未观看的数量及订阅的剧集数
     *
     * @param int|null $user_id
     * @return array 例 ['subscribed_num' => 10, 'feed_num' => 4]
     */
    public static function getDramaFeedNum(?int $user_id): array
    {
        if ($user_id <= 0) {
            return ['subscribed_num' => 0, 'feed_num' => 0];
        }
        return Drama::rpc('drama/get-feed-num', [
            'user_id' => $user_id,
        ]);
    }

}
