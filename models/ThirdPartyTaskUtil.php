<?php

namespace app\models;

use Exception;
use Yii;
use yii\helpers\Json;

class ThirdPartyTaskUtil
{
    // token 签名算法
    const TOKEN_DELIMITER = '|';

    // 第三方场景
    const SCENE_BAIDU = 'baidu';  // 百度
    const SCENE_CTRIP = 'ctrip';  // 携程
    const SCENE_DIANPING = 'dianping';  // 大众点评
    const SCENE_BAIDUMAP = 'baidumap';  // 百度地图
    const SCENE_YOUKU = 'youku';  // 优酷视频
    const SCENE_QQ_BROWSER = 'qq_browser';  // QQ 浏览器
    const SCENE_WEIBO = 'weibo';  // 微博
    const SCENE_QUARK = 'quark';  // 夸克
    const SCENE_LOFTER = 'lofter';  // LOFTER
    const SCENE_QQ_MUSIC = 'qq_music'; // QQ 音乐
    const SCENE_QIDIAN = 'qidian'; // 起点
    const SCENE_ARR = [
        self::SCENE_BAIDU,
        self::SCENE_CTRIP,
        self::SCENE_DIANPING,
        self::SCENE_BAIDUMAP,
        self::SCENE_YOUKU,
        self::SCENE_QQ_BROWSER,
        self::SCENE_WEIBO,
        self::SCENE_QUARK,
        self::SCENE_LOFTER,
        self::SCENE_QQ_MUSIC,
        self::SCENE_QIDIAN,
    ];

    // 参数中的 scene 与数据库中的映射
    const SCENE_MAP = [
        self::SCENE_BAIDU => MThirdPartyTask::SCENE_BAIDU,
        self::SCENE_CTRIP => MThirdPartyTask::SCENE_CTRIP,
        self::SCENE_DIANPING => MThirdPartyTask::SCENE_DIANPING,
        self::SCENE_BAIDUMAP => MThirdPartyTask::SCENE_BAIDUMAP,
        self::SCENE_YOUKU => MThirdPartyTask::SCENE_YOUKU,
        self::SCENE_QQ_BROWSER => MThirdPartyTask::SCENE_QQ_BROWSER,
        self::SCENE_WEIBO => MThirdPartyTask::SCENE_WEIBO,
        self::SCENE_QUARK => MThirdPartyTask::SCENE_QUARK,
        self::SCENE_LOFTER => MThirdPartyTask::SCENE_LOFTER,
        self::SCENE_QQ_MUSIC => MThirdPartyTask::SCENE_QQ_MUSIC,
        self::SCENE_QIDIAN => MThirdPartyTask::SCENE_QIDIAN,
    ];

    /**
     * 签名
     *
     * @param array $params 需要生成签名的参数
     * @return string
     */
    public static function buildSign(array $params): string
    {
        unset($params['sign']);
        ksort($params);
        $str_to_sign = $str_to_sign = http_build_query($params, '', null, PHP_QUERY_RFC3986);
        $sign_key = APP_THIRD_PARTY_TASK_API_SIGN_KEY[$params['scene']];
        return strtolower(md5($str_to_sign . $sign_key));
    }

    /**
     * 是否为合法签名
     *
     * @param array $params 需要验签的参数
     * @return bool
     */
    public static function isValidSign(array $params): bool
    {
        return self::buildSign($params) === $params['sign'];
    }

    /**
     * 更新任务状态为已完成未领取奖励
     *
     * @param string $token 任务 token
     * @param string $scene 任务场景
     * @return int
     */
    public static function finishTask(string $token, string $scene): int
    {
        $token = urldecode($token);
        $task = MThirdPartyTask::find()->where('token = :token AND scene = :scene', [
            ':token' => $token,
            ':scene' => self::SCENE_MAP[$scene],
        ])->one();
        if (!$task) {
            Yii::error(sprintf('任务 token 不存在，token：%s，scene：%s', $token, $scene), __METHOD__);
            return 0;
        }
        if (date('Y-m-d', $_SERVER['REQUEST_TIME']) !== date('Y-m-d', $task->task_time)) {
            Yii::warning(sprintf('token 已过期，token：' . $token), __METHOD__);
            return 0;
        }
        if ($task->status !== MThirdPartyTask::TASK_STATUS_ON_GOING) {
            return 0;
        }
        $task->status = MThirdPartyTask::TASK_STATUS_FINISHED;
        return $task->update(false);
    }
}
