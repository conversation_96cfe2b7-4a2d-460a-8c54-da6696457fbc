<?php

namespace app\models;

use app\components\util\MUtils;
use Yii;

/**
 * This is the model class for table "msg_read".
 *
 * @property int $id 主键
 * @property int $msg_id 信件 ID
 * @property int $user_id 用户 ID
 * @property string $equip_id 设备 ID
 * @property string $buvid BUVID
 * @property int $created_time 创建时间
 * @property int $modified_time 更新时间
 */
class MsgRead extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'msg_read';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db2');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['msg_id'], 'required'],
            [['msg_id', 'user_id', 'created_time', 'modified_time'], 'integer'],
            [['equip_id'], 'string', 'max' => 36],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'msg_id' => '信件 ID',
            'user_id' => '用户 ID',
            'equip_id' => '设备 ID',
            'created_time' => '创建时间',
            'modified_time' => '更新时间',
            'buvid' => 'BUVID'
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->created_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 获取用户已查看的信件
     *
     * @param string $equip_id 设备 ID
     * @param integer|null $user_id 用户 ID
     * @param array $msg_ids 信件 ID
     * @return array
     */
    public static function getReadMsgIds(string $equip_id, $user_id = null, array $msg_ids = [])
    {
        $query = self::find()->select('msg_id');
        if ($msg_ids) {
            $query->where(['msg_id' => $msg_ids]);
        }
        if ($user_id) {
            // 该账号已查看的信件
            $msg_ids_read = $query
                ->andWhere('user_id = :user_id AND equip_id IS NULL', [':user_id' => $user_id])
                ->column();
        } else {
            // 设备已查看的信件
            $msg_ids_read = $query
                ->andWhere('user_id = 0 AND equip_id = :equip_id', [':equip_id' => $equip_id])
                ->column();
        }
        return array_map('intval', $msg_ids_read);
    }

    /**
     * 同步设备与账号之间的信件
     *
     * @param string $equip_id
     * @param null|int $user_id
     * @return bool
     * @throws \yii\db\Exception
     */
    public static function syncMsgIds(string $equip_id, ?int $user_id)
    {
        if (!$user_id) return false;
        $now = $_SERVER['REQUEST_TIME'];
        $res = self::find()->select('msg_id, user_id, equip_id')
            ->where('(user_id = 0 AND equip_id = :equip_id) OR (user_id = :user_id AND equip_id IS NULL)')
            ->params([':user_id' => $user_id, ':equip_id' => $equip_id])
            ->asArray()->all();
        if (!$res) return false;
        $msg_ids_group = MUtils::groupArray($res, 'user_id', 'msg_id');
        $msgids_equip_id = $msg_ids_group[0] ?? [];
        $msgids_userid = $msg_ids_group[$user_id] ?? [];

        $expired_msg_ids = MsgBox::getExpiredIds(array_column($res, 'msg_id'));
        $records = array_reduce($res, function ($ret, $item) use ($msgids_equip_id, $msgids_userid,
                $now, $equip_id, $user_id, $expired_msg_ids) {
            if (in_array($item['msg_id'], $expired_msg_ids)) {
                return $ret;
            }
            $msg = [];
            if ($item['user_id'] && !in_array($item['msg_id'], $msgids_equip_id)) {
                $msg['equip_id'] = $equip_id;
                $msg['buvid'] = Yii::$app->equip->getBuvid() ?? '';
                $msg['user_id'] = 0;
            } elseif (!$item['user_id'] && !in_array($item['msg_id'], $msgids_userid)) {
                $msg['equip_id'] = null;
                $msg['buvid'] = '';
                $msg['user_id'] = $user_id;
            }
            if ($msg) {
                $msg['msg_id'] = $item['msg_id'];
                $msg['created_time'] = $msg['modified_time'] = $now;
                $ret[] = $msg;
            }
            return $ret;
        }, []);
        if (!$records) return false;
        self::getDb()->createCommand()
            ->batchInsert(self::tableName(), array_keys(current($records)), $records)
            ->execute();
        return true;
    }

    /**
     * 记录新的已读信件
     *
     * @param array $msg_ids 信件 ID
     * @param string $equip_id 设备 ID
     * @param integer|null $user_id 用户 ID
     * @return bool
     * @throws \yii\db\Exception
     */
    public static function insertNewReads(array $msg_ids, string $equip_id, $user_id)
    {
        if (!$msg_ids) return false;
        $now = $_SERVER['REQUEST_TIME'];
        $records = array_map(function ($msg_id) use ($equip_id, $user_id, $now) {
            return [
                'msg_id' => $msg_id,
                'equip_id' => $user_id ? null : $equip_id,
                'buvid' => $user_id ? '' : Yii::$app->equip->getBuvid() ?? '',
                'user_id' => $user_id ?: 0,
                'created_time' => $now,
                'modified_time' => $now,
            ];
        }, $msg_ids);
        self::getDb()->createCommand()
            ->batchInsert(self::tableName(), array_keys(current($records)), $records)
            ->execute();
        return true;
    }

    /**
     * 获取已读的信件数量
     *
     * @param string $equip_id 设备 ID
     * @param array $msg_ids 信件 ID
     * @param integer|null $user_id 用户 ID
     * @return integer
     */
    public static function getReadCount(string $equip_id, array $msg_ids, ?int $user_id = 0)
    {
        $condition = $user_id ? ['user_id' => $user_id, 'equip_id' => null] : ['user_id' => 0, 'equip_id' => $equip_id];
        return (int)self::find()
            ->where($condition)
            ->andWhere(['msg_id' => $msg_ids])
            ->count();
    }
}
