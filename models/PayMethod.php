<?php

namespace app\models;

use app\components\util\Equipment;
use missevan\storage\StorageClient;
use yii\helpers\Json;
use Yii;

class PayMethod
{
    const CHANNEL_COMMON = 0b01;
    const CHANNEL_GOOGLE_PLAY = 0b10;

    public static function isGooglePlayChannel(int $channel): bool
    {
        return ($channel & self::CHANNEL_GOOGLE_PLAY) > 0;
    }

    public static function isCommonChannel(int $channel): bool
    {
        return ($channel & self::CHANNEL_COMMON) > 0;
    }

    public static function getAndroidPayMethod(array $excluded_types, bool $is_google_play_new_version): array
    {
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_TOPUP_PAYMENT_METHOD, Equipment::Android);
        $payment_method_cache = $redis->get($key);
        $payment_method_list = $payment_method_cache ? Json::decode($payment_method_cache) : [];

        return array_reduce($payment_method_list, function ($ret, $item) use ($excluded_types, $is_google_play_new_version) {
            if (!in_array($item['type'], $excluded_types)) {
                $item['icon'] = StorageClient::getFileUrl($item['icon']);
                $pay_method_channel = $item['channel'] ?? self::CHANNEL_COMMON;
                unset($item['channel']);

                if ($is_google_play_new_version) {
                    if (self::isGooglePlayChannel($pay_method_channel)) {
                        $ret[] = $item;
                    }
                } else {
                    if (self::isCommonChannel($pay_method_channel)) {
                        $ret[] = $item;
                    }
                }
            }
            return $ret;
        }, []);
    }

}
