<?php

namespace app\models;

use missevan\util\MUtils;
use Yii;

/**
 * This is the model class for table "account_info".
 *
 * @property string $id
 * @property integer $user_id 用户 ID
 * @property string $real_name 真实姓名
 * @property string $account 提现账号
 * @property string $mobile 手机号
 * @property string $id_number 身份证号
 * @property string $bank 开户银行
 * @property string $bank_branch 支行信息
 * @property string $bank_account 银行账户
 * @property integer $create_time 创建时间
 * @property integer $modified_time 修改时间
 * @property integer $type 账号类型
 * @property integer $status 信息状态
 */
class AccountInfo extends ActiveRecord
{
    // 支付宝账户
    const TYPE_ALIPAY = 0;
    // 银行账户
    const TYPE_BANK = 1;

    // 信息状态 -1：历史保存；0：保存；1：已确认
    const STATUS_SAVED = -1;
    const STATUS_SAVE = 0;
    const STATUS_CONFIRM = 1;

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->paydb;
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'account_info';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'real_name', 'mobile', 'id_number', 'bank', 'bank_branch', 'bank_account', 'create_time'],
                'required'],
            [['user_id', 'create_time', 'type', 'status'], 'integer'],
            ['bank_account', 'number'],
            [['real_name', 'bank'], 'string', 'max' => 20],
            [['id_number'], 'string', 'max' => 44],
            [['bank_account'], 'string', 'max' => 30],
            [['account', 'bank_branch'], 'string', 'max' => 255],
            [['mobile'], 'string', 'max' => 15],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => '用户 ID',
            'real_name' => '真实姓名',
            'account' => '提现账号',
            'mobile' => '手机号',
            'id_number' => '身份证号',
            'bank' => '开户银行',
            'bank_branch' => '支行信息',
            'bank_account' => '银行账户',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'type' => '账户类型',  // 0：支付宝账号；1：银行卡账号
            'status' => '信息状态',  // -1：历史保存 0：待确认 1：已确认
        ];
    }

    /**
     * 获取解密后的手机号
     *
     * @return string
     */
    public function getDecryptMobile(): string
    {
        return MUtils::decrypt($this->mobile, $this->create_time, SENSITIVE_INFORMATION_KEY);
    }

    /**
     * 获取解密后的身份证号
     *
     * @return string
     */
    public function getDecryptIdNumber(): string
    {
        return MUtils::decrypt($this->id_number, $this->create_time, SENSITIVE_INFORMATION_KEY);
    }

    /**
     * 获取解密后的银行卡号
     *
     * @return string
     */
    public function getDecryptBankAccount(): string
    {
        if (self::TYPE_BANK === $this->type) {
            return MUtils::decrypt($this->bank_account, $this->create_time, SENSITIVE_INFORMATION_KEY);
        }
        return '';
    }

    /**
     * 获取解密后的支付宝账号
     *
     * @return string
     */
    public function getDecryptAccount(): string
    {
        if (self::TYPE_ALIPAY === $this->type) {
            return MUtils::decrypt($this->account, $this->create_time, SENSITIVE_INFORMATION_KEY);
        }
        return '';
    }
}
