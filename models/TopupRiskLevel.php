<?php

namespace app\models;

use app\components\auth\AuthBiliBili;
use app\components\util\MUtils;
use Exception;
use missevan\util\HttpExceptionI18n;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "topup_risk_level".
 *
 * @property int $id 主键
 * @property int $order_id 订单 ID
 * @property int $user_id 用户 ID
 * @property int $level 风控级别
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 */
class TopupRiskLevel extends ActiveRecord
{
    const LEVEL_HIGH_RISK_REJECT_DIRECTLY = 1;
    const LEVEL_NO_RISK = 5;

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('logdb');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'topup_risk_level';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['order_id', 'user_id'], 'required'],
            [['order_id', 'user_id', 'level', 'create_time', 'modified_time'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'order_id' => '订单 ID',
            'user_id' => '用户 ID',
            // 1 高风险，直接拒绝, 2 高风险，拦截交易, 3 中等风险，事先验证, 4 轻微风险，事后验证, 5 无风险
            'level' => '风控级别',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 检查充值订单风险等级
     *
     * @param string $out_order_id 外部订单号
     * @param array $receipt_body Apple 小票详情
     * @param string $transaction_id 交易 ID
     * @return int 风险等级
     */
    public static function checkRisk($out_order_id, $receipt_body, $transaction_id)
    {
        try {
            $order_id = RechargeOrder::getRealOrderId($out_order_id);
            $order = RechargeOrder::find()
                ->where(['id' => $order_id])
                ->limit(1)->one();
            if (!$order) {
                throw new HttpExceptionI18n(404, 'Order not found');
            }
            $trace_id = md5($order->getOrderId());

            AuthBiliBili::payRskAsk([
                'traceId' => $trace_id,
                'txId' => $out_order_id,
                'orderId' => $out_order_id,
                'reqData' => [
                    'orderId' => $out_order_id,
                    'orderTime' => date('Y-m-d H:i:s.0', $order->create_time),
                    'payAmount' => (string)Balance::profitUnitConversion($order->price, Balance::CONVERT_YUAN_TO_FEN),
                    'createUa' => Yii::$app->equip->getUserAgent(),
                    'shieldTransactionIds' => '',
                    'transactionId' => $transaction_id,
                ],
                'verifyIapReceiptResult' => $receipt_body,
                'interfaceName' => 'IAP_RECEIPT_CHECK',
            ]);

            // Bilibili 风控链路存在 100ms 时延，在查询风控结果前停留 200ms
            usleep(200 * 1000);
            $risk_check = AuthBiliBili::payRskCheck([
                'traceId' => $trace_id,
                'txId' => $out_order_id,
                'orderId' => $out_order_id,
            ]);
            // $risk_check 默认都是有 riskLevel 返回结果，为避免出现请求异常情况加上存在性判断
            $level = $risk_check['riskLevel'] ?? 0;
            if ($level !== 0 && $level < self::LEVEL_NO_RISK) {
                $content = 'iOS 充值出现风控风险: '
                    . $risk_check['shieldShowMsg'] ?? ''
                    . ', 订单 ID ' . $order->id
                    . ', 金额 ' . $order->price
                    . ', 风控详情 ' . Json::encode($risk_check);
                Yii::warning($content, __METHOD__);
            }

            $model = new self;
            $model->setAttributes([
                'user_id' => $order->uid,
                'order_id' => $order->id,
                'level' => $level,
            ]);
            if (!$model->save()) {
                throw new Exception(MUtils::getFirstError($model));
            }

            return $model->level;
        } catch (\Exception $e) {
            Yii::error("充值风控异常 {$e->getMessage()}");
            return 0;
        }
    }

}
