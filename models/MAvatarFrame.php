<?php

namespace app\models;

use app\components\util\Tools;
use Exception;
use missevan\rpc\ServiceRpc;
use Yii;
use yii\helpers\Html;

class MAvatarFrame
{
    // 新人头像框 ID
    const NEW_USER_AVATAR_FRAME_ID = 1;

    // 新人头像框"新人"判定时长（视作新人的注册时长，20 天，单位：秒）
    const NEW_USER_AVATAR_FRAME_REGISTER_DURATION = 1728000;
    // 获取新人头像框连续签到天数
    const GET_NEW_USER_AVATAR_FRAME_SIGN_DAYS = 7;

    /**
     * 发放新人头像框
     *
     * @param int $user_id 用户 ID
     * @param int $register_time 用户注册时间戳，单位：秒
     * @param int $sign_days 用户连续签到天数
     * @return array|null 头像框信息，未发放或发放失败时返回 null
     */
    public static function sendNewUserAvatarFrame(int $user_id, int $register_time, int $sign_days)
    {
        // 获取注册当日零点时间
        $register_day_time = strtotime('today', $register_time);
        $is_new_user = $_SERVER['REQUEST_TIME'] < ($register_day_time + self::NEW_USER_AVATAR_FRAME_REGISTER_DURATION);
        if ($is_new_user && $sign_days >= self::GET_NEW_USER_AVATAR_FRAME_SIGN_DAYS) {
            // 新用户连续签到天数到达发放条件时，发放新人头像框
            // 此处大于发放条件的天数也需要进行发放，避免补签后直接超过发放天数的情况，如：
            // 本月开始注册签到，今天是 8 号，1 ~ 6 号都签到了，今天签到后，再补签 7 号，导致连续签到数直接到了 8 天
            // 相关 RPC 接口有幂等性，重复请求时不会重复发放
            try {
                // WORKAROUND: 为了兼容优先佩戴十周年任务头像框（MEvent::exchange10thAnniversaryPrize），临时改为自动佩戴，活动结束后需要改回直接佩戴
                $result = Yii::$app->serviceRpc->sendAvatarFrame($user_id,
                    self::NEW_USER_AVATAR_FRAME_ID, null, 1);
                if ($result['is_new']) {
                    // 若成功下发，发送系统通知
                    // expire_duration 单位为秒
                    $expire_days = floor($result['expire_duration'] / ONE_DAY);
                    // WORKAROUND: 是否提醒前往【最新版】（当前时间在 2023-12-01 00:00:00 前需要提醒），后续可删除
                    $notice_newest_version = $_SERVER['REQUEST_TIME'] < 1701360000;
                    Yii::$app->tools->sendNotification([
                        'user_id' => $user_id,
                        'title' => '恭喜！您已获得新人专属头像框！',
                        'content' => sprintf(
                            '恭喜小耳朵达成新人连签 %d 日成就，获得新人专属头像框【%s】一个，装扮现已生效，有效期 %d 天；您可以前往【%sApp 个人主页 > 头像 > 更换头像挂件】查看哦！',
                            self::GET_NEW_USER_AVATAR_FRAME_SIGN_DAYS,
                            Html::encode($result['name']),
                            $expire_days,
                            $notice_newest_version ? '最新版 ' : '')
                    ], Tools::SEND_SYS_MSG);
                }
                return $result;
            } catch (Exception $e) {
                // PASS: 出错时避免影响签到业务，不抛出异常
                Yii::error('发放新人头像框或发送相关系统通知失败：' . $e->getMessage(), __METHOD__);
                return null;
            }
        }
        return null;
    }
}
