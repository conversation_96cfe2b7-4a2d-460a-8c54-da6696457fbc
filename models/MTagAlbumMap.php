<?php

namespace app\models;

use app\components\util\MUtils;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "m_tag_album_map".
 *
 * @property integer $tag_id
 * @property integer $album_id
 */
class MTagAlbumMap extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_tag_album_map';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['tag_id', 'album_id'], 'required'],
            [['tag_id', 'album_id'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'tag_id' => 'Tag ID',
            'album_id' => 'Album ID',
        ];
    }

    public function getTagAlbum($tid, $order = 0, $page_size, $page = 1)
    {
        $query = MAlbum::find()
            ->alias('t')
            ->leftJoin('m_tag_album_map t1', 't.id = t1.album_id')
            ->orderBy('create_time desc');

        $redis = Yii::$app->redis;
        if ($tid == 0) {
            $album_id_key = ALBUMS_RECOMMENDED_ALL;
            $query->where(['>', 't.music_count', 10]);
            $query->andWhere(['t1.tag_id' => [25, 26310, 26311, 2674, 370, 376, 273, 13349, 850, 26312, 28, 4421, 5, 170]]);
            $query->groupBy('t1.album_id');
        } else {
            $album_id_key = $redis->generateKey(ALBUMS_RECOMMENDED_SPECIAL_TAG_ALL, $tid);
            $query->where('t1.tag_id = :tag_id', [':tag_id' => $tid]);
            $query->andWhere(['>', 't.music_count', 0]);
        }

        if ($order) {
            $albums = MUtils::getPaginationModels($query, $page_size);
            $albums->Datas = array_map(function ($album) {
                return [
                    'id' => $album->id,
                    'title' => $album->title,
                    'front_cover' => $album->front_cover,
                    'music_count' => $album->music_count,
                    'user_id' => $album->user_id,
                    'username' => $album->username,
                ];
            }, $albums->Datas);
            return $albums;
        }

        $key = $redis->generateKey(ALBUMS_RECOMMENDED_SPECIAL_TAG, $tid);
        $count = $redis->lLen($key);
        // 根据对应的标签 ID 获取随机的音单 ID
        if (!$count) {
            $albums = MUtils::getRandomModels($query->select('id'));
            $album_ids = array_column($albums, 'id');
            if ($album_id = $redis->get($album_id_key)) {
                $album_id = explode(',', $album_id);
                $album_ids = array_diff($album_ids, $album_id);
                $album_ids = array_merge($album_id, $album_ids);
            }
            $album_ids = array_unique($album_ids);

            foreach ($album_ids as $id) {
                $redis->rPush($key, $id);
            }

            $count = $redis->lLen($key);
            $redis->expire($key, 12 * 3600);
        }
        $limit = $page_size;
        $offset = ($page - 1) * $page_size;
        $albums = Yii::$app->memcache->get('cache:' . $key . ":{$offset}:{$limit}");
        if (is_string($albums)) $albums = Json::decode($albums);
        if (!$albums) {
            if ($album_ids = $redis->lRange($key, $offset, ($offset + $limit - $count) < 0 ? ($offset + $limit - $count - 1) : -1)) {
                $albums = MAlbum::find()->allByColumnValues('id', $album_ids);
                $albums = array_map(function ($album) {
                    return [
                        'id' => $album->id,
                        'title' => $album->title,
                        'front_cover' => $album->front_cover,
                        'music_count' => $album->music_count,
                        'user_id' => $album->user_id,
                        'username' => $album->username,
                    ];
                }, $albums);
                Yii::$app->memcache->set('cache:' . $key . ":{$offset}:{$limit}", Json::encode($albums), 600);
            }
        }

        $pagination = [
            'p' => $page,
            'count' => $count,
            'pagesize' => $page_size,
            'maxpage' => ceil($count / $page_size),
        ];

        $modelDTO = [];
        $modelDTO['Datas'] = $albums;
        $modelDTO['pagination'] = $pagination;

        return $modelDTO;

    }
}
