<?php

namespace app\models;

use missevan\storage\StorageClient;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "goods".
 *
 * @property int $id 主键
 * @property string $title 标题
 * @property string $intro 简介
 * @property string $ip 作品 IP 名称
 * @property string $cover 封面
 * @property string $banners 商品轮播图（json 格式）
 * @property string $detail 详情图
 * @property int $attr 属性（0 预售、1 现货、 2 余量现货）
 * @property int $status 状态（1 在售、0 保留、-1 下架、-2 删除归档）
 * @property int $sort 展示顺序
 * @property string $agreement 相关条款（json 格式：运费说明、签收货、售后说明）
 * @property string $more 其它说明（json 格式：预售时存储预发货时间、截止时间、开售时间、标签）
 * @property int $like_num 喜欢人数
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 */
class Goods extends ActiveRecord
{
    // 商品属性（预售、现货、余量现货）
    public const ATTR_BOOKING = 0;
    public const ATTR_FULL_STOCK = 1;
    public const ATTR_REMAIN = 2;

    // 商品状态（-2 删除归档、-1 下架、1 在售）
    public const STATUS_ARCHIVED = -2;
    public const STATUS_OFF_THE_SHELVE = -1;
    public const STATUS_SALE = 1;

    // App 首页商城入口图标地址
    const ICON_URL = 'oss://system/app/icons/normal/live.png';
    const ICON_DARK_URL = 'oss://system/app/icons/dark/live.png';

    // Bilibili 会员购地址
    const MALL_BILIBILI_LINK = 'missevan://mall/bilibili';

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('malldb');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'goods';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title', 'banners', 'create_time', 'modified_time'], 'required'],
            [['attr', 'status', 'sort', 'like_num', 'create_time', 'modified_time'], 'integer'],
            [['agreement', 'more', 'banners', 'detail'], 'string'],
            [['cover'], 'string', 'max' => 255],
            [['ip'], 'string', 'max' => 30],
            [['title'], 'string', 'max' => 32],
            [['intro'], 'string', 'max' => 55],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'title' => '标题',
            'intro' => '简介',
            'ip' => '作品 IP 名称',
            'cover' => '封面',
            'banners' => '商品轮播图',  // json 格式
            'detail' => '详情',
            'attr' => '属性',  // 0 预售、1 现货、2 余量现货
            'status' => '状态',  // -2 删除归档、-1 下架、0 保留、1 在售
            'sort' => '展示顺序',  // 数值越大排位越靠前
            'agreement' => '相关条款',  // json 格式：运费说明、签收货、售后说明
            'more' => '其它说明',  // json 格式：预售时存储预发货时间、截止时间、开售时间、标签
            'like_num' => '喜欢人数',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->cover) {
            $this->cover = StorageClient::getFileUrl($this->cover) ?: Yii::$app->params['defaultCoverUrl'];
        }
        if ($this->detail) {
            $detail = $this->detail;
            $detail['image'] = array_map(function ($img) {
                return StorageClient::getFileUrl($img);
            }, $detail['image'] ?? []) ?: [Yii::$app->params['defaultCoverUrl']];
            $this->detail = $detail;
        }
        if ($this->banners) {
            $this->banners = array_map(function ($img) {
                $img['url'] = StorageClient::getFileUrl($img['url']);
                return $img;
            }, $this->banners) ?: [['url' => Yii::$app->params['defaultCoverUrl']]];
        }
    }

    /**
     * 判断是否为新品
     * （可通过 object->is_new 调用获取）
     *
     * @return boolean
     */
    public function getIs_new(): bool
    {
        if ($this->attr === self::ATTR_REMAIN) {
            return false;
        }

        return ($_SERVER['REQUEST_TIME'] - $this->more['opening_time']) < ONE_WEEK;
    }

    /**
     * 获取商城商品详情页跳转地址
     *
     * @param int $goods_id
     * @return string
     */
    public static function getGoodsDetailLink(int $goods_id): string
    {
        // 客户端跳转
        return 'missevan://mall/detail/' . $goods_id;
    }
}
