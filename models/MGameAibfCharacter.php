<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "m_game_aibf_character".
 *
 * @property int $id 主键
 * @property string $name 角色名
 * @property string $introduction 角色介绍
 * @property string $avatar_url 头像地址
 * @property string $drawing_url 立绘地址
 * @property int $start_point 初始好感度
 * @property int $appear_time 角色出现时间
 * @property int $price 角色购买价格，单位：猫耳钻石
 */
class MGameAibfCharacter extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_game_aibf_character';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name', 'introduction', 'avatar_url', 'drawing_url'], 'required'],
            [['start_point', 'appear_time', 'price'], 'integer'],
            [['name'], 'string', 'max' => 32],
            [['introduction'], 'string', 'max' => 255],
            [['avatar_url', 'drawing_url'], 'string', 'max' => 128],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => '角色名',
            'introduction' => '角色介绍',
            'avatar_url' => '头像地址',
            'drawing_url' => '立绘地址',
            'start_point' => '初始好感度',
            'appear_time' => '角色出现时间',
            'price' => '角色购买价格',  // 单位：猫耳钻石
        ];
    }
}
