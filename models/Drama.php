<?php

namespace app\models;

use app\components\util\Equipment;
use app\components\util\Go;
use app\components\util\MUtils;
use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\web\HttpException;

class Drama
{
    // 剧集的付费类型 0：免费；1：单集付费；2：整剧付费
    const PAY_TYPE_FREE = 0;
    const PAY_TYPE_EPISODE = 1;
    const PAY_TYPE_DRAMA = 2;

    // 剧集的付费情况 0：免费；1：付费剧集未付费；2：付费剧集已付费
    const DRAMA_FREE = 0;
    const DRAMA_UNPAID = 1;
    const DRAMA_PAID = 2;

    // 播放页单集显示的状态
    const EPISODE_STATUS_COMMON = 0;
    const EPISODE_STATUS_NEW = 1;

    // 榜单类型
    const RANK_TYPE_DRAMA = 0;
    const RANK_TYPE_USER = 1;

    // 剧集、用户打赏榜单（七日或周榜 1、月榜 2、总榜 3）
    const RANK_PERIOD_WEEK = 1;
    const RANK_PERIOD_MONTH = 2;
    const RANK_PERIOD_ALL = 3;

    // 单位时间内剧集被打赏增量最大数额（若超过数额则更新缓存）
    const REWARD_COIN_MAX_INCREMENT = 5000;

    // 打赏榜单显示个数
    const RANK_ITEM_LENTH = 50;
    // 默认的打赏前几名用户个数
    const REWARD_TOP_USER_COUNT = 4;
    // Web 播放页显示用户打赏榜单个数
    const REWARD_TOP_USER_COUNT_WEB = 6;

    // 剧集是否可以被打赏标识，0：不可被打赏；1：可被打赏
    const REWARD_DISABLE = 0;
    const REWARD_ENABLE = 1;

    // 剧集审核状态 0：未审核；1：审核通过；2：审核未通过；3：报警（待整合）；4：合约期满下架
    const CHECKED_NOT_VERIFY = 0;
    const CHECKED_PASS = 1;
    const CHECKED_NOT_PASS = 2;
    const CHECKED_POLICE = 3;
    const CHECKED_CONTRACT_EXPIRED = 4;

    // 单集所属类型，0：正剧，1：访谈，2：音乐
    const EPISODE_TYPE_DRAMA = 0;
    const EPISODE_TYPE_INTERVIEW = 1;
    const EPISODE_TYPE_MUSIC = 2;

    // 播放页免费剧集推荐位数量
    // TODO: 之后应通过使用场景来获取推荐剧集
    const PLAY_PAGE_RECOMMEND_FREE_NUM = 1;

    // 剧集商品周边的类型
    const DERIVATIVE_TYPE_GOODS = 1;

    // 广播剧性向
    const TYPE_BL = 4;  // 纯爱
    const TYPE_LESBIAN = 5;  // 双女主
    const TYPE_OTOME = 6;  // 乙女
    // WORKAROUND: 新增 3 个性向用于临时规避敏感类型
    const TYPE_SERIALIZING = 7;  // 未完结
    const TYPE_END = 8;  // 完结
    const TYPE_ONE_AND_MINI = 9;  // 全一期
    // 剧集分区 ID
    const DRAMA_CATALOG_ID_AUDIO_BOOK = 86;  // 听书
    const DRAMA_CATALOG_ID_AUDIO_BOOK_NOVEL = 91;  // 轻小说
    const DRAMA_CATALOG_ID_AUDIO_BOOK_NETWORK = 93;  // 网络小说
    const DRAMA_CATALOG_ID_AUDIO_BOOK_CHILDREN = 98;  // 儿童
    const DRAMA_CATALOG_ID_CN_RADIO_DRAMA = 89;  // 中文广播剧
    const DRAMA_CATALOG_ID_JAPAN_RADIO_DRAMA = 90;  // 日抓
    const DRAMA_CATALOG_ID_CN_CARTOON = 96;  // 中文有声漫画
    const DRAMA_CATALOG_ID_JAPAN_AUDIO_COMICS = 97;  // 有声日漫
    const DRAMA_CATALOG_ID_MUSIC = 94;  // 音乐
    const DRAMA_CATALOG_ID_RADIO = 88;  // 电台
    const DRAMA_CATALOG_ID_ASMR = 87;  // 催眠
    const DRAMA_CATALOG_ID_VOICE_LOVER = 114;  // 声音恋人

    // 剧集完结度
    const INTEGRITY_NAME_SERIALIZING = 1;  // 未完结
    const INTEGRITY_NAME_END = 2;  // 已完结
    const INTEGRITY_NAME_ONE = 3;  // 全一期
    const INTEGRITY_NAME_MINI = 4;  // 微小剧
    const INTEGRITY_NAME_ONE_AND_MINI = 5;  // 全一期和微小剧

    // 剧集音频分类映射到其所属剧集分类
    const SOUND_TO_DRAMA_CATALOG_IDS = [
        Catalog::CATALOG_ID_DRAMA => self::DRAMA_CATALOG_ID_CN_RADIO_DRAMA,
        Catalog::CATALOG_ID_AUDIO_BOOK => self::DRAMA_CATALOG_ID_AUDIO_BOOK,
        Catalog::CATALOG_ID_JAPAN_DRAMA => self::DRAMA_CATALOG_ID_JAPAN_RADIO_DRAMA,
        // NOTICE: 有声漫画音频分类目前已被映射为中文有声漫画剧集分类
        Catalog::CATALOG_ID_AUDIO_COMICS => self::DRAMA_CATALOG_ID_CN_CARTOON,
    ];

    // 剧集分类名称
    const CATALOG_NAMES = [
        self::DRAMA_CATALOG_ID_CN_RADIO_DRAMA => '广播剧',  // 实际为中文广播剧
        self::DRAMA_CATALOG_ID_AUDIO_BOOK => '听书',
        self::DRAMA_CATALOG_ID_JAPAN_RADIO_DRAMA => '日抓',
        self::DRAMA_CATALOG_ID_CN_CARTOON => '有声漫画',  // 实际为有声中文漫画
    ];

    // 剧集样式 0：默认样式；1：音乐集样式；2：互动广播剧
    const STYLE_DEFAULT = 0;
    const STYLE_MUSIC = 1;
    const STYLE_INTERACTIVE = 2;

    // 0：取消追剧；1：追剧
    const DRAMA_UNSUBSCRIBE = 0;
    const DRAMA_SUBSCRIBE = 1;

    // 剧集推荐场景
    const RECOMMEND_SCENE_APP_DRAMA_VIEW_NEW = 'app_drama_view_new';  // App 剧集详情页从新剧中推荐剧集
    const RECOMMEND_SCENE_APP_DRAMA_VIEW_ALL = 'app_drama_view_all';  // App 剧集详情页从所有剧集中推荐剧集

    // 剧集属性类型
    const REFINED_TYPE_RISKING = 'risking';  // 擦边球
    const REFINED_TYPE_JAPAN_FORBIDDEN = 'japan_forbidden';  // 日本禁听
    const REFINED_TYPE_INTERACTIVE = 'interactive';  // 互动广播剧
    const REFINED_TYPE_LOSSLESS = 'lossless';  // 无损音质广播剧
    const REFINED_TYPE_SPECIAL = 'special';  // 特殊剧集
    const REFINED_SENSITIVE = 'sensitive';  // 敏感剧集（无推荐模块）
    const REFINED_NO_JAPAN_SALE = 'no_japan_sale';  // 日本禁购

    // 是否为特殊剧集（比特位第五位为 1）
    const REFINED_SPECIAL = 1 << 4;

    // 获取剧集子分区的方式
    const SUB_CATEGORY_BY_TYPE = 'type';  // 通过剧集分类获取
    const SUB_CATEGORY_BY_INTEGRITY = 'integrity';  // 通过剧集完结度获取

    // 音频一级分类和剧集一级分类的映射关系
    // 相关文档：https://info.missevan.com/pages/viewpage.action?pageId=50732385
    // NOTICE: 如果之后新加了音频一级分类，这边的映射会缺少对应的剧集分类，之后可以考虑补充或者放在配置文件中处理
    const SOUND_CATALOG_DRAMA_CATALOG_MAP = [
        Catalog::CATALOG_ID_AUDIO_COMICS => self::DRAMA_CATALOG_ID_CN_CARTOON,  // 有声漫画 => 中文有声漫画
        Catalog::CATALOG_ID_DRAMA => self::DRAMA_CATALOG_ID_CN_RADIO_DRAMA,  // 广播剧 => 中文广播剧
        Catalog::CATALOG_ID_MUSIC => self::DRAMA_CATALOG_ID_MUSIC,  // 音乐 => 音乐
        Catalog::CATALOG_ID_SOUND_LOVER => self::DRAMA_CATALOG_ID_VOICE_LOVER,  // 声音恋人 => 声音恋人
        Catalog::CATALOG_ID_ILLUSTRATION => self::DRAMA_CATALOG_ID_RADIO,  // 电台 => 电台
        // 日抓 => 日文广播剧，日文有声漫画
        Catalog::CATALOG_ID_JAPAN_DRAMA => [
            self::DRAMA_CATALOG_ID_JAPAN_RADIO_DRAMA,
            self::DRAMA_CATALOG_ID_JAPAN_AUDIO_COMICS,
        ],
        // 听书 => 听书（虚拟分类，实际为：轻小说、网络小说、儿童）
        Catalog::CATALOG_ID_AUDIO_BOOK => [
            self::DRAMA_CATALOG_ID_AUDIO_BOOK_NOVEL,  // 轻小说
            self::DRAMA_CATALOG_ID_AUDIO_BOOK_NETWORK,  // 网络小说
            self::DRAMA_CATALOG_ID_AUDIO_BOOK_CHILDREN,  // 儿童
        ],
        Catalog::CATALOG_ID_ASMR => self::DRAMA_CATALOG_ID_ASMR,  // 放松 => 催眠
    ];

    // 会员单集状态 0：非会员单集；1：会员剧下的试听单集；2：会员剧下的非试听单集
    const EPISODE_VIP_NOT = 0;
    const EPISODE_VIP_NOT_LIMIT = 1;
    const EPISODE_VIP_LIMIT = 2;

    public static function tableName()
    {
        // DB: app_missevan_radio_drama
        return 'radio_drama_dramainfo';
    }

    public static function checkNeedPay(&$dramas, $user_id)
    {
        if (empty($dramas)) {
            return true;
        }
        if (ArrayHelper::isAssociative($dramas)) {
            // 如果传进来的是字典的时候，需要变成数组
            $dramas_arr = [&$dramas];
            return self::checkNeedPay($dramas_arr, $user_id);
        }
        if (!$user_id) {
            // 用户没有登录，付费剧集都是未付费标识，免费剧集就是免费标识
            foreach ($dramas as &$drama) {
                if (self::PAY_TYPE_FREE === $drama['pay_type']) {
                    $drama['need_pay'] = self::DRAMA_FREE;
                } else {
                    $drama['need_pay'] = self::DRAMA_UNPAID;
                }
            }
        } else {
            $not_free_drama_ids = [];
            foreach ($dramas as $drama) {
                if (self::PAY_TYPE_FREE !== (int)$drama['pay_type']) {
                    $not_free_drama_ids[] = $drama['id'];
                }
            }
            // 判断单集付费剧集和整剧付费剧集是否被购买
            $drama_paid_type = [TransactionLog::TYPE_DRAMA, TransactionLog::TYPE_SOUND];
            $paid_drama_ids = [];
            if (!empty($not_free_drama_ids)) {
                $paid_drama_ids = TransactionLog::find()
                    ->select('gift_id')
                    ->distinct()
                    ->where([
                        'from_id' => $user_id,
                        'gift_id' => $not_free_drama_ids,
                        'type' => $drama_paid_type,
                        'status' => TransactionLog::STATUS_SUCCESS
                    ])
                    ->column();
            }
            foreach ($dramas as &$drama) {
                if (in_array($drama['id'], $not_free_drama_ids)) {
                    $drama['need_pay'] = self::DRAMA_UNPAID;
                    if (in_array($drama['id'], $paid_drama_ids)) {
                        $drama['need_pay'] = self::DRAMA_PAID;
                    }
                } else {
                    $drama['need_pay'] = self::DRAMA_FREE;
                }
            }
        }

        return true;
    }

    /**
     * 获取猜你喜欢推荐模块中剧集的数据
     * @param array $drama_ids 剧集 ID
     * @param int $user_id 用户 ID
     * @return array 猜你喜欢推荐模块中剧集的数据（数组索引为剧集的 ID）
     */
    public static function getRecommendedDramas($drama_ids, $user_id)
    {
        if (!$drama_ids) return [];
        $drama_data = self::rpc('api/get-recommended-dramas', ['drama_ids' => $drama_ids]);
        self::checkNeedPay($drama_data, $user_id);
        $drama_data = array_column($drama_data, null, 'id');
        return $drama_data;
    }

    /**
     * 获取搜索剧集详情
     *
     * @param mixed $s 关键字
     * @param integer $page 当前页，默认值为 1
     * @param integer $page_size 每页个数，默认值为 8
     * @param integer|null $user_id 用户 ID
     * @param integer $sort 排序规则 0：综合排序，1：最多播放，2：最新发布
     * @param integer $search_catalog_id 搜索分区 ID
     * @param bool $get_lucky_bags 获取剧集福袋信息；false：不获取；true：获取
     * @return array 根据关键字搜索的剧集详情
     * @throws
     */
    public static function getSearch($s, int $page = 1, int $page_size = 8, ?int $user_id = 0, int $sort = 0,
            int $search_catalog_id = 0, bool $get_lucky_bags = false)
    {
        $catalog_ids = [];
        // 根据搜索分区 ID 获取音频分类 ID
        $sound_catalog_id = Catalog::getSoundCatalogBySearchCatalog($search_catalog_id);
        if (Catalog::CATALOG_ID_DRAMA_RING === $sound_catalog_id) {
            // 筛选铃声分类时，不需要筛选剧集
            return Yii::$app->go->getEmptySearch($page, $page_size);
        }
        if ($sound_catalog_id > 0) {
            // 根据音频分类 ID 获取对应的剧集分类 ID
            $drama_catalog_ids = self::SOUND_CATALOG_DRAMA_CATALOG_MAP[$sound_catalog_id] ?? [];
            $catalog_ids = is_array($drama_catalog_ids) ? $drama_catalog_ids : [$drama_catalog_ids];
        }
        $params = [
            's' => $s,
            'sort' => $sort,
            'catalog_ids' => $catalog_ids,
            'page' => $page,
            'page_size' => $page_size,
            'sensitive' => Yii::$app->user->isExam ? 1 : 0,
            'user_id' => $user_id,
            'guide_word' => trim(Yii::$app->request->get('guide_word')),
            'suggest_request_id' => trim(Yii::$app->request->get('suggest_request_id')),
            'scenario' => Go::SCENARIO_MAIN_SEARCH,
        ];

        $response = self::rpc('api/search-drama', $params);
        if (isset($response['Datas'])) {
            // 是否为可隐藏分类名模块的版本
            $can_hide_type_name_version = !Equipment::isAppOlderThan('4.7.4', '5.6.2');
            $response['Datas'] = array_map(function ($drama) use ($can_hide_type_name_version) {
                $type_name = $drama['type_name'];
                if ($can_hide_type_name_version && in_array($drama['catalog'],
                    [self::DRAMA_CATALOG_ID_CN_RADIO_DRAMA, self::DRAMA_CATALOG_ID_CN_CARTOON])) {
                    // WORKAROUND: 若为可隐藏分类模块的版本且为中文广播剧或中文有声漫画，则 type_name 返回空字符串进行隐藏
                    $type_name = '';
                }
                $data = [
                    'id' => $drama['id'],
                    'name' => $drama['name'],
                    'newest' => $drama['newest'],
                    'catalog' => $drama['catalog_name'],
                    'type' => $drama['type'],
                    'type_name' => $type_name,
                    'cover' => $drama['cover'],
                    'cover_color' => $drama['cover_color'],
                    'serialize' => $drama['serialize'],
                    'pay_type' => $drama['pay_type'],
                    'price' => $drama['price'],
                    'checked' => $drama['checked'],
                    'view_count' => $drama['view_count'],
                    'ipr_id' => $drama['ipr_id'],
                ];
                if (isset($drama['is_insert'])) {
                    $data['is_insert'] = $drama['is_insert'];
                }
                return $data;
            }, $response['Datas']);
            self::compatibleFillCornerMark($response['Datas'], (int)$user_id);
            if ($get_lucky_bags) {
                self::getDramaLuckybags($response['Datas']);
            }
        }
        return $response;
    }

    /**
     * 获取剧集福袋信息
     * 如果搜索结果中包含多个 IPR 的剧集，每个 IPR 下福袋都展示在该 IPR 的第一部（剧集露出顺序）剧集下，
     * 如果搜索结果中包含没有 IPR 的剧集，每部没 IPR 的剧集都查询和展示福袋
     *
     * @param array $dramas 剧集信息
     * @return null
     */
    public static function getDramaLuckybags(array &$dramas)
    {
        $no_ipr_drama_ids = $ipr_drama_ids_map = [];
        foreach ($dramas as $drama) {
            if ($drama['ipr_id']) {
                if (!isset($ipr_drama_ids_map[$drama['ipr_id']])) {
                    // 由于同 IPR 下只在第一个剧集展示福袋，所以只记录第一个剧集 ID
                    $ipr_drama_ids_map[$drama['ipr_id']] = $drama['id'];
                }
            } else {
                $no_ipr_drama_ids[] = $drama['id'];
            }
        }

        $ipr_ids = null;
        if ($ipr_drama_ids_map) {
            $ipr_ids = array_keys($ipr_drama_ids_map);
        }
        if (empty($no_ipr_drama_ids)) {
            $no_ipr_drama_ids = null;
        }
        try {
            $drama_luckybags = Yii::$app->liveRpc->getDramaLuckybags($ipr_ids, $no_ipr_drama_ids);
            if (!$drama_luckybags) {
                return null;
            }
            if (isset($drama_luckybags['ipr_lucky_bags']) && $drama_luckybags['ipr_lucky_bags']) {
                $ipr_drama_ids = array_values($ipr_drama_ids_map);
                foreach ($drama_luckybags['ipr_lucky_bags'] as $ipr_id => $ipr_lucky_bag) {
                    foreach ($dramas as &$drama) {
                        if ((int)$ipr_id === $drama['ipr_id'] && in_array($drama['id'], $ipr_drama_ids)) {
                            $drama['live_lucky_bag'] = $ipr_lucky_bag;
                            break;
                        }
                    }
                }
            }
            if (isset($drama_luckybags['drama_lucky_bags']) && $drama_luckybags['drama_lucky_bags']) {
                foreach ($drama_luckybags['drama_lucky_bags'] as $drama_id => $drama_lucky_bag) {
                    foreach ($dramas as &$drama) {
                        if ((int)$drama_id === $drama['id']) {
                            $drama['live_lucky_bag'] = $drama_lucky_bag;
                            break;
                        }
                    }
                }
            }
        } catch (Exception $e) {
            $msg = '综合搜索剧集 ' . Json::encode(array_column($dramas, 'id')) . ' 时获取福袋信息出错：';
            Yii::error($msg . $e->getMessage(), __METHOD__);
            // PASS
        }
    }

    /**
     * 获取剧集所属 IPR ID
     *
     * @param int $drama_id 剧集 ID
     * @return int IPR ID
     */
    public static function getDramaIprId(int $drama_id)
    {
        $ipr_info_key = MUtils::generateCacheKey(KEY_DRAMA_IPR_INFO, $drama_id);
        $ipr_info = Yii::$app->memcache->get($ipr_info_key);
        if ($ipr_info) {
            $ipr_info = Json::decode($ipr_info);
            if (isset($ipr_info['ipr_id'])) {
                return $ipr_info['ipr_id'];
            }
        }
        $ipr_id = 0;
        try {
            // 过审剧集获取 IPR 信息
            $drama = Yii::$app->serviceRpc->getDramaInfo($drama_id);
            if ($drama['drama']) {
                $ipr_id = $drama['drama']['ipr_id'];
            }
            Yii::$app->memcache->set($ipr_info_key, Json::encode(['ipr_id' => $ipr_id]), TEN_MINUTE);
        } catch (Exception $e) {
            Yii::error('获取剧集 ' . $drama_id . ' 信息 rpc 接口出错: ' . $e->getMessage(), __METHOD__);
            // PASS
        }
        return $ipr_id;
    }

    /**
     * 获取单个福袋信息
     *
     * @param array $drama_ids 剧集 IDs, 当 $ipr_id 为 0 时按照 $drama_ids 中元素顺序返回第一个福袋信息
     * @param int $ipr_id 剧集所属 IPR ID, 当 $ipr_id 不为 0 时获取 $ipr_id 下福袋信息
     * @return mixed|null
     */
    public static function getDramaLuckybag(array $drama_ids, int $ipr_id)
    {
        $ipr_ids = $ipr_id ? [$ipr_id] : null;
        $param_drama_ids = $ipr_id ? null : $drama_ids;
        try {
            $luckybags = Yii::$app->liveRpc->getDramaLuckybags($ipr_ids, $param_drama_ids);
            if (!$luckybags) {
                return null;
            }
            if ($ipr_id) {
                $ipr_luckybag = $luckybags['ipr_lucky_bags'][(string)$ipr_id] ?? null;
                if ($ipr_luckybag) {
                    return $ipr_luckybag;
                }
            } else {
                if (isset($luckybags['drama_lucky_bags'])) {
                    // 取剧集列表里的第一个关联的福袋
                    foreach ($drama_ids as $drama_id) {
                        $drama_id_str = (string)$drama_id;
                        if (key_exists($drama_id_str, $luckybags['drama_lucky_bags'])) {
                            return $luckybags['drama_lucky_bags'][$drama_id_str];
                        }
                    }
                }
            }
        } catch (Exception $e) {
            $msg = '获取剧集 ' . Json::encode($drama_ids) . ' IPR ' . $ipr_id . ' 福袋信息 rpc 接口出错: ';
            Yii::error($msg . $e->getMessage(), __METHOD__);
            // PASS
        }
        return null;
    }

    public static function checkRankPeriod($period)
    {
        if (in_array($period, [self::RANK_PERIOD_WEEK, self::RANK_PERIOD_MONTH, self::RANK_PERIOD_ALL])) {
            return true;
        }
        return false;
    }

    /**
     * 获取剧集榜单无数据情况下的提示
     *
     * @param integer $period 榜单周期
     * @return string 提示信息
     * @throws HttpException
     */
    public static function getDramaRankTip($period)
    {
        $now = $_SERVER['REQUEST_TIME'];
        switch ($period) {
            case self::RANK_PERIOD_WEEK:
                $time = strtotime('next week Monday', $now);
                $day = date('n 月 j 日', $time);
                $tip = "榜单将于 {$day} 零时更新，敬请期待";
                break;
            case self::RANK_PERIOD_MONTH:
                $time = strtotime('first day of next month midnight', $now);
                $day = date('n 月 j 日', $time);
                $tip = "榜单将于 {$day} 零时更新，敬请期待";
                break;
            case self::RANK_PERIOD_ALL:
                $tip = '快去给你喜欢的广播剧打赏吧~';
                break;
            default:
                throw new \Exception('参数错误');
        }
        return $tip;
    }

    /**
     * 根据榜单类型及周期获取时间范围
     *
     * @param integer $type 类型（0 为剧集榜、1 为用户榜）
     * @param integer $period 榜单周期（0 为七日、1 为月、2 为总时间）
     * @return array 时间范围
     * @throws HttpException
     */
    public static function getTimeSpan($type, $period)
    {
        $now = $_SERVER['REQUEST_TIME'];
        switch ($period) {
            case self::RANK_PERIOD_WEEK:
                if (self::RANK_TYPE_USER === $type) {
                    // 用户七日榜（七日前此刻到现在）
                    $to_time = $_SERVER['REQUEST_TIME'];
                    $from_time = strtotime('-7 day', $to_time) + 1;
                } else {
                    // 剧集上周榜（上周一到上周日）
                    $from_time = strtotime('last week Monday', $now);
                    $to_time = strtotime('+7 day', $from_time) - 1;
                }
                break;
            case self::RANK_PERIOD_MONTH:
                $FIRST_DAY_OF_MONTH = strtotime('first day of this month midnight', $now);
                if (self::RANK_TYPE_USER === $type) {
                    // 用户当月榜
                    $to_time = $now;
                    $from_time = $FIRST_DAY_OF_MONTH;
                } else {
                    // 剧集上月榜
                    $to_time = $FIRST_DAY_OF_MONTH - 1;
                    $from_time = strtotime('-1 month', $to_time) + 1;
                }
                break;
            case self::RANK_PERIOD_ALL:
                $to_time = $now;
                $from_time = 0;
                break;
            default:
                throw new \Exception('参数错误');
                break;
        }
        return [$from_time, $to_time];
    }

    private static function getRewardUserDetails($user_ids, $drama_id)
    {
        if (empty($user_ids)) {
            return [];
        }
        $users = Mowangskuser::getRewardUsers($user_ids);
        $msgs = RewardMessage::find()->select('user_id, message')
            ->where(['user_id' => $user_ids, 'drama_id' => $drama_id])->orderBy('user_id, create_time ASC')
            ->asArray()->all();
        $user_msg_map = array_column($msgs, 'message', 'user_id');

        return array_map(function ($item) use ($user_msg_map) {
            $msg = RewardMessage::getDefaultMessage();
            if (isset($user_msg_map[$item['id']])) {
                $msg = $user_msg_map[$item['id']] ?: RewardMessage::getDefaultMessage();
            }
            $item['message'] = $msg;
            return $item;
        }, $users);
    }

    private static function mergeRewardData($type, $ranks, $details)
    {
        $details = array_column($details, null, 'id');
        $result = array_reduce($ranks, function ($result, $item) use ($details, $type) {
            $id = $type ? $item['user_id'] : $item['drama_id'];
            if ($detail = $details[$id] ?? []) {
                unset($item['drama_id'], $item['user_id']);
                $result[$id] = array_merge($detail, $item);
            }
            return $result;
        }, []);
        $result = array_values($result);
        return $result;
    }

    /**
     * 获取榜单
     *
     * @param integer $type 类型（0 为剧集榜、1 为用户榜）
     * @param integer $period 榜单周期（0 为周、1 为月、2 为总时间）
     * @param integer $count 长度
     * @param integer $drama_id 剧集 ID
     * @return array 榜单结果
     * @throws HttpException
     */
    public static function getRewardRankList($type, $period, $count = self::RANK_ITEM_LENTH, $drama_id = 0)
    {
        [$from_time, $to_time] = self::getTimeSpan($type, $period);

        if (self::RANK_TYPE_USER === $type) {
            // 用户榜
            $ranks = TransactionLog::getUserRewardRankList($from_time, $to_time, $count, $drama_id);
            $user_ids = array_column($ranks, 'user_id');
            $details = self::getRewardUserDetails($user_ids, $drama_id);
        } else {
            // 剧集榜
            $ranks = MDramaRewardRanks::getRankAllList($count);
            $details = [];
            if (!empty($ranks)) {
                $details = self::rpc('api/drama-rank',
                    ['drama_ids' => array_column($ranks, 'drama_id')]);
            }
        }

        return self::mergeRewardData($type, $ranks, $details);
    }

    /**
     * 获取剧集打赏榜单
     *
     * @param integer $period 榜单周期（0 为周、1 为月、2 为总时间）
     * @param integer $page 第几页
     * @param integer $page_size 每页个数
     * @param integer $user_id 用户 ID
     * @return ReturnModel 榜单结果
     */
    public static function getDramaRewardRankList($period, $page = 1, $page_size = self::RANK_ITEM_LENTH, $user_id = 0)
    {
        $ranks = [];
        if (in_array($period, [self::RANK_PERIOD_WEEK, self::RANK_PERIOD_MONTH])) {
            // 剧集打赏周榜和月榜数据是在 backend 定时任务生成（/drama/drama-reward-rank 每周一生成周榜/每月一号生成月榜）
            $redis = Yii::$app->redis;
            $key = $redis->generateKey(KEY_REWARD_DRAMA_RANKS_OSS_PATH, $period);
            if ($remote_path = $redis->get($key)) {
                $ranks = json_decode(Yii::$app->storage->download($remote_path), true);
            }
        } elseif ($period === self::RANK_PERIOD_ALL) {
            $ranks = self::getDramaRewardRankListFromCache();
        } else {
            throw new Exception('参数错误');
        }
        $data = array_slice($ranks, ($page - 1) * $page_size, $page_size);
        if (Yii::$app->equip->getOs() === Equipment::Web) {
            $drama_ids_subscribed = [];
            if ($user_id && $data) {
                $drama_ids_subscribed = self::getSubscribedDramaIds(array_column($data, 'id'), $user_id);
            }
            $data = array_map(function ($item) use ($drama_ids_subscribed) {
                $item['like'] = 0;
                if ($drama_ids_subscribed && in_array($item['id'], $drama_ids_subscribed)) {
                    $item['like'] = 1;
                }
                unset($item['thumbnail'], $item['shortintro'], $item['coin']);
                return $item;
            }, $data);
        } else {
            $data = array_map(function ($item) {
                $item['cover'] = $item['thumbnail'] ?: $item['cover'];
                $item['abstract'] = $item['shortintro'] ?: $item['abstract'];
                unset($item['thumbnail'], $item['shortintro'], $item['coin']);
                return $item;
            }, $data);
        }
        $return = ReturnModel::getPaginationData($data, count($ranks), $page, $page_size);
        return $return;
    }

    /**
     * 获取剧集打赏总榜数据
     *
     * @return array
     */
    public static function getDramaRewardRankListFromCache()
    {
        $memcache = Yii::$app->memcache;
        $key = MUtils::generateCacheKey(KEY_REWARD_DRAMA_RANKS, self::RANK_PERIOD_ALL);
        if (!$ranks = json_decode($memcache->get($key), true)) {
            $ranks = self::getRewardRankList(self::RANK_TYPE_DRAMA, self::RANK_PERIOD_ALL,
                RewardDramaRanks::RANK_ITEM_LENGTH_ARCHIVE);
            $ranks = array_slice($ranks, 0, self::RANK_ITEM_LENTH);
            $memcache->set($key, Json::encode($ranks), HALF_HOUR);
        }
        return $ranks;
    }

    /**
     * 获取剧集打赏排名
     *
     * @param integer $drama_id 剧集 ID
     * @param integer $period 榜单周期（0 为上周、1 为上月）
     * @return integer 排名（若为 0 则代表暂未上榜）
     */
    public static function getDramaRewardRank($drama_id, $period = self::RANK_PERIOD_WEEK)
    {
        // 剧集榜单上周（月）的时间区间
        [$from_time, $to_time] = self::getTimeSpan(self::RANK_TYPE_DRAMA, $period);
        // 生成排名时的本周（月）时间区间
        if (self::RANK_PERIOD_WEEK === $period) {
            $from_time = strtotime('+1 week', $from_time);
            $to_time = strtotime('+1 week', $to_time);
        } else {
            $from_time = strtotime('+1 month', $from_time);
            $to_time = strtotime('+1 month', $to_time);
        }
        // RewardDramaRanks 中 create_time 是运行定时任务时的时刻
        // 定时任务 ./yii drama/drama-reward-rank 生成的是上周（月）的剧集排名
        // 通过 self::getTimeSpan() 获取的是剧集上周的时间区间
        // 故要获取本周（月）生成的上周（月）的剧集排名，需要对 self::getTimeSpan() 的时间区间加上 1 周（月）的时间
        return (int)RewardDramaRanks::find()->select('rank')->where(['drama_id' => $drama_id, 'type' => $period])
            ->andWhere(
                'create_time BETWEEN :from_time AND :to_time',
                [':from_time' => $from_time, ':to_time' => $to_time]
            )
            ->scalar();
    }

    /**
     * 获取用户打赏榜单
     *
     * @param integer $drama_id 剧集 ID
     * @param integer $period 榜单周期（0 为七日内、1 为本月、2 为总时间）
     * @param integer $user_id 用户 ID
     * @param integer $count 个数
     * @return array 榜单结果
     */
    public static function getUserRewardRankList($drama_id, $period, $user_id, $count = self::RANK_ITEM_LENTH)
    {
        $ranks = self::getUserRewardRankListFromCache($drama_id, $period, self::RANK_ITEM_LENTH);

        $user_status = null;
        if ($user_id) {
            $redis = Yii::$app->redis;
            $key = $redis->generateKey(KEY_REWARD_DRAMA_USER_COUNT, $drama_id);
            $last_reward_time = $redis->zScore($key, $user_id);

            [$from_time, $to_time] = self::getTimeSpan(self::RANK_TYPE_USER, $period);
            $coin = TransactionLog::getUserRewardCoin($from_time, $to_time, $user_id, $drama_id);

            $user_ids = array_column($ranks, 'id');
            // 若用户刚刚打赏，同时在上榜或满足上榜的条件则在缓存中加入该用户的数据
            if ($_SERVER['REQUEST_TIME'] - $last_reward_time < HALF_HOUR) {
                $pos = array_search($user_id, $user_ids);
                if ($pos === false) {
                    $pos = count($ranks);
                }
                if ($pos !== false) {
                    if (!isset($ranks[$pos])) {
                        $user = Mowangskuser::getRewardUsers($user_id)[0];
                        $user_name = $user['username'];
                        $avatar = $user['avatar'];
                    } else {
                        $user_name = $ranks[$pos]['username'];
                        $avatar = $ranks[$pos]['avatar'];
                    }
                    $message = RewardMessage::find()->select('message')
                        ->where(['drama_id' => $drama_id, 'user_id' => $user_id])->orderBy('create_time DESC')
                        ->limit(1)->scalar() ?: RewardMessage::getDefaultMessage();
                    $ranks[$pos] = [
                        'id' => $user_id,
                        'username' => $user_name,
                        'avatar' => $avatar ?: Yii::$app->params['defaultAvatarUrl'],
                        'message' => $message,
                        'coin' => $coin,
                        'ctime' => $last_reward_time,
                    ];
                    usort($ranks, function ($a, $b) {
                        if ($a['coin'] !== $b['coin']) {
                            return $a['coin'] < $b['coin'];
                        } else {
                            return $a['ctime'] < $b['ctime'];
                        }
                    });
                    $ranks = array_slice($ranks, 0, self::RANK_ITEM_LENTH);
                }
            }
            // 用户状态
            $position = 0;
            if ($coin && !empty($ranks)) {
                if ($user_id === $ranks[0]['id']) {
                    $coin_remain = 0;
                    $position = 1;
                } else {
                    $user_ids = array_column($ranks, 'id');
                    if (in_array($user_id, $user_ids)) {
                        $pre = array_search($user_id, $user_ids) - 1;
                        $coin_remain = $ranks[$pre]['coin'] - $coin + 1;
                        $position = array_search($user_id, $user_ids) + 1;
                    } else {
                        $coin_remain = end($ranks)['coin'] - $coin + 1;
                    }
                }
            } else {
                $coin_remain = DramaRewardMenu::REWARD_MIN_COIN;
                if (count($ranks) >= self::RANK_ITEM_LENTH) {
                    $coin_remain = end($ranks)['coin'] + 1;
                }
            }
            $user = Mowangskuser::getRewardUsers($user_id);
            $user_status = [
                'id' => $user_id,
                // 用户主用户表数据不存在时，昵称返回注销用户默认昵称，头像返回默认头像
                'username' => $user[0]['username'] ?? Mowangskuser::DELETED_USERNAME,
                'avatar' => $user[0]['avatar'] ?? Yii::$app->params['defaultAvatarUrl'],
                'coin' => $coin,
                'position' => $position,
                'coin_remain' => $coin_remain,
            ];
        }
        return [
            'ranks' => array_slice($ranks, 0, $count),
            'user_status' => $user_status,
        ];
    }

    /**
     * 从缓存中获取用户打赏榜单
     * 当缓存过期时，会生成三个榜单（七日、月、总榜）的缓存数据，数据格式如下：
     *   [{
     *     "id": 346287,
     *     "username": "苍天啊苍天",
     *     "avatar": "https:\/\/static.missevan.com\/avatars\/201803\/26\/c8ba0f80fa029e79a7d582f99d640732.png",
     *     "message": "TA 傲娇地打赏了本剧…",
     *     "coin": 65,
     *     "ctime": 1530080590
     *   }, {
     *     "id": 452370,
     *     "username": "蓓蓓酱",
     *     "avatar": "https:\/\/static.missevan.com\/avatars\/201702\/17\/12329ee936ebb42818902e163bc213631.jpg",
     *     "message": "TA 傲娇地打赏了本剧…",
     *     "coin": 23,
     *     "ctime": 1527955200
     *   }]
     * 缓存时间规则：七日榜、月榜和总榜统一缓存 5 分钟且更新时间统一
     * @param integer $drama_id 剧集 ID
     * @param integer $period 榜单周期（0 为七日内、1 为本月、2 为总时间）
     * @param integer $count 个数
     * @return array 榜单结果
     */
    public static function getUserRewardRankListFromCache($drama_id, $period, $count = self::RANK_ITEM_LENTH)
    {
        $ranks_key = MUtils::generateCacheKey(KEY_REWARD_USER_RANKS, $period, $drama_id);
        $lock_cache_expire_time = MUtils::generateCacheKey(LOCK_REWARD_USER_RANKS_TIME, $drama_id);
        $lock_create_cache = MUtils::generateCacheKey(LOCK_CREATING_REWARD_USER_RANKS, $drama_id);

        // 榜单数据缓存时间
        $rank_cache_time = TEN_MINUTE;
        // 榜单数据缓存失效时间
        $rank_cache_expire_time = FIVE_MINUTE;
        // 生成榜单缓存的加锁时间
        $lock_expire_time = ONE_MINUTE;

        $memcache = Yii::$app->memcache;
        $redis = Yii::$app->redis;
        if (!$redis->lock($lock_cache_expire_time, $rank_cache_expire_time)) {
            // 若加锁失败，则榜单缓存数据未过期，直接取榜单缓存数据返回
            if ($rank_json = $memcache->get($ranks_key)) {
                return self::formatUserRewardRankList($rank_json, $count);
            }
        }
        // 生成新的榜单缓存，并给此行为加一个 1 分钟的 Redis 锁
        $redis = Yii::$app->redis;
        if (!$redis->lock($lock_create_cache, $lock_expire_time)) {
            // 若加锁失败，表明榜单缓存正在生成，此时返回已过期的老榜单缓存数据
            // 否则生成新的榜单缓存，避免缓存穿透的情况
            if ($rank_json = $memcache->get($ranks_key)) {
                return self::formatUserRewardRankList($rank_json, $count);
            }
        }
        try {
            // 本次请求要获取的榜单数据
            $rank = [];
            // 七日榜、月榜、总榜榜单需要同时更新
            foreach ([self::RANK_PERIOD_WEEK, self::RANK_PERIOD_MONTH, self::RANK_PERIOD_ALL] as $v) {
                // 查询对应榜单数据
                $data = self::getRewardRankList(self::RANK_TYPE_USER, $v, $count, $drama_id);
                $data_key = MUtils::generateCacheKey(KEY_REWARD_USER_RANKS, $v, $drama_id);
                // 生成榜单数据缓存
                $memcache->set($data_key, Json::encode($data), $rank_cache_time);
                if ($period === $v) {
                    $rank = $data;
                }
            }
            return $rank;
        } catch (\Exception $e) {
            Yii::error("生成剧集下用户榜单缓存异常：{$e->getMessage()}", __METHOD__);
            $redis->unlock($lock_cache_expire_time);
            throw $e;
        } finally {
            $redis->unlock($lock_create_cache);
        }
    }

    /**
     * 格式化剧集用户榜榜单缓存
     *
     * @param string $rank_json 缓存的 json 字符串
     * @param int $count 榜单排名数
     * @return array 榜单数据
     */
    private static function formatUserRewardRankList(string $rank_json, int $count): array
    {
        if (!$rank_json) {
            return [];
        }
        $ranks = Json::decode($rank_json, true);
        return array_slice($ranks, 0, $count);
    }

    /**
     * 获取七日（或本月、总时间）内打赏的用户前几名
     *
     * @param integer $drama_id 剧集 ID
     * @param integer $period 榜单周期（0 为周、1 为月、2 为总时间）
     * @param integer $count 前多少个
     * @return array 前几名的用户
     */
    public static function getDramaRewardTopUsers($drama_id, $user_id, $period = self::RANK_PERIOD_WEEK,
            $count = self::REWARD_TOP_USER_COUNT)
    {
        return self::getUserRewardRankList($drama_id, $period, $user_id, $count)['ranks'];
    }

    /**
     * 获取对指定剧集打赏的用户数
     *
     * @param integer $drama_id 剧集 ID
     * @param integer $period 时间范围（0 为七日内、1 为本月、2 为总时间）
     * @return integer 用户数
     */
    public static function getRewardUserCount($drama_id, $user_id, $period = self::RANK_PERIOD_ALL)
    {
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_REWARD_DRAMA_USER_COUNT, $drama_id);
        if (!$redis->exists($key)) {
            self::initRewardDramaUser($drama_id);
        }

        if ($period === self::RANK_PERIOD_ALL) {
            // 打赏人数为剧集打赏人数的有序集合缓存中元素个数减去初始化该值时给定的默认元素个数
            $count = $redis->zCard($key) - 1;
        } else {
            [$from_time, $to_time] = self::getTimeSpan(self::RANK_TYPE_USER, $period);
            $count = $redis->zCount($key, $from_time, $to_time);
        }

        return $count;
    }

    /**
     * 获得剧集打赏人数
     *
     * @param int $drama_id 剧集 ID
     * @return array 剧集总打赏人数与七日内打赏人数组成的数组
     */
    public static function getRewardDramaUserCount(int $drama_id, $user_id): array
    {
        $reward_user_num = self::getRewardUserCount($drama_id, $user_id, self::RANK_PERIOD_ALL);
        $one_week_reward_user_num = self::getRewardUserCount($drama_id, $user_id, self::RANK_PERIOD_WEEK);
        return [$reward_user_num, $one_week_reward_user_num];
    }

    /**
     * 初始化打赏剧集的人员及其打赏时间信息
     * 当打赏某剧集用户较多时，初始化可能比较耗时
     *
     * @param int $drama_id 剧集 ID
     */
    public static function initRewardDramaUser(int $drama_id)
    {
        $pass_time = 0;
        $timeout = ONE_MINUTE;
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_REWARD_DRAMA_USER_COUNT, $drama_id);
        $lock_key = md5($key) . $drama_id;
        while (!$redis->exists($key) && $pass_time <= $timeout) {
            if ($redis->lock($lock_key, $timeout)) {
                if ($redis->exists($key)) break;
                // TODO: 迁移到其它地方执行
                $reward_users = TransactionLog::find()
                    ->select('from_id, MAX(confirm_time) as last_time, SUM(all_coin) AS total_reward')
                    ->where(
                        'gift_id = :from_id AND type = :type AND status = :status',
                        [
                            ':from_id' => $drama_id,
                            ':type' => TransactionLog::TYPE_REWARD,
                            ':status' => TransactionLog::STATUS_SUCCESS,
                        ]
                    )
                    ->asArray()
                    ->groupBy('from_id')
                    ->orderBy('total_reward DESC')
                    ->all();
                // 避免没有打赏时创建有序集合失败，给集合一个默认的初始元素
                $elements = [0, 0];
                foreach ($reward_users as $reward_user) {
                    $elements[] = (int)$reward_user['last_time'];
                    $elements[] = (int)$reward_user['from_id'];
                }
                $redis->zAdd($key, ...$elements);
                $redis->unlock($lock_key);
                break;
            }
            $pass_time += 0.5;
            usleep(500000);
        }
    }

    /**
     * 获取 App 首页剧集打赏榜数据
     *
     * @param int $number 需要的榜单排名数目
     * @return array 剧集打赏榜周榜榜单
     */
    public static function getHomepageDramaRewardRank(int $number): array
    {
        if ($number <= 0) {
            return [];
        }
        // 获取广播剧打赏榜周榜
        $week_rank = self::getDramaRewardRankList(self::RANK_PERIOD_WEEK, 1, $number)->Datas;
        return $week_rank;
    }

    /**
     * 获得剧集打赏详情
     *
     * @param int $drama_id 剧集 ID
     * @return array 总打赏人数、七日内打赏人数、剧集排名、前几位打赏者信息组成的数组
     */
    public static function getRewardInfo(int $drama_id, $user_id)
    {
        [$reward_num, $one_week_reward_num] = self::getRewardDramaUserCount($drama_id, $user_id);
        $rank = self::getDramaRewardRank($drama_id, self::RANK_PERIOD_WEEK);
        $reward_users = self::getDramaRewardTopUsers($drama_id, $user_id);
        return [
            'reward_num' => $reward_num,
            'one_week_reward_num' => $one_week_reward_num,
            'rank' => $rank,
            'reward_users' => $reward_users,
        ];
    }

    /**
     * 打赏成功更新缓存数据（若单位时间内某剧打赏数额突破限制则更新剧集总榜缓存，更新某剧打赏人数）
     *
     * @param int $drama_id 剧集 ID
     * @param int $user_id 用户 ID
     * @param int $coin 打赏钻石数
     * @return null
     */
    public static function updateRewardRankCache(int $drama_id, int $user_id, int $coin)
    {
        $redis = Yii::$app->redis;
        // 若剧集半小时内被打赏数额突增则更新剧集总榜缓存
        $counter_key = $redis->generateKey(KEY_REWARD_DRAMA_COIN_LIMIT_COUNTER, $drama_id);
        if (!$redis->exists($counter_key)) $redis->expire($counter_key, HALF_HOUR);
        if ($redis->incrBy($counter_key, $coin) > self::REWARD_COIN_MAX_INCREMENT) {
            $ranks_key = MUtils::generateCacheKey(KEY_REWARD_DRAMA_RANKS, self::RANK_PERIOD_ALL);
            Yii::$app->memcache->delete($ranks_key);
            $redis->del($counter_key);
        }
        // 更新统计人数
        $user_count_key = $redis->generateKey(KEY_REWARD_DRAMA_USER_COUNT, $drama_id);
        if (!$redis->exists($user_count_key)) {
            self::initRewardDramaUser($drama_id);
        }
        $redis->zAdd($user_count_key, $_SERVER['REQUEST_TIME'], $user_id);
    }

    /**
     * 追剧或取消追剧
     *
     * @param int $drama_id 剧集 ID
     * @param int $user_id 用户 ID
     * @param int $type 是否追剧 0：取消追剧；1：追剧；2：切换追剧或取消追剧
     * @return array 追剧或取消追剧详情
     */
    public static function subscribe($drama_id, $user_id, $type)
    {
        $return = self::rpc('api/subscribe', [
            'drama_id' => $drama_id,
            'user_id' => $user_id,
            'type' => $type,
        ]);
        // 追剧或取消追剧时更新用户画像分数
        Persona::setPointsFromSubscribeDrama($return['drama_type'], $user_id, (bool)$type);
        return $return;
    }

    /**
     * 请求剧集数据
     *
     * @param string $api
     * @param array $params 请求参数
     * @return mixed
     */
    public static function rpc(string $api, array $params = [])
    {
        return Yii::$app->tools->requestDrama('/rpc/' . $api, $params);
    }

    /**
     * 播放页获取推荐剧集
     *
     * @param int $sound_id 单音 ID
     * @param int $user_id 用户 ID
     * @param int $free_num 免费剧集推荐位个数
     * @param int $paid_num 付费剧集推荐位个数
     * @return array 返回推荐剧集
     * @todo 之后应通过使用场景来获取推荐剧集而非直接传入数量
     */
    public static function getRecommendDrama(int $sound_id, int $user_id, int $free_num, int $paid_num)
    {
        try {
            $dramas = self::rpc('api/get-recommend-drama', [
                'sound_id' => $sound_id,
                'free_num' => $free_num,
                'paid_num' => $paid_num,
                'sensitive' => (int)Yii::$app->user->isExam
            ]);
        } catch (\Exception $e) {
            // 参数单音 ID 不属于任一剧集时，需要捕获剧集不存在的异常并返回空数组
            $dramas = [];
        }
        if (!empty($dramas)) {
            self::compatibleFillCornerMark($dramas, $user_id);
        }
        $dramas = array_map(function ($drama) {
            $result = [
                'id' => $drama['id'],
                'name' => $drama['name'],
                'front_cover' => $drama['front_cover'],
                'pay_type' => $drama['pay_type'],
                'strategy_id' => $drama['strategy_id'],
                'view_count' => $drama['view_count']
            ];
            // WORKAROUND: 之后不再使用 need_pay 字段，改为使用 corner_mark 字段
            if (isset($drama['need_pay'])) {
                $result['need_pay'] = $drama['need_pay'];
            }
            if (isset($drama['corner_mark'])) {
                $result['corner_mark'] = $drama['corner_mark'];
            }
            return $result;
        }, $dramas);
        return $dramas;
    }

    /**
     * 剧集详情页获取推荐剧集
     *
     * @param int $drama_id 剧集 ID
     * @param int $user_id 用户 ID
     * @param string $scene 推荐场景
     * @return array 返回推荐剧集
     */
    public static function getRecommendById(int $drama_id, int $user_id, string $scene)
    {
        if (self::isSensitiveDrama($drama_id)) {
            // WORKAROUND: 敏感剧集不进行推荐
            return [];
        }
        $dramas = self::rpc('drama/get-recommend-by-id', [
            'drama_id' => $drama_id,
            'scene' => $scene,
        ]);
        if (!empty($dramas)) {
            self::compatibleFillCornerMark($dramas, $user_id);
        }
        return $dramas;
    }

    public static function getSearchSuggest(string $s, int $count = Discovery::SUGGEST_COUNT)
    {
        return Yii::$app->go->suggest($s, Discovery::SEARCH_DRAMA, $count);
    }

    /**
     * 获取单集 ID 集合
     *
     * @param array $episodes 例：["episode" => [[...], [...]], "music" => [[...], [...]], "ft" => [[...]]]
     * @return array
     */
    public static function getEpisodeIds(array $episodes): array
    {
        return array_reduce($episodes, function ($ret, $item) {
            $ep_ids = array_column($item, 'sound_id');
            $ret = array_merge($ret, $ep_ids);
            return $ret;
        }, []);
    }

    /**
     * 获取单集的音频信息
     *
     * @param array $sound_ids
     * @param integer $up_user_id 剧集的 UP 主
     * @param null|integer $user_id 当前登录用户 ID
     * @param boolean $sensitive 是否获取敏感音频
     * @return array
     */
    public static function getSounds(array $sound_ids, int $up_user_id, ?int $user_id, bool $sensitive = false): array
    {
        $checked_statuses = [MSound::CHECKED_PASS];
        if ($sensitive || $up_user_id === $user_id) {
            // UP 主视角也过滤报警音
            $checked_statuses[] = MSound::CHECKED_CONTRACT_EXPIRED;
        }

        $sounds = MSound::find()->select('id, create_time, duration, soundstr, user_id, username, cover_image, intro,
                pay_type, download, view_count, checked')
            ->where(['id' => $sound_ids, 'checked' => $checked_statuses])->asArray()->all();

        // 为单音添加是否需要付费字段
        MSound::checkNeedPay($sounds, $user_id);

        return array_map(function ($item) {
            $item['front_cover'] = MSound::getFrontCoverUrl($item['cover_image']);
            unset($item['cover_image']);
            return $item;
        }, $sounds);
    }

    public static function getExistingEpisodes(array $episodes, array $existing_sound_ids)
    {
        $ret = [];
        foreach ($episodes as $ep) {
            if (in_array($ep['sound_id'], $existing_sound_ids)) {
                $ret[] = $ep;
            }
        }
        return $ret;
    }

    public static function checkEpisodeStatus(array &$episode, array $drama_info)
    {
        // 若单音付费但用户未付费，去掉音频地址
        if ($episode['need_pay'] === MSound::SOUND_UNPAID) {
            unset($episode['soundurl'], $episode['soundurl_32'], $episode['soundurl_64'], $episode['soundurl_128']);
            if (self::DRAMA_UNPAID === $drama_info['need_pay']
                    && MSound::CHECKED_CONTRACT_EXPIRED === (int)$episode['checked']) {
                $episode = null;
            }
        } elseif (MSound::SOUND_FREE === $episode['pay_type']
                && self::DRAMA_UNPAID === $drama_info['need_pay']
                && MSound::CHECKED_CONTRACT_EXPIRED === (int)$episode['checked']) {
            $episode = null;
        }
        if (!is_null($episode)) {
            // 如果两周内用户尚未观看剧集的最后更新的单集则显示为 "NEW" 的样式
            // 文档地址：https://github.com/MiaoSiLa/requirements-doc/pull/47
            $episode['status'] = self::EPISODE_STATUS_COMMON;
            if (self::INTEGRITY_NAME_SERIALIZING === $drama_info['integrity'] && $episode['name'] === $drama_info['newest']
                    && $_SERVER['REQUEST_TIME'] - $drama_info['lastupdate_time'] < 2 * ONE_WEEK) {
                $episode['status'] = self::EPISODE_STATUS_NEW;
            }
        }
    }

    /**
     * 处理单集的详情信息
     *
     * @param array $episodes_group 例：["episode" => [[...], [...]], "music" => [[...], [...]], "ft" => [[...]]]
     * @param array $sounds
     * @param array $drama_info
     */
    public static function processEpisodes(array &$episodes_group, array $sounds, array $drama_info)
    {
        $existing_sound_ids = array_column($sounds, 'id');
        foreach ($episodes_group as &$episodes) {
            $episodes = self::getExistingEpisodes($episodes, $existing_sound_ids);
            // 添加单音信息
            $episodes = array_map(function ($ep) use ($sounds, $drama_info) {
                foreach ($sounds as $sound) {
                    $sound['id'] = (int)$sound['id'];
                    if ($ep['sound_id'] === $sound['id']) {
                        $sound['download'] = (int)$sound['download'];
                        $ep['eid'] = $ep['id'];
                        $ep = array_merge($ep, $sound);
                        $ep['pay_type'] = (int)$ep['pay_type'];
                        $ep['need_pay'] = $sound['need_pay'];
                        $ep['view_count'] = (int)$sound['view_count'];
                        $ep['price'] = 0;
                        if (self::PAY_TYPE_EPISODE === $drama_info['pay_type']) {
                            $ep['price'] = MSound::SOUND_FREE !== $ep['pay_type'] ? $drama_info['episode_price'] : 0;
                        }
                        break;
                    }
                }
                self::checkEpisodeStatus($ep, $drama_info);

                return $ep;
            }, $episodes);
            $episodes = array_values(array_filter($episodes));
        }
    }

    /**
     * 获取剧集详情
     *
     * @param integer $drama_id 剧集 ID
     * @param integer $user_id 用户 ID
     * @param integer $persona_module 画像 ID
     * @return array 例：["drama" => [], "episodes" => [], "cvs" => [], "tags" => [], "reward_info" => []]
     * @throws HttpException
     */
    public static function getDramaDetail(int $drama_id, int $user_id, int $persona_module): array
    {
        $thumbnail = (int)Yii::$app->equip->isFromMiMiApp();
        // 剧集信息
        $return = self::rpc('api/get-drama-detail', [
            'drama_id' => $drama_id,
            'user_id' => $user_id,
            'thumbnail' => $thumbnail
        ]);
        if (Equipment::isAppOlderThanVipVersion() && $return['drama']['pay_type'] === Drama::PAY_TYPE_DRAMA
                && key_exists('vip_discount', $return['drama']) && MUserVip::isVipUser($user_id)) {
            // WORKAROUND: 对于不支持会员的老版本，若为整剧付费且对当前会员用户打折，则直接返回打折后的价格
            // 对于单集付费的剧集，由于打折价格需要汇总购买单集的总价后再打折，所以老版本只能下发原价，此时显示上会为原价，但在实际发生支付的时候按打折价购买
            $return['drama']['price'] = $return['drama']['vip_discount']['price'];
        }
        // 判断用户是否需要付费
        self::checkNeedPay($return['drama'], $user_id);
        $drama_arr = [$return['drama']];
        self::fillCornerMark($drama_arr, (int)$user_id);
        $return['drama'] = $drama_arr[0];
        // 检查剧集状态
        self::checkStatus($return['drama'], $user_id);
        $return['reward_info'] = null;
        if (self::REWARD_DISABLE !== $return['drama']['rewardable']) {
            $return['reward_info'] = self::getRewardInfo($return['drama']['id'], $user_id);
        }
        // CV 信息
        Mowangsksoundseiy::processCVDetails($return['cvs']);
        if (!Equipment::isAppOlderThan('4.9.3', '5.7.8')) {
            // WORKAROUND: iOS >= 4.9.3 版本及 Android >= 5.7.8 版本时，置顶两个开播的声优
            $return['cvs'] = Mowangsksoundseiy::topLiveCvs($return['cvs']);
        }
        if (!Equipment::isAppOlderThan('4.3.3', '5.2.4')) {
            // WORKAROUND: iOS 大于 4.3.2 版本及 Android 大于 5.2.3 版本时，处理周边信息
            $return['derivatives'] = self::getDerivativeInfo($return['derivatives']);
        } else {
            unset($return['derivatives']);
        }
        // UP 主信息
        $up_user_id = array_key_exists('user_id', $return['drama']) ? $return['drama']['user_id'] : 0;
        if ($up_user_id) {
            $return['user'] = Mowangskuser::getUpUserInfo($up_user_id, $user_id);
        }
        // 单集信息
        $return['episodes'] = self::getTypeEpisodes($return['episodes']);
        if ($return['drama']['style'] === self::STYLE_INTERACTIVE
                && MSoundNode::isUsableVersion()) {
            // WORKAROUND: 若为互动广播剧且为支持互动剧版本，不返回单集信息，返回节点绑定的音频 ID
            // 第一个单集对应音频 ID 即为节点绑定音频 ID
            $return['drama']['node_sound_id'] = $return['episodes']['episode'][0]['sound_id'] ?? 0;
            // 将单集名称更新为“立即播放”
            $return['episodes']['episode'][0]['name'] = '立即播放';
        }
        // 获取排行榜信息
        if ($persona_module && $rank = MHomepageRank::getDramaDetailRank($drama_id, $persona_module)) {
            $return['drama']['rank'] = $rank;
        }
        if (!$sound_ids = self::getEpisodeIds($return['episodes'])) {
            return $return;
        }

        // WORKAROUND: 临时方案，将 $sensitive 改成整剧已购
        $sensitive = $return['drama']['need_pay'] === self::DRAMA_PAID;
        $sounds = self::getSounds($sound_ids, $return['drama']['user_id'], $user_id, $sensitive);
        self::processEpisodes($return['episodes'], $sounds, $return['drama']);
        $return['drama']['like'] = (int)$return['drama']['like'];
        // 获取可显示的季度剧集信息
        self::getSeasonDramas($return['seasons'], $user_id);
        // 获取单集付费条文案
        if (self::shouldEpisodePriceInfo($return, $user_id)) {
            $hairsp = MUtils2::HAIR_SPACE;
            $return['drama']['episode_price_info'] = "单集付费 {$return['drama']['episode_price']} 钻{$hairsp}/{$hairsp}集，随你畅听";
        }
        return $return;
    }

    /**
     * 是否应该展示单集付费条
     *
     * @param array $drama_detail 剧集详情信息
     * @param int $user_id 用户 ID
     * @return boolean
     */
    private static function shouldEpisodePriceInfo(array $drama_detail, int $user_id)
    {
        if ($drama_detail['drama']['pay_type'] !== self::PAY_TYPE_EPISODE
                || $drama_detail['drama']['checked'] === self::CHECKED_CONTRACT_EXPIRED) {
            // 不是单集付费类型或状态是【合约期满下架】的剧集不展示单集付费条
            return false;
        }
        if ($drama_detail['drama']['integrity'] === self::INTEGRITY_NAME_SERIALIZING) {
            // 【未完结】状态时常驻展示单集付费条
            return true;
        }
        if (in_array($drama_detail['drama']['integrity'], [self::INTEGRITY_NAME_END, self::INTEGRITY_NAME_ONE])) {
            // 【已完结】【全一期】状态时，需判断用户是否所有付费单集均已购买（游客视为全未购买）
            // 若全部已购买，则隐藏单集付费条，否则需要展示单集付费条
            if (!$user_id) {
                return true;
            }
            foreach ($drama_detail['episodes'] as $episodes) {
                foreach ($episodes as $episode) {
                    if ($episode['need_pay'] === MSound::SOUND_UNPAID) {
                        // 有未付费的单集则需要展示单集付费条
                        return true;
                    }
                }
            }
        }
        return false;
    }

    public static function getCoverUrl(?string $drama_cover_url)
    {
        return $drama_cover_url ? Yii::$app->params['dramaCoverUrl'] . $drama_cover_url
            : Yii::$app->params['defaultCoverUrl'];
    }

    /**
     * 获取（完善）剧集周边信息
     *
     * @param array $derivatives 剧集周边
     * @return array 完整的剧集周边信息
     */
    public static function getDerivativeInfo(array $derivatives)
    {
        if (empty($derivatives)) {
            return $derivatives;
        }
        if (Yii::$app->equip->isFromMiMiApp()) {
            return [];
        }
        $goods_ids = array_map(function ($derivative) {
            if ($derivative['type'] === self::DERIVATIVE_TYPE_GOODS) {
                return $derivative['element_id'];
            }
        }, $derivatives);
        $goods = Goods::find()
            ->alias('t1')
            ->select('t1.id, t1.title, t1.like_num, MIN(t2.price) AS price')
            ->leftJoin(GoodsSpecification::tableName() . ' AS t2', 't1.id = t2.goods_id')
            ->where([
                't1.id' => $goods_ids,
                't1.status' => Goods::STATUS_SALE,
                // 商品最低价包含上架及下架的规格价格
                't2.status' => [GoodsSpecification::STATUS_SALE, GoodsSpecification::STATUS_OFF_THE_SHELVE],
            ])
            ->groupBy('t1.id')
            ->asArray()
            ->all();
        $goods = array_column($goods, null, 'id');
        $derivative_info = [];
        foreach ($derivatives as $derivative) {
            if ($derivative['type'] === self::DERIVATIVE_TYPE_GOODS) {
                if (!array_key_exists($derivative['element_id'], $goods)) {
                    // 周边关联商品不存在或下架时，去掉该周边
                    continue;
                }
                // 更新商品周边的信息
                $derivative['price'] = (string)Balance::profitUnitConversion(
                    $goods[$derivative['element_id']]['price'], Balance::CONVERT_FEN_TO_YUAN);
                $derivative['like_num'] = (int)$goods[$derivative['element_id']]['like_num'];
                $derivative['url'] = Goods::getGoodsDetailLink($derivative['element_id']);
            }
            $derivative['url'] = MUtils::getUsableAppLink($derivative['url']);
            // 去掉客户端不需要的字段
            unset($derivative['element_id']);
            $derivative_info[] = $derivative;
        }
        return $derivative_info;
    }

    /**
     * 获取剧集索引
     *
     * @return array
     */
    public static function getDramaTag()
    {
        $index = self::rpc('api/drama-index');
        return [
            'index' => $index,
        ];
    }

    /**
     * 获取分区首页推荐内容
     *
     * @param int $drama_catalog_id 分区 ID
     * @param int $user_id 用户 ID
     * @return array
     */
    public static function getCatalogHomepage(int $drama_catalog_id, int $user_id)
    {
        $redis = Yii::$app->redis;
        $catalog_homepage_key = $redis->generateKey(KEY_CATALOG_DRAMA_HOMEPAGE, $drama_catalog_id);
        // 分区顶部轮播图，热门推荐（新作速递），付费精品，人气周榜（免费周榜），大家都在看，分区轮播通栏相关缓存在广播剧分区后台生成
        $catalog_homepage = $redis->hGetAll($catalog_homepage_key);
        $return = [
            'catalog_name' => self::CATALOG_NAMES[$drama_catalog_id] ?? '剧集首页',
            'hot_recommend' => [
                'title' => $catalog_homepage['hot_recommend_title'] ?? '热门推荐',
                'elements' => [],
            ],
            'classic_paid' => [
                'title' => '付费精品',
                'elements' => [],
                // 点更多时进入「剧集索引」页并选中「付费」标签
                // WORKAROUND: 因为剧集索引页删除了分类筛选项，之后分类筛选项补回后，需要调整成 1_0_0_0_0_2
                'more' => [
                    'drama_filters' => '1_0_0_0_2'
                ],
            ],
            'weekly_rank' => [
                'title' => self::DRAMA_CATALOG_ID_CN_RADIO_DRAMA === $drama_catalog_id ? '免费周榜' : '人气周榜',
                'elements' => [],
            ],
        ];
        if (self::DRAMA_CATALOG_ID_CN_RADIO_DRAMA === $drama_catalog_id) {
            // 对于中文广播剧，需要下发"时间表"入口信息
            // TODO: 完善 open_url 的获取
            $return['schedule'] = ['open_url' => ''];
        }
        // 获取分区 banner
        $return['banners'] = Catalog::getCatalogBanner(MRecommendedElements::MODULE_TYPE_DRAMA_BANNER,
            $drama_catalog_id);
        // 大家都在看
        $return['hot_words'] = array_key_exists('hot_words', $catalog_homepage) ?
            Json::decode($catalog_homepage['hot_words']) : [];
        // WORKAROUND：对老版本数据做版本兼容处理
        if (Equipment::isAppOlderThan('4.5.2', '5.4.2')) {
            $return['hot_words'] = array_map(function ($value) {
                // 处理 iOS < 4.5.2 或 Android < 5.4.2 版本的旧数据信息
                if (!is_array($value)) {
                    return $value;
                }
                // 处理 iOS < 4.5.2 或 Android < 5.4.2 版本的新数据信息
                return $value['title'] ?? '';
            }, $return['hot_words']);
        } else {
            $return['hot_words'] = array_map(function ($value) {
                // 处理 iOS >= 4.5.2 或 Android >= 5.4.2 版本的旧数据信息
                if (!is_array($value)) {
                    return [
                        'title' => $value,
                        'url' => 'missevan://search?keyword=' . urlencode($value),
                    ];
                }
                // 处理 iOS >= 4.5.2 或 Android >= 5.4.2 版本的新数据信息
                return [
                    'title' => $value['title'],
                    'url' => MUtils::getUsableAppLink($value['url']),
                ];
            }, $return['hot_words']);
        }
        // 人气周榜纯爱前十榜单数据
        $PAGE_SIZE = 10;

        $weekly_rank = self::getWeeklyRank($drama_catalog_id, $user_id, self::INTEGRITY_NAME_END,
            self::TYPE_END, 1, $PAGE_SIZE);
        self::compatibleFillCornerMark($weekly_rank['Datas'], $user_id);
        $return['weekly_rank']['elements'] = $weekly_rank['Datas'];
        // 分区轮播通栏
        // TODO: 轮播通栏之后需要改为从数据库直接取
        $return['extra_banners'] = array_key_exists('extra_banners', $catalog_homepage) ?
            Json::decode($catalog_homepage['extra_banners']) : null;
        $return['extra_banners'] = array_map(function ($banners) {
            return array_map(function ($banner) {
                $banner['url'] = MUtils::getUsableAppLink($banner['url']);
                return $banner;
            }, $banners);
        }, $return['extra_banners'] ?: []) ?: null;
        // 热门推荐，付费精品
        $hot_recommend_drama_ids = array_key_exists('hot_recommend_drama', $catalog_homepage) ?
            explode(',', $catalog_homepage['hot_recommend_drama']) : [];
        // 当前仅剧集分区首页下存在“精品付费”模块
        $classic_paid_drama_ids = [];
        if ($drama_catalog_id === self::DRAMA_CATALOG_ID_CN_RADIO_DRAMA) {
            $classic_paid_drama_ids = array_key_exists('classic_paid_drama', $catalog_homepage) ?
                explode(',', $catalog_homepage['classic_paid_drama']) : [];
        }
        $all_drama_ids = array_values(array_unique(array_merge($hot_recommend_drama_ids, $classic_paid_drama_ids)));
        if (empty($all_drama_ids) || !MUtils2::isUintArr($all_drama_ids)) {
            Yii::error('热门推荐或付费精品设置的剧集 ID 不正确，分区 ID: ' . $drama_catalog_id, __METHOD__);
            return $return;
        }
        $all_dramas = self::rpc('drama/get-drama-by-ids', ['drama_ids' => $all_drama_ids]);
        $all_dramas = array_map(function ($item) {
            $item['front_cover'] = $item['cover'];
            unset($item['cover']);
            return $item;
        }, $all_dramas);
        self::compatibleFillCornerMark($all_dramas, $user_id);
        $id_drama_arr = array_column($all_dramas, null, 'id');
        foreach ($hot_recommend_drama_ids as $id) {
            if (!key_exists($id, $id_drama_arr)) {
                // 若剧集未查询到（下架、内容敏感等原因造成），则记录到日志提醒更换
                Yii::error("PGC 分区首页“热门推荐”中含有下架或敏感内容剧集，剧集 ID：{$id}", __METHOD__);
                continue;
                // PASS
            }
            $return['hot_recommend']['elements'][] = $id_drama_arr[$id];
        }
        foreach ($classic_paid_drama_ids as $id) {
            if (!key_exists($id, $id_drama_arr)) {
                // 若剧集未查询到（下架、内容敏感等原因造成），则记录到日志提醒更换
                Yii::error("PGC 分区首页“付费精品”中含有下架或敏感内容剧集，剧集 ID：{$id}", __METHOD__);
                continue;
                // PASS
            }
            $return['classic_paid']['elements'][] = $id_drama_arr[$id];
        }
        return $return;
    }

    /**
     * 获取广播剧人气周榜
     *
     * @param int $catalog_id 剧集分区 ID
     * @param int $user_id 用户 ID
     * @param int $integrity 完结度 1：未完结；2：完结；5：全一期和微小剧
     * @param int $type 性向 3：全年龄；4：纯爱；5：双女主；6：言情
     * @param int $page 当前页
     * @param int $page_size 每页个数
     * @return array
     */
    public static function getWeeklyRank(int $catalog_id, int $user_id, int $integrity, int $type, int $page = 1,
            int $page_size = PAGE_SIZE_20)
    {
        if ($catalog_id !== self::DRAMA_CATALOG_ID_CN_RADIO_DRAMA) {
            // 当前仅广播剧分区下的分类有各自的榜单，其余分区仅可获取总榜榜单数据
            $integrity = 0;
        }
        $return_models = ReturnModel::empty($page, $page_size);
        $return = [
            'tabs' => [],
            'Datas' => $return_models->Datas,
            'pagination' => $return_models->pagination,
        ];
        $redis = Yii::$app->redis;
        $weekly_rank_key = $redis->generateKey(KEY_CATALOG_HOMEPAGE_WEEKLY_RANK_DRAMA, $catalog_id);
        $drama_ids_str = $redis->hGet($weekly_rank_key, $integrity);
        if (!$drama_ids_str) {
            return $return;
        }
        $drama_ids = array_map('intval', explode(',', $drama_ids_str));
        if (empty($drama_ids) || !MUtils2::isUintArr($drama_ids)) {
            return $return;
        }
        $count = count($drama_ids);
        // 先分页再查询
        $offset = ($page - 1) * $page_size;
        $drama_ids = array_slice($drama_ids, $offset, $page_size);

        if (!Equipment::isAppOlderThan('4.7.6', '5.6.4')) {
            // Drama 接口返回数据格式 ['tabs' => [...], 'dramas' => [...]]
            $data = self::rpc('drama/get-integrity-weekly-rank', [
                'catalog_id' => $catalog_id,
                'drama_ids' => $drama_ids,
                'thumbnail' => 1,
                'integrity' => $integrity,
            ]);
            if (!$data) {
                return $return;
            }
            $tabs = array_map(function ($item) use ($integrity) {
                if ($integrity === $item['integrity']) {
                    $item['active'] = true;
                }
                return $item;
            }, $data['tabs']);
        } else {
            // Drama 接口返回数据格式 ['tabs' => [...], 'dramas' => [...]]
            $data = self::rpc('drama/get-weekly-rank', [
                'catalog_id' => $catalog_id,
                'drama_ids' => $drama_ids,
                'thumbnail' => 1,
                'type' => $type,
            ]);
            if (!$data) {
                return $return;
            }
            $tabs = array_map(function ($item) use ($type) {
                if ($type === $item['type']) {
                    $item['active'] = true;
                }
                return $item;
            }, $data['tabs']);
        }
        $dramas = array_map(function ($drama) {
            $drama['front_cover'] = $drama['thumbnail'];
            unset($drama['thumbnail']);
            return $drama;
        }, $data['dramas']);
        self::compatibleFillCornerMark($dramas, $user_id);

        $return_models = ReturnModel::getPaginationData($dramas, $count, $page, $page_size);
        return [
            'tabs' => $tabs,
            'Datas' => $return_models->Datas,
            'pagination' => $return_models->pagination,
        ];
    }

    /**
     * 获取分区分类下的剧集
     *
     * @param int $drama_catalog_id 剧集分类 ID
     * @param string $sub_type 分区类型，剧集类型（type）或完结度（integrity）
     * @param int $sub_value 分区值
     * @param int $user_id 用户 ID
     * @param int $order 排序方式：1：综合排序；2：最多播放；3：最多评论；4：最新更新
     * @param int $page 当前页数
     * @param int $page_size 每页个数
     * @return array
     */
    public static function getCatalogDrama(int $drama_catalog_id, string $sub_type, int $sub_value, int $user_id,
            int $order = 1, int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        $api = 'drama/get-drama-by-type';
        $params = [
            'catalog_id' => $drama_catalog_id,
            'order' => $order,
            'page' => $page,
            'page_size' => $page_size
        ];
        if ($sub_type === self::SUB_CATEGORY_BY_TYPE) {
            $params['type'] = $sub_value;
        } elseif ($sub_type === self::SUB_CATEGORY_BY_INTEGRITY) {
            $api = 'drama/get-drama-by-integrity';
            $params['integrity'] = $sub_value;
        } else {
            throw new \Exception("sub_type 参数错误：{$sub_type}");
        }
        $return = self::rpc($api, $params);
        if ($return) {
            // 判断用户是否购买
            $return['Datas'] = array_map(function ($item) {
                $item['front_cover'] = $item['cover'];
                unset($item['cover']);
                return $item;
            }, $return['Datas']);
            self::compatibleFillCornerMark($return['Datas'], $user_id);
        }
        return $return;
    }

    /**
     * 获取广播剧分区子分类
     *
     * @param int $drama_catalog_id 剧集分类
     * @return array 例：[['type' => 5, 'type_name' => '全年龄'], ['type' => '4', 'type_name' => '纯爱（中文）']]
     */
    public static function getDramaTypes(int $drama_catalog_id)
    {
        $key = MUtils::generateCacheKey(KEY_DRAMA_TYPE, $drama_catalog_id);
        if (!$drama_type = Yii::$app->memcache->get($key)) {
            $drama_type = self::rpc('drama/get-drama-type', ['catalog_id' => $drama_catalog_id]) ?? [];
            $drama_type = Json::encode($drama_type);
            // 此处数据基本无变化，缓存有效期可用半小时，若调整了剧集项目相关配置文件，需要手动删除该缓存
            // WORKAROUND: 特殊时期，随时调整分类，将缓存有效期调整为 5 分钟
            Yii::$app->memcache->set($key, $drama_type, FIVE_MINUTE);
        }
        return Json::decode($drama_type);
    }

    /**
     * 获取所有广播剧完结度名称
     *
     * @return array
     * @todo 从 drama rpc 接口获取
     */
    public static function getDramaIntegrityNames()
    {
        return [
            self::INTEGRITY_NAME_SERIALIZING => '未完结',
            self::INTEGRITY_NAME_END => '已完结',
            self::INTEGRITY_NAME_ONE => '全一期',
            self::INTEGRITY_NAME_MINI => '微小剧',
            self::INTEGRITY_NAME_ONE_AND_MINI => '全一期',  // 实际还包含了微小剧
        ];
    }

    /**
     * 获取广播剧完结度名称
     *
     * @param int $integrity
     * @return string 若该完结度不存在，则返回空字符串
     */
    public static function getDramaIntegrityName(int $integrity): string
    {
        $integrity_names = self::getDramaIntegrityNames();
        return $integrity_names[$integrity] ?? '';
    }

    /**
     * 根据完结度获取广播剧分区子分类
     *
     * @param int $drama_catalog_id 剧集分类
     * @return array 例：[['integrity' => 2, 'integrity_name' => '完结'], ['integrity' => 1, 'integrity_name' => '未完结']]
     */
    public static function getDramaIntegrity(int $drama_catalog_id)
    {
        $key = MUtils::generateCacheKey(KEY_DRAMA_INTEGRITY, $drama_catalog_id);
        if (!$drama_integrity = Yii::$app->memcache->get($key)) {
            $drama_integrity = self::rpc('drama/get-drama-integrity', ['catalog_id' => $drama_catalog_id]) ?? [];
            $drama_integrity = Json::encode($drama_integrity);
            // 此处数据基本无变化，缓存有效期可用半小时，若调整了剧集项目相关配置文件，需要手动删除该缓存
            // WORKAROUND: 特殊时期，随时调整分类，将缓存有效期调整为 5 分钟
            Yii::$app->memcache->set($key, $drama_integrity, FIVE_MINUTE);
        }
        return Json::decode($drama_integrity);
    }

    /**
     * 根据音频分类 ID 获取音频所属剧集分类 ID
     *
     * @param int $catalog_id 音频分类 ID
     * @return int 剧集分类 ID
     */
    public static function getCatalogBySoundCatalog(int $catalog_id): int
    {
        return self::SOUND_TO_DRAMA_CATALOG_IDS[$catalog_id] ?? 0;
    }

    /**
     * 根据剧集分类 ID 获取其音频所属分类 ID
     *
     * @param int $catalog_id 剧集分类 ID
     * @return int 音频分类 ID
     */
    public static function getSoundCatalogByCatalog(int $catalog_id): int
    {
        $drama_to_sound_catalog_ids = array_flip(self::SOUND_TO_DRAMA_CATALOG_IDS);
        return $drama_to_sound_catalog_ids[$catalog_id] ?? 0;
    }

    /**
     * 获取青少年剧集
     *
     * @param array $drama_ids 剧集 ID
     * @return array 猜你喜欢推荐模块中剧集的数据（数组索引为剧集的 ID）
     */
    public static function getTeenagerDramas(array $drama_ids)
    {
        if (empty($drama_ids)) return [];
        return self::rpc('api/get-teenager-dramas', ['drama_ids' => $drama_ids]);
    }

    /**
     * 获取显示的季度剧集
     *
     * @param array $seasons 季度剧集信息
     * @param int $user_id 用户 ID
     * @param boolean $show_sounds 是否获取季度剧集下的音频信息
     */
    public static function getSeasonDramas(array &$seasons, ?int $user_id, bool $show_sounds = false)
    {
        if (empty($seasons)) {
            return;
        }
        // WORKAROUND: 暂时兼容 checked 和 pay_type 字段不存在的情况
        // https://github.com/MiaoSiLa/missevan-drama/pull/661 该 PR 上线后可删除此兼容
        $seasons = array_map(function ($item) {
            if (!array_key_exists('checked', $item)) {
                // 默认值为过审状态可不用走下面合约下架剧集相关的判断
                $item['checked'] = self::CHECKED_PASS;
            }
            if (!array_key_exists('pay_type', $item)) {
                // 默认值为免费可不用走下面付费相关的判断
                $item['pay_type'] = self::DRAMA_FREE;
            }
            return $item;
        }, $seasons);

        if (!$user_id) {
            // 未登录状态，季度剧集信息只显示审核通过剧集
            $show_season_dramas = array_filter($seasons, function ($item) {
                return $item['checked'] === self::CHECKED_PASS;
            });
        } else {
            // 登录状态，季度剧集信息显示审核通过剧集和已购买的合约期下架剧集
            $other_season_dramas = array_filter($seasons, function ($item) {
                return $item['checked'] === self::CHECKED_CONTRACT_EXPIRED &&
                    $item['pay_type'] !== self::PAY_TYPE_FREE;
            });
            $other_drama_ids = array_column($other_season_dramas, 'drama_id');
            $show_drama_ids = [];
            if (!empty($other_drama_ids)) {
                $show_drama_ids = TransactionLog::find()
                    ->select('gift_id')
                    ->where([
                        'status' => TransactionLog::STATUS_SUCCESS,
                        'from_id' => $user_id,
                        'type' => [TransactionLog::TYPE_SOUND, TransactionLog::TYPE_DRAMA],
                        'gift_id' => $other_drama_ids
                    ])->column();
                $show_drama_ids = array_map('intval', $show_drama_ids);
            }
            $show_season_dramas = array_filter($seasons, function ($item) use ($show_drama_ids) {
                return $item['checked'] === self::CHECKED_PASS || in_array($item['drama_id'], $show_drama_ids);
            });
        }
        sort($show_season_dramas);
        $seasons = $show_season_dramas;
        // 过滤冗余字段
        if (!$show_sounds) {
            // 不获取季度剧集单集信息
            $seasons = array_map(function ($item) {
                unset($item['checked'], $item['pay_type'], $item['season'], $item['sound_id']);
                return $item;
            }, $seasons);
        } else {
            // 获取季度剧集单集信息
            $season_sound_ids = [];
            array_map(function ($item) use (&$season_sound_ids) {
                $season_sound_ids = array_merge($season_sound_ids, array_column($item['episodes'], 'sound_id'));
            }, $seasons);
            $season_sound_titles = MSound::find()->select('soundstr')
                ->where(['id' => $season_sound_ids])->indexBy('id')->column();
            if (!empty($season_sound_titles)) {
                foreach ($seasons as &$item) {
                    $item['sounds'] = [];
                    foreach ($item['episodes'] as $episode) {
                        if (array_key_exists($episode['sound_id'], $season_sound_titles)) {
                            $item['sounds'][] = [
                                'sound_id' => $episode['sound_id'],
                                'soundstr' => $season_sound_titles[$episode['sound_id']],
                                'episode_name' => $episode['episode_name']
                            ];
                        }
                    }
                    unset($item['episodes'], $item['checked'], $item['pay_type']);
                }
                unset($item);
            }
        }
    }

    /**
     * 获取单集分类信息
     *
     * @param array $episodes 单集信息
     * @return array
     */
    public static function getTypeEpisodes(array $episodes)
    {
        $return = [
            'ft' => [],
            'music' => [],
            'episode' => []
        ];
        if (empty($episodes)) {
            return $return;
        }
        // WORKAROUND: 临时兼容 https://github.com/MiaoSiLa/missevan-drama/pull/661 该 PR 上线后可删除此兼容
        // 因为该 PR 未上线时，rpc 接口返回的单集信息是已区分 ft、music、episodes
        if (key_exists('ft', $episodes) && key_exists('music', $episodes) && key_exists('episode', $episodes)) {
            return $episodes;
        }
        $sound_ids = array_column($episodes, 'sound_id');
        if (empty($sound_ids)) {
            return $return;
        }
        $video_sound_ids = SoundVideo::find()->select('sid')
            ->where(['sid' => $sound_ids, 'checked' => SoundVideo::CHECKED_PASS])->column();
        $video_sound_ids = array_map('intval', $video_sound_ids);
        foreach ($episodes as $episode) {
            if (!empty($video_sound_ids) && in_array($episode['sound_id'], $video_sound_ids)) {
                // 如果单音有对应的视频，则返回的视频字段为 1
                $episode['video'] = 1;
            }
            switch ($episode['type']) {
                case self::EPISODE_TYPE_DRAMA:
                    $return['episode'][] = $episode;
                    break;
                case self::EPISODE_TYPE_MUSIC:
                    $return['music'][] = $episode;
                    break;
                case self::EPISODE_TYPE_INTERVIEW:
                    $return['ft'][] = $episode;
                    break;
            }
        }
        return $return;
    }

    /**
     * 判断剧集属性
     * 如果剧集属性为限定剧集，限定剧集是免费的合约下架剧集，并且不能搜索、追剧、音频喜欢、收藏、评论、弹幕等操作
     *
     * @param array|int $drama_ids 剧集 IDs
     * @param string $operator 操作类型 all：所有都为特殊剧集则为真；any：其中有一个为特殊剧集则为真
     * @param string $type 剧集属性类型 risking：擦边球；japan_forbidden：日本禁听；interactive：互动广播剧；
     * lossless：无损音质广播剧；special：特殊剧集
     * @return boolean
     */
    public static function checkDramaRefined($drama_ids, string $type, string $operator = 'any')
    {
        if (!is_array($drama_ids)) {
            $drama_ids = [$drama_ids];
        }
        if (empty($drama_ids) || !MUtils2::isUintArr($drama_ids)) {
            throw new Exception('drama_ids 参数错误：' . Json::encode($drama_ids));
        }
        return self::rpc('drama/check-drama-refined', [
            'drama_ids' => $drama_ids,
            'operator' => $operator,
            'type' => $type,
        ]);
    }

    public static function isSensitiveDrama(int $drama_id): bool
    {
        return self::checkDramaRefined($drama_id, self::REFINED_SENSITIVE);
    }

    /**
     * 判断剧集的状态
     *
     * @param array $drama 剧集信息
     * @param int $user_id 用户 ID
     * @return boolean
     * @throws HttpException
     */
    public static function checkStatus(&$drama, ?int $user_id)
    {
        if (self::checkDramaRefined($drama['id'], self::REFINED_TYPE_SPECIAL)) {
            if (!$user_id || !TransactionLog::find()->where([
                'from_id' => $user_id,
                'gift_id' => $drama['id'],
                // 限定剧集只可以通过兑换码进行兑换，兑换后相当于整剧购买
                'type' => TransactionLog::TYPE_DRAMA,
                'status' => TransactionLog::STATUS_SUCCESS
            ])->exists()) {
                throw new HttpException(403, '你还不能查看本限定剧集哦');
            }
            // 已兑换用户加入已购标识
            $drama['need_pay'] = self::DRAMA_PAID;
        } elseif (self::CHECKED_CONTRACT_EXPIRED === $drama['checked'] && self::DRAMA_PAID !== $drama['need_pay']) {
            // 对于合约期满下架的剧集，若为已购的付费剧则可访问，否则不能访问
            throw new HttpException(403, '当前内容暂不能进行此操作');
        }
        return true;
    }

    /**
     * 获取用户的剧集作品
     *
     * @param int $view_user_id 剧集 UP 主 ID
     * @param int|null $user_id 访问者 ID
     * @param int $page 当前页数
     * @param int $page_size 每页个数
     * @return array 例：['Datas' => [], 'pagination' => ['count' => 3, 'pagesize' => 20, 'p' => 1, 'maxpage' => 1]]
     * @throws Exception
     */
    public static function getUserDrama(int $view_user_id, ?int $user_id, int $page = 1,
            int $page_size = PAGE_SIZE_20)
    {
        if ($view_user_id <= 0) {
            throw new Exception('参数错误');
        }
        $SCENE_PERSON_HOMEPAGE = 'personhomepage';
        return self::rpc('api/get-user-dramas', [
            'view_user_id' => $view_user_id,
            // view_cv = 1 获取 UP 主作为声优参演的剧集；view_cv = 0 时不获取
            'view_cv' => 1,
            'user_id' => $user_id,
            'page' => $page,
            'page_size' => $page_size,
            'scene' => $SCENE_PERSON_HOMEPAGE,
            'scenario' => Go::SCENARIO_PERSON_SEARCH,
        ]);
    }

    /**
     * 在个人主页关键字搜索用户的剧集作品
     *
     * @param string $q 搜索关键字
     * @param int $view_user_id 剧集 UP 主 ID
     * @param int $user_id 访问者 ID
     * @param int $page 当前页数
     * @param int $page_size 每页个数
     * @return array 例：['Datas' => [], 'pagination' => ['count' => 3, 'pagesize' => 20, 'p' => 1, 'maxpage' => 1]]
     * @throws Exception
     */
    public static function searchUserDrama(string $q, int $view_user_id, int $user_id, int $page = 1,
            int $page_size = PAGE_SIZE_20)
    {
        return self::rpc('api/search-user-dramas', [
            'q' => $q,
            'view_user_id' => $view_user_id,
            'user_id' => $user_id,
            'page' => $page,
            'page_size' => $page_size,
        ]);
    }

    /**
     * 通过音频 ID 获取其所属剧集 ID
     *
     * @param int $sound_id 音频 ID
     * @return int 剧集 ID
     */
    public static function getDramaIdBySoundId(int $sound_id): int
    {
        $res = self::rpc('api/get-dramaid-by-soundid', ['sound_ids' => [$sound_id]]);
        return $res[0]['drama_id'] ?? 0;
    }

    /**
     * 获取精品周更
     *
     * @return array|mixed
     */
    public static function getWeeklyDrama()
    {
        $dramas = [];
        $dramas_info = self::rpc('api/summerdrama');
        if (!empty($dramas_info)) {
            $dramas_info = MUtils2::groupArray($dramas_info, 'date');
            // 取当前日期为开始进行正序排序，当前日期之前的排到末尾
            // 如当前是周二，那么周二到周六的剧集按时间顺序先排，然后排周日、周一的剧集
            ksort($dramas_info);
            $w = date('w', $_SERVER['REQUEST_TIME']);
            $w = ($w >= 0) ? $w : 6;
            $header_dramas = [];
            $footer_dramas = [];
            foreach ($dramas_info as $date => $date_dramas) {
                if ($date >= $w) {
                    // 获取今天至周六更新的剧集
                    $header_dramas = array_merge($header_dramas, $date_dramas);
                } else {
                    // 获取周六至昨天更新的剧集
                    $footer_dramas = array_merge($footer_dramas, $date_dramas);
                }
            }
            $dramas = array_merge($header_dramas, $footer_dramas);
        }
        return $dramas;
    }

    /**
     * 根据剧集 IDs 获取剧集信息
     *
     * @param array $drama_ids 剧集 IDs
     * @return array
     * @throws Exception
     */
    public static function getDramaByIds(array $drama_ids)
    {
        if (empty($drama_ids)) {
            return [];
        }
        if (!MUtils2::isUintArr($drama_ids)) {
            throw new Exception('存在无效的剧集 ID');
        }
        $drama_ids = array_map('intval', $drama_ids);
        return self::rpc('drama/get-drama-by-ids', ['drama_ids' => $drama_ids]);
    }

    /**
     * 返回用户已追剧集
     *
     * @param array $drama_ids
     * @param int $user_id
     * @return array 已追剧集 ID
     */
    public static function getSubscribedDramaIds(array $drama_ids, int $user_id)
    {
        return self::rpc('api/get-subscribed-drama-ids',
            ['drama_ids' => $drama_ids, 'user_id' => $user_id]);
    }

    /**
     * 判断剧集是否禁止打赏
     *
     * @param int $drama_id 剧集 ID
     * @return bool
     * @todo 之后改成使用剧集的 refined 值属性来判断
     */
    public static function isForbiddenReward(int $drama_id): bool
    {
        // WORKAROUND: 剧集《天官赐福》在 2023-08-22 09:50:00 前不支持打赏
        $forbidden_drama_id = 52400;
        $forbidden_end_time = strtotime('2023-08-22 09:50:00');
        $now = $_SERVER['REQUEST_TIME'];
        if ($now < $forbidden_end_time && $drama_id === $forbidden_drama_id) {
            return true;
        }
        return false;
    }

    /**
     * 区分版本赋值对应的角标信息
     *
     * @param array $dramas 剧集信息数组
     * @param int $user_id 用户 ID
     */
    public static function compatibleFillCornerMark(array &$dramas, int $user_id)
    {
        if (empty($dramas)) {
            return;
        }
        // WORKAROUND: iOS < 4.8.8、Android < 5.7.4 时，调用 checkNeedPay 方法，不补充对应角标
        if (Equipment::isAppOlderThan('4.8.8', '5.7.4')) {
            self::checkNeedPay($dramas, $user_id);
            return;
        }
        self::fillCornerMark($dramas, $user_id);
    }

    /**
     * 补充剧集角标信息
     *
     * @param array $dramas 剧集信息数组
     * @param int $user_id 用户 ID
     */
    public static function fillCornerMark(array &$dramas, int $user_id)
    {
        if (empty($dramas)) {
            return;
        }
        $drama_ids = array_map('intval', array_column($dramas, 'id'));

        // 获取剧集角标信息
        $corner_mark_map = Drama::getDramaCornerMark($drama_ids, $user_id);
        if (!$corner_mark_map) {
            return;
        }
        foreach ($dramas as &$drama) {
            if (isset($corner_mark_map[$drama['id']])) {
                $drama['corner_mark'] = $corner_mark_map[$drama['id']];
            }
        }
        unset($drama);
    }

    /**
     * 给剧集模块补充剧集角标信息
     *
     * @param array $modules 模块数据
     * @param integer $user_id 用户 ID
     */
    public static function fillCornerMarkInDramaModules(array &$modules, int $user_id)
    {
        $drama_ids = [];
        // 取出剧集模块中所有的剧集 ID
        foreach ($modules as &$module) {
            if (MPersonaModuleElement::MODULE_TYPE_DRAMA === $module['type']) {
                foreach ($module['elements'] as $element) {
                    $drama_ids[] = $element['id'];
                }
            }
        }

        if (empty($drama_ids)) {
            return;
        }

        // 获取剧集角标信息
        $corner_mark_map = Drama::getDramaCornerMark($drama_ids, $user_id);
        if (!$corner_mark_map) {
            return;
        }

        // 将剧集角标信息赋值到剧集中
        array_map(function (&$module) use ($corner_mark_map) {
            if (MPersonaModuleElement::MODULE_TYPE_DRAMA === $module['type']) {
                foreach ($module['elements'] as &$element) {
                    if (isset($corner_mark_map[$element['id']])) {
                        $element['corner_mark'] = $corner_mark_map[$element['id']];
                    }
                }
            }
        }, $modules);
        unset($element);
    }

    /**
     * 获取剧集角标
     *
     * @param array $drama_ids 剧集 IDs
     * @param integer $user_id 用户 ID
     * @return array|null
     */
    public static function getDramaCornerMark(array $drama_ids, int $user_id)
    {
        if (empty($drama_ids)) {
            return null;
        }

        // 获取剧集角标信息
        try {
            $corner_mark_map = Yii::$app->serviceRpc->getDramaCornerMark($drama_ids, $user_id);
            if (!$corner_mark_map) {
                return null;
            }
            // WORKAROUND: iOS < 6.2.5 由于客户端会闪退，故不下发 text_start_color 和 text_end_color 角标颜色渐变字段
            if (Equipment::isAppOlderThan('6.2.5', null)) {
                $corner_mark_map = array_map(function ($corner_mark) {
                    if (isset($corner_mark['text_start_color'])) {
                        unset($corner_mark['text_start_color']);
                    }
                    if (isset($corner_mark['text_end_color'])) {
                        unset($corner_mark['text_end_color']);
                    }
                    return $corner_mark;
                }, $corner_mark_map);
            }
            return $corner_mark_map;
        } catch (Exception $e) {
            Yii::error('获取角标信息 rpc 接口出错: ' . $e->getMessage(), __METHOD__);
            // PASS
            return null;
        }
    }

    /**
     * 获取剧集分区 ID
     *
     * @param int $drama_id 剧集 ID
     * @return int 返回剧集分区 ID
     */
    public static function getDramaCatalogId(int $drama_id): int
    {
        if ($drama_id <= 0) {
            return 0;
        }
        $catalog_id = 0;
        try {
            $drama = Yii::$app->serviceRpc->getDramaInfo($drama_id);
            if ($drama && $drama['drama']) {
                $catalog_id = $drama['drama']['catalog_id'] ?? 0;
            }
        } catch (Exception $e) {
            Yii::error('获取剧集 ' . $drama_id . ' 信息 rpc 接口出错: ' . $e->getMessage(), __METHOD__);
            // PASS
        }
        return $catalog_id;
    }
}
