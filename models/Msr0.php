<?php

namespace app\models;

class Msr0
{
    const SCHEME_URL_HOMEPAGE = 'missevan://user/homepage';
    const SCHEMA_PREFIX = 'missevan://';
    const SCHEME_URL_LIVE_TAB = 'missevan://live';

    /**
     * 获取用户详情页的 SchemeURL
     * 当 user_id 为 0 或者不传时返回当前登录用户的主页 URL，否则返回对应用户主页的 URL
     *
     * @param int $user_id 用户 ID
     * @return string
     */
    public static function getUserHomepage(int $user_id = 0): string
    {
        if (!$user_id) {
            return self::SCHEME_URL_HOMEPAGE;
        }
        return sprintf('%s?user_id=%d', self::SCHEME_URL_HOMEPAGE, $user_id);
    }
}
