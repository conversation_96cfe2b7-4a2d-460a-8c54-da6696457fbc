<?php

namespace app\models;

use app\components\auth\wechat\WechatOffiaccount;
use Yii;
use yii\db\Expression;

/**
 * This is the model class for table "m_wechat_offiaccount_reply".
 *
 * @property string $id 主键
 * @property string $create_time 创建时间戳，单位：秒
 * @property string $modified_time 更新时间戳，单位：秒
 * @property string $delete_time 删除时间戳，单位：秒
 * @property int $scene 场景。1：消息回复；2：事件回复
 * @property int $reply_type 回复的消息类型。1：文本；2：图片；3：语音；4：视频；5：音乐；6：图文
 * @property array $keywords 自动回复匹配的关键字数组；若为事件，数组元素存入的是事件的 key（event_key）
 * @property array $reply 回复的内容
 */
class MWechatOffiaccountReply extends ActiveRecord
{
    const DEFAULT_TEXT_REPLY = "欢迎来到M站～\n\n超多优质广播剧尽在🐱猫耳FM，快来收听吧！\n如有站内产品相关问题需要解答，请移步APP端内咨询客服娘～";

    const SUBSCRIBE_EVENT_KEY = 'subscribe_wechat_offiaccount';  // 关注公众号自动回复的事件 key

    // 场景
    const SCENE_MESSAGE = 1;  // 消息回复
    const SCENE_EVENT = 2;    // 事件回复

    // 回复类型
    const REPLY_TYPE_TEXT = 1;  // 文本消息
    const REPLY_TYPE_IMAGE = 2;  // 图片消息
    const REPLY_TYPE_VOICE = 3;  // 语音消息

    public static function getDb()
    {
        return Yii::$app->get('db');
    }

    public static function tableName()
    {
        return 'm_wechat_offiaccount_reply';
    }

    public function rules()
    {
        return [
            [['create_time', 'modified_time', 'delete_time'], 'integer'],
            [['reply'], 'string', 'max' => 300],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间',  // 单位：秒
            'modified_time' => '更新时间',  // 单位：秒
            'delete_time' => '删除时间',  // 单位：秒
            'scene' => '场景',  // 1：消息回复；2：事件回复
            'reply_type' => '消息类型',  // 1：文本；2：图片；3：语音；4：视频；5：音乐；6：图文
            'keywords' => '关键字数组',  // 自动回复匹配的关键字数组；若为事件，数组元素存入的是事件的 key（event_key）
            'reply' => '回复的内容',
        ];
    }

    /**
     * 查询并获取回复内容
     *
     * @param int $scene
     * @param string $keyword 关键词或事件的 key
     * @return array
     */
    public static function getReply(int $scene, string $keyword): ?array
    {
        $keyword = trim($keyword);
        if (!$keyword) {
            return null;
        }
        $model = self::find()
            ->where(['delete_time' => 0, 'scene' => $scene])
            ->andWhere(new Expression('JSON_CONTAINS(keywords, :keyword)',
                [':keyword' => json_encode($keyword, JSON_UNESCAPED_UNICODE)]))
            ->orderBy(['modified_time' => SORT_DESC])  // 如果匹配到多条，以最新的一条为准
            ->limit(1)
            ->one();
        if (!$model) {
            return null;
        }
        $reply = [];
        switch ($model->reply_type) {
            case self::REPLY_TYPE_TEXT:
                $content = $model->reply['content'] ?? '';
                if ($content) {
                    $reply = self::formatTextReply($content);
                } else {
                    Yii::error("微信公众号自动回复文本配置错误，关键词: $keyword", __METHOD__);
                }
                break;
            case self::REPLY_TYPE_IMAGE:
                $media_id = $model->reply['image']['media_id'] ?? '';
                if ($media_id) {
                    $reply = self::formatImageReply($media_id);
                } else {
                    Yii::error("微信公众号自动回复图片配置错误，关键词: $keyword", __METHOD__);
                }
                break;
            case self::REPLY_TYPE_VOICE:
                $media_id = $model->reply['voice']['media_id'] ?? '';
                if ($media_id) {
                    $reply = self::formatVoiceReply($media_id);
                } else {
                    Yii::error("微信公众号自动回复语音配置错误，关键词: $keyword", __METHOD__);
                }
                break;
            default:
                // PASS: 对于暂不支持的类型，记录错误日志即可，不需要直接报错避免影响正常响应
                Yii::error("微信公众号自动回复配置了不支持的类型，回复类型: {$model->reply_type}，关键词：{$keyword}",
                    __METHOD__);
                break;
        }
        return $reply;
    }

    /**
     * 格式化文本回复内容
     *
     * @param string $content 回复内容
     * @return array
     */
    public static function formatTextReply(string $content)
    {
        return [
            'MsgType' => WechatOffiaccount::MSG_TYPE_TEXT,
            'Content' => $content,
        ];
    }

    /**
     * 格式化图片回复内容
     *
     * @param string $media_id 媒体文件ID
     * @return array
     */
    public static function formatImageReply(string $media_id)
    {
        return [
            'MsgType' => WechatOffiaccount::MSG_TYPE_IMAGE,
            'Image' => [
                'MediaId' => $media_id,
            ],
        ];
    }

    /**
     * 格式化语音回复内容
     *
     * @param string $media_id 媒体文件ID
     * @return array
     */
    public static function formatVoiceReply(string $media_id)
    {
        return [
            'MsgType' => WechatOffiaccount::MSG_TYPE_VOICE,
            'Voice' => [
                'MediaId' => $media_id,
            ],
        ];
    }
}
