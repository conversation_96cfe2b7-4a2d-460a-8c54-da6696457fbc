<?php

namespace app\models;

use Exception;
use Yii;

class AdTrackIQiYi extends AdTrack implements AdTrackInterface
{
    const CALLBACK_EVENT_TYPE_MAP = [
        self::CALLBACK_EVENT_ACTIVATE => self::CALLBACK_EVENT_TYPE_ACTIVATE,
        self::CALLBACK_EVENT_ONE_DAY_RETENTION => self::CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION,
        self::CALLBACK_EVENT_PAY => self::CALLBACK_EVENT_TYPE_PAY,
        self::CALLBACK_EVENT_REGISTER => self::CALLBACK_EVENT_TYPE_REGISTER,
        // TODO: 之后关键行为回传为调整成消费、充值回传，不需定义单独的消费、充值回传事件
        self::CALLBACK_EVENT_KEY_ACTION => null,
        self::CALLBACK_EVENT_TRANSACTION => null,  // 消费、充值
    ];

    // 激活
    const CALLBACK_EVENT_TYPE_ACTIVATE = 0;
    // 注册
    const CALLBACK_EVENT_TYPE_REGISTER = 1;
    // 通用付费，奇麟后台会判断具体对应「首次付费」、「每次付费」、「首日 ROI」中的哪种转化目标
    const CALLBACK_EVENT_TYPE_PAY = 17;
    // 次日留存
    const CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION = 3;

    /**
     * 转化事件回调
     *
     * @param string $event_type 事件类型
     * @param mixed $arg 支付金额（单位：元），关键行为参数
     */
    public function callback(string $event_type, $arg = 0)
    {
        try {
            if (!array_key_exists($event_type, self::CALLBACK_EVENT_TYPE_MAP)) {
                throw new Exception('爱奇艺点击回传事件错误：' . $event_type);
            }
            $event = self::CALLBACK_EVENT_TYPE_MAP[$event_type];
            if (is_null($event)) {
                return true;
            }
            $data = Yii::$app->tools->requestRemote($this->getCallbackUrl($event, $arg));
            if (!$data) {
                throw new Exception('爱奇艺点击回传失败：data is null');
            }
            if ($data['status'] !== 200) {
                throw new Exception(sprintf('爱奇艺点击回传失败：code[%d], msg[%s]', $data['status'], $data['message'] ?? ''));
            }
            return true;
        } catch (Exception $e) {
            Yii::error('aiqiyi ad error: ' . $e->getMessage(), __METHOD__);
        }
        return false;
    }

    /**
     * 转化事件回调
     *
     * @param int $callback_event 事件类型
     * @param mixed $arg 付费金额（单位：元），或关键行为参数
     * @return string
     */
    private function getCallbackUrl(int $callback_event, $arg = 0)
    {
        $callback_url = $this->track_id;
        if (($callback_event === self::CALLBACK_EVENT_TYPE_PAY)) {
            $pay_amount = $this->getCallbackPayAmount($arg);
            $callback_url = str_replace(['__PAYMENT_AMOUNT__', '__CONVERSION_TIME__'], [$pay_amount, $_SERVER['REQUEST_TIME']], $callback_url);
        }
        $callback_url .= '&event_type=' . $callback_event;
        return sprintf('%s&signature=%s', $callback_url, $this->getSignature($callback_url));
    }

    private function getSignature(string $callback_url)
    {
        $key = $this->getKey();
        return md5(strtolower($callback_url . $key));
    }

    private function getKey()
    {
        if (!$this->more || !array_key_exists('key', $this->more)) {
            Yii::error(sprintf('爱奇艺广告回传 ID：%d、账户 %s，加密密钥不存在', $this->id, $this->more['account_id']), __METHOD__);
            return '';
        }
        return $this->more['key'];
    }
}
