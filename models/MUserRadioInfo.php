<?php

namespace app\models;

use app\components\models\traits\UserTrait;
use Yii;
use yii\db\Exception;

/**
 * This is the model class for table "m_user_radio_info".
 *
 * @property int $id 用户 ID
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 * @property int $played_duration 催眠专享收听时长
 */
class MUserRadioInfo extends ActiveRecord
{
    use UserTrait;

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_user_radio_info';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['create_time', 'modified_time', 'played_duration'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '用户 ID',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'played_duration' => '播放时长',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($this->isNewRecord) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 更新用户催眠专享收听时长
     *
     * @param int $user_id 用户 ID
     * @param int $add_played_duration 新增时长
     * @throws \yii\web\HttpException 用户不存在时抛出异常
     */
    public static function addPlayedDuration(int $user_id, int $add_played_duration)
    {
        if ($user_id <= 0 || !$add_played_duration) {
            throw new \Exception("params error, user_id: {$user_id}, add_played_duration: {$add_played_duration}");
        }
        try {
            $user_radio_info = self::getByPk($user_id);
            $update_played_duration = $user_radio_info->updateCounters(['played_duration' => $add_played_duration]);
            if (!$update_played_duration) {
                throw new Exception('更新失败，'
                    . "user_id: {$user_id}, played_duration: {$add_played_duration}");
            }
        } catch (Exception $e) {
            Yii::error('更新用户催眠专享收听时长出错：' . $e->getMessage(), __METHOD__);
            // PASS: 避免影响用户使用，记录错误日志即可
        }
    }

    /**
     * 获取用户催眠专享收听时长
     *
     * @param int $user_id 用户 ID
     * @return int 催眠专享收听时长
     * @throws \yii\web\HttpException 用户不存在时抛出异常
     */
    public static function getPlayedDuration(int $user_id): int
    {
        if ($user_id <= 0) {
            throw new \Exception("params error, user_id: {$user_id}");
        }
        try {
            $user_radio_info = self::getByPk($user_id);
            // 初次创建时 played_duration 属性默认值为 null，需要转整
            return (int)$user_radio_info->played_duration;
        } catch (Exception $e) {
            Yii::error('获取用户催眠专享收听时长出错：' . $e->getMessage(), __METHOD__);
            // PASS
            return 0;
        }
    }
}
