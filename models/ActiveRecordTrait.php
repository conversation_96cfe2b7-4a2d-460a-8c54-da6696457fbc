<?php

namespace app\models;

use app\components\util\ModelTrait;

/**
 * Trait ActiveRecordTrait
 * @package app\models
 */
trait ActiveRecordTrait
{

    use ModelTrait;

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

}
