<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "m_person_channel_page".
 *
 * @property integer $recid
 * @property integer $soundid
 * @property integer $channelid
 */
class MPersonChannelPage extends Feed
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_person_channel_page';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['recid', 'soundid', 'channelid'], 'integer'],
            [['recid', 'soundid'], 'unique', 'targetAttribute' => ['recid', 'soundid'], 'message' => 'The combination of Recid and Soundid has already been taken.'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'recid' => 'Recid',
            'soundid' => 'Soundid',
            'channelid' => '频道id',
        ];
    }

    /**
     * 获取关注的频道的动态
     *
     * @param string|array $condition
     * @param string|array $order
     * @param integer $limit
     * @return array
     */
    public static function getFeed($condition, $order, $limit): array
    {
        $select = 't.id, t.create_time, t.duration, t.soundstr, t.cover_image, 
            t1.channelid AS channel_id, t.username, t.user_id, t.view_count';

        $feed2 = MSound::find()->alias('t')->select($select)->innerJoin('m_person_channel_page t1', 't1.soundid = t.id')
            ->where($condition)->orderBy($order)->limit($limit)->asArray()->all();

        $without_channel = array_filter($feed2, function ($fee) {
            return !$fee['channel_id'];
        });

        $with_channel = array_filter($feed2, function ($fee) {
            return $fee['channel_id'];
        });

        $without_channel = self::handleSoundsWithoutChannelid($without_channel);
        $feed2 = array_merge($with_channel, $without_channel);

        $channel_ids = array_unique(array_column($feed2, 'channel_id'));

        $channels = MTag::find()->where(['id' => $channel_ids])->indexBy('id')->all();
        return array_map(function ($item) use ($channels) {
            $channel = $channels[$item['channel_id']] ?? null;
            if ($channel) {
                $item['channel_cover'] = $channel->smallpic ?: Yii::$app->params['defaultCoverUrl'];
                $item['tags'] = $channel['name'];
            } else {
                $item['channel_cover'] = Yii::$app->params['defaultCoverUrl'];
                $item['tags'] = '';
            }

            $item['id'] = (int)$item['id'];
            $item['user_id'] = (int)$item['user_id'];
            $item['create_time'] = (int)$item['create_time'];
            $item['duration'] = (int)$item['duration'];
            $item['view_count'] = (int)$item['view_count'];
            $item['front_cover'] = MSound::getFrontCoverUrl($item['cover_image']);
            $item['channel_id'] = (int)$item['channel_id'];
            $item['type'] = 'channel';

            unset($item['cover_image']);
            return $item;
        }, $feed2);
    }

    private static function handleSoundsWithoutChannelid($without_channels)
    {
        $new_without_sounds = [];
        if ($without_channels) {
            $user_id = Yii::$app->user->id;
            // 获取用户关注的标签
            $muser_tags = AvAttentionTag::findAll(['user_id' => $user_id]);
            $tag_ids = array_unique(array_column($muser_tags, 'tag_id'));
            $sound_ids = array_column($without_channels, 'id');
            $msound_tags = MTagSoundMap::findAll([
                'sound_id' => $sound_ids,
                'tag_id' => $tag_ids
            ]);
            $sound_tags = array_column($msound_tags, 'tag_id', 'sound_id');
            foreach ($without_channels as $without_channel) {
                if ($tag_id = $sound_tags[$without_channel['id']] ?? 0) {
                    $without_channel['channel_id'] = $tag_id;
                    $new_without_sounds[] = $without_channel;
                    self::updateAll(
                        ['channelid' => $tag_id], 'recid = :recid and soundid = :soundid',
                        [':recid' => $user_id, ':soundid' => $without_channel['id']]);
                } else {
                    self::deleteAll(['recid' => $user_id, 'soundid' => $without_channel['id']]);
                }
            }
        }
        return $new_without_sounds;
    }
}
