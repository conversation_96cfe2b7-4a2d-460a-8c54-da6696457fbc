<?php

namespace app\models;

use yii\web\HttpException;

class LuckyGift
{
    /**
     * 幸运签 ID
     * @var int
     */
    private $id;

    /**
     * 幸运签价格（单位：钻石）
     * @var int
     */
    private $price;

    /**
     * 幸运签数量（单位：个）
     * @var int
     */
    private $num;

    /**
     * 幸运签开箱礼物实际价值：主播收入（单位：分）
     * @var int
     */
    private $creator_income;

    private function __construct(int $id, int $price, int $num, int $creator_income)
    {
        if ($id <= 0 || $price <= 0 || $num <= 0 || $creator_income < 0) {
            throw new HttpException(400, '参数错误');
        }
        $this->id = $id;
        $this->price = $price;
        $this->num = $num;
        $this->creator_income = $creator_income;
    }

    /**
     * @param int $id 宝箱礼物 ID
     * @param int $price 幸运签价格（钻石）
     * @param int $num 幸运签数量（个）
     * @param int $creator_income 幸运签开箱礼物：主播收入（分）
     * @return LuckyGift
     * @throws HttpException
     */
    public static function newInstance(int $id, int $price, int $num, int $creator_income)
    {
        return new self($id, $price, $num, $creator_income);
    }

    public function getId()
    {
        return $this->id;
    }

    public function getPrice(): int
    {
        return $this->price;
    }

    public function getNum(): int
    {
        return $this->num;
    }

    public function getTotalPrice()
    {
        return $this->price * $this->num;
    }

    public function getCreatorIncome(): int
    {
        return $this->creator_income;
    }

}
