<?php

namespace app\models;

use Exception;
use app\components\util\MUtils;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "certification".
 *
 * @property integer $id
 * @property integer $user_id
 * @property string $user_name
 * @property string $real_name
 * @property integer $gender
 * @property integer $id_type
 * @property string $id_number
 * @property string $id_people
 * @property string $id_front
 * @property string $id_back
 * @property integer $method
 * @property integer $checked
 * @property integer $create_time
 * @property integer $update_time
 */
class Certification extends ActiveRecord
{
    // 实名认证审核状态 0：未审核；1：已审核；2：审核拒绝
    public const CHECKED_CREATE = 0;
    public const CHECKED_SUCCEED = 1;
    public const CHECKED_FAILED = 2;

    // 认证方式 0：未知；1：身份证；2：芝麻信用
    public const METHOD_ID_CARD = 1;
    public const METHOD_ALIPAY_ZHIMA = 2;

    // 1 身份证，2 港澳居民来往内地通行证，3 台湾居民来往大陆通行证
    // 4 护照（中国签发），5 护照（境外签发），6 外国人永久居留证
    public const ID_TYPE_ID_CARD_MAINLAND = 1;
    public const ID_TYPE_TRAVEL_PERMIT_HK_MO = 2;
    public const ID_TYPE_TRAVEL_PERMIT_TW = 3;
    public const ID_TYPE_PASSPORT_PRC = 4;
    public const ID_TYPE_PASSPORT_FOREIGN = 5;
    public const ID_TYPE_PERMANENT_RESIDENCE_CARD_FOR_FOREIGNER = 6;

    // 加密常量
    const TYPE_ENCRYPT = 10;

    // 成年人年龄
    const ADULT_AGE = 18;

    // 性别 1：男性；2：女性
    const GENDER_MALE = 1;
    const GENDER_FEMALE = 2;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'certification';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'user_name', 'real_name', 'id_number', 'id_people'], 'required'],
            [['user_id', 'id_type', 'gender', 'method', 'checked', 'create_time', 'update_time'], 'integer'],
            [['user_name'], 'string', 'max' => 20],
            [['id_number'], 'string', 'max' => 50],
            [['real_name'], 'string', 'max' => 120],
            [['id_people', 'id_front', 'id_back'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => 'M号',
            'user_name' => '用户名',
            'real_name' => '真实姓名',
            'gender' => '性别',  // 0 为未知，1 为男，2 为女
            'id_type' => '证件类型',
            'id_number' => '证件号码',
            'id_people' => '上传的手持证件照',
            'id_front' => '上传的证件照正面',
            'id_back' => '上传的证件照背面',
            'method' => '认证方式',
            'checked' => '审核状态',
            'create_time' => '申请时间',
            'update_time' => '更新/审核时间',
        ];
    }

    /**
     * 入库前自动处理
     *
     * @param bool $insert
     * @return bool
     */
    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        if ($this->id_type < self::TYPE_ENCRYPT) {
            $this->id_type += self::TYPE_ENCRYPT;
            // 身份证号用于搜索，加密时固定一个 IV 值，真实姓名或其他需要加密的字段使用 create_time IV 值
            $this->id_number = MUtils::encrypt($this->id_number, SENSITIVE_FIXED_IV_KEY);
            $this->real_name = MUtils::encrypt($this->real_name, $this->create_time);
        }
        $this->update_time = $time;
        return true;
    }

    /**
     * 校验身份证号
     * 校验规则：
     * 1. 对身份证号位数做判断
     * 2. 对未成年人做相应处理
     *
     * @param string $id_no 身份证号
     * @throws HttpException
     */
    public static function checkIdcard($id_no): void
    {
        if (18 !== strlen($id_no)) throw new HttpException(400, '请输入正确的身份证号');

        $age = self::getAgeByIdcard($id_no);
        if ($age === false || $age <= 0) {
            throw new HttpException(400, '请输入正确的身份证号');
        } elseif ($age < self::ADULT_AGE) {
            // 未成年用户不能通过实名认证
            throw new HttpException(403, '未满 ' . self::ADULT_AGE . ' 岁，未能通过实名认证');
        }
    }

    /**
     * 通过身份证号获取年龄
     *
     * @param string $id_card 身份证号
     * @return integer 年龄
     * @return boolean|integer 年龄，若为 false 表示身份证号未填或者不合法
     * @todo 该方法之后需要迁移至 php-utils 工具类中
     */
    public static function getAgeByIdcard($id_card)
    {
        if (!$id_card) return false;
        // 获取出生年月日
        $birthday_year = substr($id_card, 6, 4);
        $birthday_month = substr($id_card, 10, 2);
        $birthday_day = substr($id_card, 12, 2);
        // 判断是否都为数字，防止用户乱输入一些字符导致计算出错
        if (!MUtils2::isUintArr([$birthday_year, $birthday_month, $birthday_day])) {
            return false;
        }
        // 获取当前年月日
        [$year, $month, $day] = explode('-', date('Y-m-d', $_SERVER['REQUEST_TIME']));
        // 开始计算年龄
        $age = $year - $birthday_year;
        if ($age <= 0) {
            return 0;
        }
        if ($birthday_month > $month || ($birthday_month === $month && $birthday_day + 1 > $day)) {
            // 按照法规对年龄定义，从周岁生日的第二天年龄增长一岁。若当前日期小于生日后一天日期，则实际年龄需要减 1
            $age -= 1;
        }
        return $age;
    }

    /**
     * 通过身份证号获取性别，根据身份证号第 17 位判断，奇数为男性，偶数为女性
     *
     * @param string $id_card 身份证号
     * @return integer 性别 1：男性；2：女性
     * @throws HttpException
     */
    public static function getGenderByIdcard(string $id_card)
    {
        if (!$id_card) throw new HttpException(400, '请输入正确的身份证号');
        // 获取第 17 位数字
        $gender = substr($id_card, -2, 1);

        // 判断是否为数字，防止用户乱输入一些字符导致计算结果不准确
        if (!is_numeric($gender)) {
            throw new HttpException(400, '请输入正确的身份证号');
        }

        if ($gender % 2 === 0) {
            return self::GENDER_FEMALE;
        }
        return self::GENDER_MALE;
    }

    /**
     * 获取解密后的身份证号
     *
     * @return string
     */
    public function getDecryptIdNumber(): string
    {
        if (!$this->id_number) {
            return '';
        }
        return MUtils::decrypt($this->id_number, SENSITIVE_FIXED_IV_KEY);
    }

    /**
     * 创建实名认证信息
     *
     * @param int $user_id
     * @param string $username
     * @param string $id_number
     * @param string $real_name
     * @param string $id_people
     * @param int $gender
     * @return Certification
     * @throws HttpException
     */
    public static function certifyCreate(int $user_id, string $username, string $id_number, string $real_name,
            string $id_people, int $gender): self
    {
        if ($user_id <= 0 || !$id_number || !$real_name || !$id_people) {
            throw new HttpException(400, '参数错误');
        }
        $certification = self::find()
            ->where([
                'user_id' => $user_id,
                'id_type' => [
                    self::ID_TYPE_ID_CARD_MAINLAND,
                    self::ID_TYPE_ID_CARD_MAINLAND + self::TYPE_ENCRYPT
                ],
                'method' => self::METHOD_ALIPAY_ZHIMA,
                'checked' => self::CHECKED_CREATE,
            ])->orderBy('id DESC')->limit(1)->one();
        if (!$certification) {
            $certification = new self([
                'user_id' => $user_id,
                'id_type' => self::ID_TYPE_ID_CARD_MAINLAND,
                'method' => self::METHOD_ALIPAY_ZHIMA,
                'checked' => self::CHECKED_CREATE,
            ]);
        }
        $certification->setAttributes([
            'id_type' => self::ID_TYPE_ID_CARD_MAINLAND,
            'id_people' => $id_people,
            'id_number' => $id_number,
            'real_name' => $real_name,
            'user_name' => $username,
            'gender' => $gender
        ]);
        if (!$certification->save()) {
            Yii::error(sprintf('芝麻创建认证失败：%s', MUtils::getFirstError($certification)), __METHOD__);
            throw new HttpException(500, '认证失败请重试或联系客服');
        }
        return $certification;
    }

    /**
     * 设置实名认证通过
     * @throws HttpException
     * @return boolean
     */
    public function certifyPass(): bool
    {
        if (self::CHECKED_SUCCEED === $this->checked) {
            return true;
        }
        $user = Mowangskuser::findOne(['id' => $this->user_id]);
        if (!$user) {
            throw new HttpException(404, '用户不存在');
        }
        $transaction = self::getDb()->beginTransaction();
        try {
            $user->authorizeConfirm(Mowangskuser::CONFIRM_CERTIFICATED);
            $user->save();
            $this->checked = self::CHECKED_SUCCEED;
            $this->save();
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            // 记录错误日志
            Yii::error('实名认证通过后，更新状态失败，原因：' . $e->getMessage(), __METHOD__);
            return false;
        }
        return true;
    }
}
