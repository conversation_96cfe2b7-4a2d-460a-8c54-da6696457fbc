<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "transaction_sound_log".
 *
 * @property integer $id
 * @property integer $sound_id
 * @property integer $user_id
 * @property integer $drama_id
 * @property integer $tid
 * @property integer $status
 * @property integer $attr
 * @property integer $create_time
 * @property integer $modified_time
 */
class TransactionSoundLog extends ActiveRecord
{

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->paydb;
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'transaction_sound_log';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['sound_id', 'user_id', 'drama_id', 'tid'], 'required'],
            [['user_id', 'drama_id', 'tid', 'status', 'attr'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'sound_id' => '单音 ID',
            'user_id' => '用户 ID',
            'drama_id' => '剧集 ID',
            'tid' => 'transaction_log 关联表 ID',
            'status' => '订单状态',  // 同 transaction_log.status
            'attr' => '订单属性',  // 同 transaction_log.attr
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    public static function buyDramaEpisodes(array $sound_ids, int $drama_id, int $user_id, int $transaction_id)
    {
        $now = $_SERVER['REQUEST_TIME'];
        $rows = array_map(function ($sound_id) use ($drama_id, $user_id, $transaction_id, $now) {
            return [
                'sound_id' => $sound_id,
                'drama_id' => $drama_id,
                'user_id' => $user_id,
                'tid' => $transaction_id,
                'status' => TransactionLog::STATUS_SUCCESS,
                'attr' => TransactionLog::ATTR_COMMON,
                'create_time' => $now,
                'modified_time' => $now,
            ];
        }, $sound_ids);

        if (!empty($rows)) {
            return self::getDb()
                ->createCommand()
                ->batchInsert(
                    self::tableName(),
                    array_keys($rows[0]),
                    $rows
                )->execute() ?: false;
        }
        return false;
    }

    public function isSupportRePurchaseAfterRefund()
    {
        return ($this->attr & TransactionLog::ATTR_REPURCHASE_AFTER_REFUND) !== 0;
    }

    /**
     * 检查用户是否购买了单集付费剧集下指定的单集
     *
     * @param int $user_id 用户 ID
     * @param int $drama_id 剧集 ID
     * @param int $sound_id 音频 ID
     * @return bool
     */
    public static function checkUserPaidDramaSound(int $user_id, int $drama_id, int $sound_id): bool
    {
        return TransactionSoundLog::find()->where([
            'user_id' => $user_id,
            'drama_id' => $drama_id,
            'sound_id' => $sound_id,
            'status' => TransactionLog::STATUS_SUCCESS,
        ])->exists();
    }
}
