<?php

namespace app\models;

use app\components\util\MUtils;
use Yii;
use yii\db\Expression;

/**
 * This is the model class for table "statistic_download_id".
 *
 * @property int $id
 * @property int $user_id 用户 ID
 * @property string $download_id 区分苹果账号标志
 * @property int $num 统计值
 * @property int $type 记录类型
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 */
class StatisticDownloadId extends ActiveRecord
{
    // 同一个用户 ID 可以使用多少个 download_id（Apple ID 标识）
    const DOWNLOAD_ID_LEVEL_LIMIT_COUNT = 15;
    // 同一个 download_id（Apple ID 标识）允许给多少个用户充值
    const USER_ID_LIMIT_COUNT = 15;

    const TYPE_DOWNLOAD_ID_NUM = 1;
    const TYPE_USER_ID_NUM = 2;
    const TYPE_USER_ID_DOWNLOAD_ID_NUM = 3;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'statistic_download_id';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'download_id', 'num', 'type'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => '用户 ID',
            'download_id' => '区分苹果账号标志',
            'num' => '统计值',
            // 0 保留
            // 1 num 为用户的 download_id 数（download_id 字段为 0）
            // 2 num 为 download_id 的用户数（user_id 字段为 0）
            // 3 num 为 download_id 被用户的引用次
            'type' => '记录类型',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    private static function getOrInsert(array $attributes)
    {
        if (!$model = self::find()->where($attributes)->limit(1)->one()) {
            $model = new self();
            $model->setAttributes($attributes + ['download_id' => 0, 'num' => 0]);
            if (!$model->save()) {
                throw new \Exception(MUtils::getFirstError($model));
            }
        }

        return $model;
    }

    /**
     * 记录 download_id 记录
     *
     * @param int $user_id
     * @param int $download_id,
     * @return bool
     */
    public static function updateStatistics($user_id, $download_id): bool
    {
        $connection = Yii::$app->db;
        $transaction = $connection->beginTransaction();
        try {
            $m = self::getOrInsert(['user_id' => $user_id, 'type' => self::TYPE_DOWNLOAD_ID_NUM]);
            $n = self::getOrInsert(['download_id' => $download_id, 'type' => self::TYPE_USER_ID_NUM]);

            $update_fields = [
                'num' => new Expression('num + 1'),
                'modified_time' => $_SERVER['REQUEST_TIME'],
            ];

            $o = self::find()->where([
                'type' => self::TYPE_USER_ID_DOWNLOAD_ID_NUM,
                'user_id' => $user_id,
                'download_id' => $download_id,
            ])->limit(1)->one();
            if (!$o) {
                $o = new self();
                $o->setAttributes([
                    'num' => 0,
                    'download_id' => $download_id,
                    'user_id' => $user_id,
                    'type' => self::TYPE_USER_ID_DOWNLOAD_ID_NUM,
                ]);
                if (!$o->save()) {
                    throw new \Exception(MUtils::getFirstError($o));
                }

                $m->updateAttributes($update_fields);
                $n->updateAttributes($update_fields);
            }

            $o->updateAttributes($update_fields);

            $transaction->commit();
            return true;
        } catch (\Exception $e) {
            $transaction->rollBack();
            Yii::error("iOS 充值记录 download_id 异常：{$e->getMessage()}", __METHOD__);
            return false;
        }
    }

    public static function checkDownloadID(int $user_id, int $download_id = 0)
    {
        // 苹果小票中的 download_id 可能会返回 null 的情况或 9999
        if (in_array($download_id, [0, 9999])) {
            $is_normal_download_id = false;
        } else {
            $is_normal_download_id = true;
        }

        // 该用户用过多少个苹果账号
        $download_id_count = (int)self::find()->select('num')
            ->where(['user_id' => $user_id, 'type' => self::TYPE_DOWNLOAD_ID_NUM])
            ->scalar();
        if ($download_id_count > self::DOWNLOAD_ID_LEVEL_LIMIT_COUNT) {
            // 用户用过超过一定数量的苹果账号则检查不通过
            return false;
        }

        if ($is_normal_download_id) {
            // 该苹果账号给多少个用户用过
            $user_id_count = (int)self::find()->select('num')
                ->where(['download_id' => $download_id, 'type' => self::TYPE_USER_ID_NUM])
                ->scalar();
            if ($user_id_count > self::USER_ID_LIMIT_COUNT) {
                // 苹果账号被一定数量用户使用则检查不通过
                return false;
            }
        }

        return true;
    }

}
