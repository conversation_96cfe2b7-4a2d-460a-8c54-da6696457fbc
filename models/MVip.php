<?php

namespace app\models;

use Exception;
use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "m_vip".
 *
 * @property integer $id 主键
 * @property integer $create_time 创建时间（秒级时间戳）
 * @property integer $modified_time 修改时间（秒级时间戳）
 * @property integer $delete_time 删除时间（秒级时间戳）
 * @property integer $deduct_fee_schedule 付费周期：1 单次付费、2 连续包月、3 连续包季
 * @property integer $sort 展示顺序
 * @property integer $price 价格（单位：分）
 * @property integer $platform 平台：1 iOS、2 Android（非 Google 渠道）、3 Google Play
 * @property array $more 更多详情，e.g. { "first_subscribe_discount_price": 9900 }
 */
class MVip extends ActiveRecord
{
    use ActiveRecordTrait;

    // 付费周期
    const DEDUCT_FEE_SCHEDULE_SINGLE = 1; // 单次付费
    const DEDUCT_FEE_SCHEDULE_MONTHLY = 2; // 连续包月
    const DEDUCT_FEE_SCHEDULE_QUARTERLY = 3; // 连续包季

    // 付费周期等级，用于自动续订变更付费周期时，确定是升级还是降级
    const DEDUCT_FEE_SCHEDULE_LEVEL = [
        self::DEDUCT_FEE_SCHEDULE_SINGLE => 0,
        self::DEDUCT_FEE_SCHEDULE_MONTHLY => 1,
        self::DEDUCT_FEE_SCHEDULE_QUARTERLY => 2,
    ];

    // 平台
    const PLATFORM_IOS = 1;
    const PLATFORM_ANDROID = 2;
    const PLATFORM_GOOGLE_PLAY = 3;

    const IOS_IAP_PRODUCT_ID_PREFIX = 'com.missevan.CatEarFM.vip';

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->db;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_vip';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['deduct_fee_schedule', 'price', 'platform'], 'required'],
            [['create_time', 'modified_time', 'delete_time', 'deduct_fee_schedule', 'price', 'platform'], 'integer'],
            [['deduct_fee_schedule'], 'in', 'range' => [
                self::DEDUCT_FEE_SCHEDULE_SINGLE,
                self::DEDUCT_FEE_SCHEDULE_MONTHLY,
                self::DEDUCT_FEE_SCHEDULE_QUARTERLY,
            ]],
            [['platform'], 'in', 'range' => [
                self::PLATFORM_IOS,
                self::PLATFORM_ANDROID,
                self::PLATFORM_GOOGLE_PLAY,
            ]],
            [['more'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'delete_time' => '删除时间',
            'deduct_fee_schedule' => '付费周期',
            'sort' => '展示顺序',
            'price' => '价格',
            'platform' => '平台',
            'more' => '更多详情',
        ];
    }

    /**
     * 是否是订阅付费（连续包月、连续包季）
     *
     * @return bool
     */
    public function isSubscription(): bool
    {
        return $this->deduct_fee_schedule !== self::DEDUCT_FEE_SCHEDULE_SINGLE;
    }

    public function isMonthlyDeductFeeSchedule(): bool
    {
        return $this->deduct_fee_schedule === self::DEDUCT_FEE_SCHEDULE_MONTHLY;
    }

    public function isQuarterlyDeductFeeSchedule(): bool
    {
        return $this->deduct_fee_schedule === self::DEDUCT_FEE_SCHEDULE_MONTHLY;
    }

    public function isScheduleLevelGreaterThan(int $target_schedule): bool
    {
        $current_level = self::DEDUCT_FEE_SCHEDULE_LEVEL[$this->deduct_fee_schedule] ?? -1;
        $target_level = self::DEDUCT_FEE_SCHEDULE_LEVEL[$target_schedule] ?? -1;
        if ($current_level === -1) {
            throw new Exception('未知的付费周期：' . $this->deduct_fee_schedule);
        }
        if ($target_level === -1) {
            throw new Exception('未知的付费周期：' . $this->$target_schedule);
        }
        return $current_level > $target_level;
    }

    /**
     * 获取首开折扣价格
     *
     * @return int|null 单位：分，null 表示没有首开折扣
     */
    public function getFirstSubscribeDiscountPrice(): ?int
    {
        return $this->more['first_subscribe_discount_price'] ?? null;
    }

    /**
     * 获取微信协议代扣模板 ID
     *
     * @return int|null null 表示没有协议代扣模板 ID
     */
    public function getWechatPlanId(): ?int
    {
        return $this->more['wechat_plan_id'] ?? null;
    }

    /**
     * 获取支付宝签约场景码
     *
     * @return string|null 表示没有协议代扣模板 ID
     */
    public function getAlipaySignScene(): ?string
    {
        return $this->more['alipay_sign_scene'] ?? null;
    }

    /**
     * 获取会员周期时间（单位：秒）
     *
     * @return int|null null 表示没有会员周期时间
     */
    public function getPeriod(): ?int
    {
        return $this->more['period'] ?? null;
    }

    /**
     * 获取订单标题，例如：连续包月猫耳FM会员
     *
     * @return string|null null 表示没有订单标题
     */
    public function getOrderSubject(): ?string
    {
        return $this->more['order_subject'] ?? null;
    }

    public static function parseVipId(string $product_id): int
    {
        return (int)substr($product_id, -4);
    }

    public static function isVipProductId(string $product): bool
    {
        return str_starts_with($product, self::IOS_IAP_PRODUCT_ID_PREFIX);
    }

    public static function getVipByProductId(string $product_id, $platform = -1)
    {
        $vip_id = self::parseVipId($product_id);
        $condition = ['id' => $vip_id, 'delete_time' => 0];
        if ($platform !== -1) {
            $condition['platform'] = $platform;
        }
        return self::findOne($condition);
    }

    /**
     *
     * @param bool $has_discount 是否享受优惠
     * @return int
     */
    public function getPriceToDeduct(bool $has_discount)
    {
        if ($this->deduct_fee_schedule !== self::DEDUCT_FEE_SCHEDULE_MONTHLY) {
            return $this->price;
        }

        if ($has_discount) {
            // 用户首次订阅，且订阅的是连续包月，则享受优惠价格
            return $this->getFirstSubscribeDiscountPrice() ?: $this->price;
        }

        return $this->price;
    }

}
