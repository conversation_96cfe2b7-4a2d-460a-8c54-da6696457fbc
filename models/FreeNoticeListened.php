<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "free_notice_listened".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 * @property int $user_id 用户 id，mowangskuser主键
 * @property int $work_id 作品 id，work 表主键
 * @property int $role_id 角色 ID，角色 ID 为 0 时为全局提醒数
 * @property int $card_id 卡 id，card 主键。
 * @property int $has_notice 是否影响 notice
 */
class FreeNoticeListened extends ActiveRecord
{
    const HAS_NOTICE_NO = 0;
    const HAS_NOTICE_YES = 1;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'free_notice_listened';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db2');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'work_id', 'role_id', 'card_id'], 'required'],
            [['id', 'create_time', 'modified_time', 'user_id', 'work_id',
                'role_id', 'card_id', 'has_notice'], 'integer'],
            [['id'], 'unique'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'user_id' => '用户 ID，mowangskuser 主键',
            'work_id' => '作品 ID，work 表主键',
            'role_id' => '角色 ID',
            'card_id' => '卡 ID，card 主键（角色 ID 为 0 时为全局提醒数）',
            'has_notice' => '是否影响 notice',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    public static function getHistory(array $condition = [])
    {
        // 当 role_id = 0 时 card_id 代表全局提醒数量而非卡片 ID
        return self::find()->where($condition)
            ->andWhere('role_id > 0')->all();
    }

    public static function getHistoryByWork($work_id, $user_id,
            $has_notice = [self::HAS_NOTICE_YES, self::HAS_NOTICE_NO])
    {
        if (!$user_id) return [];
        return self::getHistory(['work_id' => $work_id, 'has_notice' => $has_notice, 'user_id' => $user_id]);
    }

    public static function getHistoryByRole($role_id, $user_id,
            $has_notice = [self::HAS_NOTICE_YES, self::HAS_NOTICE_NO])
    {
        if (!$user_id) return [];
        return self::getHistory(['role_id' => $role_id, 'has_notice' => $has_notice, 'user_id' => $user_id]);
    }

    public static function getHistoryByCard($card_id, $user_id)
    {
        return self::find()->where(['card_id' => $card_id, 'user_id' => $user_id])
            ->andWhere('role_id <> 0')->limit(1)->one();
    }

    public static function syncFree($user_id, $card_ids)
    {
        $free_cards = Card::find()->select('id, role_id, work_id')->where([
            'id' => $card_ids,
            'special' => Card::SPECIAL_FREE,
            'price' => Card::PRICE_FREE_CARD_HAVE_NOTICE
        ])->indexBy('id')->all();

        $free_listened = self::find()
            ->where(['user_id' => $user_id, 'card_id' => $card_ids, 'has_notice' => self::HAS_NOTICE_YES])
            ->andWhere('role_id <> 0')->all();
        if (count($card_ids) === count($free_listened)) return false;
        $ids_unsync = array_diff($card_ids, array_column($free_listened, 'card_id'));

        $rows = [];
        $time = $_SERVER['REQUEST_TIME'];
        foreach ($ids_unsync as $card_id) {
            if (!($card = $free_cards[$card_id] ?? null)) continue;
            $rows[] = [
                'create_time' => $time,
                'modified_time' => $time,
                'card_id' => $card_id,
                'user_id' => $user_id,
                'role_id' => $card->role_id,
                'work_id' => $card->work_id,
                'has_notice' => self::HAS_NOTICE_YES,
            ];
        }
        if ($rows) {
            return (bool)Yii::$app->db2->createCommand()
                ->batchInsert(self::tableName(), array_keys(current($rows)), $rows)->execute();
        }
        return false;
    }

    public static function getSum($work_id, $user_id)
    {
        if (!$user_id) return 0;
        // 当 role_id 为 0 时 card_id 代表用户听过所有免费音、热度福利音总数
        return (int)self::find()->select('card_id')
            ->where([
                'work_id' => $work_id,
                'role_id' => 0,
                'user_id' => $user_id,
                'has_notice' => self::HAS_NOTICE_YES
            ])->scalar();
    }

    public static function getTotalListenedModel($user_id, $work_id)
    {
        $model = FreeNoticeListened::findOne(['user_id' => $user_id, 'role_id' => 0, 'work_id' => $work_id]);
        if (!$model) {
            $model = new FreeNoticeListened;
            $model->work_id = $work_id;
            $model->user_id = $user_id;
            $model->role_id = 0;
            $model->has_notice = self::HAS_NOTICE_YES;
            // 当 role_id 为 0 时 card_id 代表用户听过所有免费音总数
            $model->card_id = 0;
        }
        return $model;
    }

    public static function cleanNotice($user_id, $work_id, $notice_count)
    {
        $model = self::getTotalListenedModel($user_id, $work_id);
        $model->card_id = $notice_count + count(Card::getHotCardsUnlocked($work_id));
        return $model->save();
    }

    public static function getListenedCards($card_ids, $user_id)
    {
        return self::find()->select('card_id')
            ->where(['card_id' => $card_ids, 'user_id' => $user_id])->andWhere('role_id <> 0')->column();
    }

}
