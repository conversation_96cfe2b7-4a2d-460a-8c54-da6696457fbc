<?php

namespace app\models;

use missevan\util\MUtils as MUtils2;
use Yii;

/**
 * This is the model class for table "ios_receipt_detail".
 *
 * @property int $id ios_recepit.id
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 * @property string $price 价格
 * @property string $equip_id 设备号
 * @property string $buvid buvid
 * @property string $ip 操作 IP
 * @property string $ua 设备信息
 * @property string $receipt_data 交易凭据
 * @property string $download_id 区分苹果账号标志
 */
class IosReceiptDetail extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'ios_receipt_detail';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('logdb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'equip_id', 'ua', 'receipt_data'], 'required'],
            [['id', 'create_time', 'modified_time', 'download_id'], 'integer'],
            [['price'], 'number'],
            [['receipt_data'], 'string'],
            [['equip_id'], 'string', 'max' => 36],
            [['buvid'], 'string', 'max' => 64],
            [['ip'], 'string', 'max' => MUtils2::IP_MAX_LENGTH],
            [['ua'], 'string', 'max' => 120],
            [['id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ios_recepit.id',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'price' => '价格',  // 货币单位（猫耳为人民币元，MiMi 为日元）
            'equip_id' => '设备号',
            'buvid' => 'buvid',
            'ip' => '操作 IP',
            'ua' => '设备信息',
            'receipt_data' => '交易凭据',
            // https://developer.apple.com/documentation/appstorereceipts/responsebody/receipt?changes=latest_minor
            'download_id' => '区分苹果账号标志',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

}
