<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "m_comment_black_user".
 *
 * The followings are the available columns in table 'm_comment_black_user':
 * @property integer $id
 * @property integer $user_id
 * @property string $username
 * @property integer $create_time
 * @property integer $modified_time
 */
class MCommentBlackUser extends ActiveRecord
{

    /**
     * @inheritdoc
     */
    public static function getDb()
    {
        return Yii::$app->messagedb;
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_comment_black_user';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'username'], 'required'],
            [['username'], 'string', 'max' => 20],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键 ID',
            'user_id' => '用户 ID',
            'username' => '用户名',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }
}
