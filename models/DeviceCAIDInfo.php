<?php

namespace app\models;

use app\components\util\Tools;
use Exception;
use Yii;
use yii\helpers\Json;

/**
 * 设备 CAID 归因
 *
 * @link https://info.missevan.com/pages/viewpage.action?pageId=60528934
 * Class DeviceCAIDInfo
 * @package app\models
 */
class DeviceCAIDInfo
{
    // CAID 算法包版本
    // TODO: 自建服务或广告平台的算法包升级时会存在多个版本的 CAID 值，此时需要进行兼容
    const CAID_VERSION = '20201230';

    private $data = null;

    // 设备启动时间，例：1600607106
    private $boot_time_in_sec;
    // 国家，例：CN
    private $country_code;
    // 语言，例：zh-Hans-CN
    private $language;
    // 设备名称（为设备名原值进行 md5 计算后，取小写 16 进制的结果，长度为 32 个字节），例：e910dddb2748c36b47fcde5dd720eec1
    private $device_name;
    // 系统版本，例：13.1.1
    private $system_version;
    // 设备 machine，例：iPhone10,3、iPad11,1
    private $machine;
    // 运营商，例：中国移动
    private $carrier_info;
    // 物理内存，例：4047224832
    private $memory;
    // 磁盘容量，例：127938088960
    private $disk;
    // 系统更新时间，例：1595214620.383940
    private $sys_file_time;
    // 设备 model，例：D22AP
    private $model;
    // 时区，例：28800
    private $time_zone;
    // 系统 ID，例：C02ML9CFFD5
    private $mnt_id;
    // 设备初始化时间，例：1610004188.958739218
    private $device_init_time;

    public function __construct(array $data)
    {
        $this->data = $data;
        $this->composeFields();
    }

    private function composeFields()
    {
        $this->boot_time_in_sec = $this->getItem('bootTimeInSec');
        $this->country_code = $this->getItem('countryCode');
        $this->language = $this->getItem('language');
        $this->device_name = strtolower($this->getItem('deviceName'));
        $this->system_version = $this->getItem('systemVersion');
        $this->machine = $this->getItem('machine');
        $this->carrier_info = $this->getItem('carrierInfo');
        $this->memory = $this->getItem('memory');
        $this->disk = $this->getItem('disk');
        $this->sys_file_time = $this->getItem('sysFileTime');
        $this->model = $this->getItem('model');
        $this->time_zone = $this->getItem('timeZone');
        // TODO: 等 iOS 客户端 CAID SDK 升级后上报上来
        // $this->mnt_id = $this->getItem('mntId');
        // $this->device_init_time = $this->getItem('deviceInitTime');

        // iOS 16 及其以上的系统，获取设备名需要申请权限
        // 中广协对于 iOS 16 及其以上的系统，iPhone 设备名需要使用 "iPhone" 进行归因，iPad 设备名需要使用 "iPad" 进行归因
        if (version_compare($this->system_version, '16', '>=')) {
            if (strpos($this->machine, 'iPhone') !== false) {
                $this->device_name = md5('iPhone');
            } elseif (strpos($this->machine, 'iPad') !== false) {
                $this->device_name = md5('iPad');
            } else {
                Yii::error('unknown ios device: ' . $this->machine, __METHOD__);
                $this->device_name = md5('iPhone');
            }
        }
        // iOS 17 及其以上系统获取不到运营商参数，传 "unknown" 字段
        if (version_compare($this->system_version, '17', '>=') && in_array($this->carrier_info, ['', '--', ' ', 'nil', 'null'])) {
            $this->carrier_info = 'unknown';
        }
    }

    private function getItem(string $key, $default = '')
    {
        if (array_key_exists($key, $this->data)) {
            return $this->data[$key];
        }
        Yii::error(sprintf('caid info error: %s not exists', $key), __METHOD__);
        return $default;
    }

    public function generateCAID()
    {
        try {
            // TODO: 等 iOS 客户端 CAID SDK 升级后上报 mnd_id, device_init_time
            $resp = Yii::$app->serviceRpc->deviceCAID($this->boot_time_in_sec, $this->country_code,
                $this->language, $this->device_name,
                $this->system_version, $this->machine,
                $this->carrier_info, $this->memory,
                $this->disk, $this->sys_file_time,
                $this->model, $this->time_zone);
            if ($resp['remaining_days'] - 7 <= 0) {
                Yii::error('CAID 算法包接近有效期', __METHOD__);
            }
            return $resp['caid'];
        } catch (Exception $e) {
            Yii::error('caid info error: ' . $e->getMessage(), __METHOD__);
        }

        return null;
    }

    public function __toString(): string
    {
        return Json::encode([
            'boot_time_in_sec' => $this->boot_time_in_sec,
            'country_code' => $this->country_code,
            'language' => $this->language,
            'device_name' => $this->device_name,
            'system_version' => $this->system_version,
            'machine' => $this->machine,
            'carrier_info' => $this->carrier_info,
            'memory' => $this->memory,
            'disk' => $this->disk,
            'sys_file_time' => $this->sys_file_time,
            'model' => $this->model,
            'time_zone' => $this->time_zone,
            // TODO: 等 iOS 客户端 CAID SDK 升级后上报上来
            // 'mnt_id' => $this->mnt_id,
            // 'device_init_time' => $this->device_init_time,
        ]);
    }

}
