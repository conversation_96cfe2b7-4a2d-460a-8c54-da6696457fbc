<?php

namespace app\models;

use missevan\util\MUtils as MUtils2;
use Exception;
use Yii;
use yii\helpers\Json;
use yii\web\HttpException;

/**
 * This is the model class for table "m_user_history".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 * @property int $delete_time 删除时间
 * @property int $user_id 用户 ID
 * @property int $element_id 元素 ID
 * @property int $element_type 元素类型
 * @property int $access_time 访问时间（毫秒）
 * @property array $more 更多详情
 *
 * @property-read bool isSound 是否属于音频历史记录
 * @property-read bool isDrama 是否属于剧集历史记录
 * @property-read bool isRoom 是否属于直播间历史记录
 */
class MUserHistory extends ActiveRecord
{
    // 元素类型：音频、剧集、直播间
    const ELEMENT_TYPE_SOUND = 1;
    const ELEMENT_TYPE_DRAMA = 2;
    const ELEMENT_TYPE_LIVE_ROOM = 3;

    // 历史记录最大长度
    const HISTORY_MAX_LENGTH = 500;

    // delete_time 类型
    const DELETE_TIME_NOT_DELETED = 0;
    const DELETE_TIME_ARCHIVED = -1;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_user_history';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'element_id', 'element_type', 'access_time'], 'required'],
            [['create_time', 'modified_time', 'delete_time', 'user_id', 'element_id', 'element_type', 'access_time'], 'integer'],
            [['more'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'delete_time' => '删除时间',
            'user_id' => '用户 ID',
            'element_id' => '元素 ID',
            'element_type' => '元素类型',
            'access_time' => '最新访问时间',  // 毫秒
            'more' => '更多详情',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    private static function parseMarker(string $marker): array
    {
        if (!$marker) {
            return [null, null];
        }

        $parts = explode(':', $marker);
        if (count($parts) !== 2) {
            throw new HttpException(400, '参数错误');
        }
        [$label, $last_time] = $parts;
        if ($label !== 'last_time' || $last_time < 0) {
            throw new HttpException(400, '参数错误');
        }
        $last_time = (int)$last_time;
        return [
            $label,
            $last_time,
        ];
    }

    private static function generateMarker(int $last_time): string
    {
        return sprintf('last_time:%d', $last_time);
    }

    private static function getRecords(int $user_id, string $marker, int $page_size)
    {
        $query = self::find()
            ->select('id, element_type, element_id, access_time, more')
            ->where(['user_id' => $user_id, 'delete_time' => self::DELETE_TIME_NOT_DELETED])
            ->orderBy('access_time DESC');

        [, $last_time] = self::parseMarker($marker);
        if ($last_time > 0) {
            $query->andWhere('access_time < :last_time', [':last_time' => $last_time]);
        }

        $records = $query->limit($page_size)->all();
        $has_more = false;
        $new_marker = null;
        if (count($records) === $page_size) {
            $new_marker = self::generateMarker($records[$page_size - 1]['access_time']);
            $has_more = true;
        }
        return [
            $records,
            $has_more,
            $new_marker,
        ];
    }

    public function getIsSound()
    {
        return $this->element_type === self::ELEMENT_TYPE_SOUND;
    }

    public function getIsDrama()
    {
        return $this->element_type === self::ELEMENT_TYPE_DRAMA;
    }

    public function getIsRoom()
    {
        return $this->element_type === self::ELEMENT_TYPE_LIVE_ROOM;
    }

    /**
     * TODO: 后续改为从 rpc 接口获取历史记录
     * 获取播放记录
     *
     * @param int $user_id
     * @param string $marker
     * @param $page_size
     * @return array
     * @throws Exception
     */
    public static function getList(int $user_id, string $marker, int $page_size): array
    {
        [$records, $has_more, $new_marker] = self::getRecords($user_id, $marker, $page_size);
        $model = new MUserHistoryList($user_id, $records);
        $list = [
            'data' => $model->getResult(),
            'has_more' => $has_more,
        ];
        if ($list['has_more']) {
            $list['marker'] = $new_marker;
        }
        return $list;
    }

    public static function newSound(int $user_id, int $access_time, int $sound_id, float $completion)
    {
        $history = new self([
            'user_id' => $user_id,
            'element_type' => self::ELEMENT_TYPE_SOUND,
            'element_id' => $sound_id,
            'access_time' => $access_time,
            'more' => ['completion' => $completion],
        ]);
        $history->add();
    }

    public static function newDrama(int $user_id, int $access_time, int $drama_id, int $sound_id, float $completion, int $node_id = 0)
    {
        $more = ['last_play_sound' => ['id' => $sound_id]];
        if ($node_id) {
            $more['node'] = ['id' => $node_id, 'completion' => $completion];
        } else {
            $more['last_play_sound']['completion'] = $completion;
        }
        $history = new self([
            'user_id' => $user_id,
            'element_type' => self::ELEMENT_TYPE_DRAMA,
            'element_id' => $drama_id,
            'access_time' => $access_time,
            'more' => $more,
        ]);
        $history->add();
    }

    public static function newLiveRoom(int $user_id, int $access_time, int $room_id)
    {
        $history = new self([
            'user_id' => $user_id,
            'element_type' => self::ELEMENT_TYPE_LIVE_ROOM,
            'element_id' => $room_id,
            'access_time' => $access_time,
        ]);
        $history->add();
    }

    private static function archiveOldHistory(int $user_id, int $count)
    {
        if ($count <= 0) {
            throw new Exception('参数错误');
        }
        // delete_time != 0 的记录将通过 DMS 定时归档至 DLA ODS 层，腾出 m_user_history 空间
        return self::getDb()
            ->createCommand(
                'UPDATE ' . self::tableName()
                . ' SET delete_time = :archived, modified_time = :modified_time'
                . ' WHERE user_id = :user_id AND delete_time = :valid'
                . ' ORDER BY access_time ASC LIMIT :limit',
                [
                    ':modified_time' => $_SERVER['REQUEST_TIME'],
                    ':archived' => self::DELETE_TIME_ARCHIVED,
                    ':valid' => self::DELETE_TIME_NOT_DELETED,
                    ':user_id' => $user_id,
                    ':limit' => $count,
                ])
            ->execute();
    }

    /**
     * 获取历史记录数量
     *
     * @param int $user_id 用户 ID
     * @param bool $no_limit 是否不限制获取到的历史记录最大数量
     * @return int
     * @throws Exception
     */
    public static function getCount(int $user_id, bool $no_limit = false): int
    {
        $count = (int)self::find()->where(['user_id' => $user_id, 'delete_time' => self::DELETE_TIME_NOT_DELETED])->count();
        return $no_limit ? $count : min($count, self::HISTORY_MAX_LENGTH);
    }

    /**
     * 添加历史记录
     *
     * @link https://github.com/MiaoSiLa/requirements-doc/pull/525
     * @throws Exception
     */
    private function add()
    {
        if ($this->user_id <= 0 || $this->element_type <= 0 || $this->element_id <= 0 || $this->access_time <= 0) {
            throw new Exception('参数错误');
        }
        if (!$this->isNewRecord) {
            return;
        }

        $history = self::findOne([
            'user_id' => $this->user_id,
            'element_id' => $this->element_id,
            'element_type' => $this->element_type,
            'delete_time' => self::DELETE_TIME_NOT_DELETED,
        ]);
        if (is_null($history)) {
            if (!$this->save()) {
                throw new Exception(MUtils2::getFirstError($this));
            }
            $all_count = self::getCount($this->user_id, true);
            if ($all_count > self::HISTORY_MAX_LENGTH) {
                // 超出最大条数的部分进行软删除
                // 若删除历史数据且写库数据没有及时同步到读库时，在获取历史记录数量时会从读库获取到超过 HISTORY_MAX_LENGTH 数量的历史数据
                // TODO: 会出现每次只删除一个情形，需要优化
                self::archiveOldHistory($this->user_id, $all_count - self::HISTORY_MAX_LENGTH);
            }
        } else {
            $history->access_time = $this->access_time;
            $history->more = $this->more;
            if (!$history->save()) {
                throw new Exception(MUtils2::getFirstError($history));
            }
        }
    }

    /**
     * 获取音频所属剧集 ID 及用户播放的最后一个节点 ID（为互动剧的话）
     * 此方法仅给未给相关接口传 drama_id 及 node_id 的客户端旧版本使用
     *
     * @param int $sound_id 剧集 ID
     * @param int $user_id 用户 ID
     * @return array 剧集 ID 与节点 ID，无数据时返回 [0, 0]
     * @throws Exception
     */
    public static function getDramaIdAndNodeId(int $sound_id, int $user_id)
    {
        $drama_id = $node_id = 0;
        $memcache = Yii::$app->memcache;
        $key = MUtils2::generateCacheKey(KEY_ADD_PLAY_TIMES_SOUND_INFO, $sound_id);
        $info = $memcache->get($key);
        if ($info && $drama_info = Json::decode($info)) {
            $drama_id = $drama_info['drama_id'];
            if ($drama_info['is_interactive']) {
                $node_id = MUserNodeLog::getUserCurrentNodeID($user_id, $sound_id, Yii::$app->equip->getEquipId());
            }
        } else {
            $ret = Drama::rpc('api/get-drama-paytype-by-sound', ['sound_ids' => [$sound_id]]);
            $is_interactive = false;
            if ($ret && array_key_exists($sound_id, $ret)) {
                $drama = $ret[$sound_id];
                $drama_id = $drama['drama_id'];
                $is_interactive = $drama['is_interactive'];
            }
            $memcache->set($key, Json::encode(['drama_id' => $drama_id, 'is_interactive' => $is_interactive]), TEN_MINUTE);
            if ($is_interactive) {
                $node_id = MUserNodeLog::getUserCurrentNodeID($user_id, $sound_id, Yii::$app->equip->getEquipId());
            }
        }
        return [$drama_id, $node_id];
    }
}
