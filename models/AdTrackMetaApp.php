<?php

namespace app\models;

use Exception;
use Yii;

class AdTrack<PERSON><PERSON>App extends AdTrack implements AdTrackInterface
{
    const CALLBACK_EVENT_TYPE_MAP = [
        self::CALLBACK_EVENT_ACTIVATE => self::CALLBACK_EVENT_TYPE_ACTIVATE,
        self::CALLBACK_EVENT_ONE_DAY_RETENTION => self::CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION,
        self::CALLBACK_EVENT_PAY => self::CALLBACK_EVENT_TYPE_PAY,
        self::CALLBACK_EVENT_REGISTER => self::CALLBACK_EVENT_TYPE_REGISTER,
        self::CALLBACK_EVENT_KEY_ACTION => null,
        self::CALLBACK_EVENT_TRANSACTION => null,  // 无消费 / 充值关键行为
    ];
    const CALLBACK_EVENT_TYPE_ACTIVATE = 100500;  // 激活
    const CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION = 100504;  // 次日留存
    const CALLBACK_EVENT_TYPE_PAY = 100502;  // 付费
    const CALLBACK_EVENT_TYPE_REGISTER = 100501;  // 注册

    /**
     * @param string $event_type
     * @param mixed $arg
     * @return bool
     */
    public function callback(string $event_type, $arg = 0)
    {
        try {
            if (!array_key_exists($event_type, self::CALLBACK_EVENT_TYPE_MAP)) {
                throw new Exception('MetaApp 点击回传事件错误：' . $event_type);
            }
            $event = self::CALLBACK_EVENT_TYPE_MAP[$event_type];
            if (is_null($event)) {
                return true;
            }
            $data = Yii::$app->tools->requestRemote($this->getCallbackUrl($event, $arg));
            if (!($data && $data['code'] === 200)) {
                throw new Exception(sprintf('MetaApp 广告点击回传失败：code[%d], msg[%s]',
                    $data['code'] ?? $data['return_code'], $data['msg'] ?? $data['return_msg']));
            }
            return true;
        } catch (Exception $e) {
            Yii::error('MetaApp ad error: ' . $e->getMessage(), __METHOD__);
        }
        return false;
    }

    private function getCallbackUrl(int $callback_event, $arg = 0)
    {
        return preg_replace('/statsType=\d+/', "statsType={$callback_event}", $this->track_id);
    }
}
