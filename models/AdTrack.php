<?php

namespace app\models;

use missevan\util\MUtils as MUtils2;
use app\components\util\Equipment;
use app\components\util\MUtils;
use Exception;
use missevan\util\NotImplementedException;
use Yii;
use yii\db\Expression;
use yii\db\Query;
use yii\helpers\Json;
use yii\web\HttpException;

/**
 * This is the model class for table "ad_track".
 *
 * @link https://info.missevan.com/pages/viewpage.action?pageId=62162461
 * @property int $id 主键
 * @property string $track_id 用于追踪来源广告的追踪 ID
 * @property string $creative_id 创意 ID，用于广告主与自己平台上的创意进行映射
 * @property string $equip_id 设备号
 * @property string $idfa iOS IDFA
 * @property string $android_id_md5 Android ID MD5 摘要
 * @property string $imei_md5 IMEI MD5 摘要
 * @property string $mac_md5 MAC 地址 MD5 摘要
 * @property string $mac2_md5 MAC 地址 MD5 摘要
 * @property string $ua 用户代理
 * @property string $ip 客户端 IP
 * @property int $os 设备类型: 1 为 Android; 2 为 iOS；6 为 HarmonyOS
 * @property string $click_time 点击的时间戳（毫秒）
 * @property int $vendor 广告平台（0 Bilibili）
 * @property array $more 更多详情
 * @property string $buvid buvid
 * @property string $bili_buvid bilibili 点击传来的 buvid
 * @property string $mid bilibili mid
 * @property int $ad_from 广告位来源
 * @property string $idfa_md5 iOS IDFA MD5
 * @property string $project_id 广告计划 ID
 * @property string $group_id 广告组 ID
 * @property string $oaid Android 设备匿名标识符
 * @property string $oaid_md5 Android OAID MD5
 * @property int $converted 是否已转化
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 *
 * @property string $currentTableName
 */
class AdTrack extends ActiveRecord
{
    const VENDOR_BILIBILI = 0;
    const VENDOR_DOUYIN = 1;
    const VENDOR_KUAISHOU = 2;
    const VENDOR_TENGXUN = 3;
    const VENDOR_YOUDAO = 4;
    const VENDOR_WEIBO = 5;
    const VENDOR_BAIDU_SEARCH = 6;
    const VENDOR_BAIDU_INFO_FLOW = 7;
    const VENDOR_METAAPP = 8;
    const VENDOR_CILIJUXING = 9;
    const VENDOR_APPLE = 10;
    const VENDOR_APPSFLYER = 11;
    const VENDOR_WANGYIYUN = 12;
    const VENDOR_IQIYI = 13;
    const VENDOR_YIXIAO = 14;
    const VENDOR_VIVO = 15;
    const VENDOR_XINGTU = 16;
    const VENDOR_SIGMOB = 17;
    const VENDOR_XIMALAYA = 18;
    const VENDOR_XUANYI = 19;
    const VENDOR_THIRDPARTY = 20;  // 从内部落地页第三方渠道（手淘芭芭农场、B漫等）
    const VENDOR_XIAOHONGSHU = 21;

    const NOT_CONVERTED = 0;
    const CONVERTED = 1;

    const CONVERT_STAGE_ACTIVATE = 1 << 0;
    const CONVERT_STAGE_ONE_DAY_RETENTION = 1 << 1;
    const CONVERT_STAGE_PAY = 1 << 2;
    const CONVERT_STAGE_REGISTER = 1 << 3;
    const CONVERT_STAGE_KEY_ACTION = 1 << 4;
    const CONVERT_STAGE_TRANSACTION = 1 << 5;
    const CONVERT_STAGE_APP_CALLUP = 1 << 6;

    // 广告分级
    const AD_LEVEL_ONE = 0;
    const AD_LEVEL_TWO = 1;
    const AD_LEVEL_THREE = 2;

    // 归因取数范围
    const TRACK_TIME_PERIOD = ONE_DAY;

    // 访问到指定数量的音频时，做付费回传
    const VISIT_SOUND_NUM = 5;

    // 注释为 mac_md5 对应的明文
    const BLACK_LIST_MAC_MD5 = [
        '0f607264fc6318a92b9e13c65db7cd3c',  // 02:00:00:00:00:00
        'd41d8cd98f00b204e9800998ecf8427e',  // 空值
        '528c8e6cd4a3c6598999a0e9df15ad32',  // 00:00:00:00:00:00
        '00865256196d82d9663ead08c67ce04e',  // 02:00:00:00:00:02
    ];

    // 注释为 imei_md5 对应的明文
    const BLACK_LIST_IMEI_MD5 = [
        '5284047f4ffb4e04824a2fd1d1f0cd62',  // 000000000000000
        '21371d265b5711b289344b479f583909',  // 012345678912345
        '4b997118e2db4480b95d28cc741b3917',  // 812345678912345
        '80f54f6966ab4a4cd3c913e653cc4180',  // 812345678912343
        'dd4b21e9ef71e1291183a46b913ae6f2',  // 00000000
    ];

    const BLACK_LIST_OAID = [
        '00000000-0000-0000-0000-000000000000',
    ];

    const BLACK_LIST_IDFA = [
        '00000000-0000-0000-0000-000000000000',
    ];

    // 广告归因类型（1: 拉新；2: 促活）
    const AD_ATTRIBUTION_TYPE_APP_ACTIVATE = 1;
    const AD_ATTRIBUTION_TYPE_APP_CALLUP = 2;

    // 关键行为回传类型（1: 充值；2: 消费）
    const CONVERSION_TRANSACTION_TYPE_PAY = 1;
    const CONVERSION_TRANSACTION_TYPE_CONSUME = 2;

    // 广告事件类型（1：拉新广告；2：促活广告）
    const AD_EVENT_TYPE_ACTIVATE = 1;
    const AD_EVENT_TYPE_CALLUP = 2;

    // 检查 mac 是否在黑名单列表
    public static function isBlackMacMd5($mac)
    {
        return in_array(strtolower($mac), self::BLACK_LIST_MAC_MD5);
    }

    // 检查 imei 是否在黑名单列表
    public static function isBlackImeiMd5($imei)
    {
        return in_array(strtolower($imei), self::BLACK_LIST_IMEI_MD5);
    }

    protected $_current_table_name;

    /**
     * 本次归因的途径（用于微博和小红书回传时指定回传的设备信息字段）
     *
     * @var int
     */
    public $tracked_type = 0;

    const TRACKED_TYPE_IDFA = 1;
    const TRACKED_TYPE_UA_IP = 2;
    const TRACKED_TYPE_BUVID = 3;
    const TRACKED_TYPE_OAID = 4;
    const TRACKED_TYPE_ANDROID_ID = 5;
    const TRACKED_TYPE_IMEI_IP = 6;
    const TRACKED_TYPE_MAC_IP = 7;
    const TRACKED_TYPE_CAID = 8;
    const TRACKED_TYPE_IP_DEVICE_SCREEN = 9;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'ad_track';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->growthdb;
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['os'], 'required'],
            [['os', 'click_time', 'vendor', 'create_time', 'modified_time'], 'integer'],
            [['track_id', 'ua'], 'string', 'max' => 500],
            [['creative_id', 'project_id', 'group_id'], 'string', 'max' => 50],
            [['equip_id', 'idfa'], 'string', 'max' => 36],
            [['oaid'], 'string', 'max' => 100],
            [['ip'], 'string', 'max' => MUtils2::IP_MAX_LENGTH],
            [['android_id_md5', 'imei_md5', 'mac_md5', 'oaid_md5'], 'string', 'max' => 32],
            [['more'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'track_id' => '用于追踪来源广告的追踪 ID',
            'creative_id' => '创意 ID',  // 用于广告主与自己平台上的创意进行映射
            'project_id' => '广告计划 ID',
            'group_id' => '广告组 ID',
            'oaid' => 'Android 设备匿名标识符',
            'oaid_md5' => 'Android OAID MD5',
            'equip_id' => '设备号',
            'idfa' => 'iOS IDFA',
            'android_id_md5' => 'Android ID MD5 摘要',
            'imei_md5' => 'IMEI MD5 摘要',
            'mac_md5' => 'MAC 地址 MD5 摘要',
            'mac2_md5' => 'MAC 地址 MD5 摘要',
            'ua' => '用户代理',
            'ip' => '客户端 IP',
            'os' => '设备类型',  // 1 为 Android; 2 为 iOS，6 为 HarmonyOS
            'click_time' => '点击的时间戳（毫秒）',
            'vendor' => '广告平台',  // 0 Bilibili
            'more' => '更多详情',  // json 格式（存 bili_buvid, mid, from, idfa_md5, mac2_md5 等）
            'buvid' => 'buvid',
            'bili_buvid' => 'bilibili 点击传来的 buvid',
            'mid' => 'bilibili mid',
            'ad_from' => '广告位来源',
            'idfa_md5' => 'iOS IDFA MD5',
            'converted' => '是否已转化',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
            $this->vendor = $this->getAdVendor();
        }
        $this->modified_time = $time;
        return true;
    }

    public function getAdVendor(): int
    {
        throw new NotImplementedException('method should be implemented');
    }

    public function afterFind()
    {
        parent::afterFind();
        $this->ad_from = (int)$this->ad_from;
    }

    public function setCurrentTableName(string $table_name)
    {
        $this->_current_table_name = $table_name;
    }

    public function getCurrentTableName()
    {
        return $this->_current_table_name;
    }

    protected function isAndroid()
    {
        return Equipment::Android === (int)$this->os;
    }

    protected function isIOS()
    {
        return Equipment::iOS === (int)$this->os;
    }

    /**
     * @param InstallLog|InstallLogAndroid|InstallLogIOS $install_log
     * @param int $ad_attribution_type 广告归因类型 (1: 拉新；2: 促活)
     * @param int $vendor 广告平台
     * @return static|null
     */
    public static function getRecord(InstallLog $install_log, int $ad_attribution_type = self::AD_ATTRIBUTION_TYPE_APP_ACTIVATE, int $vendor = self::VENDOR_BILIBILI)
    {
        $query = self::find()
            ->select('*')
            // 对于 B 站广告：效果广告、CPT 广告的优先级高于花火邀约广告
            ->addSelect(new Expression('IF(vendor = :vendor_bili AND ad_from = :bili_huahuo, 0, 1) AS level',
                [':vendor_bili' => self::VENDOR_BILIBILI, ':bili_huahuo' => AdTrackBilibili::FROM_BILIBILI_HUAHUO_AD]))
            ->where(['os' => $install_log->device_type, 'converted' => self::NOT_CONVERTED]);
        if ($ad_attribution_type === self::AD_ATTRIBUTION_TYPE_APP_CALLUP) {
            $query = $query->andWhere('vendor = :vendor', [':vendor' => $vendor])
                ->andWhere('JSON_EXTRACT(more, "$.ad_event_type") = :ad_event_type',
                    [':ad_event_type' => self::AD_EVENT_TYPE_CALLUP])
                // 促活只归因一个小时内的广告点击记录
                // 广告回传文档：https://info.bilibili.co/pages/viewpage.action?pageId=*********
                ->andWhere('create_time > :from_time', [':from_time' => $_SERVER['REQUEST_TIME'] - ONE_HOUR]);
        } else {
            $query = $query->andWhere('JSON_EXTRACT(more, "$.ad_event_type") = :ad_event_type OR JSON_EXTRACT(more, "$.ad_event_type") IS NULL',
                    [':ad_event_type' => self::AD_EVENT_TYPE_ACTIVATE])
                ->andWhere('create_time > :from_time', [':from_time' => $_SERVER['REQUEST_TIME'] - self::TRACK_TIME_PERIOD]);
        }
        $query = $query
            ->orderBy('level DESC, id DESC')
            ->limit(1);

        if ($install_log->isIOSApp()) {
            $record_list = AdTrack::trackIOSAdClickRecord($query, null, $install_log);
        } else {
            // TODO: 广告投放归因逻辑 HarmonyOS 暂时同 Android，待媒体侧文档更新后进行调整
            $record_list = AdTrack::trackAndroidAdClickRecord($query, null, $install_log);
        }

        // 取最新的一条点击记录
        return array_reduce($record_list, function ($result, $item) {
            /**
             * @var self $item
             */
            if (!$result) {
                $result = $item;
            }
            if ($item->vendor !== $result->vendor && $item->create_time > $result->create_time) {
                $result = $item;
            }
            return $result;
        }, null);
    }

    /**
     * 获取从落地页上报渠道（手淘芭芭农场、B漫等）的点击记录
     *
     * @param InstallLog $install_log
     * @return AdTrack|null
     */
    public static function getThirdPartyRecord(InstallLog $install_log)
    {
        $device_brand_name = $install_log->more['device_info']['brand_name'] ?? null;
        $device_model = $install_log->more['device_info']['model'] ?? null;
        $screen_resolution = $install_log->more['screen_resolution'] ?? null;
        $screen_dpr = $install_log->more['screen_dpr'] ?? null;
        if (!$device_brand_name || !$device_model || !$screen_resolution || !$screen_dpr) {
            // 品牌名称、机型、手机屏幕分辨率和设备像素比中有空值时直接返回 null，不进行归因
            return null;
        }
        /**
         * @var AdTrack $ad_track
         */
        $ad_track = self::find()
            ->where([
                'vendor' => self::VENDOR_THIRDPARTY,
                'converted' => self::NOT_CONVERTED,
                'ip' => $install_log->ip,
                'device_brand_name' => $device_brand_name,
                'device_model' => $device_model,
                'screen_resolution' => $screen_resolution,
                'screen_dpr' => $screen_dpr,
            ])
            ->andWhere('create_time > :from_time', [':from_time' => $_SERVER['REQUEST_TIME'] - self::TRACK_TIME_PERIOD])
            ->orderBy('id DESC')
            ->limit(1)
            ->one();
        if (!$ad_track) {
            return null;
        }
        $ad_track = new AdTrackThirdparty($ad_track);
        if (!$ad_track->isValidTracked()) {
            return null;
        }
        $ad_track->currentTableName = self::tableName();
        $ad_track->tracked_type = self::TRACKED_TYPE_IP_DEVICE_SCREEN;
        return $ad_track;
    }

    /**
     * @param Query $query
     * @param null|string $old_table
     * @param InstallLogIOS $install_log
     * @return array
     */
    public static function trackIOSAdClickRecord(Query $query, ?string $old_table, InstallLogIOS $install_log)
    {
        $record_list = [];
        if ($install_log->getIDFA() && !in_array($install_log->getIDFA(), self::BLACK_LIST_IDFA)) {
            $record = self::getRecordByCondition($query, ['idfa_md5' => md5(strtoupper($install_log->adid))], $old_table);
            if ($record) {
                $record->tracked_type = self::TRACKED_TYPE_IDFA;
                $record_list[] = $record;
            }
        }
        if ($install_log->user_agent && $install_log->ip) {
            $record = self::getRecordByCondition($query, ['ua' => $install_log->user_agent, 'ip' => $install_log->ip], $old_table);
            if ($record) {
                $record->tracked_type = self::TRACKED_TYPE_UA_IP;
                $record_list[] = $record;
            }
        }
        if ($install_log->more && array_key_exists('caid', $install_log->more)) {
            $record = self::trackClickRecordByCAID($query, $install_log, $old_table);
            if ($record) {
                $record->tracked_type = self::TRACKED_TYPE_CAID;
                $record_list[] = $record;
            }
        }

        return $record_list;
    }

    // TODO: 后续移除 $old_table 的逻辑（ad_track 现在为分区表）
    public static function trackClickRecordByCAID(Query $query, InstallLog $install_log, ?string $old_table = null)
    {
        if (!($install_log->more && array_key_exists('caid', $install_log->more))) {
            return null;
        }

        $condition = [];
        foreach ($install_log->more['caid'] as $version => $caid_value) {
            $condition[] = "{$version}_{$caid_value}";
            // 有部分媒体 CAID 回传的是 MD5 值（如星图）
            $condition[] = sprintf('%s_%s', $version, md5($caid_value));
        }
        // 例：JSON_OVERLAPS(more->'$.caid', CAST('["20220111_c1cec971df9af1dae3cea41000cf2452","20201230_2fdcc7dd0e52de2a1b7e080f1ea082b8"]' AS JSON))
        return self::getRecordByCondition($query, sprintf("JSON_OVERLAPS(more->'$.caid', CAST('%s' AS JSON))", Json::encode($condition)));
    }

    /**
     * @param Query $query
     * @param null|string $old_table
     * @param InstallLogAndroid $install_log
     * @return array
     */
    public static function trackAndroidAdClickRecord(Query $query, ?string $old_table, InstallLogAndroid $install_log)
    {
        $record_list = [];
        if ($install_log->buvid) {
            $record = self::getRecordByCondition($query, ['bili_buvid' => $install_log->buvid], $old_table);
            if ($record) {
                $record->tracked_type = self::TRACKED_TYPE_BUVID;
                $record_list[] = $record;
            }
        }
        if ($install_log->oaid && !in_array($install_log->oaid, self::BLACK_LIST_OAID)) {
            $record = self::getRecordByCondition($query, ['oaid_md5' => $install_log->oaid_md5], $old_table);
            if ($record) {
                $record->tracked_type = self::TRACKED_TYPE_OAID;
                $record_list[] = $record;
            }
        }
        if ($install_log->android_id_md5) {
            $record = self::getRecordByCondition($query, ['android_id_md5' => $install_log->android_id_md5], $old_table);
            if ($record) {
                $record->tracked_type = self::TRACKED_TYPE_ANDROID_ID;
                $record_list[] = $record;
            }
        }
        if ($install_log->ip && $install_log->imei_md5 && !self::isBlackImeiMd5($install_log->imei_md5)) {
            $record = self::getRecordByCondition($query, ['ip' => $install_log->ip, 'imei_md5' => $install_log->imei_md5], $old_table);
            if ($record) {
                $record->tracked_type = self::TRACKED_TYPE_IMEI_IP;
                $record_list[] = $record;
            }
        }
        if ($install_log->ip && $install_log->mac_md5 && !self::isBlackMacMd5($install_log->mac_md5)) {
            $record = self::getRecordByCondition($query, ['ip' => $install_log->ip, 'mac_md5' => $install_log->mac_md5], $old_table);
            if ($record) {
                $record->tracked_type = self::TRACKED_TYPE_MAC_IP;
                $record_list[] = $record;
            }
        }

        return $record_list;
    }

    /**
     * 根据条件归因广告记录
     *
     * @param Query $query
     * @param array|string $condition
     * @param null|string $old_table
     * @param array $params
     * @return static|null
     */
    public static function getRecordByCondition(Query $query, $condition, ?string $old_table = null, array $params = [])
    {
        // 生成的 SQL 示例：
        /*
WITH t1 AS (
    SELECT *, IF( vendor = 0 AND ad_from = 1, 0, 1 ) AS LEVEL, 'ad_track' AS currentTableName
    FROM `ad_track`
    WHERE
        ( `os` = 1 )
        AND ( `converted` = 0 )
        AND ( equip_id IS NULL AND create_time > 1614479560 )
        AND ( `buvid` = 'XY1C82A1C458339D7CD182DFFD28AD03DB299' )
        AND ( JSON_EXTRACT(more, "$.ad_event_type") = 1 OR JSON_EXTRACT(more, "$.ad_event_type") IS NULL )
    ORDER BY `level` DESC, `id` DESC LIMIT 1
)
SELECT * FROM `t1` ORDER BY `level` DESC, `create_time` DESC LIMIT 1;
        */
        // TODO: 后续移除 $old_table 的逻辑（ad_track 现在为分区表）
        $q = new Query();
        $q->withQuery(
            (clone $query)
            ->addSelect(new Expression(':table_name1 AS currentTableName', [':table_name1' => self::tableName()]))
            ->andWhere($condition)
            ->addParams($params)
            ->from(self::tableName()),
            't1');

        if ($old_table) {
            $q->withQuery(
                (clone $query)
                ->addSelect(new Expression(':table_name2 AS currentTableName', [':table_name2' => $old_table]))
                ->andWhere($condition)
                ->addParams($params)
                ->from($old_table),
                't2'
            );

            $q->withQuery(
                (new Query())->from('t1')->union((new Query())->from('t2')), 't3'
            );
            $q->from('t3');
        } else {
            $q->from('t1');
        }

        $data = $q->orderBy('level DESC, create_time DESC')->limit(1)->one(self::getDb());
        if ($data) {
            unset($data['level']);
            /**
             * @var AdTrack $record
             */
            $class = self::getChildClass($data['vendor']);
            if (!$class) {
                Yii::error('ad track wrong vendor: ' . $data['vendor'], __METHOD__);
                return null;
            }
            if ($data['more']) {
                $data['more'] = Json::decode($data['more']);
            } else {
                $data['more'] = [];
            }
            $record = new $class($data);
            $record->id = (int)$record->id;
            $record->ad_from = (int)$record->ad_from;
            $record->vendor = (int)$record->vendor;
            $record->create_time = (int)$record->create_time;
            if ($record->isValidTracked()) {
                return $record;
            }
            return null;
        }

        return null;
    }

    private static function getChildClass($vendor)
    {
        switch ($vendor) {
            case self::VENDOR_BILIBILI:
                return AdTrackBilibili::class;
            case self::VENDOR_DOUYIN:
                return AdTrackDouYin::class;
            case self::VENDOR_KUAISHOU:
                return AdTrackKuaiShou::class;
            case self::VENDOR_TENGXUN:
                return AdTrackTengXun::class;
            case self::VENDOR_YOUDAO:
                return AdTrackYouDao::class;
            case self::VENDOR_WEIBO:
                return AdTrackWeibo::class;
            case self::VENDOR_BAIDU_SEARCH:
                return AdTrackBaiduSearch::class;
            case self::VENDOR_BAIDU_INFO_FLOW:
                return AdTrackBaiduInfoFlow::class;
            case self::VENDOR_METAAPP:
                return AdTrackMetaApp::class;
            case self::VENDOR_CILIJUXING:
                return AdTrackCiLiJuXing::class;
            case self::VENDOR_APPLE:
                return AdTrackApple::class;
            case self::VENDOR_APPSFLYER:
                return AdTrackAppsFlyer::class;
            case self::VENDOR_WANGYIYUN:
                return AdTrackWangYiYun::class;
            case self::VENDOR_IQIYI:
                return AdTrackIQiYi::class;
            case self::VENDOR_YIXIAO:
                return AdTrackYiXiao::class;
            case self::VENDOR_VIVO:
                return AdTrackVivo::class;
            case self::VENDOR_XINGTU:
                return AdTrackXingTu::class;
            case self::VENDOR_SIGMOB:
                return AdTrackSigmob::class;
            case self::VENDOR_XIMALAYA:
                return AdTrackXimalaya::class;
            case self::VENDOR_XUANYI:
                return AdTrackXuanYi::class;
            case self::VENDOR_THIRDPARTY:
                return AdTrackThirdparty::class;
            case self::VENDOR_XIAOHONGSHU:
                return AdTrackXiaohongshu::class;
        }

        return null;
    }

    public function isCallbackRequired()
    {
        return true;
    }

    /**
     * 是否属于有效归因
     * @link https://info.bilibili.co/pages/viewpage.action?pageId=*********
     *
     * @return bool
     */
    public function isValidTracked()
    {
        return $this->create_time > $_SERVER['REQUEST_TIME'] - HALF_DAY;
    }

    /**
     * 获取账户 ID
     *
     * @return string
     */
    public function getAccountID()
    {
        if (array_key_exists('account_id', $this->more)) {
            return $this->more['account_id'];
        }

        return '';
    }

    /**
     * 获取点击记录，按以下次序查询
     * Android: buvid > oaid => android_id > imei + ip > mac + ip
     * iOS: idfa > ua + ip
     *
     * @param InstallLog $install_log
     * @param int|null $target_vendor
     * @param array|AppsFlyerBody $attribution_data
     * @return array 例 [true, ['gender' => 1]]，第一个值为是否归因成功，第二值为性别信息
     * @throws HttpException
     */
    public static function track(InstallLog $install_log, ?int $target_vendor = null, $attribution_data = null)
    {
        if (is_null($target_vendor)) {
            $ad_track = self::getRecord($install_log);
            if (!$ad_track) {
                return [false, null];
            }
            $ad_track->markAsConverted($ad_track->currentTableName, $install_log);
            if ($ad_track->callbackActivate()) {
                $install_log->addToConvertedPool($ad_track->getUniqueTrackedId());
            }
        } elseif ($target_vendor === self::VENDOR_APPLE || $target_vendor === self::VENDOR_APPSFLYER) {
            /**
             * @var AdTrackApple|AdTrackAppsFlyer $ad_class
             */
            $ad_class = self::getChildClass($target_vendor);
            $ad_track = $ad_class::newRecord($install_log, $attribution_data);
            if (!$ad_track) {
                return [false, null];
            }
            if (!$ad_track->save()) {
                Yii::error('ad_track save failed: ' . MUtils2::getFirstError($ad_track), __METHOD__);
                throw new HttpException(500, '服务器内部错误');
            }
            $install_log->addToConvertedPool($ad_track->getUniqueTrackedId());
        } else {
            throw new Exception('暂不支持');
        }

        // 第三方导流（手淘、B漫等）暂时没有弹窗广告
        if (((int)$ad_track->vendor) === self::VENDOR_THIRDPARTY) {
            return [true, null];
        }

        return [true, $ad_track->matchPopup()];
    }

    /**
     * 内部落地页第三方渠道的广告归因
     *
     * @param InstallLog $install_log
     * @return boolean 是否归因成功
     * @throws HttpException
     */
    public static function thirdPartyTrack(InstallLog $install_log)
    {
        $ad_track = self::getThirdPartyRecord($install_log);
        if (!$ad_track) {
            return false;
        }
        // TODO: 第三方导流任务完成用户下载安装并打开猫耳 app 时，暂时只归因处理，不进行回调通知第三方服务
        $ad_track->markAsConverted($ad_track->currentTableName, $install_log);
        if ($ad_track->callbackActivate()) {
            $install_log->addToConvertedPool($ad_track->getUniqueTrackedId());
        }
        return true;
    }

    public function matchPopup()
    {
        // 根据广告设置用户画像分数
        // TODO: 若之后每个广告都设置了弹窗，此处设置分数可去掉
        $set_points = Persona::setPointsByAdTrack($this);
        if (!$popup = MRecommendPopup::getPopup($this)) {
            return null;
        }
        if (!$set_points) {
            // 若广告 ID 未设置画像分数，则通过弹窗画像设置画像分数
            Persona::setPointsByRecommendPopup($popup, $this->equip_id, $this->buvid);
        }
        $popup->bindEquipID($this);
        // WORKAROUND: iOS 4.6.3 以下、Android 5.5.2 以下返回性别字段
        if (Equipment::isAppOlderThan('4.6.3', '5.5.2')) {
            return ['gender' => $popup->gender];
        }
        // 对于苹果 ASA 广告归因调用链时间很长、且有未归因成功时的重试机制，让用户自己选择性别
        // 对于 AppsFlyer 归因则是异步过程，需要等 AppsFlyer 服务器通知，让用户自己选择性别
        if ($popup->gender && $this->vendor !== AdTrack::VENDOR_APPLE && $this->vendor !== AdTrack::VENDOR_APPSFLYER) {
            Yii::$app->serviceRpc->setPersona($popup->getPersonaByGender(), $this->equip_id, $this->buvid);
        }

        return null;
    }

    /**
     * 生成唯一的 ID
     *
     * @return string
     */
    public function getUniqueTrackedId()
    {
        return $this->currentTableName . '.' . $this->id;
    }

    /**
     * 根据唯一的 ID 获取记录
     *
     * @param string $unique_tracked_id
     * @return array|null|\yii\db\ActiveRecord|self
     * @throws Exception
     */
    public static function getRecordByUniqueTrackedId(string $unique_tracked_id)
    {
        [$table_name, $id] = explode('.', $unique_tracked_id);
        if (!$table_name || !$id) {
            throw new Exception('数据错误：' . $unique_tracked_id);
        }

        /**
         * @var self $record
         */
        $record = self::find()->from($table_name)->where(['id' => (int)$id])->one();
        if ($record) {
            $record->currentTableName = $table_name;
        }
        return $record;
    }

    /**
     * @param string $table_name
     * @param InstallLog $install_log
     * @param array $columns_update
     * @return bool
     */
    public function markAsConverted(string $table_name, InstallLog $install_log, array $columns_update = [])
    {
        try {
            $columns = [
                'converted' => self::CONVERTED,
                'equip_id' => $install_log->equip_id,
                'modified_time' => $_SERVER['REQUEST_TIME'],
            ];
            if ($install_log->buvid) {
                $columns['buvid'] = $install_log->buvid;
            }

            $this->setAttributes($columns);
            $status = self::getDb()->createCommand()->update($table_name,
                array_merge($columns, $columns_update),
                'id = :id AND converted = :not_converted',
                [':id' => $this->id, ':not_converted' => self::NOT_CONVERTED]
            )->execute();
            if (!$status) {
                return false;
            }
            return true;
        } catch (Exception $e) {
            Yii::error("ad track save equip id error：{$e->getMessage()}", __METHOD__);
            return false;
        }
    }

    /**
     * 广告点击回传
     *
     * @param string $event_type 事件类型（激活：activate，次日留存：one_day_retention，注册：register，充值：pay \
     * ，听了 5 首音关键行为：key_action，消费 / 充值关键行为：transaction，唤起 APP：app_callup）
     * @param mixed ...$args
     * event_type 为 register 时：args[0] 为 用户 ID
     * event_type 为 pay 时：args[0] 为 支付金额，args[1] 为 用户 ID
     * event_type 为 key_action 时：args[0] 为音频收听个数
     * event_type 为 transaction 时：args[0] 为 1（消费 / 充值次数）
     * @return mixed
     * @throws Exception
     */
    public function callbackVendor(string $event_type = 'activate', ...$args)
    {
        if (!in_array($event_type, ['activate', 'one_day_retention', 'register', 'pay', 'key_action', 'transaction', 'app_callup'])) {
            throw new Exception('参数错误');
        }

        $child_class = self::getChildClass($this->vendor);
        if (!$child_class) {
            Yii::error('ad track wrong vendor: ' . $this->vendor, __METHOD__);
            return false;
        }
        $model = $this->toInstance($child_class);
        if (!$model->isCallbackRequired()) {
            return false;
        }
        if ($model->callback($event_type, ...$args)) {
            $stage = 0;
            // NOTICE: JSON 字段默认为 NULL 的时候，无法写入数据
            // 这里向 ad_track 写入时，默认有值，不用处理
            if ($model->more && array_key_exists('stage', $model->more)) {
                $stage = $model->more['stage'];
            }
            $more = new Expression('JSON_SET(more, "$.stage", :stage)', [':stage' => $stage]);
            switch ($event_type) {
                case 'activate':
                    $stage |= self::CONVERT_STAGE_ACTIVATE;
                    $more = new Expression(
                        'JSON_SET(more, "$.stage", :stage, "$.activate_time", :time, "$.track_type", :track_type)',
                        [
                            ':stage' => $stage,
                            ':time' => $_SERVER['REQUEST_TIME'],
                            ':track_type' => $model->tracked_type,
                        ]);
                    break;
                case 'one_day_retention':
                    $stage |= self::CONVERT_STAGE_ONE_DAY_RETENTION;
                    $more = new Expression(
                        'JSON_SET(more, "$.stage", :stage, "$.one_day_retention_time", :time)',
                        [
                            ':stage' => $stage,
                            ':time' => $_SERVER['REQUEST_TIME']
                        ]);
                    break;
                case 'register':
                    $stage |= self::CONVERT_STAGE_REGISTER;
                    $more = new Expression(
                        'JSON_SET(more, "$.stage", :stage, "$.first_register_time", :register_time, "$.first_register_user_id", :register_user_id)',
                        [
                            ':stage' => $stage,
                            ':register_time' => $_SERVER['REQUEST_TIME'],
                            ':register_user_id' => $args[0]
                        ]);
                    break;
                case 'pay':
                    if (MUtils::bitIsSet($stage, self::CONVERT_STAGE_PAY)) {
                        $more = new Expression(
                            'JSON_SET(more, "$.last_pay_time", :last_pay_time, "$.total_pay_amount", IFNULL(JSON_EXTRACT(more, "$.total_pay_amount"), 0) + :total_pay_amount)',
                            [
                                ':last_pay_time' => $_SERVER['REQUEST_TIME'],
                                ':total_pay_amount' => $args[0]
                            ]);
                    } else {
                        $stage |= self::CONVERT_STAGE_PAY;
                        // 这里 0 + :pay_amount 是为了解决 float 类型在写入到 JSON 时，会被转为字符串的 BUG
                        $more = new Expression(
                            'JSON_SET(more, "$.stage", :stage, "$.first_pay_time", :pay_time, "$.first_pay_user_id", :pay_user_id, "$.first_pay_amount", 0 + :pay_amount, "$.total_pay_amount", 0 + :total_pay_amount)',
                            [
                                ':stage' => $stage,
                                ':pay_time' => $_SERVER['REQUEST_TIME'],
                                ':pay_user_id' => $args[1],
                                ':pay_amount' => $args[0],
                                ':total_pay_amount' => $args[0]
                            ]);
                    }
                    break;
                case 'key_action':
                    $stage |= self::CONVERT_STAGE_KEY_ACTION;
                    $more = new Expression(
                        'JSON_SET(more, "$.stage", :stage, "$.key_action_time", :time)',
                        [
                            ':stage' => $stage,
                            ':time' => $_SERVER['REQUEST_TIME']
                        ]);
                    break;
                case 'transaction':
                    $stage |= self::CONVERT_STAGE_TRANSACTION;
                    $more = new Expression(
                        'JSON_SET(more, "$.stage", :stage, "$.transaction_time", :time)',
                        [
                            ':stage' => $stage,
                            ':time' => $_SERVER['REQUEST_TIME']
                        ]);
                    break;
                case 'app_callup':
                    $stage |= self::CONVERT_STAGE_APP_CALLUP;
                    $more = new Expression(
                        'JSON_SET(more, "$.stage", :stage, "$.app_callup_time", :time)',
                        [
                            ':stage' => $stage,
                            ':time' => $_SERVER['REQUEST_TIME'],
                        ]);
                    break;
            }
            self::getDb()->createCommand()->update($this->getCurrentTableName(), [
                'more' => $more
            ], 'id = :id', [':id' => $this->id])->execute();
        }

        return true;
    }

    /**
     * @param string $class
     * @return static|AdTrackBilibili|AdTrackTengXun|AdTrackDouYin|AdTrackKuaiShou|AdTrackYouDao|AdTrackWeibo|AdTrackBaiduSearch|AdTrackBaiduInfoFlow|AdTrackMetaApp
     */
    public function toInstance(string $class)
    {
        if ($this instanceof $class) {
            return $this;
        } else {
            return new $class($this->toArray());
        }
    }

    public function callbackActivate()
    {
        return $this->callbackVendor('activate');
    }

    public function callbackOneDayRetention()
    {
        return $this->callbackVendor('one_day_retention');
    }

    /**
     * 唤起 APP 广告回传
     *
     * @param mixed ...$args 第三方导流换量回传时，args[0] 为第三方平台，args[1] 为第三方任务 token
     * @return bool
     * @throws Exception
     */
    public function callbackAppCallup(...$args)
    {
        return $this->callbackVendor('app_callup', ...$args);
    }

    /**
     * 注册回传
     *
     * @param int $user_id
     * @param string $buvid
     * @return bool
     * @throws Exception
     */
    public static function callbackRegister(int $user_id, ?string $buvid): bool
    {
        if (!$buvid) {
            return false;
        }

        $adtrack = self::getTrackedRecordFromPool(KEY_AD_TRACKED_BUVID, $buvid, function ($unique_track_id, $ttl) use ($user_id) {
            $redis = Yii::$app->redis;
            // user_id 记录用于付费的回传
            $key = $redis->generateKey(KEY_AD_TRACKED_USER_ID, $user_id);
            $redis->set($key, $unique_track_id, ['nx', 'ex' => $ttl]);
        });
        if (is_null($adtrack)) {
            return false;
        }
        // 注册回传截止时间：设备激活时间后的 2 个自然日，增加 1 分钟的容错
        if ($adtrack->isAlreadyRegisterCallback()
                || $_SERVER['REQUEST_TIME'] >= strtotime('+2 day midnight', $adtrack->getActivatedTime()) + ONE_MINUTE) {
            return false;
        }
        $adtrack->callbackVendor('register', $user_id);

        return true;
    }

    /**
     * 充值回传
     *
     * @param int $user_id 用户 ID
     * @param float $pay_amount 支付金额（单位：元）
     * @return bool
     */
    public static function callbackPay(int $user_id, float $pay_amount): bool
    {
        try {
            $adtrack = self::getTrackedRecordFromPool(KEY_AD_TRACKED_USER_ID, $user_id);
            if (is_null($adtrack)) {
                return false;
            }

            // 快手、抖音、腾讯、网易云、爱奇艺、百度（信息流、搜索）、Vivo、Sigmob、喜马拉雅、炫逸、小红书回传设备激活后 8 天内的所有付费金额，用于广告平台统计 ROI
            // ROI 文档：https://info.missevan.com/pages/viewpage.action?pageId=53184169
            switch ($adtrack->vendor) {
                case AdTrack::VENDOR_DOUYIN:
                case AdTrack::VENDOR_KUAISHOU:
                case AdTrack::VENDOR_TENGXUN:
                case AdTrack::VENDOR_WANGYIYUN:
                case AdTrack::VENDOR_IQIYI:
                case AdTrack::VENDOR_BAIDU_INFO_FLOW:
                case AdTrack::VENDOR_BAIDU_SEARCH:
                case AdTrack::VENDOR_VIVO:
                case AdTrack::VENDOR_SIGMOB:
                case AdTrack::VENDOR_XIMALAYA:
                case AdTrack::VENDOR_XUANYI:
                case AdTrack::VENDOR_XIAOHONGSHU:
                    $adtrack->callbackVendor('pay', $pay_amount, $user_id);
                    return true;
            }

            // 其它渠道，仅回传设备激活后首次付费记录
            $redis = Yii::$app->redis;
            // 付费行为由 user_id 来记录
            $redis->del($redis->generateKey(KEY_AD_TRACKED_USER_ID, $user_id));

            // 当回传用户已经支付回传过时，不再回传
            if ($adtrack->isAlreadyPayCallback()) {
                return false;
            }
            $adtrack->callbackVendor('pay', $pay_amount, $user_id);
            return true;
        } catch (Exception $e) {
            Yii::error(sprintf('pay callback error', $e->getMessage()));
        }

        return false;
    }

    /**
     * 访问到指定数量的音频时，做关键行为回传
     *
     * @deprecated 访问到指定数量的音频的关键行为回传已不再使用
     * @param int $user_id 用户 ID
     * @param int $sound_id 音频 ID
     * @param string|null $buvid 唯一设备标识
     * @return bool
     * @throws Exception
     */
    public static function callbackVisitSound(int $user_id, int $sound_id, ?string $buvid): bool
    {
        if (!$buvid) {
            return false;
        }
        try {
            $adtrack = self::getTrackedRecordFromPool(KEY_AD_TRACKED_BUVID, $buvid);
            if (is_null($adtrack)) {
                return false;
            }
            // 回传截止时间：设备激活时间后的 2 个自然日，增加 1 分钟的容错
            if ($adtrack->isAlreadyKeyActionCallback() || $_SERVER['REQUEST_TIME'] >= strtotime('+2 day midnight',
                $adtrack->modified_time) + ONE_MINUTE) {
                return false;
            }

            $redis = Yii::$app->redis;
            $key = $redis->generateKey(KEY_AD_TRACKED_BUVID_SOUND_ID, $buvid);

            [$sound_num, $exists] = $redis->multi()
                ->sCard($key)
                ->sIsMember($key, $sound_id)
                ->exec();
            if ($sound_num > self::VISIT_SOUND_NUM || $exists) {
                return false;
            }
            $adtrack_buvid_key = $redis->generateKey(KEY_AD_TRACKED_BUVID, $buvid);
            // 获取成功进行广告归因的设备剩余过期时间
            $ttl = $redis->ttl($adtrack_buvid_key);
            if ($ttl <= 0) {
                return false;
            }
            [$is_newly_added,] = $redis->multi()
                ->sAdd($key, $sound_id)
                ->expire($key, $ttl)
                ->exec();

            if (!$is_newly_added || ($sound_num + 1) < self::VISIT_SOUND_NUM) {
                return false;
            }

            $adtrack->callbackVendor('key_action', self::VISIT_SOUND_NUM);
            // 关键行为由 buvid 来记录
            $redis->del($key, $redis->generateKey(KEY_AD_TRACKED_BUVID, $adtrack->buvid));
            return true;
        } catch (Exception $e) {
            Yii::error('callbackVisitSound error: ' . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    /**
     * 消费 / 充值关键行为回传
     *
     * @param string|null $buvid 唯一设备标识
     * @param string 事件 ID
     * @return bool
     */
    public static function callbackTransaction(?string $buvid, string $event_id)
    {
        if (!$buvid) {
            return false;
        }
        try {
            $adtrack = self::getTrackedRecordFromPool(KEY_AD_TRACKED_BUVID, $buvid);
            if (is_null($adtrack)) {
                return false;
            }
            // 当用户已经回传过消费 / 充值关键行为时，不再回传
            if ($adtrack->isAlreadyTransactionCallback()) {
                return false;
            }
            // 快手渠道对于【整剧付费剧的剧集详情页下剧集目录模块里的单集音频点击】埋点不进行回传
            if ($adtrack->vendor === self::VENDOR_KUAISHOU && preg_match('/^drama\.drama_detail\.episode_list_.*\.click$/', $event_id)) {
                return false;
            }
            $adtrack->callbackVendor('transaction', 1);
            return true;
        } catch (Exception $e) {
            Yii::error('callbackTransaction error: ' . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    public function isAlreadyTransactionCallback(): bool
    {
        return MUtils::bitIsSet($this->more['stage'] ?? 0, self::CONVERT_STAGE_TRANSACTION);
    }

    public function isAlreadyKeyActionCallback(): bool
    {
        return MUtils::bitIsSet($this->more['stage'] ?? 0, self::CONVERT_STAGE_KEY_ACTION);
    }

    public function isAlreadyPayCallback(): bool
    {
        return MUtils::bitIsSet($this->more['stage'] ?? 0, self::CONVERT_STAGE_PAY);
    }

    public function isAlreadyRegisterCallback(): bool
    {
        return MUtils::bitIsSet($this->more['stage'] ?? 0, self::CONVERT_STAGE_REGISTER);
    }

    /**
     * 根据广告分级获取对应的 ID
     *
     * @param int $ad_level 广告分级
     * @return string
     * @throws Exception
     */
    public function getAdId(int $ad_level)
    {
        switch ($ad_level) {
            case self::AD_LEVEL_ONE:
                return $this->project_id;
            case self::AD_LEVEL_TWO:
                return $this->group_id;
            case self::AD_LEVEL_THREE:
                return $this->creative_id;
        }

        throw new Exception('参数错误');
    }

    /**
     * 根据广告分级获取对应的广告名称
     *
     * @param int $ad_level
     * @return string
     * @throws Exception
     */
    public function getAdName(int $ad_level)
    {
        if (!$this->more) {
            return '';
        }

        switch ($ad_level) {
            case self::AD_LEVEL_ONE:
                return array_key_exists('project_name', $this->more) ? $this->more['project_name'] : '';
            case self::AD_LEVEL_TWO:
                return array_key_exists('group_name', $this->more) ? $this->more['group_name'] : '';
            case self::AD_LEVEL_THREE:
                return array_key_exists('creative_name', $this->more) ? $this->more['creative_name'] : '';
        }

        throw new Exception('参数错误');
    }

    /**
     * 获取广告对应的弹窗 ID
     *
     * @return int
     * @throws Exception
     */
    public function getPopupId()
    {
        if ($this->vendor === self::VENDOR_XUANYI) {
            $popup_id = $this->getPopupIdFromMore();
            if ($popup_id) {
                return $popup_id;
            }
        }
        $ad_name = $this->getAdNameToParse();
        if ($ad_name) {
            return $this->parseTypeIdFromAdName($ad_name);
        }

        return 0;
    }

    /**
     * 从 more 字段中获取弹窗 ID
     */
    protected function getPopupIdFromMore()
    {
        return $this->more ? (int)($this->more['popup_id'] ?? 0) : 0;
    }

    /**
     * 可用于解析的广告名称
     *
     * @return string
     * @throws Exception
     */
    protected function getAdNameToParse()
    {
        return $this->getAdName(self::AD_LEVEL_TWO);
    }

    /**
     * 从广告名称中解析出指定类型的 ID
     *
     * @link https://info.missevan.com/pages/viewpage.action?pageId=35128492
     * @param string $ad_name 广告名称
     * @param string $type 指定类型（popup: 弹窗）
     * @return int
     */
    protected function parseTypeIdFromAdName(string $ad_name, string $type = 'popup')
    {
        if (empty($ad_name)) {
            return 0;
        }

        // 例：名称中包含 popup_233，则会解析出 233 的 ID 值
        // 格式：版位-广告形式-类别-细分-标题-{type}_{id}-创建日期
        // (创建日期之后可能被媒体追加其它额外信息)
        $str = strstr($ad_name, "{$type}_");
        if ($str === false) {
            return 0;
        }
        $part_str = substr($str, strlen("{$type}_"));
        $items = explode('-', $part_str);
        if (!ctype_digit($items[0])) {
            Yii::error(sprintf('wrong ad_name to parse: ad_name[%s], type[%s]', $ad_name, $type), __METHOD__);
            return 0;
        }

        return (int)$items[0];
    }

    /**
     * 获取激活的时间戳
     *
     * @return int
     */
    public function getActivatedTime(): int
    {
        if ($this->converted !== self::CONVERTED) {
            return 0;
        }

        return $this->more['activate_time'] ?? $this->modified_time;
    }

    public static function getTrackedRecordFromPool(string $key_pattern, $user_identifier, callable $func = null)
    {
        $redis = Yii::$app->redis;
        $key = $redis->generateKey($key_pattern, $user_identifier);
        $unique_adtrack_id = $redis->get($key);
        if (!$unique_adtrack_id) {
            return null;
        }
        if (is_callable($func)) {
            $func($unique_adtrack_id, $redis->ttl($key));
        }
        if (!$adtrack = self::getRecordByUniqueTrackedId($unique_adtrack_id)) {
            Yii::error(sprintf('未找到广告归因的记录：unique_adtrack_id[%s]', $unique_adtrack_id), __METHOD__);
            return null;
        }

        return $adtrack;
    }

    // ROI 回传支付金额优化系数
    const CALLBACK_PAY_ROI_FACTOR = 4.7;
    // ROI 回传支付金额单位
    const CALLBACK_PAY_UNIT_FEN = 0;
    const CALLBACK_PAY_UNIT_YUAN = 1;

    /**
     * 获取用于广告回传的付费金额值
     *
     * @link https://info.missevan.com/pages/viewpage.action?pageId=62162461
     * @param mixed $pay_amount_in_yuan 实际付费金额（单位：元）
     * @param int $currency_unit 单位转换类型
     * @return mixed
     * @throws HttpException
     */
    public function getCallbackPayAmount($pay_amount_in_yuan, $currency_unit = self::CALLBACK_PAY_UNIT_FEN)
    {
        $factor = self::CALLBACK_PAY_ROI_FACTOR;
        switch ($currency_unit) {
            case self::CALLBACK_PAY_UNIT_FEN:
                return Balance::profitUnitConversion($pay_amount_in_yuan * $factor, Balance::CONVERT_YUAN_TO_FEN);
            case self::CALLBACK_PAY_UNIT_YUAN:
                return $pay_amount_in_yuan * $factor;
            default:
                throw new Exception('未支持的 currency unit: ' . $currency_unit);
        }
    }

    /**
     * 设置 popup_id
     *
     * @param int $popup_id popup_id
     * @return bool
     */
    public function savePopupId(int $popup_id): bool
    {
        $more = new Expression(
            // JSON 字段默认为 NULL 的时候无法写入数据，故此处先通过 COALESCE 函数将 NULL 转为 {}
            'JSON_SET(COALESCE(more, "{}"), "$.popup_id", :popup_id)',
            [':popup_id' => $popup_id]
        );
        $time = $_SERVER['REQUEST_TIME'];
        return self::updateByPk($this->id, ['more' => $more, 'modified_time' => $time]) > 0;
    }
}
