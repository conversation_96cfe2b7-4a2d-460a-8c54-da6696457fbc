<?php

namespace app\models;

use missevan\storage\StorageClient;
use Yii;

/**
 * This is the model class for table "m_sound_subtitle".
 *
 * @property int $id primary key
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 * @property int $delete_time delete time
 * @property int $sound_id 音频 ID
 * @property string $subtitle_url 字幕地址
 */
class MSoundSubtitle extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_sound_subtitle';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['sound_id', 'subtitle_url'], 'required'],
            [['create_time', 'modified_time', 'delete_time', 'sound_id'], 'integer'],
            [['subtitle_url'], 'string', 'max' => 100],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'primary key',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'delete_time' => '删除时间',  // 大于 0 为删除
            'sound_id' => '音频 ID',
            'subtitle_url' => '字幕地址',
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        if (!is_null($this->subtitle_url)) {
            $this->subtitle_url = $this->subtitle_url ? StorageClient::getFileUrl($this->subtitle_url) : '';
        }
    }

    /**
     * 获取音频官方字幕
     *
     * @param int $sound_id
     * @return string
     */
    public static function getSubtitleUrl(int $sound_id)
    {
        /**
         * @var self $data
         */
        $data = self::find()
            ->select(['subtitle_url'])
            ->where(['sound_id' => $sound_id, 'delete_time' => 0])
            ->orderBy(['id' => SORT_DESC])
            ->limit(1)
            ->one();
        return $data ? $data->subtitle_url : '';
    }
}
