<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "m_user_like_danmaku".
 *
 * @property string $id 主键
 * @property string $create_time 创建时间
 * @property string $modified_time 修改时间
 * @property string $delete_time 删除时间，若不为 0，则表示已被软删除
 * @property string $user_id 点赞用户 ID
 * @property string $danmaku_id 弹幕 ID
 * @property string $element_id 弹幕所属元素 ID
 * @property string $element_type 弹幕所属元素类型，1：音频；2：互动剧节点
 */
class MUserLikeDanmaku extends ActiveRecord
{
    // 弹幕所属元素类型，1：音频；2：互动剧节点
    const ELEMENT_TYPE_SOUND = 1;
    const ELEMENT_TYPE_NODE = 2;

    /**
     * {@inheritdoc}
     */
    public static function getDb()
    {
        return Yii::$app->get('messagedb');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_user_like_danmaku';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'danmaku_id', 'element_id', 'element_type'], 'required'],
            [['create_time', 'modified_time', 'delete_time', 'user_id', 'danmaku_id', 'element_id', 'element_type'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'delete_time' => '删除时间',  // 若不为 0，则表示已被软删除
            'user_id' => '点赞用户 ID',
            'danmaku_id' => '弹幕 ID',
            'element_id' => '弹幕所属元素 ID',
            'element_type' => '弹幕所属元素类型',  // 1：音频；2：互动剧节点
        ];
    }

    /**
     * 获取用户在某元素（音频或互动节点）中点赞的弹幕 ID
     *
     * @param int $element_type
     * @param int $element_id
     * @param int $user_id
     * @return array
     */
    public static function getUserLikeDanmakuIds(int $element_type, int $element_id, int $user_id): array
    {
        return self::find()->select('danmaku_id')
            ->where([
                'user_id' => $user_id,
                'element_id' => $element_id,
                'element_type' => $element_type,
                'delete_time' => 0,
            ])
            ->column();
    }
}
