<?php

namespace app\models;

use missevan\util\MUtils as MUtils2;
use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "m_user_like_element".
 *
 * @property int $id 主键
 * @property int $user_id 用户 ID
 * @property int $element_type 元素类型，1：催眠专享音频
 * @property int $element_id 元素 ID
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 */
class MUserLikeElement extends ActiveRecord
{
    // 元素类型，1：催眠专享音频；
    const ELEMENT_TYPE_RADIO_SOUND = 1;

    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_user_like_element';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'element_type', 'element_id'], 'required'],
            [['id', 'user_id', 'element_type', 'element_id', 'create_time', 'modified_time'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => '用户 ID',
            'element_type' => '元素类型',  // 1：催眠专享音频；
            'element_id' => '元素 ID',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $now = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $now;
        }
        $this->modified_time = $now;
        return true;
    }

    /**
     * 用户是否喜欢
     *
     * @param int $user_id 用户 ID
     * @param int $element_type 元素类型
     * @param int $element_id 元素 ID
     * @return bool
     */
    public static function isLike(int $user_id, int $element_type, int $element_id): bool
    {
        return self::find()
            ->where(['user_id' => $user_id, 'element_type' => $element_type, 'element_id' => $element_id])
            ->exists();
    }

    /**
     * 喜欢或取消喜欢
     *
     * @param int $user_id 用户 ID
     * @param int $element_type 元素类型
     * @param int $element_id 元素 ID
     * @param int $is_like 是否为喜欢
     * @return bool
     * @throws
     */
    public static function likeOrNot(int $user_id, int $element_type, int $element_id, $is_like)
    {
        // 加锁
        $lock = Yii::$app->redis->generateKey(LOCK_USER_LIKE_ELEMENT, $user_id, $element_type, $element_id);
        if (!Yii::$app->redis->lock($lock, ONE_MINUTE)) {
            throw new HttpException(400, Yii::t('app/error',
                'The operation is too frequent. Please try again later'));
        }
        try {
            if ($is_like) {
                // 若为喜欢创建新记录
                $model = new self();
                $model->user_id = $user_id;
                $model->element_type = $element_type;
                $model->element_id = $element_id;
                $is_save = $model->save();
                if (!$is_save) {
                    // 保存失败一般为异常，记录到日志中
                    Yii::error('用户喜欢元素记录保存失败：' . MUtils2::getFirstError($model));
                }
                return $is_save;
            } else {
                return (bool)self::deleteAll([
                    'user_id' => $user_id,
                    'element_type' => $element_type,
                    'element_id' => $element_id
                ]);
            }
        } catch (\yii\db\Exception $e) {
            // 忽略唯一索引抛出的异常
            if (!MUtils2::isUniqueError($e, self::getDb())) {
                throw $e;
            }
            return true;
        } finally {
            // 解锁
            Yii::$app->redis->unlock($lock);
        }
    }
}
