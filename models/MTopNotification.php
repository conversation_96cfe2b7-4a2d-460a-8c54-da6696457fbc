<?php

namespace app\models;

use missevan\storage\StorageClient;
use yii\helpers\Json;

/**
 * This is the model class for table "m_top_notification".
 *
 * @property int $id 主键 ID
 * @property int $create_time 创建时间戳，单位：秒
 * @property int $modified_time 更新时间戳，单位：秒
 * @property int $notification_type 通知类型，1: iOS 充值页面通知
 * @property string $title 通知栏标题
 * @property string $subtitle 通知栏副标题
 * @property string $button_text 通知栏按钮文案
 * @property string $link 通知栏跳转链接
 * @property string $icon 通知栏图片
 * @property array $more
 */
class MTopNotification extends ActiveRecord
{
    public $icon_url;

    // TODO: 后续改为 notification_type 定义
    const IOS_TOPUP_MENU = 2;  // iOS 充值页面通知 ID

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_top_notification';
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->icon) {
            $this->icon_url = StorageClient::getFileUrl($this->icon);
        }
    }
}
