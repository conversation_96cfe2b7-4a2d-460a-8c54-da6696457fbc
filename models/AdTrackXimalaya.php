<?php

namespace app\models;

use Exception;
use Yii;
use yii\helpers\Json;

class AdTrackXimalaya extends AdTrack implements AdTrackInterface
{
    const CALLBACK_EVENT_TYPE_ACTIVATE = 'act';
    const CALLBACK_EVENT_TYPE_RETENTION = 'leave';
    const CALLBACK_EVENT_TYPE_REGISTER = 'register';
    const CALLBACK_EVENT_TYPE_PAY = 'pay';

    const CALLBACK_EVENT_TYPE_MAP = [
        self::CALLBACK_EVENT_ACTIVATE => self::CALLBACK_EVENT_TYPE_ACTIVATE,
        self::CALLBACK_EVENT_ONE_DAY_RETENTION => self::CALLBACK_EVENT_TYPE_RETENTION,
        self::CALLBACK_EVENT_REGISTER => self::CALLBACK_EVENT_TYPE_REGISTER,
        self::CALLBACK_EVENT_PAY => self::CALLBACK_EVENT_TYPE_PAY,
        self::CALLBACK_EVENT_KEY_ACTION => null,
        self::CALLBACK_EVENT_TRANSACTION => null,
        self::CALLBACK_EVENT_APP_CALLUP => null,
    ];

    /**
     * 转化事件回调
     *
     * @param string $event_type 事件类型
     * @param mixed $pay_amount 支付金额，仅付费场景传入此参数
     */
    public function callback(string $event_type, $pay_amount = 0)
    {
        try {
            if (!array_key_exists($event_type, self::CALLBACK_EVENT_TYPE_MAP)) {
                throw new Exception('喜马拉雅点击回传事件错误：' . $event_type);
            }
            $event = self::CALLBACK_EVENT_TYPE_MAP[$event_type];
            if (is_null($event)) {
                return true;
            }
            $data = Yii::$app->tools->requestRemote($this->getCallbackUrl($event, $pay_amount));
            if (!$data || strtolower($data['type']) !== 'success') {
                throw new Exception(sprintf(
                    '喜马拉雅点击回传失败：type[%d], text[%s], errors[%s]',
                    $data['type'], $data['text'], Json::encode($data['errors'])
                ));
            }
            return true;
        } catch (Exception $e) {
            Yii::error('Ximalaya ad callback error: ' . $e->getMessage(), __METHOD__);
        }
        return false;
    }

    /**
     * 获取转化事件回调 URL
     *
     * @link https://docs.qq.com/doc/DUm9MaFdyS29Bb1ZL
     *
     * @param string $event 事件类型
     * @param mixed $pay_amount 支付金额（单位：元），仅付费场景传入此参数
     *
     */
    private function getCallbackUrl(string $event, $amount = 0): string
    {
        // track_id 形式：https://ad.ximalaya.com/ad-action?uid=535430&timestamp=1710161736698&ip=**************&os=ios&imei_md5=_IMEI_MD5_&oaid=_OAID_&androidid=_ANDROIDID_&materialid=29181106&idfa=_IDFA_&type=act&invokeid=461754898&responseid=15395513953638
        if ($event === self::CALLBACK_EVENT_TYPE_ACTIVATE) {
            return $this->track_id;
        }
        // 默认 type=act，其它事件类型需要替换 type 参数
        $url = str_replace('type=act', 'type=' . $event, $this->track_id);
        if ($event === self::CALLBACK_EVENT_TYPE_PAY) {
            $url .= sprintf('&pay_amount=%.2f', $this->getCallbackPayAmount($amount, self::CALLBACK_PAY_UNIT_YUAN));
        }
        return $url;
    }
}
