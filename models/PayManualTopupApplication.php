<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "pay_manual_topup_application".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 * @property int $applicant_id 申请人用户 ID
 * @property array $user_ids 待充值的用户 ID 列表
 * @property int $coin_num 待充值的钻石数
 * @property string $reason 充值原因
 * @property array $receipt_img 充值凭证图片地址
 * @property int $checked 审批状态（-1 审批被拒绝、0 审批中、1 审批通过）
 * @property array $audit_info 申请的其它详情
 * @property array $more 更多详情
 * @property-read string $oaflow_order_id OA 审批流工单号
 */
class PayManualTopupApplication extends ActiveRecord
{
    use ActiveRecordTrait;

    // 审批状态：废弃、被拒绝、审批中、审批通过
    const CHECKED_TERMINATED = -2;
    const CHECKED_REJECTED = -1;
    const CHECKED_REVIEWING = 0;
    const CHECKED_APPROVED = 1;

    public static function getDb()
    {
        return Yii::$app->paydb;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'pay_manual_topup_application';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['create_time', 'modified_time', 'applicant_id', 'coin_num', 'checked'], 'integer'],
            [['user_ids', 'receipt_img', 'audit_info', 'more'], 'safe'],
            [['reason'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'applicant_id' => '申请人用户 ID',
            'user_ids' => '待充值的用户 ID 列表',
            'coin_num' => '待充值的钻石数',
            'reason' => '充值原因',
            'receipt_img' => '充值凭证图片地址',
            'checked' => '审批状态',  // -1 审批被拒绝、0 审批中、1 审批通过
            'audit_info' => '申请的其它详情',
            'more' => '更多详情',
            'oaflow_order_id' => 'OA 审批流工单号',
        ];
    }

    public static function findByOrderId(string $order_id): ?self
    {
        return self::findOne(['oaflow_order_id' => $order_id]);
    }

}
