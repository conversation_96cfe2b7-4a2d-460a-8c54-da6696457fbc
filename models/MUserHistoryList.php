<?php

namespace app\models;

use app\components\util\MUtils;
use Yii;

class MUserHistoryList
{
    /**
     * @var array|MUserHistory[]
     */
    private $list = [];
    /**
     * @var int
     */
    private $user_id = 0;

    public function __construct(int $user_id, array $history)
    {
        $this->user_id = $user_id;
        $this->list = $history;
    }

    public function getResult()
    {
        $result = array_merge($this->getSoundDetails(), $this->getDramaDetails(), $this->getRoomDetails());
        return MUtils::sortByValueOrder($result, 'id', array_column($this->list, 'id'));
    }

    private function getSoundDetails()
    {
        $elements = array_reduce($this->list, function ($ret, $item) {
            /**
             * @var MUserHistory $item
             */
            if ($item->isSound) {
                $ret[] = $item;
            }
            return $ret;
        }, []);
        $sound_element_map = array_column($elements, null, 'element_id');
        $sounds = $video_sound_ids = [];
        if (!empty($elements)) {
            $sound_ids = array_keys($sound_element_map);
            /**
             * @var []MSound $sounds
             */
            $sounds = MSound::find()
                ->select('id, soundstr, intro, view_count, duration, cover_image, user_id, username, checked')
                ->where(['id' => $sound_ids])
                ->all();
            $video_sound_ids = SoundVideo::getVideoSoundIds($sound_ids);
        }
        return array_map(function ($sound) use ($sound_element_map, $video_sound_ids) {
            /**
             * @var MSound $sound
             * @var MUserHistory $element
             */
            $element = $sound_element_map[$sound->id];
            // CHECKED_UNPASS 是为了保证 UP 主看自己已转码未过审的音频没问题
            $show_sound = in_array($sound->checked,
                [MSound::CHECKED_UNPASS, MSound::CHECKED_PASS, MSound::CHECKED_CONTRACT_EXPIRED]);
            $data = [
                'id' => $element->id,
                'element_type' => $element->element_type,
                'sound_id' => $element->element_id,
                'soundstr' => $show_sound ? $sound->soundstr : '已失效音频',
                'view_count' => $show_sound ? $sound->view_count : 0,
                'duration' => $show_sound ? $sound->duration : 0,
                'front_cover' => $show_sound ? $sound->front_cover : Yii::$app->params['defaultCoverUrl'],
                'user_id' => $sound->user_id,
                'username' => $sound->username,
                // 暂时不返回播放进度
                // 'more' => [
                //     'completion' => $element->more['completion'],
                // ],
            ];
            if (in_array($element->element_id, $video_sound_ids)) {
                $data['video'] = true;
            }
            return $data;
        }, $sounds);
    }

    private function getDramaDetails()
    {
        $sound_ids = $node_ids = [];
        $elements = array_reduce($this->list, function ($ret, $item) use (&$sound_ids, &$node_ids) {
            /**
             * @var MUserHistory $item
             */
            if ($item->isDrama) {
                $ret[] = $item;
                $sound_ids[] = $item->more['last_play_sound']['id'];
                if ($item->more['node'] ?? false) {
                    $node_ids[] = $item->more['node']['id'];
                }
            }
            return $ret;
        }, []);
        $video_sound_ids = SoundVideo::getVideoSoundIds($sound_ids);
        $drama_element_map = array_column($elements, null, 'element_id');

        $dramas = $episodes = $sound_infos = $nodes = [];
        if (!empty($elements)) {
            $dramas = Drama::rpc('drama/get-dramas', ['drama_id' => array_keys($drama_element_map)]);
            Drama::checkNeedPay($dramas, $this->user_id);

            $sound_infos = MSound::find()->select('id, view_count, duration')->where(['id' => $sound_ids])
                ->indexBy('id')->asArray()->all();
            $episodes = Drama::rpc('api/get-episode-by-sound', ['sound_id' => $sound_ids]);
            $episodes = array_column($episodes, null, 'sound_id');

            if (!empty($node_ids)) {
                $nodes = MSoundNode::find()->select('id, title, duration')
                    ->where(['id' => $node_ids])->indexBy('id')->asArray()->all();
            }
        }

        $details = array_map(function ($drama) use ($drama_element_map, $episodes, $sound_infos, $nodes, $video_sound_ids) {
            /**
             * @var MUserHistory $element
             */
            $element = $drama_element_map[$drama['id']];
            $sound_id = $element->more['last_play_sound']['id'];
            if (!array_key_exists($sound_id, $episodes) || !array_key_exists($sound_id, $sound_infos)) {
                return null;
            }
            if (array_key_exists('node', $element->more) && array_key_exists($element->more['node']['id'], $nodes)) {
                $node_id = $element->more['node']['id'];
                $more = [
                    'last_play_sound' => ['id' => $sound_id],
                    'node' => [
                        'id' => $node_id,
                        'title' => $nodes[$node_id]['title'],
                        'duration' => (int)$nodes[$node_id]['duration'],
                        // 暂时不返回播放进度
                        // 'completion' => $element->more['node']['completion'],
                    ],
                ];
            } else {
                $episode = $episodes[$sound_id];
                $sound_info = $sound_infos[$sound_id];
                $more = [
                    'last_play_sound' => [
                        'id' => $sound_id,
                        'name' => $episode['name'],
                        // 暂时不返回播放进度
                        // 'completion' => $element->more['completion'],
                        'duration' => (int)$sound_info['duration'],
                        'view_count' => (int)$sound_info['view_count'],
                    ],
                ];
            }
            if (in_array($sound_id, $video_sound_ids)) {
                $more['last_play_sound']['video'] = true;
            }

            return [
                'id' => $element->id,
                'element_type' => $element->element_type,
                'drama_id' => $drama['id'],
                'name' => $drama['name'],
                'cover' => $drama['cover'],
                'cover_color' => $drama['cover_color'],
                'view_count' => $drama['view_count'],
                'pay_type' => $drama['pay_type'],
                'need_pay' => $drama['need_pay'],
                'more' => $more,
            ];

        }, $dramas);

        return array_values(array_filter($details));
    }

    private function getRoomDetails()
    {
        $elements = array_reduce($this->list, function ($ret, $item) {
            /**
             * @var MUserHistory $item
             */
            if ($item->isRoom) {
                $ret[] = $item;
            }
            return $ret;
        }, []);
        $room_element_map = array_column($elements, null, 'element_id');

        $rooms = $catalog_names = $creator_usernames = [];
        if (!empty($elements)) {
            $rooms = Live::find()->select('room_id, user_id, cover, catalog_id, title, status')
                ->where(['room_id' => array_keys($room_element_map)])
                ->all();

            if (!empty($rooms)) {
                $catalog_names = Catalog::find()->select('catalog_name')
                    ->where(['id' => array_column($rooms, 'catalog_id')])
                    ->indexBy('id')->column();
                $creator_usernames = Mowangskuser::find()->select('username')
                    ->where(['id' => array_column($rooms, 'user_id')])
                    ->indexBy('id')->column();
            }
        }

        return array_map(function ($room) use ($room_element_map, $catalog_names, $creator_usernames) {
            /**
             * @var Live $room
             * @var MUserHistory $element
             */
            $element = $room_element_map[$room->room_id];

            return [
                'id' => $element->id,
                'element_type' => $element->element_type,
                'room_id' => $room->room_id,
                'title' => $room->title,
                'cover' => $room->cover ?: Yii::$app->params['defaultRoomCoverUrl'],
                'catalog_name' => $catalog_names[$room->catalog_id] ?? '',
                'catalog_id' => $room->catalog_id,
                'user_id' => $room->user_id,
                'username' => $creator_usernames[$room->user_id] ?? '-',
                'status' => $room->status,
            ];
        }, $rooms);
    }

}
