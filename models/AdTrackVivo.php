<?php

namespace app\models;

use Exception;
use Yii;
use yii\helpers\Json;

class AdTrackVivo extends AdTrack implements AdTrackInterface
{
    // 用户行为数据上传 url
    const CALLBACK_GATEWAY = 'https://marketing-api.vivo.com.cn/openapi/v1/advertiser/behavior/upload';
    const SRC_TYPE = 'APP';  // 数据源类型
    const PKG_NAME = 'cn.missevan';  // 应用包名

    // 刷新 refresh_token url
    const METHOD_TOKEN_REFRESH = 'https://marketing-api.vivo.com.cn/openapi/v1/oauth2/refreshToken';

    const CALLBACK_EVENT_TYPE_MAP = [
        self::CALLBACK_EVENT_ACTIVATE => self::CALLBACK_EVENT_TYPE_ACTIVATE,
        self::CALLBACK_EVENT_ONE_DAY_RETENTION => self::CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION,
        self::CALLBACK_EVENT_PAY => self::CALLBACK_EVENT_TYPE_PAY,
        self::CALLBACK_EVENT_REGISTER => self::CALLBACK_EVENT_TYPE_REGISTER,
        self::CALLBACK_EVENT_KEY_ACTION => null,
        self::CALLBACK_EVENT_TRANSACTION => null,  // 无消费 / 充值关键行为
    ];

    // 激活
    const CALLBACK_EVENT_TYPE_ACTIVATE = 'ACTIVATION';
    // 付费
    const CALLBACK_EVENT_TYPE_PAY = 'PAY';
    // 次日留存
    const CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION = 'RETENTION_1';
    // 注册
    const CALLBACK_EVENT_TYPE_REGISTER = 'REGISTER';

    /**
     * 转化事件回调
     *
     * @param string $event_type 事件类型
     * @param mixed $arg 支付金额（单位：元），关键行为参数
     */
    public function callback(string $event_type, $arg = 0)
    {
        try {
            if (!array_key_exists($event_type, self::CALLBACK_EVENT_TYPE_MAP)) {
                throw new Exception('vivo 点击回传事件错误：' . $event_type);
            }
            $event = self::CALLBACK_EVENT_TYPE_MAP[$event_type];
            if (is_null($event)) {
                return true;
            }
            $data = Yii::$app->tools->requestRemote($this->getCallbackUrl($event), [], 'POST',
                $this->getCallbackBody($event, $arg), 0, [
                    'Content-Type' => 'application/json',
                ]
            );
            if (!($data && $data['code'] === 0)) {
                throw new Exception(sprintf('vivo 点击回传失败：code[%d], msg[%s]', $data['code'], $data['message']));
            }
            return true;
        } catch (Exception $e) {
            Yii::error('vivo ad error: ' . $e->getMessage(), __METHOD__);
        }
        return false;
    }

    private function getCallbackUrl(string $callback_event)
    {
        return sprintf(
            '%s?access_token=%s&timestamp=%d&nonce=%s&advertiser_id=%s',
            self::CALLBACK_GATEWAY,
            $this->getAccessToken(),
            $_SERVER['REQUEST_TIME'] * 1000,  // 转化为毫秒
            $this->getNonce($callback_event),
            $this->getAccountID()
        );
    }

    /**
     * 转化事件回调
     */
    private function getCallbackBody(string $callback_event, $arg)
    {
        $config = $this->getConfig();
        $body = [
            'srcType' => self::SRC_TYPE,
            'pkgName' => self::PKG_NAME,
            'srcId' => $config['src_id'],
            'dataList' => [
                [
                    'userIdType' => 'OTHER',
                    // BUVID 存在为空的情况，且重复率较 equip_id 高（VIVO 对于重复的设备 ID 只会计一次）
                    'userId' => Yii::$app->equip->getEquipId(),
                    'cvType' => $callback_event,
                    'cvTime' => $_SERVER['REQUEST_TIME'] * 1000,  // 转化为毫秒
                    'requestId' => $this->track_id,
                    'creativeId' => $this->creative_id,
                ]
            ],
        ];
        // 为查看当日 roi，增加回传付费金额信息，vivo 媒体付费金额暂未上算法模型
        // 回传的付费金额只能在转换管理里面看（不能拆开自然量和推广量带来的付费）
        // 所回传的付费金额不能归因至推广层级
        if ($callback_event === self::CALLBACK_EVENT_TYPE_PAY) {
            $money = $this->getCallbackPayAmount($arg);
            $body['dataList'][0]['cvCustom'] = "money:$money";
        }
        return Json::encode($body);
    }

    /**
     * 获取 access token
     *
     * @return mixed
     * @throws Exception
     */
    public function getAccessToken()
    {
        $cache = Yii::$app->memcache;
        if ($access_token = $cache->get(KEY_VIVO_AD_ACCESS_TOKEN)) {
            return $access_token;
        }
        $redis = Yii::$app->redis;
        if (!$refresh_token = $redis->get(KEY_VIVO_AD_REFRESH_TOKEN)) {
            throw new Exception('vivo refresh_token 失效');
        }
        $config = $this->getConfig();
        $data = $this->refreshToken($config, $refresh_token);
        $this->cacheToken($data);
        return $data['access_token'];
    }

    /**
     * 刷新 token
     *
     * @link https://open-ad.vivo.com.cn/doc/index?id=396
     *
     * @param array $config
     * @param string $refresh_token
     * @return mixed
     * @throws Exception
     */
    private function refreshToken(array $config, string $refresh_token)
    {
        $params = [
            'client_id' => $config['client_id'],
            'client_secret' => $config['client_secret'],
            'refresh_token' => $refresh_token,
        ];
        $data = Yii::$app->tools->requestRemote(self::METHOD_TOKEN_REFRESH, $params);
        if (!($data && $data['code'] === 0)) {
            throw new Exception(sprintf('vivo 刷新 refresh_token 失败：code[%d], msg[%s]', $data['code'], $data['message']));
        }
        return $data['data'];
    }

    /**
     * @param array $token_info
     */
    private function cacheToken(array $token_info)
    {
        $time = $_SERVER['REQUEST_TIME'];
        // token_date（单位：毫秒）
        // token_date_ttl = access_token 截止的有效日期（转化为秒）- 当前时间 - ONE_HOUR
        $token_date_ttl = (int)($token_info['token_date'] / 1000) - $time - ONE_HOUR;
        if ($token_date_ttl <= 0) {
            // vivo access_token 有效期为 1 天，若发生变更则设置为 60s 作为缓存有效期
            Yii::error(sprintf('vivo access_token token_date is changed: %d', $token_info['token_date']), __METHOD__);
            $token_date_ttl = ONE_MINUTE;
        }
        // refresh_token（单位：毫秒）
        // refresh_token_ttl = refresh_token 截止的有效日期（转化为秒）- 当前时间 - ONE_HOUR
        $refresh_token_ttl = (int)($token_info['refresh_token_date'] / 1000) - $time - ONE_HOUR;
        if ($refresh_token_ttl <= 0) {
            // vivo refresh_token 有效期为 365 天，若发生变更则设置为 60s 作为缓存有效期
            Yii::error(sprintf('vivo refresh_token refresh_token_date is changed: %d', $token_info['refresh_token_date']), __METHOD__);
            $refresh_token_ttl = ONE_MINUTE;
        }

        $cache = Yii::$app->memcache;
        $redis = Yii::$app->redis;
        $cache->set(KEY_VIVO_AD_ACCESS_TOKEN, $token_info['access_token'], $token_date_ttl);
        $redis->setex(KEY_VIVO_AD_REFRESH_TOKEN, $refresh_token_ttl, $token_info['refresh_token']);
    }

    private function getNonce(string $callback_event)
    {
        return $callback_event
            . $_SERVER['REQUEST_TIME']
            . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
    }

    private function getConfig()
    {
        $account_id = $this->getAccountID();
        $service = Yii::$app->params['service']['ad']['vivo'];
        if (!array_key_exists($account_id, $service)) {
            throw new Exception('vivo 配置出错，不存在账号 ID：' . $account_id);
        }
        return $service[$account_id];
    }

    /**
     * @inheritdoc
     */
    public function isValidTracked()
    {
        return $this->create_time > $_SERVER['REQUEST_TIME'] - ONE_DAY;
    }

}
