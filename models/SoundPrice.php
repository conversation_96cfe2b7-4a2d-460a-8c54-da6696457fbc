<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "sound_price".
 *
 * @property integer $id
 * @property integer $price
 * @property double $rate
 * @property integer $user_id
 * @property string $title
 */
class SoundPrice extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'sound_price';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'price', 'rate', 'user_id', 'title'], 'required'],
            [['id', 'price', 'user_id'], 'integer'],
            [['rate'], 'number'],
            [['title'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'price' => 'Price',
            'rate' => 'Rate',
            'user_id' => 'User ID',
            'title' => 'Title',
        ];
    }
}
