<?php

namespace app\models;

use app\components\util\Equipment;
use app\components\util\MUtils;
use Yii;

/**
 * This is the model class for table "commentnotice".
 *
 * @property integer $id
 * @property integer $c_user_id
 * @property string $c_user_name
 * @property integer $a_user_id
 * @property string $a_user_name
 * @property integer $type
 * @property integer $eId
 * @property string $title
 * @property integer $comment_id
 * @property integer $sub
 * @property integer $isread
 * @property integer $work_id
 */
class Commentnotice extends ActiveRecord
{
    const TYPE_COMMENT = 0;
    const TYPE_SUB_COMMENT = 1;

    const NOTICE_TYPE_COMMENT = 0;
    const NOTICE_TYPE_AT = 1;

    const NOT_READ = 0;
    const ALREADY_READ = 1;

    // 评论提醒类型 1：音频；2：音单；6：专题；7：活动；8：语音包；9：运势语音
    const COMMENT_NOTICE_TYPE_SOUND = 1;
    const COMMENT_NOTICE_TYPE_ALBUM = 2;
    const COMMENT_NOTICE_TYPE_TOPIC = 6;
    const COMMENT_NOTICE_TYPE_EVENT = 7;
    const COMMENT_NOTICE_TYPE_VOICE_CARD = 8;
    const COMMENT_NOTICE_TYPE_OMIKUJI_CARD = 9;

    public $front_cover;
    public $authenticated;
    public $username;
    public $icon;
    public $comment;
    // 消息提醒为语音包或运势签类型时，其作品 ID
    public $work_id;
    // 消息提醒为音频或音单时，返回其对应的用户 ID、用户名
    public $user_id;

    // 消息已读
    CONST HAS_READ = 1;

    // 是否为子评论
    CONST IS_SUB = 1;
    CONST IS_NOT_SUB = 0;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'commentnotice';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['c_user_id', 'c_user_name', 'a_user_id', 'a_user_name', 'type', 'eId', 'title', 'comment_id', 'sub', 'isread'], 'required'],
            [['c_user_id', 'a_user_id', 'type', 'eId', 'comment_id', 'sub', 'isread'], 'integer'],
            [['c_user_name', 'a_user_name'], 'string', 'max' => 20],
            [['title'], 'string', 'max' => 30],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'c_user_id' => '评论用户',
            'c_user_name' => '评论用户名',
            'a_user_id' => '被at用户的用户id',
            'a_user_name' => '被at用户的用户名',
            'type' => '种类1是音频，2是专辑，3是新闻,  4频道,  6专题,  7活动',
            // TODO: 之后会调整 app_missevan.commentnotice 表中的 eId 字段为 element_id
            'eId' => '对应id',
            'title' => '被评论的title',
            'comment_id' => '评论id',
            'sub' => '是否为子评论，1为是，0为否',
            'isread' => '是否已读',
        ];
    }

    //添加评论提示
    public static function addCommentNotice($users, $type, $eId, $comment_id, $title, $sub = 0, $beRemindedUser = [])
    {
        unset($users[Yii::$app->user->id]);//自己评论自己的不进行提醒
        foreach ($users as $key => $value) {
            $notice = new static;
            $notice->c_user_id = Yii::$app->user->id;
            $notice->c_user_name = Yii::$app->user->name;
            $notice->a_user_id = $key;
            $notice->a_user_name = $value;
            $notice->type = $type;
            $notice->eId = $eId;
            $notice->comment_id = $comment_id;
            $notice->title = mb_substr($title, 0, 30);
            $notice->sub = $sub;
            $notice->isread = 0;
            //若存在被@的用户 （使该条记录notice_type为1即为@的提醒，其它为0评论提醒）
            if (isset($beRemindedUser[$key]) && $value == $beRemindedUser[$key]) $notice->notice_type = 1;

            if (!$notice->save()) {
                return $notice->getErrors();
            }
        }
    }

    /**
     * 获取未读提醒数
     *
     * @param int $user_id 用户 ID
     * @return array 未读提醒：at 和 comment
     */
    public static function getUnreadNotice($user_id)
    {
        $condition = 'a_user_id = :a_user_id AND isread = :isread';
        $params = [':a_user_id' => $user_id, ':isread' => self::NOT_READ];
        if (!self::isNoticeCardComment()) {
            $condition .= ' AND `type` NOT IN (' . SoundComment::TYPE_VOICE_CARD . ', '
                . SoundComment::TYPE_OMIKUJI_CARD . ')';
        }
        // at 消息 notice_type 为 1，评论消息为 0
        $notices = self::findBySql('SELECT COUNT(*) AS `count`, SUM(notice_type) AS `at` FROM '
                . self::tableName() . ' WHERE ' . $condition)
            ->params($params)
            ->asArray()
            ->one();
        return [
            'comment' => (int)$notices['count'] - (int)$notices['at'],
            'at' => (int)$notices['at'],
        ];
    }

    /**
     * 是否对语音卡的评论及 @ 消息进行提醒
     *
     * @workaround 对于无语音卡评论功能的版本不进行提醒，版本更迭较多后（预计在 Android 5.2.8 及 iOS 4.3.6 版本后），
     * 可移除调用此方法处的判断条件
     * @return bool
     */
    public static function isNoticeCardComment()
    {
        return !Equipment::isAppOlderThan('4.3.0', '5.2.2');
    }

    /**
     * 清除提醒消息
     *
     * @param int $comment_id 父评论 ID
     * @throws \Exception
     */
    public static function deleteRelation(int $comment_id): void
    {
        $sub_ids = SoundSubComment::find()
            ->select('id')
            ->where(['comment_id' => $comment_id])
            ->column();
        $condition = '(comment_id = :id AND sub = :type_comment)';
        $params = [
            ':id' => $comment_id,
            ':type_comment' => self::TYPE_COMMENT,
        ];
        if (!empty($sub_ids)) {
            $condition .= ' OR (' . MUtils::generateIntegerIn('comment_id', $sub_ids) . ' AND sub = :type_sub_comment)';
            $params[':type_sub_comment'] = self::TYPE_SUB_COMMENT;
        }
        self::deleteAll($condition, $params);
    }
}
