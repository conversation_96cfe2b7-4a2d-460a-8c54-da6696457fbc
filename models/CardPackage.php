<?php

namespace app\models;

use app\components\random\PackageInterface;
use app\components\util\MUtils;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "card_package".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 * @property int $role_id 角色 ID
 * @property string $title 季包标题
 * @property int $season 所属季包季度, 从 1 开始
 * @property int $is_online 是否上线（0 为否，1 为是）
 * @property int $work_id 作品 id，work 表主键
 * @property int $price 季包价格
 * @property int $given_coupon 抽奖价格
 * @property string $push_rule 推送规则
 * @property int $attr 季包属性
 */
class CardPackage extends ActiveRecord implements PackageInterface
{
    public $season_cards = [];

    const OFFLINE = 0;
    const ONLINE = 1;

    // 季包显示状态（0 未付费未集齐、1 已付费未集齐、2 已集齐、3 下架）
    const STATUS_NEEDPAY = 0;
    const STATUS_PAID = 1;
    const STATUS_COLLECTED = 2;
    const STATUS_UNSHELVE = 3;

    const SEASON_NAMES = [
        1 => '第一季',
        2 => '第二季',
        3 => '第三季',
        4 => '第四季',
        5 => '第五季',
        6 => '第六季',
        7 => '第七季',
        8 => '第八季',
        9 => '第九季',
        10 => '第十季',
        11 => '第十一季',
        12 => '第十二季',
    ];

    // 季包中普通卡片数量
    const COMMON_CARD_COUNT = 24;

    // 季包属性（按位运算）
    const ATTR_SPECIAL_ROLE_PACKAGE = 0b01;  // 特殊角色包
    const ATTR_NOT_PURCHASABLE = 0b10;  // 不可购买
    const ATTR_NOT_DRAWABLE = 0b100;  // 季包不能抽（只能通过购买/兑换获得）
    const ATTR_MULTI_ROLE = 0b1000;  // 季包为多角色模式
    const ATTR_FILTER_LEVEL = 0b10000;  // 季包开启等级筛选模块

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'card_package';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db2');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                ['create_time', 'modified_time', 'role_id', 'season', 'is_online', 'work_id', 'price', 'push_rule'],
                'required'
            ],
            [
                ['create_time', 'modified_time', 'role_id', 'season', 'work_id', 'price', 'given_coupon', 'is_online', 'attr'],
                'integer'
            ],
            [['push_rule'], 'string', 'max' => 1024],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'season' => '季度',
            'is_online' => '是否上线',
            'work_id' => '作品 ID',
            'role_id' => '角色 ID',
            'price' => '购买价格',
            'given_coupon' => '购买季包 赠送荣耀点',
            'push_rule' => '推送规则',
            'title' => '季包标题',
            // attr 按位运算：
            // 第 1 位为 1 时为特殊角色包
            // 第 2 位为 1 时季包不可购买（只能通过抽卡/兑换获得）
            // 第 3 位为 1 时季包不能抽（只能通过购买/兑换获得）
            // 第 4 位为 1 时季包为多角色模式（0 为单角色模式）
            // 第 5 位为 1 时季包开启等级筛选模块
            'attr' => '季包属性',
        ];
    }

    public function getCards()
    {
        if (!$this->season_cards) {
            $this->season_cards = Card::getPackageCards($this->id, Card::SPECIAL_NORMAL);
        }
        return $this->season_cards;
    }

    public static function getObtainingMethod()
    {
        return GetCardLog::TYPE_BUY;
    }

    public function getPushRules(): array
    {
        $time = $_SERVER['REQUEST_TIME'] ?? time();
        // TODO: 除全职外新的语音包推送周期规则在 push_rule 定义
        // {"level":[1,2,3,4],"interval":0,"after":0}
        $rules = Json::decode($this->push_rule) ?: [
            [
                'level' => [Card::LEVEL_R, Card::LEVEL_SR, Card::LEVEL_SSR],
                'interval' => ONE_DAY,
                'after' => 0,
            ],
            [
                'level' => [Card::LEVEL_N],
                'interval' => ONE_DAY,
                'after' => 0,
            ],
        ];

        foreach ($rules as &$rule) {
            $rule['start_time'] = $time + $rule['after'] ?? 0;
        }
        unset($rule);
        return $rules;
    }

    /**
     * 获取购买季包赠送的荣耀点数量
     *
     * @return int 赠送的荣耀点数量
     */
    public function getGiveCoupon(): int
    {
        return $this->given_coupon;
    }

    /**
     * 实现接口对象中定义的获取卡包抽卡次数方法，在此类中无实际作用
     *
     * @return int
     */
    public function getDrawTimes(): int
    {
        return 0;
    }

    /**
     * 获取某角色下的各季包信息
     *
     * @param array $role 角色信息，例 ['id' => 15, 'name' => '韩文清', 'work_id' => 1, 'cover_detail' => '...']
     * @return array
     */
    public static function getPackages(array &$role)
    {
        $packages = self::find()->select('id AS card_package_id, title, season, price, is_online, attr')
            ->where(['role_id' => $role['id']])->orderBy('season DESC')->asArray()->all();

        $role['special'] = 0;
        $packages = array_map(function ($item) use (&$role) {
            $item['card_package_id'] = (int)$item['card_package_id'];
            $item['season'] = (int)$item['season'];
            $item['season_name'] = $item['title'] ?: self::SEASON_NAMES[$item['season']];
            $item['is_online'] = (int)$item['is_online'];
            // TODO: 旧版本对于不可购买的季包设置 price 值大于某个很大的数值，如 99999
            // 新版本不可购买的季包由 attr & 2 != 0 来区分
            $item['price'] = (int)$item['price'];
            $item['attr'] = (int)$item['attr'];
            // TODO: 将原有的特殊角色包 attr 字段填值为 1（特殊角色包不再由 push_rule 是否为空字符串来区分）
            if (self::ATTR_SPECIAL_ROLE_PACKAGE & $item['attr']) {
                // 若包含特殊事件包的角色，则显示的季包名为角色名，且标记为此角色为特殊角色（如生日语音/快问快答）
                $item['season_name'] = $role['name'];
                $role['special'] = 1;
            }
            return $item;
        }, $packages);

        return $packages;
    }

    /**
     * 获取某一角色下各季包的卡片详情
     *
     * @param int|null $user_id
     * @param array $role
     * @param array $cards
     * @return array
     */
    public static function getPackageCards(?int $user_id, array &$role, array $cards)
    {
        $packages = self::getPackages($role);
        $paid_package_ids = TransactionLog::getPackageIdsPaid($user_id, $role['work_id']);

        $package_cards = array_filter($cards, function ($card) {
            return $card ? (Card::SPECIAL_FESTIVAL !== $card['special']) : false;
        });
        $package_cards = MUtils::groupArray($package_cards, 'card_package_id');

        $result = array_map(function ($item) use ($package_cards, $paid_package_ids) {
            $card_package_id = $item['card_package_id'];
            $cards = $package_cards[$item['card_package_id']] ?? [];

            $item['status'] = in_array($card_package_id, $paid_package_ids)
                ? CardPackage::STATUS_PAID : CardPackage::STATUS_NEEDPAY;

            $cards_status = array_column($cards, 'status', 'card_id');
            if (!in_array(GetCard::SHOW_DISACTIVE, $cards_status) && !in_array(GetCard::SHOW_ACTIVE, $cards_status)) {
                $item['status'] = CardPackage::STATUS_COLLECTED;
            }

            $unfree_cards = array_filter($cards, function ($item) {
                return Card::SPECIAL_FREE !== $item['special'] && GetCard::SHOW_DISACTIVE !== $item['status'];
            });
            if (CardPackage::OFFLINE === $item['is_online']) {
                if (!$unfree_cards) return null;
                $item['status'] = CardPackage::STATUS_UNSHELVE;
            }
            $item['cards'] = $cards;
            return $item;
        }, $packages);
        $result = array_values(array_filter($result));

        return $result;
    }

}
