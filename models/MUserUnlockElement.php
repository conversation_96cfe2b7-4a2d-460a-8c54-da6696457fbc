<?php

namespace app\models;

use Yii;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "m_user_unlock_element".
 *
 * @property int $id
 * @property int $create_time 创建时间（秒级时间戳）
 * @property int $modified_time 修改时间（秒级时间戳）
 * @property int $user_id 用户 ID
 * @property int $element_id 元素 ID
 * @property int $element_type 元素类型
 * @property int $unlock_time 解锁时间（秒级时间戳）
 * @property int $start_time 福利生效时间（秒级时间戳）
 * @property int $end_time 福利结束时间（秒级时间戳）
 * @property string|null $more 更多详情
 */
class MUserUnlockElement extends ActiveRecord
{
    // 元素类型
    const ELEMENT_TYPE_SOUND = 1;  // 音频

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_user_unlock_element';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'element_id', 'element_type', 'unlock_time', 'start_time', 'end_time'], 'required'],
            [['create_time', 'modified_time', 'user_id', 'element_id', 'element_type', 'unlock_time', 'start_time', 'end_time'], 'integer'],
            [['more'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'user_id' => '用户 ID',
            'element_id' => '元素 ID',
            'element_type' => '元素类型',
            'unlock_time' => '解锁时间',
            'start_time' => '解锁生效时间（含此时刻）',
            'end_time' => '解锁结束时间（不含此时刻）',
            'more' => '更多详情',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * @param int $user_id
     * @param int $element_type
     * @param int|array $element_ids
     * @param int $timestamp
     * @return array
     */
    public static function getUnlockedElementIds($user_id, int $element_type, $element_ids, int $timestamp = 0): array
    {
        if (!$user_id) {
            return [];
        }
        if ($timestamp === 0) {
            $timestamp = $_SERVER['REQUEST_TIME'];
        }
        return self::find()
            ->select('element_id')
            ->where(['user_id' => $user_id, 'element_type' => $element_type, 'element_id' => $element_ids])
            ->andWhere(':time >= start_time AND :time < end_time', [':time' => $timestamp])
            ->column();
    }

    public static function isSoundUnlocked($user_id, $sound_id, int $timestamp = 0): bool
    {
        return count(self::getUnlockedElementIds($user_id, self::ELEMENT_TYPE_SOUND, $sound_id, $timestamp)) > 0;
    }

    public static function checkUnLocked(array &$sounds, $user_id)
    {
        if (!$user_id) {
            return;
        }
        if (is_object($sounds) || ArrayHelper::isAssociative($sounds)) {
            // 如果传进来的是字典或对象的时候，需要变成数组
            $sound_arr = [&$sounds];
            return self::checkUnLocked($sound_arr, $user_id);
        }

        $ids_free_unlocked = MUserUnlockElement::getUnlockedElementIds($user_id, MUserUnlockElement::ELEMENT_TYPE_SOUND, array_column($sounds, 'id'));
        $sounds = array_map(function ($item) use ($ids_free_unlocked) {
            // 对于经过其它渠道（非购买途径）解锁的付费音频，以免费试听音的形式展现给解锁的用户
            if (is_object($item)) {
                if ($item->need_pay === MSound::SOUND_UNPAID && in_array($item->id, $ids_free_unlocked)) {
                    $item->pay_type = MSound::SOUND_FREE;
                    $item->need_pay = MSound::SOUND_FREE;
                }
            } else {
                if ($item['need_pay'] === MSound::SOUND_UNPAID && in_array($item['id'], $ids_free_unlocked)) {
                    $item['pay_type'] = MSound::SOUND_FREE;
                    $item['need_pay'] = MSound::SOUND_FREE;
                }
            }
            return $item;
        }, $sounds);
    }

}
