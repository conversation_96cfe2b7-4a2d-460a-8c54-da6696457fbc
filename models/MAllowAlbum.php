<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "m_allow_album".
 *
 * @property int $id 主键 ID
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 * @property int $album_id 音单 ID
 */
class MAllowAlbum extends ActiveRecord
{
    use ActiveRecordTrait;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_allow_album';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['album_id'], 'required'],
            [['album_id', 'create_time', 'modified_time'], 'integer']
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'album_id' => '音单 ID',
        ];
    }

    /**
     * 检查音单是否在白名单中
     *
     * @param int $album_id 音单 ID
     * @return bool 是否在白名单中
     */
    public static function isAllowed($album_id)
    {
        $album_id = (int)$album_id;
        return self::find()->where(['album_id' => $album_id])->exists();
    }

    /**
     * 添加音单到白名单
     *
     * @param int|array $album_id 音单 ID 或 ID 数组
     * @return bool 是否添加成功
     */
    public static function addAlbum($album_id)
    {
        // 转换为数组格式
        $album_ids = is_array($album_id) ? array_map('intval', $album_id) : [(int)$album_id];
        try {
            $allow_list = self::find()->where(['album_id' => $album_ids])->all();
            $existing_album_ids = array_column($allow_list, 'album_id');
            $insert_data = [];
            foreach ($album_ids as $id) {
                if (in_array($id, $existing_album_ids)) {
                    continue;
                }
                $insert_data[] = [
                    'album_id' => $id,
                    'create_time' => $_SERVER['REQUEST_TIME'],
                    'modified_time' => $_SERVER['REQUEST_TIME'],
                ];
            }
            if (!empty($insert_data)) {
                return (bool)Yii::$app->db->createCommand()
                    ->batchInsert(self::tableName(), array_keys($insert_data[0]), $insert_data)
                    ->execute();
            }
            return false;
        } catch (\Exception $e) {
            throw new \Exception('添加白名单失败: ' . $e->getMessage());
        }
    }

    /**
     * 从白名单中移除音单
     *
     * @param int|array $album_id 音单 ID 或 ID 数组
     * @return bool 是否移除成功
     */
    public static function removeAlbum($album_id)
    {
        // 转换为数组格式
        $album_ids = is_array($album_id) ? array_map('intval', $album_id) : [(int)$album_id];
        try {
            return (bool)self::deleteAll(['album_id' => $album_ids]);
        } catch (\Exception $e) {
            throw new \Exception('移除白名单失败: ' . $e->getMessage());
        }
    }
}
