<?php

namespace app\models;

use yii\web\HttpException;

class PaginationParams
{
    const MAX_PAGE_SIZE = 120;
    const DEFAULT_PAGE_SIZE = 10;

    public $page;
    public $page_size;

    /**
     * 处理分页参数是否合法
     * @param integer $page 页数
     * @param integer $page_size 每页个数
     * @return PaginationParams
     * @throws HttpException
     */
    public static function process(int $page, int $page_size)
    {
        if (0 >= $page || 0 >= $page_size) {
            $page_size = self::DEFAULT_PAGE_SIZE;
            $page = 1;
        }
        if (self::MAX_PAGE_SIZE < $page_size) {
            $page_size = self::MAX_PAGE_SIZE;
        }
        $return_obj = new self;
        $return_obj->page = $page;
        $return_obj->page_size = $page_size;
        return $return_obj;
    }

    /**
     * 生成空的分页数据
     * @param integer $page 页数
     * @param integer $page_size 每页个数
     * @return array
     */
    public static function empty($page, $page_size)
    {
        return [
            'p' => $page,
            'count' => 0,
            'maxpage' => 1,
            'pagesize' => $page_size,
        ];
    }

}
