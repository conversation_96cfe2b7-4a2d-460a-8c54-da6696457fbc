<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "user_cover".
 *
 * @property integer $user_id
 * @property integer $cover_id
 */
class UserCover extends ActiveRecord
{
    public $have;
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'user_cover';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'cover_id'], 'required'],
            [['user_id', 'cover_id'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'user_id' => 'User ID',
            'cover_id' => 'Cover ID',
        ];
    }
}
