<?php

namespace app\models;

use missevan\storage\StorageClient;
use Yii;

/**
 * This is the model class for table "m_person_home_page".
 *
 * @property integer $recid
 * @property integer $soundid
 */
class MPersonHomePage extends Feed
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_person_home_page';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['recid', 'soundid'], 'required'],
            [['recid', 'soundid'], 'integer'],
            [['soundid', 'recid'], 'unique', 'targetAttribute' => ['soundid', 'recid'], 'message' => 'The combination of Recid and Soundid has already been taken.'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'recid' => 'Recid',
            'soundid' => 'Soundid',
        ];
    }

    /**
     * 获取关注的用户的动态
     *
     * @param string|array $condition
     * @param string|array $order
     * @param integer $limit
     * @return array
     */
    public static function getFeed($condition, $order, $limit): array
    {
        $select = 't.id, t.user_id, t.username, t.create_time, t.duration, t.soundstr, t.view_count, t.cover_image';

        $feed = MSound::find()->alias('t')
            ->select($select)
            ->innerJoin('m_person_home_page t1', 't1.soundid = t.id')
            ->where($condition)->orderBy($order)
            ->limit($limit)
            ->asArray()->all();
        // 获取用户头像
        $user_ids = array_unique(array_column($feed, 'user_id'));
        $users = Mowangskuser::find()->select('id, username, iconurl, boardiconurl, icontype, avatar, confirm')
            ->where(['id' => $user_ids])->indexBy('id')->all();
        return array_map(function ($item) use ($users) {
            $user = $users[$item['user_id']] ?? null;
            if ($user) {
                $item['iconurl'] = $user->iconurl;
                $item['username'] = $user->username;
                $item['authenticated'] = $user->authenticated;
            } else {
                $item['iconurl'] = Yii::$app->params['defaultAvatarUrl'];
                $item['username'] = '';
                $item['authenticated'] = 0;
            }

            $item['id'] = (int)$item['id'];
            $item['user_id'] = (int)$item['user_id'];
            $item['create_time'] = (int)$item['create_time'];
            $item['duration'] = (int)$item['duration'];
            $item['view_count'] = (int)$item['view_count'];
            $item['front_cover'] = MSound::getFrontCoverUrl($item['cover_image']);
            $item['type'] = 'sound';

            unset($item['cover_image']);
            return $item;
        }, $feed);
    }

    /**
     * 获取关注的用户的投稿动态提醒
     *
     * @param int $user_id 用户 ID
     * @param int $start_time 起始时间点
     * @return int 过审的音频 ID
     */
    public static function getFeedNotice(int $user_id, int $start_time = 0): int
    {
        $query = MSound::find()
            ->alias('t')
            ->select('t.id')
            ->innerJoin(self::tableName() . ' AS t2', 't2.soundid = t.id')
            ->where('t2.recid = :recid AND t.checked = :checked', [
                ':recid' => $user_id,
                ':checked' => MSound::CHECKED_PASS
            ]);
        if ($start_time > 0) {
            $query = $query->andWhere('t2.gmt_create > :gmt_create', [
                ':gmt_create' => date('Y-m-d H:i:s', $start_time)
            ]);
        }
        $sound_id = (int)$query->orderBy(['t2.id' => SORT_DESC])->limit(1)->scalar();
        return $sound_id;
    }
}
