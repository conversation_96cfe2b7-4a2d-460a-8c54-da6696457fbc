<?php

namespace app\models;

use Exception;
use missevan\util\MUtils;
use yii\helpers\Json;

class TransactionItemsLogList
{
    /**
     * @var array|TransactionItemsLog[]
     */
    private $transaction_items = [];

    public function __construct(array $items = [])
    {
        $this->transaction_items = $items;
    }

    public function addItem(TransactionItemsLog $item)
    {
        $this->transaction_items[] = $item;
    }

    public function items()
    {
        return $this->transaction_items;
    }

    public function getGoodsID()
    {
        if (empty($this->transaction_items)) {
            throw new Exception('transaction items is empty');
        }
        return $this->transaction_items[0]->goods_id;
    }

    public function getGoodsTitle()
    {
        $count = count($this->transaction_items);
        if ($count === 0) {
            throw new Exception('transaction items is empty');
        }

        return $this->transaction_items[0]->goods_title;
    }

    public function getTotalCoins()
    {
        $coins = $this->getTotalCommonCoins();
        foreach ($this->transaction_items as $item) {
            /**
             * @var TransactionItemsLog $item
             */
            foreach ($item->more_detail['common_coins_redeem'] ?? [] as $coin_type => $coin_num) {
                $coins[$coin_type] = ($coins[$coin_type] ?? 0) + $coin_num;
            }
        }

        return $coins;
    }

    public function getTotalCommonCoins()
    {
        $common_coins = [];
        foreach ($this->transaction_items as $item) {
            /**
             * @var TransactionItemsLog $item
             */
            foreach ($item->more_detail['common_coins'] as $coin_type => $coin_num) {
                $common_coins[$coin_type] = ($common_coins[$coin_type] ?? 0) + $coin_num;
            }
        }
        return $common_coins;
    }

    public function getTotalTax()
    {
        return array_reduce($this->transaction_items, function ($tax, $item) {
            if (array_key_exists('tax', $item->more_detail)) {
                $tax += $item->more_detail['tax'];
            }

            return $tax;
        }, 0);
    }

    public function getTotalGoodsNum()
    {
        $goods_nums = array_column($this->transaction_items, 'goods_num');
        return array_sum($goods_nums);
    }

    public function getTotalIncome()
    {
        return array_reduce($this->transaction_items, function ($income, $item) {
            if (array_key_exists('income', $item->more_detail)) {
                $income += $item->more_detail['income'];
            }

            return $income;
        }, 0);
    }

    public function save(int $transaction_id = 0, ?callable $after_save = null)
    {
        if (empty($this->transaction_items)) {
            return 0;
        }
        if (is_callable($after_save)) {
            foreach ($this->transaction_items as $item) {
                if ($transaction_id) {
                    $item->tid = $transaction_id;
                }
                if (!$item->save()) {
                    throw new Exception(MUtils::getFirstError($item));
                }
                $after_save($item);
            }
            return count($this->transaction_items);
        }

        $nowstamp = $_SERVER['REQUEST_TIME'];
        $rows = array_map(function ($item) use ($transaction_id, $nowstamp) {
            /**
             * @var TransactionItemsLog $item
             */
            return [
                'create_time' => $nowstamp,
                'modified_time' => $nowstamp,
                'goods_id' => $item->goods_id,
                'goods_title' => $item->goods_title,
                'goods_price' => $item->goods_price,
                'goods_num' => $item->goods_num,
                'tid' => $transaction_id ?: $item->tid,
                'user_id' => $item->user_id,
                'status' => $item->status,
                'type' => $item->type,
                'more' => $item->more_detail ? Json::encode($item->more_detail) : null,
            ];
        }, $this->transaction_items);
        return TransactionItemsLog::getDb()->createCommand()
            ->batchInsert(TransactionItemsLog::tableName(), array_keys(current($rows)), $rows)
            ->execute();
    }

}
