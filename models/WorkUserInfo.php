<?php

namespace app\models;

use app\forms\TransactionForm;
use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "work_user_info".
 *
 * @property int $id 主键
 * @property int $user_id 用户 ID
 * @property int $work_id 作品 ID
 * @property int $coupon 语音包卡片兑换点数
 * @property int $notice 语音包消息提醒数
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 */
class WorkUserInfo extends ActiveRecord
{
    // 未登录用户免费抽卡状态
    const FREE_DRAW_NOT_LOGGED = -2;
    // 免费抽卡最后一天抽过卡的状态
    const FREE_DRAW_LAST_DAY = -1;
    // 用户还未免费抽标识
    const HAS_FREE_DRAW_CHANCE = 0;

    // 没有免费抽活动的标识（免费时长设置为此值）
    const HAS_NOT_FREE_EVENT = 0;

    // 作品类型
    public $type;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'work_user_info';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db2');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'create_time', 'modified_time', 'coupon', 'user_id', 'work_id'], 'integer'],
            [['id'], 'unique'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => '用户 ID',
            'work_id' => '作品 ID',
            'coupon' => '语音包卡片兑换点数',
            'notice' => '语音包消息提醒数',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
        ];
    }

    public static function getUserInfo(int $user_id, int $work_id)
    {
        if ((!$instance = self::findOne(['user_id' => $user_id, 'work_id' => $work_id]))) {
            if (!Mowangskuser::find()->where(['id' => $user_id])->exists()) {
                throw new HttpException(404, '用户不存在', 200020001);
            }
            if (!Work::find()->where(['id' => $work_id])->exists()) {
                throw new HttpException(404, '作品不存在', 200020001);
            }
            $redis = Yii::$app->redis;
            $key = $redis->generateKey(LOCK_GENERATE_WORK_USER_INFO, $work_id, $user_id);
            if ($redis->lock($key, 1)) {
                if (!$instance = self::findOne(['user_id' => $user_id, 'work_id' => $work_id])) {
                    $instance = new self();
                    $instance->user_id = $user_id;
                    $instance->work_id = $work_id;
                    $instance->save();
                    $redis->unlock($key);
                }
            } else {
                sleep(1);
                if (!$instance = self::findOne(['user_id' => $user_id, 'work_id' => $work_id])) {
                    throw new \Exception(Yii::t('yii', '{:class} {:work_id} {:user_id} generate failed', [
                        ':class' => static::class,
                        ':work_id' => $work_id,
                        ':user_id' => $user_id,
                    ]));
                }
                $redis->unlock($key);
            }
        }
        return $instance;
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
            $this->notice = 0;  // 消息提醒数默认为 0
            $this->coupon = 0;  // 初始兑换点为 0
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 获取语音包用户在某作品下剩余兑换点数
     *
     * @param int $user_id 用户 ID
     * @param int $work_id 作品 ID，若存入该值，则只查询该作品下剩余的兑换点数
     * @return int 兑换点
     */
    public static function getCoupon(?int $user_id, int $work_id)
    {
        // 若用户未登录，则兑换点（显示）为 0
        if (!$user_id) return 0;
        if ($work_id) {
            // 若存在作品 ID，则只查询该作品下兑换点数
            $user_info = self::getUserInfo($user_id, $work_id);
            if (!$user_info) return 0;
            return $user_info->coupon;
        }
    }

    /**
     * 获取用户在某作品下提醒数
     *
     * @param int|null $user_id 用户 ID
     * @param int $work_id 作品 ID
     * @return int 提醒数
     */
    public static function getNotice($user_id, int $work_id): int
    {
        if (!$user_id) return 0;
        // 获取消息时未必进入了相关作品，不使用 self::getUserInfo 获取用户信息
        // 避免提前创建了用户在作品中的记录而影响免费抽
        return (int)self::find()
            ->select('notice')
            ->where(['user_id' => $user_id, 'work_id' => $work_id])
            ->scalar();
    }

    /**
     * 判断用户是否为抽卡功能新用户
     *
     * @param int $user_id 用户 ID
     * @param Work $work
     * @return bool 当前用户是否为抽卡功能新用户
     * @todo 之后需要按作品下的季度划分新用户，目前以作品第一季度设定的值为准
     * @todo 语音包和求签包需要分开处理
     */
    public static function isNewUser(int $user_id, Work $work, int $season = 1): bool
    {
        // 迎新免费活动截止时间
        $free_end_time = 0;
        if ($work->type === Work::TYPE_OMIKUJI) {
            // 目前仅求签包有免费活动截止时间
            // TODO: 该数据之后应存于数据库中
            $redis = Yii::$app->redis;
            $key = $redis->generateKey(KEY_OMIKUJI_WORK_SEASON_EVENT_TIME, $work->id, $season);
            $free_end_time = (int)$redis->hGet($key, 'expiry_time');
        }
        $free_duration = self::getWorkFreeDuration($work, $season);
        if (!$user_id) {
            // 若用户未登录且作品有免费抽活动时，则视其为新用户
            $is_new_user = ($free_duration !== self::HAS_NOT_FREE_EVENT);
            if ($is_new_user && $free_end_time && $free_end_time < $_SERVER['REQUEST_TIME']) {
                // 若有免费活动截止时间且截止时间已到，则未登录用户不再视为新用户
                $is_new_user = false;
            }
            return $is_new_user;
        }
        // 用户第一次进入相关作品的时间点
        $user_info = self::getUserInfo($user_id, $work->id);
        if ($free_end_time && $user_info->create_time > $free_end_time) {
            // 若有免费活动截止时间且用户初次进入时间大于截止时间，则不再视为新用户
            return false;
        }
        // 迎新免费抽截止日 24:00
        $end_time = $free_duration + strtotime(date('Y-m-d', $user_info->create_time));
        $now = $_SERVER['REQUEST_TIME'];
        if ($now > $end_time) {
            return false;
        }
        // 迎新免费抽截止日 00:00
        $last_day = $end_time - ONE_DAY;
        if ($now > $last_day && $now <= $end_time) {
            // 若为迎新最后一天，用户当天未免费抽过语音则视为新用户，否则不为新用户
            return !WorkUserInfo::hasFreeDrew($user_id, $work->id);
        }
        return true;
    }

    /**
     * 获取作品迎新免费活动时长
     *
     * @param Work $work
     * @return int
     * @todo 先默认使用第一季度设定的时间，之后需要支持获取其他季度的值
     */
    public static function getWorkFreeDuration(Work $work, int $season = 1): int
    {
        switch ($work->type) {
            case Work::TYPE_VOICE:
                return (int)Yii::$app->redis->hGet(KEY_VOICE_FREE_DURATION, $work->id);
            case Work::TYPE_OMIKUJI:
                $redis = Yii::$app->redis;
                $key = $redis->generateKey(KEY_OMIKUJI_WORK_SEASON_EVENT_TIME, $work->id, $season);
                $duration = $redis->hGet($key, 'event_days');
                // 新用户免费时长，为 0 时表示不免费，未设置时长时默认免费一周
                return $duration !== false ? (int)$duration : ONE_WEEK;
                break;
            default:
                return 0;
        }
    }

    /**
     * 用户当天是否已经免费抽过卡
     *
     * @param int $user_id 用户 ID
     * @param int $work_id 作品 ID
     * @return bool 用户当天是否已经免费抽过卡
     */
    public static function hasFreeDrew(int $user_id, int $work_id): bool
    {
        if (!$user_id) return false;
        return self::FreeDrawExpires($work_id, $user_id) !== self::HAS_FREE_DRAW_CHANCE;
    }

    /**
     * 用户当天免费抽卡后下次免费抽卡的时间
     *
     * @param int $work_id 作品 ID
     * @param int $user_id 用户 ID
     * @return int 下次免费抽卡的时间，-2：用户未登录；-1：表示抽过了且当天是免费的最后一天；0：未抽卡；
     * 值大于 0 表示下次免费抽的时间
     */
    public static function FreeDrawExpires(int $work_id, int $user_id): int
    {
        if (!$user_id) return self::FREE_DRAW_NOT_LOGGED;
        // 判断用户是否当日已经免费抽过卡
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_VOICE_LOCK_DRAW_USER, $work_id, $user_id);
        // TODO: 键值的过期时间为设置时第二天的零点，冗余一秒，不存在该键值时即表示当天还未抽卡
        return (int)$redis->get($key);
    }

    /**
     * 获取抽卡机会
     *
     * @param int $user_id 用户 ID
     * @param LotteryPackage $lottery_package 抽卡卡包
     * @param int $season 季度
     * @param int $free 是否免费
     * @return bool|array 免费抽卡时返回 bool 值，花费钻石抽卡时返回消费记录 ID、钻石余额、花费钻石数组成的数组
     * @throws HttpException 作品不存在、语音包卡包未设置相关规则、余额不足时将抛出异常
     */
    public static function getDrawChance(int $user_id, LotteryPackage $lottery_package, int $season, int $free = 0)
    {
        $work = Work::findOne($lottery_package->work_id);
        if (!$work) {
            throw new HttpException(404, '作品不存在 T_T');
        }
        if ($free) {
            $now = $_SERVER['REQUEST_TIME'];
            $redis = Yii::$app->redis;
            $key = $redis->generateKey(KEY_VOICE_LOCK_DRAW_USER, $lottery_package->work_id, $user_id);
            if ($now >= $lottery_package->start_time && $now <= $lottery_package->end_time
                    && !$redis->get($key)) {
                $tomorrow = strtotime('tomorrow');
                // 得到免费抽卡的截止时间
                if (in_array($lottery_package->mark,
                    [LotteryPackage::MARK_FREE_DRAW, LotteryPackage::MARK_OMIKUJI_DRAW])) {
                    $user_info = WorkUserInfo::getUserInfo($user_id, $lottery_package->work_id);
                    $free_duration = self::getWorkFreeDuration($work, $season);
                    $end_time = $free_duration + strtotime(date('Y-m-d', $user_info->create_time));
                } else {
                    // 非新人语音包卡包及求签包时，使用语音活动卡包的活动截止时间
                    $end_time = $lottery_package->end_time;
                }
                // TODO: 过期时间和键值加 1 秒的冗余用于解决服务器时间不同步的问题，以后可以考虑更好的解决方案
                $expire_time = $end_time > $tomorrow ? $tomorrow + 1 : WorkUserInfo::FREE_DRAW_LAST_DAY;
                return $redis->set($key, $expire_time, ['nx', 'ex' => $tomorrow - $now + 1]);
            }
            return false;
        } else {
            // 花费钻石购买抽卡
            $is_voice = $work->type === Work::TYPE_VOICE;
            if ($is_voice && (!($rule = $lottery_package->getRules()))) {
                throw new HttpException(500, '语音包卡包未设置相关规则 T_T');
            }
            // 抽卡（求签）次数，求签包默认只为抽 1 次
            $draw_times = $is_voice ? $rule['times'] : 1;
            // 消耗钻石抽卡
            $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_VOICE]);
            $form->gift_id = $lottery_package->id;
            $form->from_id = $user_id;
            $form->num = $season;
            $form->title = "{$work->title} ({$season}, {$draw_times})";
            $form->type = $is_voice ? TransactionLog::TYPE_DRAW_CARD : TransactionLog::TYPE_OMIKUJI;
            $form->work_id = $work->id;
            $buy_info = $form->buyCard($lottery_package->price);
            return $buy_info;
        }
    }
}
