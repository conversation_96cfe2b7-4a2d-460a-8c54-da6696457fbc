<?php

namespace app\models;

/**
 * This is the model class for table "m_message_user_map".
 *
 * The followings are the available columns in table 'm_message_user_map':
 * @property int $id ID
 * @property int $recuid 接收用户 ID
 * @property int $messageid 信息 ID
 * @property int $statue 状态
 * @property int $time 创建时间
 */
class MMessageUserMap extends ActiveRecord
{
    const STATUS_UNREAD = 1;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_message_user_map';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['recuid', 'messageid', 'statue', 'time'], 'required'],
            [['statue'], 'integer']
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'recuid' => '接收用户 ID',
            'messageid' => '信息 ID',
            // @TODO: 之后该字段名应改回“status”
            'statue' => '状态',
            'time' => '创建时间',
        ];
    }
}
