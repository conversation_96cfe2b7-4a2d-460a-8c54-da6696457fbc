<?php

namespace app\models;

use app\components\auth\AuthBiliBili;
use app\components\auth\AuthPayPal;
use app\components\auth\AuthQQ;
use app\components\auth\wechat\WechatApiV3;
use app\components\util\MUtils;
use app\components\auth\AuthAli;
use app\components\auth\AuthWechat;
use app\components\util\Equipment;
use app\forms\RechargeForm;
use app\forms\UserContext;
use Exception;
use Google_Client, Google_Service_AndroidPublisher;
use GuzzleHttp\Client;
use missevan\util\MUtils as MUtils2;
use yii\db\Expression;
use yii\db\Transaction;
use yii\helpers\Json;
use yii\web\HttpException;
use Yii;

/**
 * This is the model class for table "recharge_order".
 *
 * @property integer $id
 * @property integer $uid
 * @property string $tid
 * @property integer $ctime
 * @property integer $cid
 * @property string $price
 * @property integer $num
 * @property integer $ccy
 * @property integer $status
 * @property integer $type
 * @property integer $origin
 * @property integer $create_time
 * @property integer $modified_time
 * @property integer $confirm_time
 *
 * @property array $detail
 */
class RechargeOrder extends ActiveRecord
{
    // 人民币元转分比例
    const RMB_YUAN_FEN_RATE = 100;

    // 充值平台
    const TYPE_APPLEPAY = 0;
    const TYPE_ALIPAY = 1;
    const TYPE_WECHATPAY = 2;
    const TYPE_CASH = 3;
    const TYPE_QQPAY = 4;
    const TYPE_TMALL_IOS = 5;
    const TYPE_TMALL_ANDROID = 6;
    const TYPE_PAYPAL = 7;
    const TYPE_GOOGLE_PAY = 8;
    const TYPE_IOS_SUPPLEMENT_ORDER = 9;
    const TYPE_CORP_TO_CORP = 10;
    const TYPE_DOUDIAN = 11;
    const TYPE_JINGDONG = 12;
    const TYPE_BILI_LARGE_PAY = 13;
    const TYPE_VIP = 14;  // 点播会员领取福利钻石

    // 订单状态
    const STATUS_SUCCESS = 1;  // 成功
    const STATUS_CREATE = 0;  // 创建
    const STATUS_CANCELED = -1;  // 取消（已退款）
    const STATUS_ERROR = -2;  // 错误
    const STATUS_DUPLICATE_PAY = -3; // 重复支付

    // 充值钻石的最大值与最小值
    const MAX_DIAMOND = 1000000;
    const MIN_DIAMOND = 1;
    const MIN_DIAMOND_PAYPAL = 100;

    // 钻石与人民币的兑换比率
    const DIAMOND_EXCHANGE_PRICE_RATE = 0.1;

    // 请求来源（桌面网页、手机网页、App、天猫旗舰店、抖店、微信公众号）
    const ORIGIN_DESKTOP_WEB = 0;
    const ORIGIN_MOBILE_WEB = 1;
    const ORIGIN_APP = 2;
    const ORIGIN_TMALL = 3;
    const ORIGIN_DOUDIAN = 4;
    const ORIGIN_JINGDONG = 5;
    const ORIGIN_WECHAT_PUBLIC_ACCOUNT = 6;

    // 谷歌支付状态，用于返回禁止谷歌重试的结构用到的变量
    const GOOGLE_PAY_STATUS_FAIL = -1;  // 失败
    const GOOGLE_PAY_STATUS_SUCCESS = 1;  // 成功

    // https://developers.google.com/android-publisher/api-ref/purchases/products#resource
    const GOOGLE_PAY_PURCHASE_SUCCESS = 0;
    const GOOGLE_PAY_PURCHASE_CANCELLED = 1;
    const GOOGLE_PAY_ACKNOWLEDGED = 1;
    const GOOGLE_PAY_CONSUMED = 1;

    // Google Pay 测试账号购买
    const GOOGLE_PAY_PURCHASE_TYPE_TEST = 0;

    // 支付方式
    const TOPUP_METHOD_WECHATPAY = 'wechatpay';
    const TOPUP_METHOD_ALIPAY = 'alipay';
    const TOPUP_METHOD_QQPAY = 'qqpay';
    const TOPUP_METHOD_PAYPAL = 'paypal';
    const TOPUP_METHOD_GOOGLEPAY = 'googlepay';

    private $_detail;

    private static $google_client;

    public $title;
    public $status_msg;
    public $more;  // 对应 RechargeOrderDetail 的 more

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->paydb;
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'recharge_order';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['uid', 'cid', 'price', 'num', 'ccy', 'status'], 'required'],
            [
                [
                    'uid', 'ctime', 'cid', 'num', 'ccy', 'status', 'type', 'origin', 'create_time', 'modified_time', 'confirm_time'
                ],
                'integer'
            ],
            [['price'], 'number'],
            [['tid'], 'string', 'max' => 32],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'uid' => '用户id',
            'type' => '充值平台',
            'tid' => '平台订单号',
            'ctime' => '订单创建时间',
            'cid' => '虚拟货币id',
            'price' => '虚拟货币价格',
            'num' => '虚拟货币数量',
            'ccy' => '虚拟货币类型',
            'status' => '订单状态',
            'origin' => '设备来源',  // 0 Web，1 手机网页，2 App
            'create_time' => '创建时间',
            'modified_time' => '最后修改时间',
            'confirm_time' => '交易确认时间',  // 退款、取消订单不更新此字段
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        if (Yii::$app->equip->isFromMiMiApp()) {
            // 去除小数点，例 '120.00' => 120 => '120'
            $this->price = (string)intval($this->price);
        }
        if ($this->more) {
            $this->more = Json::decode($this->more);
        }
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->ctime = $time;
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    public function setDetail(array $detail)
    {
        $this->_detail = $detail;
    }

    public function getDetail()
    {
        return $this->_detail;
    }

    // 该函数有副作用
    public static function createOrder(int $ccy_id, int $user_id, int $device, UserContext $user_context, int $type = self::TYPE_APPLEPAY,
            int $origin = self::ORIGIN_DESKTOP_WEB, int $custom_diamond_num = -1)
    {
        /**
         * @var $user Mowangskuser
         */
        $user = Mowangskuser::find()
            ->select('id, ctime, confirm')
            ->where(['id' => $user_id])
            ->one();
        if (!$user) {
            throw new HttpException(404, '用户不存在');
        }
        if ($user->isBanTopupAndConsume()) {
            throw new HttpException(403, '您的账号暂被系统停封，不可进行充值消费操作');
        }
        /**
         * @var TopupMenu $ccy
         */
        $ccy = TopupMenu::getItem($ccy_id);
        if (!$ccy) {
            throw new HttpException(404, Yii::t('app/error', 'The coin goods does not exist'), 200360001);
        }
        if ($ccy->isNewUserScope() && ($balance = Balance::getByPk($user->id)) && !$balance->hasNewUserTopupDiscount($user)) {
            throw new HttpException(403, '福利已过期');
        }
        // 自定义充值
        if (0 === (int)$ccy->price) {
            if ($custom_diamond_num === -1) {
                $custom_diamond_num = (int)Yii::$app->request->post('diamond');
            }
            if ($custom_diamond_num > self::MAX_DIAMOND || $custom_diamond_num < self::MIN_DIAMOND) {
                throw new HttpException(400, '充值钻石数不合法');
            }
            // PayPal 最低充值金额进行限制（10 元及其以上）
            if (self::TYPE_PAYPAL === $type && $custom_diamond_num < self::MIN_DIAMOND_PAYPAL) {
                throw new HttpException(400, sprintf('PayPal 最低充值金额需大于或等于 %d 元',
                    Balance::profitUnitConversion(self::MIN_DIAMOND_PAYPAL, Balance::CONVERT_DIAMOND_TO_YUAN)));
            }
            $price = (string)round(($custom_diamond_num / DIAMOND_EXRATE), 1);
            $ccy->price = $price;
            $ccy->num = $custom_diamond_num;
        }
        $order = new self([
            'uid' => $user_id,
            'cid' => $ccy->id,
            'price' => $ccy->price,
            'ccy' => $ccy->ccy,
            'num' => $ccy->num,
            'status' => self::STATUS_CREATE,
            'type' => $type,
            'origin' => $origin,
            'detail' => $user_context->toArray(),
        ]);
        if (!$order->save()) {
            Yii::error(sprintf('create order error: %s', MUtils2::getFirstError($order)), __METHOD__);
            throw new HttpException(500, '服务器暂时维护中，请稍候再试');
        }

        $order_detail = $order->generateDetail();
        if (!$order_detail->save()) {
            Yii::error(sprintf('create order detail error: %s', MUtils2::getFirstError($order_detail)), __METHOD__);
            throw new HttpException(500, '服务器暂时维护中，请稍候再试');
        }
        return $order;
    }

    /**
     * 加锁获取订单
     * @todo 该方法必须在事务中使用，之后代码控制
     * @param array|int|string $condition 查询条件，例
     *   15
     *  'dev123'
     *   ['id' => 15, 'status' => 2]
     *   ['id' => 15, 'status' => [1, 2]]
     *   ['status' => 1, 'tid' => ['dev123', 'dev124']]
     *
     * @return null|self
     */
    public static function findForUpdate($condition)
    {
        if (is_array($condition)) {
            $where = implode(' AND ', array_map(function ($key, $value) {
                if (is_array($value)) {
                    if (MUtils2::isUintArr($value)) {
                        return MUtils::generateIntegerIn($key, $value);
                    } else {
                        return $key . ' IN ('
                            . implode(',', array_map(function ($v) {
                                return "'{$v}'";
                            }, $value)) . ')';
                    }
                } elseif (is_numeric($value)) {
                    return "{$key} = {$value}";
                } else {
                    return "{$key} = '{$value}'";
                }
            }, array_keys($condition), $condition));
        } elseif (is_numeric($condition)) {
            $where = "id = {$condition}";
        } else {
            $where = "id = '{$condition}'";
        }

        $table = self::tableName();
        return self::findBySql("SELECT * FROM $table WHERE $where FOR UPDATE")->one();
    }

    /**
     * @param int $cid 充值类型 ID
     * @param int $user_id 用户 ID
     * @param int $origin 来源
     * @param UserContext $user_context 充值订单传参（UA、IP）
     * @param string $openid 微信 openid
     * @return array
     * @throws HttpException
     */
    public static function generateWechatpayOrder(int $cid, int $user_id, int $origin, UserContext $user_context, string $openid = '')
    {
        if ($origin === self::ORIGIN_WECHAT_PUBLIC_ACCOUNT) {
            $wechat = WechatApiV3::newInstance();
        } else {
            $wechat = new AuthWechat();
        }
        switch ($origin) {
            case self::ORIGIN_APP:
                $order = self::createOrder($cid, $user_id, $user_context->equipment->getOs(), $user_context, self::TYPE_WECHATPAY, $origin);
                $pay = $wechat->tradeAppPay($order);
                return [
                    'order' => $order,
                    'orderString' => $pay,
                ];
                break;
            case self::ORIGIN_MOBILE_WEB:
                $order = self::createOrder($cid, $user_id, Equipment::Web, $user_context, self::TYPE_WECHATPAY, $origin);
                $mweb_url = $wechat->tradeMobileWebPay($order);
                return [
                    'order' => $order,
                    'code_url' => '',
                    'mweb_url' => $mweb_url,
                    'order_id' => $order->getOrderId(),
                ];
                break;
            case self::ORIGIN_DESKTOP_WEB:
                $order = self::createOrder($cid, $user_id, Equipment::Web, $user_context, self::TYPE_WECHATPAY, $origin);
                $code_url = $wechat->tradePagePay($order);
                return [
                    'order' => $order,
                    'code_url' => $code_url,
                    'mweb_url' => '',
                    'order_id' => $order->getOrderId(),
                ];
                break;
            case self::ORIGIN_WECHAT_PUBLIC_ACCOUNT:
                $order = self::createOrder($cid, $user_id, Equipment::Web, $user_context, self::TYPE_WECHATPAY, $origin);
                $invoke_body = $wechat->tradeJSAPIPay($order, $openid);
                return [
                    'order' => $order,
                    'invoke_body' => $invoke_body,
                    'order_id' => $order->getOrderId(),
                ];
            default:
                throw new HttpException(400, '参数错误');
        }
    }

    /**
     * 生成 QQ 支付订单
     *
     * @param integer $cid 充值类型 ID
     * @param integer $user_id 用户 ID
     * @param integer $origin 来源
     * @param UserContext $user_context 充值订单传参（UA、IP）
     * @return array
     * @throws HttpException
     */
    public static function generateQQPayOrder(int $cid, int $user_id, int $origin, UserContext $user_context)
    {
        $qq = new AuthQQ();
        switch ($origin) {
            case self::ORIGIN_APP:
                $order = self::createOrder($cid, $user_id, $user_context->equipment->getOs(), $user_context, self::TYPE_QQPAY, $origin);
                $pay = $qq->tradeAppPay($order);
                // App 支付时需要签名 sign (客户端的单独数字签名)、nonce、appid 及 bargainorId 等信息
                // 文档地址：https://qpay.qq.com/buss/wiki/38/1196
                return [
                    'order' => $order,
                    'orderString' => $pay,
                ];
                break;
            case self::ORIGIN_MOBILE_WEB:
                $order = self::createOrder($cid, $user_id, Equipment::Web, $user_context, self::TYPE_QQPAY, $origin);
                $pay = $qq->tradeMobileWebPay($order);
                $redirect_url = Yii::$app->params['domainMobileWeb']
                    . '/wallet/topupresult?out_trade_no=' . $order->getOrderId();
                return [
                    'order' => $order,
                    'orderString' => [
                        'appId' => $pay['appId'],
                        'bargainorId' => $pay['bargainorId'],
                        'tokenId' => $pay['tokenId'],
                        'pubAcc' => $pay['pubAcc'],
                    ],
                    'redirect_url' => $redirect_url,
                ];
                break;
            case self::ORIGIN_DESKTOP_WEB:
                $order = self::createOrder($cid, $user_id, Equipment::Web, $user_context, self::TYPE_QQPAY, $origin);
                $pay = $qq->tradeNativeScanPay($order);
                $redirect_url = Yii::$app->params['domainMissevan'] .
                    sprintf('/mperson/wallet/topupresult?out_trade_no=%s', $order->getOrderId());
                return [
                    'order' => $order,
                    'code_url' => $pay['code_url'],
                    'redirect_url' => $redirect_url,
                ];
                break;
            default:
                throw new \Exception('参数错误');
        }
    }

    /**
     * 生成 PayPal 支付订单
     *
     * @param integer $cid 充值类型 ID
     * @param integer $user_id 用户 ID
     * @param integer $origin 来源
     * @param UserContext $user_context 充值订单传参（UA、IP）
     * @return array
     * @throws HttpException
     */
    public static function generatePayPalOrder(int $cid, int $user_id, int $origin, UserContext $user_context)
    {
        $device = self::ORIGIN_APP === $origin ? Equipment::Android : Equipment::Web;
        $order = self::createOrder($cid, $user_id, $device, $user_context, self::TYPE_PAYPAL, $origin);

        $paypal = new AuthPayPal();
        $form = $paypal->tradePay($order, $origin);
        return [
            'order' => $order,
            'form' => $form,
        ];
    }

    /**
     * 批量生成现金支付订单
     *
     * @param int $topup_menu_id 充值价目 ID
     * @param array $user_ids 用户 IDs
     * @param int $diamond 自定义充值时钻石数目
     * @param UserContext $user_context 充值订单传参（UA、IP）
     * @param int $order_type 充值类型（默认为现金充值）
     * @param string $coin_field 充值货币类型
     * @param array $more 额外信息
     * @param Transaction|null $transaction
     * @param callable|null $before_commit
     * @return array
     * @throws Exception
     */
    public static function batchGenerateCashOrder(int $topup_menu_id, array $user_ids, int $diamond, UserContext $user_context,
            int $order_type = RechargeOrder::TYPE_CASH, string $coin_field = PayAccount::COIN_FIELD_IOS, array $more = [],
            ?Transaction $transaction = null, ?callable $before_commit = null)
    {
        try {
            // 检查参数
            if (!in_array($coin_field, PayAccount::TYPE_INDEX_COIN_FIELD_MAP)) {
                throw new HttpException(400, '不存在该充值货币类型');
            }
            if ($coin_field === PayAccount::COIN_FIELD_IOS && self::isIOSGooglePayNewTopupMenuTime()) {
                $coin_field = PayAccount::COIN_FIELD_NEW_IOS;
            }
            if ($coin_field === PayAccount::COIN_FIELD_GOOGLE_PAY && self::isIOSGooglePayNewTopupMenuTime()) {
                $coin_field = PayAccount::COIN_FIELD_NEW_GOOGLEPAY;
            }
            if (empty($user_ids)) {
                throw new HttpException(400, '用户 ID 不可为空');
            }
            $user_ids = array_map('intval', $user_ids);
            if (!empty(array_diff_assoc($user_ids, array_unique($user_ids)))) {
                throw new HttpException(400, '存在重复的用户 ID');
            }

            $origin = self::ORIGIN_DESKTOP_WEB;
            if ($user_context->equipment && $user_context->equipment->isFromApp()) {
                $origin = self::ORIGIN_APP;
            }
            if (array_key_exists('event_id', $more) && $more['event_id'] && $event = MEvent::findOne($more['event_id'])) {
                if ($event->biz_type && !array_key_exists('biz_type', $more)) {
                    $more['biz_type'] = $event->biz_type;
                }
                if ($event->play_type && !array_key_exists('play_type', $more)) {
                    $more['play_type'] = $event->play_type;
                }
            }
            $user_context->add('more', $more);

            set_time_limit(0);
            $transaction = $transaction ?: self::getDb()->beginTransaction();
            $order_ids = [];
            foreach ($user_ids as $user_id) {
                $order = self::createOrder($topup_menu_id, $user_id, Equipment::Web, $user_context, $order_type, $origin, $diamond);
                PayAccount::generateCommonCoin([array_flip(PayAccount::TYPE_INDEX_COIN_FIELD_MAP)[$coin_field] => $order->num], $order->id, $user_id, function (PayAccount $account) use ($more, $order) {
                    $account->more = array_merge($account->more ?: [], $more, ['order_type' => $order->type]);
                });
                $order_ids[] = $order->id;
                Balance::getByPk($user_id)->updateCounters(['all_topup' => $diamond, 'all_coin' => $diamond]);
            }
            $time = $_SERVER['REQUEST_TIME'];
            $update_fields = ['status' => self::STATUS_SUCCESS, 'tid' => self::generateTidSqlExpression(), 'confirm_time' => $time, 'modified_time' => $time];
            if (in_array($order_type, [self::TYPE_CASH, self::TYPE_VIP])) {
                $update_fields['price'] = 0;
            }
            if (self::updateAll($update_fields, ['id' => $order_ids, 'status' => self::STATUS_CREATE]) !== count($order_ids)) {
                throw new Exception('修改充值信息数量异常，请稍后再试');
            }
            if (array_key_exists('price', $update_fields) && $update_fields['price'] === 0) {
                RechargeOrderDetail::updateAll(['real_price' => 0], ['id' => $order_ids]);
            }
            if (is_callable($before_commit)) {
                $before_commit($order_ids);
            }
            $transaction->commit();
            return [
                'ids' => $order_ids,
                'num' => $diamond,
            ];
        } catch (Exception $e) {
            if ($transaction) {
                $transaction->rollBack();
            }
            throw $e;
        }
    }

    /**
     * 获取两个值之间的所有值
     *
     * @param int $first_id
     * @param int $last_id
     * @return array
     */
    private static function getNumberList(int $first_id, int $last_id)
    {
        if ($first_id <= 0 || $last_id <= 0 || $first_id > $last_id) {
            throw new Exception('ID 值错误');
        }

        return range($first_id, $last_id);
    }

    /**
     * 生成支付宝支付订单
     *
     * @param integer $cid 充值类型 ID
     * @param integer $user_id 用户 ID
     * @param integer $origin 来源
     * @param UserContext $user_context 充值订单传参（UA、IP）
     * @return array
     * @throws HttpException
     */
    public static function generateAlipayOrder(int $cid, int $user_id, int $origin, UserContext $user_context)
    {
        $ali = new AuthAli();
        switch ($origin) {
            case self::ORIGIN_APP:
                $order = self::createOrder($cid, $user_id, $user_context->equipment->getOs(), $user_context, self::TYPE_ALIPAY, $origin);
                $response = $ali->tradeAppPay($order);
                return [
                    'order' => $order,
                    'orderString' => $response,
                ];
                break;
            case self::ORIGIN_MOBILE_WEB:
                $order = self::createOrder($cid, $user_id, Equipment::Web, $user_context, self::TYPE_ALIPAY, $origin);
                $form = $ali->tradeMobileWebPay($order);
                return [
                    'order' => $order,
                    'form' => $form,
                ];
                break;
            case self::ORIGIN_DESKTOP_WEB:
                $order = self::createOrder($cid, $user_id, Equipment::Web, $user_context, self::TYPE_ALIPAY, $origin);
                $form = $ali->tradePagePay($order);
                return [
                    'order' => $order,
                    'form' => $form,
                ];
                break;
            default:
                throw new HttpException(400, '参数错误');
        }
    }

    /**
     * @param int $ccy_id
     * @param string $transaction_id
     * @param int $user_id
     * @param array $receipt_body
     * @param UserContext $user_context
     * @return bool|int
     */
    private static function recreateIOSOrderAndCheckRisk(int $ccy_id, string $transaction_id, int $user_id, array $receipt_body, UserContext $user_context)
    {
        // 创建订单 & 发起B站充值风控异步请求
        $out_order_id = self::generateIOSOrderNew($ccy_id, $user_id, $user_context);
        $order_id = self::getRealOrderId($out_order_id);
        if (ENABLE_BILIBILI_RISK_CHECK) {
            // B站风控链路存在 100ms 时延，在查询风控结果前停留 200ms
            usleep(200 * 1000);
            // 检查B站充值风控结果
            $risk_level = TopupRiskLevel::checkRisk($out_order_id, $receipt_body, $transaction_id);
            // B站充值高风险（直接拒绝）误伤率较高，添加 download_id=0 判断（代充用户 download_id 几乎都是 0）
            if ($risk_level === TopupRiskLevel::LEVEL_HIGH_RISK_REJECT_DIRECTLY && 0 === (int)$receipt_body['receipt']['download_id']) {
                Yii::error(sprintf('iOS 充值重试未通过风控: order_id[%d]', $order_id), __METHOD__);
                return false;
            }
        }

        return $order_id;
    }

    public static function generateIOSOrder(TopupMenu $ccy, string $transaction_id, int $user_id, array $receipt_body, UserContext $user_context)
    {
        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_GENERATE_ORDER, $transaction_id);
        if ($redis->lock($lock, ONE_MINUTE)) {
            if (!RechargeOrder::find()
                ->where(['tid' => $transaction_id, 'type' => self::TYPE_APPLEPAY])
                ->exists()
            ) {
                $transaction = null;
                try {
                    /**
                     * @var Balance $balance
                     */
                    $balance = Balance::getByPk($user_id);
                    $order_id = self::recreateIOSOrderAndCheckRisk($ccy->id, $transaction_id, $user_id, $receipt_body, $user_context);
                    if (!$order_id) {
                        // 未通过风控，假装充值成功
                        return $balance->getTotalBalance() + $ccy->num;
                    }

                    $transaction = self::getDb()->beginTransaction();
                    $affected_num = self::updateAll([
                        'status' => self::STATUS_SUCCESS,
                        'tid' => $transaction_id,
                        'modified_time' => $_SERVER['REQUEST_TIME'],
                        'confirm_time' => $_SERVER['REQUEST_TIME'],
                    ], ['id' => $order_id, 'uid' => $user_id, 'status' => self::STATUS_CREATE]);
                    if ($affected_num <= 0) {
                        Yii::error(sprintf('订单更新失败：order_id[%d]', $order_id));
                        throw new Exception('订单更新失败');
                    }

                    if (self::isIOSGooglePayNewTopupMenuTime()) {
                        PayAccount::generateCommonCoin([PayAccount::TYPE_COIN_INDEX_NEW_IOS => $ccy->num], $order_id, $user_id);
                        $balance->updateCounters([
                            'all_topup' => $ccy->num,
                            'all_coin' => $ccy->num,
                        ]);
                    } else {
                        $balance->updateCounters([
                            'ios' => $ccy->num,
                            'all_topup' => $ccy->num,
                            'all_coin' => $ccy->num,
                        ]);
                    }
                    $transaction->commit();
                    $transaction = null;
                    return $balance->getTotalBalance();
                } catch (\Exception $e) {
                    if ($transaction) {
                        $transaction->rollBack();
                    }
                    throw $e;
                } finally {
                    $redis->unlock($lock);
                }
            } else {
                throw new HttpException(200, Yii::t('app/error', 'Transaction has been closed'));
            }
        }
        throw new HttpException(403, Yii::t('app/error', 'Order is being updated'), 200360002);
    }

    /**
     * 获取充值记录
     *
     * @param int $user_id 用户 ID
     * @param int|array $status 充值状态（例 1 或 [1, -3]）
     * @param int $page 所在页
     * @return ReturnModel
     */
    public static function getList(int $user_id, $status = RechargeOrder::STATUS_SUCCESS, int $page = 1)
    {
        $recharge_order = self::find()
            ->alias('t1')
            ->select('t1.id, t1.uid, t1.tid, t1.cid, t1.price, t1.num, t1.status, t1.type, t1.create_time, t2.more')
            ->leftJoin(RechargeOrderDetail::tableName() . ' AS t2', 't1.id = t2.id')
            ->where(['t1.uid' => $user_id, 't1.status' => $status])
            ->andWhere('t1.type <> :type', [':type' => self::TYPE_DOUDIAN])
            ->orderBy(['t1.id' => SORT_DESC]);

        $list = MUtils::getPaginationModels($recharge_order,
            DEFAULT_PAGE_SIZE,
            ['current_page' => $page]
        );
        foreach ($list->Datas as $item) {
            /**
             * @var RechargeOrder $item
             */
            $item->id = $item->getOrderId();
            $item->ctime = $item->create_time;
            $item->setOrderTitleAndStatusMsg();
            unset($item->more);
        }
        return $list;
    }

    public function getOrderId()
    {
        return ('dev' === YII_ENV ? 'dev' : '') .
            $this->create_time .
            str_pad($this->type, 3, '0', STR_PAD_LEFT) .
            str_pad($this->id, 10, '0', STR_PAD_LEFT);
    }

    public static function getRealOrderId($order_id)
    {
        return (int)substr($order_id, -10);
    }

    /**
     * 苹果充值前创建 iOS 订单
     *
     * @param integer $cid 钻石价目表 ccy 的主键
     * @param integer $user_id 用户 ID
     * @param UserContext $user_context 充值订单传参（UA、IP）
     * @return string 订单号（根据 recharge_order 表中对应记录生成）
     */
    public static function generateIOSOrderNew(int $cid, int $user_id, UserContext $user_context)
    {
        $order = self::createOrder($cid, $user_id, Equipment::iOS, $user_context, self::TYPE_APPLEPAY,
            self::ORIGIN_APP);
        $out_order_id = $order->getOrderId();

        if (ENABLE_BILIBILI_RISK_CHECK) {
            AuthBiliBili::payRskAsk([
                'traceId' => md5($order->getOrderId()),
                'txId' => $out_order_id,
                'orderId' => $out_order_id,
                'reqData' => [
                    'orderId' => $out_order_id,
                    'orderTime' => date('Y-m-d H:i:s.0', $order->create_time),
                    'payAmount' => (string)Balance::profitUnitConversion($order->price, Balance::CONVERT_YUAN_TO_FEN),
                    'createUa' => Yii::$app->equip->getUserAgent(),
                    'shieldTransactionIds' => '',
                    'transactionId' => '',
                ],
                'interfaceName' => 'PAY',
            ]);
        }
        return $out_order_id;
    }

    /**
     * 苹果充值成功后更新 iOS 订单
     * （对于订单金额/主人的不匹配暂时处理：创建新订单、记录日志）
     *
     * @param TopupMenu $ccy 钻石价目表类 app\models\TopupMenu 实例
     * @param string $transaction_id 苹果交易凭证 ID
     * @param integer $user_id 用户 ID
     * @param integer $order_id 充值订单表 recharge_order 主键 ID
     * @return integer 充值成功后的钻石总额
     * @throws \Exception
     */
    public static function updateIOSOrder(TopupMenu $ccy, string $transaction_id, int $user_id, int $order_id)
    {
        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_GENERATE_ORDER, $transaction_id);
        if (!$redis->lock($lock, ONE_MINUTE)) {
            throw new HttpException(400, Yii::t('app/error', 'Order is being updated'));
        }

        $connection = self::getDb();
        $transaction = $connection->beginTransaction();
        $error_msg = null;
        try {
            if (self::find()
                    ->where(['tid' => $transaction_id, 'type' => self::TYPE_APPLEPAY])
                    ->exists()) {
                $error_msg = Yii::t('app/error', 'Transaction has been closed');
                throw new \Exception($error_msg);
            }
            if (!$order = self::findForUpdate(['id' => $order_id, 'status' => self::STATUS_CREATE])) {
                $error_msg = Yii::t('app/error', 'Order not found');
                throw new \Exception($error_msg);
            }
            $balance = Balance::getByPk($order->uid);
            if ($ccy->isNewUserScope() && !$balance->hasNewUserTopupDiscount(Yii::$app->user)) {
                throw new HttpException(403, '福利已过期');
            }
            if ($order->uid !== $user_id || $order->cid !== $ccy->id) {
                // 对于订单金额对不上及用户 ID 对不上的处理：新建订单处理并记录错误日志
                // 原因：iOS 本次请求更新的订单可能是上次未更新的订单；
                // 上次充值后的前一用户退出前未更新订单，登录了新用户而发起更新前一用户的订单
                if ($order->uid !== $user_id) {
                    $error_log = "订单主人错误：当前请求用户 {$user_id}，订单原主人 {$order->uid}";
                } else {
                    $error_log = "订单金额错误：当前充值金额 {$ccy->price}，订单原金额 {$order->price}";
                }
                Yii::error($error_log, __METHOD__);
                $order = new self([
                    'uid' => $user_id,
                    'tid' => $transaction_id,
                    'cid' => $ccy->id,
                    'price' => $ccy->price,
                    'num' => $ccy->num,
                    'ccy' => $ccy->ccy,
                    'status' => self::STATUS_SUCCESS,
                    'type' => self::TYPE_APPLEPAY,
                    'origin' => self::ORIGIN_APP,
                ]);
            }
            $order->tid = $transaction_id;
            $order->status = self::STATUS_SUCCESS;
            $order->confirm_time = $_SERVER['REQUEST_TIME'];
            if (!$order->save()) {
                Yii::error('订单更新失败：' . MUtils::getFirstError($order));
                throw new \Exception(Yii::t('app/error', 'Update order failed'));
            }
            if (self::isIOSGooglePayNewTopupMenuTime()) {
                PayAccount::generateCommonCoin([PayAccount::TYPE_COIN_INDEX_NEW_IOS => $ccy->num], $order_id, $user_id);
                $balance->updateCounters([
                    'all_topup' => $order->num,
                    'all_coin' => $order->num,
                ]);
            } else {
                $balance->updateCounters([
                    'ios' => $order->num,
                    'all_topup' => $order->num,
                    'all_coin' => $order->num,
                ]);
            }
            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            if ($error_msg) {
                // 对于“订单不存在”情况，可能 iOS 充值后更新订单时会连发两个请求
                // 其中一个请求已经成功更新订单，另一请求需同样返回成功信息以提示用户本次充值成功
                throw new HttpException(200, $error_msg);
            }
            throw $e;
        } finally {
            $redis->unlock($lock);
        }
        return $balance->getTotalBalance();
    }

    /**
     * 生成 Google Pay 订单
     *
     * @param int $cid 钻石价目表 ccy 的主键
     * @param int $user_id 用户 ID
     * @return string 外部订单号
     * @param UserContext $user_context 充值订单传参（UA、IP）
     * @throws HttpException
     */
    public static function generateGooglePayOrder(int $cid, int $user_id, UserContext $user_context)
    {
        $device = TopupMenu::DEVICE_ANDROID;
        if (Yii::$app->equip->isFromMissEvanApp()) {
            $device = TopupMenu::DEVICE_GOOGLE_PLAY;
        }
        $order = self::createOrder($cid, $user_id, $device, $user_context, self::TYPE_GOOGLE_PAY,
            self::ORIGIN_APP);
        $out_order_id = $order->getOrderId();

        return $out_order_id;
    }

    public function isSuccess(): bool
    {
        return $this->status === self::STATUS_SUCCESS;
    }

    public function isProcessing()
    {
        return $this->status === self::STATUS_CREATE;
    }

    public static function initiateOrder(int $user_id, TopupMenu $ccy, int $type, int $origin)
    {
        return new self([
            'uid' => $user_id,
            'cid' => $ccy->id,
            'price' => $ccy->price,
            'num' => $ccy->num,
            'ccy' => $ccy->ccy,
            'status' => self::STATUS_CREATE,
            'type' => $type,
            'origin' => $origin,
        ]);
    }

    /**
     * 验证 Google Pay 订单
     *
     * @param int $user_id 用户 ID
     * @param string $out_trade_no 外部订单号
     * @param string $purchase_id 购买商品 ID
     * @param string $purchase_token 支付后的 token
     * @return array|int
     * @throws HttpException
     */
    public static function verifyGooglePayOrder(int $user_id, string $out_trade_no, string $purchase_id, string $purchase_token)
    {
        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_GENERATE_ORDER, $out_trade_no ?: $user_id);
        if (!$redis->lock($lock, ONE_MINUTE)) {
            throw new HttpException(400, Yii::t('app/error', 'Order is being updated'));
        }
        $order = null;
        if ($out_trade_no) {
            $order_id = self::getRealOrderId($out_trade_no);
            $order = self::findOne([
                'id' => $order_id,
                'type' => self::TYPE_GOOGLE_PAY,
                'status' => [self::STATUS_CREATE, self::STATUS_SUCCESS],
            ]);
            if (!$order) {
                Yii::error('充值订单不存在：' . $order_id, __METHOD__);
                throw new HttpException(404, Yii::t('app/error', 'Order not found'));
            }
            if ($order->isSuccess()) {
                return self::unlockAndReturnGooglePayOrderResp($user_id, self::GOOGLE_PAY_STATUS_SUCCESS, '', $lock);
            }
        }

        // 官方文档验证订单步骤：https://developer.android.com/google/play/billing/lifecycle/one-time?hl=zh-cn#process
        // 1. 查询 purchases.products.get 端点，以获取最新的一次性商品购买交易状态。
        if (!$res = self::getGooglePayOrder($purchase_id, $purchase_token)) {
            throw new HttpException(404, Yii::t('app/error', 'Google Pay order not found'));
        }

        if (!Equipment::isAppOlderThan(null, '6.2.4')) {
            if ($res->getPurchaseState() === self::GOOGLE_PAY_PURCHASE_CANCELLED) {
                // 当订单取消时，禁止重试
                // https://developers.google.com/android-publisher/api-ref/rest/v3/purchases.products?hl=zh-cn
                return self::unlockAndReturnGooglePayOrderResp($user_id, self::GOOGLE_PAY_STATUS_FAIL,
                    Yii::t('app/error', 'Transaction has been cancelled'), $lock);
            }
        }

        // 2. 调用 getPurchaseState()，并确保购买交易状态为 PURCHASED。
        if ($res->getPurchaseState() !== self::GOOGLE_PAY_PURCHASE_SUCCESS) {
            throw new HttpException(400, Yii::t('app/error', 'Purchase has not been completed'));
        }

        // 3. 如果商品是消耗型商品，请调用 purchases.products.consume 方法将商品标记为已消耗，
        // 以便用户在消耗掉商品后可以再次购买该商品。此方法也能确认购买交易。
        // 消耗需要在确认前调用，消耗动作会包含确认动作
        if (!Equipment::isAppOlderThan('', '6.2.4')) {
            // 安卓 6.1.9 开始支持在服务端进行消耗
            if ($res->getConsumptionState() !== self::GOOGLE_PAY_CONSUMED) {
                self::consumeGooglePurchase($purchase_id, $purchase_token);
                // 消耗后，重新调用 get
                if (!$res = self::getGooglePayOrder($purchase_id, $purchase_token)) {
                    throw new HttpException(404, Yii::t('app/error', 'Google Pay order not found'));
                }
            }
        }

        if ($res->getAcknowledgementState() !== self::GOOGLE_PAY_ACKNOWLEDGED) {
            throw new HttpException(400, Yii::t('app/error', 'Purchase has not been completed'));
        }
        if (YII_ENV_PROD && $res->getPurchaseType() === self::GOOGLE_PAY_PURCHASE_TYPE_TEST) {
            if (Equipment::isAppOlderThan(null, '6.2.4')) {
                throw new HttpException(403, '测试账号不可用于线上购买');
            }
            // 测试账号禁止重试
            return self::unlockAndReturnGooglePayOrderResp($user_id, self::GOOGLE_PAY_STATUS_FAIL, '测试账号不可用于线上购买', $lock);
        }
        if (self::find()->where(['tid' => $res->getOrderId()])->limit(1)->exists()) {
            if (Equipment::isMiMiAppOlderThan(null, '1.0.6')) {
                throw new HttpException(400, Yii::t('app/error', 'Order has been completed'));
            }
            if (Equipment::isAppOlderThan(null, '6.2.4')) {
                throw new HttpException(200, Yii::t('app/error', 'Order has been completed'));
            }
            // 订单已完成，禁止重试
            return self::unlockAndReturnGooglePayOrderResp($user_id, self::GOOGLE_PAY_STATUS_SUCCESS,
                Yii::t('app/error', 'Order has been completed'), $lock);
        }
        $device = TopupMenu::DEVICE_ANDROID;
        if (Yii::$app->equip->isFromMissEvanApp()) {
            $device = TopupMenu::DEVICE_GOOGLE_PLAY;
        }

        $transaction = self::getDb()->beginTransaction();
        try {
            if (!$ccy = TopupMenu::findProduct($purchase_id, $device)) {
                if (Equipment::isAppOlderThan(null, '6.2.4')) {
                    throw new HttpException(404, Yii::t('app/error', 'The coin goods does not exist'));
                }
                // 禁止重试
                return self::unlockAndReturnGooglePayOrderResp($user_id, self::GOOGLE_PAY_STATUS_FAIL,
                    Yii::t('app/error', 'The coin goods does not exist'));
            }
            $balance = Balance::getByPk($user_id);
            if ($ccy->isNewUserScope() && !$balance->hasNewUserTopupDiscount(Yii::$app->user)) {
                if (Equipment::isAppOlderThan(null, '6.2.4')) {
                    throw new HttpException(403, '福利已过期');
                }
                // 禁止重试
                return self::unlockAndReturnGooglePayOrderResp($user_id, self::GOOGLE_PAY_STATUS_FAIL, '福利已过期');
            }
            if ($order) {
                if ($order->uid !== $user_id || $order->cid !== $ccy->id) {
                    if ($order->uid !== $user_id) {
                        Yii::error("订单主人错误：当前请求用户 {$user_id}，订单原主人 {$order->uid}", __METHOD__);
                    } else {
                        Yii::error("订单金额错误：当前充值金额 {$ccy->price}，订单原金额 {$order->price}", __METHOD__);
                    }
                    $order = self::initiateOrder($user_id, $ccy, self::TYPE_GOOGLE_PAY, self::ORIGIN_APP);
                }
            } else {
                $order = self::initiateOrder($user_id, $ccy, self::TYPE_GOOGLE_PAY, self::ORIGIN_APP);
            }

            $order->tid = $res->getOrderId();
            $order->status = self::STATUS_SUCCESS;
            $order->confirm_time = $_SERVER['REQUEST_TIME'];
            if (!$order->save()) {
                Yii::error('订单更新失败：' . MUtils::getFirstError($order), __METHOD__);
                throw new Exception(Yii::t('app/error', 'Update order failed'));
            }
            if (self::isIOSGooglePayNewTopupMenuTime()) {
                PayAccount::generateCommonCoin([PayAccount::TYPE_COIN_INDEX_NEW_GOOGLEPAY => $order->num], $order->id, $user_id);
                $balance->updateCounters([
                    'all_topup' => $order->num,
                    'all_coin' => $order->num,
                ]);
            } else {
                $balance->updateCounters([
                    'googlepay' => $order->num,
                    'all_topup' => $order->num,
                    'all_coin' => $order->num,
                ]);
            }

            $transaction->commit();

            return self::unlockAndReturnGooglePayOrderResp($user_id, self::GOOGLE_PAY_STATUS_SUCCESS);
        } catch (Exception $e) {
            $transaction->rollBack();
            throw $e;
        } finally {
            $redis->unlock($lock);
        }
    }

    // 返回谷歌订单的响应
    private static function unlockAndReturnGooglePayOrderResp(int $user_id, int $status, string $error = '', $lock = '')
    {
        if ($lock) {
            Yii::$app->redis->unlock($lock);
        }
        $balance = Balance::getByPk($user_id)->getTotalBalance();
        if (Equipment::isAppOlderThan(null, '6.2.4')) {
            // 老版本调用时，返回用户钻石余额
            return $balance;
        }
        return [
            'balance' => $balance,
            'status' => $status,
            'error' => $error
        ];
    }

    /**
     * 获取 google client
     *
     * @throws HttpException
     */
    private static function getGoogleClient(): Google_Client
    {
        if (self::$google_client) {
            return self::$google_client;
        }
        try {
            $client = new Google_Client();
            if ($proxy = Yii::$app->params['service']['proxy']['http'] ?? null) {
                $client->setHttpClient(new Client([
                    'proxy' => $proxy,
                ]));
            }
            $client->addScope(Google_Service_AndroidPublisher::ANDROIDPUBLISHER);
            $client->setAuthConfig(GOOGLE_PAY_AUTH_CONFIG);
            self::$google_client = $client;
            return self::$google_client;
        } catch (Exception $e) {
            Yii::error('初始化 Google Pay client 出错：' . $e->getMessage(), __METHOD__);
            throw new HttpException(500, '服务器内部错误');
        }
    }

    /**
     * 获取 Google Pay 订单
     *
     * 字段示例
     * {
     *   "kind": "androidpublisher#productPurchase",  // 内购类型
     *   "orderId": "GPA.3375-6056-1674-14261",  // Google Play 订单 ID
     *   "purchaseTimeMillis": "1580971480039",
     *   "purchaseState": 1,  // 订单状态（0 购买，1 取消 2 待支付）
     *   "purchaseType": 0,  // 扩展字段（0 测试 1 促销）
     *   "consumptionState": 0,  // 内购商品的消费状态（0 待消费，1 已消费）
     *   "developerPayload": "",  // 自定义信息
     *   "acknowledgementState": 0  // 内购商品的确认状态（0 待确认，1 确认）
     * }
     * @param string $purchase_id
     * @param string $purchase_token
     * @return \Google_Service_AndroidPublisher_ProductPurchase|null
     * @link https://developers.google.com/android-publisher/api-ref/purchases/products#resource
     */
    private static function getGooglePayOrder(string $purchase_id, string $purchase_token)
    {
        try {
            $client = self::getGoogleClient();
            $service = new Google_Service_AndroidPublisher($client);
            $res = $service->purchases_products->get(RechargeForm::getAppPackageName(), $purchase_id, $purchase_token);
            return $res;
        } catch (Exception $e) {
            Yii::error('Google Pay 获取订单信息出错：' . $e->getMessage(), __METHOD__);
            return null;
        }
    }

    /**
     * 消耗 Google Pay 用户购买的商品
     *
     * @param string $purchase_id 购买商品 ID
     * @param string $purchase_token 支付后的 token
     * @throws HttpException
     */
    public static function consumeGooglePurchase(string $purchase_id, string $purchase_token)
    {
        try {
            $client = self::getGoogleClient();
            $service = new Google_Service_AndroidPublisher($client);
            $res = $service->purchases_products->consume(
                RechargeForm::getAppPackageName(), $purchase_id, $purchase_token
            );
            return $res;
        } catch (Exception $e) {
            Yii::error('Google Pay 消耗商品出错：' . $e->getMessage(), __METHOD__);
            throw new HttpException(400, Yii::t('app/error', 'consume order failed'));
        }
    }

    /**
     * 获取今天及本月的充值总额
     *
     * @param int $user_id
     * @param int $type
     * @return array 例 ['day_topup' => 0, 'month_topup' => 0]
     */
    public static function getTodayAndCurrentMonthTopupSum(int $user_id, int $type = self::TYPE_PAYPAL)
    {
        $now = $_SERVER['REQUEST_TIME'];
        $month_start = strtotime(date('Y-m-01', $now));
        $today_start = strtotime(date('Y-m-d'));

        $detail = self::find()->select('SUM(price) AS month_topup')
            ->addSelect(new Expression('SUM(IF(confirm_time >= :today_start_time, price, 0)) AS day_topup',
                [':today_start_time' => $today_start]))
            ->where(['uid' => $user_id, 'status' => self::STATUS_SUCCESS, 'type' => $type])
            ->andWhere('confirm_time >= :start_time AND confirm_time < :end_time')
            ->addParams([':start_time' => $month_start, ':end_time' => $now])
            ->asArray()->one();
        if (!$detail) {
            return ['day_topup' => 0, 'month_topup' => 0];
        }
        return [
            'day_topup' => (float)$detail['day_topup'],
            'month_topup' => (float)$detail['month_topup'],
        ];
    }

    public static function getOutOrder(array $condition)
    {
        if ($order = self::findOne($condition)) {
            $order->id = $order->getOrderId();
        }
        return $order;
    }

    public function getBalanceField()
    {
        switch ($this->type) {
            case self::TYPE_APPLEPAY:
            case self::TYPE_CASH:
            case self::TYPE_IOS_SUPPLEMENT_ORDER:
                if (self::isIOSGooglePayNewTopupMenuTime()) {
                    return PayAccount::COIN_FIELD_NEW_IOS;
                }
                return PayAccount::COIN_FIELD_IOS;
            case self::TYPE_ALIPAY:
            case self::TYPE_WECHATPAY:
            case self::TYPE_QQPAY:
            case self::TYPE_TMALL_ANDROID:
            case self::TYPE_DOUDIAN:
            case self::TYPE_JINGDONG:
            case self::TYPE_BILI_LARGE_PAY:
            case self::TYPE_CORP_TO_CORP:
                return PayAccount::COIN_FIELD_ANDROID;
            case self::TYPE_TMALL_IOS:
                return PayAccount::COIN_FIELD_TMALL_IOS;
            case self::TYPE_PAYPAL:
                return PayAccount::COIN_FIELD_PAYPAL;
            case self::TYPE_GOOGLE_PAY:
                if (self::isIOSGooglePayNewTopupMenuTime()) {
                    return PayAccount::COIN_FIELD_NEW_GOOGLEPAY;
                }
                return PayAccount::COIN_FIELD_GOOGLE_PAY;
            case self::TYPE_VIP:
                return PayAccount::COIN_FIELD_VIP;
        }
        return null;
    }

    /**
     * iOS 退款处理：对 iOS 钻石余额进行扣减
     *
     * @param callable|null $func
     * @throws Exception
     */
    public function refundIOS(callable $func = null)
    {
        $db_transaction = self::getDb()->beginTransaction();
        try {
            $pay_accounts = PayAccounts::getAccounts($this->uid, PayAccount::SCOPE_COMMON);
            [, $log_info] = $pay_accounts->refund($this, PayAccount::COIN_IOS_REFUND_ORDER);

            if (is_callable($func)) {
                $func();
            }
            MMessageAssign::sendSysMsg(
                $this->uid,
                'iOS 钻石充值退款通知',
                "尊敬的用户，由于您的 iOS 钻石充值订单 {$this->getOrderId()} 已收到苹果商店的退款，"
                . "平台需回收该笔订单对应的钻石数额：{$this->num} 钻。请前往钱包页确认余额。如有问题，请联系客服。"
            );
            MAdminLogger::addOne([
                'intro' => $log_info,
                'catalog' => MAdminLogger::CATALOG_USER_LAUNCH_IOS_REFUND_SUCCESS,
                'channel_id' => $this->id,
                'user_id' => $this->uid,
            ]);
            $db_transaction->commit();
        } catch (Exception $e) {
            $db_transaction->rollBack();
            throw $e;
        }
    }

    public function generateDetail(?int $real_price_in_fen = null, int $promotion_price_in_fen = 0)
    {
        if (is_null($real_price_in_fen)) {
            $real_price_in_fen = Balance::profitUnitConversion($this->price, Balance::CONVERT_YUAN_TO_FEN);
        }
        $detail = $this->getDetail();
        $recharge_order_detail = new RechargeOrderDetail([
            'id' => $this->id,
            'ip' => $detail['ip'] ?? '',
            'user_agent' => $detail['user_agent'] ?? '',
            'os' => Equipment::Web,
            'real_price' => $real_price_in_fen,
        ]);

        $detail_more = $detail['more'] ?? [];
        $equip = Yii::$app->equip;
        if ($equip->isFromApp()) {
            $recharge_order_detail->equip_id = $equip->getEquipId();
            $recharge_order_detail->buvid = (string)$equip->getBuvid();
            $recharge_order_detail->os = $equip->getOs();
            $detail_more = $detail_more + ['channel' => $equip->getChannel()];
        }

        if ($promotion_price_in_fen !== 0) {
            $detail_more = $detail_more + ['promotion_price' => $promotion_price_in_fen];
        }
        if ($detail_more) {
            $recharge_order_detail->more = $detail_more;
        }

        return $recharge_order_detail;
    }

    public static function queryDetail(int $order_id): ?RechargeOrderDetail
    {
        return RechargeOrderDetail::findOne(['id' => $order_id]);
    }

    public function priceInFen(): int
    {
        return (int)bcmul($this->price, self::RMB_YUAN_FEN_RATE);
    }

    /**
     * 设置充值订单名称和支付状态
     */
    public function setOrderTitleAndStatusMsg()
    {
        $status_msg = $this->status === self::STATUS_SUCCESS ? '成功' : '失败';
        $title = $this->more['title'] ?? '';
        $status_title = $this->more['status_title'] ?? '';
        if ($title && $status_title) {
            $this->title = $title;
            $this->status_msg = $status_title . $status_msg;
        } elseif ($this->type === self::TYPE_VIP) {
            $this->title = '会员免费领取钻石';
            $this->status_msg = '领取' . $status_msg;
        } elseif ($this->type === self::TYPE_CASH && ($this->more['event_id'] ?? 0) > 0) {
            $this->title = '活动奖励';
            $this->status_msg = '发放' . $status_msg;
        } else {
            $this->title = '购买钻石';
            $this->status_msg = '购买' . $status_msg;
        }
    }

    /**
     * 生成特殊（非第三方平台充值）的订单号信息
     *
     * @return Expression
     */
    public static function generateTidSqlExpression(): Expression
    {
        if (YII_ENV_PROD) {
            return new Expression("CONCAT(CONCAT(create_time, LPAD(type, 3, '0')), LPAD(id, 10, '0'))");
        } else {
            return new Expression("CONCAT(CONCAT('dev', create_time, LPAD(type, 3, '0')), LPAD(id, 10, '0'))");
        }
    }

    /**
     * 是否充值过
     *
     * @param int $user_id
     * @return bool
     */
    public static function hasTopuped(int $user_id)
    {
        return self::find()
            ->where(['uid' => $user_id])
            ->andWhere('status <> :status', [':status' => self::STATUS_CREATE])
            ->andWhere(['NOT', ['type' => [self::TYPE_CASH, self::TYPE_VIP]]])
            ->exists();
    }

    public static function isIOSGooglePayNewTopupMenuTime()
    {
        return $_SERVER['REQUEST_TIME'] >= Yii::$app->params['ios_googlepay_new_topup_menu_time'];
    }
}
