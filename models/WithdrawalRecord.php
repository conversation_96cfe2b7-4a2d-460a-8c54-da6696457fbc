<?php

namespace app\models;

use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "withdrawal_record".
 *
 * @property integer $id
 * @property integer $user_id
 * @property integer $account_id
 * @property double $profit
 * @property integer $create_time
 * @property integer $modified_time
 * @property integer $status
 * @property integer $type
 */
class WithdrawalRecord extends ActiveRecord
{
    const STATUS_CREATE = 1;
    const STATUS_CONFIRM = 2;
    const STATUS_INVALID = 3;

    // 提现的收益类型 1：其他；2：旧直播提现；3：剧集购买；4：剧集打赏；5：新直播提现
    const TYPE_WITHDRAW_OTHER = 1;
    const TYPE_WITHDRAW_LIVE = 2;
    const TYPE_WITHDRAW_DRAMA_BUY = 3;
    const TYPE_WITHDRAW_DRAMA_REWARD = 4;
    const TYPE_WITHDRAW_LIVE_NEW = 5;

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->paydb;
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'withdrawal_record';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'account_id', 'profit', 'create_time'], 'required'],
            [['user_id', 'account_id', 'create_time', 'status', 'modified_time'], 'integer'],
            [['profit'], 'number'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => '用户 ID',
            'account_id' => '账号 ID',
            'profit' => '兑换金额',
            'create_time' => '创建时间戳',  // 单位：秒
            'modified_time' => '更新时间戳',  // 单位：秒
            'status' => '申请状态', // 1：申请中，2：确认打款，3：拒绝打款
            // 1：其他收益 ( 微信男友 + 全职语音包 )；2：旧直播间收益；3：剧集购买收益；4：剧集打赏收益；5：新直播提现收益
            // （旧直播间收益：2020.06.01 00:00:00 之前，新直播间收益 2020.06.01 00:00:00 及其之后）
            'type' => '提现的收益类型',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }
}
