<?php

namespace app\models;

use app\components\util\Equipment;
use Exception;
use missevan\util\MUtils;
use Yii;

class AdTrackThirdparty extends AdTrack implements AdTrackInterface
{
    // 到端任务平台
    const TO_THIRD_PARTY_BAIDU_TASK = 'baidu_task';  // 百度
    const TO_THIRD_PARTY_CTRIP_TASK = 'ctrip_task';  // 携程
    const TO_THIRD_PARTY_BAIDUMAP_TASK = 'baidumap_task';  // 百度地图
    const TO_THIRD_PARTY_YOUKU_TASK = 'youku_task';  // 优酷视频
    const TO_THIRD_PARTY_QQ_BROWSER_TASK = 'qqbrowser_task';  // QQ 浏览器
    const TO_THIRD_PARTY_WEIBO_TASK = 'weibo_task';  // 微博
    const TO_THIRD_PARTY_LOFTER_TASK = 'lofter_task';  // lofter
    const TO_THIRD_PARTY_QQ_MUSIC_TASK = 'qqmusic_task';  // QQ 音乐
    const TO_THIRD_PARTY_QUARK_TASK = 'quark_task';  // 夸克
    const TO_THIRD_PARTY_QIDIAN_TASK = 'qidian_task';  // 起点

    const TO_THIRD_PARTY_LIST = [
        self::TO_THIRD_PARTY_BAIDU_TASK,
        self::TO_THIRD_PARTY_CTRIP_TASK,
        self::TO_THIRD_PARTY_BAIDUMAP_TASK,
        self::TO_THIRD_PARTY_YOUKU_TASK,
        self::TO_THIRD_PARTY_QQ_BROWSER_TASK,
        self::TO_THIRD_PARTY_WEIBO_TASK,
        self::TO_THIRD_PARTY_LOFTER_TASK,
        self::TO_THIRD_PARTY_QQ_MUSIC_TASK,
        self::TO_THIRD_PARTY_QUARK_TASK,
        self::TO_THIRD_PARTY_QIDIAN_TASK,
    ];

    public function callback(string $event_type, $arg = 0)
    {
        if ($event_type !== self::CALLBACK_EVENT_APP_CALLUP) {
            return true;
        }
        if (!($arg instanceof ToThirdPartyParams)) {
            return true;
        }
        $ad_source_from = $arg->ad_source_from;
        $track_id = $arg->track_id;
        if (!$track_id) {
            return false;
        }
        switch ($ad_source_from) {
            case self::TO_THIRD_PARTY_BAIDU_TASK:
                return MThirdPartyBaiduTask::callback($track_id);
            case self::TO_THIRD_PARTY_CTRIP_TASK:
                return MThirdPartyCtripTask::callback($track_id);
            case self::TO_THIRD_PARTY_BAIDUMAP_TASK:
                return MThirdPartyBaiduMapTask::callback($track_id);
            case self::TO_THIRD_PARTY_YOUKU_TASK:
                return MThirdPartyYoukuTask::callback($track_id);
            case self::TO_THIRD_PARTY_QQ_BROWSER_TASK:
                return MThirdPartyQQBrowserTask::callback($track_id);
            case self::TO_THIRD_PARTY_WEIBO_TASK:
                return MThirdPartyWeiboTask::callback($track_id);
            case self::TO_THIRD_PARTY_LOFTER_TASK:
                return MThirdPartyLofterTask::callback($track_id);
            case self::TO_THIRD_PARTY_QQ_MUSIC_TASK:
                return MThirdPartyQQMusicTask::callback($track_id);
            case self::TO_THIRD_PARTY_QUARK_TASK:
                return MThirdPartyQuarkTask::callback($track_id);
            case self::TO_THIRD_PARTY_QIDIAN_TASK:
                return MThirdPartyQidianTask::callback($track_id);
            default:
                return false;
        }
    }

    public function getAdVendor(): int
    {
        return self::VENDOR_THIRDPARTY;
    }

    public static function initiateRecord(InstallLog $install_log, string $ad_source_from, string $track_id)
    {
        try {
            $initiate_columns = [
                'more' => [
                    'channel' => $ad_source_from,
                    'ad_event_type' => AdTrack::AD_ATTRIBUTION_TYPE_APP_CALLUP,
                ],
            ];
            if ($install_log->device_type === Equipment::Android) {
                $initiate_columns['mac_md5'] = $install_log->mac_md5;
                $initiate_columns['imei_md5'] = $install_log->imei_md5;
                $initiate_columns['android_id_md5'] = $install_log->android_id_md5;
                $initiate_columns['oaid'] = $install_log->oaid;
                $initiate_columns['oaid_md5'] = $install_log->oaid_md5;
            } elseif ($install_log->device_type === Equipment::iOS) {
                $initiate_columns['idfa'] = $install_log->adid;
                $initiate_columns['more']['idfa_md5'] = md5(strtoupper($install_log->adid));
            }
            if ($track_id) {
                $initiate_columns['track_id'] = $track_id;
            }
            if (isset($install_log->more['drm_id'])) {
                $initiate_columns['more']['drm_id'] = $install_log->more['drm_id'];
            }
            $record = new AdTrackThirdparty();
            $record->attributes = array_merge([
                'vendor' => self::VENDOR_THIRDPARTY,
                'converted' => self::NOT_CONVERTED,
                'os' => $install_log->device_type,
                'ip' => $install_log->ip,
                'ua' => $install_log->user_agent,
                'buvid' => $install_log->buvid,
                'equip_id' => $install_log->equip_id,
            ], $initiate_columns);
            if (!$record->save()) {
                throw new Exception('第三方到端任务归因失败，原因：' . MUtils::getFirstError($record));
            }
            $record->currentTableName = self::tableName();
        } catch (Exception $e) {
            Yii::error($e->getMessage(), __METHOD__);
            return null;
        }
        return $record;
    }
}
