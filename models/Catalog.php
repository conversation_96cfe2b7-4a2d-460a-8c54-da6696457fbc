<?php

namespace app\models;

use app\components\tree\Catalogs;
use app\components\util\Equipment;
use app\components\util\MUtils;
use Yii;
use yii\helpers\Json;
use yii\web\HttpException;

/**
 * This is the model class for table "catalog".
 *
 * @property integer $id
 * @property integer $parent_id
 * @property string $catalog_name
 * @property string $catalog_name_second
 * @property string $catalog_name_alias
 * @property string $content
 * @property string $seo_title
 * @property string $seo_keywords
 * @property string $seo_description
 * @property string $attach_file
 * @property string $attach_thumb
 * @property integer $sort_order
 * @property integer $data_count
 * @property integer $page_size
 * @property string $status_is
 * @property string $menu_is
 * @property string $redirect_url
 * @property string $display_type
 * @property string $template_list
 * @property string $template_page
 * @property string $template_show
 * @property integer $create_time
 * @property integer $last_update_time
 */
class Catalog extends ActiveRecord
{
    // 分类首页推荐音数量
    const HOMEPAGE_RECOMMEND_SOUND_COUNT = 6;
    // 分类首页版头图数量
    const HOMEPAGE_BANNER_COUNT = 5;
    // 分类首页图标
    const ICON_DARK_RECOMMEND = '201703/09/36c0806384ba9fd844986145948af8d0103332.png';
    const ICON_DARK_TIME = '201703/09/e5053da461c552bb0671f829ca1e2b3c103332.png';
    const ICON_DARK_HOT = '201703/09/f983075a4e06d8384f691be9163e7036103332.png';
    const ICON_RECOMMEND = '201703/09/3df7fd527e9ead3cef3aafd50d512ab9103332.png';
    const ICON_TIME = '201703/09/b6def98316d58504cb4c07bd21b540bb103332.png';
    const ICON_HOT = '201703/09/49f7384d864b33e91e273a198cf48dfb103330.png';

    const ICON_NEWS = 'oss://coversmini/201911/27/dd2cf6c10f9934cdd718d8d58398bda4163447.png';
    const ICON_NEWS_DARK = 'oss://coversmini/201911/27/93efa869d25ba802fbf6ef676966f734163454.png';

    // 音频排序：最新、猫耳、热度、点赞数、评论数、播放量、综合（最近时间内的新增播放量）
    const ORDER_NEWEST = 0;
    const ORDER_POINTS = 1;
    const ORDER_HOT = 2;
    const ORDER_LIKES = 3;
    const ORDER_COMMENTS = 4;
    const ORDER_VIEWS = 5;
    const ORDER_RECENTLY_VIEWS = 6;

    // 音频分类
    const CATALOG_SOUND_ID = 1;
    // 分类为情感的 ID
    const CATALOG_ID_EMOTION = 4;
    // 分类为异闻的 ID
    const CATALOG_ID_STRANGE_NEWS = 11;
    // 分类为鬼畜/搞笑的 ID
    const CATALOG_ID_FUNNY = 36;
    // 催眠分类
    const CATALOG_ID_ASMR = 54;
    // 催眠区的子分类（ASMR 中文、国外、无人声、女性向）
    const CATALOG_ID_ASMR_CN = 71;
    const CATALOG_ID_ASMR_OVERSEAS = 72;
    const CATALOG_ID_ASMR_NOVOICE = 73;
    const CATALOG_ID_ASMR_GIRL = 77;

    // 分类为剧情歌的 ID
    const CATALOG_ID_STORY_SONG = 76;
    // 直播回放分类
    const CATALOG_ID_LIVE = 80;
    // 分类为 FT/花絮 的 ID
    const CATALOG_ID_FT = 100;
    // 分类为知识的 ID
    const CATALOG_ID_KNOWLEDGE = 123;

    const LIMITED_CATALOG_IDS = [
        self::CATALOG_ID_ASMR_CN,
        self::CATALOG_ID_ASMR_NOVOICE,
        self::CATALOG_ID_ASMR_GIRL,
    ];

    // 中文广播剧言情分类
    const CATALOG_ID_CHINESE_DRAMA_ROMANTIC = 18;
    // 中文广播剧耽美分类
    const CATALOG_ID_CHINESE_DRAMA_TANBI = 19;
    // 日抓及其子分类：纯爱（日文）、乙女、一般
    const CATALOG_ID_JAPAN_DRAMA = 41;
    const CATALOG_ID_JAPAN_DRAMA_TANBI = 43;
    const CATALOG_ID_JAPAN_DRAMA_OTOME = 44;
    const CATALOG_ID_JAPAN_DRAMA_COMMON = 45;
    // 有声漫画
    const CATALOG_ID_AUDIO_COMICS = 46;
    // 有声日漫
    const CATALOG_ID_JAPAN_AUDIO_COMICS = 33;
    // 有声中漫
    const CATALOG_ID_CN_AUDIO_COMICS = 47;

    // 声音自然音分类
    const CATALOG_ID_SOUND_NATURAL = 101;
    // 配音
    const CATALOG_ID_DUB = 102;
    // 分类为广播剧的 ID
    const CATALOG_ID_DRAMA = 5;
    // 听书分类
    const CATALOG_ID_AUDIO_BOOK = 6;
    // 音乐分类
    const CATALOG_ID_MUSIC = 8;
    // 娱乐分类
    const CATALOG_ID_ILLUSTRATION = 26;
    // 铃声分类
    const CATALOG_ID_DRAMA_RING = 65;
    // 铃声 - 来电
    const CATALOG_ID_RINGTONE_CALL = 66;
    // 铃声 - 短信
    const CATALOG_ID_RINGTONE_SMS = 67;
    // 铃声 - 闹钟
    const CATALOG_ID_RINGTONE_CLOCK = 68;
    // 声音恋人分类
    const CATALOG_ID_SOUND_LOVER = 108;
    // 声音恋人女性向分类
    const CATALOG_ID_SOUND_LOVER_GIRL = 109;
    // 声音恋人男性向分类
    const CATALOG_ID_SOUND_LOVER_BOY = 110;
    // 有声漫画纯爱分类
    const CATALOG_ID_CHINESE_COMICS_TANBI = 111;
    // 有声漫画言情分类
    const CATALOG_ID_CHINESE_COMICS_ROMANTIC = 112;

    // 催眠专享分类 ID
    const CATALOG_RADIO_ASMR = 117;
    // 催眠专享入睡分类 ID
    const CATALOG_RADIO_SLEEP = 118;
    // 催眠专享音乐分类 ID
    const CATALOG_RADIO_MUSIC = 119;
    // 催眠专享电台男 CV 分类 ID
    const CATALOG_RADIO_ASMR_CV_MAN = 120;
    // 催眠专享电台女 CV 分类 ID
    const CATALOG_RADIO_ASMR_CV_WOMAN = 121;

    // 旧的催眠专享分类
    // WORKAROUND: 客户端新版催眠专享上线后可删除
    const OLD_RADIO_CATALOG_IDS = [
        self::CATALOG_RADIO_SLEEP,
        self::CATALOG_RADIO_MUSIC,
        self::CATALOG_RADIO_ASMR_CV_MAN,
        self::CATALOG_RADIO_ASMR_CV_WOMAN,
    ];

    const CATALOG_ID_RADIO_HYPNOSIS = 156; // 催眠专享 - 助眠
    const CATALOG_ID_RADIO_STUDY_ROOM = 157; // 催眠专享 - 自习室
    const CATALOG_ID_RADIO_RELAX = 158; // 催眠专享 - 解压
    const CATALOG_ID_RADIO_LIGHT_MUSIC = 159; // 催眠专享 - 轻音乐
    const CATALOG_ID_RADIO_COAX_SLEEP = 160; // 催眠专享 - 哄睡
    const CATALOG_ID_RADIO_COAX_SLEEP_BOY = 161; // 催眠专享 - 哄睡 - 小哥哥
    const CATALOG_ID_RADIO_COAX_SLEEP_GIRL = 162; // 催眠专享 - 哄睡 - 小姐姐

    // PGC 分区，包含广播剧、有声漫画、听书、日抓分类（音频分类）
    const PGC_CATALOG_IDS = [
        self::CATALOG_ID_DRAMA,
        self::CATALOG_ID_AUDIO_BOOK,
        self::CATALOG_ID_AUDIO_COMICS,
        self::CATALOG_ID_JAPAN_DRAMA,
    ];

    // UGC 分区，包含催眠，声音恋人，音乐，娱乐，铃声
    const UGC_CATALOG_IDS = [
        self::CATALOG_ID_ASMR,
        self::CATALOG_ID_SOUND_LOVER,
        self::CATALOG_ID_MUSIC,
        self::CATALOG_ID_ILLUSTRATION,
        self::CATALOG_ID_DRAMA_RING,
    ];

    // 状态：Y 显示，N 隐藏
    const STATUS_OPEN = 'Y';
    const STATUS_OFFLINE = 'N';

    // 乙女音频分类
    const CATALOGS_OTOME = [
        self::CATALOG_ID_CHINESE_DRAMA_ROMANTIC,
        self::CATALOG_ID_JAPAN_DRAMA_OTOME,
        self::CATALOG_ID_CHINESE_COMICS_ROMANTIC,
    ];

    // 纯爱音频分类
    const CATALOGS_TANBI = [
        self::CATALOG_ID_CHINESE_DRAMA_TANBI,
        self::CATALOG_ID_JAPAN_DRAMA_TANBI,
        self::CATALOG_ID_CHINESE_COMICS_TANBI,
    ];

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'catalog';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['parent_id', 'sort_order', 'data_count', 'page_size', 'create_time', 'last_update_time'], 'integer'],
            [['catalog_name'], 'required'],
            [['content', 'seo_description', 'status_is', 'menu_is', 'display_type'], 'string'],
            [['catalog_name', 'catalog_name_second', 'catalog_name_alias', 'seo_title', 'attach_file', 'attach_thumb', 'template_list', 'template_page', 'template_show'], 'string', 'max' => 100],
            [['seo_keywords', 'redirect_url'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'parent_id' => 'Parent ID',
            'catalog_name' => 'Catalog Name',
            'catalog_name_second' => 'Catalog Name Second',
            'catalog_name_alias' => 'Catalog Name Alias',
            'content' => 'Content',
            'seo_title' => 'Seo Title',
            'seo_keywords' => 'Seo Keywords',
            'seo_description' => 'Seo Description',
            'attach_file' => 'Attach File',
            'attach_thumb' => 'Attach Thumb',
            'sort_order' => 'Sort Order',
            'data_count' => 'Data Count',
            'page_size' => 'Page Size',
            'status_is' => 'Status Is',
            'menu_is' => 'Menu Is',
            'redirect_url' => 'Redirect Url',
            'display_type' => 'Display Type',
            'template_list' => 'Template List',
            'template_page' => 'Template Page',
            'template_show' => 'Template Show',
            'create_time' => 'Create Time',
            'last_update_time' => 'Last Update Time',
        ];
    }

    /**
     * 取分类
     */
    static public function get($parentid = 0, $array = array(), $level = 0, $add = 2, $repeat = '&nbsp;&nbsp;&nbsp;&nbsp;')
    {

        $str_repeat = '';
        if ($level) {
            for ($j = 0; $j < $level; $j++) {
                $str_repeat .= $repeat;
            }
        }
        $newarray = array();
        $temparray = array();
        foreach (( array )$array as $v) {
            if ($v ['parent_id'] == $parentid) {
                $newarray [] = array('id' => $v ['id'], 'catalog_name' => $v ['catalog_name'], 'catalog_name_alias' => $v ['catalog_name_alias'], 'parent_id' => $v ['parent_id'], 'level' => $level, 'sort_order' => $v ['sort_order'], 'seo_keywords' => $v ['seo_keywords'], 'seo_description' => $v ['seo_description'], 'attach_file' => $v ['attach_file'], 'attach_thumb' => $v ['attach_thumb'], 'status_is' => $v ['status_is'], 'data_count' => $v ['data_count'], 'display_type' => $v ['display_type'], 'menu_is' => $v ['menu_is'], 'template_list' => $v ['template_list'], 'template_page' => $v ['template_page'], 'template_show' => $v ['template_show'], 'create_time' => $v ['create_time'], 'str_repeat' => $str_repeat, 'page_size' => $v['page_size']);

                $temparray = self::get($v ['id'], $array, ($level + $add));
                if ($temparray) {
                    $newarray = array_merge($newarray, $temparray);
                }
            }
        }
        return $newarray;
    }

    /**
     * 获取下级子类，普通模式
     *
     * @param integer $parentId 父节点
     * @param array $array
     * @param array $params
     * @return array 返回子分类相关信息
     */
    public static function lite($parentId, array $array = [], $params = [])
    {
        if (empty($parentId)) {
            return [];
        }
        $catalogs = new Catalogs($parentId);
        $eachArr = $catalogs->getSons($parentId) ?: [];
//        $eachArr = empty($array) ? Mcache::system('_catalog', 86400) : $array;
//        $arr = array();
//        foreach ((array)$eachArr as $row) {
//            if ($row['parent_id'] == $parentId)
//                $arr[] = $row;
//        }
        return $eachArr;
    }

    public static function getAllCatalog($catalog_id)
    {
        $catalog = self::findOne($catalog_id);
        if (!$catalog) throw new HttpException(404, '分类不存在', 200110201);
        $cname = $catalog->catalog_name_alias;

        // 获取 banner、推荐音及推荐标签
        $memcache = Yii::$app->memcache;

        $catalog_data_key = MUtils::generateCacheKey(CATALOG_HOMEPAGE, $cname);
        if (($catalog_data = $memcache->get($catalog_data_key))) {
            $catalog_data = Json::decode($catalog_data);
        } else {
            $redis = Yii::$app->redis;
            // 获取版头图
            $banners = [];
            for ($i = 0; $i < self::HOMEPAGE_BANNER_COUNT; $i++) {
                $banner_key = $redis->generateKey(CATALOG_HOMEPAGE_BANNER, $cname, $i);
                if (($banner = $redis->hGetAll($banner_key))) $banners[] = $banner;
            }
            list($sound_ids, $tag_ids, $element, $sort) = $redis->mget([
                $redis->generateKey(CATALOG_HOMEPAGE_RECOMMENDED_SOUND, $cname),
                $redis->generateKey(CATALOG_HOMEPAGE_TAG, $cname),
                $redis->generateKey(CATALOG_HOMEPAGE_CHANNEL_OR_EVENT, $cname),
                $redis->generateKey(CATALOG_HOMEPAGE_CATALOG_SORT, $cname)
            ]);

            // 获取推荐音
            $sound_ids = array_map('intval', explode(',', $sound_ids));
            $select_column = 'id, soundstr, comment_count, comments_count, sub_comments_count, cover_image, view_count,
                checked';
            $sounds = MSound::find()->select($select_column)->allByColumnValues('id', $sound_ids);
            $count = count($sounds);
            if ($count < self::HOMEPAGE_RECOMMEND_SOUND_COUNT) {
                $query = MSound::find()
                    ->select($select_column)
                    ->where('refined = :refined', [':refined' => MSound::CHECKED_PASS]);
                $sounds += MUtils::getRandomModels($query, self::HOMEPAGE_RECOMMEND_SOUND_COUNT - $count);
            }
            // 过滤无用的字段
            $sounds = array_map(function ($sound) {
                unset($sound->comments_count, $sound->comments_count, $sound->sub_comments_count, $sound->cover_image,
                    $sound->checked, $sound->need_pay, $sound->price);
                return $sound;
            }, $sounds);
            // 推荐标签
            $tag_ids = array_map('intval', explode(',', $tag_ids));
            $tags = MTag::find()->select('id, name')->allByColumnValues('id', $tag_ids);

            // 子分类 ID
            $catalog_ids = [$catalog_id];
            $sub_catalogs = Catalog::lite($catalog->id);
            if (!empty($sub_catalogs) && is_array($sub_catalogs)) {
                $catalog_ids = array_column($sub_catalogs, 'id');
            }

            // 推荐频道或活动
            if ($element) $element = Json::decode($element);
            $catalog_data = [
                'banner' => $banners,
                'recommend' => $sounds,
                'tag' => $tags,
                'sort' => $sort,
                'element' => $element,
                'catalog_ids' => array_map('intval', $catalog_ids)
            ];

            $memcache->set($catalog_data_key, Json::encode($catalog_data), FIVE_MINUTE);
        }

        return $catalog_data;
    }

    public static function getCatalogHomepageDynamic($tag_id, $catalog, $limit, $dark = 0)
    {
        $recommend = $catalog['recommend'] ?: [];

        // $time = MSound::getCatalogSounds($catalog['catalog_ids'], $tag_id, [], $limit);

        $hot = MSound::getSoundsByCat($tag_id, $catalog['catalog_ids'], 90);
        if ($dark) {
            $icon_recommend = Yii::$app->params['mimagesbigUrl'] . self::ICON_DARK_RECOMMEND;
            // $icon_time = Yii::$app->params['mimagesbigUrl'] . self::ICON_DARK_TIME;
            $icon_hot = Yii::$app->params['mimagesbigUrl'] . self::ICON_DARK_HOT;
        } else {
            $icon_recommend = Yii::$app->params['mimagesbigUrl'] . self::ICON_RECOMMEND;
            // $icon_time = Yii::$app->params['mimagesUrl'] . self::ICON_TIME;
            $icon_hot = Yii::$app->params['mimagesbigUrl'] . self::ICON_HOT;
        }

        // 组合数据
        $info = [
            ['name' => '热门推荐', 'item' => $recommend, 'icon' => $icon_recommend],
            // ['name' => '最新投稿', 'item' => $time, 'icon' => $icon_time],
            ['name' => '全区动态', 'item' => $hot, 'icon' => $icon_hot],
        ];
        // unset($hot, $recommend, $time);
        unset($hot, $recommend);
        // 排序
        $items = [];
        if ($catalog['sort'] != '') {
            $sort = explode(',', $catalog['sort']);
            foreach ($sort as $item) {
                if (isset($info[$item - 1]) && $info[$item - 1]['item']) {
                    $items[] = $info[$item - 1];
                }
            }
        } else {
            foreach ($info as $item) {
                if (count($item['item'])) $items [] = $item;
            }
        }

        return $items;
    }

    /**
     * 获取分类的图标
     *
     * @param integer $catalog_id 分类 ID
     * @param integer $dark 白天或黑夜样式（0 为白天，1 为黑夜）
     * @return string
     */
    public static function getIcon($catalog_id, $dark)
    {
        return $dark ? Yii::$app->params['catalogIconUrl'] . "{$catalog_id}-dark.png"
            : Yii::$app->params['catalogIconUrl'] . "{$catalog_id}.png";
    }

    /**
     * 获取分区首页推荐内容
     * 分区音频分区首页数据：版头图，大家都在听，精选音单、人气周榜、热门推荐，轮播通栏数据
     * 相关数据在 Web UGC 分区后台设置并生成缓存
     *
     * @param int $catalog_id 分区 ID
     * @return array
     * @throws HttpException 分类不存在时抛出异常
     */
    public static function getSoundHomepage(int $catalog_id)
    {
        $catalog_name = self::find()
            ->select('catalog_name')
            ->where('id = :id AND status_is = :status AND parent_id = :parent_id',
                [':id' => $catalog_id, ':status' => self::STATUS_OPEN, ':parent_id' => self::CATALOG_SOUND_ID])
            ->scalar();
        if (!$catalog_name) {
            throw new HttpException(404, '音频分类不存在');
        }
        $redis = Yii::$app->redis;
        $catalog_homepage_key = $redis->generateKey(KEY_CATALOG_SOUND_HOMEPAGE, $catalog_id);
        $catalog_homepage = $redis->hGetAll($catalog_homepage_key);
        $return = [
            'catalog_name' => $catalog_name,
            'hot_recommend' => [
                'title' => $catalog_homepage['hot_recommend_title'] ?? '热门推荐',
                'elements' => [],
            ],
            'classic_album' => [
                'title' => '精选音单',
                'module_id' => 0,
                'elements' => [],
            ],
            'recommend_up' => [
                'title' => '推荐 UP 主',
                'elements' => [],
            ],
            'weekly_rank' => [
                'title' => '人气周榜',
                'ranks' => [],
            ],
        ];
        // 获取分区 banner
        $return['banners'] = self::getCatalogBanner(MRecommendedElements::MODULE_TYPE_SOUND_BANNER, $catalog_id);
        // 分区轮播通栏 [1 => [['url' => 'xxx', 'pic' => 'xxx', 'sort' => x]]]
        // TODO: 轮播通栏之后需要改为从数据库直接取
        $return['extra_banners'] = array_key_exists('extra_banners', $catalog_homepage) ?
            Json::decode($catalog_homepage['extra_banners']) : null;
        if ($return['extra_banners']) {
            // 轮播通栏链接兼容处理
            foreach ($return['extra_banners'] as $key => $extra_banner) {
                $return['extra_banners'][$key] = array_map(function ($banner) {
                    if (key_exists('url', $banner)) {
                        $banner['url'] = MUtils::getUsableAppLink($banner['url']);
                    }
                    return $banner;
                }, $return['extra_banners'][$key]);
            }
        }
        // 大家都在看
        $return['hot_words'] = array_key_exists('hot_words', $catalog_homepage) ?
            Json::decode($catalog_homepage['hot_words']) : [];
        // WORKAROUND：对老版本数据做版本兼容处理
        if (Equipment::isAppOlderThan('4.5.2', '5.4.2')) {
            $return['hot_words'] = array_map(function ($value) {
                // 处理 iOS < 4.5.2 或 Android < 5.4.2 版本的旧数据信息
                if (!is_array($value)) {
                    return $value;
                }
                // 处理 iOS < 4.5.2 或 Android < 5.4.2 版本的新数据信息
                return $value['title'] ?? '';
            }, $return['hot_words']);
        } else {
            $return['hot_words'] = array_map(function ($value) {
                // 处理 iOS >= 4.5.2 或 Android >= 5.4.2 版本的旧数据信息
                if (!is_array($value)) {
                    return [
                        'title' => $value,
                        'url' => 'missevan://search?keyword=' . urlencode($value),
                    ];
                }
                // 处理 iOS >= 4.5.2 或 Android >= 5.4.2 版本的新数据信息
                return [
                    'title' => $value['title'],
                    'url' => $value['url'],
                ];
            }, $return['hot_words']);
        }
        // 精选音单
        $classic_album_module_id = array_key_exists('classic_album', $catalog_homepage) ?
            (int)$catalog_homepage['classic_album'] : 0;
        // 获取精选音单模块信息
        if ($classic_album_module_id) {
            $elements = MPersonaModuleElement::getElementsRecommended($classic_album_module_id);
            $album_data = MPersonaModuleElement::getAlbums($elements);
            $return['classic_album']['module_id'] = $classic_album_module_id;
            $return['classic_album']['elements'] = $album_data;
        }

        // 获取推荐 UP 主音频 ID
        $recommend_up_sound_ids = array_key_exists('recommend_up_sound', $catalog_homepage) ?
            array_map('intval', explode(',', $catalog_homepage['recommend_up_sound'])) : [];
        // 需要查询的音频 ID
        $all_sound_ids = [];
        if (!empty($recommend_up_sound_ids)) {
            $all_sound_ids = $recommend_up_sound_ids;
        }

        // 获取子分类下人气周榜前三榜单音频 ID
        $RANK_PAGE_SIZE = 3;
        $weekly_rank_key = $redis->generateKey(KEY_CATALOG_HOMEPAGE_WEEKLY_RANK_SOUND, $catalog_id);
        $weekly_ranks = $redis->hGetAll($weekly_rank_key);
        $rank_sounds = [];
        // hidden（0：人气周榜不隐藏；1：人气周榜隐藏）
        if (!isset($weekly_ranks['hidden']) || (int)$weekly_ranks['hidden'] === 0) {
            $tabs = Catalog::getCatalogTabs($catalog_id);
            foreach ($tabs as $tab) {
                $sound_ids_str = $weekly_ranks[$tab['id']] ?? null;
                if (!$sound_ids_str) {
                    // 榜单下无数据不显示该榜单
                    continue;
                }
                $sound_ids = array_map('intval', explode(',', $sound_ids_str));
                $sound_ids = array_slice($sound_ids, 0, $RANK_PAGE_SIZE);
                $rank_sounds[] = [
                    'name' => $tab['name'],
                    'catalog_id' => $tab['id'],
                    'sound_ids' => $sound_ids,
                ];
                $all_sound_ids = array_merge($all_sound_ids, $sound_ids);
            }
        }

        // 获取热门推荐音频 ID
        $hot_recommend_sound_ids = array_key_exists('hot_recommend_sound', $catalog_homepage) ?
            array_map('intval', explode(',', $catalog_homepage['hot_recommend_sound'])) : [];
        if (!empty($hot_recommend_sound_ids)) {
            $all_sound_ids = array_merge($all_sound_ids, $hot_recommend_sound_ids);
        }

        if (empty($all_sound_ids)) {
            return $return;
        }
        // 查询音频信息
        // 仅推荐 UP 主模块会返回音频 ID，而它们不为付费音，所以此处不对音频字段做处理
        $sounds = MSound::find()
            ->select('id, user_id, soundurl_64, soundurl_128,
                soundstr, cover_image, view_count, duration, comment_count, comments_count, sub_comments_count')
            ->where(['id' => $all_sound_ids, 'checked' => MSound::CHECKED_PASS])
            ->andWhere('NOT refined & :not_refined', [':not_refined' => MSound::REFINED_BLOCK])
            ->all();
        if (empty($sounds)) {
            Yii::error("音频分区（{$catalog_id}）内容设置错误", __METHOD__);
            return $return;
        }
        $sounds = array_column($sounds, null, 'id');
        [$recommend_ups, $rank, $hot_recommend] = self::getHomepageSound($sounds, $recommend_up_sound_ids,
            $rank_sounds, $hot_recommend_sound_ids);
        $return['recommend_up']['elements'] = $recommend_ups;
        $return['weekly_rank']['ranks'] = $rank;
        $return['hot_recommend']['elements'] = $hot_recommend;

        return $return;
    }

    /**
     * 获取分区 banner
     *
     * @param int $module_type 分区类型（PGC 或 UGC）
     * @param int $catalog_id 分类 ID
     * @return array
     */
    public static function getCatalogBanner(int $module_type, int $catalog_id): array
    {
        $banners = [];
        $banner_key = MUtils::generateCacheKey(KEY_APP_CATALOG_BANNER, $catalog_id);
        $memcache = Yii::$app->memcache;
        if (!($data = $memcache->get($banner_key))) {
            $client = Equipment::All;
            $banners = MRecommendedElements::getOnlineOrMaxEndTimeElements($client, $module_type, true,
                $catalog_id);
            if (!empty($banners)) {
                // 获取版头图缓存时长
                $duration = MRecommendedElements::getDuration($client, $module_type, $catalog_id, $banners);
            } else {
                Yii::error("App 分区（{$catalog_id}）Banner 全部下线", __METHOD__);
                // Banner 都自动下线时，取最后自动下线的一张图展示，若最后自动下线有多张，取排序靠前的一张
                $banners = MRecommendedElements::getOnlineOrMaxEndTimeElements($client, $module_type, false,
                    $catalog_id);
                $duration = HALF_MINUTE;
            }
            $data = Json::encode($banners);
            // 设置缓存和过期时间，默认过期时间加 1s，错开时间上线
            $memcache->set($banner_key, $data, $duration + 1);
        } else {
            $banners = Json::decode($data);
        }
        return array_map(function ($banner) {
            return [
                'url' => MUtils::getUsableAppLink($banner['url']),
                'pic_app' => $banner['pic'],
                'sort' => $banner['sort'],
            ];
        }, $banners);
    }

    /**
     * 获取音频分类周榜 tab
     *
     * @param int $catalog_id
     * @return array
     */
    public static function getCatalogTabs(int $catalog_id): array
    {
        $key = MUtils::generateCacheKey(KEY_SOUND_CATALOG, $catalog_id);
        if (!$tabs = Yii::$app->memcache->get($key)) {
            // 当前所有音频分区排行榜都为其子分类的榜单
            $catalogs = Catalog::lite($catalog_id);
            $tabs = array_map(function ($catalog) {
                return [
                    'id' => $catalog['id'],
                    'name' => $catalog['name'],
                ];
            }, $catalogs);
            $tabs = Json::encode($tabs);
            // 此处数据基本无变化，缓存有效期半小时
            // WORKAROUND: 特殊时期，随时调整分类，将缓存有效期调整为 5 分钟
            Yii::$app->memcache->set($key, $tabs, FIVE_MINUTE);
        }
        return Json::decode($tabs);
    }

    /**
     * 获取音频分区首页音频信息
     *
     * @param array $sounds
     * @param array $recommend_up_sound_ids
     * @param array $rank_sounds
     * @param array $hot_recommend_sound_ids
     * @return array
     */
    private static function getHomepageSound(array $sounds, array $recommend_up_sound_ids, array $rank_sounds,
            array $hot_recommend_sound_ids): array
    {
        $recommend_ups = $rank = $hot_recommend = [];
        if (!empty($recommend_up_sound_ids)) {
            // 完善推荐 UP 主信息
            $recommend_up_sounds = [];
            foreach ($recommend_up_sound_ids as $up_sound_id) {
                if (key_exists($up_sound_id, $sounds)) {
                    $recommend_up_sounds[] = $sounds[$up_sound_id];
                }
            }
            $up_user_ids = array_column($recommend_up_sounds, 'user_id');
            $ups = Mowangskuser::find()
                ->select('id, username, avatar')
                ->where(['id' => $up_user_ids])
                ->all();
            $ups = array_column($ups, null, 'id');
            $recommend_ups = array_values(array_filter(array_map(function ($sound) use ($ups) {
                $up = $ups[$sound->user_id] ?? null;
                if (!$up) {
                    Yii::error("分区设置的推荐 UP 不存在，推荐 UP 音频 ID：$sound->id", __METHOD__);
                    return null;
                }
                MSound::getSoundSignUrls($sound, true);
                return [
                    'user_id' => $up->id,
                    'avatar' => $up->avatar2,
                    'username' => $up->username,
                    'soundurl' => $sound->soundurl,
                    'soundurl_64' => $sound->soundurl_64,
                    'soundurl_128' => $sound->soundurl_128,
                ];
            }, $recommend_up_sounds)));
        }

        if (!empty($rank_sounds)) {
            // 完善周榜榜单信息
            foreach ($rank_sounds as $key => $rank) {
                $rank_sounds[$key]['elements'] = array_reduce($rank['sound_ids'],
                    function ($elements, $sound_id) use ($sounds) {
                        if (key_exists($sound_id, $sounds)) {
                            $sound = $sounds[$sound_id];
                            $elements[] = [
                                'id' => $sound->id,
                                'title' => $sound->soundstr,
                                'view_count' => $sound->view_count,
                                'duration' => $sound->duration,
                                'front_cover' => $sound->front_cover,
                            ];
                        }
                        return $elements;
                    }, []);
                unset($rank_sounds[$key]['sound_ids']);
            }
            $rank = $rank_sounds;
        }

        if (!empty($hot_recommend_sound_ids)) {
            // 完善热门推荐信息
            $hot_recommend = array_reduce($hot_recommend_sound_ids, function ($elements, $sound_id) use ($sounds) {
                if (key_exists($sound_id, $sounds)) {
                    $sound = $sounds[$sound_id];
                    $elements[] = [
                        'id' => $sound->id,
                        'title' => $sound->soundstr,
                        'view_count' => $sound->view_count,
                        'duration' => $sound->duration,
                        'front_cover' => $sound->front_cover,
                        'all_comments' => $sound->all_comments,
                    ];
                }
                return $elements;
            }, []);
            if (empty($hot_recommend)) {
                Yii::error('分区设置的热门推荐数据异常，音频（ID: ' . implode(',', $hot_recommend_sound_ids)
                    . '）均不存在或状态异常', __METHOD__);
            } else {
                $video_sound_ids = array_column($hot_recommend, 'id');
                $video_sound_ids = SoundVideo::getVideoSoundIds($video_sound_ids);
                if (!empty($video_sound_ids)) {
                    foreach ($hot_recommend as &$hot) {
                        if (in_array($hot['id'], $video_sound_ids)) {
                            $hot['video'] = true;
                        }
                    }
                    unset($hot);
                }
            }
        }
        return [$recommend_ups, $rank, $hot_recommend];
    }

    /**
     * 获取音频分类下周榜音频 IDs
     *
     * @param int $catalog_id 分类 ID
     * @param int $son_catalog_id 子分类 ID
     * @return array
     */
    public static function getWeekRankSoundIds(int $catalog_id, int $son_catalog_id): array
    {
        $redis = Yii::$app->redis;
        $weekly_rank_key = $redis->generateKey(KEY_CATALOG_HOMEPAGE_WEEKLY_RANK_SOUND, $catalog_id);
        $sound_ids_str = $redis->hget($weekly_rank_key, $son_catalog_id);
        if (!$sound_ids_str) {
            return [];
        }
        $sound_ids = array_map('intval', explode(',', $sound_ids_str));
        return $sound_ids;
    }

    /**
     * 获取音频分类人气周榜
     *
     * @param int $catalog_id 音频分类 ID
     * @param int $page 当前页
     * @param int $page_size 每页个数
     * @return array
     * @throws HttpException 分类不存在时抛出异常
     */
    public static function getWeeklyRank(int $catalog_id, int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        $catalog = Catalog::find()
            ->where('id = :id AND status_is = :status', [':id' => $catalog_id, ':status' => self::STATUS_OPEN])
            ->one();
        if (!$catalog) {
            throw new HttpException(404, '分类不存在');
        }
        // 获取 tab
        $tabs = Catalog::getCatalogTabs($catalog->parent_id);
        foreach ($tabs as $key => $tab) {
            if ($catalog_id === $tab['id']) {
                $tabs[$key]['active'] = true;
                break;
            }
        }
        $return_models = ReturnModel::empty($page, $page_size);
        $return = [
            'tabs' => $tabs,
            'Datas' => $return_models->Datas,
            'pagination' => $return_models->pagination,
        ];

        $sound_ids = self::getWeekRankSoundIds($catalog->parent_id, $catalog_id);
        if (empty($sound_ids)) {
            Yii::error("音频分区（{$catalog_id}）周榜设置错误", __METHOD__);
            return $return;
        }

        // 先分页再查询
        $count = count($sound_ids);
        $offset = ($page - 1) * $page_size;
        $sound_ids = array_slice($sound_ids, $offset, $page_size);
        $sounds = [];
        if (!empty($sound_ids)) {
            $sounds = MSound::find()
                ->select('id, soundstr, cover_image, view_count, duration')
                ->where(['checked' => MSound::CHECKED_PASS])
                ->andWhere('NOT refined & :not_refined', [':not_refined' => MSound::REFINED_BLOCK])
                ->allByColumnValues('id', $sound_ids);
            $video_sound_ids = SoundVideo::getVideoSoundIds($sound_ids);
            // 处理数据
            $sounds = array_map(function ($sound) use ($video_sound_ids) {
                $return = [
                    'id' => $sound->id,
                    'title' => $sound->soundstr,
                    'view_count' => $sound->view_count,
                    'duration' => $sound->duration,
                    'front_cover' => $sound->front_cover,
                ];
                if (in_array($sound->id, $video_sound_ids)) {
                    $return['video'] = true;
                }
                return $return;
            }, $sounds);
        }
        $return_models = ReturnModel::getPaginationData($sounds, $count, $page, $page_size);
        $return['Datas'] = $return_models->Datas;
        $return['pagination'] = $return_models->pagination;
        return $return;
    }

    /**
     * 获取音频分类
     *
     * @return array
     */
    public static function getSoundCatalogIds()
    {
        $son_catalog_ids = self::find()->select('id')
            ->where(['status_is' => self::STATUS_OPEN, 'parent_id' => self::UGC_CATALOG_IDS])
            ->column();
        return array_merge($son_catalog_ids, self::UGC_CATALOG_IDS);
    }

    /**
     * 获取分类下自定义模块
     *
     * @param int $catalog_id
     * @return array
     */
    public static function getRecommendModules(int $catalog_id): array
    {
        $module_persona = MPersonaModuleElement::getCatalogPersonaId($catalog_id);
        $memcache = Yii::$app->memcache;
        $modules_data_key = MUtils::generateCacheKey(KEY_GUESSYOURLIKES_MODULES, $module_persona);
        $from_cache = false;
        if ($modules = $memcache->get($modules_data_key)) {
            $from_cache = true;
            $modules = Json::decode($modules);
        } else {
            // 获取模块数据
            $modules = MPersonaModuleElement::getModules($module_persona, null,
                YouMightLikeModule::ELEMENT_ATTR_CLASSIC_ALBUM);
            if (empty($modules)) {
                return $modules;
            }
            // 获取模块包含的元素（音单 ID、剧集 ID、音频 ID）
            $module_ids = array_unique(array_column($modules, 'module_id'));
            $elements = MPersonaModuleElement::getElementsRecommended($module_ids);
            $album_data = MPersonaModuleElement::getAlbums($elements);
            $sound_data = MPersonaModuleElement::getSounds($elements);
            $drama_data = MPersonaModuleElement::getDramas($elements, Yii::$app->user->id);
            $elems_module_group = MUtils::groupArray(array_merge($album_data, $drama_data, $sound_data),
                'module_id');
            $modules = array_map(function ($module) use ($elems_module_group) {
                $module['elements'] = $elems_module_group[$module['module_id']] ?? [];
                if ($module['elements']) return $module;
            }, $modules);
            $modules = array_values(array_filter($modules));
            $memcache->set($modules_data_key, Json::encode($modules), TEN_MINUTE);
        }
        $modules = array_map(function ($module) {
            if (isset($module['more']['url'])) {
                $module['more']['url'] = MUtils::getUsableAppLink($module['more']['url']);
            }
            $elements = $module['elements'];
            switch ($module['style']) {
                case MPersonaModuleElement::MODULE_STYLE_DEFAULT:
                case MPersonaModuleElement::MODULE_STYLE_HORIZONTAL:
                    // 非排行榜的模块，截取所需数据
                    $module['elements'] = array_slice($elements, 0,
                        MPersonaModuleElement::DRAMA_HOMEPAGE_ELEMENT_RECOMMENDED_COUNT);
                    break;
                case MPersonaModuleElement::MODULE_STYLE_TOP_PLAY_STYLE:
                    // 排行榜模块，取全部数据
                    $module['elements'] = $elements;
                    break;
                case MPersonaModuleElement::MODULE_STYLE_SLIDE:
                    // WORKAROUND: 滑动模块，旧版本（iOS < 6.0.1, Android < 6.0.1）展示为横版音频模块
                    if (Equipment::isAppOlderThan('6.0.1', '6.0.1')) {
                        $module['elements'] = array_slice($elements, 0,
                            MPersonaModuleElement::DRAMA_HOMEPAGE_ELEMENT_RECOMMENDED_COUNT);
                        $module['style'] = MPersonaModuleElement::MODULE_STYLE_HORIZONTAL;
                        break;
                    }
                    // 滑动模块，截取所需数据
                    $module['elements'] = array_slice($elements, 0,
                        MPersonaModuleElement::APP_SLIDE_SOUND_RECOMMENDED_MAX_COUNT);
                    break;
                default:
                    throw new HttpException(400, '参数错误');
            }
            if ($module['type'] === YouMightLikeModule::TYPE_DRAMA) {
                $module['elements'] = array_map(function ($drama) use ($module) {
                    // 分区的剧集模块统一使用正方形封面图
                    if (!key_exists('cover', $drama)) {
                        $drama['cover'] = Yii::$app->params['defaultCoverUrl'];
                    }
                    $drama['front_cover'] = $drama['cover'];
                    unset($drama['thumbnail'], $drama['cover']);
                    return $drama;
                }, $module['elements']);
            }
            // WORKAROUND: 兼容旧版本（iOS < 6.0.2, Android < 6.0.2）下发 direction 字段
            if (Equipment::isAppOlderThan('6.0.2', '6.0.2')) {
                $module['direction'] = $module['style'];
            }
            return $module;
        }, $modules);

        $user_id = (int)Yii::$app->user->id;
        $new_version_flag = false;
        // WORKAROUND: iOS >= 4.8.8，Android >= 5.7.4，给剧集模块补充剧集角标信息
        if (!Equipment::isAppOlderThan('4.8.8', '5.7.4')) {
            $new_version_flag = true;
            Drama::fillCornerMarkInDramaModules($modules, $user_id);
        }

        if ($from_cache && !$new_version_flag) {
            $modules = array_map(function ($module) {
                if (MPersonaModuleElement::MODULE_TYPE_DRAMA === $module['type']) {
                    Drama::checkNeedPay($module['elements'], Yii::$app->user->id);
                }
                return $module;
            }, $modules);
        }
        return $modules;
    }

    /**
     * 判断投稿的音频分类是否需要添加积分（猫耳学院暑期纳新活动）
     *
     * @param int $catalog_id 分类 ID
     * @return bool true 需要添加积分，false 为不需要添加积分
     * @throws HttpException
     */
    public static function isGetPoints(int $catalog_id)
    {
        if ($catalog_id < 0) {
            return false;
        }
        // 子分类限定为：搞笑、情感、知识、异闻，才需要添加积分
        if (in_array($catalog_id, [Catalog::CATALOG_ID_FUNNY, Catalog::CATALOG_ID_EMOTION,
            Catalog::CATALOG_ID_KNOWLEDGE, Catalog::CATALOG_ID_STRANGE_NEWS])) {
            return true;
        }
        $parent_catalog = Catalog::find()
            ->select(['parent_id'])
            ->where('id = :id AND status_is = :status_is', [':id' => $catalog_id, ':status_is' => self::STATUS_OPEN])
            ->one();
        if (!$parent_catalog) {
            throw new HttpException(404, '分类不存在');
        }

        // 投稿有音乐分区，放松分区，声音恋人分区，必须添加积分
        $catalog_arr = [
            self::CATALOG_ID_MUSIC,
            self::CATALOG_ID_SOUND_LOVER,
            self::CATALOG_ID_ASMR
        ];
        if (in_array((int)$parent_catalog->parent_id, $catalog_arr)) {
            return true;
        }
        return false;
    }

    /**
     * 获取指定父分类下的子分类
     *
     * @param integer $parent_id 父分类 ID
     * @return array
     * @throws HttpException
     */
    public static function getSonCatalogs(int $parent_id)
    {
        if ($parent_id < 0) {
            throw new HttpException(400, '参数错误');
        }
        $key = MUtils::generateCacheKey(KEY_SON_CATALOG, $parent_id);
        $memcache = Yii::$app->memcache;
        if (!$catalogs = $memcache->get($key)) {
            $catalogs = self::find()->select('id, catalog_name')
                ->where(['parent_id' => $parent_id, 'status_is' => self::STATUS_OPEN])
                ->orderBy('sort_order DESC, id ASC')
                ->asArray()
                ->all();
            $memcache->set($key, Json::encode($catalogs), HALF_HOUR);
        } else {
            $catalogs = Json::decode($catalogs);
        }
        $catalogs = array_map(function ($catalog) {
            $catalog['id'] = (int)$catalog['id'];
            return $catalog;
        }, $catalogs);
        return $catalogs;
    }

    /**
     * 根据搜索分区 ID 获取对应的音频分类 ID
     *
     * @param int $search_catalog_id
     * @return int
     * @throws HttpException
     */
    public static function getSoundCatalogBySearchCatalog(int $search_catalog_id): int
    {
        if ($search_catalog_id <= 0) {
            return $search_catalog_id;
        }
        // 获取对应的音频分类 ID
        $catalogs = Catalog::getSonCatalogs(Catalog::CATALOG_SOUND_ID);
        $catalog_ids = array_column($catalogs, 'id');
        // $search_catalog_id 实际上是 $catalogs 数组的索引值
        // 因为搜索的分区 ID 0 为全部分区，而从 1 开始才是具体的子分类，
        // 而 catalogs_ids 数组里的子分类 ID 的索引是从 0 开始，所以需要减一
        return $catalog_ids[$search_catalog_id - 1] ?? 0;
    }
}
