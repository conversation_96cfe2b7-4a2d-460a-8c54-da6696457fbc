<?php

namespace app\models;

use Yii;
use yii\web\HttpException;

/**
 * 投票规则：
 * 1、每个用户每天自然获得 1 次投票资格；通过活动后台 limit 配置来限制
 * 2、通过页面内悬浮的分享活动按钮分享成功后，每个用户每天可再获得 1 次投票资格；
 * 活动投票配置：每日投票限制 1，每日可投票作品数量限制 0，每日每作品可投票数量限制 2
 */

class GloryofkingsActivity extends ActiveRecord
{
    const EVENT_ID_ARR = [180, 181];

    const IS_LUCK_VOTE = 2;  // 用户当天已经投票
    const IS_MEET_LUCK_VOTE = 1;  // 用户已满足投票条件

    /**
     * 设置满足条件的投票用户
     *
     * @param int $user_id 用户 ID
     * @param MEvent $event 活动数据
     */
    public static function setVoteConditionsUser(int $user_id, $event)
    {
        $today_time = date('Ymd', $_SERVER['REQUEST_TIME']);
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_GLORY_OF_KINGS_CONDITIONS_USER, $today_time);

        $is_luck_vote = (int)$redis->hGet($key, $user_id);
        // 用户今日已满足投票条件或者用户当天已经投票，直接跳出
        if ($is_luck_vote === self::IS_MEET_LUCK_VOTE || $is_luck_vote === self::IS_LUCK_VOTE) {
            return;
        }
        $redis->multi()
            ->hSet($key, $user_id, self::IS_MEET_LUCK_VOTE)
            ->expireAt($key, $event->vote_end_time + ONE_WEEK)
            ->exec();
    }

    /**
     * 设置用户分享投票状态
     *
     * @param int $user_id 用户 ID
     * @param MEvent $event 活动数据
     */
    public static function setShareVote(int $user_id, $event)
    {
        $today_time = date('Ymd', $_SERVER['REQUEST_TIME']);
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_GLORY_OF_KINGS_CONDITIONS_USER, $today_time);

        // 用户已满足投票条件时，才能进行投票
        if ((int)$redis->hGet($key, $user_id) === self::IS_MEET_LUCK_VOTE) {
            $redis->multi()
                ->hSet($key, $user_id, self::IS_LUCK_VOTE)
                ->expireAt($key, $event->vote_end_time + ONE_WEEK)
                ->exec();
        }
    }

    /**
     * 检查用户分享投票状态
     *
     * @param int $user_id 用户 ID
     * @throws HttpException
     */
    public static function checkShareVote(int $user_id)
    {
        $today_time = date('Ymd', $_SERVER['REQUEST_TIME']);
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_GLORY_OF_KINGS_CONDITIONS_USER, $today_time);
        $value = (int)$redis->hGet($key, $user_id);
        // 判断用户是否有投票资格
        if ($value !== self::IS_MEET_LUCK_VOTE) {
            throw new HttpException(403, '没有投票资格了哦~', 200410101);
        }
    }
}
