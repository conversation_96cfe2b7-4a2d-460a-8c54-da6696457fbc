<?php

namespace app\models;

use app\components\util\MUtils;
use missevan\storage\StorageClient;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "m_radio_sound".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 * @property int $delete_time 删除时间
 * @property int $catalog_id 分类 ID
 * @property int $sound_id 音频 ID
 * @property string $title 标题
 * @property string $cover 封面图
 * @property string background_cover 播放页图片背景
 * @property string background_video 播放页视频背景
 * @property int sort 排序
 * @property string $more 额外的信息，json 类型。
 * 其中 show_play_info 为 true 时表示播放页需要展示播放相关信息（UP 主信息及进度条）
 */
class MRadioSound extends ActiveRecord
{
    // 历史播放页最大显示数量
    const HISTORY_PAGE_SIZE = 60;
    // 喜欢页最大显示数量
    const LIKE_SOUNDS_PAGE_SIZE = 60;

    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_radio_sound';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['create_time', 'modified_time', 'delete_time', 'catalog_id', 'sound_id', 'sort'], 'integer'],
            [['title'], 'string', 'max' => 50],
            [['cover', 'background_cover', 'background_video',], 'string', 'max' => 125],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'delete_time' => '删除时间',
            'catalog_id' => '分类 ID',
            'sound_id' => '音频 ID',
            'title' => '标题',
            'cover' => '封面图',
            'background_cover' => '播放图片背景',
            'background_video' => '播放视频背景',
            'sort' => '排序',
            'more' => '额外的信息',
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->cover) {
            $this->cover = StorageClient::getFileUrl($this->cover) ?: Yii::$app->params['defaultCoverUrl'];
        }
        if ($this->background_cover) {
            $this->background_cover = StorageClient::getFileUrl($this->background_cover);
        }
        if ($this->background_video) {
            $this->background_video = StorageClient::getFileUrl($this->background_video);
        }
    }

    /**
     * 获取催眠专享分类信息
     *
     * @return array
     */
    public static function getCatalogs(): array
    {
        $memcache = Yii::$app->memcache;
        if ($data = $memcache->get(KEY_ASMR_RADIO_CATALOGS)) {
            $catalogs = Json::decode($data);
        } else {
            $catalogs = Catalog::lite(Catalog::CATALOG_RADIO_ASMR);
            $cache_duration = QUARTER_HOUR;
            if (empty($catalogs)) {
                $cache_duration = HALF_MINUTE;
            } else {
                $catalog_ids = array_column($catalogs, 'id');
                // 查询分类 icon 信息
                $catalogs_info = Catalog::find()->select('id, content')
                    ->where(['id' => $catalog_ids])->all();
                $catalogs_info = array_column($catalogs_info, null, 'id');
                $catalogs = array_reduce($catalogs, function ($ret, $catalog) use ($catalogs_info) {
                    // WORKAROUND: 新版催眠专享客户端上线前，需要过滤老的分类，客户端上线后隐藏老分类，去掉此处兼容
                    $catalog_id = $catalog['id'];
                    if (!in_array($catalog_id, Catalog::OLD_RADIO_CATALOG_IDS)) {
                        $catalog_content = self::getCatalogContent($catalog_id, $catalogs_info);
                        if ($catalog_content) {
                            $catalog = array_merge($catalog, $catalog_content);
                        }
                        if (key_exists('sons', $catalog) && !empty($catalog['sons'])) {
                            // 对子分类进行处理
                            $catalog['sub_catalogs'] = array_map(function ($sub_catalog) {
                                unset($sub_catalog['parent_id'], $sub_catalog['sort'], $sub_catalog['level']);
                                return $sub_catalog;
                            }, $catalog['sons']);
                        }
                        unset($catalog['parent_id'], $catalog['sort'], $catalog['level'], $catalog['sons']);
                        $ret[] = $catalog;
                    }
                    return $ret;
                }, []);
            }
            $memcache->set(KEY_ASMR_RADIO_CATALOGS, Json::encode($catalogs), $cache_duration);
        }
        return $catalogs;
    }

    /**
     * 获取催眠专享分类图标及分享文案信息
     *
     * @param int $catalog_id 分类 ID
     * @param array $catalogs_info 分类信息
     * @return array 返回分类图标及分享信息
     */
    private static function getCatalogContent(int $catalog_id, array $catalogs_info): array
    {
        $info = ['share_title' => '', 'share_url' => ''];
        if (key_exists($catalog_id, $catalogs_info) && $catalogs_info[$catalog_id]['content']) {
            $content = Json::decode($catalogs_info[$catalog_id]['content']);
            if (key_exists('share_title', $content)) {
                $info['share_title'] = $content['share_title'];
            }
            if (key_exists('share_url', $content)) {
                $info['share_url'] = $content['share_url'];
            }
            if (key_exists('icon_url', $content)) {
                // 若有图标信息，则进行添加
                $info['icon'] = StorageClient::getFileUrl($content['icon_url']);
            }
        }
        if (!$info['share_title'] || !$info['share_url']) {
            Yii::error("催眠专享分类（{$catalog_id}）未配置分享信息");
            // PASS
        }
        return $info;
    }

    /**
     * 获取指定分类下催眠专享音频信息
     *
     * @param int $catalog_id
     * @return array
     */
    public static function getCatalogSounds(int $catalog_id): array
    {
        $radio_sounds = self::find()
            ->select('id, catalog_id, sound_id, title, cover, background_cover')
            ->where(['catalog_id' => $catalog_id, 'delete_time' => 0])
            ->orderBy('sort ASC')
            ->all();
        if (empty($radio_sounds)) {
            return [];
        }
        // 此处跨库，故使用 IN 查询而非 JOIN（每个分类下音频数量在 100 内）
        $radio_sounds = array_column($radio_sounds, null, 'sound_id');
        $sound_ids = array_keys($radio_sounds);
        // 查询可收听的音频 ID
        $sound_ids = MSound::find()
            ->select('id')
            ->where(['id' => $sound_ids])
            // 催眠专享音频展示不考虑风险、下架的问题，只要过审就能看（听）
            ->andWhere('checked >= :checked', [':checked' => MSound::CHECKED_PASS])
            ->column();
        if (empty($sound_ids)) {
            return [];
        }
        return array_reduce($radio_sounds, function ($ret, $radio_sound) use ($sound_ids) {
            if (in_array($radio_sound->sound_id, $sound_ids)) {
                $ret[] = [
                    'id' => $radio_sound->id,
                    'catalog_id' => $radio_sound->catalog_id,
                    'title' => $radio_sound->title,
                    'front_cover' => $radio_sound->cover,
                    'background_cover' => $radio_sound->background_cover,
                ];
            }
            return $ret;
        }, []);
    }

    /**
     * 获取催眠专享音频信息
     *
     * @param int $id
     * @return array|null 音频不存在时返回 null
     */
    public static function getSound(int $id): ?array
    {
        $radio_sound = self::find()
            ->select('catalog_id, sound_id, title, cover, background_cover, background_video, more')
            ->where(['id' => $id, 'delete_time' => 0])
            ->one();
        if (!$radio_sound) {
            return null;
        }
        $sound = MSound::find()
            ->select('id, duration, soundurl_64, soundurl_128, user_id')
            ->where(['id' => $radio_sound->sound_id])
            // 催眠专享音频展示不考虑风险、下架的问题，只要过审就能看（听）
            ->andWhere('checked >= :checked', [':checked' => MSound::CHECKED_PASS])
            ->one();
        if (!$sound) {
            // 若无对应的可播音频，则记录错误日志方便排查纠错
            Yii::error("催眠专享音频（{$id}）不存在");
            return null;
        }
        // 音频地址加签
        MSound::getSoundSignUrls($sound, true);
        $sound_info = [
            'id' => $id,
            'catalog_id' => $radio_sound->catalog_id,
            'title' => $radio_sound->title,
            'front_cover' => $radio_sound->cover,
            'background_cover' => $radio_sound->background_cover,
            'duration' => $sound->duration,
            'soundurl' => $sound->soundurl,
            'soundurl_128' => $sound->soundurl_128,
        ];
        if ($radio_sound->background_video) {
            // TODO: 之后需要把视频宽高一起下发
            $sound_info['background_video'] = $radio_sound->background_video;
        }
        $show_play_info = $radio_sound->showPlayInfo();
        if ($show_play_info) {
            $up_info = Mowangskuser::find()
                ->select('id, username, icontype, iconurl, boardiconurl, avatar')
                ->where(['id' => $sound->user_id])
                ->one();
            if ($up_info) {
                $sound_info = array_merge($sound_info, [
                    'user_id' => $up_info->id,
                    'username' => $up_info->username,
                    'avatar' => $up_info->iconurl,
                ]);
            } else {
                Yii::error("催眠专享音频（{$id}）所属用户不存在");
                // PASS
            }
        }
        $sound_info['progress'] = (int)$show_play_info;
        return $sound_info;
    }

    /**
     * 是否需要展示播放相关 UP 主及进度条信息
     *
     * @return bool
     */
    public function showPlayInfo(): bool
    {
        if ($this->more && key_exists('show_play_info', $this->more)) {
            return $this->more['show_play_info'];
        }
        return false;
    }

    /**
     * 获取首页分享信息
     *
     * @return array
     */
    public static function getHomepageShareInfo(): array
    {
        $share_info = [
            'share_title' => '',
            'share_content' => '',
            'share_cover' => '',
            'share_url' => '',
        ];
        $content = Catalog::find()->select('content')
            ->where(['id' => Catalog::CATALOG_RADIO_ASMR])->scalar();
        if (!$content) {
            Yii::error("催眠专享未配置首页分享信息");
            return $share_info;
        }
        $content = Json::decode($content);
        $share_info['share_title'] = $content['share_title'] ?? '';
        $share_info['share_content'] = $content['share_content'] ?? '';
        $share_cover = $content['share_cover'] ?? '';
        $share_info['share_cover'] = StorageClient::getFileUrl($share_cover);
        $share_info['share_url'] = $content['share_url'] ?? '';
        return $share_info;
    }
}
