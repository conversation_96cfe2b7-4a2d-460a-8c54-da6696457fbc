<?php

namespace app\models;

use Exception;
use Yii;
use yii\db\Expression;
use yii\db\Query;

/**
 * This is the model class for table "m_recommend_popup".
 *
 * @property int $id 主键
 * @property string $creative_id 创意 ID，用于广告主与自己平台上的创意进行映射
 * @property string $project_id 广告计划 ID
 * @property string $group_id 广告组 ID
 * @property int $gender 用户画像（0 未知，1 为普通男，2 为普通女，3 为乙女）
 * @property string $name 主题
 * @property string $url 弹窗链接
 * @property int $tab 首页默认 tab 1：推荐；2：直播
 * @property int $element_id 元素 ID
 * @property int $vendor 渠道（0：Bilibili；1：Douyin；2：Kuaishou；3：Tencent）
 * @property int $status 状态 -1：已停止；0：未生效；1：生效中
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 * @property array $more 额外信息，格式为 json 字符串
 */
class MRecommendPopup extends ActiveRecord
{
    const STATUS_PUBLISHED = 1;

    // 广告推荐针对的用户画像
    // NOTICE: 此画像与 Persona 中画像含义相同，但对应值不相等，使用时需要进行相关转换
    const GENDER_UNKNOWN = 0;  // 无画像
    const GENDER_MALE = 1;  // 普通男
    const GENDER_FEMALE = 2;  // 普通女
    const GENDER_OTOME = 3;  // 乙女

    const TAB_POPUP = 1;  // 推荐弹窗
    const TAB_LIVE = 2;  // 直播间页
    const TAB_CUSTOM = 3;  // 自定义打开页

    const ORIGIN_SITE_TABS = 1;
    const ORIGIN_YOUMIGHTLIKE_GETRECOMMENDS = 2;

    // 特定主播直播间未开播时的跳转策略
    const FALLBACK_STRATEGY_LIVE_PAGE = 1; // 跳转到小时榜上随机直播间
    const FALLBACK_STRATEGY_HOME_PAGE = 2; // 跳转到个人主页

    const SCENARIO_ADD_URL = 'add_url';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_recommend_popup';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name'], 'required'],
            [['url'], 'required', 'on' => self::SCENARIO_ADD_URL],
            [['gender', 'tab', 'vendor', 'status', 'create_time', 'modified_time'], 'integer'],
            [['creative_id', 'project_id', 'group_id'], 'string', 'max' => 50],
            [['name'], 'string', 'max' => 100],
            [['url'], 'string', 'max' => 255],
            [['more'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'creative_id' => '创意 ID',  // 用于广告主与自己平台上的创意进行映射
            'project_id' => '广告计划 ID',
            'group_id' => '广告组 ID',
            // TODO: 字段名需要调整为 persona
            'gender' => '用户画像',  // 0 未知，1 为普通男，2 为普通女，3 为乙女
            'name' => '主题',
            'url' => '弹窗链接',
            'tab' => '首页默认 tab',  // 1：推荐；2：直播
            'vendor' => '渠道',  // 0：Bilibili；1：Douyin；2：Kuaishou；3：Tencent
            'status' => '状态',  // -1：已停止；0：未生效；1：生效中
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'more' => '更多详情', // json 格式（存 fallback_strategy 其中 fallback_strategy 是直播间未开播时的跳转策略）
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
    }

    /**
     * 获取推荐弹窗查询条件
     *
     * 广告分级结构：一、二、三级
     * 抖音：广告组 (group_id) - 广告计划 (project_id) - 广告创意 (creative_id)
     * 快手：广告计划 (project_id) - 广告组 (group_id) - 广告创意 (creative_id)
     * 腾讯：广告计划 (project_id) - 广告组 (group_id) - 广告创意 (creative_id)
     * B 站：广告计划 (project_id) - 广告单元 (group_id) - 广告创意 (creative_id)
     * 网易有道：广告系列 (project_id) - 广告组 (group_id) - 广告创意 (creative_id)
     * 微博：广告系列 - 广告计划 (group_id) - 广告创意 (creative_id)
     * MetaApp：广告组 - 广告计划 - 广告创意 (creative_id)
     * 磁力聚星：广告计划 - 广告组 - 广告创意
     * 网易云：广告计划 (project_id) - 广告组 (group_id) - 广告创意 (creative_id)
     * 爱奇艺：广告系列 (project_id) - 广告组 (group_id) - 广告创意 (creative_id)
     * 易效：广告计划 - 广告组 - 广告创意 (creative_id)
     * vivo：广告计划 (project_id) - 广告组 (group_id) - 广告 (ad_id) - 广告创意 (creative_id)
     * 星图：项目 - 任务 (group_id)
     * sigmob：产品 - 广告计划 (group_id) - 广告创意 (creative_id)
     * 喜马拉雅：广告组 (project_id) - 广告计划 (group_id) - 广告创意 (creative_id)
     * 炫逸：广告计划 (project_id) - 广告组 (group_id)
     * 小红书：广告计划 (project_id) - 广告单元 (group_id) - 广告创意 (creative_id)
     *
     * 弹窗优先级：三级 > 二级 > 一级
     * 需求文档地址：https://info.missevan.com/pages/viewpage.action?pageId=28282946
     *
     * @param AdTrack $ad_track 归因数据
     * @return array 按照排序规则组合的查询条件
     * @throws Exception
     */
    public static function getSelectCondition(AdTrack $ad_track)
    {
        $conditions = [];
        // TODO: 之后统一下不同广告方的查询级别（直接调整成按三级存储）
        switch ($ad_track->vendor) {
            case AdTrack::VENDOR_DOUYIN:
                if ($ad_track->creative_id) {
                    array_push($conditions, ['creative_id' => $ad_track->creative_id]);
                }
                if ($ad_track->project_id) {
                    array_push($conditions, ['project_id' => $ad_track->project_id]);
                }
                if ($ad_track->group_id) {
                    array_push($conditions, ['group_id' => $ad_track->group_id]);
                }
                return $conditions;
            case AdTrack::VENDOR_BILIBILI:
            case AdTrack::VENDOR_KUAISHOU:
            case AdTrack::VENDOR_TENGXUN:
            case AdTrack::VENDOR_YOUDAO:
            case AdTrack::VENDOR_WEIBO:
            case AdTrack::VENDOR_BAIDU_SEARCH:
            case AdTrack::VENDOR_BAIDU_INFO_FLOW:
            case AdTrack::VENDOR_METAAPP:
            case AdTrack::VENDOR_CILIJUXING:
            case AdTrack::VENDOR_APPLE:
            case AdTrack::VENDOR_APPSFLYER:
            case AdTrack::VENDOR_WANGYIYUN:
            case AdTrack::VENDOR_IQIYI:
            case AdTrack::VENDOR_YIXIAO:
            case AdTrack::VENDOR_VIVO:
            case AdTrack::VENDOR_XIMALAYA:
            case AdTrack::VENDOR_XIAOHONGSHU:
                if ($ad_track->creative_id) {
                    array_push($conditions, ['creative_id' => $ad_track->creative_id]);
                }
                if ($ad_track->group_id) {
                    array_push($conditions, ['group_id' => $ad_track->group_id]);
                }
                if ($ad_track->project_id) {
                    array_push($conditions, ['project_id' => $ad_track->project_id]);
                }
                return $conditions;
            case AdTrack::VENDOR_XINGTU:
                if ($ad_track->group_id) {
                    array_push($conditions, ['group_id' => $ad_track->group_id]);
                }
                return $conditions;
            case AdTrack::VENDOR_SIGMOB:
                if ($ad_track->creative_id) {
                    array_push($conditions, ['creative_id' => $ad_track->creative_id]);
                }
                if ($ad_track->group_id) {
                    array_push($conditions, ['group_id' => $ad_track->group_id]);
                }
                return $conditions;
            case AdTrack::VENDOR_XUANYI:
                if ($ad_track->group_id) {
                    array_push($conditions, ['group_id' => $ad_track->group_id]);
                }
                if ($ad_track->project_id) {
                    array_push($conditions, ['project_id' => $ad_track->project_id]);
                }
                return $conditions;
            case AdTrack::VENDOR_THIRDPARTY:
                return [];
            default:
                throw new Exception('参数错误');
        }
    }

    /**
     * 为设备绑定弹窗信息
     *
     * @param AdTrack $ad_track
     * @throws Exception
     */
    public function bindEquipID(AdTrack $ad_track)
    {
        $redis = Yii::$app->redis;
        // 将设备号和广告 ID 进行绑定
        $recommend_popup_equip_key = $redis->generateKey(KEY_RECOMMEND_POPUP_EQUIP, $ad_track->equip_id);
        $url = $this->getTargetUrl($ad_track);
        $value = sprintf('%d,%s', $this->tab, $url);
        $fallback_strategy = $this->getFallbackStrategy(); // 获取配置的直播间未开播时的跳转策略
        if (!is_null($fallback_strategy)) {
            $value = sprintf('%d,%s,%d', $this->tab, $url, $fallback_strategy);
        }
        // 设置 7 天的缓存
        $redis->setex($recommend_popup_equip_key, ONE_WEEK, $value);
        if (!$ad_track->savePopupId($this->id)) {
            $message = sprintf(
                'save ad more.popup_id failed: ad_track_id[%s], popup_id[%d]',
                $ad_track->getUniqueTrackedId(), $this->id
            );
            Yii::error($message, __METHOD__);
            // PASS
        }
    }

    /**
     * 获取配置的直播间未开播时的跳转策略
     * @return int|null
     */
    public function getFallbackStrategy(): ?int
    {
        if ($this->tab !== self::TAB_LIVE) {
            // 其他「承接位置」无跳转策略
            return null;
        }

        if (!$this->more || !isset($this->more['fallback_strategy'])) {
            return self::FALLBACK_STRATEGY_LIVE_PAGE;
        }

        $fallback_strategy = (int)$this->more['fallback_strategy'];

        // 判断 fallback_strategy 是否符合预期值
        if ($fallback_strategy === self::FALLBACK_STRATEGY_LIVE_PAGE
                || $fallback_strategy === self::FALLBACK_STRATEGY_HOME_PAGE
        ) {
            return $fallback_strategy;
        }

        // 「承接位置」为直播时, 默认的承接策略为随机小时榜上直播间
        return self::FALLBACK_STRATEGY_LIVE_PAGE;
    }

    private function getFrom(AdTrack $ad_track)
    {
        // m_recommend_popup 的 project_id、group_id、creative_id 由增长同学手动填写（优先使用），不完整，需要用 ad_track 的下发给客户端
        // 例：vendor=2 快手，project_id 为空，group_id 为 258199867，creative_id 为空，即快手广告组 258199867 下的所有广告创意使用一个弹窗
        $project_id = $group_id = $creative_id = '0';
        if ($this->project_id) {
            $project_id = $this->project_id;
        } elseif ($ad_track->project_id) {
            $project_id = $ad_track->project_id;
        }

        if ($this->group_id) {
            $group_id = $this->group_id;
        } elseif ($ad_track->group_id) {
            $group_id = $ad_track->group_id;
        }

        if ($this->creative_id) {
            $creative_id = $this->creative_id;
        } elseif ($ad_track->creative_id) {
            $creative_id = $ad_track->creative_id;
        }

        // ad.0.{vendor}.{project_id}_{group_id}_{creative_id}_{popup_id}
        return sprintf(
            'ad.0.%d.%s_%s_%s_%d',
            $this->vendor, $project_id, $group_id, $creative_id, $this->id
        );
    }

    /**
     * 获取目标链接地址
     *
     * @param AdTrack $ad_track
     * @return string
     * @throws Exception
     */
    public function getTargetUrl(AdTrack $ad_track)
    {
        switch ($this->tab) {
            case self::TAB_LIVE:
                if ($this->element_id) {
                    $uri = Yii::$app->live->roomSchemeUrl($this->element_id);
                } else {
                    $uri = 'tab:live';
                }
                // 格式：
                // missevan://live/xxx?from=ad.0.{vendor}.{project_id}_{group_id}_{creative_id}_{popup_id}&event_id_from=ad.0.{vendor}.{project_id}_{group_id}_{creative_id}_{popup_id}
                // tab:live?from=ad.0.{vendor}.{project_id}_{group_id}_{creative_id}_{popup_id}&event_id_from=ad.0.{vendor}.{project_id}_{group_id}_{creative_id}_{popup_id}
                $from = $this->getFrom($ad_track);
                // WORKAROUND: 客户端 6.1.7 及以上版本使用 event_id_from 参数，暂时保留 from 参数用于兼容旧版本
                $query = 'from=' . $from . '&event_id_from=' . $from;
                $target_url = self::appendUrlQuery($uri, $query);
                break;
            case self::TAB_POPUP:
                $target_url = $this->url;
                break;
            case self::TAB_CUSTOM:
                $from = $this->getFrom($ad_track);
                // WORKAROUND: 客户端 6.1.7 及以上版本使用 event_id_from 参数，暂时保留 from 参数用于兼容旧版本
                $query = 'from=' . $from . '&event_id_from=' . $from;
                $target_url = self::appendUrlQuery($this->url, $query);
                break;
            default:
                throw new Exception('tab 参数错误：' . $this->tab);
        }

        return $target_url;
    }

    /**
     * 根据广告获取弹窗
     *
     * @param AdTrack $ad_track
     * @return self|null|\yii\db\ActiveRecord
     * @throws Exception
     */
    public static function getPopup(AdTrack $ad_track)
    {
        $select = 'id, tab, gender, url, element_id, creative_id, project_id, group_id, vendor, more';
        if ($popup_id = $ad_track->getPopupId()) {
            $record = self::find()->select($select)->where(['id' => $popup_id])->one();
            if (is_null($record)) {
                Yii::error(sprintf('ad popup record is not found: popup_id[%d], ad_track_id[%s]', $popup_id, $ad_track->getUniqueTrackedId()), __METHOD__);
            }
            return $record;
        }

        // 通过渠道来组合查询条件，不同渠道对应的广告投放结构均为三级结构
        $conditions = self::getSelectCondition($ad_track);
        if (empty($conditions)) {
            return null;
        }
        $sql = null;
        foreach ($conditions as $idx => $condition) {
            $tmp = ":idx{$idx}";
            $query = new Query();
            $sql_next = $query
                ->select($select)
                ->addSelect(new Expression("{$tmp} AS idx", [$tmp => $idx]))
                ->from(self::tableName())
                ->where($condition)
                ->andWhere(['vendor' => $ad_track->vendor, 'status' => self::STATUS_PUBLISHED])
                ->orderBy('id DESC')
                ->limit(1);
            if (!$sql) {
                $sql = $sql_next;
            } else {
                $sql = $sql->union($sql_next);
            }
        }

        $data = $sql->orderBy('idx ASC')->limit(1)->one();
        if (!$data) {
            return null;
        }
        unset($data['idx']);
        $record = new self($data);
        $record->id = (int)$data['id'];
        $record->gender = (int)$record->gender;
        $record->tab = (int)$record->tab;
        $record->element_id = (int)$record->element_id;
        $record->vendor = (int)$record->vendor;
        return $record;
    }

    public function getPersonaByGender()
    {
        switch ($this->gender) {
            case self::GENDER_MALE:
                return Persona::TYPE_BOY;
            case self::GENDER_FEMALE:
                // break intentionally omitted
            case self::GENDER_OTOME:
                // 若为女性用户，返回普通女性画像
                return Persona::TYPE_GIRL;
            default:
                throw new Exception('gender 参数错误：' . $this->gender);
        }
    }

    /**
     * 获取推荐弹窗数据
     *
     * @param integer $origin 请求来源接口 1：/site/tabs；2：/you-might-like/get-recommends
     * @param string $equip_id 设备 ID
     * @return null|array 推荐弹窗数据，值分别为 [承接位置, url, 未开播时跳转策略]
     *  承接位置：
     *  - 1：推荐弹窗
     *  - 2：直播间页
     *  - 3：自定义打开页
     *  未开播跳转策略：
     *  - 1：小时榜随机直播间
     *  - 2：主播个人页
     * 例如：[1, 'http://static.missevan.com/standalone/app/test/index.html']
     * 或者 [2, 'tab:live?from=ad.0.{vendor}.{project_id}_{group_id}_{creative_id}_{popup_id}', 1]
     * 或者 [2, 'missevan://live/463640018?from=ad.0.{vendor}.{project_id}_{group_id}_{creative_id}_{popup_id}', 2]
     *
     */
    public static function getRecommendData(int $origin, string $equip_id)
    {
        $redis = Yii::$app->redis;
        $recommend_popup_key = $redis->generateKey(KEY_RECOMMEND_POPUP_EQUIP, $equip_id);
        $recommend_url = $redis->get($recommend_popup_key);

        // 这里不用 ! 的原因是如果 redis 里存的是字符串 0 这里也是失败的，而不能走到下面 del
        if ($recommend_url === false) {
            return null;
        }
        $items = explode(',', $recommend_url);
        $is_has_tab = self::isRecommendTabUrl($recommend_url);
        $result = null;
        switch (count($items)) {
            case 2:
                [$tab, $url] = $items;
                if ((int)$tab === self::TAB_LIVE) {
                    // 旧数据设置默认跳转策略为小时榜上随机直播间
                    $result = [(int)$tab, $url, self::FALLBACK_STRATEGY_LIVE_PAGE];
                } else {
                    $result = [(int)$tab, $url];
                }
                break;
            case 3:
                [$tab, $url, $fallback_strategy] = $items;
                $result = [(int)$tab, $url, (int)$fallback_strategy];
                break;
            default:
                Yii::error('Invalid recommendation data: ' . $recommend_url, __METHOD__);
        }

        if (($origin === self::ORIGIN_YOUMIGHTLIKE_GETRECOMMENDS && !$is_has_tab)
                || ($origin === self::ORIGIN_SITE_TABS && $is_has_tab)) {
            // 设置个较短的过期时间是为了后置调用接口能取到相关数据
            $redis->expire($recommend_popup_key, 10);
        }

        return $result;
    }

    /**
     * 是否为 tab 协议地址
     *
     * @param string $recommend_url 推荐弹窗地址
     * @return boolean
     */
    public static function isRecommendTabUrl(string $recommend_url): bool
    {
        return substr($recommend_url, 0, 4) === 'tab:';
    }

    /**
     * 获取直播间页打开地址
     * 1）推荐链接若为直播间的链接，且该直播间处于开播状态，则返回该推荐链接，未开播则返回主播个人主页链接地址，主播已注销返回小时榜开播直播间的链接地址
     * 2）推荐链接若为直播 tab 页，则取小时榜开播直播间的链接地址
     *
     * @param string $recommend_url
     * @param int $fallback_strategy
     * @return string|null
     * @throws Exception
     */
    public static function getLiveOpenUrl(string $recommend_url, int $fallback_strategy)
    {
        if ($room_id = Yii::$app->live->parseRoomIdFromSchemeUrl($recommend_url)) {
            $live = Live::getLiveByRoomId($room_id, true);
            if ($live) {
                // 特定主播直播间开播, 则直接进入直播间
                if ($live->status === Live::STATUS_OPEN) {
                    return $recommend_url;
                }
                // 特定主播直播间未开播
                if ($live->status === Live::STATUS_CLOSE) {
                    // 特定主播未开播情况下，根据未开播的跳转策略进行处理
                    if ($fallback_strategy === self::FALLBACK_STRATEGY_LIVE_PAGE) { // 跳转到小时榜开播直播间
                        return self::handleLivePageStrategy($recommend_url);
                    } elseif ($fallback_strategy === self::FALLBACK_STRATEGY_HOME_PAGE) { // 跳转到主播个人主页
                        return self::handleHomePageStrategy($recommend_url, $live);
                    }
                }
                return self::getLiveUrl($recommend_url);
            }
            return null;
        } elseif (MRecommendPopup::isRecommendTabUrl($recommend_url)) {
            return self::getLiveUrl($recommend_url);
        }

        return null;
    }

    /**
     * 处理跳转到小时榜开播直播间的策略
     *
     * @param string $recommend_url 推荐链接
     * @return string
     */
    private static function handleLivePageStrategy(string $recommend_url)
    {
        return self::getLiveUrl($recommend_url);
    }

    /**
     * 处理跳转到主播个人主页的策略
     *
     * @param string $recommend_url 推荐链接
     * @param object $live 当前的直播信息对象
     * @return string
     * @throws Exception
     */
    private static function handleHomePageStrategy(string $recommend_url, $live): string
    {
        if (Mowangskuser::isUserBlocked($live->user_id)) {
            return self::getLiveUrl($recommend_url);
        }
        // 获取主播个人主页链接
        return self::getUserHomepageWithQuery($recommend_url, $live->user_id);
    }

    /**
     * 获取小时榜上随机直播间，若无开播直播间则返回直播 tab
     *
     * @param string $recommend_url 推荐直播间的 URL
     * @return string 直播间链接或者默认的直播 tab URL
     */
    private static function getLiveUrl(string $recommend_url): string
    {
        return Yii::$app->live->getOpenUrlFromLiveHourRank($recommend_url) ?: Msr0::SCHEME_URL_LIVE_TAB;
    }

    /**
     * 获取带有查询参数的主播个人主页链接
     *
     * @param string $recommend_url 推荐链接
     * @param int $user_id 主播的用户 ID
     * @return string
     */
    private static function getUserHomepageWithQuery(string $recommend_url, int $user_id)
    {
        $home_url = Msr0::getUserHomepage($user_id);

        // 如果推荐链接中有 query 参数，保留并附加到个人主页链接中
        if ($query = parse_url($recommend_url, PHP_URL_QUERY)) {
            $home_url = self::appendUrlQuery($home_url, $query);
        }

        return $home_url;
    }

    /**
     * 将查询字符串追加到给定的 URL
     *
     * @param string $url 需要追加查询字符串的 URL，例如：missevan://user/homepage?user_id=123456
     * @param string $query 需要追加的查询字符串，例如：from=xxx
     * @return string
     */
    private static function appendUrlQuery(string $url, string $query): string
    {
        if (!is_null(parse_url($url, PHP_URL_QUERY))) {
            return $url . '&' . $query;
        }
        return $url . '?' . $query;
    }

    /**
     * 获取推荐弹窗跳转链接
     *
     * @param string|null $channel 渠道标识
     * @param string|null $buvid 唯一设备标识
     * @param string $equip_id 设备 ID
     * @return array 包含 popup_url 和 open_url 的数组
     */
    public static function getPopupUrls(string|null $channel, string|null $buvid, string $equip_id)
    {
        $return = [];
        $recommend = self::getRecommendData(self::ORIGIN_YOUMIGHTLIKE_GETRECOMMENDS, $equip_id);
        if ($recommend) {
            [$tab, $recommend_url] = $recommend;
            switch ($tab) {
                case self::TAB_POPUP:
                    $return['popup_url'] = $recommend_url;
                    break;
                case self::TAB_LIVE:
                    // 承接位置为直播时, 需要解析出相应的未开播跳转策略
                    [, , $fallback_strategy] = $recommend;
                    if ($open_url = self::getLiveOpenUrl($recommend_url, $fallback_strategy)) {
                        $return['open_url'] = $open_url;
                    }
                    break;
                case self::TAB_CUSTOM:
                    $return['open_url'] = $recommend_url;
                    break;
            }
        }
        $channel_open_urls = Yii::$app->params['channel_open_urls'];
        // 特定渠道包，无广告推荐弹窗或广告跳转时，首次启动跳转到对应的活动页
        if (array_key_exists($channel, $channel_open_urls) && $buvid) {
            $redis = Yii::$app->redis;
            // 防止整个 key 过大，对启动过的 buvid 进行聚合存储处理
            $index = substr(md5($buvid), 0, 2);
            $key = $redis->generateKey(KEY_CHANNEL_LAUNCHED_BUVID_LIST, $index);
            $is_already_launched = $redis->sIsMember($key, $buvid);
            // 优先广告弹窗、广告跳转
            if (empty($return) && !$is_already_launched) {
                $return['open_url'] = $channel_open_urls[$channel];
            }
            if (!$is_already_launched) {
                // 记录首次启动过的 buvid
                // TODO: 临时延长过期时间，后续产品会提需求重新定义【首次启动】概念
                $expire_time = strtotime('2025-10-01 00:00:00');
                $redis->multi()
                    ->sAdd($key, $buvid)
                    ->expireAt($key, $expire_time)
                    ->exec();
            }
        }
        return $return;
    }
}
