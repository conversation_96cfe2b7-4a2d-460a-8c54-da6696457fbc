<?php

namespace app\models;

use Exception;
use Yii;
use yii\helpers\Json;

class MThirdPartyCtripTask
{
    // 合作方回调接口文档：https://info.bilibili.co/pages/viewpage.action?pageId=958111694
    const CALLBACK_URL_CTRIP = 'https://m.ctrip.com/restapi/soa2/22598/externalTrigger';

    // 与携程方的约定参数
    const CTRIP_CHANNEL_ID = 'missevan_001_prd';
    // 生成携程回调接口签名密钥
    const CTRIP_SIGN_KEY = 'd16add1b520941b2';

    // 回调成功响应 code
    const CTRIP_REQUEST_SUCCESS_CODE = 200;

    /**
     * 请求携程回调
     *
     * @param string $track_id
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function callback(string $track_id): bool
    {
        $params = [
            'channelId' => self::CTRIP_CHANNEL_ID,
            'taskUnion' => $track_id,
            'taskExtra' => '{}',
        ];
        $params['sign'] = self::buildSign($params);
        try {
            $data = Yii::$app->tools->requestRemote(self::CALLBACK_URL_CTRIP, [], 'POST', Json::encode($params), 0, [
                'Content-Type' => 'application/json',
            ]);
            if (!$data) {
                throw new Exception('携程点击回传失败，返回值为空');
            }
            if ($data['code'] !== self::CTRIP_REQUEST_SUCCESS_CODE) {
                throw new Exception(sprintf('code[%d], msg[%s]', $data['code'], $data['message']));
            }
        } catch (Exception $e) {
            Yii::error('携程点击回传失败，原因：' . $e->getMessage(), __METHOD__);
            return false;
        }
        return true;
    }

    /**
     * 获取携程方回调接口需要的签名
     *
     * @param array $params 生成签名的参数
     * @return string 签名
     */
    private static function buildSign(array $params): string
    {
        // 携程方参数顺序是固定的，所以手动按顺序拼接处理
        $str_to_sign = 'channelId=' . $params['channelId'] . '&taskUnion=' . $params['taskUnion'] .
            '&taskExtra=' . $params['taskExtra'] . '&signKey=' . self::CTRIP_SIGN_KEY;
        return strtolower(md5($str_to_sign));
    }
}
