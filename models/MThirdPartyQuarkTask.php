<?php

namespace app\models;

use Exception;
use Yii;

class MThirdPartyQuarkTask
{
    // 合作方对接文档
    // https://doc.weixin.qq.com/sheet/e3_AUAAMAYJACECNpcyiRXYQT1CyegN8?scode=ANYAEAdoABEQEck1c6ADEA7wbyADg&tab=nhc8by
    const CALLBACK_URL = 'https://coral2.quark.cn/task/v3/completeByAccessCode';
    const SUCCESS_CODE = 'OK';

    public static function callback(string $track_id): bool
    {
        $params = [
            'accessCode' => $track_id,
        ];

        $headers = [
            'Content-Type' => 'application/x-www-form-urlencoded',
        ];

        try {
            $url = Yii::$app->params['mthirdPartQuarkTaskCallBackURL'] ?? self::CALLBACK_URL;
            $data = Yii::$app->tools->requestRemote($url, $params, 'POST', null, 0, $headers);
            if (!$data) {
                throw new Exception('返回值为空');
            }
            if ($data['code'] !== self::SUCCESS_CODE) {
                throw new Exception(sprintf('code[%d] msg[%s]', $data['code'], $data['msg']));
            }
        } catch (Exception $e) {
            Yii::error(sprintf('夸克点击回传失败，参数 = %s，原因 = %s', json_encode($params), $e->getMessage()), __METHOD__);
            return false;
        }
        return true;
    }
}
