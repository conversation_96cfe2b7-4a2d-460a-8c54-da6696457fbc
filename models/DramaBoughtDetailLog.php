<?php

namespace app\models;

use Exception;
use Yii;
use yii\helpers\Json;

class DramaBoughtDetailLog
{
    // 请求来源
    // App
    const ORIGIN_APP = 1;
    // Web
    const ORIGIN_WEB = 2;
    // 抖店
    const ORIGIN_DOUDIAN = 3;

    // 兑换类型 1：兑换码兑换；2：福袋兑换
    const REDEEM_TYPE_NORMAL = 1;
    const REDEEM_TYPE_LUCKY_BAG = 2;

    /**
     * 新增购买剧集记录
     *
     * @param integer $user_id 用户 ID
     * @param integer $drama_id 剧集 ID
     * @param integer $origin 请求来源 1: App; 2: Web; 3: 抖店
     * @param integer $pay_type 付费类型 1：单集付费；2：整剧付费
     * @param array $more 其他信息，单集付费时传入所购音频 IDs，e.g. ['sound_ids' => [1, 2, 5]]，
     * 或剧集是由兑换而来传入兑换类型，e.g. ['redeem_type' => 1]
     * @return bool
     */
    public static function addLog(int $user_id, int $drama_id, int $origin = self::ORIGIN_APP,
            int $pay_type = Drama::PAY_TYPE_DRAMA, array $more = []): bool
    {
        try {
            $data = [
                'user_id' => $user_id,
                'drama_id' => $drama_id,
                'pay_type' => $pay_type,
                'origin' => $origin,
                'create_time' => $_SERVER['REQUEST_TIME'],
            ];
            if ($more) {
                $data['more'] = $more;
            }
            Yii::$app->databus->pub($data, 'buy_drama_detail_log:' . $user_id);
            return true;
        } catch (Exception $e) {
            Yii::error('databus 出错：' . $e->getMessage() . ', 当前 log: ' . Json::encode($data), __METHOD__);
            // PASS: databus 出错记录错误并忽略异常
        }
        return false;
    }
}
