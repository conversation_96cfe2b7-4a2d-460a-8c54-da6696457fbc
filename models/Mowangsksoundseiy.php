<?php

namespace app\models;

use app\components\util\Go;
use app\components\util\MUtils;
use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "mowangsksoundseiy".
 *
 * @property integer $id
 * @property string $name
 * @property string $icon
 * @property string $profile
 * @property integer $gender
 * @property integer $birthyear
 * @property integer $birthmonthday
 * @property integer $bloodtype
 * @property integer $career
 * @property integer $birthmonth
 * @property integer $birthday
 * @property string $group
 * @property string $weibo
 * @property string $weiboname
 * @property string $baike
 * @property string $baikename
 * @property integer $mid
 * @property integer $checked
 * @property integer $soundline1
 * @property integer $soundline2
 * @property integer $soundline3
 * @property string $seiyalias
 * @property integer $sound_type
 * @property integer $initial
 */
class Mowangsksoundseiy extends ActiveRecord
{
    // 免费类型
    const FREE_PAY = 0;
    // 单集付费类型
    const EPISODE_PAY = 1;
    // 整剧付费类型
    const DRAMA_PAY = 2;

    // 付费剧集已付费
    const DRAMA_PAID = 2;
    // 付费剧集未付费
    const DRAMA_UNPAID = 1;

    // 播放页置顶正在直播声优的数量
    const MAX_TOP_OPEN_LIVE = 2;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'mowangsksoundseiy';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['name', 'icon', 'profile', 'gender', 'bloodtype', 'career', 'birthmonth', 'group', 'weibo', 'weiboname', 'baike', 'baikename', 'soundline1', 'soundline2', 'soundline3', 'initial'], 'required'],
            [['profile'], 'string'],
            [['gender', 'birthyear', 'birthmonthday', 'bloodtype', 'career', 'birthmonth', 'birthday', 'mid', 'checked', 'soundline1', 'soundline2', 'soundline3', 'initial'], 'integer'],
            [['name'], 'string', 'max' => 40],
            [['icon'], 'string', 'max' => 100],
            [['group'], 'string', 'max' => 32],
            [['weibo', 'weiboname', 'baike', 'baikename'], 'string', 'max' => 64],
            [['seiyalias'], 'string', 'max' => 60],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Name',
            'icon' => 'Icon',
            'profile' => 'Profile',
            'gender' => 'Gender',
            'birthyear' => 'Birthyear',
            'birthmonthday' => 'Birthmonthday',
            'bloodtype' => '血型',
            'career' => '职业',
            'birthmonth' => 'Birthmonth',
            'birthday' => 'Birthday',
            'group' => '社团',
            'weibo' => '微博',
            'weiboname' => '微博名称',
            'baike' => '百科',
            'baikename' => '百科名称',
            'mid' => 'Mid',
            'checked' => 'Checked',
            'soundline1' => '声线1',
            'soundline2' => '声线2',
            'soundline3' => '声线3',
            'seiyalias' => 'Seiyalias',
            'sound_type' => '声线 每一位代表一个声线。暂没有规定',
            'initial' => '首字母,0代表其他 1到26代表26个字母',
        ];
    }

    public function afterFind()
    {
        parent::afterFind(); // TODO: Change the autogenerated stub

        $this->icon = Yii::$app->params['seiyIconUrl'] . $this->icon;
    }

    /**
     * 搜索声优
     *
     * @param string $s 搜索关键词
     * @param integer $page 所在页
     * @param integer $page_size 每页个数
     * @return array
     */
    public static function getSearch(string $s, int $page, int $page_size): array
    {
        $results = Yii::$app->go->search($s, Discovery::SEARCH_SEIY, $page, $page_size, ['scenario' => Go::SCENARIO_MAIN_SEARCH]);
        return $results;
    }

    public static function getIconUrl(?string $icon)
    {
        return $icon ? Yii::$app->params['seiyIconUrl'] . $icon
            : Yii::$app->params['defaultCoverUrl'];
    }

    /**
     * 获取声优信息
     *
     * @param array $cv_ids 声优 IDs
     * @return array
     */
    public static function getCvs(array $cv_ids)
    {
        $cvs = self::find()->select('id, icon, name, group, mid AS user_id')
            ->where(['id' => $cv_ids])->asArray()->all();
        return array_map(function ($item) {
            $item['id'] = (int)$item['id'];
            $item['user_id'] = (int)$item['user_id'];
            $item['icon'] = self::getIconUrl($item['icon']);
            return $item;
        }, $cvs);
    }

    /**
     * 处理声优个人信息资料（将饰演角色与头像，昵称等组合）
     *
     * @param array $cv_infos 剧集中声优角色
     */
    public static function processCVDetails(&$cv_infos)
    {
        $cv_ids = array_column($cv_infos, 'cv_id');
        $cvs_details = self::getCvs($cv_ids);
        foreach ($cv_infos as &$cv_info) {
            $cv_info['cvinfo'] = null;
            foreach ($cvs_details as $cv_detail) {
                if ($cv_info['cv_id'] === $cv_detail['id']) {
                    $cv_info['cvinfo'] = $cv_detail;
                    break;
                }
            }
        }
        $cv_infos = array_values(array_filter($cv_infos, function ($item) {
            return null !== $item['cvinfo'];
        }));
    }

    /**
     * 获取搜索联想词
     *
     * @param string $s 搜索的联想词
     * @param int $count 联想词数量
     * @return array
     */
    public static function getSearchSuggest(string $s, int $count = Discovery::SUGGEST_COUNT): array
    {
        return Yii::$app->go->suggest($s, Discovery::SEARCH_SEIY, $count);
    }

    /**
     * 置顶正在直播且热度在前两位的声优 \
     * 置顶规则：先按参演角色：主役、协役、龙套的顺序排序
     *   角色类型相同的情况下，按直播间热度降序排序
     *
     * @param array $cvs 声优信息 \
     * [{
     *     id: int,
     *     episode_id: int,
     *     cv_id: int,
     *     character: string,
     *     main: int,
     *     drama_id: int,
     *     cvinfo: {
     *         id: int,
     *         user_id: int,
     *         icon: string,
     *         name: string,
     *         group: string,
     *     }
     * }]
     * @param int $top_count 置顶正在直播的声优数量
     * @return array 置顶后的声优信息 \
     * [{
     *     id: int,
     *     episode_id: int,
     *     cv_id: int,
     *     character: string,
     *     main: int,
     *     drama_id: int,
     *     cvinfo: {
     *         id: int,
     *         user_id: int,
     *         icon: string,
     *         name: string,
     *         group: string,
     *     },
     *     live: {
     *         room_id: int,
     *         status: int
     *     }
     * }]
     */
    public static function topLiveCvs(array $cvs, int $top_count = self::MAX_TOP_OPEN_LIVE): array
    {
        if (empty($cvs)) {
            return $cvs;
        }
        $cv_info = array_column($cvs, 'cvinfo');
        if (empty($cv_info)) {
            return $cvs;
        }
        // 获取声优绑定的 M 号（未绑定时，user_id 为 0）
        $cv_user_ids = array_filter(array_unique(array_column($cv_info, 'user_id')));
        if (empty($cv_user_ids)) {
            return $cvs;
        }
        // 获取开播的声优房间号
        $live_info = Live::find()
            ->select('user_id, room_id, status, score')
            ->where(['user_id' => $cv_user_ids, 'status' => Live::STATUS_OPEN])
            ->orderBy(['score' => SORT_DESC])
            ->indexBy('user_id')
            ->asArray()
            ->all();
        if (empty($live_info)) {
            return $cvs;
        }
        // 开播的声优信息
        $live_cvs = [];
        // 写入开播声优的房间信息
        foreach ($cvs as &$cv) {
            $cv_user_id = $cv['cvinfo']['user_id'] ?? 0;
            if ($cv_user_id > 0 && array_key_exists($cv_user_id, $live_info)) {
                $live_cv = $live_info[$cv_user_id];
                $cv['live'] = [
                    'room_id' => (int)$live_cv['room_id'],
                    'status' => (int)$live_cv['status'],
                    'score' => (int)$live_cv['score'],
                ];
                $live_cvs[] = $cv;
            }
        }
        unset($cv);
        // 相同角色类型的声优开播，则按直播热度降序排序
        usort($live_cvs, function ($pre, $next) {
            if ($pre['main'] === $next['main']) {
                return $pre['live']['score'] < $next['live']['score'];
            }
            return $pre['main'] > $next['main'];
        });
        // 获取置顶指定数量的声优信息
        $top_cv_info = array_slice($live_cvs, 0, $top_count);
        $top_cv_ids = array_column($top_cv_info, 'cv_id');
        // 获取除置顶以外的其他声优信息
        $remaining_cv_info = array_filter($cvs, function ($cv) use ($top_cv_ids) {
            return !in_array($cv['cv_id'], $top_cv_ids);
        });
        $cvs = array_merge($top_cv_info, $remaining_cv_info);
        // 过滤不返回的 score 字段
        $cvs = array_map(function ($cv) {
            if (array_key_exists('live', $cv)) {
                unset($cv['live']['score']);
            }
            return $cv;
        }, $cvs);
        return $cvs;
    }
}
