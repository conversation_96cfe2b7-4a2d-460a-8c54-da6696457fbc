<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "free_notice".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 * @property int $role_id 角色 ID，角色 ID 为 0 时为全局提醒数
 * @property int $work_id 作品 ID
 * @property int $notice 提醒数
 * @property string $role_icon 语音包首页角色图标
 */
class FreeNotice extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'free_notice';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db2');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'role_id', 'work_id', 'role_icon'], 'required'],
            [['id', 'create_time', 'modified_time', 'role_id', 'work_id', 'notice'], 'integer'],
            [['role_icon'], 'string', 'max' => 120],
            [['id'], 'unique'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => 'Create Time',
            'modified_time' => 'Modified Time',
            'role_id' => 'Role ID',
            'work_id' => 'Work ID',
            'notice' => 'Notice',
            'role_icon' => 'Role Icon',
        ];
    }

    public static function getSum($work_id)
    {
        // role_id 为 0 时 notice 为作品的所有免费音提醒数
        return (int)self::find()->select('notice')
            ->where(['work_id' => $work_id, 'role_id' => 0])->scalar();
    }

    public static function getWorkNotices($work_id)
    {
        $work_notice = self::find()->select('modified_time, modified_time AS last_time, role_id, notice, role_icon')
            ->where('work_id = :work_id AND role_id <> :role_id', [':work_id' => $work_id, ':role_id' => 0])
            ->orderBy('modified_time DESC')->asArray()->all();
        return array_map(function ($item) {
            $item['last_time'] = (int)$item['last_time'];
            $item['role_id'] = (int)$item['role_id'];
            $item['notice'] = (int)$item['notice'];
            return $item;
        }, $work_notice);
    }

    public static function getUnreadNotice($work_id, $user_id)
    {
        $free_notice = self::getWorkNotices($work_id);
        $free_notice_listened = FreeNoticeListened::getHistoryByWork($work_id, $user_id,
            FreeNoticeListened::HAS_NOTICE_YES);
        $counts_listend = array_count_values(array_column($free_notice_listened, 'role_id'));

        $unread_notice = array_map(function ($item) use ($counts_listend) {
            $item['notice'] -= ($counts_listend[$item['role_id']] ?? 0);
            if ($item['notice'] < 0) $item['notice'] = 0;
            return $item;
        }, $free_notice);
        return $unread_notice;
    }

    public static function getFreeNotNotice($role_ids, $user_id)
    {
        $not_notice = Card::getFreeNotNoticeCard($role_ids);
        $listened = FreeNoticeListened::getListenedCards(array_column($not_notice, 'card_id'), $user_id);

        $result = array_reduce($not_notice, function ($result, $item) use ($listened) {
            $role_id = $item['role_id'];
            $result[$role_id] = $result[$role_id] ?? [
                'role_id' => $role_id,
                'notice' => 0,
                'last_time' => $item['last_time'],
            ];

            if ($result[$role_id]['last_time'] < $item['last_time']) {
                $result[$role_id]['last_time'] = $item['last_time'];
            }
            if (!in_array($item['card_id'], $listened)) {
                $result[$role_id]['notice'] += 1;
            }
            return $result;
        }, []);
        return array_values($result);
    }

    public static function getUnreadFreeCardNotNotice($role_ids, $user_id)
    {
        $not_notice = Card::getFreeNotNoticeCard($role_ids);
        $listened = FreeNoticeListened::getListenedCards(array_column($not_notice, 'card_id'), $user_id);
        $cards = array_map(function ($item) use ($listened) {
            if (in_array($item['card_id'], $listened)) return null;
            return [
                'id' => $item['card_id'],
                'title' => $item['title'],
                'level' => $item['level'],
                'last_time' => $item['last_time'],
            ];
        }, $not_notice);
        $cards = array_values(array_filter($cards));
        return $cards;
    }

}
