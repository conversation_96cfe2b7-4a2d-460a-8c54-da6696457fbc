<?php

namespace app\models;

use app\components\util\MUtils;
use Yii;
use yii\db\Expression;

/**
 * This is the model class for table "m_tag_sound_map".
 *
 * @property integer $tag_id
 * @property integer $sound_id
 */
class MTagSoundMap extends ActiveRecord
{
    // 频道中音频按热度进行排序
    const ORDER_BY_HOT = 3;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_tag_sound_map';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['tag_id', 'sound_id'], 'required'],
            [['tag_id', 'sound_id'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'tag_id' => '标签ID',
            'sound_id' => '声音ID',
        ];
    }

    //根据标签获取单音分页信息
    public static function getTagSound($tag_id, $page_size = 10, $order = 0)
    {
        // 查询音频信息（此处查询音频地址用于分享）
        $query = MSound::find()->alias('t')
            ->select('t.id, t.username, t.cover_image, t.soundstr, t.checked, '
                . 't.duration, t.view_count, t.intro, t.refined, t.download')
            ->where('tag_id = :tag_id')
            ->andWhere('NOT refined & ' . MSound::REFINED_BLOCK)
            ->leftJoin('m_tag_sound_map t1', 't.id = t1.sound_id')
            ->addParams([':tag_id' => $tag_id]);

        if (!Yii::$app->user->isExam) {
            $query->andWhere('checked = 1');
        } elseif (Yii::$app->user->isLimited) {
            $catalog_ids_limited = implode(',', Catalog::LIMITED_CATALOG_IDS);
            $checked = sprintf('checked = 1 OR (checked = 2 AND catalog_id NOT IN (%s))', $catalog_ids_limited);
            $query->andWhere($checked);
        } else {
            $query->andWhere('checked IN (1, 2)');
        }
        switch ($order) {
            case 1:
                // 按播放量进行排序时，若音频为报警音（即播放量对用户显示为 0），也按真实的播放量进行排序
                $query->orderBy('view_count DESC');
                break;
            case 2:
                $query->orderBy('point DESC');
                break;
            case 3:
                $query->orderBy(new Expression('uptimes + favorite_count + point/4 DESC'));
                break;
            case 4:
                $query->orderBy('favorite_count DESC');
                break;
            case 5:
                $query->orderBy('comment_count DESC');
                break;
            case 6:
                $query->orderBy('uptimes DESC');
                break;
            default:
                $query->orderBy('create_time DESC');
        }
        $return_model = MUtils::getPaginationModels($query, $page_size);

        if ($return_model->Datas) {
            $return_model->Datas = array_map(function ($sound) {
                $data = [
                    'id' => $sound->id,
                    'username' => $sound->username,
                    'front_cover' => $sound->front_cover,
                    'soundstr' => $sound->soundstr,
                    'duration' => $sound->duration,
                    'view_count' => $sound->view_count,
                    'intro' => $sound->intro,
                    'refined' => $sound->refined,
                    'download' => $sound->download,
                ];
                return $data;
            }, $return_model->Datas);
        }
        return $return_model;
    }
}
