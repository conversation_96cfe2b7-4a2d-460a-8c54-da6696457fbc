<?php

namespace app\models;

use missevan\storage\StorageClient;
use Yii;

/**
 * This is the model class for table "role".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 * @property string $name 角色名
 * @property string $icon 语音包首页角色图标
 * @property string $cover 角色列表页角色图片
 * @property string $cover_detail 角色详情页顶部图片
 * @property string $intro 角色介绍
 * @property int $sort 排序值
 * @property int $work_id 作品 id，work 表主键
 * @property int $attr 角色属性
 */
class Role extends ActiveRecord
{
    // 角色属性（按位计算）
    public const ATTR_COMMON = 0b00;  // 普通角色
    public const ATTR_SPECIAL = 0b01;  // 特殊角色
    public const ATTR_ADVANCE = 0b10;  // 预告角色

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'role';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db2');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['create_time', 'modified_time', 'sort', 'work_id', 'attr'], 'integer'],
            [['name'], 'string', 'max' => 20],
            [['icon', 'cover', 'cover_detail'], 'string', 'max' => 120],
            [['intro'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'name' => '角色名',
            'icon' => '角色图标',
            'cover' => '列表页角色图片',
            'cover_detail' => '角色详情页图片',
            'intro' => '角色介绍',
            'sort' => '排序值',
            'work_id' => '所属作品',
            'attr' => '特殊角色',
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->icon) $this->icon = StorageClient::getFileUrl($this->icon);
        if ($this->cover) $this->cover = StorageClient::getFileUrl($this->cover);
        if ($this->cover_detail) $this->cover_detail = StorageClient::getFileUrl($this->cover_detail);
    }

    public static function getRolesName(array $role_ids)
    {
        return self::find()->select('name')->where(['id' => $role_ids])
            ->indexBy('id')->column();
    }

    public static function getRoles(array $role_ids)
    {
        return self::find()->select('id, name, icon')->where(['id' => $role_ids])
            ->indexBy('id')->all();
    }

    /**
     * 获取某作品下的角色
     *
     * @param integer $work_id 作品 ID
     * @return array
     */
    public static function getAllRoles($work_id): array
    {
        $roles = self::find()->select('id, name, cover, intro, attr')->where(['work_id' => $work_id])
            ->orderBy(['sort' => SORT_ASC])->asArray()->all();
        return array_map(function ($item) {
            $item['id'] = (int)$item['id'];
            $item['cover'] = StorageClient::getFileUrl($item['cover']);
            // special & 1 != 0 特殊角色的图片的尺寸与普通角色图尺寸不一致，客户端需要用于确定显示图片的间隔
            // special & 2 != 0 为预告角色
            $item['special'] = (int)$item['attr'];
            unset($item['attr']);
            return $item;
        }, $roles);
    }

}
