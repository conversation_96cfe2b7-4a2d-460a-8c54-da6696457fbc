<?php

namespace app\models;

use missevan\util\Logger;
use missevan\util\MUtils as MUtils2;

/**
 * This is the model class for table "guild".
 *
 * @property int $id 主键
 * @property string $name 公会名称
 * @property string $intro 公会简介
 * @property string $owner_name 法人代表姓名
 * @property string $owner_id_number 法人代表身份证号
 * @property string $owner_id_people 法人代表手持身份证正面照
 * @property string $owner_backcover 法人代表身份证背面
 * @property string $mobile 法人代表手机号
 * @property string $email 邮箱
 * @property string $corporation_name 公司名称
 * @property string $corporation_address 公司地址
 * @property string $corporation_phone 公司电话
 * @property string $business_license_number 营业执照号
 * @property string $business_license_frontcover 营业执照扫描件
 * @property string $tax_account 纳税人识别号
 * @property string $bank_account 银行卡号
 * @property string $bank_account_name 银行开户名
 * @property string $bank 开户行
 * @property string $bank_address 开户行地址
 * @property string $bank_branch 开户支行
 * @property int $checked 公会状态（-1 审核驳回，0 审核中，1 审核通过，2 解散）
 * @property int $user_id 公会创建人用户 ID
 * @property int $apply_time 申请时间
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 * @property int $live_num 签约主播数
 */
class Guild extends ActiveRecord
{
    // 公会状态。-1：审核驳回；0：审核中；1：审核通过；2：解散
    const CHECKED_PASS = 1;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'guild';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['name', 'owner_name', 'owner_id_number', 'owner_id_people', 'owner_backcover', 'mobile', 'email', 'corporation_name', 'corporation_address', 'corporation_phone', 'business_license_number', 'business_license_frontcover', 'tax_account', 'bank_account', 'bank_account_name', 'bank', 'bank_address', 'bank_branch', 'user_id', 'create_time', 'modified_time'], 'required'],
            [['invoice_rate', 'type', 'checked', 'user_id', 'apply_time', 'create_time', 'modified_time'], 'integer'],
            [['name'], 'string', 'max' => 50],
            [['intro'], 'string', 'max' => 200],
            [['owner_name', 'owner_id_number', 'owner_id_people', 'owner_backcover', 'mobile', 'email', 'corporation_name', 'corporation_address', 'corporation_phone', 'business_license_number', 'business_license_frontcover', 'tax_account', 'bank_account', 'bank_address', 'bank_branch'], 'string', 'max' => 255],
            [['qq', 'bank_account_name', 'bank'], 'string', 'max' => 20],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => '公会创建人用户 ID',
            'name' => '公会名称',
            'intro' => '公会简介',
            'owner_name' => '法人代表姓名',
            'owner_id_number' => '法人代表身份证号',
            'owner_id_people' => '法人代表手持身份证正面照',
            'owner_backcover' => '法人代表身份证背面',
            'mobile' => '法人代表手机号',
            'email' => '邮箱',
            'corporation_name' => '公司名称',
            'corporation_address' => '公司地址',
            'corporation_phone' => '公司电话',
            'business_license_number' => '营业执照号',
            'business_license_frontcover' => '营业执照扫描件',
            'tax_account' => '纳税人识别号',
            'bank_account' => '银行卡号',
            'bank_account_name' => '银行开户名',
            'bank' => '开户行',
            'bank_address' => '开户行地址',
            'bank_branch' => '开户支行',
            'checked' => '公会状态（-1 审核驳回，0 审核中，1 审核通过，2 解散）',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    /**
     * 获取解密后的公会所属公司名称
     *
     * @return string
     */
    public function getDecryptCorporationName(): string
    {
        $corporation_name = MUtils2::decrypt($this->corporation_name, $this->create_time, SENSITIVE_INFORMATION_KEY);
        if ($corporation_name === false) {
            Logger::errorf('guild info decrypt failed: guild_id[%d], corporation_name[%s]', [$this->id, $this->corporation_name], __METHOD__);
            return '';
        }

        return $corporation_name;
    }

}
