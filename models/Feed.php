<?php

namespace app\models;

abstract class Feed extends ActiveRecord
{
    // 用户及频道的动态
    const FEED_TYPE_USER_CHANNEL = 0;
    // 剧集的动态
    const FEED_TYPE_DRAMA = 1;
    // 用户、频道及剧集的动态
    const FEED_TYPE_USER_CHANNEL_DRAMA = 2;

    /**
     * 获取动态 feed 流（用户、频道或剧集等）
     *
     * @param string|array $condition
     * @param string|array $order
     * @param integer $limit
     * @return array
     */
    abstract public static function getFeed($condition, $order, $limit): array;

    /**
     * 检查动态类型参数
     *
     * @param integer $feed_type
     * @return bool
     */
    public static function checkFeedType(int $feed_type): bool
    {
        $feed_type_array = [
            self::FEED_TYPE_USER_CHANNEL,
            self::FEED_TYPE_DRAMA,
            self::FEED_TYPE_USER_CHANNEL_DRAMA,
        ];
        return in_array($feed_type, $feed_type_array);
    }

}
