<?php

namespace app\models;

use Exception;
use Yii;
use yii\helpers\Json;

class MThirdPartyBaiduMapTask extends MThirdPartyBaiduTask
{
    /**
     * 请求百度地图回调
     *
     * @param string $track_id
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function callback(string $track_id): bool
    {
        $params = [
            'reqId' => self::buildRequestId(),
            'token' => $track_id,
        ];
        $params['sign'] = self::buildSign($params);
        try {
            $data = Yii::$app->tools->requestRemote(self::CALLBACK_URL_BAIDU, [], 'POST', Json::encode($params), 0, [
                'Content-Type' => 'application/json',
            ]);
            if (!$data) {
                throw new Exception('百度地图点击回传失败，返回值为空');
            }
            if ($data['errno'] !== self::BAIDU_REQUEST_SUCCESS_CODE) {
                throw new Exception(sprintf('code[%d], msg[%s]', $data['errno'], $data['errmsg']));
            }
        } catch (Exception $e) {
            Yii::error('百度地图点击回传失败，原因：' . $e->getMessage(), __METHOD__);
            return false;
        }
        return true;
    }
}
