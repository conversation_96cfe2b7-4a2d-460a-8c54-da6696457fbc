<?php

namespace app\models;

use app\components\util\MUtils;
use missevan\storage\StorageClient;
use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "m_user_cover".
 *
 * @property int $id 主键
 * @property int $user_id 用户 ID
 * @property string $cover 封面图
 * @property string $reason 拒绝原因
 * @property int $checked 审核状态（0: 未审核 1: 已通过 2: 已拒绝 3: 已失效）
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 */
class MUserCover extends ActiveRecord
{
    // 封面图完整地址
    public $cover_url;

    // 封面图审核状态 0：未审核；1：审核已通过；2: 审核已拒绝；3: 已失效
    const CHECKED_NO = 0;
    const CHECKED_PASS = 1;
    const CHECKED_DISMISS = 2;
    const CHECKED_INVALID = 3;

    // 默认封面图协议地址
    const DEFAULT_USER_COVER_URL1 = 'oss://usercover/background01.png';  // 默认图 1
    const DEFAULT_USER_COVER_URL2 = 'oss://usercover/background02.png';  // 默认图 2
    const DEFAULT_USER_COVER_URL3 = 'oss://usercover/background03.png';  // 默认图 3 白天模式图
    const DEFAULT_USER_COVER_URL3_DARK = 'oss://usercover/background03-dark.png';  // 默认图 3 黑夜模式图

    // 封面图位置
    const POSITION_DEFAULT_USER_OTHER = 0;
    const POSITION_DEFAULT_USER_COVER1 = 1;
    const POSITION_DEFAULT_USER_COVER2 = 2;
    const POSITION_DEFAULT_USER_COVER3 = 3;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_user_cover';
    }

    /**
     * 新增或修改封面图
     *
     * @param int $user_id 用户 ID
     * @param string $cover 封面图协议地址
     * @throws HttpException
     */
    public static function changeUserCover(int $user_id, string $cover)
    {
        $user_cover = self::findOne(['user_id' => $user_id, 'checked' => MUserCover::CHECKED_NO]);
        if ($user_cover) {
            // 已有未审核的封面图，则更新未审核的封面图
            $user_cover->cover = $cover;
            $user_cover->checked = self::CHECKED_NO;
        } else {
            $user_cover = new MUserCover(['user_id' => $user_id,
                'cover' => $cover,
                'checked' => self::CHECKED_NO]);
        }
        if (!($user_cover->save())) {
            throw new HttpException(400, MUtils::getFirstError($user_cover));
        }
    }

    /**
     * 获取用户封面图信息
     *
     * @param int $user_id 用户 ID
     * @param bool $is_self 是否是用户自己
     * @param string $cover_new mowangskuser 表 coverurl_new 字段值
     * @return string 封面图 URL
     */
    public static function getUserCoverByUserId(int $user_id, bool $is_self = false, string $cover_new = '')
    {
        if ($is_self) {
            // 未审核的封面图仅对用户自己可见
            $user_cover = self::find()
                ->select('cover')
                ->where(['user_id' => $user_id, 'checked' => MUserCover::CHECKED_NO])
                ->limit(1)->one();
            if ($user_cover) {
                return $user_cover->cover_url;
            }
            if (strpos($cover_new, Yii::$app->storage->protocolUrl) === 0) {
                // mowangskuser 表 coverurl_new 字段值是协议地址，说明是审核通过的封面图
                return StorageClient::getFileUrl($cover_new);
            }
            if ($cover_new !== '') {
                return Yii::$app->params['userCoverUrl'] . $cover_new;
            }
            return Yii::$app->params['defaultUserCoverUrl'];
        } else {
            // 不是用户本人
            if (strpos($cover_new, Yii::$app->storage->protocolUrl) === 0) {
                // mowangskuser 表 coverurl_new 字段值是协议地址，说明是审核通过的封面图
                return StorageClient::getFileUrl($cover_new);
            }
            return Yii::$app->params['defaultUserCoverUrl'];
        }
    }

    /**
     * 根据用户封面图获取封面图位置和黑夜模式封面图
     *
     * @param string $cover_url 最终的用户封面图 URL
     * @return array 封面图位置和黑夜模式封面图数组
     */
    public static function getDarkCoverAndPositionByCoverUrl(string $cover_url)
    {
        switch ($cover_url) {
            case StorageClient::getFileUrl(MUserCover::DEFAULT_USER_COVER_URL1):
                $cover_id = MUserCover::POSITION_DEFAULT_USER_COVER1;
                $dark_cover_url = '';
                break;
            case StorageClient::getFileUrl(MUserCover::DEFAULT_USER_COVER_URL2):
                $cover_id = MUserCover::POSITION_DEFAULT_USER_COVER2;
                $dark_cover_url = '';
                break;
            case StorageClient::getFileUrl(MUserCover::DEFAULT_USER_COVER_URL3):
                $cover_id = MUserCover::POSITION_DEFAULT_USER_COVER3;
                // 当用户设置默认图 3 时，需要同时下发黑夜模式默认图
                $dark_cover_url = StorageClient::getFileUrl(MUserCover::DEFAULT_USER_COVER_URL3_DARK);
                break;
            default:
                $cover_id = MUserCover::POSITION_DEFAULT_USER_OTHER;
                $dark_cover_url = '';
        }
        return ['cover_id' => $cover_id, 'dark_cover_url' => $dark_cover_url];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['cover'], 'required'],
            [['id', 'user_id', 'checked', 'create_time', 'modified_time'], 'integer'],
            [['cover'], 'string', 'max' => 100],
            [['reason'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'user_id' => '用户 ID',
            'cover' => '封面图',
            'checked' => '审核状态',  // 0: 未审核 1: 已通过 2: 已拒绝 3: 已失效
            'reason' => '拒绝原因',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    /**
     * 入库前自动处理
     *
     * @return boolean
     */
    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->cover) {
            $this->cover_url = StorageClient::getFileUrl($this->cover);
        }
    }
}
