<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "m_power_sound_ip".
 *
 * @property int $id primary key
 * @property int $create_time create time
 * @property int $modified_time modify time
 * @property string $ip_name ip 名
 * @property int $sound_num 单音数量
 * @property int $archive 是否归档（1 为是，0 为否）
 * @property int $sort_order 排序（从大到小）
 * @property int $attr IPR 属性（按位处理）比特位第一位为 1：不支持默认选中
 */
class MPowerSoundIp extends ActiveRecord
{
    const ARCHIVE_ONLINE = 0;
    const ARCHIVE_ARCHIVED = 1;

    // IPR 属性
    const ATTR_UNSUPPORTED_DEFAULT_SELECTION = 0b01;  // 不支持默认选中

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_power_sound_ip';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['create_time', 'modified_time', 'sound_num', 'archive', 'sort_order', 'sort_order', 'attr'], 'integer'],
            [['ip_name'], 'required'],
            [['ip_name'], 'string', 'max' => 100],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'primary key',
            'create_time' => 'create time',
            'modified_time' => 'modify time',
            'ip_name' => 'ip 名',
            'sound_num' => '单音数量',
            'archive' => '是否归档（1 为是，0 为否）',
            'sort_order' => '排序（从大到小）',
            'attr' => 'IPR 属性',  // （按位处理）比特位第一位为 1：不支持默认选中
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * IPR 是否支持默认选中
     *
     * @param int $attr 属性
     * @return bool true：是；false：否
     */
    public static function isSupportDefaultSelection(int $attr): bool
    {
        return ($attr & self::ATTR_UNSUPPORTED_DEFAULT_SELECTION) !== self::ATTR_UNSUPPORTED_DEFAULT_SELECTION;
    }
}
