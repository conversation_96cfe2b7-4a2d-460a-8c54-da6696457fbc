<?php

namespace app\models;

use app\components\util\Equipment;
use Exception;
use missevan\storage\StorageClient;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\db\Expression;
use yii\helpers\Json;

/**
 * This is the model class for table "m_video_card".
 *
 * @property integer $id 主键
 * @property integer $create_time 创建时间（单位：秒）
 * @property integer $modified_time 修改时间（单位：秒）
 * @property integer $delete_time 删除时间（单位：秒）
 * @property string $title 标题
 * @property integer $sound_id 音频 ID
 * @property string $url 跳转链接
 * @property string $cover 封面图
 * @property integer $start_time 上线时间（单位：秒）
 * @property integer $end_time 下线时间（单位：秒）
 * @property string $more 更多信息，文档地址：https://info.missevan.com/pages/viewpage.action?pageId=90796670
 * @property integer $type 类型，1：普通视频大卡；2：游戏视频大卡
 */
class MVideoCard extends ActiveRecord
{
    const TYPE_NORMAL = 1;  // 普通视频大卡
    const TYPE_GAME = 2;  // 游戏视频大卡

    // 视频大卡下发位置
    const POSITION_1 = 1;  // “猜你喜欢”模块下方
    const POSITION_2 = 2;  // 第一个自定义推荐模块下方

    /**
     * {@inheritdoc}
     */
    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_video_card';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title', 'sound_id', 'cover', 'start_time', 'end_time', 'more', 'type'], 'required'],
            [['id', 'create_time', 'modified_time', 'delete_time', 'sound_id', 'start_time', 'end_time', 'type'], 'integer'],
            [['title', 'url', 'cover'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'delete_time' => '删除时间',
            'title' => '标题',
            'sound_id' => '音频 ID',  // 绑定视频的音频 ID
            'url' => '跳转链接',
            'cover' => '封面图',
            'start_time' => '上线时间',
            'end_time' => '下线时间',
            'more' => '更多信息',  // 更多信息，文档地址：https://info.missevan.com/pages/viewpage.action?pageId=90796670
            'type' => '类型'  // 1：普通视频大卡；2：游戏视频大卡
        ];
    }

    /**
     * 入库前自动处理
     *
     * @return boolean
     */
    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 获取视频大卡池
     *
     * @param int $persona_id 用户画像 ID
     * @return array
     * @throws Exception
     */
    public static function getVideoCardPool(int $persona_id)
    {
        $memcache = Yii::$app->memcache;
        $key = MUtils2::generateCacheKey(KEY_VIDEO_CARD_POOL, $persona_id);
        if ($video_card_pool = $memcache->get($key)) {
            $video_card_pool = Json::decode($video_card_pool);
        } else {
            $time = $_SERVER['REQUEST_TIME'];
            // 获取上线中的视频大卡列表
            $more_expression = new Expression(
                // JSON_CONTAINS 方法的第二个参数必须为 JSON 字符串，否则 SQL 执行会报错
                // 参考文档：https://dev.mysql.com/doc/refman/8.0/en/json-search-functions.html#function_json-contains
                'JSON_CONTAINS(more, :persona_id, "$.persona_ids")', [':persona_id' => (string)$persona_id]
            );
            $video_card_list = self::find()
                ->select('id, title, sound_id, url, cover, end_time, more, type')
                ->where('start_time <= :now_time AND end_time > :now_time AND delete_time = 0',
                    [':now_time' => $time])
                ->andWhere($more_expression)
                ->orderBy(['end_time' => SORT_ASC])
                ->all();
            // 若没有上线中的视频大卡则缓存空缓存，保底缓存时间为 1 分钟
            $duration = ONE_MINUTE;
            $min_end_time = 0;
            $video_card_pool = [];
            if (!empty($video_card_list)) {
                $sound_ids = array_unique(array_column($video_card_list, 'sound_id'));
                $video_info_list = SoundVideo::getVideoInfoBySoundIds($sound_ids);
                $error_sound_ids = [];
                if (empty($video_info_list)) {
                    $error_sound_ids = $sound_ids;
                }

                // 查询游戏信息
                $game_ids = [];
                foreach ($video_card_list as $card) {
                    if ($card['type'] === self::TYPE_GAME) {
                        if (!isset($card['more']['game']['id'])) {
                            Yii::error("游戏视频大卡游戏 ID 不存在，card_id: $card->id", __METHOD__);
                            continue;
                        }
                        $game_ids[] = $card['more']['game']['id'];
                    }
                }

                $game_info_map = [];
                if (!empty($game_ids)) {
                    // 游戏 ID 数量较少
                    $game_ids = array_values(array_unique(array_filter($game_ids)));
                    $game_info_map = MGameCenter::find()
                        ->where(['id' => $game_ids])
                        ->indexBy('id')
                        ->all();
                }
                foreach ($video_card_list as $item) {
                    if (!isset($video_info_list[$item->sound_id])) {
                        $error_sound_ids[] = $item->sound_id;
                        continue;
                    } else {
                        if (!$min_end_time) {
                            // 即将下线的大卡的结束时间
                            // $video_card_list 已经按 end_time 倒序排序，所以第一个元素的的 end_time 是最小时间
                            $min_end_time = $item->end_time;
                        }
                        $data = [
                            'id' => $item->id,
                            'type' => $item->type,
                            'title' => $item->title,
                            'sound_id' => $item->sound_id,
                            'url' => $item->url,
                            'cover' => $item->cover,
                            'priority' => $video_info_list[$item->sound_id]['priority'],
                            'duration' => $video_info_list[$item->sound_id]['duration'],
                            'resources' => $video_info_list[$item->sound_id]['resources'],
                            'position' => $item['more']['position'] ?? self::POSITION_2
                        ];
                        if ($item->type === self::TYPE_GAME) {
                            if (!isset($game_info_map[$item['more']['game']['id']])) {
                                Yii::error("游戏视频大卡游戏 ID 不存在或未查到游戏信息，card_id: $card->id", __METHOD__);
                                continue;
                            }
                            $game = $game_info_map[$item['more']['game']['id']];
                            $os = Yii::$app->equip->getOs();
                            $download_query = "?game_id={$game->id}&os={$os}";
                            $data['game_card'] = [
                                'id' => $game->id,
                                'name' => $game->name,
                                'url' => $game->url,
                                'icon' => StorageClient::getFileUrl($game->icon),
                                'title' => $item->title,  // 游戏预约卡片标题，游戏预约卡片标题与视频标题相同
                                'subtitle' => $item['more']['game']['subtitle'] ?? '',
                                'download_url' => Yii::$app->params['domainMissevan'] . '/x/gamecenter/download'
                                    . $download_query,
                                'package_name' => $game->extended_fields['package_name'] ?? '',
                                'package_version_code' => $game->extended_fields['package_version_code'] ?? 0,
                            ];
                        }
                        $video_card_pool[] = $data;
                    }
                }
                if (!empty($error_sound_ids)) {
                    $error_sound_id_str = implode(',', $error_sound_ids);
                    Yii::error('配置了未审核通过或付费的视频大卡，音频 ID: ' . $error_sound_id_str,
                        __METHOD__);
                }
            }
            // 获取最近要上线的视频大卡的开始时间
            $next_start_time = (int)self::find()
                ->select('start_time')
                ->where('start_time > :now_time AND delete_time = 0',
                    [':now_time' => $_SERVER['REQUEST_TIME']])
                ->orderBy(['start_time' => SORT_ASC])
                ->scalar();
            if ($next_start_time > 0 && $min_end_time > 0) {
                // 有即将要下线的大卡和即将要上线的大卡，缓存时间为两者的最小值
                $duration = min($next_start_time, $min_end_time) - $time;
            } elseif ($next_start_time > 0 && $min_end_time === 0) {
                // 只有即将要上线的大卡，没有即将要下线的大卡（当前没有上线中的大卡），缓存时间为即将要上线的大卡时间
                $duration = $next_start_time - $time;
            } elseif ($next_start_time === 0 && $min_end_time > 0) {
                // 只有即将要下线的大卡，没有即将要上线的大卡，缓存时间为即将要下线的大卡时间
                $duration = $min_end_time - $time;
            }
            if ($duration <= 0) {
                // 避免设置负数值，负数值 MemCache Set 会转换成永久不过期
                // 设置 1 秒的过期时间，避免在缓存失效的那个时间点产生调用峰值的问题
                $duration = 1;
            } elseif ($duration > FIVE_MINUTE) {
                // 最大缓存时间为五分钟
                $duration = FIVE_MINUTE;
            }
            $memcache->set($key, Json::encode($video_card_pool), $duration);
        }
        $return_video_card_pool = [
            MVideoCard::POSITION_1 => [],
            MVideoCard::POSITION_2 => []
        ];
        foreach ($video_card_pool as $card) {
            $position = $card['position'];
            unset($card['position']);
            $return_video_card_pool[$position][] = $card;
        }
        return $return_video_card_pool;
    }
}
