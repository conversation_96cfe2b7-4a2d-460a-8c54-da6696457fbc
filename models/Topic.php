<?php

namespace app\models;

use app\components\util\MUtils;
use missevan\storage\StorageClient;
use Yii;

/**
 * This is the model class for table "topic".
 *
 * @property integer $id
 * @property string $title
 * @property string $pic_url
 * @property string $mobile_pic_url
 * @property string $html_url
 * @property integer $create_time
 * @property integer $comment_num
 * @property integer $status
 * @property integer $modified_time
 */
class Topic extends ActiveRecord
{
    // 已发布
    const STATUS_PUBLISHED = 1;
    // 隐藏专题（列表页隐藏）
    const STATUS_LIST_HIDDEN = 8;

    public $url;
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'topic';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['title'], 'required'],
            [['create_time', 'comment_num'], 'integer'],
            [['title', 'pic_url', 'mobile_pic_url', 'html_url'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '专题 ID',
            'title' => '标题',
            'pic_url' => '封面图片',
            'mobile_pic_url' => '手机端封面图片',
            'html_url' => '页面地址',
            'create_time' => '创建时间',
            'comment_num' => '评论数',
            'status' => '状态', // 0：未发布；1：已发布
            'modified_time' => '更新时间',
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        $this->url = Yii::$app->params['topicUrl'] . $this->id;
        if ($this->pic_url && !MUtils::hasHttpScheme($this->pic_url)) {
            // WORKAROUND: 此处判断条件用于兼容数据库中老的图片地址数据，待数据刷新成统一格式后可去掉此条件
            $this->pic_url = StorageClient::getFileUrl($this->pic_url);
        }
        if ($this->mobile_pic_url && !MUtils::hasHttpScheme($this->mobile_pic_url)) {
            // WORKAROUND: 此处判断条件用于兼容数据库中老的图片地址数据，待数据刷新成统一格式后可去掉此条件
            $this->mobile_pic_url = StorageClient::getFileUrl($this->mobile_pic_url);
        }
    }
}
