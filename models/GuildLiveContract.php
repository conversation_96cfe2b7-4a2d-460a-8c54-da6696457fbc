<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "guild_live_contract".
 *
 * @property int $id 主键
 * @property int $guild_id 公会 ID
 * @property int $live_id 主播 ID
 * @property int $contract_start 合约起始时间
 * @property int $contract_end 合约结束时间
 * @property double $rate 主播所占分成比例
 * @property string $kpi 指标
 * @property int $type 签约类型。1：主播申请；2：公会邀约
 * @property int $status 合约状态。-3：已解约；-2：已失效；-1：已拒绝；0：申请/邀约中；1：签约成功
 * @property int $create_time 创建时间
 * @property int $guild_owner 会长 ID
 * @property string $guild_name 公会名称
 * @property int $modified_time 修改时间
 */
class GuildLiveContract extends ActiveRecord
{
    // 合约状态，-3：已解约；-2：已失效；-1：已拒绝；0：申请/邀约中；
    // 1：合约生效中（主播申请入会通过/公会邀约通过，主播申请退会/公会发起解约过程中及被拒绝后）；
    // 2：合约生效中（主播申请退会、公会发起解约）
    const STATUS_USELESS = -2;
    const STATUS_INVALID = -1;
    const STATUS_PENDING = 0;
    const STATUS_CONTRACTION = 1;
    const STATUS_TERMINATION = 2;

    // 合约生效状态
    const CONTRACT_EFFECTIVE = [self::STATUS_CONTRACTION, self::STATUS_TERMINATION];

    // 签约类型，1：主播申请；2：公会邀约；
    const TYPE_APPLY = 1;
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'guild_live_contract';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['guild_id', 'live_id', 'rate', 'type', 'status'], 'required'],
            [['guild_id', 'live_id', 'contract_start', 'contract_end', 'status', 'create_time', 'modified_time'], 'integer'],
            [['rate'], 'number'],
            [['kpi'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'guild_id' => '公会 ID',
            'live_id' => '主播的用户 ID',
            'contract_start' => '合约起始时间',
            'contract_end' => '合约结束时间',
            'rate' => '主播所占分成比例',
            'kpi' => '指标',
            'type' => '签约类型',
            'status' => '合约状态',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    public static function getGuildId(int $creator_id)
    {
        return (int)self::find()
            ->select('guild_id')
            ->where(['live_id' => $creator_id, 'status' => self::CONTRACT_EFFECTIVE])
            ->limit(1)
            ->scalar();
    }

}
