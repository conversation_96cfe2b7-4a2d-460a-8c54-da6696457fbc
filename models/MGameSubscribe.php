<?php

namespace app\models;

use missevan\util\MUtils as MUtils2;
use Yii;
/**
 * This is the model class for table "m_game_subscribe".
 *
 * @property integer $id 主键
 * @property integer $game_id 游戏 ID
 * @property integer $user_id 用户 ID
 * @property string $ip 用户 IP
 * @property integer $create_time 创建时间
 * @property integer $modified_time 更新时间
 * @property integer $delete_time 删除时间
 * @property integer $os 预约的设备类型 0：其他；1：Android；2：iOS；3：Web；6：HarmonyOS
 */
class MGameSubscribe extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_game_subscribe';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['game_id', 'user_id'], 'required'],
            [['game_id', 'user_id', 'create_time', 'modified_time', 'delete_time'], 'integer'],
            [['ip'], 'string', 'max' => MUtils2::IP_MAX_LENGTH],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'game_id' => '游戏 ID',
            'user_id' => '用户 ID',
            'ip' => '用户 IP',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'delete_time' => '删除时间',
            'os' => '预约的设备类型',  // 0：其他；1：Android；2：iOS；3：Web；6：HarmonyOS
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
            $this->ip = Yii::$app->request->userIP;
            $this->os = Yii::$app->equip->getOs();
        }
        $this->modified_time = $time;
        return true;
    }
}
