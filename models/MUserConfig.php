<?php

namespace app\models;

use app\components\util\Equipment;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "m_user_config".
 *
 * @property string $id 主键
 * @property string $create_time 创建时间
 * @property string $modified_time 更新时间
 * @property string $user_id 用户 ID
 * @property string $buvid 设备号
 * @property array $app_config APP 设置，json 字符串
 */
class MUserConfig extends ActiveRecord
{
    // APP 是否开启个性化推荐 type 名称
    const APP_CONF_TYPE_PERSONALIZED_RECOMMEND = 'personalized_recommend';
    // APP 是否在个人主页公开“我的追剧”
    const APP_CONF_TYPE_SHOW_SUBSCRIBE_DRAMA = 'show_subscribe_drama';
    // APP 是否在个人主页公开“我的收藏”
    const APP_CONF_TYPE_SHOW_USER_COLLECT = 'show_user_collect';
    // APP 用户消息推送设置
    const APP_CONF_TYPE_MESSAGE_NOTIFICATION = 'message_notification';
    // @ 我
    const MESSAGE_NOTIFICATION_AT_ME = 'at_me';
    // 点赞
    const MESSAGE_NOTIFICATION_LIKE = 'like';
    // 评论
    const MESSAGE_NOTIFICATION_COMMENT = 'comment';
    // 私信
    const MESSAGE_NOTIFICATION_PRIVATE_MESSAGE = 'private_message';
    // 主播开播提醒
    const MESSAGE_NOTIFICATION_LIVE = 'live';
    // 推送我可能感兴趣的内容
    const MESSAGE_NOTIFICATION_INTEREST_RECOMMEND = 'interest_recommend';

    // 用户 APP 配置项是否开启
    const APP_CONF_DISABLE = 0;  // 关闭
    const APP_CONF_ENABLE = 1;  // 开启

    // 游客用户 ID
    const USER_GUEST_ID = 0;

    // 用户 APP 配置项默认配置
    const APP_DEFAULT_USER_CONF = [
        self::APP_CONF_TYPE_SHOW_SUBSCRIBE_DRAMA => self::APP_CONF_ENABLE,
        self::APP_CONF_TYPE_SHOW_USER_COLLECT => self::APP_CONF_ENABLE,
        self::APP_CONF_TYPE_MESSAGE_NOTIFICATION => self::APP_CONF_TYPE_MESSAGE_NOTIFICATION_CONF,
    ];

    // 用户消息推送配置项默认配置
    const APP_CONF_TYPE_MESSAGE_NOTIFICATION_CONF = [
        self::MESSAGE_NOTIFICATION_AT_ME => self::APP_CONF_ENABLE,
        self::MESSAGE_NOTIFICATION_LIKE => self::APP_CONF_ENABLE,
        self::MESSAGE_NOTIFICATION_COMMENT => self::APP_CONF_ENABLE,
        self::MESSAGE_NOTIFICATION_PRIVATE_MESSAGE => self::APP_CONF_ENABLE,
        self::MESSAGE_NOTIFICATION_LIVE => self::APP_CONF_ENABLE,
        self::MESSAGE_NOTIFICATION_INTEREST_RECOMMEND => self::APP_CONF_ENABLE,
    ];

    // 允许默认开启私信的用户的注册时间: 2023-04-30 00:00:00
    const MESSAGE_NOTIFICATION_PRIVATE_MESSAGE_ENABLE_TIME = 1682784000;

    // 设备配置项默认配置
    const APP_DEFAULT_EQUIP_CONF = [
        self::APP_CONF_TYPE_PERSONALIZED_RECOMMEND => self::APP_CONF_ENABLE,
    ];

    public static $default_conf = [];

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_user_config';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['create_time', 'modified_time', 'user_id'], 'integer'],
            [['buvid'], 'string', 'max' => 64],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'user_id' => '用户 ID',
            'buvid' => '设备号',
            'app_config' => 'APP 设置',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 获取用户 APP 配置项默认配置
     *
     * @param int $user_id 用户 ID
     * @return array
     */
    public static function getDefaultAppConfig(int $user_id): array
    {
        if ($user_id) {
            $app_user_conf = self::APP_DEFAULT_USER_CONF;
            if (Equipment::isAppOlderThan('4.9.8', '5.8.2')
                    || (Yii::$app->user->registerAt < self::MESSAGE_NOTIFICATION_PRIVATE_MESSAGE_ENABLE_TIME)) {
                // WORKAROUND: 注册时间小于生效时间或 iOS < 4.9.8 或安卓 < 5.8.2 时关闭私信推送
                $app_user_conf[self::APP_CONF_TYPE_MESSAGE_NOTIFICATION][self::MESSAGE_NOTIFICATION_PRIVATE_MESSAGE] = self::APP_CONF_DISABLE;
            }
            self::$default_conf = array_merge($app_user_conf, self::APP_DEFAULT_EQUIP_CONF);
        } else {
            self::$default_conf = self::APP_DEFAULT_EQUIP_CONF;
        }
        return self::$default_conf;
    }

    /**
     * 创建新的用户配置对象
     *
     * @param int $user_id 用户 ID
     * @param string|null $buvid 设备号
     * @return self
     */
    public static function newUserConfig(int $user_id, ?string $buvid): self
    {
        $buvid = $buvid ?? '';
        $user_config = new self();
        if ($user_id) {
            // 若登录，则 buvid 存空字符串
            $user_config->user_id = $user_id;
            $user_config->buvid = '';
        } else {
            // 若未登录，则 user_id 存 0
            $user_config->user_id = self::USER_GUEST_ID;
            $user_config->buvid = $buvid;
        }
        $user_config->app_config = self::getDefaultAppConfig($user_id);
        return $user_config;
    }

    /**
     * 获取用户配置
     *
     * @param int $user_id 用户 ID
     * @param string|null $buvid 设备号
     * @return self
     */
    public static function getUserConfig(int $user_id, ?string $buvid = null): self
    {
        if (!$user_id && !$buvid) {
            // WORKAROUND: 若未登录且无 buvid，则返回默认用户配置，之后都能取到 buvid 时该兼容可去掉
            return self::newUserConfig($user_id, $buvid);
        }
        // 登录时查找用户的配置，否则查找设备未登录前的配置
        $condition = $user_id ? ['user_id' => $user_id, 'buvid' => ''] : ['user_id' => 0, 'buvid' => $buvid];
        $user_config = self::find()->where($condition)->one();
        if (!$user_config) {
            $user_config = self::newUserConfig($user_id, $buvid);
        } else {
            // 若配置记录已存在，则补全配置项，避免后续新增的配置项在老记录中不存在的情况
            $user_config->app_config = array_merge(self::getDefaultAppConfig($user_id), $user_config->app_config);
        }
        return $user_config;
    }

    /**
     * 判断 APP 相关配置项是否开启
     *
     * @param int $user_id 用户 ID
     * @param string|null $buvid 设备号
     * @param string $type 配置项名
     * @return bool
     * @throws \Exception 传入不存在的配置项时抛出异常
     */
    public static function isAppConfigEnable(int $user_id, ?string $buvid, string $type): bool
    {
        $user_config = MUserConfig::getUserConfig($user_id, $buvid);
        if (!key_exists($type, $user_config->app_config)) {
            throw new \Exception("isEnable 参数错误，type: {$type}");
        }
        return $user_config->app_config[$type] === self::APP_CONF_ENABLE;
    }
}
