<?php
/**
 * Created by PhpStorm.
 * User: Tom<PERSON>ao
 * Date: 2018/12/5
 * Time: 2:49 PM
 */

namespace app\models;

use app\components\service\biliai\RecommendItem;
use app\components\service\biliai\RecommendParams;
use app\components\service\biliai\TianMa;
use app\components\util\Equipment;
use Exception;
use Yii;
use yii\helpers\Json;
use yii\web\HttpException;

abstract class RecommendBase extends ActiveRecord
{
    // App 首页“猜你喜欢”音个数
    public const RECOMMEND_SOUNDS_NUM = 6;
    // “猜你喜欢”音推荐方案。0：默认方案；2：按播放时间降权的方案；3：使用 `recommend3` 作为推荐数据源的方案
    public const STRATEGY_DEFAULT = 0;
    public const STRATEGY_WEIGHT_BY_TIME = 2;
    public const STRATEGY_3 = 3;

    // 元素类型，与 app_missevan.m_recommended_elements 表推荐元素类型一致
    const ELEMENT_TYPE_OTHERS = 0;
    const ELEMENT_TYPE_ALBUM = 1;
    const ELEMENT_TYPE_DRAMA = 2;
    const ELEMENT_TYPE_SOUND = 3;
    const ELEMENT_TYPE_EVENT = 4;
    const ELEMENT_TYPE_LIVE = 5;

    // 猜你喜欢下发的资源位来源
    const RECOMMEND_ATTR_TIANMA = 1;  // 天马推荐算法下发
    const RECOMMEND_ATTR_SUPPLEMENT = 2;  // 天马推荐算法下发不足时（音频下架等）进行补位
    const RECOMMEND_ATTR_SUBSTITUTE = 3;  // 根据运营策略替换
    const RECOMMEND_ATTR_MANUAL = 4;  // 人工配置（关闭个性化推荐）

    /**
     * @param bool $filter_sensitive 是否过滤擦边球及报警音频
     * @return array $sound_point [sound_id1 => point1, sound_id2: point2]
     */
    abstract protected function getSoundPoints(bool $filter_sensitive = false);

    /**
     * @return int
     * @todo 权重相关算法待调整
     */
    protected static function getWeight(int $listened_time, int $sound_id, int $strategy)
    {
        return 1;
    }

    /**
     * @param array $sound_points
     * @param array $listened_sounds
     * @return array sound_ids [1, 2, 3]
     */
    protected static function filterSounds(array $sound_points, array $listened_sounds = [])
    {
        $sound_ids = array_keys($sound_points);
        try {
            $drama_sound_counter = [];
            $sound_dramas = Drama::rpc('api/get-drama-paytype-by-sound',
                ['sound_ids' => $sound_ids]);
            $sound_ids = array_filter($sound_ids, function ($sound_id) use (&$sound_dramas, &$drama_sound_counter) {
                if (key_exists($sound_id, $sound_dramas)) {
                    // 过滤属于剧集且所属剧集不为免费类型的音频
                    if ($sound_dramas[$sound_id]['pay_type'] !== Drama::PAY_TYPE_FREE) return false;
                    $drama_id = $sound_dramas[$sound_id]['drama_id'];
                    if (key_exists($drama_id, $drama_sound_counter)) {
                        // 每个剧集下最多保存 2 个音
                        if ($drama_sound_counter[$drama_id] >= 2) return false;
                        $drama_sound_counter[$drama_id]++;
                    } else {
                        $drama_sound_counter[$drama_id] = 1;
                    }
                }
                return true;
            });
        } catch (HttpException $e) {
            // 若相关接口请求失败，不抛出异常避免影响首页显示，此时不进行付费剧音频的过滤
            Yii::warning('Drama API: "api/get-drama-paytype-by-sound" error: ' . $e->getMessage(), __METHOD__);
        }
        return $sound_ids;
    }

    /**
     * 根据音频 ID 获取相关推荐音频
     *
     * @param array $listened_sound_ids
     * @return array
     */
    protected static function getRecommendPool(array $listened_sound_ids)
    {
        return static::find()->where(['sound_id' => $listened_sound_ids])->all();
    }

    /**
     * 获取用户的推荐音频（首页“猜你喜欢”音）
     *
     * @param array $listened_sounds 已收听音频 ID 组成的数组
     * @param int $strategy 推荐方案
     * @return MSound[] 音频对象组成的数组
     */
    public static function getRecommendSounds(array $listened_sounds, int $strategy = self::STRATEGY_DEFAULT)
    {
        $listened_sound_ids = array_keys($listened_sounds);
        $recommends = static::getRecommendPool($listened_sound_ids);
        $sound_points = [];
        foreach ($recommends as $recommend) {
            $sound2recommend = $recommend->getSoundPoints(true);
            $weight = static::getWeight($listened_sounds[$recommend->sound_id], $recommend->sound_id, $strategy);
            self::calPoints($sound_points, $sound2recommend, $weight);
        }
        $sound_ids = static::filterSounds($sound_points, $listened_sounds);
        $sounds = MSound::find()
            ->select('id, user_id, soundstr, catalog_id, create_time, cover_image, duration, view_count,
                comment_count, comments_count, sub_comments_count')
            ->where(['id' => $sound_ids, 'pay_type' => MSound::SOUND_FREE, 'checked' => MSound::CHECKED_PASS])
            ->andWhere(['NOT IN', 'catalog_id', [
                Catalog::CATALOG_ID_ASMR_CN, Catalog::CATALOG_ID_ASMR_OVERSEAS,
                Catalog::CATALOG_ID_ASMR_NOVOICE, Catalog::CATALOG_ID_ASMR_GIRL]])
            ->andWhere('refined & ' . MSound::REFINED_POLICE . ' = 0')
            ->all();
        return $sounds;
    }

    /**
     * @param array $sound_point 为引用类型，返回计算后的结果
     * @param array $sound2points 用于计算的数据，key 为音频 id，value 为 point
     * @param int $weight 计算权重
     */
    public static function calPoints(array &$sound_point, array $sound2points, int $weight)
    {
        foreach ($sound2points as $sound_id => $point) {
            $w_point = $point * $weight;
            if (key_exists($sound_id, $sound_point)) {
                $sound_point[$sound_id] += $w_point;
            } else {
                $sound_point[$sound_id] = $w_point;
            }
        }
    }

    /**
     * 根据音频 ID 返回相似音频的 ID
     * 根据音频推荐项权重，计算音频推荐分数，分数越高，越有可能被推荐。
     * 返回的推荐音频会根据是否为答题用户做出相关过滤
     *
     * @param int $num 返回推荐音频数量，最多返回 $num 个音频 id
     * @return array 返回推荐音频 ID 数组
     */
    public function getSimilarSoundIds(int $num)
    {
        // 获取推荐音频数据（不过滤擦边球内容）
        $sound_points = $this->getSoundPoints();
        $ids = array_keys($sound_points);
        $sounds_checked_ids = MSound::find()
            ->select('id')
            ->where(['id' => $ids, 'checked' => MSound::CHECKED_PASS])
            // 推荐音频中不可出现擦边球 1 及擦边球 2 音频
            ->andWhere('NOT refined & :not_refined',
                [':not_refined' => MSound::REFINED_POLICE_AND_BLOCK])
            ->indexBy('id')
            ->column();
        $sound_points = array_intersect_key($sound_points, $sounds_checked_ids);
        $result = [];
        $index = 0;
        // return $num random sound ids by weighted
        foreach ($sound_points as $sound_id => $point) {
            $k = lcg_value() ** (1 / $point);
            if (++$index <= $num) {
                $result[$sound_id] = $k;
                arsort($result);
            } elseif (end($result) < $k) {
                array_pop($result);
                $result[$sound_id] = $k;
                arsort($result);
            }
        }
        return array_keys($result);
    }

    /**
     * 获取“猜你喜欢”模块的推荐直播
     *
     * @param integer $live_marker_id 当前推荐直播标记
     *
     * @return array|null 返回推荐直播数据及直播推荐标记 ID，若无推荐直播或主播未开播，则为 null
     */
    public static function getRecommendLive($live_marker_id)
    {
        $recommend_live_json = Yii::$app->redis->get(KEY_APP_HOMEPAGE_RECOMMEND_LIVE);
        if (!$recommend_live_json) {
            return null;
        }
        $recommend_live = Json::decode($recommend_live_json);
        if ($recommend_live['id'] === $live_marker_id) {
            // 若直播标记 ID 与当前推荐直播 ID 相同，表示客户端已经显示过该直播推荐，不再进行推荐
            return null;
        }
        // 查询是否为有直播消费行为的用户（需消费成功）
        $had_live_consumption = TransactionLog::find()->where([
            'from_id' => Yii::$app->user->id,
            'type' => [TransactionLog::TYPE_LIVE, TransactionLog::TYPE_GUILD_LIVE],
            'status' => TransactionLog::STATUS_SUCCESS,
        ])->exists();
        if (!$had_live_consumption) {
            return null;
        }
        // 查询主播信息（需处于开播状态）
        $live = Live::find()
            ->select('room_id, title')
            ->where(['user_id' => $recommend_live['creator_id'], 'status' => Live::STATUS_OPEN])
            ->one();
        if (!$live) {
            return null;
        }
        return [
            'room_id' => $live->room_id,
            'name' => $live->title,
            'cover_url' => $recommend_live['cover_url'],
            'creator_username' => $recommend_live['creator_username'],
            'creator_id' => $recommend_live['creator_id'],
            'type' => self::ELEMENT_TYPE_LIVE,
            'live_marker_id' => $recommend_live['id'],
        ];
    }

    /**
     * 获取“猜你喜欢”模块的广告位音频
     *
     * @param int $ad_marker_id 当前广告位 ID
     * @param int $boot 是否为启动客户端后请求，0：否，1：是
     * @return ActiveRecord|null 返回广告位（音频）数据，若无广告，则为 null
     */
    public static function getRecommendAdSound(int $ad_marker_id, int $boot)
    {
        $sound_id = MLaunch::getLaunch(MLaunch::ELEMENT_TYPE_YOU_MIGHT_LIKE_ADV);
        if ($sound_id <= 0) {
            // 广告位未设置或设置了非法值，则视为无广告
            return null;
        }
        if (!$boot && $sound_id === $ad_marker_id) {
            // 非初次启动时，已经出现过的广告有概率重新出现
            // TODO: 之后采取后台配置概率的方式处理
            $probability = (int)Yii::$app->redis->get(KEY_SHOW_AD_SOUND_PROBABILITY);
            if (mt_rand(1, 100) > $probability) {
                // 若当前广告位 ID 与设置的 ID 相同，表示客户端已经显示过该广告，不再显示
                return null;
            }
        }
        return MSound::find()->select('id, soundstr, catalog_id, create_time, cover_image,
                duration, view_count, comment_count, comments_count, sub_comments_count')
            ->where(['id' => $sound_id])->one();
    }

    public static function getRecommends(RecommendParams $params, array &$marker, int $boot, int $network, bool $enable_personalized = false)
    {
        $recommend_items = [];
        $user_feature = '';
        // 用户是否开启【个性化推荐】且命中算法
        if ($enable_personalized && $recommend = TianMa::getHomepageRecommends($params)) {
            $recommend_items = $recommend->getItems();
            $user_feature = $recommend->getUserFeature();
        }

        $recommend_track_id = '';
        $sound_ids = array_reduce($recommend_items, function ($ret, $item) use (&$recommend_track_id) {
            /**
             * @var RecommendItem $item
             */
            if ($item->isSoundElementType()) {
                $ret[] = $item->getId();
            }
            if (!$recommend_track_id) {
                $recommend_track_id = $item->getTrackId();
            }
            return $ret;
        }, []);

        $sounds = [];
        if (count($sound_ids) > 0) {
            $sounds = MSound::find()
                ->select('id, user_id, soundstr, catalog_id, create_time, cover_image, duration, view_count,
                    comment_count, comments_count, sub_comments_count')
                ->where(['id' => $sound_ids, 'pay_type' => MSound::SOUND_FREE, 'checked' => MSound::CHECKED_PASS])
                ->andWhere('refined & ' . MSound::REFINED_POLICE . ' = 0')
                ->indexBy('id')->all();
        }

        $refresh_type = $params->getFreshType();
        $trace_arr = [
            'refresh_type' => $refresh_type,
            'refresh_num' => $marker['refresh_num'] ?? -1,
            'attr' => self::RECOMMEND_ATTR_TIANMA
        ];
        $recommends = array_reduce($recommend_items, function ($ret, $item) use ($sounds, $refresh_type, $marker, $trace_arr) {
            /**
             * @var RecommendItem $item
             */
            if ($item->isSoundElementType() && array_key_exists($item->getId(), $sounds)) {
                /**
                 * @var MSound $sound
                 */
                $sound = $sounds[$item->getId()];
                $recommend = self::getRecommendItem($sound, $refresh_type, $item->getTrackId(), $trace_arr, self::RECOMMEND_ATTR_TIANMA);
                $recommend['source'] = $item->getSource();
                $recommend['av_feature'] = $item->getAvFeature();
                $ret[] = $recommend;
            }
            return $ret;
        }, []);
        $length = count($recommends);
        // 命中算法的推荐音频按剧集维度展示
        if ($length > 0) {
            $recommends = self::replaceWithDramaInfo($recommends, $enable_personalized);
        }

        // 如果数量不足则追加至 6 个
        if ($length < self::RECOMMEND_SOUNDS_NUM) {
            // 判断用户是否开启【个性化推荐】按钮，是的话 attr 使用补位天马推荐，否则使用人工配置
            $attr = $enable_personalized ? self::RECOMMEND_ATTR_SUPPLEMENT : self::RECOMMEND_ATTR_MANUAL;
            $trace_arr['attr'] = $attr;
            $recommend_sounds_append = array_map(function ($sound) use ($params, $refresh_type, $trace_arr, $attr) {
                return self::getRecommendItem($sound, $refresh_type, '', $trace_arr, $attr);
            }, Persona::getLikeSounds($params->getPersona()));
            if ($enable_personalized) {
                $recommends = array_merge($recommends, $recommend_sounds_append);
            } else {
                // 关闭【个性化推荐】按钮，推荐内容获取走后台人工配置推荐池
                $recommends = $recommend_sounds_append;
            }
            $recommends = array_slice($recommends, 0, self::RECOMMEND_SOUNDS_NUM);
        } elseif ($length > self::RECOMMEND_SOUNDS_NUM) {
            $recommends = array_slice($recommends, 0, 6);
        }

        /**
         * 某些特定情况替换猜你喜欢最后一个推荐（广告位）
         * @var MSound $ad_sound
         */
        $ad_sound = self::getRecommendAdSound($marker['ad'], $boot);
        if ($ad_sound) {
            $trace_arr['attr'] = self::RECOMMEND_ATTR_SUBSTITUTE;
            $recommends[count($recommends) - 1] = self::getRecommendItem($ad_sound, $refresh_type, '', $trace_arr,
                self::RECOMMEND_ATTR_SUBSTITUTE);
            // 更新广告位 marker ID
            $marker['ad'] = $ad_sound->id;
        }

        if ($params->getUser()->getId()) {
            // 登录的用户需要替换推荐直播数据
            $live_id = $marker['live'];
            $recommend_live = self::getRecommendLive($live_id);
            if ($recommend_live) {
                if ($refresh_type !== -1) {
                    $recommend_live['track_id'] = '';
                    $recommend_live['trace'] = Json::encode(array_merge($trace_arr,
                        ['attr' => self::RECOMMEND_ATTR_SUBSTITUTE]));
                }
                $recommend_live['attr'] = self::RECOMMEND_ATTR_SUBSTITUTE;

                // 推荐直播出现在第一排（前 3 个音频）随机位置
                $MAX_REPLACE_INDEX = 2;
                $rand_position = rand(0, $MAX_REPLACE_INDEX);
                $recommends[$rand_position] = $recommend_live;
                // 使用 marker 伪码参数时，更新相关 marker 值
                $marker['live'] = $recommend_live['live_marker_id'];
                unset($recommend_live['live_marker_id']);
            }
        }

        if ($params->getEquip()->getOs() !== Equipment::Web) {
            self::addRecommendsExposureLog($recommends, $recommend_track_id, $user_feature, $params, $network);
        }

        return array_map(function ($item) {
            unset($item['av_feature'], $item['source'], $item['attr']);
            return $item;
        }, $recommends);
    }

    public static function getRecommendItem(MSound $sound, int $refresh_type, string $track_id, array $trace_arr, int $attr)
    {
        $item = [
            'id' => $sound->id,
            'soundstr' => $sound->soundstr,
            'catalog_id' => $sound->catalog_id,
            'create_time' => $sound->create_time,
            'duration' => $sound->duration,
            'view_count' => $sound->view_count,
            'all_comments' => $sound->all_comments,
            // TODO: 待不支持播放页的版本淘汰后，该字段可删除
            'video' => (bool)($sound->video ?? false),
            'front_cover' => $sound->front_cover,
            'type' => RecommendBase::ELEMENT_TYPE_SOUND,
            'attr' => $attr,
        ];
        if ($refresh_type !== -1) {
            $item['track_id'] = $track_id;
            $item['trace'] = Json::encode(array_merge($trace_arr, ['track_id' => $track_id]));
        }

        return $item;
    }

    public static function addRecommendsExposureLog(array $recommends, string $recommend_track_id, string $user_feature, RecommendParams $params, int $network)
    {
        try {
            $user_id = (int)$params->getUser()->getId();
            $ip = $params->getRequest()->getUserIP();
            $equipment = $params->getEquip();
            $key_suffix = $user_id ?: crc32($ip);

            $pos = 0;
            $log = [
                'user_id' => $user_id,
                'os' => $equipment->getOs(),
                'equip_id' => $equipment->getEquipId(),
                'buvid' => (string)$equipment->getBuvid(),
                'channel' => $equipment->getChannel(),
                'app_version' => $equipment->getAppVersion(),
                'user_agent' => $equipment->getUserAgent(),
                'ip' => $ip,
                'network' => $network,
                'refresh_type' => $params->getFreshType(),
                'refresh_num' => $params->getDisplayId(),
                'create_time' => $_SERVER['REQUEST_TIME'],
                'track_id' => $recommend_track_id,
                'user_feature' => $user_feature,
                'showlist' => array_map(function ($item) use (&$pos) {
                    $pos++;
                    return [
                        'id' => $item['id'] ?? $item['room_id'],
                        'goto' => $item['type'] === self::ELEMENT_TYPE_SOUND
                            ? TianMa::GOTO_SOUND
                            : TianMa::GOTO_LIVE,
                        'pos' => $pos,
                        'source' => $item['source'] ?? '',
                        'av_feature' => $item['av_feature'] ?? '',
                        'attr' => $item['attr'],
                    ];
                }, $recommends),
                'env' => getenv('DEPLOY_ENV', true),
            ];
            Yii::$app->databus->pub($log, 'recommend_exposure_log:' . $key_suffix);
        } catch (\Exception $e) {
            Yii::error('add recommends exposure log error: ' . $e->getMessage(), __METHOD__);
        }
    }

    /**
     * 是否展示评论字段
     *
     * @param bool $enable_personalized
     * @return bool true 展示；false 不展示
     */
    public static function isRecommendsShowCommentNum(bool $enable_personalized = false): bool
    {
        // 用户关闭【个性化推荐】按钮（小编推荐模块）展示评论
        // WORKAROUND: iOS < 6.2.6 Android < 6.2.6 猜你喜欢模块展示评论
        return !$enable_personalized || Equipment::isAppOlderThan('6.2.6', '6.2.6');
    }

    /**
     * 替换成剧集维度显示
     *
     * @param array $recommends 需要替换的推荐音频内容
     * @param bool $enable_personalized 是否开启个性化推荐
     * @return array
     */
    public static function replaceWithDramaInfo(array $recommends, bool $enable_personalized = false): array
    {
        $sound_ids = array_column($recommends, 'id');
        $info = null;
        try {
            $info = Yii::$app->serviceRpc->getDramaInfoBySounds($sound_ids);
        } catch (Exception $e) {
            // 记录错误日志
            Yii::error(sprintf('获取音频（%s）剧集信息出错：%s', implode(', ', $sound_ids), $e->getMessage()),
                __METHOD__);
            // PASS
        }
        if (!$info || !$info['dramas']) {
            return $recommends;
        }
        $dramas = $info['dramas'];
        $is_show_all_comments = self::isRecommendsShowCommentNum($enable_personalized);
        foreach ($recommends as &$recommend) {
            $sound_id = (string)($recommend['id']);
            if (!isset($dramas[$sound_id])) {
                continue;
            }
            $recommend['soundstr'] = $dramas[$sound_id]['name'];
            $recommend['front_cover'] = $dramas[$sound_id]['cover_url'];
            $recommend['view_count'] = $dramas[$sound_id]['view_count'];
            // 判断是否要显示评论
            if ($is_show_all_comments) {
                $recommend['all_comments'] = $dramas[$sound_id]['comment_count'];
            } else {
                unset($recommend['all_comments']);
            }
        }
        unset($recommend);
        return $recommends;
    }
}
