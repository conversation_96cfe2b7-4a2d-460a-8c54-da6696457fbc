<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "guild_live_contract_applyment".
 *
 * @property int $id 主键
 * @property int $live_id 主播 ID
 * @property int $guild_id 公会 ID
 * @property string $guild_name 公会名称
 * @property int $contract_id 合同 ID
 * @property int $contract_expire_time 合同过期时间
 * @property int $type 合约类型
 * @property int $contract_duration 合约时长
 * @property int $rate 分成比例或违约金额（分）
 * @property int $expire_time 失效时间
 * @property int $status 状态
 * @property int $initiator 发起方（1 主播发起，2 公会发起）
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 */
class GuildLiveContractApplyment extends ActiveRecord
{
    const STATUS_INVALID = -3;
    const STATUS_REVOKED = -2;
    const STATUS_DECLINED = -1;
    const STATUS_PENDING = 0;
    const STATUS_AGREED = 1;

    const TYPE_LIVE_SIGN = 1;
    const TYPE_GUILD_SIGN = 2;
    const TYPE_LIVE_RENEW = 3;
    const TYPE_GUILD_RENEW = 4;
    const TYPE_LIVE_TERMINATE = 5;
    const TYPE_GUILD_EXPEL = 6;
    const TYPE_LIVE_TERMINATE_FORCELY = 7;

    const TYPE_INITIATOR_LIVE = 1;
    const TYPE_INITIATOR_GUILD = 2;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'guild_live_contract_applyment';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['live_id', 'guild_id', 'contract_id', 'contract_expire_time', 'type', 'expire_time', 'initiator'], 'required'],
            [['live_id', 'guild_id', 'contract_id', 'contract_expire_time', 'type', 'contract_duration', 'rate', 'expire_time', 'status', 'initiator', 'create_time', 'modified_time'], 'integer'],
            [['guild_name'], 'string', 'max' => 50],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'live_id' => '主播 ID',
            'guild_id' => '公会 ID',
            'guild_name' => '公会名称',
            'contract_id' => '合同 ID',
            'contract_expire_time' => '合同过期时间',
            // 1. 主播申请签约；2. 公会申请签约；
            // 3. 主播申请续约；4. 公会申请续约；
            // 5. 协商解约；6. 公会清退；
            // 7. 强制解约
            'type' => '合约类型',
            'contract_duration' => '合约时长',  // 1. 6 个月，2. 1 年
            'rate' => '分成比例或违约金额（分）',
            // 签约：当前时间 + 7 * 24 小时
            // 续约时间：合同到期时间
            // 协商解约时间：当前时间 + 7 * 24 小时
            // 公会清退：当前时间 + 48 小时
            // 强制解约：统一写当前时间
            'expire_time' => '失效时间',
            // -3 已失效
            // -2 已撤回
            // -1 被对方拒绝
            // 0 待处理
            // 1 被对方同意
            'status' => '状态',
            'initiator' => '发起方',  // 1 主播发起，2 公会发起
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

}
