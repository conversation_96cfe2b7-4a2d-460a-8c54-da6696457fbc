<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "goods_like".
 *
 * @property int $id 主键
 * @property int $goods_id 商品 ID
 * @property int $user_id 用户 ID
 * @property int $status 状态
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 */
class GoodsLike extends ActiveRecord
{
    public const STATUS_CANCEL_LIKE = 0;
    public const STATUS_LIKE = 1;

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('malldb');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'goods_like';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['goods_id', 'user_id'], 'required'],
            [['goods_id', 'user_id', 'status', 'create_time', 'modified_time'], 'integer'],
            [['goods_id', 'user_id'], 'unique', 'targetAttribute' => ['goods_id', 'user_id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'goods_id' => '商品 ID',
            'user_id' => '用户 ID',
            'status' => '状态',   // 0 不喜欢，1 喜欢
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

}
