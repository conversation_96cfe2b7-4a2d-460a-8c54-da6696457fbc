<?php

namespace app\models;

use app\components\models\traits\UserTrait;
use Yii;
use yii\helpers\Json;
use yii\web\HttpException;

/**
 * This is the model class for table "user_info".
 *
 * @property int $id 主键，用户 id
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 * @property int $end_time 新用户免费抽的截止时间
 * @property int $coupon 获得碎片
 * @property int $notice 提醒数
 */
class UserInfo extends ActiveRecord
{
    use UserTrait;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'user_info';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db2');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id'], 'required'],
            [['id', 'create_time', 'modified_time', 'coupon', 'notice'], 'integer'],
            [['id'], 'unique'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '用户 ID',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'end_time' => '免费抽截止时间',
            'coupon' => '荣耀点',
            'notice' => '提醒数'
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
            // 当为新纪录时，更新该用户的“迎新”（新用户免费抽卡）截止时间
            $redis = Yii::$app->redis;
            // 新用户免费时长，为 0 时表示不免费
            $duration = $redis->get(KEY_VOICE_FREE_DURATION);
            $duration = $duration !== false ? (int)$duration : ONE_WEEK;
            $today = strtotime('today');
            $free_duration = $duration ?: ONE_WEEK;
            $this->end_time = (int)$free_duration + $today;
        }
        $this->modified_time = $time;
        return true;
    }

    public static function getCoupon($user_id)
    {
        if (!$user_id) return 0;
        return self::getByPk($user_id)->coupon;
    }

    public static function getNotice($user_id)
    {
        if (!$user_id) return 0;
        return (int)self::getByPk($user_id)->notice;
    }
}
