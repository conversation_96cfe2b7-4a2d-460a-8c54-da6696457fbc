<?php

namespace app\models;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;
use Yii;

/**
 * @implements AdTrackInterface
 */
class AdTrackSigmob extends AdTrack implements AdTrackInterface
{
    const CALLBACK_EVENT_TYPE_MAP = [
        self::CALLBACK_EVENT_ACTIVATE => 1,
        self::CALLBACK_EVENT_ONE_DAY_RETENTION => 1,
        self::CALLBACK_EVENT_PAY => 1,
        self::CALLBACK_EVENT_REGISTER => null,  // 可能会将注册做为转化目标，但根据 sigmob 要求不用回传
        self::CALLBACK_EVENT_KEY_ACTION => null,
        self::CALLBACK_EVENT_TRANSACTION => null,
        self::CALLBACK_EVENT_APP_CALLUP => null,
    ];

    const SUBSEQUENT_EVENTS = [
        self::CALLBACK_EVENT_PAY,
        self::CALLBACK_EVENT_ONE_DAY_RETENTION,
    ];

    const SIGMOB_GATEWAY = 'https://dc.sigmob.cn/log';

    const CALLBACK_ACTIVATE_SUCCESS_CODE = 200;
    const CALLBACK_SUBSEQUENT_SUCCESS_CODE = 204;

    /**
     * 获取回传 url
     *
     * @link https://rzvo5fieru.feishu.cn/docx/doxcn1x4N78KYcaPcPPqvn41Hme
     *
     * @param string $callback_event 事件类型
     * @param mixed $arg 付费金额（单位：元）
     * @throws Exception
     */
    private function getCallbackUrl(string $callback_event, $arg = 0): string
    {
        switch ($callback_event) {
            case self::CALLBACK_EVENT_ACTIVATE:
                return $this->track_id;
            case self::CALLBACK_EVENT_PAY:
            case self::CALLBACK_EVENT_ONE_DAY_RETENTION:
                if (!isset($this->more['click_id'])) {
                    throw new Exception('sigmob 广告点击唯一标识有误');
                }
                $params = [
                    '_uniq_key' => 'sig_active',
                    '_ac_type' => 80,
                    'platform' => 'maoer',  // 自定义标识，Sigmob 只做非空校验
                    'clickid' => $this->more['click_id'],
                    'event_time' => $_SERVER['REQUEST_TIME'],
                ];
                if ($callback_event === self::CALLBACK_EVENT_PAY) {
                    $params['e'] = 'pay';
                    $params['currency_type'] = 'CNY';
                    $params['currency_amount'] = (int)$this->getCallbackPayAmount($arg);
                }
                if ($callback_event === self::CALLBACK_EVENT_ONE_DAY_RETENTION) {
                    $params['e'] = 'login';
                }
                return self::SIGMOB_GATEWAY . '?' . http_build_query($params);
            default:
                throw new Exception('事件类型有误');
        }
    }

    /**
     * 请求 sigmob 接口
     *
     * @param string $callback_url 请求地址
     * @param string $callback_event 回传类型
     * @return bool
     */
    private function requestSigmob(string $callback_url, string $callback_event): bool
    {
        try {
            $options = [
                RequestOptions::HEADERS => [
                    'User-Agent' => Yii::$app->name . '/' . Yii::$app->version,
                ],
                RequestOptions::TIMEOUT => 5,
            ];
            $res = (new Client())->request('GET', $callback_url, $options);
            $status_code = $res->getStatusCode();
            if ($callback_event === self::CALLBACK_EVENT_ACTIVATE && $status_code === self::CALLBACK_ACTIVATE_SUCCESS_CODE) {
                return true;
            }
            if (in_array($callback_event, self::SUBSEQUENT_EVENTS) && $status_code === self::CALLBACK_SUBSEQUENT_SUCCESS_CODE) {
                return true;
            }
            Yii::error(sprintf('request api: %s fail, status_code: %d, body_content: %s',
                $callback_url, $res->getStatusCode(), $res->getBody()->getContents()), __METHOD__);
            return false;
        } catch (Exception $e) {
            Yii::error('request sigmob error: ' . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    /**
     * @param string $event_type 事件类型
     * @param mixed $arg 支付金额
     */
    public function callback(string $event_type, $arg = 0): bool
    {
        try {
            if (!array_key_exists($event_type, self::CALLBACK_EVENT_TYPE_MAP)) {
                throw new Exception('sigmob 点击回传事件错误：' . $event_type);
            }
            if (is_null(self::CALLBACK_EVENT_TYPE_MAP[$event_type])) {
                return true;
            }
            return $this->requestSigmob($this->getCallbackUrl($event_type, $arg), $event_type);
        } catch (Exception $e) {
            Yii::error('sigmob ad callback error: ' . $e->getMessage(), __METHOD__);
        }
        return false;
    }
}
