<?php

namespace app\models;

use Exception;
use yii\db\Expression;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "pay_account".
 *
 * @property int $id 主键
 * @property int $user_id 用户 ID
 * @property int $tid 关联 tid
 * @property int $account_amount 充值金额
 * @property int $balance 余额
 * @property int $scope 适用范围：0 通用、1 直播间（含公会主播）
 * @property int $type 账户类型
 * @property int $withhold_order 扣款顺序
 * @property int $expire_time 过期时间（秒级时间戳）
 * @property int $create_time 创建时间（秒级时间戳）
 * @property int $modified_time 修改时间（秒级时间戳）
 * @property int $status 状态
 * @property array $more 更多详情
 */
class PayAccount extends ActiveRecord
{
    const SCOPE_COMMON = 0;
    const SCOPE_LIVE = 1;

    const ATTR_COMMON_COIN = 0;
    const ATTR_NOBLE_COIN = 1;
    const ATTR_NOBLE_FREE_COIN = 2;

    const TYPE_OFFSET_STEP = 10;

    const ATTR_TYPE_OFFSET_MAP = [
        self::ATTR_COMMON_COIN => 0,
        self::ATTR_NOBLE_COIN => 10,
        self::ATTR_NOBLE_FREE_COIN => 20,
    ];

    const TYPE_COIN_INDEX_ANDROID = 0;
    const TYPE_COIN_INDEX_TMALL_IOS = 1;
    const TYPE_COIN_INDEX_PAYPAL = 2;
    const TYPE_COIN_INDEX_IOS = 3;
    const TYPE_COIN_INDEX_GOOGLEPAY = 4;
    const TYPE_COIN_INDEX_DOUDIAN = 5;
    const TYPE_COIN_INDEX_VIP = 6;

    // 2025-07-01 及其之后新档位充值的钻石（注：新档位货币与钻石不是 1:10）
    // https://info.missevan.com/pages/viewpage.action?pageId=*********
    const TYPE_COIN_INDEX_NEW_IOS = 7;
    const TYPE_COIN_INDEX_NEW_GOOGLEPAY = 8;

    const COIN_FIELD_IOS = 'ios';
    const COIN_FIELD_ANDROID = 'android';
    const COIN_FIELD_TMALL_IOS = 'tmallios';
    const COIN_FIELD_PAYPAL = 'paypal';
    const COIN_FIELD_GOOGLE_PAY = 'googlepay';
    const COIN_FIELD_DOUDIAN = 'doudian';
    const COIN_FIELD_VIP = 'vip';
    const COIN_FIELD_NEW_IOS = 'new_ios';
    const COIN_FIELD_NEW_GOOGLEPAY = 'new_googlepay';

    const TYPE_INDEX_COIN_FIELD_MAP = [
        self::TYPE_COIN_INDEX_ANDROID => self::COIN_FIELD_ANDROID,
        self::TYPE_COIN_INDEX_TMALL_IOS => self::COIN_FIELD_TMALL_IOS,
        self::TYPE_COIN_INDEX_PAYPAL => self::COIN_FIELD_PAYPAL,
        self::TYPE_COIN_INDEX_IOS => self::COIN_FIELD_IOS,
        self::TYPE_COIN_INDEX_GOOGLEPAY => self::COIN_FIELD_GOOGLE_PAY,
        self::TYPE_COIN_INDEX_DOUDIAN => self::COIN_FIELD_DOUDIAN,
        self::TYPE_COIN_INDEX_VIP => self::COIN_FIELD_VIP,
        self::TYPE_COIN_INDEX_NEW_IOS => self::COIN_FIELD_NEW_IOS,
        self::TYPE_COIN_INDEX_NEW_GOOGLEPAY => self::COIN_FIELD_NEW_GOOGLEPAY,
    ];

    const LEGACY_COIN_TYPE_INDEX = [
        self::TYPE_COIN_INDEX_ANDROID,
        self::TYPE_COIN_INDEX_TMALL_IOS,
        self::TYPE_COIN_INDEX_PAYPAL,
        self::TYPE_COIN_INDEX_IOS,
        self::TYPE_COIN_INDEX_GOOGLEPAY,
    ];

    // 钻石消费扣钻顺序（大的先扣）
    // 见：https://info.missevan.com/pages/viewpage.action?pageId=********
    // https://www.tapd.cn/35612194/prong/stories/view/1135612194004620253
    const COIN_CONSUME_ORDER = [
        self::TYPE_COIN_INDEX_DOUDIAN => 0,
        self::TYPE_COIN_INDEX_ANDROID => 1,
        self::TYPE_COIN_INDEX_TMALL_IOS => 2,
        self::TYPE_COIN_INDEX_PAYPAL => 3,
        self::TYPE_COIN_INDEX_NEW_IOS => 4,
        self::TYPE_COIN_INDEX_IOS => 5,
        self::TYPE_COIN_INDEX_VIP => 6,
        self::TYPE_COIN_INDEX_NEW_GOOGLEPAY => 7,
        self::TYPE_COIN_INDEX_GOOGLEPAY => 8,
    ];

    // iOS 退钻顺序（大的先扣）
    const COIN_IOS_REFUND_ORDER = [
        self::TYPE_COIN_INDEX_NEW_GOOGLEPAY => 0,
        self::TYPE_COIN_INDEX_GOOGLEPAY => 1,
        self::TYPE_COIN_INDEX_VIP => 2,
        self::TYPE_COIN_INDEX_PAYPAL => 3,
        self::TYPE_COIN_INDEX_TMALL_IOS => 4,
        self::TYPE_COIN_INDEX_ANDROID => 5,
        self::TYPE_COIN_INDEX_DOUDIAN => 6,
        self::TYPE_COIN_INDEX_NEW_IOS => 7,
        self::TYPE_COIN_INDEX_IOS => 8,
    ];

    // 天猫 iOS 退钻顺序（大的先扣）
    const COIN_TMALL_IOS_REFUND_ORDER = [
        self::TYPE_COIN_INDEX_NEW_GOOGLEPAY => 0,
        self::TYPE_COIN_INDEX_GOOGLEPAY => 1,
        self::TYPE_COIN_INDEX_VIP => 2,
        self::TYPE_COIN_INDEX_NEW_IOS => 3,
        self::TYPE_COIN_INDEX_IOS => 4,
        self::TYPE_COIN_INDEX_PAYPAL => 5,
        self::TYPE_COIN_INDEX_ANDROID => 6,
        self::TYPE_COIN_INDEX_DOUDIAN => 7,
        self::TYPE_COIN_INDEX_TMALL_IOS => 8,
    ];

    // 默认的退钻顺序（大的先扣）
    const COIN_DEFAULT_REFUND_ORDER = [
        self::TYPE_COIN_INDEX_NEW_GOOGLEPAY => 0,
        self::TYPE_COIN_INDEX_GOOGLEPAY => 1,
        self::TYPE_COIN_INDEX_VIP => 2,
        self::TYPE_COIN_INDEX_NEW_IOS => 3,
        self::TYPE_COIN_INDEX_IOS => 4,
        self::TYPE_COIN_INDEX_PAYPAL => 5,
        self::TYPE_COIN_INDEX_TMALL_IOS => 6,
        self::TYPE_COIN_INDEX_ANDROID => 7,
        self::TYPE_COIN_INDEX_DOUDIAN => 8,
    ];

    // 渠道费率场景
    const FEE_RATE_SCENE_LIVE = 'live_scene';
    const FEE_RATE_SCENE_RADIO = 'radio_scene';

    // 成功
    const STATUS_SUCCESS = 1;
    // 已退款（人民币）
    const STATUS_REFUND = -3;
    // 已退款（猫耳钻石）
    const STATUS_REFUND_DIAMOND = -4;
    // 代充取消交易记录
    const STATUS_ILLEGAL_TOPUP = -5;
    // 未成年退款
    const STATUS_TEENAGER_REFUND = -6;

    // 贵族过期后贵族钻石冻结期
    const NOBLE_COIN_FROZEN_PERIOD = 90 * ONE_DAY;

    // expire_time 永久生效值
    const EXPIRE_TIME_FOREVER_EFFECTIVE = **********;

    /**
     * @var int 本次该账户消费总额（钻石）
     */
    public $consume_amount = 0;

    /**
     * 特殊的渠道费率
     * @var null|float
     */
    private $_special_fee_rate = null;

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->paydb;
    }

    public static function tableName()
    {
        return 'pay_account';
    }

    public function rules()
    {
        return [
            [['user_id', 'tid', 'account_amount', 'balance', 'scope', 'type', 'withhold_order', 'expire_time', 'create_time', 'modified_time', 'status'], 'integer'],
            [['more'], 'safe'],
        ];
    }

    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'user_id' => '用户 ID',
            'tid' => '交易记录 ID',
            'account_amount' => '充值金额',
            'balance' => '余额',
            // 0 通用钻石，1 直播间（含公会主播）
            'scope' => '适用范围',
            // int(type / 10): 0 普通钻石，1 贵族普通钻石，2 贵族免费钻石
            // type % 10：0 Android, 1 天猫 iOS, 2 PayPal, 3 iOS, 4 GooglePay, 5 抖店, 6 点播会员领取的福利钻石
            'type' => '账户类型',
            'withhold_order' => '扣款顺序',  // 越大的越先扣
            'expire_time' => '过期时间',  // 秒级时间戳，0 代表永久
            'create_time' => '创建时间',  // 秒级时间戳
            'modified_time' => '修改时间',  // 秒级时间戳
            'status' => '状态',
            'more' => '更多详情',
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        if (!$this->more) {
            $this->more = [];
        }
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * @return int 获取账号新的余额
     * @throws Exception 消费金额不可为负数
     */
    public function getNewBalance()
    {
        return $this->balance - $this->consume_amount;
    }

    /**
     * @param TransactionLog $transaction_log
     * @return float 获取平台费用（单位为元，精确到分）
     * @throws Exception 消费金额不可为负数
     */
    public function getFee(TransactionLog $transaction_log)
    {
        if ($this->consume_amount < 0) {
            throw new Exception('消费金额不可为负数');
        }
        return self::calFee($this->consume_amount, $this->getFeeRate($transaction_log));
    }

    /**
     * 获取用户贵族账户持有明细
     *
     * @param int $user_id 用户 ID
     * @return array 按过去时间聚合的贵族余额明细
     */
    public static function getNobleBalanceDetails(int $user_id): array
    {
        $now = $_SERVER['REQUEST_TIME'];
        $last_month = strtotime('first day of last month midnight');
        /** @var PayAccount[] $accounts */
        $accounts = self::find()
            ->select('id, balance, expire_time')
            ->where('user_id = :user_id AND expire_time > :last_month AND balance > 0 AND scope = :scope')
            ->params([':user_id' => $user_id, ':last_month' => $last_month, ':scope' => self::SCOPE_LIVE])
            ->andWhere(['status' => self::STATUS_SUCCESS])
            ->all();
        $day_balances = array_reduce($accounts, function ($day_balances, $account) {
            $expire_time = $account['expire_time'];
            if (!key_exists($expire_time, $day_balances)) {
                $day_balances[$expire_time] = 0;
            }
            $day_balances[$expire_time] += $account['balance'];
            return $day_balances;
        }, []);
        krsort($day_balances);
        $balance_details = [];
        foreach ($day_balances as $expire_time => $balance) {
            $balance_detail = [
                // 贵族钻石的过期时间是过期日的最后一秒，即 23:59:59
                'expire_date' => date('Y-m-d H:i', $expire_time),
                'balance' => $balance,
                'has_expired' => ($expire_time <= $now),
            ];
            $balance_details[] = $balance_detail;
        }
        return $balance_details;
    }

    /**
     * 获取用户在给定时间段到期钻石总额
     *
     * @param int $start_time 开始时间戳（单位：秒）
     * @param int $end_time 结束时间戳（单位：秒）
     * @param int type 类型（0 取过期的钻石，1 取清零的钻石，即超过贵族钻石冻结期的钻石）
     * @param int $scope 涉及什么样的账户
     * @return array
     */
    public static function getExpireBalanceByTime(int $start_time, int $end_time, int $type = 0, int $scope = self::SCOPE_LIVE): array
    {
        if ($type) {
            $start_time -= self::NOBLE_COIN_FROZEN_PERIOD;
            $end_time -= self::NOBLE_COIN_FROZEN_PERIOD;
        }

        $users_balance_items = self::find()
            ->select('user_id, SUM(balance) AS total_balance')
            ->where('balance > 0 AND status = :status AND scope = :scope', [':status' => self::STATUS_SUCCESS, ':scope' => $scope])
            // start_time 等于 expire_time 时钻石尚在有效期
            ->andWhere('expire_time >= :start_time AND expire_time < :end_time', [':start_time' => $start_time, ':end_time' => $end_time])
            ->groupBy('user_id')
            ->asArray()
            ->all();
        return array_map(function ($item) {
            return [
                'user_id' => (int)$item['user_id'],
                'total_balance' => (int)$item['total_balance'],
            ];
        }, $users_balance_items);
    }

    /**
     * 获取账号余额
     *
     * @param PayAccount[] $accounts 用户所有的账户
     * @return int 获取用户余额
     */
    public static function getBalance(array $accounts): int
    {
        if (empty($accounts)) return 0;
        return array_reduce($accounts, function (int $balance, PayAccount $account) {
            return $account->balance + $balance;
        }, 0);
    }

    /**
     * 获取所有发生变动的账户
     * 按账户数组顺序来进行扣款
     *
     * @param PayAccount[] $accounts 用户所有的账户
     * @param int $price 消耗钻石
     * @param array $deduct_order 扣钻顺序
     * @return array 返回修改后的账户及余额
     * @throws Exception 价格不能为负数
     */
    public static function getChangeAccount(array $accounts, int $price, array $deduct_order = self::COIN_CONSUME_ORDER): array
    {
        if ($price < 0) throw new Exception('价格不能为负数');
        if (empty($accounts) || 0 === $price) {
            return [[], $price];
        }

        self::sortByTargetOrder($accounts, $deduct_order);

        $change_accounts = [];
        foreach ($accounts as $account) {
            $account->consume_amount = min($account->balance, $price);
            $price -= $account->consume_amount;
            $change_accounts[] = $account;
            if ($price <= 0) break;
        }
        return [$change_accounts, $price];
    }

    public static function sortByTargetOrder(array &$accounts, array $order = self::COIN_CONSUME_ORDER)
    {
        // 重排账户顺序（用于消费扣款）
        // https://info.missevan.com/pages/viewpage.action?pageId=********
        usort($accounts, function ($next, $pre) use ($order) {
            /**
             * @var PayAccount $next
             * @var PayAccount $pre
             */
            // scope 大的，则先使用（贵族钻石 scope > 普通钻石）
            $v = $next->scope - $pre->scope;
            if ($v !== 0) {
                return -$v;
            }

            // 过期时间小的在前，先过期的先使用
            $v = $next->expire_time - $pre->expire_time;
            if ($v !== 0) {
                return $v;
            }

            // TODO: 后续改为 attr 来判断是否是贵族钻石、免费贵族钻石，不再使用 type
            // 免费贵族钻石先使用，再使用普通贵族钻石，再使用普通钻石
            $v = $next->getTypeOffset() - $pre->getTypeOffset();
            if ($v !== 0) {
                return -$v;
            }
            if (array_key_exists('attr', $next->more) && array_key_exists('attr', $pre->more)) {
                $v = $next->more['attr'] - $pre->more['attr'];
                if ($v !== 0) {
                    return -$v;
                }
            }

            // 扣款顺序大的在前
            $v = $order[$next->getCoinType()] - $order[$pre->getCoinType()];
            if ($v !== 0) {
                return -$v;
            }

            // 创建时间小的在前，先获得的先使用
            return $next->create_time - $pre->create_time;
        });
    }

    public function getFeeRate(TransactionLog $transaction_log)
    {
        if (!is_null($this->_special_fee_rate)) {
            return $this->_special_fee_rate;
        }

        $scene = self::FEE_RATE_SCENE_RADIO;
        // NOTICE: 直播间喵喵广播剧福袋按点播渠道费率走
        if ($transaction_log->isLiveProduct() && $transaction_log->attr !== TransactionLog::ATTR_LIVE_BUY_LUCKY_BAG) {
            $scene = self::FEE_RATE_SCENE_LIVE;
        }
        return Yii::$app->params['coin_rate'][$scene][self::TYPE_INDEX_COIN_FIELD_MAP[$this->getCoinType()]];
    }

    /**
     * @param float $fee_rate
     */
    public function setSpecialFeeRate(float $fee_rate)
    {
        $this->_special_fee_rate = $fee_rate;
    }

    /**
     * 获取贵族钻石的账户分布明细
     *
     * @param int[] $currencies = [0 => $android, 1 => $tmall_ios, 2 => $paypal, 3 => $ios, 4 => $googlepay]
     * @param int $rebate
     * @return int[] = [4 => $googlepay, 3 => $ios, 2 => $paypal, 1 => $tmall_ios, 0 => $android]
     * @throws Exception
     */
    public static function getRebateDetail(array $currencies, int $rebate)
    {
        $len = count(self::TYPE_INDEX_COIN_FIELD_MAP);
        if (count($currencies) !== $len) {
            throw new Exception('参数不正确');
        }
        if (0 === $rebate) {
            return array_fill(0, $len, 0);
        }

        $total = array_sum($currencies);
        if ($total === 0) {
            throw new Exception('参数不正确');
        }

        $rates = array_map(function ($currency) use ($total) {
            return $currency / $total;
        }, $currencies);
        $remaining = $rebate;
        $rebate_details = [];

        // 调整贵族不同渠道钻石返还顺序（与 $currencies 的顺序是相反的）
        // https://github.com/MiaoSiLa/requirements-doc/issues/1015
        krsort($rates);

        foreach ($rates as $type => $rate) {
            $rebate_detail = (int)ceil($rate * $rebate);
            $rebate_detail = min($remaining, $rebate_detail);
            $rebate_details[$type] = $rebate_detail;
            $remaining -= $rebate_detail;
        }
        return $rebate_details;
    }

    /**
     * 更新所有发生开销的账户余额
     * 这边考虑到用户涉及到的特殊账户不会太多。所以这边使用循环更新。
     * NOTICE: 需要在事务中使用
     *
     * @param PayAccount[] $accounts 所有发生变动的账户
     * @param bool $sync_all_consumption 是否同步更新总消费值
     * @throws Exception 这边如果没有更新说明有并发情况，放弃本次更新
     */
    public static function updateAccounts(array $accounts, bool $sync_all_consumption = true): void
    {
        if (empty($accounts)) return;
        $now = $_SERVER['REQUEST_TIME'];
        $legacy_coins_cost = [];
        $common_coin_num = 0;
        foreach ($accounts as $account) {
            if ($account->scope === self::SCOPE_COMMON) {
                $common_coin_num += $account->consume_amount;
            }
            if ($account->consume_amount === 0 || $account->isLegacy()) {
                if ($account->isLegacy()) {
                    $legacy_coins_cost[$account->getCoinField()] = ($legacy_coins_cost[$account->getCoinField()] ?? 0) + $account->consume_amount;
                }
                continue;
            }
            $cost = $account->consume_amount;  // 负数时为付费问答取消返还钻石（贵族钻石、会员福利钻石等）
            $row_num = self::updateAll(
                [
                    'balance' => new Expression('balance - :cost', [':cost' => $cost]),
                    'modified_time' => $now,
                ],
                'id = :id AND balance >= :balance',
                [':id' => $account->id, ':balance' => $cost]
            );
            if ($row_num === 0) {
                Yii::info(
                    sprintf('balance not enough (optimistic lock): user_id=%d, balance=%d, price=%d',
                        $account->user_id, $account->balance, $account->consume_amount),
                    __METHOD__
                );
                throw new Exception('同时有多个订单正在被处理');
            }
        }

        if ($common_coin_num === 0) {
            return;
        }
        self::updateLegacyAccounts($accounts[0]->user_id, $legacy_coins_cost, $common_coin_num, $sync_all_consumption);
    }

    public static function updateLegacyAccounts(int $user_id, array $legacy_coins_cost, int $common_coin_num, bool $sync_all_consumption)
    {
        if ($user_id <= 0) {
            throw new Exception('用户 ID 不合法');
        }
        $condition = ['AND', ['id' => $user_id]];
        array_walk($legacy_coins_cost, function ($value, $field) use (&$condition) {
            if ($value > 0) {
                $condition[] = ['>=', $field, $value];
            }
        });

        $counters = array_map(function ($v) {
            return -$v;
        }, $legacy_coins_cost) + ['all_coin' => -$common_coin_num];
        if ($sync_all_consumption) {
            $counters += ['all_consumption' => $common_coin_num];
        }
        $row_affected = Balance::updateAllCounters($counters, $condition);
        if ($row_affected === 0) {
            Yii::info(sprintf('balance not enough (optimistic lock): user_id=%d', $user_id), __METHOD__);
            throw new HttpException(400, '您的余额不足，请充值后购买', *********);
        }
    }

    public static function generateCommonCoin(array $type_currency_map, int $tid, int $user_id, ?callable $func = null)
    {
        return self::generate($type_currency_map, $tid, $user_id,
            PayAccount::EXPIRE_TIME_FOREVER_EFFECTIVE, self::ATTR_COMMON_COIN, PayAccount::SCOPE_COMMON, $func);
    }

    public static function generate(array $type_currency_map, int $tid, int $user_id, int $expire_time, int $attr, int $scope, ?callable $func = null)
    {
        if (!array_key_exists($attr, self::ATTR_TYPE_OFFSET_MAP)) {
            throw new Exception("未知的 attr: {$attr}");
        }
        $accounts = [];
        foreach ($type_currency_map as $coin_type => $currency) {
            if ($currency !== 0) {
                $pay_account = new PayAccount([
                    'user_id' => $user_id,
                    'tid' => $tid,
                    'account_amount' => $currency,
                    'balance' => $currency,
                    'scope' => $scope,
                    'type' => $coin_type + self::ATTR_TYPE_OFFSET_MAP[$attr],  // TODO: 后续 type 仅作为钻石渠道，是否为贵族（免费）钻石改为 more.attr 来判断
                    'withhold_order' => self::COIN_CONSUME_ORDER[$coin_type],
                    'expire_time' => $expire_time,
                    'status' => self::STATUS_SUCCESS,
                    'more' => ['attr' => $attr],
                ]);
                if (is_callable($func)) {
                    $func($pay_account);
                }
                if (!$pay_account->save()) {
                    Yii::error('pay_account save error: ' . MUtils2::getFirstError($pay_account), __METHOD__);
                    throw new Exception('保存失败');
                }
                $accounts[] = $pay_account;
            }
        }

        return $accounts;
    }

    /**
     * 计算税费
     *
     * @param int|float $total_amount 花费的钻石数（涉及小数的情况：幸运签开箱礼物计算主播税费时）
     * @param float $fee_rate 平台费率
     * @return float 平台费（单位为元，精确到分）
     */
    public static function calFee($total_amount, float $fee_rate): float
    {
        $CENT_PER_YUAN_UNIT = 100;

        return ceil($total_amount * $fee_rate * $CENT_PER_YUAN_UNIT) / $CENT_PER_YUAN_UNIT;
    }

    /**
     * @workaround 后续改为贵族钻石/贵族免费钻石使用 more.attr 来判断
     *
     * @return int
     */
    public function getTypeOffset()
    {
        return intval($this->type - $this->getCoinType());
    }

    /**
     * @workaround 贵族钻石/贵族免费钻石由 more.attr 来判断后，直接使用 type 值
     *
     * @return int
     */
    public function getCoinType()
    {
        return $this->type % self::TYPE_OFFSET_STEP;
    }

    public function getCoinField(): string
    {
        $coin_field = self::TYPE_INDEX_COIN_FIELD_MAP[$this->getCoinType()];
        if ($this->more && array_key_exists('attr', $this->more)) {
            switch ($this->more['attr']) {
                case self::ATTR_COMMON_COIN:
                    return $coin_field;
                case self::ATTR_NOBLE_COIN:
                    return 'noble_' . $coin_field;
                case self::ATTR_NOBLE_FREE_COIN:
                    return 'noble_free_' . $coin_field;
                default:
                    throw new Exception(sprintf('wrong pay account: attr[%d], id[%d]', $this->more['attr'], $this->id));
            }
        }

        switch ($this->getTypeOffset()) {
            case self::ATTR_TYPE_OFFSET_MAP[self::ATTR_COMMON_COIN]:
                return $coin_field;
            case self::ATTR_TYPE_OFFSET_MAP[self::ATTR_NOBLE_COIN]:
                return 'noble_' . $coin_field;
            case self::ATTR_TYPE_OFFSET_MAP[self::ATTR_NOBLE_FREE_COIN]:
                return 'noble_free_' . $coin_field;
            default:
                throw new Exception(sprintf('wrong pay account: type[%d], id[%d]', $this->type, $this->id));
        }
    }

    /**
     * 更新贵族钻石有效期
     *
     * @param int $user_id
     * @param int $expire_time
     * @param bool $is_trial
     */
    public static function updateNobleCoinExpireTime(int $user_id, int $expire_time, bool $is_trial = false)
    {
        $start_time = strtotime('midnight', $_SERVER['REQUEST_TIME']);
        if (!$is_trial) {
            // 体验贵族不能解冻贵族钻石，可以续期贵族钻石
            // 普通贵族/上神贵族可解冻、续期贵族钻石
            $start_time -= self::NOBLE_COIN_FROZEN_PERIOD;
        }
        self::updateAll(
            [
                // 更新为贵族过期时间
                'expire_time' => $expire_time,
                'modified_time' => $_SERVER['REQUEST_TIME'],
            ],
            'user_id = :user_id AND expire_time >= :expire_time AND scope = :scope AND status = :status AND balance > 0',
            [
                ':user_id' => $user_id,
                ':expire_time' => $start_time,
                ':scope' => self::SCOPE_LIVE,
                ':status' => self::STATUS_SUCCESS,
            ]
        );
    }

    /**
     * @var bool
     */
    private $_is_legacy = false;

    public static function initiate(array $attribute, bool $is_legacy = false)
    {
        $acc = new self($attribute);
        $acc->_is_legacy = $is_legacy;
        return $acc;
    }

    /**
     * 是否旧的钻石账户（来自 balance）
     *
     * @return bool
     */
    public function isLegacy()
    {
        return $this->_is_legacy;
    }

}
