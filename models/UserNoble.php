<?php

namespace app\models;

use Yii;

class UserNoble
{
    const INVALID = 0;
    const VALID = 1;

    // 直播贵族
    const TYPE_LIVE_VIP_NOBLE = 1;
    // 上神贵族
    const TYPE_LIVE_VIP_HIGHNESS = 2;
    // 体验贵族
    const TYPE_LIVE_VIP_TRIAL_NOBLE = 3;

    /**
     * @var int 贵族的价格（单位：钻石）
     */
    private $price;

    /**
     * @var int 返回的贵族钻石数（单位：钻石）
     */
    private $rebate;

    /**
     * @var int 贵族最大的有效期截止时间点（含），秒级时间戳
     */
    private $max_expire_time;

    public function __construct(int $price, int $rebate, int $max_expire_time)
    {
        $this->price = $price;
        $this->rebate = $rebate;
        $this->max_expire_time = $max_expire_time;
    }

    /**
     * @deprecated 贵族钻石有效期跟随贵族有效期
     *
     * @param int $day 多少天之后过期
     * @param int $start_time 开始计算过期的时刻
     *
     * @return int 过期的时间戳
     */
    public static function getExpireAt(int $day, int $start_time)
    {
        $day += 1;
        return strtotime("+ $day day midnight", $start_time) - 1;
    }

    public static function costs2Currencies(array $costs)
    {
        $currencies = [];
        foreach (PayAccount::TYPE_INDEX_COIN_FIELD_MAP as $index => $key) {
            $currencies[$index] = (int)($costs[$key] ?? 0);
        }
        return $currencies;
    }

    public function getRebate(array $costs, int $tid, int $user_id)
    {
        if ((int)$this->price !== array_sum($costs)) {
            throw new \Exception('金额不匹配，不能得到贵族钻石');
        }
        $currencies = self::costs2Currencies($costs);
        $rebate = $this->rebate;
        $is_free = false; // 该字段同时包含两个含义，是否存在免费代币，是否为免费代币。
        if ($this->rebate > $this->price) {
            $is_free = true;
            $rebate -= $this->price;
        }
        $rebate_details = PayAccount::getRebateDetail($currencies, $rebate);
        $account_expire_time = $this->max_expire_time;
        if (!$account_expire_time) {
            $account_expire_time = UserNoble::getExpireAt(90, $_SERVER['REQUEST_TIME']);
        }
        if ($is_free) {
            PayAccount::generate($currencies, $tid, $user_id, $account_expire_time, PayAccount::ATTR_NOBLE_FREE_COIN, PayAccount::SCOPE_LIVE);
        }
        PayAccount::generate($rebate_details, $tid, $user_id, $account_expire_time, PayAccount::ATTR_NOBLE_COIN, PayAccount::SCOPE_LIVE);
    }

    /**
     * 获取可用的贵族钻石
     *
     * @param int $user_id
     * @return int
     */
    public static function getValidBalance(int $user_id): int
    {
        return PayAccounts::getAccounts($user_id, PayAccount::SCOPE_LIVE)->getTotalBalance();
    }

    /**
     * 获取冻结的贵族钻石
     * （贵族钻石冻结期：贵族过期时间 ~ 贵族过期时间 +90 天）
     *
     * @param int $user_id
     * @param int $expire_time
     * @return int
     */
    public static function getFrozenBalance(int $user_id, int $expire_time): int
    {
        $live_noble_frozen_balance = 0;
        if (strtotime('midnight', $_SERVER['REQUEST_TIME']) - $expire_time < PayAccount::NOBLE_COIN_FROZEN_PERIOD) {
            // 贵族过期后，过期后的 90 天内的贵族钻石处于冻结状态
            $live_noble_frozen_balance = PayAccounts::getAccounts($user_id, PayAccount::SCOPE_LIVE, $expire_time)->getTotalBalance();
        }

        return $live_noble_frozen_balance;
    }

    public static function isNobleValid(int $user_id): array
    {
        $user_vip_list = Yii::$app->sso->getUserVipList(
            [UserNoble::TYPE_LIVE_VIP_NOBLE, UserNoble::TYPE_LIVE_VIP_HIGHNESS, UserNoble::TYPE_LIVE_VIP_TRIAL_NOBLE],
            $user_id
        );
        if (empty($user_vip_list)) {
            return [
                'is_valid' => false,
                'max_expire_time' => 0,
            ];
        }

        $max_expire_time = max(array_column($user_vip_list, 'expire_time'));
        return [
            'is_valid' => $max_expire_time >= $_SERVER['REQUEST_TIME'],
            'max_expire_time' => $max_expire_time,
        ];
    }

    public static function getBalance(int $user_id)
    {
        ['is_valid' => $is_noble_valid, 'max_expire_time' => $max_expire_time] = self::isNobleValid($user_id);

        $live_noble_balance_status = UserNoble::INVALID;
        $live_noble_frozen_balance = 0;
        $live_noble_balance = 0;
        if ($max_expire_time > 0) {
            if ($is_noble_valid) {
                // 贵族未过期
                $live_noble_balance_status = UserNoble::VALID;
                $live_noble_balance = UserNoble::getValidBalance($user_id);
            } else {
                $live_noble_frozen_balance = UserNoble::getFrozenBalance($user_id, $max_expire_time);
            }
        }

        return [
            'live_noble_balance_status' => $live_noble_balance_status,
            'live_noble_balance' => UserNoble::VALID === $live_noble_balance_status ? $live_noble_balance : $live_noble_frozen_balance,
        ];
    }

}
