<?php

namespace app\models;

use missevan\util\MUtils as MUtils2;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "m_theatre_blind_box".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 * @property int $sound_id 音频 ID
 * @property int $drama_id 音频所属剧集 ID
 */
class MTheatreBlindBox extends ActiveRecord
{
    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_theatre_blind_box';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['create_time', 'modified_time', 'sound_id', 'drama_id'], 'integer'],
            [['sound_id', 'drama_id'], 'required'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => 'Create Time',
            'modified_time' => 'Modified Time',
            'sound_id' => 'Sound ID',
            'drama_id' => 'Drama ID',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 获取盲盒池
     *
     * @param int $user_id 用户 ID
     * @param bool $no_cache 是否不从缓存获取盲盒池
     * @return array 用户未抽和已抽的盲盒组
     */
    public static function getBlindBoxPool(int $user_id, bool $no_cache = false)
    {
        // NOTICE: 现在盲盒数量较少（几十条），故可以全部查出来放入缓存，之后超过 1000 条需要优化该方案（较低概率）
        $memcache = Yii::$app->memcache;
        $cache_key = MUtils2::generateCacheKey(THEATRE_USER_BIND_BOX_POOL, $user_id);
        if (!$no_cache && $blind_box_pool = $memcache->get($cache_key)) {
            $blind_box_pool = Json::decode($blind_box_pool);
        } else {
            $blind_box_pool = self::getBlindBoxs($user_id);
            // 数据为空时缓存 1 分钟避免缓存穿透
            // 有数据时缓存半小时保证用户停留在盲盒剧场，在这个时间内抽取的序列不发生变化
            $cache_duration = (empty($blind_box_pool[0]) && empty($blind_box_pool[1])) ? ONE_MINUTE : HALF_HOUR;
            // 缓存生效期间内盲盒相关音频下架等操作对盲盒无效，此情况一般不会发生，可忽略
            $memcache->set($cache_key, Json::encode($blind_box_pool), $cache_duration);
        }
        return $blind_box_pool;
    }

    /**
     * 根据用户 ID 获取盲盒
     *
     * @param int $user_id 用户 ID
     * @return array 用户未抽和已抽的盲盒组
     */
    public static function getBlindBoxs(int $user_id): array
    {
        // 现在盲盒数量较少（几十条），故可以全部查出来，查询结果会被放入缓存
        $all_boxs = MTheatreBlindBox::find()->select('sound_id, drama_id')->all();
        if (empty($all_boxs)) {
            return [[], []];
        }
        // 过滤掉盲盒中非过审状态的音频
        $all_sound_ids = array_column($all_boxs, 'sound_id');
        $pass_sound_ids = MSound::find()->select('id')
            ->where(['id' => $all_sound_ids, 'checked' => MSound::CHECKED_PASS])->column();
        if (empty($pass_sound_ids)) {
            return [[], []];
        }
        $pass_all_boxs = [];
        foreach ($all_boxs as $box) {
            if (in_array($box['sound_id'], $pass_sound_ids)) {
                $pass_all_boxs[] = $box;
            }
        }
        if (!$user_id) {
            // 未登录用户将所有盲盒视作未抽取过
            return [$pass_all_boxs, []];
        }
        // 获取抽到过的盲盒音频
        $draw_sound_ids = MTheatreDrawHistory::getUserDrawSoundIds($user_id);
        if (empty($draw_sound_ids)) {
            return [$pass_all_boxs, []];
        }
        $draw_boxs = $not_draw_boxs = [];
        foreach ($pass_all_boxs as $box) {
            if (in_array($box->sound_id, $draw_sound_ids)) {
                $draw_boxs[] = $box;
            } else {
                $not_draw_boxs[] = $box;
            }
        }
        return [$not_draw_boxs, $draw_boxs];
    }
}
