<?php

namespace app\models;

/**
 * This is the model class for table "m_game_element".
 *
 * @property integer $id
 * @property integer $create_time 创建时间戳，单位：秒
 * @property integer $modified_time 修改时间戳，单位：秒
 * @property integer $game_id 游戏 ID
 * @property integer $element_type 元素类型，1：音频；2：剧集
 * @property integer $element_id 元素 ID
 */
class MGameElement extends ActiveRecord
{
    // 音频
    const ELEMENT_TYPE_SOUND = 1;
    // 剧集
    const ELEMENT_TYPE_DRAMA = 2;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_game_element';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['game_id', 'element_type', 'element_id'], 'required'],
            [['create_time', 'modified_time', 'game_id', 'element_type', 'element_id'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'game_id' => '游戏 ID',
            'element_type' => '元素类型',  // 1：音频；2：剧集
            'element_id' => '元素 ID',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }
}
