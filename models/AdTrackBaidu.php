<?php

namespace app\models;

use Exception;
use Yii;

abstract class AdT<PERSON><PERSON><PERSON><PERSON> extends AdTrack implements AdTrackInterface
{
    const CALLBACK_EVENT_TYPE_ACTIVATE = 'activate';
    const CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION = 'retain_1day';
    const CALLBACK_EVENT_TYPE_PAY = 'orders';
    const CALLBACK_EVENT_TYPE_REGISTER = 'register';
    const CALLBACK_EVENT_TYPE_APP_CALLUP = 'deeplink';
    const CALLBACK_EVENT_USER_DEFINED = 'user_defined';

    const CALLBACK_EVENT_TYPE_MAP = [
        self::CALLBACK_EVENT_ACTIVATE => self::CALLBACK_EVENT_TYPE_ACTIVATE,
        self::CALLBACK_EVENT_ONE_DAY_RETENTION => self::CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION,
        self::CALL<PERSON><PERSON>K_EVENT_PAY => self::CALLBACK_EVENT_TYPE_PAY,
        self::CALLBACK_EVENT_REGISTER => self::CALLBACK_EVENT_TYPE_REGISTER,
        self::CALLBACK_EVENT_APP_CALLUP => self::CALLBACK_EVENT_TYPE_APP_CALLUP,  // 促活
        self::CALLBACK_EVENT_KEY_ACTION => self::CALLBACK_EVENT_USER_DEFINED,
        self::CALLBACK_EVENT_TRANSACTION => self::CALLBACK_EVENT_USER_DEFINED,  // 消费 / 充值关键行为
    ];

    public function callback(string $event_type, $arg = 0)
    {
        try {
            if (!array_key_exists($event_type, self::CALLBACK_EVENT_TYPE_MAP)) {
                throw new Exception('百度广告点击回传事件错误：' . $event_type);
            }
            $event = self::CALLBACK_EVENT_TYPE_MAP[$event_type];
            if (is_null($event)) {
                return true;
            }
            $data = Yii::$app->tools->requestRemote(
                $this->getCallbackUrl($event, $_SERVER['REQUEST_TIME'] * 1000, $arg)
            );
            if (!($data && $data['error_code'] === 0)) {
                throw new Exception(sprintf('百度广告点击回传失败：code[%d], msg[%s]', $data['error_code'] ?? -1, $data['error_msg'] ?? ''));
            }
            return true;
        } catch (Exception $e) {
            Yii::error('baidu ad error: ' . $e->getMessage(), __METHOD__);
            // PASS
        }
        return false;
    }

    /**
     * @link https://dev2.baidu.com/content?sceneType=0&pageId=101213&nodeId=663 百度信息流
     * @link https://dev2.baidu.com/content?sceneType=0&pageId=101214&nodeId=662 百度搜索
     */
    protected function getCallbackUrl(string $callback_event, int $conv_time, $arg = 0)
    {
        $value = 0;
        if ($callback_event === self::CALLBACK_EVENT_TYPE_PAY) {
            $value = $this->getCallbackPayAmount($arg);
        }
        $callback_url = str_replace('{{ATYPE}}', $callback_event, $this->track_id);
        $callback_url = str_replace('{{AVALUE}}', $value, $callback_url);
        $params = [
            'cb_event_time' => $conv_time,
            'cb_ip' => Yii::$app->request->userIP,
            'cb_os_version' => Yii::$app->equip->getOsVersion(),
            'cb_device_brand' => Yii::$app->equip->getPhone(),
        ];
        switch ($this->tracked_type) {
            case 1:
                if ($this->idfa) {
                    $params = array_merge($params, [
                        'cb_join_type' => 'idfa',
                        'cb_idfa' => $this->idfa,
                    ]);
                }
                break;
            case 5:
                $params = array_merge($params, [
                    'cb_join_type' => 'android_id',
                    'cb_android_id_md5' => $this->android_id_md5,
                ]);
                break;
            case 6:
                $params = array_merge($params, [
                    'cb_join_type' => 'imei',
                    'cb_imei_md5' => $this->imei_md5,
                ]);
                break;
        }
        $callback_url .= '&' . http_build_query($params);

        $sign = md5($callback_url . $this->getAKey());
        return $callback_url . '&sign=' . $sign;
    }

    protected function getAKey()
    {
        return $this->more['auth_key'];
    }

}
