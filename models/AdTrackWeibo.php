<?php

namespace app\models;

use Exception;
use Yii;

class AdT<PERSON><PERSON>ei<PERSON> extends AdTrack implements AdTrackInterface
{
    const CALLBACK_GATEWAY = 'https://appmonitor.biz.weibo.com/sdkserver/active';

    const CALLBACK_EVENT_TYPE_ACTIVATE = 1;  // 激活
    const CALLBACK_EVENT_TYPE_PURCHASE_BY_ORDER = 2;  // 下单购买（微博渠道给我们加了白名单可以按照付费模型出价优化）
    const CALLBACK_EVENT_TYPE_REGISTER = 3;  // 注册
    const CALLBACK_EVENT_TYPE_PAY = 4;  // 付费
    const CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION = 7;  // 次日留存

    const CALLBACK_EVENT_TYPE_MAP = [
        self::CALLBACK_EVENT_ACTIVATE => self::CALLBACK_EVENT_TYPE_ACTIVATE,
        self::CALLBACK_EVENT_ONE_DAY_RETENTION => self::CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION,
        self::CALLBACK_EVENT_PAY => self::CALLBACK_EVENT_TYPE_PURCHASE_BY_ORDER,
        self::CALLBACK_EVENT_REGISTER => self::CALLBACK_EVENT_TYPE_REGISTER,
        self::CALLBACK_EVENT_KEY_ACTION => null,
        self::CALLBACK_EVENT_TRANSACTION => null,  // 无消费 / 充值关键行为
    ];

    public function callback(string $event_type, $arg = 0)
    {
        try {
            if (!array_key_exists($event_type, self::CALLBACK_EVENT_TYPE_MAP)) {
                throw new Exception('微博广告点击回传事件错误：' . $event_type);
            }
            $event = self::CALLBACK_EVENT_TYPE_MAP[$event_type];
            if (is_null($event)) {
                return true;
            }
            $data = Yii::$app->tools->requestRemote(
                $this->getCallbackUrl($event, $_SERVER['REQUEST_TIME'], $arg)
            );
            if (!($data && $data['Code'] === '0')) {
                throw new Exception(sprintf('微博广告点击回传失败：code[%d], msg[%s]', $data['Code'] ?? '-1', $data['result'] ?? ''));
            }
            return true;
        } catch (Exception $e) {
            Yii::error('weibo ad error: ' . $e->getMessage(), __METHOD__);
            // PASS
        }
        return false;
    }

    private function getCallbackUrl(int $callback_event, int $conv_time, $arg)
    {
        $url = sprintf(
            '%s?company=%s&IMP=%s&action_type=%d&active_time=%d',
            self::CALLBACK_GATEWAY,
            $this->getCompany(),
            $this->getIMP(),
            $callback_event,
            $conv_time
        );
        // if ($callback_event === self::CALLBACK_EVENT_TYPE_PAY) {
        //    $url .= sprintf('&price=%d', $arg);
        // }
        switch ($this->tracked_type) {
            case 1:
                return $url . '&idfa=' . $this->idfa_md5;
            case 2:
                return $url . '&ua=' . $this->ua . '&ip=' . $this->ip;
            case 4:
                return $url . '&oaid=' . $this->oaid;
            case 5:
                return $url . '&imei=' . $this->imei_md5 . '&ip=' . $this->ip;
            default:
                return $url;
        }
    }

    private function getIMP()
    {
        return $this->track_id;
    }

    private function getCompany()
    {
        return $this->more['company'];
    }

}
