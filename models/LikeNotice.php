<?php

namespace app\models;

use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "like_notice".
 *
 * @property integer $id
 * @property integer $c_user_id
 * @property integer $a_user_id
 * @property integer $type
 * @property integer $comment_id
 * @property string $title
 * @property integer $isread
 * @property integer $time
 * @property integer $eId
 * @property integer $sub
 * @property string $content
 */
class LikeNotice extends ActiveRecord
{
    // 消息状态，0：未读；1：已读
    const NOT_READ = 0;
    const ALREADY_READ = 1;

    public $icon;
    public $username;
    public $authenticated;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'like_notice';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['c_user_id', 'a_user_id', 'comment_id', 'title', 'time', 'eId', 'content'], 'required'],
            [['c_user_id', 'a_user_id', 'type', 'comment_id', 'isread', 'time', 'eId', 'sub'], 'integer'],
            [['title'], 'string'],
            [['content'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'c_user_id' => '点赞用户',
            'a_user_id' => '被点赞用户',
            'type' => '1是音频，2是专辑，3是新闻,  4频道,  6专题,  7活动',
            'comment_id' => '评论id',
            'title' => '消息标题',
            'isread' => '是否已读',
            'time' => '点赞时间',
            'eId' => '资源id',
            'sub' => '0是父评论点赞1是子评论点赞',
            'content' => '消息内容',
        ];
    }

    /**
     * 添加评论点赞提醒消息
     *
     * @param SoundComment|SoundSubComment $comment 点赞的评论对象
     * @param int $user_id 点赞用户 ID
     * @throws HttpException 添加消息失败抛出异常
     */
    public static function addNotice($comment, int $user_id)
    {
        if ((int)$comment->userid === $user_id) return;
        $sub = (int)isset($comment->comment_id);
        if (!self::find()->where('comment_id = :comment_id AND sub = :sub AND c_user_id = :c_user_id',
            [':comment_id' => $comment->id, ':sub' => $sub,
                ':c_user_id' => $user_id])->exists()
        ) {
            if ($f = !isset($comment->element_id)) {
                $f_comment = SoundComment::findOne(['id' => $comment->comment_id]);
                if (!$f_comment) return;
            }
            $element_id = $f ? $f_comment->element_id : $comment->element_id;
            $type = $f ? $f_comment->c_type : $comment->c_type;
            $notice = new self();
            $notice->c_user_id = $user_id;
            $notice->a_user_id = $comment->userid;
            $notice->type = (int)$type;
            $notice->eId = (int)$element_id;
            $notice->comment_id = $comment->id;
            $notice->title = $comment->comment_content;
            $notice->content = '赞了这条评论';
            $notice->sub = $sub;
            $notice->isread = self::NOT_READ;
            $notice->time = $_SERVER['REQUEST_TIME'];
            if (!$notice->validate()) {
                // 若保存失败，取出验证错误中第一个 key 下的信息作为提示抛出异常
                throw new HttpException(400, MUtils::getFirstError($notice));
            }
            if (!$notice->save(false)) {
                throw new HttpException(500, '点赞失败 T_T');
            }
        }
    }

    public static function getPicOfCommentNotice(&$likes)
    {
        if (!$likes) return [];
        $user_ids = array_column($likes, 'c_user_id');

        if ($user_ids) {
            $user_ids = array_unique($user_ids);
            $users = Mowangskuser::find()->select('id, username, iconurl, boardiconurl, icontype, avatar, confirm')
                ->where(['id' => $user_ids])->all();
            unset($user_ids);
            $users = array_column($users, NULL, 'id');
        }

        foreach ($likes as &$like) {
            $like->icon = $users[$like->c_user_id]->iconurl ?? '';
            $like->username = $users[$like->c_user_id]->username ?? '';
            $like->authenticated = $users[$like->c_user_id]->authenticated ?? 0;
            //$like->time = date('Y-m-d H:i:s', (int)$like->time);
        }
    }

    /**
     * 获取用户未读点赞消息数量
     *
     * @param int $user_id 用户 ID
     * @return int 点赞消息数量
     */
    public static function getUserLikeNoticeCount(int $user_id): int
    {
        if (!$user_id) {
            return 0;
        }
        return LikeNotice::find()->where(['a_user_id' => $user_id, 'isread' => self::NOT_READ])->count();
    }
}
