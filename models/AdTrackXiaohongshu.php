<?php

namespace app\models;

use app\components\util\Equipment;
use Exception;
use Yii;
use yii\helpers\Json;

class AdTrackXiaohongshu extends AdTrack implements AdTrackInterface
{
    const CALLBACK_RETRIES = 3;

    // 回传请求的 URL 地址，支持 POST 请求
    const CALLBACK_URL = 'https://adapi.xiaohongshu.com/api/open/conversion';

    // 事件场景，固定为 701，代表应用下载
    const CALLBACK_SCENE = '701';

    // 操作系统
    const OS_ANDROID = 0;
    const OS_IOS = 1;

    const CALLBACK_EVENT_TYPE_ACTIVATE = 411;
    const CALLBACK_EVENT_TYPE_REGISTER = 412;
    const CALLBACK_EVENT_TYPE_PAY = 413;
    const CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION = 450;

    const CALLBACK_EVENT_TYPE_MAP = [
        self::CALLBACK_EVENT_ACTIVATE => self::CALLBACK_EVENT_TYPE_ACTIVATE,
        self::CALLBACK_EVENT_ONE_DAY_RETENTION => self::CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION,
        self::CALLBACK_EVENT_REGISTER => self::CALLBACK_EVENT_TYPE_REGISTER,
        self::CALLBACK_EVENT_PAY => self::CALLBACK_EVENT_TYPE_PAY,
        self::CALLBACK_EVENT_KEY_ACTION => null,
        self::CALLBACK_EVENT_TRANSACTION => null,
        self::CALLBACK_EVENT_APP_CALLUP => null,
    ];

    /**
     * 转化事件回调
     *
     * @param string $event_type 事件类型
     * @param mixed $pay_amount 支付金额（单位：元），仅付费场景传入此参数
     */
    public function callback(string $event_type, $pay_amount = 0)
    {
        try {
            if (!array_key_exists($event_type, self::CALLBACK_EVENT_TYPE_MAP)) {
                throw new Exception('小红书点击回传事件错误：' . $event_type);
            }
            $event_type_id = self::CALLBACK_EVENT_TYPE_MAP[$event_type];
            if (is_null($event_type_id)) {
                return true;
            }
            $body = Json::encode($this->getCallbackBody($event_type_id, $pay_amount));
            $headers = [
                'Content-Type' => 'application/json',
            ];

            $retries = self::CALLBACK_RETRIES;
            while ($retries > 0) {
                $data = Yii::$app->tools->requestRemote(self::CALLBACK_URL, [], 'POST', $body, 0, $headers);
                if ($data && $data['code'] === 0) {
                    return true;
                }
                $retries--;
                if ($retries > 0) {
                    Yii::warning(sprintf(
                        '小红书点击回传失败，准备重试：code[%d], msg[%s]',
                        $data['code'] ?? null, $data['msg'] ?? null
                    ), __METHOD__);
                    usleep(200 * 1000);
                }
            }

            throw new Exception(sprintf(
                '小红书点击回传失败：code[%d], msg[%s]',
                $data['code'] ?? null, $data['msg'] ?? null
            ));
        } catch (Exception $e) {
            Yii::error('Xiaohongshu ad callback error: ' . $e->getMessage(), __METHOD__);
        }
        return false;
    }

    private function getCallbackBody(int $event_type, $pay_amount = 0)
    {
        $timestamp = intval(microtime(true) * 1000);
        $body = [
            'event_type' => $event_type,
            'click_id' => $this->track_id,
            'timestamp' => $timestamp, // 事件发生时间（单位：毫秒）
            'platform' => '猫耳FM',
            'advertiser_id' => $this->more['account_id'],
            'scene' => self::CALLBACK_SCENE,
            'os' => $this->getCallbackOS(),
        ];
        if ($this->tracked_type === self::TRACKED_TYPE_IDFA) {
            $body['idfa_md5'] = $this->idfa_md5;
        }

        $properties = [];
        if ($event_type === self::CALLBACK_EVENT_TYPE_PAY) {
            // 付费金额（单位：分）
            $properties['pay'] = $this->getCallbackPayAmount($pay_amount);
        }
        if (in_array($event_type, [self::CALLBACK_EVENT_TYPE_PAY, self::CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION])
                && $this->more && array_key_exists('activate_time', $this->more)
        ) {
            // 激活时间（单位：毫秒）
            $properties['activate_timestamp'] = $this->more['activate_time'] * 1000;
        }
        if ($properties) {
            $body['context'] = [
                'properties' => $properties,
            ];
        }

        return $body;
    }

    private function getCallbackOS()
    {
        switch ($this->os) {
            case Equipment::Android:
                return self::OS_ANDROID;
            case Equipment::iOS:
                return self::OS_IOS;
            default:
                throw new Exception('暂不支持的设备类型：' . $this->os);
        }
    }
}
