<?php

namespace app\models;

use app\components\util\Equipment;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "you_might_like_module".
 *
 * @property string $id
 * @property string $title 模块名称
 * @property int $creator_id 负责人 ID
 * @property int $element_type 模块类型，1：音单模块；2：剧集模块；3：音频模块；5：直播模块
 * @property int $element_attr 模块的属性，比特位第 3 位为 1 时表示封面图样式为堆叠样式，为 0 时表示封面图样式为扁平样式
 * @property int $element_style 模块样式，0：竖版（默认）；1：横版；2：排行榜；3：滑动
 * @property int $weekly_task_target 每周更新音频要求的数量
 * @property int $skipped 比特位为 1 -- 跳过对应类型检查，0 -- 不跳过；
 * 最低比特位 -- weekly-module，次低比特位 -- 以后用到的请向这里添加，依此类推
 * @property int $update_type 模块更新类型，1：主题模块；2：轮换模块；3：换血模块
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 * @property string $more 额外信息，格式为 json 字符串
 * url：更多按钮跳转链接，不下发或为空时跳转到默认的原生自定义模块详情页
 * 详情文档：https://info.missevan.com/pages/viewpage.action?pageId=97891396
 */
class YouMightLikeModule extends ActiveRecord
{
    // 模块的分类
    const TYPE_ALBUM = 1;
    const TYPE_DRAMA = 2;
    const TYPE_SOUND = 3;

    // 模块是否排行榜样式标识，比特位第二位为 1 时表示为排行榜样式
    const ELEMENT_ATTR_RANK = 0b10;
    // 模块是否为音频分区下精选音单，比特位第 4 位为 1 时为音频分区下精选音单
    const ELEMENT_ATTR_CLASSIC_ALBUM = 0b1000;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'you_might_like_module';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title', 'element_type'], 'required'],
            [
                ['creator_id', 'element_type', 'element_attr', 'weekly_task_target', 'skipped', 'update_type', 'create_time', 'modified_time'],
                'integer'
            ],
            [['title'], 'string', 'max' => 100],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'title' => '模块名称',
            'creator_id' => '负责人 ID',
            'element_type' => '模块元素类型',  // 1：音单模块；2：剧集模块；3：音频模块；5：直播模块
            'element_attr' => '模块的属性',  // 模块封面图样式，比特位第 3 位为 1 时为堆叠样式，为 0 时为扁平样式
            'element_style' => '模块样式',  // 0：竖版（默认）；1：横版；2：排行榜；3：滑动
            'weekly_task_target' => '每周更新音频要求的数量',
            'skipped' => '检测类型',
            'update_type' => '模块更新类型',  // 0: 普通模块；1: 主题模块；2：轮换模块；3：换血模块
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'more' => '额外信息',  // 格式为 json 字符串
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        $this->more = $this->more ? Json::decode($this->more) : [];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $this->more = $this->more ? Json::encode($this->more) : null;
        return true;
    }

    /**
     * 获取画像下模块信息
     *
     * @param $persona
     * @return array
     * @throws \Exception
     */
    public static function getPersonModules(int $persona): array
    {
        $from_cache = true;
        // WORKAROUND: iOS < 6.0.9、安卓 < 6.0.9 的版本用分区名称下发直播推荐元素中个性词条名称
        // TODO: 后续修改时需要查询一下是否还有用旧版本客户端的新用户，若没有或极少则无需再兼容旧版
        $is_old = ($persona === MPersonaModuleElement::PERSONA_ID_NEW_USER)
            && Equipment::isAppOlderThan('6.0.9', '6.0.9');
        $key = $is_old ? KEY_GUESSYOURLIKES_MODULES_OLD : KEY_GUESSYOURLIKES_MODULES;
        $modules_data_key = MUtils2::generateCacheKey($key, $persona);
        $modules = MUtils2::getOrSetDistrubutedCache($modules_data_key, function () use ($persona, &$from_cache, $is_old) {
            $from_cache = false;
            // 获取画像对应的模块数据
            $modules = MPersonaModuleElement::getModules($persona);
            if (empty($modules)) {
                Yii::warning("用户画像下未设置可见模块，画像 ID: {$persona}", __METHOD__);
                return $modules;
            }
            // 获取模块包含的元素（音单 ID、剧集 ID、音频 ID 和 主播 ID）
            $module_ids = array_unique(array_column($modules, 'module_id'));
            $elements = MPersonaModuleElement::getElementsRecommended($module_ids);
            $album_data = MPersonaModuleElement::getAlbums($elements);
            $drama_data = MPersonaModuleElement::getDramas($elements, Yii::$app->user->id);
            $sound_data = MPersonaModuleElement::getSounds($elements);
            $live_data = [];
            if ($persona === MPersonaModuleElement::PERSONA_ID_NEW_USER) {
                $live_data = MPersonaModuleElement::getLives($elements, $is_old);
            }
            $elems_module_group = MUtils2::groupArray(array_merge($album_data, $drama_data, $sound_data, $live_data), 'module_id');
            $return_modules = [];
            foreach ($modules as $module) {
                if ($persona !== MPersonaModuleElement::PERSONA_ID_NEW_USER
                        && $module['type'] === MPersonaModuleElement::ELEMENT_TYPE_SOUND
                        && $module['style'] !== MPersonaModuleElement::MODULE_STYLE_SLIDE) {
                    // 首页目前只支持滑动样式的音频模块，不支持横版、竖版样式的音频模块（新人 Tab 页都支持）
                    continue;
                }
                $module['elements'] = $elems_module_group[$module['module_id']] ?? [];
                if (empty($module['elements'])) {
                    continue;
                }
                // 去除客户端未使用的字段
                unset($module['id'], $module['sort']);
                $return_modules[] = $module;
            }
            return $return_modules;
        }, FIVE_MINUTE);
        return [$modules, $from_cache];
    }

    /**
     * 截取模块（只支持首页和新人 Tab 的截取）
     *
     * @param array $module 模块信息
     * @param int $persona_id 画像 ID
     */
    public static function truncateModule(array &$module, int $persona_id)
    {
        $elements = $module['elements'];
        switch ($module['style']) {
            case MPersonaModuleElement::MODULE_STYLE_DEFAULT:
                // 竖版（默认）模块，截取所需数据
                if ($persona_id === MPersonaModuleElement::PERSONA_ID_NEW_USER) {
                    if ($module['type'] === MPersonaModuleElement::ELEMENT_TYPE_LIVE) {
                        // 直播类型的模块截取所需数据（直播类型的模块目前只有新人 Tab 支持）
                        $module['elements'] = array_slice($elements, 0,
                            MPersonaModuleElement::APP_LIVE_RECOMMENDED_COUNT);
                        break;
                    }
                    $module['elements'] = array_slice($elements, 0,
                        MPersonaModuleElement::NEW_USER_ELEMENT_RECOMMENDED_MAX_COUNT);
                    break;
                }
                $module['elements'] = array_slice($elements, 0,
                    MPersonaModuleElement::ELEMENT_RECOMMENDED_COUNT);
                break;
            case MPersonaModuleElement::MODULE_STYLE_HORIZONTAL:
                // 横版模块，截取所需数据
                if ($persona_id === MPersonaModuleElement::PERSONA_ID_NEW_USER) {
                    $module['elements'] = array_slice($elements, 0,
                        MPersonaModuleElement::NEW_USER_ELEMENT_RECOMMENDED_MAX_COUNT);
                    break;
                }
                $module['elements'] = array_slice($elements, 0,
                    MPersonaModuleElement::ELEMENT_RECOMMENDED_COUNT);
                break;
            case MPersonaModuleElement::MODULE_STYLE_TOP_PLAY_STYLE:
                // 排行榜模块，取全部数据
                $module['elements'] = $elements;
                break;
            case MPersonaModuleElement::MODULE_STYLE_SLIDE:
                // 目前只有盲盒剧场可以配置滑动剧集模块，新人 tab 等页面展示滑动剧集模块表现异常，因此对其进行过滤
                if ($module['type'] === MPersonaModuleElement::ELEMENT_TYPE_SOUND) {
                    // 对滑动音频模块截取所需数据
                    $module['elements'] = array_slice($elements, 0,
                        MPersonaModuleElement::APP_SLIDE_SOUND_RECOMMENDED_MAX_COUNT);
                } else {
                    Yii::error("画像 ID: {$persona_id} 错误配置了滑动模块，module_id: {$module['module_id']}, module_type: {$module['type']}", __METHOD__);
                    $module = null;
                }
                break;
            default:
                // 模块数据错误时记录错误日志并降级
                $module['elements'] = array_slice($elements, 0,
                    MPersonaModuleElement::ELEMENT_RECOMMENDED_COUNT);
                Yii::error("模块数据错误，module_id: {$module['module_id']}, module_type: {$module['type']}, module_style: {$module['style']}",
                    __METHOD__);
                // PASS
                break;
        }
    }

    /**
     * 设置剧集模块封面图
     *
     * @param array $module 模块信息
     */
    public static function processDramaModuleCover(array &$module)
    {
        if ($module['type'] !== self::TYPE_DRAMA) {
            return;
        }
        // 是否是竖版
        $is_vertical = $module['style'] === MPersonaModuleElement::MODULE_STYLE_DEFAULT;
        $module['elements'] = array_map(function ($drama) use ($is_vertical) {
            // 根据模块样式选择使用不同的封面图片（竖版样式使用长方形图，横版和排行榜样式使用正方形图）
            $image_keys = ['thumbnail', 'cover'];
            foreach ($image_keys as $image_key) {
                if (!key_exists($image_key, $drama)) {
                    $drama[$image_key] = Yii::$app->params['defaultCoverUrl'];
                }
            }
            $drama['front_cover'] = $is_vertical ? $drama['thumbnail'] : $drama['cover'];
            unset($drama['thumbnail'], $drama['cover']);
            return $drama;
        }, $module['elements']);
    }
}
