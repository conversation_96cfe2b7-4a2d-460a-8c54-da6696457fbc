<?php

namespace app\models;

use app\components\util\MUtils;
use yii\helpers\Json;
use Yii;

/**
 * This is the model class for table "teenager_recommended_elements".
 *
 * @property int $id
 * @property int $element_id 元素 ID
 * @property int $element_type 元素类型 0 音频，1 剧集
 * @property int $archive 是否归档 1 为是，0 为否
 * @property int $create_time
 * @property int $modified_time
 */
class TeenagerRecommendedElements extends ActiveRecord
{
    // 元素类型
    const ELEMENT_SOUND = 0;
    const ELEMENT_DRAMA = 1;
    public static $element_types = [
        self::ELEMENT_SOUND,
        self::ELEMENT_DRAMA,
    ];

    // 是否归档（1 为是，0 为否）
    const ARCHIVE_ONLINE = 0;
    const ARCHIVE_ARCHIVED = 1;

    // 排序方式
    const ORDER_RANDOM = 0;
    const ORDER_VIEW_COUNT = 1;
    public static $element_orders = [
        self::ORDER_RANDOM,
        self::ORDER_VIEW_COUNT,
    ];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'teenager_recommended_elements';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['element_id', 'element_type', 'archive', 'create_time', 'modified_time'], 'integer'],
            [['element_type'], 'required'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'element_id' => '元素 ID',
            'element_type' => '元素类型', //  0 音频，1 剧集
            'archive' => '是否归档', //  1 为是，0 为否
            'create_time' => 'Create Time',
            'modified_time' => 'Modified Time',
        ];
    }

    /**
     * 获取青少年单音列表
     *
     * @param int $order 排序方式（0 随机、1 按播放量）
     * @param int $page
     * @param int $page_size
     * @return ReturnModel
     */
    public static function getTeenagerSounds(
            int $order, int $page = 1, int $page_size = PAGE_SIZE_20): ReturnModel
    {
        $memcache = Yii::$app->memcache;
        $sound_ids = [];
        if (!(($cache_data = $memcache->get(KEY_TEENAGER_SOUND_IDS)) &&
            ($sound_ids = Json::decode($cache_data) ?: []))) {
            // TODO: 这里目前使用的是单音表中 catalog_id = 101 的音，之后可能会使用青少年表
            // $sound_ids = self::find()
            //    ->select('element_id')
            //    ->where([
            //        'element_type' => TeenagerRecommendedElements::ELEMENT_SOUND,
            //        'archive' => self::ARCHIVE_ONLINE
            //    ])->column();
            $query = MSound::find()->select('id')
                ->where([
                    'catalog_id' => Catalog::CATALOG_ID_SOUND_NATURAL,
                    'checked' => Msound::CHECKED_PASS,
                    'pay_type' => MSound::SOUND_FREE,
                ])->andWhere('NOT refined & :not_refined', [
                    ':not_refined' => MSound::REFINED_POLICE_AND_BLOCK
                ]);
            $sound_ids = MUtils::getRandomModels($query, 1000);
            if (empty($sound_ids)) {
                return ReturnModel::empty($page, $page_size);
            }
            $sound_ids = array_column($sound_ids, 'id');
            $memcache->set(KEY_TEENAGER_SOUND_IDS, Json::encode($sound_ids), TEN_MINUTE);
        }
        $query = MSound::find()
            ->select('id, soundstr, intro, view_count, comment_count, comments_count, sub_comments_count,
                cover_image, duration');
        $total_count = count($sound_ids);
        if ($order === self::ORDER_RANDOM) {
            // 随机排序
            $offset = ($page - 1) * $page_size;
            $sound_ids = array_slice($sound_ids, $offset, $page_size);
            if (empty($sound_ids)) {
                return ReturnModel::empty($page, $page_size);
            }
            $query = $query->where(['id' => $sound_ids]);
            $m_sounds = MUtils::getRandomModels($query, $page_size);
            $return_data = ReturnModel::getPaginationData($m_sounds, $total_count, $page, $page_size);
        } else {
            // 按播放量排序
            $m_sounds = $query->where(['id' => $sound_ids])->orderBy('view_count DESC');
            $return_data = MUtils::getPaginationModels($m_sounds, $page_size, ['current_page' => $page]);
        }
        $m_sound_ids = array_column($return_data->Datas, 'id');
        $exist_video_sound_ids = SoundVideo::find()->select('sid')
            ->where(['sid' => $m_sound_ids, 'checked' => SoundVideo::CHECKED_PASS])->column();
        $return_data->Datas = array_map(function ($sound) use ($exist_video_sound_ids) {
            return [
                'id' => $sound['id'],
                'front_cover' => $sound['front_cover'],
                'soundstr' => $sound['soundstr'],
                'intro' => $sound['intro'],
                'view_count' => $sound['view_count'],
                'comment_count' => $sound['comment_count'],
                'all_comments' => $sound['all_comments'],
                'duration' => $sound['duration'],
                'video' => in_array($sound['id'], $exist_video_sound_ids),
            ];
        }, $return_data->Datas);
        return $return_data;
    }

    /**
     * 获取青少年剧集列表
     *
     * @param $user_id 用户 id
     * @param int $order 排序方式（0 随机、1 按播放量）
     * @param int $page
     * @param int $page_size
     * @return ReturnModel
     * @throws \Exception
     */
    public static function getTeenagerDramas(
            $user_id, int $order, int $page = 1, int $page_size = PAGE_SIZE_20): ReturnModel
    {
        // 获取剧集详情
        $memcache = Yii::$app->memcache;
        $drama_list = [];
        if (!(($cache_data = $memcache->get(KEY_TEENAGER_DRAMAS)) &&
            ($drama_list = Json::decode($cache_data) ?: []))) {
            $drama_ids = self::find()
                ->select('element_id')
                ->where([
                    'element_type' => TeenagerRecommendedElements::ELEMENT_DRAMA,
                    'archive' => self::ARCHIVE_ONLINE
                ])->column();
            $drama_ids = array_map('intval', $drama_ids);
            $drama_list = Drama::getTeenagerDramas($drama_ids);
            if (empty($drama_list)) {
                return ReturnModel::empty($page, $page_size);
            }
            $drama_list = array_map(function ($value) {
                return [
                    'id' => $value['id'],
                    'name' => $value['name'],
                    'abstract' => $value['abstract'],
                    'cover_color' => $value['cover_color'],
                    'pay_type' => $value['pay_type'],
                    'front_cover' => $value['cover'],
                    'view_count' => $value['view_count'],
                    'newest' => $value['newest'],
                    'integrity' => $value['integrity'],
                    'play_sound_id' => $value['play_sound_id'],
                ];
            }, $drama_list);
            $memcache->set(KEY_TEENAGER_DRAMAS, Json::encode($drama_list), TEN_MINUTE);
        }
        $total_count = count($drama_list);
        if ($order === self::ORDER_VIEW_COUNT) {
            // 按音频播放量排序
            $drama_list = MUtils::arrayOrderByField($drama_list, 'view_count', SORT_DESC);
        }
        // 数组分页
        $offset = ($page - 1) * $page_size;
        $drama_list = array_slice($drama_list, $offset, $page_size);
        if (empty($drama_list)) {
            return ReturnModel::empty($page, $page_size);
        }
        if ($order === self::ORDER_RANDOM) {
            // 打乱数组
            shuffle($drama_list);
        }
        $return_data = ReturnModel::getPaginationData($drama_list, $total_count, $page, $page_size);

        if ($user_id) {
            // 登录状态获取用户历史播放记录
            $drama_ids = array_column($return_data->Datas, 'id');
            $history_info = Drama::rpc('api/get-saw-history', [
                'drama_ids' => $drama_ids,
                'user_id' => $user_id,
            ]);
            $drama_history = array_column($history_info, 'sound_id', 'drama_id');
            $return_data->Datas = array_map(function ($drama) use ($drama_history) {
                $drama['play_sound_id'] = $drama_history[$drama['id']]['sound_id'] ?? $drama['play_sound_id'];
                return $drama;
            }, $return_data->Datas);
        }
        return $return_data;
    }
}
