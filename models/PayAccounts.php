<?php

namespace app\models;

use Exception;
use missevan\util\MUtils;
use Yii;
use yii\db\Expression;

class PayAccounts
{
    /**
     * @var PayAccount[] 用户可用所有账户
     */
    private $accounts = [];

    /**
     * @var PayAccount[] 用户发生变动的所有账户
     */
    private $change_accounts = [];

    /**
     * @var int|null 账户总余额
     */
    private $total_balance = null;

    /**
     * @var int 需要花费的价格
     */
    private $consume_amount = 0;

    /**
     * PayAccounts constructor.
     * @param PayAccount[] $accounts
     */
    public function __construct(array $accounts)
    {
        $this->accounts = $accounts;
    }

    /**
     * 获取所有可以使用的账户
     *
     * @param int $user_id 用户 ID
     * @param int|array $scope 账户类型（0 为普通钻石账户，1 为直播贵族钻石账户）
     * @param int $expire_time 过期时间（默认为当前时间）
     * @param Balance|null $legacy_balance 旧的钻石账户
     * @return PayAccounts 获取用户所有有效账户
     */
    public static function getAccounts(int $user_id, int|array $scope, int $expire_time = -1, ?Balance $legacy_balance = null): self
    {
        if ($expire_time === -1) {
            $expire_time = $_SERVER['REQUEST_TIME'];
        }

        $accounts = PayAccount::find()
            ->where('user_id = :user_id AND balance > 0 AND expire_time >= :expire_time')
            ->params([':user_id' => $user_id, ':expire_time' => $expire_time])
            ->andWhere(['status' => PayAccount::STATUS_SUCCESS, 'scope' => $scope])
            ->orderBy(['create_time' => SORT_ASC])
            ->all();
        $obj = new self($accounts);
        if ($scope === PayAccount::SCOPE_COMMON || (is_array($scope) && in_array(PayAccount::SCOPE_COMMON, $scope))) {
            if (is_null($legacy_balance)) {
                $legacy_balance = Balance::getByPk($user_id);
            }
            $legacy_accounts = self::legacyBalanceToAccounts($legacy_balance);
            $obj->addAccounts($legacy_accounts);
        }
        return $obj;
    }

    public function getTotalBalanceByScope(int $scope)
    {
        $accounts = array_filter($this->accounts, function ($account) use ($scope) {
            return $account->scope === $scope;
        });
        return PayAccount::getBalance($accounts);
    }

    /**
     * 返回账户余额
     *
     * @return int 账户余额
     */
    public function getTotalBalance(): int
    {
        if (is_null($this->total_balance)) {
            $this->total_balance = PayAccount::getBalance($this->accounts);
        }
        return $this->total_balance - $this->consume_amount;
    }

    /**
     * 返回消费后的余额
     *
     * @return int
     */
    public function getBalance(): int
    {
        return $this->getTotalBalance();
    }

    /**
     * 花费金额
     *
     * @param int $price 价格（钻石）
     * @param array $deduct_order 扣钻顺序
     * @return int 账户不够付的钻石余额
     * @throws Exception
     */
    public function cost(int $price, array $deduct_order = PayAccount::COIN_CONSUME_ORDER): int
    {
        [$change_accounts, $remaining] = PayAccount::getChangeAccount($this->accounts, $price, $deduct_order);
        $this->change_accounts = $change_accounts;
        $this->consume_amount += ($price - $remaining);
        return $remaining;
    }

    /**
     * @param array|PayAccount[] $change_accounts
     * @return void
     */
    public function setChangeAccounts(array $change_accounts)
    {
        $this->change_accounts = $change_accounts;
    }

    /**
     * 获取变更的账户
     *
     * @return PayAccount[]
     */
    public function getChangeAccounts()
    {
        return $this->change_accounts;
    }

    /**
     * 账户是否发生变化
     *
     * @return bool
     */
    public function hasChanged(): bool
    {
        return (bool)$this->change_accounts;
    }

    /**
     * 获取渠道费用
     *
     * @return float
     */
    public function getTotalFee(TransactionLog $transaction_log): float
    {
        if (empty($this->change_accounts)) {
            return 0;
        }
        return array_reduce($this->change_accounts, function ($total_fee, $account) use ($transaction_log) {
            /**
             * @var PayAccount $account
             */
            $total_fee += $account->getFee($transaction_log);
            return $total_fee;
        }, 0);
    }

    /**
     * 更新所有发生变动的账户，需要在事务中使用
     * @param bool $sync_all_consumption 是否同步更新总消费值
     * @throws Exception
     */
    public function updateAccounts(bool $sync_all_consumption = true): void
    {
        PayAccount::updateAccounts($this->change_accounts, $sync_all_consumption);
    }

    /**
     * 将更新的账户与原账户合并
     *
     * @return void
     */
    public function refresh(): void
    {
        if (empty($this->change_accounts) || empty($this->accounts)) {
            return;
        }
        /**
         * @var PayAccount[] $change_account
         */
        $change_account = array_column($this->change_accounts, null, 'id');
        $this->accounts = array_map(function ($account) use ($change_account) {
            if (array_key_exists($account->id, $change_account)) {
                $account->balance -= $change_account[$account->id]->consume_amount;
                return $account;
            }
            return $account;
        }, $this->accounts);
    }

    /**
     * @param TransactionLog $transaction_log 交易订单
     * @param int $status 需要生成的交易详情状态
     *
     * @throws Exception 订单变动保存失败
     */
    public function updatePurchaseDetails(TransactionLog $transaction_log, int $status): void
    {
        PayAccountPurchaseDetail::purchaseDetail(
            $this->change_accounts,
            $transaction_log,
            $status
        );
    }

    public function addAccounts(array $accounts)
    {
        $this->accounts = array_merge($this->accounts, $accounts);
        return $this;
    }

    public static function legacyBalanceToAccounts(array|Balance $balance, int $user_id = 0): array
    {
        if (is_array($balance) && !array_key_exists('id', $balance) && !$user_id) {
            throw new Exception('user_id 不能为空');
        }
        $coin_field_type_index_map = array_flip(PayAccount::TYPE_INDEX_COIN_FIELD_MAP);
        $coin_types = is_array($balance) ? array_keys($balance) : Balance::balanceFields();

        return array_map(function ($coin_field) use ($coin_field_type_index_map, $balance, $user_id) {
            $type_index = $coin_field_type_index_map[$coin_field];
            $is_legacy = in_array($type_index, PayAccount::LEGACY_COIN_TYPE_INDEX);

            return PayAccount::initiate([
                'user_id' => $balance['id'] ?? $user_id,
                'tid' => 0,
                'account_amount' => $balance[$coin_field] ?? 0,
                'balance' => $balance[$coin_field] ?? 0,
                'scope' => PayAccount::SCOPE_COMMON,
                'type' => $type_index,
                'withhold_order' => PayAccount::COIN_CONSUME_ORDER[$type_index],
                'create_time' => $_SERVER['REQUEST_TIME'],
                'modified_time' => $_SERVER['REQUEST_TIME'],
                'expire_time' => PayAccount::EXPIRE_TIME_FOREVER_EFFECTIVE,
                'status' => PayAccount::STATUS_SUCCESS,
                'more' => ['attr' => PayAccount::ATTR_COMMON_COIN],
            ], $is_legacy);
        }, $coin_types);
    }

    public function refund(RechargeOrder $order, array $deduct_coin_order = PayAccount::COIN_DEFAULT_REFUND_ORDER)
    {
        if ($this->getTotalBalance() > 0) {
            $left = $this->cost($order->num, $deduct_coin_order);
            $this->updateAccounts(false);
        } else {
            $left = $order->num;
        }

        Balance::updateByPk($order->uid, [
            'all_topup' => new Expression('GREATEST(all_topup, :balance) - :balance', [':balance' => $order->num]),
        ]);
        $coin_label = '';
        $debt_type = 0;
        switch ($order->type) {
            case RechargeOrder::TYPE_APPLEPAY:
                $coin_label = 'iOS';
                $debt_type = PayBadDebt::TYPE_IOS_REFUND;
                break;
            case RechargeOrder::TYPE_TMALL_IOS:
            case RechargeOrder::TYPE_TMALL_ANDROID:
                $coin_label = '天猫充值';
                $debt_type = PayBadDebt::TYPE_TMALL_REFUND;
                break;
        }
        if ($left > 0) {
            $log_info = sprintf(
                '%s 退款：用户 %d 钻石余额不足，未能足额扣减（退款 %d 钻，扣减 %d 钻），订单 ID %d',
                $coin_label,
                $order->uid,
                $order->num,
                $order->num - $left,
                $order->id
            );
            Yii::error($log_info, __METHOD__);
            $debt = new PayBadDebt([
                'user_id' => $order->uid,
                'order_id' => $order->id,
                'debt' => $left,
                'type' => $debt_type,
                'reason' => $log_info,
            ]);
            if (!$debt->save()) {
                throw new Exception(MUtils::getFirstError($debt));
            }
        } else {
            $log_info = sprintf('%s 退款：用户 %d iOS 钻石退款 %d，订单 ID %d', $coin_label, $order->uid, $order->num, $order->id);
            Yii::warning($log_info, __METHOD__);
        }

        // 取消充值订单
        $order->updateAttributes([
            'status' => RechargeOrder::STATUS_CANCELED,
            'modified_time' => $_SERVER['REQUEST_TIME'],
        ]);

        return [
            $left,
            $log_info,
        ];
    }

}
