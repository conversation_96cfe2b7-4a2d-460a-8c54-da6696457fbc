<?php

namespace app\models;

use app\components\models\traits\AlbumTrait;
use app\components\util\Go;
use app\components\util\Image;
use app\components\util\MUtils;
use missevan\storage\StorageClient;
use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\db\Expression;
use yii\web\HttpException;

/**
 * This is the model class for table "m_album".
 *
 * @property integer $id
 * @property string $title
 * @property string $intro
 * @property integer $catalog_id
 * @property integer $create_time
 * @property integer $last_update_time
 * @property integer $user_id
 * @property string $username
 * @property string $cover_image
 * @property integer $uptimes
 * @property integer $refined
 * @property string $source
 * @property integer $view_count
 * @property integer $comment_count
 * @property integer $favorite_count
 * @property integer $music_count
 * @property integer $checked
 * @property integer $sort
 */
class MAlbum extends ActiveRecord
{
    use AlbumTrait;
    // 音单 ID 字段名赋值给常量，用于 Trait 中自定义排序函数
    const ALBUM_ID_COLUMN = 'id';

    const ALBUM_COMMON = 0;
    const ALBUM_LIKED = 1;

    // 音单收藏单音的最大值
    const MAX_COLLECT_NUM = 1000;
    // 播放页推荐音单数量
    const PLAY_PAGE_RECOMMEND_NUM = 3;

    // 收藏和取消收藏：1：收藏操作；0：取消收藏操作
    const SOUND_COLLECT = 1;
    const SOUND_CANCEL_COLLECT = 0;

    // 排序值偏移量 2^20 (1048576)
    const SORT_INTERVAL = 1048576;
    // 排序最大绝对阈值（绝对值）2^52
    const SORT_MAX = 4503599627370496;

    // 音单状态，0：未审核；1：已审核通过；2：报警；3：修改中
    const CHECKED_NOT_VERIFY = 0;
    const CHECKED_PASS = 1;
    const CHECKED_POLICE = 2;
    const CHECKED_REMODIFY = 3;

    // 音单默认属性
    const REFINED_DEFAULT = 0;
    // 判断音单是否加精时进行位运算标识
    const REFINED = 1;
    // 判断音单是否有封面时进行位运算标识
    // TODO: 之后应不使用这种方式判断音单是否有封面图
    const REFINED_NO_COVER = 1 << 1;
    // 擦边球
    const REFINED_NEAR_DANGER = 1 << 2;
    // 搜索隐藏
    const REFINED_SEARCH_LIMIT = 1 << 3;
    // 判断音单是否为私有时位运算标识（比特位第 5 位为 1 表示私有音单）
    const REFINED_PRIVATE = 1 << 4;

    // 用户可创建的最大音单数量
    const MAX_CREATE_ALBUM_NUM = 1000;
    // 用户一次性批量删除的音单上限
    const MAX_DELETE_ALBUM_NUM = 100;
    // 音单标题最大长度限制为 30 个字符
    const MAX_TITLE_LENGTH = 30;

    // 音单被当前用户收藏状态 0：未收藏，1：已收藏
    const ALBUM_NOT_COLLECTED = 0;
    const ALBUM_COLLECTED = 1;

    public $musicnum;
    public $name;
    public $collected;
    public $front_cover;
    public $exists_sound;
    // 音单是否为私密
    public $is_private;
    // 音单是否为失效
    public $is_invalid;

    public function afterFind()
    {
        parent::afterFind(); // TODO: Change the autogenerated stub

        $this->assignAlbumFrontCover();
        // FIXME: 需要调整为正确类型
        $this->musicnum = (string)$this->music_count;
        if ($this->intro) {
            // 过滤简介中的 Html 标签
            $this->intro = MUtils::plainText($this->intro);
        }
    }

    public function checkTitle($attribute)
    {
        if ($this->isAttributeChanged($attribute)) {
            // 音单标题长度不能超过 30 个字符
            if (mb_strlen($this->$attribute) > self::MAX_TITLE_LENGTH) {
                $this->addError($attribute, '音单标题超过 ' . self::MAX_TITLE_LENGTH . ' 个字符');
                return;
            }
            // 音单标题不支持 Emoji
            if (MUtils2::containsEmoji($this->$attribute)) {
                $this->addError($attribute, '音单标题不支持 Emoji');
                return;
            }
            // 验证标题是否命中屏蔽词
            if (MUtils::hasForbiddenKeywords($this->$attribute, MUtils::FORBIDDEN_WORD_TYPE_ALBUM)) {
                $this->addError($attribute, '音单标题中含有敏感信息');
                return;
            }
            // 同一个用户不能有重复的标题名称
            $is_exist = self::find()->where('user_id = :user_id AND title = :title', [
                ':user_id' => $this->user_id,
                ':title' => $this->$attribute
            ])->exists();
            if ($is_exist) {
                $this->addError($attribute, '音单标题重复');
            }
        }
    }

    public function assignAlbumFrontCover(): void
    {
        if (!$this->cover_image) {
            $this->front_cover = StorageClient::getFileUrl(Yii::$app->params['defaultAlbumCoverUrl']);
        } else {
            // TODO: cover_image 需要调整为保存协议地址
            $this->front_cover = Yii::$app->params['coverUrl'] . $this->cover_image;
        }
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_album';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['title'], 'required', 'message' => '音单标题不能为空'],
            [['title'], 'checkTitle'],
            [['intro', 'source'], 'string'],
            [['catalog_id', 'user_id', 'refined'], 'integer'],
            [['cover_image'], 'string', 'max' => 255],
            [['username'], 'string', 'max' => 20],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'title' => '专辑标题',
            'intro' => '专辑介绍',
            'catalog_id' => '分类 ID',
            'create_time' => '创建时间',
            'last_update_time' => '最后更新',
            'user_id' => '作者 ID',
            'username' => '作者名称',
            'cover_image' => '封面图片',
            'uptimes' => '被赞次数',
            'refined' => '是否加精',
            'source' => '来源',
            'view_count' => '查看数',
            'comment_count' => '评论数',
            'favorite_count' => '收藏数',
            'music_count' => '音乐数',
            'checked' => '审核状态',
            'sort' => '自建音单排序值',
        ];
    }

    /**
     * 验证音单信息是否含有敏感词
     */
    public function afterValidate()
    {
        // 有别的错误的时候不需要进行文字检测
        if (!$this->getErrors()) {
            $check_arr = [$this->title];
            if ($this->intro) {
                array_push($check_arr, $this->intro);
            }
            // 对音单标题及音单简介进行检测
            $check_result = Yii::$app->go->checkText($check_arr, Go::SCENE_INTRO, true);
            if (!empty($check_result)) {
                foreach ($check_result as $key => $result) {
                    if ($key === 0 && !$result['pass']) {
                        $this->addError('title', '音单标题中含有敏感信息');
                        return;
                    }
                    if ($key === 1 && !$result['pass']) {
                        $this->addError('intro', '音单简介中含有敏感信息');
                        return;
                    }
                }
            }
        }
        parent::afterValidate();
    }

    /**
     * 获取包含该音频的音单
     *
     * @param integer $sound_id 音频 ID
     * @param integer $num 获取的音单数量
     * @param integer $origin_album_id 来源页的音单 ID
     * @return ActiveRecord[]
     */
    public static function getLikeAlbum(int $sound_id, int $num, int $origin_album_id = 0)
    {
        if (!$sound_id || !$num) return [];
        $query = self::find()
            ->alias('t')
            ->select('t.id, t.title, t.cover_image, t.music_count')
            ->leftJoin('m_sound_album_map t1', 't.id = t1.album_id')
            ->orderBy(['t.view_count' => SORT_DESC])
            ->where('t1.sound_id = :sound_id AND (refined & :not_refined) = 0',
                [':sound_id' => $sound_id, ':not_refined' => self::REFINED_PRIVATE]);
        if ($origin_album_id) {
            $num -= 1;
            $query->andWhere('t.id <> :origin_album_id', [':origin_album_id' => $origin_album_id]);
        }
        $albums = $query->limit($num)->asArray()->all();
        if ($origin_album_id) {
            $origin_album = self::find()
                ->select('id, title, cover_image, music_count')
                ->where(['id' => $origin_album_id])
                ->asArray()->one();
            if ($origin_album) {
                array_unshift($albums, $origin_album);
            }
        }

        $albums = array_reduce($albums, function ($ret, $item) use ($origin_album_id) {
            if (MUtils::hasForbiddenKeywords($item['title'], MUtils::FORBIDDEN_WORD_TYPE_ALBUM)) {
                return $ret;
            }
            $item['id'] = (int)$item['id'];
            $item['music_count'] = (int)$item['music_count'];
            $item['front_cover'] = self::getFrontCoverUrl($item['cover_image']);
            // 从音单点击进来的，则对相同的音单显示 “来源” 标识
            $item['origin'] = 0;
            if ($item['id'] === $origin_album_id) {
                $item['origin'] = 1;
            }
            unset($item['cover_image']);

            $ret[] = $item;
            return $ret;
        }, []);
        return $albums;
    }

    /**
     * 上传音单封面图
     *
     * @param string $image_file 封面图文件地址
     * @throws HttpException
     */
    public function uploadCoverImage(string $image_file): void
    {
        if (!$image_file) {
            throw new HttpException(400, '图片地址错误');
        }
        try {
            // 有图片信息时检测图片格式
            $extension = MUtils::getFileExtension($image_file);
            // WORKAROUND: JPG 格式需要转换成 JPEG，用于 Image::getImageExtension 方法来获取后缀名
            $format = ($extension === 'jpg' ? Image::FORMAT_JPEG : strtoupper($extension));
            // 非图片格式会抛出 Http 异常
            $extension = Image::getImageExtension($format);
            $cover_image_path = MUtils2::generateFilePath($image_file, $extension);
            $full_image_path = 'covers/' . $cover_image_path;
            Yii::$app->storage->upload($image_file, $full_image_path, true);
            // 封面图片涉黄涉政拦截
            $storage_img_url = Yii::$app->params['originalCoverUrl'] . $cover_image_path;
            $result = Yii::$app->go->checkImage($storage_img_url, Go::SCENE_ALBUM);
            if ($result && !$result['pass']) {
                // 若违规，删除 storage 上的此资源
                Yii::$app->storage->deleteFile($full_image_path);
                throw new HttpException(400, '上传失败，封面图违规喔~');
            }
            // 生成缩略图
            $mini_cover_image_file = MImage::editCoverImage($image_file);
            $mini_image_path = 'coversmini/' . $cover_image_path;
            Yii::$app->storage->upload($mini_cover_image_file, $mini_image_path, true);
            $this->cover_image = $cover_image_path;
        } catch (Exception $e) {
            if ($e instanceof HttpException) {
                throw $e;
            }
            // 记录错误日志
            Yii::error('上传音单封面图失败，原因：' . $e->getMessage(), __METHOD__);
            throw new HttpException(500, '上传失败，如果多次失败请联系管理员');
        }
    }

    /**
     * 判断是否为无封面的音单（比特位第 2 位为 1 时是无封面音单）
     *
     * @return boolean
     */
    public function isNoCover(): bool
    {
        return (bool)($this->refined & self::REFINED_NO_COVER);
    }

    /**
     * 判断是否为私有的音单（比特位第 5 位为 1 表示私有音单）
     *
     * @return boolean
     */
    public function isPrivate(): bool
    {
        return (bool)($this->refined & self::REFINED_PRIVATE);
    }

    /**
     * 无封面图的音单用最新添加审核通过的音频封面图替代
     *
     * @throws
     */
    public function updateImage(): void
    {
        // 判断是否为无封面图的音单
        if (!$this || !$this->isNoCover()) {
            return;
        }
        $cover_image = (string)MSoundAlbumMap::find()
            ->alias('t')
            ->select('t1.cover_image')
            ->leftJoin(MSound::tableName() . ' AS t1', 't1.id = t.sound_id')
            ->where(['t1.checked' => Msound::CHECKED_PASS, 't.album_id' => $this->id])
            ->orderBy('t.id DESC')
            ->limit(1)
            ->scalar();
        // 更新音单封面
        // 如果取消收藏了最后一个音频，则音单封面赋值为空字符串，获取音单信息时，afterFind 会给默认封面图
        $this->cover_image = $cover_image;
        if (!$this->save(false, ['cover_image'])) {
            // 记录错误日志
            Yii::error("音单 ID: {$this->id} 封面图更新失败：" . MUtils2::getFirstError($this), __METHOD__);
            throw new Exception('音单封面图更新失败');
        }
    }

    public static function getTags(int $album_id)
    {
        return MTag::findBySql('SELECT t.id, t.name FROM
            m_tag t LEFT JOIN m_tag_album_map t1 ON t.id = t1.tag_id
            WHERE t1.album_id = :album_id', [':album_id' => $album_id])->all();
    }

    public static function DoYouCollect(int $album_id)
    {
        return (int)MCollectAlbum::find()
            ->where('user_id = :user_id AND album_id = :album_id',
                [':user_id' => Yii::$app->user->id, ':album_id' => $album_id])
            ->exists();
    }

    public static function isCollected(&$albums, $user_id = 0)
    {
        $my_id = Yii::$app->user->id;
        if (!$my_id || !$albums) return;

        if (gettype($albums) === 'array') {

            if ($user_id == Yii::$app->user->id) {
                $collected = true;
                $collections = [];
            } else {
                $collected = false;
                $ids = array_column($albums, 'id');
                $collections = MCollectAlbum::find()->select('album_id')
                    ->where(['user_id' => $my_id, 'album_id' => $ids])->column();
            }
            foreach ($albums as &$album) {
                $album->collected = (int)($collected || in_array($album->id, $collections));
            }

        } else {
            if ($my_id == $user_id
                || MCollectAlbum::findOne(['user_id' => $my_id, 'album_id' => $albums->id])
            ) {
                $albums->collected = 1;
            } else {
                $albums->collected = 0;
            }

        }
    }

    /**
     * 获取用户收藏音单
     *
     * @param int $view_user_id 被访问者 ID
     * @param int $user_id 访问者 ID
     * @param bool $pagination 是否分页
     * @param int $page_size 每页个数
     * @return ReturnModel|array
     * @throws \Exception
     */
    public static function getCollectAlbum(int $view_user_id, int $user_id = 0, bool $pagination = false,
            int $page_size = PAGE_SIZE_20)
    {
        $select = 't.username, t.user_id, t.id, t.title, t.cover_image, t.music_count, t.refined, t.checked, t1.sort';
        $query = self::find()->alias('t')->select($select)
            ->leftJoin(MCollectAlbum::tableName() . ' AS t1', 't1.album_id = t.id')
            ->where('t1.user_id = :view_user_id', [':view_user_id' => $view_user_id]);
        // 访问用户收藏的音单 IDs
        $collect_album_ids = [];
        if ($view_user_id !== $user_id) {
            // 他人的主页，UP 主收藏音单：(审核通过 && 公开) || (当前访问用户收藏 && 非报警 && 公开)
            $query = $query->andWhere('((t.refined & :refined) = 0)', [':refined' => self::REFINED_PRIVATE]);
            if ($user_id > 0) {
                // 访问用户是登录状态时，可查看当前访问用户收藏 && 非报警 && 公开
                // 需求上线后所有音单都会变成审核中状态，这个显示规则是为了能让用户看到他人和自己共同收藏的审核中状态的音单
                // WORKAROUND: 所有音单审核完成后可以去掉“可查看当前访问用户收藏”的条件
                $collect_album_ids = MCollectAlbum::find()
                    ->alias('a')
                    ->select('album_id')
                    ->leftJoin(self::tableName() . ' AS a1', 'a1.id = a.album_id')
                    ->where('a.user_id = :user_id AND a1.checked <> :checked', [
                        ':user_id' => $user_id,
                        ':checked' => self::CHECKED_POLICE
                    ])
                    ->limit(MCollectAlbum::MAX_COLLECT_ALBUM_NUM)
                    ->column();
            }
            if (!empty($collect_album_ids)) {
                $conditions = ['OR', ['t.checked' => self::CHECKED_PASS], ['IN', 't.id', $collect_album_ids]];
            } else {
                // 未登录时，只能查看审核通过 && 公开
                $conditions = ['t.checked' => self::CHECKED_PASS];
            }
            $query = $query->andWhere($conditions);
        }
        $query = $query->orderBy(['t1.sort' => SORT_ASC, 'time' => SORT_DESC]);
        if ($pagination) {
            // 分页显示
            $return_model = MUtils::getPaginationModels($query, $page_size);
            $return_model->Datas = self::showAlbumList($return_model->Datas, $view_user_id, $user_id,
                $collect_album_ids);
            return $return_model;
        }
        // 不分页显示
        $return = $query->limit(MCollectAlbum::MAX_COLLECT_ALBUM_NUM)->all();
        return self::showAlbumList($return, $view_user_id, $user_id, $collect_album_ids);
    }

    /**
     * 获取显示的音单列表
     *
     * @param array $album_list 音单列表
     * @param int $view_user_id 被访问者 ID
     * @param int $user_id 访问者 ID
     * @param array $collect_album_ids 访问者收藏的音单 IDs
     * @return array
     */
    private static function showAlbumList(array $album_list, int $view_user_id, int $user_id,
            array $collect_album_ids = [])
    {
        foreach ($album_list as $album) {
            $album->collected = self::ALBUM_NOT_COLLECTED;
            $album->is_private = $album->isPrivate();
            $album->is_invalid = false;
            if ($view_user_id === $user_id || in_array($album->id, $collect_album_ids)) {
                // 自己的主页，都标识为已收藏的状态
                // 他人的主页，标识当前访问用户已收藏的音单
                $album->collected = self::ALBUM_COLLECTED;
            }
            if ($album->checked === self::CHECKED_POLICE) {
                // 自己的主页，收藏的音单为报警音单时，显示为：已失效音单
                $album->title = '已失效音单';
                $album->music_count = 0;
                $album->front_cover = StorageClient::getFileUrl(Yii::$app->params['defaultAlbumMiniCoverUrl']);
                $album->is_invalid = true;
            } elseif ($album->is_private) {
                // 收藏的音单为私密音单时，显示为：此音单已被创建者设为私密
                $album->title = '此音单已被创建者设为私密';
                $album->music_count = 0;
                $album->front_cover = StorageClient::getFileUrl(Yii::$app->params['privateAlbumCoverUrl']);
            }
            // 去掉无用的字段
            unset($album->cover_image, $album->refined, $album->musicnum, $album->checked);
        }
        return $album_list;
    }

    public static function getUserAlbumOld($user_id, $page_size, $sound_id = 0)
    {
        $select = "t.username, t.user_id, t.id, t.title, t.cover_image, t.music_count";
        $query = self::find()->alias('t')->select($select)
            ->where("t.user_id = :userId", [":userId" => $user_id])
            ->orderBy(['id' => SORT_DESC]);

        $return_model = MUtils::getPaginationModels($query, 1000);

        self::isCollected($return_model->Datas);
        self::existsSound($return_model->Datas, $sound_id);
        return $return_model;
    }

    /**
     * 获取用户自建音单
     *
     * @param int $view_user_id 被访问者 ID
     * @param int $user_id 访问者 ID
     * @param bool $pagination 是否分页
     * @param int $page_size 每页个数
     * @return ReturnModel|array
     * @throws \Exception
     */
    public static function getUserAlbum(int $view_user_id, int $user_id = 0, bool $pagination = false,
            int $page_size = PAGE_SIZE_20)
    {
        $select = 't.username, t.user_id, t.id, t.title, t.cover_image, t.view_count, t.music_count, t.refined, t.checked, t.sort';
        $query = self::find()->alias('t')->select($select)
            ->where('t.user_id = :user_id', [':user_id' => $view_user_id]);
        // 是否为查看自己的主页
        if ($view_user_id !== $user_id) {
            // 他人的主页，UP 主自建音单：(审核通过 && 公开) || (当前访问用户收藏 && 非报警 && 公开)
            $query = $query->andWhere('((t.refined & :refined) = 0)', [':refined' => self::REFINED_PRIVATE]);
            if ($user_id > 0) {
                // 访问用户是登录状态时，可查看当前访问用户收藏 && 非报警 && 公开
                $on_condition = "t1.album_id = t.id AND t1.user_id = {$user_id}";
                $query = $query->leftJoin(MCollectAlbum::tableName() . ' AS t1', $on_condition);
                $conditions = [
                    'OR',
                    ['t.checked' => self::CHECKED_PASS],
                    ['AND', ['t1.user_id' => $user_id], ['NOT', ['t.checked' => self::CHECKED_POLICE]]]
                ];
            } else {
                // 未登录时，只能查看审核通过 && 公开
                $conditions = ['t.checked' => self::CHECKED_PASS];
            }
            $query = $query->andWhere($conditions);
        }
        // 最新的音单排在前面
        $query = $query->orderBy(['t.sort' => SORT_ASC, 't.id' => SORT_DESC]);
        if ($pagination) {
            $return_model = MUtils::getPaginationModels($query, $page_size);
            self::isCollected($return_model->Datas);
            foreach ($return_model->Datas as $album) {
                $album->is_private = $album->isPrivate();
                // 去掉无用的字段
                unset($album->cover_image, $album->refined, $album->user_id, $album->musicnum);
            }
            return $return_model;
        }
        $return = $query->limit(self::MAX_CREATE_ALBUM_NUM)->all();
        self::isCollected($return);
        foreach ($return as $album) {
            $album->is_private = $album->isPrivate();
            // 去掉无用的字段
            unset($album->cover_image, $album->refined, $album->user_id, $album->musicnum);
        }
        return $return;
    }

    public static function getUserAllAlbum($user_id, $sound_id = 0)
    {
        $select = 't.id, t.title, t.music_count, t.refined';
        $query = self::find()->alias('t')->select($select)
            ->where("t.user_id = :userId", [":userId" => $user_id])
            ->orderBy(['id' => SORT_DESC]);
        $albums = $query->limit(1000)->all();
        foreach ($albums as $key => $album) {
            $album->is_private = (bool)($album->refined & self::REFINED_PRIVATE);
            // 去掉无用的字段
            unset($album->refined);
        }
        self::isCollected($albums);
        self::existsSound($albums, $sound_id);
        return $albums;
    }

    public static function existsSound(&$models, int $sound_id = 0)
    {
        if (!$sound_id) return;
        if (gettype($models) === 'array') {
            $ids = array_column($models, 'id');
            $maps = MSoundAlbumMap::findAll(['album_id' => $ids, 'sound_id' => $sound_id]);
            if ($maps) {
                $maps = array_column($maps, 'album_id', 'album_id');
                foreach ($models as $k => $model) {
                    if (isset($maps[$model->id])) {
                        $model['exists_sound'] = 1;
                        $exists = $model;
                        unset($models[$k]);
                        array_unshift($models, $exists);
                    }
                }
            }
        } else {
            $models['exists_sound'] = (int)MSoundAlbumMap::find()->where('album_id = :album_id AND sound_id = :sound_id',
                [':album_id' => $models->id, ':sound_id' => $sound_id])->exists();
        }
    }

    /**
     * 搜索音单
     *
     * @param string|integer $s 关键词
     * @param integer $page 第几页
     * @param integer $page_size 每页个数
     * @param string $sort 排序规则（可选值："album"）
     * @return array
     */
    public static function getSearch($s, int $page = 1, int $page_size = 8, $sort = 'album')
    {
        // WORKAROUND: 临时屏蔽音单搜索功能 2019 年 7 月 7 日始
        $results['Datas'] = [];
        $results['pagination'] = [
            'p' => 1,
            'pagesize' => 0,
            'count' => 0,
            'maxpage' => 0
        ];
        return $results;

        $filters = ['(refined & ' . self::REFINED_PRIVATE . ') = 0'];
        if (!Yii::$app->user->isExam) {
            $filters[] = sprintf('checked = %d', self::CHECKED_PASS);
        }

        $results = Yii::$app->go->search($s, Discovery::SEARCH_MALBUM, $page, $page_size);

        $results['Datas'] = array_map(function ($data) {
            $data['front_cover'] = self::getFrontCoverUrl($data['cover_image']);
            unset($data['cover_image'], $data['musicnum']);
            return $data;
        }, $results['Datas']);

        return $results;
    }

    /**
     * 入库前自动处理
     */
    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
            $this->checked = MAlbum::CHECKED_NOT_VERIFY;
            // 新创建的音单排在首位
            $first_album_sort = self::getFirstAlbumSort($this->user_id);
            $this->sort = $first_album_sort - self::SORT_INTERVAL;
        }
        $this->last_update_time = $time;
        return true;
    }

    public function beforeDelete()
    {
        if (parent::beforeDelete()) {
            // 若为推荐的音单，则禁止删除
            if (self::isRecommendAlbum($this->id)) return false;
            $tag_ids = MTagAlbumMap::find()->select('tag_id')->where(['album_id' => $this->id])->column();
            MTagAlbumMap::deleteAll('album_id = :album_id', [':album_id' => $this->id]);
            if ($tag_ids) {
                $range1 = implode(',', $tag_ids);
                MTag::updateAllCounters(['album_num' => -1], ['AND', 'album_num > 0', "id IN ($range1)"]);
                MTag::deleteAll(['AND', '(album_num <= 1 OR ISNULL(album_num)) AND (sound_num <= 0 OR ISNULL(sound_num)) AND (image_num <= 0 OR ISNULL(image_num))', "id IN ($range1)"]);
            }

            $sound_ids = MSoundAlbumMap::find()->select('sound_id')->where(['album_id' => $this->id])->column();
            if ($sound_ids) {
                $range2 = implode(',', $sound_ids);
                MSound::updateAllCounters(['favorite_count' => -1], ['AND', 'favorite_count > 0', "id IN ($range2)"]);
            }
            MSoundAlbumMap::deleteAll(['album_id' => $this->id]);
            Mowangskuser::updateAllCounters(['albumnum' => -1], 'albumnum > 0 AND id = :id',
                ['id' => $this->user_id]);
            MCollectAlbum::deleteAll('album_id = :album_id', [':album_id' => $this->id]);
            return true;
        } else {
            return false;
        }
    }

    /**
     * 是否为推荐的音单
     *
     * @param int|array $ids
     * @return bool 是否为推荐的音单
     */
    public static function isRecommendAlbum($ids): bool
    {
        if (!$ids) {
            return false;
        }
        if (!is_array($ids)) {
            $ids = [(int)$ids];
        } else {
            $ids = array_map('intval', $ids);
        }
        // App 首页推荐“猜你喜欢”音选取的热门音单
        $is_hot = MPersonaCollection::find()->where(
            ['elem_type' => MPersonaCollection::TYPE_ALBUM, 'elem_id' => $ids])->exists();
        if ($is_hot) return true;
        // App 首页音单模块推荐音单
        return MPersonaModuleElement::find()
            ->where(['element_type' => MPersonaModuleElement::MODULE_TYPE_ALBUM, 'element_id' => $ids])
            ->exists();
    }

    /**
     * 获取猜你喜欢推荐模块中音单的数据
     * @param array $album_ids 音单 ID
     * @return array 猜你喜欢推荐模块中音单的数据（数组索引为音单的 ID）
     */
    public static function getRecommendedAlbums($album_ids)
    {
        $album_data = self::find()
            ->select('id, title, intro, cover_image, view_count, music_count')
            ->where(['id' => $album_ids])
            ->andWhere('(refined & ' . self::REFINED_PRIVATE . ') = 0')
            ->asArray()
            ->all();
        $album_data = array_map(function ($album) {
            return [
                'id' => (int)$album['id'],
                'title' => $album['title'],
                'front_cover' => self::getFrontCoverUrl($album['cover_image']),
                'intro' => MUtils::plainText($album['intro']),
                'view_count' => (int)$album['view_count'],
                'music_count' => (int)$album['music_count'],
            ];
        }, $album_data);
        $album_data = array_column($album_data, null, 'id');
        return $album_data;
    }

    public static function getFrontCoverUrl($image_path)
    {
        if ($image_path) {
            $return = Yii::$app->params['coverUrl'] . $image_path;
        } else {
            $return = StorageClient::getFileUrl(Yii::$app->params['defaultAlbumMiniCoverUrl']);
        }
        return $return;
    }

    /**
     * 对音单中音频排序前判断音频是否发生变动
     *
     * @param integer $album_id 音单 ID
     * @param integer $last_update_time 音单最后更新时间
     * @return boolean
     */
    public static function isAlbumChanged(int $album_id, int $last_update_time)
    {
        if (self::find()->where(['id' => $album_id, 'last_update_time' => $last_update_time])->exists()) {
            return false;
        }
        return true;
    }

    /**
     * 获取联想词
     *
     * @param string $s 搜索的关键词
     * @param int $count 联想词数量
     * @return array
     */
    public static function getSearchSuggest(string $s, int $count = Discovery::SUGGEST_COUNT): array
    {
        return Yii::$app->go->suggest($s, Discovery::SEARCH_MALBUM, $count);
    }

    /**
     * 获取优质的的联想词（收藏数 >= 30 的音单）
     *
     * @param string $s 搜索词
     * @param integer $count 联想词数量
     * @return array
     */
    public static function getGoodSearchSuggest(string $s, int $count): array
    {
        if ($album = self::getSearchSuggest($s)) {
            // TODO: title 目前没有索引
            $album = self::find()
                ->select('title')
                ->where(['title' => $album])
                ->andWhere('favorite_count >= 30')
                ->limit($count)
                ->column();
        }
        return $album;
    }

    /**
     * 检查是否可以查看音单
     * 自建音单或白名单音单：全部显示 \
     * 收藏音单：公开 && 非报警 \
     * 非收藏音单：公开 && 审核通过
     *
     * @param MAlbum|null $album 音单
     * @param int|null $user_id 当前登录用户
     * @throws HttpException
     */
    public static function checkCanViews(?MAlbum $album, ?int $user_id): void
    {
        if (!$album) {
            throw new HttpException(404, '音单不存在', 200120001);
        }
        if ($album->user_id !== $user_id) {
            if ($album->checked === self::CHECKED_POLICE) {
                // 访问报警音单提示：音单已失效
                throw new HttpException(403, '音单已失效', 200120001);
            }
            if ($album->isPrivate()) {
                // 访问私有音单，提示音单不存在
                throw new HttpException(404, '音单不存在', 200120001);
            }
            // TODO: 音单的审核后台上线后需要去掉白名单规则，统一走过审逻辑
            if ($album->checked !== self::CHECKED_PASS &&
                    !MAllowAlbum::isAllowed($album->id)) {
                // 访问非白名单，未收藏，且审核不通过状态的音单时，提示音单不存在
                $is_collected = false;
                if ($user_id) {
                    $is_collected = MCollectAlbum::find()->where(['user_id' => $user_id, 'album_id' => $album->id])
                        ->exists();
                }
                if (!$is_collected) {
                    throw new HttpException(404, '音单不存在', 200120001);
                }
            }
        }
    }

    /**
     * 拷贝头像作为音单封面图
     *
     * @param string $iconurl 头像
     * @return boolean
     */
    public function copyIconUrl($iconurl)
    {
        if (!$iconurl) return false;
        $iconurl = parse_url($iconurl, PHP_URL_PATH);
        if (substr($iconurl, 0, 1) === '/') {
            $image_file = substr($iconurl, 1);
        } else {
            $image_file = $iconurl;
        }
        $cover_image = date('Ym') . '/' . date('d') . '/' . md5($iconurl) . date('His')
            . '.' . pathinfo($iconurl, PATHINFO_EXTENSION);
        $image_path = 'covers/' . $cover_image;
        $mini_image_path = 'coversmini/' . $cover_image;
        try {
            Yii::$app->storage->copy($image_file, $image_path);
            Yii::$app->storage->copy($image_file, $mini_image_path);
            $this->cover_image = $cover_image;
        } catch (Exception $e) {
            throw new HttpException(408, '上传失败，如果多次失败请联系管理员', 100010001);
        }
        return true;
    }

    /**
     * 批量删除音单
     *
     * @param array $album_ids 音单 IDs
     * @param int $user_id 用户 ID
     * @throws HttpException
     */
    public static function deleteAlbums(array $album_ids, int $user_id): void
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            // 删除音单
            $delete_record_num = self::deleteAll(['id' => $album_ids, 'user_id' => $user_id]);
            if (0 === $delete_record_num) {
                return;
            }
            // 删除关联数据
            // 获取标签和音单数量对应的 map
            $tag_album_map = MTagAlbumMap::find()
                ->select('COUNT(*)')
                ->where(['album_id' => $album_ids])
                ->groupBy('tag_id')
                ->indexBy('tag_id')
                ->column();
            if ($tag_album_map) {
                // 更新标签表中，音单数量冗余字段
                MTag::updateTagAlbumCount($tag_album_map);
            }
            // 删除标签关联
            MTagAlbumMap::deleteAll(['album_id' => $album_ids]);

            // 获取音频和所属音单数量的 map
            $sound_album_map = MSoundAlbumMap::find()->select('COUNT(*)')
                ->where(['album_id' => $album_ids])
                ->groupBy('sound_id')
                ->indexBy('sound_id')
                ->column();
            if ($sound_album_map) {
                // 音频的收藏数量冗余字段 - 删除的音单数量
                MSound::updateSoundFavoriteCount($sound_album_map);
            }
            // 删除音频与音单的关联
            MSoundAlbumMap::deleteAll(['album_id' => $album_ids]);

            // 用户所属的音单数量冗余字段 - 删除的音单数量
            $album_num_expression = new Expression('GREATEST(albumnum, :albumnum) - :albumnum',
                [':albumnum' => $delete_record_num]);
            Mowangskuser::updateAll(['albumnum' => $album_num_expression], 'id = :user_id',
                [':user_id' => $user_id]);

            // 删除收藏关联
            MCollectAlbum::deleteAll(['album_id' => $album_ids]);
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollback();
            // 记录错误日志
            Yii::error('删除音单失败，原因：' . $e->getMessage(), __METHOD__);
            throw new HttpException(500, '删除失败');
        }
    }
}
