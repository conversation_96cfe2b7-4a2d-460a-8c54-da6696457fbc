<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "m_catalog_tags".
 *
 * @property integer $id
 * @property string $catalog_name_alias
 * @property string $tags
 */
class MCatalogTags extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_catalog_tags';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['catalog_name_alias', 'tags'], 'required'],
            [['catalog_name_alias', 'tags'], 'string', 'max' => 64],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'catalog_name_alias' => '分类名',
            'tags' => '推荐标签',
        ];
    }
}
