<?php

namespace app\models;

use app\components\util\Equipment;
use Yii;

/**
 * This is the model class for table "m_message_text".
 *
 * The followings are the available columns in table 'm_message_text':
 * @property int $id ID
 * @property string $title 标题
 * @property string $content 内容
 * @property int $send_uid 发送用户 ID
 * @property int $time 创建时间
 * @property int $os 平台
 * @property int $special 是否为特殊通知
 */
class MMessageText extends ActiveRecord
{
    const SPECIAL_COMMON = 0;
    const SPECIAL_IMPORTANT = 1;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_message_text';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['title', 'content', 'send_uid', 'time'], 'required'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'title' => '标题',
            'content' => '内容',
            'send_uid' => '发送用户 ID',
            'time' => '创建时间',
            'os' => '平台',
            'special' => '是否为特殊通知',
        ];
    }

    /**
     * 获取未读的特殊全局系统通知数量
     * @link https://github.com/MiaoSiLa/requirements-documents/blob/master/2018-08-07%20我的消息/README.md
     * @param integer $user_id 用户 ID
     * @return integer 通知数量
     */
    public static function getUnreadSpecialMsgCount($user_id)
    {
        return count(self::getUnreadMsgIds($user_id, self::SPECIAL_IMPORTANT));
    }

    public static function getTotalMsgCount()
    {
        $os_arr = [Equipment::All, Yii::$app->equip->getOs()];
        // 存在从老鸿蒙升级到纯血鸿蒙用户，需要添加 Android 的消息，避免读取不到以前的消息
        if (Yii::$app->equip->isHarmonyOS()) {
            $os_arr[] = Equipment::Android;
        }
        return (int)self::find()->where(['os' => $os_arr])
            ->andWhere('time > :time', [':time' => Yii::$app->user->registerAt])
            ->count();
    }

    /**
     * 获取未读的全局系统通知
     *
     * @param integer $user_id 用户 ID
     * @param array|integer $special 是否为特殊通知
     * @return array
     */
    public static function getUnreadMsgIds($user_id, $special = [self::SPECIAL_COMMON, self::SPECIAL_IMPORTANT])
    {
        if (!$user_id) return [];
        $os_arr = [Equipment::All, Yii::$app->equip->getOs()];
        // 存在从老鸿蒙升级到纯血鸿蒙用户，需要添加 Android 的消息，避免读取不到以前的消息
        if (Yii::$app->equip->isHarmonyOS()) {
            $os_arr[] = Equipment::Android;
        }
        $msg_ids = self::find()
            ->select('id')
            ->where(['os' => $os_arr, 'special' => $special])
            ->andWhere('time > :time', [':time' => Yii::$app->user->registerAt])
            ->andFilterWhere(['NOT IN', 'id', MMessageUserMap::find()->select('messageid')->where(['recuid' => $user_id])->column()])
            ->column();
        return $msg_ids;
    }

}
