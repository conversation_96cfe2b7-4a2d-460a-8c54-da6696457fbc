<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "recharge_order_detail".
 *
 * @property int $id recharge_order.id
 * @property string $ip IP 地址
 * @property int $os 设备类型: 1 为 Android; 2 为 iOS；3 为 Web
 * @property string $user_agent User-Agent
 * @property string $equip_id 设备号
 * @property string $buvid 唯一设备标识
 * @property int $real_price 实际支付金额（单位：分）
 * @property array $more 更多详情
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 */
class RechargeOrderDetail extends ActiveRecord
{

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->paydb;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'recharge_order_detail';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'os', 'real_price'], 'required'],
            [['id', 'os', 'create_time', 'modified_time', 'real_price'], 'integer'],
            [['ip'], 'string', 'max' => 50],
            [['user_agent'], 'string', 'max' => 500],
            [['equip_id'], 'string', 'max' => 36],
            [['buvid'], 'string', 'max' => 64],
            [['more'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'recharge_order.id',
            'ip' => 'IP 地址',
            'os' => '设备类型', // 1 为 Android; 2 为 iOS；3 为 Web；6 为 HarmonyOS
            'user_agent' => 'User-Agent',
            'equip_id' => '设备号',
            'buvid' => '唯一设备标识',
            'real_price' => '实际支付金额',  // 单位：分
            'more' => '更多详情',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

}
