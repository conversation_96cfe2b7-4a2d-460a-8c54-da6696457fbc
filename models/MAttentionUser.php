<?php

namespace app\models;

use app\components\util\Equipment;
use app\components\util\Go;
use app\components\util\MUtils;
use Yii;
use yii\db\Expression;
use yii\db\IntegrityException;
use yii\web\HttpException;

/**
 * This is the model class for table "m_attention_user".
 *
 * @property integer $id
 * @property integer $user_active
 * @property integer $user_passtive
 * @property integer $time
 */
class MAttentionUser extends ActiveRecord
{
    // 用户关注状态 0：未关注；1：已关注；2：粉丝；3：已互粉
    const TYPE_STRANGER = 0;
    const TYPE_FOLLOWING = 1;
    const TYPE_FANS = 2;
    const TYPE_FRIEND = 3;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_attention_user';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_active', 'user_passtive', 'time'], 'required'],
            [['user_active', 'user_passtive', 'time'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'user_active' => '关注者',
            'user_passtive' => '被关注者',
            'time' => '关注时间',
        ];
    }

    /**
     * 当前用户是否关注被访问的用户
     *
     * @param integer $user_id 被访问的用户 ID
     * @return integer
     */
    public static function DoYouAttention(int $user_id): int
    {
        return (int)self::find()->where(['user_active' => Yii::$app->user->id, 'user_passtive' => $user_id])->exists();
    }

    /**
     * 获取双方互相关注状态
     *
     * @param integer $view_user_id 被访问用户 ID
     * @param integer $user_id 当前用户 ID
     * @return integer 0：双方互相都未关注；1：当前用户已关注被访问用户；2：被访问用户已关注当前用户；3：已互粉
     */
    public static function getAttention(int $view_user_id, int $user_id): int
    {
        $data = self::find()
            ->select('user_passtive')
            ->where(['user_active' => $view_user_id, 'user_passtive' => $user_id])
            ->orWhere(['user_active' => $user_id, 'user_passtive' => $view_user_id])
            ->column();
        $attention = self::TYPE_STRANGER;
        // 二进制运算：1 位，当前用户是否关注被访问用户（0：未关注，1：已关注）
        if (in_array($view_user_id, $data)) {
            $attention |= self::TYPE_FOLLOWING;
        }
        // 二进制运算：2 位，被访问用户是否关注当前用户（0：未关注，1：已关注）
        if (in_array($user_id, $data)) {
            $attention |= self::TYPE_FANS;
        }
        // 如果互粉可以通过以上两步计算算出 $attention 值为 self::TYPE_FRIEND
        return $attention;
    }

    /**
     * 获取用户关注 / 粉丝
     *
     * @param int $view_user_id 被访问的用户 ID
     * @param int $user_id 访问者 ID
     * @param int $type 0：获取关注用户；1：获取粉丝用户
     * @param int $page_size 页面个数
     * @return ReturnModel
     * @throws HttpException
     */
    public static function getUserAttention(int $view_user_id, int $user_id, int $type, int $page_size = PAGE_SIZE_20)
    {
        // 0：获取关注用户；1：获取粉丝用户
        $TYPE_FOLLOWING = 0;
        $TYPE_FANS = 1;
        switch ($type) {
            case $TYPE_FOLLOWING:
                // 获取关注用户
                $condition = 'a.user_active = :owner';
                $join_on_condition = 'b.user_id = a.user_passtive';
                $field = 'user_passtive';
                $total_count = Mowangskuser::getFollowNum($view_user_id);
                break;
            case $TYPE_FANS:
                // 获取粉丝
                $condition = 'a.user_passtive = :owner';
                $join_on_condition = 'b.user_id = a.user_active';
                $field = 'user_active';
                $total_count = Mowangskuser::getFansNum($view_user_id);
                break;
            default:
                throw new HttpException(400, '参数错误');
        }
        // TODO: 查询较慢，后续考虑优化
        $query = self::find()
            ->alias('a')
            ->select('a.user_active, a.user_passtive')
            ->where($condition, [':owner' => $view_user_id]);
        if (Equipment::isAppOlderThan('4.7.1', '5.6.0')) {
            // WORKAROUND: iOS < 4.7.1，Android < 5.6.0 时不需要查用户有无直播间
            $query = $query->orderBy(['a.time' => SORT_DESC]);
        } elseif ($user_id === $view_user_id && $type === $TYPE_FOLLOWING) {
            // 用户查看自己的关注列表时查询关注用户有无直播间
            $query->addSelect('b.room_id')
                ->addSelect(new Expression('IF(b.status IS NULL, 0, b.status) AS `status`'))
                ->addSelect(new Expression('IF(b.score IS NULL, 0, b.score) AS score'))
                ->leftJoin(Live::tableName() . ' AS b', $join_on_condition)
                ->orderBy(['`status`' => SORT_DESC, 'score' => SORT_DESC, 'a.time' => SORT_DESC]);
        } else {
            // 查看别人的关注列表时不需要查用户有无直播间
            $query = $query->orderBy(['a.time' => SORT_DESC]);
        }
        $return_model = MUtils::getPaginationModels($query, $page_size, [], $total_count, true);

        if (!empty($return_model->Datas)) {
            $user_ids = array_column($return_model->Datas, $field);
            $users = Mowangskuser::find()
                ->select('id, username, userintro, fansnum, soundnumchecked, confirm, avatar, boardiconurl, icontype')
                ->where(['id' => $user_ids])
                ->asArray()
                ->indexBy('id')
                ->all();

            $attention_users = $follow_users = $friends = [];
            if ($user_id > 0) {
                // 用户登录的情况
                $ids = array_column($users, 'id');
                // 获取当前登录用户的关注
                if ($TYPE_FOLLOWING === $type && $view_user_id === $user_id) {
                    $attention_users = $ids;
                } else {
                    $attention_users = self::find()->select('user_passtive')
                        ->where(['user_active' => $user_id, 'user_passtive' => $ids])->column();
                }
                // 获取当前登录用户的粉丝
                if ($TYPE_FANS === $type && $view_user_id === $user_id) {
                    $follow_users = $ids;
                } else {
                    $follow_users = self::find()->select('user_active')
                        ->where(['user_active' => $ids, 'user_passtive' => $user_id])->column();
                }
                // 获取当前登录用户互粉的用户
                $friends = array_intersect($follow_users, $attention_users);
            }
            $is_unset = false;
            foreach ($return_model->Datas as $k => &$value) {
                if (!isset($users[$value[$field]])) {
                    $is_unset = true;
                    unset($return_model->Datas[$k]);
                    continue;
                }
                $u = $users[$value[$field]];
                $u['id'] = (int)$u['id'];
                $attention = self::TYPE_STRANGER;
                if (in_array($u['id'], $friends)) {
                    // 互粉
                    $attention = self::TYPE_FRIEND;
                } elseif (in_array($u['id'], $attention_users)) {
                    // 已关注
                    $attention = self::TYPE_FOLLOWING;
                } elseif (in_array($u['id'], $follow_users)) {
                    // 粉丝
                    $attention = self::TYPE_FANS;
                }
                // 设置用户额外数据
                Mowangskuser::setExtraInfo($u);
                $return = [
                    'id' => $u['id'],
                    'username' => $u['username'],
                    'userintro' => $u['userintro'],
                    'fansnum' => (int)$u['fansnum'],
                    'soundnum' => (int)$u['soundnumchecked'],
                    'authenticated' => (int)$u['authenticated'],
                    'iconurl' => $u['iconurl'],
                    'attention' => $attention,
                ];
                $value['status'] = isset($value['status']) ? (int)$value['status'] : null;
                // 当用户有直播间且开播时才返回直播状态和直播间房间 ID
                if ($value['status']) {
                    $return['live_status'] = $value['status'];
                    $return['room_id'] = (int)$value['room_id'];
                }
                $return_model->Datas[$k] = $return;
            }
            unset($value);
            if ($is_unset) {
                $return_model->Datas = array_values($return_model->Datas);
            }
        }
        return $return_model;
    }

    /**
     * 关注
     *
     * @param int $fans_id 粉丝用户 ID
     * @param int $follow_id 被关注者用户 ID
     * @return array
     * @throws HttpException
     */
    public static function follow(int $fans_id, int $follow_id)
    {
        return Yii::$app->go->follow($fans_id, $follow_id, Go::FROM_APP);
    }

    /**
     * 取消关注
     *
     * @param int $fans_id 粉丝用户 ID
     * @param int $follow_id 被关注者用户 ID
     * @return array
     * @throws HttpException
     */
    public static function unfollow(int $fans_id, int $follow_id)
    {
        return Yii::$app->go->unfollow($fans_id, $follow_id, Go::FROM_APP);
    }

    /**
     * 取消双关
     *
     * @param int $user1_id 用户1 ID
     * @param int $user2_id 用户2 ID
     * @return bool
     * @throws HttpException
     */
    public static function cancelMutualFollow(int $user1_id, int $user2_id): bool
    {
        if ($user1_id <= 0 || $user2_id <= 0) {
            throw new HttpException(400, '参数错误');
        }
        // 取消 user1 关注 user2
        self::unfollow($user1_id, $user2_id);
        // 取消 user2 关注 user1
        self::unfollow($user2_id, $user1_id);
        return true;
    }

    /**
     * 获取关注的用户 IDs
     *
     * @param int $user_id
     * @return array
     */
    public static function getFollowUserIds(int $user_id)
    {
        // 目前平均关注人数为 5，故此处存在性能问题可能性较小
        return MAttentionUser::find()->select('user_passtive')
            ->where('user_active = :user_active', [':user_active' => $user_id])
            ->column();
    }
}
