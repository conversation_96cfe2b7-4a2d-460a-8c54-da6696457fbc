<?php

namespace app\models;

use Yii;

class PointDetailLog
{
    // 类型
    const TYPE_NEW_USER_REGISTER = 1; // 新用户注册
    const TYPE_TASK_SIGN = 2;  // 签到
    const TYPE_TASK_GET_POINT = 3;  // 摸鱼
    const TYPE_TASK_COMMENT = 4;  // 评论
    const TYPE_TASK_TS = 5;  // 每日任务投食
    const TYPE_SOUND_TS = 6;  // 音频投食
    const TYPE_TASK_SHARE = 7;  // 分享
    const TYPE_UPDATE_USER_INFO = 8;  // 修改用户信息（用户昵称、用户封面图等）
    const TYPE_ADMIN_GRANT = 9;  // 后台管理员发放
    const TYPE_ACTIVITY = 10;  // 活动
    const TYPE_TASK_PATCH_SIGN = 11;  // 补签
    // const TYPE_UPDATE_MEDAL_NAME = 12;  // 直播更新粉丝勋章名
    const TYPE_SOUND_GAIN_TS = 13;  // 通过投稿的音频被投食获得鱼干
    const TYPE_TASK_ADDITIONAL = 14;  // 通过每日限时任务获得鱼干
    const TYPE_TASK_WECHAT_OFFIACCOUNT = 15; // 通过微信公众号任务获得鱼干

    // 设备来源 0：Web；1：手机网页；2：App
    const ORIGIN_DESKTOP_WEB = 0;
    const ORIGIN_MOBILE_WEB = 1;
    const ORIGIN_APP = 2;

    const POINT_NEW_USER_REGISTER = 100;
    const POINT_CONSUME_CHANGE_USERNAME = 50;
    const POINT_CONSUME_CHANGE_COVERURL = 30;
    // 签到可获取小鱼干数量
    const POINT_TASK_SIGN = 3;
    // 补签需要花费的小鱼干数量
    const POINT_TASK_PATCH_SIGN_REQUIRED = 30;
    const POINT_SOUND_TS_UP_GET = 5;
    const POINT_SOUND_TS = 1;

    /**
     * 新增小鱼干操作日志记录
     *
     * @param int $num 数量
     * @param int $type 类型
     * @param int $user_id 用户 ID
     * @param null|array $more 小鱼干操作相关信息
     * @return bool 是否成功
     */
    public static function addLog(int $num, int $type, int $user_id, ?array $more = null): bool
    {
        try {
            $data = [
                'num' => $num,
                'type' => $type,
                'user_id' => $user_id,
                'origin' => self::ORIGIN_APP,
                'create_time' => $_SERVER['REQUEST_TIME'],
            ];
            if ($more) {
                $data['more'] = $more;
            }
            Yii::$app->databus->pub($data, 'user_point_detail_log:' . $user_id);
            return true;
        } catch (\Exception $e) {
            Yii::error('Point add log error: ' . $e->getMessage(), __METHOD__);
            // PASS: 添加日志出错不抛出异常，避免影响正常业务流程
            return false;
        }
    }
}
