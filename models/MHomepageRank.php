<?php

namespace app\models;

use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\db\Expression;
use yii\helpers\Json;

/**
 * This is the model class for table "m_homepage_rank".
 *
 * The followings are the available columns in table 'm_homepage_rank':
 * @property integer $id 主键 ID
 * @property integer $create_time 创建时间（单位：秒）
 * @property integer $modified_time 更新时间（单位：秒）
 * @property string $bizdate 业务日期（格式：2023-03-01）
 * @property integer $type 榜单类型（1: 新品榜；2: 人气榜；3: 打赏榜；4: 免费榜；5: 言情榜；6: 声音恋人榜）
 * @property integer $sub_type 榜单子类型（1: 日榜；2: 周榜；3: 月榜）
 * @property array $data 榜单数据（对应榜单排序后的元素 ID【音频 ID 或剧集 ID】，e.g. [8012,4002,705,888]）
 */
class MHomepageRank extends ActiveRecord
{
    // 首页榜单在首页展示的数量
    const RANK_ITEM_LENGTH_HOMEPAGE_SHOW = 3;

    // 不同榜单在榜单详情页展示的元素数量
    const RANK_ITEM_SHOW_LENGTH_MAP = [
        // 新品榜相关榜单业务端展示的剧集数量
        self::TYPE_RANK_DRAMA_NEW => 30,
        // 人气榜相关榜单业务端展示的剧集数量
        self::TYPE_RANK_DRAMA_POPULARITY => 100,
        // 打赏榜相关榜单业务端展示的剧集数量
        self::TYPE_RANK_DRAMA_REWARD => 50,
        // 免费榜相关榜单业务端展示的剧集数量
        self::TYPE_RANK_DRAMA_FREE => 50,
        // 言情榜相关榜单业务端展示的剧集数量
        self::TYPE_RANK_DRAMA_ROMANTIC => 50,
        // 声音恋人榜相关榜单业务端展示的音频数量
        self::TYPE_RANK_SOUND_LOVER => 20,
        // 直播榜相关榜单业务端展示的直播间数量
        self::TYPE_RANK_LIVE => 20,
    ];

    // 首页榜单需要查询的数量（每种榜单在首页展示 3 个，但查询时会过滤不符合要求的剧集或音频，因此多查询 5 个）
    const RANK_ITEM_LENGTH_HOMEPAGE_SELECT = 8;

    // 榜单类型
    const TYPE_RANK_DRAMA_NEW = 1;  // 新品榜
    const TYPE_RANK_DRAMA_POPULARITY = 2;  // 人气榜
    const TYPE_RANK_DRAMA_REWARD = 3;  // 打赏榜
    const TYPE_RANK_DRAMA_FREE = 4;  // 免费榜
    const TYPE_RANK_DRAMA_ROMANTIC = 5;  // 言情榜
    const TYPE_RANK_SOUND_LOVER = 6;  // 声音恋人榜
    const TYPE_RANK_LIVE = 7;  // 直播榜

    // 榜单子类型
    const SUB_TYPE_RANK_DAY = 1;  // 日榜
    const SUB_TYPE_RANK_WEEK = 2;  // 周榜
    const SUB_TYPE_RANK_MONTH = 3;  // 月榜
    const SUB_TYPE_RANK_ALL = 4;  // 总榜

    // 直播榜单子类型
    const SUB_TYPE_LIVE_RANK_HOUR = 4;  // 直播榜小时榜

    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_homepage_rank';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['bizdate', 'type', 'sub_type'], 'required'],
            [['type', 'sub_type', 'create_time', 'modified_time'], 'integer']
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键 ID',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'bizdate' => '业务日期',
            'type' => '榜单类型',  // 1: 新品榜；2: 人气榜；3: 打赏榜；4: 免费榜；5: 言情榜；6: 声音恋人榜
            'sub_type' => '榜单子类型',  // 1: 日榜；2: 周榜；3: 月榜
            'data' => '榜单数据'  // 对应榜单排序后的元素 ID【音频 ID 或剧集 ID】，e.g. [8012,4002,705,888]
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        if (YII_ENV === 'test' && $this->data) {
            // sqlite json 字段不支持自动 decode，需进行 Json::decode 处理
            $this->data = Json::decode($this->data);
        }
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        if (YII_ENV === 'test' && $this->data) {
            // sqlite json 字段不支持自动 encode，需进行 Json::encode 处理
            $this->data = Json::encode($this->data);
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 获取首页排行榜数据
     *
     * @param array $persona_rank_types 画像对应的榜单 type 数组（已排除直播榜）
     * @return array
     */
    public static function getHomepageRanks(array $persona_rank_types)
    {
        // 查询最近 data 不为 null 的榜单的第一个子榜单的数据
        $sub_query = self::find()
            ->select('type, sub_type, MAX(bizdate) AS max_bizdate')
            ->where('data IS NOT NULL')
            // 只查询榜单的第一个子榜单
            ->andWhere(
                [
                    'OR',
                    [
                        'type' => [
                            self::TYPE_RANK_DRAMA_NEW,
                            self::TYPE_RANK_DRAMA_FREE,
                            self::TYPE_RANK_DRAMA_ROMANTIC,
                        ],
                        'sub_type' => self::SUB_TYPE_RANK_DAY,
                    ],
                    [
                        'type' => [
                            self::TYPE_RANK_DRAMA_POPULARITY,
                            self::TYPE_RANK_DRAMA_REWARD,
                            self::TYPE_RANK_SOUND_LOVER,
                        ],
                        'sub_type' => self::SUB_TYPE_RANK_WEEK,
                    ]
                ]
            )->groupBy('type, sub_type');
        $ranks = self::find()
            ->alias('t')
            ->select('t.bizdate, t.type, t.data')
            ->innerJoin(['t1' => $sub_query],
                't.type = t1.type AND t.sub_type = t1.sub_type AND t.bizdate = t1.max_bizdate')
            ->all();
        $rank_type_map = array_column($ranks, null, 'type');
        $res = [];
        foreach ($persona_rank_types as $type) {
            $res[$type] = array_key_exists($type, $rank_type_map) ? $rank_type_map[$type]->data : [];
            if (empty($res[$type])) {
                // 榜单数据不存在时记录错误日志
                Yii::error("首页排行榜的第一个子榜单数据不存在，榜单类型为：{$type}", __METHOD__);
            }
        }
        return $res;
    }

    /**
     * 获取直播类型榜单
     *
     * @param array $live_rank_types 直播类型的榜单的榜单类型数组
     * @return array
     * @throws Exception
     */
    public static function getRankLives(array $live_rank_types)
    {
        if (empty($live_rank_types)) {
            return [];
        }
        $rank_type_live_detail_map = [];
        foreach ($live_rank_types as $rank_type) {
            switch ($rank_type) {
                case self::TYPE_RANK_LIVE:
                    $res = Yii::$app->live->getUserLiveRankList(self::SUB_TYPE_LIVE_RANK_HOUR, self::RANK_ITEM_LENGTH_HOMEPAGE_SHOW);
                    if (!$res || empty($res['data'])) {
                        Yii::error("首页榜单的直播类型榜单数据获取失败或数据不存在，榜单类型：{$rank_type}", __METHOD__);
                        // PASS
                        $rank_type_live_detail_map[$rank_type] = [];
                        break;
                    }
                    $rank_type_live_detail_map[$rank_type] = $res['data'];
                    break;
                default:
                    throw new Exception("获取直播类型榜单参数错误，type: {$rank_type}");
            }
        }
        return $rank_type_live_detail_map;
    }

    /**
     * 获取音频类型榜单详情
     *
     * @param array $rank_type_sound_id_map 榜单类型和音频 ID 数组，e.g. [2 => [74562, 87576], 3 => [87576, 45064]]
     * @return array
     */
    public static function getRankSounds(array $rank_type_sound_id_map)
    {
        if (!$rank_type_sound_id_map) {
            return [];
        }
        $rank_view_count_sounds_ids = [];
        $rank_type_sounds_detail_map = [];
        $rank_type_sounds_map = [];
        foreach ($rank_type_sound_id_map as $rank_type => $sound_ids) {
            $rank_view_count_sounds_ids = array_merge($rank_view_count_sounds_ids, $sound_ids);
            // 初始化 map 每个 key 的值
            $rank_type_sounds_detail_map[$rank_type] = [];
            $rank_type_sounds_map[$rank_type] = [];
        }
        $rank_view_count_sounds_ids = array_unique($rank_view_count_sounds_ids);
        $sounds = MSound::find()->select('id, duration, user_id, username, cover_image, soundstr, view_count, pay_type')
            ->where(['id' => $rank_view_count_sounds_ids])
            ->andWhere('checked = :checked AND NOT refined & :not_refined',
                [':checked' => MSound::CHECKED_PASS, ':not_refined' => MSound::REFINED_POLICE_AND_BLOCK])
            ->all();
        if (empty($sounds)) {
            $sound_ids_str = implode(', ', $rank_view_count_sounds_ids);
            Yii::error("首页排行榜音频类型榜单没有符合要求的音频数据，音频 ID: {$sound_ids_str}", __METHOD__);
            return [];
        }
        $sounds_map = array_column($sounds, null, 'id');
        $sids = [];
        $user_ids = [];
        $rank_type_sounds_map = [];
        foreach ($rank_type_sound_id_map as $rank_type => $sound_ids) {
            $index = 0;
            foreach ($sound_ids as $sound_id) {
                if (array_key_exists($sound_id, $sounds_map)) {
                    $rank_type_sounds_map[$rank_type][] = $sounds_map[$sound_id];
                    $sids[] = $sound_id;
                    $user_ids[] = $sounds_map[$sound_id]->user_id;
                    $index++;
                }
                if ($index >= self::RANK_ITEM_LENGTH_HOMEPAGE_SHOW) {
                    // 数量达到展示数量则终止循环
                    break;
                }
            }
            if (empty($rank_type_sounds_map[$rank_type])) {
                Yii::error("首页排行榜没有符合要求的数据，榜单类型为：{$rank_type}", __METHOD__);
                // PASS
            }
        }
        $video_sound_ids = SoundVideo::getVideoSoundIds(array_unique($sids));
        $user_map = Mowangskuser::getSimpleInfoMap(array_unique($user_ids));
        foreach ($rank_type_sounds_map as $rank_type => $sounds) {
            foreach ($sounds as $sound) {
                $user = $user_map[$sound->user_id] ?? null;
                $rank_type_sounds_detail_map[$rank_type][] = [
                    'id' => $sound->id,
                    'duration' => $sound->duration,
                    'soundstr' => $sound->soundstr,
                    'view_count' => $sound->view_count,
                    'front_cover' => $sound->front_cover,
                    'pay_type' => $sound->pay_type,
                    'user_id' => $sound->user_id,
                    'username' => $user ? $user->username : $sound->username,
                    'iconurl' => $user ? $user->iconurl : '',
                    // TODO: 封装获取音频是否有绑定的视频的公共方法
                    'video' => in_array($sound->id, $video_sound_ids)
                ];
            }
        }
        return $rank_type_sounds_detail_map;
    }

    /**
     * 获取剧集类型榜单详情
     *
     * @param array $rank_type_drama_id_map 榜单类型和剧集 ID 数组，e.g. [2 => [74562, 87576], 3 => [87576, 45064]]
     * @return array
     */
    public static function getRankDramas(array $rank_type_drama_id_map)
    {
        if (!$rank_type_drama_id_map) {
            return [];
        }
        $rank_reward_drama_ids = [];
        $rank_view_count_drama_ids = [];
        $rank_type_dramas_map = [];
        foreach ($rank_type_drama_id_map as $rank_type => $drama_ids) {
            $rank_type_dramas_map[$rank_type] = [];
            if ($rank_type === self::TYPE_RANK_DRAMA_REWARD) {
                // 打赏榜剧集 ID
                $rank_reward_drama_ids = $drama_ids;
            } else {
                // 其他榜单剧集 ID
                $rank_view_count_drama_ids = array_merge($rank_view_count_drama_ids, $drama_ids);
            }
        }
        // 获取打赏榜需要在首页展示的剧集信息
        if (!empty($rank_reward_drama_ids)) {
            // 打赏榜需要获取合约期满下架的剧集
            $params = ['drama_ids' => array_values(array_unique($rank_reward_drama_ids)),
                'is_rank' => 1,
                'sensitive' => 1];
            $rank_reward_dramas = Drama::rpc('drama/get-drama-by-ids', $params);
            // RPC 返回的剧集内容已经是根据 $rank_reward_drama_ids 排序后的内容
            $index = 0;
            foreach ($rank_reward_dramas as $drama) {
                $rank_type_dramas_map[self::TYPE_RANK_DRAMA_REWARD][] = [
                    'id' => $drama['id'],
                    'name' => $drama['name'],
                    'cover' => $drama['cover'],
                    'cover_color' => $drama['cover_color'],
                    'abstract' => $drama['abstract'],
                    'pay_type' => $drama['pay_type'],
                    'view_count' => $drama['view_count'],
                    'integrity' => $drama['integrity'],
                    'newest' => $drama['newest'],
                ];
                $index++;
                if ($index >= self::RANK_ITEM_LENGTH_HOMEPAGE_SHOW) {
                    // 数量达到展示数量则终止循环
                    break;
                }
            }
        }

        // 获取打赏榜以外的榜单需要在首页展示的剧集信息（剧集打赏榜以外的榜单不需要获取合约期满下架的剧集）
        if (!empty($rank_view_count_drama_ids)) {
            $params = ['drama_ids' => array_values(array_unique($rank_view_count_drama_ids)),
                'is_rank' => 1,
                'sensitive' => 0];
            $rank_view_count_dramas = Drama::rpc('drama/get-drama-by-ids', $params);
            $drama_map = array_column($rank_view_count_dramas, null, 'id');
            foreach ($rank_type_drama_id_map as $rank_type => $drama_ids) {
                if ($rank_type === self::TYPE_RANK_DRAMA_REWARD) {
                    // 排除打赏榜（剧集打赏榜以外的榜单不需要获取合约期满下架的剧集）
                    continue;
                }
                $index = 0;
                foreach ($drama_ids as $drama_id) {
                    if (array_key_exists($drama_id, $drama_map)) {
                        $drama = $drama_map[$drama_id];
                        $rank_type_dramas_map[$rank_type][] = [
                            'id' => $drama['id'],
                            'name' => $drama['name'],
                            'cover' => $drama['cover'],
                            'cover_color' => $drama['cover_color'],
                            'abstract' => $drama['abstract'],
                            'pay_type' => $drama['pay_type'],
                            'view_count' => $drama['view_count'],
                            'integrity' => $drama['integrity'],
                            'newest' => $drama['newest'],
                        ];
                        $index++;
                    }
                    if ($index >= self::RANK_ITEM_LENGTH_HOMEPAGE_SHOW) {
                        // 数量达到展示数量则终止循环
                        break;
                    }
                }
                if (empty($rank_type_dramas_map[$rank_type])) {
                    Yii::error("首页排行榜没有符合要求的数据，榜单类型为：{$rank_type}", __METHOD__);
                    // PASS
                }
            }
        }
        return $rank_type_dramas_map;
    }

    /**
     * 获取榜单内容类型
     *
     * @param int $type 榜单类型
     * @return int 榜单内容类型
     * @throws Exception
     */
    public static function getRankElementType(int $type)
    {
        switch ($type) {
            case self::TYPE_RANK_DRAMA_NEW:
            case self::TYPE_RANK_DRAMA_POPULARITY:
            case self::TYPE_RANK_DRAMA_REWARD:
            case self::TYPE_RANK_DRAMA_FREE:
            case self::TYPE_RANK_DRAMA_ROMANTIC:
                return MPersonaModuleElement::ELEMENT_TYPE_DRAMA;
            case self::TYPE_RANK_SOUND_LOVER:
                return MPersonaModuleElement::ELEMENT_TYPE_SOUND;
            case self::TYPE_RANK_LIVE:
                return MPersonaModuleElement::ELEMENT_TYPE_LIVE;
            default:
                throw new Exception("参数错误，type: {$type}");
        }
    }

    /**
     * 获取该画像下该剧集上榜的排行最高的榜单信息
     *
     * @param int $drama_id 剧集 ID
     * @param int $persona_module 画像 ID
     * @return array|null 该画像下该剧集上榜的排行最高的榜单信息 e.g. ['name' => '打赏总榜', 'url' => 'xxx', 'sort' => 1]
     */
    public static function getDramaDetailRank(int $drama_id, int $persona_module)
    {
        $memcache = Yii::$app->memcache;
        $key = MUtils2::generateCacheKey(KEY_DRAMA_DETAIL_RANKS, $drama_id, $persona_module);
        // 获取该画像下该剧集上榜的所有榜单信息
        if ($data = $memcache->get($key)) {
            $ranks = Json::decode($data);
        } else {
            $ranks = self::getDramaRanks($drama_id, $persona_module);
            $memcache->set($key, Json::encode($ranks), ONE_MINUTE);
        }
        if (empty($ranks)) {
            return null;
        }
        // 若剧集同时出现在多个榜单上，则取上榜排名最高的榜单
        // 若剧集同时出现在多个榜单上，且排名相同，则取优先级最高的榜单，优先级为：人气月榜 > 打赏总榜 > 新品周榜 > 言情周榜 > 免费周榜
        $rank_type_order = [
            self::TYPE_RANK_DRAMA_POPULARITY,
            self::TYPE_RANK_DRAMA_REWARD,
            self::TYPE_RANK_DRAMA_NEW,
            self::TYPE_RANK_DRAMA_ROMANTIC,
            self::TYPE_RANK_DRAMA_FREE,
        ];
        usort($ranks, function ($a, $b) use ($rank_type_order) {
            if ($a['sort'] === $b['sort']) {
                // 如果 sort 值相等，按预定义的 rank_type_order 顺序排序
                $a_type_index = array_search($a['rank_type'], $rank_type_order);
                $b_type_index = array_search($b['rank_type'], $rank_type_order);
                return $a_type_index - $b_type_index;
            }
            // 否则按 sort 值进行升序排序
            return $a['sort'] - $b['sort'];
        });
        return self::processPersonaRank($ranks[0]['rank_type'], $ranks[0]['sort'], $persona_module);
    }

    /**
     * 获取该画像下该剧集上榜的所有榜单信息
     *
     * @param int $drama_id 剧集 ID
     * @param int $persona_module 画像 ID
     * @return array 该画像下该剧集上榜的所有榜单信息 e.g. [['rank_type' => 3, 'sort' => 10], ['rank_type' => 2, 'sort' => 19]]
     */
    private static function getDramaRanks(int $drama_id, int $persona_module)
    {
        // 该画像下该剧集上榜的所有榜单信息
        $ranks = [];
        // 获取该剧集在打赏总榜上的排名
        $reward_rank_all_details = Drama::getDramaRewardRankListFromCache();
        foreach ($reward_rank_all_details as $key => $reward_rank_all_detail) {
            if ($reward_rank_all_detail['id'] === $drama_id) {
                $ranks[] = ['rank_type' => self::TYPE_RANK_DRAMA_REWARD, 'sort' => $key + 1];
                break;
            }
        }

        // 获取该画像下展示的榜单
        $persona_rank_types = MPersonaRank::getRankTypesByPersonaId($persona_module);
        if (empty($persona_rank_types)) {
            // 该画像下未配置榜单时，直接返回
            return $ranks;
        }
        // 此处榜单周期最长为人气月榜，取数范围最多为一个月（按最多 31 天算）内的榜单数据
        $start_bizdate = date('Y-m-d', $_SERVER['REQUEST_TIME'] - 31 * ONE_DAY);
        // 获取该剧集在人气月榜、新品周榜、言情周榜、免费周榜上的排名
        $sub_query = self::find()
            ->select('type, sub_type, MAX(bizdate) AS max_bizdate')
            ->where('data IS NOT NULL')
            ->andWhere(['type' => $persona_rank_types])
            // 此处榜单周期最长为人气月榜，取数范围最多为一个月（按最多 31 天算）内的榜单数据
            ->andWhere('bizdate >= :start_bizdate', [':start_bizdate' => $start_bizdate])
            ->andWhere(
                [
                    'OR',
                    ['type' => self::TYPE_RANK_DRAMA_POPULARITY, 'sub_type' => self::SUB_TYPE_RANK_MONTH],
                    [
                        'type' => [
                            self::TYPE_RANK_DRAMA_NEW,
                            self::TYPE_RANK_DRAMA_ROMANTIC,
                            self::TYPE_RANK_DRAMA_FREE,
                        ],
                        'sub_type' => self::SUB_TYPE_RANK_WEEK,
                    ]
                ]
            )
            ->groupBy('type, sub_type');
        $drama_ranks = self::find()
            ->alias('t')
            ->select('t.bizdate, t.type, t.data')
            ->innerJoin(['t1' => $sub_query],
                't.type = t1.type AND t.sub_type = t1.sub_type AND t.bizdate = t1.max_bizdate')
            // data 为排名后的剧集 ID 数组，e.g. [59723, 71605, 64911, 38226, 11025, 52347, 71614, 69040, 55785]
            ->where(new Expression('JSON_CONTAINS(t.data, :drama_id)', [':drama_id' => (string)$drama_id]))
            // 此处榜单周期最长为人气月榜，取数范围最多为一个月（按最多 31 天算）内的榜单数据
            ->andWhere('t.bizdate >= :start_bizdate', [':start_bizdate' => $start_bizdate])
            ->all();
        if (empty($drama_ranks)) {
            // drama_ranks 为空说明该剧集未上榜，直接返回
            return $ranks;
        }

        $all_drama_ids = [];
        $drama_rank_type_map = [];
        foreach ($drama_ranks as $drama_rank) {
            $all_drama_ids = array_merge($all_drama_ids, $drama_rank->data);
            $drama_rank_type_map[$drama_rank->type] = $drama_rank;
        }
        $all_drama_ids = array_values(array_unique($all_drama_ids));
        $dramas = Drama::rpc('drama/get-drama-by-ids',
            ['drama_ids' => $all_drama_ids, 'is_rank' => 1, 'sensitive' => 0]);
        $drama_map = array_column($dramas, null, 'id');
        if (!array_key_exists($drama_id, $drama_map)) {
            // drama_id 不在 drama_map 中，说明该剧集已被过滤，不会出现在榜单中，直接返回
            return $ranks;
        }
        foreach ($drama_rank_type_map as $rank_type => $drama_rank) {
            $sort = 0;
            foreach ($drama_rank->data as $rank_drama_id) {
                if (!array_key_exists($rank_drama_id, $drama_map)) {
                    // rank_drama_id 不在 drama_map 中，说明该剧集不会在 rank_type 榜单中展示
                    continue;
                }
                $sort++;
                if ($sort > self::RANK_ITEM_SHOW_LENGTH_MAP[$rank_type]) {
                    // sort 大于在榜单详情页上展示的数量，说明该剧集不会在该榜单上展示
                    break;
                }
                if ($rank_drama_id === $drama_id) {
                    // 在该榜单上找到该剧集，记录排名信息
                    $ranks[] = ['rank_type' => $rank_type, 'sort' => $sort];
                    break;
                }
            }
        }
        return $ranks;
    }

    /**
     * 处理榜单信息
     *
     * @param int $rank_type 榜单类型
     * @param int $sort 榜单排名
     * @param int $persona_module 画像 ID
     * @return array|null
     */
    private static function processPersonaRank(int $rank_type, int $sort, int $persona_module)
    {
        $homepage_rank_details_link = Yii::$app->params['web_links']['homepage_rank_details'];
        // navhide 用途参考文档：https://info.missevan.com/pages/viewpage.action?pageId=67240173
        $query_rank_month = http_build_query(['persona_id' => $persona_module, 'type' => $rank_type, 'sub_type' => self::SUB_TYPE_RANK_MONTH, 'navhide' => 1]);
        $url_rank_month = "{$homepage_rank_details_link}?{$query_rank_month}";
        $query_rank_week = http_build_query(['persona_id' => $persona_module, 'type' => $rank_type, 'sub_type' => self::SUB_TYPE_RANK_WEEK, 'navhide' => 1]);
        $url_rank_week = "{$homepage_rank_details_link}?{$query_rank_week}";
        // 人气月榜、新品周榜、言情周榜、免费周榜
        switch ($rank_type) {
            case self::TYPE_RANK_DRAMA_REWARD:
                return ['name' => '打赏总榜', 'url' => 'missevan://reward/rank?type=drama&tab=3', 'sort' => $sort];
            case self::TYPE_RANK_DRAMA_POPULARITY:
                return ['name' => '人气月榜', 'url' => $url_rank_month, 'sort' => $sort];
            case self::TYPE_RANK_DRAMA_NEW:
                return ['name' => '新品周榜', 'url' => $url_rank_week, 'sort' => $sort];
            case self::TYPE_RANK_DRAMA_ROMANTIC:
                return ['name' => '言情周榜', 'url' => $url_rank_week, 'sort' => $sort];
            case self::TYPE_RANK_DRAMA_FREE:
                return ['name' => '免费周榜', 'url' => $url_rank_week, 'sort' => $sort];
            default:
                Yii::error("获取剧集详情页榜单信息时榜单类型错误，rank_type: {$rank_type}", __METHOD__);
                return null;
        }
    }
}
