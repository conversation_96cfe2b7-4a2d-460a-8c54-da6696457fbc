<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "gift".
 *
 * @property integer $id
 * @property string $name
 * @property integer $price
 * @property integer $type
 */
class Gift extends ActiveRecord
{
    const TYPE_LIVE_GIFT = 1;  // 直播间礼物
    const TYPE_BOYFRIEND = 4;  // 猫耳男友
    const TYPE_DRAMA_REWARD = 7;  // 剧集打赏
    const TYPE_LIVE_REBATE_GIFT = 8;  // 直播间白给礼物
    const TYPE_LIVE_LUCKY_GIFT = 9;  // 幸运签礼物
    const TYPE_LIVE_TOKEN_REFUNDABLE = 10;  // 二级货币（可退回），如飞镖
    const TYPE_LIVE_TOKEN_UNREFUNDABLE = 11;  // 二级货币（不可退回），如钥匙

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'gift';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['price', 'type'], 'integer'],
            [['name'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => '礼物名称',
            'price' => '钻石数',
            'type' => '礼物类型',
        ];
    }
}
