<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "m_node_image_map".
 *
 * @property int $id
 * @property int $node_id 节点 ID
 * @property int $image_id 图片 ID
 * @property string $stime 插入时间点（单位：秒）
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 * @property string $img_url 图片尺寸
 * @property int $img_width 图片宽度
 * @property int $img_height 图片高度
 * @property int $size 图片大小
 */
class MNodeImageMap extends ActiveRecord
{
    // 图片相关信息
    public $img_url;
    public $img_width;
    public $img_height;
    public $size;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_node_image_map';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['node_id', 'image_id', 'stime'], 'required'],
            [['node_id', 'image_id', 'create_time', 'modified_time'], 'integer'],
            [['stime'], 'number'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'node_id' => '节点 ID',
            'image_id' => '图片 ID',
            'stime' => '插入时间点',  // 单位：毫秒
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }
}
