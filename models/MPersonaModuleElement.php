<?php

namespace app\models;

use app\components\util\MUtils;
use Yii;
use yii\helpers\Json;
use yii\web\HttpException;

/**
 * This is the model class for table "m_persona_module_element".
 *
 * @property string $id
 * @property string $module_id
 * @property string $persona_id
 * @property integer $element_type
 * @property string $element_id
 * @property integer $sort
 * @property string $summary
 * @property string $cover
 */
class MPersonaModuleElement extends ActiveRecord
{
    // 隐藏的模块
    const MODULE_HIDDEN = 0;
    // 模块类型
    const MODULE_TYPE_ALBUM = 1;
    const MODULE_TYPE_DRAMA = 2;

    // APP 推荐剧集或音单要求的个数
    const ELEMENT_RECOMMENDED_COUNT = 3;
    // WEB 推荐剧集或音单要求的个数
    const WEB_ELEMENT_RECOMMENDED_COUNT = 5;
    // APP 剧集首页自定义模块要求推荐元素最大个数
    const DRAMA_HOMEPAGE_ELEMENT_RECOMMENDED_COUNT = 6;
    // APP 滑动音频模块推荐元素的最大个数
    const APP_SLIDE_SOUND_RECOMMENDED_MAX_COUNT = 30;
    // APP 推荐直播要求的个数
    const APP_LIVE_RECOMMENDED_COUNT = 6;
    // 新人 Tab 推荐音频、剧集或音单要求的最大个数
    const NEW_USER_ELEMENT_RECOMMENDED_MAX_COUNT = 6;
    // APP 首页 Feed 流自定义模块推荐元素最小个数
    const APP_HOMEPAGE_FEED_ELEMENT_RECOMMENDED_MIN_COUNT = 5;
    // APP 首页 Feed 流自定义模块推荐元素最大个数
    const APP_HOMEPAGE_FEED_ELEMENT_RECOMMENDED_MAX_COUNT = 10;

    // 元素类型
    const ELEMENT_TYPE_ALBUM = 1;
    const ELEMENT_TYPE_DRAMA = 2;
    const ELEMENT_TYPE_SOUND = 3;
    const ELEMENT_TYPE_LIVE = 5;
    // 正在直播模块
    const ELEMENT_TYPE_LIVING = 100;

    // 模块样式，0: 竖版（默认）；1: 横版；2: 播放量排行样式；3: 滑动
    const MODULE_STYLE_DEFAULT = 0;
    const MODULE_STYLE_HORIZONTAL = 1;
    const MODULE_STYLE_TOP_PLAY_STYLE = 2;
    const MODULE_STYLE_SLIDE = 3;

    // 元素封面图显示方式标识，二进制第 3 位为 1 时标识封面图为正方形
    const MODULE_ELEMENT_ATTR_STACKED = 0b100;

    // 模块封面图样式（0 为扁平样式，1 为堆叠样式）
    const MODULE_STYLE_FLAT = 0;
    const MODULE_STYLE_STACKED = 1;

    // 精品推荐模块 ID
    const MODULE_RECOMMENDED_ID = 56;

    // 剧集或音频分类 ID 映射为画像需要添加的值
    const CATALOG_TO_PERSONA = 1000;

    // 盲盒剧场推荐模块 persona_id
    const PERSONA_ID_THEATRE = 2000;

    // 新人 Tab persona_id
    const PERSONA_ID_NEW_USER = 2001;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_persona_module_element';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['module_id'], 'required'],
            [['module_id', 'persona_id', 'element_type', 'element_id', 'sort'], 'integer'],
            [['summary', 'cover'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'module_id' => '模块 ID',
            'persona_id' => '用户画像 ID',
            'element_type' => '元素类型：1 为音单，2 为剧集',
            'element_id' => '元素 ID',
            'sort' => '0 为隐藏元素，正整数为元素排序顺序，有小到大排序',
            'summary' => '元素（音单或剧集）之自定义简介',
            'cover' => '元素（音单或剧集）之自定义封面',
        ];
    }

    /**
     * 获取画像下的音单、剧集、音频、直播模块
     *
     * @param int $persona 用户画像
     * @param array $module_ids 模块 ID，如传入则仅查询该数组中的模块
     * @param int|null $filter_attr 需要过滤掉的模块属性比特位
     * @return array
     */
    public static function getModules($persona = null, $module_ids = null, ?int $filter_attr = null)
    {
        $query = self::find()->alias('p')
            ->select('p.module_id, p.sort, m.id, m.title, m.element_type AS type, m.element_style, m.more')
            ->where('sort <> :sort', [':sort' => self::MODULE_HIDDEN])
            ->andWhere(['m.element_type' => [
                self::ELEMENT_TYPE_ALBUM,
                self::ELEMENT_TYPE_DRAMA,
                self::ELEMENT_TYPE_SOUND,
                self::ELEMENT_TYPE_LIVE,
            ]])
            ->innerJoin(YouMightLikeModule::tableName() . ' AS m', 'p.module_id = m.id')
            ->orderBy(['p.sort' => SORT_ASC]);
        if ($persona) {
            $query->andWhere('persona_id = :persona_id', [':persona_id' => $persona]);
        } else {
            // 模块必定存在于画像下，故查询画像 ID 大于 0 的情况
            $query->andWhere('persona_id > 0');
        }
        if ($module_ids) {
            $query->andWhere(['module_id' => $module_ids]);
        }
        if (!is_null($filter_attr)) {
            $query->andWhere('NOT m.element_attr & :element_attr', [':element_attr' => $filter_attr]);
        }
        $modules = $query->asArray()->all();
        if (empty($modules)) {
            return $modules;
        }
        $modules = array_map(function ($item) {
            $item['id'] = (int)$item['id'];
            // 去除标题结尾中半角括号内的备注内容（全角括号内容不需要去除）
            $item['title'] = preg_replace('/ \(.*?\)$/', '', $item['title']);
            $item['type'] = (int)$item['type'];
            $item['module_id'] = (int)$item['module_id'];
            $item['style'] = (int)$item['element_style'];
            unset($item['element_attr'], $item['element_style']);
            $item['sort'] = (int)$item['sort'];
            $more = $item['more'] ? Json::decode($item['more']) : [];
            if (isset($more['url'])) {
                $item['more'] = ['url' => $more['url']];
            } else {
                unset($item['more']);
            }
            return $item;
        }, $modules);
        if (is_numeric($module_ids)) {
            $modules = current($modules);
        }
        return $modules;
    }

    public static function getElementsRecommended($module_ids)
    {
        $elements_recommended = MRecommendedElements::getLikeElementsRecommended($module_ids);
        $elems_module_group = MUtils::groupArray($elements_recommended, 'module_id', 'element_id');

        // 如果推荐位的个数不满足三个则补上非推荐元素
        $counts = array_count_values(array_column($elements_recommended, 'module_id'));
        foreach ($counts as $module_id => $count) {
            if (0 < ($lack = self::ELEMENT_RECOMMENDED_COUNT - $count)) {
                $elem_ids = $elems_module_group[$module_id] ?? [];
                $elements_lack = MPersonaModuleElement::find()
                    ->select('module_id, element_type, element_id, summary, cover, sort')
                    ->where('element_id <> 0 AND module_id = :module_id', [':module_id' => $module_id])
                    ->andWhere(['NOT IN', 'element_id', $elem_ids])
                    ->orderBy(['sort' => SORT_ASC, 'id' => SORT_DESC])->limit($lack)->all();
                $elements_lack = array_map(function ($item) {
                    $item['module_id'] = (int)$item['module_id'];
                    $item['element_id'] = (int)$item['element_id'];
                    return $item;
                }, $elements_lack);

                $elements_recommended = array_merge($elements_recommended, $elements_lack);
            }
        }

        return $elements_recommended;
    }

    public static function getElements($module_ids)
    {
        $elements = self::find()->select('module_id, element_type, element_id, sort, summary, cover')
            ->where('element_id != 0')->andWhere(['module_id' => $module_ids])
            ->orderBy(['sort' => SORT_ASC])->all();
        $elements = array_map(function ($elem) {
            $elem->module_id = (int)$elem->module_id;
            $elem->element_id = (int)$elem->element_id;
            return $elem;
        }, $elements);
        return $elements;
    }

    /**
     * 获取元素（音单或剧集）在模块中的位置
     * @param integer $module_id 模块 ID
     * @param integer $elem_type 元素类型（1 音单，2 剧集）
     * @param integer $elem_id 元素 ID
     * @return integer 元素（音单或剧集）在模块中的位置
     */
    public static function getElemPosition(int $module_id, int $elem_type, int $elem_id)
    {
        $elem_ids = self::find()->select('element_id')
            ->where('module_id = :module_id AND element_type = :element_type')
            ->params([':module_id' => $module_id, ':element_type' => $elem_type])
            ->orderBy(['sort' => SORT_ASC, 'id' => SORT_DESC])->column();
        if (!in_array($elem_id, $elem_ids)) {
            $elem_ids = MRecommendedElements::find()->select('element_id')
                ->where('module_type = :module_type AND module_id = :module_id AND element_type = :element_type')
                ->params([
                    ':module_type' => MRecommendedElements::MODULE_TYPE_LIKE_DRAMA_ALBUM,
                    ':module_id' => $module_id,
                    ':element_type' => $elem_type
                ])->orderBy(['sort' => SORT_ASC])->asArray()->column();
        }

        $elem_position = array_search($elem_id, $elem_ids) + 1;
        return $elem_position;
    }

    public static function getTargetElements($type, $elements)
    {
        $elem_group = MUtils::groupArray($elements, 'element_type');
        $elems = $elem_group[$type] ?? [];
        return $elems;
    }

    public static function getTargetElementIDs($type, $elements)
    {
        $elems = self::getTargetElements($type, $elements);
        $ids = array_column($elems, 'element_id');
        return $ids;
    }

    public static function processFields($type, $datas, $elements)
    {
        $elems = self::getTargetElements($type, $elements);
        $result = array_map(function ($elem) use ($datas, $type) {
            foreach ($datas as $data) {
                if ((isset($data['id']) && $elem['element_id'] === $data['id'])
                        || ($type === self::ELEMENT_TYPE_LIVE && $elem['element_id'] === $data['user_id'])) {
                    $data['sort'] = $elem['sort'];
                    $data['module_id'] = $elem['module_id'];
                    switch ($type) {
                        case self::ELEMENT_TYPE_ALBUM:
                        case self::ELEMENT_TYPE_SOUND:
                            $data['intro'] = MUtils::plainText($elem['summary'] ?: $data['intro']);
                            $data['front_cover'] = $elem['cover'] ?: $data['front_cover'];
                            break;
                    }
                    return $data;
                }
            }
        }, $elems);
        $result = array_filter($result);
        usort($result, function ($a, $b) {
            return $a['sort'] > $b['sort'];
        });
        return $result;
    }

    public static function getAlbums($elements)
    {
        $album_ids = self::getTargetElementIDs(self::ELEMENT_TYPE_ALBUM, $elements);
        $album_data = MAlbum::getRecommendedAlbums($album_ids);
        return self::processFields(self::ELEMENT_TYPE_ALBUM, $album_data, $elements);
    }

    public static function getDramas($elements, $user_id = null)
    {
        $drama_ids = self::getTargetElementIDs(self::ELEMENT_TYPE_DRAMA, $elements);
        $drama_data = Drama::getRecommendedDramas($drama_ids, $user_id);
        return self::processFields(self::ELEMENT_TYPE_DRAMA, $drama_data, $elements);
    }

    public static function getSounds($elements)
    {
        $sound_ids = self::getTargetElementIDs(self::ELEMENT_TYPE_SOUND, $elements);
        $sound_data = MSound::getRecommendedSounds($sound_ids);
        return self::processFields(self::ELEMENT_TYPE_SOUND, $sound_data, $elements);
    }

    /**
     * 获取直播推荐模块详情
     *
     * @param array $elements 推荐元素
     * @param bool $is_old 是否需要返回旧版本数据，iOS < 6.0.9、安卓 < 6.0.9 的版本传入 true
     * @return array
     */
    public static function getLives(array $elements, bool $is_old = false)
    {
        // TODO: 客户端直播推荐模块只需要展示 6 个元素，后续需要改为先获取 6 个要展示元素的用户 IDs 后再请求 rpc 接口
        $user_ids = self::getTargetElementIDs(self::ELEMENT_TYPE_LIVE, $elements);
        $live_data = Live::getRecommendedLives($user_ids, $is_old);
        if (!empty($live_data)) {
            $live_data = self::processFields(self::ELEMENT_TYPE_LIVE, $live_data, $elements);
            return self::sortLiveByStatus($live_data);
        }
        return [];
    }

    /**
     * 将直播推荐模块元素按照直播状态排序（直播中在前，未开播的在后）
     *
     * @param array $live_list 直播推荐模块元素列表
     * @return array 排序后的直播推荐模块元素列表
     */
    private static function sortLiveByStatus(array $live_list): array
    {
        // 直播中的主播
        $status_open_lives = [];
        // 未开播的主播
        $status_close_lives = [];
        foreach ($live_list as $live) {
            if ($live['status'] === Live::STATUS_OPEN) {
                $status_open_lives[] = $live;
            } else {
                $status_close_lives[] = $live;
            }
        }
        return array_merge($status_open_lives, $status_close_lives);
    }

    /**
     * 获取模块详情
     *
     * @param integer $module_id 模块 ID
     * @param integer|null $persona 模块画像值
     * @return array 例：['type' => MPersonaModuleElement::MODULE_TYPE_ALBUM, 'data' => [[...], [...]]]
     * @throws \Exception
     */
    public static function getModuleDetails(int $module_id, ?int $persona)
    {
        $module = self::getModules($persona, $module_id);
        if (empty($module)) {
            return $module;
        }
        $elements = self::getElements($module_id);

        $data = [];
        switch ($module['type']) {
            case self::MODULE_TYPE_ALBUM:
                $data = self::getAlbums($elements);
                break;
            case self::MODULE_TYPE_DRAMA:
                $module['elements'] = self::getDramas($elements);
                YouMightLikeModule::processDramaModuleCover($module);
                $data = $module['elements'];
                break;
            case self::ELEMENT_TYPE_SOUND:
                $data = self::getSounds($elements);
                break;
            default:
                throw new \Exception('未知错误 _(:зゝ∠)_ 请刷新重试');
        }

        return [
            'module' => [
                'id' => $module['id'],
                'title' => $module['title'],
                'style' => $module['style'],
                'type' => $module['type'],
            ],
            'data' => $data,
        ];
    }

    /**
     * 模块点击更多后详情
     *
     * @param integer $module_id 模块 ID
     * @param PaginationParams $p 分页信息
     * @param integer|null $persona 画像
     * @return array 模块及模块下元素信息
     * @throws \Exception 获取到的模块类型非法时抛出异常
     */
    public static function getFavorDetails(int $module_id, PaginationParams $p, ?int $persona = null)
    {
        $memcache = Yii::$app->memcache;
        $modules_data_key = MUtils::generateCacheKey(KEY_RECOMMEND_MODULE_DETAIL, $module_id);
        if ($module_details = $memcache->get($modules_data_key)) {
            $module_details = Json::decode($module_details);
        } else {
            $module_details = self::getModuleDetails($module_id, $persona);
            if (empty($module_details)) {
                return null;
            }
            Yii::$app->memcache->set($modules_data_key, Json::encode($module_details), FIVE_MINUTE);
        }
        $offset = ($p->page - 1) * $p->page_size;
        $count = count($module_details['data']);
        $data = array_slice($module_details['data'], $offset, $p->page_size);
        $elements = ReturnModel::getPaginationData($data, $count, $p->page, $p->page_size);
        return [
            'id' => $module_id,
            'title' => $module_details['module']['title'],
            'module_style' => $module_details['module']['style'],
            'module_type' => $module_details['module']['type'],
            'elements' => $elements,
        ];
    }

    /**
     * 获取精品推荐详情
     *
     * @param integer|null $user_id
     * @param PaginationParams $p
     * @return ReturnModel
     * @throws \Exception
     */
    public static function getRecommendedDramas(?int $user_id, PaginationParams $p)
    {
        $module = self::getFavorDetails(self::MODULE_RECOMMENDED_ID, $p, Persona::TYPE_GENERAL);
        if (!$module) {
            Yii::error('精品推荐模块数据异常');
            return ReturnModel::empty($p->page, $p->page_size);
        }
        $dramas = $module['elements'];
        if (MPersonaModuleElement::MODULE_TYPE_DRAMA === $module['module_type']) {
            Drama::checkNeedPay($dramas->Datas, $user_id);
        }
        return $dramas;
    }

    /**
     * 获取画像下直播模块信息
     *
     * @param int $persona 画像
     * @return YouMightLikeModule|null
     */
    public static function getLiveModule(int $persona)
    {
        $live_module = YouMightLikeModule::find()->alias('m')
            ->where('p.sort <> :hide AND p.persona_id = :persona_id AND m.element_type = :element_type',
                [
                    ':hide' => self::MODULE_HIDDEN,
                    ':persona_id' => $persona,
                    ':element_type' => self::ELEMENT_TYPE_LIVING,
                ])
            ->LeftJoin(self::tableName() . ' AS p', 'p.module_id = m.id')
            ->one();
        return $live_module;
    }

    /**
     * 查询推荐模块下元素数量
     *
     * @param $module_ids
     * @return array
     */
    public static function countModulesElements($module_ids)
    {
        $modules = self::find()
            ->select('module_id, COUNT(*) AS elements_num')
            // element_id 为 0 时为画像与模块关联数据，需要过滤掉，只查询元素与模块绑定数据
            ->where('element_id <> 0')
            ->andWhere(['module_id' => $module_ids])
            ->groupBy('module_id')
            ->indexBy('module_id')
            ->asArray()
            ->all();
        $return = [];
        foreach ($modules as $module_id => $module) {
            $return[$module_id] = (int)$module['elements_num'];
        }
        return $return;
    }

    /**
     * 获取剧集或音频分类对应的模块画像 ID
     *
     * @param int $catalog_id 音频或剧集分类 ID
     * @return int 音频或剧集分类模块画像 ID
     * @throws HttpException 分类不存在时抛出异常
     */
    public static function getCatalogPersonaId(int $catalog_id): int
    {
        if (!Catalog::find()->where(['id' => $catalog_id, 'status_is' => Catalog::STATUS_OPEN])->exists()) {
            throw new HttpException(404, '分类不存在');
        }
        return self::CATALOG_TO_PERSONA + $catalog_id;
    }
}
