<?php

namespace app\models;

use app\components\util\Go;
use app\components\util\MUtils;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\db\Expression;
use yii\web\HttpException;

/**
 * This is the model class for table "sound_comment".
 *
 * @property integer $id
 * @property string $comment_content
 * @property integer $c_type
 * @property integer $element_id
 * @property integer $ctime
 * @property integer $userid
 * @property string $username
 * @property integer $sub_comment_num
 * @property integer $like_num
 * @property integer $floor
 * @property array $subcomments
 * @property integer $authenticated
 * @property integer $control_num
 * @property boolean $is_ad
 * @property integer $checked 评论属性，-1：软删除；1：正常评论；2：评论违规
 * @property integer $liked 是否点赞（0：否；1：是）
 * @property integer $disliked 是否点踩（0：否；1：是）
 * @property float $hot 评论热度
 * @property string $ip_detail 用户 IP 详情
 */
class SoundComment extends ActiveRecord
{
    const SCENARIO_ADD = 'add_comment';

    // 被评论的元素类型
    const TYPE_SOUND = 1;
    const TYPE_ALBUM = 2;
    const TYPE_NEWS = 3;
    const TYPE_TAG = 4;
    const TYPE_USER = 5;
    const TYPE_TOPIC = 6;
    const TYPE_EVENT = 7;
    const TYPE_VOICE_CARD = 8;
    const TYPE_OMIKUJI_CARD = 9;
    const TYPE_DRAMA = 10;

    const LABEL_OF_TYPE = [
        self::TYPE_SOUND => '音频',
        self::TYPE_ALBUM => '音单',
        self::TYPE_NEWS => '新闻',
        self::TYPE_TAG => '频道',
        self::TYPE_USER => '用户',
        self::TYPE_TOPIC => '专题',
        self::TYPE_EVENT => '活动',
        self::TYPE_VOICE_CARD => '语音包',
        self::TYPE_OMIKUJI_CARD => '求签包',
    ];

    // 评论属性，-1：已被软删除，0：特殊值，获取全部评论时使用，1：正常评论，2：违规评论
    // TODO: 目前暂未使用软删除，之后需要补上
    const ATTR_DELETE = -1;
    const ATTR_All = 0;
    const CHECKED_COMMON = 1;
    const CHECKED_VIOLATION = 2;

    // 评论最小长度
    const MIN_LENGTH = 3;
    // 评论最大长度
    const MAX_LENGTH = 2000;

    // 新用户（注册不满 3 天的用户）每天最大评论数
    const NEW_USER_COMMENT_MAX_NUM = 20;
    // 老用户每天最大评论数
    const USER_COMMENT_MAX_NUM = 500;

    public $icon;
    public $subcomments;
    public $authenticated;
    public $liked;
    public $disliked;
    public $ip_location;
    // 评论热度（评论热度 = 赞值数 - (真实踩值 + 后台设置的管控值) * 比重）
    public $hot;
    public $is_blacklist;
    // 用户信息
    public $user;

    // 是否为广告类型的评论
    protected $is_ad;

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios[self::SCENARIO_ADD] = ['comment_content', 'element_id', 'c_type'];
        return $scenarios;
    }

    /**
     * @inheritdoc
     */
    public static function getDb()
    {
        return Yii::$app->messagedb;
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'sound_comment';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['comment_content', 'c_type', 'element_id'], 'required'],
            [['comment_content'], 'string', 'max' => self::MAX_LENGTH],
            ['comment_content', 'checkText', 'on' => self::SCENARIO_ADD],
            [
                ['c_type', 'element_id', 'ctime', 'userid', 'sub_comment_num', 'like_num', 'floor', 'checked'],
                'integer'
            ],
            [['username'], 'string', 'max' => 20],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '评论的主键，自增',
            'comment_content' => '评论内容',
            'c_type' => '对什么的评论，1表示单音，2表示专辑,3表示新闻（暂定），4表示小剧场（暂定）',
            'element_id' => '对象对应的id',
            'ctime' => '创建评论时间',
            'userid' => '上传者id，mowangskuser中的id',
            'username' => '上传者用户名，mowangskuser中的username',
            'sub_comment_num' => '子评论数',
            'like_num' => '点赞数',  // 该字段为冗余字段
            'dislike_num' => '点踩数',  // 该字段为用户真实点踩数量冗余字段
            'control_num' => '热力数',  // 管控点踩数（叫“热力数”避免该字段名称意外暴露给用户时造成负面影响）
            'floor' => 'Floor',  // TODO: 数据库主库升级到 5.7 版本后，考虑删除 floor 字段
            'checked' => '评论属性',  // -1：软删除；1：正常评论；2：评论违规
            'ip_detail' => '用户 IP 详情',
        ];
    }

    /**
     * 对评论内容进行过滤和检查
     *
     * @param string $attribute 验证的属性
     * @param array $params 自定义参数
     */
    public function checkText($attribute, $params)
    {
        $this->$attribute = trim(MUtils::filterSpecialCodes($this->$attribute));
        if (mb_strlen($this->$attribute) < self::MIN_LENGTH) {
            $this->addError($attribute, Yii::t('app/error',
                'Please send non-emotional comments larger than {num} words', ['num' => self::MIN_LENGTH]));
        }
        // TODO: 整合在一起判断
        // 检测评论违规情况
        if (!$result = Yii::$app->go->checkText($this->$attribute, Go::SCENE_COMMENT)) {
            return;
        }
        if (!$item = current($result)) {
            return;
        }
        if (!$item['pass']) {
            $this->checked = self::CHECKED_VIOLATION;
            // $this->addError($attribute, $this->getAttributeLabel($attribute) . '中含有违规词汇喔~');
        } elseif (is_array($item['labels']) && $this->checkShamSend($item['labels'])) {
            $this->checked = self::CHECKED_VIOLATION;
        }
    }

    /**
     * 检查是否假发送
     *
     * @param $labels
     * @return bool
     */
    protected function checkShamSend(array $labels): bool
    {
        return !empty(array_intersect([Go::LABEL_AD, Go::LABEL_EVIL], $labels));
    }

    // 删除关联字段
    public function afterDelete()
    {
        CommentLike::deleteAll('cid = :cid AND sub = :sub', [
            ':cid' => $this->id,
            ':sub' => Commentnotice::TYPE_COMMENT
        ]);
        // 清除提醒消息
        Commentnotice::deleteRelation($this->id);
        $subcomment_count = SoundSubComment::deleteAll(['comment_id' => $this->id]);
        if ($this->checked === self::CHECKED_COMMON) {
            // 更新关联表的对应评论数量
            $time = $_SERVER['REQUEST_TIME'];
            switch ($this->c_type) {
                case self::TYPE_SOUND:
                    MSound::updateAll([
                        'comments_count' => new Expression('GREATEST(comments_count, 1) - 1'),
                        'sub_comments_count' => new Expression(
                            "GREATEST(sub_comments_count, $subcomment_count) - $subcomment_count"),
                        'last_update_time' => $time,
                    ], 'id = :id', [':id' => $this->element_id]);
                    break;
                case self::TYPE_ALBUM:
                    MAlbum::updateAll([
                        'comment_count' => new Expression('GREATEST(comment_count, 1) - 1'),
                        'last_update_time' => $time,
                    ], 'id = :id', [':id' => $this->element_id]);
                    break;
            }
        }
    }

    /**
     * 删除评论或子评论
     * @param integer $comment_id 评论或子评论的 ID
     * @param integer $sub 类型：0 为评论，1 为子评论
     * @return string
     * @throws HttpException
     */
    public static function del(int $comment_id, int $sub = Commentnotice::TYPE_COMMENT)
    {
        $comment = $sub_comment = null;
        if (Commentnotice::TYPE_COMMENT === $sub) {
            // 查询出要删除的评论
            $comment = SoundComment::find()->where(['id' => $comment_id])->one(SoundCommentRO::getDb());
            if (!$comment) {
                throw new HttpException(404, Yii::t('app/error', 'Original comment is missing'), 200310001);
            }
            $author_id = $comment->userid;
        } elseif (Commentnotice::TYPE_SUB_COMMENT === $sub) {
            // 查询出要删除的回复及回复的评论
            $sub_comment = SoundSubComment::find()->where(['id' => $comment_id])->one(SoundSubCommentRO::getDb());
            if (!$sub_comment) {
                throw new HttpException(404, Yii::t('app/error', 'The reply does not exist'), 200310001);
            }
            $author_id = $sub_comment->userid;
            $comment = SoundComment::find()->where(['id' => $sub_comment->comment_id])->one(SoundCommentRO::getDb());
            if (!$comment) {
                throw new HttpException(404, Yii::t('app/error', 'Original comment is missing'), 200310001);
            }
        }
        if (Yii::$app->user->id !== $author_id) {
            switch ($comment->c_type) {
                case self::TYPE_SOUND:
                    $work = MSound::findOne($comment->element_id);
                    break;
                case self::TYPE_ALBUM:
                    $work = MAlbum::findOne($comment->element_id);
                    break;
                default:
                    $work = null;
            }
            if (!$work || $work->user_id !== Yii::$app->user->id) {
                throw new HttpException(403,
                    Yii::t('app/error', 'Only reviewer or uploader has right to operate'), 200310101);
            }
        }

        if (Commentnotice::TYPE_COMMENT === $sub && !$comment->delete()) {
            throw new HttpException(500, Yii::t('app/error', 'Failed to delete'));
        }
        if (Commentnotice::TYPE_SUB_COMMENT === $sub && !$sub_comment->delete()) {
            throw new HttpException(500, Yii::t('app/error', 'Failed to delete'));
        }

        return Yii::t('app/base', 'Successfully deleted');
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        if ($insert) {
            $this->ctime = $_SERVER['REQUEST_TIME'];
            $this->userid = Yii::$app->user->id;
            $this->username = Yii::$app->user->name;
            $this->floor = 0;  // 字段未被使用，默认为 0
            // 评论属性默认为正常评论
            $this->checked = $this->checked ?? self::CHECKED_COMMON;
        }
        return true;
    }

    public function afterSave($insert, $changedAttributes)
    {
        parent::afterSave($insert, $changedAttributes);
        if ($insert && $this->checked === self::CHECKED_COMMON) {
            switch ($this->c_type) {
                case self::TYPE_SOUND:
                    MSound::updateAllCounters(['comments_count' => 1], 'id = :id', [':id' => $this->element_id]);
                    break;
                case self::TYPE_ALBUM:
                    MAlbum::updateAllCounters(['comment_count' => 1], 'id = :id', [':id' => $this->element_id]);
                    break;
            }
        }
        return true;
    }

    /**
     * 验证评论对象是否存在，获取评论对象的 UP 主 ID
     *
     * @param int $type 评论对象类型 1：单音评论，2：音单评论，4：频道评论，8：语音卡评论，9：运势卡评论
     * @param int $id 评论对象 ID
     * @param int $up_user_id 评论对象 UP 主 ID
     * @throws HttpException
     */
    public static function checkAndGetElementUpId(int $type, int $id, int &$up_user_id = 0)
    {
        switch ($type) {
            case self::TYPE_SOUND:
                $up_user_id = (int)MSound::find()->select('user_id')
                    ->where('id = :id', [':id' => $id])->scalar();
                if (!$up_user_id) {
                    throw new HttpException(404, '该音频不存在', 200120001);
                }
                break;
            case self::TYPE_ALBUM:
                $up_user_id = (int)MAlbum::find()->select('user_id')
                    ->where('id = :id', [':id' => $id])->scalar();
                if (!$up_user_id) {
                    throw new HttpException(404, '该音单不存在', 200120001);
                }
                break;
            case self::TYPE_TAG:
                if (!MTag::find()->where('id = :id', [':id' => $id])->exists()) {
                    throw new HttpException(404, '该频道不存在', 200430001);
                }
                break;
            case self::TYPE_VOICE_CARD:
            case self::TYPE_OMIKUJI_CARD:
                if (!Card::find()->where('id = :id', [':id' => $id])->exists()) {
                    throw new HttpException(404, '该语音不存在', 200430001);
                }
                break;
            default:
                throw new HttpException(404, '评论对象不存在', 200430001);
        }
    }

    // 评论内容解析
    public static function parseComment(&$comment_content, $elem_id, $type, int $user_id)
    {
        $comment_content = trim($comment_content);
        if (!$comment_content) {
            throw new HttpException(400,
                Yii::t('app/error', 'Comment content cannot be empty'), 200310100);
        }
        $users = [];
        $comment_content = preg_replace_callback('/((?:@)([\x{2E80}-\x{9FFF}A-Za-z0-9_]+?))(\s:|\s|：|$)/u',
            function ($matches) use (&$users, $type, $elem_id, $user_id) {
                // 兼容客户端旧版本 @ 用户文本格式为空格加半角冒号，将其统一转化为全角冒号
                $colon = preg_replace('/^(\s:)/', '：', $matches[3]);
                $user = Mowangskuser::find()->select('id, username')->where(['username' => $matches[2]])
                    ->one();
                if ($user) {
                    if (in_array($type, [SoundComment::TYPE_VOICE_CARD, SoundComment::TYPE_OMIKUJI_CARD])
                            && !GetCard::find()->where(['user_id' => $user->id, 'card_id' => $elem_id])->exists()) {
                        // 若为评论语音卡且被 @ 的用户没有语音卡，则不提醒
                        return $matches[1] . $colon;
                    }
                    // @ 黑名单关系的用户时，没有 @ 的通知提醒
                    $blacklist_relation = BlackUser::getBlacklistRelation($user_id, $user->id);
                    if (BlackUser::BLACKLIST_NONE === $blacklist_relation) {
                        // 双方都为非拉黑状态时可以收到 @ 通知
                        $users[$user->id] = $user->username;
                    }
                    return $matches[1] . '(' . $user->id . ')' . $colon;
                }
                return $matches[1] . $colon;
            },
            $comment_content);
        return $users;
    }

    /**
     * 获取元素子评论总数
     *
     * @param int $type 评论元素的类型
     * @param int $element_id 评论的元素 ID
     * @return int 元素的子评论总数
     */
    public static function getAllSubCommentNum(int $type, int $element_id) : int
    {
        if ($type === self::TYPE_SOUND) {
            // 若为元素类型为单音，直接查询单音下的子评论数
            $sub_comment_num = MSound::find()
                ->select('sub_comments_count')
                ->where("id = $element_id")
                ->scalar();
        } else {
            $sub_comment_num = SoundCommentRO::find()
                ->select('SUM(sub_comment_num)')
                ->where(['c_type' => $type, 'element_id' => $element_id])
                ->scalar();
        }
        return (int)$sub_comment_num;
    }

    /**
     * 检查评论的元素是否存在
     *
     * @param int $elem_type 评论元素的类型
     * @param int $elem_id 评论的元素 ID
     * @param int $user_id 评论的元素 ID
     * @return array 元素的信息
     * @throws HttpException
     */
    public static function checkCommentElement($elem_type, $elem_id, $user_id)
    {
        $up_username = $title = $up_user_id = null;
        switch ($elem_type) {
            case self::TYPE_ALBUM:
                if (!$album = MAlbum::findOne(['id' => $elem_id])) {
                    throw new HttpException(404, '该音单不存在', 200120001);
                }
                $up_user_id = $album->user_id;
                $up_username = $album->username;
                $title = $album->title;
                break;
            case self::TYPE_TAG:
                if (!$tag = MTag::findOne(['id' => $elem_id, 'recommended' => MTag::RECOMMENDED_CHANNEL])) {
                    throw new HttpException(404, '该频道不存在', 200430001);
                }
                $title = $tag->name;
                break;
            case self::TYPE_TOPIC:
                if (!$topic = Topic::findOne(['id' => $elem_id])) {
                    throw new HttpException(404, '该专题不存在', 200120001);
                }
                $title = $topic->title;
                break;
            case self::TYPE_EVENT:
                if (!$event = MEvent::findOne(['id' => $elem_id])) {
                    throw new HttpException(404, '该活动不存在', 200120001);
                }
                $title = $event->title;
                break;
            case self::TYPE_SOUND:
                if (!$sound = MSound::findOne(['id' => $elem_id])) {
                    throw new HttpException(404, Yii::t('app/error', 'Audio does not exist'), 200120001);
                }
                if (MSound::CHECKED_CONTRACT_EXPIRED === $sound->checked) {
                    throw new HttpException(403, Yii::t('app/error',
                        'The copyright of the current content has expired. The operation is prohibited'));
                }
                if (MSound::SOUND_FREE !== $sound->pay_type && $user_id !== $sound->user_id) {
                    MSound::checkNeedPay($sound, $user_id);
                    if (MSound::SOUND_UNPAID === $sound->need_pay) {
                        throw new HttpException(403, Yii::t('app/error', 'Only purchasers can comment'));
                    }
                }
                $up_user_id = $sound->user_id;
                $up_username = $sound->username;
                $title = $sound->soundstr;
                break;
            case self::TYPE_VOICE_CARD:
                if (!$card = Card::findOne(['id' => $elem_id])) {
                    throw new HttpException(404, '评论的语音不存在', 200120001);
                }
                if ($card->special !== Card::SPECIAL_FREE && $card->special !== Card::SPECIAL_HOTCARD) {
                    if (!GetCard::find()->where(['user_id' => $user_id, 'card_id' => $elem_id])->exists()) {
                        throw new HttpException(403, '未获得的语音不能评论哦');
                    }
                }
                $title = $card->title;
                break;
            case self::TYPE_OMIKUJI_CARD:
                if (!$card = Card::findOne(['id' => $elem_id])) {
                    throw new HttpException(404, '评论的语音不存在', 200120001);
                }
                if (!GetCard::find()->where(['user_id' => $user_id, 'card_id' => $elem_id])->exists()) {
                    throw new HttpException(403, '未获得的语音不能评论哦');
                }
                $title = $card->title;
                break;
            default:
                throw new HttpException(400, '参数错误');
        }

        return [
            'up_user_id' => $up_user_id,
            'up_username' => $up_username,
            'elem_title' => $title,
        ];
    }

    /**
     * 限制用户每日评论数量
     * 主要用于限制无意义灌水和广告评论
     *
     * @param int $user_id 用户 ID
     * @throws HttpException 评论额度用完时抛出异常
     */
    public static function limitCount(int $user_id)
    {
        $now = $_SERVER['REQUEST_TIME'];
        $user = Mowangskuser::find()->select('ctime')->where('id = :user_id', [':user_id' => $user_id])->one();
        $register_time = $user->ctime;
        // 若为注册时间不满 3 天的用户，视作新用户，新老用户每日可发送弹幕额度不同
        $limit_count = ($now - $register_time) > THREE_DAYS ? self::USER_COMMENT_MAX_NUM
            : self::NEW_USER_COMMENT_MAX_NUM;
        // 若为注册时间不满 3 天的用户，视作新用户，限制每日评论数
        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(KEY_LOCK_USER_COMMENT, $user_id);
        if ($redis->incr($lock) > $limit_count) {
            throw new HttpException(403, Yii::t('app/error',
                'Today\'s comment count has reached quota ({max_num}/{max_num})', [
                    'max_num' => self::NEW_USER_COMMENT_MAX_NUM,
                ]));
        }
        if ($redis->ttl($lock) === -1) {
            // 获得第二天零点时间戳
            $tomorrow_unix = strtotime('tomorrow');
            $time = $tomorrow_unix - $now;
            $redis->expire($lock, $time);
        }
    }

    /**
     * 屏蔽评论中黑名单用户的评论
     *
     * @param array $comments 评论数组
     * @param array $blacklist_user_ids 黑名单用户 IDs
     */
    public static function filterBlacklistComments(?array &$comments, array $blacklist_user_ids)
    {
        if ($comments && !empty($blacklist_user_ids)) {
            foreach ($comments as &$comment) {
                // 处理评论
                if (in_array($comment->userid, $blacklist_user_ids)) {
                    $comment->username = '黑名单用户';
                    $comment->icon = Yii::$app->params['blacklistUserAvatarUrl'];
                    $comment->comment_content = '该用户的评论已被你屏蔽';
                    $comment->is_blacklist = 1;
                }
                if (isset($comment->subcomments) && $comment->subcomments) {
                    // 处理子评论
                    self::filterBlacklistComments($comment->subcomments, $blacklist_user_ids);
                }
            }
        }
    }

    /**
     * 是否包含违规的评论
     *
     * @param int $type
     * @param int $user_id
     * @param int|array $element_ids
     * @return bool
     */
    public static function hasViolationComment(int $type, int $user_id, $element_ids): bool
    {
        return static::find()
            ->where([
                'c_type' => $type,
                'userid' => $user_id,
                'element_id' => $element_ids,
                'checked' => self::CHECKED_VIOLATION,
            ])
            ->exists();
    }

    /**
     * 展示 IP 属地信息
     *
     * @param SoundComment|SoundComment[]|SoundSubComment|SoundSubComment[] $models 评论数据
     */
    public static function showIPLocation(&$models)
    {
        if (!$models) {
            return;
        }
        if (is_array($models)) {
            foreach ($models as &$model) {
                $model['ip_location'] = MUtils2::getIPLocationFromIPDetail($model['ip_detail']);
                unset($model['ip_detail']);
            }
            unset($model);
        } else {
            $models['ip_location'] = MUtils2::getIPLocationFromIPDetail($models['ip_detail']);
            unset($models['ip_detail']);
        }
    }
}
