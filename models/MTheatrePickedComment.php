<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "m_theatre_picked_comment".
 *
 * @property integer $id
 * @property integer $create_time 创建时间
 * @property integer $modified_time 更新时间
 * @property integer $comment_id 评论 ID
 * @property string $comment_content 评论的具体内容
 * @property integer $drama_id 评论所属剧集 ID
 * @property string $drama_name 剧集名称
 * @property integer $sound_id 评论所属音频 ID
 * @property integer $user_id 发表评论的用户 ID
 * @property string $sort 评论展示顺序
 */
class MTheatrePickedComment extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_theatre_picked_comment';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['comment_id', 'comment_content', 'drama_id', 'drama_name', 'sound_id', 'user_id', 'create_time'],
                'required'],
            [['comment_id', 'drama_id', 'sound_id', 'user_id', 'sort'], 'integer'],
            [['drama_name'], 'string', 'max' => 60],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'comment_id' => '评论 ID',
            'comment_content' => '评论的具体内容',
            'drama_id' => '评论所属剧集 ID',
            'drama_name' => '剧集名称',
            'sound_id' => '评论所属音频 ID',
            'user_id' => '发表评论的用户 ID',
            'sort' => '评论展示顺序',
        ];
    }

    /**
     * 获取所有精选评论
     *
     * @return array
     */
    public static function getPickedComments()
    {
        $picked_comments = self::find()
            ->select('comment_id, comment_content, drama_name AS name, sound_id, user_id, sort')
            ->orderBy('sort ASC')->asArray()->all();
        if (!empty($picked_comments)) {
            $user_ids = array_unique(array_column($picked_comments, 'user_id'));
            $users = Mowangskuser::find()
                ->select('id, username, iconurl, boardiconurl, avatar, icontype')
                ->where(['id' => $user_ids])
                ->indexBy('id')
                ->all();
            $picked_comments = array_map(function ($item) use ($users) {
                $item['comment_id'] = (int)$item['comment_id'];
                $item['sound_id'] = (int)$item['sound_id'];
                $item['user_id'] = (int)$item['user_id'];
                $item['sort'] = (int)$item['sort'];
                $item['iconurl'] = '';
                $item['username'] = '';
                if (isset($users[$item['user_id']])) {
                    $item['username'] = $users[$item['user_id']]['username'];
                    $item['iconurl'] = $users[$item['user_id']]['iconurl'];
                }
                return $item;
            }, $picked_comments);
        }
        return $picked_comments;
    }
}
