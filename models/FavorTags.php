<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "favor_tags".
 *
 * @property integer $id
 * @property integer $gender
 * @property integer $tag_id
 * @property string $tag_name
 * @property string $icon
 * @property integer $is_show
 * @property integer $sort
 */
class FavorTags extends ActiveRecord
{
    const GENDER_MALE = 0;
    const GENDER_FEMALE = 1;
    const TAG_HIDE = 0;
    const TAG_SHOW = 1;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'favor_tags';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['gender', 'tag_id', 'tag_name', 'icon'], 'required'],
            [['gender', 'tag_id', 'is_show', 'sort'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'gender' => '性别',
            'tag_id' => '标签 ID',
            'tag_name' => '标签显示名称',
            'icon' => '标签的图标地址',
            'is_show' => '是否显示',
            'sort' => '排序',
        ];
    }

    public static function getTags()
    {
        $tags = self::find()->select('gender, tag_id, tag_name, icon')
            ->where(['is_show' => self::TAG_SHOW])->orderBy(['sort' => SORT_ASC])->asArray()->all();
        $data['male'] = $data['female'] = [];
        foreach ($tags as $tag) {
            if ($tag['gender']) {
                unset($tag['gender']);
                $data['female'][] = $tag;
            } else {
                unset($tag['gender']);
                $data['male'][] = $tag;
            }
        }
        return $data;
    }
}
