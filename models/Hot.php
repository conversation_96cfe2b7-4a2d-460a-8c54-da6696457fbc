<?php

namespace app\models;

use Yii;
use yii\base\Exception;

/**
 * This is the model class for table "hot".
 *
 * @property int $id 主键
 * @property int $user_id 用户 ID
 * @property int $work_id 作品 ID
 * @property int $hot_value 贡献的热度值
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 */
class Hot extends ActiveRecord
{
    // 抽卡、购买及每日首播时增加的热度值
    const HOT_ONE_DRAW = 4;
    const HOT_TEN_DRAW = 40;
    const HOT_BUY = 30;
    const HOT_LISTEN = 2;

    // 热度值格式化时的最大长度，暂定为千万级别
    const HOT_ENERGY_MAX_LENGTH = 8;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'hot';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db2');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'work_id', 'hot_value'], 'integer']
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => '用户 ID',
            'work_id' => '作品 ID',
            'hot_value' => '贡献的热度值',
            'create_time' => '创建时间',
            'modified_time' => '更新时间'
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 播放语音卡更新热度值
     * 用户每天首次播放语音卡时进行热度值更新操作
     *
     * @param int $work_id
     * @param int $user_id
     */
    public static function updateHotByListen(int $work_id, int $user_id)
    {
        $redis = Yii::$app->redis;
        $listened_key = $redis->generateKey(LOCK_VOICE_LISTENED_WORK_USER, $work_id, $user_id);
        $expire = strtotime('tomorrow') - ($_SERVER['REQUEST_TIME'] ?? time());
        if (!$redis->lock($listened_key, $expire)) return;
        // 加入首播日志
        $add_log = FirstListenLog::addFirstListenLog($work_id, $user_id);
        if ($add_log) {
            // 更新用户贡献值
            self::updateUserHot($work_id, $user_id, self::HOT_LISTEN);
        }
    }

    /**
     * 更新用户贡献的热度值
     *
     * @param int $work_id 作品 ID
     * @param int $user_id 用户 ID
     * @param int $hot_value 增长的热度值
     */
    public static function updateUserHot(int $work_id, int $user_id, int $hot_value)
    {
        if ((!$user_hot_energy = self::findOne(['work_id' => $work_id, 'user_id' => $user_id]))) {
            $user_hot_energy = new self([
                'work_id' => $work_id,
                'user_id' => $user_id,
                'hot_value' => $hot_value
            ]);
            $hot_update = $user_hot_energy->save();
        } else {
            $hot_update = $user_hot_energy->updateCounters(['hot_value' => $hot_value]);
        }
        if ($hot_update) {
            $redis = Yii::$app->redis;
            // 更新作品总热度值
            $key = $redis->generateKey(KEY_VOICE_HOT_VALUE_WORK_ID, $work_id);
            $redis->hIncrBy($key, 'hot_value', $hot_value);
            $all_hot = array_sum($redis->hGetAll($key));
            // 解锁热度卡片
            Card::updateHotCards($work_id, $all_hot);
        }
    }

    /**
     * 获取作品热度值
     *
     * @param int $work_id 作品 ID
     * @param int $user_id 用户 ID，传入该值时表示只获取该用户贡献的热度值
     * @return int 某用户或全部用户贡献的热度值
     */
    public static function getHot(int $work_id, int $user_id = 0): int
    {
        if (!$user_id) {
            $redis = Yii::$app->redis;
            $key = $redis->generateKey(KEY_VOICE_HOT_VALUE_WORK_ID, $work_id);
            $hots = $redis->hGetAll($key);
            // 返回热度值实际额度与补偿额度的总和
            return array_sum($hots);
        }
        return (int)self::find()
            ->select('hot_value')
            ->where(['work_id' => $work_id, 'user_id' => $user_id])
            ->scalar();
    }

    /**
     * 格式化热度值
     * 如将 2333 格式化为 “00,023,333”
     *
     * @param int $work_id 作品 ID
     * @param int $user_id 用户 ID，传入该值时表示只获取该用户贡献的热度值
     * @return string 格式化后的字符串
     */
    public static function formatValue(int $work_id, int $user_id = 0): string
    {
        // 获得作品热度值
        $all_hot = self::getHot($work_id, $user_id);
        if (strlen($all_hot) < self::HOT_ENERGY_MAX_LENGTH && !$user_id) {
            // 当热度值长度小于 8 位并且不是个人热度值时，需要在前面补零
            $str = str_pad($all_hot, self::HOT_ENERGY_MAX_LENGTH, '0', STR_PAD_LEFT);
            // 将最高位设置为 1
            $str[0] = 1;
            $str = number_format($str);
            // 将最高位还原为 0
            $str[0] = 0;
        } else {
            $str = number_format($all_hot);
        }
        return $str;
    }
}
