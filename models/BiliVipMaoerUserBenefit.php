<?php

namespace app\models;

use app\components\util\SSOClient;
use Exception;
use Yii;
use yii\helpers\Json;
use yii\web\HttpException;

/**
 * This is the model class for table "bili_vip_maoer_user_benefit".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间（秒级时间戳）
 * @property int $modified_time 修改时间（秒级时间戳）
 * @property int $deliver_time 权益发放时间
 * @property int $status 权益状态
 * @property int $benefit_id 权益 ID
 * @property int $region B站大会员对应的手机号的国际区号
 * @property string $mobile B站大会员对应的手机号（密文）
 * @property string $bili_uid B站用户 ID
 * @property int $maoer_uid 猫耳用户 ID
 * @property array|null $more 更多详情
 * @property string $request_id 请求唯一标识
 *
 * @property-read int $bili_vip_period_start B站大会员当前周期生效时间
 * @property-read int $bili_vip_period_end B站大会员当前周期结束时间
 */
class BiliVipMaoerUserBenefit extends ActiveRecord
{
    use ActiveRecordTrait;

    // 权益状态
    const STATUS_PENDING = 1;  // 发放中
    const STATUS_SUCCESS = 2;  // 发放成功
    const STATUS_FAILED = 3;  // 发放失败
    const STATUS_EXPIRED = 4;  // 权益过期

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->db;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'bili_vip_maoer_user_benefit';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['region', 'mobile', 'bili_uid', 'maoer_uid', 'deliver_time', 'status', 'benefit_id'], 'required'],
            [['create_time', 'modified_time', 'region', 'maoer_uid', 'deliver_time', 'status', 'benefit_id'], 'integer'],
            [['more'], 'safe'],
            [['mobile'], 'string', 'max' => 60],
            [['bili_uid'], 'string', 'max' => 32],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间（秒级时间戳）',
            'modified_time' => '修改时间（秒级时间戳）',
            'deliver_time' => '权益发放时间',
            'status' => '权益状态',
            'benefit_id' => '权益 ID',
            'region' => 'B站大会员对应的手机号的国际区号',
            'mobile' => 'B站大会员对应的手机号（密文）',
            'bili_uid' => 'B站用户 ID',
            'maoer_uid' => '猫耳用户 ID',
            'more' => '更多详情',
        ];
    }

    public function afterFind()
    {
        if (!$this->more) {
            $this->more = [];
        }
    }

    public function getBili_vip_period_start(): ?int
    {
        return $this->more['bili_vip_period_start'] ?? null;
    }

    public function getBili_vip_period_end(): ?int
    {
        return $this->more['bili_vip_period_end'] ?? null;
    }

    public static function initiate(BiliVipMaoerBenefitMenu $item, MobileNumber $mobile_info, int $user_id, string $bili_uid, array $more): self
    {
        switch ($item->benefit_type) {
            case BiliVipMaoerBenefitMenu::BENEFIT_TYPE_YOUZAN_MALL_COUPON:
                $more += ['coupon_ids' => $item->more['coupon_ids']];
                break;
        }
        return new self([
            'deliver_time' => $_SERVER['REQUEST_TIME'],
            'status' => self::STATUS_PENDING,
            'benefit_id' => $item->id,
            'mobile' => $mobile_info->mobile_num,
            'region' => $mobile_info->region_num,
            'bili_uid' => $bili_uid,
            'maoer_uid' => $user_id,
            'more' => $more,
        ]);
    }

    public static function checkIsDelivered(BiliVipMaoerBenefitMenu $item, self $benefit)
    {
        return self::find()
            ->where(['benefit_id' => $item->id, 'status' => self::STATUS_SUCCESS])
            ->andWhere('deliver_time BETWEEN :start_time AND :end_time - 1', [
                ':start_time' => $benefit->bili_vip_period_start,
                ':end_time' => $benefit->bili_vip_period_end,
            ])->andWhere([
                'mobile' => $benefit->mobile,
                'region' => $benefit->region,
            ])->exists();
    }

    public function deliver(BiliVipMaoerBenefitMenu $benefit_item, ?callable $callback)
    {
        switch ($benefit_item->benefit_type) {
            case BiliVipMaoerBenefitMenu::BENEFIT_TYPE_YOUZAN_MALL_COUPON:
                $error_map = [];
                foreach ($benefit_item->more['coupon_ids'] as $coupon_id) {
                    $resp = Yii::$app->sso->takeYouzanCoupon($this->maoer_uid, $coupon_id);
                    if ($resp['code'] !== SSOClient::CODE_SUCCESS) {
                        throw new HttpException($resp['status'], $resp['info'], $resp['code']);
                    }
                    if (array_key_exists($this->maoer_uid, $resp['info']['user_error_map']) && $resp['info']['user_error_map'][$this->maoer_uid]) {
                        $error_map[$coupon_id] = $resp['info']['user_error_map'][$this->maoer_uid];
                    }
                }
                $error_msg = null;
                if ($error_map) {
                    $error_msg = Json::encode($error_map);
                }
                if (is_callable($callback)) {
                    $callback($this, $error_msg);
                }
                break;
            default:
                throw new Exception("未知的权益类型: {$benefit_item->benefit_type}");
        }
    }

    /**
     * @param int $user_id
     * @param int $benefit_type
     * @return array|self[]
     */
    public static function getPendingBenefits(int $user_id, int $benefit_type): array
    {
        if (!$user_id) {
            return [];
        }
        return self::find()
            ->alias('b')
            ->select('b.*')
            ->innerJoin(BiliVipMaoerBenefitMenu::tableName() . ' AS m', 'b.benefit_id = m.id')
            ->where([
                'b.maoer_uid' => $user_id,
                'b.status' => self::STATUS_PENDING,
                'm.benefit_type' => $benefit_type,
            ])->all();
    }

}
