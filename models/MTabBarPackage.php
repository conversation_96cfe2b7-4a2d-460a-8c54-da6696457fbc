<?php

namespace app\models;

use missevan\storage\StorageClient;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "m_tab_bar_package".
 *
 * @property integer $id 主键
 * @property integer $create_time 创建时间戳，单位：秒
 * @property integer $modified_time 最后更新时间戳，单位：秒
 * @property string $package 资源压缩包地址
 * @property integer $start_time 开始展示时间点。单位：秒
 * @property integer $end_time 结束展示时间点，为 0 时表示永久生效。单位：秒
 */
class MTabBarPackage extends ActiveRecord
{
    const END_TIME_FOREVER_EFFECTIVE = 0;

    public $package_url;  // 资源压缩包完整地址

    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_tab_bar_package';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['create_time', 'modified_time', 'start_time', 'end_time'], 'integer'],
            [['package'], 'string'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间戳',  // 单位：秒
            'modified_time' => '最后更新时间戳',  // 单位：秒
            'package' => '资源压缩包地址',
            'start_time' => '开始展示时间点',  // 单位：秒
            'end_time' => '结束展示时间点',  // 为 0 时表示永久生效。单位：秒
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->package) {
            $this->package_url = StorageClient::getFileUrl($this->package);
        }
    }

    /**
     * 获取生效中的应用底部图标
     *
     * @return array|null
     */
    public static function getTabBarPackage()
    {
        $now = $_SERVER['REQUEST_TIME'];
        $memcache = Yii::$app->memcache;
        if ($data = $memcache->get(KEY_TAB_BAR_PACKAGE)) {
            $tab_bar_package = Json::decode($data);
        } else {
            $tab_bar_package = self::find()
                ->where('start_time <= :now_time AND (end_time > :now_time OR end_time = :end_time)',
                    [':now_time' => $now, ':end_time' => self::END_TIME_FOREVER_EFFECTIVE])
                ->orderBy('start_time DESC')
                ->limit(1)
                ->one();
            $cache_duration = MIN_CACHE_DURATION;
            if ($tab_bar_package) {
                $tab_bar_package = $tab_bar_package->toArray();
                $cache_duration = FIVE_MINUTE;
                if ($tab_bar_package['end_time'] && $tab_bar_package['end_time'] < ($now + $cache_duration)) {
                    $cache_duration = max($tab_bar_package['end_time'] - $now, MIN_CACHE_DURATION);
                }
            }
            $memcache->set(KEY_TAB_BAR_PACKAGE, Json::encode($tab_bar_package), $cache_duration);
        }
        if (!$tab_bar_package ||
                ($tab_bar_package['end_time'] && $tab_bar_package['end_time'] <= $now)) {
            return null;
        }
        return [
            'id' => $tab_bar_package['id'],
            'expire_duration' => $tab_bar_package['end_time'] ?
                $tab_bar_package['end_time'] - $now : MTabBarPackage::END_TIME_FOREVER_EFFECTIVE,
        ];
    }
}
