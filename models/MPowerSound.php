<?php

namespace app\models;

use Exception;
use missevan\storage\StorageClient;
use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "m_power_sound".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 * @property int $sound_id 单音 ID
 * @property string $cv 声优
 * @property string $role_name 角色名
 * @property int $ip_id ip ID
 * @property string $cover 封面地址
 * @property string $icon 图标地址
 * @property string $intro 文字说明
 * @property int $sort_order 排序（从大到小）
 * @property int $archive 是否归档（1 为是，0 为否）
 * @property string $playurl 播放地址
 */
class MPowerSound extends ActiveRecord
{
    const ARCHIVE_ONLINE = 0;
    const ARCHIVE_ARCHIVED = 1;
    const DEFAULT_PAGE_SIZE = 1000;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_power_sound';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['sound_id', 'cv', 'role_name', 'ip_id', 'cover', 'intro', 'icon', 'create_time', 'modified_time', 'sort_order'], 'required'],
            [['sound_id', 'ip_id', 'archive', 'create_time', 'modified_time', 'sort_order'], 'integer'],
            [['cv', 'role_name'], 'string', 'max' => 50],
            [['cover', 'intro', 'icon'], 'string', 'max' => 255],
            [['playurl'], 'string', 'max' => 100],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'sound_id' => '单音 ID',
            'cv' => '声优',
            'role_name' => '角色名',
            'ip_id' => 'ip ID',
            'cover' => '封面地址',
            'intro' => '文字说明',
            'icon' => '图标地址',
            'archive' => '是否归档（1 为是，0 为否）',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'sort_order' => '排序（从大到小）',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    public function afterFind()
    {
        parent::afterFind();

        if ($this->cover) {
            $this->cover = StorageClient::getFileUrl($this->cover);
        }
        if ($this->icon) {
            $this->icon = StorageClient::getFileUrl($this->icon);
        }
        self::getPlayUrl($this);
    }

    /**
     * 启动音播放协议地址转化为完整播放地址
     * 如：upos://bucket/test.m4a => http://static.test.com/bucket/test.m4a
     *
     * @param $power_sound self|array $power_sound 启动音信息
     * @return void
     * @throws Exception 播放地址非法或 storage 配置错误时抛出异常
     */
    public static function getPlayUrl(&$power_sound)
    {
        if (isset($power_sound['playurl']) && $power_sound['playurl']) {
            $power_sound['playurl'] = Yii::$app->upos->getNoSignUrl($power_sound['playurl']);
        }
    }

    /**
     * 获取启动音
     *
     * @param int $page 当前页
     * @param int $page_size 每页显示条数
     * @param string $keyword 关键字
     * @return array
     */
    public static function getPowerSoundList(int $page = 1, int $page_size = self::DEFAULT_PAGE_SIZE, string $keyword = '')
    {
        $model = self::find()
            ->alias('t')
            ->leftJoin(MPowerSoundIp::tableName() . ' AS t1', 't.ip_id = t1.id')
            ->select([
                't.id',
                't.cv',
                't.role_name',
                't.cover',
                't.icon',
                't.intro',
                't.sound_id',
                't.sort_order AS sound_sort_order',
                't.playurl',
                't1.ip_name',
                't1.sort_order AS ip_sort_order',
                't1.attr AS ip_attr'
            ])->where(['t.archive' => MPowerSound::ARCHIVE_ONLINE, 't1.archive' => MPowerSoundIp::ARCHIVE_ONLINE]);
        if ($keyword !== '') {
            $model = $model->andWhere([
                'OR',
                [
                    'LIKE', 't.cv', $keyword
                ],
                [
                    'LIKE', 't.role_name', $keyword
                ],
                [
                    'LIKE', 't.intro', $keyword
                ],
                [
                    'LIKE', 't1.ip_name', $keyword
                ],
            ]);
        }

        $count = (int)$model->count();
        if (!$count) {
            return [[], $count];
        }

        // t1 为启动音 IP 表，t 为 启动音表，排序要求：先排序启动音 IP，再将启动音进行排序
        $sounds = $model->orderBy('t1.sort_order DESC, t.sort_order DESC')
            ->offset(($page - 1) * $page_size)
            ->limit($page_size)
            ->asArray()
            ->all();
        if (empty($sounds)) {
            return [[], $count];
        }

        $ip_list = [];
        $sound_ids = array_column($sounds, 'sound_id');
        // 查找符合显示条件的音频 ID
        $sound_ids = MSound::find()->select('id')
            ->where(['checked' => MSound::CHECKED_PASS, 'id' => $sound_ids])->column();
        if (empty($sound_ids)) {
            return [[], $count];
        }
        foreach ($sounds as $val) {
            if (!in_array($val['sound_id'], $sound_ids) || !$val['playurl']
                    || !($cover = StorageClient::getFileUrl($val['cover']))
                    || !($icon = StorageClient::getFileUrl($val['icon']))) {
                Yii::error("启动音（{$val['id']}）数据存在问题");
                continue;
            }
            self::getPlayUrl($val);
            $ip_list[] = [
                'id' => (int)$val['id'],
                'cv' => $val['cv'],
                'role_name' => $val['role_name'],
                'ip_name' => $val['ip_name'],
                'cover' => $cover,
                'icon' => $icon,
                'intro' => $val['intro'],
                'playurl' => $val['playurl'],
                'sound_sort_order' => (int)$val['sound_sort_order'],
                'ip_sort_order' => (int)$val['ip_sort_order'],
                'ip_attr' => (int)$val['ip_attr'],
            ];
        }
        return [$ip_list, $count];
    }

    /**
     * 根据启动音 ID 获取启动音信息
     *
     * @param int $id 启动音 ID
     * @return array 启动音信息
     * @throws HttpException
     */
    public static function getPowerSoundById(int $id)
    {
        $power_sound = self::find()
            ->select(['id', 'cv', 'role_name', 'cover', 'icon', 'intro', 'sound_id', 'playurl'])
            ->where(['id' => $id, 'archive' => MPowerSound::ARCHIVE_ONLINE])
            ->one();
        if (!$power_sound) {
            throw new HttpException(404, '该启动音不存在');
        }
        $sound_exists = MSound::find()
            ->where(['checked' => MSound::CHECKED_PASS, 'id' => $power_sound->sound_id])
            ->exists();
        if (!$sound_exists) {
            Yii::error("启动音（{$id}）对应的音频（{$power_sound->sound_id}）不存在或不可见");
            throw new HttpException(404, '该启动音对应的音频不存在');
        }
        return [
            'id' => $power_sound['id'],
            'cv' => $power_sound['cv'],
            'intro' => $power_sound['intro'],
            'role_name' => $power_sound['role_name'],
            'cover' => $power_sound['cover'],
            'icon' => $power_sound['icon'],
            'soundurl' => $power_sound['playurl'],
        ];
    }
}
