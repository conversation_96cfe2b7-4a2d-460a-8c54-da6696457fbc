<?php

namespace app\models;

use app\components\util\MUtils;
use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\db\Expression;
use yii\helpers\Json;
use yii\web\HttpException;

/**
 * This is the model class for table "m_like_sound".
 *
 * @property integer $id
 * @property integer $user_id
 * @property integer $sound_id
 * @property integer $ctime
 */
class MLikeSound extends ActiveRecord
{
    const MAX_LIKE_NUM = 1000;

    // 批量喜欢音频接口 type 参数值含义 0：取消喜欢音频；1：喜欢音频
    const TYPE_CANCEL_LIKE_SOUND = 0;
    const TYPE_LIKE_SOUND = 1;

    // 喜欢音频接口提示文案（从多个文案中随机选取一个）
    const LIKE_MSGS = [
        '你的喜欢会被更多人听见！',
        '我也喜欢你！',
        '太棒啦，已把声音推荐给更多人~',
    ];

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_like_sound';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'sound_id'], 'required'],
            [['user_id', 'sound_id', 'ctime'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'user_id' => '点赞者id',
            'sound_id' => '点赞的声音id',
            'ctime' => '点赞时间',  // 0 为没有设置时间时，用户点赞数据
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        if ($insert) {
            $this->ctime = $_SERVER['REQUEST_TIME'];
        }
        return true;
    }

    /**
     * 批量喜欢音频
     *
     * @param array $sound_ids 音频 IDs
     * @param int $user_id 用户 ID
     * @param int $type 操作类型 1：喜欢音频；0：取消喜欢音频
     * @throws HttpException
     * @return boolean
     */
    public static function batchLikeOrNot(array $sound_ids, int $user_id, int $type): bool
    {
        if ($user_id <= 0 || empty($sound_ids) || !MUtils2::isUintArr($sound_ids)) {
            throw new Exception('参数错误');
        }
        // 加锁
        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_SOUND_BATCH_LIKE, $user_id);
        if (!$redis->lock($lock, ONE_MINUTE)) {
            throw new HttpException(400, '操作过于频繁，请稍候再试');
        }
        $liked_sound_ids = MLikeSound::find()
            ->select('sound_id')
            ->where(['user_id' => $user_id, 'sound_id' => $sound_ids])
            ->column();
        $liked_sound_ids = array_map('intval', $liked_sound_ids);
        $transaction = null;
        try {
            switch ($type) {
                case self::TYPE_CANCEL_LIKE_SOUND:
                    // 取消喜欢
                    if (empty($liked_sound_ids)) {
                        // 所选音频都是未喜欢的状态，则直接返回 true
                        return true;
                    }
                    $transaction = Yii::$app->db->beginTransaction();
                    MLikeSound::deleteAll(['user_id' => $user_id, 'sound_id' => $sound_ids]);
                    // 音频的喜欢数冗余字段 -1
                    $uptimes_expression = new Expression('GREATEST(uptimes, 1) - 1');
                    MSound::updateAll(['uptimes' => $uptimes_expression], ['id' => $liked_sound_ids]);
                    // 用户喜欢的音频数冗余字段 - 喜欢的音频数量
                    $like_num = count($liked_sound_ids);
                    $like_num_expression = new Expression('GREATEST(likenum, :like_num) - :like_num',
                        [':like_num' => $like_num]);
                    Mowangskuser::updateAll(['likenum' => $like_num_expression], 'id = :user_id',
                        [':user_id' => $user_id]);
                    break;
                case self::TYPE_LIKE_SOUND:
                    // 添加到喜欢
                    $not_like_sound_ids = array_values(array_diff($sound_ids, $liked_sound_ids));
                    if (empty($not_like_sound_ids)) {
                        // 所选的音频都已添加到喜欢，则直接返回 true
                        return true;
                    }
                    $transaction = Yii::$app->db->beginTransaction();
                    self::batchInsertLikeSounds($not_like_sound_ids, $user_id);
                    // 被喜欢的音频，喜欢数冗余字段 +1
                    MSound::updateAllCounters(['uptimes' => 1], ['id' => $not_like_sound_ids]);
                    // 用户喜欢的音频数冗余字段 + 喜欢的音频数量
                    $like_num = count($not_like_sound_ids);
                    Mowangskuser::updateAllCounters(['likenum' => $like_num], 'id = :user_id',
                        [':user_id' => $user_id]);
                    break;
                default:
                    throw new Exception("type 参数错误: {$type}");
            }
            $transaction->commit();
        } catch (Exception $e) {
            if (!$transaction) {
                throw $e;
            }
            $transaction->rollBack();
            // 记录错误日志
            Yii::error('批量喜欢或取消喜欢音频失败，原因：' . $e->getMessage(), __METHOD__);
            return false;
        } finally {
            // 解锁
            $redis->unlock($lock);
        }
        return true;
    }

    /**
     * 批量写入数据
     *
     * @param array $sound_ids 音频 IDs
     * @param int $user_id 用户 ID
     * @return int 写入的音频数量
     * @throws \yii\db\Exception
     */
    public static function batchInsertLikeSounds(array $sound_ids, int $user_id)
    {
        if ($user_id <= 0 || empty($sound_ids) || !MUtils2::isUintArr($sound_ids)) {
            throw new Exception("参数错误，user_id: {$user_id}, array: " . Json::encode($sound_ids));
        }
        $insert_datas = [];
        foreach ($sound_ids as $sound_id) {
            $insert_datas[] = [$user_id, $sound_id, $_SERVER['REQUEST_TIME']];
        }
        return Yii::$app->db->createCommand()
            ->batchInsert(self::tableName(), ['user_id', 'sound_id', 'ctime'], $insert_datas)->execute();
    }

    /**
     * 获取个人主页收藏模块数据
     *
     * @param int $view_user_id 个人主页 UP 主 ID
     * @param int $user_id 当前用户 ID
     * @return array
     * @throws Exception
     */
    public static function getHomepageCollects(int $view_user_id, int $user_id)
    {
        if ($view_user_id <= 0) {
            throw new Exception('参数错误');
        }
        $up_user_info = Mowangskuser::find()->select('avatar, boardiconurl, icontype')
            ->where(['id' => $view_user_id])->one();
        $user_collects = [
            [
                'id' => 0,
                'title' => 'TA 喜欢的音频',
                // 他人视角不显示用户喜欢的音频数量
                'music_count' => 0,
                // 他人视角查看喜欢的音频，封面图显示被查看用户的头像
                'front_cover' => $up_user_info->iconurl ?? Yii::$app->params['defaultAvatarUrl'],
            ]
        ];
        $return = [
            'user_collects' => $user_collects,
            'elements_num' => 1
        ];
        if ($view_user_id === $user_id) {
            // UP 主视角可以查看喜欢的音频和音单信息
            $user_collects[0]['title'] = '我喜欢的音频';
            $user_collects[0]['music_count'] = (int)MLikeSound::find()->where(['user_id' => $view_user_id])->count();
            $latest_like_sound = MSound::find()->alias('s')
                ->select('s.cover_image')
                ->leftJoin(self::tableName() . ' AS l', 'l.sound_id = s.id')
                // UP 主视角，获取最新喜欢的审核通过的音频用于“我喜欢的音频”封面显示
                ->where(['l.user_id' => $view_user_id, 's.checked' => MSound::CHECKED_PASS])
                ->orderBy('l.id DESC')
                ->limit(1)
                ->one();
            if ($latest_like_sound) {
                // UP 主视角，我喜欢的音频封面显示最新喜欢的审核通过音频封面图
                $user_collects[0]['front_cover'] = $latest_like_sound->front_cover;
            }
            // 获取用户创建的音单
            $PAGE_SIZE = 2;
            $user_albums = MAlbum::getUserAlbum($view_user_id, $user_id, true, $PAGE_SIZE);
            $return['user_collects'] = array_merge($user_collects, $user_albums->Datas);
            $return['elements_num'] += $user_albums->pagination['count'];
        }
        return $return;
    }
}
