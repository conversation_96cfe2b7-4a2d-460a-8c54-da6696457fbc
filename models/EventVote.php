<?php
namespace app\models;

use app\components\util\MUtils;
use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "event_vote".
 *
 * @property integer $id
 * @property integer $event_id
 * @property integer $eid
 * @property integer $vote_num
 * @property integer $category
 */
class EventVote extends ActiveRecord
{
    // 资源类型 0：声音；1：图片；2：视频；3：特殊活动（如众筹活动）4：预约活动；6：投稿活动
    const CATEGORY_SOUND = 0;
    const CATEGORY_IMAGE = 1;
    const CATEGORY_VIDEO = 2;
    const CATEGORY_SPECIAL = 3;
    const CATEGORY_SUBSCRIBE = 4;
    const CATEGORY_UPLOAD = 6;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'event_vote';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['event_id', 'eid', 'category'], 'required'],
            [['event_id', 'eid', 'vote_num', 'category'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'event_id' => '活动 ID',
            'eid' => '资源 ID',
            'vote_num' => '投票数',
            'category' => '资源类型',  // 0 单音、1 图片
        ];
    }

    public static function getWorks($event_id, $order = 0, $page_size = 20)
    {
        $event = MEvent::findOne($event_id);

        if (!$event)
            throw new HttpException(404, '活动不存在');

        $alias = 't';
        $condition = 'checked = 1 AND event_id = ' . $event_id;
        $join = 'event_vote';
        $on = 't.id = eid';
        if ($order) {
            $order = ['vote_num' => SORT_DESC];
        } else {
            $order = ['id' => SORT_DESC];
        }

        if (!$event->type) {//sound
            $select = 't.id, t.soundstr, t.cover_image, t.user_id, t.username, t.soundurl_64, vote_num, duration';
            $query = MSound::find()->alias($alias)->select($select)
                ->where($condition)->leftJoin($join, $on)->orderBy($order);
            $return_model = MUtils::getPaginationModels($query, $page_size);

            MEvent::GetPlayerAvatar($return_model->Datas);
        } else {//image
            $select = 't.id, t.title, t.save_name, t.user_id, t.username, vote_num';
            $query = MImage::find()->alias($alias)->select($select)
                ->where($condition)->leftJoin($join, $on)->orderBy($order);
            $return_model = MUtils::getPaginationModels($query, $page_size);
        }

        self::DoYouVote($return_model->Datas, $event);

        return $return_model;
    }

    public static function DoYouVote(&$models, $event)
    {
        $user_id = Yii::$app->user->id;
        $time = time();
        if (!$user_id || !$models ||
                ($time < $event->vote_start_time) || ($time >= $event->vote_end_time)
        ) {
            return;
        }

        $eids = array_column($models, 'id');
        $beginToday = mktime(0, 0, 0, date('m'), date('d'), date('Y'));
        $endToday = mktime(0, 0, 0, date('m'), date('d') + 1, date('Y')) - 1;
        $vote_ids = EventVoteDetail::find()->select('eid')
            ->where(['event_id' => $event->id, 'user_id' => $user_id, 'eid' => $eids])
            ->andWhere(['between', 'time', $beginToday, $endToday])->column();
        foreach ($models as &$model) {
            $model->vote = in_array($model->id, $vote_ids) ? 0 : 1;
        }
    }
}
