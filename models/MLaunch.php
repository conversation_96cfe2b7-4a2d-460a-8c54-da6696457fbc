<?php

namespace app\models;

use app\components\util\Equipment;
use app\components\util\MUtils;
use missevan\storage\StorageClient;
use missevan\util\MUtils as MUtils2;
use yii\db\Exception;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "m_launch".
 *
 * @property string $id
 * @property string $element_id
 * @property string $url
 * @property integer $element_type
 * @property integer $status
 * @property string $start_time
 * @property string $end_time
 * @property string $redirect_url
 * @property string $label_str
 * @property integer $attr
 * @property string $message
 * @property string $create_time
 * @property string $modified_time
 */
class MLaunch extends ActiveRecord
{
    // 默认启动图（仅用作客户端判断，实际文件是否存在不影响）
    const DEFAULT_LAUNCH_PIC = 'oss://mimages/201703/28/3ab641f736366f4306b9bf73220e436c141357.png';

    // 默认启动音
    const DEFAULT_LAUNCH_SOUND = 'oss://MP3/201605/14/297966dceaf85625530bad6e682dda47120400.mp3';

    // 启动图
    const ELEMENT_TYPE_LAUNCH_PIC = 1;
    // 启动音
    const ELEMENT_TYPE_LAUNCH_SOUND = 2;
    // 猜你喜欢广告位
    const ELEMENT_TYPE_YOU_MIGHT_LIKE_ADV = 3;

    // 已发布
    const STATUS_PUBLISHED = 1;

    // 不可跳过闪屏广告
    const ATTR_NO_SKIP = 0b1;
    // 视频
    const ATTR_VIDEO = 0b10;
    // 部分地区展示受限
    const ATTR_LIMIT = 0b100;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_launch';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['element_id', 'element_type', 'status', 'start_time', 'end_time', 'attr', 'create_time', 'modified_time'],
                'integer'],
            [['element_type', 'start_time', 'end_time'], 'required'],
            [['url', 'redirect_url'], 'string', 'max' => 255],
            [['label_str', 'message'], 'string', 'max' => 100],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'element_id' => '元素 ID',  // 元素类型是猜你喜欢广告位这里是音频 ID
            'url' => '链接地址',
            'element_type' => '元素类型',  // 1：启动图；2：启动音；3：猜你喜欢广告位
            'status' => '状态',  // -1：已删除；0：未发布；1：已发布；
            'start_time' => '上线时间',
            'end_time' => '下线时间',
            'redirect_url' => '跳转地址',
            'label_str' => '认证标志',
            'attr' => '闪屏属性',
            'message' => '跳转按钮文案',
            'create_time' => '创建时间',
            'modified_time' => '最后编辑时间',
        ];
    }

    /**
     * 获取启动图和启动音
     *
     * @return array 启动图和启动音数据
     */
    public static function getLaunchPicAndSound()
    {
        if (Equipment::isAppOlderThan('4.6.6', '5.5.5')) {
            // WORKAROUND: iOS < 4.6.6 或 Android < 5.5.5 版本时（即不支持闪屏版本）
            // 返回默认启动音图
            return [
                'pic_url' => self::parsePicUrl(self::DEFAULT_LAUNCH_PIC),
                'sound_url' => '',
            ];
        }

        $splashs = self::getLaunch(self::ELEMENT_TYPE_LAUNCH_PIC);
        $splashs = Json::decode($splashs);
        $pics_data = [];
        $isLimitLocation = MUtils::isLimitLocation();
        foreach ($splashs as $key => $splash) {
            $pics = [];
            foreach ($splash as $pic) {
                if ($pic['limit'] && $isLimitLocation) {
                    // 在特定地区限制不下发闪屏
                    continue;
                }
                $pics[] = $pic;
            }
            $pics_data[$key] = $pics;
        }
        if (Equipment::isAppVersion(Equipment::Android, '6.0.1')
                && Yii::$app->redis->sIsMember(KEY_SPECIAL_LAUNCH_BUVID_LIST, Yii::$app->equip->getBuvid())) {
            // WORKAROUND: Android = 6.0.1 版本特定的 buvid 用户下发默认的启动音
            $sound_url = self::DEFAULT_LAUNCH_SOUND;
        } else {
            $sound_url = self::getLaunch(self::ELEMENT_TYPE_LAUNCH_SOUND);
        }
        $sound_url = StorageClient::getFileUrl($sound_url);

        if (Equipment::isAppOlderThan('4.7.1', '5.6.0')) {
            // WORKAROUND: iOS < 4.7.1 或 Android < 5.6.0 版本时， 返回非二维数组的 splash 结构
            $pics_data = array_filter(array_map(function ($value) {
                // 过滤闪屏类型为视频的闪屏，iOS < 4.7.1 或 Android < 5.6.0 版本不支持闪屏类型为视频的闪屏
                if ($value['launch_type'] === 0) {
                    return $value;
                }
            }, $pics_data['splash']));
            if (empty($pics_data)) {
                // 获取默认数据
                $pics_data = Json::decode(self::getLaunchData([], self::ELEMENT_TYPE_LAUNCH_PIC));
            }
            $pic_data = current($pics_data);
            return [
                'splash' => self::getLaunchPicAndSoundData($pic_data),
                'sound_url' => $sound_url
            ];
        }

        return [
            'splash' => array_map(function ($value) {
                return self::getLaunchPicAndSoundData($value);
            }, $pics_data['splash']),
            'splash_ready' => array_map(function ($value) {
                return [
                    'pic_url' => self::parsePicUrl($value['pic_url']),
                    'launch_type' => $value['launch_type'],
                ];
            }, $pics_data['splash_ready']),
            'sound_url' => $sound_url
        ];
    }

    /**
     * @param array $value 闪屏数据
     * @return array
     */
    public static function getLaunchPicAndSoundData(array $value)
    {
        if (!$value['pic_url'] && Equipment::isAppOlderThan(null, '5.7.0')) {
            // WORKAROUND: Android 5.7.0 之前版本默认启动图需要返回固定地址
            $value['pic_url'] = self::DEFAULT_LAUNCH_PIC;
        }
        $data = [
            'id' => $value['id'],
            'pic_url' => self::parsePicUrl($value['pic_url']),
            'label' => $value['label'],
            'no_skip' => $value['no_skip'],
            'launch_type' => $value['launch_type'],
        ];
        if ($value['redirect_url']) {
            $data['redirect_url'] = MUtils::getUsableAppLink($value['redirect_url']);
        }
        if ($value['message']) {
            $data['message'] = $value['message'];
        }
        return $data;
    }

    /**
     * 解析 pic url, 支持 upos:// 无鉴权资源
     *
     * @param string $url 资源地址
     * @return string 完整资源地址
     */
    public static function parsePicUrl($url): string
    {
        if (!$url) {
            return '';
        }
        if (MUtils::isUposUrl($url)) {
            return Yii::$app->upos->getNoSignUrl($url);
        }
        return StorageClient::getFileUrl($url);
    }

    /**
     * 从缓存中获取启动图、启动音、猜你喜欢广告位
     *
     * @param integer $element_type 元素类型 1：启动图；2：启动音；3：猜你喜欢广告位
     * @return integer|string
     */
    public static function getLaunch(int $element_type)
    {
        $memcache = Yii::$app->memcache;

        $key = self::getKeyByElementType($element_type);
        if (($data = $memcache->get($key)) === false) {
            $callable = [new self(), 'getLaunchInfo'];
            [$data, $duration] = $callable($element_type);
            // 设置缓存和过期时间，默认过期时间加 1s，错开时间上线
            $memcache->set($key, $data, $duration + 1);
        }
        return $data;
    }

    /**
     * 获取启动图、启动音、猜你喜欢广告位数据
     *
     * @param integer $element_type 元素类型 1：启动图；2：启动音；3：猜你喜欢广告位
     * @return array 值和过期时间组成的数据
     */
    public static function getLaunchInfo(int $element_type)
    {
        // 如果有定时启动图（音）（猜你喜欢广告位），则使用它生成缓存
        $time = $_SERVER['REQUEST_TIME'];
        $query = self::find()
            ->select('id, element_id, url, redirect_url, end_time, label_str, attr, message')
            ->where('element_type = :element_type AND status = :status AND :now_time BETWEEN start_time AND end_time', [
                ':element_type' => $element_type,
                ':status' => self::STATUS_PUBLISHED,
                ':now_time' => $time,
            ])->orderBy('id DESC');
        // 启动图支持同一时间段内有多个存在，启动音、猜你喜欢广告位在同一时间段内只能有一个
        if ($element_type === self::ELEMENT_TYPE_LAUNCH_PIC) {
            $launch = $query->all();
        } else {
            $launch = $query->limit(1)->all();
        }
        $data = self::getLaunchData($launch, $element_type);
        $duration = self::getDuration($launch, $element_type, $time);

        if ($element_type === self::ELEMENT_TYPE_LAUNCH_PIC) {
            $data = Json::encode([
                'splash' => Json::decode($data),
                'splash_ready' => self::getSplashReady($time),  // 待上线闪屏
            ]);
        }
        return [$data, $duration];
    }

    /**
     * 获取待上线闪屏图
     *
     * @param int $time
     * @return array
     */
    public static function getSplashReady(int $time)
    {
        $splash_ready = self::find()
            ->select('url, attr')
            ->where(['element_type' => self::ELEMENT_TYPE_LAUNCH_PIC, 'status' => self::STATUS_PUBLISHED])
            ->andWhere('start_time > :from_time AND start_time <= :to_time', [
                ':from_time' => $time,
                ':to_time' => $time + ONE_DAY,
            ])->orderBy('id DESC')->all();
        if (!$splash_ready) {
            return [];
        }
        return array_map(function ($value) {
            return [
                'pic_url' => $value->url,
                'launch_type' => ($value->attr & self::ATTR_VIDEO) ? 1 : 0,
                'limit' => ($value->attr & self::ATTR_LIMIT) ? 1 : 0,
            ];
        }, $splash_ready);
    }

    /**
     * 获取缓存时长（当元素为空时，返回缓存时长为最近要上线的元素时间减去当前时间，
     * 不为空时，返回缓存时长为此次元素的结束时间和最近要上线的元素的开始时间的最小值减去当前时间）
     *
     * @param array $launch 启动图、启动音、猜你喜欢广告位数据
     * @param int $element_type 元素类型 1：启动图；2：启动音；3：猜你喜欢广告位
     * @param int $time 当前时间
     * @return int
     */
    public static function getDuration(array $launch, int $element_type, int $time)
    {
        // 获取最近要下线的元素时间
        $end_times = array_column($launch, 'end_time');
        if (!empty($end_times)) {
            $next_start_time = $min_end_time = min($end_times);
        }
        // 获取最近要上线的元素时间
        $start_time = self::getNextStartTime($element_type, $time);
        if ($start_time) {
            $next_start_time = $start_time;
        }
        if (!empty($end_times)) {
            $duration = (int)(min($next_start_time, $min_end_time)) - $time;
        } else {
            $duration = $start_time - $time;
        }
        if ($duration <= 0) {
            // 避免设置负数值，负数值 MemCache Set 会转换成永久不过期
            // 设置 1 秒的过期时间，避免在缓存失效的那个时间点产生调用峰值的问题
            $duration = 1;
        } elseif ($duration > TEN_MINUTE) {
            $duration = TEN_MINUTE;
        }
        return $duration;
    }

    /**
     * 获取最近要上线的启动图、启动音、猜你喜欢广告位时间
     *
     * @param int $element_type 元素类型 1：启动图；2：启动音；3：猜你喜欢广告位
     * @param int $time 当前时间
     * @return int
     */
    public static function getNextStartTime(int $element_type, int $time)
    {
        return (int)self::find()
            ->select(['start_time'])
            ->where(['element_type' => $element_type, 'status' => self::STATUS_PUBLISHED])
            ->andWhere('start_time > :time', [':time' => $time])
            ->orderBy(['start_time' => SORT_ASC, 'id' => SORT_ASC])
            ->limit(1)
            ->scalar();
    }

    /**
     * 获取启动图、启动音、猜你喜欢广告位数据（当为启动图时返回 json 字符串，当为启动音或猜你喜欢广告位时返回字符串或整型）
     *
     * @param array $launch 启动图、启动音、猜你喜欢广告位数据
     * @param int $element_type 元素类型 1：启动图；2：启动音；3：猜你喜欢广告位
     * @return int|string
     * @throws Exception
     */
    public static function getLaunchData(array $launch, int $element_type)
    {
        if ($element_type === self::ELEMENT_TYPE_LAUNCH_PIC) {
            if (empty($launch)) {
                return Json::encode([
                    [
                        'id' => 0,
                        'pic_url' => self::getDefaultLaunch(self::ELEMENT_TYPE_LAUNCH_PIC),
                        'redirect_url' => '',
                        'label' => '',
                        'no_skip' => 0,
                        'launch_type' => 0,
                        'limit' => 0,
                        'message' => '',
                    ]
                ]);
            }
            $data = array_map(function ($value) {
                return [
                    'id' => (int)$value->id,
                    'pic_url' => $value->url,
                    'redirect_url' => $value->redirect_url,
                    'label' => $value->label_str,
                    'no_skip' => ($value->attr & self::ATTR_NO_SKIP) ? 1 : 0,
                    'launch_type' => ($value->attr & self::ATTR_VIDEO) ? 1 : 0,
                    'limit' => ($value->attr & self::ATTR_LIMIT) ? 1 : 0,
                    'message' => $value->message,
                ];
            }, $launch);
            $data = Json::encode($data);
        } elseif (!empty($launch)) {
            $data = $element_type === self::ELEMENT_TYPE_YOU_MIGHT_LIKE_ADV
                ? (int)(current($launch))->element_id
                : (current($launch))->url;
        } else {
            $data = self::getDefaultLaunch($element_type);
        }
        return $data;
    }

    /**
     * 获取默认启动图、启动音、猜你喜欢广告位的值
     *
     * @param integer $element_type 元素类型 1：启动图；2：启动音；3：猜你喜欢广告位
     * @return string|integer 默认值
     * @throws Exception
     */
    private static function getDefaultLaunch(int $element_type)
    {
        switch ($element_type)
        {
            case self::ELEMENT_TYPE_LAUNCH_PIC:
                // 返回空字符串时客户端会使用本地默认启动图
                $default_launch = '';
                break;
            case self::ELEMENT_TYPE_LAUNCH_SOUND:
                // 返回空字符串时客户端会使用本地默认启动音
                $default_launch = '';
                break;
            case self::ELEMENT_TYPE_YOU_MIGHT_LIKE_ADV:
                // 当元素类型是猜你喜欢广告位，默认值是 0，表示无广告位
                $default_launch = 0;
                break;
            default:
                throw new Exception('参数错误');
        }
        return $default_launch;
    }

    /**
     * 根据元素类型获取 key
     *
     * @param integer $element_type 元素类型 1：启动图；2：启动音；3：猜你喜欢广告位
     * @return string 获取的 key
     * @throws Exception
     */
    private static function getKeyByElementType(int $element_type)
    {
        switch ($element_type)
        {
            case self::ELEMENT_TYPE_LAUNCH_PIC:
                $key = KEY_APP_LAUNCH_SPLASH;
                break;
            case self::ELEMENT_TYPE_LAUNCH_SOUND:
                $key = KEY_APP_LAUNCH_SOUND;
                break;
            case self::ELEMENT_TYPE_YOU_MIGHT_LIKE_ADV:
                $key = KEY_APP_YOU_MIGHT_LIKE_ADV;
                break;
            default:
                throw new Exception('参数错误');
        }
        return $key;
    }
}
