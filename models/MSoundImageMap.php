<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "m_sound_image_map".
 *
 * @property integer $id
 * @property integer $sound_id
 * @property integer $image_id
 * @property integer $user_id
 * @property string $stime
 * @property integer $size
 * @property integer $color
 * @property integer $mode
 * @property integer $date
 * @property integer $pool
 */
class MSoundImageMap extends ActiveRecord
{
    public $img_url;
    public $img_width;
    public $img_height;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_sound_image_map';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['sound_id', 'image_id', 'user_id', 'stime', 'date'], 'required'],
            [['sound_id', 'image_id', 'user_id', 'size', 'color', 'mode', 'date', 'pool'], 'integer'],
            [['stime'], 'string', 'max' => 11],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'sound_id' => 'Sound ID',
            'image_id' => 'Image ID',
            'user_id' => 'User ID',
            'stime' => 'Stime',
            'size' => 'Size',
            'color' => 'Color',
            'mode' => 'Mode',
            'date' => 'Date',
            'pool' => 'Pool',
        ];
    }

    public static function getSoundPics($sound_id)
    {
        $pics = [];
        $sound_images =  MSoundImageMap::find()
            ->alias('t')
            ->select('t.stime, t.date, t1.save_name AS img_url,
                t1.width AS img_width, t1.height AS img_height, t1.file_size AS size')
            ->leftJoin(MImage::tableName() . ' AS t1', 't.image_id = t1.id')
            ->where('t.sound_id = :sound_id', [':sound_id' => $sound_id])->all();
        if (!empty($sound_images)) {
            foreach ($sound_images as $sound_image) {
                $sound_image['img_url'] = Yii::$app->params['mimagesbigUrl'] . $sound_image['img_url'];
                // 文档：https://info.missevan.com/pages/viewpage.action?pageId=90788778
                if ($sound_image['img_width'] > 2000 || $sound_image['img_height'] > 2000) {
                    $sound_image['img_url'] .= '?x-oss-process=image/resize,l_2000/format,webp';
                }
                $pics[(int)($sound_image->stime * 1000)] = $sound_image;
            }
            ksort($pics);
        }
        return array_values($pics);
    }
}
