<?php

namespace app\models;

use missevan\storage\StorageClient;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "m_tab".
 *
 * @property int $id
 * @property string $title Tab 名称
 * @property string $url 链接
 * @property string $cover Tab 花体字图片
 * @property int $sort 排序
 * @property int $active 默认选中 0：否；1：是
 * @property int $archive 是否为历史归档 0：否；1：是（归档）
 * @property int $position Tab 位置 1：App 首页顶部 Tab
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 * @property array $more 扩展信息
 */
class MTab extends ActiveRecord
{

    // 在线的推荐位
    const ARCHIVE_ONLINE = 0;
    // 历史归档的推荐位
    const ARCHIVE_HISTORY = 1;
    // 默认选中 Tab
    const INACTIVE = 0;
    const ACTIVE = 1;
    // Tab 位置
    const POSITION_APP_HOMEPAGE_TOP = 1;
    // Tab ID 1：推荐
    const TAB_ID_RECOMMEND = 1;
    // Tab ID 3：直播
    const TAB_ID_LIVE = 3;
    // 默认显示直播 Tab 的 App 渠道名
    const ACTIVE_LIVE_TAB_CHANNEL = 'missevan_yunyouxi';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_tab';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title', 'url', 'create_time', 'modified_time'], 'required'],
            [['sort', 'active', 'archive', 'create_time', 'modified_time'], 'integer'],
            [['title'], 'string', 'max' => 20],
            [['url', 'cover'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'title' => 'Tab 名称',
            'url' => '链接',
            'cover' => 'Tab 花体字图片',
            'sort' => '排序',
            'active' => '默认选中',  //  0：否；1：是
            'archive' => '是否为历史归档',  // 0 ：否， 1 ：是（被删去的）
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'more' => '扩展信息',
        ];
    }

    /**
     * 获取首页 Tabs
     *
     * @return array 例：['tabs' => ['id' => 3, 'title' => '直播', 'url' => 'missevan://live']]
     * @throws \Exception
     */
    public static function getHomepageTabs()
    {
        return MUtils2::getOrSetDistrubutedCache(KEY_SITE_TAB_LIST, function () {
            // 缓存中没有数据则从表中获取
            // 该缓存会在 Tab 管理后台（App -> 首页管理 -> 首页 Tabs）添加，编辑，删除操作后被删除
            $info = self::find()
                ->select('id, title, url, active, more')
                ->where('archive = :archive AND position = :position', [
                    ':archive' => self::ARCHIVE_ONLINE,
                    ':position' => self::POSITION_APP_HOMEPAGE_TOP,
                ])
                ->orderBy('sort ASC')->asArray()->all();
            if (empty($info)) {
                Yii::error('App 首页 Tab 数据异常');
                return ['tabs' => []];
            }
            $tabs = array_map(function ($item) {
                $item['id'] = (int)$item['id'];
                $item['active'] = (int)$item['active'];
                if (self::INACTIVE === $item['active']) {
                    // 默认未选中的 Tab，不返回 active 字段
                    unset($item['active']);
                }
                if ($item['more']) {
                    $more = Json::decode($item['more']);
                    if (isset($more['icon']) && $more['icon']) {
                        $item['icon'] = StorageClient::getFileUrl($more['icon']);
                    }
                    if (isset($more['dark_icon']) && $more['dark_icon']) {
                        $item['dark_icon'] = StorageClient::getFileUrl($more['dark_icon']);
                    }
                    if (isset($more['page_mark']) && $more['page_mark']) {
                        $item['page_mark'] = $more['page_mark'];
                    }
                }
                unset($item['more']);
                return $item;
            }, $info);
            $tab_list = ['tabs' => $tabs];
            return $tab_list;
        }, ONE_DAY);
    }
}
