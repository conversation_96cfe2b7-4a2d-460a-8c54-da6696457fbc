<?php

namespace app\models;

use app\components\util\Go;
use missevan\util\MUtils as MUtils2;
use missevan\util\HttpExceptionI18n;
use missevan\util\I18nMessage;
use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "reward_message".
 *
 * @property int $id 主键
 * @property int $drama_id 剧集 ID
 * @property int $user_id 用户 ID
 * @property int $transaction_id 交易记录 ID
 * @property string $message 打赏留言
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 */
class RewardMessage extends ActiveRecord
{
    // 最大留言长度
    const MAX_MESSAGE_LONG = 40;
    const MAX_MESSAGE_LONG_MIMI = 30;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'reward_message';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'drama_id', 'transaction_id'], 'integer'],
            [['message'], 'required'],
            ['message', 'checkMessage'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'drama_id' => '剧集 ID',
            'user_id' => '用户 ID',
            'transaction_id' => '交易记录 ID',
            'message' => '打赏留言',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    public static function getDefaultMessage()
    {
        return I18nMessage::success('Reward with no message');
    }

    /**
     * 验证留言是否符合规则
     *
     * @param string $attribute 验证的属性
     * @param mixed[] $params 其他自定义参数
     */
    public function checkMessage($attribute)
    {
        if (!$this->$attribute) {
            $this->addError($attribute, I18nMessage::error('Reward message content cannot only contain emoji'));
            return;
        }
        if (Yii::$app->equip->isFromMiMiApp()) {
            $len = mb_strlen($this->$attribute);
            if ($len > self::MAX_MESSAGE_LONG_MIMI) {
                $this->addError($attribute, I18nMessage::error(['Reward message length can only contain up to {num} characters', ['num' => self::MAX_MESSAGE_LONG_MIMI]]));
                return;
            }
        } else {
            $len = 0.5 * (strlen($this->$attribute) + mb_strlen($this->$attribute));
            if ($len > self::MAX_MESSAGE_LONG) {
                $this->addError($attribute, I18nMessage::error(['Reward message length can only contain up to {num1} characters or {num2} letters', ['num1' => 20, 'num2' => 40]]));
                return;
            }
        }
        // 检测留言违规情况
        $result = Yii::$app->go->checkText($this->$attribute, Go::SCENE_USER_INFO, true);
        if ($result && !current($result)['pass']) {
            $this->addError($attribute, I18nMessage::error('Reward message content should not contain sensitive words'));
        }
    }

    /**
     * 格式化留言
     * 去掉留言文本中的 emoji 表情，将换行符转换为一个空格
     *
     * @param $message
     */
    static public function formatMessage(string &$message)
    {
        if ($message) {
            // 换行符替换为一个空格，去掉留言左右边界 空格
            $message = trim(str_replace(["\r", "\n"], ' ', $message));
            // 去掉 emoji 表情
            $message = MUtils2::removeEmoji($message);
            // 若为纯 emoji 则进行抛出提示，避免在 save 的时候 required rule 的检验使得提示与预期不符
            if ($message === '') {
                throw new HttpExceptionI18n(403, 'Reward message content cannot only contain emoji');
            }
        }
    }

    /**
     * 添加剧集打赏留言
     *
     * @param int $drama_id 剧集 ID
     * @param int $transaction_id 交易记录 ID
     * @param int $user_id 用户 ID
     * @param string $message 留言信息
     * @throws HttpException 用户还未打赏或已经留言时抛出异常
     */
    public static function AddMessage(int $drama_id, int $transaction_id, int $user_id, string $message)
    {
        $reward_exists = TransactionLog::find()
            ->where('id = :id AND from_id = :from_id AND gift_id = :gift_id AND type = :type AND status = :status', [
                ':id' => $transaction_id,
                ':from_id' => $user_id,
                ':gift_id' => $drama_id,
                ':type' => TransactionLog::TYPE_REWARD,
                ':status' => TransactionLog::STATUS_SUCCESS
            ])
            ->exists();
        if (!$reward_exists) {
            throw new HttpExceptionI18n(403, "Can't send reward message before rewarding drama");
        }
        $message_exists = self::find()
            ->where('transaction_id = :transaction_id', [':transaction_id' => $transaction_id])
            ->exists();
        if ($message_exists) {
            throw new HttpExceptionI18n(403, 'Reward message has been sent for this reward');
        }
        // 规范留言的格式
        self::formatMessage($message);
        $reward_message = new self();
        $reward_message->drama_id = $drama_id;
        $reward_message->transaction_id = $transaction_id;
        $reward_message->user_id = $user_id;
        $reward_message->message = $message;
        if (!$reward_message->save()) {
            throw new HttpException(400, MUtils2::getFirstError($reward_message) ?: I18nMessage::error('Reward message failed to send'));
        }
    }
}
