<?php

namespace app\models;

class ReturnModel implements \JsonSerializable
{
    public $Datas = [];
    public $pagination;
    public $has_more;

    public function jsonSerialize()
    {
        $return = new \stdClass();
        $return->Datas = $this->Datas;
        $return->pagination = $this->pagination;
        if ($this->has_more !== null) {
            $return->has_more = $this->has_more;
        }
        return $return;
    }

    public function __construct($datas = [], $pagination = null)
    {
        $this->Datas = $datas;
        $this->pagination = $pagination;
    }

    /**
     * 获取空的分页数据
     * @param integer $page 页数
     * @param integer $page_size 每页个数
     * @return ReturnModel
     */
    public static function empty($page, $page_size)
    {
        $model = new self();
        $model->pagination = PaginationParams::empty($page, $page_size);
        return $model;
    }

    /**
     * 获取分页数据
     * @param array $datas 呈现的数据
     * @param integer $total_count 内容总数
     * @param integer $page 页数
     * @param integer $page_size 每页个数
     * @return ReturnModel
     */
    public static function getPaginationData($datas, $total_count, $page, $page_size)
    {
        $model = new self();
        $max_page = 0;
        if ($page_size) {
            $model->Datas = $datas;
            $max_page = ceil($total_count / $page_size);
        }
        $model->pagination = [
            'p' => $page,
            'maxpage' => $max_page,
            'count' => $total_count,
            'pagesize' => $page_size,
        ];
        return $model;
    }
}
