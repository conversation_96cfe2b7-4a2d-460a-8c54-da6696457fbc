<?php

namespace app\models;

use app\components\util\Equipment;
use Yii;
use yii\db\Expression;

/**
 * This is the model class for table "persona".
 *
 * @property string $id
 * @property string $equip_id
 * @property string $buvid
 * @property integer $persona
 * @property integer $user_id
 * @property integer $create_time
 * @property integer $modified_time
 * @property string $favor_tags
 */
class Persona extends ActiveRecord
{
    // 用户画像（用于 APP 及 Web 首页显示）
    // NOTICE: 女性画像 ID 需要为奇数
    const TYPE_GENERAL = 1;
    const TYPE_BOY = 2;
    const TYPE_GIRL = 3;
    const TYPE_FUJOSHI = 7;  // 腐女
    const TYPE_OTOME = 9;  // 乙女
    const TYPE_MANUAL_GIRL = 11;  // 人工维护女性画像
    const TYPE_MANUAL_BOY = 12;  // 人工维护男性画像

    // 剧集分区首页模块（用于 APP 分区首页）
    const TYPE_DRAMA_HOMEPAGE = 5;

    // 字段 persona 第 1~8 位存模块画像，第 9~16 位存猜你喜欢音推荐策略
    const PERSONA_MODULE_MASK = 0x00ff;
    const STRATEGY_LIKE_SOUNDS_MASK = 0xff00;
    const PERSONA_DEFAULT_MASK = 0xffff;

    // 字段 persona 为“猜你喜欢音"推荐策略二时进行位运算标识
    const RECOMMEND_SOUND_STRATEGY_2 = 0x200;
    // 字段 persona 为“猜你喜欢音"推荐策略二时进行位运算标识
    const RECOMMEND_SOUND_STRATEGY_3 = 0x300;

    // 用户行为可获得的画像分数
    const POINT_PLAY = 1;  // 收听音频
    const POINT_LIKE_SOUND = 5;  // 点赞音频
    const POINT_SUBSCRIBE_DRAMA = 20;  // 追剧
    const POINT_AD_TRACK = 100;  // 来源广告
    // 为乙女画像时的最低分
    const OTOME_MIN_POINT = 5;

    const USER_PERSONAS = [
        self::TYPE_GENERAL,
        self::TYPE_BOY,
        self::TYPE_GIRL,
        self::TYPE_FUJOSHI,
        self::TYPE_OTOME,
    ];

    public $sort;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'persona';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['persona', 'user_id', 'create_time', 'modified_time'], 'integer'],
            [['equip_id'], 'string', 'length' => 36],
            [['favor_tags'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'equip_id' => '设备 ID',
            'persona' => '画像 ID',
            'user_id' => '用户 ID',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'favor_tags' => '用户选择的偏好标签',
            'buvid' => 'BUVID'
        ];
    }

    // 获取用户画像 ID
    public static function getPersonaType(int $type = self::PERSONA_DEFAULT_MASK)
    {
        $user_id = (int)Yii::$app->user->id;
        $equip_id = Yii::$app->equip->getEquipId();
        $buvid = Yii::$app->equip->getBuvid();
        $persona = Yii::$app->serviceRpc->getPersona($equip_id, $buvid, $user_id);
        // 默认使用女用户画像（与客户端默认画像保持一致）
        $_persona = self::TYPE_GIRL;
        if (isset($persona['persona']) && $persona['persona'] !== 0) {
            $_persona = $persona['persona'];
        }
        // Android 云游戏渠道包默认使用女用户画像
        if (Yii::$app->equip->isAndroid() && Equipment::isFromYunYouXiChannel()) {
            $_persona = self::TYPE_GIRL;
        }
        return $_persona & $type;
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 获取画像下“猜你喜欢”音频
     *
     * @param int $persona_id 画像 ID
     * @param bool $check_length 是否检查获得的音频长度
     * @return MSound[] 音频信息组成的数组
     */
    public static function getLikeSounds(int $persona_id, bool $check_length = true): array
    {
        // 获取后台设置的“猜你喜欢”音单中的音频 ID
        $elem_group = MPersonaCollection::getFeed($persona_id);
        if (!$elem_group || !key_exists(MPersonaCollection::TYPE_ALBUM, $elem_group)) {
            Yii::error("画像（{$persona_id}）未设置猜你喜欢音频的音单", __METHOD__);
            return [];
        }
        $albums = $elem_group[MPersonaCollection::TYPE_ALBUM];
        $album_pace = array_column($albums, 'pace', 'elem_id');
        $redis = Yii::$app->redis;
        $like_sound_ids = [];
        // 获取的音频数量
        $need_sounds_length = 0;
        foreach ($album_pace as $album_id => $pace) {
            $need_sounds_length += $pace;
            $key = $redis->generateKey(KEY_NOOB_LIKE_SOUND_IDS, $album_id);
            if (!$sound_ids = $redis->sRandMember($key, $pace)) {
                $album_sound_ids = MSoundAlbumMap::find()
                    ->alias('t1')
                    ->select('t1.sound_id')
                    ->leftJoin(MSound::tableName() . ' AS t2', 't1.sound_id = t2.id')
                    ->where([
                        't1.album_id' => $album_id,
                        't2.pay_type' => MSound::SOUND_FREE,
                        't2.checked' => MSound::CHECKED_PASS,
                    ])
                    ->andWhere('t2.refined & ' . MSound::REFINED_POLICE . ' = 0')
                    ->asArray()
                    ->column();
                $redis->multi()
                    ->del($key)  // 避免并发重复写入
                    ->sAddArray($key, $album_sound_ids)
                    ->expire($key, FIVE_MINUTE)
                    ->exec();
                // 因单个用户触发此处概率较低，不进行随机取元素的操作
                $sound_ids = array_slice($album_sound_ids, 0, $pace);
            }
            $like_sound_ids = array_merge($like_sound_ids, $sound_ids);
        }
        $sounds = MSound::find()
            ->select('id, user_id, soundstr, catalog_id, create_time, cover_image, duration, view_count,
                comment_count, comments_count, sub_comments_count')
            ->where(['id' => $like_sound_ids,  'pay_type' => MSound::SOUND_FREE, 'checked' => MSound::CHECKED_PASS])
            ->andWhere('refined & ' . MSound::REFINED_POLICE . ' = 0')
            ->all();
        if ($check_length && count($sounds) < $need_sounds_length) {
            // 若音频数量少于需要的数量，则表示音单数据有更新，需更新缓存
            $keys = array_map(function ($album_id) use ($redis) {
                return $key = $redis->generateKey(KEY_NOOB_LIKE_SOUND_IDS, $album_id);
            }, array_keys($album_pace));
            $redis->del(...$keys);
            // 重新获取音频（避免无限递归，此次获取不确认音频长度）
            $sounds = self::getLikeSounds($persona_id, false);
        }
        // 打乱数组顺序，随机下发
        shuffle($sounds);
        return $sounds;
    }

    /**
     * 验证用户画像是否已定义
     *
     * @param int $persona 用户画像
     * @return bool 是否未已定义画像
     */
    public static function validatePersona(int $persona): bool
    {
        return in_array($persona, self::USER_PERSONAS);
    }

    /**
     * 对首页“猜你喜欢”模块进行 AB Test
     * 需求文档地址：https://github.com/MiaoSiLa/requirements-doc/tree/master/2019-04-10%20%E9%A6%96%E9%A1%B5%E7%8C%9C%E4%BD%A0%E5%96%9C%E6%AC%A2%E6%A8%A1%E5%9D%97%20AB%20%E6%B5%8B%E8%AF%95%20App
     */
    public static function abTestGetLikeSounds()
    {
        $redis = Yii::$app->redis;
        $user_id = Yii::$app->user->id;
        $equip_id = Yii::$app->equip->getEquipId();
        if ($user_id) {
            $condition = 'user_id = :user_id';
            $bind_params = [':user_id' => $user_id];
            $element = $user_id;
        } else {
            $condition = 'equip_id = :equip_id';
            $bind_params = [':equip_id' => $equip_id];
            $element = $equip_id;
        }
        // 将元素加入计数器
        $add = $redis->pfAdd(COUNTER_PF_LIKE_SOUND_AB_TEST, [$element]);
        if (!$add) {
            // 若元素加入计数器失败（重复元素），表示已经被设置过策略
            return;
        }
        $total = $redis->pfCount(COUNTER_PF_LIKE_SOUND_AB_TEST);
        $remainder = $total % 10;
        // 取总量除以 10 的余数，若余数为 0 则设定该用户走推荐策略二，余数为 1 走策略三，其余走默认策略
        // 实现 10% 的用户走推荐策略二，10% 的用户走推荐策略三，其余用户走默认策略
        if ($remainder > 1) {
            return;
        }
        $persona_id = self::find()
            ->select('persona')
            ->where($condition, $bind_params)
            ->scalar();
        // 若用户无画像，则使用默认画像（女性画像）
        $persona_id = $persona_id ? (int)$persona_id : self::TYPE_GIRL;
        $strategy = ($persona_id & self::STRATEGY_LIKE_SOUNDS_MASK) >> 8;
        if (in_array($strategy, [RecommendBase::STRATEGY_WEIGHT_BY_TIME, RecommendBase::STRATEGY_3])) {
            // 若已分配到策略二或策略三，则不再进行分配操作
            return;
        }
        // 余数为 0 则设定该用户走推荐策略二，余数为 1 走策略三
        $recommend_sound_strategy = $remainder === 0 ? self::RECOMMEND_SOUND_STRATEGY_2
            : self::RECOMMEND_SOUND_STRATEGY_3;
        // 更新用户推荐策略
        $expression = new Expression("persona | {$recommend_sound_strategy}");
        self::updateAll(['persona' => $expression], $condition, $bind_params);
    }

    /**
     * 获取音频所属画像 ID
     * 目前仅支持获取纯爱及乙女画像
     *
     * @param int $catalog_id
     * @return int
     */
    public static function getSoundPersonaByCatalogId(int $catalog_id): int
    {
        $persona_id = 0;
        if (in_array($catalog_id, Catalog::CATALOGS_TANBI)) {
            $persona_id = self::TYPE_FUJOSHI;
        } elseif (in_array($catalog_id, Catalog::CATALOGS_OTOME)) {
            $persona_id = self::TYPE_OTOME;
        }
        return $persona_id;
    }

    /**
     * 从安装广告来源设置设备的默认用户画像
     * 通过 creative_id 映射画像
     *
     * @param AdTrack $ad_track
     * @return bool 画像是否设置成功
     */
    public static function setPointsByAdTrack(AdTrack $ad_track): bool
    {
        if ($ad_track->vendor !== AdTrack::VENDOR_BILIBILI || !$ad_track->creative_id) {
            // 若非 bilibili 来源或无广告 ID，不设置画像
            return false;
        }
        $redis = Yii::$app->redis;
        if ($redis->sIsMember(KEY_AD_TRACK_ROMANTIC_ID, $ad_track->creative_id)) {
            // 若为乙女广告来源用户，设置其乙女画像分数值
            return self::setPersonaPoints(self::TYPE_OTOME, self::POINT_AD_TRACK, $ad_track->equip_id, $ad_track->buvid);
        }
        return false;
    }

    /**
     * 从广告弹窗画像设置默认用户画像分数
     * 目前仅设置乙女画像分数
     *
     * @param MRecommendPopup $popup 推荐弹窗对象
     * @param string $equip_id 设备号
     * @param null|string $buvid buvid
     * @return bool 是否设置成功
     * @throws \Exception 推荐弹窗中的画像值非法时抛出异常
     */
    public static function setPointsByRecommendPopup(MRecommendPopup $popup, string $equip_id, ?string $buvid): bool
    {
        switch ($popup->gender) {
            case MRecommendPopup::GENDER_UNKNOWN:
            // break intentionally omitted
            case MRecommendPopup::GENDER_MALE:
            // break intentionally omitted
            case MRecommendPopup::GENDER_FEMALE:
                return false;
            case MRecommendPopup::GENDER_OTOME:
                // 若为乙女广告来源弹窗推荐用户，设置其乙女画像分数值
                return self::setPersonaPoints(self::TYPE_OTOME, self::POINT_AD_TRACK, $equip_id, $buvid);
            default:
                throw new \Exception('gender 参数错误：' . $popup->gender);
        }
    }

    /**
     * 收听音频给用户画像加分数
     * 目前仅支持给纯爱及乙女画像加分数
     *
     * @param int $catalog_id 音频分类 ID
     * @param string $equip_id 设备号
     * @param null|string $buvid buvid
     * @param int $user_id 用户 ID
     * @return bool
     */
    public static function setPointsFromPlaySound(int $catalog_id, string $equip_id, ?string $buvid, int $user_id = 0): bool
    {
        $persona_id = self::getSoundPersonaByCatalogId($catalog_id);
        return self::setPersonaPoints($persona_id, self::POINT_PLAY, $equip_id, $buvid, $user_id);
    }

    /**
     * 收听音频给用户画像加分数
     * 目前仅支持给纯爱及乙女画像加分数
     *
     * @param int $catalog_id 音频分类 ID
     * @param int $user_id 用户 ID
     * @return bool
     * @todo 之后用户画像设置考虑迁移至 missevan-go 统一处理
     */
    public static function setPointsFromLikeSound(int $catalog_id, int $user_id, bool $is_like): bool
    {
        $persona_id = self::getSoundPersonaByCatalogId($catalog_id);
        $equip_id = Yii::$app->equip->getEquipId();
        $buvid = Yii::$app->equip->getBuvid();
        $points = $is_like ? self::POINT_LIKE_SOUND : -self::POINT_LIKE_SOUND;
        return self::setPersonaPoints($persona_id, $points, $equip_id, $buvid, $user_id);
    }

    /**
     * 收听音频给用户画像加分数
     * 目前仅支持给纯爱及乙女画像加分数
     *
     * @param int $catalog_id 剧集类型
     * @param int $user_id 用户 ID
     * @param bool $is_subscribe 是否为追剧
     * @return bool
     * @todo 之后用户画像设置考虑迁移至 missevan-go 统一处理
     */
    public static function setPointsFromSubscribeDrama(int $drama_type, int $user_id, bool $is_subscribe): bool
    {
        $persona_id = 0;
        if ($drama_type === Drama::TYPE_BL) {
            $persona_id = self::TYPE_FUJOSHI;
        } elseif ($drama_type === Drama::TYPE_OTOME) {
            $persona_id = self::TYPE_OTOME;
        }
        $equip_id = Yii::$app->equip->getEquipId();
        $buvid = Yii::$app->equip->getBuvid();
        $points = $is_subscribe ? self::POINT_SUBSCRIBE_DRAMA : -self::POINT_SUBSCRIBE_DRAMA;
        return self::setPersonaPoints($persona_id, $points, $equip_id, $buvid, $user_id);
    }

    /**
     * 设置用户画像分数
     *
     * @param int $persona_id 画像 ID
     * @param int $point 分数
     * @param string $equip_id 设备号
     * @param null|string $buvid 设备号
     * @param int $user_id 用户 ID
     * @return bool
     * @todo 之后用户画像设置考虑迁移至 missevan-go 统一处理
     */
    public static function setPersonaPoints(int $persona_id, int $points, string $equip_id, ?string $buvid, int $user_id = 0): bool
    {
        if (!$persona_id || !$points) {
            return false;
        }
        Yii::$app->serviceRpc->addPersonaPoint($persona_id, $points, $equip_id, $buvid, $user_id);
        return true;
    }

    /**
     * 是否为女性画像
     *
     * @param int $persona_id 画像 ID
     * @return bool
     */
    public static function isFemale(int $persona_id): bool
    {
        // 奇数为女性，偶数为男性
        return (bool)($persona_id & 1);
    }
}
