<?php

namespace app\models;

use app\components\util\Go;
use app\components\util\MUtils;
use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "m_sound_comment".
 *
 * @property integer $id
 * @property integer $sound_id
 * @property integer $user_id
 * @property string $text
 * @property string $stime
 * @property integer $size
 * @property integer $color
 * @property integer $mode
 * @property integer $date
 * @property integer $pool
 */
class MSoundComment extends ActiveRecord
{
    // 弹幕池等级，参考文档：https://info.missevan.com/pages/viewpage.action?pageId=21794709
    // 劣质弹幕
    const POOL_INFERIOR_QUALITY = 50;
    // 灌水弹幕
    const POOL_SPAM = 80;
    // 常规弹幕
    const POOL_NORMAL = 160;
    // 优质弹幕
    const POOL_HIGH_QUALITY = 195;
    // 字幕
    const POOL_SUBTITLE = 240;

    // 弹幕中是否含有字幕标识
    const HAS_NOT_SUBTITLE = 0;  // 无
    const HAS_SUBTITLE = 1;  // 有

    // 弹幕显示方式，1：滑动；4：底部；5：顶部
    const MODE_SLIDE = 1;
    const MODE_BOTTOM = 4;
    const MODE_TOP = 5;

    // 弹幕正常字号大小
    const SIZE_NORMAL = 25;

    // 弹幕数量
    const DM_NUM_30_SEC = 500;
    const DM_NUM_60_SEC = 850;
    const DM_NUM_180_SEC = 2500;
    const DM_NUM_600_SEC = 8500;
    const DM_NUM_900_SEC = 12000;
    const DM_NUM_2400_SEC = 25000;
    const DM_NUM_3600_SEC = 35000;
    const DM_MAX_NUM = 50000;

    // 新用户（注册时间不满 3 天）每日发送弹幕最大数量
    const NEW_USER_SEND_DM_MAX_NUM = 50;
    // 老用户每日发送弹幕最大数量
    const USER_SEND_DM_MAX_NUM = 500;

    // 假发送
    protected $_isShamSend = false;

    /**
     * @inheritdoc
     */
    public static function getDb()
    {
        return Yii::$app->messagedb;
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_sound_comment';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['sound_id', 'user_id', 'text', 'stime'], 'required'],
            [['sound_id', 'user_id', 'size', 'color', 'mode', 'date', 'pool'], 'integer'],
            [
                'text',
                'string',
                'encoding' => 'utf-8',
                'max' => 100,
                'tooLong' => '{attribute}长度不能超过 100 个字哦 _(:з」∠)_'
            ],
            [['stime'], 'string', 'max' => 11],
            ['text', 'checkText']
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'sound_id' => '音频 ID',
            'user_id' => '用户 ID',
            'text' => '弹幕',
            'stime' => '弹幕出现时间点',
            'size' => 'Size',
            'color' => '弹幕颜色',
            'mode' => 'Mode',
            'date' => '发送时间',
            'pool' => 'Pool',
        ];
    }

    /**
     * 验证弹幕是否违规
     *
     * @param string $attribute 验证的属性
     * @param mixed[] $params 其他自定义参数
     */
    public function checkText($attribute)
    {
        // 过滤特殊字符
        $this->$attribute = trim(MUtils::filterSpecialCodes($this->$attribute));
        if ($this->$attribute === '') {
            $this->addError($attribute, '请输入非纯表情的弹幕哦~');
            return;
        }
        // 检测弹幕违规情况
        if (!$result = Yii::$app->go->checkText($this->$attribute, Go::SCENE_DANMAKU)) {
            return;
        }
        if (!$item = current($result)) {
            return;
        }
        if (!$item['pass']) {
            $this->addError($attribute, $this->getAttributeLabel($attribute) . '中含有违规词汇喔~');
        } elseif (is_array($item['labels'])) {
            $this->_isShamSend = $this->checkShamSend($item['labels']);
        }
    }

    /**
     * 检查是否假发送
     *
     * @param $labels
     * @return bool
     */
    protected function checkShamSend(array $labels): bool
    {
        return in_array(Go::LABEL_EVIL, $labels);
    }

    //删除emoji表情
    public static function removeEmoji($text)
    {
        $newtext = '';
        // Match Emoticons
        $regexEmoticons = '/[\x{1F600}-\x{1F64F}]/u';
        $newtext = preg_replace($regexEmoticons, '', $text);

        // Match Miscellaneous Symbols and Pictographs
        $regexSymbols = '/[\x{1F300}-\x{1F5FF}]/u';
        $newtext = preg_replace($regexSymbols, '', $newtext);

        // Match Transport And Map Symbols
        $regexTransport = '/[\x{1F680}-\x{1F6FF}]/u';
        $newtext = preg_replace($regexTransport, '', $newtext);

        // Match Miscellaneous Symbols
        $regexMisc = '/[\x{2600}-\x{26FF}]/u';
        $newtext = preg_replace($regexMisc, '', $newtext);

        // Match Dingbats
        $regexDingbats = '/[\x{2700}-\x{27BF}]/u';
        $newtext = preg_replace($regexDingbats, '', $newtext);

        return $newtext;
    }

    public function textProcessing()
    {
        $this->text = trim($this->text);
        $this->text = str_replace(["\r", "\n"], '', $this->text);
        if (!$this->text) throw new HttpException(400, '请发送 1 至 100 字的非纯表情文本');
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        if ($insert) {
            if (!$sound = MSound::findOne($this->sound_id)) {
                throw new HttpException(404, '此单音已被删除');
            }
            $this->stime = floatval($this->stime);
            if ($this->stime * 1000 > $sound->duration && $this->stime < 0) {
                throw new HttpException(400, '参数有误，请联系工作人员');
            }
            $this->color = hexdec(str_replace('#', '', $this->color));
            if ($this->color < 0 || $this->color > 16777215) {
                $this->color = 16777215;
            }
            $this->date = $_SERVER['REQUEST_TIME'];
            $this->mode = self::MODE_SLIDE;
            $this->size = self::SIZE_NORMAL;
            $this->pool = self::POOL_NORMAL;
            $this->textProcessing();
        }
        return true;
    }

    /**
     * 获取音频弹幕
     *
     * @param int $sound_id 音频 ID
     * @return self[] 音频弹幕对象组成的数组
     * @todo 调用 rpc 获取弹幕列表
     */
    public static function getDm(int $sound_id): array
    {
        $sound = MSound::findOne($sound_id);
        // 获得音频弹幕数量
        $count = (int)$sound->comment_count;
        if ($count <= 0) {
            return [];
        }
        $need_count = 0;
        // 音频秒数
        $duration = intdiv($sound->duration, 1000);
        // 根据音频长度获取弹幕数量
        if ($duration <= HALF_MINUTE) {
            $need_count = self::DM_NUM_30_SEC;
        } elseif ($duration <= ONE_MINUTE) {
            $need_count = self::DM_NUM_60_SEC;
        } elseif ($duration <= ONE_MINUTE * 3) {
            $need_count = self::DM_NUM_180_SEC;
        } elseif ($duration <= TEN_MINUTE) {
            $need_count = self::DM_NUM_600_SEC;
        } elseif ($duration <= ONE_MINUTE * 15) {
            $need_count = self::DM_NUM_900_SEC;
        } elseif ($duration <= ONE_MINUTE * 40) {
            $need_count = self::DM_NUM_2400_SEC;
        } elseif ($duration <= ONE_HOUR) {
            $need_count = self::DM_NUM_3600_SEC;
        } else {
            $need_count = self::DM_MAX_NUM;
        }
        // 获取弹幕使用只读数据库
        $dm_info = MSoundCommentRO::find()
            ->from(self::tableName() . ' AS t FORCE INDEX(idx_soundid_pool)')
            ->select('t.stime, t.mode, t.size, t.color, t.date, t.pool, t.user_id, t.id, t.text')
            // 取出劣质弹幕以上等级的弹幕
            ->where('t.sound_id = :sound_id AND pool > :pool', [
                ':sound_id' => $sound->id,
                ':pool' => self::POOL_INFERIOR_QUALITY
            ])
            ->orderBy('t.pool DESC, t.id DESC')
            ->limit($need_count)->asArray()->all();
        if (empty($dm_info)) {
            return $dm_info;
        }
        $blacklist = BlackUser::getBlacklistUserIDs($sound->user_id);
        if (empty($blacklist)) {
            return $dm_info;
        }
        // 过滤音频 UP 主黑名单用户的弹幕
        foreach ($dm_info as $key => &$dm) {
            if (in_array($dm['user_id'], $blacklist)) {
                unset($dm_info[$key]);
            }
        }
        unset($dm);
        return array_values($dm_info);
    }

    /**
     * 限制用户每日发送弹幕数量
     * 主要用于限制无意义的灌水和广告弹幕
     *
     * @param int $user_id 用户 ID
     * @throws HttpException 弹幕额度用完时抛出异常
     */
    public static function limitCount(int $user_id)
    {
        $now = $_SERVER['REQUEST_TIME'];
        $user = Mowangskuser::find()->select('ctime')->where('id = :user_id', [':user_id' => $user_id])->one();
        $register_time = $user->ctime;
        // 若为注册时间不满 3 天的用户，视作新用户，新老用户每日可发送弹幕额度不同
        $limit_count = ($now - $register_time) > THREE_DAYS ? self::USER_SEND_DM_MAX_NUM
            : self::NEW_USER_SEND_DM_MAX_NUM;
        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(KEY_LOCK_USER_DM, $user_id);
        if ($redis->incr($lock) > $limit_count) {
            throw new HttpException(403, Yii::t('app/error',
                'Today\'s danmaku count has reached quota ({max_num}/{max_num})', ['max_num' => $limit_count]));
        }
        if ($redis->ttl($lock) === -1) {
            // 获得第二天零点时间戳
            $tomorrow_unix = strtotime('tomorrow');
            $time = $tomorrow_unix - $now;
            $redis->expire($lock, $time);
        }
    }

    public static function addTaskPoint()
    {
        // 积分任务，此后获取积分可以在此方法里面添加规则
    }
}
