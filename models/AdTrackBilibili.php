<?php

namespace app\models;

use Exception;
use Yii;

/**
 * Class AdTrackBilibili
 *
 * @package app\models
 *
 */
class AdTrackB<PERSON>bili extends AdTrack implements AdTrackInterface
{
    const BILIBILI_AD_GATEWAY = 'https://cm.bilibili.com/conv/api/conversion/ad/cb/v1';

    const CALLBACK_EVENT_TYPE_MAP = [
        self::CALLBACK_EVENT_ACTIVATE => self::CALLBACK_EVENT_TYPE_ACTIVATE,
        self::CALLBACK_EVENT_ONE_DAY_RETENTION => self::CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION,
        self::CALLBACK_EVENT_PAY => self::CALLBACK_EVENT_TYPE_PAY,
        self::CALLBACK_EVENT_REGISTER => self::CALLBACK_EVENT_TYPE_REGISTER,
        // TODO: 收听 5 个音的关键行为已调整为消费 / 充值关键行为，之后可删除此类型
        self::CALLBACK_EVENT_KEY_ACTION => self::CALLBACK_EVENT_TYPE_ACTION,
        self::CALLBACK_EVENT_TRANSACTION => self::CALLBACK_EVENT_TYPE_ACTION,  // 消费 / 充值关键行为
        self::CALLBACK_EVENT_APP_CALLUP => self::CALLBACK_EVENT_TYPE_APP_CALLUP,  // 唤起 APP (促活行为)
    ];
    const CALLBACK_EVENT_TYPE_ACTIVATE = 'APP_FIRST_ACTIVE';  // 激活
    const CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION = 'RETENTION';  // 次日留存
    const CALLBACK_EVENT_TYPE_PAY = 'USER_COST';  // 付费
    const CALLBACK_EVENT_TYPE_REGISTER = 'USER_REGISTER';  // 注册
    const CALLBACK_EVENT_TYPE_ACTION = 'ACTION_VALID';  // 关键行为
    const CALLBACK_EVENT_TYPE_APP_CALLUP = 'APP_CALLUP';  // 唤起 APP


    // 广告位来源：bilibili 广告后台，bilibili 花火后台，bilibili CPT 资源创意
    const FROM_BILIBILI_COMMON_AD = 0;
    const FROM_BILIBILI_HUAHUO_AD = 1;
    const FROM_BILIBILI_CPT_AD = 2;

    /**
     * @param string $event_type
     * @param mixed $arg
     */
    public function callback(string $event_type, $arg = 0)
    {
        try {
            if (!array_key_exists($event_type, self::CALLBACK_EVENT_TYPE_MAP)) {
                throw new Exception('B 站广告点击回传事件错误：' . $event_type);
            }
            $event = self::CALLBACK_EVENT_TYPE_MAP[$event_type];
            if (is_null($event)) {
                return true;
            }
            $data = Yii::$app->tools->requestRemote(
                self::BILIBILI_AD_GATEWAY,
                $this->getCallbackBody($event, $arg)
            );
            if (!($data && $data['code'] === 0)) {
                throw new Exception(sprintf('B 站广告点击回传失败：code[%d], msg[%s]', $data['code'], $data['message'] ?? ''));
            }
            return true;
        } catch (Exception $e) {
            Yii::error('bilibili ad error: ' . $e->getMessage(), __METHOD__);
        }
        return false;
    }

    private function getCallbackBody(string $callback_event, $arg = 0)
    {
        $body = [
            'conv_type' => $callback_event,
            'conv_time' => $_SERVER['REQUEST_TIME'] * 1000,
            'client_ip' => Yii::$app->request->userIP,
            'track_id' => $this->track_id,
            'ua' => urlencode($this->ua),
            'model' => Yii::$app->equip->getPhone(),
        ];
        if ($callback_event === self::CALLBACK_EVENT_TYPE_ACTION) {
            $body['conv_count'] = $arg;
        }
        if ($this->imei_md5) {
            $body['imei'] = $this->imei_md5;
        }
        if ($this->idfa) {
            $body['idfa'] = $this->idfa;
        }
        if ($this->oaid) {
            $body['oaid'] = $this->oaid;
        }
        if ($this->mac_md5) {
            $body['mac'] = $this->mac_md5;
        }
        // 暂时不回传付费金额（付费金额属于机密数据）
        // if ($callback_event === self::CALLBACK_EVENT_TYPE_PAY) {
        //     $body['conv_value'] = Balance::profitUnitConversion($arg, Balance::CONVERT_YUAN_TO_FEN);
        //     $body['conv_count'] = 1;
        // }
        return $body;
    }

    public function isCallbackRequired()
    {
        return $this->ad_from !== AdTrackBilibili::FROM_BILIBILI_HUAHUO_AD;
    }

    /**
     * @inheritdoc
     */
    public function isValidTracked()
    {
        return $this->create_time > $_SERVER['REQUEST_TIME'] - ONE_DAY;
    }

    public function getAdVendor(): int
    {
        return self::VENDOR_BILIBILI;
    }

}
