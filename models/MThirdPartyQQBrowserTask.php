<?php

namespace app\models;

use Exception;
use Yii;
use yii\helpers\Json;

class MThirdPartyQQBrowserTask
{
    // 合作方回调接口文档：https://info.bilibili.co/pages/viewpage.action?pageId=958111694
    const CALLBACK_URL_QQ_BROWSER = 'https://ugpage.html5.qq.com/thirdparty/report';

    // 与 QQ 浏览器方的约定参数
    const QQ_BROWSER_FROM = 'missevan';
    // 加密 Token 参数的密钥
    const QQ_BROWSER_SIGN_KEY = 'MLxCvj3AXUe8rm3IQH8TvdmQ';

    // 回调成功响应信息
    const QQ_BROWSER_REQUEST_SUCCESS_MSG = 'success';

    // 加密 Token 时，使用的初始化向量长度
    const AES_BLOCK_SIZE = 16;

    /**
     * 请求 QQ 浏览器回调
     *
     * @param string $track_id
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function callback(string $track_id): bool
    {
        $params = [
            'from' => self::QQ_BROWSER_FROM,
            'token' => self::encryptToken($track_id),
        ];
        try {
            $data = Yii::$app->tools->requestRemote(self::CALLBACK_URL_QQ_BROWSER, [], 'POST', Json::encode($params), 0, [
                'Content-Type' => 'application/json',
            ]);
            if (!$data) {
                throw new Exception('返回值为空');
            }
            if ($data['msg'] !== self::QQ_BROWSER_REQUEST_SUCCESS_MSG) {
                // token 验证失败时 msg 会返回 fail
                throw new Exception(sprintf('msg[%s]', $data['msg']));
            }
        } catch (Exception $e) {
            Yii::error('QQ 浏览器点击回传失败，原因：' . $e->getMessage(), __METHOD__);
            return false;
        }
        return true;
    }

    /**
     * 获取 QQ 浏览器加密后的 token
     *
     * @param string $token 待加密的 token
     * @return string 加密后的 token
     * @throws Exception
     */
    private static function encryptToken(string $token): string
    {
        $key_length = strlen(self::QQ_BROWSER_SIGN_KEY);
        $method = 'aes-' . ($key_length * 8) . '-cfb';

        // 创建一个指定长度的随机 IV
        $iv = openssl_random_pseudo_bytes(self::AES_BLOCK_SIZE);

        // 使用 AES CFB 模式加密
        $cipher_text = openssl_encrypt($token, $method, self::QQ_BROWSER_SIGN_KEY, OPENSSL_RAW_DATA, $iv);

        // 将 IV 和密文合并，并进行 base64URl 编码
        $result = self::base64UrlEncode($iv . $cipher_text);

        return $result;
    }

    /**
     * 获取 base64URl 编码后的密文
     *
     * @param string $str 待编码的字符串
     * @return string
     */
    private static function base64UrlEncode(string $str): string
    {
        return rtrim(strtr(base64_encode($str), '+/', '-_'), '=');
    }
}
