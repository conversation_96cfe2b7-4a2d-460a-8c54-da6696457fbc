<?php

namespace app\models;

use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "guess_your_likes_clicks".
 *
 * @property integer $id
 * @property integer $module_id
 * @property integer $elem_type
 * @property integer $elem_id
 * @property string $equip_id
 * @property string $buvid
 * @property integer $user_id
 * @property integer $time
 * @property integer $is_more
 * @property integer $module_position
 * @property integer $elem_position
 * @property integer $persona_id;
 * @property integer $view
 * @property integer $module_type
 * @property integer $os
 * @property string $channel
 */
class GuessYourLikesClicks extends ActiveRecord
{
    const ELEM_TYPE_OTHERS = 0;
    const ELEM_TYPE_ALBUM = 1;
    const ELEM_TYPE_DRAMA = 2;
    const ELEM_TYPE_SOUND = 3;

    const MODULE_TYPE_RECOMMENDED_SOUNDS = 3;
    const MODULE_TYPE_RECOMMENDED_MODULE = 5;

    // 首页推荐页面
    const VIEW_HOMEPAGE_RECOMMEND = 1;
    // 模块更多页面
    const VIEW_HOMEPAGE_MODULE_MORE = 151;

    /**
     * @inheritdoc
     */
    public static function getDb()
    {
        return Yii::$app->logdb;
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'guess_your_likes_clicks';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [
                [
                    'view',
                    'is_more',
                    'module_id',
                    'module_position',
                    'elem_type',
                    'elem_id',
                    'elem_position',
                    'user_id',
                    'persona_id',
                    'time',
                    'module_type',
                    'os',
                ],
                'integer'
            ],
            [['elem_id', 'equip_id', 'time'], 'required'],
            [['equip_id'], 'string', 'length' => 36],
            [['channel'], 'string', 'length' => 50],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'module_id' => '模块 ID',
            'elem_type' => '所点击的元素类型',  // 0 其它、1 为模块中的音单、2 为模块中的剧集、3 为猜你喜欢的单音
            'elem_id' => '元素 ID',
            'equip_id' => '设备 ID',
            'user_id' => '用户 ID',
            'time' => '点击时间',
            'is_more' => '是否是属于模块的更多页面',  // 0 为否，1 为是
            'view' => '点击的视图位置',  // 1 为首页推荐，151 为推荐模块更多页面
            'module_position' => '模块的位置',  // 从 1 开始，从上至下递增
            'elem_position' => '元素（音单或剧集）在模块中的位置',  // 从 1 开始，从左至右、从上至下递增
            'persona_id' => '用户画像', // 包含模块及猜你喜欢推荐策略
            'module_type' => '模块类型',
            'os' => '设备类型',  // 1 为安卓、2 为 iOS
            'buvid' => 'BUVID',
            'channel' => '渠道标识',
        ];
    }

    public static function checkElemType(int $elem_type)
    {
        if (in_array($elem_type,
            [self::ELEM_TYPE_OTHERS, self::ELEM_TYPE_SOUND, self::ELEM_TYPE_ALBUM, self::ELEM_TYPE_DRAMA])) {
            return true;
        }
        return false;
    }

    /**
     * 采集用户点击
     *
     * @param integer $view 视图位置
     * @param integer $is_more 是否为点击更多
     * @param integer $module_type 模块类型
     * @param integer $module_id 模块 ID
     * @param integer $module_position 模块位置
     * @param integer $elem_type 元素类型
     * @param integer $elem_id 元素 ID
     * @param integer $elem_position 元素在模块中的位置
     * @return boolean
     */
    public static function collectClick(int $view, int $is_more, int $module_type, int $module_id,
        int $module_position, int $elem_type, int $elem_id, int $elem_position)
    {
        $equip = Yii::$app->equip;
        $equip_id = $equip->getEquipId();
        $persona_id = Persona::getPersonaType();
        $new_click = [
            'view' => $view,
            'is_more' => $is_more,
            'module_type' => $module_type,
            'module_id' => $module_id,
            'module_position' => $module_position,
            'elem_id' => $elem_id,
            'elem_type' => $elem_type,
            'elem_position' => $elem_position,
            'equip_id' => $equip_id,
            'user_id' => (int)Yii::$app->user->id,
            'persona_id' => $persona_id,
            'time' => $_SERVER['REQUEST_TIME'],
            'os' => $equip->getOs(),
            'buvid' => $equip->getBuvid() ?? '',
            'channel' => $equip->getChannel(),
        ];

        $memcache = Yii::$app->memcache;
        if (($clicks = $memcache->get(KEY_GUESSYOURLIKES_CLICKS))) {
            $clicks = Json::decode($clicks);
        } else {
            $clicks = [];
        }
        $MAX_CLICKS = 100;
        $clicks[] = $new_click;

        if (count($clicks) >= $MAX_CLICKS) {
            $memcache->delete(KEY_GUESSYOURLIKES_CLICKS);
            // TODO: 待数据格式正确后删除之兼容代码
            // 兼容因增加 channel 字段之前 redis 没有 channel，批量插入的时候数据 key value 不对应情况
            foreach ($clicks as &$click) {
                if (!isset($click['channel'])) {
                    $click['channel'] = '';
                }
            }
            // 插入 value 值顺序需和 array_keys 保持一致
            self::getDb()->createCommand()->batchInsert(self::tableName(), array_keys($new_click), $clicks)
                ->execute();
        } else {
            $memcache->set(KEY_GUESSYOURLIKES_CLICKS, Json::encode($clicks), ONE_WEEK);
        }
        return true;
    }
}
