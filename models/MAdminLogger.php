<?php

namespace app\models;

use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;

/**
 * This is the model class for table "m_admin_logger".
 *
 * @property integer $id
 * @property integer $user_id
 * @property integer $catalog
 * @property integer $channel_id
 * @property string $url
 * @property string $intro
 * @property string $ip
 * @property integer $create_time
 */
class MAdminLogger extends ActiveRecord
{
    // 发送海外短信
    const SEND_FOREIGNSMS = 28;
    // 站内充值
    const CATALOG_TOPUP = 33;
    // 违规未审评论
    const CATALOG_NOT_VERIFY_COMMENT = 38;
    // iOS 用户通过苹果后台发起退款成功
    const CATALOG_USER_LAUNCH_IOS_REFUND_SUCCESS = 150;
    // 直播结算系统提现审核
    const CATALOG_SYSTEM_WITHDRAWAL_CHECK = 311;

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('logdb');
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_admin_logger';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'catalog', 'channel_id', 'create_time'], 'integer'],
            [['catalog'], 'required'],
            [['intro'], 'string'],
            [['url'], 'string', 'max' => 255],
            [['ip'], 'string', 'max' => MUtils2::IP_MAX_LENGTH],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => '用户id',
            'catalog' => '类型',
            'channel_id' => '频道id',
            'url' => 'url',
            'intro' => '操作',
            'ip' => '操作ip',
            'create_time' => '操作时间',
        ];
    }

    /**
     * 记录审计日志
     *
     * @param array $attributes
     * @throws Exception
     */
    public static function addOne(array $attributes)
    {
        $model = new self(array_merge([
            'intro' => '',
            'catalog' => 0,
            'channel_id' => 0,
            'user_id' => 0,
            'url' => Yii::$app->request->url,
            'ip' => Yii::$app->request->userIP,
            'create_time' => time(),
        ], $attributes));
        if (!$model->save()) {
            throw new \Exception(MUtils2::getFirstError($model));
        }
    }

}
