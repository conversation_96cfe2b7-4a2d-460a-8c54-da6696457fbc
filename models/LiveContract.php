<?php

namespace app\models;

use Yii;

/**
 * @deprecated
 * This is the model class for table "live_contract".
 *
 * @property int $id
 * @property string $name 类型名称
 * @property double $rate 享受分成
 * @property int $create_time
 * @property int $modified_time
 */
class LiveContract extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'live_contract';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['name', 'rate'], 'required'],
            [['rate'], 'number'],
            [['create_time', 'modified_time'], 'integer'],
            [['name'], 'string', 'max' => 20],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => '类型名称',
            'rate' => '享受分成',
            'create_time' => '创建日期',
            'modified_time' => '修改日期',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }
}
