<?php
namespace app\models;

use app\components\util\Equipment;
use app\components\util\Go;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "event_vote_detail".
 *
 * @property integer $id
 * @property integer $user_id
 * @property integer $eid
 * @property integer $event_id
 * @property integer $time
 * @property string $ip
 * @property integer $env
 */
class EventVoteDetail extends ActiveRecord
{
    const EQUIPMENT_TYPE_WEB = 1;
    const EQUIPMENT_TYPE_ANDROID = 2;
    const EQUIPMENT_TYPE_IOS = 3;
    const EQUIPMENT_TYPE_MOBILE_WEB = 4;
    const EQUIPMENT_TYPE_HARMONYOS = 5;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'event_vote_detail';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'eid', 'event_id'], 'required'],
            [['user_id', 'eid', 'event_id', 'time', 'env'], 'integer'],
            [['ip'], 'string', 'max' => MUtils2::IP_MAX_LENGTH],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => '用户M号',
            'eid' => '资源 id',
            'event_id' => '活动 id',
            'time' => '投票时间',
            'ip' => '用户 ip',
            'env' => '终端环境',  // 1 web 端，2 安卓，3 iOS，4 手机网页，5 鸿蒙
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->time = $time;
            $this->ip = Yii::$app->request->userIP;
            switch (Yii::$app->equip->getOs()) {
                case Equipment::Android:
                    $this->env = self::EQUIPMENT_TYPE_ANDROID;
                    break;
                case Equipment::iOS:
                    $this->env = self::EQUIPMENT_TYPE_IOS;
                    break;
                case Equipment::HarmonyOS:
                    $this->env = self::EQUIPMENT_TYPE_HARMONYOS;
                    break;
            }
        }
        return true;
    }

    /**
     * 活动作品投票
     * 投票规则：
     * 1. 用户投票的数量不能超过用户对单个活动投票总数的限制
     * 2. 用户投票的作品数量不能超过用户对单个活动可投票作品的限制
     * 3. 用户对单个作品的投票数量不能超过用户对单个活动中单个作品的投票总数限制
     *
     * @param int $event_id 活动 ID
     * @param int $sound_id 作品 ID
     * @param int $user_id 用户 ID
     * @throws HttpException
     * @throws \Exception
     */
    public static function vote(int $event_id, int $sound_id, int $user_id)
    {
        $equip = Yii::$app->equip;

        if (!$event = MEvent::findOne($event_id)) {
            throw new HttpException(404, '活动不存在', 200410001);
        }

        if (!($event->attr & MEvent::ATTR_VOTE)) {
            throw new HttpException(403, '活动不支持投票');
        }
        $event->check(true);

        $vote = EventVote::findOne(['event_id' => $event_id, 'eid' => $sound_id]);
        if (!$vote) {
            throw new HttpException(404, '没有此作品', 200410102);
        }

        // 投票风险检验
        $result = MEvent::checkRisk();
        $is_evil = !empty($result['labels']) && in_array(Go::LABEL_EVIL, $result['labels']);
        if ($is_evil && in_array($event_id, GloryofkingsActivity::EVENT_ID_ARR)) {
            // 抛出相关异常
            throw new HttpException(403, '投票失败！检测到您有多个账号异常登录投票的行为，请使用正常权益投票！');
        }

        // 给投票操作上锁
        $event_limit_key = Yii::$app->redis->generateKey(KEY_EVENT_USER_VOTE, $event_id, $user_id);
        if (Yii::$app->redis->lock($event_limit_key, ONE_MINUTE)) {
            // 用户对单个活动每天投票总数的限制
            $limit_total = 0;
            $limit_vote = 0;

            $transaction = Yii::$app->db->beginTransaction();
            $redis = Yii::$app->redis;
            try {
                if ($event->limit || $event->limit_vote) {
                    $limit = Yii::$app->redis->hMGet($event_limit_key, ["user_id:$user_id", $sound_id]);
                    $limit_total = (int)$limit["user_id:$user_id"];
                    $limit_vote = (int)$limit[$sound_id];
                }
                if ($event_id === 121) {
                    // WORKAROUND: 暑期声恋区投稿活动临时调整
                    $end_today = $event->end_time + ONE_WEEK;
                } else {
                    $end_today = strtotime('tomorrow');
                }

                // 每天投票总数限制
                if ($event->limit && $limit_total >= $event->limit) {
                    if ($event_id === 121) {
                        // WORKAROUND: 暑期声恋区投稿活动临时调整
                        throw new HttpException(403, '已经没有投票机会了哦~', 200410101);
                    } elseif (in_array($event_id, GloryofkingsActivity::EVENT_ID_ARR)) {
                        // WORKAROUND: 天涯明月刀配音活动临时调整，2021.12.24 投票结束后考虑删除
                        GloryofkingsActivity::checkShareVote($user_id);
                    } else {
                        throw new HttpException(403, '一天只能投 ' . $event->limit . ' 票哟~',
                            200410101);
                    }
                }

                // 每天每个作品投票数量限制
                if ($event->limit_vote) {
                    if ($limit_vote >= $event->limit_vote) {
                        if ($event_id === 121) {
                            // WORKAROUND: 暑期声恋区投稿活动临时调整
                            throw new HttpException(403, '已经为该作品投过票了哦', 200410101);
                        } else {
                            throw new HttpException(403, '每个作品一天最多只能投 ' . $event->limit_vote .
                            ' 票哟~', 200410101);
                        }
                    }
                    // 作品每天有投票上限时，每台设备限制只能以 3 个用户账号进行投票
                    $equip_id = $equip->getEquipId();
                    $equip_limit_key = $redis->generateKey(KEY_EQUIP_EVENT_SOUND_VOTE, $event_id, $sound_id);
                    $equip_vote_num = (int)Yii::$app->redis->hGet($equip_limit_key, $equip_id);
                    if ($equip_vote_num >= $event->limit_vote * 3) {
                        throw new HttpException(403, '今日投票已达上限', 200410101);
                    }
                    // 投票成功后对应的 redis hash key 加 1
                    // 投票之前已设置防并发锁，此处不需要考虑并发的情况
                    $redis->multi()
                        ->hIncrBy($equip_limit_key, $equip_id, 1)
                        ->expireAt($equip_limit_key, $end_today)
                        ->exec();
                }

                // 每天投票作品数量限制
                if ($event->limit_work) {
                    $vote_eid_count = Yii::$app->redis->hLen($event_limit_key) - 1;
                    if ($vote_eid_count >= $event->limit_work && !$redis->hExists($event_limit_key, $sound_id)) {
                        throw new HttpException(403, '一天只能给 ' . $event->limit_work .
                            ' 个作品投票哟~', 200410101);
                    }
                }

                if (!$is_evil) {
                    $model = new self();
                    $model->user_id = $user_id;
                    $model->event_id = $event_id;
                    $model->eid = $sound_id;
                    if (!$model->save()) {
                        throw new HttpException(400, '投票失败，请稍后再试');
                    }

                    $vote->updateCounters(['vote_num' => 1]);
                }

                // 投票成功后对应的 redis hash key 加 1
                // 投票之前已设置防并发锁，此处不需要考虑并发的情况
                $redis->multi()
                    ->hIncrBy($event_limit_key, $sound_id, 1)
                    ->hIncrBy($event_limit_key, "user_id:$user_id", 1)
                    ->expireAt($event_limit_key, $end_today)
                    ->exec();

                // 每天投票总数限制
                if ($event->limit && $limit_total >= $event->limit) {
                    if (in_array($event_id, GloryofkingsActivity::EVENT_ID_ARR)) {
                        GloryofkingsActivity::setShareVote($user_id, $event);
                    }
                }

                $transaction->commit();
            } catch (\Exception $e) {
                $transaction->rollBack();
                throw $e;
            } finally {
                $redis->unlock($event_limit_key);
            }
        } else {
            throw new HttpException(400, '操作过于频繁，请稍后再试');
        }
    }
}
