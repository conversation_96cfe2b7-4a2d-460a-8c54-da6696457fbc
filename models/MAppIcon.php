<?php

namespace app\models;

use app\components\util\Equipment;
use missevan\storage\StorageClient;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "m_app_icon".
 *
 * @property integer $id 主键
 * @property integer $create_time 创建时间戳，单位：秒
 * @property integer $modified_time 最后更新时间戳，单位：秒
 * @property string $icon App 图标地址
 * @property integer $start_time 开始展示时间点。单位：秒
 * @property integer $end_time 结束展示时间点，为 0 时表示永久生效。单位：秒
 * @property array $supported_version 支持的最低客户端版本，数据库存储类型为 json, e.g. {"ios": "6.1.3"}，安卓采用发版的形式变更图标
 */
class MAppIcon extends ActiveRecord
{
    const END_TIME_FOREVER_EFFECTIVE = 0;

    public $icon_url;  // App 图标完整地址

    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_app_icon';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['create_time', 'modified_time', 'start_time', 'end_time'], 'integer'],
            [['icon'], 'string'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间戳',  // 单位：秒
            'modified_time' => '最后更新时间戳',  // 单位：秒
            'icon' => 'App 图标地址',
            'start_time' => '开始展示时间点',  // 单位：秒
            'end_time' => '结束展示时间点',  // 为 0 时表示永久生效。单位：秒
            'supported_version' => '支持的最低客户端版本',  // e.g. {"ios": "6.1.3"}
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->icon) {
            $this->icon_url = StorageClient::getFileUrl($this->icon);
        }
        if (YII_ENV === 'test' && $this->supported_version) {
            // sqlite json 字段不支持自动 decode，需进行 Json::decode 处理
            $this->supported_version = Json::decode($this->supported_version);
        }
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        if (YII_ENV === 'test' && $this->supported_version) {
            // sqlite json 字段不支持自动 encode，需进行 Json::encode 处理
            $this->supported_version = Json::encode($this->supported_version);
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 获取生效中的应用图标
     *
     * @return array|null
     */
    public static function getAppIcon()
    {
        $now = $_SERVER['REQUEST_TIME'];
        $memcache = Yii::$app->memcache;
        if ($data = $memcache->get(KEY_APP_ICON)) {
            $app_icon = Json::decode($data);
        } else {
            $app_icon = self::find()
                ->where('start_time <= :now_time AND (end_time > :now_time OR end_time = :end_time)',
                    [':now_time' => $now, ':end_time' => self::END_TIME_FOREVER_EFFECTIVE])
                ->orderBy('start_time DESC')
                ->limit(1)
                ->one();
            $cache_duration = MIN_CACHE_DURATION;
            if ($app_icon) {
                if (!isset($app_icon['supported_version']['ios'])) {
                    Yii::error('应用图标数据未配置支持的客户端最低版本号，id: ' . $app_icon['id'], __METHOD__);
                    $app_icon = null;
                } else {
                    $app_icon = $app_icon->toArray();
                    $cache_duration = FIVE_MINUTE;
                    if ($app_icon['end_time'] && $app_icon['end_time'] < ($now + $cache_duration)) {
                        $cache_duration = max($app_icon['end_time'] - $now, MIN_CACHE_DURATION);
                    }
                }
            }
            $memcache->set(KEY_APP_ICON, Json::encode($app_icon), $cache_duration);
        }
        if (!$app_icon || ($app_icon['end_time'] && $app_icon['end_time'] <= $now) ||
                // NOTICE: 安卓采用发版的形式变更图标
                Equipment::isAppOlderThan($app_icon['supported_version']['ios'], null)) {
            return null;
        }
        return [
            'id' => $app_icon['id'],
            'expire_duration' => $app_icon['end_time'] ?
                $app_icon['end_time'] - $now : MAppIcon::END_TIME_FOREVER_EFFECTIVE,
        ];
    }
}
