<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/17
 * Time: 12:24
 */

namespace app\models;

use app\components\util\Equipment;
use Yii;
use ArrayAccess;
use yii\base\ArrayableTrait;
use yii\base\BaseObject;
use yii\web\HttpException;
use yii\web\IdentityInterface;
use yii\base\Arrayable;
use app\components\util\SSOClient;

class User extends BaseObject implements IdentityInterface, Arrayable, ArrayAccess
{
    use ArrayableTrait;

    public $user_id;
    public $username;
    public $iconurl;
    public $iconcolor;
    public $confirm;

    public $equip_id;
    public $equip;

    public $ctime;
    public $login_at;

    public $token;
    public $expire_at;
    public $iconid;
    public $mobile;
    public $region;
    public $email;

    private $expiration = 0;

    private $from = self::FROM_CACHE;

    const FROM_CACHE = 0;
    const FROM_SSO = 1;
    const FROM_DB = 2;

    public static function Login($account, $type, $password, $region = 86, $openid = null)
    {
        $user = Yii::$app->sso->login($account, $type, $password, $region, $openid);

        $equipment = Yii::$app->equip;

        $model = new self;
        $model->token = $user['token'];
        $model->expire_at = $user['expire_at'];
        $model->equip_id = $equipment->getEquipId();
        $model->equip = $equipment->getOs();
        $model->login_at = $user['login_at'];
        $model->from = self::FROM_SSO;
        $model->load($user['user']);
        return $model;
    }

    /**
     * {@inheritdoc}
     */
    public function Logout()
    {
        Yii::$app->sso->logout();
        Yii::$app->memcache->delete(Pre_Token . $this->token);
        $redis = Yii::$app->redis;
        $equip_play_key = $redis->generateKey(KEY_EQUIP_PLAY_USER_ID, Yii::$app->user->id);
        $redis->zRem($equip_play_key, Yii::$app->equip->getEquipId());
    }

    /**
     * {@inheritdoc}
     *
     * 改为用 token 获取数据
     */
    public static function findIdentity($token)
    {
        if (!$token) {
            return null;
        }

        if (!$info = Yii::$app->memcache->get(Pre_Token . $token)) {
            $equipment = Yii::$app->equip;

            try {
                $user = Yii::$app->sso->session($token);
            } catch (HttpException $e) {
                // 为避免不需要鉴权且携带失效 token 的 API 无法执行，此处跳过鉴权报错
                // 需鉴权的 API，可使用 AccessControl 要求登录状态和用户权限
                if ($e->statusCode === 401) {
                    return null;
                }
                throw new HttpException($e->statusCode, $e->getMessage(), $e->getCode());
            }
            $info = $user['user'];
            $info['token'] = $user['token'];
            $info['expire_at'] = $user['expire_at'];
            $info['login_at'] = $user['login_at'];
            $info['from'] = self::FROM_SSO;
            $info['equip_id'] = $equipment->getEquipId();
            $info['equip'] = $equipment->getOs();
            // 补全请求登录与登录中请求的不同项
            $info['expiration'] = $_SERVER['REQUEST_TIME'] + FIVE_MINUTE;
        }

        $model = new self();
        $model->load($info);
        return $model;
    }

    /**
     * {@inheritdoc}
     */
    public static function findIdentityByAccessToken($token, $type = null)
    {
        return null;
    }

    /**
     * {@inheritdoc}
     */
    public function renewal()
    {
        if ($this->token) {
            $this->from = self::FROM_CACHE;
            $this->expiration = $_SERVER['REQUEST_TIME'] + FIVE_MINUTE;
            Yii::$app->memcache->set(Pre_Token . $this->token, $this, FIVE_MINUTE);
            return true;
        }
        return false;
    }

    /**
     * 获取 identity 来源，调用renewal方法后来源会变成self::FROM_CACHE
     *
     * @return int self::FROM_CACHE, self::FROM_SSO, self::FROM_DB
     */
    public function getFrom()
    {
        return $this->from;
    }

    public function getId()
    {
        return $this->user_id;
    }

    public function getInfo()
    {
        return [
            'username' => $this->username,
            'iconurl' => $this->iconurl,
            'iconcolor' => $this->iconcolor,
            'confirm' => $this->confirm,
            'token' => $this->token,
            'expire_at' => $this->expire_at,
        ];
    }

    public function getExpiration()
    {
        return $this->expiration;
    }

    public function getAuthKey()
    {
        return $this->token;
    }

    public function validateAuthKey($authKey)
    {
        return $this->token === $authKey;
    }

    public function load($datas)
    {
        foreach ($datas as $key => $value) {
            if (property_exists($this, $key)) {
                $this->$key = $value;
            }
        }
    }

    public function offsetExists($offset)
    {
        return isset($this->$offset);
    }

    public function offsetGet($offset)
    {
        return $this->$offset;
    }

    public function offsetSet($offset, $value)
    {
        $this->$offset = $value;
    }

    public function offsetUnset($offset)
    {
        $this->offset = NULL;
    }
}
