<?php

namespace app\models;

use missevan\storage\StorageClient;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "m_homepage_flow".
 *
 * @property int $id 主键
 * @property string $title 标题
 * @property string $intro 简介
 * @property string $more 更多信息（JSON）
 * @property string $cover 封面
 * @property string $url 跳转地址
 * @property int $elem_id 元素 ID
 * @property int $elem_type 元素类型（同 m_recommended_elements 表元素类型一致）
 * @property int $attr 属性
 * @property int $archive 是否归档（1 是，0 否）
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 */
class MHomepageFlow extends ActiveRecord
{
    const ARCHIVE_ONLINE = 0;
    const ARCHIVE_ARCHIVED = 1;

    // 元素类型（同 m_recommended_elements 保持一致）
    const ELEMENT_TYPE_OTHERS = 0;
    const ELEM_TYPE_ALBUM = 1;
    const ELEM_TYPE_DRAMA = 2;
    const ELEM_TYPE_SOUND = 3;
    const ELEM_TYPE_EVENT = 4;
    const ELEM_TYPE_LIVE = 5;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_homepage_flow';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['elem_id', 'elem_type', 'attr', 'archive'], 'integer'],
            [['title', 'intro', 'cover', 'url'], 'string', 'max' => 255],
            [['more'], 'string', 'max' => 1024],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'title' => '标题',
            'intro' => '简介',
            'more' => '更多信息（JSON）',  // 剧集存储更新至
            'cover' => '封面',
            'url' => '跳转地址',
            'elem_id' => '元素 ID',
            'elem_type' => '元素类型',  // 0 剧集
            'attr' => '属性',
            'archive' => '是否归档',  // 1 是，0 否
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 获取首页推荐剧集
     * @param int $last_id
     * @param int $page_size
     * @param int|null $user_id
     * @return array
     */
    public static function getData(int $last_id, int $page_size = PAGE_SIZE_20, ?int $user_id = null): array
    {
        $query = self::find()->select('id, elem_id, title, more, elem_type, cover, url')
            ->where([
                'archive' => self::ARCHIVE_ONLINE,
                'elem_type' => [self::ELEM_TYPE_DRAMA, self::ELEMENT_TYPE_OTHERS],
            ]);
        if ($last_id > 0) {
            $query = $query->andWhere('id < :id', [':id' => $last_id]);
        }
        $data = $query
            ->limit($page_size)
            ->orderBy('id DESC')
            ->asArray()->all();
        $has_more = true;
        if (count($data) !== $page_size) {
            $has_more = false;
        }

        $drama_ids = array_reduce($data, function ($ret, $item) {
            if (self::ELEM_TYPE_DRAMA === (int)$item['elem_type']) {
                $ret[] = $item['elem_id'];
            }
            return $ret;
        }, []);
        $drama_data = [];
        if (!empty($drama_ids)) {
            $drama_data = Drama::getRecommendedDramas($drama_ids, $user_id);
        }
        $data = array_map(function ($item) use ($drama_data) {
            $item['outline'] = null;
            $item['elem_id'] = (int)$item['elem_id'];
            $item['elem_type'] = (int)$item['elem_type'];
            $item['cover'] = StorageClient::getFileUrl($item['cover']);
            if ($more = Json::decode($item['more']) ?: null) {
                $item['more'] = $more;
            } else {
                unset($item['more']);
            }
            $item['id'] = (int)$item['id'];

            if ($drama = $drama_data[$item['elem_id']] ?? null) {
                $item['outline'] = $drama['outline'] ?? null;
                $item['pay_type'] = $drama['pay_type'];
                $item['need_pay'] = $drama['need_pay'];
            }
            return $item;
        }, $data);

        // TODO: 调整为 class 方式处理
        return [
            'Datas' => $data,
            'pagination' => [
                'has_more' => $has_more,
            ],
        ];
    }

}
