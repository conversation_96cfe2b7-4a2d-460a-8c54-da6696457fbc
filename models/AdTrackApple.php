<?php

namespace app\models;

use app\components\util\AppleAdManagementClient;
use app\components\util\Equipment;
use Exception;
use Yii;
use yii\helpers\Json;

class AdTrackApple extends AdTrack implements AdTrackInterface
{
    const DEV_CAMPAIGN_ID = 1234567890;
    const DEV_GROUP_ID = 1234567890;

    public function callback(string $event_type, $arg = 0)
    {
        return true;
    }

    public function getAdVendor(): int
    {
        return self::VENDOR_APPLE;
    }

    public static function newRecord(InstallLog $install_log, array $data)
    {
        // 通过 iAd 获取的归因数据在 2023-02-07 之后的归因标识 iad-attribution 均为 'false' 或错误
        // https://developer.apple.com/documentation/iad/setting_up_apple_search_ads_attribution
        if (array_key_exists('Version3.1', $data)) {
            return null;
        }
        // 通过 AdServices Framework 归因的数据（iOS 14.3 及其之后）
        if (!array_key_exists('token', $data)) {
            throw new Exception('参数错误');
        }

        $record = null;
        $attribution_data = self::getAdsAttributionData($data['token']);
        if (!$attribution_data) {
            Yii::error(sprintf('apple asa token is expired or invalid: %s', Json::encode($data)), __METHOD__);
            return $record;
        }
        if ($attribution_data['attribution']) {
            // 归因报文中广告计划 ID 及广告组 ID 会出现 1234567890 情形（苹果广告后台不存在此 ID，而只在开发过程中的示例报文中出现过）
            if (self::DEV_CAMPAIGN_ID === (int)$attribution_data['campaignId'] && self::DEV_GROUP_ID === (int)$attribution_data['adGroupId']) {
                Yii::error(sprintf('apple asa attribution data is invalid: %s', Json::encode($attribution_data)), __METHOD__);
                return $record;
            }
            $record = self::initiateRecord($install_log);
            $record->setAdsAttributionData($attribution_data);
        }

        return $record;
    }

    private static function initiateRecord(InstallLog $install_log)
    {
        $record = new AdTrackApple([
            'track_id' => '',
            'os' => Equipment::iOS,
            'idfa' => $install_log->adid,
            'ip' => $install_log->ip,
            'ua' => $install_log->user_agent,
            'buvid' => $install_log->buvid,
            'equip_id' => $install_log->equip_id,
            'converted' => self::CONVERTED,
            'creative_id' => '',
            'more' => [
                'idfa_md5' => md5(strtoupper($install_log->adid)),
                'stage' => self::CONVERT_STAGE_ACTIVATE,
                'activate_time' => $_SERVER['REQUEST_TIME'],
            ],
        ]);
        $record->setCurrentTableName(self::tableName());
        return $record;
    }

    /**
     * 设置通过 iAd 获取的归因数据
     * （适用于 iOS 4 及其之后，限于用户允许追踪的设备）
     * @deprecated 2023-02-07 之后，iad-attribution 均为 'false' 或错误
     * @see 使用 self::getAdsAttributionData 代替
     *
     * @link https://developer.apple.com/documentation/iad/setting_up_apple_search_ads_attribution
     * @param array $attribution_data
     */
    private function setIAdAttributionData(array $attribution_data)
    {
        $more = [];
        if (array_key_exists('iad-org-id', $attribution_data)) {
            $more['org_id'] = $attribution_data['iad-org-id'];
        }
        if (array_key_exists('iad-campaign-name', $attribution_data)) {
            $more['project_name'] = $attribution_data['iad-campaign-name'];
        }
        if (array_key_exists('iad-adgroup-name', $attribution_data)) {
            $more['group_name'] = $attribution_data['iad-adgroup-name'];
        }
        if (array_key_exists('iad-creativeset-name', $attribution_data)) {
            $more['creative_name'] = $attribution_data['iad-creativeset-name'];
        }
        if (array_key_exists('iad-org-name', $attribution_data)) {
            $more['org_name'] = $attribution_data['iad-org-name'];
        }
        if (array_key_exists('iad-keyword-id', $attribution_data)) {
            $more['keyword_id'] = (string)$attribution_data['iad-keyword-id'];
        }
        if (array_key_exists('iad-keyword', $attribution_data)) {
            $more['keyword_name'] = (string)$attribution_data['iad-keyword'];
        }
        if (array_key_exists('iad-keyword-matchtype', $attribution_data)) {
            $more['keyword_matchtype'] = $attribution_data['iad-keyword-matchtype'];
        }

        $client = AppleAdManagementClient::instance($attribution_data['iad-org-id'] ?? AppleAdManagementClient::getDefaultOrgId());
        if (array_key_exists('iad-campaign-id', $attribution_data)) {
            if (!array_key_exists('project_name', $more)) {
                $more['project_name'] = $client->getCampaignName($attribution_data['iad-campaign-id']);
            }
            $this->setAttribute('project_id', (string)$attribution_data['iad-campaign-id']);
        }
        if (array_key_exists('iad-adgroup-id', $attribution_data)) {
            if (!array_key_exists('group_name', $more) && $this->project_id) {
                $more['group_name'] = $client->getAdGroupName($this->project_id, $attribution_data['iad-adgroup-id']);
            }
            $this->setAttribute('group_id', (string)$attribution_data['iad-adgroup-id']);
        }
        if (array_key_exists('iad-creativeset-id', $attribution_data)) {
            if (!array_key_exists('creative_name', $more)) {
                $more['creative_name'] = $client->getCreativeSetName($attribution_data['iad-creativeset-id']);
            }
            $this->setAttribute('creative_id', (string)$attribution_data['iad-creativeset-id']);
        }
        if ($more) {
            if ($this->more) {
                $more = array_merge($this->more, $more);
            }
            $this->setAttribute('more', $more);
        }
        $this->setAttributes([
            'click_time' => array_key_exists('iad-click-date', $attribution_data)
                ? strtotime($attribution_data['iad-click-date']) * 1000
                : $_SERVER['REQUEST_TIME'] * 1000,
        ]);
    }

    /**
     * 通过 token 获取 AdServices Framework 归因数据
     *
     * @link https://developer.apple.com/documentation/adservices/aaattribution/3697093-attributiontoken
     * @param string $token
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @return array|null
     */
    private static function getAdsAttributionData(string $token)
    {
        // 状态码为 404 时：API 无法检索到请求的归因记录；token 超过 24 小时有效期也会返回 404；建议每个 token 请求 3 次，每次间隔 5 秒
        $retry = 3;
        $status_code = 0;
        while ($retry > 0) {
            $retry--;
            $attribution_data = Yii::$app->tools->requestRemoteWithProxy('https://api-adservices.apple.com/api/v1/',
                [], 'POST', $token, 0, ['Content-Type' => 'text/plain'], $status_code);
            if ($status_code === 404 && $retry > 0) {
                sleep(5);
                continue;
            }

            if ($status_code !== 404) {
                return $attribution_data;
            }
        }

        Yii::error(sprintf('apple asa get ads attribution data failed: token[%s]', $token), __METHOD__);
        return null;
    }

    /**
     * 设置通过 AdServices Framework 获取的归因数据
     * （适用于 iOS 14.3 及其之后）
     *
     * @link https://developer.apple.com/documentation/adservices/aaattribution/3697093-attributiontoken
     * @param array $attribution_data
     */
    private function setAdsAttributionData(array $attribution_data)
    {
        $project_id = (string)$attribution_data['campaignId'];
        $group_id = (string)$attribution_data['adGroupId'];
        $org_id = $attribution_data['orgId'];

        $client = AppleAdManagementClient::instance($org_id);
        $project_name = $client->getCampaignName($project_id);
        $group_name = $client->getAdGroupName($project_id, $group_id);

        $more = [
            'project_name' => $project_name,
            'group_name' => $group_name,
            'org_id' => $client->getOrgId(),
            'org_name' => $client->getOrgName(),
            'parent_org_id' => $client->getParentOrgId(),
        ];
        if (array_key_exists('creativeSetId', $attribution_data)) {
            $creative_id = (string)$attribution_data['creativeSetId'];
            $more['creative_name'] = $client->getCreativeSetName($creative_id);
            $this->setAttribute('creative_id', $creative_id);
        }
        if (array_key_exists('keywordId', $attribution_data)) {
            $keyword_id = (string)$attribution_data['keywordId'];
            $more['keyword_id'] = $keyword_id;
            $more['keyword_name'] = $client->getKeywordName($project_id, $group_id, $keyword_id);
        }
        if ($this->more) {
            $more = array_merge($this->more, $more);
        }
        $this->setAttributes([
            'more' => $more,
            'project_id' => $project_id,
            'group_id' => $group_id,
            'click_time' => array_key_exists('clickDate', $attribution_data)
                ? strtotime($attribution_data['clickDate']) * 1000
                : $_SERVER['REQUEST_TIME'] * 1000,
        ]);
    }

}
