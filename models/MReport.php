<?php

namespace app\models;

/**
 * This is the model class for table "m_report".
 *
 * @property integer $id
 * @property integer $target
 * @property integer $source_id
 * @property integer $user_id
 * @property integer $reason
 * @property string $content
 * @property integer $status
 * @property integer $serious
 * @property integer $create_time
 */
class MReport extends ActiveRecord
{
    // 举报元素类型
    const TARGET_SOUND = 1;
    const TARGET_IMAGE = 2;
    const TARGET_DANMAKU = 3;
    const TARGET_LIVE_USER = 4;  // 直播间用户举报（直播间点击排行榜用户头像举报或直播间弹幕举报等）
    const TARGET_LIVE = 5;
    const TARGET_COMMENT = 6;
    const TARGET_SUB_COMMENT = 7;
    const TARGET_PRIVATE_MESSAGE = 8;
    const TARGET_ALBUM = 9;
    const TARGET_NODE_DANMAKU = 10;  // 互动剧节点弹幕
    const TARGET_USER_INFO = 11;  // 个人信息

    // 举报原因
    const REASON_AD = 1;
    const REASON_PORN = 2;
    const REASON_COPYRIGHT = 3;
    const REASON_POLITICS = 4;
    const REASON_OTHER = 5;

    // 处理状态
    const STATUS_UNHANDLE = 0;
    const STATUS_HANDLED = 1;

    public static $target = [
        self::TARGET_SOUND => '单音',
        self::TARGET_IMAGE => '图片',
        self::TARGET_DANMAKU => '弹幕',
        self::TARGET_LIVE_USER => '直播间用户',
        self::TARGET_LIVE => '直播',
        self::TARGET_COMMENT => '父评论',
        self::TARGET_SUB_COMMENT => '子评论',
        self::TARGET_PRIVATE_MESSAGE => '私信',
        self::TARGET_ALBUM => '音单',
        self::TARGET_NODE_DANMAKU => '互动剧节点弹幕',
        self::TARGET_USER_INFO => '个人信息',
    ];
    public static $reason = [
        self::REASON_AD => '广告',
        self::REASON_PORN => '色情',
        self::REASON_COPYRIGHT => '版权',
        self::REASON_POLITICS => '政治',
        self::REASON_OTHER => '其他',
    ];
    public static $status = [
        self::STATUS_UNHANDLE => '未处理',
        self::STATUS_HANDLED => '已处理'
    ];

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_report';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['target', 'source_id', 'user_id', 'reason'], 'required'],
            [['target', 'source_id', 'user_id', 'reason', 'status', 'serious', 'create_time'], 'integer'],
            [['content'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'target' => '举报元素类型',
            'source_id' => '举报资源的 ID',
            'user_id' => '举报人 ID',
            'reason' => '举报原因',
            'content' => '举报详细原因',
            'status' => '是否受理',
            'serious' => '是否严重违规',
            'create_time' => '举报时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        return true;
    }
}
