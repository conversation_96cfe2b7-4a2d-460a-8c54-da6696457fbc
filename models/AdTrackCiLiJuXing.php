<?php

namespace app\models;

use Exception;
use Yii;

class AdTrackCiLiJuXing extends AdTrack implements AdTrackInterface
{
    const CALLBACK_EVENT_TYPE_MAP = [
        self::CALLBACK_EVENT_ACTIVATE => null,
        self::CALLBACK_EVENT_ONE_DAY_RETENTION => null,
        self::CALLBACK_EVENT_PAY => self::CALLBACK_EVENT_TYPE_ACTIVATE,  // 用户付费时回传激活行为
        self::CALLBACK_EVENT_REGISTER => null,
        self::CALLBACK_EVENT_KEY_ACTION => null,
        self::CALLBACK_EVENT_TRANSACTION => null,  // 无消费 / 充值关键行为
    ];

    // 激活
    const CALLBACK_EVENT_TYPE_ACTIVATE = 1;
    // 付费
    const CALLBACK_EVENT_TYPE_PAY = 3;
    // 次日留存
    const CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION = 7;
    // 注册
    const CALLBACK_EVENT_TYPE_REGISTER = 2;

    /**
     * 转化事件回调
     *
     * @link https://docs.qq.com/doc/DU3N4c3BDcVhNZlNC
     *
     * @param string $event_type 事件类型
     * @param mixed $arg 付费金额（单位：元），或关键行为参数
     */
    public function callback(string $event_type, $arg = 0)
    {
        try {
            if (!array_key_exists($event_type, self::CALLBACK_EVENT_TYPE_MAP)) {
                throw new Exception('磁力聚星广告点击回传事件错误：' . $event_type);
            }
            $event = self::CALLBACK_EVENT_TYPE_MAP[$event_type];
            if (is_null($event)) {
                return true;
            }
            $data = Yii::$app->tools->requestRemote($this->getCallbackUrl($event, $_SERVER['REQUEST_TIME'] * 1000, $arg));
            if (!($data && $data['result'] === 1)) {
                throw new Exception(sprintf('磁力聚星广告点击回传失败：code[%d], msg[%s]', $data['result'], $data['error_msg'] ?? ''));
            }
            return true;
        } catch (Exception $e) {
            Yii::error('cilijuxing ad error: ' . $e->getMessage(), __METHOD__);
        }
        return false;
    }

    /**
     * 获取回传的 URL
     *
     * @param int $callback_event 事件类型
     * @param int $event_time 转化时间
     * @param mixed $arg 付费金额（单位：元），或关键行为参数
     * @return string
     */
    private function getCallbackUrl(int $callback_event, int $event_time, $arg = 0): string
    {
        // track_id 形式：http://ad.partner.gifshow.com/track/activate?callback=xxxxxxxxxx
        return sprintf('%s&event_type=%d&event_time=%d', $this->track_id, $callback_event, $event_time);
    }

    /**
     * @inheritdoc
     */
    protected function getAdNameToParse()
    {
        return ($this->more && array_key_exists('tag', $this->more)) ? $this->more['tag'] : '';
    }
}
