<?php

namespace app\models;

use app\components\util\Equipment;
use app\components\util\Go;
use app\components\util\HttpExceptionWithData;
use app\components\util\Image;
use app\components\util\MUtils;
use app\components\util\SSOClient;
use app\forms\LoginForm;
use Exception;
use GuzzleHttp\Client;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\web\HttpException;
use yii\helpers\Json;

/**
 * This is the model class for table "mowangskuser".
 *
 * @property integer $id
 * @property integer $confirm
 * @property string $username
 * @property string $cip
 * @property string $uip
 * @property integer $ctime
 * @property integer $utime
 * @property string $quanxian
 * @property integer $teamid
 * @property string $teamname
 * @property integer $ban
 * @property integer $ustr
 * @property integer $uint
 * @property integer $uagi
 * @property integer $point
 * @property string $nowsound
 * @property integer $iconid
 * @property string $iconurl
 * @property string $iconcolor
 * @property string $subtitle
 * @property integer $boardiconid
 * @property string $boardiconurl
 * @property string $boardiconcolor
 * @property integer $coverid
 * @property string $coverurl
 * @property integer $isnewmsg
 * @property string $userintro
 * @property integer $userintro_audio
 * @property integer $likenum
 * @property integer $fansnum
 * @property integer $follownum
 * @property integer $soundnum
 * @property integer $albumnum
 * @property integer $imagenum
 * @property integer $feednum
 * @property integer $soundnumchecked
 * @property integer $imagenumchecked
 * @property integer $mlevel
 * @property string $avatar
 * @property integer $icontype
 * @property string $soundurl
 * @property string $soundurl_64
 * @property string $soundurl_128
 *
 * @property boolean $is_official 是否为官方账号
 * @property bool $_is_show_ban
 * @property array|null $vip_info 点播会员信息
 */
class Mowangskuser extends ActiveRecord
{
    // 黑 V 认证标识值
    const CONFIRM_BLACK_VIP = 1;
    // 金 V 认证标识值
    const CONFIRM_GOLDEN_VIP = 2;
    // 蓝 V 认证标识值
    const CONFIRM_BLUE_VIP = 3;
    // 加 V 标识位偏移量及掩码值
    const VIP_BIT_OFFSET_MASK = [24, 0xffff];
    // 通过实名认证：开直播
    const CONFIRM_CERTIFICATED = 2;

    const SCENARIO_INFO = 'info';
    const SCENARIO_PASSWORD = 'password';
    const SCENARIO_USERNAME = 'update_name';
    const SCENARIO_USERINTRO = 'update_intro';
    const SCENARIO_RETRYREGIST = 'retry_regist';
    const SCENARIO_REGIST = 'regist';
    const SCENARIO_THIRD_REGIST = 'third_regist';
    const SCENARIO_FASTREGIST = 'fastregist';

    // 个性签名最大字符长度
    const MAX_INTRO_LENGTH = 70;

    // 头像类型，0：二次元头像；1：三次元头像
    const TYPE_CARTOON_ICON = 0;
    const TYPE_REALITY_ICON = 1;

    const USER_ID_LIMIT = 3790000;

    const ICON_NAME_DEFAULT = 'icon01.png';
    // 用户每等级能创建的音单数目
    const ALBUM_NUM_OF_ONE_LEVEL = 100;

    // 已答题用户进行位运算的标识
    const CONFIRM_PASS_EXAMINATION = 1;
    // 用户设置隐私
    const CONFIRM_PRIVACY = 1 << 8;
    // 用户永久禁止登录
    const CONFIRM_BAN_LOGIN = 1 << 7;
    // 用户设置青少年模式
    const CONFIRM_TEENAGER = 1 << 9;
    // 用户禁止充值及消费
    const CONFIRM_BAN_TOPUP_AND_CONSUME = 1 << 10;
    // 用户已注销
    const CONFIRM_DELETED = 1 << 12;
    // 用户为猫耳官方账号
    const CONFIRM_OFFICIAL = 1 << 14;

    // 已注销账号的用户名
    const DELETED_USERNAME = '账号已注销';

    // username 长度最值、正则表达式
    const USERNAME_MAX_LENGTH = 20;
    const USERNAME_MIN_LENGTH = 4;
    const REG_USERNAME =
        '/[^\x{2E80}-\x{2FDF}\x{3040}-\x{309F}\x{30A0}-\x{312F}\x{31C0}-\x{4DBF}\x{4E00}-\x{9FFF}A-Za-z0-9_]/u';

    // 密码长度最值
    const PASSWORD_MAX_LENGTH = 16;
    const PASSWORD_MIN_LENGTH = 6;

    // 青少年模式
    const TEENAGER_MODE_ENABLED = 1;
    const TEENAGER_MODE_CLOSE = 0;

    // 官方账号“M娘”用户 ID
    const OFFICIAL_M_GIRL_USER_ID = 26;
    // 官方账号“猫耳FM客服娘”用户 ID
    const OFFICIAL_CUSTOMER_SERVICE_STAFF_USER_ID = 3162222;

    const BAN_LOGIN_MESSAGE = "对不起，由于您的账号违反了用户协议\n，您已被禁止登录！详情请联系客服\nQQ：********** 或 **********，\n也可以加入 QQ 群：*********";
    const BAN_LOGIN_NEW_MESSAGE = '您的账号目前处于禁止登录状态，暂不能进行此操作，详情可联系客服进行了解。';
    // Android < 5.6.5，iOS < 4.7.8 黑产手机号禁止注册提示语
    const HIGH_RISK_ACCOUNT_OLD_MESSAGE = "您的注册邮箱或手机号处于高危状态，\n请联系客服 QQ：********** 或 **********，也可以加入 QQ 群：*********，协助注册";
    // WORKAROUND: 这两个 CODE 目前在此 model 中要用到，先放在这里处理方便使用
    // 全局弹窗提示错误 code
    const PROMPT_MODAL_CODE = *********;
    // 弹出的框有联系客服按钮的 code
    const PROMPT_FEEDBACK_MODAL_CODE = *********;

    public $password;

    public $avatar_file;
    public $iconurl2;
    public $avatar2;
    public $boardiconurl3;
    public $boardiconurl2;
    public $coverurl2;
    public $coverurl_new2;
    public $dark_coverurl;  // 黑夜模式用户封面图片
    public $duration;  // 头像音时长

    public $authenticated;
    public $hotSound;
    public $cvid;
    // 头像音地址
    public $soundurl;
    // 头像音原音质地址
    public $soundurl_64;
    // 头像音 128k 地址
    public $soundurl_128;

    public $followed;
    public $drama_bought_count;
    public $balance;
    // 青少年模式状态 1 开启 0 关闭
    public $teenager_status;
    // 注册所用的账号类型，默认为 -1：未知的账号类型
    public $operate_type = -1;

    // 是否为官方账号
    public $is_official;
    // 点播会员信息
    public $vip_info;

    /**
     * WORKAROUND: beforeSave 操作时当作参数传给 sso 注册，后续字段删了之后改回使用 email，mobile，region
     */
    public $email2;
    public $mobile2;
    public $region2;

    /**
     * @var bool 是否展示被封禁信息
     */
    private $_is_show_ban;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'mowangskuser';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['confirm', 'ctime', 'utime', 'teamid', 'ban', 'ustr', 'uint', 'uagi', 'point', 'nowsound', 'iconid', 'boardiconid', 'coverid', 'isnewmsg', 'userintro_audio', 'likenum', 'fansnum', 'follownum', 'soundnum', 'albumnum', 'imagenum', 'feednum', 'soundnumchecked', 'imagenumchecked', 'mlevel', 'icontype'], 'integer'],
            ['username', 'required'],
            [['userintro'], 'string', 'max' => self::MAX_INTRO_LENGTH, 'on' => self::SCENARIO_USERINTRO],
            [
                'password',
                'string',
                'min' => self::PASSWORD_MIN_LENGTH,
                'max' => self::PASSWORD_MAX_LENGTH,
                'on' => self::SCENARIO_PASSWORD,
                'tooLong' => Yii::t('app/error', 'The password should be no more than {max_length} characters',
                    ['max_length' => Mowangskuser::PASSWORD_MAX_LENGTH]),
                'tooShort' => Yii::t('app/error', 'The password should be no less than {min_length} characters',
                    ['min_length' => Mowangskuser::PASSWORD_MIN_LENGTH]),
            ],
            [['teamname'], 'string', 'max' => 20],
            [['iconcolor', 'boardiconcolor'], 'string', 'max' => 50],
            [['cip', 'uip'], 'string', 'max' => MUtils2::IP_MAX_LENGTH],
            [['quanxian'], 'string', 'max' => 5],
            [['iconurl'], 'string', 'max' => 240],
            [['subtitle'], 'string', 'max' => 10],
            [['boardiconurl', 'coverurl', 'coverurl_new'], 'string', 'max' => 60],
            [['avatar'], 'string', 'max' => 100],
            ['username', 'checkName', 'on' => self::SCENARIO_USERNAME],
            ['userintro', 'checkIntro', 'on' => self::SCENARIO_USERINTRO],
            ['avatar_file', 'checkAvatarFile'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'confirm' => '二进制数据 位1代表是否答题 位2代表是否通过实名认证(certification) 位3代表是否加V(authentication) 位6(32)代表金V',
            'username' => 'Username',
            'cip' => 'Cip',
            'uip' => 'Uip',
            'ctime' => 'Ctime',
            'utime' => 'Utime',
            'quanxian' => 'Quanxian',
            'teamid' => 'Teamid',
            'teamname' => 'Teamname',
            'ban' => 'Ban',
            'ustr' => 'Ustr',
            'uint' => 'Uint',
            'uagi' => 'Uagi',
            'point' => 'Point',
            'nowsound' => '记录用户当前M音时长',
            'iconid' => 'Iconid',
            'iconurl' => 'Iconurl',
            'iconcolor' => 'Iconcolor',
            'subtitle' => 'Subtitle',
            'boardiconid' => 'Boardiconid',
            'boardiconurl' => 'Boardiconurl',
            'boardiconcolor' => 'Boardiconcolor',
            'coverid' => '封面图 ID',
            'coverurl' => '封面图',
            'isnewmsg' => '是否有新消息',
            'userintro' => '个性签名',
            'userintro_audio' => '头像音 ID',
            'likenum' => '点赞数',
            'fansnum' => '粉丝数',
            'follownum' => '关注数',
            'soundnum' => '个人语音数',
            'albumnum' => '个人专辑数',
            'imagenum' => '个人图片数',
            'feednum' => 'feed流未读信息',
            'soundnumchecked' => '审核通过的声音',
            'imagenumchecked' => '审核通过的图片',
            'mlevel' => '用户当前等级',
            'avatar' => '三次元头像',
            'icontype' => 'Icontype',
        ];
    }

    /**
     * @inheritdoc
     */
    public function hasAttribute($name)
    {
        // WORKAROUND: 保存老的协议音频地址为了后续可以获取音频 upos 签名地址
        if (in_array($name, ['soundurl_64', 'soundurl_128'])) {
            return true;
        }
        return parent::hasAttribute($name);
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios[self::SCENARIO_INFO] = ['username', 'userintro', 'userintro_audio', 'avatar_file', 'avatar', 'icontype', 'coverid'];
        $scenarios[self::SCENARIO_PASSWORD] = ['password'];
        $scenarios[self::SCENARIO_USERNAME] = ['username', 'avatar_file', 'avatar'];
        $scenarios[self::SCENARIO_USERINTRO] = ['userintro'];
        $scenarios[self::SCENARIO_RETRYREGIST] = [];
        $scenarios[self::SCENARIO_REGIST] = ['avatar_file'];
        $scenarios[self::SCENARIO_THIRD_REGIST] = ['avatar_file'];

        return $scenarios;
    }

    /**
     * 验证昵称是否符合规则
     *
     * @param string $attribute 验证的属性
     */
    public function checkName($attribute)
    {
        if ($result = self::checkUsername($this->$attribute)) {
            $this->addError($attribute, $result);
        }
    }

    /**
     * 验证个性签名是否违规
     *
     * @param string $attribute 验证的属性
     * @param mixed[] $params 其他自定义参数
     */
    public function checkIntro($attribute)
    {
        // 检测个性签名违规情况，但不包括因广告违规的情况
        $result = Yii::$app->go->checkText($this->$attribute, Go::SCENE_USER_INFO, true);
        if ($result && !current($result)['pass']) {
            $this->addError($attribute, '个性签名中含有违规词汇喔~');
        }
    }

    /**
     * @param $attribute
     * @return bool
     * @throws \Exception
     */
    public function checkAvatarFile($attribute)
    {
        // 需要执行 validate 检查上传的头像文件是否合法
        if ($this->$attribute) {
            $extension = MUtils::getFileExtension($this->$attribute);
            if (strtoupper($extension) === Image::FORMAT_GIF) {
                // 若图片为 gif 格式，则转换为 jpg 格式图片
                $this->$attribute = Image::convertImageFormat($this->$attribute, Image::FORMAT_JPEG);
                $extension = 'jpg';
            }
            $scenario = $this->getScenario();
            if (!in_array($extension, ['jpg', 'png'])) {
                $this->avatar_file = null;
                if (in_array($scenario, [self::SCENARIO_REGIST, self::SCENARIO_THIRD_REGIST])) {
                    // 若头像是不支持的图片格式，注册仍能通过但使用默认的猫图
                    return true;
                }
                $this->addError('avatar', Yii::t('app/error', 'Avatar only supports JPG and PNG formats'));
                return false;
            }
            if (!$save_path = MUtils::generateFilePath($this->$attribute)) {
                $this->avatar_file = null;
                if (in_array($scenario, [self::SCENARIO_REGIST, self::SCENARIO_THIRD_REGIST])) {
                    // 若头像文件路径错误，注册仍能通过但使用默认的猫图
                    return true;
                }
                $this->addError('avatar', Yii::t('app/error', 'File does not exist'));
                return false;
            }
            $storage_path = 'avatars/' . $save_path;
            $this->$attribute = MImage::editCoverImage($this->$attribute);
            Yii::$app->storage->upload($this->$attribute, $storage_path, true);
            $img_url = Yii::$app->params['avatarUrl'] . $save_path;
            $result = Yii::$app->go->checkImage($img_url, Go::SCENE_USER_INFO);
            if ($result && !$result['pass']) {
                $this->avatar_file = null;
                // TODO: 若检测成功并且图片违规，删除该图片并提示用户头像违规，检测失败的情况暂时不处理
                Yii::$app->storage->deleteFile($storage_path);
                if (self::SCENARIO_THIRD_REGIST === $scenario) {
                    // 若为第三方账号注册时其头像违规不抛出异常，注册仍能通过但使用默认的猫图
                    // （客户端获取的第三方账号的图只有一种尺寸，故在服务端进行获取）
                    return true;
                }
                // 普通注册或修改头像时，若上传的是违规图则进行报错，让用户更换图片
                $this->addError('avatar', Yii::t('app/error', 'Avatar violation, please upload other avatar'));
                return false;
            }
            // 图片不违规（或检测失败），则赋值
            if (in_array($scenario, [self::SCENARIO_REGIST, self::SCENARIO_THIRD_REGIST])) {
                // 注册时给 iconurl 赋值
                $this->iconurl = $img_url;
            }
            $this->avatar = $save_path;
            $this->icontype = self::TYPE_REALITY_ICON;
        }
        return true;
    }

    public function afterFind()
    {
        parent::afterFind();
        self::setExtraInfo($this);
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        if (self::SCENARIO_FASTREGIST === $this->getScenario()) {
            return true;
        }
        $nowstamp = $_SERVER['REQUEST_TIME'];
        $user_ip = Yii::$app->request->userIP;
        if ($insert) {
            $this->ctime = $this->ctime ?: $nowstamp;
            if (self::SCENARIO_RETRYREGIST !== $this->getScenario()) {
                // 不是重新注册 mowangskuser 表时，使用 SSO 注册返回的用户信息
                $resp = Yii::$app->sso->regist(array_merge($this->toArray(), ['email' => $this->email2,
                    'mobile' => $this->mobile2, 'region' => $this->region2]));
                $user = $resp['user'];
                $this->id = $user['user_id'];
                $this->iconid = $user['iconid'];
                $this->iconurl = $user['iconurl'];
                $this->iconcolor = $user['iconcolor'];
                $this->ctime = $user['ctime'];
            }
            $this->cip = $user_ip;
            $this->quanxian = 'B';
            $this->subtitle = '';
            $this->boardiconid = 0;
            $this->boardiconurl = '';
            $this->boardiconcolor = '';
        } else {
            $dirty_attributes = $this->getDirtyAttributes();
            if (array_key_exists('username', $dirty_attributes)) {
                if (!$this->changeUsername()) return false;
            }

            if (array_key_exists('coverid', $dirty_attributes)) {
                if (!$this->changeCover()) return false;
            }

            if (array_key_exists('userintro_audio', $dirty_attributes)) {
                if (!$this->changeAudio()) return false;
            }
            if ($this->getErrors()) {
                return false;
            }
            // REVIEW: 需要确认一下此处为什么没有错误要改成空字符串
            $this->iconurl = '';
        }
        $this->utime = $nowstamp;
        $this->uip = $user_ip;
        return true;
    }

    public function afterSave($insert, $changed_attributes)
    {
        if (!$insert) {
            $token = '';
            $update_fields = [];
            // 若属性有更新，则对 SSO 上的信息进行更新
            if ($changed_attributes) {
                foreach ($changed_attributes as $key => $value) {
                    if (in_array($key, SSOClient::USER_FIELDS) || $key === 'avatar') {
                        // 若修改的属性或其关联需要在 SSO 上同步更新，则获取新值到更新字段值数组 $update_fields 中
                        $value = $this->getAttribute($key);
                        switch ($key) {
                            case 'avatar':
                                $update_fields['iconid'] = 0;
                                $update_fields['iconurl'] = Yii::$app->params['avatarUrl'] . $value;
                                $update_fields['iconcolor'] = $this->boardiconcolor;
                                break;
                            case 'mobile':
                            case 'region':
                                $update_fields['mobile'] = (int)$this->mobile2;
                                $update_fields['region'] = (int)$this->region2;
                                break;
                            case 'teamid':
                            case 'confirm':
                                $update_fields[$key] = (int)$value;
                                break;
                            default:
                                $update_fields[$key] = $value;
                        }
                    }
                }
            }
            if ($this->password) {
                // 用户更新自己的密码时，需过期使用旧密码的 Token
                $token = ($this->id === Yii::$app->user->id) ? Yii::$app->user->identity->token : '';
                $update_fields['password'] = $this->password;
            }
            if ($update_fields) {
                // 更新 SSO 数据
                Yii::$app->sso->update($this->id, $update_fields, $token);
                Yii::$app->user->update($this);
            }
        }
        parent::afterSave($insert, $changed_attributes);
    }

    /**
     * 获取用户信息
     *
     * @param int $user_id 用户 ID
     * @param boolean $is_new_user 是否是新用户 false：否；true：是
     * @return self 用户信息
     * @throws
     */
    public static function getPersonInfo($user_id, $is_new_user = false)
    {
        $my_id = Yii::$app->user->id;
        $is_me = $user_id === $my_id;

        $soundnum = $is_me ? 't.soundnum' : 't.soundnumchecked soundnum';
        $select = 't.id, t.username, t.boardiconurl, t.iconurl, t.userintro,
            t.coverurl, t.coverurl_new, t.avatar, t.icontype, t.albumnum, t.follownum, t.fansnum,
            t1.soundurl_64, t1.soundurl_128, t.userintro_audio,
            t1.duration, t.confirm, ' . $soundnum;
        if ($is_me) {
            $select .= ', t.point';
        }
        $transaction = null;
        // 新用户开启事务，在主库中查询，避免数据库只读同步的问题
        if ($is_new_user) {
            $transaction = Yii::$app->db->beginTransaction();
        }
        try {
            $user = self::find()->alias('t')->select($select)
                ->leftJoin(MSound::tableName() . ' AS t1',
                    't.userintro_audio = t1.id AND t1.checked = ' . MSound::CHECKED_PASS)
                ->where('t.id = :id', [':id' => $user_id])
                ->one();
            if ($transaction) {
                $transaction->commit();
            }
        } catch (Exception $e) {
            if ($transaction) {
                $transaction->rollback();
            }
            throw $e;
        }
        if (!$user) {
            throw new HttpException(404, Yii::t('app/error', 'User does not exist'));
        }
        $user_cover_url = MUserCover::getUserCoverByUserId($user_id, $is_me, $user->coverurl_new);
        $res = MUserCover::getDarkCoverAndPositionByCoverUrl($user_cover_url);
        if ($res['dark_cover_url']) {
            $user->dark_coverurl = $res['dark_cover_url'];
        }
        $user->coverid = $res['cover_id'];

        // iOS 4.6.9 和 安卓 5.5.8 及以上版本封面图统一使用 coverurl 字段
        $user->coverurl = $user_cover_url;

        // WORKAROUND：iOS 4.6.9 和 安卓 5.5.8 以下版本封面图使用 coverurl_new2 字段
        if (Equipment::isAppOlderThan('4.6.9', '5.5.8')) {
            $user->coverurl_new2 = $user_cover_url;
        }

        $user->hotSound = self::getAuthenticated($user->confirm);
        $user->cvid = Mowangsksoundseiy::findOne(['mid' => $user_id])['id'] ?? 0;
        $user->duration = (int)$user->duration;

        if (!$user->soundurl_64) {
            // 若未设置头像音，则使用默认头像音
            $userintro_audio_key = MUtils::generateCacheKey(KEY_USERINTRO_AUDIO);
            $memcache = Yii::$app->memcache;
            $return = null;
            if ($sound_info = $memcache->get($userintro_audio_key)) {
                $return = Json::decode($sound_info);
            } else {
                $sound_info = MSound::find()
                    ->select('id, soundurl_64, soundurl_128, duration')
                    ->where(['id' => Yii::$app->params['defaultAvatarSoundId'], 'checked' => MSound::CHECKED_PASS])
                    ->asArray()
                    ->one();
                if ($sound_info) {
                    $return = [
                        'soundurl_64' => $sound_info['soundurl_64'],
                        'soundurl_128' => $sound_info['soundurl_128'],
                        'duration' => (int)$sound_info['duration'],
                    ];
                    $memcache->set($userintro_audio_key, Json::encode($return), ONE_DAY);
                } else {
                    Yii::error('默认头像音不存在，ID：' . Yii::$app->params['defaultAvatarSoundId'], __METHOD__);
                }
            }
            if ($return) {
                $user->soundurl_64 = $return['soundurl_64'];
                $user->soundurl_128 = $return['soundurl_128'];
                $user->duration = $return['duration'];
            } else {
                $user->soundurl_64 = $user->soundurl_128 = '';
                $user->duration = 0;
            }
        }
        // WORKAROUND: 保存老的协议音频地址为了后续可以获取音频 upos 签名地址
        $user->setOldAttribute('soundurl_64', $user->soundurl_64);
        $user->setOldAttribute('soundurl_128', $user->soundurl_128);

        if ($user->soundurl_64) {
            $sounds = [
                'soundurl_64' => $user->soundurl_64,
                'soundurl_128' => $user->soundurl_128,
            ];
            // 将音频地址转化为完整 Url 地址
            MSound::getSoundUrl($sounds);
            foreach ($sounds as $key => $sound_url) {
                $user->$key = $sound_url;
            }
        }
        $user->soundurl = $user->soundurl_64;

        if ($is_me) {
            $user->drama_bought_count = TransactionLog::getDramaBoughtCount($user_id);
            $user->balance = Balance::getByPk($user_id)->getTotalBalance();
        }

        // 青少年模式状态
        $user->teenager_status = self::isTeenagerModeEnabled($user->id, $user->confirm);
        // WORKAROUND：老版本不返回用户青少年状态
        if (Equipment::isAppOlderThan('4.4.9', '5.3.9')) {
            $user->teenager_status = null;
        }
        unset($user->coverurl_new);
        // 获取用户是否为会员
        $user->vip_info = MUserVip::getUserVipInfo($user_id);
        return $user;
    }

    // 获取加 V 认证标识（供前端使用）：1 代表黑 V，2 代表金 V，3 代表蓝 V
    public static function getAuthenticated($confirm = 0)
    {
        $v = ($confirm >> self::VIP_BIT_OFFSET_MASK[0]) & self::VIP_BIT_OFFSET_MASK[1];
        if (self::CONFIRM_BLUE_VIP === $v && Equipment::isAppOlderThan('4.3.1', '5.2.3')) {
            // 安卓 App 版本小于 5.2.3，iOS App 版本小于 4.3.1 时，蓝 V 账号仍显示成金 V
            return self::CONFIRM_GOLDEN_VIP;
        } elseif (!in_array($v, [self::CONFIRM_BLACK_VIP, self::CONFIRM_GOLDEN_VIP, self::CONFIRM_BLUE_VIP]) &&
                Equipment::isAppOlderThan(null, '5.2.3')) {
            // 安卓 App 版本小于 5.2.3 时，计算出的值除黑 V，金 V，蓝 V 以外的其他值都返回 0
            return 0;
        }
        return $v;
    }

    /**
     * 用户添加权限或加 V 标识
     *
     * @param integer $confirm_level 权限或加 V 标识
     * @param boolean $is_v 是否为 V 标识
     */
    public function authorizeConfirm(int $confirm_level, bool $is_v = false)
    {
        if ($is_v) {
            $this->revokeConfirm($confirm_level, true);
            $this->confirm |= ($confirm_level << self::VIP_BIT_OFFSET_MASK[0]);
        } else {
            $this->confirm |= $confirm_level;
        }
    }

    /**
     * 撤销用户授权或加 V 标识
     *
     * @param integer $confirm_level 权限或加 V 标识
     * @param boolean $is_v 是否为 V 标识
     */
    public function revokeConfirm(int $confirm_level, bool $is_v = false)
    {
        if ($is_v) {
            $confirm_level = self::VIP_BIT_OFFSET_MASK[1] << self::VIP_BIT_OFFSET_MASK[0];
        }
        $this->confirm = MUtils::bitUnset($this->confirm, $confirm_level);
    }

    public static function getAccounts(int $user_id)
    {
        $accounts = Yii::$app->sso->getUser($user_id);
        if ($accounts['email']) {
            $accounts['email'] = MUtils::mosaicString($accounts['email'], MUtils::MOSAIC_EMAIL);
        }
        if ($accounts['mobile']) {
            $accounts['mobile'] = MUtils::mosaicString($accounts['mobile'], MUtils::MOSAIC_MOBILE);
        }
        return $accounts;
    }

    /**
     * 搜索 UP
     *
     * @param string|integer $s 关键词
     * @param integer $page 第几页
     * @param integer $page_size 每页个数
     * @param string $sort 排序规则（可选值："user"）
     * @return array
     */
    public static function getSearch($s, int $page = 1, int $page_size = 8, $sort = 'user')
    {
        $results = Yii::$app->go->search($s, Discovery::SEARCH_USER, $page, $page_size, ['scenario' => Go::SCENARIO_MAIN_SEARCH]);

        $results['Datas'] = array_map(function ($data) {

            $data['soundnum'] = $data['soundnumchecked'];

            return $data;
        }, $results['Datas']);

        return $results;
    }

    private function changeUsername(): bool
    {
        if (Yii::$app->equip->isFromMissEvanApp()) {
            if ($this->point < PointDetailLog::POINT_CONSUME_CHANGE_USERNAME) {
                $this->addError('username',
                    sprintf('修改昵称需要 %d 小鱼干', PointDetailLog::POINT_CONSUME_CHANGE_USERNAME));
                return false;
            }

            $this->point -= PointDetailLog::POINT_CONSUME_CHANGE_USERNAME;
            PointDetailLog::addLog(-PointDetailLog::POINT_CONSUME_CHANGE_USERNAME,
                PointDetailLog::TYPE_UPDATE_USER_INFO, $this->id);
        }

        // 加锁
        $lock = Yii::$app->redis->generateKey(CHANGE_ACCOUNT, $this->id);
        if (Yii::$app->redis->lock($lock, ONE_MINUTE)) {
            try {
                if (self::find()->where(['username' => $this->username])->exists()) {
                    $this->addError('username',
                        Yii::t('app/error', 'username has been occupied'));
                    return false;
                }
                $RELATED_CLASS = [MAlbum::class, MSound::class, MImage::class, Live::class];
                foreach ($RELATED_CLASS as $class) {
                    $class::updateAll(['username' => $this->username], 'user_id = :user_id', [':user_id' => $this->id]);
                }
                return true;
            } finally {
                // 解锁
                Yii::$app->redis->unlock($lock);
            }
        } else {
            throw new HttpException(400,
                Yii::t('app/error', 'The operation is too frequent. Please try again later'));
        }
    }

    private function changeCover(): bool
    {
        $cover = MImage::findOne(['id' => $this->coverid, 'checked' => 1, 'catalog_id' => 30]);
        if ($cover) {
            if (!UserCover::find()
                ->where(['user_id' => $this->id, 'cover_id' => $this->coverid])
                ->exists()
            ) {
                if ($this->point < PointDetailLog::POINT_CONSUME_CHANGE_COVERURL) {
                    $this->addError('point', '修改封面图需要 ' . PointDetailLog::POINT_CONSUME_CHANGE_COVERURL . ' 小鱼干');
                    return false;
                } else {
                    $user_cover = new UserCover();
                    $user_cover->user_id = $this->id;
                    $user_cover->cover_id = $this->coverid;
                    $user_cover->save();
                }
                $this->point -= PointDetailLog::POINT_CONSUME_CHANGE_COVERURL;
                PointDetailLog::addLog(-PointDetailLog::POINT_CONSUME_CHANGE_COVERURL,
                    PointDetailLog::TYPE_UPDATE_USER_INFO, $this->id);
            }
            $this->coverurl = $cover->save_name;
            return true;
        } else {
            $this->addError('cover', '该封面图不存在');
            return false;
        }
    }

    private function changeAudio(): bool
    {
        $this->userintro_audio = (int)$this->userintro_audio;
        if (!$sound = MSound::findOne(['id' => $this->userintro_audio, 'checked' => 1])) {
            $this->addError('userintro_audio', '音频文件不存在');
            return false;
        } elseif ($sound->duration > 11000) {
            $this->addError('userintro_audio', '音频文件过长');
            return false;
        }
        return true;
    }

    public static function changeFmUserStatus($user_id)
    {

        $client = new Client();
        $data = [
            'event' => 'updated',
            'user_id' => (int)$user_id,
        ];

        $data = base64_encode(Json::encode($data));
        $timestamp = time();
        $sign = hash_hmac('sha256', "$data $timestamp", MISSEVAN_PRIVATE_KEY);

        $body = "$data $sign $timestamp";
        $url = 'https://fm.missevan.com/api/user/notify';

        try {
            $res = $client->request('POST', $url, [
                'headers' => [
                    'Content-Type' => 'text/plain'
                ],
                'body' => $body,
            ]);

            $body = $res->getBody();
            $response = json_decode($body->getContents());
            if (isset($response->user)) {
                if (($response->user->code == 200) && ($response->user->ok == 1)) {
                    return true;
                }
                return false;
            }
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取打赏用户的 ID，昵称，头像
     *
     * @param int|array $user_ids
     * @return array
     */
    public static function getRewardUsers($user_ids)
    {
        $avatars = self::find()
            ->select('id, username, icontype, iconurl, avatar, boardiconurl')
            ->where(['id' => $user_ids])->all();
        return array_map(function ($item) {
            return [
                'id' => $item['id'],
                'username' => $item['username'],
                'avatar' => $item['iconurl'],
            ];
        }, $avatars);
    }

    /**
     * 判断是否为黑名单用户
     *
     * @return bool 是否为黑名单用户
     */
    public function inBlacklist(): bool
    {
        return Blacklist::model()->hasUser($this->id);
    }

    /**
     * 验证昵称是否符合规范
     *
     * @param string $username 昵称
     * @param bool $short_alert 是否返回短提示
     * @return string|bool 错误提示或布尔假值
     */
    public static function checkUsername($username, $short_alert = false)
    {
        if ($short_alert) {
            $alert = [
                Yii::t('app/error', 'Contains special characters'),
                Yii::t('app/error', 'Username is too long'),
                Yii::t('app/error', 'Username is too short'),
                Yii::t('app/error', 'Contains illegal words'),
            ];
        } else {
            $alert = [
                Yii::t('app/error', 'Nickname only supports Chinese, English, Japanese and underscores'),
                Yii::t('app/error', 'Nickname should be less than 10 characters or 20 letters'),
                Yii::t('app/error', 'Nickname should be longer than 2 characters or 4 letters'),
                Yii::t('app/error', 'Nickname contains illegal words'),
            ];
        }

        if (preg_match(self::REG_USERNAME, $username)) {
            return $alert[0];
        }

        $len = 0.5 * strlen($username) + 0.5 * mb_strlen($username);
        if ($len > self::USERNAME_MAX_LENGTH) {
            return $alert[1];
        } elseif ($len < self::USERNAME_MIN_LENGTH) {
            return $alert[2];
        }
        $result = Yii::$app->go->checkText($username, Go::SCENE_USER_INFO, true);
        if ($result && !current($result)['pass']) {
            return $alert[3];
        }

        return false;
    }

    /**
     * 获取搜索联想词
     *
     * @param string $s 搜索的关键词
     * @param int $count 联想词数量
     * @return array|null
     */
    public static function getSearchSuggest(string $s, int $count = Discovery::SUGGEST_COUNT)
    {
        return Yii::$app->go->suggest($s, Discovery::SEARCH_USER, $count);
    }

    /**
     * 获取优质的的联想词（粉丝数 >= 1000 的用户）
     *
     * @param string $s 搜索词
     * @param integer $count 联想词数量
     * @return array|null
     */
    public static function getGoodSearchSuggest(string $s, int $count)
    {
        if ($user = self::getSearchSuggest($s)) {
            $user['suggestions'] = self::find()
                ->select('username')
                ->where(['username' => $user['suggestions']])
                ->andWhere('fansnum >= 1000')
                ->limit($count)
                ->column();
        }
        return $user;
    }

    /**
     * 获取动态 feed 流（最后返回的都是音频数据）
     *
     * @param integer $user_id 用户 ID
     * @param integer $feed_type 动态类型（0 用户及频道动态；1 剧集动态；2 用户、频道及剧集动态）
     * @param integer $last_sound_id 当前页的最后一个音频 ID
     * @param integer $page_size 每页个数
     * @return array
     */
    public static function getUserFeed(int $user_id, int $feed_type, int $last_sound_id = 0,
            int $page_size = PAGE_SIZE_20)
    {
        $condition = sprintf('t1.recid = %d AND (%s)', $user_id, MSound::getCheckedInSql());
        if ($last_sound_id) {
            $condition .= sprintf(' AND t.id < %d', $last_sound_id);
        }
        // 比需要显示的条数多查一条
        // 如果能多查到，则代表还有数据，则 hasMore 为 true，否则为 false
        $limit = $page_size + 1;
        // 若使用 id（m_sound 的主键）会导致 "Using temporary; Using filesort"
        // （m_person_home_page 及 m_person_drama_page 含 recid 与 soundid 的联合索引）
        $order = ['t1.soundid' => SORT_DESC];

        switch ($feed_type) {
            case Feed::FEED_TYPE_USER_CHANNEL:
                // WORKAROUND: 临时取消频道 Feed 流
                $feed = [];
                // $user_feed = MPersonHomePage::getFeed($condition, $order, $limit);
                // $channel_feed = MPersonChannelPage::getFeed($condition, $order, $limit);
                // $feed = array_merge($user_feed, $channel_feed);
                break;
            case Feed::FEED_TYPE_DRAMA:
                $feed = MPersonDramaPage::getFeed($condition, $order, $limit);
                break;
            case Feed::FEED_TYPE_USER_CHANNEL_DRAMA:
                $user_feed = MPersonHomePage::getFeed($condition, $order, $limit);
                // WORKAROUND: 临时取消频道 Feed 流
                // $channel_feed = MPersonChannelPage::getFeed($condition, $order, $limit);
                $channel_feed = [];
                $drama_feed = MPersonDramaPage::getFeed($condition, $order, $limit);
                $feed = array_merge($user_feed, $channel_feed, $drama_feed);
                break;
        }

        usort($feed, function ($a, $b) {
            return $a['id'] < $b['id'];
        });
        return $feed;
    }

    /**
     * 判断用户是否已注销或隐私设置或被封禁
     *
     * @param int $user_id 用户 ID
     * @return bool
     * @throws HttpException
     */
    public static function isUserBlocked(int $user_id): bool
    {
        if (!$user_id || !$user_info = Mowangskuser::getPersonInfo($user_id)) {
            return true;
        }

        // 检查用户是否已注销或隐私设置
        if ((isset($user_info->confirm) && Mowangskuser::isDeleted($user_info->confirm))
                || (isset($user_info->confirm) && Mowangskuser::isPrivacyUser($user_info->confirm))) {
            return true;
        }

        // 检查用户是否被封禁
        return Blacklist::model()->getUserExpire($user_id) !== false;
    }

    /**
     * 判断是否是屏蔽的用户
     *
     * @param int $confirm
     * @return bool
     */
    public static function isPrivacyUser(int $confirm): bool
    {
        return MUtils::bitIsSet($confirm, self::CONFIRM_PRIVACY);
    }

    /**
     * 判断是否是注销的用户
     *
     * @param int $confirm
     * @return bool
     */
    public static function isDeleted(int $confirm): bool
    {
        return MUtils::bitIsSet($confirm, self::CONFIRM_DELETED);
    }

    /**
     * 获取用户直播间信息
     *
     * @return null|\yii\db\ActiveRecord
     */
    public function getLive()
    {
        // status 为 直播间状态（0：没有开启房间；1：房间开启）
        return Live::find()
            ->select('room_id, title, status')
            ->where(['user_id' => $this->id])
            ->andWhere(['<>', 'status', Live::STATUS_DELETED])
            ->one();
    }

    /**
     * 判断用户是否是永久禁止登录
     *
     * @return bool
     */
    public function isBanLogin(): bool
    {
        return MUtils::bitIsSet($this->confirm, self::CONFIRM_BAN_LOGIN);
    }

    public function isBanTopupAndConsume(): bool
    {
        return MUtils::bitIsSet($this->confirm, self::CONFIRM_BAN_TOPUP_AND_CONSUME);
    }

    /**
     * 是否需要显示被封禁
     * 除用户被禁止充值消费外，其余封禁都需要显示
     *
     * @link https://info.missevan.com/pages/viewpage.action?pageId=48432957
     * @return bool
     */
    public function isShowBan(): bool
    {
        if (is_null($this->_is_show_ban)) {
            if ($this->isBanLogin()) {
                // 若用户永久禁止登录，则为 true
                $this->_is_show_ban = true;
            } else {
                // 若用户被封禁且不为禁止充值消费，则为 true
                $expire = Blacklist::model()->getUserExpire($this->id);
                $this->_is_show_ban = ($expire !== false && ($expire > $_SERVER['REQUEST_TIME']
                    || ($expire < 0 && $expire !== Blacklist::BAN_TIME_FOREVER_TOPUP_AND_CONSUME)));
            }
        }
        return $this->_is_show_ban;
    }

    /**
     * 返回用户青少年状态
     *
     * @param $user_id
     * @param integer|null $confirm 等于 null 时，去用户表获取 confirm
     * @return int 1 开启 0 关闭
     */
    public static function isTeenagerModeEnabled($user_id, $confirm = null)
    {
        if (is_null($confirm)) {
            $confirm = (int)self::find()->select('confirm')->where(['id' => $user_id])->scalar();
        }
        return MUtils::bitIsSet($confirm, Mowangskuser::CONFIRM_TEENAGER) ?
            self::TEENAGER_MODE_ENABLED : self::TEENAGER_MODE_CLOSE;
    }

    public static function authLogin($data)
    {
        $info = Yii::$app->sso->authLogin($data);
        if (!$info) throw new HttpException(400, Yii::t('app/error', 'Login failed'));
        if (array_key_exists('error_code', $info)) {
            // 返回第三方账号的昵称与头像用于客户端注册时使用，方便客户端在之后的版本不用传递此类参数，直接在服务器端处理返回
            $info = array_merge($info, ['nickname' => $data['username'], 'iconurl' => $data['iconurl']]);
            throw new HttpExceptionWithData(403, $info['msg'], $info['error_code'], $info);
        }
        return self::getLoginUserInfo($info);
    }

    /**
     * 一键登录
     *
     * @param array $data 需要的登录信息组成的数组
     * @return array
     * @throws HttpException
     * @throws \yii\db\IntegrityException
     * @throws \Exception
     */
    public static function fastLogin($data)
    {
        $user_info = Yii::$app->sso->fastLogin($data);
        if (!$user_info) {
            switch ($data['type']) {
                case SSOClient::TYPE_FAST_LOGIN:
                    throw new HttpException(400, '一键登录失败');
                    break;
                case SSOClient::TYPE_SMS_LOGIN:
                case SSOClient::TYPE_SMS_AUTH_BIND:
                    throw new HttpException(400, '验证码登录失败');
                    break;
                case SSOClient::TYPE_FAST_AUTH_BIND:
                    throw new HttpException(400, '一键绑定失败');
                    break;
                default:
                    throw new HttpException(400, Yii::t('app/error', 'params error'));
            }
        }
        $auth_info = $user_info['auth_info'] ?? null;

        $is_new_user = false;
        // 是一个新账号或者是老账号在 mowangskuser 表没有数据进行同步数据的操作
        if ($user_info['is_new'] || !Mowangskuser::find()->where('id = :id',
            [':id' => $user_info['user']['user_id']])->exists()) {
            // 将 SSO 数据同步至 mowangskuser 表
            self::syncUser($user_info['user']['user_id'], $auth_info);
            $is_new_user = true;
        } elseif ($auth_info) {
            // 第三方绑定已有账号一键登录时，同步第三方信息至 user_addendum 表
            UserAddendum::saveAuthAccount($user_info['user']['user_id'], $auth_info);
        }
        return self::getLoginUserInfo($user_info, $is_new_user);
    }

    /**
     * 将 SSO 注册返回的用户信息数据同步至 mowangskuser 表里
     *
     * @param integer $user_id 需要同步的用户 ID
     * @param array|null $auth_info 第三方登录信息
     *
     * @throws HttpException
     * @throws \yii\db\IntegrityException
     */
    public static function syncUser(int $user_id, ?array $auth_info)
    {
        $user_info = Yii::$app->sso->getUser($user_id);
        if (!$user_info) {
            throw new HttpException(404, Yii::t('app/error', 'User does not exist'));
        }
        $user_info['user_id'] = $user_info['id'];
        $login_form = new LoginForm();
        // 这里不需要进行 validate 不用加场景规则
        $login_scenario = LoginForm::SCENARIO_FAST_LOGIN;
        if ($auth_info) {
            $login_scenario = LoginForm::SCENARIO_FAST_AUTH_BIND;
        }
        $login_form->setScenario($login_scenario);
        if (LoginForm::SCENARIO_FAST_LOGIN === $login_scenario) {
            $login_form->type = LoginForm::MOBILE;
        } elseif (LoginForm::SCENARIO_FAST_AUTH_BIND === $login_scenario) {
            $login_form->auth_type = $auth_info['auth_type'];
            $login_form->third_name = $auth_info['username'];
        }
        // 注册新用户送 100 小鱼干
        $user_info['point'] = PointDetailLog::POINT_NEW_USER_REGISTER;
        PointDetailLog::addLog(PointDetailLog::POINT_NEW_USER_REGISTER,
            PointDetailLog::TYPE_NEW_USER_REGISTER, $user_id);
        return $login_form->retryRegist($user_info, false);
    }

    /**
     * 获取登录用户信息
     *
     * @param array $info SSO 返回的用户信息
     * @param boolean $is_new_user 是否是新用户 false：否；true：是
     * @return array
     * @throws HttpException
     */
    private static function getLoginUserInfo($info, $is_new_user = false)
    {
        $user = self::getPersonInfo($info['user']['user_id'], $is_new_user);
        // 判断用户是否是永久禁止登录
        if ($user && $user->isBanLogin()) {
            // 清空用户所有 session
            Yii::$app->sso->clearSession($user->id);
            self::throwBanLoginException();
        }
        return [
            'id' => $info['user']['user_id'],
            'expire_at' => $info['expire_at'],
            'is_new' => $info['is_new'] ?? false,
            'token' => $info['token'],
            'user' => $user,
        ];
    }

    /**
     * 设置一键登录使用次数
     */
    public static function setFastLoginUsedTimes()
    {
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_COUNTER_FASTLOGIN_EQUIP, Yii::$app->equip->getEquipId());
        // 开始事务
        $redis->multi()->incr($key)->expireAt($key, strtotime('tomorrow'))->exec();
    }

    /**
     * 是否为官方账号
     *
     * @param int $confirm
     * @return bool
     */
    public static function isOfficial(int $confirm): bool
    {
        return MUtils::bitIsSet($confirm, self::CONFIRM_OFFICIAL);
    }

    /**
     * 设置用户额外数据
     *
     * @param mixed $user 用户数据
     */
    public static function setExtraInfo(&$user): void
    {
        $user['authenticated'] = self::getAuthenticated((int)$user['confirm']);
        $user['boardiconurl'] = $user['boardiconurl'] ?: 'icon01.png';
        $user['avatar'] = $user['avatar'] ?: 'icon01.png';
        // 头像
        $user['boardiconurl2'] = Yii::$app->params['profileUrl'] . $user['boardiconurl'];
        $user['avatar2'] = Yii::$app->params['avatarUrl'] . $user['avatar'];
        $user['iconurl'] = ((int)$user['icontype'] === self::TYPE_CARTOON_ICON)
            ? $user['boardiconurl2']
            : ((int)$user['icontype'] === self::TYPE_REALITY_ICON ? $user['avatar2'] : $user['iconurl']);
        // TODO: 用户封面图、黑夜模式封面图、封面图位置之后可在此处理
    }

    /**
     * 获取用户关注数
     *
     * @param int $id
     * @return int
     */
    public static function getFollowNum(int $id)
    {
        return (int)self::find()->select('follownum')
            ->where(['id' => $id])
            ->scalar();
    }

    /**
     * 获取用户粉丝数
     *
     * @param int $id 用户 ID
     * @return int
     */
    public static function getFansNum(int $id)
    {
        return (int)self::find()->select('fansnum')
            ->where(['id' => $id])
            ->scalar();
    }

    public static function throwBanLoginException()
    {
        if (Equipment::isAppOlderThan('4.7.8', '5.6.5')) {
            throw new HttpException(403, self::BAN_LOGIN_MESSAGE, self::PROMPT_MODAL_CODE);
        } else {
            throw new HttpException(403, self::BAN_LOGIN_NEW_MESSAGE, self::PROMPT_FEEDBACK_MODAL_CODE);
        }
    }

    public static function throwHighRiskAccountException(int $status, string $message)
    {
        // WORKAROUND: Android >= 5.6.5，iOS >= 4.7.8 时返回新的提示
        if (Equipment::isAppOlderThan('4.7.8', '5.6.5')) {
            throw new HttpException($status, self::HIGH_RISK_ACCOUNT_OLD_MESSAGE, self::PROMPT_MODAL_CODE);
        } else {
            throw new HttpException($status, $message, self::PROMPT_FEEDBACK_MODAL_CODE);
        }
    }

    /**
     * 获取 UP 主信息
     *
     * @param integer $up_user_id UP 主 ID
     * @param null|integer $user_id 当前用户 ID
     * @return null|array
     */
    public static function getUpUserInfo(int $up_user_id, ?int $user_id = 0): ?array
    {
        $up_user = null;
        if ($up_user_id) {
            $up_user_info = self::find()
                ->select('id, username, icontype, avatar, boardiconurl, iconurl, follownum, fansnum, confirm')
                ->where(['id' => $up_user_id])
                ->one();
            if ($up_user_info) {
                $up_user = [
                    'id' => $up_user_id,
                    'username' => $up_user_info['username'],
                    'iconurl' => $up_user_info['iconurl'],
                    'follownum' => $up_user_info['follownum'],
                    'fansnum' => $up_user_info['fansnum'],
                    'authenticated' => $up_user_info['authenticated'],
                    'is_vip' => (int)MUserVip::isVipUser($up_user_id),
                ];
                if ($user_id && $user_id !== $up_user_id) {
                    $up_user['attention'] = MAttentionUser::getAttention($up_user_id, $user_id);
                }
                $room_id = (int)Live::find()
                    ->select('room_id')
                    ->where(['user_id' => $up_user_id, 'status' => Live::STATUS_OPEN])
                    ->limit(1)
                    ->scalar();
                if ($room_id) {
                    $up_user['live'] = [
                        'status' => Live::STATUS_OPEN,
                        'room_id' => $room_id,
                    ];
                }
            }
        }
        return $up_user;
    }

    /**
     * 获取用户昵称（用户不存在时返回 false）
     *
     * @param int $user_id
     * @return false|string
     */
    public static function getUsernameById(int $user_id)
    {
        if ($user_id <= 0) {
            return false;
        }
        return self::find()->select('username')->where(['id' => $user_id])->scalar() ?: false;
    }

    /**
     * 获取用户简单信息 Map
     *
     * @param array $user_ids 用户 ID 数组
     * @return array 用户简单信息 Map
     */
    public static function getSimpleInfoMap(array $user_ids)
    {
        if (empty($user_ids)) {
            return [];
        }
        // TODO: 之后需要在 afterFind 里处理已注销用户的用户名
        $user_list = self::find()
            ->select('id, confirm, username, userintro, icontype, iconurl, avatar, boardiconurl')
            ->where(['id' => $user_ids])
            ->all();
        $user_map = array_column($user_list, null, 'id');
        return $user_map;
    }

    public function getRegisterAt()
    {
        return $this->ctime;
    }

    /**
     * 获取用户简单信息
     *
     * @param int $user_id 用户 ID
     * @return self|null 用户简单信息
     */
    public static function getSimpleInfoById(int $user_id)
    {
        if ($user_id <= 0) {
            return null;
        }
        return self::find()
            ->select('id, confirm, username, userintro, icontype, iconurl, avatar, boardiconurl')
            ->where(['id' => $user_id])
            ->one();
    }

    /**
     * 更新关联的表中的用户昵称
     *
     * @param int $user_id
     * @param string $username
     */
    public static function updateRelationUsername(int $user_id, string $username)
    {
        MAlbum::updateAll(['username' => $username], 'user_id = :user_id',
            [':user_id' => $user_id]);
        MSound::updateAll(['username' => $username], 'user_id = :user_id',
            [':user_id' => $user_id]);
        MImage::updateAll(['username' => $username], 'user_id = :user_id',
            [':user_id' => $user_id]);
        Live::updateAll(['username' => $username], 'user_id = :user_id AND status <> :status',
            [':user_id' => $user_id, ':status' => Live::STATUS_DELETED]);
    }
}
