<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "balance_settlement_change_log".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 * @property int $type 类型
 * @property int $status 状态
 * @property int $user_id 用户 ID
 * @property array $detail 详情
 * @property string $note 备注
 *
 * detail 字段示例说明
 * {
 *   "task": {
 *     "request_id": "xxxxx",  // 请求唯一标识
 *     "fail_reason": "xxxxx",  // 处理失败原因
 *     "order_ids": [135798]  // 处理的订单 ID
 *   },
 *   "balance": {  // 余额变动前后
 *     "old": {"ios": 33, "android": 30, "tmallios": 0, "paypal": 30, "googlepay": 10, "new_live_profit": 36900, "new_all_live_profit": 36900},
 *     "new": {"ios": 0, "android": 0, "tmallios": 0, "paypal": 0, "googlepay": 10, "new_live_profit": 36900, "new_all_live_profit": 36900}
 *   },
 *   "coin_changes": [
 *     {
 *       "order_id": 135798,
 *       "coin": -300,  // 充值订单退款时应扣（负值），或消费订单退款应返还的钻石数（正值）
 *       "coin_changed": -200  // 实际扣除或返还的钻石数
 *     }
 *   ],
 *   "revenue_change": {
 *     "revenue": -3000,  // 主播应扣的余额（单位：分）
 *     "revenue_changed": -3000  // 主播实扣的余额（单位：分）
 *   }
 * }
 */
class BalanceSettlementChangeLog extends ActiveRecord
{
    // 类型
    const TYPE_TOPUP_ORDER_REFUND = 1;  // 充值订单退款
    const TYPE_CONSUME_ORDER_REFUND = 2;  // 消费订单退款
    const TYPE_SINGLE_CREATOR_DEDUCT_BALANCE = 3;  // 素人主播扣减收益余额

    // 状态
    const STATUS_FAILED = -1;  // 失败
    const STATUS_CREATE = 0;  // 创建（处理中）
    const STATUS_SUCCESS = 1;  // 成功

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'balance_settlement_change_log';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('paydb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'note'], 'required'],
            [['create_time', 'modified_time', 'type', 'status', 'user_id'], 'integer'],
            [['detail', 'more'], 'safe'],
            [['note'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'type' => '类型',
            'status' => '状态',
            'user_id' => '用户 ID',
            'detail' => '详情',
            'note' => '备注',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;

        return true;
    }

    public function taskId()
    {
        return str_pad($this->type, 3, '0', STR_PAD_LEFT)
            . str_pad($this->id, 10, '0', STR_PAD_LEFT);
    }

    public static function parseTaskId(string $task_id)
    {
        return [
            'id' => (int)substr($task_id, -10),
            'type' => (int)substr($task_id, 0, 3),
        ];
    }

    public static function newLog(int $type, int $user_id, string $note, array $detail = [])
    {
        return new self([
            'type' => $type,
            'user_id' => $user_id,
            'note' => $note,
            'detail' => $detail,
            'status' => self::STATUS_CREATE,
        ]);
    }

    public function saveWhenFailed(string $fail_reason)
    {
        $this->status = self::STATUS_FAILED;
        $detail = $this->detail;
        $detail['task']['fail_reason'] = $fail_reason;
        $this->detail = $detail;
        $this->ignoreExceptionSave();
    }

    /**
     * 检查 request_id 是否可用
     *
     * @param int $user_id
     * @param int $type
     * @param string $request_id
     * @return bool
     */
    public static function isRequestIdExists(int $user_id, int $type, string $request_id)
    {
        return self::find()
            ->where(['user_id' => $user_id, 'type' => $type])
            ->andWhere('JSON_EXTRACT(detail, "$.task.request_id") = :request_id', [':request_id' => $request_id])
            ->exists();
    }

}
