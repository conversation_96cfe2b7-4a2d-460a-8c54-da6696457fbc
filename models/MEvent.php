<?php

namespace app\models;

use app\components\util\MUtils;
use app\components\util\Go;
use app\components\util\Captcha;
use app\components\util\Equipment;
use Exception;
use missevan\storage\StorageClient;
use Yii;
use yii\helpers\Json;
use yii\web\HttpException;

/**
 * This is the model class for table "m_event".
 *
 * @property integer $id 主键
 * @property string $title 活动名称
 * @property string $mobile_cover 模板页版头图
 * @property string $main_cover 网页封面图
 * @property string $mini_cover 客户端封面图
 * @property string $intro 活动介绍
 * @property string $short_intro 活动短介绍
 * @property string $tag 活动标签
 * @property integer $type 活动类型
 * @property integer $vote_start_time 投票开始时间
 * @property integer $vote_end_time 投票结束时间
 * @property integer $draw_start_time 抽奖开始时间
 * @property integer $draw_end_time 抽奖结束时间
 * @property integer $create_time 活动创建时间  // DEPRECATED: 待 start_time 字段上线后，考虑废弃
 * @property integer $start_time 活动开始时间
 * @property integer $end_time 活动结束时间
 * @property string $head 音频头
 * @property string $tail 音频尾
 * @property string|array $extended_fields 额外数据
 * @property integer $status 状态
 * @property integer $limit 每日投票限制
 * @property integer $limit_work 每日可投票作品数量限制
 * @property integer $limit_vote 每日每作品可投票数量限制
 * @property integer $do_comment 活动是否支持评论
 * @property integer $attr 活动属性
 *
 * @property-read integer $biz_type 业务类型（点播、直播等）
 * @property-read integer $play_type 玩法类型（活动、任务等）
 */
class MEvent extends ActiveRecord
{
    // 活动类型 0：活动作品是声音；1：活动作品是图片；2：活动作品是视频；3：特殊活动（如众筹活动）；4：预约活动；6：投稿活动；7：其他活动；8：游戏活动
    const TYPE_SOUND = 0;
    const TYPE_IMAGE = 1;
    const TYPE_VIDEO = 2;
    const TYPE_SPECIAL = 3;
    const TYPE_SUBSCRIBE = 4;
    const TYPE_UPLOAD = 6;
    const TYPE_OTHER = 7;
    const TYPE_GAME = 8;

    // APP 显示
    const STATUS_APP = 1;
    // APP 定制页面显示
    const STATUS_APP_SPECIAL = 2;
    // WEB 显示
    const STATUS_WEB = 4;
    // 隐藏活动
    const STATUS_HIDDEN = 8;
    // 隐藏活动
    const STATUS_LIST_HIDDEN = 1 << 4;

    // 评论
    const ATTR_COMMENT = 0b1;
    // 投票
    const ATTR_VOTE = 0b10;
    // 抽奖
    const ATTR_DRAW = 0b100;

    // 猫耳学院暑期纳新活动 ID
    const EVENT_ID_SUMMER_NAXIN = 161;
    // 盲盒剧场抽奖活动三期 ID
    const EVENT_ID_THEATRE_DRAW_III = 300;
    // 猫猫星球活动 ID
    const EVENT_ID_MAOMAO = 205;
    // 十周年钻石折扣包活动
    const EVENT_ID_10TH_ANNIVERSARY = 566;

    public $is_end;
    public $url;
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_event';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['title', 'main_cover', 'intro', 'tag', 'type', 'vote_end_time', 'create_time', 'start_time',
                'end_time'], 'required'],
            [['intro'], 'string'],
            [['tag_id', 'type', 'vote_start_time', 'vote_end_time', 'draw_start_time', 'draw_end_time',
                'create_time', 'start_time', 'end_time', 'status', 'limit', 'attr'], 'integer'],
            [['title', 'main_cover', 'mini_cover'], 'string', 'max' => 256],
            [['mobile_cover', 'short_intro', 'head', 'tail'], 'string', 'max' => 255],
            [['tag'], 'string', 'max' => 64],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'title' => '活动名称',
            'mobile_cover' => '手机端封面',
            'main_cover' => '活动封面',
            'mini_cover' => '290活动封面',
            'intro' => '活动介绍',
            'short_intro' => '活动短介绍',
            'tag_id' => 'Tag ID',
            'tag' => '活动标签',
            'type' => '活动上传类型 0单音1图片2配音',
            'vote_start_time' => '投票开始时间',
            'vote_end_time' => '投票结束时间',
            'draw_start_time' => '抽奖开始时间',
            'draw_end_time' => '抽奖结束时间',
            'create_time' => '活动创建时间',
            'start_time' => '活动开始时间',
            'end_time' => '活动结束时间',
            'head' => '音频头',
            'tail' => '音频尾',
            'extended_fields' => '额外数据', // JSON format
            'status' => '手机端是否可见 1为可见， 第2位为具有html5',
            'limit' => '活动每日投票上限',
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->main_cover && !MUtils::hasHttpScheme($this->main_cover)) {
            // WORKAROUND: 此处判断条件用于兼容数据库中老的图片地址数据，待数据刷新成统一格式后可去掉此条件
            $this->main_cover = StorageClient::getFileUrl($this->main_cover);
        }
        if ($this->mobile_cover && !MUtils::hasHttpScheme($this->mobile_cover)) {
            // WORKAROUND: 此处判断条件用于兼容数据库中老的图片地址数据，待数据刷新成统一格式后可去掉此条件
            $this->mobile_cover = StorageClient::getFileUrl($this->mobile_cover);
        }
        if ($this->mini_cover && !MUtils::hasHttpScheme($this->mini_cover)) {
            // WORKAROUND: 此处判断条件用于兼容数据库中老的图片地址数据，待数据刷新成统一格式后可去掉此条件
            $this->mini_cover = StorageClient::getFileUrl($this->mini_cover);
        }
        if ($this->extended_fields) {
            $this->extended_fields = Json::decode($this->extended_fields);
        }
    }

    public function getBiz_type()
    {
        if (!$this->extended_fields || !is_array($this->extended_fields) || !array_key_exists('biz_type', $this->extended_fields)) {
            return null;
        }
        return $this->extended_fields['biz_type'];
    }

    public function getPlay_type()
    {
        if (!$this->extended_fields || !is_array($this->extended_fields) || !array_key_exists('play_type', $this->extended_fields)) {
            return null;
        }
        return $this->extended_fields['play_type'];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        if ($this->extended_fields) {
            $this->extended_fields = Json::encode($this->extended_fields);
        }
        return true;
    }

    /**
     * 检查活动有效期
     *
     * @param boolean $voted 是否包含投票
     * @throws \Exception
     */
    public function check(bool $voted = false)
    {
        $time = $_SERVER['REQUEST_TIME'] + ($this->extended_fields['time_offset'] ?? 0);

        if ($voted) {
            if ($time < $this->vote_start_time) {
                throw new HttpException(403, '投票未开始', 200410100);
            } elseif ($time >= ($this->vote_end_time)) {
                throw new HttpException(403, '投票已截止', 200410100);
            }
        } else {
            if ($time < $this->start_time) {
                throw new HttpException(403, '活动未开始', 200410006);
            } elseif ($time >= ($this->end_time)) {
                throw new HttpException(403, '活动已过期', 200410006);
            }
        }
    }

    public static function WorkSubmission(int $event_id, int $eid)
    {
        if (!$event = MEvent::findOne($event_id)) {
            throw new HttpException(404, '活动不存在', 200410001);
        }
        $event->check();
        //if ($event->type == 1) {}@todo check eid
        $model = new EventVote();
        $model->category = $event->type;
        $model->eid = $eid;
        $model->event_id = $event_id;
        if (!$model->save()) throw new HttpException(400, '投稿失败', 200110101);
    }

    public static function GetPlayerAvatar(&$models)
    {
        if (!$models) return;

        if (is_array($models)) {
            $user_ids = array_column($models, 'user_id');
            $players = Mowangskuser::findAll($user_ids);

            $bordiconurls = array_column($players, 'boardiconurl2', 'id');

            foreach ($models as &$model) {
                $model['playeravatar'] = $bordiconurls[$model->user_id] ?? '';
            }
        } else {
            $player = Mowangskuser::findOne($models->user_id);
            $models['playeravatar'] = $player->boardiconurl2;
        }
    }

    /**
     * 根据音频获取所属活动
     *
     * @param integer $sound_id 音频 ID
     * @param integer|null $user_id 用户 ID
     * @return null|array
     */
    public static function getEventBySound(int $sound_id, ?int $user_id)
    {
        if (!$work = EventVote::findOne(['eid' => $sound_id, 'category' => EventVote::CATEGORY_UPLOAD])) {
            return null;
        }

        if (!$event = MEvent::findOne($work->event_id)) {
            return null;
        }
        try {
            $do_vote = (bool)($event->attr & self::ATTR_VOTE);
            $event->check($do_vote);
        } catch (\Exception $e) {
            return null;
        }
        $return = [
            'id' => $event->id,
            'title' => $event->title,
            'vote_start_time' => $event->vote_start_time ?: $event->start_time,
            'voted' => 1,  // 用户当前是否可投票（0：否；1：可投票，用户今日未投票）
            // 活动是否支持投票
            'do_vote' => $do_vote,
            'url' => Yii::$app->params['domainMissevan'] . '/mevent/' . $event->id,
        ];

        if ($user_id) {
            $from_time = strtotime('today');
            $to_time = strtotime('tomorrow') - 1;
            $return['voted'] = EventVoteDetail::find()
                ->where(['eid' => $sound_id, 'user_id' => $user_id])
                ->andWhere('time BETWEEN :from_time AND :to_time', [
                    ':from_time' => $from_time,
                    ':to_time' => $to_time,
                ])->exists() ? 0 : 1;
        }
        return $return;
    }

    /**
     * 根据元素获取特殊活动
     *
     * @param integer $elem_id 元素 ID
     * @param integer $elem_type 元素类型
     * @return null|array 例：["id" => 85, "title" => "本剧正在参加众筹活动，快去众筹榜看看吧", "url" => "https://..."]
     */
    public static function getSpecialEventByElement(int $elem_id, int $elem_type)
    {
        if (!$event_intro = SpecialEventElem::getEventIntro($elem_id, $elem_type)) {
            return null;
        }
        if ($event_intro->event_id) {
            if (!$event = MEvent::findOne($event_intro->event_id)) {
                return null;
            }
            try {
                $event->check();
            } catch (\Exception $e) {
                return null;
            }
        }

        return [
            'id' => $event_intro->event_id,
            'title' => $event_intro->intro,
            'url' => $event_intro->url ?: Yii::$app->params['domainMissevan'] . '/mevent/' . $event_intro->event_id,
        ];
    }

    /**
     * 根据元素获取所属活动
     *
     * @param integer $elem_id 元素 ID
     * @param integer $elem_type 元素类型
     * @param integer|null $user_id 用户 ID
     * @return array|null 例：["id" => 85, "title" => "本剧正在参加众筹活动，快去众筹榜看看吧"]
     */
    public static function getEventByElement(int $elem_id, int $elem_type, ?int $user_id)
    {
        $return = null;
        switch ($elem_type) {
            case SpecialEventElem::ELEM_TYPE_SOUND:
                $return = self::getEventBySound($elem_id, $user_id);
                break;
            case SpecialEventElem::ELEM_TYPE_DRAMA:
                $return = self::getSpecialEventByElement($elem_id, $elem_type);
                break;
        }

        return $return;
    }

    /**
     * 检查活动元素参数是否合法
     *
     * @param integer $elem_type
     * @return boolean
     */
    public static function checkElementType(int $elem_type): bool
    {
        $elem_type_array = [
            SpecialEventElem::ELEM_TYPE_SOUND,
            SpecialEventElem::ELEM_TYPE_DRAMA,
        ];
        return in_array($elem_type, $elem_type_array);
    }

    /**
     * 投票风险检验规则
     *
     * 第一次请求风险识别检测 API：
     * 1. 判断返回值 pass 为 false 提示投票失败
     * 2. 判断返回值 pass 为 true 并且返回值 label 有特定的值时抛出需要滑动验证的提示，验证完成后再次请求投票接口
     * 第二次请求风险识别检测 API：
     * 1. 带上滑动验证的参数进行验证，验证成功之后，判断返回值 pass 只要为 true，就通过风险识别
     */
    public static function checkRisk()
    {
        // iOS 4.3.8 以下、Android 5.2.8 以下提示升级 App
        if (Equipment::isAppOlderThan('4.3.8', '5.2.8')) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }
        // 人机验证是否通过的标识
        $is_validated = false;
        if (ENABLE_NOCAPTCHA && !defined('YII_CAPTCHA_TEST')) {
            $captcha_arr = Captcha::getSlideParams();
            if (!empty($captcha_arr)) {
                [$session_id, $token, $sig] = $captcha_arr;
                $result = Yii::$app->captcha->verifyCaptcha($session_id, $token, $sig, Captcha::SCENEORIGINAL_OTHER);
                // 判断滑动验证是否成功
                if (Captcha::SUCCESS !== $result->Code) {
                    throw new HttpException(403, Yii::$app->params['standaloneUrl'] . '403/slide.html?scene=' .
                        Captcha::SCENEORIGINAL_OTHER, 100010017);
                }
                $is_validated = true;
            }
        } else {
            $is_validated = true;
        }
        $result = Yii::$app->go->checkRisk(Go::RISK_SCENE_VOTE);
        if (!$result['pass']) {
            // 抛出相关异常
            throw new HttpException(403, '操作异常，投票失败！请使用正常权益投票！');
        }
        if (!$is_validated && $result['pass'] && !empty($result['labels'])) {
            if (array_intersect($result['labels'], [Go::LEVEL_LOW, Go::LEVEL_MEDIUM])) {
                throw new HttpException(403, Yii::$app->params['standaloneUrl'] . '403/slide.html?scene=' .
                    Captcha::SCENEORIGINAL_OTHER, 100010017);
            }
        }
        return $result;
    }

    /**
     * 判断活动是否在抽奖期内
     *
     * @return boolean
     */
    public function isInDrawTime()
    {
        $time = $_SERVER['REQUEST_TIME'];
        $time_offset = $this->extended_fields['time_offset'] ?? 0;
        if (($time + $time_offset) < $this->draw_start_time || ($time + $time_offset) >= $this->draw_end_time) {
            return false;
        }
        return true;
    }

    /**
     * 判断当前是否在获得积分时间范围内
     *
     * @return boolean
     */
    public function inDrawPointTime()
    {
        $time = $_SERVER['REQUEST_TIME'] + ($this->extended_fields['time_offset'] ?? 0);
        $draw_point_start_time = $this->extended_fields['draw_point_start_time'] ?? 0;
        $draw_point_end_time = $this->extended_fields['draw_point_end_time'] ?? 0;
        return ($time >= $draw_point_start_time && $time < $draw_point_end_time);
    }

    /**
     * 获取充值活动
     *
     * @return self|null
     */
    public static function getTopupEvent(): ?self
    {
        $event_id = Yii::$app->params['topup_discount_event_id'] ?? 0;
        if (!$event_id || $event_id <= 0) {
            Yii::error('充值优惠活动 ID 未配置或配置错误', __METHOD__);
            return null;
        }
        $event = self::findOne(['id' => $event_id]);
        if (!$event) {
            Yii::error('充值优惠活动不存在，活动 ID: ' . $event_id, __METHOD__);
            return null;
        };
        return $event;
    }

    /**
     * 十周年钻石折扣包活动连续签到达 3 天后发放活动奖品
     *
     * @param int $user_id 用户 ID
     * @param int $sign_days 截止到当天连续签到时长，单位：天
     * @param int $sign_date 签到（补签）日期零点时间戳，单位：秒
     * @return bool 是否成功发放奖品
     */
    public static function exchange10thAnniversaryPrize(int $user_id, int $sign_days, int $sign_date): bool
    {
        $mevent = self::findOne(['id' => self::EVENT_ID_10TH_ANNIVERSARY]);
        if (!$mevent) {
            Yii::error(self::EVENT_ID_10TH_ANNIVERSARY . ' 活动不存在', __METHOD__);
            return false;
        }
        $time_offset = $mevent->extended_fields['time_offset'] ?? 0;
        $now = $_SERVER['REQUEST_TIME'] + $time_offset;
        $sign_date = $sign_date + $time_offset;
        $exchange_sign_start_time = $mevent->start_time;
        $exchange_sign_end_time = $mevent->end_time;
        $MIN_SIGN_DAYS = 3;
        /**
         * 活动 7.10 开始，start_time 需要配置为 7.10 零点
         * 活动期间连续签到达 3 天（含补签）可获取奖品，若用户 7.10 签到并补签 7.8、7.9，不算完成任务
         * 最早 7.10 - 7.12 都有签到记录才算完成，所以最早能获取奖品的时间为 7.12 零点
         */
        $event_start_exchange_prize_time = $exchange_sign_start_time + ($MIN_SIGN_DAYS - 1) * ONE_DAY;
        if ($now < $event_start_exchange_prize_time || $now >= $exchange_sign_end_time
                || $sign_date < $exchange_sign_start_time || $sign_date >= $exchange_sign_end_time) {
            return false;
        }
        /**
         * $sign_days 为包含今日的连续签到天数
         * 例：用户 7.20 签到并补签 7.11 - 7.13 依然算完成任务，此时由于有断签，$sign_days 为 1，需要查询活动期间用户最大连续签到天数
         */
        $is_sign = $sign_date === strtotime('today', $now);
        if (($is_sign && $sign_days < $MIN_SIGN_DAYS)
                || (!$is_sign && !UserSignHistory::isFinishedContinuousSign($user_id, $exchange_sign_start_time, $exchange_sign_end_time, $MIN_SIGN_DAYS))) {
            return false;
        }
        // 相关 RPC 接口有幂等性，重复请求时不会重复发放
        try {
            Yii::$app->serviceRpc->eventExchange(self::EVENT_ID_10TH_ANNIVERSARY, $user_id);
            return true;
        } catch (Exception $e) {
            // PASS: 出错时避免影响签到业务，不抛出异常
            Yii::error('发放 ' . self::EVENT_ID_10TH_ANNIVERSARY . ' 活动连续签到奖品失败：' . $e->getMessage(),
                __METHOD__);
        }
        return false;
    }
}
