<?php

namespace app\models;

use app\components\util\Equipment;
use Yii;
use yii\db\Expression;

/**
 * This is the model class for table "ccy".
 *
 * @property integer $id
 * @property string $price
 * @property double $real_price
 * @property integer $num
 * @property integer $ccy
 * @property integer $device
 * @property integer $scope
 * @property array $more
 * @property integer $delete_time
 */
class TopupMenu extends ActiveRecord
{
    /**
     * @var string iOS 内购商品标识 ID
     */
    public $product_id;
    /**
     * @var bool 是否为有库存量限制的充值商品
     */
    private $is_limit_stock;
    /**
     * @var string 角标文案
     */
    public $corner_mark;
    /**
     * @var integer 原钻石数
     */
    public $original_num;
    /**
     * @var int 是否默认选中
     */
    public $checked;

    const DIAMOND = 1;
    const DIAMOND_PROMOTIONS = 2; // 钻石优惠活动

    // 设备类型
    const DEVICE_ANDROID = 1;  // 安卓机
    const DEVICE_IOS = 2;  // iOS 设备
    const DEVICE_WEB = 3;  // 网页
    const DEVICE_BACKEND = 4;  // 后台充值
    const DEVICE_TMALL = 5;  // 天猫旗舰店
    const DEVICE_DOUDIAN = 6;  // 抖店
    const DEVICE_JINGDONG = 7;  // 京东
    const DEVICE_BILI_LARGE_PAY = 8;  // B站大额支付
    const DEVICE_GOOGLE_PLAY = 9;  // Google Play
    const DEVICE_VIP = 10;  // 主站会员领钻

    // TODO: 下面这些折扣活动相关数值可以配置化到活动拓展配置中
    // 天猫钻石充值活动商品单次可购买最大数量（为 -1 表示不做限制）
    const ACTIVITY_TMALL_DIAMOND_TOPUP_ONCE_MAX_BUY_NUMBER = 5;
    // 天猫钻石充值活动商品单用户累计可购买单个商品最大数量（为 -1 表示不做限制）
    const ACTIVITY_TMALL_DIAMOND_TOPUP_TOTAL_MAX_BUY_NUMBER = 5;
    // 天猫钻石充值活动商品购买次数无限制标识
    const ACTIVITY_TMALL_DIAMOND_TOPUP_BUY_NUMBER_UNLIMITED = -1;

    // 天猫钻石充值活动商品限制购买类型，1：购买数量超过限制；2：未购买整剧付费集剧
    const LIMIT_TYPE_BUY_NUMBER = 1;
    const LIMIT_TYPE_NOT_BUY_DRAMA = 2;

    // 默认选中的充值档位不存在时的选中位置
    const DEFAULT_POSITION = 0;

    // 应用范围
    const SCOPE_ALL_USER = 0b001;  // 全体用户
    const SCOPE_OLD_USER = 0b010;  // 老用户（不满足新人福利条件的用户）
    const SCOPE_NEW_USER = 0b100;  // 新人福利（注册时间 < 14*24h && 未充值过）
    const SCOPE_LIMIT_STOCK = 0b1000;  // 有库存限制
    const SCOPE_VIP = 0b10000;  // 主站会员领取的福利钻石

    // 充值类型
    // 钻石
    const COIN_TYPE_DIAMONDS = 1;

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->paydb;
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'topup_menu';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['price', 'num', 'ccy', 'device'], 'required'],
            [['price'], 'number'],
            [['num', 'ccy', 'device', 'scope', 'delete_time'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'price' => '价格',
            'num' => '获取货币数量',
            'ccy' => '货币类型',
            'device' => '设备',
            'scope' => '应用范围',
            'more' => '更多详情',
            'delete_time' => '删除时间',
        ];
    }

    public function fields()
    {
        $fields = parent::fields();
        if ($this->hasProductID()) {
            $fields['product_id'] = 'product_id';
        }
        unset($fields['device']);
        return $fields;
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->hasProductID()) {
            $this->product_id = $this->getProductID();
        }
        if (Yii::$app->equip->isFromMiMiApp()) {
            // 去除小数点，例 '120.00' => 120 => '120'
            $this->price = (string)intval($this->price);
        }
    }

    private function hasProductID(): bool
    {
        return Equipment::iOS === $this->device
            || Yii::$app->equip::isFromGoogleChannel()
            || Yii::$app->equip->isFromMiMiApp();
    }

    private function getProductID(): string
    {
        return self::getIAPProductID() . str_pad($this->id, 4, '0', STR_PAD_LEFT);
    }

    private static function getIAPProductID()
    {
        $equip = Yii::$app->equip;
        if ($equip->isFromMiMiApp()) {
            return IAP_PRODUCT_IDS['mimi'];
        }
        if ($equip::isConceptVersion()) {
            return IAP_PRODUCT_IDS['missevan_concept'];
        }
        if ($equip->isIOS()) {
            return IAP_PRODUCT_IDS['missevan_ios'];
        }

        return IAP_PRODUCT_IDS['missevan_android'];
    }

    /**
     * @param string $product_id 例 jp.mimifm.app.item0012 或 com.missevan.CatEarFM0001
     * @param int $device
     * @return self
     */
    public static function findProduct(string $product_id, int $device): ?self
    {
        $id = (int)str_replace(self::getIAPProductID(), '', $product_id);
        return self::getItem($id, $device);
    }

    /**
     * @param int $id 主键
     * @param int $device 设备类型，-1 时代表不限制设备
     * @return TopupMenu|null
     */
    public static function getItem(int $id, int $device = -1): ?self
    {
        if ($device === Equipment::HarmonyOS) {
            $device = Equipment::Android;
        }
        $query = self::find()
            ->select('id, price, num, ccy, device, scope')
            ->where(['id' => $id]);
        if ($device !== -1) {
            $query->andWhere(['device' => $device]);
        }

        return $query->one();
    }

    /**
     * @param bool $is_new_user 是否为专属新用户
     * @param int $scope 当前的 scope
     * @return int
     */
    public static function getScope(bool $is_new_user = false, int $scope = self::SCOPE_ALL_USER)
    {
        if ($is_new_user) {
            $scope |= self::SCOPE_NEW_USER;
        } else {
            $scope |= self::SCOPE_OLD_USER;
        }
        return $scope;
    }

    public static function getCoinPrice(int $device, int $scope)
    {
        if ($device === Equipment::HarmonyOS) {
            $device = Equipment::Android;
        }
        $list = self::find()
            ->select('id, price, num, device')
            ->addSelect(new Expression("JSON_UNQUOTE(JSON_EXTRACT(more, '$.corner_mark')) AS corner_mark"))
            ->addSelect(new Expression("JSON_EXTRACT(more, '$.original_num') AS original_num"))
            ->addSelect(new Expression("JSON_EXTRACT(more, '$.checked') AS checked"))
            ->addSelect(new Expression("JSON_EXTRACT(more, '$.sort') AS sort"))
            ->where('ccy = :ccy AND device = :device AND scope & :scope AND delete_time = 0',
                [':ccy' => self::DIAMOND, ':device' => $device, ':scope' => $scope])
            ->orderBy('sort ASC')
            ->all();
        foreach ($list as &$item) {
            /**
             * @var self $item
             */
            if (!is_null($item->original_num)) {
                $item->original_num = (int)$item->original_num;
            }
            if (!is_null($item->checked)) {
                $item->checked = (int)$item->checked;
            }
        }
        unset($item);
        return $list;
    }

    public function isNewUserScope()
    {
        return (bool)(self::SCOPE_NEW_USER & (int)$this->scope);
    }

    /**
     * 购买充值的数量是否合法
     * 充值活动商品可能会有购买上限
     *
     * @param int $number 购买数量
     * @return bool 购买充值数量是否合法
     */
    public function checkBuyNumber(int $buy_number): bool
    {
        if ($buy_number <= 0 || ($this->isLimitStock()
            && self::ACTIVITY_TMALL_DIAMOND_TOPUP_ONCE_MAX_BUY_NUMBER
            !== self::ACTIVITY_TMALL_DIAMOND_TOPUP_BUY_NUMBER_UNLIMITED
            && $buy_number > self::ACTIVITY_TMALL_DIAMOND_TOPUP_ONCE_MAX_BUY_NUMBER)) {
            // 购买数量不大于 0 或购买活动充值活动商品超过单次上限时非法
            return false;
        }
        return true;
    }

    /**
     * 是否为有库存量的充值商品
     * 目前仅有天猫充值优惠活动有库存限制
     *
     * @return bool 是否有库存量限制
     */
    public function isLimitStock(): bool
    {
        if ($this->is_limit_stock === null) {
            $this->is_limit_stock = (bool)($this->scope & self::SCOPE_LIMIT_STOCK);
        }
        return $this->is_limit_stock;
    }

    /**
     * 检查充值的商品是否还有库存
     *
     * @param int $number 购买数量
     * @return bool 是否还有满足购买数量的库存
     */
    public function hasStock(int $number): bool
    {
        // 有库存限制时，判断是否还有余量
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(COUNTER_TOPUP_GOODS_STOCK, $this->id);
        $stock = $redis->get($key);
        if ($stock === false) {
            // 未设置库存时，视作无库存
            return false;
        }
        return ((int)$stock - $number) >= 0;
    }

    /**
     * 检查用户购买是否被限制
     * 当前规则：购买超过指定次数或未购买过整剧付费剧集的用户被限制
     * https://github.com/MiaoSiLa/requirements-doc/issues/1109
     *
     * @param int $number 购买数量
     * @param int $user_id 用户 ID
     * @param bool $limit_buy_drama 必须购买过剧集限制
     *
     * @return int 用户购买商品受限类型，0：未被限制；1：购买数量超过限制；2：未购买整剧付费集剧
     */
    public function buyLimit(int $number, int $user_id, bool $limit_buy_drama = false): int
    {
        if (self::ACTIVITY_TMALL_DIAMOND_TOPUP_ONCE_MAX_BUY_NUMBER
                !== self::ACTIVITY_TMALL_DIAMOND_TOPUP_BUY_NUMBER_UNLIMITED
                && $number > self::ACTIVITY_TMALL_DIAMOND_TOPUP_ONCE_MAX_BUY_NUMBER) {
            // 一次购买超过可购买最大数量时受限
            return self::LIMIT_TYPE_BUY_NUMBER;
        }
        if ($limit_buy_drama) {
            $has_buy = TransactionLog::find()
                ->where('from_id = :user_id AND type = :type AND status = :status', [
                    ':user_id' => $user_id,
                    ':type' => TransactionLog::TYPE_DRAMA,
                    ':status' => TransactionLog::STATUS_SUCCESS,
                ])->exists();
            if (!$has_buy) {
                // 若用户未购买过整剧付费剧集，则返回对应限制类型
                return self::LIMIT_TYPE_NOT_BUY_DRAMA;
            }
        }
        if (self::ACTIVITY_TMALL_DIAMOND_TOPUP_TOTAL_MAX_BUY_NUMBER
                !== self::ACTIVITY_TMALL_DIAMOND_TOPUP_BUY_NUMBER_UNLIMITED) {
            // 若限制了累计购买次数，则判断是否累计购买次数达到限制数量
            $redis = Yii::$app->redis;
            // TODO: 该缓存在活动结束后可删除
            $key = $redis->generateKey(COUNTER_SET_TOPUP_GOODS_USER_BUY_NUMBER, $this->id);
            $current_number = $redis->zIncrBy($key, $number, $user_id);
            if ($current_number > self::ACTIVITY_TMALL_DIAMOND_TOPUP_TOTAL_MAX_BUY_NUMBER) {
                // 累计购买数量超过最大数量时受限
                if ($number > 1) {
                    // 购买数量大于 1 时，扣除增加的的购买量，避免用户减少购买数量后不能再次购买
                    $redis->zIncrBy($key, -$number, $user_id);
                }
                return self::LIMIT_TYPE_BUY_NUMBER;
            }
        }
        return 0;
    }

    /**
     * 更新库存量
     *
     * @param int $number 加减库存的数量
     * @return bool 是否更新成功，仅在库存不足时更新失败
     */
    public function updateCounterStock(int $number): bool
    {
        $update = true;
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(COUNTER_TOPUP_GOODS_STOCK, $this->id);
        $stock = $redis->incrBy($key, $number);
        if ($number < 0 && $stock < 0) {
            // 库存最小值应为 0，减库存小于 0 时，补回扣除的库存量，避免新增库存时不能正确增加
            $redis->incrBy($key, -$number);
            $update = false;
        }
        return $update;
    }

    public static function getCustomDiamondNumId(int $device = self::DEVICE_WEB)
    {
        if ($id = self::find()->select('id')->where(['num' => 0, 'device' => $device, 'delete_time' => 0])->limit(1)->scalar()) {
            return (int)$id;
        }
        Yii::error('custom diamond num record not found', __METHOD__);
        return 0;
    }

}
