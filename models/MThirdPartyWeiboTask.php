<?php

namespace app\models;

use Exception;
use missevan\util\MUtils AS MUtils2;
use Yii;

class MThirdPartyWeiboTask
{
    // 合作方回调接口文档：https://info.bilibili.co/pages/viewpage.action?pageId=958111694
    const CALLBACK_URL_WEIBO = 'https://api.weibo.com/2/proxy/task/complete';

    // 微博方分配的任务名称
    const WEIBO_TASK_NAME = 'exchange_maoer';

    // 回调成功响应 code
    const WEIBO_REQUEST_SUCCESS_CODE = 10000;

    /**
     * 生成唯一请求标识
     *
     * @return string
     * @throws \Exception
     */
    private static function buildRequestId(): string
    {
        return MUtils2::randomKeys(5, 7);
    }

    /**
     * 请求微博回调
     *
     * @param string $track_id
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function callback(string $track_id): bool
    {
        $params = [
            'access_token' => Yii::$app->params['service']['weibo_third_party_task']['access_token'],
            'source' => Yii::$app->params['service']['weibo_third_party_task']['app_key'],
            'request_id' => self::buildRequestId(),
            'task' => self::WEIBO_TASK_NAME,
            'token' => $track_id,
        ];
        try {
            $data = Yii::$app->tools->requestRemote(self::CALLBACK_URL_WEIBO, $params, 'POST', null, 0, [
                'Content-Type' => 'application/x-www-form-urlencoded',
            ]);
            if (!$data) {
                throw new Exception('返回值为空');
            }
            if ($data['status'] !== self::WEIBO_REQUEST_SUCCESS_CODE) {
                throw new Exception(sprintf('code[%d], msg[%s]', $data['status'], $data['msg']));
            }
        } catch (Exception $e) {
            Yii::error('微博点击回传失败，原因：' . $e->getMessage(), __METHOD__);
            return false;
        }
        return true;
    }
}
