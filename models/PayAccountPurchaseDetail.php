<?php

namespace app\models;

use app\components\util\MUtils;
use Yii;
use Exception;
use yii\helpers\Json;

/**
 * This is the model class for table "pay_account_purchase_detail".
 *
 * @property string $id 主键
 * @property int $account_id pay_account 表 ID
 * @property int $tid transaction_log 表 ID
 * @property int $purchase_amount 销售金额（钻石，价值 0.1 元）
 * @property double $fee_rate 交易时的费率
 * @property int $status 单笔交易类型 -1. 交易已取消 0. 交易尚未完成，资金暂时冻结 1. 已经和收款方结算
 * @property int $create_time 创建时间（秒级时间戳）
 * @property int $modified_time 修改时间（秒级时间戳）
 * @property array $more 更多详情
 * @property int $user_id 用户 ID
 */
class PayAccountPurchaseDetail extends ActiveRecord
{
    const STATUS_REFUND = -2;
    const STATUS_CANCEL = -1;
    const STATUS_PENDING = 0;
    const STATUS_CONFIRM = 1;

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->paydb;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'pay_account_purchase_detail';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['account_id', 'tid', 'purchase_amount', 'status', 'create_time', 'modified_time', 'user_id'], 'integer'],
            [['fee_rate'], 'number'],
            [['more'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'account_id' => '账户 ID',
            'tid' => '交易记录 ID',
            'purchase_amount' => '销售金额',
            'fee_rate' => '交易时涉及平台费率',
            'status' => '交易状态',
            'more' => '更多详情',
            'user_id' => '用户 ID',
            'create_time' => '创建时间（秒级时间戳）',
            'modified_time' => '修改时间（秒级时间戳）',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 变动账号生成交易明细
     *
     * @param PayAccount[] $accounts 发生变动的账号
     * @param TransactionLog $transaction_log 交易订单
     * @param int $status 订单状态
     * @param array $more 更多详情
     * @return int
     * @throws Exception 保存出错
     */
    public static function purchaseDetail(array $accounts, TransactionLog $transaction_log, int $status, array $more = [])
    {
        if (empty($accounts)) {
            return 0;
        }
        $time = $_SERVER['REQUEST_TIME'];
        $rows = array_reduce($accounts, function ($carry, $account) use ($transaction_log, $status, $time, $more) {
            /**
             * @var PayAccount $account
             */
            if ($account->isLegacy()) {
                return $carry;
            }
            $more = array_merge($more, [
                'account_scope' => $account->scope,
                'account_type' => $account->type,
            ]);
            if (array_key_exists('attr', $account->more)) {
                $more['account_attr'] = $account->more['attr'];
            }
            $carry[] = [
                'user_id' => $account->user_id,
                'account_id' => $account->id,
                'tid' => $transaction_log->id,
                'purchase_amount' => $account->consume_amount,
                'fee_rate' => $account->getFeeRate($transaction_log),
                'status' => $status,
                'create_time' => $time,
                'modified_time' => $time,
                'more' => Json::encode($more),
            ];
            return $carry;
        }, []);
        if (empty($rows)) return 0;

        return self::getDb()->createCommand()
            ->batchInsert(
                self::tableName(),
                array_keys($rows[0]),
                $rows
            )->execute();
    }

    public static function getNobleCostDetail(int $tid): int
    {
        return (int)self::find()
            ->where('tid = :tid AND status = :status')
            ->params([':tid' => $tid, ':status' => self::STATUS_CONFIRM])
            ->sum('purchase_amount');
    }

    public static function proceed(TransactionLog $transaction_log, int $target_status, int $current_status = self::STATUS_PENDING)
    {
        /** @var $details PayAccountPurchaseDetail[] */
        $details = self::find()
            ->select('account_id, purchase_amount, more')
            ->where('tid = :tid AND status = :status')
            ->params(['tid' => $transaction_log->id, 'status' => $current_status])
            ->all();
        $account_ids = array_column($details, 'account_id');
        $accounts = PayAccount::find()
            ->where(['id' => $account_ids, 'status' => PayAccount::STATUS_SUCCESS])
            ->indexBy('id')
            ->all();

        switch ($target_status) {
            case self::STATUS_CONFIRM:
                // PASS
                break;
            case self::STATUS_CANCEL:
            case self::STATUS_REFUND:
                $change_accounts = array_reduce($details, function ($carry, $detail) use ($accounts) {
                    /**
                     * @var PayAccount $account
                     * @var self $detail
                     */
                    if (!$account = $accounts[$detail->account_id] ?? null) {
                        return $carry;
                    }
                    $account->consume_amount = -1 * $detail->purchase_amount;
                    $carry[] = $account;
                    return $carry;
                }, []);
                $pay_account_nums = [];
                foreach ($details as $item) {
                    /**
                     * @var PayAccount $account
                     */
                    $account = $accounts[$item->account_id] ?? null;
                    if (is_null($account)) {
                        continue;
                    }
                    $coin_field = $account->getCoinField();
                    $pay_account_nums[$coin_field] = ($pay_account_nums[$coin_field] ?? 0) + $item->purchase_amount;
                }
                $costs = [];
                foreach (Balance::balanceFields() as $coin_field) {
                    // transaction_log.xxx_coin 为普通钻石消耗（包含来自 balance 表及 pay_account 表）
                    // 退款时需要区分开，避免重复计算
                    $costs[$coin_field] = $transaction_log->{$coin_field . '_coin'};
                    if (array_key_exists($coin_field, $pay_account_nums)) {
                        $costs[$coin_field] -= $pay_account_nums[$coin_field];
                    }
                    // transaction_log.ios_coin 及 googlepay_coin 包含旧档位（balance 表 ios/googlepay）及新档位（pay_account 表 new_ios/new_googlepay）
                    // 退款时需要区分开，避免重复计算
                    if (in_array($coin_field, ['ios', 'googlepay']) && array_key_exists('new_' . $coin_field, $pay_account_nums)) {
                        $costs[$coin_field] -= $pay_account_nums['new_' . $coin_field];
                    }
                }
                $legacy_change_accounts = PayAccounts::legacyBalanceToAccounts($costs, $transaction_log->from_id);
                foreach ($legacy_change_accounts as &$acc) {
                    $acc->consume_amount = -$acc->balance;
                }
                unset($acc);
                // 将钻石花费（如付费问答）退回账户余额
                PayAccount::updateAccounts(array_merge($change_accounts, $legacy_change_accounts));
                break;
            default:
                throw new Exception('不支持的状态');
        }
        self::updateAll(
            [
                'status' => $target_status,
                'modified_time' => $_SERVER['REQUEST_TIME'],
            ],
            'tid = :tid AND status = :status',
            [':tid' => $transaction_log->id, ':status' => $current_status]
        );
    }

    public static function confirm(TransactionLog $transaction_log)
    {
        self::proceed($transaction_log, self::STATUS_CONFIRM);
    }

    public static function cancel(TransactionLog $transaction_log)
    {
        self::proceed($transaction_log, self::STATUS_CANCEL);
    }
}
