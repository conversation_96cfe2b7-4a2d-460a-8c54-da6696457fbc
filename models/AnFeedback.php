<?php

namespace app\models;

use app\components\util\Image;
use app\components\util\MUtils;
use app\forms\AnFeedbackForm;
use missevan\storage\StorageClient;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\db\Expression;
use yii\web\HttpException;
use yii\web\UploadedFile;
use yii\helpers\Json;

/**
 * This is the model class for table "an_feedback".
 *
 * @property integer $id
 * @property integer $ticket_id
 * @property integer $type 反馈类型
 * @property string $ip 用户提交反馈时的 IP
 * @property string $equip_id 设备 ID
 * @property string $buvid BUVID
 * @property string $content 反馈内容
 * @property integer $client 客户端平台
 * @property integer $create_time 发送时间
 * @property integer $modified_time 修改时间
 * @property integer $status 消息属性
 * @property integer $user_id 用户 ID
 * @property string $images 反馈图片
 */
class AnFeedback extends ActiveRecord
{
    // 反馈图片最短边的最小值为 42 px，最长边的最大值为 135 px
    const FEED_BACK_IMAGE_MIN = 42;
    const FEED_BACK_IMAGE_MAX = 135;
    // 设备和 App 等信息的正则匹配
    const EQUIPMENT_INFO_REGEX = '/(\n\(版本:.*\))|(---小尾巴:.*)?$/';
    const CONTENT_MAX_LENGTH = 255;
    // 与客服私信（原意见反馈）
    // TODO: 原意见反馈已不再使用，需要删除
    const FEEDBACK_DIRECT = 0;
    // 反馈类型 0：与客服私信（原意见反馈）；1：其他；2：播放问题；3：闪退
    // 4：注册登录问题；5：购买支付；6：下载问题；7：首页；8：直播问题；
    const TYPE_FEEDBACK_DIRECT = 0;
    const TYPE_FEEDBACK_OTHER = 1;
    const TYPE_FEEDBACK_SOUND_PLAY = 2;
    const TYPE_FEEDBACK_BROKEN = 3;
    const TYPE_FEEDBACK_LOGIN_REGIST = 4;
    const TYPE_FEEDBACK_BUY = 5;
    const TYPE_FEEDBACK_DOWNLOAD = 6;
    const TYPE_FEEDBACK_HOMEPAGE = 7;
    const TYPE_FEEDBACK_LIVE = 8;
    const TYPE_FEEDBACK_ARR = [
        self::TYPE_FEEDBACK_OTHER => '其他',
        self::TYPE_FEEDBACK_SOUND_PLAY => '播放问题',
        self::TYPE_FEEDBACK_BROKEN => '闪退',
        self::TYPE_FEEDBACK_LOGIN_REGIST => '注册登录问题',
        self::TYPE_FEEDBACK_BUY => '购买支付问题',
        self::TYPE_FEEDBACK_DOWNLOAD => '下载问题',
        self::TYPE_FEEDBACK_HOMEPAGE => '首页问题',
        self::TYPE_FEEDBACK_LIVE => '直播问题',
    ];
    // 消息属性：0：用户已读客服也已读；1：客服未读用户反馈；2：用户未读客服回复；4：客服身份；6：客服的回复未被用户查看
    const STATUS_STAFF_USER_READ = 0;
    const STATUS_STAFF_UNREAD = 1;
    const STATUS_STAFF = 4;
    const STATUS_USER_UNREAD = 2;
    const STATUS_STAFF_REPLY_USER_UNREAD = 6;

    public $user;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'an_feedback';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['client', 'create_time', 'modified_time', 'status', 'user_id'], 'integer'],
            [['equip_id'], 'string', 'max' => 44],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'ticket_id' => '用户针对补充的或客服针对回复的 an_feedback_ticket 表 ID',
            // 反馈类型 0：与客服私信（原意见反馈）；1：其他；2：播放问题；3：闪退；
            // 4：注册登录问题；5：购买支付；6：下载问题；7：首页；8：直播问题；
            'type' => '反馈类型',
            'ip' => '用户提交反馈时的 IP',
            'equip_id' => '设备 ID',
            'content' => '反馈内容',
            'client' => '客户端平台', // 1 安卓、2 iOS、6 HarmonyOS
            'create_time' => '发送时间',
            'modified_time' => '更新时间',
            // status 二进制运算：1 位用户的反馈是否已被客服阅读（1 未读，0 已读）；
            // 2 位客服的回复是否已被用户阅读（1 未读，0 已读）；3 位标明身份（1 客服，0 用户）
            'status' => '消息属性',
            'user_id' => '用户 ID',
            // JSON 格式的图片地址数组
            'images' => '反馈图片',
            'buvid' => 'BUVID',
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->content) {
            // 只在获取反馈详情接口里查询了该字段
            // 显示的反馈内容需要过滤掉设备和 App 的版本信息
            $this->content = preg_replace(self::EQUIPMENT_INFO_REGEX, '', $this->content);
        }
        if ($this->images) {
            $this->images = json_decode($this->images, true);
        }
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        if (!empty($this->images)) {
            $this->images = Json::encode($this->images);
        } else {
            $this->images = null;
        }
        return true;
    }

    public static function getFeedback($equip_id, $page_size)
    {
        //默认的提醒消息
        $feedback_QQ = ['android' => 488461136, 'ios' => 450959169];
        $equipment = Yii::$app->equip;
        if ($equipment->isAndroidOrHarmonyOS()) {
            $QQ = $feedback_QQ['android'];
        } else {
            $QQ = $feedback_QQ['ios'];
        }
        $default_msg = '感谢使用猫耳FM，我是为你服务的M娘。' .
            '使用中遇到任何问题或者对 App 有什么建议，欢迎随时向我吐槽哦，' .
            '我会认真阅读每一条反馈哒。（还可以加入猫耳FM官方用户反馈 QQ 群：' . $QQ .
            '，优秀的反馈和建议会奖励小鱼干哦！）';
        $m_tip = [
            'id' => 1,
            'equip_id' => $equip_id,
            'content' => $default_msg,
            'client' => 1,
            'create_time' => 1,
            'user_id' => null,
            'user' => '客服'
        ];
        // 查询与客服私信的数据并处理
        $condition = 'equip_id = :equip_id AND ticket_id = :ticket_id';
        $params = [':equip_id' => $equip_id, ':ticket_id' => self::FEEDBACK_DIRECT];
        $anfeedback = AnFeedback::find()
            ->where($condition, $params)
            ->orderBy(['create_time' => SORT_DESC])
            ->all();
        foreach ($anfeedback as $key => &$feedback) {
            if ($feedback->status & 4) {
                $feedback->user = '客服';
            } else {
                $feedback->user = '我';
            }
            unset($feedback->status);
            if (empty($feedback->content) && empty($feedback->images)) {
                // 过滤掉创建了反馈 ID，但未上传图片情况下的空白消息记录
                unset($anfeedback[$key]);
            } elseif (!empty($feedback->images)) {
                $return = [];
                foreach ($feedback->images as $image) {
                    $return[] = [
                        'origin' => StorageClient::getFileUrl($image['origin']),
                        'mini' => StorageClient::getFileUrl($image['mini']),
                        'width' => $image['width'],
                        'height' => $image['height'],
                        'index' => $image['index'],
                    ];
                }
                $feedback->images = $return;
                unset($feedback->content);
            } else {
                unset($feedback->images);
            }
        }
        // 重新建立数组索引
        $anfeedback = array_values($anfeedback);
        //加入需要的提醒消息并分页
        array_push($anfeedback, $m_tip);
        $returnModel = self::array_pagination($anfeedback, $page_size);
        // 更新为已读状态
        AnFeedback::updateAll(['status' => new Expression('status & 5')],
            'equip_id = :equip_id AND ticket_id = :ticket_id',
            [':equip_id' => $equip_id, ':ticket_id' => self::FEEDBACK_DIRECT]);
        return $returnModel;
    }

    private static function array_pagination($array, $page_size)
    {
        $page = Yii::$app->request->get('page', 1);
        $page_size = $page_size > 0 ? $page_size : 1;
        $start = ($page - 1) * $page_size; #计算每次分页的开始位置
        $totals = count($array);
        $countpage = ceil($totals / $page_size); #计算总页面数
        $pagedata = array_slice($array, $start, $page_size);
        return [
            'Datas' => $pagedata,
            'pagination' => [
                'p' => $page,
                'count' => $totals,
                'maxpage' => $countpage,
                'pagesize' => $page_size
            ]
        ];
    }

    /**
     * 创建图片反馈
     *
     * @param int $feedback_id 反馈 ID
     * @param int $images_num 多图上传时，用户选择上传的图片数量
     * @param int $image_index 多图上传时，图片位置
     * @param int $retry 是否是重复发送的请求 0：否；1：是
     * @param string $equipment 小尾巴
     * @param int $user_id 用户 ID
     * @return int 返回图片反馈 ID
     * @throws HttpException
     */
    public static function createImageFeedback($feedback_id, $images_num, $image_index, $retry, $equipment, $user_id)
    {
        $client = Yii::$app->equip->getOs();
        $equip_id = Yii::$app->equip->getEquipId();
        if (!$equip_id || !$client || !$equipment) {
            throw new HttpException(400, '参数错误');
        }
        if (!$feedback_id && $images_num > 1) {
            // 多图上传之前，先给客户端返回反馈 ID
            $feedback = new AnFeedback();
        } elseif (!$feedback_id && $images_num === 1) {
            // 用户选择一张图片上传
            $feedback = new AnFeedback();
            $image = UploadedFile::getInstanceByName('image_file');
            $feedback->uploadFeedbackImage($image, $image_index);
        } elseif ($feedback_id && $images_num > 1) {
            // 用户选择多图上传
            $transaction = Yii::$app->db->beginTransaction();
            try {
                // 加行锁，防止并发上传图片时，图片传丢
                $feedback = AnFeedback::findBySql('SELECT id, images, create_time FROM ' . AnFeedback::tableName() .
                        ' WHERE id = :id AND equip_id = :equip_id AND client = :client FOR UPDATE')
                    ->addParams([':id' => $feedback_id, ':equip_id' => $equip_id, ':client' => $client])->one();
                if (!$feedback) {
                    throw new HttpException(404, '找不到相关反馈内容');
                }
                if ((!empty($feedback->images) && count($feedback->images) > $images_num) ||
                        $_SERVER['REQUEST_TIME'] > $feedback->create_time + HALF_HOUR) {
                    // 用户选择多图上传时，这些图片上传的时间范围是 30 分钟内
                    throw new HttpException(408, '上传失败，请重新上传', 100010015);
                }
                if (!$retry || ($retry && !$feedback->checkRepeatedImage($image_index))) {
                    $image = UploadedFile::getInstanceByName('image_file');
                    $feedback->uploadFeedbackImage($image, $image_index);
                    if (!$feedback->save()) {
                        throw new HttpException(400, '上传失败，请重新上传');
                    }
                    $transaction->commit();
                } else {
                    // 上传的图片已存在则无需重复上传
                    $transaction->rollBack();
                }
                return $feedback->id;
            } catch (\Exception $e) {
                $transaction->rollBack();
                throw $e;
            }
        } else {
            throw new HttpException(400, '参数错误');
        }
        return $feedback->addImageFeedback($user_id, $equip_id, $client, $equipment);
    }

    public function uploadFeedbackImage($image, int $image_index)
    {
        if (!$image || empty($image->tempName)) {
            throw new HttpException(400, '图片上传失败，请重试');
        }
        $pic_path = $image->tempName;
        // 缩略图文件地址和扩展名
        [$mini_image_path, $extension, $image_width, $image_height] = Image::resizeImage($pic_path,
            self::FEED_BACK_IMAGE_MIN, self::FEED_BACK_IMAGE_MAX);
        $image_path = MUtils2::generateFilePath($pic_path, strtolower($extension));
        $image_path = urldecode($image_path);
        // 原文件 storage 存放地址
        $origin_save_name = 'mimages/' . $image_path;
        // 缩略图 storage 存放地址
        $mini_save_name = 'mimagesmini/' . $image_path;
        // 原图和缩略图都上传到 storage
        $storage_origin_image = Yii::$app->storage->upload($pic_path, $origin_save_name, true, [], true);
        $storage_mini_image = Yii::$app->storage->upload($mini_image_path, $mini_save_name, true, [], true);
        $new_image = [
            'origin' => $storage_origin_image,
            'mini' => $storage_mini_image,
            'width' => $image_width,
            'height' => $image_height,
            'index' => $image_index
        ];
        $this->addImage($new_image);
    }

    /**
     * 调整 images 字段
     *
     * @param array $new_image 新的反馈图片信息
     * @throws HttpException
     */
    protected function addImage($new_image)
    {
        if (empty($new_image) || !array_key_exists('index', $new_image)) {
            throw new HttpException(400, '参数错误');
        }
        $images = [$new_image];
        if (!empty($this->images)) {
            $images = array_merge($this->images, $images);
            // 转换成数组并根据 index 排序
            usort($images, function ($before, $current) {
                return $before['index'] <=> $current['index'];
            });
        }
        $this->images = $images;
    }

    /**
     * 保存反馈图片
     *
     * @param int $user_id 用户 ID
     * @param String $equip_id 设备号
     * @param int $client 操作系统 1：安卓；2：iOS
     * @param string $equipment 小尾巴
     * @return int 返回反馈 ID
     * @throws HttpException
     */
    public function addImageFeedback(int $user_id, string $equip_id, int $client, $equipment)
    {
        if ($this->isNewRecord) {
            $this->equip_id = $equip_id;
            $this->client = $client;
            $this->create_time = $_SERVER['REQUEST_TIME'];
            $this->status = self::STATUS_STAFF_UNREAD;
            $this->user_id = $user_id;
            $this->content = $equipment;
            $this->type = self::TYPE_FEEDBACK_DIRECT;
            $this->ticket_id = self::FEEDBACK_DIRECT;
            $this->ip = Yii::$app->request->userIP;
        }
        if (!$this->save()) {
            throw new HttpException(400, MUtils::getFirstError($this));
        }
        return $this->id;
    }

    /**
     * 用户选择多图上传的时候，验证图片是否重复
     *
     * @param int $image_index 图片位置
     * @return bool 重复返回 true, 否则返回 false
     */
    public function checkRepeatedImage(int $image_index)
    {
        if (empty($this->images)) {
            return false;
        }
        return in_array($image_index, array_column($this->images, 'index'));
    }

    /**
     * 通过用户 ID 获取反馈详情
     *
     * @param int $ticket_id 反馈 ID
     * @param int $user_id 用户 ID
     * @param int $page_size 分页大小
     * @return array|null 反馈记录，若用户无 $ticket_id 对应无反馈记录，返回 null
     */
    public static function getTicketByUserId(int $ticket_id, int $user_id, int $page_size = PAGE_SIZE_20)
    {
        // 判断是否有权限获取该 ticket_id 对应的反馈记录
        if (!AnFeedbackTicket::find()->where('id = :id AND user_id = :user_id',
            [':id' => $ticket_id, ':user_id' => $user_id])->exists()) {
            return null;
        }
        return self::getTicket($ticket_id, $page_size);
    }

    /**
     * 通过设备号获取反馈详情
     *
     * @param int $ticket_id 反馈 ID
     * @param string $equip_id 设备号
     * @param int $page_size 分页大小
     * @return array|null 反馈记录，若无反馈记录，返回 null
     */
    public static function getTicketByEquipId(int $ticket_id, string $equip_id, int $page_size = PAGE_SIZE_20)
    {
        // 判断是否有权限获取该 ticket_id 对应的反馈记录
        // 按设备号获取反馈信息时，视作游客，用户 ID 需要为 0
        if (!AnFeedbackTicket::find()->where('id = :id AND equip_id = :equip_id AND user_id = 0',
            [':id' => $ticket_id, ':equip_id' => $equip_id])->exists()) {
            return null;
        }
        return self::getTicket($ticket_id, $page_size);
    }

    /**
     * 获取反馈详情
     *
     * @param int $ticket_id 反馈 ID
     * @param int $page_size 分页大小
     * @return array|null 反馈记录，若无反馈记录，返回 null
     */
    private static function getTicket(int $ticket_id, int $page_size)
    {
        $records = AnFeedback::find()
            ->alias('f')
            ->select('f.id, f.content, f.images, f.create_time, f.status, f.ticket_id, f.type, f.user_id,
                u.username, u.avatar')
            ->leftJoin(Mowangskuser::tableName() . ' u', 'f.user_id = u.id')
            ->where('f.ticket_id = :ticket_id', [':ticket_id' => $ticket_id])
            ->orderBy(['f.create_time' => SORT_DESC])
            ->asArray()
            ->all();
        if (empty($records)) {
            return null;
        }
        $result = array_map(function ($item) {
            $info = [
                'id' => (int)$item['id'],
                'content' => preg_replace(self::EQUIPMENT_INFO_REGEX, '', $item['content']),
                'create_time' => (int)$item['create_time'],
                'status' => (int)$item['status'],
                'ticket_id' => (int)$item['ticket_id'],
                'type' => (int)$item['type']
            ];
            if ((int)$item['status'] & self::STATUS_STAFF) {
                // 客服
                $info['username'] = 'M娘';
                $info['iconurl'] = StorageClient::getFileUrl(Yii::$app->params['feedbackIconUrl']);
            } else {
                // 用户
                $info['images'] = self::getFeedbackImages($item['images']);
                $info['type_name'] = self::TYPE_FEEDBACK_ARR[(int)$item['type']];
                $info['username'] = $item['username'] ?: '一个有想法的游客 _(:з」∠)_';
                $info['iconurl'] = $item['avatar'] ? Yii::$app->params['avatarUrl'] . $item['avatar'] :
                    Yii::$app->params['avatarUrl'] . 'icon01.png';
            }
            return $info;
        }, $records);
        $return_model = self::array_pagination($result, $page_size);
        // 调整显示顺序，使客户端可以上拉加载显示老数据
        usort($return_model['Datas'], function ($a, $b) {
            return $a['create_time'] <=> $b['create_time'];
        });
        // 更新为已读状态
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $status_expression = new Expression('status & 5');
            AnFeedbackTicket::updateAll(['status' => $status_expression], 'id = :id', [':id' => $ticket_id]);
            AnFeedback::updateAll(['status' => $status_expression], 'ticket_id = :ticket_id',
                [':ticket_id' => $ticket_id]);
            $transaction->commit();
            return $return_model;
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * 处理图片信息
     *
     * @param string $images json 格式的图片信息
     * @return array 返回处理好的图片信息
     */
    public static function getFeedbackImages($images)
    {
        $return = [];
        if ($images) {
            $images_arr = json_decode($images, true);
            foreach ($images_arr as $image) {
                $return[] = [
                    'origin' => StorageClient::getFileUrl($image['origin']),
                    'mini' => StorageClient::getFileUrl($image['mini']),
                    'width' => $image['width'],
                    'height' => $image['height'],
                ];
            }
        }
        return $return;
    }

    /**
     * 获取帮助中心入口处提醒
     *
     * @param array $ticket_ids 反馈工单 IDs
     * @return array
     */
    public static function getFeedbackNotice(array $ticket_ids)
    {
        $res = ['notice' => 0, 'last_time' => 0];
        if (empty($ticket_ids)) {
           return $res;
        }
        $ticket_ids = array_map('intval', $ticket_ids);
        // 获取未读的客服对于反馈的回复提醒（status & 2 为 2）
        // 获取最后的客服回复（status & 4 不为 0）
        $res['notice'] = AnFeedbackTicket::find()
            ->where('status & :status', [':status' => AnFeedback::STATUS_USER_UNREAD])
            ->andWhere(['id' => $ticket_ids])
            ->exists() ? 1 : 0;
        if ($res['notice']) {
            $res['last_time'] = (int)AnFeedback::find()->select('create_time')
                ->where('status & :status', [':status' => AnFeedback::STATUS_STAFF])
                ->andWhere(['ticket_id' => $ticket_ids])
                ->orderBy('create_time DESC')->limit(1)->scalar();
        }
        return $res;
    }

    /**
     * 根据设备号获取帮助中心入口处提醒
     *
     * @param string $equip_id 设备号
     * @return array
     */
    public static function getFeedbackNoticeByEquipId(string $equip_id)
    {
        $ticket_ids = AnFeedbackTicket::find()->select('id')
            ->where('equip_id = :equip_id AND user_id = 0',
                [':equip_id' => $equip_id])
            ->column();
        return self::getFeedbackNotice($ticket_ids);
    }

    /**
     * 根据用户 ID 获取帮助中心入口处提醒
     *
     * @param int $user_id 用户 ID
     * @return array
     */
    public static function getFeedbackNoticeByUserId(int $user_id)
    {
        $ticket_ids = AnFeedbackTicket::find()->select('id')
            ->where('user_id = :user_id', [':user_id' => $user_id])
            ->column();
        return self::getFeedbackNotice($ticket_ids);
    }
}
