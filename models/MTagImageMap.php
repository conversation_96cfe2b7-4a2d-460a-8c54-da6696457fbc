<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "m_tag_image_map".
 *
 * @property integer $tag_id
 * @property integer $image_id
 */
class MTagImageMap extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_tag_image_map';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['tag_id', 'image_id'], 'required'],
            [['tag_id', 'image_id'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'tag_id' => 'Tag ID',
            'image_id' => 'Image ID',
        ];
    }
}
