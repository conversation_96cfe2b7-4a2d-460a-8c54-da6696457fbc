<?php

namespace app\models;

use app\components\random\ItemInterface;
use app\components\util\MUtils;
use Exception;
use missevan\storage\StorageClient;
use missevan\storage\UposClient;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "card".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 * @property string $title 卡片标题
 * @property string $icon 卡片图标地址
 * @property string $cover 抽卡图片
 * @property string $play_cover 播放页图片
 * @property string $subtitles 字幕
 * @property string $intro 卡片介绍
 * @property int $duration 音频时长
 * @property string $voice 语音地址
 * @property string $voice_source 语音源地址
 * @property int $level 卡片等级，1: N, 2: R, 3: SR, 4: SSR
 * @property int $special 卡片类型，0 为普通卡片，1 为节日卡，2 为小剧场卡，3 为免费卡，4 热度福利卡
 * @property int $rank 卡包显示顺序
 * @property int $push 普通卡片季度推送顺序，从 1 开始。特殊卡片自定义
 * @property int $card_package_id 季包 ID
 * @property int $coupon 重复价值荣誉点
 * @property int $price 兑换所需荣誉点
 * @property int $work_id 作品 ID，work 表主键
 * @property int $role_id 角色 ID, role 表主键
 * @property int $is_online 是否上架，1 为是，0 为否
 * @property string $pics 播放页不同时间点显示的图片
 */
class Card extends ActiveRecord implements ItemInterface
{
    // 小剧场卡配音人员
    const EPISODE_CARD_NAME = [Work::ID_QUANZHI => '荣耀全员'];

    const LEVEL_N = 1;
    const LEVEL_R = 2;
    const LEVEL_SR = 3;
    const LEVEL_SSR = 4;

    const LEVEL_STR = [
        self::LEVEL_N => 'N',
        self::LEVEL_R => 'R',
        self::LEVEL_SR => 'SR',
        self::LEVEL_SSR => 'SSR'
    ];

    // 卡片类型，0：普通卡片；1：节日卡；2：剧场卡；3：免费卡；4：热度福利；5：求签普通卡；6：求签小剧场
    const SPECIAL_NORMAL = 0;
    const SPECIAL_FESTIVAL = 1;
    const SPECIAL_EPISODE = 2;
    const SPECIAL_FREE = 3;
    const SPECIAL_HOTCARD = 4;
    const SPECIAL_OMIKUJI = 5;
    const SPECIAL_OMIKUJI_EPISODE = 6;

    // 免费卡（special = 3）有提醒时 price = 1，无提醒时 price = 0
    const PRICE_FREE_CARD_NOT_NOTICE = 0;
    const PRICE_FREE_CARD_HAVE_NOTICE = 1;

    // 是否下架，0：已下架；1：未下架
    const ONLINE = 1;
    const OFFLINE = 0;

    const EVENT_INTERVAL_1 = [
        'from' => '2018-05-29',
        'to' => '2018-08-31',
    ];
    // 从 2018-06-01 开始之后的活动周期为 3 个月
    const EVENT_FORMAL_START_DATE = '2018-06-01';
    const EVENT_MONTH_LENGTH = 3;

    // 给客户端的卡片显示状态：0：未解锁（获得）；2：未收听；3：已收听；4：下架
    const STATUS_LOCK = 0;
    const STATUS_NOTICE = 2;
    const STATUS_LISTENED = 3;
    const STATUS_OFFLINE = 4;

    // 吉凶等级，依次为大吉、吉、半吉、小吉、末小吉、末吉、凶
    const BLESSING_GREAT = 7;
    const BLESSING_MIDDLE = 6;
    const BLESSING_SMALL = 5;
    const BLESSING_HALF = 4;
    const BLESSING_ENDING_SMALL = 3;
    const BLESSING_ENDING = 2;
    const BLESSING_CURSE = 1;
    // 吉凶描述
    const BLESSINGS = [
        self::BLESSING_GREAT => '大吉',
        self::BLESSING_MIDDLE => '吉',
        self::BLESSING_SMALL => '半吉',
        self::BLESSING_HALF => '小吉',
        self::BLESSING_ENDING_SMALL => '末小吉',
        self::BLESSING_ENDING => '末吉',
        self::BLESSING_CURSE => '平',
    ];

    // 默认水印
    const DEFAULT_WATERMARK = 'oss://image/works/201806/01/a5d58030422c0fa41e38dfdcc8f5b58d115116.png';

    // 是否为新卡
    public $is_new;
    // 得到卡片时得到的荣耀点
    public $given_coupon;
    // 提前解锁卡片时需要的荣耀点
    public $unlock_price;

    // 卡片所属作品名称
    public $work_title;
    // 卡片所属作品图标
    public $work_icon;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'card';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db2');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['create_time', 'modified_time', 'title', 'icon', 'cover', 'play_cover', 'subtitles', 'intro',
                'voice', 'voice_source', 'level', 'special', 'rank', 'push', 'card_package_id', 'coupon',
                'price', 'work_id', 'role_id'], 'required'],
            [['create_time', 'modified_time', 'push', 'card_package_id', 'coupon', 'price', 'work_id',
                'role_id', 'level', 'special', 'rank', 'is_online'], 'integer'],
            [['title'], 'string', 'max' => 20],
            [['icon', 'cover', 'play_cover', 'subtitles', 'voice', 'voice_source'], 'string', 'max' => 120],
            [['intro'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'title' => '标题',
            'icon' => '图标',
            'cover' => '抽卡页图片',
            'play_cover' => '播放背景图',
            'subtitles' => '字幕地址',
            'intro' => '介绍',
            'duration' => '音频时长',
            'voice' => '音频地址',
            'voice_source' => '语音源地址',
            'level' => '卡片等级',
            'special' => '卡片类型',
            'rank' => '展示顺序',
            'push' => '推送顺序',
            'card_package_id' => '卡包 id',
            'coupon' => '获得荣誉点',
            'price' => '兑换所需荣誉点',
            'work_id' => '作品 ID',
            'role_id' => '角色 ID',
            'is_online' => '是否上线', // 下线需要同时删除 redis 中对应的元素 id
            'pics' => '播放页不同时间点显示的图片',
        ];
    }

    /**
     * 获得季包下的卡片
     *
     * @param int $card_package_id 季包 ID
     * @param int|null|array $special 卡片类型，0：普通卡片；1：节日卡；2：剧场卡；3：免费卡
     * @return static[] 卡片对象组成的数组
     * @throws HttpException $special 参数不合法
     */
    public static function getPackageCards(int $card_package_id, $special = null)
    {
        $conditions = ['card_package_id' => $card_package_id, 'is_online' => self::ONLINE];
        if ($special !== null) {
            if (!is_int($special) && !MUtils2::isUintArr($special)) {
                throw new HttpException(500, '查询季包下卡片参数错误');
            }
            $conditions = array_merge($conditions, ['special' => $special]);
        }
        $cards = self::find()->select('id, level, card_package_id, role_id, special, work_id, coupon, push')
            ->where($conditions)
            ->orderBy('push')
            ->all();
        return $cards;
    }

    /**
     * 获取卡包某个分类下的卡片
     *
     * @param array $types 卡包中卡片的分类组成的数组
     * @return static[] 卡片对象组成的数组
     */
    public static function getRandomItemsByTypes(array $types): array
    {
        $redis = Yii::$app->redis;
        $card_ids = [];
        foreach ($types as $type) {
            $card_id = $redis->sRandMember($type);
            if ($card_id) $card_ids[] = $card_id;
        }
        $id_cards = self::find()
            ->where(['id' => array_unique($card_ids), 'is_online' => self::ONLINE])
            ->indexBy('id')
            ->all();
        $cards = [];
        foreach ($card_ids as $id) {
            if (isset($id_cards[$id])) $cards[] = $id_cards[$id];
        }
        return $cards;
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->icon) $this->icon = StorageClient::getFileUrl($this->icon);
        if ($this->cover) $this->cover = StorageClient::getFileUrl($this->cover);
        if ($this->play_cover) $this->play_cover = StorageClient::getFileUrl($this->play_cover);
        if ($this->subtitles) $this->subtitles = StorageClient::getFileUrl($this->subtitles);
        if ($this->pics = json_decode($this->pics, true) ?: []) {
            $this->pics = array_values(array_map(function ($item) {
                $item['img_url'] = StorageClient::getFileUrl($item['img_url']);
                return $item;
            }, $this->pics));
        }
        if (isset($this->voice_source)) {
            // 业务中不需要下发音频源地址，统一在 afterFind unset
            unset($this->voice_source);
        }
    }

    /**
     * 获取某个角色下的所有卡片
     *
     * @param $role_id int 角色 ID
     * @return static[] 卡片对象组成的数组
     */
    public static function getRoleCards(int $role_id)
    {
        $cards = self::find()->where([
                'role_id' => $role_id,
                'special' => [self::SPECIAL_NORMAL, self::SPECIAL_FESTIVAL, self::SPECIAL_FREE],
            ])
            ->select('id AS card_id, icon, title, intro, level, special, card_package_id, price, is_online, push')
            ->orderBy('level DESC, rank ASC')->asArray()->all();
        $cards = array_map(function ($item) {
            $item['card_id'] = (int)$item['card_id'];
            $item['special'] = (int)$item['special'];
            $item['is_online'] = (int)$item['is_online'];
            $item['card_package_id'] = (int)$item['card_package_id'];
            $item['level'] = (int)$item['level'];
            $item['price'] = (int)$item['price'];
            $item['push'] = (int)$item['push'];
            $item['icon'] = StorageClient::getFileUrl($item['icon']);

            return $item;
        }, $cards);

        return $cards;
    }

    /**
     * 返回吉凶等级相关描述
     * 求签卡中 role_id 字段存储了“吉凶”等级
     *
     * @param int $blessing 吉凶等级
     * @return string 吉凶描述，如“大吉”
     */
    public static function getBlessingName(int $blessing)
    {
        return self::BLESSINGS[$blessing] ?? '未知';
    }

    /**
     * 获取用户拥有的某个角色下的所有卡片
     *
     * @param $role_id int 角色 ID
     * @param $user_id int 用户 ID
     * @return static[] 卡片对象组成的数组
     */
    public static function getAllCards(int $role_id, int $user_id)
    {
        $cards = self::getRoleCards($role_id);
        $user_cards = GetCard::userCards(array_column($cards, 'card_id'), $user_id);
        $user_cards_status = array_column($user_cards, 'status', 'card_id');
        $free_listened = array_column(FreeNoticeListened::getHistoryByRole($role_id, $user_id), 'card_id');

        $cards = array_map(function ($item) use ($user_cards_status, $free_listened) {
            $item['status'] = GetCard::SHOW_DISACTIVE;
            if (Card::SPECIAL_FREE === $item['special']) {
                $item['status'] = GetCard::SHOW_NOTICE;
                if (in_array($item['card_id'], $free_listened)) $item['status'] = GetCard::SHOW_LISTENED;
            }

            if ($user_cards_status[$item['card_id']] ?? false) {
                switch ($user_cards_status[$item['card_id']]) {
                    case GetCard::STATUS_UNLOCK:
                        $item['status'] = GetCard::SHOW_NOTICE;
                        break;
                    case GetCard::STATUS_LISTENED:
                        $item['status'] = GetCard::SHOW_LISTENED;
                        break;
                    default:
                        $item['status'] = GetCard::SHOW_ACTIVE;
                }
            }

            if (Card::OFFLINE === $item['is_online']) {
                if (GetCard::SHOW_DISACTIVE === $item['status']) return null;
                $item['status'] = GetCard::SHOW_UNSHELVE;
            }
            return $item;
        }, $cards);
        $cards = array_filter($cards);

        return $cards;
    }

    /**
     * 获取卡片中的节日（福利）卡
     *
     * @param array $cards 卡片组成的数组
     * @return array 福利卡组成的数组
     */
    public static function getFestivalCards(array $cards)
    {
        $festival_cards = array_filter($cards, function ($card) {
            return self::SPECIAL_FESTIVAL === $card['special']
                && in_array($card['status'], [GetCard::SHOW_NOTICE, GetCard::SHOW_LISTENED, GetCard::SHOW_UNSHELVE]);
        });
        usort($festival_cards, function ($a, $b) {
            return $a['push'] < $b['push'];
        });
        return $festival_cards;
    }

    /**
     * 购买季包添加节日（福利）卡
     *
     * @param int $work_id 作品 ID
     * @param int $user_id 用户 ID
     * @param int|int[] $role_id 角色 ID
     * @return bool
     */
    public static function insertFestivalCards(int $work_id, int $user_id, $role_id)
    {
        $from_time = strtotime(date('Y-m-01'));

        $now = $_SERVER['REQUEST_TIME'] ?? time();
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_VOICE_WORK_PACKAGE_FESTIVAL_EVENT_DATE, $work_id);
        $festival_dates = $redis->hGetAll($key);
        if ($festival_dates && $now >= $festival_dates['from_time'] && $now <= $festival_dates['to_time']) {
            $to_time = $festival_dates['to_time'];
        } else {
            $months_elapsed = self::getMonthsInterval(strtotime(self::EVENT_FORMAL_START_DATE), $now);
            $months_left = self::EVENT_MONTH_LENGTH - $months_elapsed % self::EVENT_MONTH_LENGTH;
            $to_time = strtotime("+{$months_left} month", $from_time);
        }

        $festival_cards = self::find()->select('id, push, work_id, role_id, level, special, card_package_id')
            ->where(['role_id' => $role_id, 'special' => self::SPECIAL_FESTIVAL, 'is_online' => self::ONLINE])
            ->andWhere('push >= :from_time AND push < :to_time',
                [':from_time' => $from_time, ':to_time' => $to_time])->all();
        $user_cards = array_column(GetCard::userCards(array_column($festival_cards, 'id'), $user_id), 'card_id');

        $buy_time = strtotime('midnight') + ONE_DAY;
        $notice = 0;
        $get_cards = $get_card_logs = [];
        foreach ($festival_cards as $card) {
            if (in_array($card->id, $user_cards)) continue;
            $status = GetCard::STATUS_LOCK;
            if ($buy_time > $card->push) {
                $status = GetCard::STATUS_UNLOCK;
                $notice++;
            }

            $get_card = GetCard::initialize($card);
            $get_card->user_id = $user_id;
            $get_card->appear_time = $card->push;
            $get_card->status = $status;
            $get_cards[] = $get_card;

            $get_card_log = GetCardLog::initialize($card);
            $get_card_log->user_id = $user_id;
            $get_card_log->method_of_obtaining = GetCardLog::TYPE_BUY;
            $get_card_log->card_package_id = 0;
            $get_card_log->coupon = 0;
            $get_card_logs[] = $get_card_log;
        }
        if ($get_cards) {
            GetCard::addCards($get_cards);
            GetCardLog::addLogs($get_card_logs);
            return true;
        }
        return false;
    }

    private static function getMonthsInterval($from_time, $to_time)
    {
        $from_date = date('Y-m', $from_time);
        $to_date = date('Y-m', $to_time);
        [$from_year, $from_month] = explode('-', $from_date);
        [$to_year, $to_month] = explode('-', $to_date);
        $months = ($to_year - $from_year) * 12 + $to_month - $from_month;
        return $months;
    }

    public static function getFreeCards($work_id, $role_ids = [])
    {
        $query = self::find()->select('id, role_id')
            ->where([
                'work_id' => $work_id,
                'is_online' => self::ONLINE,
                'special' => self::SPECIAL_FREE,
                'price' => self::PRICE_FREE_CARD_HAVE_NOTICE,
            ]);
        if ($role_ids) $query->andWhere(['role_id' => $role_ids]);
        return $query->all();
    }

    public static function getFreeNotNoticeCard($role_ids)
    {
        $cards = self::find()
            ->select('id, role_id, create_time AS last_time, title, level, card_package_id')
            ->where([
                'role_id' => $role_ids,
                'special' => self::SPECIAL_FREE,
                'price' => self::PRICE_FREE_CARD_NOT_NOTICE,
                'is_online' => self::ONLINE,
            ])->asArray()->all();
        $cards = array_map(function ($item) {
            $item['id'] = (int)$item['id'];
            $item['card_id'] = (int)$item['id'];
            $item['role_id'] = (int)$item['role_id'];
            $item['last_time'] = (int)$item['last_time'];
            $item['level'] = (int)$item['level'];
            $item['card_package_id'] = (int)$item['card_package_id'];
            return $item;
        }, $cards);
        return $cards;
    }

    /**
     * 获取作品下的热度卡并根据不同用户赋予卡片对应显示状态
     *
     * @param int $work_id 作品 ID
     * @param int $user_id 用户 ID
     * @return array 一张或多张卡片信息组成的数组
     */
    public static function getHotCards(int $work_id, int $user_id)
    {
        // 获取作品下全部热度卡
        $hot_cards = self::getHotCardsByWork($work_id);
        $card_ids = array_column($hot_cards, 'id');
        $has_listened_card_ids = FreeNoticeListened::getListenedCards($card_ids, $user_id);
        return array_filter(array_map(function ($hot_card) use ($has_listened_card_ids) {
            if ($hot_card->is_online === Card::OFFLINE && $hot_card->push === 0) {
                // 语音卡已被下架并且未被解锁时，不展示给用户
                return null;
            }
            $card = [
                'id' => $hot_card->id,
                'title' => $hot_card->title,
                'intro' => $hot_card->intro,
                'hot' => $hot_card->price,
                'status' => Card::STATUS_LOCK
            ];
            if ($hot_card->is_online === Card::OFFLINE) {
                $card['status'] = Card::STATUS_OFFLINE;
            } elseif (in_array($hot_card->id, $has_listened_card_ids)) {
                $card['status'] = Card::STATUS_LISTENED;
            } elseif ($hot_card->push > 0) {
                $card['status'] = Card::STATUS_NOTICE;
            }
            return $card;
        }, $hot_cards));
    }

    /**
     * 获得某作品下热度卡片
     *
     * @param int $work_id 作品 ID
     * @param bool $is_online 是否必需上线，默认为 false
     * @todo 该类型卡片数量较少，暂不考虑分页
     * @return static[] 作品下热度卡片
     */
    public static function getHotCardsByWork(int $work_id, bool $is_online = false): array
    {
        $condition = [
            'special' => Card::SPECIAL_HOTCARD,
            'work_id' => $work_id
        ];
        if ($is_online) {
            $condition['is_online'] = Card::ONLINE;
        }
        return self::find()
            ->where($condition)
            ->orderBy('price ASC, rank ASC')
            ->all();
    }

    /**
     * 更新热度卡的解锁（获得）时间
     * 使用 push 字段保存解锁时间，当 push 为 0 时表示还未解锁
     *
     * @param int $work_id 作品 ID
     * @param int $hot_value 作品热度值
     */
    public static function updateHotCards(int $work_id, int $hot_value)
    {
        // 获取达到解锁条件需要的热度值
        $memcache = Yii::$app->memcache;
        $unlock_threshold_key = MUtils::generateCacheKey(KEY_VOICE_HOT_UNLOCK_THRESHOLD_WORK_ID, $work_id);
        $unlock_threshold = $memcache->get($unlock_threshold_key);
        if (!$unlock_threshold) {
            // 若不存在解锁阈值，则去查询下一张被锁住的卡片的解锁值作为阈值
            $next_locked_card = self::find()
                ->where(
                    'special = :special AND work_id = :work_id AND push = 0',
                    [
                        ':special' => self::SPECIAL_HOTCARD,
                        ':work_id' => $work_id
                    ]
                )
                ->orderBy('price ASC')
                ->limit(1)
                ->one();
            if (!$next_locked_card) {
                // 若不存在待解锁的卡，则将阈值设置为 -1
                $unlock_threshold = -1;
            } else {
                $unlock_threshold = $next_locked_card->price;
            }
            $memcache->set($unlock_threshold_key, $unlock_threshold, HALF_HOUR);
        } else {
            $unlock_threshold = (int)$unlock_threshold;
        }
        if ($hot_value >= $unlock_threshold && $unlock_threshold >= 0) {
            // 当前总热度值大于解锁阈值（下一张卡的解锁值），解锁热度卡片
            $now = $_SERVER['REQUEST_TIME'] ?? time();
            self::updateAll(
                ['push' => $now],
                'special = :special AND work_id = :work_id AND push = 0 AND price <= :hot_value',
                [
                    ':special' => self::SPECIAL_HOTCARD,
                    ':work_id' => $work_id,
                    ':hot_value' => $hot_value,
                ]
            );
        }
    }

    /**
     * 获取已解锁的卡片
     *
     * @param int $work_id 作品 ID
     * @return static[] 卡片对象组成的数组
     */
    public static function getHotCardsUnlocked(int $work_id)
    {
        // 获取作品总热度值
        $hot_value = Hot::getHot($work_id);
        return self::find()->select('id, title, push, price')
            ->where(['work_id' => $work_id, 'special' => self::SPECIAL_HOTCARD, 'is_online' => self::ONLINE])
            ->andWhere('price <= :hot_value', [':hot_value' => $hot_value])
            ->orderBy('price ASC, rank ASC')
            ->all();
    }

    /**
     * 获取语音的评论数
     *
     * @param int $type 语音类型（1 语音包，2 求签）
     * @param bool $has_sub 是否包含子评论数
     * @return int
     * @throws HttpException
     */
    public function getCommentCount(int $type, bool $has_sub = true): int
    {
        switch ($type) {
            case Work::TYPE_VOICE:
                $comment_type = SoundComment::TYPE_VOICE_CARD;
                break;
            case Work::TYPE_OMIKUJI:
                $comment_type = SoundComment::TYPE_OMIKUJI_CARD;
                break;
            default:
                throw new HttpException(400, '参数错误');
        }

        $comment_count = (int)SoundCommentRO::find()
            ->where('c_type = :type AND element_id = :card_id',
                [':type' => $comment_type, ':card_id' => $this->id])
            ->count();
        if ($has_sub) {
            $comment_count += SoundComment::getAllSubCommentNum($comment_type, $this->id);
        }
        return $comment_count;
    }

    public function isMultiRole()
    {
        if ($this->special === self::SPECIAL_NORMAL) {
            $attr = (int)CardPackage::find()->select('attr')
                ->where(['id' => $this->card_package_id])
                ->scalar();
            return ($attr & CardPackage::ATTR_MULTI_ROLE) !== 0;
        }

        return false;
    }

    public function isOffline(): bool
    {
        return $this->is_online === self::OFFLINE;
    }

    /**
     * 获取 card 音频 playurl 签名地址
     *
     * @param string $voice 音频地址
     * @param bool $is_beta_module 是否是灰度测试模块
     * @return string 音频 playurl 签名地址或全路径的文件地址
     */
    public static function getSoundSignUrl(string $voice, bool $is_beta_module = false)
    {
        $handled = false;
        try {
            // 判断协议地址是 sound:// 开头以及是否需要返回 playurl 签名地址
            if (strpos($voice, 'sound://') !== false && MSound::isBVCSoundCDN($is_beta_module)) {
                $upos_uri = str_replace('sound://', 'mefmxcodeboss/', $voice);
                $equipment = Yii::$app->equip;
                if ($equipment->isAndroidOrHarmonyOS()) {
                    $platform = UposClient::PLATFORM_ANDROID;
                } else {
                    $platform = UposClient::PLATFORM_IPHONE;
                }
                $voice_upos_url = Yii::$app->upos->getSignUrl($upos_uri, $platform);
                if ($voice_upos_url) {
                    $handled = true;
                }
            }
        } catch (Exception $e) {
            // 若请求出错，将错误记录到日志中
            Yii::error('获取 voice playurl 签名地址错误：' . $e->getMessage(), __METHOD__);
            // PASS
        }
        if (!$handled) {
            $voice_upos_url = StorageClient::getFileUrl($voice);
        }
        return $voice_upos_url;
    }
}
