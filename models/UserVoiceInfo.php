<?php

namespace app\models;

use app\components\models\traits\UserTrait;
use Yii;
use yii\helpers\Json;
use yii\web\HttpException;

/**
 * This is the model class for table "user_voice_info".
 *
 * @property int $id 主键，用户 id
 * @property int $omikuji_coupon 获得碎片
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 */
class UserVoiceInfo extends ActiveRecord
{
    use UserTrait;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'user_voice_info';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db2');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id'], 'required'],
            [['id', 'create_time', 'modified_time', 'omikuji_coupon'], 'integer'],
            [['id'], 'unique'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '用户 ID',
            'omikuji_coupon' => '求签幸运点',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
            // 用户初始幸运点为 0
            $this->omikuji_coupon = 0;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 获取用户求签幸运点
     *
     * @param int|null $user_id 用户 ID
     * @return int
     */
    public static function getOmikujiCoupon($user_id): int
    {
        if (!$user_id) return 0;
        return self::getByPk($user_id)->omikuji_coupon;
    }

    /**
     * 更新用户求签幸运点
     *
     * @param int $user_id 用户 ID
     * @param int $coupon 需要加减的点数
     * @return bool
     */
    public static function updateOmikujiCoupon(int $user_id, int $coupon)
    {
        if (!$user_id) return false;
        $user_info = self::getByPk($user_id);
        $user_info->updateCounters(['omikuji_coupon' => $coupon]);
    }
}
