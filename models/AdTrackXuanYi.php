<?php

namespace app\models;

use Exception;
use Yii;

class AdTrackXuanYi extends AdTrack implements AdTrackInterface
{
    const CALLBACK_EVENT_TYPE_ACTIVATE = 1;
    const CALLBACK_EVENT_TYPE_REGISTER = 2;
    const CALLBACK_EVENT_TYPE_PAY = 3;
    const CALLBACK_EVENT_TYPE_RETENTION = 4;

    const CALLBACK_EVENT_TYPE_MAP = [
        self::CALLBACK_EVENT_ACTIVATE => self::CALLBACK_EVENT_TYPE_ACTIVATE,
        self::CALLBACK_EVENT_ONE_DAY_RETENTION => self::CALLBACK_EVENT_TYPE_RETENTION,
        self::CALLBACK_EVENT_REGISTER => self::CALLBACK_EVENT_TYPE_REGISTER,
        self::CALLBACK_EVENT_PAY => self::CALLBACK_EVENT_TYPE_PAY,
        self::CALLBACK_EVENT_KEY_ACTION => null,
        self::CALLBACK_EVENT_TRANSACTION => null,
        self::CALLBACK_EVENT_APP_CALLUP => null,
    ];

    /**
     * 转化事件回调
     *
     * @param string $event_type 事件类型
     * @param mixed $pay_amount 支付金额，仅付费场景传入此参数
     */
    public function callback(string $event_type, $pay_amount = 0): bool
    {
        try {
            if (!array_key_exists($event_type, self::CALLBACK_EVENT_TYPE_MAP)) {
                throw new Exception('炫逸点击回传事件错误：' . $event_type);
            }
            $event = self::CALLBACK_EVENT_TYPE_MAP[$event_type];
            if (is_null($event)) {
                return true;
            }
            $data = Yii::$app->tools->requestRemote($this->getCallbackUrl($event, $pay_amount));
            if (!$data || $data['code'] !== 200) {
                throw new Exception(sprintf(
                    'XuanYi ad callback failed: code[%d], message[%s]',
                    $data['code'] ?? null, $data['message'] ?? null
                ));
            }
            return true;
        } catch (Exception $e) {
            Yii::error('XuanYi ad callback error: ' . $e->getMessage(), __METHOD__);
        }
        return false;
    }

    /**
     * 获取转化事件回调 URL
     *
     * @link https://file.tapd.cn/35612194/attachments/preview_attachments/1135612194001462798/story
     *
     * @param int $event 事件类型
     * @param mixed $pay_amount 支付金额（单位：元），仅付费场景传入此参数
     */
    private function getCallbackUrl(int $event, $pay_amount = 0): string
    {
        // track_id 形式：https://ad.xxxxx.com/ad-action?request_id=123123123123
        if ($event === self::CALLBACK_EVENT_TYPE_ACTIVATE) {
            return $this->track_id;
        }
        // event_type 参数默认为激活事件，其它事件需要替换 event_type 参数
        $url = $this->track_id;
        if (strpos($url, 'event_type=') !== false) {
            $url = preg_replace('/event_type=\d/', 'event_type=' . $event, $url);
        } else {
            $url .= '&event_type=' . $event;
        }
        if ($event === self::CALLBACK_EVENT_TYPE_PAY) {
            $url .= sprintf(
                '&currency_amount=%.2f',
                $this->getCallbackPayAmount($pay_amount, self::CALLBACK_PAY_UNIT_YUAN)
            );
        }
        return $url;
    }
}
