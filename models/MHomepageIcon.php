<?php

namespace app\models;

use app\components\util\Equipment;
use app\components\util\MUtils;
use Exception;
use missevan\storage\StorageClient;
use missevan\util\MUtils as MUtils2;
use Yii;

/**
 * This is the model class for table "m_homepage_icon".
 *
 * @property int $id
 * @property int $tab_id 对应的 Tab ID
 * @property string $title 名称
 * @property string $url 链接
 * @property string $icon 日间图标文件
 * @property string $dark_icon 夜间图标文件
 * @property int $type 图标类型 1：首页 tab 所属图标
 * @property int $sort 排序
 * @property int $archive 是否为历史归档 0 ：否，1 ：是（归档）
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 * @property string $anchor_name 图标名称标识（只有提示红点的图标设置此字段）
 */
class MHomepageIcon extends ActiveRecord
{
    // webp 图片参数
    const ICON_WEBP_PARAM = '?x-oss-process=style/webp';

    // 在线的推荐位
    const ARCHIVE_ONLINE = 0;
    // 历史归档的推荐位
    const ARCHIVE_HISTORY = 1;

    // icon 我的钱包唯一标识
    const ANCHOR_NAME_WALLET = 'wallet';

    // Icon 类型 1：我的页图标；2：首页 Tab 下的图标
    const TYPE_ICON_HOMEPAGE = 1;
    const TYPE_ICON_TAB = 2;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_homepage_icon';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'tab_id', 'type', 'sort', 'archive', 'create_time', 'modified_time'], 'integer'],
            [
                ['title', 'url', 'icon', 'dark_icon', 'type', 'sort', 'archive', 'create_time', 'modified_time'],
                'required'
            ],
            [['title', 'anchor_name'], 'string', 'max' => 20],
            [['url', 'icon', 'dark_icon'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'tab_id' => '对应的 Tab ID',  // TODO: tab_id 字段之后可调整成 json 类型，方便扩展其他元素 ID 下的 Icon
            'title' => '名称',
            'url' => '链接',
            'icon' => '日间图标文件',
            'dark_icon' => '夜间图标文件',
            'type' => '图标类型',  // 1：首页 tab 所属图标
            'sort' => '排序',
            'archive' => '是否为历史归档',  // 0 ：否，1 ：是（归档）
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'anchor_name' => '图标名称标识',  // 只有提示红点的图标设置此字段
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->icon) {
            $this->icon = StorageClient::getFileUrl($this->icon);
        }
        if ($this->dark_icon) {
            $this->dark_icon = StorageClient::getFileUrl($this->dark_icon);
        }
    }

    /**
     * 获取我的页图标
     *
     * @return array
     */
    public static function getIcons(): array
    {
        $icons_data = self::find()->select('id, title, icon, dark_icon, url, anchor_name')
            ->where('archive = :archive AND type = :type', [
                ':archive' => self::ARCHIVE_ONLINE,
                ':type' => self::TYPE_ICON_HOMEPAGE,
            ])
            ->orderBy(['sort' => SORT_ASC])->all();

        $icons = [];
        $enable_tab_bar_live = self::enableTabBarLive();
        $is_black_channel = Blacklist::model()->isBlackChannel();
        foreach ($icons_data as $icon) {
            $url = MUtils::getUsableAppLink($icon['url']);
            $url_path = parse_url($url, PHP_URL_PATH);
            if (!$enable_tab_bar_live && (in_array($url, ['missevan://topic', 'missevan://event'])
                || $url_path === '/mgame/lovegame')) {
                // 若未开启 tab bar 直播按钮（此时为"发现"按钮），则需要过滤"发现"页中已有的 icon 入口：活动、专题、手机恋人
                continue;
            }
            if ($is_black_channel && ($url_path === '/mgame/lovegame'
                || preg_match('/^missevan:\/\/game(\/.*)?$/', $url)
                || preg_match('/^missevan:\/\/theatre(\/.*)?$/', $url))) {
                // 隐藏拉黑渠道的“手机恋人”、“游戏中心”、“盲盒剧场”图标入口
                continue;
            }
            $return = [
                'id' => $icon['id'],
                'title' => $icon['title'],
                'icon' => $icon['icon'],
                'dark_icon' => $icon['dark_icon'],
                'url' => $url,
            ];
            if ($icon['anchor_name']) {
                // 后台设置了 name 有值，则返回
                // 用于标记 icon 的唯一标识，对应关系可在 m_homepage_icon 表查看
                $return['name'] = $icon['anchor_name'];
            }
            if ($icon['anchor_name'] === self::ANCHOR_NAME_WALLET && self::showNewUserTopup()) {
                $return['is_new_user'] = true;
            }
            $icons[] = $return;
        }
        return $icons;
    }

    /**
     * 是否启用了 tab bar 直播按钮
     *
     * @return bool
     */
    public static function enableTabBarLive(): bool
    {
        if (Equipment::isAppOlderThan('6.2.2', '6.2.2')) {
            // WORKAROUND: 低于 6.2.2 的客户端版本不支持 tab bar 直播按钮
            return false;
        }
        return Yii::$app->equip->isBeta(ENABLE_TAB_BAR_LIVE_RATIO);
    }

    /**
     * 是否显示我的钱包：新人角标
     *
     * @return bool
     */
    private static function showNewUserTopup(): bool
    {
        try {
            // 谷歌渠道无首充福利
            if (Equipment::isFromGoogleChannel()) {
                return false;
            }

            $user_id = Yii::$app->user->id;
            if (!$user_id) {
                // 未登录
                return Yii::$app->equip->isNewEquipment();
            }

            // 登录用户
            if (in_array($user_id, Yii::$app->params['newuser_user_allowlist'])) {
                // 若用户在新人用户白名单中，返回 true
                // 新设备锁有过期时间，所以白名单用户可以跳过删除设备锁
                return true;
            }

            // 检查并删除新人锁（当前设备为新设备但设备登录了老用户时删除）
            Yii::$app->equip->checkAndRemoveNewEquipmentFlag();
            return (Balance::getByPk($user_id))->hasNewUserTopupDiscount(Yii::$app->user);
        } catch (Exception $e) {
            Yii::error(sprintf('判断我的页图标「我的钱包」是否显示新人角标出错，error: %s', $e->getMessage()), __METHOD__);
            // PASS
            return false;
        }
    }

    /**
     * 获取 Tab 标签下的 Icon 图标
     *
     * @param int $tab_id Tab ID
     * @return array 例：['icons' => ['id' => 2, 'title' => '索引', 'url' => 'missevan://drama/filter',
     * 'icon' => 'http://static.missevan.com/profile/icon01.png',
     * 'dark_icon' => 'http://static.missevan.com/profile/icon01.png']]
     * @throws Exception
     * @todo 返回值不需要放到 icons 键名下，应返回由 icon 信息组成的纯数组
     */
    public static function getTabIcons(int $tab_id)
    {
        if ($tab_id < 0) {
            throw new Exception('参数错误');
        }
        $key = MUtils2::generateCacheKey(KEY_TAB_ICON_LIST, $tab_id);
        $icons = MUtils2::getOrSetDistrubutedCache($key, function () use ($tab_id) {
            // 缓存中没有数据则从表中获取
            // 该缓存会在 Icon 管理后台（App -> 首页管理 -> 首页 Tabs）进行添加，编辑，删除操作后被删除
            $icons = self::find()
                ->select('id, title, url, icon, dark_icon')
                ->where('tab_id = :tab_id AND archive = :archive AND type = :type', [
                    ':tab_id' => $tab_id,
                    ':archive' => self::ARCHIVE_ONLINE,
                    ':type' => self::TYPE_ICON_TAB,
                ])
                ->orderBy('sort ASC')->all();
            $icon_list = ['icons' => $icons];
            return $icon_list;
        }, ONE_DAY);
        $icons = array_map(function ($icon) {
            $icon['url'] = MUtils::getUsableAppLink($icon['url']);
            return $icon;
        }, $icons['icons']);
        return ['icons' => $icons];
    }
}
