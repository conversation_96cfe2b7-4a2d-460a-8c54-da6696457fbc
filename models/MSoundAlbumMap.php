<?php

namespace app\models;

use app\components\util\MUtils;
use Yii;
use yii\db\Expression;
use yii\web\HttpException;

/**
 * This is the model class for table "m_sound_album_map".
 *
 * @property integer $id
 * @property integer $album_id
 * @property integer $sound_id
 * @property integer $sort
 * @property integer $hot
 * @property integer $time
 */
class MSoundAlbumMap extends ActiveRecord
{
    const SORT_INTERVAL = 100000;
    const SORT_MAX = 2000000000;
    const SORT_MIN_THRESHOLD = 4;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_sound_album_map';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['album_id', 'sound_id', 'time'], 'required'],
            [['album_id', 'sound_id', 'sort', 'hot', 'time'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'album_id' => '专辑ID',
            'sound_id' => '声音ID',
            'sort' => '排序',
            'hot' => '排序',
            'time' => '添加时间',
        ];
    }

    /**
     * 批量收藏音频（当没有事务开启时，会自动开启事务并提交）
     *
     * @param int $album_id 音单 ID
     * @param array $sound_ids 音频 IDs
     * @throws \Exception
     * @todo 之后调整成私有的方法，收藏音频的业务都走 collectSoundOrNot 这个方法
     */
    public static function collectSound(int $album_id, array $sound_ids): void
    {
        $collect_func = function ($model, $album_id, $sound_ids) {
            $album_sound_ids = $model::find()->select('sound_id')
                ->where(['album_id' => $album_id, 'sound_id' => $sound_ids])->column();
            $album_sound_ids = array_map('intval', $album_sound_ids);
            if (count($album_sound_ids) === count($sound_ids)) {
                // 传过来的音频都已收藏至音单，则直接返回
                return;
            }
            $top_sound = $model::find()->where(['album_id' => $album_id])
                ->orderBy('sort ASC')->limit(1)->one();
            // 获取需要加入音单的音频 IDs
            $add_sound_ids = array_diff($sound_ids, $album_sound_ids);
            $add_sound_count = count($add_sound_ids);
            $album_sound_count = (int)MAlbum::find()->select('music_count')->where(['id' => $album_id])->scalar();
            if ($album_sound_count + $add_sound_count > MAlbum::MAX_COLLECT_NUM) {
                // 已收藏的音频数量 + 能收藏的音频数量不能超过上限
                throw new HttpException(400, '音单收藏音频数不能超过 ' . MAlbum::MAX_COLLECT_NUM);
            }
            $insert_datas = [];
            $time = $_SERVER['REQUEST_TIME'];
            // TODO: 为确保能够推迟重排的触发，排序值初始化时可以考虑都用正数
            $new_sort = $top_sound->sort ?? self::SORT_INTERVAL;
            foreach ($add_sound_ids as $sound_id) {
                $new_sort -= self::SORT_INTERVAL;
                $insert_datas[] = [
                    'album_id' => $album_id,
                    'sound_id' => $sound_id,
                    'sort' => $new_sort,
                    'time' => $time
                ];
            }
            if (empty($insert_datas)) {
                return;
            }
            $column = array_keys($insert_datas[0]);
            self::getDb()->createCommand()->batchInsert($model::tableName(), $column, $insert_datas)->execute();
            $need_reorder = $new_sort < -MSoundAlbumMap::SORT_MAX;
            if ($need_reorder) {
                MSoundAlbumMap::reorderSounds($album_id);
            }
            // 更新音频的被收藏数
            MSound::updateAllCounters(['favorite_count' => 1], ['id' => $add_sound_ids]);
            // 更新音单的收藏音频数
            $music_count = count($add_sound_ids);
            MAlbum::updateAllCounters(['music_count' => $music_count], 'id = :id', [':id' => $album_id]);
        };
        MUtils::ensureDbTransaction(self::class, $collect_func, $album_id, $sound_ids);
    }

    /**
     * 批量取消收藏音频（当没有事务开启时，会自动开启事务并提交）
     *
     * @param int $album_id 音单 ID
     * @param array $sound_ids 音频 IDs
     * @throws \Exception
     * @todo 之后调整成私有的方法，取消收藏音频的业务都走 collectSoundOrNot 这个方法
     */
    public static function cancelCollectSound(int $album_id, array $sound_ids): void
    {
        $cancel_collect_func = function ($model, $album_id, $sound_ids) {
            $album_sound_ids = $model::find()->select('sound_id')
                ->where(['album_id' => $album_id, 'sound_id' => $sound_ids])->column();
            $album_sound_ids = array_map('intval', $album_sound_ids);
            if (empty($album_sound_ids)) {
                // 传过来的音频不在音单中，则直接返回
                return;
            }
            // 获取需要批量取消收藏的音频 IDs
            $delete_sound_ids = array_intersect($sound_ids, $album_sound_ids);
            // 取消收藏
            $model::deleteAll(['sound_id' => $delete_sound_ids, 'album_id' => $album_id]);
            // 使用表达式确保数据合法性
            // 更新音频的被收藏数
            $favorite_count_expression = new Expression('GREATEST(favorite_count, 1) - 1');
            MSound::updateAll(['favorite_count' => $favorite_count_expression], ['id' => $delete_sound_ids]);
            // 更新音单收藏的音频数量
            $music_count = count($delete_sound_ids);
            $music_count_expression = new Expression('GREATEST(music_count, :music_count) - :music_count',
                [':music_count' => $music_count]);
            MAlbum::updateAll(['music_count' => $music_count_expression], 'id = :id', [':id' => $album_id]);
        };
        MUtils::ensureDbTransaction(self::class, $cancel_collect_func, $album_id, $sound_ids);
    }

    /**
     * 批量收藏或取消收藏音频
     *
     * @param array $sound_ids 音频 IDs
     * @param MAlbum $album 音单信息
     * @param int $type 操作类型 0：取消收藏音频；1：收藏音频
     * @return array
     * @throws \Exception
     */
    public static function collectSoundOrNot(array $sound_ids, ?MAlbum $album, int $type)
    {
        if (empty($sound_ids) || !$album) {
            throw new HttpException(400, '参数错误');
        }
        $album_id = $album->id;
        // 加锁
        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_ALBUM_COLLECT_SOUND, $album_id);
        if (!$redis->lock($lock, ONE_MINUTE)) {
            throw new HttpException(400, '操作过于频繁，请稍候再试');
        }
        $transaction = Yii::$app->db->beginTransaction();
        try {
            switch ($type) {
                case MAlbum::SOUND_CANCEL_COLLECT:
                    // 取消收藏音频
                    self::cancelCollectSound($album_id, $sound_ids);
                    $info = '取消收藏音频成功';
                    $collection = false;
                    break;
                case MAlbum::SOUND_COLLECT:
                    // 收藏音频
                    self::collectSound($album_id, $sound_ids);
                    $info = '收藏音频成功';
                    $collection = true;
                    break;
                default:
                    throw new HttpException(400, '参数错误');
            }
            // 更新无封面音单的音单封面图
            $album->updateImage();
            MAlbum::updateAll(['last_update_time' => $_SERVER['REQUEST_TIME']], 'id = :id', [':id' => $album_id]);
            $transaction->commit();
            return [
                'msg' => $info,
                'collection_status' => $collection
            ];
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        } finally {
            // 解锁
            $redis->unlock($lock);
        }
    }

    public static function getAlbumsViewCounts($album_ids)
    {
        $view_counts = self::find()->alias('m')->select('m.album_id, SUM(view_count) AS view_count, COUNT(s.id) AS music_count')
            ->where(['m.album_id' => $album_ids])->innerJoin('m_sound s', 'm.sound_id = s.id')
            ->groupBy('m.album_id')->orderBy(['m.album_id' => SORT_ASC])->asArray()->all();
        $view_counts = array_map(function ($item) {
            $item['album_id'] = (int)$item['album_id'];
            $item['view_count'] = (int)$item['view_count'];
            $item['music_count'] = (int)$item['music_count'];
            return $item;
        }, $view_counts);
        return $view_counts;
    }

    public static function reorderSounds($album_id)
    {
        $sound_ids = self::find()->select('sound_id')->where(['album_id' => $album_id])
            ->orderBy('`sort` ASC')
            ->column();
        $case_value = 'CASE sound_id';
        $sort = 0;
        foreach ($sound_ids as $sound_id) {
            $case_value .= " WHEN $sound_id THEN $sort ";
            $sort += self::SORT_INTERVAL;
        }
        $table_name = self::tableName();
        $case_value .= 'END';
        $in_sound_condition = MUtils::generateIntegerIn('sound_id', $sound_ids);
        $sql = <<<SQL
            UPDATE {$table_name}
            SET `sort` = {$case_value}
            WHERE album_id = {$album_id} AND {$in_sound_condition};
SQL;
        return (bool)Yii::$app->db->createCommand($sql)->execute();
    }

}
