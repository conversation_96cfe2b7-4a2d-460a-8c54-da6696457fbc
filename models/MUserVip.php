<?php

namespace app\models;

use app\components\util\Tools;
use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\db\ActiveRecord;

/**
 * This is the model class for table "m_user_vip".
 *
 * @property integer $id 主键
 * @property integer $create_time 创建时间（秒级时间戳）
 * @property integer $modified_time 修改时间（秒级时间戳）
 * @property integer $vip_id vip id
 * @property integer $user_id 用户 ID
 * @property integer $type vip 类型，4：点播会员
 * @property integer $start_time 开始时间（秒级时间戳），包含此时刻
 * @property integer $end_time 过期时间（秒级时间戳），不包含此时刻
 * @property integer $deduct_record_id 扣费记录 ID
 */
class MUserVip extends ActiveRecord
{
    use ActiveRecordTrait;

    // 会员类型 4：点播会员
    const TYPE_PLAY = 4;

    const STATUS_NOT_YET = 0;   // 未开通过正式会员
    const STATUS_IN_EFFECT = 1; // 正式会员
    const STATUS_TRIAL = 2;     // 体验会员
    const STATUS_EXPIRED = 3;   // 正式会员已过期

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->db;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        // TODO: 按 user_id 分区
        return 'm_user_vip';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['vip_id', 'user_id', 'type', 'start_time', 'end_time'], 'required'],
            [['create_time', 'modified_time', 'vip_id', 'user_id', 'type', 'start_time', 'end_time', 'deduct_record_id'], 'integer'],
            [['type'], 'in', 'range' => [self::TYPE_PLAY]],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'vip_id' => 'VIP ID',
            'user_id' => '用户 ID',
            'type' => 'vip 类型',
            // 有效期判断 now >= start_time AND now < end_time
            'start_time' => '开始时间',  // 包含此刻
            'end_time' => '过期时间',  // 不包含此刻
            'deduct_record_id' => '扣费记录 ID',
        ];
    }

    /**
     * 创建或延长用户会员
     * （注：需要在事务中执行）
     *
     * @param int $user_id 用户 ID
     * @param MVip $vip 即将开通或续费的会员价目
     * @param int $deduct_record_id 扣费订单 ID
     * @return bool
     * @throws Exception
     */
    public static function createOrExtend(int $user_id, MVip $vip, int $deduct_record_id): bool
    {
        $vip_duration = $vip->getPeriod();
        if (!$vip_duration) {
            Yii::error(sprintf('会员价目未配置会员周期时间，价目 ID: %d', $vip->id), __METHOD__);
            return false;
        }
        $now = $_SERVER['REQUEST_TIME'];
        $vip_max_end_time = (int)self::find()
            ->where(['user_id' => $user_id, 'type' => self::TYPE_PLAY])
            ->andWhere('end_time > :now', [':now' => $now])
            ->max('end_time');
        $vip_start_time = $vip_max_end_time ?: $now;
        $vip_end_time = self::calculateEndTime($vip_start_time, $vip_duration);
        $user_vip = new self([
            'vip_id' => $vip->id,
            'user_id' => $user_id,
            'type' => self::TYPE_PLAY,
            'start_time' => $vip_start_time,
            'end_time' => $vip_end_time,
            'deduct_record_id' => $deduct_record_id,
        ]);
        if (!$user_vip->save()) {
            Yii::error(sprintf('创建用户会员信息失败: %s', MUtils2::getFirstError($user_vip)),
                __METHOD__);
            return false;
        }
        return true;
    }

    /**
     * 计算会员结束时间
     *
     * @param int $start_time 会员开始时间（单位：秒）
     * @param int $duration_in_second 会员周期（单位：秒）
     * @return int 会员结束时间（单位：秒）
     */
    public static function calculateEndTime(int $start_time, int $duration_in_second)
    {
        // 会员的有效期如果不是天的整数倍，则处理成天的整数倍
        if ($duration_in_second % ONE_DAY !== 0) {
            $duration_in_second = (int)ceil($duration_in_second / ONE_DAY) * ONE_DAY;
        }
        return strtotime('midnight', $start_time) + $duration_in_second;
    }

    /**
     * 是否为会员
     *
     * @param integer $user_id 用户 ID
     * @return bool
     */
    public static function isVipUser(int $user_id)
    {
        if ($user_id <= 0) {
            return false;
        }
        return !is_null(self::getCurrentVip($user_id));
    }

    /**
     * 获取用户会员信息
     *
     * @param int $user_id
     * @return array
     */
    public static function getUserVipInfo(int $user_id): array
    {
        $vip_info = [
            'status' => self::STATUS_NOT_YET,
            'end_time' => 0,
        ];
        $end_time = (int)self::find()
            ->select('end_time')
            ->where(['user_id' => $user_id, 'type' => self::TYPE_PLAY])
            ->orderBy(['end_time' => SORT_DESC])
            ->limit(1)
            ->scalar();
        if (!$end_time) {
            return $vip_info;
        }
        // 过期时间保存的是结束日时间的下一秒，所以这里减一秒以便展示时为正确的过期日时间
        $vip_info['end_time'] = $end_time - 1;
        if ($vip_info['end_time'] < $_SERVER['REQUEST_TIME']) {
            $vip_info['status'] = self::STATUS_EXPIRED;
        } else {
            $vip_info['status'] = self::STATUS_IN_EFFECT;
        }
        return $vip_info;
    }

    /**
     * @param int $user_id
     * @return self|null
     */
    public static function getCurrentVip(int $user_id): ?self
    {
        if ($user_id <= 0) {
            return null;
        }
        return self::newMainValidVipQuery($user_id)->one();
    }

    /**
     * 获取 vip 用户列表
     *
     * @param array $user_ids 用户 ID 列表
     * @return array vip 用户 ID 列表
     */
    public static function getVipUserIds(array $user_ids): array
    {
        if (empty($user_ids)) {
            return [];
        }
        return self::newMainValidVipQuery($user_ids)
            ->select('user_id')
            ->distinct(true)
            ->column();
    }

    /**
     * 获取用户生效中的 vip ID
     *
     * @param integer $user_id 用户 ID
     * @return integer vip_id
     */
    public static function getVipID(int $user_id): int
    {
        if ($user_id <= 0) {
            return 0;
        }
        $vip = self::getCurrentVip($user_id);
        return $vip ? $vip->id : 0;
    }

    /**
     * @param int|array $user_id
     * @return \yii\db\ActiveQuery
     */
    public static function newMainValidVipQuery($user_id)
    {
        return self::find()
            ->where(['user_id' => $user_id])
            ->andWhere(['type' => self::TYPE_PLAY])
            ->andWhere(':now >= start_time AND :now < end_time', [':now' => $_SERVER['REQUEST_TIME']]);
    }

    public static function isDuplicateVip(int $user_id, int $vip_id): bool
    {
        return self::find()
            ->where(['user_id' => $user_id, 'vip_id' => $vip_id, 'type' => self::TYPE_PLAY])
            ->andWhere('end_time > :now', [':now' => $_SERVER['REQUEST_TIME']])
            ->exists();
    }

    /**
     * iOS 升级订阅（如包月升到包季），iOS 会按比例退还原订阅对应未使用时长的费用，服务端需要对会员权益的有限期进行扣减
     * iOS 退款，扣减未使用的剩余时长
     *
     * @link https://blog.csdn.net/gllaxq/article/details/120313761
     * @link https://www.jianshu.com/p/6d4e1ae39332
     *
     * @param VipFeeDeductedRecord $original_deduct_record
     * @param int $deduct_timestamp 发生扣减的时间戳
     * @return void
     * @throws Exception
     */
    public static function deductLeftDuration(VipFeeDeductedRecord $original_deduct_record, int $deduct_timestamp = 0): void
    {
        if ($original_deduct_record->pay_type !== VipFeeDeductedRecord::PAY_TYPE_IOS) {
            throw new Exception('仅支持 iOS 订阅');
        }
        $now = $_SERVER['REQUEST_TIME'];
        $valid_vips = self::find()
            ->where(['type' => self::TYPE_PLAY, 'user_id' => $original_deduct_record->user_id])
            ->andWhere('end_time > :now', [':now' => $now])
            ->orderBy(['start_time' => SORT_ASC, 'id' => SORT_ASC])
            ->all();

        $current_vip = null;
        $last_process_item = null;
        foreach ($valid_vips as $item) {
            /**
             * @var MUserVip $item
             * @var MUserVip $current_vip
             * @var MUserVip $last_process_item
             */
            if (!$current_vip && $item->vip_id === $original_deduct_record->vip_id && $item->deduct_record_id === $original_deduct_record->id) {
                // 找到原订阅记录
                $current_vip = $item;

                if ($now >= $item->start_time) {
                    // 如果原订阅是正在生效中的订阅，则立即过期
                    $item->end_time = $now;
                } else {
                    // 如果原订阅属于待生效状态，则扣减此时距开通时天数
                    $purchase_time = $item->create_time;
                    $vip = null;
                    if ($carry_data = $original_deduct_record->getCarryData()) {
                        [$vip, $purchase_time] = $carry_data;
                    }
                    if (!$deduct_timestamp) {
                        $deduct_timestamp = $_SERVER['REQUEST_TIME'];
                    }
                    if (!$vip) {
                        $vip = MVip::findOne(['id' => $original_deduct_record->vip_id]);
                    }
                    // 未使用的时长比例
                    $ratio = ($original_deduct_record->next_deduct_time - $deduct_timestamp) / ($original_deduct_record->next_deduct_time - $purchase_time);
                    $elapsed_duration = intval($ratio * $vip->getPeriod());
                    Yii::error(sprintf('iOS 订阅升级扣减原订阅未使用的时长: user_id=%d, duration=%d', $original_deduct_record->user_id, $elapsed_duration), __METHOD__);
                    $item->end_time = self::calculateEndTime($item->end_time, -$elapsed_duration);
                }

                if (!$item->save()) {
                    throw new Exception(MUtils2::getFirstError($item));
                }

                $last_process_item = $item;
            } else if ($current_vip && $item->id !== $current_vip->id) {
                // 后面待生效的订阅起止时间依次往前推进
                $item->end_time = self::calculateEndTime($last_process_item->end_time, $item->end_time - $item->start_time);
                $item->start_time = $last_process_item->end_time;
                if (!$item->save()) {
                    throw new Exception(MUtils2::getFirstError($item));
                }

                $last_process_item = $item;
            }
        }
    }

    /**
     * 开通或续费成功时，发送开通或续费成功的系统通知
     *
     * @param int $user_id
     * @param bool $is_renew
     * @return void
     */
    public static function sendSuccessfulNotification(int $user_id, bool $is_renew)
    {
        $vip_center_url = Yii::$app->params['vip']['center_url'];
        Yii::$app->tools->sendNotification([
            'user_id' => $user_id,
            'title' => $is_renew ? '会员续费提醒' : '会员开通提醒',
            'content' => $is_renew
                ? sprintf('尊敬的会员，恭喜您续费会员服务，可以继续每天领钻石、畅听会员剧，<a href="%s">点击了解详情</a>', $vip_center_url)
                : sprintf('尊敬的会员，恭喜您开通了会员服务，钻石每天领、会员剧畅听，<a href="%s">点击了解详情</a>', $vip_center_url),
        ], Tools::SEND_SYS_MSG);
    }

}
