<?php

namespace app\models;

use Exception;
use Yii;

class MThirdPartyYoukuTask
{
    // 合作方回调接口文档：https://info.bilibili.co/pages/viewpage.action?pageId=958111694
    const CALLBACK_URL_YOUKU = 'https://3part.youku.com/api/v2/completeTask';

    // 与优酷方的约定参数
    const YOUKU_APP_KEY = 'share_maoer';
    // 生成优酷回调接口签名密钥
    const YOUKU_SIGN_KEY = '75910f3ba74e4f16aebf929e1e3ed99a';

    // 回调成功响应 code
    const YOUKU_REQUEST_SUCCESS_CODE = 'SUCCESS';

    /**
     * 请求优酷回调
     *
     * @param string $track_id
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function callback(string $track_id): bool
    {
        $params = [
            'token' => $track_id,
        ];
        $time = intval(microtime(true) * 1000);
        $build_sign_params = ['token' => $track_id, 'timestamp' => $time];
        $headers = [
            'appKey' => self::YOUKU_APP_KEY,
            'appSign' => self::buildSign($build_sign_params),
            'timestamp' => $time,  // 毫秒
        ];
        try {
            $data = Yii::$app->tools->requestRemote(self::CALLBACK_URL_YOUKU, $params, 'GET', null, 0, $headers);
            if (!$data) {
                throw new Exception('返回值为空');
            }
            if ($data['code'] !== self::YOUKU_REQUEST_SUCCESS_CODE) {
                throw new Exception(sprintf('code[%s], msg[%s]', $data['code'], $data['message']));
            }
            if (!$data['content']) {
                throw new Exception('任务完成详情返回值为空');
            }
            if (!$data['content']['success']) {
                throw new Exception(sprintf('code[%s], msg[%s]', $data['content']['msgCode'],
                    $data['content']['msgInfo']));
            }
        } catch (Exception $e) {
            Yii::error('优酷点击回传失败，原因：' . $e->getMessage(), __METHOD__);
            return false;
        }
        return true;
    }

    /**
     * 获取优酷方回调接口需要的签名
     *
     * @param array $params 生成签名的参数
     * @return string 签名
     */
    private static function buildSign(array $params): string
    {
        ksort($params);
        $str_to_sign = '';
        $last_key = array_key_last($params);
        foreach ($params as $k => $v) {
            $str_to_sign .= "{$k}={$v}";
            if ($k !== $last_key) {
                $str_to_sign .= '&';
            }
        }
        return hash_hmac('md5', $str_to_sign, self::YOUKU_SIGN_KEY);
    }
}
