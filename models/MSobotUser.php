<?php

namespace app\models;

use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "m_sobot_user".
 *
 * @property integer $id 主键 ID
 * @property integer $create_time 创建时间
 * @property integer $modified_time 更新时间
 * @property integer $user_id 用户 ID
 * @property string $visitor_id 智齿访客 ID
 */
class MSobotUser extends ActiveRecord
{
    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_sobot_user';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'visitor_id'], 'required'],
            [['create_time', 'modified_time', 'user_id'], 'integer'],
            [['visitor_id'], 'string'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'user_id' => '用户 ID',
            'visitor_id' => '智齿访客 ID',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 更新或保存用户的 visitor_id
     *
     * @param int $user_id 用户 ID
     * @param string $sobot_visitor_id 智齿访客 ID
     * @throws HttpException
     */
    public static function updateVisitorId(int $user_id, string $sobot_visitor_id)
    {
        $m_sobot_user = MSobotUser::findOne(['user_id' => $user_id]);
        $need_update = false;
        if (!$m_sobot_user) {
            // 没有记录则保存
            $m_sobot_user = new MSobotUser();
            $m_sobot_user->user_id = $user_id;
            $m_sobot_user->visitor_id = $sobot_visitor_id;
            $need_update = true;
        } elseif ($sobot_visitor_id !== $m_sobot_user->visitor_id) {
            // 有记录并且 visitor_id 有变化则更新
            $m_sobot_user->visitor_id = $sobot_visitor_id;
            $need_update = true;
        }
        if ($need_update && !$m_sobot_user->save()) {
            throw new HttpException(400, MUtils2::getFirstError($m_sobot_user));
        }
    }

    /**
     * 获取智齿未读消息数量
     *
     * @param string $sobot_visitor_id 智齿访客 ID
     * @return int 智齿未读消息数量
     */
    public static function getSobotOfflineMsgCount(string $sobot_visitor_id)
    {
        $msg_count = 0;
        if (!$sobot_visitor_id) {
            // 没有 sobot_visitor_id 时，说明没有初始化过智齿客户端，返回数量为 0
            return $msg_count;
        }
        try {
            $result = Yii::$app->serviceRpc->sobotGetNotice($sobot_visitor_id);
            if (!isset($result['unread_count'])) {
                throw new Exception('数据格式错误');
            }
            $msg_count = $result['unread_count'];
        } catch (Exception $e) {
            // 如果 RPC 请求失败，则降级并记录错误日志
            Yii::error(sprintf('获取智齿未读消息数量 rpc 接口出错，sobot_visitor_id: %s, error: %s',
                $sobot_visitor_id, $e->getMessage()), __METHOD__);
            // PASS
        }
        return $msg_count;
    }

    /**
     * 检查传入的 visitor_id 是否有效
     *
     * @param int $user_id 用户 ID
     * @param string $sobot_visitor_id 智齿访客 ID
     * @return bool 传入的 visitor_id 是否有效
     */
    public static function isValidVisitorId(int $user_id, string $sobot_visitor_id): bool
    {
        $visitor_id = MSobotUser::find()->select('visitor_id')->where(['user_id' => $user_id])->scalar();
        return $sobot_visitor_id === $visitor_id;
    }
}
