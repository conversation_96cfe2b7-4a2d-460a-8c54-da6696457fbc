<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "drama_reward_menu".
 *
 * @property integer $id
 * @property integer $create_time
 * @property integer $modified_time
 * @property string $name
 * @property integer $price
 */
class DramaRewardMenu extends ActiveRecord
{
    // 最低、最高打赏数额
    const REWARD_MIN_COIN = 10;
    const REWARD_MIN_COIN_MIMI = 100;
    const REWARD_MAX_COIN = 999999;

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->paydb;
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'drama_reward_menu';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'create_time', 'price'], 'required'],
            [['id', 'create_time', 'modified_time', 'price'], 'integer'],
            [['name'], 'string', 'max' => 30],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'name' => '价目名称',
            'price' => '钻石数',
        ];
    }

    public static function getRewardMinCoinNum()
    {
        if (Yii::$app->equip->isFromMiMiApp()) {
            return self::REWARD_MIN_COIN_MIMI;
        }
        return self::REWARD_MIN_COIN;
    }

    public static function getRewardMaxCoinNum()
    {
        return self::REWARD_MAX_COIN;
    }

    public static function getRewardPrice()
    {
        return self::find()->select('id, name, price')->all();
    }
}
