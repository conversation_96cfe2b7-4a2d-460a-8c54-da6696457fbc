<?php

namespace app\models;

use missevan\util\MUtils as MUtils2;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "launch_report_log".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 * @property int $device_type 设备类型: 1 为 Android; 2 为 iOS; 6 为 HarmonyOS
 * @property string $equip_id 设备号
 * @property string $user_agent 用户代理
 * @property string $ip 客户端 IP
 * @property string $buvid 唯一设备标识
 * @property string $version App 版本
 * @property string $idfa iOS IDFA
 * @property string $idfv iOS IDFV
 * @property int $is_root Android 是否越狱，0：未越狱；1：越狱
 * @property string $ct Android 渠道标识
 * @property string $adid Android 广告 ID
 * @property string $mac Android 设备 MAC 地址
 * @property string $imei Android 设备 IMEI
 * @property string $android_id Android 设备 ID
 * @property string $oaid Android 匿名设备标识符
 * @property array $more 更多详情
 */
class LaunchReportLog extends ActiveRecord
{
    // 未越狱
    const IS_NOT_ROOT = 0;
    // 越狱
    const IS_ROOT = 1;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        if (YII_ENV === 'test') {
            // 单元测试环境不分表
            return 'launch_report_log';
        }
        // 按月循环分表，后续数据会实时同步给B站，同步后的月表数据会进行归档
        $m = date('m', $_SERVER['REQUEST_TIME']);
        return 'launch_report_log_' . $m;
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->logdb;
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['device_type'], 'required'],
            [['create_time', 'modified_time', 'is_root'], 'integer'],
            [['device_type'], 'integer', 'max' => 9],
            [['equip_id', 'adid', 'idfa', 'idfv'], 'string', 'max' => 36],
            [['imei'], 'string', 'max' => 20],
            [['android_id'], 'string', 'max' => 50],
            [['mac'], 'string', 'max' => 17],
            [['user_agent', 'oaid'], 'string', 'max' => 255],
            [['ip'], 'string', 'max' => MUtils2::IP_MAX_LENGTH],
            [['ct'], 'string', 'max' => 50],
            [['buvid'], 'string', 'max' => 64],
            [['version'], 'string', 'max' => 20],
            [['more'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'equip_id' => '设备号',
            'device_type' => '设备类型',  // 1: Android; 2: iOS; 6: HarmonyOS
            'user_agent' => '用户代理',
            'ip' => '客户端 IP',
            'buvid' => '唯一设备标识',
            'version' => '客户端版本号',
            'idfa' => 'iOS IDFA',
            'idfv' => 'iOS IDFV',
            'is_root' => 'Android 是否越狱',
            'ct' => 'Android 渠道标识',
            'adid' => 'Android 广告 ID',
            'mac' => 'Android 设备 MAC 地址',  // 大写保留冒号分隔符
            'imei' => 'Android 设备 IMEI',
            'android_id' => 'Android 设备 ID',
            'oaid' => 'Android 匿名设备标识符',
            'more' => '更多详情',  // json 格式
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        if (YII_ENV === 'test' && $this->more) {
            // sqlite json 字段不支持自动 decode，需进行 Json::decode 处理
            $this->more = Json::decode($this->more);
        }
    }

    /**
     * 入库前自动处理
     */
    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        if (YII_ENV === 'test' && $this->more) {
            // sqlite json 字段不支持自动 encode，需进行 Json::encode 处理
            $this->more = Json::encode($this->more);
        }
        return true;
    }
}
