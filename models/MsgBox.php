<?php

namespace app\models;

use app\components\util\MUtils;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "msg_box".
 *
 * @property int $id 主键（信件唯一 ID）
 * @property string $title 标题
 * @property string $content 内容
 * @property int $created_time 创建时间
 * @property int $modified_time 更新时间
 * @property int $expired_time 过期时间
 * @property int $work_id 作品 ID
 */
class MsgBox extends ActiveRecord
{
    const STATUS_UNREAD = 0;
    const STATUS_READ = 1;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'msg_box';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db2');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['title', 'content', 'expired_time'], 'required'],
            [['content'], 'string'],
            [['created_time', 'modified_time', 'expired_time', 'work_id'], 'integer'],
            [['title'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键（信件唯一 ID）',
            'title' => '标题',
            'content' => '内容',
            'created_time' => '创建时间',
            'modified_time' => '更新时间',
            'expired_time' => '过期时间',
            'work_id' => '作品 ID',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->created_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 获取用户信箱消息
     *
     * @param integer $work_id 作品 ID
     * @param string $equip_id 设备 ID
     * @param integer|null $user_id 用户 ID
     * @return array
     */
    public static function getAllMsgs(int $work_id, string $equip_id, $user_id = null)
    {
        // 获取信件
        $msgs = self::find()->select('id, title, content')
            ->where('work_id = :work_id AND expired_time > :time')
            ->params([':work_id' => $work_id, ':time' => $_SERVER['REQUEST_TIME']])
            ->asArray()->all();
        $msg_ids_read = MsgRead::getReadMsgIds($equip_id, $user_id, array_column($msgs, 'id'));
        // 处理已读/未读状态
        $now = $_SERVER['REQUEST_TIME'];
        $msgs = array_reduce($msgs, function ($ret, $item) use ($now, $msg_ids_read) {
            $item['id'] = (int)$item['id'];
            $item['status'] = self::STATUS_UNREAD;
            if (in_array($item['id'], $msg_ids_read)) {
                $item['status'] = self::STATUS_READ;
            }
            $ret[] = $item;
            return $ret;
        }, []);
        // 信件最新的在前
        usort($msgs, function ($a, $b) {
            return $a['id'] < $b['id'];
        });
        return [
            'msgs' => $msgs,
            'msg_ids_read' => $msg_ids_read,
        ];
    }

    /**
     * 获取信箱提醒消息
     *
     * @param int $work_id 作品 ID
     * @param int $page 所在页
     * @param string $equip_id 设备 ID
     * @param int|null $user_id 用户 ID
     * @return ReturnModel
     * @throws \yii\db\Exception
     */
    public static function getMsgs(int $work_id, int $page, string $equip_id, $user_id = null)
    {
        $memcache = Yii::$app->memcache;
        $key = MUtils::generateCacheKey(KEY_VOICE_MSG_BOX, $work_id, $user_id, $equip_id);
        if ($data = $memcache->get($key)) {
            $msgs = Json::decode($data);
            $msg_ids_read = MsgRead::getReadMsgIds($equip_id, $user_id, array_column($msgs, 'id'));
            // 标记已读
            $msgs = array_map(function ($item) use ($msg_ids_read) {
                if (in_array($item['id'], $msg_ids_read)) {
                    $item['status'] = self::STATUS_READ;
                }
                return $item;
            }, $msgs);
        } else {
            $res = self::getAllMsgs($work_id, $equip_id, $user_id);
            $msgs = $res['msgs'];
            $msg_ids_read = $res['msg_ids_read'];
            $memcache->set($key, Json::encode($msgs), FIVE_MINUTE);
        }
        // 分页设置
        if ($data = $msgs[$page - 1] ?? []) {
            $data = [$data];
        }
        $return = ReturnModel::getPaginationData($data, count($msgs), $page, 1);
        // 记录新的已读信件
        if ($ids_reading = array_diff(array_column($return->Datas, 'id'), $msg_ids_read)) {
            MsgRead::insertNewReads($ids_reading, $equip_id, $user_id);
            if ($user_id) {
                MsgRead::syncMsgIds($equip_id, $user_id);
            }
        }
        return $return;
    }

    /**
     * 获取信箱提醒数量
     *
     * @param integer $work_id 作品 ID
     * @param string $equip_id 设备 ID
     * @param integer|null $user_id 用户 ID
     * @return integer
     */
    public static function getNoticeNum(int $work_id, string $equip_id, $user_id = null)
    {
        $msg_ids = self::find()->select('id')
            ->where('work_id = :work_id AND expired_time > :time',
                [':work_id' => $work_id, ':time' => $_SERVER['REQUEST_TIME']])
            ->asArray()->column();
        $notice_num = count($msg_ids) - MsgRead::getReadCount($equip_id, $msg_ids, $user_id);
        return $notice_num;
    }

    public static function getExpiredIds(array $msg_ids)
    {
        $now = $_SERVER['REQUEST_TIME'];
        $expired_msg_ids = self::find()->select('id')
            ->where(['id' => $msg_ids])
            ->andWhere('expired_time < :time', [':time' => $now])
            ->column();
        return array_map('intval', $expired_msg_ids);
    }

}
