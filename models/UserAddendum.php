<?php

namespace app\models;

use app\components\models\traits\UserTrait;
use app\components\util\Go;
use app\forms\LoginForm;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\db\Exception;
use yii\helpers\Json;
use yii\web\HttpException;

/**
 * This is the model class for table "user_addendum".
 *
 * @property integer $id 用户 ID
 * @property integer $sex 性别
 * @property string $birthday 生日
 * @property string $qq QQ 昵称
 * @property string $weibo 微博昵称
 * @property string $wechat 微信昵称
 * @property string $bilibili 哔哩哔哩昵称
 * @property string $apple 苹果昵称
 * @property string $message_config 消息设置
 * @property string $ip 用户 IP
 * @property string $ip_detail 用户 IP 详情
 * @property integer $sobot 用户智齿客服 partnerId 版本，0: 新版；-1: 旧版；> 0: 旧版访问时间戳
 * @property string $sign 用户签到信息
 * e.g. {"continuous_days":97,"continuous_days_end_time":1670428800,"current_days":2,"current_sign_time":1673107200}
 * continuous_days: 超过 30 天的连续签到天数
 * continuous_days_end_time: 超过 30 天的连续签到最后签到日期时间戳，单位：秒
 * current_days: 当前连续签到天数
 * current_sign_time: 最后签到日期时间戳，单位：秒
 * @property string $more 更多详情
 * @property integer $birthdate_mmdd 用于检索的生日月日
 */
class UserAddendum extends ActiveRecord
{
    use UserTrait;

    const MALE = 1;
    const FEMALE = 2;

    // receive：设置是否接收未关注人私信 key 名；fold：设置是否收起未关注人私信 key 名
    const MSG_CFG_TYPE_RECEIVE = 'receive';
    const MSG_CFG_TYPE_FOLD = 'fold';
    // 是否接收未关注人私信 0：否；1：是
    const MESSAGE_RECEIVE = 1;
    const MESSAGE_REJECT = 0;
    // 是否收起未关注人私信 0：展开；1：收起
    const MESSAGE_FOLD = 1;
    const MESSAGE_EXPAND = 0;

    // 用户智齿客服 partnerId 版本
    // 0: 新版
    const SOBOT_PARTNERID_NEW = 0;
    // -1: 旧版
    const SOBOT_PARTNERID_OLD = -1;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'user_addendum';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id'], 'required'],
            [['id', 'sex', 'sobot'], 'integer'],
            [['birthday', 'more'], 'safe'],
            [['qq', 'weibo', 'bilibili', 'wechat', 'apple'], 'string', 'max' => 32],
            [['ip'], 'string', 'max' => 50],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'sex' => '性别',  // 0 为未知，1 为男，2 为女
            'birthday' => '生日',
            'qq' => 'QQ 昵称',
            'weibo' => '微博昵称',
            'bilbili' => 'Bilibili 昵称',
            'wechat' => '微博昵称',
            'apple' => '苹果昵称',
            'message_config' => '消息设置',
            'ip' => '用户 IP',
            'ip_detail' => '用户 IP 详情',
            'sobot' => '用户智齿客服 partnerId 版本',
            'sign' => '用户签到信息',
            'more' => '更多详情',
            'birthdate_mmdd' => '用于检索的生日月日',
        ];
    }

    public function getSex()
    {
        switch ($this->sex) {
            case self::MALE:
                return '男';
            case self::FEMALE:
                return '女';
            default:
                return '保密';
        }
    }

    public static function setSex($user_id, $sex)
    {
        $user = self::getByPk($user_id);

        switch ($sex) {
            case self::MALE: break;
            case self::FEMALE: break;
            default: $sex = 0;
        }
        $user->sex = $sex;
        return $user->save();
    }

    public static function setBirthday($user_id, $birthday)
    {
        $date = explode('-', $birthday);
        if (3 === count($date) && checkdate($date[1], $date[2], $date[0])) {
            [$year, $month, $day] = $date;
            $user = self::getByPk($user_id);
            // 补零使其成为 YYYY-mm-dd 的格式
            $user->birthday = sprintf('%04d-%02d-%02d', $year, $month, $day);
            // 补零使其成为 mmdd 的格式
            $user->birthdate_mmdd = sprintf('%02d%02d', $month, $day);
            if ($user->save()) {
                SetBirthdayDetailLog::addLog($user_id, $user->birthday, $user->birthdate_mmdd);
                return true;
            }
        }
        return false;
    }

    /**
     * 获取用户的智齿 partner_id
     *
     * @param int $user_id 用户 ID
     * @return int|string
     * @throws Exception
     */
    public static function getSobotPartnerId(int $user_id)
    {
        return self::renewSobot($user_id) ? md5($user_id . USER_SOBOT_PARTNERID_SECRET_KEY) : $user_id;
    }

    /**
     * 更新用户智齿客服用户信息并返回当前是否启用新版 partnerId
     * 技术方案文档：https://info.missevan.com/pages/viewpage.action?pageId=84725945
     *
     * @param integer $user_id 用户 ID
     * @return bool 当前是否启用新版 partnerId
     */
    public static function renewSobot(int $user_id): bool
    {
        $user = self::getByPk($user_id);
        $old_sobot = $user->sobot ?? UserAddendum::SOBOT_PARTNERID_NEW;
        if ($old_sobot === UserAddendum::SOBOT_PARTNERID_NEW) {
            // 已经是新版 partnerId 可直接返回
            return true;
        }
        $is_new = false;
        $sobot = $_SERVER['REQUEST_TIME'];
        if ($old_sobot !== UserAddendum::SOBOT_PARTNERID_OLD && ($sobot - $old_sobot) > THIRTY_DAYS) {
            // 距离上次访问超过一个月的用户可下发新版 partnerId
            $is_new = true;
            $sobot = UserAddendum::SOBOT_PARTNERID_NEW;
        }
        $user->sobot = $sobot;
        if (!$user->save()) {
            throw new Exception(MUtils2::getFirstError($user));
        }
        return $is_new;
    }

    /**
     * 更新用户签到信息
     *
     * @param int $user_id 用户 ID
     * @param array $sign_history 近一个月签到日期记录 e.g. [1673280000, 1673366400]
     * @return array 连续签到信息
     */
    public static function renewSign(int $user_id, array $sign_history): array
    {
        $user = self::getByPk($user_id);
        $user_sign = $user->sign ?? [];
        // 最后一次连续签到超过 30 天时的连续天数
        $last_above_continuous_30_days = $user_sign['continuous_days'] ?? 0;
        // 最后一次连续签到超过 30 天的签到时间
        $last_above_continuous_30_time = $user_sign['continuous_days_end_time'] ?? 0;
        // 获取最后一次连续签到超过 30 天的时间点距今的天数
        $today = strtotime('today', $_SERVER['REQUEST_TIME']);
        $last_above_30_sign_distance_today = ($today - $last_above_continuous_30_time) / ONE_DAY;
        if ($last_above_30_sign_distance_today <= 31) {
            // 若最后一次连续签到超过 30 天的时间点距今不大于 31 天（包含今天），则需要续上连续天数
            if ($last_above_30_sign_distance_today === 31) {
                $sign_history[] = $last_above_continuous_30_time;
            }
            for ($i = $last_above_30_sign_distance_today - 1; $i >= 0; $i--) {
                if (in_array($today - $i * ONE_DAY, $sign_history)) {
                    // 上一次签到与今天相隔 31 天及以内，补签后若与之前连续，则更新该签到天数和时间
                    $last_above_continuous_30_time += ONE_DAY;
                    $last_above_continuous_30_days++;
                } else {
                    break;
                }
            }
        } else {
            // 若之前的连续签到信息已经超过 30 天，则该数据不再有意义，赋值为 0 不再更新
            $last_above_continuous_30_days = $last_above_continuous_30_time = 0;
        }
        // 最后一次签到时的连续签到天数
        $last_sign_days = $user_sign['current_days'] ?? 0;
        // 最后一次签到时的签到时间
        $last_sign_time = $user_sign['current_sign_time'] ?? 0;
        if ($last_sign_days <= 30 || $last_sign_time !== ($today - ONE_DAY)) {
            // 若最后一次连续签到天数不大于 30 天（不包含今天）或已非连续签到，
            // 则从签到历史中（签到历史中已包含本次签到）重新获取最新的连续签到天数
            $last_sign_days = 0;
            for ($i = 1; $i <= 30; $i++) {
                // 截止到昨天的连续签到天数
                // 由于今天不签到也不算做断签，故从昨天开始统计
                if (in_array($today - $i * ONE_DAY, $sign_history)) {
                    $last_sign_days++;
                } else {
                    break;
                }
            }
            if (in_array($today, $sign_history)) {
                // 如果今天已经签到，连续签到天数 +1
                $last_sign_days++;
                $last_sign_time = $today;
            }
        } else {
            // 若最后一次连续签到天数大于 30 天且未断签，则连续签到天数 +1
            $last_sign_days++;
            $last_sign_time = $today;
        }
        if (!$last_above_continuous_30_days && $last_sign_days > 30) {
            // 若之前无连续签到大于 30 天的信息，在签到满 30 天之后，需要更新
            $last_above_continuous_30_days = $last_sign_days;
            $last_above_continuous_30_time = $last_sign_time;
        }
        if ($last_above_continuous_30_days) {
            if ($last_above_continuous_30_time === $last_sign_time && $last_sign_days < $last_above_continuous_30_days) {
                // 若最后一次连续签到超过 30 天的签到时间等于当前签到时间，则当前签到天数重新赋值，以续上之前的签到时间
                $last_sign_days = $last_above_continuous_30_days;
            }
            $user_sign = array_merge($user_sign, [
                'continuous_days' => $last_above_continuous_30_days,
                'continuous_days_end_time' => $last_above_continuous_30_time,
            ]);
        }
        $user_sign = array_merge($user_sign, [
            'current_days' => $last_sign_days,
            'current_sign_time' => $last_sign_time,
        ]);
        $user->sign = $user_sign;
        if (!$user->save()) {
            throw new Exception(MUtils2::getFirstError($user));
        }
        return Json::decode($user->sign);
    }

    public static function checkGender($gender)
    {
        if (in_array($gender, [self::MALE, self::FEMALE])) {
            return true;
        }
        return false;
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->message_config) {
            $this->message_config = Json::decode($this->message_config);
        } else {
            $this->message_config = [
                self::MSG_CFG_TYPE_RECEIVE => self::MESSAGE_RECEIVE,
                self::MSG_CFG_TYPE_FOLD => self::MESSAGE_EXPAND
            ];
        }
        if ($this->ip_detail) {
            $this->ip_detail = Json::decode($this->ip_detail);
        }
        if ($this->sign) {
            $this->sign = Json::decode($this->sign);
        }
    }
    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        if ($this->message_config) {
            $this->message_config = Json::encode($this->message_config);
        }
        if ($this->ip_detail) {
            $this->ip_detail = Json::encode($this->ip_detail);
        }
        if ($this->sign) {
            $this->sign = Json::encode($this->sign);
        }
        return true;
    }

    /**
     * 创建第三方账号记录
     *
     * @param int $user_id 用户 ID
     * @param array|null $auth_info 第三方账号信息
     * @return bool
     * @throws Exception
     * @throws HttpException
     */
    public static function saveAuthAccount(int $user_id, ?array $auth_info): bool
    {
        if ($user_id <= 0) {
            throw new HttpException(404, Yii::t('app/error', 'User does not exist'));
        }
        if (!$auth_info) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        $auth_type = $auth_info['auth_type'] ?? 0;
        $third_name = $auth_info['username'] ?? null;
        if (is_null($third_name)) {
            throw new HttpException(400, Yii::t('app/error', 'Missing parameters'));
        }
        $column_value = [];
        switch ($auth_type) {
            case LoginForm::QQ:
                $column_value['qq'] = $third_name;
                break;
            case LoginForm::WEIBO:
                $column_value['weibo'] = $third_name;
                break;
            case LoginForm::WECHAT:
                $column_value['wechat'] = $third_name;
                break;
            case LoginForm::BILIBILI:
                $column_value['bilibili'] = $third_name;
                break;
            case LoginForm::APPLE:
                $column_value['apple'] = $third_name;
                break;
            default:
                throw new HttpException(400, Yii::t('app/error', 'Incorrect third-party account type'));
        }
        if (self::find()->where('id = :id', [':id' => $user_id])->exists()) {
            return (bool)self::updateAll($column_value, ['id' => $user_id]);
        }
        $insert_columns = array_merge(['id' => $user_id], $column_value);
        // 生成的 SQL: INSERT INTO `user_addendum` (`id`, `qq`) VALUES (346287, 'test') ON DUPLICATE KEY UPDATE `qq`=VALUES(`qq`)
        return (bool)Yii::$app->db->createCommand()->upsert(self::tableName(), $insert_columns)->execute();
    }

    /**
     * 添加用户 IP databus 日志
     *
     * @param integer $user_id 用户 ID
     */
    public static function addIPLog(int $user_id)
    {
        if ($user_id === 0) {
            return false;
        }
        try {
            $log = [
                'user_id' => $user_id,
                'ip' => Yii::$app->request->userIP ?: '',
                'create_time' => $_SERVER['REQUEST_TIME'],
                'from' => Go::FROM_APP
            ];
            Yii::$app->databus->pub($log, 'collect_user_ip_log:' . $user_id);
            return true;
        } catch (\Exception $e) {
            Yii::error('collect user ip log error: ' . $e->getMessage(), __METHOD__);
            // PASS: 添加日志出错不抛出异常，避免影响正常业务流程
            return false;
        }
    }

    /**
     * 展示 IP 属地信息
     *
     * @param int $user_id 用户 ID
     * @return string
     */
    public static function showIPLocation(int $user_id)
    {
        if (!$user_id) {
            return '';
        }
        $add_user = self::find()
            ->select('ip_detail')
            ->where(['id' => $user_id])
            ->one();
        if (!$add_user) {
            return '';
        }
        return MUtils2::getIPLocationFromIPDetail($add_user->ip_detail);
    }

    /**
     * 获取用户连续签到天数和最近一个月签到记录
     *
     * @param int $user_id 用户 ID
     * @param bool $get_history_data 是否获取用户近一个月签到记录
     * @param array|null $user_sign 用户签到信息
     * @param array|null $sign_history 用户近一个月签到数据
     * @return array 用户连续签到信息
     */
    public static function getUserSign(int $user_id, bool $get_history_data = false, ?array $user_sign = null, ?array $sign_history = null)
    {
        if (is_null($user_sign)) {
            $user = UserAddendum::getByPk($user_id);
            $user_sign = $user->sign ?? [];
        }
        $today = strtotime('today', $_SERVER['REQUEST_TIME']);
        $current_days = 0;
        $current_sign_time = $user_sign['current_sign_time'] ?? 0;
        if ($today - $current_sign_time <= ONE_DAY) {
            $current_days = $user_sign['current_days'] ?? 0;
        }

        $return = [
            'current_days' => $current_days,
        ];

        if (!$get_history_data) {
            return $return;
        }
        // 获取数字表示的用户近一个月签到记录
        $history_data = 0;
        if (is_null($sign_history)) {
            $sign_history = UserSignHistory::getUserSignHistoryList($user_id);
        }
        if (!empty($sign_history)) {
            for ($i = 0; $i <= 30; $i++) {
                if (in_array($today - $i * ONE_DAY, $sign_history)) {
                    $history_data = $history_data | (1 << $i);
                }
            }
        }
        $return['history_data'] = $history_data;
        return $return;
    }

    /**
     * 获取用户微信公众号 openid
     *
     * @return null|string
     */
    public function getWechatPubAccOpenId(): ?string
    {
        if ($this->more && ($more = Json::decode($this->more)) && array_key_exists('wechat_pub_acc_openid', $more)) {
            return $more['wechat_pub_acc_openid'];
        }

        return null;
    }

    /**
     * 保存用户微信公众号 openid
     *
     * @param string $openid
     */
    public function saveWechatPubAccOpenId(string $openid)
    {
        // more 字段中保存用户最新的微信 openid，用于创建微信公众号支付订单
        // 由于无法区分本次登录的微信是否和上次登录的微信是否一致，因此每次都需要进行静默授权
        $more = $this->more ? Json::decode($this->more) : [];
        $more['wechat_pub_acc_openid'] = $openid;
        $this->updateAttributes(['more' => Json::encode($more)]);
    }

}
