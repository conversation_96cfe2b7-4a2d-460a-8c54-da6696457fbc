<?php

namespace app\models;

use Yii;
use yii\helpers\Json;

class CarnivalActivity extends ActiveRecord
{
    const ORIGIN_LIKE_SOUND = 1;
    const ORIGIN_ADD_COMMENT = 2;
    const MEVENT_ID = 133; // 活动 ID

    const IS_LUCK_DRAW = 2; // 用户当天已经抽奖
    const IS_MEET_LUCK_DRAW = 1; // 用户已满足抽奖条件

    /**
     * 设置满足条件的抽奖用户
     *
     * @param int $user_id 用户 ID
     * @param int $works_id 作品 ID
     * @param int $origin 来源 1：点赞单音；2：添加评论
     * @param bool $status
     */
    public static function setMeetConditionsUser(int $user_id, int $works_id, int $origin, bool $status)
    {
        $time = $_SERVER['REQUEST_TIME'];

        // 当活动不存在时，直接返回
        if (!$event = MEvent::findOne(self::MEVENT_ID)) {
            return;
        }
        // 当活动未开始或者已结束直接返回
        if ($time < $event->start_time || $time >= $event->end_time) {
            return;
        }
        // 获取是否国庆学习挑战活动内的投稿作品
        $exist = EventVote::find()->where([
            'event_id' => self::MEVENT_ID,
            'eid' => $works_id,
            'category' => EventVote::CATEGORY_SPECIAL,
        ])->exists();

        // 不是国庆学习挑战活动内的投稿作品直接返回
        if (!$exist) {
            return;
        }
        $redis = Yii::$app->redis;
        $today_time = date('Ymd', $time);

        // 存储满足条件抽奖用户 key
        $key = $redis->generateKey(KEY_EVENT_MEET_CONDITIONS_USER, $today_time);

        $is_luck_draw = (int)$redis->hGet($key, $user_id);
        // 用户今日已抽奖或已满足抽奖条件，直接跳出
        if ($is_luck_draw === self::IS_LUCK_DRAW || $is_luck_draw === self::IS_MEET_LUCK_DRAW) {
            return;
        }

        // 当天开始时间
        $start_time = strtotime('today');
        $now = $_SERVER['REQUEST_TIME'];

        // 当用户点赞成功，判断用户对同一作品是否评论，是则满足抽奖条件
        if ($origin === self::ORIGIN_LIKE_SOUND) {
            $exist = SoundComment::find()->where(
                'userid = :userid AND element_id = :element_id AND c_type = :c_type 
                    AND ctime >= :start_time AND ctime <= :end_time', [
                ':userid' => $user_id,
                ':element_id' => $works_id,
                ':c_type' => SoundComment::TYPE_SOUND,
                ':start_time' => $start_time,
                ':end_time' => $now
            ])->exists();
            if ($exist) {
                $redis->multi()
                    ->hSet($key, $user_id, self::IS_MEET_LUCK_DRAW)
                    ->expireAt($key, $event->end_time + ONE_WEEK)
                    ->exec();
            }
        }
        // 当用户评论成功，判断用户对同一作品是否点赞，是则满足抽奖条件
        if ($origin === self::ORIGIN_ADD_COMMENT) {
            $exist = MLikeSound::find()->where(
                'user_id = :user_id AND sound_id = :sound_id', [
                    ':user_id' => $user_id,
                    ':sound_id' => $works_id
                ])->exists();
            if ($exist) {
                $redis->multi()
                    ->hSet($key, $user_id, self::IS_MEET_LUCK_DRAW)
                    ->expireAt($key, $event->end_time + ONE_WEEK)
                    ->exec();
            }
        }
    }

    public static function addDrawCardPoint()
    {
        // 抽卡获取积分，此后抽卡获取积分可以在此方法中添加规则
    }

    public static function addBuyPoint()
    {
        // 消费送积分，此后消费获取积分可以在此方法中添加规则
    }
}
