<?php

namespace app\models;

use app\components\auth\AuthAli;
use app\components\auth\AuthWechat;
use app\components\util\Equipment;
use app\components\util\Tools;
use app\forms\UserContext;
use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "vip_fee_deducted_record".
 *
 * @property integer $id 主键
 * @property integer $create_time 创建时间（秒级时间戳）
 * @property integer $modified_time 修改时间（秒级时间戳）
 * @property integer $user_id 用户 ID
 * @property integer $vip_id 会员价目 ID
 * @property integer $sign_agreement_id 签约协议 ID（0 为单次付费）
 * @property integer $pay_type 付费方式：1 iOS、2 微信、3 支付宝、4 Google Play
 * @property integer $price 本次扣款金额（单位：分）
 * @property integer $tax 渠道费（单位：分）
 * @property integer $status 本次扣款状态：1 待扣款，2 扣款成功，3 扣款失败
 * @property integer $next_deduct_time 下次扣款时间（秒级时间戳，0 为单次付费）
 * @property string $transaction_id 交易票据 ID
 * @property array $more 更多详情，e.g. { "is_first_topup_discount": true }
 * @property integer $confirm_time 交易确认时间
 *
 * more 中的字段
 * @property int $expect_price
 * @property bool $is_first_topup_discount
 */
class VipFeeDeductedRecord extends ActiveRecord
{
    use ActiveRecordTrait;

    const TRADE_NO_PREFIX = 'vip_';

    // 扣款状态
    const STATUS_PENDING = 1; // 待扣款
    const STATUS_SUCCESS = 2; // 扣款成功
    const STATUS_FAILED = 3; // 扣款失败
    const STATUS_DUPLICATE_PAY = 4; // 重复支付
    const STATUS_CANCELED = 5;  // 被取消（退款等原因）

    // 付费方式（与 VipSubscriptionSignAgreement 中的同名常量一致）
    const PAY_TYPE_IOS = 1;
    const PAY_TYPE_WECHAT = 2;
    const PAY_TYPE_ALIPAY = 3;
    const PAY_TYPE_GOOGLE_PLAY = 4;

    // 生成订单编号时各参数的所占位数长度
    const TRADE_NO_PAY_TYPE_PAD_LENGTH = 3;  // 支付方式所占位数长度
    const TRADE_NO_ID_PAD_LENGTH = 10;  // 订单 ID 所占位数长度

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->paydb;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'vip_fee_deducted_record';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'vip_id', 'sign_agreement_id', 'pay_type', 'price', 'status', 'next_deduct_time'], 'required'],
            [['create_time', 'modified_time', 'user_id', 'vip_id', 'sign_agreement_id', 'pay_type', 'price', 'tax', 'status', 'next_deduct_time', 'confirm_time'], 'integer'],
            [['pay_type'], 'in', 'range' => [
                self::PAY_TYPE_IOS,
                self::PAY_TYPE_WECHAT,
                self::PAY_TYPE_ALIPAY,
                self::PAY_TYPE_GOOGLE_PLAY,
            ]],
            [['status'], 'in', 'range' => [
                self::STATUS_PENDING,
                self::STATUS_SUCCESS,
                self::STATUS_FAILED,
                self::STATUS_DUPLICATE_PAY,
                self::STATUS_CANCELED,
            ]],
            [['transaction_id', 'more'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'user_id' => '用户 ID',
            'vip_id' => '会员价目 ID',
            'sign_agreement_id' => '签约协议 ID',
            'pay_type' => '付费方式',
            'price' => '本次扣款金额',
            'tax' => '渠道费',
            'status' => '本次扣款状态',
            'next_deduct_time' => '下次扣款时间',
            'transaction_id' => '交易票据 ID',
            'more' => '更多详情',
            'confirm_time' => '交易确认时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
            if ($this->status === self::STATUS_SUCCESS) {
                $this->confirm_time = $time;
            }
        } elseif (array_key_exists('status', $this->getDirtyAttributes()) && $this->status === self::STATUS_SUCCESS) {
            $this->confirm_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 是否是订阅付费（连续包月、连续包季）
     *
     * @return bool
     */
    public function isSubscription(): bool
    {
        return $this->sign_agreement_id > 0;
    }

    /**
     * 是否是首开折扣价
     *
     * @return bool
     */
    public function getIs_first_topup_discount(): bool
    {
        return $this->more['is_first_topup_discount'] ?? false;
    }

    /**
     * 设置是否是首开折扣价
     *
     * @param bool $is_first_topup_discount
     */
    public function setIs_first_topup_discount(bool $is_first_topup_discount): void
    {
        $more = $this->more ?? [];
        if ($is_first_topup_discount) {
            $more['is_first_topup_discount'] = true;
        } else {
            unset($more['is_first_topup_discount']);
        }
        $this->more = $more;
    }

    public static function getIosDefaultOrderId()
    {
        return (YII_ENV_PROD ? '' : 'dev_') .
            self::TRADE_NO_PREFIX .
            str_repeat('0', 10) .
            str_pad(self::PAY_TYPE_IOS, self::TRADE_NO_PAY_TYPE_PAD_LENGTH, '0', STR_PAD_LEFT) .
            str_pad('0', self::TRADE_NO_ID_PAD_LENGTH, '0', STR_PAD_LEFT);
    }

    /**
     * 获取交易编号
     *
     * @return string
     */
    public function getOutTradeNo(): string
    {
        return (YII_ENV_PROD ? '' : 'dev_') .
            self::TRADE_NO_PREFIX .
            $this->create_time .
            str_pad($this->pay_type, self::TRADE_NO_PAY_TYPE_PAD_LENGTH, '0', STR_PAD_LEFT) .
            str_pad($this->id, self::TRADE_NO_ID_PAD_LENGTH, '0', STR_PAD_LEFT);
    }

    /**
     * 交易编号前缀是否是会员交易编号前缀
     *
     * @param string $trade_no 交易编号
     * @return bool
     */
    public static function isVipTradeNoPrefix(string $trade_no): bool
    {
        $prefix = (YII_ENV_PROD ? '' : 'dev_') . self::TRADE_NO_PREFIX;
        return str_starts_with($trade_no, $prefix);
    }

    /**
     * 根据交易编号获取实际 ID
     *
     * @param string $trade_no 交易编号
     * @return int
     */
    public static function getRealId(string $trade_no): int
    {
        return (int)substr($trade_no, -self::TRADE_NO_ID_PAD_LENGTH);
    }

    /**
     * 解析交易编号，获取实际 ID 和支付方式
     *
     * @param string $trade_no
     * @return array|null 如果交易编号无效，返回 null
     */
    public static function parseTradeNo(string $trade_no): ?array
    {
        $prefix = (YII_ENV_PROD ? '' : 'dev_') . self::TRADE_NO_PREFIX;
        if (!str_starts_with($trade_no, $prefix)) {
            return null;
        }

        $id = self::getRealId($trade_no);

        $pay_type = (int)substr($trade_no, -(self::TRADE_NO_PAY_TYPE_PAD_LENGTH + self::TRADE_NO_ID_PAD_LENGTH),
            self::TRADE_NO_PAY_TYPE_PAD_LENGTH);
        if (!in_array($pay_type, [
            self::PAY_TYPE_IOS,
            self::PAY_TYPE_WECHAT,
            self::PAY_TYPE_ALIPAY,
            self::PAY_TYPE_GOOGLE_PLAY,
        ])) {
            return null;
        }

        return [
            'id' => $id,
            'pay_type' => $pay_type,
        ];
    }

    public static function generateWechatOrder(int $vip_id, int $user_id, UserContext $user_context)
    {
        $auth = new AuthWechat();
        $auth->carry(self::PAY_TYPE_WECHAT);
        return self::generateOrder($auth, $vip_id, $user_id, $user_context);
    }

    public static function generateAlipayOrder(int $vip_id, int $user_id, UserContext $user_context)
    {
        $auth = new AuthAli();
        $auth->carry(self::PAY_TYPE_ALIPAY);
        return self::generateOrder($auth, $vip_id, $user_id, $user_context);
    }

    /**
     * 生成开通会员订单
     *
     * @param AuthWechat|AuthAli $auth
     * @param int $vip_id 会员价目 ID
     * @param int $user_id 用户 ID
     * @param UserContext $user_context
     * @return array
     * @throws Exception
     * @throws HttpException
     */
    public static function generateOrder(AuthWechat|AuthAli $auth, int $vip_id, int $user_id, UserContext $user_context)
    {
        $user = Mowangskuser::getSimpleInfoById($user_id);
        if (!$user) {
            throw new HttpException(404, '用户不存在');
        }
        if ($user->isBanTopupAndConsume()) {
            throw new HttpException(403, '您的账号暂被系统停封，不可进行充值消费操作');
        }

        $transaction = null;
        try {
            // 加锁
            $redis = Yii::$app->redis;
            $lock = $redis->generateKey(LOCK_GENERATE_VIP_ORDER, $user_id);
            if (!$redis->lock($lock, ONE_MINUTE)) {
                throw new HttpException(400, '操作过于频繁，请稍候再试');
            }
            $vip = MVip::findOne(['id' => $vip_id, 'platform' => MVip::PLATFORM_ANDROID, 'delete_time' => 0]);
            if (!$vip) {
                throw new HttpException(404, '会员价目不存在');
            }

            $pay_type = $auth->getCarryData();
            $transaction = self::getDb()->beginTransaction();
            // 会员扣费记录
            $order = new self([
                'user_id' => $user_id,
                'vip_id' => $vip_id,
                'pay_type' => $pay_type,
                'price' => $vip->price,
                'status' => self::STATUS_PENDING,
                'sign_agreement_id' => 0,
                'next_deduct_time' => 0,  // 实际下次扣费时间以支付成功回调接口时间为准
                'more' => self::getMoreExtraInfo(Yii::$app->equip, $user_context),
            ]);
            // 是否是订阅付费（连续包月、连续包季）
            $is_subscription = $vip->isSubscription();
            $sign_agreement = null;
            if ($is_subscription) {
                // 用户订阅会员周期扣款签约协议
                $sign_agreement = new VipSubscriptionSignAgreement([
                    'user_id' => $user_id,
                    'pay_type' => $pay_type,
                    'vip_id' => $vip_id,
                    'status' => VipSubscriptionSignAgreement::STATUS_PENDING,
                    // 实际签约开始时间和过期时间以签约、解约成功回调接口时间为准
                    'start_time' => 0,
                    'expire_time' => 0,
                    'more' => $order->more,
                ]);
                if (!$sign_agreement->save()) {
                    Yii::error(sprintf('create sign agreement error: %s', MUtils2::getFirstError($sign_agreement)),
                        __METHOD__);
                    throw new HttpException(500, '服务器暂时维护中，请稍候再试');
                }
                $order->sign_agreement_id = $sign_agreement->id;
                if ($vip->deduct_fee_schedule === MVip::DEDUCT_FEE_SCHEDULE_MONTHLY
                        && !self::hasSubscribedPromotional($user_id)) {
                    // 连续包月且用户未享受过首次优惠时，价格为首次优惠价格
                    $order->is_first_topup_discount = true;
                    $order->price = $vip->getPriceToDeduct(true);
                }
            }
            if (!$order->save()) {
                Yii::error(sprintf('create order error: %s', MUtils2::getFirstError($order)), __METHOD__);
                throw new HttpException(500, '服务器暂时维护中，请稍候再试');
            }

            $transaction->commit();
            $transaction = null;

            return $auth->tradeVipAppPay($order, $sign_agreement, $vip, $user, $is_subscription);
        } catch (Exception $e) {
            if ($transaction) {
                $transaction->rollback();
            }
            throw $e;
        } finally {
            // 解锁
            $redis->unlock($lock);
        }
    }

    public static function getMoreExtraInfo(Equipment $equip, UserContext $user_context)
    {
        $user_context_arr = $user_context->toArray();
        return [
            'user_context' => $user_context_arr,
            'device_info' => [
                'equip_id' => $equip->getEquipId(),
                'buvid' => $equip->getBuvid(),
                'os' => $equip->getOs(),
                'channel' => $equip->getChannel(),
            ],
        ];
    }

    /**
     * 更新扣款记录
     *
     * @param int $pay_type 付费方式（1: iOS；2: 微信；3: 支付宝；4: Google Play）
     * @param string $out_trade_no 商户订单号
     * @param string $transaction_id 平台交易 ID
     * @param int $price 价格（单位：分）
     * @param null $func
     * @return bool
     * @throws Exception
     */
    public static function updateOrder(int $pay_type, string $out_trade_no, string $transaction_id, int $price, $func = null)
    {
        $order_id = self::getRealId($out_trade_no);
        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_UPDATE_VIP_ORDER, $order_id);
        if (!$redis->lock($lock, ONE_MINUTE)) {
            return false;
        }
        try {
            $transaction = self::getDb()->beginTransaction();
            $record = self::findOneForUpdate('id = :id AND pay_type = :pay_type',
                [':id' => $order_id, ':pay_type' => $pay_type]);
            if (!$record) {
                throw new Exception('扣款记录不存在');
            }
            switch ($record->status) {
                case self::STATUS_PENDING:
                    break;
                case self::STATUS_SUCCESS:
                    return true;
                case self::STATUS_FAILED:
                    return false;
                default:
                    throw new Exception(sprintf('未知扣款状态：%d', $record->status));
            }
            $vip = MVip::findOne(['id' => $record->vip_id]);
            if (!$vip) {
                throw new Exception('会员价目不存在');
            }
            $more = $record->more ?? [];
            $more['out_trade_no'] = $out_trade_no;
            if (is_callable($func)) {
                $more = $func($more);
            }
            $record->more = $more;
            $record->transaction_id = $transaction_id;
            $record->tax = self::calculateTax($record->price, $record->pay_type);
            // 创建或延长用户会员之前用户是否是会员
            $is_vip = MUserVip::isVipUser($record->user_id);
            if ($record->is_first_topup_discount && self::hasSubscribedPromotional($record->user_id)) {
                // 用户未达到享受首次开通优惠的条件。比如创建多个首次充值并且待支付的订单，然后支付其中一个订单，剩余的其他订单如果再次完成支付会进入此分支
                // 这里我们不自动退款，需要找客服退款或延长会员时长
                Yii::error(sprintf('用户 %d 未达到享受折扣福利的条件，扣款记录 %d',
                    $record->user_id, $record->id), __METHOD__);
                $record->status = self::STATUS_DUPLICATE_PAY;
            } elseif ($record->price === $price) {
                $record->status = self::STATUS_SUCCESS;
                if ($vip->isSubscription() && !$record->next_deduct_time) {
                    // 周期扣费定时任务根据该时间提前发起扣款
                    // 统一记录加上周期时间后的时间，是否提前扣费统一由周期扣费定时任务处理
                    $record->next_deduct_time = strtotime('midnight', $_SERVER['REQUEST_TIME']) + $vip->getPeriod();
                }
            } else {
                Yii::error(sprintf('扣款记录（ID: %d）金额不匹配，应付金额 %s，实际支付金额 %s',
                    $record->id, $record->price, $price), __METHOD__);
                $record->status = self::STATUS_FAILED;
            }
            if (!$record->save()) {
                throw new Exception(sprintf('扣款记录更新失败: %s', MUtils2::getFirstError($record)));
            }
            if ($record->status === self::STATUS_SUCCESS) {
                // 创建或延长用户会员
                $res = MUserVip::createOrExtend($record->user_id, $vip, $record->id);
                if (!$res) {
                    throw new Exception('用户会员信息更新失败');
                }
            }
            $transaction->commit();
            $transaction = null;
            if ($record->status === self::STATUS_SUCCESS) {
                // 开通或续费成功时，发送开通或续费成功的系统通知
                $vip_center_url = $rank_link = Yii::$app->params['vip']['center_url'];
                Yii::$app->tools->sendNotification([
                    'user_id' => $record->user_id,
                    'title' => $is_vip ? '会员续费提醒' : '会员开通提醒',
                    'content' => $is_vip
                        ? sprintf('尊敬的会员，恭喜您续费会员服务，可以继续每天领钻石、畅听会员剧，<a href="%s">点击了解详情</a>', $vip_center_url)
                        : sprintf('尊敬的会员，恭喜您开通了会员服务，钻石每天领、会员剧畅听，<a href="%s">点击了解详情</a>', $vip_center_url),
                ], Tools::SEND_SYS_MSG);
                AdTrack::callbackPay($record->user_id, Balance::profitUnitConversion($record->price, Balance::CONVERT_FEN_TO_YUAN));
            }
            return true;
        } catch (Exception $e) {
            if ($transaction) {
                $transaction->rollBack();
            }
            Yii::error(sprintf('更新扣款记录失败：%s，扣款记录 %d', $e->getMessage(), $order_id), __METHOD__);
            return false;
        } finally {
            // 解锁
            $redis->unlock($lock);
        }
    }

    /**
     * 检查用户是否享受过连续包月首次优惠
     *
     * @param int $user_id 用户 ID
     * @param int $filter_id 需要排除的扣费记录 ID
     * @param int $pay_type 付费方式
     * @return bool
     */
    public static function hasSubscribedPromotional(int $user_id, int $filter_id = 0, int $pay_type = 0)
    {
        $query = self::find()
            ->where(['user_id' => $user_id, 'status' => self::STATUS_SUCCESS]);
        if (YII_ENV === 'test') {
            // WORKAROUND: SQLite 不支持 IS TRUE 的写法，使用 IS NOT NULL 代替（理论上表里不会存 is_first_topup_discount 为 false 的数据）
            $query = $query->andWhere('JSON_EXTRACT(more, "$.is_first_topup_discount") IS NOT NULL');
        } else {
            $query = $query->andWhere('JSON_EXTRACT(more, "$.is_first_topup_discount") IS TRUE');
        }
        if ($filter_id) {
            $query = $query->andWhere('id <> :id', [':id' => $filter_id]);
        }
        if ($pay_type) {
            $query = $query->andWhere(['pay_type' => self::PAY_TYPE_IOS]);
        }
        return $query->exists();
    }

    /**
     * 获取用户上下文
     *
     * @return UserContext|null
     */
    public function getUserContext(): ?UserContext
    {
        $user_context = $this->more['user_context'] ?? null;
        if (!$user_context) {
            return null;
        }
        return new UserContext(
            $user_context['user_agent'],
            $user_context['ip'],
            $user_context['equip_id'] ?? null,
            $user_context['buvid'] ?? null
        );
    }

    /**
     * 计算渠道费
     *
     * @param int $price 总价（单位：分）
     * @param int $pay_type 付费方式：1 iOS、2 微信、3 支付宝、4 Google Play
     * @return int 渠道费（单位：分）
     */
    public static function calculateTax(int $price, int $pay_type): int
    {
        return (int)ceil($price * self::getFeeRate($pay_type));
    }

    public static function getFeeRate(int $pay_type)
    {
        switch ($pay_type) {
            case self::PAY_TYPE_IOS:
                return Yii::$app->params['vip_fee_rate']['ios'];
            default:
                return Yii::$app->params['vip_fee_rate']['android'];
        }
    }

    public static function getRecord(string $transaction_id, int $pay_type)
    {
        return self::findOne(['transaction_id' => $transaction_id, 'pay_type' => $pay_type]);
    }

    public function isSuccess()
    {
        return $this->status === self::STATUS_SUCCESS;
    }

    public function isPending()
    {
        return $this->status === self::STATUS_PENDING;
    }

    public function setExpect_price(int $expect_price)
    {
        $more = $this->more ?: [];
        $more['expect_price'] = $expect_price;
        $this->more = $more;
    }

    public function getExpect_price()
    {
        $more = $this->more ?: [];
        return $more['expect_price'] ?? null;
    }

    /**
     * @param string|null $transaction_id
     * @param int $user_id
     * @param MVip $vip
     * @param int $pay_type
     * @param VipSubscriptionSignAgreement|null $agreement
     * @return self
     */
    public static function newRecord(?string $transaction_id, int $user_id, MVip $vip, int $pay_type, ?VipSubscriptionSignAgreement $agreement, int $status)
    {
        $args = [$user_id];
        if ($pay_type === self::PAY_TYPE_IOS) {
            $args = [$user_id, 0, $pay_type];
        }
        $has_subscribe_promotional = self::hasSubscribedPromotional(...$args);
        return new self([
            'user_id' => $user_id,
            'vip_id' => $vip->id,
            'sign_agreement_id' => $agreement ? $agreement->id : 0,
            'pay_type' => $pay_type,
            'price' => $vip->getPriceToDeduct(!$has_subscribe_promotional),
            'status' => $status,
            'next_deduct_time' => 0,
            'transaction_id' => $transaction_id ?: '',
        ]);
    }
}
