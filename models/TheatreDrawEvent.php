<?php

namespace app\models;

use Exception;
use Yii;

/**
 * 该 Model 用于封装盲盒剧场抽奖活动相关方法
 *
 * @link https://info.missevan.com/pages/viewpage.action?pageId=90792160
 * @todo 活动结束后（2023-04-29 后）需要删除
 */

class TheatreDrawEvent
{
    // 抽盲盒时获得的积分数量
    const POINT_DRAW_BLIND_BOX = 1;

    // 每周抽盲盒任务最多可获得的积分数量
    const WEEKLY_THEATRE_DRAW_BLIND_BOX_MAX_POINT = 1;

    /**
     * 抽盲盒添加积分
     *
     * @param int $user_id 用户 ID
     * @throws Exception 参数错误抛出异常
     */
    public static function addDrawBlindBoxPoint(int $user_id)
    {
        if ($user_id <= 0) {
            throw new Exception('参数错误，user_id: ' . $user_id);
        }
        try {
            $event = MEvent::find()->where('id = :id', [':id' => MEvent::EVENT_ID_THEATRE_DRAW_III])->one();
            if (!$event) {
                // 相关活动不存在，不加积分
                return;
            }
            $now = $_SERVER['REQUEST_TIME'] + ($event->extended_fields['time_offset'] ?? 0);
            $draw_point_start_time = $event->extended_fields['draw_point_start_time'] ?? 0;
            $draw_point_end_time = $event->extended_fields['draw_point_end_time'] ?? 0;
            if ($now < $draw_point_start_time || $now >= $draw_point_end_time) {
                // 若不在抽奖活动时间内，不加积分
                return;
            }
            $redis = Yii::$app->redis;
            $cache_key = $redis->generateKey(KEY_DRAW_POINT_QUESTS_EVENT_ID_USER_ID, MEvent::EVENT_ID_THEATRE_DRAW_III,
                $user_id);
            $cache_hash_field = 'theatre_draw';
            $point = $redis->hGet($cache_key, $cache_hash_field) ?: 0;
            if ((int)$point >= self::WEEKLY_THEATRE_DRAW_BLIND_BOX_MAX_POINT) {
                // 若已达到该任务每周可获得的最大积分数，不再加积分
                return;
            }
            // 事务
            [$point, ] = $redis->multi()
                ->hIncrBy($cache_key, $cache_hash_field, self::POINT_DRAW_BLIND_BOX)
                ->expireAt($cache_key, $event->end_time + THIRTY_DAYS)  // 过期时间为活动结束时间加上 30 天
                ->exec();
            if (($point - self::POINT_DRAW_BLIND_BOX) >= self::WEEKLY_THEATRE_DRAW_BLIND_BOX_MAX_POINT) {
                // 若已达到该任务每周可获得的最大积分数，不再加积分
                return;
            }
            // 给用户添加积分
            Yii::$app->minigame->updateEventDrawPoint(MEvent::EVENT_ID_THEATRE_DRAW_III, $user_id,
                self::POINT_DRAW_BLIND_BOX);
        } catch (Exception $e) {
            Yii::error('抽盲盒加积分异常：' . $e->getMessage(), __METHOD__);
            // PASS: 加积分出错不能影响正常业务流程
        }
    }
}
