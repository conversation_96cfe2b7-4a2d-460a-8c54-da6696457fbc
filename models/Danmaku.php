<?php

namespace app\models;

use app\components\util\Go;
use app\components\util\MUtils;
use Yii;

/**
 * This is the model class for table "danmaku".
 *
 * @property int $id 主键
 * @property int $card_id 卡片 ID
 * @property int $user_id 用户 ID
 * @property string $text 内容
 * @property string $stime 出现时间
 * @property int $size 字号
 * @property int $color 颜色
 * @property int $mode 模式：1 滚动、2 顶部、3 底部
 * @property int $date 添加时间
 */
class Danma<PERSON> extends ActiveRecord
{

    const DEFAULT_SIZE = 25;
    const DEAFULT_COLOR = 16777215;
    const DEFAULT_MODE = 1;

    const ONE_DAY_LIMIT = 1000;

    // 假发送
    private $_isShamSend = false;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'danmaku';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db2');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['card_id', 'user_id', 'text', 'stime'], 'required'],
            [['card_id', 'user_id', 'color', 'date', 'size', 'mode', 'stime'], 'integer'],
            [['text'], 'checkText'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'card_id' => '卡片 ID',
            'user_id' => '用户 ID',
            'text' => '弹幕',
            'stime' => '发送时间点',
            'size' => '字体大小',
            'color' => '字体颜色',
            'mode' => 'Mode',
            'date' => '发送时间',
        ];
    }

    public function getIsShamSend()
    {
        return $this->_isShamSend;
    }

    /**
     * 验证弹幕是否合规
     *
     * @param string $attribute 验证的属性
     * @param mixed[] $params 其他自定义参数
     */
    public function checkText($attribute)
    {
        // 过滤特殊字符
        $this->$attribute = trim(MUtils::filterSpecialCodes($this->$attribute));
        if ($this->$attribute === '') {
            $this->addError($attribute, '请输入非纯表情的弹幕哦~');
            return;
        }
        // 检测弹幕违规情况
        if (!$result = Yii::$app->go->checkText($this->$attribute, Go::SCENE_DANMAKU)) {
            return;
        }
        if (!$item = current($result)) {
            return;
        }
        if (!$item['pass']) {
            $this->addError($attribute, $this->getAttributeLabel($attribute) . '中含有违规词汇喔~');
        } elseif (is_array($item['labels'])) {
            $this->_isShamSend = $this->checkShamSend($item['labels']);
        }
    }

    /**
     * 检查是否假发送
     *
     * @param $labels
     * @return bool
     */
    private function checkShamSend(array $labels): bool
    {
        return in_array(Go::LABEL_EVIL, $labels);
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->date = $time;
            $this->color = self::DEAFULT_COLOR;
            $this->size = self::DEFAULT_SIZE;
            $this->mode = self::DEFAULT_MODE;
        }
        return true;
    }

}
