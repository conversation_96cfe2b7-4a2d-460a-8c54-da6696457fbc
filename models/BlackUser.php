<?php

namespace app\models;

use app\components\base\db\ActiveQuery;
use app\components\util\MUtils;
use app\components\web\AuthManage;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\db\Expression;
use yii\web\HttpException;

/**
 * This is the model class for table "black_user".
 *
 * @property integer $big_id ID 更大的用户 ID
 * @property integer $small_id ID 更小的用户 ID
 * @property integer $status 状态
 * @property integer $create_time 创建时间
 * @property integer $modified_time 更新时间
 */
class BlackUser extends ActiveRecord
{
    // 拉黑状态：1：small_id 拉黑 big_id；2：big_id 拉黑 small_id
    const STATUS_SMALL_BAN_BIG = 1;
    const STATUS_BIG_BAN_SMALL = 2;
    // 与私信对象关系 0：双方都为非拉黑状态；1：拉黑对方；2：被拉黑；3：互相拉黑
    const BLACKLIST_NONE = 0;
    const BLACKLIST_ACTIVE = 1;
    const BLACKLIST_PASSIVE = 2;
    const BLACKLIST_BOTH = self::STATUS_SMALL_BAN_BIG | self::STATUS_BIG_BAN_SMALL;

    // 禁止用户拉黑的官方白名单账号
    const ALLOW_LIST_USER_IDS = [
        Mowangskuser::OFFICIAL_M_GIRL_USER_ID,
        Mowangskuser::OFFICIAL_CUSTOMER_SERVICE_STAFF_USER_ID,
    ];

    /**
     * @inheritdoc
     */
    public static function getDb()
    {
        return Yii::$app->messagedb;
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'black_user';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['big_id', 'small_id', 'status'], 'required'],
            [['big_id', 'small_id', 'status'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'big_id' => 'ID 大的用户 ID',
            'small_id' => 'ID 小的用户 ID',
            // status 二进制运算：1 位：small_id 拉黑 big_id；2 位：big_id 拉黑 small_id
            'status' => '状态',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 获取两位用户之间的关系
     *
     * @param int $own_id 自己 ID
     * @param int $user_id 对方 ID
     * @return array 返回拉黑状态和关注状态，例 ['blacklist' => 0, follow => 1]
     * blacklist 0：双方都为非拉黑状态；1：自己已拉黑对方；2：自己被拉黑；3：互相拉黑
     * follow 0: 未关注；1：已关注
     */
    public static function relation(int $own_id, int $user_id)
    {
        $return = [
            'blacklist' => 0,
            'followed' => 0,
        ];
        if ($own_id && $own_id !== $user_id) {
            // 判断是否关注该用户
            $followed = (int)MAttentionUser::find()
                ->where('user_active = :user_active AND user_passtive = :user_passtive', [
                    ':user_active' => $own_id,
                    ':user_passtive' => $user_id,
                ])->exists();
            // 判断是否在黑名单中
            $blacklist = self::getBlacklistRelation($own_id, $user_id);
            $return = [
                'blacklist' => $blacklist,
                'followed' => $followed
            ];
        }
        return $return;
    }

    /**
     * 获取两个用户之间黑名单关系
     *
     * @param int $own_id 自己 ID
     * @param int $user_id 对方 ID
     * @return int 0：双方都为非拉黑状态；1：自己已拉黑对方；2：自己被拉黑；3：互相拉黑
     */
    public static function getBlacklistRelation(int $own_id, int $user_id)
    {
        if ($own_id && $own_id !== $user_id) {
            $is_bigger = $own_id > $user_id;
            if ($is_bigger) {
                $small_id = $user_id;
                $big_id = $own_id;
            } else {
                $small_id = $own_id;
                $big_id = $user_id;
            }
            $blacklist_info = self::findOne(['small_id' => $small_id, 'big_id' => $big_id]);
            $blacklist_status = $blacklist_info ? $blacklist_info->status : self::BLACKLIST_NONE;
            if (($is_bigger && self::STATUS_BIG_BAN_SMALL === $blacklist_status) ||
                    (!$is_bigger && self::STATUS_SMALL_BAN_BIG === $blacklist_status)) {
                // 自己拉黑对方
                return self::BLACKLIST_ACTIVE;
            } elseif (($is_bigger && self::STATUS_SMALL_BAN_BIG === $blacklist_status) ||
                    (!$is_bigger && self::STATUS_BIG_BAN_SMALL === $blacklist_status)) {
                // 被对方拉黑
                return self::BLACKLIST_PASSIVE;
            } else {
                return $blacklist_status;
            }
        }
        return self::BLACKLIST_NONE;
    }

    /**
     * 获取黑名单用户列表
     *
     * @param int $user_id 用户 ID
     * @param int $page_size 每页个数
     * @return ReturnModel
     */
    public static function getBlacklist(int $user_id, int $page_size = DEFAULT_PAGE_SIZE)
    {
        if (!$user_id) {
            return ReturnModel::empty(1, $page_size);
        }
        $blacklist_query = BlackUser::find()
            // 此处查询出来的 big_id 为已加入黑名单的用户 ID
            ->select('(small_id + big_id - :user_id) AS big_id, create_time')
            ->where('small_id = :user_id AND status & :small_blacklist_status = :small_blacklist_status')
            ->orWhere('big_id = :user_id AND status & :big_blacklist_status = :big_blacklist_status')
            ->addParams([
                ':user_id' => $user_id,
                ':small_blacklist_status' => self::STATUS_SMALL_BAN_BIG,
                ':big_blacklist_status' => self::STATUS_BIG_BAN_SMALL,
            ])
            ->orderBy('create_time DESC');
        $return_model = MUtils::getPaginationModels($blacklist_query, $page_size);
        if ($return_model->Datas) {
            $blacklist_user_ids = array_column($return_model->Datas, 'big_id');
            $user_info = Mowangskuser::find()->select('id, username, confirm, avatar, boardiconurl, icontype, coverurl')
                ->where(['id' => $blacklist_user_ids])
                ->indexBy('id')
                ->all();
            $return_model->Datas = array_map(function ($item) use ($user_id, $user_info) {
                return [
                    'id' => $item->big_id,
                    'username' => $user_info[$item->big_id]->username,
                    'iconurl' => $user_info[$item->big_id]->iconurl,
                    'authenticated' => $user_info[$item->big_id]->authenticated,
                    'create_time' => $item->create_time,
                ];
            }, $return_model->Datas);
        }
        return $return_model;
    }

    /**
     * 加入黑名单或移除黑名单
     *
     * @param int $user_id 操作加入黑名单用户 ID
     * @param int $blacklist_user_id 被加入黑名单用户 ID
     * @param int $is_del 操作类型：0：加入黑名单；1：移除黑名单
     * @throws \Throwable
     * @throws \yii\db\Exception
     */
    public static function blacklistOrNot(int $user_id, int $blacklist_user_id, int $is_del)
    {
        $is_bigger = $user_id > $blacklist_user_id;
        $transaction = null;
        try {
            if ($is_bigger) {
                $blacklist_condition = 'small_id = :blacklist_user_id AND big_id = :user_id';
                $small_id = $blacklist_user_id;
                $big_id = $user_id;
                $status = self::STATUS_BIG_BAN_SMALL;
                $blacklist_status = AnMsg::STATUS_BIG_BLACKLIST_MSG;
            } else {
                $blacklist_condition = 'small_id = :user_id AND big_id = :blacklist_user_id';
                $small_id = $user_id;
                $big_id = $blacklist_user_id;
                $status = self::STATUS_SMALL_BAN_BIG;
                $blacklist_status = AnMsg::STATUS_SMALL_BLACKLIST_MSG;
            }
            $blacklist_info = BlackUser::find()->where($blacklist_condition)->addParams([
                ':user_id' => $user_id,
                ':blacklist_user_id' => $blacklist_user_id
            ])->one();
            $msg_exists_query = AnMsgRO::find()
                ->where('small_id = :small_id AND big_id = :big_id', [':small_id' => $small_id, ':big_id' => $big_id]);
            if (!$is_del) {
                // 加入黑名单
                if (!$blacklist_info) {
                    $blacklist_info = new BlackUser();
                    $blacklist_info->small_id = $small_id;
                    $blacklist_info->big_id = $big_id;
                    $blacklist_info->status = $status;
                    if (!$blacklist_info->save()) {
                        throw new HttpException(400, MUtils::getFirstError($blacklist_info));
                    }
                } else {
                    $status_expression = new Expression("status | {$status}");
                    // 加乐观锁
                    $blacklist_condition .= ' AND status = :status';
                    BlackUser::updateAll(['status' => $status_expression], $blacklist_condition, [
                        ':blacklist_user_id' => $blacklist_user_id,
                        ':user_id' => $user_id,
                        ':status' => $blacklist_info->status
                    ]);
                }
                // 由于黑名单用户表迁移到评论库中，事务中不需要包含黑名单用户表的相关操作
                $transaction = Yii::$app->db->beginTransaction();
                // 取消双关
                MAttentionUser::cancelMutualFollow($small_id, $big_id);
                $msg_exists = $msg_exists_query->andWhere('status & :blacklist_status = 0',
                    [':blacklist_status' => $blacklist_status])->exists();
                if ($msg_exists) {
                    // 更新私信为黑名单且已读状态
                    $blacklist_status_expression = new Expression('(status | :status_blacklist) &~ :status_unread',
                        [':status_blacklist' => $blacklist_status, ':status_unread' => AnMsg::STATUS_UNREAD]);
                    AnMsg::updateAll(['status' => $blacklist_status_expression],
                        'small_id = :small_id AND big_id = :big_id AND status & :blacklist_status = 0',
                        [
                            ':small_id' => $small_id,
                            ':big_id' => $big_id,
                            ':blacklist_status' => $blacklist_status,
                        ]);
                }
                $transaction->commit();
            } elseif ($blacklist_info) {
                // 从黑名单移除
                // big_id 单向拉黑 small_id 情况下，移除黑名单则直接删除
                // small_id 单向拉黑 big_id 的情况下，移除黑名单则直接删除
                $delete = $is_bigger ? ($blacklist_info->status === self::STATUS_BIG_BAN_SMALL) :
                    ($blacklist_info->status === self::STATUS_SMALL_BAN_BIG);
                if ($delete) {
                    $blacklist_info->delete();
                } elseif ($blacklist_info->status === self::BLACKLIST_BOTH) {
                    // 互相拉黑的情况下需要判断解除谁拉黑谁的黑名单关系
                    $blacklist_info->status &= $is_bigger ? self::STATUS_SMALL_BAN_BIG : self::STATUS_BIG_BAN_SMALL;
                    if (!$blacklist_info->save()) {
                        throw new HttpException(400, MUtils::getFirstError($blacklist_info));
                    }
                }
                $msg_exists = $msg_exists_query->andWhere('status & :blacklist_status = :blacklist_status',
                    [':blacklist_status' => $blacklist_status])->exists();
                if ($msg_exists) {
                    // 解除私信黑名单状态
                    $blacklist_status_expression = new Expression("status &~ {$blacklist_status}");
                    AnMsg::updateAll(['status' => $blacklist_status_expression],
                        'small_id = :small_id AND big_id = :big_id AND status & :blacklist_status = :blacklist_status',
                        [
                            ':small_id' => $small_id,
                            ':big_id' => $big_id,
                            ':blacklist_status' => $blacklist_status,
                        ]);
                }
            }
        } catch (\yii\db\Exception $e) {
            if ($transaction) {
                $transaction->rollBack();
            }
            // 忽略唯一索引抛出的异常
            if (!MUtils2::isUniqueError($e, BlackUser::getDb())) {
                throw $e;
            }
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * 获取黑名单用户 IDs
     *
     * @param int|array $user_ids 用户 ID（可以是一个用户 ID，也可以是用户 IDs 数组）
     * @return array
     */
    public static function getBlacklistUserIDs($user_ids)
    {
        if (!$user_ids) {
            return [];
        }
        if (!is_array($user_ids)) {
            $user_ids = [(int)$user_ids];
        }
        // TODO: 之后可将黑名单用户存入 memcache，并在设置发生改变时重新生成缓存
        $query1 = BlackUser::find()
            ->select('small_id, big_id, status')
            ->where(['small_id' => $user_ids])
            ->andWhere('status & :small_blacklist_status = :small_blacklist_status', [
                ':small_blacklist_status' => self::STATUS_SMALL_BAN_BIG
            ]);
        $query2 = BlackUser::find()
            ->select('small_id, big_id, status')
            ->where(['big_id' => $user_ids])
            ->andWhere('status & :big_blacklist_status = :big_blacklist_status', [
                ':big_blacklist_status' => self::STATUS_BIG_BAN_SMALL
            ]);
        $blacklist_users_info = $query1->union($query2, true)->all();
        // $blacklist_users_info 结构：[['small_id' => '1', 'big_id' => '2'], ['small_id' => '2', 'big_id' => '4']]
        // 需要的返回值结构：[1, 4]
        $blacklist_user_ids = [];
        foreach ($blacklist_users_info as $item) {
            // user_ids 里的用户可能为黑名单关系，需要通过 status 判断
            if ((self::STATUS_BIG_BAN_SMALL & $item['status']) && in_array($item['big_id'], $user_ids)) {
                $blacklist_user_ids[] = $item['small_id'];
            }
            if ((self::STATUS_SMALL_BAN_BIG & $item['status']) && in_array($item['small_id'], $user_ids)) {
                $blacklist_user_ids[] = $item['big_id'];
            }
        }
        return array_unique($blacklist_user_ids);
    }

    /**
     * 获取过滤黑名单用户评论的 query
     *
     * @param ActiveQuery $query
     * @param array $check_user_ids 需要过滤黑名单用户 IDs
     * @param string $user_id_field 用户 ID 字段名
     *
     * @return ActiveQuery
     * @throws
     */
    public static function getFilterBlacklistQuery(ActiveQuery $query, array $check_user_ids = [],
            string $user_id_field = 't.user_id')
    {
        if (empty($check_user_ids) || !$user_id_field) {
            return $query;
        }
        // 过滤被 UP 主和查看用户拉黑用户的评论 ON 条件
        $query1 = BlackUser::find()
            ->select('big_id AS black_id')
            ->where(['small_id' => $check_user_ids])
            ->andWhere('status & :small_blacklist_status = :small_blacklist_status', [
                ':small_blacklist_status' => self::STATUS_SMALL_BAN_BIG
            ]);
        $query2 = BlackUser::find()
            ->select('small_id AS black_id')
            ->where(['big_id' => $check_user_ids])
            ->andWhere('status & :big_blacklist_status = :big_blacklist_status', [
                ':big_blacklist_status' => self::STATUS_BIG_BAN_SMALL
            ]);
        $blacklist_users = $query1->union($query2);

        return $query->leftJoin(['b' => $blacklist_users], "$user_id_field = b.black_id")
            ->andWhere('b.black_id IS NULL');
    }

    /**
     * 判断是否能拉黑用户
     *
     * @param int $blacklist_user_id 拉黑的用户 ID
     * @param int $user_id 当前登录的用户 ID
     * @throws HttpException
     * @todo 考虑迁移到 php-utils 项目中，方便各个项目使用
     */
    public static function checkSetBlacklist(int $blacklist_user_id, int $user_id): void
    {
        if ($blacklist_user_id <= 0) {
            throw new HttpException(400, '参数错误');
        }
        if ($user_id === $blacklist_user_id) {
            throw new HttpException(403, '我不允许你把自己拉黑！');
        }
        if (self::isAllowListUser($blacklist_user_id)) {
            // 目前因为拒绝提现审核，拒绝加 V 认证会通过M娘私信用户拒绝的原因，所以不允许拉黑M娘
            throw new HttpException(403, 'M娘开挂中，地球人无法拉黑她！');
        }
        if (!MowangskUser::find()->where('id = :id', [':id' => $blacklist_user_id])->exists()) {
            throw new HttpException(404, '用户不存在');
        }
        if (Yii::$app->authManager->isRoleByUser($blacklist_user_id, AuthManage::LIVE_OPERATOR,
            AuthManage::LIVE_ADMIN, AuthManage::AUDIT_LIVE))
        {
            throw new HttpException(403, '不可拉黑超管大大 T_T');
        }
    }

    /**
     * 判断是否为白名单中的用户 ID
     *
     * @param int $user_id
     * @return bool
     */
    public static function isAllowListUser(int $user_id): bool
    {
        return in_array($user_id, self::ALLOW_LIST_USER_IDS);
    }
}
