<?php

namespace app\models;

use app\components\util\MUtils;
use Yii;
use yii\db\Query;

/**
 * This is the model class for table "an_feedback_ticket".
 *
 * @property int $id
 * @property string $equip_id 设备号
 * @property string $buvid BUVID
 * @property string $content 反馈内容
 * @property int $client 客户端类型 1：安卓；2：iOS
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 * @property int $user_id 用户 ID
 * @property int $status 消息属性
 * @property int $type 消息类型
 * @property string $contact 联系方式
 */
class AnFeedbackTicket extends ActiveRecord
{
    // 状态显示颜色 0：客服未读；1：客服已读
    const STATUS_NOTICE_STAFF_UNREAD = 0;
    const STATUS_NOTICE_STAFF_READ = 1;

    // 设备号最大长度限制
    const EQUIP_ID_MAX_LENGTH = 44;
    // 联系方式最大长度限制
    const CONTACT_MAX_LENGTH = 255;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'an_feedback_ticket';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['content', 'type'], 'required'],
            [['content', 'buvid'], 'string'],
            [['client', 'create_time', 'modified_time', 'user_id', 'status', 'type'], 'integer'],
            [['equip_id'], 'string', 'max' => self::EQUIP_ID_MAX_LENGTH],
            [['contact'], 'string', 'max' => self::CONTACT_MAX_LENGTH],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'equip_id' => '设备号',
            'content' => '反馈内容',
            'client' => '客户端类型',  // 1：安卓；2：iOS；6：HarmonyOS
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'user_id' => '用户 ID',
            // status 二进制运算：1 位用户的反馈是否已被客服阅读（1 未读，0 已读）；
            // 2 位客服的回复是否已被用户阅读（1 未读，0 已读）；3 位标明身份（1 客服，0 用户）
            'status' => '消息属性',
            // 消息类型 1：其他；2：播放问题；3：闪退；4：注册登录问题；
            // 5：购买支付；6：下载问题；7：首页；8：直播问题；
            'type' => '消息类型',
            'buvid' => 'BUVID',
            'contact' => '联系方式',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
            if ($this->contact) {
                // 联系方式加密存储
                $this->contact = MUtils::encrypt($this->contact, $this->create_time);
            }
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 通过用户 ID 获取反馈记录
     *
     * @param int $user_id 用户 ID
     * @param int $page_size
     * @return ReturnModel
     */
    public static function listTicketByUserId(int $user_id, $page_size = PAGE_SIZE_20)
    {
        $query = AnFeedbackTicket::find()->select('id, content, status, create_time')
            ->where('user_id = :user_id', [':user_id' => $user_id]);
        return self::listTicket($query, $page_size);
    }

    /**
     * 通过设备号获取反馈记录
     *
     * @param string $equip_id 设备号
     * @param int $page_size
     * @return ReturnModel
     */
    public static function listTicketByEquipId(string $equip_id, $page_size = PAGE_SIZE_20)
    {
        // 按设备号获取反馈记录时，视作游客，用户 ID 需要为 0
        $query = AnFeedbackTicket::find()->select('id, content, status, create_time')
            ->where('equip_id = :equip_id AND user_id = 0', [':equip_id' => $equip_id]);
        return self::listTicket($query, $page_size);
    }

    /**
     * 获取反馈记录
     *
     * @param Query $query 查询 Query
     * @param int $page_size 分页大小
     * @return ReturnModel
     */
    private static function listTicket(Query $query, int $page_size)
    {
        $query = $query->orderBy('create_time DESC');
        $return_model = MUtils::getPaginationModels($query, $page_size);
        if (!empty($return_model->Datas)) {
            $result = [];
            array_map(function ($data) use (&$result) {
                $info['id'] = $data->id;
                $info['content'] = preg_replace(AnFeedback::EQUIPMENT_INFO_REGEX, '', $data->content);
                // 显示最新的一条回复时间（不论该条回复是用户还是客服）
                $last_time = AnFeedback::find()->select('create_time')
                    ->where('ticket_id = :ticket_id', [':ticket_id' => $data->id])
                    ->orderBy('create_time DESC')
                    ->limit(1)
                    ->scalar();
                $info['create_time'] = $last_time;
                $info['status_name'] = '';
                $info['status_notice'] = self::STATUS_NOTICE_STAFF_UNREAD;
                if ($data->status & AnFeedback::STATUS_USER_UNREAD) {
                    // 获取客服的未读消息数量
                    $unread_num = AnFeedback::find()->select('create_time')
                        ->where('ticket_id = :ticket_id AND status = :status',
                            [':ticket_id' => $data->id, ':status' => AnFeedback::STATUS_STAFF_REPLY_USER_UNREAD])
                        ->count();
                    $info['status_name'] = $unread_num . ' 条新回复';
                    $info['status_notice'] = self::STATUS_NOTICE_STAFF_READ;
                } elseif ($data->status === AnFeedback::STATUS_STAFF_USER_READ) {
                    // 因为用户已读客服也已读的状态是 0，不能进行与运算
                    $info['status_name'] = '已回复';
                    $info['status_notice'] = self::STATUS_NOTICE_STAFF_READ;
                } elseif ($data->status & AnFeedback::STATUS_STAFF_UNREAD) {
                    $info['status_name'] = '待回复';
                }
                $result[] = $info;
            }, $return_model->Datas);
            // 根据显示时间降序排列
            usort($result, function ($a, $b) {
                return $b['create_time'] <=> $a['create_time'];
            });
            $return_model->Datas = $result;
        }
        return $return_model;
    }
}
