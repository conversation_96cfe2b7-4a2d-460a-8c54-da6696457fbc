<?php

namespace app\models;

use app\components\random\PackageInterface;
use app\components\util\MUtils;
use Yii;
use yii\helpers\ArrayHelper;
use yii\web\HttpException;

/**
 * This is the model class for table "get_card".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 * @property int $appear_time 获取卡片时间
 * @property int $user_id 用户 ID
 * @property int $card_id 获取语音包卡片 ID
 * @property int $work_id 作品 ID，work 表主键
 * @property int $role_id 角色 ID
 * @property int $level 卡片等级，1: N, 2: R, 3: SR, 4: SSR（小剧场卡 level 为 0）
 * @property int $status 1 未生效，2 尚未收听，3 已收听
 * @property int $special 卡片类型：0 为普通卡片，1 为节日卡，2 为小剧场卡，3 为免费卡，4 为热度福利卡
 */
class GetCard extends ActiveRecord
{
    // 待推、未听、已听
    const STATUS_LOCK = 1;
    const STATUS_UNLOCK = 2;
    const STATUS_LISTENED = 3;

    // 给客户端的卡片显示状态：未解锁（未购/未获得）、待解锁（推送）、未收听（已获得）、已收听（已获得）、下架
    const SHOW_DISACTIVE = 0;
    const SHOW_ACTIVE = 1;
    const SHOW_NOTICE = 2;
    const SHOW_LISTENED = 3;
    const SHOW_UNSHELVE = 4;

    // 小剧场每解锁一张卡所需花费的钻石数
    const UNLOCK_EPISODE_COIN_LIMIT = 500;

    // 提前解锁待推送语音卡时需要的荣耀点折扣
    const UNLOCK_CARD_DISCOUNT = 0.5;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'get_card';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db2');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['appear_time', 'user_id', 'card_id', 'work_id', 'role_id', 'level', 'special'], 'required'],
            [['create_time', 'modified_time', 'appear_time', 'user_id', 'card_id', 'work_id', 'role_id', 'level', 'status', 'special'], 'integer'],
            [['user_id', 'card_id'], 'unique', 'targetAttribute' => ['user_id', 'card_id']],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'appear_time' => '推送',
            'user_id' => '用户 ID',
            'card_id' => '卡片 ID',
            'work_id' => '作品 ID',
            'role_id' => '所属角色 ID',
            'level' => 'Level',
            'status' => '卡片状态',
            'special' => '卡片类型',
        ];
    }

    public static function initialize($card)
    {
        $own = new self();
        $time = $_SERVER['REQUEST_TIME'] ?? time();
        $own->create_time = $time;
        $own->modified_time = $time;
        $own->appear_time = $time;
        $own->status = self::STATUS_UNLOCK;
        $own->card_id = $card->id;
        $own->work_id = $card->work_id;
        $own->role_id = $card->role_id;
        $own->level = $card->level;
        $own->special = $card->special;
        $own->card_package_id = $card->card_package_id;
        $own->interval = 0;
        return $own;
    }

    public static function buySeasonCards(int $card_package_id, int $user_id)
    {
        if (!$card_package = CardPackage::findOne($card_package_id)) {
            throw new HttpException(404, '该语音包不存在');
        }

        // buy $card_package;
        self::getPackageCards($card_package, $user_id, GetCardLog::TYPE_BUY);
    }

    /**
     * 获取抽卡得到的卡片
     *
     * @param int $lottery_package_id 卡包 ID
     * @param int $user_id 用户 ID
     * @return mixed 卡片对象组成的数组
     * @throws HttpException 语音包不存在时
     */
    public static function drawCards(int $lottery_package_id, int $user_id)
    {
        if (!$lottery_card_package = LotteryPackage::findOne($lottery_package_id)) {
            throw new HttpException(404, '该语音包不存在');
        }
        return self::getPackageCards($lottery_card_package, $user_id, GetCardLog::TYPE_DRAW);
    }

    /**
     * 获取求签得到的卡片
     *
     * @param int $lottery_package_id 卡包 ID
     * @param int $user_id 用户 ID
     * @return Card 卡片对象
     * @throws HttpException 语音包不存在时
     */
    public static function getOmikujiCard(int $lottery_package_id, int $user_id)
    {
        if (!$lottery_package = LotteryPackage::findOne($lottery_package_id)) {
            throw new HttpException(404, '该求签包不存在');
        }
        $redis = Yii::$app->redis;
        // 卡池缓存健名
        $key = $redis->generateKey(KEY_OMIKUJI_PACKAGE_WORK_ID_SEASON, $lottery_package->work_id,
            $lottery_package->season);
        // 求签包中每一签获得概率相同，从 Redis 集合中随机获取一签
        $card_id = 0;
        if (!($card_ids = $redis->sMembers($key))) {
            $card_ids = Card::find()
                ->select('id')
                ->where('card_package_id = :card_package_id AND special = :special AND is_online = :is_online',
                    [
                        ':card_package_id' => $lottery_package_id,
                        ':special' => Card::SPECIAL_OMIKUJI,
                        ':is_online' => Card::ONLINE
                    ])
                ->column();
            if (!$card_ids) {
                Yii::error("Lottery package has not cards，package_id：{$lottery_package_id}", __METHOD__);
                throw new HttpException(404, '求签包里空空的 T_T，请联系管理员');
            }
            $redis->sAddArray($key, $card_ids);
        }
        // 获取用户连续抽卡（重复）的数量
        $draw_counter_key = $redis->generateKey(COUNTER_OMIKUJI_DRAW, $lottery_package->work_id,
            $lottery_package->season, $user_id);
        $draw_counter = $redis->incr($draw_counter_key);
        if ($draw_counter > LotteryPackage::OMIKUJI_GUARANTEED_NUM) {
            // 若连续获得一定数量重复卡，则此次必定获得新卡
            $user_cards = self::userCards($card_ids, $user_id);
            $user_card_ids = array_column($user_cards, 'card_id');
            $new_card_id = array_diff($card_ids, $user_card_ids);
            if ($new_card_id) {
                // 若相对于用户还有新卡，随机获得一张新卡
                $card_id = $new_card_id[array_rand($new_card_id)];
            }
        }
        if (!$card_id) {
            $card_id = $redis->sRandMember($key);
        }
        // 此处使用 all() 查询出数组，方便调用相关相关方法
        $cards = Card::find()->where(['id' => $card_id, 'is_online' => Card::ONLINE])->all();
        if (!$cards) {
            Yii::error("下架卡片依然在求签包中，卡包 ID：{$lottery_package_id}，卡片 ID：{$card_id}", __METHOD__);
            throw new HttpException(404, '好像抽到了未知的签 T_T，请联系管理员');
        }
        // 给卡片添加是否为新卡的标识和获得卡片同时得到的兑换（幸运）点数量
        self::addCardParams($cards, $user_id, $lottery_package->given_coupon);
        if (current($cards)->is_new) {
            // 若抽到新卡，清除计数器
            $redis->del($draw_counter_key);
        }
        self::getCards($cards, $user_id, GetCardLog::TYPE_OMIKUJI);
        return current($cards);
    }

    /**
     * 兑换卡片
     *
     * @param Card $card 卡片对象
     * @param int $user_id 用户 ID
     */
    public static function exchangeCard($card, int $user_id, $method_of_obtaining = GetCardLog::TYPE_EXCHANGE)
    {
        $card->given_coupon = -$card->price;
        self::getCards([$card], $user_id, $method_of_obtaining);
    }

    /**
     * 解锁待推送卡片
     *
     * @param Card $card 卡片对象
     * @param int $user_id 用户 ID
     */
    public static function unlockCard($card, int $user_id)
    {
        self::getCards([$card], $user_id, GetCardLog::TYPE_UNLOCK);
    }

    /**
     * 领取小剧场卡片
     *
     * @param Card $card 卡片对象
     * @param int $user_id 用户 ID
     */
    public static function getEpisodeCard($card, int $user_id)
    {
        $card->given_coupon = 0;
        self::getCards([$card], $user_id, GetCardLog::TYPE_EPISODE);
    }

    /**
     * @param $cards
     * @param $rule
     *     array [{"level": ["level1", "level2"], "interval": time, "after": time}]
     *     抽卡时传 []
     * @param $user_id
     */
    private static function getPackageCards(PackageInterface $card_package, $user_id, int $type)
    {
        // 初始化
        $cards = $card_package->getCards();
        $rules = $card_package->getPushRules();
        $method_of_obtaining = $card_package::getObtainingMethod();
        // 获得购买或抽卡时赠送荣耀点
        $give_coupon = $card_package->getGiveCoupon();
        // 获取抽卡次数
        $times = $card_package->getDrawTimes();
        $one_card_given_coupon = 0;
        if ($times) {
            // 抽卡时，赠送荣耀点平均分配到抽到的每张卡的点数
            $one_card_given_coupon = $give_coupon / $times;
            if (!is_int($one_card_given_coupon)) {
                throw new HttpException(500, '卡包配置错误 T_T');
            }
            $give_coupon = 0;
        }
        // 给卡片添加是否为新卡的标识和获得卡片同时得到的荣耀点数量
        self::addCardParams($cards, $user_id, $one_card_given_coupon);
        self::getCards($cards, $user_id, $method_of_obtaining, $card_package->id, $rules, $give_coupon);
        return $cards;
    }

    private static function getCards($cards, int $user_id, $method_of_obtaining, $card_package_id = 0, $rules = [],
            $total_coupon = 0)
    {
        $get_cards = [];
        $get_card_logs = [];
        $user_cards = self::userCards(array_column($cards, 'id'), $user_id);
        $work_id = current($cards)->work_id;
        $repeat_cards = [];
        // 开启数据库事务，保持用户拥有卡与用户荣耀点的数据一致性
        $transaction = Yii::$app->db2->beginTransaction();
        $get_card_status = true;
        $card_ids = [];
        try {
            foreach ($cards as $card) {
                $card_id = $card->id;
                $card_ids[] = $card_id;
                $get_card_log = GetCardLog::initialize($card);
                $get_card_log->user_id = $user_id;
                $get_card_log->method_of_obtaining = $method_of_obtaining;
                if (($repeat_card = $repeat_cards[$card_id] ?? false) || ($user_card = $user_cards[$card_id] ?? NULL)) {
                    // 卡片重复时对用户荣耀点进行相关操作并修改相关卡片推送时间
                    switch ($method_of_obtaining) {
                        case GetCardLog::TYPE_UNLOCK:
                            $get_card_log->coupon = $card->unlock_price;
                            $total_coupon -= $card->unlock_price;
                            break;
                        case GetCardLog::TYPE_BUY:
                        case GetCardLog::TYPE_DRAW:
                        case GetCardLog::TYPE_OMIKUJI:
                            // 日志中记录兑换（幸运）点，包含赠与的兑换（幸运）点
                            $get_card_log->coupon = $card->given_coupon;
                            $total_coupon += $card->given_coupon;
                            break;
                        default:
                            throw new HttpException(403, '您已获得该卡片');
                    }
                    // 有推送规则，不改变原来的推送规则
                    if (!$rules && !$repeat_card && $user_card->status === self::STATUS_LOCK) {
                        $user_card->updateLockCard($user_id);
                    }
                } else {
                    $repeat_cards[$card_id] = true;
                    if ($method_of_obtaining === GetCardLog::TYPE_UNLOCK) {
                        throw new HttpException(403, '还未获得的卡不可解锁哦');
                    }
                    $total_coupon += $card->given_coupon;
                    $get_card_log->coupon = abs($card->given_coupon);
                    // appear_time
                    $get_card = self::initialize($card);
                    $get_card->user_id = $user_id;
                    foreach ($rules as $key => &$rule) {
                        if (in_array($card->level, $rule['level'])) {
                            $interval = $rule['interval'];
                            if ($get_card->appear_time !== $rule['start_time']) {
                                $get_card->appear_time = $rule['start_time'];
                                $get_card->status = self::STATUS_LOCK;
                            }
                            $get_card->interval = $interval;
                            $rule['start_time'] += $interval;
                            break;
                        }
                    }
                    if (!$get_card->appear_time) break;
                    $get_cards[] = $get_card;
                }
                $get_card_logs[] = $get_card_log;
            }
            $command = Yii::$app->db2->createCommand();
            if ($get_cards) {
                $rows = ArrayHelper::getColumn($get_cards, 'attributes');
                $command->batchInsert(self::tableName(), $get_cards[0]->attributes(), $rows)->execute();
            }
            if (in_array($method_of_obtaining, [GetCardLog::TYPE_OMIKUJI_EPISODE, GetCardLog::TYPE_OMIKUJI])) {
                // 若为获得求签语音，则更新用户在语音签中的总幸运点
                UserVoiceInfo::updateOmikujiCoupon($user_id, $total_coupon);
            } else {
                // 若为获得语音卡，更新用户在相关作品中的兑换点
                $user_info = WorkUserInfo::getUserInfo($user_id, $work_id);
                $user_info->updateCounters(['coupon' => $total_coupon]);
            }
            if ($get_cards && in_array($method_of_obtaining, [GetCardLog::TYPE_DRAW, GetCardLog::TYPE_EXCHANGE])) {
                // 通过抽卡或兑换而集齐卡片的，获得集齐的季包所在角色的节日卡片
                self::getGift($get_cards, $user_id);
            }
            $transaction->commit();
        } catch (\Exception $e) {
            $get_card_status = false;
            $card_ids = implode(', ', $card_ids);
            Yii::error("语音卡获取失败，获取方式：{$method_of_obtaining}，卡片 ID：{$card_ids}，错误原因："
                . $e->getMessage(), __METHOD__);
            $transaction->rollBack();
        }
        if ($get_card_logs) {
            // 新增日志记录（用户拥有卡片更新失败时，日志依然记录）
            GetCardLog::addLogs($get_card_logs);
        }
        if (!$get_card_status) {
            // 若用户卡片更新失败，则抛出异常
            throw new HttpException(500, '获得卡片失败了嘤嘤嘤 T_T');
        }
    }

    public static function getGift($new_cards, $user_id)
    {
        $package_ids = array_unique(array_column($new_cards, 'card_package_id'));
        $get_cards = GetCard::find()->select('card_id, card_package_id, role_id')
            ->where(['user_id' => $user_id, 'card_package_id' => $package_ids, 'special' => Card::SPECIAL_NORMAL])
            ->all();
        $get_cards_group = MUtils::groupArray($get_cards, 'card_package_id', 'card_id');

        $cards = Card::find()->select('id, card_package_id, role_id, work_id')
            ->where(['card_package_id' => $package_ids, 'special' => Card::SPECIAL_NORMAL, 'is_online' => Card::ONLINE])
            ->all();
        $cards_group = MUtils::groupArray($cards, 'card_package_id', 'id');
        $roles = array_column($cards, 'role_id', 'card_package_id');

        $roles_getfestival = [];
        foreach ($get_cards_group as $card_package_id => $card_ids) {
            if (!array_diff($cards_group[$card_package_id], $card_ids)) {
                $roles_getfestival[] = $roles[$card_package_id];
            }
        }

        if ($roles_getfestival) Card::insertFestivalCards($cards[0]->work_id, $user_id, $roles_getfestival);
    }

    public static function userCards(array $card_ids, $user_id)
    {
        if (!$user_id) return [];
        $user_cards = self::find()
            ->select('id, card_id, interval, appear_time, status, card_package_id, special, level, work_id')
            ->where(['card_id' => $card_ids, 'user_id' => $user_id])
            ->indexBy('card_id')
            ->all();
        return $user_cards;
    }

    // 更新锁定卡牌
    public function updateLockCard($user_id)
    {
        $rules = CardPackage::findOne($this->card_package_id)->getPushRules();
        $rule = [];
        foreach ($rules as $item) {
            if (in_array($this->level, $item['level'])) {
                $rule = $item;
                break;
            }
        }
        if ($rule) {
            $levels_range = MUtils::generateIntegerIn('level', $rule['level']);
            $condition = "appear_time >= :appear_time AND status = :status AND card_package_id = :card_package_id
                AND user_id = :user_id AND special = :special AND $levels_range";

            GetCard::updateAllCounters(
                ['appear_time' => -$rule['interval']],
                $condition,
                [
                    ':appear_time' => $this->appear_time,
                    ':status' => self::STATUS_LOCK,
                    ':card_package_id' => $this->card_package_id,
                    ':user_id' => $user_id,
                    ':special' => $this->special,
                ]);
        }
        $time = $_SERVER['REQUEST_TIME'] ?? time();
        $this->status = self::STATUS_UNLOCK;
        $this->appear_time = $time;
        $this->update(false);
    }

    /**
     * 将传入的卡片标识新旧并添加荣耀点
     *
     * @cards array 卡片对象组成的数组
     * @param int $user_id 用户 ID
     * @param int $one_card_given_coupon 每张卡赠送的荣耀点
     * @return bool 是否成功标识
     */
    public static function addCardParams(&$cards, int $user_id, int $one_card_given_coupon): bool
    {
        $user_cards = self::userCards(array_column($cards, 'id'), $user_id);
        $repeat_cards = [];
        $cards = array_map(function ($card) use (&$repeat_cards, $user_cards, $one_card_given_coupon) {
            $get_card = clone $card;
            $card_id = $get_card->id;
            if (in_array($card_id, $repeat_cards) || isset($user_cards[$card_id])) {
                // 当得到用户已有的卡或得到相同的新卡时
                $get_card->is_new = false;
                $get_card->given_coupon = $get_card->coupon + $one_card_given_coupon;
            } else {
                $get_card->is_new = true;
                $get_card->given_coupon = $one_card_given_coupon;
                $repeat_cards[] = $card_id;
            }
            return $get_card;
        }, $cards);
        return true;
    }

    /**
     * 获取用户在小剧场中卡片的解锁数
     *
     * @param integer $work_id 作品 ID
     * @param integer $user_id 用户 ID
     * @param integer $season 季度
     * @return integer 解锁的小剧场卡片数
     */
    public static function getEpisodeUnlockedCount($work_id, $user_id, $season)
    {
        if (!$user_id) return 0;
        $voice_expense = TransactionLog::getVoiceExpense($work_id, $user_id, $season);
        return intdiv($voice_expense, self::UNLOCK_EPISODE_COIN_LIMIT);
    }

    /**
     * 小剧场详情页（特定季度）
     *
     * @deprecated 供老接口 /voice/episode 使用，新接口使用 getEpisodes() 方法
     * @param integer $work_id 作品 ID
     * @param integer $season 季度
     * @param integer $user_id 用户 ID
     * @param integer $unlocked_count 解锁的小剧场卡数
     * @return array
     */
    public static function getEpisodeCards($work_id, $season, $user_id, $unlocked_count)
    {
        $episode_cards = Card::find()
            ->select('id, title, intro, price, is_online, card_package_id')
            ->where(['special' => Card::SPECIAL_EPISODE, 'work_id' => $work_id, 'card_package_id' => $season])
            ->orderBy('rank ASC')->asArray()->all();
        $getcards_status = [];
        $toget_count = 0;
        if ($user_id) {
            $getcards = self::userCards(array_column($episode_cards, 'id'), $user_id);
            $getcards_status = array_column($getcards, 'status', 'card_id');
            $toget_count = $unlocked_count ? ($unlocked_count - count($getcards_status)) : 0;
        }
        $toget_count = [$season => $toget_count];
        $episode_cards = self::processEpisodeStatus($episode_cards, $getcards_status, $toget_count);
        return $episode_cards;
    }

    /**
     * 小剧场详情页（各个季度）
     *
     * @param integer $work_id 作品 ID
     * @param integer $user_id 用户 ID
     * @return array
     */
    public static function getEpisodes($work_id, $user_id)
    {
        $episode_cards = Card::find()
            ->select('id, title, intro, price, is_online, card_package_id')
            ->where(['special' => Card::SPECIAL_EPISODE, 'work_id' => $work_id])
            ->orderBy('rank ASC')->asArray()->all();

        // $unlocked_count 为已解锁的卡片数，$toget_count 为待领取的卡片数，$voice_expense 语音包钻石花费
        $unlocked_count = $toget_count = $voice_expense = $getcards_status = [];
        if ($user_id) {
            $getcards = self::userCards(array_column($episode_cards, 'id'), $user_id);
            $getcards_status = array_column($getcards, 'status', 'card_id');

            $voice_expense = TransactionLog::getVoiceExpense($work_id, $user_id);
            // 已解锁的小剧场卡数（包含已领取与未领取）
            $unlocked_count = array_map(function ($v) {
                return intdiv($v, self::UNLOCK_EPISODE_COIN_LIMIT);
            }, $voice_expense);
            // 未领取的小剧场卡数
            $toget_count = $unlocked_count;
            $got_count = array_map('count',
                MUtils::groupArray($getcards, 'card_package_id', 'card_id'));
            array_walk($toget_count, function (&$count, $season) use ($got_count) {
                if (array_key_exists($season, $got_count)) {
                    $count = $count - $got_count[$season];
                }
            });
        }
        $episode_cards = self::processEpisodeStatus($episode_cards, $getcards_status, $toget_count);
        $episode_cards = MUtils::groupArray(array_values(array_filter($episode_cards)), 'card_package_id');
        $return = [];
        foreach ($episode_cards as $season => $cards) {
            $sum = count($cards);
            $return[] = [
                'season' => $season,
                'season_name' => CardPackage::SEASON_NAMES[$season],
                'all_count' => $sum,
                'unlocked_count' => min([$unlocked_count[$season] ?? 0, $sum]),
                'expense' => $voice_expense[$season] ?? 0,
                'expense_limit' => $sum * self::UNLOCK_EPISODE_COIN_LIMIT,
                'cards' => $cards,
            ];
        }
        usort($return, function ($a, $b) {
            return $a['season'] < $b['season'];
        });
        return $return;
    }

    /**
     * 处理小剧场卡片状态
     *
     * @param array $episode_cards 小剧场卡
     * @param array $getcards_status 已领取的小剧场卡
     * @param array $toget_count 各季小剧场待领取的数量
     * @return array
     */
    private static function processEpisodeStatus($episode_cards, $getcards_status, $toget_count)
    {
        // 确定小剧场卡片未解锁、待领取、未收听、已下架等状态状态
        $episode_cards = array_map(function ($card) use ($getcards_status, &$toget_count) {
            // card 表中当卡片为小剧场时字段 card_package_id 存储所属季度
            $season = (int)$card['card_package_id'];
            $card['is_online'] = (int)$card['is_online'];
            if (isset($getcards_status[$card['id']])) {
                $card['status'] = (int)$getcards_status[$card['id']];
                if (Card::OFFLINE === $card['is_online']) $card['status'] = GetCard::SHOW_UNSHELVE;
            } elseif (Card::OFFLINE === $card['is_online']) {
                return null;
            } elseif (($toget_count[$season] ?? 0) > 0) {
                $card['status'] = self::SHOW_ACTIVE;
                $toget_count[$season]--;
            } else {
                $card['status'] = self::SHOW_DISACTIVE;
            }
            $card['id'] = (int)$card['id'];
            $card['price'] = (int)$card['price'];
            $card['card_package_id'] = (int)$card['card_package_id'];
            return $card;
        }, $episode_cards);
        return $episode_cards;
    }

    /**
     * 小剧场详情页（各个季度）
     *
     * @param int $user_id 作品 ID
     * @param int $coupon 用户剩余幸运（兑换）点数
     * @return array
     */
    public static function getOmikujiEpisodes(int $user_id, int $coupon)
    {
        $now = $_SERVER['REQUEST_TIME'];
        $episode_cards = Card::find()
            ->alias('t1')
            ->select('t1.id, t1.title, t1.intro, t1.price, t1.is_online, t1.work_id, t1.`rank`, t2.season,
                t2.start_time AS package_start_time, t2.end_time AS package_end_time, t1.card_package_id')
            ->leftJoin(LotteryPackage::tableName() . ' AS t2', 't1.card_package_id = t2.id')
            ->where(['special' => Card::SPECIAL_OMIKUJI_EPISODE])
            ->andWhere(':now_time >= t2.start_time')
            ->params([':now_time' => $now])
            ->orderBy('rank ASC')
            ->asArray()
            ->all();
        $user_cards = [];
        if ($user_id) {
            $user_cards = self::userCards(array_column($episode_cards, 'id'), $user_id);
        }
        $paid_package_ids = TransactionLog::getPaidLotteryPackages($user_id,
            array_column($episode_cards, 'card_package_id'),
            array_column($episode_cards, 'work_id')
        );

        // 确定小剧场卡片未解锁、待领取、未收听、已下架等状态状态
        // 作品季度下可解锁卡片数量
        $work_season_can_unlock_count = [];
        $episode_cards = array_map(function ($card) use ($user_cards, &$work_season_can_unlock_count, $coupon,
                $now, $paid_package_ids) {
            $card['is_online'] = (int)$card['is_online'];
            $card['price'] = (int)$card['price'];
            $card_id = $card['id'] = (int)$card['id'];
            if (array_key_exists($card_id, $user_cards)) {
                $card['status'] = (int)$user_cards[$card_id]['status'];
                if (Card::OFFLINE === $card['is_online']) $card['status'] = GetCard::SHOW_UNSHELVE;
            } elseif (Card::OFFLINE === $card['is_online'] || $card['package_start_time'] > $now) {
                // 若卡片或卡片所属季包未上架，则移除
                return null;
            } elseif ($card['package_end_time'] < $now) {
                if (in_array($card['card_package_id'], $paid_package_ids)) {
                    $card['status'] = self::SHOW_UNSHELVE;
                } else {
                    // 不显示未购的已下架卡片
                    return null;
                }
            } elseif (($can_unlock_count = $work_season_can_unlock_count[$card['work_id']][$card['season']] ?? 0)
                    < 1) {
                // 同一作品下仅能同时解锁 1 个卡片
                $card['status'] = $card['price'] <= $coupon ? self::SHOW_ACTIVE : self::SHOW_DISACTIVE;
                // TODO: 此处直接加 1，若之后可解锁数量有调整，此处代码需要调整（剩余兑换点数处理）
                $work_season_can_unlock_count[$card['work_id']][$card['season']] = $can_unlock_count + 1;
            } else {
                $card['status'] = self::SHOW_DISACTIVE;
            }
            return $card;
        }, $episode_cards);
        $episode_cards = MUtils::groupArray(array_values(array_filter($episode_cards)), 'work_id');
        $works = Work::getWorksByType(Work::TYPE_OMIKUJI);
        return array_values(array_filter(array_map(function ($work) use ($episode_cards) {
            $cards = $episode_cards[$work->id] ?? [];
            $seasons = [];
            if (!empty($cards)) {
                $cards = MUtils::groupArray($cards, 'season');
                $seasons = array_keys($cards);
                $seasons = array_map(function ($season) use ($cards) {
                    return [
                        'season' => $season,
                        'subject' => Work::getSeasonSubject($season),
                        'cards' => $cards[$season],
                    ];
                }, $seasons);
            }
            if (empty($seasons)) {
                // 若无小剧场卡，则不在页面显示该作品
                return null;
            }
            return [
                'work_id' => $work->id,
                'work_name' => $work->title,
                'seasons' => $seasons,
            ];
        }, $works)));
    }

    public static function addCards(array $get_cards)
    {
        $rows = ArrayHelper::getColumn($get_cards, 'attributes');
        Yii::$app->db2->createCommand()->batchInsert(self::tableName(), $get_cards[0]->attributes(), $rows)
            ->execute();
    }

    /**
     * 全职首页语音列表
     *
     * @param int $work_id 作品 ID
     * @param int $user_id 用户 ID
     * @return array 每个角色的最新的提醒
     */
    public static function getMsgList($work_id, $user_id)
    {
        // 免费卡的提醒
        $notice = FreeNotice::getUnreadNotice($work_id, $user_id);
        if ($user_id) {
            // 非免费卡的提醒
            $package_notice = self::getMyCards($work_id, $user_id);
            $free_not_notice = FreeNotice::getFreeNotNotice(array_unique(array_column($package_notice, 'role_id')),
                $user_id);
            $notice = array_merge($notice, $package_notice, $free_not_notice);
        }

        $episode_icon = Work::getWorkPic($work_id, Work::TYPE_EPISODE_ICON);
        $role_ids = array_unique(array_column($notice, 'role_id'));
        $roles = Role::getRoles($role_ids);
        $freecards = MUtils::groupArray(Card::getFreeCards($work_id, $role_ids), 'role_id', 'id');

        $role_notices = array_reduce($notice, function ($role_notices, $item) use ($roles, $episode_icon, $freecards) {
            $role_id = $item['role_id'];
            $role = $role_id ? $roles[$role_id] : [];
            $role_notices[$role_id] = $role_notices[$role_id] ?? [
                'role_id' => $role_id,
                'role_name' => $role_id ? $role->name : '小剧场',
                'role_icon' => $role_id ? $role->icon : $episode_icon,
                'notice' => 0,
                'last_time' => $item['last_time'],
                'free_cards' => $freecards[$role_id] ?? [],
            ];
            if ($role_notices[$role_id]['last_time'] < $item['last_time']) {
                $role_notices[$role_id]['last_time'] = $item['last_time'];
            }

            if (isset($item['notice'])) {
                $role_notices[$role_id]['notice'] += $item['notice'];
            } elseif ((int)$item['status'] === self::STATUS_UNLOCK) {
                $role_notices[$role_id]['notice'] += 1;
            }
            return $role_notices;
        }, []);

        if ($user_id) {
            $hot_cards = Card::getHotCardsUnlocked($work_id);
            $listened = FreeNoticeListened::getListenedCards(array_column($hot_cards, 'id'), $user_id);
            $notice = count(array_filter($hot_cards, function ($item) use ($listened) {
                return !in_array($item['id'], $listened);
            }));
            if ($hot_cards) {
                $role_notices[] = [
                    'role_id' => -1,
                    'role_name' => '热度福利剧场',
                    'role_icon' => Work::getWorkPic($work_id, Work::TYPE_WELFARE_ICON),
                    'notice' => $notice,
                    'last_time' => max(array_column($hot_cards, 'push')),
                ];
            }
        }
        usort($role_notices, function ($a, $b) {
            return $b['last_time'] > $a['last_time'];
        });
        return $role_notices;
    }

    /**
     * 获取用户已解锁（收听、未收听）的卡片
     *
     * @param int $work_id 作品 ID
     * @param int $user_id 用户 ID
     * @param int $special 卡片类型
     * @return array 用户已解锁（收听、未收听）的卡片信息
     */
    public static function getMyCards($work_id, $user_id, $special = null)
    {
        $query = self::find()->select('appear_time AS last_time, role_id, card_id, status')->where([
            'work_id' => $work_id,
            'user_id' => $user_id,
            'status' => [self::STATUS_UNLOCK, self::STATUS_LISTENED]
        ])->orderBy('appear_time DESC');
        if (!is_null($special)) $query->andWhere(['special' => $special]);
        $mycards = $query->asArray()->all();
        return array_map(function ($item) {
            $item['last_time'] = (int)$item['last_time'];
            $item['role_id'] = (int)$item['role_id'];
            $item['card_id'] = (int)$item['card_id'];
            $item['status'] = (int)$item['status'];
            return $item;
        }, $mycards);
    }

    /**
     * 未读消息弹窗
     *
     * @param int $work_id 作品 ID
     * @param int $role_id 当为 0 时代表小剧场，-1 时代表热度福利卡，大于 0 时为角色卡片
     * @param int $user_id 用户 ID
     * @return array 作品或角色的未读消息
     */
    public static function getUnreadMsg($work_id, $role_id, $user_id)
    {
        if ($role_id === -1) {
            $msg = self::getHotCardsMsg($work_id, $user_id);
            $msg = array_map(function ($item) {
                return [
                    'id' => $item['id'],
                    'title' => $item['title'],
                ];
            }, $msg);
        } elseif ($role_id) {
            $msg = self::getRoleMsg($role_id, $user_id);
        } else {
            $msg = self::getEpisodeMsg($work_id, $user_id);
        }
        return $msg;
    }

    /**
     * 角色的未读消息
     *
     * @param int $role_id 角色 ID
     * @param int $user_id 用户 ID
     * @return array 角色的未读消息
     */
    public static function getRoleMsg($role_id, $user_id)
    {
        // 免费卡
        $free_listened = array_column(
            FreeNoticeListened::getHistoryByRole($role_id, $user_id, FreeNoticeListened::HAS_NOTICE_YES), 'card_id');
        $cards = Card::find()->select('id, title, level, create_time AS last_time, card_package_id')
            ->where([
                'special' => Card::SPECIAL_FREE,
                'role_id' => $role_id,
                'price' => Card::PRICE_FREE_CARD_HAVE_NOTICE,
                'is_online' => Card::ONLINE,
            ])->andWhere(['NOT IN', 'id', $free_listened])->orderBy('create_time DESC')->asArray()->all();
        if ($package_ids = array_column($cards, 'card_package_id')) {
            $package_ids = array_map('intval', array_unique($package_ids));
            $online_package_ids = CardPackage::find()->select('id')
                ->where(['id' => $package_ids, 'is_online' => CardPackage::ONLINE])->column();
            $cards = array_values(array_filter($cards, function ($item) use ($online_package_ids) {
                return in_array($item['card_package_id'], $online_package_ids);
            }));
        }

        if ($user_id) {
            // 非免费卡
            $get_cards = GetCard::find()->alias('g')
                ->select('c.id, c.title, c.level, g.appear_time AS last_time, status')
                ->where([
                    'g.role_id' => $role_id,
                    'g.user_id' => $user_id,
                    'g.special' => [Card::SPECIAL_FESTIVAL, Card::SPECIAL_NORMAL],
                    'c.is_online' => Card::ONLINE,
                ])
                ->innerJoin('card AS c', 'c.id = g.card_id')->orderBy('g.appear_time DESC')
                ->asArray()->all();
            if ($get_cards) {
                $not_notice_cards = FreeNotice::getUnreadFreeCardNotNotice($role_id, $user_id);
                $get_cards = array_filter($get_cards, function ($item) {
                    return self::STATUS_UNLOCK === (int)$item['status'];
                });
                $get_cards = array_merge($get_cards, $not_notice_cards);
            }
            $cards = array_merge($cards, $get_cards);
            usort($cards, function ($a, $b) {
                return $b['last_time'] > $a['last_time'];
            });
        }
        return array_map(function ($msg) {
            $msg['id'] = (int)$msg['id'];
            $msg['level'] = (int)$msg['level'];
            unset($msg['last_time']);
            return $msg;
        }, $cards);
    }

    /**
     * 小剧场的未读消息
     *
     * @param int $work_id 作品 ID
     * @param int $user_id 用户 ID
     * @return array 小剧场的未读消息
     */
    public static function getEpisodeMsg($work_id, $user_id)
    {
        if (!$user_id) return [];
        $msg = self::getMyCards($work_id, $user_id, Card::SPECIAL_EPISODE);
        $cards = Card::find()->select('id, title, level, special, is_online')
            ->where(['id' => array_column($msg, 'card_id')])->indexBy('id')->all();

        $msg = array_map(function ($item) use ($cards) {
            $card = $cards[$item['card_id']];
            if (self::STATUS_UNLOCK === $item['status'] && Card::ONLINE === $card->is_online) {
                return [
                    'id' => $card->id,
                    'title' => $card->title,
                ];
            }
        }, $msg);
        return array_values(array_filter($msg));
    }

    public static function getHotCardsMsg($work_id, $user_id)
    {
        $cards = Card::getHotCardsUnlocked($work_id);
        if ($user_id) {
            $listened = FreeNoticeListened::getListenedCards(array_column($cards, 'id'), $user_id);
            $cards = array_values(array_filter($cards, function ($item) use ($listened) {
                return !in_array($item['id'], $listened);
            }));
        }
        return $cards;
    }
}
