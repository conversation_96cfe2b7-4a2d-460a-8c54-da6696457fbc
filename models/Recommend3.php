<?php

namespace app\models;

use Yii;
use yii\helpers\Json;
use yii\web\HttpException;

/**
 * This is the model class for table "recommend3".
 *
 * @property int $sound_id 音频 ID
 * @property string $similar_items1 包含敏感音频的推荐单音数据
 * 格式："sound_id1:point1 sound_id2:point2 ..." | null
 * @property string $similar_items2 已过滤掉敏感音频的推荐单音数据
 * 格式："sound_id1:point1 sound_id2:point2 ..." | null
 */
class Recommend3 extends RecommendBase
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'recommend3';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id'], 'required'],
            [['similar_items1', 'similar_items2'], 'string', 'max' => 512],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'Sound ID',
            'similar_items1' => 'Similar Item1',
            'similar_items2' => 'Similar Item2',
        ];
    }

    protected static function filterSounds(array $sound_points, array $listened_sounds = [])
    {
        // 已收听过的音不再推荐
        // TODO: 可能对于很早之前听过的音还可以推荐给用户。
        $sound_points = array_diff_key($sound_points, $listened_sounds);
        arsort($sound_points);
        // TODO: 只需要前 500 条音频，这边之后这个数量可能还需要作调整
        $sound_points = array_slice($sound_points, 0, 500, true);
        return parent::filterSounds($sound_points, $listened_sounds);
    }

    public function getSoundPoints(bool $filter_sensitive = false)
    {
        $point_key = $filter_sensitive ? 'similar_item2' : 'similar_item1';
        // string "key1:value1 key2:value2" => map "{key1: value1, key2: value2}".
        $sounds_point = array_column(array_map(function ($sound) {
            return explode(':', $sound);
        }, explode(' ', $this->$point_key)), 1, 0);
        return $sounds_point;
    }
}
