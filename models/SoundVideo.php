<?php

namespace app\models;

use app\components\util\Equipment;
use Exception;
use missevan\storage\StorageClient;
use missevan\storage\UposClient;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "sound_video".
 *
 * @property integer $id
 * @property integer $sid 音频 ID
 * @property string $video_url 原始视频地址
 * @property integer $create_time 创建时间
 * @property integer $modified_time 创建时间
 * @property string $videourl_360 360P 视频地址
 * @property string $videourl_480 480P 视频地址
 * @property string $videourl_720 720P 视频地址
 * @property string $videourl_1080 视频地址
 * @property integer $attr 视频属性，比特位第一位为 1 时表示优先播放
 * @property integer $source 视频来源，1：后台绑定；2：配音秀
 * @property integer $checked 视频审核状态，-1：待转码；0：待审核；1：审核通过
 * @property string $more 视频额外信息，格式为 json 字符串
 * e.g. {"duration":233,"details":{"videourl_360":{"height":360,"width":640,"size":1},...}}
 * duration 字段存放视频时长，单位 ms
 * details 存放不同分辨率视频宽高及大小，其中 size 单位 Bytes
 */
class SoundVideo extends ActiveRecord
{
    // 审核状态
    const CHECKED_SOUND_TRANSCODE = -1;  // 待转码
    const CHECKED_UNPASS = 0;  // 未审核
    const CHECKED_PASS = 1;  // 审核通过

    // 视频来源
    const SOURCE_BACKEND = 1;  // 后台绑定
    const SOURCE_DUB = 2;  // 配音秀

    // 优先于音频播放比特位
    const ATTR_PRIORITY = 1 << 0;

    const PLAY_STATUS_NORMAL = 0;
    // 需要登录后播放比特位
    const PLAY_STATUS_LOGIN_REQUIRED = 1 << 0;

    // 需要登录后才能播放的视频
    const LOGIN_REQUIRED_URL_FIELDS = ['videourl_1080', 'videourl_720'];

    // 视频地址字段，key 代表视频质量
    // 若之后出现新的低于 1080P，高于 360P 质量的视频，按取中的原则加到对应的播放质量中间
    // 若出现更高质量视频，则使用当前最高质量 x2 作为 key，反之亦然
    const VIDEO_URL_FIELDS = [
        128 => 'videourl_1080',
        64 => 'videourl_720',
        32 => 'videourl_480',
        16 => 'videourl_360'
    ];

    // 视频质量短名称（用于客户端全屏播放时显示）
    const SHORT_NAMES = [
        'videourl_1080' => '1080P',
        'videourl_720' => '720P',
        'videourl_480' => '480P',
        'videourl_360' => '360P',
    ];

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'sound_video';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['sid', 'video_url'], 'required'],
            [['sid'], 'integer'],
            [['video_url'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'sid' => '音频 ID',
            'video_url' => '原始视频地址',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'videourl_360' => '360P 流畅',  // 360P 视频地址
            'videourl_480' => '480P 清晰',  // 480P 视频地址
            'videourl_720' => '720P 高清',  // 720P 视频地址
            'videourl_1080' => '1080P 高清',  // 1080P 视频地址
            'attr' => '视频属性',  // 比特位第一位为 1 时表示优先播放
            'source' => '视频来源',  // 1：后台绑定；2：配音秀
            'checked' => '审核状态', // -1：待转码；0：待审核；1：审核通过
            'more' => '视频额外信息',
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        $this->more = $this->more ? Json::decode($this->more) : null;
    }

    /**
     * 获取可播视频信息
     *
     * @param MSound $sound 音频数据
     * @param int $user_id 用户 ID
     * @return null|array
     */
    public static function getVideoInfo(MSound $sound, int $user_id): ?array
    {
        $video = self::find()
            ->select('id, sid, videourl_360, videourl_480, videourl_720, videourl_1080, attr, more')
            ->where(['sid' => $sound->id, 'checked' => self::CHECKED_PASS])
            ->limit(1)
            ->one();
        if (!$video) {
            return null;
        }
        $resources = [];
        $attribute_labels = $video->attributeLabels();
        foreach (self::VIDEO_URL_FIELDS as $quality => $url_field) {
            $url = $video->$url_field;
            if (!$url) {
                continue;
            }
            $status = self::getPlayStatus($url_field);
            $video_detail = self::getVideoDetail($url_field, $video->more);
            [$width, $height, $size] = $video_detail
                ? [$video_detail['width'], $video_detail['height'], $video_detail['size']]
                : [0, 0, 0];
            if ($sound->limit_type || (!$user_id && ($status & self::PLAY_STATUS_LOGIN_REQUIRED))) {
                // 若视频所属音频是限制播放或者用户未登录且该字段的视频需要登录才可播放，则播放地址等信息置零值
                $url = '';
                $size = $width = $height = 0;
            }
            $resources[] = [
                'quality' => $quality,
                'name' => $attribute_labels[$url_field] ?? '其他',
                'short_name' => self::SHORT_NAMES[$url_field] ?? '其他',
                'url' => $url,
                'size' => $size,
                'width' => $width,
                'height' => $height,
                'status' => $status,
            ];
        }
        // 获取视频播放地址
        self::getResourcePlayUrls($resources);
        return [
            'priority' => (int)$video->isPriorityPlay(),
            'duration' => (int)($video->more['duration'] ?? 0),
            'resources' => $resources,
        ];
    }

    /**
     * 是否优先（于音频）播放
     *
     * @return bool
     */
    public function isPriorityPlay(): bool
    {
        return (bool)($this->attr & self::ATTR_PRIORITY);
    }

    /**
     * 获取视频播放状态
     *
     * @param string $video_field 视频字段
     * @return int
     */
    public static function getPlayStatus(string $video_field): int
    {
        $status = self::PLAY_STATUS_NORMAL;
        if (in_array($video_field, self::LOGIN_REQUIRED_URL_FIELDS)) {
            $status |= self::PLAY_STATUS_LOGIN_REQUIRED;
        }
        return $status;
    }

    /**
     * 是否为有原生视频播放页的版本
     *
     * @return bool
     */
    public static function isUsableVersion(): bool
    {
        return !Equipment::isAppOlderThan('4.7.7', '5.6.5');
    }

    /**
     * 获取视频文件详情信息
     * 若未存放该字段视频大小信息，则返回 0
     *
     * @param string $field 存放视频地址的字段名
     * @param array|null
     * @return array 视频宽（像素）、高（像素）、体积（Bytes）
     * @throws Exception 参数错误时抛出异常
     */
    public static function getVideoDetail(string $field, ?array $more): ?array
    {
        if (!in_array($field, self::VIDEO_URL_FIELDS)) {
            throw new Exception('getVideoDetail 参数错误：' . $field);
        }
        return isset($more['details'][$field]) ? $more['details'][$field] : null;
    }

    /**
     * 获取给定音频 ID 中已绑定视频的音频 ID 列表
     *
     * @param array $sound_ids 要检查的音频 ID 数组
     * @return array
     */
    public static function getVideoSoundIds(array $sound_ids): array
    {
        if (!empty($sound_ids)) {
            return self::find()
                ->select('sid')
                ->where(['sid' => $sound_ids, 'checked' => self::CHECKED_PASS])
                ->column();
        }
        return [];
    }

    /**
     * 获取音频绑定的视频最高质量视频播放信息
     *
     * @param int $sound_id
     * @return array|null 视频信息，若未绑定视频返回 null
     */
    public static function getHDVideo(int $sound_id): ?array
    {
        $video = SoundVideo::find()
            ->select('id, videourl_360, videourl_480, videourl_720, videourl_1080, more')
            ->where(['sid' => $sound_id, 'checked' => SoundVideo::CHECKED_PASS])
            ->limit(1)
            ->one();
        if (!$video) {
            return null;
        }
        $hd_video_field = '';
        // 优先返回 1080P 分辨率视频，若无则取其下最高分辨率的视频
        if ($video->videourl_1080) {
            $hd_video_field = 'videourl_1080';
        } elseif ($video->videourl_720) {
            $hd_video_field = 'videourl_720';
        } elseif ($video->videourl_480) {
            $hd_video_field = 'videourl_480';
        } else {
            $hd_video_field = 'videourl_360';
        }
        $video_detail = self::getVideoDetail($hd_video_field, $video->more);
        $return = [
            'videourl' => $video->$hd_video_field,
            'duration' => $video->more['duration'],
            'width' => $video_detail['width'],
            'height' => $video_detail['height'],
        ];
        self::getPlayUrls($return, ['videourl']);
        return $return;
    }

    /**
     * 根据音频 ID 数组获取免费视频信息
     *
     * @param array $sound_ids 音频 ID 数组
     * @return array
     * @throws Exception
     */
    public static function getVideoInfoBySoundIds(array $sound_ids)
    {
        $video_list = self::find()
            ->alias('a')
            ->select('a.id, a.sid, a.videourl_360, a.videourl_480, a.videourl_720, a.videourl_1080, a.attr, a.more')
            ->leftJoin(MSound::tableName() . ' AS b', 'a.sid = b.id')
            ->where(['a.sid' => $sound_ids,
                'a.checked' => SoundVideo::CHECKED_PASS,
                'b.checked' => MSound::CHECKED_PASS,
                'b.pay_type' => MSound::SOUND_FREE])
            ->all();
        if (empty($video_list)) {
            return [];
        }
        $videos = [];
        $attribute_labels = (new self())->attributeLabels();
        foreach ($video_list as $video) {
            $resources = [];
            foreach (self::VIDEO_URL_FIELDS as $quality => $url_field) {
                // 缓存中保存原协议地址，下发的完整资源地址需要因人而异
                $url = $video->getOldAttribute($url_field);
                if (!$url) {
                    continue;
                }
                $video_detail = self::getVideoDetail($url_field, $video->more);
                [$width, $height, $size] = $video_detail
                    ? [$video_detail['width'], $video_detail['height'], $video_detail['size']]
                    : [0, 0, 0];
                $status = self::getPlayStatus($url_field);
                $resources[] = [
                    'quality' => $quality,
                    'name' => $attribute_labels[$url_field] ?? '其他',
                    'short_name' => self::SHORT_NAMES[$url_field] ?? '其他',
                    'url' => $url,
                    'size' => $size,
                    'width' => $width,
                    'height' => $height,
                    'status' => $status,
                ];
            }
            $videos[$video->sid] = [
                'priority' => (int)$video->isPriorityPlay(),
                'duration' => (int)$video->more['duration'] ?? 0,
                'resources' => $resources,
            ];
        }
        return $videos;
    }

    /**
     * 获取并设置视频播放地址
     *
     * @param array|self $video
     * @param array|null $fields 需要设置的属性或数组 key，当 $fields 传入 null 时，会把 $video 视作数组获取其 key 作为 field
     */
    public static function getPlayUrls(&$video, ?array $fields = null)
    {
        if (is_null($fields)) {
            $fields = array_keys($video);
        }
        try {
            $sign_urls = [];
            foreach ($fields as $field) {
                if ($video[$field]) {
                    if (strpos($video[$field], Yii::$app->upos->protocolUrl) === false) {
                        // WORKAROUND: 非 UPOS 协议地址不进行签名，待历史视频都重新转码为 UPOS 资源后此处兼容可删除
                        $video[$field] = StorageClient::getFileUrl($video[$field]);
                    } else {
                        $sign_urls[$field] = $video[$field];
                    }
                }
            }
            if ($sign_urls) {
                $sign_play_urls = MSound::getSignUrls(array_values($sign_urls));
                if ($sign_play_urls) {
                    // $sign_play_urls 中的地址顺序与传入的 $sign_urls 顺序相同
                    $i = 0;
                    foreach ($sign_urls as $field => $item) {
                        // TODO: 之后需要返回多个地址便于客户端快速重试
                        $video[$field] = $sign_play_urls[$i][0];
                        $i++;
                    }
                }
            }
        } catch (Exception $e) {
            Yii::error("获取视频播放地址错误：{$e->getMessage()}", __METHOD__);
            // PASS: 忽略该错误，给播放地址赋值空字符串，避免影响显示（会造成视频无法播放）
            // TODO: 之后需要降级为可用地址
            foreach ($fields as $field) {
                $video[$field] = '';
            }
        }
    }

    /**
     * 获取并设置不同质量视频的播放地址
     *
     * @param array $resources 不同质量视频资源
     */
    public static function getResourcePlayUrls(array &$resources)
    {
        $video_urls = array_column($resources, 'url', 'quality');
        SoundVideo::getPlayUrls($video_urls);
        foreach ($resources as &$item) {
            $item['url'] = $video_urls[$item['quality']];
        }
        unset($item);
    }
}
