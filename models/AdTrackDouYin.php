<?php

namespace app\models;

use Exception;
use Yii;

class AdTrackDouYin extends AdTrack implements AdTrackInterface
{
    const CALLBACK_EVENT_TYPE_MAP = [
        self::CALLBACK_EVENT_ACTIVATE => self::CALLBACK_EVENT_TYPE_ACTIVATE,
        self::CALLBACK_EVENT_ONE_DAY_RETENTION => self::CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION,
        self::CALLBACK_EVENT_PAY => self::CALLBACK_EVENT_TYPE_PAY,
        self::CALLBACK_EVENT_REGISTER => self::CALLBACK_EVENT_TYPE_REGISTER,
        // TODO: 收听 5 个音的关键行为已调整为消费 / 充值关键行为，之后可删除此类型
        self::CALLBACK_EVENT_KEY_ACTION => self::CALLBACK_EVENT_TYPE_KEY_ACTION,
        self::CALLBACK_EVENT_TRANSACTION => self::CALLBACK_EVENT_TYPE_KEY_ACTION,  // 消费 / 充值关键行为
        self::CALLBACK_EVENT_APP_CALLUP => self::CALLBACK_EVENT_TYPE_KEY_APP_CALLUP,  // 唤起 APP (促活行为)
    ];

    // 激活
    const CALLBACK_EVENT_TYPE_ACTIVATE = 0;
    // 付费
    const CALLBACK_EVENT_TYPE_PAY = 2;
    // 次日留存
    const CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION = 6;
    // 注册
    const CALLBACK_EVENT_TYPE_REGISTER = 1;
    // 关键行为
    const CALLBACK_EVENT_TYPE_KEY_ACTION = 25;
    // App 内访问（用户成功打开访问应用）
    const CALLBACK_EVENT_TYPE_KEY_APP_CALLUP = 21;

    // 广告点击来源
    const FROM_JULIANG_VERSION1 = 0;
    const FROM_JULIANG_VERSION2 = 1;

    /**
     * 转化事件回调
     *
     * @link https://ad.oceanengine.com/openapi/doc/index.html?id=1301
     * @param string $event_type
     * @param mixed $arg
     * @throws \Exception
     */
    public function callback(string $event_type, $arg = 0)
    {
        try {
            if (!array_key_exists($event_type, self::CALLBACK_EVENT_TYPE_MAP)) {
                throw new Exception('抖音广告点击回传事件错误：' . $event_type);
            }
            $event = self::CALLBACK_EVENT_TYPE_MAP[$event_type];
            if (is_null($event)) {
                return true;
            }
            $data = Yii::$app->tools->requestRemote($this->getCallbackUrl($event, $_SERVER['REQUEST_TIME'], $arg));
            if (!($data && $data['code'] === 0)) {
                throw new Exception(sprintf('抖音广告点击回传失败：code[%d], msg[%s]', $data['code'], $data['msg'] ?? ''));
            }
            return true;
        } catch (Exception $e) {
            Yii::error('douyin ad error: ' . $e->getMessage(), __METHOD__);
        }
        return false;
    }

    /**
     * 转化事件回调
     *
     * @link https://ad.oceanengine.com/openapi/doc/index.html?id=1301
     *
     * @param int $callback_event 事件类型
     * @param int $event_time 转化发生时间
     * @param mixed $arg 付费金额（单位：元），或关键行为参数
     */
    private function getCallbackUrl(int $callback_event, int $event_time, $arg = 0)
    {
        // 付费金额回传文档：https://bytedance.feishu.cn/docs/doccngF8a7jg5Q6OWOeIRtpQ4Dd
        if ($callback_event === self::CALLBACK_EVENT_TYPE_PAY) {
            $s = urlencode(sprintf('{"pay_amount":%d}', (int)$this->getCallbackPayAmount($arg)));
            return sprintf('%s&event_type=%d&conv_time=%d&props=%s', $this->track_id, $callback_event, $event_time, $s);
        }
        // track_id 形式：http://ad.toutiao.com/track/activate/?callback=xxxxx&os=0&muid=zzzzzz
        return sprintf('%s&event_type=%d&conv_time=%d', $this->track_id, $callback_event, $event_time);
    }

    private function isFromOldVersion()
    {
        return !($this->more && array_key_exists('from', $this->more) && $this->more['from'] === self::FROM_JULIANG_VERSION2);
    }

    /**
     * @inheritdoc
     */
    public function getAdId(int $ad_level)
    {
        // WORKAROUND: 抖音广告 2022.10 起巨量旧版开始下线，待广告都迁移至巨量新版后，去除此兼容
        if ($this->isFromOldVersion()) {
            switch ($ad_level) {
                case self::AD_LEVEL_ONE:
                    return $this->group_id;
                case self::AD_LEVEL_TWO:
                    return $this->project_id;
                case self::AD_LEVEL_THREE:
                    return $this->creative_id;
            }
        }

        switch ($ad_level) {
            case self::AD_LEVEL_ONE:
                return $this->project_id;
            case self::AD_LEVEL_TWO:
                return $this->group_id;
            case self::AD_LEVEL_THREE:
                return $this->creative_id;
        }

        throw new Exception('参数错误');
    }

    /**
     * @inheritdoc
     */
    public function getAdName(int $ad_level)
    {
        if (!$this->more) {
            return '';
        }

        // WORKAROUND: 抖音广告 2022.10 起巨量旧版开始下线，待广告都迁移至巨量新版后，去除此兼容
        if ($this->isFromOldVersion()) {
            switch ($ad_level) {
                case self::AD_LEVEL_ONE:
                    return array_key_exists('group_name', $this->more) ? $this->more['group_name'] : '';
                case self::AD_LEVEL_TWO:
                    return array_key_exists('project_name', $this->more) ? $this->more['project_name'] : '';
                case self::AD_LEVEL_THREE:
                    return array_key_exists('creative_name', $this->more) ? $this->more['creative_name'] : '';
            }
        }

        switch ($ad_level) {
            case self::AD_LEVEL_ONE:
                return array_key_exists('project_name', $this->more) ? $this->more['project_name'] : '';
            case self::AD_LEVEL_TWO:
                return array_key_exists('group_name', $this->more) ? $this->more['group_name'] : '';
            case self::AD_LEVEL_THREE:
                return array_key_exists('creative_name', $this->more) ? $this->more['creative_name'] : '';
        }

        throw new Exception('参数错误');
    }
}
