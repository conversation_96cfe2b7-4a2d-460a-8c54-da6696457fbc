<?php

namespace app\models;

use app\components\models\BalanceInterface;
use app\components\util\MUtils;
use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "guild_balance".
 *
 * @property integer $id 主键，公会 ID
 * @property integer $in_ios iOS 收益余额
 * @property integer $in_tmallios 天猫 iOS 收益余额
 * @property integer $in_android Android 收益余额
 * @property integer $in_paypal PayPal 收益余额
 * @property integer $in_googlepay Google Pay 收益余额
 * @property integer $live_profit 直播总收益余额（可提现）
 * @property integer $all_live_profit 直播累计收益
 * @property integer $rate 公会与平台分成比例
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 */
class GuildBalance extends ActiveRecord implements BalanceInterface
{
    // 公会与平台默认分成比例
    const RATE = 0.5;

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->paydb;
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'guild_balance';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id'], 'required'],
            [array_merge(['id', 'live_profit', 'all_live_profit'], self::profitFields()), 'integer'],
            [['rate'], 'number'],
        ];
    }

    /**
     * 返回和用户收益相关的字段
     *
     * @return array
     */
    public static function profitFields()
    {
        return ['in_ios', 'in_tmallios', 'in_android', 'in_paypal', 'in_googlepay'];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键，公会 ID',
            'in_ios' => 'iOS 收益余额',
            'in_tmallios' => '天猫 iOS 收益余额',
            'in_android' => 'Android 收益余额',
            'in_paypal' => 'PayPal 收益余额',
            'in_googlepay' => 'Google Pay 收益余额',
            'live_profit' => '直播总收益余额（可提现）',
            'all_live_profit' => '直播累计收益',
            'rate' => '公会与平台分成比例',
            'create_time' => '创建时间2',
            'modified_time' => '修改时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
            // 默认收益都为 0
            foreach (self::profitFields() as $field) {
                $this->{$field} = 0;
            }
            $this->live_profit = 0;
            $this->all_live_profit = 0;
            // 若未设置分成比例，则默认为 0.5
            $this->rate = $this->rate ?? self::RATE;
        }
        $this->modified_time = $time;
        return true;
    }

    public static function getByPk($id)
    {
        if ((!$instance = self::findOne($id))) {
            if ($id && !Guild::find()->where(['id' => $id])->exists()) {
                throw new HttpException(404, '该公会不存在', 200020001);
            }
            $redis = Yii::$app->redis;
            $class = self::class;
            $key = $redis->generateKey(LOCK_GENERATE_CLASS, $class, $id);
            if ($redis->lock($key, 1)) {
                if (!$instance = self::findOne($id)) {
                    $instance = new self(['id' => $id]);
                    if (!$instance->save()) {
                        throw new \Exception(MUtils::getFirstError($instance));
                    }
                    $redis->unlock($key);
                }
            } else {
                sleep(1);
                if (!$instance = self::findOne($id)) {
                    throw new Exception(Yii::t('yii', ':class :id generate failed', [
                        ':class' => self::class,
                        ':id' => $id
                    ]));
                }
                $redis->unlock($key);
            }
        }
        return $instance;
    }

    /**
     * 返回公会总累计收益
     *
     * @return integer
     */
    public function getTotalIncome()
    {
        $count = 0;
        $profit_types = self::profitFields();
        foreach ($profit_types as $field) {
            $count += $this->{$field};
        }
        return $count;
    }

    public function isGuild()
    {
        return true;
    }

}
