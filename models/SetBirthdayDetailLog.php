<?php

namespace app\models;

use Exception;
use Yii;

class SetBirthdayDetailLog
{
    /**
     * 新增设置生日操作日志记录
     *
     * @param integer $user_id 用户 ID
     * @param string $birthday 生日，格式：YYYY-mm-dd
     * @param string $birthdate_mmdd 用于检索的生日月日
     * @return bool 是否成功
     */
    public static function addLog(int $user_id, string $birthday, string $birthdate_mmdd)
    {
        try {
            $data = [
                'user_id' => $user_id,
                'birthday' => $birthday,
                'birthdate_mmdd' => $birthdate_mmdd,
            ];
            Yii::$app->databus->pub($data, 'set_birthday_log:' . $user_id);
            return true;
        } catch (Exception $e) {
            Yii::error('databus 出错：' . $e->getMessage(), __METHOD__);
            // PASS: databus 出错记录错误并忽略异常
            return false;
        }
    }
}
