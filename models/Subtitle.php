<?php

namespace app\models;

use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "subtitle".
 *
 * @property integer $id
 * @property string $context
 * @property integer $stime
 * @property integer $etime
 * @property integer $did
 * @property string $role
 */
class Subtitle extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'subtitle';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['context', 'stime', 'etime', 'did', 'role'], 'required'],
            [['stime', 'etime', 'did'], 'integer'],
            [['context'], 'string', 'max' => 255],
            [['role'], 'string', 'max' => 10],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'context' => 'Context',
            'stime' => '字幕起始时间，单位为毫秒',
            'etime' => '字幕结束时间，单位为毫秒',
            'did' => '配音id',
            'role' => '人物',
        ];
    }

    public static function getDubSubtitle($did)
    {
        $subtitles = self::find()
            ->select('context, stime, etime, role')
            ->where(['did' => $did])
            ->orderBy(['stime' => SORT_ASC])
            ->all();

        return array_map(function ($subtitle) {
            return [
                'context' => $subtitle->context,
                'stime' => $subtitle->stime,
                'etime' => $subtitle->etime,
                'role' => $subtitle->role
            ];
        }, $subtitles);
    }

    public static function arrayToSrt(array $subtitles): string
    {
        $srt = "";
        foreach ($subtitles as $key => $subtitle) {
            $stime = self::_format_time($subtitle['stime'] ?? Null);
            $etime = self::_format_time($subtitle['etime'] ?? Null);
            $key++;
            $srt .= "{$key}\n";
            $srt .= "{$stime} --> {$etime}\n";
            $srt .= "{$subtitle['context']}\n\n";
        }
        return $srt;
    }

    private static function _format_time(string $time): string
    {
        if (Null === $time)
            throw new HttpException(400, '字幕格式不正确');
        $ms = $time % 1000;
        $sec = intdiv($time, 1000);
        return gmstrftime("%H:%M:%S,{$ms}", $sec);
    }
}
