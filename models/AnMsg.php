<?php

namespace app\models;

use app\components\util\Equipment;
use app\components\util\Go;
use app\components\util\MUtils;
use Exception;
use missevan\storage\StorageClient;
use missevan\util\MUtils AS MUtils2;
use Yii;
use yii\db\Expression;
use yii\web\HttpException;

/**
 * This is the model class for table "an_msg".
 *
 * @property integer $id
 * @property integer $small_id
 * @property integer $big_id
 * @property string $post_name
 * @property string $post_icon
 * @property string $post_color
 * @property integer $status
 * @property string $msg
 * @property integer $type
 * @property integer $ctime
 */
class AnMsg extends ActiveRecord
{
    // 新增私信场景
    const SCENARIO_ADD_MSG = 'add_msg';
    // 私信类型 0：获取全部；1：获取未关注人私信
    const TYPE_MESSAGE_ALL = 0;
    const TYPE_MESSAGE_UNFOLLOWED = 1;
    // 消息属性 status 二进制运算：1 位是否已读（0：已读，1：未读）
    // 2 位是否是客服（0：非客服，1：客服）
    // 3 位发信方（0：small_id，1：big_id）
    // 4、5 位关闭会话方（4 位 small_id 关闭房间，5 位 big_id 关闭房间）
    // 6、7 位是否显示这条消息（0：显示；1：不显示）（6 位 small_id 显示，7 位 big_id 显示）
    // 8、9 位是否是拉黑的消息（0：显示；1：拉黑）（8 位 small_id 拉黑 big_id，9 位 big_id 拉黑 small_id）
    const STATUS_UNREAD = 0b1;
    const STATUS_STAFF = 0b10;
    const STATUS_BIG_POSTER = 0b100;
    const STATUS_SMALL_POSTER = 0;
    const STATUS_SMALL_CLOSE_CHAT = 0b1000;
    const STATUS_BIG_CLOSE_CHAT = 0b10000;
    const STATUS_SMALL_HIDE_MSG = 0b100000;
    const STATUS_BIG_HIDE_MSG = 0b1000000;
    const STATUS_SMALL_BLACKLIST_MSG = 0b10000000;
    const STATUS_BIG_BLACKLIST_MSG = 0b100000000;
    // 设置消息状态 1：已读；2：清空
    const TYPE_SET_READ = 1;
    const TYPE_SET_CLEAN = 2;

    // 私信文本类型，0：纯文本，1：HTML
    const MSG_TYPE_TEXT = 0;
    const MSG_TYPE_HTML = 1;

    // 发送私信用户角色，0：普通用户
    const ROLE_NORMAL = 0;

    // sendShamAllow 假发送
    const SEND_STATUS_SHAM_ALLOW = 0;
    // sendAllow 允许发送
    const SEND_STATUS_ALLOW = 1;

    public $post_id;
    public $not_read;
    public $receive_id;
    public $receive_name;
    public $receive_icon;
    public $authenticated = 0;

    /**
     * @inheritdoc
     */
    public static function getDb()
    {
        return Yii::$app->messagedb;
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'an_msg';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['small_id', 'big_id', 'status', 'msg', 'ctime'], 'required'],
            [['small_id', 'big_id', 'status', 'ctime'], 'integer'],
            [['post_name'], 'string', 'max' => 20],
            [['post_icon'], 'string', 'max' => 150],
            [['post_color'], 'string', 'max' => 50],
            [['msg'], 'string', 'max' => 255],
            ['msg', 'checkMsg', 'on' => self::SCENARIO_ADD_MSG],
        ];
    }

    public static function msgLimit()
    {
        //私信限制
        $redis = Yii::$app->redis;
        $ip = Yii::$app->request->userIP;
        $time = intdiv(time(), 60);
        $ipLock = $lock = $redis->generateKey(MSG_IP, $ip, $time);
        $userLock = $lock = $redis->generateKey(MSG_USER, Yii::$app->user->id, $time);
        if ($redis->incr($userLock) > 10)
            throw new HttpException(403, 'M娘来不及传递私信啦~回血中>-<');
        if ($redis->incr($ipLock) > 60)
            throw new HttpException(403, 'M娘来不及传递私信啦~回血中>-<');
        $expire = (int)($time * 60 + 61);
        $redis->expireAt($userLock, $expire);
        $redis->expireAt($ipLock, $expire);
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'small_id' => '会话双方中小的 user_id ',
            'big_id' => '会话双方中大的 user_id',
            'post_name' => '发信方用户名',
            'post_icon' => '发信方头像',
            'post_color' => '消息气泡颜色', // 同 mowangskuser 表中的 字段 boardiconcolor
            // status 二进制运算
            // 1 位是否已读（0：已读，1：未读）
            // 2 位是否是客服（0：非客服，1：客服）
            // 3 位发信方（0：small_id，1：big_id）
            // 4、5 位关闭会话方（4 位 small_id 关闭房间，5 位 big_id 关闭房间）
            // 6、7 位是否显示这条消息（0：显示；1：不显示）（6 位 small_id 显示，7 位 big_id 显示）
            // 8、9 位是否是拉黑不显示的消息（0：显示；1：拉黑不显示）
            // （8 位 small_id 拉黑 big_id，9 位 big_id 拉黑 small_id）
            'status' => '消息属性',
            'msg' => '私信内容',
            'type' => '私信内容的类型',  // 0：纯文本，1：HTML
            'ctime' => '发送时间',
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->msg && $this->type === self::MSG_TYPE_HTML) {
            // WORKAROUND: 私信含 HTML 需要对老版本显示的私信内容进行处理
            [$this->msg, $this->type] = self::formatMessageLink($this->msg);
        }
    }

    private function smallIgnore()
    {
        $this->status |= self::STATUS_SMALL_HIDE_MSG;
    }

    private function bigIgnore()
    {
        $this->status |= self::STATUS_BIG_HIDE_MSG;
    }

    private function isPostedByBig()
    {
        return $this->status & self::STATUS_BIG_POSTER;
    }

    /**
     * 验证私信是否违规
     *
     * @param string $attribute 验证的属性
     * @param mixed[] $params 其他自定义参数
     */
    public function checkMsg($attribute)
    {
        // 检测私信违规情况，但不包括因广告违规的情况
        $result = Yii::$app->go->checkText($this->$attribute, Go::SCENE_PRIVATE_MSG);
        if ($result && !current($result)['pass']) {
            $this->addError($attribute, $this->getAttributeLabel($attribute) . '中含有违规词汇喔~');
        }
    }

    public static function getMessageDetail(int $user_id, int $from_user_id, int $page_size = DEFAULT_PAGE_SIZE)
    {
        //检验收信人
        if (!Mowangskuser::find()->where('id = :id', [':id' => $user_id])->exists())
            throw new HttpException(404, '用户不存在');

        // 查询私信内容
        $is_bigger = $from_user_id > $user_id;
        $condition = $is_bigger ? 'small_id = :small_id AND big_id = :big_id' :
            'small_id = :small_id AND big_id = :big_id';
        $params = $is_bigger ? [':small_id' => $user_id, ':big_id' => $from_user_id] :
            [':small_id' => $from_user_id, ':big_id' => $user_id];
        if (!Equipment::isAppOlderThan('4.4.0', '5.3.1')) {
            // 安卓 5.3.1（含）之后的版本，iOS 4.4.0（含）之后的版本过滤用户清空的消息内容
            $condition .= $is_bigger ? ' AND status & :big_hide_status = 0' : ' AND status & :small_hide_status = 0';
            if ($is_bigger) {
                $params[':big_hide_status'] = self::STATUS_BIG_HIDE_MSG;
            } else {
                $params[':small_hide_status'] = self::STATUS_SMALL_HIDE_MSG;
            }
        }

        $query = AnMsgRO::find()->where($condition, $params)->orderBy(['id' => SORT_DESC]);
        $return_model = MUtils::getPaginationModels(clone $query, $page_size);

        $users = Mowangskuser::find()->select('id, username, iconurl, boardiconurl, icontype, avatar, confirm')
            ->where(['id' => [$from_user_id, $user_id]])->all();
        $users = array_column($users, NULL, 'id');
        foreach ($return_model->Datas as &$msg) {
            $msg->post_id = ($msg->status & 4) ? $msg->big_id : $msg->small_id;
            $msg->authenticated = $users[$msg->post_id]->authenticated ?? 0;
            $msg->post_name = $users[$msg->post_id]->username;
            $msg->post_icon = $users[$msg->post_id]->iconurl;
            unset($msg->big_id, $msg->small_id);
        }
        // 接收者用户信息
        $receive_user = [
            'user_id' => $user_id,
            'username' => $users[$user_id]->username,
            'authenticated' => $users[$user_id]->authenticated,
            'is_official' => Mowangskuser::isOfficial($users[$user_id]->confirm),
        ];
        unset($msg, $users);

        $unread_msg_ids = $query->andWhere('status & :big_send = :status', [
            ':big_send' => self::STATUS_BIG_POSTER,
            ':status' => $is_bigger ? 0 : self::STATUS_BIG_POSTER,
        ])->andWhere('status & :unread', [':unread' => self::STATUS_UNREAD])->column();
        if (!empty($unread_msg_ids)) {
            $unread_msg_ids = array_map('intval', $unread_msg_ids);
            // 更新状态
            AnMsg::updateByPk($unread_msg_ids,
                ['status' => new Expression('status &~ :unread', [':unread' => self::STATUS_UNREAD])]);
        }
        $info = BlackUser::relation($from_user_id, $user_id);
        $relation = [
            // 接口只需要返回自己是否拉黑对方
            'blacklist' => in_array($info['blacklist'], [
                BlackUser::BLACKLIST_NONE,
                BlackUser::BLACKLIST_PASSIVE,
            ]) ? BlackUser::BLACKLIST_NONE : BlackUser::BLACKLIST_ACTIVE,
            'followed' => $info['followed'],
        ];
        return [
            'receive_user' => $receive_user,
            'Datas' => $return_model->Datas,
            'pagination' => $return_model->pagination,
            'relation' => $relation,
        ];
    }

    /**
     * 针对版本处理私信中的链接
     * 该方法仅在私信类型为 HTML 时调用
     *
     * @param string $message 私信内容
     * @return array 处理后的私信内容和私信类型
     * @todo 之后客户端版本更迭为都支持 HTML 私信版本后，此方法可删除
     */
    public static function formatMessageLink(string $message): array
    {
        if ($message === '' || !Equipment::isAppOlderThan('4.5.7', '5.4.7')) {
            return [$message, self::MSG_TYPE_HTML];
        }
        // WORKAROUND: 私信为老版本，则需要对老版本私信内容进行处理
        $reg = '/<a .*?href=["|\'](.*?)["|\'].*?>.*?<\/a>/';
        preg_match_all($reg, $message, $matches);
        if (empty($matches[0])) {
            return [$message, self::MSG_TYPE_HTML];
        }
        // $matches[0] 为 a 标签集合，$matches[1] 为链接集合
        foreach ($matches[0] as $key => $matche) {
            $a_tag_link = $matches[1][$key];
            if (MUtils::isAppSchemeUrl($a_tag_link)) {
                // 若为协议链接，则链接处改为提示用户升级
                $message = str_replace($matche, '「请将 App 升级至最新版查看该条消息链接」', $message);
            } else {
                // 若非协议地址，提取 url 并替换 a 标签内容
                // e.g. str_replace('<a href="url">xxx</a>', 'url', '链接：<a href="url">xxx</a>')
                $a_tag_link = MUtils::filterHtml($a_tag_link);
                $message = str_replace($matche, $a_tag_link, $message);
            }
        }
        // 需要反转义 HTML 特殊字符让其可以正常显示
        // 私信类型降级为出纯文本
        return [htmlspecialchars_decode($message), self::MSG_TYPE_TEXT];
    }

    /**
     * 获取私信列表
     *
     * @param int $user_id 用户 ID
     * @param int $type 获取私信类型 0：全部；1：未关注人私信
     * @param int $page 当前页
     * @param int $page_size 每页个数
     * @return ReturnModel
     * @throws
     */
    public static function getMessageList(int $user_id, int $type = self::TYPE_MESSAGE_UNFOLLOWED, $page = 1,
            $page_size = DEFAULT_PAGE_SIZE)
    {
        // 查询私信列表的 SQL 举例：
        // SELECT t.msg, t.status, t.small_id, t.big_id, t1.not_read, t.type, t.ctime,
        //   IF(t1.not_read = 0, 0, 1) AS read_order
        // FROM an_msg AS t
        // RIGHT JOIN (
        //   SELECT MAX(id) AS id,
        //     SUM(IF((status & 69 = 1 AND big_id = 2881692) OR (status & 37 = 5 AND small_id = 2881692), 1, 0))
        //     AS not_read
        //   FROM an_msg
        //   WHERE (small_id = 2881692 OR big_id = 2881692)
        // -- 只查询对我可见的最后一条
        //     AND IF(small_id = 2881692, (status & 32) = 0, (status & 64) = 0)
        //   GROUP BY small_id, big_id
        //   ORDER BY id DESC
        // ) AS t1 ON t1.id = t.id
        // WHERE (small_id = 2881692 AND status & 168 = 0)
        //  OR (big_id = 2881692 AND status & 336 = 0)
        // ORDER BY read_order DESC, t1.id DESC
        // LIMIT 30
        // OFFSET 0
        // 统计未读私信数量时，需要过滤掉不显示的私信状态
        $expression = new Expression('SUM(IF(
            (status & :big_hide_small_post_unread = :small_post_unread AND big_id = :user_id)
            OR
            (status & :small_hide_big_post_unread = :big_post_unread AND small_id = :user_id),
            1, 0)) AS not_read');
        $sub_query = AnMsgRO::find()->select('MAX(id) AS id')
            ->addSelect($expression)
            ->where('small_id = :user_id OR big_id = :user_id')
            // 只查询对【我】可见的最后一条
            ->andWhere('IF(small_id = :user_id, (status & :status_small_id_hide) = 0, (status & :status_big_id_hide) = 0)')
            ->groupBy('small_id, big_id');
        $order_expression = new Expression('IF(t1.not_read = 0, 0, 1) AS read_order');
        // 过滤不可见的私信（用户自己清空、关闭窗口和黑名单用户的私信）
        $query = AnMsgRO::find()
            ->alias('t')
            ->select('t.msg, t.status, t.small_id, t.big_id, t1.not_read, t.type, t.ctime')
            ->addSelect($order_expression)
            ->rightJoin(['t1' => $sub_query], 't1.id = t.id')
            ->where('small_id = :user_id AND status & :small_hide_close_blacklist_status = 0')
            ->orWhere('big_id = :user_id AND status & :big_hide_close_blacklist_status = 0')
            ->addParams([
                ':user_id' => $user_id,
                ':big_hide_small_post_unread' => self::STATUS_BIG_HIDE_MSG | self::STATUS_BIG_POSTER |
                    self::STATUS_UNREAD,
                ':small_post_unread' => self::STATUS_UNREAD,
                ':small_hide_big_post_unread' => self::STATUS_SMALL_HIDE_MSG | self::STATUS_BIG_POSTER |
                    self::STATUS_UNREAD,
                ':big_post_unread' => self::STATUS_UNREAD | self::STATUS_BIG_POSTER,
                ':small_hide_close_blacklist_status' => self::STATUS_SMALL_HIDE_MSG | self::STATUS_SMALL_CLOSE_CHAT |
                    self::STATUS_SMALL_BLACKLIST_MSG,
                ':big_hide_close_blacklist_status' => self::STATUS_BIG_HIDE_MSG | self::STATUS_BIG_CLOSE_CHAT |
                    self::STATUS_BIG_BLACKLIST_MSG,
                ':status_small_id_hide' => self::STATUS_SMALL_HIDE_MSG,
                ':status_big_id_hide' => self::STATUS_BIG_HIDE_MSG,
            ])->orderBy('read_order DESC, t1.id DESC');
        $show_status = UserAddendum::getByPk($user_id)->message_config[UserAddendum::MSG_CFG_TYPE_FOLD];
        if (UserAddendum::MESSAGE_EXPAND === $show_status) {
            // 展开未关注人私信
            $return_model = MUtils::getPaginationModels($query, $page_size);
        } else {
            // 收起未关注人私信或只获取未关注人私信
            $all_messages = $query->all();
            $total_count = count($all_messages);
            if (0 === $total_count) {
                return ReturnModel::empty($page, $page_size);
            }
            $show_messages = self::getShowMsg($all_messages, $user_id, $type);
            // 分页
            $offset = ($page - 1) * $page_size;
            $data = array_slice($show_messages, $offset, $page_size);
            $return_model = ReturnModel::getPaginationData($data, $total_count, $page, $page_size);
        }
        if ($return_model->Datas) {
            $chat_user_ids = [];
            array_map(function ($item) use ($user_id, &$chat_user_ids) {
                // 过滤收起的未关注人消息
                if (isset($item['big_id'])) {
                    if ($item['big_id'] === $user_id) {
                        $chat_user_ids[] = $item['small_id'];
                    } else {
                        $chat_user_ids[] = $item['big_id'];
                    }
                }
            }, $return_model->Datas);
            if (!empty($chat_user_ids)) {
                $users = Mowangskuser::find()
                    ->select('id, username, confirm, avatar, boardiconurl, icontype, coverurl')
                    ->where(['id' => $chat_user_ids])->all();
                $users = array_column($users, NULL, 'id');
                $vip_user_ids = MUserVip::getVipUserIds(array_column($users, 'id'));
                $return_model->Datas = array_map(function ($item) use ($users, $user_id, $vip_user_ids) {
                    // 过滤收起的未关注人消息
                    if (isset($item['big_id'])) {
                        $receive_id = $item['big_id'] === $user_id ? $item['small_id'] : $item['big_id'];
                        if (!array_key_exists($receive_id, $users)) {
                            // 已注销用户信息
                            $return = [
                                'receive_name' => '已注销',
                                'receive_icon' => Yii::$app->params['defaultAvatarUrl'],
                                'authenticated' => 0,
                                // 是否为官方账号
                                'is_official' => false,
                                'is_vip' => 0,
                            ];
                        } else {
                            $return = [
                                'receive_name' => $users[$receive_id]->username,
                                'receive_icon' => $users[$receive_id]->iconurl,
                                'authenticated' => $users[$receive_id]->authenticated,
                                // 是否为官方账号
                                'is_official' => Mowangskuser::isOfficial($users[$receive_id]->confirm),
                                'is_vip' => (int)in_array($receive_id, $vip_user_ids),
                            ];
                        }
                        $return_default = [
                            'receive_id' => $receive_id,
                            'msg' => $item['msg'],
                            'type' => $item['type'],
                            'ctime' => $item['ctime'],
                            'not_read' => (int)$item['not_read'],
                        ];
                        $return = array_merge($return_default, $return);
                    } else {
                        $return = $item;
                        $return['is_official'] = false;
                    }
                    return $return;
                }, $return_model->Datas);
            }
        }
        return $return_model;
    }

    /**
     * 获取显示的私信列表
     *
     * @param array $all_messages 所有私信详情
     * @param int $user_id 用户 ID
     * @param int $type 私信类型 0：全部；1：未关注用户私信
     * @return array
     * @throws \yii\base\Exception
     */
    public static function getShowMsg(array $all_messages, int $user_id, int $type)
    {
        if (!$all_messages || $user_id <= 0) {
            return [];
        }
        $attention_user_ids = MAttentionUser::find()->select('user_passtive')
            ->where('user_active = :user_active', [':user_active' => $user_id])
            ->column();
        $latest_time = [];
        $not_read = 0;
        $result = [];
        if (self::TYPE_MESSAGE_UNFOLLOWED === $type) {
            // 只获取未关注用户私信
            $result = array_filter($all_messages, function ($item) use ($attention_user_ids) {
                if (!($item->status & self::STATUS_STAFF) && (empty($attention_user_ids) ||
                    ($attention_user_ids && !in_array($item->small_id, $attention_user_ids) &&
                        !in_array($item->big_id, $attention_user_ids)))) {
                    // 获取未关注用户私信（不包括客服）
                    return $item;
                }
            });
        } else {
            array_map(function ($item) use ($attention_user_ids, $type, &$not_read, &$latest_time, &$result) {
                if (!($item->status & self::STATUS_STAFF) && (empty($attention_user_ids) ||
                    ($attention_user_ids && !in_array($item->small_id, $attention_user_ids) &&
                        !in_array($item->big_id, $attention_user_ids)))) {
                    // 获取未关注用户私信的未读数和最新时间（不包括客服）
                    $not_read += $item->not_read;
                    $latest_time[] = $item->ctime;
                } else {
                    $result[] = $item;
                }
            }, $all_messages);
            if ($latest_time) {
                $result[] = [
                    'receive_id' => 0,
                    'receive_name' => '未关注人消息',
                    'receive_icon' => StorageClient::getFileUrl(Yii::$app->params['foldMsgIconUrl']),
                    'authenticated' => 0,
                    'msg' => $not_read ? '[ ' . $not_read . ' 条 ] 有新的未关注人消息' : '暂无新的未关注人消息',
                    'ctime' => max($latest_time),
                    'not_read' => $not_read,
                ];
                // 排序
                usort($result, function ($a, $b) {
                    if ((int)$a['not_read'] === (int)$b['not_read']) {
                        return $b['ctime'] > $a['ctime'];
                    }
                    return $b['not_read'] > $a['not_read'];
                });
            }
        }
        return $result;
    }

    /**
     * 获取关注人的未读消息数
     *
     * @param int $user_id 用户 ID
     * @return int
     * @throws HttpException
     * @throws Yii\db\Exception
     */
    public static function getUnreadFollowedNum(int $user_id)
    {
        if ($user_id <= 0) {
            return 0;
        }
        // 获取收信方可见的未读消息
        $big_condition = 'status & :unread_mask = :small_post_unread AND big_id = :user_id ' .
            'AND status & :big_hide_status = 0';
        $small_condition = 'status & :unread_mask = :big_post_unread AND small_id = :user_id ' .
            'AND status & :small_hide_status = 0';
        $unread_post_ids = AnMsgRO::find()
            ->select(new Expression('IF(small_id = :user_id, big_id, small_id) AS post_id'))
            ->where($big_condition)
            ->orWhere($small_condition)
            ->addParams([
                ':user_id' => $user_id,
                ':unread_mask' => self::STATUS_UNREAD | self::STATUS_BIG_POSTER,
                ':small_post_unread' => self::STATUS_UNREAD,
                ':big_post_unread' => self::STATUS_UNREAD | self::STATUS_BIG_POSTER,
                ':big_hide_status' => self::STATUS_BIG_HIDE_MSG,
                ':small_hide_status' => self::STATUS_SMALL_HIDE_MSG
            ])->column();
        $msg_num = count($unread_post_ids);
        if (!$msg_num) {
            return $msg_num;
        }
        $user_addendum = UserAddendum::getByPk($user_id);
        $is_fold = $user_addendum ? $user_addendum->message_config['fold'] : UserAddendum::MESSAGE_EXPAND;
        if ($is_fold) {
            // 未关注人消息收起状态不显示未关注人的未读消息数
            $follow_user_ids = MAttentionUser::getFollowUserIds($user_id);
            $stranger_ids = array_diff($unread_post_ids, $follow_user_ids);
            return max($msg_num - count($stranger_ids), 0);
        }
        return $msg_num;
    }

    /**
     * 获取私信信息
     *
     * @param int $user_id 用户 ID
     * @param int $type 类型 1：未读私信；2：可见的私信
     * @return array
     * @throws Exception
     */
    public static function getMsgInfo(int $user_id, int $type): array
    {
        $query = AnMsgRO::find()
            ->select(new Expression('IF(small_id = :user_id, big_id, small_id) AS post_id'));
        switch ($type) {
            case self::TYPE_SET_READ:
                // 获取所有未读的私信 ID 与发送者 ID 组成的 map
                $big_condition = 'status & :unread_mask = :small_post_unread AND big_id = :user_id';
                $small_condition = 'status & :unread_mask = :big_post_unread AND small_id = :user_id';
                $params = [
                    ':unread_mask' => self::STATUS_UNREAD | self::STATUS_BIG_POSTER,
                    ':small_post_unread' => self::STATUS_UNREAD,
                    ':big_post_unread' => self::STATUS_UNREAD | self::STATUS_BIG_POSTER,
                ];
                break;
            case self::TYPE_SET_CLEAN:
                // 获取所有可见的私信 ID 与发送者 ID 组成的 map
                $big_condition = 'status & :big_hide_status = 0 AND big_id = :user_id';
                $small_condition = 'status & :small_hide_status = 0 AND small_id = :user_id';
                $params = [
                    ':big_hide_status' => self::STATUS_BIG_HIDE_MSG,
                    ':small_hide_status' => self::STATUS_SMALL_HIDE_MSG,
                ];
                break;
        }
        $staff_condition = 'status & :staff_status = 0';
        $params[':user_id'] = $user_id;
        $params[':staff_status'] = self::STATUS_STAFF;
        return $query->where(['OR', $big_condition, $small_condition])
            ->andWhere($staff_condition)
            ->addParams($params)->indexBy('id')->column();
    }

    /**
     * 已读未关注用户发送的私信
     *
     * @param int $user_id 当前用户 ID
     * @return int
     * @throws Exception
     */
    public static function readUnfollowedMsg(int $user_id): int
    {
        $msg_map = self::getMsgInfo($user_id, self::TYPE_SET_READ);
        $follow_user_ids = [];
        if ($msg_map) {
            // 获取关注的用户 IDs
            $follow_user_ids = MAttentionUser::getFollowUserIds($user_id);
        }
        // 获取所有未读的非关注用户发送的私信 ID
        $unread_msg_ids = [];
        foreach ($msg_map as $id => $post_id) {
            if (!in_array($post_id, $follow_user_ids)) {
                $unread_msg_ids[] = (int)$id;
            }
        }
        if (empty($unread_msg_ids)) {
            return 0;
        }
        $read_expression = new Expression('status &~ :read_status', [
            ':read_status' => self::STATUS_UNREAD
        ]);
        $id_condition = MUtils2::generateSqlIntegerIn('id', $unread_msg_ids);
        return AnMsg::updateAll(['status' => $read_expression],
            "{$id_condition} AND status & :read_status = :read_status", [
                ':read_status' => self::STATUS_UNREAD
            ]);
    }

    /**
     * 清空未关注用户发送的私信
     *
     * @param int $user_id 当前用户 ID
     * @return int
     * @throws Exception
     */
    public static function cleanUnfollowedMsg(int $user_id): int
    {
        $msg_map = self::getMsgInfo($user_id, self::TYPE_SET_CLEAN);
        $follow_user_ids = [];
        if ($msg_map) {
            // 获取关注的用户 IDs
            $follow_user_ids = MAttentionUser::getFollowUserIds($user_id);
        }
        // 获取所有可见的非关注用户发送的私信 ID
        $big_hide_ids = $small_hide_ids = [];
        foreach ($msg_map as $id => $post_id) {
            if (!in_array($post_id, $follow_user_ids)) {
                if ($post_id < $user_id) {
                    // 当前用户是 big_id
                    $big_hide_ids[] = (int)$id;
                } else {
                    // 当前用户是 small_id
                    $small_hide_ids[] = (int)$id;
                }
            }
        }
        if (empty($big_hide_ids) && empty($small_hide_ids)) {
            return 0;
        }
        $affect1_rows = $affect2_rows = 0;
        $transaction = self::getDb()->beginTransaction();
        try {
            if (!empty($big_hide_ids)) {
                $id_condition = MUtils2::generateSqlIntegerIn('id', $big_hide_ids);
                $big_hide_expression = new Expression('status | :big_hide_status', [
                    ':big_hide_status' => self::STATUS_BIG_HIDE_MSG
                ]);
                $affect1_rows = self::updateAll(['status' => $big_hide_expression],
                    "{$id_condition} AND status & :big_hide_status = 0", [
                        ':big_hide_status' => self::STATUS_BIG_HIDE_MSG
                    ]);
            }
            if (!empty($small_hide_ids)) {
                $id_condition = MUtils2::generateSqlIntegerIn('id', $small_hide_ids);
                $small_hide_expression = new Expression('status | :small_hide_status', [
                    ':small_hide_status' => self::STATUS_SMALL_HIDE_MSG
                ]);
                $affect2_rows = AnMsg::updateAll(['status' => $small_hide_expression],
                    "{$id_condition} AND status & :small_hide_status = 0", [
                        ':small_hide_status' => self::STATUS_SMALL_HIDE_MSG
                    ]);
            }
            $transaction->commit();
            return $affect1_rows + $affect2_rows;
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }
}
