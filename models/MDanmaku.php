<?php

namespace app\models;

use app\components\util\Go;
use app\components\util\MUtils;
use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "m_danmaku".
 *
 * @property int $id 主键
 * @property int $element_id 弹幕所属元素 ID
 * @property int $element_type 弹幕所属元素类型
 * @property int $user_id 用户 ID
 * @property string $text 内容
 * @property string $stime 出现时间
 * @property int $size 字号
 * @property int $color 颜色
 * @property int $mode 模式：1 滚动、2 顶部、3 底部
 * @property int $pool 弹幕等级
 * @property int $create_time 添加时间
 * @property int $modified_time 修改时间
 */
class MDanmaku extends ActiveRecord
{
    const DEFAULT_SIZE = 25;
    const DEAFULT_COLOR = 16777215;

    // 滚动弹幕
    const MODE_SLIDE = 1;

    // 弹幕所属元素类型
    const ELEMENT_TYPE_NODE = 2;  // 互动广播剧节点

    // 弹幕最大长度限制为 35 个字符
    const MAX_DANMAKU_LENGTH = 35;

    /**
     * @inheritdoc
     */
    public static function getDb()
    {
        return Yii::$app->messagedb;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_danmaku';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['element_id', 'element_type', 'user_id', 'text', 'stime'], 'required'],
            [
                ['element_id', 'element_type', 'user_id', 'size', 'color', 'mode', 'create_time', 'modified_time'],
                'integer'
            ],
            [['stime'], 'string', 'max' => 11],
            [['text'], 'checkText'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'element_id' => '弹幕所属元素 ID',
            'element_type' => '弹幕所属元素类型',
            'user_id' => '用户 ID',
            'text' => '弹幕内容',
            'stime' => '插入时间',
            'size' => '字体大小',
            'color' => '字体颜色',
            'mode' => '弹幕类型',
            'pool' => '弹幕等级',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($this->isNewRecord) {
            $this->create_time = $time;
            $this->color = self::DEAFULT_COLOR;
            $this->size = self::DEFAULT_SIZE;
            $this->mode = self::MODE_SLIDE;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 验证弹幕是否合规
     *
     * @param string $attribute 验证的属性
     * @param mixed[] $params 其他自定义参数
     */
    public function checkText($attribute)
    {
        // 过滤特殊字符
        $this->$attribute = trim(MUtils::filterSpecialCodes($this->$attribute));
        if ($this->$attribute === '') {
            $this->addError($attribute, '请输入非纯表情的弹幕哦~');
            return;
        }
        // 检测弹幕违规情况
        $result = Yii::$app->go->checkText($this->$attribute, Go::SCENE_DANMAKU);
        if ($result && !current($result)['pass']) {
            $this->addError($attribute, $this->getAttributeLabel($attribute) . '中含有违规词汇喔~');
        }
    }

    /**
     * 保存弹幕
     *
     * @param int $element_type 弹幕所属元素类型
     * @param int $element_id 弹幕所属元素 ID
     * @param int $user_id 用户 ID
     * @param int $stime 发送时间
     * @param string $text 弹幕内容
     * @return self
     * @throws HttpException 保存失败时抛出异常
     */
    public static function saveDanmaku(int $sound_id, int $element_type, int $element_id, int $user_id,
            string $stime, string $text): self
    {
        $danmaku = new self();
        $danmaku->element_type = $element_type;
        $danmaku->element_id = $element_id;
        $danmaku->user_id = $user_id;
        $danmaku->stime = $stime;
        $danmaku->text = $text;
        $transaction = self::getDb()->beginTransaction();
        try {
            if (!$danmaku->save()) {
                $transaction = null;
                throw new HttpException(400, MUtils::getFirstError($danmaku));
            }
            // 节点绑定的音频弹幕量冗余字段为节点弹幕数量，互动剧节点新增弹幕，冗余值更新
            $update = MSound::updateAllCounters(['comment_count' => 1],
                'id = :sound_id', [':sound_id' => $sound_id]);
            if (!$update) {
                throw new HttpException(500, Yii::t('app/error',
                    'Send danmaku failed. Please try again later'));
            }
            $transaction->commit();
            return $danmaku;
        } catch (\Exception $e) {
            if ($transaction) {
                $transaction->rollBack();
            }
            throw $e;
        }
    }
}
