<?php

namespace app\models;

use app\components\util\Equipment;
use Exception;
use Yii;

class AdTrackYouDao extends AdTrack implements AdTrackInterface
{
    const CALLBACK_GATEWAY = 'https://conv.youdao.com/api/track';

    const CALLBACK_EVENT_TYPE_IOS_ACTIVATE = 'ios_activate';
    const CALLBACK_EVENT_TYPE_IOS_ONE_DAY_RETENTION = 'ios_day1retention';
    const CALLBACK_EVENT_TYPE_IOS_PAY = 'ios_purchase';
    const CALLBACK_EVENT_TYPE_IOS_REGISTER = 'ios_register';
    const CALLBACK_EVENT_TYPE_IOS_CUSTOM = 'ios_custom';

    const CALLBACK_EVENT_TYPE_ANDROID_ACTIVATE = 'android_activate';
    const CALLBACK_EVENT_TYPE_ANDROID_ONE_DAY_RETENTION = 'android_day1retention';
    const CALLBACK_EVENT_TYPE_ANDROID_PAY = 'android_purchase';
    const CALLBACK_EVENT_TYPE_ANDROID_REGISTER = 'android_register';
    const CALLBACK_EVENT_TYPE_ANDROID_CUSTOM = 'android_custom';

    const CALLBACK_EVENT_GROUP = [
        Equipment::iOS => [
            self::CALLBACK_EVENT_ACTIVATE => self::CALLBACK_EVENT_TYPE_IOS_ACTIVATE,
            self::CALLBACK_EVENT_ONE_DAY_RETENTION => self::CALLBACK_EVENT_TYPE_IOS_ONE_DAY_RETENTION,
            self::CALLBACK_EVENT_PAY => self::CALLBACK_EVENT_TYPE_IOS_PAY,
            self::CALLBACK_EVENT_REGISTER => self::CALLBACK_EVENT_TYPE_IOS_REGISTER,
            // TODO: 收听 5 个音的关键行为已调整为消费 / 充值关键行为，之后可删除此类型
            self::CALLBACK_EVENT_KEY_ACTION => self::CALLBACK_EVENT_TYPE_IOS_CUSTOM,
            self::CALLBACK_EVENT_TRANSACTION => self::CALLBACK_EVENT_TYPE_IOS_CUSTOM,  // 消费 / 充值关键行为
        ],
        Equipment::Android => [
            self::CALLBACK_EVENT_ACTIVATE => self::CALLBACK_EVENT_TYPE_ANDROID_ACTIVATE,
            self::CALLBACK_EVENT_ONE_DAY_RETENTION => self::CALLBACK_EVENT_TYPE_ANDROID_ONE_DAY_RETENTION,
            self::CALLBACK_EVENT_PAY => self::CALLBACK_EVENT_TYPE_ANDROID_PAY,
            self::CALLBACK_EVENT_REGISTER => self::CALLBACK_EVENT_TYPE_ANDROID_REGISTER,
            // TODO: 收听 5 个音的关键行为已调整为消费 / 充值关键行为，之后可删除此类型
            self::CALLBACK_EVENT_KEY_ACTION => self::CALLBACK_EVENT_TYPE_ANDROID_CUSTOM,
            self::CALLBACK_EVENT_TRANSACTION => self::CALLBACK_EVENT_TYPE_ANDROID_CUSTOM,  // 消费 / 充值关键行为
        ],
    ];

    /**
     * @param string $event_type 事件类型
     * @param mixed $arg 支付金额（单位：元），关键行为参数
     */
    public function callback(string $event_type, $arg = 0)
    {
        try {
            $event_type_map = self::CALLBACK_EVENT_GROUP[$this->os];
            if (!array_key_exists($event_type, $event_type_map)) {
                throw new Exception('有道广告点击回传事件错误：' . $event_type);
            }
            $event = $event_type_map[$event_type];
            if (is_null($event)) {
                return true;
            }
            $data = Yii::$app->tools->requestRemote($this->getCallbackUrl($event, $arg));
            if (!($data && $data['code'] === 'success')) {
                throw new Exception(sprintf('有道广告点击回传失败：code[%d], msg[%s]', $data['code'], $data['msg'] ?? ''));
            }
            return true;
        } catch (Exception $e) {
            Yii::error('youdao ad error: ' . $e->getMessage(), __METHOD__);
        }
        return false;
    }

    private function getCallbackUrl(string $callback_event, $arg = 0)
    {
        // 暂时不回传付费金额（付费金额属于机密数据）
        // if (in_array($callback_event, [self::CALLBACK_EVENT_TYPE_ANDROID_PAY, self::CALLBACK_EVENT_TYPE_IOS_PAY])) {
        //     return sprintf('%s?conv_ext=%s&conv_action=%s&order_amount=%d',
        //         self::CALLBACK_GATEWAY,
        //         $this->getConvExt(),
        //         $callback_event,
        //         Balance::profitUnitConversion($arg, Balance::CONVERT_YUAN_TO_FEN)
        //     );
        // }

        return sprintf('%s?conv_ext=%s&conv_action=%s',
            self::CALLBACK_GATEWAY,
            $this->getConvExt(),
            $callback_event
        );
    }

    private function getConvExt()
    {
        return $this->track_id;
    }

}
