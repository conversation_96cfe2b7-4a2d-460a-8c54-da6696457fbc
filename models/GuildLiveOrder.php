<?php

namespace app\models;

use app\components\auth\AuthAli;
use app\components\util\MUtils;
use Exception;
use Yii;
use yii\db\Expression;

/**
 * This is the model class for table "guild_live_order".
 *
 * @property int $id 主键
 * @property int $live_id 主播 ID
 * @property int $guild_id 公会 ID
 * @property int $status 状态（1 成功，0 创建，-1 取消，-2 错误）
 * @property int $type 平台（同 recharge_order.type 字段）
 * @property int $price 金额（分）
 * @property string $tid 平台订单号
 * @property int $applyment_id 合约申请 ID
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 */
class GuildLiveOrder extends ActiveRecord
{
    // 最低金额（单位：分）
    const MIN_PRICE = 1;

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->paydb;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'guild_live_order';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['live_id', 'guild_id', 'status', 'type', 'price', 'applyment_id'], 'required'],
            [['live_id', 'guild_id', 'status', 'type', 'price', 'create_time', 'modified_time', 'applyment_id'], 'integer'],
            [['tid'], 'string', 'max' => 32],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'live_id' => '主播 ID',
            'guild_id' => '公会 ID',
            'status' => '状态',  // 1 成功，0 创建，-1 取消，-2 错误
            'type' => '平台',  // 同 recharge_order.type 字段
            'price' => '金额（分）',
            'tid' => '平台订单号',
            'applyment_id' => '合约申请 ID',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 创建订单
     *
     * @param GuildLiveContractApplyment $applyment 合约申请
     * @param int $price 金额（单位：分）
     * @return array
     * @throws Exception
     */
    public static function createLivePenaltyOrder(GuildLiveContractApplyment $applyment, int $price)
    {
        $order = new self();
        $order->setAttributes([
            'price' => $price,
            'live_id' => $applyment->live_id,
            'guild_id' => $applyment->guild_id,
            'applyment_id' => $applyment->id,
            'status' => RechargeOrder::STATUS_CREATE,
            'type' => RechargeOrder::TYPE_ALIPAY,  // 暂时只支持支付宝
        ]);
        if (!$order->save()) {
            throw new Exception(MUtils::getFirstError($order));
        }

        $ali = new AuthAli();
        $link = $ali->tradeLivePenaltyPay($order);

        return [
            'order' => $order,
            'link' => $link,
            'price' => (string)Balance::profitUnitConversion($order->price, Balance::CONVERT_FEN_TO_YUAN),
        ];
    }

    public function getOutTradeNo()
    {
        return ('dev' === YII_ENV ? 'dev' : '') .
            $this->create_time .
            str_pad($this->type, 3, '0', STR_PAD_LEFT) .
            str_pad($this->id, 10, '0', STR_PAD_LEFT);
    }

    public static function getRealOrderId($out_trade_no)
    {
        return (int)substr($out_trade_no, -10);
    }

    /**
     * 更新订单成功或失败状态
     *
     * @param string $out_trade_no 外部订单 ID
     * @param float $price 价格（元）
     * @param string $tid 第三方支付账单号
     * @return boolean
     */
    public static function updateOrder($out_trade_no, $price, $tid)
    {
        $order_id = self::getRealOrderId($out_trade_no);

        $connection = self::getDb();
        $transaction = $connection->beginTransaction();
        try {
            $order = self::findBySql('SELECT * FROM ' . self::tableName()
                    . ' WHERE id = :id AND status = :status FOR UPDATE')
                ->addParams([':id' => $order_id, ':status' => RechargeOrder::STATUS_CREATE])
                ->one();
            if (!$order) {
                Yii::error(sprintf('订单不存在：%d', $order_id), __METHOD__);
                throw new Exception('订单不存在');
            }

            if ($order->price !== Balance::profitUnitConversion($price, Balance::CONVERT_YUAN_TO_FEN)) {
                Yii::error(sprintf('主播强制解约支付金额错误：订单 %d', $order_id), __METHOD__);
                throw new Exception('金额错误');
            }
            // 更新订单状态
            $order->status = RechargeOrder::STATUS_SUCCESS;
            $order->tid = $tid;
            if (!$order->save()) {
                Yii::error(sprintf('公会主播支付违约金更新订单失败：%s', MUtils::getFirstError($order)), __METHOD__);
                throw new Exception('订单更新失败');
            }

            $service = Yii::$app->params['service'];
            $base_uri = $service['live']['url'];
            $res = Yii::$app->tools->requestApi('/rpc/guild/creator-terminate-forcely', [
                'applyment_id' => $order->applyment_id,
            ], $base_uri);
            if (!$res || $res['status'] !== 200) {
                Yii::error('主播强制解约 rpc 接口出错：' . ($res ? $res['info'] : '未知错误'), __METHOD__);
                throw new Exception('主播强制解约失败');
            }

            $transaction->commit();
            return true;
        } catch (Exception $e) {
            $transaction->rollBack();
            return false;
        }
    }

}
