<?php

namespace app\models;

use Exception;
use Yii;

class AdTrackXingTu extends AdTrack implements AdTrackInterface
{
    const CALLBACK_EVENT_TYPE_MAP = [
        self::CALLBACK_EVENT_ACTIVATE => self::CALLBACK_EVENT_TYPE_ACTIVATE,
        self::CALLBACK_EVENT_ONE_DAY_RETENTION => self::CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION,
        self::CALLBACK_EVENT_PAY => self::CALLBACK_EVENT_TYPE_PAY,
        self::CALLBACK_EVENT_REGISTER => self::CALLBACK_EVENT_TYPE_REGISTER,
        self::CALLBACK_EVENT_KEY_ACTION => null,
        self::CALLBACK_EVENT_TRANSACTION => null,
        self::CALLBACK_EVENT_APP_CALLUP => null,
    ];

    // 激活
    const CALLBACK_EVENT_TYPE_ACTIVATE = 0;
    // 注册
    const CALLBACK_EVENT_TYPE_REGISTER = 1;
    // 付费
    const CALLBACK_EVENT_TYPE_PAY = 2;
    // 次留
    const CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION = 6;

    /**
     * 转化事件回调
     *
     * @param string $event_type 事件类型
     * @param mixed $arg 支付金额，付费场景暂不需要回传支付金额参数
     */
    public function callback(string $event_type, $arg = 0)
    {
        try {
            if (!array_key_exists($event_type, self::CALLBACK_EVENT_TYPE_MAP)) {
                throw new Exception('星图点击回传事件错误：' . $event_type);
            }
            $event = self::CALLBACK_EVENT_TYPE_MAP[$event_type];
            if (is_null($event)) {
                return true;
            }
            $data = Yii::$app->tools->requestRemote($this->getCallbackUrl($event));
            if (!($data && $data['code'] === 0)) {
                throw new Exception(sprintf('星图点击回传失败：code[%d], msg[%s]', $data['code'], $data['msg']));
            }
            return true;
        } catch (Exception $e) {
            Yii::error('xingtu ad callback error: ' . $e->getMessage(), __METHOD__);
        }
        return false;
    }

    /**
     * 获取转化事件回调 URL
     *
     * @link https://bytedance.feishu.cn/docx/OIRXddQF3oKKQqxrKFPcIm2snXg
     *
     * @param int $callback_event 事件类型
     */
    private function getCallbackUrl(int $callback_event): string
    {
        // track_id 形式：https://ad.oceanengine.com/track/activate/?callback=star-2a9c14893cc26aa006685fd1678c9cb0
        return sprintf('%s&event_type=%d', $this->track_id, $callback_event);
    }
}
