<?php

namespace app\models;

use Exception;
use Yii;

class Share
{
    /**
     * 新增分享日志记录
     *
     * @param integer $user_id 用户 ID
     * @param integer type 分享类型 1：音频；2：催眠专享；3：盲盒剧场；4：活动；5：直播间
     * @param integer element_id 分享元素的 ID
     * @param string url 分享出去的 url
     */
    public static function addLog(int $user_id, $type, $element_id, $url)
    {
        try {
            $time = $_SERVER['REQUEST_TIME'];
            $data = [
                'type' => $type,
                'user_id' => $user_id,
                'element_id' => $element_id,
                'url' => $url,
                'create_time' => $time,
            ];
            Yii::$app->databus->pub($data, 'share_detail_log:' . $time);
        } catch (Exception $e) {
            Yii::error('databus 出错：' . $e->getMessage(), __METHOD__);
            // PASS: databus 出错记录错误并忽略异常
        }
    }
}
