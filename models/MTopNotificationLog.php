<?php

namespace app\models;

use app\components\util\Tools;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\db\Exception;

/**
 * This is the model class for table "m_top_notification_log".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间戳，单位：秒
 * @property int $modified_time 更新时间戳，单位：秒
 * @property int $user_id 用户 ID
 * @property int $notification_id 通知 ID，1: iOS 充值页面通知
 */
class MTopNotificationLog extends ActiveRecord
{
    use ActiveRecordTrait;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_top_notification_log';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'notification_id'], 'required'],
            [['create_time', 'modified_time', 'user_id', 'notification_id'], 'integer']
        ];
    }

    /**
     * 新增顶部横幅通知记录
     *
     * @param int $notification_id 通知类型 ID
     * @param int $user_id 用户 ID
     * @return array
     */
    public static function addLogAndSendSysMsg(int $notification_id, int $user_id): array
    {
        try {
            $notification_info = MTopNotification::findOne(['id' => $notification_id]);
            if (!$notification_info) {
                return [];
            }
            $model = self::findOne(['user_id' => $user_id, 'notification_id' => $notification_id]);
            if ($model) {
                return [];
            }
            $model = new MTopNotificationLog();
            $model->user_id = $user_id;
            $model->notification_id = $notification_info->id;
            if (!$model->save()) {
                throw new Exception(MUtils2::getFirstError($model));
            }
            if ($notification_info->more && isset($notification_info->more['system_notification'])) {
                Yii::$app->tools->sendNotification([
                    'user_id' => $user_id,
                    'title' => $notification_info->more['system_notification']['title'],
                    'content' => $notification_info->more['system_notification']['content'],
                ], Tools::SEND_SYS_MSG);
            }
            return [
                'title' => $notification_info->title,
                'subtitle' => $notification_info->subtitle,
                'button_text' => $notification_info->button_text ?? '',
                'link' => $notification_info->link ?? '',
                'icon_url' => $notification_info->icon_url,
            ];
        } catch (Exception $e) {
            if (!MUtils2::isUniqueError($e, MTopNotificationLog::getDb())) {
                // 非唯一索引错误抛出的异常时，记录日志
                Yii::error('下发顶部横幅通知失败：' . $e->getMessage(), __METHOD__);
            }
            // PASS
        }
        return [];
    }
}
