<?php

namespace app\models;

use Exception;
use Yii;

/**
 * This is the model class for table "m_persona_rank".
 *
 * The followings are the available columns in table 'm_persona_rank':
 * @property integer $id 主键 ID
 * @property integer $create_time 创建时间（单位：秒）
 * @property integer $modified_time 更新时间（单位：秒）
 * @property integer $persona_id 画像 ID
 * @property integer $rank_type 榜单类型（同榜单表 m_homepage_rank 的 type）
 * @property integer $sort 榜单排序
 * @property string $name 榜单名称
 * @property integer $active 是否是默认选中的榜单
 */
class MPersonaRank extends ActiveRecord
{
    // 是否是默认选中的榜单
    // 否
    const ACTIVE_OFF = 0;
    // 是
    const ACTIVE_ON = 1;

    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_persona_rank';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['persona_id', 'rank_type', 'sort', 'name'], 'required'],
            [['persona_id', 'rank_type', 'sort', 'create_time', 'modified_time', 'active'], 'integer']
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键 ID',
            'create_time' => '创建时间',
            'modified_time' => '更新时间',
            'persona_id' => '画像 ID',
            'rank_type' => '榜单类型',
            'sort' => '榜单排序',
            'name' => '榜单名称',
            'active' => '是否默认选中',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 根据画像 ID 获取该画像的榜单内容详情
     *
     * @param int $persona_id 画像 ID
     * @return array
     * @throws Exception
     */
    public static function getRanksByPersonaId(int $persona_id)
    {
        $persona_ranks = self::getPersonaRanksByPersonaId($persona_id);
        if (empty($persona_ranks)) {
            return [];
        }
        $persona_rank_types = [];
        $rank_type_content_map = [];
        $rank_type_element_type_map = [];
        $live_rank_types = [];
        foreach ($persona_ranks as $persona_rank) {
            $rank_type = $persona_rank->rank_type;
            $element_type = MHomepageRank::getRankElementType($rank_type);
            $rank_type_element_type_map[$rank_type] = $element_type;
            if ($element_type === MPersonaModuleElement::ELEMENT_TYPE_LIVE) {
                $live_rank_types[] = $rank_type;
            } else {
                $persona_rank_types[] = $rank_type;
            }
            // 初始化 map 每个 key 的值
            $rank_type_content_map[$rank_type] = [];
        }
        // 获取音频和剧集排行榜数据
        $rank_type_data_map = MHomepageRank::getHomepageRanks($persona_rank_types);
        $rank_type_sound_id_map = [];
        $rank_type_drama_id_map = [];
        foreach ($rank_type_data_map as $type => $rank_type_data) {
            $element_ids = array_slice($rank_type_data, 0, MHomepageRank::RANK_ITEM_LENGTH_HOMEPAGE_SELECT);
            if ($rank_type_element_type_map[$type] === MPersonaModuleElement::ELEMENT_TYPE_SOUND) {
                $rank_type_sound_id_map[$type] = $element_ids;
            } else {
                $rank_type_drama_id_map[$type] = $element_ids;
            }
        }
        // 获取音频类型榜单详情
        if ($rank_type_sound_id_map) {
            $rank_detail_sounds = MHomepageRank::getRankSounds($rank_type_sound_id_map);
            foreach ($rank_detail_sounds as $type => $rank_detail_sound) {
                $rank_type_content_map[$type] = $rank_detail_sound;
            }
        }
        // 获取剧集类型榜单详情
        if ($rank_type_drama_id_map) {
            $rank_detail_dramas = MHomepageRank::getRankDramas($rank_type_drama_id_map);
            foreach ($rank_detail_dramas as $type => $rank_detail_drama) {
                $rank_type_content_map[$type] = $rank_detail_drama;
            }
        }
        // 获取直播类型榜单详情
        if (!empty($persona_rank_types)) {
            $rank_detail_lives = MHomepageRank::getRankLives($live_rank_types);
            foreach ($rank_detail_lives as $type => $rank_detail_live) {
                $rank_type_content_map[$type] = $rank_detail_live;
            }
        }
        $rank_details = [];
        $homepage_rank_details_link = Yii::$app->params['web_links']['homepage_rank_details'];
        foreach ($persona_ranks as $persona_rank) {
            $type = $persona_rank->rank_type;
            if (empty($rank_type_content_map[$type])) {
                // 首页排行榜没有符合要求的可展示的数据，记录错误日志
                Yii::error("首页排行榜没有符合要求的数据，榜单类型为：{$type}，画像 ID: {$persona_id}", __METHOD__);
                continue;
            }
            $query = http_build_query(['type' => $type, 'persona_id' => $persona_id, 'navhide' => 1]);
            $details = [
                'type' => $type,
                'name' => $persona_rank->name,
                'active' => $persona_rank->active,
                // navhide 用途参考文档：https://info.missevan.com/pages/viewpage.action?pageId=67240173
                'open_url' => "{$homepage_rank_details_link}?{$query}",
                'element_type' => $rank_type_element_type_map[$type],
                'elements' => $rank_type_content_map[$type],
            ];
            if ($persona_rank->active === self::ACTIVE_OFF) {
                unset($details['active']);
            }
            $rank_details[] = $details;
        }
        return $rank_details;
    }

    /**
     * 根据画像 ID 获取该画像所配置的榜单
     *
     * @param int $persona_id 画像 ID
     * @return array
     */
    public static function getPersonaRanksByPersonaId(int $persona_id)
    {
        $persona_ranks = self::find()
            ->select('rank_type, name, active')
            ->where(['persona_id' => $persona_id])
            ->orderBy('sort ASC')
            ->all();
        return $persona_ranks;
    }

    /**
     * 根据画像 ID 获取该画像所配置的榜单类型
     *
     * @param int $persona_id 画像 ID
     * @return array
     */
    public static function getRankTypesByPersonaId(int $persona_id)
    {
        $persona_rank_types = self::find()->select('rank_type')->where(['persona_id' => $persona_id])->column();
        return array_map('intval', $persona_rank_types);
    }
}
