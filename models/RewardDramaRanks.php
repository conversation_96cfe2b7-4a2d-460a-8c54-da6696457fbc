<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "reward_drama_ranks".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 * @property int $drama_id 剧集 ID
 * @property string $abstract 剧集简介
 * @property string $cover 剧集封面
 * @property int $rank 剧集排名
 * @property int $type 榜单类型（1 为周榜、2 为月榜、3 为总榜）
 */
class RewardDramaRanks extends ActiveRecord
{
    const RANK_ITEM_LENGTH_ARCHIVE = 60;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'reward_drama_ranks';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['drama_id', 'rank', 'type'], 'required'],
            [['create_time', 'modified_time', 'drama_id'], 'integer'],
            [['abstract', 'cover'], 'string', 'max' => 255],
            [['rank', 'type'], 'string', 'max' => 3],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'drama_id' => '剧集 ID',
            'abstract' => '剧集简介',
            'cover' => '剧集封面',
            'rank' => '剧集排名',
            'type' => '榜单类型', // 1 为周榜、2 为月榜、3 为总榜
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

}
