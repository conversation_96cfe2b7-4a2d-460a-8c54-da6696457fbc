<?php

namespace app\models;

use Yii;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "m_appearance_fee_deducted_record".
 *
 * @property integer $id 主键
 * @property integer $create_time 创建时间（秒级时间戳）
 * @property integer $modified_time 修改时间（秒级时间戳）
 * @property integer $user_id 用户 ID
 * @property integer $appearance_id 外观套装 ID
 * @property string $appearance_name 外观套装名称
 * @property integer $num 购买的月份数量
 * @property integer $pay_type 付费方式 1：钻石；2：微信；3：支付宝
 * @property integer $price 本次扣款金额（单位：分）；钻石支付时为钻石数量乘 10
 * @property integer $status 本次扣款状态：1 待扣款，2 扣款成功，3 扣款失败，4 重复支付，5 已退款（现金）
 * @property string $transaction_id 交易票据 ID（微信或支付宝交易时记录）
 * @property integer $tax 渠道费（单位：分）
 * @property array $more 更多详情
 */
class MAppearanceFeeDeductedRecord extends ActiveRecord
{
    use ActiveRecordTrait;

    // 订单编号前缀
    const TRADE_NO_PREFIX = 'appearance_';

    // 支付方式
    const PAY_TYPE_DIAMOND = 1;  // 钻石
    const PAY_TYPE_WECHAT = 2;  // 微信
    const PAY_TYPE_ALIPAY = 3;  // 支付宝

    // 支付状态
    const STATUS_PENDING = 1;  // 待扣款
    const STATUS_SUCCESS = 2;  // 扣款成功
    const STATUS_FAILED = 3;  // 扣款失败
    const STATUS_DUPLICATE = 4;  // 重复支付
    const STATUS_REFUNDED = 5;  // 已退款（现金）

    /**
     * @return Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->paydb;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_appearance_fee_deducted_record';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'appearance_id', 'appearance_name', 'num', 'pay_type', 'price', 'status'], 'required'],
            [['create_time', 'modified_time', 'user_id', 'appearance_id', 'num', 'pay_type', 'price', 'status', 'tax'], 'integer'],
            [['pay_type'], 'in', 'range' => [
                self::PAY_TYPE_DIAMOND,
                self::PAY_TYPE_WECHAT,
                self::PAY_TYPE_ALIPAY,
            ]],
            [['status'], 'in', 'range' => [
                self::STATUS_PENDING,
                self::STATUS_SUCCESS,
                self::STATUS_FAILED,
                self::STATUS_DUPLICATE,
                self::STATUS_REFUNDED,
            ]],
            [['appearance_name', 'transaction_id'], 'string'],
            [['more'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'user_id' => '用户 ID',
            'appearance_id' => '外观套装 ID',
            'appearance_name' => '外观套装名称',
            'num' => '购买的月份数量',
            'pay_type' => '付费方式',  // 1：钻石；2：微信；3：支付宝
            'price' => '本次扣款金额',  // 单位：分
            'status' => '本次扣款状态',  // 1 待扣款，2 扣款成功，3 扣款失败，4 重复支付，5 已退款（现金）
            'transaction_id' => '交易票据 ID',
            'tax' => '渠道费',  // 单位：分
            'more' => '更多详情',
        ];
    }
}
