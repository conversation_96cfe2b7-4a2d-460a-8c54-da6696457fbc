<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "user_certification".
 *
 * @property integer $id
 * @property integer $create_time
 * @property integer $modified_time
 * @property integer $user_id
 * @property string $subtitle
 */
class UserCertification extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'user_certification';
    }
}
