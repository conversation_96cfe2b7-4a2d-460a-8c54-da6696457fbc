<?php

namespace app\models;

use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "transaction_items_log".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 * @property int $goods_id 商品 ID
 * @property string $goods_title 商品标题
 * @property int $goods_price 商品价格（单位：钻石）
 * @property int $goods_num 商品数量
 * @property int $tid transaction_log 主键
 * @property int $user_id 用户 ID
 * @property int $status 订单状态，同 transaction_log.status
 * @property int $type 订单类型，同 transaction_log.type
 * @property string $more 更多详情
 */
class TransactionItemsLog extends ActiveRecord
{
    use ActiveRecordTrait;

    public $more_detail = [];

    /**
     * @var mixed
     */
    private $_carry_data;

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->paydb;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'transaction_items_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['goods_id', 'goods_title', 'goods_price', 'tid', 'user_id', 'status', 'type'], 'required'],
            [['create_time', 'modified_time', 'goods_id', 'goods_price', 'goods_num', 'tid', 'user_id', 'status', 'type'], 'integer'],
            [['more'], 'safe'],
            [['goods_title'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'goods_id' => '商品 ID',
            'goods_title' => '商品标题',
            'goods_price' => '商品价格（单位：钻石）',
            'goods_num' => '商品数量',
            'tid' => 'transaction_log 主键',
            'user_id' => '用户 ID',
            'status' => '订单状态',  // 同 transaction_log.status
            'type' => '订单类型',  // 同 transaction_log.type
            'more' => '更多详情',
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->more) {
            if (is_array($this->more)) {
                $this->more_detail = $this->more;
            } else {
                $this->more_detail = Json::decode($this->more) ?: [];
            }
        }
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;

        if ($this->more_detail) {
            $this->more = Json::encode($this->more_detail);
        }
        return true;
    }

    public function carry(mixed $data): void
    {
        $this->_carry_data = $data;
    }

    public function getCarryData()
    {
        return $this->_carry_data;
    }

}
