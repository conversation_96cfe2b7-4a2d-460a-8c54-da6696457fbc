<?php

namespace app\models;

use app\components\util\Go;
use app\components\util\MUtils;
use missevan\storage\StorageClient;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\db\Expression;
use yii\web\HttpException;

/**
 * This is the model class for table "m_tag".
 *
 * @property integer $id 主键
 * @property string $name 标签或频道名
 * @property string $icon 频道图标
 * @property string $cover 频道背景图
 * @property string $sintro 频道一句话简介
 * @property string $intro 频道介绍
 * @property integer $sound_num 音频引用数
 * @property integer $image_num 图片引用数
 * @property integer $album_num 音单引用数
 * @property integer $follow_num 频道被关注数
 * @property integer $userid 绑定用户 ID
 * @property integer $seiyid 绑定声优 ID
 * @property integer $characterid 绑定角色 ID
 * @property integer $animationid 绑定作品 ID
 * @property integer $recommended 是否上首页
 * @property integer $sort_type 排序方式
 * @property integer $sort_channel 频道排序
 * @property integer $catalogid 分类 ID
 * @property integer $last_upload_time 最后上传时间
 */
class MTag extends ActiveRecord
{
    const TAG_ID_FOJOSHI = 317;

    // 普通标签
    const RECOMMENDED_TAG = 0;
    // 频道
    const RECOMMENDED_CHANNEL = 1;

    // 音单标签
    const SCENARIO_ALBUM_TAG = 'album_tag';
    // 音单标签名称长度限 10 个字符
    const MAX_ALBUM_TAG_LENGTH = 10;
    // 音单标签的数量最多为 3 个
    const MAX_ALBUM_TAG_NUM = 3;

    public $bigpic;
    public $smallpic;
    public $subscibed;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_tag';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['name'], 'required'],
            [['name'], 'checkTagName', 'on' => [self::SCENARIO_DEFAULT, self::SCENARIO_ALBUM_TAG]],
            [['intro'], 'string'],
            [['sound_num', 'image_num', 'album_num', 'follow_num', 'userid', 'seiyid', 'characterid', 'animationid', 'recommended', 'sort_type', 'sort_channel', 'catalogid', 'last_upload_time'], 'integer'],
            [['name'], 'string', 'max' => 64],
            [['icon'], 'string', 'max' => 100],
            [['sintro'], 'string', 'max' => 200],
            [['name', 'recommended'], 'unique', 'targetAttribute' => ['name', 'recommended'], 'message' => 'The combination of 标签名称 and 是否上首页 has already been taken.'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '标签id',
            'name' => '标签名称',
            'icon' => '频道图标',
            'cover' => '频道封面图',
            'sintro' => '一句话简介',
            'intro' => '标签简介',
            'sound_num' => '声音引用数',
            'image_num' => '图片引用数',
            'album_num' => '专辑引用数',
            'follow_num' => '标签订阅人数',
            'userid' => '绑定用户id',
            'seiyid' => '绑定声优id',
            'characterid' => '绑定角色id',
            'animationid' => '绑定作品id',
            'recommended' => '是否上首页',
            'sort_type' => '排序方式',
            'sort_channel' => '频道排序',
            'catalogid' => '分类id',
            'last_upload_time' => '最后上传时间',
        ];
    }

    public function checkTagName($attribute)
    {
        if ($this->isAttributeChanged($attribute)) {
            // TODO: 暂时先抛出异常处理，优化 build 方法后再调整为 addError 的处理方式
            $scenario = $this->getScenario();
            if (self::SCENARIO_ALBUM_TAG === $scenario && strlen($this->$attribute) > self::MAX_ALBUM_TAG_LENGTH) {
                // 音单标签名称长度限制
                throw new HttpException(400, '标签名称不能超过 ' . self::MAX_ALBUM_TAG_LENGTH . ' 个字符');
            }
            // 标签名称不支持 Emoji
            if (MUtils2::containsEmoji($this->$attribute)) {
                throw new HttpException(400, '标签名称不支持 Emoji');
            }
        }
    }

    public static function getLikeChannel(int $sound_id, int $num)
    {
        if (!$sound_id || !$num) return [];
        $channel = self::find()
            ->alias('t')
            ->select('t.id, t.name, t.icon, t.follow_num')
            ->leftJoin('m_tag_sound_map t1', 't.id = t1.tag_id')
            ->orderBy(['t.sort_channel' => SORT_DESC])
            ->where('t1.sound_id = :sound_id AND t.recommended = :recommended',
                [':sound_id' => $sound_id, ':recommended' => self::RECOMMENDED_CHANNEL])
            ->andWhere('t.sort_channel <> 0')
            ->limit($num)->all();
        return $channel;
    }

    static function cleanTags($tags)
    {
        if (!$tags) return [];
//        $tags = json_decode($tags);
//        if (json_last_error() != JSON_ERROR_NONE) return [];

        $tags = array_unique($tags);
        $tags = array_map(function ($tag) {
            $tag = trim($tag);
            return str_replace(';', ',', $tag);
        }, $tags);
        $tags = array_filter($tags, function ($tag) {
            return strlen($tag);
        });
        if (count($tags) > 12) array_slice($tags, 0, 12);
        $tags = array_values($tags);
        return $tags;
    }

    /**
     * 创建或修改标签
     *
     * @param string $method 方法 create：创建标签，update 更新标签
     * @param array $tags 标签信息
     * @param int $resource_id 标签对象 ID
     * @param string $for 标签对象类型 sound: 音频, album: 音单, image: 图片
     * @param string $redirect 跳转地址
     * @throws HttpException
     * @todo 需要优化
     */
    public function build(string $method, $tags = [], $resource_id = 0, string $for = '', string $redirect = '')
    {
        if (!empty($tags)) {
            if (self::SCENARIO_ALBUM_TAG === $this->getScenario() && count($tags) > self::MAX_ALBUM_TAG_NUM) {
                throw new HttpException(400, '标签添加不允许超过 ' . self::MAX_ALBUM_TAG_NUM . ' 个');
            }
            // 对标签名称进行检查
            $check_result = Yii::$app->go->checkText($tags, Go::SCENE_INTRO, true);
            if (!empty($check_result)) {
                $failed_checks = array_filter($check_result, function ($item) {
                    return !$item['pass'];
                });
                if (!empty($failed_checks)) {
                    throw new HttpException(400, '标签中含有敏感信息');
                }
            }
            $tags = self::cleanTags($tags);
            if ($method == 'create') {
                self::_tagsCreate($tags, $resource_id, $for, $redirect);
            } elseif ($method == 'update') {
                self::_tagsUpdate($tags, $resource_id, $for, $redirect);
            }
        } else {
            if ($method == 'update') {
                self::_tagsUpdate($tags, $resource_id, $for, $redirect);
            }
        }
        $className = 'app\models\\' . ucfirst($for) . 'Tags';
        $exist = 1;
        if ($tag = $className::findOne(['id' => $resource_id])) {
        } else if ($tags) {

            $tag = new $className;

            $tag->id = $resource_id;
            $exist = 0;
        } else {
            return;
        }

        $tag->tags = implode(',', $tags);
        $tag->save();
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->icon) {
            $parts = explode('://', $this->icon);
            if (count($parts) === 2) {
                $this->smallpic = StorageClient::getFileUrl($this->icon);
            } else {
                // WORKAROUND: 此处代码用于兼容数据库中未加协议头的图片地址，待数据刷新成统一格式后可去掉此兼容
                $this->smallpic = Yii::$app->params['static_domain']
                    . Yii::$app->insetParams['channelSmallUrl'] . $this->icon;
                $this->bigpic = Yii::$app->params['static_domain'] . Yii::$app->insetParams['channelUrl']
                    . $this->icon;
            }
        }
        if ($this->cover) {
            $this->bigpic = StorageClient::getFileUrl($this->cover);
        }
        unset($this->icon, $this->cover);
    }

    /**
     * tags 写入操作 parent:tags
     *
     * @param $tags
     * @param $resource_id
     * @param $for
     * @param $redirect
     */
    protected static function _tagsCreate($tags = [], $resource_id = 0, $for, $redirect)
    {
        foreach ($tags as $tag_name) {
            $tag = MTag::findOne(['name' => $tag_name, 'recommended' => 0]);

            if ($tag) {
                switch ($for) {
                    case 'sound':
                        $tag->sound_num = $tag->sound_num + 1;
                        break;
                    case 'image':
                        $tag->image_num = $tag->image_num + 1;
                        break;
                    case 'album':
                        $tag->album_num = $tag->album_num + 1;
                        break;
                    default:
                        throw new HttpException(400, '参数错误，请联系管理员');
                }
                $tag->save();
            } else {
                $tag = ($for === 'album' ? new MTag(['scenario' => self::SCENARIO_ALBUM_TAG]) : new MTag());
                switch ($for) {
                    case 'sound':
                        $tag->sound_num = 1;
                        break;
                    case 'image':
                        $tag->image_num = 1;
                        break;
                    case 'album':
                        $tag->album_num = 1;
                        break;
                    default:
                        throw new HttpException(400, '参数错误，请联系管理员');
                }
                $tag->name = $tag_name;
                $tag->save();
            }

            self::_tagsRelation($tag->id, $resource_id, $for);
        }

    }

    /**
     * tags 更新操作 parent:tags
     *
     * @param $tags
     * @param $resource_id
     * @param $for
     * @param $redirect
     */
    protected static function _tagsUpdate($tags = [], $resource_id = 0, $for = '', $redirect)
    {
        switch ($for) {
            case 'sound':
                $resource = self::find()
                    ->alias('t')
                    ->select('t.id, t.name')
                    ->leftJoin('m_tag_sound_map t1', 't.id = t1.tag_id')
                    ->where(['sound_id' => $resource_id])
                    ->all();
                break;
            case 'image':
                $resource = self::find()
                    ->alias('t')
                    ->select('t.id, t.name')
                    ->leftJoin('m_tag_image_map t1', 't.id = t1.tag_id')
                    ->where(['image_id' => $resource_id])
                    ->all();
                break;
            case 'album':
                $resource = self::find()
                    ->alias('t')
                    ->select('t.id, t.name')
                    ->leftJoin('m_tag_album_map t1', 't.id = t1.tag_id')
                    ->where(['album_id' => $resource_id])
                    ->all();
                break;
            default:
                echo "缺少参数!";
                exit();
        }

        $tagsArray = [];
        $tagsIdArray = [];
        foreach ($resource as $row) {
            $tagsArray[] = $row->name;
            $tagsIdArray[] = $row->id;
        }
        //$titleTags = implode(',', $tagsArray);
        //去除重复标签
        foreach ((array)$tags as $value) {

            if (!in_array($value, $tagsArray) && !empty($value)) {
                $dao = ($for === 'album' ? new MTag(['scenario' => self::SCENARIO_ALBUM_TAG]) : new MTag());
                $get_data = $dao->findOne(['name' => $value]);
                if (empty($get_data)) {
                    switch ($for) {
                        case 'sound':
                            $dao->sound_num = 1;
                            break;
                        case 'image':
                            $dao->image_num = 1;
                            break;
                        case 'album':
                            $dao->album_num = 1;
                            break;
                        default:
                            echo "缺少参数!";
                    }
                    $dao->name = $value;
                    if ($dao->save()) {
                        $tag_id = $dao->id; // 最后插入 id
                    } else {
                        echo '新建存储失败';
                        print_r($dao->errors);
                        exit();
                    }

                } else {
                    $condition = [];
                    switch ($for) {
                        case 'sound':
                            $condition = ['sound_num' => 1];
                            break;
                        case 'image':
                            $condition = ['image_num' => 1];
                            break;
                        case 'album':
                            $condition = ['album_num' => 1];
                            break;
                        default:
                            echo "缺少参数!";
                    }
                    if ($get_data->updateCounters($condition)) {
                        $tag_id = $get_data->id; // 最后插入 id
                    } else {
                        echo '更改存储失败';
                        exit();
                    }
                }
                //写入关联
                self::_tagsRelation($tag_id, $resource_id, $for);
            }
        }
        foreach ($resource as $row) {
            if (!in_array($row->name, $tags)) {//原来有的，更新后没有了
                $dao = ($for === 'album' ? new MTag(['scenario' => MTag::SCENARIO_ALBUM_TAG]) : new MTag());
                $get_data = $dao->findOne(['name' => $row->name]);
                $condition = [];
                switch ($for) {
                    case 'sound':
                        $condition = ['sound_num' => -1];
                        break;
                    case 'image':
                        $condition = ['image_num' => -1];
                        break;
                    case 'album':
                        $condition = ['album_num' => -1];
                        break;
                    default:
                        echo "缺少参数!";
                }

                if ($get_data->updateCounters($condition)) {
                    $tag_id = $get_data->id;
                    if ($get_data->sound_num + $get_data->image_num + $get_data->album_num <= 0) {
                        $dao->deleteAll(['id' => $tag_id]);
                    }
                    //删除关联表数据
                    switch ($for) {
                        case 'sound':
                             MTagSoundMap::deleteAll('sound_id = :sound_id AND tag_id = :tag_id',
                                ['sound_id' => $resource_id, 'tag_id' => $row->id]);
                            break;
                        case 'image':
                             MTagImageMap::deleteAll('image_id = :image_id AND tag_id = :tag_id',
                                ['image_id' => $resource_id, 'tag_id' => $row->id]);
                            break;
                        case 'album':
                            MTagAlbumMap::deleteAll('album_id = :album_id AND tag_id = :tag_id',
                                ['album_id' => $resource_id, 'tag_id' => $row->id]);
                            break;
                        default:
                            echo "缺少参数!";
                    }
                } else {
                    echo '更改存储失败';
                    exit();
                }
            }
        }

    }

    /**
     * tags 关联主题ID
     *
     * @param $tags
     * @param $resource_id
     * @param $for
     */
    protected static function _tagsRelation($tag_id, $resource_id = 0, $for = '')
    {
        switch ($for) {
            case 'sound':
                $dao = new MTagSoundMap();
                $dao->sound_id = $resource_id;
                break;
            case 'image':
                $dao = new MTagImageMap();
                $dao->image_id = $resource_id;
                break;
            case 'album':
                $dao = new MTagAlbumMap();
                $dao->album_id = $resource_id;
                break;
            default:
                echo "缺少参数!";
                exit();
        }
        $dao->tag_id = $tag_id;
        $dao->save();
    }

    public static function getSubscribedChannel($user_id, $page_size)
    {
        $query = self::find()->alias('t')->select('t.id, name, icon, cover, follow_num')
            ->leftJoin('av_attention_tag t1', 't.id = t1.tag_id')
            ->where('t.recommended = :recommended AND t1.user_id = :user_id',
                [':recommended' => self::RECOMMENDED_CHANNEL, ':user_id' => $user_id])
            ->orderBy(['t1.id' => SORT_DESC]);

        if ($user_id !== Yii::$app->user->id) {
            // 对其他用户隐藏封禁频道
            $query = $query->andWhere('t.sort_channel <> 0');
        }

        $return_model = MUtils::getPaginationModels($query, $page_size);
        self::isSubscribed($return_model->Datas, $user_id);
        return $return_model;
    }

    public static function isSubscribed(&$channels, $user_id = 0)
    {
        $my_id = Yii::$app->user->id;
        if (!$my_id) return;

        if (gettype($channels) === 'array') {

            if ($user_id == Yii::$app->user->id) {
                $subscribed = true;
                $subscribes = [];
            } else {
                $subscribed = false;
                $ids = array_column($channels, 'id');
                $subscribes = AvAttentionTag::findAll(['user_id' => $my_id, 'tag_id' => $ids]);
                $subscribes = $subscribes ? array_column($subscribes, 'tag_id') : [];
            }
            foreach ($channels as &$channel) {
                $channel->subscibed = (int)($subscribed || in_array($channel->id, $subscribes));
            }

        } else {
            if ($my_id == $user_id
                || AvAttentionTag::findOne(['user_id' => $my_id, 'tag_id' => $channels->id])
            ) {
                $channels->subscibed = 1;
            } else {
                $channels->subscibed = 0;
            }

        }
    }

    public static function UpdateSoundTag($tags_name, $sound_id)
    {
        // 对标签进行处理
        if (is_array($tags_name)) {
            $exist_tags = self::findAll(['name' => $tags_name, 'recommend' => 0]);

            $exist_tags_name = array_column($exist_tags, 'name');
            $not_exist_tags_name = array_diff($tags_name, $exist_tags_name);

            $not_exist_tags = [];
            if ($not_exist_tags_name) {
                $sql = 'INSERT INTO m_tag (name) VALUES ' .
                    array_map(function ($tag_name) {
                        return "(\"$tag_name\"),";
                    }, $not_exist_tags_name);

                $command = Yii::$app->connection->createCommand($sql);
                $command->execute();
                $not_exist_tags = self::findAll(['name' => $not_exist_tags_name, 'recommend' => 0]);
            }

            $tags = array_merge($exist_tags, $not_exist_tags);
            $tag_ids = array_column($tags, 'id');
            unset($tags_name, $exist_tags, $not_exist_tags, $exist_tags_name, $not_exist_tags_name, $tags);
            if ($tag_ids) {
                $sql = 'INSERT INTO m_tag_sound_map VALUES ' .
                    array_map(function ($tag_id) use ($sound_id) {
                        return "($tag_id, $sound_id),";
                    }, $tag_ids);

                $command = Yii::$app->connection->createCommand($sql);
                $command->execute();
                MTag::updateAllCounters([
                    'sound_num' => 1,
                    'id in :id',
                    [':id' => $tag_ids]
                ]);
            }
        }
    }

    /**
     * 批量更新标签的的音单数冗余字段
     *
     * @param array $tag_album_map 标签 ID 和需要更新的音单数量 map，例：[4 => 2, 5 => 1]
     * @return int
     * @throws \Exception
     */
    public static function updateTagAlbumCount(array $tag_album_map): int
    {
        if (!$tag_album_map) {
            return 0;
        }
        $update_func = function ($model, $tag_album_map) {
            $tag_ids = array_keys($tag_album_map);
            $album_count_arr = array_unique(array_values($tag_album_map));
            if (count($album_count_arr) === 1) {
                // 需要批量更新的标签音单数一致
                $favorite_count = $album_count_arr[0];
                $expression = new Expression('GREATEST(album_num, :album_num) - :album_num',
                    [':album_num' => $favorite_count]);
                $affected_row = $model::updateAll(['album_num' => $expression], ['id' => $tag_ids]);
            } else {
                // 需要批量更新的标签音单数不一致
                // 生成的批量更新标签音单数的 SQL 如下：
                // UPDATE m_tag
                // SET `album_num` = CASE id
                //   WHEN 4 THEN GREATEST(album_num, 2) - 2
                //   WHEN 5 THEN GREATEST(album_num, 1) - 1
                // END
                // WHERE id IN (4, 5)
                $table_name = $model::tableName();
                $in_tag_condition = MUtils::generateIntegerIn('id', $tag_ids);
                $case_value = 'CASE id';
                foreach ($tag_album_map as $tag_id => $album_num) {
                    $case_value .= " WHEN {$tag_id} THEN GREATEST(album_num, {$album_num}) - {$album_num}";
                }
                $case_value .= ' END';
                $sql = <<<SQL
UPDATE {$table_name}
SET `album_num` = {$case_value}
WHERE {$in_tag_condition};
SQL;
                $affected_row = Yii::$app->db->createCommand($sql)->execute();
            }
            return $affected_row;
        };
        return MUtils::ensureDbTransaction(self::class, $update_func, $tag_album_map);
    }

    /**
     * 获取音频标签
     *
     * @param int $sound_id
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getSoundTags(int $sound_id)
    {
        if ($sound_id <= 0) {
            return [];
        }
        return self::find()
            ->alias('t')
            ->select('t.id, t.name')
            ->leftJoin(MTagSoundMap::tableName() . ' AS t1', 't.id = t1.tag_id')
            ->where('t1.sound_id = :sound_id', [':sound_id' => $sound_id])->all();
    }
}
