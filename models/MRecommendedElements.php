<?php

namespace app\models;

use app\components\util\Equipment;
use app\components\util\MUtils;
use Exception;
use missevan\storage\StorageClient;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "m_recommended_elements".
 *
 * @property integer $id
 * @property integer $client
 * @property integer $module_type
 * @property integer $module_id
 * @property integer $element_type
 * @property integer $element_id
 * @property string $summary
 * @property string $cover
 * @property string $url
 * @property integer $sort
 * @property integer $creator_id
 * @property integer $create_time
 * @property integer $update_time
 * @property integer $archive
 * @property integer $start_time
 * @property integer $end_time
 * @property string $more
 */
class MRecommendedElements extends ActiveRecord
{
    const MODULE_TYPE_NONE = 0;
    // 版头图模块
    const MODULE_TYPE_BANNER = 1;
    // 猜你喜欢剧集或音单模块
    const MODULE_TYPE_LIKE_DRAMA_ALBUM = 5;
    // 轮播通栏图模块
    const MODULE_TYPE_EXTRA_BANNER = 7;
    // 剧集分区首页版头图模块
    const MODULE_TYPE_DRAMA_BANNER = 9;
    // 剧集分区首页轮播通栏模块
    const MODULE_TYPE_DRAMA_EXTRA_BANNER = 10;
    // 音频分区首页版头图模块
    const MODULE_TYPE_SOUND_BANNER = 11;
    // 音频分区首页轮播通栏模块
    const MODULE_TYPE_SOUND_EXTRA_BANNER = 12;
    // 盲盒剧场首页 banner 模块
    const MODULE_TYPE_BLIND_BOX_HOMEPAGE_BANNER = 16;
    // 盲盒剧场首页广告位模块
    const MODULE_TYPE_BLIND_BOX_AD_BANNER = 17;

    // banner 类型的 module_type 集合
    const BANNER_MODULE_TYPES = [
        self::MODULE_TYPE_BANNER,
        self::MODULE_TYPE_DRAMA_BANNER,
        self::MODULE_TYPE_SOUND_BANNER,
    ];

    // 轮播通栏图位置（1, 2, 3, 4）
    const MODULE_POSITION_EXTRA_BANNER_1 = 1;
    const MODULE_POSITION_EXTRA_BANNER_2 = 2;
    const MODULE_POSITION_EXTRA_BANNER_3 = 3;
    const MODULE_POSITION_EXTRA_BANNER_4 = 4;
    const MODULE_POSITION_EXTRA_BANNER = [
        self::MODULE_POSITION_EXTRA_BANNER_1,
        self::MODULE_POSITION_EXTRA_BANNER_2,
        self::MODULE_POSITION_EXTRA_BANNER_3,
        self::MODULE_POSITION_EXTRA_BANNER_4,
    ];

    // 在线的推荐位
    const ARCHIVE_ONLINE = 0;
    // 历史归档的推荐位
    const ARCHIVE_HISTORY = 1;

    const CLIENT_APP_ALL = 0;
    const CLIENT_APP_ANDROID = 1;
    const CLIENT_APP_IOS = 2;
    const MODULE_ID_NONE = 0;

    // 剧集
    const ELEMENT_TYPE_DRAMA = 2;
    // 推荐搜索词
    const ELEMENT_TYPE_SEARCH_WORDS = 6;
    const ELEMENT_ID_NONE = 0;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_recommended_elements';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['client'], 'required'],
            [
                [
                    'client',
                    'module_type',
                    'module_id',
                    'element_type',
                    'element_id',
                    'sort',
                    'creator_id',
                    'create_time',
                    'update_time',
                    'archive'
                ],
                'integer'
            ],
            [['summary', 'cover', 'url'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'client' => '平台', // 1 安卓、2 iOS
            'module_type' => '模块类型',
            'module_id' => '模块 ID',
            'element_type' => '元素类型', // 0 单音、1 音单、2 剧集、3 活动
            'element_id' => '元素 ID',
            'summary' => '简介',
            'cover' => '封面',
            'url' => '原始链接',
            'sort' => '排序',
            'creator_id' => '负责人 ID',
            'create_time' => '创建时间',
            'update_time' => '更新时间',
            'archive' => '历史归档', // 0 为否， 1 为是（即为被删去的）
            'start_time' => '自动上线时间',
            'end_time' => '自动下线时间',
        ];
    }

    public static function getLikeElementsRecommended($module_ids)
    {
        $elements = self::find()
            ->where('client = :client AND module_type = :module_type AND archive = :archive', [
                ':client' => self::CLIENT_APP_ALL,
                ':module_type' => self::MODULE_TYPE_LIKE_DRAMA_ALBUM,
                ':archive' => self::ARCHIVE_ONLINE
            ])->andWhere(['module_id' => $module_ids])->orderBy('sort ASC')->all();

        return $elements;
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->cover) {
            // TODO: 目前仅广播剧分区首页轮播通栏图保存了协议地址，需将其他模块类型的 cover 字段所存值改为协议地址
            // 若为协议地址，则返回完整地址
            $this->cover = MUtils::hasHttpScheme($this->cover) ? $this->cover
                : StorageClient::getFileUrl($this->cover);
        }
        if (YII_ENV_TEST && $this->more) {
            // sqlite json 字段不支持自动 decode，需进行 Json::decode 处理
            $this->more = Json::decode($this->more);
        }
    }

    /**
     * 获取搜索框推荐词
     * 推荐序列最多容纳 30 个推荐词，若推荐词库 + 热门搜索词的数量超过 30，则仅展示前 30 的内容
     *
     * @return array
     */
    public static function getSearchWords()
    {
        // 判断是否显示搜索框推荐词，在搜索框推荐词后台设置
        $config = Yii::$app->redis->hMGet(KEY_SEARCH_RECOMMEND_CONFIG, ['show_recommend', 'show_recommend_hot']);
        $show_recommend = (int)$config['show_recommend'];
        if (!$show_recommend) {
            // 后台搜索框显示推荐词按钮关闭，则不显示推荐词和热搜词
            return [];
        }
        $show_recommend_hot = (int)$config['show_recommend_hot'];
        // 显示推荐词
        $memcache = Yii::$app->memcache;
        // 在搜索框推荐词后台设置
        if (!$search_words = $memcache->get(KEY_SEARCH_RECOMMEND_WORDS)) {
            $condition = 'element_type = :element_type AND element_id = :element_id AND ' .
                'module_type = :module_type AND module_id = :module_id AND archive = :archive';
            $params = [
                ':element_type' => self::ELEMENT_TYPE_SEARCH_WORDS,
                ':element_id' => self::ELEMENT_ID_NONE,
                ':module_type' => self::MODULE_TYPE_NONE,
                ':module_id' => self::MODULE_ID_NONE,
                ':archive' => self::ARCHIVE_ONLINE,
            ];
            $recommend_words = MRecommendedElements::find()
                ->select('summary AS word, url, sort')
                ->where($condition, $params)
                ->orderBy('sort ASC')
                ->asArray()
                ->all();
            $recommend_words = array_map(function ($item) {
                $item['sort'] = (int)$item['sort'];
                return $item;
            }, $recommend_words);
            $word_info = $recommend_words;
            $LIMIT_MAX_COUNT = 30;
            $recommend_word_count = count($recommend_words);
            if (($recommend_word_count < $LIMIT_MAX_COUNT) && $show_recommend_hot) {
                // 推荐词数量小于 30 个，且设置可显示热搜词则增加显示热搜词
                // 在热门搜索后台设置
                $hot_search_words = self::getHotSearchWords();
                if (!empty($hot_search_words)) {
                    // 推荐词库 + 热门搜索词
                    $sort = $recommend_word_count + 1;
                    $hot_search_words = array_map(function ($item) use (&$sort) {
                        $return = [
                            'word' => $item['key'],
                            'url' => '',
                            'sort' => $sort,
                        ];
                        $sort += 1;
                        return $return;
                    }, $hot_search_words);
                    // 去重，若热搜词和推荐词库关键词相同，优先匹配推荐词库的规则
                    $recommend_words_map = array_column($recommend_words, null, 'word');
                    $hot_search_words_map = array_column($hot_search_words, null, 'word');
                    // array_merge 有相同的字符串键名时，则该键名后面的值将覆盖前一个值
                    $word_info = array_values(array_merge($hot_search_words_map, $recommend_words_map));
                    // 按 sort 字段排序
                    usort($word_info, function ($a, $b) {
                        return $b['sort'] < $a['sort'];
                    });
                    // 处理热搜词字段
                    $word_info = array_map(function ($item) {
                        unset($item['sort']);
                        return $item;
                    }, $word_info);
                }
            }
            // 推荐词数量大于或等于 30 时，则只显示前 30 个推荐词
            $word_info = array_slice($word_info, 0, $LIMIT_MAX_COUNT);
            $search_words = Json::encode($word_info);
            $memcache->set(KEY_SEARCH_RECOMMEND_WORDS, $search_words, FIVE_MINUTE);
        }
        $search_words = Json::decode($search_words);
        return array_map(function ($search_word) {
            $search_word['url'] = MUtils::getUsableAppLink($search_word['url']);
            return $search_word;
        }, $search_words);
    }

    /**
     * 获取热搜词
     *
     * @return array|mixed
     */
    public static function getHotSearchWords()
    {
        $hot_search_info = Yii::$app->redis->get(KEY_HOT_SEARCH);
        if (!$hot_search_info) {
            return [];
        }
        $hot_search_words = Json::decode($hot_search_info) ?: [];
        $SHOW_COUNT = 10;
        if (count($hot_search_words) > $SHOW_COUNT) {
            // 截取前 10 个热搜词
            $hot_search_words = array_slice($hot_search_words, 0, $SHOW_COUNT);
        }
        return $hot_search_words;
    }

    /**
     * 获取定时上下线的版头图
     *
     * @param int $client 设备类型
     * @param int|array $module_types 模块类型
     * @param bool $is_online 是否只获取未自动下线版头图
     * @return array 版头图组成的数组
     * @todo 后续需要优化获取逻辑，将所有上线及待上线（缓存有效期内）数据查出放入缓存，在使用缓存处过滤数据
     */
    public static function getOnlineOrMaxEndTimeElements(int $client, $module_types, bool $is_online = false,
            int $module_id = self::MODULE_ID_NONE): array
    {
        if ($client === Equipment::HarmonyOS) {
            $client = Equipment::Android;
        }
        $query = MRecommendedElements::find()
            ->where(['client' => $client,
                'module_id' => $module_id,
                'module_type' => $module_types,
                'archive' => self::ARCHIVE_ONLINE]);
        if ($is_online) {
            $elements = $query
                ->andWhere('start_time <= :now_time AND end_time > :now_time')
                ->params([':now_time' => $_SERVER['REQUEST_TIME']])
                // 先按 sort 升序展示，sort 一致时后创建的 banner 先展示
                ->orderBy(['sort' => SORT_ASC, 'create_time' => SORT_DESC])
                ->all();
        } else {
            // Banner 都自动下线时，取最后自动下线的一张图展示，若最后自动下线有多张，取排序靠前的一张
            $LIMIT = 1;
            $elements = $query
                ->andWhere('start_time < :now_time', [':now_time' => $_SERVER['REQUEST_TIME']])
                ->orderBy(['end_time' => SORT_DESC, 'sort' => SORT_ASC])
                ->limit($LIMIT)->all();
        }
        if (!empty($elements)) {
            $elements = array_map(function ($item) {
                return [
                    'id' => $item->id,
                    // 该值会被缓存，故此处不做根据客户端类型和版本号转化 App 跳转链接为可用链接处理（在使用该值的时候再进行处理）
                    'url' => $item->url,
                    'pic' => $item->cover,
                    'type' => $item->summary,
                    'sort' => $item->sort,
                    'module_type' => $item->module_type,
                    'start_time' => $item->start_time,
                    'end_time' => $item->end_time,
                    'element_type' => $item->element_type,
                    'element_id' => $item->element_id,
                    'more' => $item->more,
                ];
            }, $elements);
        }
        return $elements;
    }

    /**
     * 获取定时上下线的轮播通栏图
     *
     * @param int $client 设备类型
     * @return array 轮播通栏图组成的数组
     */
    public static function getOnlineElements(int $client): array
    {
        if ($client === Equipment::HarmonyOS) {
            $client = Equipment::Android;
        }
        $query = MRecommendedElements::find()
            ->where(['client' => $client,
                'module_id' => self::MODULE_POSITION_EXTRA_BANNER,
                'module_type' => MRecommendedElements::MODULE_TYPE_EXTRA_BANNER,
                'archive' => self::ARCHIVE_ONLINE]);
        $elements = $query
            ->andWhere('start_time <= :now_time AND end_time > :now_time')
            ->params([':now_time' => $_SERVER['REQUEST_TIME']])
            // TODO: 之后考虑删除
            // 安卓 5.5.0 以及之前的版本不是按位置 key 来展示，需要先按位置 (module_id) 排序
            ->orderBy(['module_id' => SORT_ASC, 'sort' => SORT_ASC])
            ->all();
        $data = [];
        foreach ($elements as $k => $item) {
            $data[$item->module_id][] = [
                'id' => $item->id,
                // 该值会被缓存，故此处不做根据客户端类型和版本号转化 App 跳转链接为可用链接处理（在使用该值的时候再进行处理）
                'url' => $item->url,
                'pic' => $item->cover,
                'type' => $item->summary,
                'sort' => $item->sort,
                'position' => $item->module_id,
                'start_time' => $item->start_time,
                'end_time' => $item->end_time
            ];
        }
        return $data;
    }

    /**
     * 获取最近要上线的 App 版头图或轮播通栏图的开始时间
     *
     * @param integer $client 设备类型 0：全部设备；1：Android；2：iOS
     * @param integer $module_type 模块类型 1：版头图；7：轮播通栏图
     * @param integer|array $module_id 模块 ID
     * @return object App 版头图或轮播通栏图组成的对象数组
     */
    public static function getNextElementStartTime(int $client, int $module_type, $module_id)
    {
        if ($client === Equipment::HarmonyOS) {
            $client = Equipment::Android;
        }
        $start_time = (int)MRecommendedElements::find()
            ->select(['start_time'])
            ->where(['client' => $client,
                'module_id' => $module_id,
                'module_type' => $module_type,
                'archive' => self::ARCHIVE_ONLINE])
            ->andWhere('start_time > :now_time')
            ->params([':now_time' => $_SERVER['REQUEST_TIME']])
            ->orderBy(['start_time' => SORT_ASC, 'sort' => SORT_ASC])
            ->scalar();
        return $start_time;
    }

    /**
     * 获取 App 版头图或轮播通栏图缓存时长
     *
     * @param integer $client 设备类型 0：全部设备；1：Android；2：iOS
     * @param integer $module_type 模块类型 1：版头图；7：轮播通栏图
     * @param integer|array $module_id 模块 ID
     * @param array $links App 版头图或轮播通栏图的数据
     * @throws Exception
     * @return integer 缓存时长
     */
    public static function getDuration(int $client, int $module_type, $module_id, array $links)
    {
        if (empty($links)) {
            throw new Exception('参数错误');
        }
        if ($client === Equipment::HarmonyOS) {
            $client = Equipment::Android;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if (in_array($module_type, self::BANNER_MODULE_TYPES)) {
            // 获取最近 banner 要下线的时间
            $end_times = array_column($links, 'end_time');
        } elseif ($module_type === self::MODULE_TYPE_EXTRA_BANNER) {
            // TODO: 支持分区轮播通栏
            $end_times = [];
            // 获取最近轮播通栏要下线的时间
            foreach ($links as $items) {
                foreach ($items as $vv) {
                    $end_times[] = $vv['end_time'];
                }
            }
        } else {
            throw new Exception('参数错误');
        }
        $next_start_time = $min_end_time = min($end_times);
        // 获取最近要上线的 App 版头图或轮播通栏图
        $start_time = MRecommendedElements::getNextElementStartTime($client, $module_type, $module_id);
        if ($start_time) {
            $next_start_time = $start_time;
        }
        $duration = min($next_start_time, $min_end_time) - $time;
        if ($duration <= 0) {
            // 避免设置负数值，负数值 MemCache Set 会转换成永久不过期
            // 设置 1 秒的过期时间，避免在缓存失效的那个时间点产生调用峰值的问题
            $duration = 1;
        } elseif ($duration > HALF_MINUTE) {
            // FIXME: App 版头图或轮播通栏图定时上线不及时，之后需要修复
            $duration = HALF_MINUTE;
        }
        return $duration;
    }
}
