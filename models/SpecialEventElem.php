<?php

namespace app\models;

use app\components\util\MUtils;
use Yii;

/**
 * This is the model class for table "special_event_elem".
 *
 * @property int $event_id 活动 ID
 * @property int $elem_id 元素 ID
 * @property int $elem_type 元素类型（1 为剧集）
 * @property string $intro 宣传介绍
 * @property string $url 入口链接
 * @property string $create_time 创建时间
 * @property string $modified_time 修改时间
 */
class SpecialEventElem extends ActiveRecord
{
    const ELEM_TYPE_SOUND = 0;
    const ELEM_TYPE_DRAMA = 1;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'special_event_elem';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['event_id', 'elem_id', 'elem_type'], 'required'],
            [['event_id', 'elem_id', 'elem_type'], 'integer'],
            [['intro', 'url'], 'string', 'max' => 255],
            [['elem_id', 'elem_type'], 'unique', 'targetAttribute' => ['elem_id', 'elem_type']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'event_id' => '活动 ID',
            'elem_id' => '元素 ID',
            'elem_type' => '元素类型（1 为剧集）',
            'intro' => '宣传介绍',
            'url' => '入口链接',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    /**
     *
     * @param int $elem_id
     * @param int $elem_type
     * @return null|ActiveRecord
     */
    public static function getEventIntro(int $elem_id, int $elem_type = self::ELEM_TYPE_DRAMA)
    {
        return self::find()->select('event_id, intro, url')
            ->where(['elem_id' => $elem_id, 'elem_type' => $elem_type])->one();
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->url) {
            $this->url = MUtils::getUsableAppLink($this->url);
        }
    }
}
