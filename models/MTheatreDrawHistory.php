<?php

namespace app\models;

use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;

/**
 * This is the model class for table "m_theatre_draw_history".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间，单位：秒
 * @property int $modified_time 更新时间，单位：秒
 * @property int $user_id 用户 ID
 * @property int $sound_id 音频 ID
 */
class MTheatreDrawHistory extends ActiveRecord
{
    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_theatre_draw_history';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['user_id', 'sound_id'], 'required'],
            [['create_time', 'modified_time', 'user_id', 'sound_id'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间',  // 单位：秒
            'modified_time' => '更新时间',  // 单位：秒
            'user_id' => '用户 ID',
            'sound_id' => '音频 ID',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 添加抽取盲盒历史
     *
     * @param int $user_id
     * @param int $sound_id
     */
    public static function addHistory(int $user_id, int $sound_id)
    {
        try {
            $exists = self::find()->where('user_id = :user_id AND sound_id = :sound_id',
                [':user_id' => $user_id, ':sound_id' => $sound_id])->exists();
            if ($exists) {
                return;
            }
            $model = new self();
            $model->user_id = $user_id;
            $model->sound_id = $sound_id;
            if (!$model->save()) {
                throw new Exception(MUtils2::getFirstError($model));
            }
        } catch (\yii\db\Exception $e) {
            if (!MUtils2::isUniqueError($e, self::getDb())) {
                // 非唯一索引错误抛出的异常时，记录日志
                Yii::error('抽取盲盒添加历史记录失败：' . $e->getMessage(), __METHOD__);
            }
            // PASS: 不抛出异常避免影响用户抽取盲盒
        } catch (Exception $e) {
            Yii::error('抽取盲盒添加历史记录失败：' . $e->getMessage(), __METHOD__);
            // PASS: 不抛出异常避免影响用户抽取盲盒
        }
    }

    /**
     * 获取用户已抽到的盲盒剧场音频 ID
     *
     * @param int $user_id
     * @return array
     */
    public static function getUserDrawSoundIds(int $user_id): array
    {
        return self::find()->select('sound_id')
            ->where('user_id = :user_id', [':user_id' => $user_id])
            ->column();
    }
}
