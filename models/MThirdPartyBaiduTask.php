<?php

namespace app\models;

use Exception;
use missevan\util\MUtils;
use Yii;
use yii\helpers\Json;

class MThirdPartyBaiduTask
{
    // 合作方回调接口文档：https://info.bilibili.co/pages/viewpage.action?pageId=958111694
    const CALLBACK_URL_BAIDU = 'https://eopa.baidu.com/api/task/external/1306/complete';

    // 生成百度回调接口签名密钥
    const BAIDU_SIGN_KEY = 'ZF1pwZw+uX0iQ7sbMEBCLjYyl2s8SkXfGpPVJxdvOOs=';

    // 回调成功响应 code
    const BAIDU_REQUEST_SUCCESS_CODE = 0;

    /**
     * 生成唯一请求标识
     *
     * @return string
     * @throws \Exception
     */
    protected static function buildRequestId(): string
    {
        return MUtils::randomKeys(5, 7);
    }

    /**
     * 请求百度回调
     *
     * @param string $track_id
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function callback(string $track_id): bool
    {
        $params = [
            'requestId' => self::buildRequestId(),
            'token' => $track_id,
        ];
        $params['sign'] = self::buildSign($params);
        try {
            $data = Yii::$app->tools->requestRemote(self::CALLBACK_URL_BAIDU, [], 'POST', Json::encode($params), 0, [
                'Content-Type' => 'application/json',
            ]);
            if (!$data) {
                throw new Exception('百度点击回传失败，返回值为空');
            }
            if ($data['errno'] !== self::BAIDU_REQUEST_SUCCESS_CODE) {
                throw new Exception(sprintf('code[%d], msg[%s]', $data['errno'], $data['errmsg']));
            }
        } catch (Exception $e) {
            Yii::error('百度点击回传失败，原因：' . $e->getMessage(), __METHOD__);
            return false;
        }
        return true;
    }

    /**
     * 获取百度方回调接口需要的签名
     *
     * @param array $params 生成签名的参数
     * @return string 签名
     */
    protected static function buildSign(array $params): string
    {
        ksort($params);
        $str_to_sign = '';
        $last_key = array_key_last($params);
        foreach ($params as $k => $v) {
            $str_to_sign .= "{$k}={$v}";
            if ($k !== $last_key) {
                $str_to_sign .= '&';
            }
        }
        return hash_hmac('sha256', $str_to_sign, self::BAIDU_SIGN_KEY);
    }
}
