<?php

namespace app\models;

use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\db\Expression;

/**
 * This is the model class for table "m_drama_reward_ranks".
 *
 * @property integer $id 主键 ID
 * @property integer $create_time 创建时间
 * @property integer $modified_time 更新时间
 * @property integer $drama_id 剧集 ID
 * @property integer $total_coin 打赏总钻石（单位：钻）
 * @property integer $type 榜单类型（1: 周榜；2: 月榜；3: 总榜）
 */
class MDramaRewardRanks extends ActiveRecord
{
    // 剧集打赏榜单类型（1: 周榜；2: 月榜；3: 总榜）
    const TYPE_RANK_ALL = 3;

    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_drama_reward_ranks';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['create_time', 'modified_time', 'drama_id', 'total_coin', 'type'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'drama_id' => '剧集 ID',
            'total_coin' => '打赏总钻石',  // 单位：钻
            'type' => '榜单类型',  // 1: 周榜；2: 月榜；3: 总榜
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 获取剧集打赏榜
     *
     * @param int $count 榜单数量
     * @return array
     */
    public static function getRankAllList(int $count)
    {
        $dramas = self::find()
            ->select('drama_id, total_coin AS coin, modified_time AS ctime')
            ->where(['type' => self::TYPE_RANK_ALL])
            ->orderBy('total_coin DESC')
            ->limit($count)
            ->asArray()->all();
        return array_map(function ($item) {
            return [
                'drama_id' => (int)$item['drama_id'],
                'coin' => (int)$item['coin'],
                'ctime' => (int)$item['ctime'],
            ];
        }, $dramas);
    }

    /**
     * 更新打赏总榜数据
     *
     * @param int $drama_id 剧集 ID
     * @param int $coin 钻石数
     * @throws Exception
     */
    public static function addRankAll(int $drama_id, int $coin)
    {
        try {
            // 若有打赏数据则更新打赏总钻石数，没有打赏数据（剧集为新的打赏剧集时）则插入新数据
            $updated_count = self::updateAll(
                ['total_coin' => new Expression('total_coin + :coin', [':coin' => $coin]),
                    'modified_time' => $_SERVER['REQUEST_TIME']],
                ['drama_id' => $drama_id, 'type' => self::TYPE_RANK_ALL]);
            if ($updated_count === 0) {
                $model = new self();
                $model->type = self::TYPE_RANK_ALL;
                $model->drama_id = $drama_id;
                $model->total_coin = $coin;
                if (!$model->save()) {
                    throw new Exception(MUtils2::getFirstError($model));
                }
            }
        } catch (Exception $e) {
            if (MUtils2::isUniqueError($e, self::getDb())) {
                // 并发插入时，由于联合唯一索引的原因，只会有一条数据插入成功，插入失败的数据需要使用 UPDATE 语句更新
                try {
                    self::updateAll(
                        ['total_coin' => new Expression('total_coin + :coin', [':coin' => $coin]),
                            'modified_time' => $_SERVER['REQUEST_TIME']],
                        ['drama_id' => $drama_id, 'type' => self::TYPE_RANK_ALL]);
                } catch (Exception $e) {
                    // 将异常记录到日志，不抛出异常，避免客户端直接报错
                    Yii::error(sprintf('更新打赏总榜数据失败：%s', $e->getMessage()), __METHOD__);
                }
            } else {
                // 将除了唯一索引错误以外的异常记录到日志，不抛出异常，避免客户端直接报错
                Yii::error(sprintf('更新或插入打赏总榜数据失败：%s', $e->getMessage()), __METHOD__);
            }
        }
    }
}
