<?php

namespace app\models;

use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "first_listen_log".
 *
 * @property int $id 主键
 * @property int $user_id 用户 ID
 * @property int $work_id 作品 ID
 * @property string $date 收听日期
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 */
class FirstListenLog extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'first_listen_log';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db2');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'work_id'], 'integer'],
            [['id'], 'unique']
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'user_id' => '用户 ID',
            'work_id' => '作品 ID',
            'date' => '收听日期',
            'create_time' => '创建时间',
            'modified_time' => '更新时间'
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $date = date('Y-m-d', $time);
            $this->create_time = $time;
            $this->date = $date;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 新增用户每日首播日志
     *
     * @param int $work_id 作品 ID
     * @param int $user_id 用户 ID
     * @return bool 日志是否新增成功
     */
    public static function addFirstListenLog(int $work_id, int $user_id): bool
    {
        try {
            $log = new self([
                'work_id' => $work_id,
                'user_id' => $user_id
            ]);
            return $log->save();
        } catch (\Exception $e) {
            // 若出现异常，则表明该用户当天已经播放过该作品的语音卡
            return false;
        }
    }
}
