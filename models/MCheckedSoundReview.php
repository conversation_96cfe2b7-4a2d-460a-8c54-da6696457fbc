<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "m_checked_sound_review".
 *
 * @property int ID
 * @property int $sound_id 单音 ID
 * @property int $user_id 用户 ID
 * @property string $soundstr 单音标题
 * @property string $intro 简介
 * @property string $cover_image 封面图片
 * @property int $source 来源 0：搬运，1：原创
 * @property int $download 是否允许下载 0：允许，1：禁止
 * @property int $catalog_id 分类 ID
 * @property int $animationid 作品 ID
 * @property int $characterid 角色 ID
 * @property int $seiyid 声优 ID
 * @property string $tags 标签
 * @property int $create_time 创建时间
 * @property int $last_update_time 最后更新时间
 * @property int $front_cover 封面图片完整地址
 */
class MCheckedSoundReview extends ActiveRecord
{
    public $front_cover;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_checked_sound_review';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [
                [
                    'sound_id',
                    'user_id',
                    'soundstr',
                    'intro',
                    'cover_image',
                    'catalog_id',
                    'animationid',
                    'characterid',
                    'seiyid',
                    'create_time',
                    'last_update_time'
                ],
                'required'
            ],
            [
                [
                    'sound_id',
                    'user_id',
                    'source',
                    'download',
                    'catalog_id',
                    'animationid',
                    'characterid',
                    'seiyid',
                    'create_time',
                    'last_update_time'
                ],
                'integer'
            ],
            [['intro'], 'string'],
            [['soundstr'], 'string', 'max' => 100],
            [['cover_image', 'tags'], 'string', 'max' => 255],
            [['sound_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'sound_id' => '单音 ID',
            'user_id' => '用户 ID',
            'soundstr' => '单音标题',
            'intro' => '简介',
            'cover_image' => '封面图片',
            'source' => '来源',  // 0：搬运，1：原创
            'download' => '是否允许下载',  // 0：允许，1：禁止
            'catalog_id' => '分类 ID',
            'animationid' => '作品 ID',
            'characterid' => '角色 ID',
            'seiyid' => '声优 ID',
            'tags' => '标签',
            'create_time' => '创建时间',
            'last_update_time' => '最后更新时间',
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        $this->front_cover = Yii::$app->params['defaultCoverUrl'];
        // 音频封面
        if ($this->cover_image) {
            $this->front_cover = Yii::$app->params['coverUrl'] . $this->cover_image;
        }

    }

    /**
     * 获取再审状态中音频的信息
     *
     * @param array $sounds 当前音频信息
     */
    public static function showReviewSounds(array &$sounds)
    {
        if (empty($sounds)) {
            return;
        }
        $sound_ids = array_column($sounds, 'id');
        $checked_sounds = MCheckedSoundReview::find()
            ->select('sound_id, soundstr, cover_image')
            ->where(['sound_id' => $sound_ids])
            ->all();
        foreach ($checked_sounds as $checked_sound) {
            foreach ($sounds as &$sound) {
                if ($sound['id'] === $checked_sound->sound_id) {
                    $sound['soundstr'] = $checked_sound->soundstr;
                    $sound['front_cover'] = $checked_sound->front_cover;
                    $sound['checked'] = MSound::CHECKED_UNPASS;
                    break;
                }
            }
        }
    }
}
