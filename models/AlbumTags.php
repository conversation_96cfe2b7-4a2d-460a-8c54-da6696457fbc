<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "album_tags".
 *
 * @property integer $id
 * @property string $tags
 */
class AlbumTags extends ActiveRecord
{
    public $tags;
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'album_tags';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id'], 'required'],
            [['id'], 'integer'],
            [['tags'], 'string'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'tags' => 'Tags',
        ];
    }
}
