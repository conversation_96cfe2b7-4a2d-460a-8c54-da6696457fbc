<?php

namespace app\models;

use Exception;
use Yii;

/**
 * This is the model class for table "bili_vip_maoer_benefit_menu".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间（秒级时间戳）
 * @property int $modified_time 修改时间（秒级时间戳）
 * @property int $delete_time 删除时间（秒级时间戳，0 为非删除状态）
 * @property int $benefit_type 权益类型：1 有赞商城优惠券
 * @property array|null $more 更多详情
 *
 * @property-read int $daily_stock_limit 日库存限制
 */
class BiliVipMaoerBenefitMenu extends ActiveRecord
{
    use ActiveRecordTrait;

    // 权益类型
    const BENEFIT_TYPE_YOUZAN_MALL_COUPON = 1;  // 有赞商城优惠券

    // 没有库存限制
    const STOCK_NO_LIMIT = -1;

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->db;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'bili_vip_maoer_benefit_menu';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['delete_time', 'benefit_type'], 'required'],
            [['create_time', 'modified_time', 'delete_time', 'benefit_type'], 'integer'],
            [['more'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间（秒级时间戳）',
            'modified_time' => '修改时间（秒级时间戳）',
            'delete_time' => '删除时间（秒级时间戳）',  // 0 为非删除状态
            'benefit_type' => '权益类型',  // 1. 有赞商城优惠券
            'more' => '更多详情',
        ];
    }

    public function afterFind()
    {
        if (!$this->more) {
            $this->more = [];
        }
    }

    public static function getBenefitItem(int $benefit_type, int $benefit_id): self|null|array
    {
        return self::find()->where([
            'id' => $benefit_id,
            'benefit_type' => $benefit_type,
            'delete_time' => 0,
        ])->one();
    }

    public function getDaily_stock_limit()
    {
        if (!array_key_exists('daily_stock_limit', $this->more)) {
            Yii::error("未设置有赞优惠券权益日库存: {$this->id}", __METHOD__);
            return self::STOCK_NO_LIMIT;
        }

        return $this->more['daily_stock_limit'];
    }

    public function getStock()
    {
        switch ($this->benefit_type) {
            case self::BENEFIT_TYPE_YOUZAN_MALL_COUPON:
                $daily_stock_limit = $this->daily_stock_limit;
                if ($daily_stock_limit === self::STOCK_NO_LIMIT) {
                    return self::STOCK_NO_LIMIT;
                }

                $today_delivered_num = self::getTodayDeliveredNum($this->benefit_type, $this->id);
                return max($daily_stock_limit - $today_delivered_num, 0);
        }

        return self::STOCK_NO_LIMIT;
    }

    public function isOutOfStock(): bool
    {
        $stock = $this->getStock();
        return $stock <= 0 && $stock !== self::STOCK_NO_LIMIT;
    }

    public function deductStock(): self
    {
        if ($this->daily_stock_limit === self::STOCK_NO_LIMIT) {
            return $this;
        }
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_BILI_VIP_MAOER_BENEFIT_TODAY_DELIVERED_NUM, $this->benefit_type, $this->id);
        $redis->multi()
            ->incr($key)
            ->expire($key, strtotime('tomorrow midnight') - $_SERVER['REQUEST_TIME'])
            ->exec();

        return $this;
    }

    public static function getTodayDeliveredNum(int $benefit_type, int $benefit_id): int
    {
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_BILI_VIP_MAOER_BENEFIT_TODAY_DELIVERED_NUM, $benefit_type, $benefit_id);
        return (int)$redis->get($key);
    }

    public function sendDeliverNotice(BiliVipMaoerUserBenefit $user_benefit): void
    {
        if (!array_key_exists('sys_notice', $this->more)) {
            return;
        }
        MMessageAssign::sendSysMsg($user_benefit->maoer_uid,
            $this->more['sys_notice']['title'],
            $this->more['sys_notice']['content']
        );
    }

}
