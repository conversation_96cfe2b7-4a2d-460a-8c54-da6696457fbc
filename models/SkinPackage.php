<?php

namespace app\models;

use app\components\util\Equipment;
use missevan\storage\StorageClient;
use Yii;

/**
 * This is the model class for table "skin_package".
 *
 * @property int $id 主键
 * @property string $url 皮肤包地址
 * @property string $screen 屏幕尺寸
 * @property int $os 设备类型（1 安卓，2 iOS）
 * @property int $work_id 所属作品 ID
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 */
class SkinPackage extends ActiveRecord
{

    const ANDROID_SCREEN_XXXHDPI_WIDTH = 1440;
    const ANDROID_SCREEN_XXHDPI_WIDTH = 1080;
    const ANDROID_SCREEN_XHDPI_WIDTH = 720;
    const ANDROID_SCREEN_HDPI_WIDTH = 480;
    const ANDROID_SCREEN_MHDPI_WIDTH = 360;

    const ANDROID_SCREEN = [
        self::ANDROID_SCREEN_XXXHDPI_WIDTH => '4x',
        self::ANDROID_SCREEN_XXHDPI_WIDTH => '3x',
        self::ANDROID_SCREEN_XHDPI_WIDTH => '2x',
        self::ANDROID_SCREEN_HDPI_WIDTH => '1.5x',
        self::ANDROID_SCREEN_MHDPI_WIDTH => '1x',
    ];

    const IOS_SCREEN_HIGHEST = '3x';
    const ANDROID_SCREEN_ALL = 'all';
    // Android >= 5.5.3 屏幕尺寸
    const ANDROID_SCREEN_NEW_ALL = 'new_all';

    // WORKAROUND: 安卓 5.7.9 以下版本返回的固定皮肤包地址，解决客户端更改旧播放器的进度更新逻辑后新的皮肤包不适用问题
    // iOS 4.6.5 以下版本返回的固定皮肤包地址，解决由于鉴权参数 auth_key 每次会变，导致这些版本每次请求都会更新皮肤包问题
    // Android < 5.7.9 剑网三皮肤包地址
    const ANDROID_OLD_SKIN_URL_JIANWANG3 = 'voice://voice/work/5/skin/2021-07-01/os-1-new_all-60dd3af2bb83d/skin.zip';
    // Android < 5.7.9 全职高手皮肤包地址
    const ANDROID_OLD_SKIN_URL_QUANZHI = 'voice://voice/work/1/skin/2021-07-13/os-1-new_all-60ed691db95ee/skin.zip';
    // iOS < 4.6.5 剑网三皮肤包地址
    const IOS_OLD_SKIN_URL_JIANWANG3 = 'oss://voice/work/5/skin/2019-11-07/os-2-3x-5dc3cb40d3731/skin.zip';
    // iOS < 4.6.5 全职高手皮肤包地址
    const IOS_OLD_SKIN_URL_QUANZHI = 'oss://voice/work/1/skin/2019-09-19/os-2-3x-5d831977b3d7b/skin.zip';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'skin_package';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db2');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['os', 'work_id', 'create_time', 'modified_time'], 'integer'],
            [['work_id', 'create_time', 'modified_time'], 'required'],
            [['url'], 'string', 'max' => 255],
            [['screen'], 'string', 'max' => 20],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'url' => '皮肤包地址',
            'screen' => '屏幕尺寸',
            'os' => '设备类型（1 安卓，2 iOS）',
            'work_id' => '所属作品 ID',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
        ];
    }

    /**
     * 获取单个作品皮肤包
     *
     * @param integer $work_id 作品 ID
     * @param integer $os 设备类型
     * @param null|string $resolution 设备分辨率（例 1080x1920）
     * @return string|null
     */
    public static function getSkinPackage(int $work_id, int $os, ?string $resolution = null)
    {
        $packages = self::getSkinPackages($work_id, $os, $resolution);
        return $packages[$work_id] ?? null;
    }

    /**
     * 获取作品皮肤包
     *
     * @param integer|array $work_id 作品 ID
     * @param integer $os 设备类型
     * @param null|string $resolution 设备分辨率（例 1080x1920）
     * @return array 例 [1 => 'https://.../skin.zip', 2 => 'https://.../skin.zip']
     */
    public static function getSkinPackages($work_id, int $os, ?string $resolution = null)
    {
        // if (Equipment::Android === $os) {
        //     $width = (int)(explode('x', $resolution)[0] ?? self::ANDROID_SCREEN_XXXHDPI_WIDTH);
        //     if ($width >= self::ANDROID_SCREEN_XXXHDPI_WIDTH) {
        //         $screen = self::ANDROID_SCREEN[self::ANDROID_SCREEN_XXXHDPI_WIDTH];
        //     } elseif ($width >= self::ANDROID_SCREEN_XXHDPI_WIDTH) {
        //         $screen = self::ANDROID_SCREEN[self::ANDROID_SCREEN_XXHDPI_WIDTH];
        //     } elseif ($width >= self::ANDROID_SCREEN_XHDPI_WIDTH) {
        //         $screen = self::ANDROID_SCREEN[self::ANDROID_SCREEN_XHDPI_WIDTH];
        //     } elseif ($width >= self::ANDROID_SCREEN_HDPI_WIDTH) {
        //         $screen = self::ANDROID_SCREEN[self::ANDROID_SCREEN_HDPI_WIDTH];
        //     } else {
        //         $screen = self::ANDROID_SCREEN[self::ANDROID_SCREEN_MHDPI_WIDTH];
        //     }
        // }

        if ($os === Equipment::HarmonyOS) {
            $os = Equipment::Android;
        }
        if (Equipment::isAppOlderThan('4.6.5', '5.7.9')) {
            if (!is_array($work_id)) {
                $work_id = [$work_id];
            }
            $urls = [];
            foreach ($work_id as $k => $v) {
                $url = '';
                switch ($v) {
                    case Work::ID_JIANWANG3:
                        $url = ($os === Equipment::Android)
                            ? StorageClient::getFileUrl(self::ANDROID_OLD_SKIN_URL_JIANWANG3)
                            : StorageClient::getFileUrl(self::IOS_OLD_SKIN_URL_JIANWANG3);
                        break;
                    case Work::ID_QUANZHI:
                        $url = ($os === Equipment::Android)
                            ? StorageClient::getFileUrl(self::ANDROID_OLD_SKIN_URL_QUANZHI)
                            : StorageClient::getFileUrl(self::IOS_OLD_SKIN_URL_QUANZHI);
                        break;
                }
                $urls[$v] = $url;
            }
            return $urls;
        } else {
            $screen = ($os === Equipment::Android) ? self::ANDROID_SCREEN_NEW_ALL : self::IOS_SCREEN_HIGHEST;
            $zip_array = self::find()
                ->select('url')
                ->where(['work_id' => $work_id, 'screen' => $screen, 'os' => $os])
                ->indexBy('work_id')
                ->column();
            return array_map(function ($v) {
                return StorageClient::getFileUrl($v);
            }, $zip_array);
        }
    }
}
