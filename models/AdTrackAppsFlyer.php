<?php

namespace app\models;

use app\components\util\Equipment;
use missevan\util\MUtils as MUtils2;

class AdTrackAppsFlyer extends AdTrack implements AdTrackInterface
{

    public function callback(string $event_type, $arg = 0)
    {
        return true;
    }

    public function getAdVendor(): int
    {
        return self::VENDOR_APPSFLYER;
    }

    public static function newRecord(InstallLog $install_log, AppsFlyerBody $body)
    {
        $more = [
            'stage' => self::CONVERT_STAGE_ACTIVATE,
            'activate_time' => $install_log->create_time,
            'media_source' => $body->media_source,
            'af_channel' => $body->af_channel,
            'creative_name' => $body->af_ad,
            'group_name' => $body->af_adset,
            'project_name' => $body->campaign,
        ];
        if ($install_log->isIOSApp() && $install_log->adid) {
            $more['idfa_md5'] = md5(strtoupper($install_log->adid));
        }
        $record = new AdTrackAppsFlyer([
            'track_id' => $body->appsflyer_id,
            'os' => $install_log->device_type,
            'idfa' => $install_log->adid ?: '',
            'ip' => $install_log->ip,
            'ua' => $install_log->user_agent,
            'buvid' => $install_log->buvid,
            'equip_id' => $install_log->equip_id,
            'converted' => AdTrack::CONVERTED,
            'creative_id' => $body->af_ad_id ?: '',
            'group_id' => $body->af_adset_id ?: '',
            'project_id' => $body->af_c_id ?: '',
            'more' => $more,
            'click_time' => $body->getEventTimestamp() * 1000,
        ]);
        $record->setCurrentTableName(self::tableName());
        return $record;
    }

    /**
     * 是否启用 AppsFlyer SDK 上报
     * @deprecated 业务不再使用 AF 进行海外渠道投放，待客户端移除 AF SDK 后去除 AF 代码
     *
     * @link https://info.bilibili.co/pages/viewpage.action?pageId=528714255
     * @param Equipment $equip
     * @return bool
     */
    public static function isEnabled(Equipment $equip): bool
    {
        // WORKAROUND: 新版本（Android Google 渠道 >= 6.1.0，或 iOS >= 6.1.1），且为海外 IP 时，则客户端启用 AppsFlyer SDK 上报
        return (
            ($equip->isAndroid() && Equipment::isFromGoogleChannel() && !Equipment::isAppOlderThan(null, '6.1.0'))
            || ($equip->isIOS() && !Equipment::isAppOlderThan('6.1.1', null))
        ) && !MUtils2::isChinaMainland();
    }

}
