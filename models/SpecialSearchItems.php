<?php

namespace app\models;

use app\components\util\Equipment;
use app\components\util\Go;
use app\components\util\MUtils;
use missevan\storage\StorageClient;
use Yii;

/**
 * This is the model class for table "special_search_items".
 *
 * @property int $id
 * @property integer $create_time 创建时间
 * @property integer $modified_time 更新时间
 * @property integer $delete_time 删除时间
 * @property string $title 主标题
 * @property string $search_words 搜索词
 * @property string $drama_ids 关联剧集
 * @property string $url 链接
 * @property integer $ip_id 关联周边 IP ID
 * @property string $background 背景图
 * @property string $cover 封面图
 * @property integer $start_time 上线时间
 * @property string $color 主题色
 * @property integer $status 状态
 * @property integer $type 类型
 */
class SpecialSearchItems extends ActiveRecord
{
    // 类型 0：搜索特型库；1：搜索专题卡
    const TYPE_SEARCH_SPECIAL = 0;
    const TYPE_SEARCH_TOPIC = 1;

    public static function getDb()
    {
        return Yii::$app->get('db1');
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'special_search_items';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['search_words', 'create_time', 'modified_time'], 'required'],
            [['ip_id', 'start_time', 'status', 'type', 'create_time', 'modified_time', 'delete_time'], 'integer'],
            [['background', 'cover', 'url'], 'string', 'max' => 255],
            [['title', 'search_words'], 'string', 'max' => 100],
            [['color'], 'string', 'max' => 50],
            [['title'], 'string', 'max' => 30],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'delete_time' => '删除时间',
            'title' => '主标题',
            'search_words' => '搜索词',  // 多个搜索词用半角逗号分隔
            'drama_ids' => '关联剧集',  // 多个剧集 ID 用半角逗号分隔
            'url' => '链接',
            'ip_id' => '关联周边 IP ID',
            'background' => '背景图',
            'cover' => '封面图',
            'start_time' => '上线时间',
            'color' => '主题色',
            'status' => '状态',  // 0：停用；1：生效
            'type' => '类型',  // 0：搜索特型库；1：搜索专题卡
        ];
    }

    /**
     * 搜索专题卡
     *
     * @param string|integer $s 关键词
     * @param integer $page 第几页
     * @param integer $page_size 每页个数
     * @return array
     * @throws
     */
    public static function getSearchTopicCard($s, int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        $results = Yii::$app->go->search($s, Discovery::SEARCH_TOPIC_CARD, $page, $page_size, ['scenario' => Go::SCENARIO_MAIN_SEARCH]);
        if (empty($results['Datas'])) {
            return [];
        }
        $data = current($results['Datas']);
        $drama_ids = $data['drama_ids'];
        // 下发搜索专题卡信息时至少需要关联的剧集数量
        $MIN_DRAMA_NUM = 2;
        if (!$drama_ids) {
            Yii::error("搜索专题卡 {$data['id']} 未配置关联剧集", __METHOD__);
            return [];
        }
        $drama_ids = array_map('intval', explode(',', $drama_ids));
        if (count($drama_ids) < $MIN_DRAMA_NUM) {
            Yii::error("搜索专题卡 {$data['id']} 配置关联剧集不足 {$MIN_DRAMA_NUM} 部", __METHOD__);
            return [];
        }
        $dramas = Drama::getDramaByIds($drama_ids);
        if (empty($dramas) || count($dramas) < $MIN_DRAMA_NUM) {
            Yii::error("搜索专题卡 {$data['id']} 配置有效关联剧集不足 {$MIN_DRAMA_NUM} 部", __METHOD__);
            return [];
        }
        Drama::fillCornerMark($dramas, (int)Yii::$app->user->id);
        $items_search_topic = [
            'title' => $data['title'],
            'dramas' => $dramas,
            'background' => StorageClient::getFileUrl($data['background']),
            'cover' => StorageClient::getFileUrl($data['cover']),
            'start_time' => (int)$data['start_time'],
            'color' => $data['color'],
            'ops_request_misc' => $results['ops_request_misc'],
        ];
        if ($data['ip_id'] && $derivatives = self::getDerivatives($data['ip_id'])) {
            $items_search_topic['derivatives'] = $derivatives;
        }
        if ($data['url']) {
            $items_search_topic['url'] = $data['url'];
        }
        $view_count_total = array_reduce($dramas, function ($ret, $item) {
            $ret += (int)$item['view_count'];
            return $ret;
        }, 0);
        $items_search_topic['view_count_total'] = MUtils::formatNumber($view_count_total);
        // 福袋展示逻辑：当专题卡绑定 IPR 时展示该 IPR 福袋信息；当专题卡未绑定 IPR 时按照剧集在专题卡上露出顺序展示第一个福袋信息
        $live_lucky_bag = Drama::getDramaLuckybag($drama_ids, $data['ip_id']);
        if ($live_lucky_bag) {
            $items_search_topic['live_lucky_bag'] = $live_lucky_bag;
        }

        return $items_search_topic;
    }

    /**
     * 搜索特型卡片、UP 主卡片和游戏卡片
     *
     * @param string $keyword 关键词
     * @param int $user_id 用户 ID
     * @param int $page 页数
     * @return array 例：['special_card' => ['data' => [], 'pagination' => []], 'up_card' => [], 'game_card' => []]
     * @todo 增加搜索专题卡的逻辑
     */
    public static function getSearchCard(string $keyword, int $user_id, int $page): array
    {
        $results = Yii::$app->go->searchCard($keyword, $user_id, $page);

        $up_card = null;
        $special_card = null;
        $game_card = null;
        if ($results) {
            if (isset($results['up_card'])) {
                if (!empty($results['up_card']['work_list'])) {
                    $drama_ids = [];
                    $TYPE_DRAMA = 2;
                    foreach ($results['up_card']['work_list'] as $attr) {
                        if ($attr['type'] === $TYPE_DRAMA) {
                            $drama_ids[] = $attr['drama_id'];
                        }
                    }
                    if (!empty($drama_ids)) {
                        // 获取剧集角标信息
                        $corner_mark_map = Drama::getDramaCornerMark($drama_ids, $user_id);
                        if ($corner_mark_map) {
                            foreach ($results['up_card']['work_list'] as &$attr) {
                                // 剧集作品需要有剧集角标
                                if ($attr['type'] === $TYPE_DRAMA && isset($corner_mark_map[$attr['drama_id']])) {
                                    $attr['corner_mark'] = $corner_mark_map[$attr['drama_id']];
                                }
                            }
                            unset($attr);
                        }
                    }
                }
                $up_card = $results['up_card'];
            }
            if (isset($results['special_card'])) {
                if (!empty($results['special_card']['data'])) {
                    foreach ($results['special_card']['data'] as &$attr) {
                        $attr['url'] = MUtils::getUsableAppLink($attr['url']);
                    }
                    unset($attr);
                }
                // WORKAROUND: iOS < 4.9.3 或 Android < 5.7.8，下发 Datas 字段
                if (Equipment::isAppOlderThan('4.9.3', '5.7.8')) {
                    $results['special_card']['Datas'] = $results['special_card']['data'];
                    unset($results['special_card']['data']);
                }
                $special_card = $results['special_card'];
            }
            if (isset($results['game_card'])) {
                $is_android = Yii::$app->equip->isAndroid();
                // 客户端操作系统类型：Android、iOS、HarmonyOS，仅支持 Android 下载
                $bit_mask_os = $is_android ? 1 << 0 : 1 << 1;
                // TODO: os 判断逻辑迁移到 missevan-go
                if (MUtils::bitIsSet($results['game_card']['show_on_os'], $bit_mask_os)) {
                    $game_card = $results['game_card'];
                    // WORKAROUND: 临时拼接 os 参数，后续在 missevan-go 处理
                    $game_card['download_url'] .= '&os=' . Yii::$app->equip->getOs();
                    unset($game_card['show_on_os']);
                    if (!$is_android) {
                        // HarmonyOS、iOS 不支持下载
                        unset($game_card['download_url'], $game_card['package_name'], $game_card['package_version_code']);
                    }
                }
            }
        }
        return [
            'special_card' => $special_card,
            'up_card' => $up_card,
            'game_card' => $game_card,
        ];
    }

    /**
     * 获取周边信息
     *
     * @param int $ip_id IP ID
     * @return array
     */
    public static function getDerivatives(int $ip_id)
    {
        $derivatives = Yii::$app->go->getDerivatives($ip_id);
        if (empty($derivatives)) {
            return [];
        }
        return Drama::getDerivativeInfo($derivatives);
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->url) {
            $this->url = MUtils::getUsableAppLink($this->url);
        }
    }
}
