<?php

namespace app\models;

use app\components\util\Equipment;
use Exception;
use Yii;

/**
 * This is the model class for table "transaction_log_detail".
 *
 * @property int $id transaction_log.id
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 * @property string $ip IP 地址
 * @property int $os 设备类型: 1 为 Android; 2 为 iOS；3 为 Web
 * @property string $user_agent User-Agent
 * @property string $equip_id 设备号
 * @property string $buvid 唯一设备标识
 * @property array $more 更多详情。e.g. {"user_hide_time":1678185990}
 * @property int user_hide_time 已购商品隐藏时间（单位：秒），为 -1 时表示官方隐藏。more.user_hide_time 的虚拟字段
 */
class TransactionLogDetail extends ActiveRecord
{
    const MAX_USER_AGENT_LENGTH = 500;

    // 官方隐藏订单记录（隐藏旧剧集同步到新剧集时创建的订单记录）
    const USER_HIDE_TIME_BACKEND = -1;
    // 用户未隐藏订单
    const USER_HIDE_TIME = 0;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'transaction_log_detail';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('paydb');
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'os'], 'required'],
            // WORKAROUND: rpc 相关调用地方都传递 IP 后开始验证
            // [['id', 'ip', 'os'], 'required'],
            [['id', 'create_time', 'modified_time', 'os'], 'integer'],
            [['more'], 'safe'],
            [['ip'], 'string', 'max' => 50],
            [['user_agent'], 'truncateUserAgent'],
            [['equip_id'], 'string', 'max' => 36],
            [['buvid'], 'string', 'max' => 64],
        ];
    }

    public function truncateUserAgent()
    {
        if (strlen($this->user_agent) > self::MAX_USER_AGENT_LENGTH) {
            $this->user_agent = substr($this->user_agent, 0, self::MAX_USER_AGENT_LENGTH);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'transaction_log.id',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'ip' => 'IP 地址',
            'os' => '设备类型',  // 1 为 Android; 2 为 iOS；3 为 Web；6 为 HarmonyOS
            'user_agent' => 'User-Agent',
            'equip_id' => '设备号',
            'buvid' => '唯一设备标识',
            'more' => '更多详情',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    public function init()
    {
        if (!$this->ip) {
            $this->ip = Yii::$app->request->userIP;
        }

        $equip = Yii::$app->equip;
        if (!$this->user_agent) {
            $this->user_agent = $equip->getUserAgent();
        }
        if ($equip->isFromApp()) {
            $this->os = $equip->getOs();
            if (is_null($this->equip_id)) {
                $this->equip_id = $equip->getEquipId();
            }
            if (is_null($this->buvid)) {
                $this->buvid = (string)$equip->getBuvid();
            }
        } else {
            $this->os = Equipment::Web;
            try {
                $equip_new = clone $equip;
                $equip_new->parseUserAgent($this->user_agent);
                if ($equip_new->isFromApp()) {
                    $this->os = $equip_new->getOs();
                }
            } catch (Exception $e) {
                Yii::error(sprintf('parse user_agent error: %s', $e->getMessage()), __METHOD__);
            }
        }
    }

    /**
     * 获取交易订单明细信息
     *
     * @param array $ids IDs
     * @return array|array[]
     */
    public static function newDetails(array $ids)
    {
        $time = $_SERVER['REQUEST_TIME'];
        $detail = new self();
        return array_map(function ($id) use ($detail, $time) {
            return [
                'id' => $id,
                'ip' => $detail['ip'],
                'os' => $detail['os'],
                'user_agent' => $detail['user_agent'],
                'equip_id' => $detail['equip_id'],
                'buvid' => $detail['buvid'],
                'create_time' => $time,
                'modified_time' => $time,
            ];
        }, $ids);
    }

    /**
     * 获取隐藏订单 SQL 条件
     *
     * @return string
     */
    public static function userHideConditionSql(): string
    {
        // 用户订单隐藏条件：设置了 user_hide_time 并且 user_hide_time 需要大于 0
        // user_hide_time 为 more.user_hide_time 的虚拟字段，用户自己隐藏商品时，会存入当时的时间戳
        return '(user_hide_time IS NOT NULL AND user_hide_time > ' . TransactionLogDetail::USER_HIDE_TIME . ')';
    }
}
