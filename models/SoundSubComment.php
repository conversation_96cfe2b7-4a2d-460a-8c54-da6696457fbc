<?php

namespace app\models;

use app\components\util\Go;
use app\components\util\MUtils;
use Yii;
use yii\db\Expression;

/**
 * This is the model class for table "sound_sub_comment".
 *
 * @property integer $id
 * @property string $comment_content
 * @property integer $comment_id
 * @property integer $ctime
 * @property integer $userid
 * @property string $username
 * @property integer $like_num
 * @property integer $floor
 * @property boolean $is_ad
 * @property integer $checked 评论属性，-1：软删除；1：正常评论；2：评论违规
 * @property integer $liked 是否点赞（0：否；1：是）
 * @property integer $disliked 是否点踩（0：否；1：是）
 * @property string $ip_detail 用户 IP 详情
 */
class SoundSubComment extends ActiveRecord
{
    const SCENARIO_ADD = 'add_sub_comment';

    // 子评论最小长度
    const MIN_LENGTH = 3;
    // 子评论最大长度
    const MAX_LENGTH = 2000;

    // 评论属性，-1：已被软删除，0：特殊值，获取全部评论时使用，1：正常评论，2：违规评论
    // TODO: 目前暂未使用软删除，之后需要补上
    const ATTR_DELETE = -1;
    const ATTR_All = 0;
    const CHECKED_COMMON = 1;
    const CHECKED_VIOLATION = 2;

    public $icon;
    public $authenticated;
    public $liked;  // 评论是否被用户点赞，类型为 int
    public $disliked;  // 评论是否被用户点踩，类型为 int
    public $is_blacklist;
    public $ip_location;
    // 用户信息
    public $user;

    // 是否为广告类型的评论
    protected $is_ad;

    /**
     * @inheritdoc
     */
    public static function getDb()
    {
        return Yii::$app->messagedb;
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'sound_sub_comment';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['comment_content', 'comment_id'], 'required'],
            [['comment_content'], 'string', 'max' => self::MAX_LENGTH],
            ['comment_content', 'checkText', 'on' => self::SCENARIO_ADD],
            [['comment_id', 'ctime', 'userid', 'like_num', 'floor', 'checked'], 'integer'],
            [['username'], 'string', 'max' => 20],
        ];
    }

    // 删除关联字段
    public function afterDelete()
    {
        CommentLike::deleteAll('cid = :cid AND sub = :sub', [
            ':cid' => $this->id,
            ':sub' => Commentnotice::TYPE_SUB_COMMENT
        ]);
        // 更新删除的子评论往后的评论 floor
        self::updateAll(['floor' => new Expression('GREATEST(floor, 1) - 1')],
            'comment_id = :comment_id AND floor > :floor',
            [':comment_id' => $this->comment_id, ':floor' => $this->floor]);
        // 清除评论提醒
        Commentnotice::deleteAll('comment_id = :comment_id AND sub = :sub', [
            ':comment_id' => $this->id,
            ':sub' => Commentnotice::TYPE_SUB_COMMENT
        ]);
        if ($this->checked === self::CHECKED_COMMON) {
            // 当删除正常评论时，修改相关冗余字段
            SoundComment::updateAll(['sub_comment_num' => new Expression('GREATEST(sub_comment_num, 1) - 1')],
                'id = :id', [':id' => $this->comment_id]);

            $comment = SoundCommentRO::find()->where(['id' => $this->comment_id, 'c_type' => SoundComment::TYPE_SOUND])->one();
            if ($comment) {
                MSound::updateAll(['sub_comments_count' => new Expression('GREATEST(sub_comments_count, 1) - 1')],
                    'id = :id', [':id' => $comment->element_id]);
            }
        }
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '子评论 ID',
            'comment_content' => '回复',
            'comment_id' => '回复的评论 ID',
            'ctime' => '评论时间',
            'userid' => '回复者 ID',
            'username' => '回复者昵称',
            'like_num' => '点赞数',
            'floor' => 'Floor',
            'checked' => '评论属性',  // -1：软删除；1：正常评论；2：评论违规
            'ip_detail' => '用户 IP 详情',
        ];
    }

    /**
     * 对评论内容进行过滤和检查
     *
     * @param string $attribute 验证的属性
     * @param array $params 自定义参数
     */
    public function checkText($attribute, $params)
    {
        $this->$attribute = trim(MUtils::filterSpecialCodes($this->$attribute));
        // 获得实际评论内容（去掉部分子评论中的“回复 @xxx :”这个前缀）
        // @todo: 客户端之后应在回复的冒号后加一个半角空格，这里也需要做兼容
        $comment = trim(preg_replace('/^回复 @.* :/', '', $this->$attribute));
        if (mb_strlen($comment) < self::MIN_LENGTH) {
            $this->addError($attribute, Yii::t('app/error',
                'Please send non-emotional comments larger than {num} words', ['num' => self::MIN_LENGTH]));
        }
        // TODO: 整合在一起判断
        // 检测评论违规情况
        if (!$result = Yii::$app->go->checkText($this->$attribute, Go::SCENE_COMMENT)) {
            return;
        }
        if (!$item = current($result)) {
            return;
        }
        if (!$item['pass']) {
            $this->checked = self::CHECKED_VIOLATION;
            // $this->addError($attribute, $this->getAttributeLabel($attribute) . '中含有违规词汇喔~');
        } elseif (is_array($item['labels']) && $this->checkShamSend($item['labels'])) {
            $this->checked = self::CHECKED_VIOLATION;
        }
    }

    /**
     * 检查是否假发送
     *
     * @param $labels
     * @return bool
     */
    protected function checkShamSend(array $labels): bool
    {
        return !empty(array_intersect([Go::LABEL_AD, Go::LABEL_EVIL], $labels));
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        if ($insert) {
            $this->ctime = $_SERVER['REQUEST_TIME'];
            $this->userid = Yii::$app->user->id;
            $this->username = Yii::$app->user->name;
            // 评论属性默认为正常评论
            $this->checked = $this->checked ?? self::CHECKED_COMMON;
        }
        return true;
    }

    public function afterSave($insert, $changedAttributes)
    {
        parent::afterSave($insert, $changedAttributes);
        if ($insert && $this->checked === self::CHECKED_COMMON) {
            SoundComment::updateAllCounters(['sub_comment_num' => 1], 'id = :id', [':id' => $this->comment_id]);
            $comment = SoundCommentRO::find()->where(['id' => $this->comment_id, 'c_type' => SoundComment::TYPE_SOUND])->one();
            if ($comment) {
                MSound::updateAllCounters(['sub_comments_count' => 1], 'id = :id', [':id' => $comment->element_id]);
            }
        }
        return true;
    }

    /**
     * 是否包违规的子评论
     *
     * @param int $user_id 用户 ID
     * @param int|array $comment_ids 评论 ID
     * @return bool
     */
    public static function hasViolationSubComment(int $user_id, $comment_ids): bool
    {
        return static::find()->where([
            'userid' => $user_id,
            'comment_id' => $comment_ids,
            'checked' => SoundSubComment::CHECKED_VIOLATION,
        ])->exists();
    }

}
