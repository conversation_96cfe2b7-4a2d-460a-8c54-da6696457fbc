<?php

namespace app\models;

use Exception;
use Yii;
use missevan\util\MUtils;

class MThirdPartyQidianTask
{
    // 合作方对接文档
    // https://docs.qq.com/doc/DTmV0SG9QU2VVYmRo
    const CALLBACK_URL = 'https://druidv6.if.qidian.com/argus/api/v1/video/adv/callback';
    const ACCESS_TOKEN = '4c897ae5be7a43ee19d28d3f5ff453cf';
    const SUCCESS_CODE = 0;

    public static function callback(string $track_id): bool
    {
        $track_data = explode('|', $track_id);
        if (count($track_data) !== 2) {
            Yii::error('起点点击回传失败：解析 track_id 错误 ' . $track_id);
            return false;
        }
        $task_id = $track_data[0];
        $user_token = $track_data[1];
        $params = [
            'access_token' => self::ACCESS_TOKEN,
            'task_id' => $task_id,
            'token' => $user_token,
            'request_id' => MUtils::randomKeys(32, 7),
            'timestamp' => intval(microtime(true) * 1000),
        ];
        $sign = self::buildSign($params);
        $params['signature'] = $sign;

        $headers = [
            'Content-Type' => 'application/x-www-form-urlencoded',
        ];

        try {
            $data = Yii::$app->tools->requestRemote(self::CALLBACK_URL, $params, 'POST', null, 0, $headers);
            if (!$data) {
                throw new Exception('返回值为空');
            }
            if ($data['Result'] !== self::SUCCESS_CODE) {
                throw new Exception(sprintf('result[%d] message[%s]', $data['Result'], $data['Message']));
            }
        } catch (Exception $e) {
            Yii::error(sprintf('起点点击回传失败，参数 = %s，原因 = %s', json_encode($params), $e->getMessage()), __METHOD__);
            return false;
        }
        return true;
    }

    private static function buildSign(array $params): string
    {
        $data = [
            $params['access_token'],
            $params['task_id'],
            $params['token'],
            $params['request_id'],
            $params['timestamp'],
        ];
        $str = join(',', $data);
        return sha1($str);
    }
}
