<?php

namespace app\models;

use app\components\util\Equipment;
use Exception;
use missevan\rpc\LiveRpc;
use missevan\storage\StorageClient;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "live".
 *
 * @property int $id 主键
 * @property int $user_id 用户 ID
 * @property int $room_id 直播间房间 ID
 * @property string $title 直播间名称
 * @property string $cover 直播间封面
 * @property int $contract_id 主播类型 ID
 * @property integer $status 直播间状态，0：没有开启房间；1：房间开启
 * @property integer $live_start_time 开播时间
 * @property integer $create_time 创建时间
 * @property integer $modified_time 修改时间
 * @property string $username 用户昵称
 */
class Live extends ActiveRecord
{
    // App 首页直播广场入口图标地址
    const ICON_URL = 'oss://system/app/icons/normal/live.png';
    const ICON_DARK_URL = 'oss://system/app/icons/dark/live.png';

    // 直播间状态 -1：用户注销；0：没有开启房间；1：房间开启
    const STATUS_DELETED = -1;
    const STATUS_CLOSE = 0;
    const STATUS_OPEN = 1;

    // 直播分成比例（默认）
    // 散人主播打赏/付费问题分成比例
    public const LIVE_RATE = 0.45;

    // 素人主播使用新分成比例起始时间（2020.06.01 00:00:00）
    public const TIMESTAMP_NEW_RATE = 1590940800;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'live';
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->cover) {
            $this->cover = StorageClient::getFileUrl($this->cover);
        }
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'user_id', 'room_id', 'title', 'cover'], 'required'],
            [['id', 'room_id', 'contract_id', 'status', 'create_time', 'modified_time', 'live_start_time'], 'integer'],
            [['id', 'room_id', 'contract_id'], 'integer', 'min' => 1],
            [['title'], 'string', 'max' => 30],
            [['user_id'], 'unique'],
            [['id'], 'unique'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'user_id' => '用户 ID',
            'room_id' => '直播间房间 ID',
            'title' => '直播间名称',
            'contract_id' => '主播合约 ID',  // 弃用
            'cover' => '直播间封面',
            'status' => '直播间状态',  // 0：没有开启房间；1：房间开启
            'live_start_time' => '开播时间',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'username' => '用户昵称',
        ];
    }

    /**
     * 获取关联用户
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(Mowangskuser::class, ['id' => 'user_id']);
    }

    /**
     * @deprecated
     * 获取关联主播合约
     *
     * @return \yii\db\ActiveQuery
     */
    public function getLiveContract()
    {
        return $this->hasOne(LiveContract::class, ['id' => 'contract_id']);
    }

    /**
     * 获取该主播的分成比例
     * 若主播不在数据库中，则赋予默认分成比例（self::LIVE_RATE）
     *
     * @param int $to_id 收礼人的 ID
     * @return float 分成比例
     */
    public static function getRate(int $to_id)
    {
        /*
        $live = Live::find()
            ->alias('l')
            ->select('lc.rate AS rate')
            ->innerJoinWith('liveContract AS lc', false)
            ->where(['l.user_id' => $to_id])
            ->asArray()
            ->one();

        if (!$live) {
            Yii::warning('获取不存在的主播（主播 ID：' . $to_id . '）', __METHOD__);
            $live = ['rate' => self::LIVE_RATE];
        }

        return (float)$live['rate'];
        */
        // 2020.06.01 之前素人主播打赏收益分成比例为 0.5，之后使用新值
        if ($_SERVER['REQUEST_TIME'] < self::TIMESTAMP_NEW_RATE) {
            return 0.5;
        }

        // 散人主播不区分是否签约，统一为默认分成比例
        return self::LIVE_RATE;
    }

    /**
     * 获取联想词
     *
     * @param string $s 搜索词
     * @param integer $count 联想词数量
     * @return array
     */
    public static function getSearchSuggest(string $s, int $count = Discovery::SUGGEST_COUNT)
    {
        return Yii::$app->go->suggest($s, Discovery::SEARCH_LIVE, $count);
    }

    /**
     *  获取关注主播最新开播时间
     *
     * @param int $user_id
     * @return int 最新开播时间
     */
    public static function getLiveLastTime(int $user_id)
    {
        $last_time = 0;
        if ($user_id) {
            $last_time = Live::find()
                ->alias('l')
                ->select('l.live_start_time')
                ->leftJoin(MAttentionUser::tableName() . ' AS a', 'l.user_id = a.user_passtive')
                ->where('a.user_active = :user_active AND l.status = :status', [
                    ':user_active' => $user_id,
                    ':status' => self::STATUS_OPEN,
                ])
                ->orderBy('l.live_start_time DESC')
                ->limit(1)
                ->scalar();
        }
        return (int)$last_time;
    }

    /**
     * 获取推荐主播
     *
     * @param array $user_ids 主播 ID 数组
     * @param bool $is_old 是否需要返回旧版本数据，iOS < 6.0.9、安卓 < 6.0.9 的版本传入 true
     * @return array
     */
    public static function getRecommendedLives(array $user_ids, bool $is_old = false)
    {
        if (empty($user_ids)) {
            return [];
        }
        $rooms = [];
        try {
            $rooms = Yii::$app->liveRpc->getLiveRecommendInfo($user_ids, $is_old);
        } catch (Exception $e) {
            // PASS
            Yii::error('获取主播 ' . Json::encode($user_ids) . ' 推荐直播间信息列表出错：' . $e->getMessage(),
                __METHOD__);
            return [];
        }
        if (count($rooms) < count($user_ids)) {
            $none_ids = array_values(array_diff($user_ids, array_keys($rooms)));
            Yii::error('主播 ' . Json::encode($none_ids) . ' 推荐直播间信息为空', __METHOD__);
            if (!$rooms) {
                return [];
            }
        }
        return array_values($rooms);
    }

    /**
     * 根据房间 ID 获取直播间信息
     *
     * @param int $room_id 直播间 ID
     * @param bool $find_deleted 是否查找已注销用户的直播间
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function getLiveByRoomId(int $room_id, bool $find_deleted = false)
    {
        $query = self::find()->where(['room_id' => $room_id]);
        if (!$find_deleted) {
            $query->andWhere('status <> :status', [':status' => self::STATUS_DELETED]);
        }
        return $query->limit(1)->one();
    }

    /**
     * 获取剧集相关直播预告
     *
     * @param int $drama_id 剧集 ID
     * @param int $user_id 用户 ID
     * @return array|null 直播预告信息，无相关直播预告或获取失败时，返回 null
     */
    public static function getDramaLivePreview(int $drama_id, int $user_id): ?array
    {
        try {
            $preview = Yii::$app->liveRpc->getLivePreview(LiveRpc::PREVIEW_ELEMENT_TYPE_DRAMA, $drama_id,
                $user_id);
            if (!$preview) {
                return null;
            }
            $now = $_SERVER['REQUEST_TIME'];
            $live_status = $preview['live_status'];
            if ($preview['live_start_time'] < $now && $live_status === Live::STATUS_CLOSE) {
                // 若当前时间已超过（显示的）开播时间，并且还未开播，则不返回预告
                return null;
            }
            if ($preview['live_schedule_time'] >= $now) {
                // 若当前未超过预计开播时间，不返回直播间开播状态
                unset($preview['live_status']);
            }
            // 去掉不需要的字段
            unset($preview['live_schedule_time']);
            return $preview;
        } catch (Exception $e) {
            Yii::error("获取剧集（{$drama_id}）直播预告失败：{$e->getMessage()}", __METHOD__);
            // PASS: 直播预告获取出错，不能影响其他信息下发
            return null;
        }
    }
}
