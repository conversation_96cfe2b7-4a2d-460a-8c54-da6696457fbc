<?php

namespace app\models;

use Yii;
use yii\db\ActiveRecord;
use yii\db\Connection;

/**
 * This is the model class for table "m_user_appearance".
 *
 * @property integer $id 主键
 * @property integer $create_time 创建时间（秒级时间戳）
 * @property integer $modified_time 修改时间（秒级时间戳）
 * @property integer $appearance_id 外观套装 ID
 * @property integer $user_id 用户 ID
 * @property integer $start_time 开始时间（秒级时间戳）
 * @property integer $end_time 过期时间（秒级时间戳）
 * @property integer $deduct_record_id 扣费记录 ID
 */
class MUserAppearance extends ActiveRecord
{
    use ActiveRecordTrait;

    /**
     * @return Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->db;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'm_user_appearance';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['appearance_id', 'user_id', 'start_time', 'end_time'], 'required'],
            [['create_time', 'modified_time', 'appearance_id', 'user_id', 'start_time', 'end_time', 'deduct_record_id'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'appearance_id' => '外观套装 ID',
            'user_id' => '用户 ID',
            'start_time' => '开始时间',
            'end_time' => '过期时间',
            'deduct_record_id' => '扣费记录 ID',
        ];
    }
}
