<?php

namespace app\models;

use ArrayAccess;
use Exception;

/**
 * Class AppsFlyerBody AppsFlyer 报文
 * @package app\models\util
 *
 * @property string advertising_id
 * @property string af_ad
 * @property string af_ad_id
 * @property string af_ad_type
 * @property string af_adset
 * @property string af_adset_id
 * @property string af_attribution_lookback
 * @property string af_c_id
 * @property string af_channel
 * @property string af_cost_currency
 * @property string af_cost_model
 * @property string af_cost_value
 * @property string af_keywords
 * @property string af_prt
 * @property string af_reengagement_window
 * @property string af_siteid
 * @property string af_sub1
 * @property string af_sub2
 * @property string af_sub3
 * @property string af_sub4
 * @property string af_sub5
 * @property string af_sub_siteid
 * @property string amazon_aid
 * @property string android_id
 * @property string api_version
 * @property string app_id
 * @property string app_name
 * @property string app_version
 * @property string appsflyer_id
 * @property string attributed_touch_time
 * @property string attributed_touch_time_selected_timezone
 * @property string attributed_touch_type
 * @property string bundle_id
 * @property string campaign
 * @property string campaign_type
 * @property string carrier
 * @property string city
 * @property string contributor_1_af_prt
 * @property string contributor_1_campaign
 * @property string contributor_1_match_type
 * @property string contributor_1_media_source
 * @property string contributor_1_touch_time
 * @property string contributor_2_af_prt
 * @property string contributor_2_campaign
 * @property string contributor_2_match_type
 * @property string contributor_2_media_source
 * @property string contributor_2_touch_time
 * @property string contributor_2_touch_type
 * @property string contributor_3_af_prt
 * @property string contributor_3_campaign
 * @property string contributor_3_match_type
 * @property string contributor_3_media_source
 * @property string contributor_3_touch_time
 * @property string contributor_3_touch_type
 * @property string conversion_type
 * @property string cost_in_selected_currency
 * @property string country_code
 * @property string custom_data
 * @property string custom_dimension
 * @property string customer_user_id
 * @property string deeplink_url
 * @property string device_category
 * @property string device_download_time
 * @property string device_download_time_selected_timezone
 * @property string device_model
 * @property string device_type
 * @property string dma
 * @property string event_name
 * @property string event_revenue
 * @property string event_revenue_currency
 * @property string event_revenue_usd
 * @property string event_source
 * @property string event_time
 * @property string event_time_selected_timezone
 * @property string event_value
 * @property string gp_broadcast_referrer
 * @property string gp_click_time
 * @property string gp_install_begin
 * @property string gp_referrer
 * @property string http_referrer
 * @property string idfa
 * @property string idfv
 * @property string imei
 * @property string install_app_store
 * @property string install_time
 * @property string install_time_selected_timezone
 * @property string ip
 * @property string is_lat
 * @property string is_primary_attribution
 * @property string is_receipt_validated
 * @property string is_retargeting
 * @property string keyword_id
 * @property string keyword_match_type
 * @property string language
 * @property string match_type
 * @property string media_source
 * @property string network_account_id
 * @property string oaid
 * @property string operator
 * @property string original_url
 * @property string os_version
 * @property string platform
 * @property string postal_code
 * @property string region
 * @property string retargeting_conversion_type
 * @property string revenue_in_selected_currency
 * @property string sdk_version
 * @property string selected_currency
 * @property string selected_timezone
 * @property string state
 * @property string store_reinstall
 * @property string user_agent
 * @property string wifi
 */
class AppsFlyerBody implements ArrayAccess
{
    /**
     * @var null|array
     */
    private $_data = null;

    public function __construct(array $body)
    {
        $this->_data = $body;
    }

    /**
     * @inheritDoc
     */
    public function offsetExists($offset)
    {
        return key_exists($offset, $this->_data);
    }

    /**
     * @inheritDoc
     */
    public function offsetGet($offset)
    {
        return $this->_data[$offset];
    }

    /**
     * @inheritDoc
     */
    public function offsetSet($offset, $value)
    {
        throw new Exception(self::class . ' properties readonly');
    }

    /**
     * @inheritDoc
     */
    public function offsetUnset($offset)
    {
        throw new Exception(self::class . ' properties readonly');
    }

    public function __get($name)
    {
        if ($this->offsetExists($name)) {
            return $this->_data[$name];
        }

        return null;
    }

    public function __set($name, $value)
    {
        throw new Exception(self::class . ' properties readonly');
    }

    /**
     * 获取事件的时间戳（秒级）
     * @return int
     */
    public function getEventTimestamp()
    {
        // FIXME: event_time_selected_timezone 自带毫秒（例：2022-01-05 20:43:27.267+0800），需要处理成毫秒级的时间戳格式返回
        return strtotime($this->event_time_selected_timezone);
    }

    public function isFromIOS()
    {
        return strtolower($this->platform) === 'ios';
    }

    public function isFromAndroid()
    {
        return strtolower($this->platform) === 'android';
    }

    /**
     * @link https://support.appsflyer.com/hc/en-us/articles/207034356
     * @return bool
     */
    public function isInstallEvent()
    {
        return strtolower($this->event_name) === 'install';
    }

    /**
     * @deprecated 用户获取事件（User acquisition）都为 install，判断激活需要用 isInstallEvent 方法
     * @link https://support.appsflyer.com/hc/en-us/articles/207034356
     * @return bool
     */
    public function isInstallConversionType()
    {
        return strtolower($this->conversion_type) === 'install';
    }

    /**
     * 是否为自然流量（非广告流量）来源
     *
     * @return bool
     */
    public function isOrganicSource()
    {
        return strtolower($this->media_source) === 'organic';
    }

}
