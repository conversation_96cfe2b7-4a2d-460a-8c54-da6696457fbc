<?php

namespace app\models;

use app\components\util\Equipment;
use missevan\storage\StorageClient;
use yii\web\HttpException;
use Yii;

/**
 * This is the model class for table "m_appupdate".
 *
 * @property int $id
 * @property string $title App 名称
 * @property string $version 版本号
 * @property string $push_version 推送版本号
 * @property string $intro 介绍
 * @property string $changelog 变更日志
 * @property int $status 发布状态
 * @property string $appurl App 下载地址
 * @property string $appurl2 App 下载地址（Android: 64 位包，Windows: zip 包）
 * @property int $update_time 更新时间
 * @property int $device
 * @property double $size App 大小（MB）
 * @property int $force_download 是否强制更新
 */
class MAppupdate extends ActiveRecord
{
    // 安卓设备
    const DEVICE_ANDROID = 0;
    // 苹果设备
    const DEVICE_IOS = 1;
    // Windows（直播助手）
    const DEVICE_WINDOWS = 2;
    // 鸿蒙设备
    const DEVICE_HARMONYOS = 3;

    // 设备值转换器，FIXME: 与 Equipment 修正为一致值
    const DEVICE_SWAP = [
        'toEqu' => [
            self::DEVICE_IOS => Equipment::iOS,
            self::DEVICE_ANDROID => Equipment::Android,
            self::DEVICE_HARMONYOS => Equipment::HarmonyOS,
        ],
        'toSelf' => [
            Equipment::iOS => self::DEVICE_IOS,
            Equipment::Android => self::DEVICE_ANDROID,
            Equipment::HarmonyOS => self::DEVICE_HARMONYOS,
        ]
    ];

    // 未发布版本
    const STATUS_UNPUBLISHED = 0;
    // 正式版（已发布）
    const STATUS_PUBLISHED = 1;
    // 测试版（已发布）
    const STATUS_BETA = 2;

    const FORCE_DOWNLOAD_YES = 1;
    const FORCE_DOWNLOAD_NO = 0;

    // Android 5.6.5 版本 ID（支持 Android 4.x 的最后一个版本）
    const ANDROID_VERSION_ID_565 = 262;
    // Android 6.3.9 版本 ID（支持 Android < 6.0 的最后一个版本）
    const ANDROID_VERSION_ID_639 = 395;
    // Android 5.7.4 版本 ID
    const ANDROID_VERSION_ID_574 = 292;
    // iOS 4.9.5 版本 ID（支持 iOS 9 的最后一个版本）
    const IOS_VERSION_ID_495 = 309;
    // iOS 6.1.1 版本 ID（支持 iOS 11 的最后一个版本）
    const IOS_VERSION_ID_611 = 341;

    // 安卓谷歌渠道版本链接
    const ANDROID_APP_GOOGLE_CHANNEL_LINK = 'https://play.google.com/store/apps/details?id=cn.missevan';
    // 安卓腾讯应用宝链接
    const LINK_ANDROID = 'https://sj.qq.com/myapp/detail.htm?apkName=cn.missevan';

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_appupdate';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['title', 'version', 'intro', 'changelog', 'status', 'appurl', 'appurl2', 'update_time', 'size'], 'required'],
            [['intro', 'changelog'], 'string'],
            [['status', 'update_time', 'device', 'force_download'], 'integer'],
            [['size'], 'number'],
            [['title', 'version', 'push_version'], 'string', 'max' => 32],
            [['status'], 'string', 'max' => 3],
            [['appurl', 'appurl2'], 'string', 'max' => 100],
            [['device'], 'string', 'max' => 4],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'title' => 'App 名称',
            'version' => '版本号',
            'push_version' => '推送版本号',  // site/launch 接口中使用
            'intro' => '介绍',
            'changelog' => '变更日志',
            'status' => '发布状态',
            'appurl' => 'App 下载地址',
            'appurl2' => 'App 下载地址（Android: 64 位包，Windows: zip 包）',
            'update_time' => '更新时间',
            'device' => 'Device',
            'size' => 'App 大小（MB）',
            'force_download' => '是否强制更新',
        ];
    }

    public function afterFind()
    {
        parent::afterFind();
        if ($this->appurl) {
            $this->appurl = StorageClient::getFileUrl($this->appurl);
        }
        if ($this->appurl2) {
            $this->appurl2 = StorageClient::getFileUrl($this->appurl2);
        }
    }

    /**
     * 获取 App 最新版
     *
     * @param int $os 设备类型
     * @return MAppupdate|null
     * @throws HttpException
     */
    public static function getLastestApp(int $os)
    {
        return self::getApp($os, [self::STATUS_BETA, self::STATUS_PUBLISHED]);
    }

    /**
     * 获取 App 正式版
     *
     * @param integer $os 设备类型
     * @return null|MAppupdate
     * @throws HttpException
     */
    public static function getPublishedApp(int $os)
    {
        return self::getApp($os, self::STATUS_PUBLISHED);
    }

    /**
     * 获取 App 版本
     *
     * @param int $os 设备类型
     * @param $status
     * @return array|\yii\db\ActiveRecord|null
     * @throws HttpException
     */
    public static function getApp(int $os, $status)
    {
        $query = self::find();
        switch ($os) {
            case Equipment::Android:
                $device = self::DEVICE_ANDROID;
                break;
            case Equipment::iOS:
                $device = self::DEVICE_IOS;
                break;
            case Equipment::HarmonyOS:
                $device = self::DEVICE_HARMONYOS;
                break;
            default:
                throw new HttpException(403, '该设备不能升级', 100010004);
        }

        // WORKAROUND: 针对 Android < 5.6.6、Android 系统为 4.x 的客户端，返回固定的（版本号为 5.6.5）的 App 版本信息
        if (self::isLegacyAndroid()) {
            $query = $query->where(['id' => self::ANDROID_VERSION_ID_565]);
        } elseif (self::isOlderThanAndroid6()) {
            // WORKAROUND: 针对 Android 系统 < 6.0 的客户端，返回固定的（版本号为 6.3.9）的 App 版本信息
            $query = $query->where(['id' => self::ANDROID_VERSION_ID_639]);
        } elseif (Equipment::isAppOlderThan(null, '5.7.4')) {
            // WORKAROUND: Android < 5.7.4，需要先更新到 5.7.4，解决直接更新到最新版本，下载的数据可能会丢失的问题
            $query = $query->where(['id' => self::ANDROID_VERSION_ID_574]);
        }

        if (self::isLegacyIOS()) {
            // WORKAROUND: 针对 iOS < 4.9.6、iOS 系统为 11.0 以下的客户端始终下发 4.9.5 版本
            $query = $query->where(['id' => self::IOS_VERSION_ID_495]);
        } elseif (self::isLegacyIOS11()) {
            // WORKAROUND: 针对 iOS 11 系统且版本小于 6.1.2 的客户端始终下发 6.1.1 版本
            $query = $query->where(['id' => self::IOS_VERSION_ID_611]);
        }

        // limit 方法使用原因：http://stackoverflow.com/questions/6519775/pdofetch-limit-1
        return $query
            ->andWhere(['device' => $device, 'status' => $status])
            ->orderBy('id DESC')
            ->limit(1)
            ->one();
    }

    public static function getIOSVersion()
    {
        return Yii::$app->redis->get(KEY_IOS_VERSION);
    }

    public static function isForceDownload(MAppupdate &$latest_app, string $current_version)
    {
        if ($latest_app->force_download === self::FORCE_DOWNLOAD_YES) {
            return;
        }

        if (!in_array($latest_app->device, [self::DEVICE_ANDROID, self::DEVICE_IOS, self::DEVICE_HARMONYOS])) {
            Yii::error('数据记录错误：' . $latest_app->id, __CLASS__);
            $latest_app->force_download = self::FORCE_DOWNLOAD_NO;
            return;
        }

        // WORKAROUND: Android < 5.6.6、Android 系统为 4.x 的客户端不再查询中间版本的强制更新提示
        if (self::isLegacyAndroid()) {
            return;
        }

        // TODO: 调整成后台设置设备最新的强制下载的版本号
        $latest_app->force_download = self::find()
            ->where(['force_download' => self::FORCE_DOWNLOAD_YES, 'device' => $latest_app->device])
            ->andWhere(['>', 'intro', $current_version])
            ->exists()
            ? self::FORCE_DOWNLOAD_YES
            : self::FORCE_DOWNLOAD_NO;
    }

    /**
     * 处理链接和签名
     *
     * @param MAppupdate $latest_app
     * @return void
     */
    public static function processAppUrlAndSign(MAppupdate &$latest_app)
    {
        if (Yii::$app->equip->isFromMiMiApp()) {
            return;
        }

        // 1. 对于谷歌渠道的 App
        //   i. 存在对应的应用包（Missevan_google.apk，代表新版本 Google Play 审核已通过）：去 Google Play 更新；
        //   ii. 否则不提示更新
        // 2. 其它版本安装普通包
        if (!$latest_app->appurl) {
            return;
        }

        if (Equipment::isFromGoogleChannel()) {
            $google_version = OSS_DIR_CHANNEL_APP . '/MissEvan-appRelease-' . trim($latest_app->intro) . '-missevan_google.apk';
            if (Yii::$app->storage->exist($google_version)) {
                $latest_app->appurl = self::ANDROID_APP_GOOGLE_CHANNEL_LINK;
            } else {
                // 如果没有 Google 的版本则不提示更新
                $latest_app->version = 0;
                $latest_app->push_version = 0;
            }
        }
    }

    /**
     * 检查用户有 Beta 测试权限
     *
     * 获取灰度资格
     * 假定 `crc32(设备号 + salt)` 的运算结果为 1234567810
     * 假定灰度测试值 ($beta) 设定为 [97, 20]
     * 20 为“本次灰度比例为 20%”
     * 97 此次灰度的 salt
     * 因 ((1234567810) % 100) < 20 为 true，该用户接到测试版的更新推送
     *
     * @param MAppupdate $info
     * @param int $device 用户设备类型 (Equipment::DeviceType)
     * @param string $equip_id 用户设备号
     * @return bool 真值为有，假值为无
     */
    public static function getIsBeta(MAppupdate $info, int $device, string $equip_id)
    {
        $redis = Yii::$app->redis;
        $device = self::DEVICE_SWAP['toSelf'][$device];

        $is_beta = false;
        if ($info
                && $info->status === MAppupdate::STATUS_BETA
                && $device === MAppupdate::DEVICE_ANDROID) {
            $key_beta = $redis->generateKey(KEY_BETA_RELEASE, $device);
            $beta = $redis->lRange($key_beta, 0, 1);
            // 预防 Redis 值尚未设置
            if (empty($beta)) {
                $beta = [0, 0];
            }
            $equip_id_crc32 = crc32($equip_id . (string)$beta[0]);
            if (($equip_id_crc32 % 100) < $beta[1]) {
                $is_beta = true;
            } else {
                // 获取参与测试的用户 ID 列表
                $key_special = $redis->generateKey(KEY_BETA_RELEASE_SPECIAL_IDS, $device);
                if ($redis->sIsMember($key_special, Yii::$app->user->id)) {
                    $is_beta = true;
                }
            }
        }

        return $is_beta;
    }

    /**
     * 获取更新信息
     *
     * @param Equipment $equipment
     * @param int $version_code 版本号
     * @param bool $is_launch 是否 site/launch 接口调用，用于主动推送更新检查
     * @param string $supported_abis 支持的 abi
     * @return array
     * @throws HttpException
     */
    public static function getUpdate(Equipment $equipment, int $version_code = 0, bool $is_launch = false, string $supported_abis = '')
    {
        $app = null;
        $version_info = null;
        // 云游戏渠道包不显示升级版本提示
        if (!Equipment::isFromYunYouXiChannel()) {
            $app = self::getLastestApp($equipment->getOs());
        }
        if ($app) {
            $app_sign = trim(Yii::$app->request->get('app_sign'));
            self::isForceDownload($app, $equipment->getAppVersion());

            $is_beta = self::getIsBeta($app, $equipment->getOs(), $equipment->getEquipId());
            // WORKAROUND: version_code 为 6000610 或 6000620 的版本存在问题，需全量更新到最新的版本（包括灰度的版本）
            if (!$is_beta) {
                $app = self::getPublishedApp($equipment->getOs());
            }

            self::processAppUrlAndSign($app);
            $version_info = [
                'download' => $app->appurl,
                'intro' => $app->intro,
                'log' => $app->changelog,
                'v' => $app->version,
                'is_beta' => $is_beta,
                'force_download' => $app->force_download,
                'app_sign' => $app_sign,
            ];
            if ($is_launch && $app->push_version) {
                $version_info['v'] = $app->push_version;
            }
            if ($is_launch) {
                if (in_array($version_code, [6000610, 6000620])) {
                    // WORKAROUND: 安卓 6.1.0 的 6000610 或 6000620 版本存在问题，需强制更新
                    $version_info['force_download'] = self::FORCE_DOWNLOAD_YES;
                }
                if (in_array($version_code, [6010081, 6010082])
                        && Equipment::isAppOlderThan(null, $app->intro)) {
                    // WORKAROUND: 安卓 6.1.0 的 6010081 和 6010082 版本埋点上报存在问题，需要提示更新到最新（大于 6.1.0）版本
                    $version_info['v'] = $app->version;
                }
            }

            // Android supported_abis 包含 arm64-v8a 下发 64 位包
            if ($equipment->getOs() === Equipment::Android
                    && in_array('arm64-v8a', explode(',', $supported_abis))
                    && $app->appurl2) {
                $version_info['download'] = $app->appurl2;
            }
        }

        $data['new_version'] = $version_info;
        if ($equipment->isIOS()) {
            // 为 iOS 获取版本号
            $data['version'] = self::getIOSVersion();
        }
        return $data;
    }

    /**
     * 获取隐私协议 URL
     *
     * @param string $channel 渠道
     * @return string
     */
    public static function getPrivacyDataUrl(string $channel)
    {
        // FIXME: 使用 channel
        if (Equipment::isConceptVersion()) {
            $privacy_data = Yii::$app->params['privacy_concept_data'];
        } elseif (Equipment::CHANNEL_GOOGLE === $channel) {
            $privacy_data = Yii::$app->params['privacy_google_data'];
        } else {
            $privacy_data = Yii::$app->params['privacy_data'];
        }
        return $privacy_data ?? '';
    }

    /**
     * 是否 Android < 5.6.6、Android 系统为 4.x 的客户端
     *
     * @return bool
     */
    public static function isLegacyAndroid(): bool
    {
        $equipment = Yii::$app->equip;
        if ($equipment->isAndroid()
                && Equipment::isAppOlderThan(null, '5.6.6')
                && strpos($equipment->getOsVersion(), '4.') === 0) {
            return true;
        }
        return false;
    }

    /**
     * 是否 Android 系统 < 6.0 的客户端
     *
     * @return bool
     */
    public static function isOlderThanAndroid6(): bool
    {
        $equipment = Yii::$app->equip;
        if ($equipment->isAndroid() && version_compare($equipment->osVersion, '6.0', '<')) {
            return true;
        }
        return false;
    }

    /**
     * 是否 iOS < 4.9.6、iOS 系统为 11.0 以下的客户端
     *
     * @return bool
     */
    public static function isLegacyIOS(): bool
    {
        $equipment = Yii::$app->equip;
        if ($equipment->isIOS()
                && Equipment::isAppOlderThan('4.9.6', null)
                && version_compare($equipment->osVersion, '11.0', '<')) {
            return true;
        }
        return false;
    }

    /**
     * 是否是 iOS 11 系统且版本小于 6.1.2 的客户端
     *
     * @return bool
     */
    private static function isLegacyIOS11(): bool
    {
        return Yii::$app->equip->isIOS11() && Equipment::isAppOlderThan('6.1.2', null);
    }
}
