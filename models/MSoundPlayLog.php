<?php

namespace app\models;

use app\components\util\Equipment;
use Yii;
use yii\web\HttpException;

/**
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 修改时间
 * @property string $session_id 记录开始收听某音频到结束收听的周期
 * @property int $start_time 每条日志开始播放时（切到该歌曲时）的时间戳（单位：毫秒）
 * @property string $playing_status 播放状态
 * @property int $player_mode 播放器模式（主播放器：1 催眠专享电台播放器：2）
 * @property int $quality 清晰度
 * @property int $operation_type 标识是开始播放的时间点还是播放中断 / 结束日志
 * @property int $network 网络状态
 * @property int $mode 播放模式
 * @property int $loop_times 循环次数（累计播放次数），从 1 开始
 * @property array $referer 进入音频播放页的来源
 * @property string $from_event_id 播放来源
 * @property string $context 上下文，AB test 时使用
 * @property int $session_duration 播放停留时长（单位：毫秒）
 * @property int $played_duration 用户（设备）播放该音频的时长（单位：毫秒）
 * @property int $paused_duration 音频暂停时长（单位：毫秒）
 * @property int $last_play_position 播放进度，指用户停止收听时的播放进度
 * @property int $max_play_position 最大播放进度（单位：毫秒）
 * @property int $danmaku_duration 弹幕可见的时长（单位：毫秒）
 * @property int $sound_id 播放音频 ID
 * @property int $sound_duration 音频的总时长（单位：毫秒）
 * @property int $drama_id 音频所属剧集 ID
 * @property int $node_id 互动广播剧的节点 ID
 * @property int $catalog_id 音频所属分类 ID
 * @property int $payment_type 音频付费类型
 * @property int $purchased 该音频是否已购买（0 未购买，1 已购买）
 * @property int $user_id 用户 ID
 * @property string $ua 设备 UA 信息
 * @property int $os 平台（1 Android, 2 iOS, 3 Web）
 * @property string $ip 用户 IP
 * @property string $equip_id 设备号
 * @property string $buvid buvid
 * @property string $channel 渠道标识
 * @property array $address 用户 IP 对应地理位置
 * @property array $more 更多详情
 */
class MSoundPlayLog extends ActiveRecord
{
    // 播放行为类型，1：播放；2：暂停；3：播放结束
    const OPERATION_TYPE_PLAY = 1;
    const OPERATION_TYPE_PAUSE = 2;
    const OPERATION_TYPE_END = 3;

    const OPERATION_TYPES = [
        self::OPERATION_TYPE_PLAY,
        self::OPERATION_TYPE_PAUSE,
        self::OPERATION_TYPE_END,
    ];

    /**
     * 添加播放日志
     *
     * @param Equipment $equipment
     * @param array $data
     * @throws HttpException
     */
    public static function add(Equipment $equipment, array $data)
    {
        $data['more']['error'] = (int)($data['error'] ?? 0);
        // 用户 ID、设备、IP 地址等信息以服务端的为准
        $databus_log = array_merge($data, [
            'user_id' => (int)Yii::$app->user->id,
            'os' => $equipment->getOs(),
            'equip_id' => $equipment->getEquipId(),
            'buvid' => (string)$equipment->getBuvid(),  // 存在 Cookie 没有携带的情况，此时为 null，转成空字符串
            'channel' => $equipment->getChannel(),
            'ip' => Yii::$app->request->userIP,
            'user_agent' => $equipment->getUserAgent(),
            'location_info' => Yii::$app->serviceRpc->getLocationInfo(Yii::$app->request->userIP) ?: new \stdClass(),
            'end_time' => $data['end_time'] ?? 0,
        ]);
        // 播放日志发送到 DataBus
        self::addDataBusLog($databus_log);
    }

    /**
     * 新增播放事件上报日志
     *
     * @param array $data
     */
    public static function addDataBusLog(array $data): void
    {
        $user_id = (int)Yii::$app->user->id;
        $user_ip = Yii::$app->request->userIP;
        try {
            $key_field = $user_id ?: crc32($user_ip);
            $data['create_time'] = $_SERVER['REQUEST_TIME'];
            Yii::$app->databus->pub($data, 'sound_play_log:app:' . $key_field);
        } catch (\Exception $e) {
            Yii::error('Sound play log error: ' . $e->getMessage(), __METHOD__);
            // PASS: databus 出错记录错误并忽略异常
        }
    }

    /**
     * 更正参数类型
     *
     * @param array $params
     * @return array
     */
    public static function formatParamsType(array $params): array
    {
        if (!$params) {
            return $params;
        }
        $float_param_names = ['completion'];
        $integer_param_names = [
            'sound_id', 'played_duration', 'mode', 'player_mode', 'sound_duration', 'last_play_position', 'drama_id',
            'node_id', 'operation_type', 'catalog_id'
        ];
        foreach ($params as $name => $value) {
            if (in_array($name, $float_param_names)) {
                $params[$name] = (float)$value;
            } elseif (in_array($name, $integer_param_names)) {
                $params[$name] = (int)$value;
            }
        }
        return $params;
    }
}
