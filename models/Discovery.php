<?php

namespace app\models;

use app\components\util\Equipment;
use app\components\util\MUtils;
use Yii;
use yii\helpers\Json;

/**
 * This is the model class for table "discovery".
 *
 * @property integer $id
 * @property string $title
 * @property string $icon
 * @property string $icon_dark
 * @property string $intro
 * @property string $link
 * @property integer $group
 * @property integer $order
 */
class Discovery extends ActiveRecord
{
    // 搜索类型 0：音频；1：UP 主；2：音单；3：综合；4：声优；5：剧集；6：直播；7：特型；8：频道；10；搜索专题卡
    const SEARCH_SOUND = 0;
    const SEARCH_USER = 1;
    const SEARCH_MALBUM = 2;
    const SEARCH_GENERAL = 3;
    const SEARCH_SEIY = 4;
    const SEARCH_DRAMA = 5;
    const SEARCH_LIVE = 6;
    const SEARCH_SPECIAL = 7;
    const SEARCH_CHANNEL = 8;
    const SEARCH_TOPIC_CARD = 10;

    const SUGGEST_COUNT = 10;
    const CATALOG_SUGGEST_COUNT = 2;

    const SEARCH_NAME_MAP = [
        1 => 'UP 主',
        2 => '音单',
        4 => '声优',
        5 => '剧集',
        6 => '直播',
    ];

    // 搜索音频排序方式：1: 按播放量倒序, 2: 创建时间倒序
    const SEARCH_SOUND_SORT_VIEW = 1;
    const SEARCH_SOUND_SORT_TIME = 2;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'discovery';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['group', 'order'], 'integer'],
            [['title'], 'string', 'max' => 50],
            [['icon', 'icon_dark', 'intro', 'link'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键',
            'title' => '标题',
            'icon' => '小图标',
            'icon_dark' => '夜间小图标',
            'intro' => '简介',
            'link' => '链接(H5或原生）',
            'group' => '分类',
            'order' => '分类内排序',
        ];
    }

    /**
     * 获取综合搜索的联想词（目前只给出音频联想词，App 上跳转的是综合 tab）
     *
     * @param string $search_word 搜索词
     * @return array 例 [['word' => '魔道祖师', 'type' => 5, 'request_id' => '158252674719723301346304']]
     */
    public static function getGeneralSuggest(string $search_word): array
    {
        $return = [];
        // 剧集联想词
        // if (($drama = Drama::getSearchSuggest($search_word, self::CATALOG_SUGGEST_COUNT))
        //        && !empty($drama['suggestions'])) {
        //    $suggests = self::processSuggest($drama, self::SEARCH_DRAMA);
        //    array_push($return, ...$suggests);
        // }

        // 音频联想词
        if (($sound = MSound::getSearchSuggest($search_word)) && !empty($sound['suggestions'])) {
            $suggests = self::processSuggest($sound, self::SEARCH_SOUND);
            array_push($return, ...$suggests);
        }

        if (count($return) >= self::SUGGEST_COUNT) {
            return array_slice($return, 0, self::SUGGEST_COUNT);
        }

        // 音单联想词（目前搜索结果设置为空，暂不需要该联想词）
        // if ($album = MAlbum::getGoodSearchSuggest($search_word, self::CATALOG_SUGGEST_COUNT)) {
        //     $album = self::processSuggest($album, self::SEARCH_MALBUM);
        //     array_push($return, ...$album);
        // }

        // if (count($return) >= self::SUGGEST_COUNT) {
        //    return array_slice($return, 0, self::SUGGEST_COUNT);
        // }

        // 用户联想词
        // if (($user = Mowangskuser::getGoodSearchSuggest($search_word, self::CATALOG_SUGGEST_COUNT))
        //        && !empty($user['suggestions'])) {
        //    $suggests = self::processSuggest($user, self::SEARCH_USER);
        //    array_push($return, ...$suggests);
        // }
        // if (count($return) >= self::SUGGEST_COUNT) {
        //    return array_slice($return, 0, self::SUGGEST_COUNT);
        // }

        // 直播联想词
        // if (($live = Live::getSearchSuggest($search_word, self::CATALOG_SUGGEST_COUNT))
        //        && !empty($live['suggestions'])) {
        //    $suggests = self::processSuggest($live, self::SEARCH_LIVE);
        //    array_push($return, ...$suggests);
        // }

        return $return;
    }

    /**
     * 处理联想词的数据
     *
     * @param array $search_suggest 联想词
     * @param int $type 联想词类型
     * @return array
     */
    public static function processSuggest(array $search_suggest, int $type): array
    {
        return array_map(function ($word) use ($type, $search_suggest) {
            return [
                'word' => $word,
                'type' => $type,
                'request_id' => $search_suggest['request_id'],
            ];
        }, $search_suggest['suggestions']);
    }

    public static function getRecords()
    {
        return self::find()
            ->select('id, title, icon, icon_dark, intro, link, group, order')
            ->asArray()
            ->all();
    }

    public static function sortIcons(array &$icons)
    {
        usort($icons, function ($a, $b) {
            if ($a['group'] === $b['group']) {
                return $a['order'] > $b['order'];
            }
            return $a['group'] > $b['group'];
        });
    }

    public static function getAllIcons()
    {
        $icons = self::getRecords();
        $icons = array_map(function ($item) {
            $item['id'] = (int)$item['id'];
            $item['group'] = (int)$item['group'];
            $item['order'] = (int)$item['order'];
            $item['link'] = Json::decode($item['link']);
            if (isset($item['link']['url'])) {
                $item['link']['url'] = MUtils::getUsableAppLink($item['link']['url']);
            }
            $item['icon'] = Yii::$app->params['static_domain'] . $item['icon'];
            $item['dark_icon'] = Yii::$app->params['static_domain'] . $item['icon_dark'];

            unset($item['icon_dark']);
            return $item;
        }, $icons);
        $is_ios = Yii::$app->equip->isIOS();
        $is_game_center_blocked = Blacklist::model()->isGameCenterBlocked();
        $icons = array_reduce($icons, function ($ret, $item) use ($is_game_center_blocked, $is_ios) {
            $url = $item['link']['url'] ?? '';
            // 不显示游戏中心相关入口
            if ($is_game_center_blocked && preg_match('/^missevan:\/\/game\/.*/', $url)) {
                return $ret;
            }
            if ($is_ios) {
                // iOS 屏蔽“兑换码”、“福利音”的入口
                $path = parse_url($url, PHP_URL_PATH);
                if ($path && in_array($path, ['/wallet/redeem', '/redeem/sound'])) {
                    return $ret;
                }
            }
            $ret[] = $item;
            return $ret;
        }, []);

        return $icons;
    }

    public static function getIconsGroup()
    {
        $icons = self::getAllIcons();
        self::sortIcons($icons);
        $group = MUtils::groupArray($icons, 'group', null, function ($item) {
            unset($item['group'], $item['order']);
            return $item;
        });
        return array_values($group);
    }

}
