<?php

namespace app\models;

use Yii;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "get_card_log".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间
 * @property int $modified_time 更新时间
 * @property int $user_id 用户 ID
 * @property int $card_id 获取语音包卡片 ID
 * @property int $work_id 作品 id，work 表主键
 * @property int $role_id 角色 ID
 * @property int $special 卡片类型：0 为普通卡片，1 为节日卡，2 为小剧场卡，3 为免费卡，4 为热度福利卡
 * @property int $level 卡片等级，1: N, 2: R, 3: SR, 4: SSR（小剧场卡 level 为 0）
 * @property int $method_of_obtaining 获得方式。-1 为兑换，0 为购买，1 为抽卡，2 为领取（小剧场卡）
 * @property int $card_package_id 卡包 ID
 * @property int $coupon 获得碎片数
 */
class GetCardLog extends ActiveRecord
{
    // 卡片获取方式，-2：解锁待推送语音；-1：兑换语音卡；0：购买；1：抽卡；2：领取语音包小剧场卡；
    // 3：求签；4：兑换求签剧场卡
    const TYPE_UNLOCK = -2;
    const TYPE_EXCHANGE = -1;
    const TYPE_BUY = 0;
    const TYPE_DRAW = 1;
    const TYPE_EPISODE = 2;
    const TYPE_OMIKUJI = 3;
    const TYPE_OMIKUJI_EPISODE = 4;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'get_card_log';
    }

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->get('db2');
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['create_time', 'modified_time', 'user_id', 'card_id', 'work_id', 'role_id', 'special',
                'level', 'method_of_obtaining', 'card_package_id'], 'required'],
            [['create_time', 'modified_time', 'user_id', 'card_id', 'work_id', 'role_id', 'special',
                'level', 'method_of_obtaining', 'card_package_id', 'coupon'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'user_id' => '用户 ID',
            'card_package_id' => '卡包 ID',
            'method_of_obtaining' => '获取方式',
            'coupon' => '获得荣誉值',
            'card_id' => '卡片 ID',
            'work_id' => '作品 ID',
            'role_id' => '角色 ID',
            'level' => '卡片等级',
            'special' => '卡片类型',
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    public static function initialize($card)
    {
        $log = new self();
        $time = $_SERVER['REQUEST_TIME'] ?? time();
        $log->create_time = $time;
        $log->modified_time = $time;
        $log->card_id = $card->id;
        $log->work_id = $card->work_id;
        $log->role_id = $card->role_id;
        $log->level = $card->level;
        $log->special = $card->special;
        $log->card_package_id = $card->card_package_id;
        return $log;
    }

    /**
     * 新增获取卡片日志
     *
     * @param array $get_card_logs 获取卡片日志对象组成的数组
     */
    public static function addLogs(array $get_card_logs)
    {
        $command = Yii::$app->db2->createCommand();
        $row_logs = ArrayHelper::getColumn($get_card_logs, 'attributes');
        $command->batchInsert(self::tableName(), $get_card_logs[0]->attributes(), $row_logs)
            ->execute();
    }
}
