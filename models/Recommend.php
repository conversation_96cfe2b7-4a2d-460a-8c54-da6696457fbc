<?php

namespace app\models;

use Yii;
use yii\helpers\Json;
use yii\web\HttpException;

/**
 * This is the model class for table "recommend".
 *
 * @property int $sound_id 音频 ID
 * @property string $similar_item1 "sound_id1:point1 sound_id2:point2 ..." | null
 * @property string $similar_item2 "sound_id1:point1 sound_id2:point2 ..." | null
 * @property string $similar_item3 "sound_id1:point1 sound_id2:point2 ..." | null
 */
class Recommend extends RecommendBase
{
    const SCENE_RECOMMEND_SIMILAR_SOUND = 'recommend_similar_sound';
    const SCENE_RECOMMEND_SAME_PERSON_SOUND = 'recommend_same_person_sound';
    const SCENE_RECOMMEND_SAME_CATALOG_SOUND = 'recommend_same_catalog_sound';
    const SCENE_RECOMMEND_SAME_TAG_SOUND = 'recommend_same_tag_sound';

    // 策略 ID
    // TODO: 之后考虑入库
    const STRATEGY_NONE = 0;
    const STRATEGY_SIMILAR_SOUND_ID = 1;
    const STRATEGY_SAME_PERSON_SOUND_ID = 2;
    const STRATEGY_SAME_CATALOG_SOUND_ID = 3;
    const STRATEGY_SAME_TAG_SOUND_ID = 4;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'recommend';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['sound_id'], 'required'],
            [['sound_id'], 'integer'],
            [['similar_item1', 'similar_item2', 'similar_item3'], 'string', 'max' => 512],
            [['sound_id'], 'unique'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'sound_id' => 'Sound ID',
            'similar_item1' => 'Similar Item1',
            'similar_item2' => 'Similar Item2',
            'similar_item3' => 'Similar Item3',
        ];
    }

    protected static function getWeight(int $listened_time, int $sound_id, int $strategy = self::STRATEGY_DEFAULT)
    {

        if ($strategy === self::STRATEGY_WEIGHT_BY_TIME) {
            // 按照音频播放时间计算权重
            $time = $_SERVER['REQUEST_TIME'] - $listened_time;
            return exp(-1.4902202892908E-5 * $time);
        }
        return parent::getWeight($listened_time, $sound_id, $strategy);
    }

    protected static function filterSounds(array $sound_points, array $listened_sounds = [])
    {
        // TODO: 已收听过的音不再推荐，但对于很早之前听过的音还可以推荐给用户。
        $sound_points = array_diff_key($sound_points, $listened_sounds);
        arsort($sound_points);
        // TODO: 只需要前 500 条音频，之后这个数量可能还需要作调整
        $sound_points = array_slice($sound_points, 0, 500, true);
        return parent::filterSounds($sound_points, $listened_sounds);
    }

    public function getSoundPoints(bool $filter_sensitive = false)
    {
        $weights = [3, 5, 1];
        $sound_point = [];
        foreach ($weights as $index => $weight) {
            $item_index = $index + 1;
            $item_key = "similar_item$item_index";
            // string "key1:value1 key2:value2" => map "{key1: value1, key2: value2}".
            $sound2points = array_column(array_map(function ($sound) {
                return explode(':', $sound);
            }, explode(' ', $this->$item_key)), 1, 0);
            self::calPoints($sound_point, $sound2points, $weight);
        }
        return $sound_point;
    }

    public static function getSoundsByTime(array $listened_sounds)
    {
        return self::getRecommendSounds($listened_sounds, self::STRATEGY_WEIGHT_BY_TIME);
    }

    /**
     * 获取音频推荐策略 ID
     *
     * @param null|string $scene 场景，传入 null 时表示未知场景
     * @return int 策略 ID，场景未知时返回策略 ID 为 0
     */
    public static function getStrategyID(?string $scene): int
    {
        switch ($scene) {
            case self::SCENE_RECOMMEND_SIMILAR_SOUND:
                return self::STRATEGY_SIMILAR_SOUND_ID;
            case self::SCENE_RECOMMEND_SAME_PERSON_SOUND:
                return self::STRATEGY_SAME_PERSON_SOUND_ID;
            case self::SCENE_RECOMMEND_SAME_CATALOG_SOUND:
                return self::STRATEGY_SAME_CATALOG_SOUND_ID;
            case self::SCENE_RECOMMEND_SAME_TAG_SOUND:
                return self::STRATEGY_SAME_TAG_SOUND_ID;
            default:
                return self::STRATEGY_NONE;
        }
    }
}
