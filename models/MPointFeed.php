<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "m_point_feed".
 *
 * @property integer $id
 * @property integer $sound_id
 * @property integer $user_id
 * @property integer $create_time
 * @property integer $num
 * @property integer $catalog_id
 */
class MPointFeed extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_point_feed';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['sound_id', 'user_id', 'num', 'catalog_id'], 'required'],
            [['sound_id', 'user_id', 'create_time', 'num', 'catalog_id'], 'integer'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'sound_id' => 'Sound ID',
            'user_id' => 'User ID',
            'create_time' => 'Create Time',
            'num' => 'Num',
            'catalog_id' => 'Catalog ID',
        ];
    }

    /**
     * 获取“获取小鱼干”的返回结果
     *
     * @param int $num 若无需随机，该值为本次获得的小鱼干的数量
     * @param bool $random 是否需要随机
     * @return array
     */
    public static function getTaskRandomPoint(int $num, bool $random)
    {
        if (!$random) {
            return [$num, "获得 {$num} 个小鱼干"];
        }
        $point_rand = mt_rand(1, 100);
        if ($point_rand <= 5) {
            $num = 1;
            $point_str = '手气一般般，摸到了 1 个小鱼干~';
        } elseif ($point_rand <= 70) {
            $num = mt_rand(2, 10);
            $point_str = "手气一般般，摸到了 {$num} 个小鱼干~";
        } elseif ($point_rand < 100) {
            $num = mt_rand(11, 20);
            $point_str = "好幸运，摸到了 {$num} 个小鱼干~";
        } else {
            $num = 50;
            $point_str = "运气爆棚，摸到 {$num} 个小鱼干！";
        }
        return [$num, $point_str];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        return true;
    }
}
