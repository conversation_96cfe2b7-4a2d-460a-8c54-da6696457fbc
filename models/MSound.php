<?php

namespace app\models;

use app\components\util\Equipment;
use app\components\util\Go;
use app\components\util\MUtils;
use missevan\storage\StorageClient;
use missevan\storage\UposClient;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\base\Exception;
use yii\base\UserException;
use yii\data\Pagination;
use yii\db\Expression;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\web\HttpException;
use yii\web\UploadedFile;

/**
 * This is the model class for table "m_sound".
 *
 * @property integer $id 主键
 * @property integer $catalog_id 分类 ID
 * @property integer $create_time 创建（投稿）时间
 * @property integer $last_update_time 最后修改时间
 * @property integer $duration 时长（单位：ms）
 * @property integer $user_id 用户 ID
 * @property string $username 用户昵称
 * @property string $cover_image 封面图
 * @property integer $animationid
 * @property integer $characterid
 * @property integer $seiyid 声优 ID
 * @property string $soundstr 音频标题
 * @property string $intro 音频简介
 * @property string $soundurl 源音频地址
 * @property string $soundurl_32 192 kbps 音频地址
 * @property string $soundurl_64 源音频码率转码后地址
 * @property string $soundurl_128 128 kbps 音频地址
 * @property integer $downtimes 下载次数
 * @property integer $uptimes 点赞次数
 * @property integer $checked 审核状态
 * @property integer $source 来源
 * @property integer $download 是否允许下载
 * @property integer $view_count 播放量
 * @property integer $comment_count 弹幕量
 * @property integer $favorite_count “喜爱”量
 * @property integer $point 小鱼干量
 * @property integer $push 是否
 * @property integer $refined 是否加精
 * @property integer $comments_count 评论数量
 * @property integer $sub_comments_count 子评论数量
 * @property integer $pay_type 付费类型，0：免费；1：单集付费；2：整剧付费
 * @property integer $type 音频类型，0：普通音频；2：音乐集（有无损音质）音频；3：互动剧音频
 * @property integer $dramaid 所属剧集 ID
 * @property integer $drama_name 剧集名称
 * @property integer $episode_name 单集名称
 * @property integer $episode_sort 单集在剧集中 ID
 * @property integer $strategy_id 推荐策略 ID
 * @property integer $ringtone 铃声设置选项，0：开放铃声设置；1：关闭铃声设置
 * @property array $video_info 音频绑定的视频信息
 *
 * @property-read boolean $isForbiddenInJapan
 */
class MSound extends ActiveRecord
{
    public $all_comments = 0;
    public $comments_num = 0;
    public $front_cover;
    public $soundurl_list;
    public $soundurl_128_list;

    // 铃声设置选项，0：开放铃声设置；1：关闭铃声设置
    public $ringtone;

    // 音频过审状态（转码失败，配音未转码，审核中，已过审，报警，下架，合约期满）
    const CHECKED_TRANSCODE_FAILED = -3;
    const CHECKED_DUB_TRANSCODE = -2;
    const CHECKED_SOUND_TRANSCODE = -1;
    const CHECKED_UNPASS = 0;
    const CHECKED_PASS = 1;
    const CHECKED_POLICE = 2;
    const CHECKED_DISCONTINUED = 3;
    const CHECKED_CONTRACT_EXPIRED = 4;

    // 普通
    const REFINED_COMMON = 0;
    // 加精
    const REFINED_REFINED = 1;
    // 音频是否为报警音时进行位运算标识（擦边球）
    const REFINED_POLICE = 1 << 1;
    // 无法被搜索到
    const REFINED_SEARCH_LIMIT = 1 << 2;
    // 音频是否在分区，标签，用户主页不可见（擦边球 2）
    const REFINED_BLOCK = 1 << 3;
    // 屏蔽日本 IP
    const REFINED_FORBID_JAPAN = 1 << 4;

    // 擦边球 1 和擦边球 2
    const REFINED_POLICE_AND_BLOCK = self::REFINED_POLICE | self::REFINED_BLOCK;

    // 音频购买状态
    // 免费音
    const SOUND_FREE = 0;
    // 未购买
    const SOUND_UNPAID = 1;
    // 已购买
    const SOUND_PAID = 2;

    const PAY_BY_SOUND = 1;
    const PAY_BY_DRAMA = 2;

    // 0：创建的所有音频，1：创建的普通音频，2：点赞的音，3：创建的直播回放音频
    const USER_SOUND_TYPE_All = 0;
    const USER_SOUND_TYPE_OWN = 1;
    const USER_SOUND_TYPE_LIKED = 2;
    const USER_SOUND_TYPE_LIVE = 3;

    // 播放（量）来源，1：游客；2：登录用户；
    const PLAY_SOURCE_GUEST = 1;
    const PLAY_SOURCE_USER = 2;

    // 被设定为刷播放量音频时，每个 IP 段每天可给其刷量最大数
    const LIMIT_ABNORMAL_SOUND_VIEWS_MAX_NUM = 20;

    // 允许下载
    const SOUND_DOWNLOAD_ALLOW = 0;
    const SOUND_DOWNLOAD_REFUSE = 1;

    // 音频类型
    const TYPE_NORMAL = 0;  // 普通音频
    const TYPE_MUSIC = 1;  // 音乐集音频（有无损音质）
    const TYPE_INTERACTIVE = 2;  // 互动广播剧音频
    const TYPE_LIVE = 3;  // 直播回放

    // 音频来源 0：搬运；1：原创
    const SOURCE_CARRY = 0;
    const SOURCE_ORIGINAL = 1;

    // 播放器模式，1：主播放器；3：盲盒剧场语音条
    const PLAYER_MODE_NORMAL = 1;
    const PLAYER_MODE_THEATRE = 3;

    // 音频标题最大长度
    const MAX_TITLE_LENGTH = 100;

    // 是否显示设置铃声进行位运算标识
    const RINGTONE_CAN_SET = 1;
    // 下载时在铃声目录下单独保存一份完整的未加密音频位运算标识
    const RINGTONE_COMPLETE_DOWNLOAD = 1 << 1;

    // 排序方式
    const SORT_NEW = 1;  // 最新
    const SORT_HOT = 2;  // 最热

    // 音频播放地址
    const KEY_SOUNDURL = 'soundurl';
    const KEY_SOUNDURL_32 = 'soundurl_32';
    const KEY_SOUNDURL_64 = 'soundurl_64';
    const KEY_SOUNDURL_128 = 'soundurl_128';

    const DOWNLOAD_SOUND = 1;
    const DOWNLOAD_RINGTONE = 2;

    // 同时播放设备数量
    const LIMIT_EQUIP_PLAY_NUM = 1;

    // 限制播放类型，1：同时播放设备限制；2：会员收听限制；3：整剧付费限制；4：单集付费限制
    const LIMIT_TYPE_EQUIP_PLAY = 1;
    const LIMIT_TYPE_VIP_PLAY = 2;
    const LIMIT_TYPE_DRAMA = 3;
    const LIMIT_TYPE_EPISODE = 4;

    // 播放场景 1：普通播放；2: 优先播放
    const PLAY_NORMAL = 1;
    const PLAY_PRIORITY = 2;

    public $liked = 0;
    public $collected = 0;
    public $followed = 0;
    public $authenticated = 0;
    public $confirm = 0;
    public $fansnum;
    public $catalog;
    public $tags;
    public $tag_id;
    public $pics;
    public $channelid;
    public $iconurl;
    public $icontype;
    public $avatar;
    // 音频是否绑定了视频（bool）
    public $video;
    // 音频绑定的视频信息（array）
    public $video_info;

    public $drama_id;
    public $drama_name;
    public $episode;
    public $episode_name;
    public $discount;
    public $subtitle_url;

    public $sort;
    // 该音频所属剧集为整剧付费剧集时，判断当前用户是否已购该剧，仅在下载和设置铃声场景下有意义
    private $drama_paid;
    public $need_pay = 0;
    public $price = 0;
    // 剧集折扣信息
    public $vip_discount;

    // 互动广播剧（根）节点 ID
    public $interactive_node_id;

    // 关联表 ID
    public $relation_id;

    // 推荐策略 ID
    public $strategy_id;

    // 限制播放类型
    public $limit_type;

    public static $is_upos_user;

    public $episode_vip;

    public $user;

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_sound';
    }

    /**
     * @inheritdoc
     */

    public function rules()
    {
        return [
            [['catalog_id', 'user_id', 'username', 'soundstr', 'soundurl'], 'required'],
            [['catalog_id', 'create_time', 'last_update_time', 'duration', 'user_id', 'animationid', 'characterid', 'seiyid', 'downtimes', 'uptimes', 'checked', 'source', 'download', 'view_count', 'comment_count', 'favorite_count', 'point', 'push', 'refined', 'comments_count', 'sub_comments_count'], 'integer'],
            [['intro'], 'string'],
            [['username'], 'string', 'max' => 20],
            [['cover_image'], 'string', 'max' => 255],
            [['soundstr', 'soundurl', 'soundurl_32', 'soundurl_64', 'soundurl_128'], 'string', 'max' => 100],
        ];
    }

    /**
     * 入库前自动处理
     */
    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            if (!$this->create_time) {
                $this->create_time = $time;
            }
            if (!$this->animationid) {
                $this->animationid = 0;
            }
            if (!$this->characterid) {
                $this->characterid = 0;
            }
            if (!$this->seiyid) {
                $this->seiyid = 0;
            }
            $this->duration = 0;
            $this->soundurl_32 = '';
            $this->soundurl_64 = '';
            $this->soundurl_128 = '';
            $this->checked = $this->checked ?: -1;
            $this->downtimes = 0;
            $this->uptimes = 0;
            $this->view_count = 0;
            $this->comment_count = 0;
            $this->favorite_count = 0;
            $this->push = 0;
            $this->source = 1;
            Mowangskuser::updateAllCounters(['soundnum' => 1],
                'id = :id', [':id' => $this->user_id]);
        }
        $this->last_update_time = $time;
        if ($this->intro) {
            $this->intro = MUtils::formatHTML($this->intro);
        }
        return true;
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'catalog_id' => '分类 ID',
            'create_time' => '创建时间',
            'last_update_time' => 'Last Update Time',
            'duration' => '持续时间',
            'user_id' => 'User ID',
            'username' => 'Username',
            'cover_image' => '封面图片',
            'animationid' => 'Animationid',
            'characterid' => 'Characterid',
            'seiyid' => 'Seiyid',
            'soundstr' => 'Soundstr',
            'intro' => '简介',
            'soundurl' => 'Soundurl',
            'soundurl_32' => 'Soundurl 32',
            'soundurl_64' => 'Soundurl 64',
            'soundurl_128' => 'Soundurl 128',
            'downtimes' => 'Downtimes',
            'uptimes' => '被赞次数',
            'checked' => '审核状态 配音秀转码：-2；转码：-1；未审核：0；审核：1；报警：2；下架：3',
            'source' => '来源',
            'download' => '是否允许下载',
            'view_count' => '查看数',
            'comment_count' => '弹幕数',
            'favorite_count' => '收藏数',
            'point' => '猫耳数',
            'push' => '是否推送',
            'refined' => '是否加精',
            'comments_count' => '评论数',
            'sub_comments_count' => '子评论数',
            'type' => '音频类型',
            'pay_type' => '音频付费类型',
        ];
    }

    public static function afterAllFind(&$models)
    {
        self::haveVideos($models);
    }

    public function afterFind()
    {
        parent::afterFind();
        // 弹幕、评论、子评论
        $this->all_comments = $this->comment_count + $this->comments_count + $this->sub_comments_count;
        // 评论、子评论
        $this->comments_num = $this->comments_count + $this->sub_comments_count;
        if (!self::checkSoundId($this->id)) {
            // 将 “本音频暂时无法播放” 的音频的评论、弹幕、播放、点赞、投食、收藏数设置为 0
            $this->all_comments = $this->comments_count = $this->view_count = $this->sub_comments_count
                = $this->comment_count = $this->favorite_count = $this->point = $this->uptimes
                    = $this->fansnum = $this->comments_num = 0;
        }
        if ($this->view_count !== null && $this->checked === self::CHECKED_POLICE) {
            $this->view_count = 0;
        }
        $this->front_cover = Yii::$app->params['defaultCoverUrl'];
        // 图片
        if ($this->cover_image) {
            $this->front_cover = Yii::$app->params['coverUrl'] . $this->cover_image;
        }
        if ($this->icontype) {
            $this->avatar = $this->avatar ?: 'icon01.png';
            $this->iconurl = Yii::$app->params['avatarUrl'] . $this->avatar;
        } else {
            $this->iconurl = $this->iconurl ?: 'icon01.png';
            $this->iconurl = Yii::$app->params['profileUrl'] . $this->iconurl;
        }
        unset($this->icontype, $this->avatar);
        // soundurl 存的是音频用户上传的源地址，业务中不需要下发源地址，
        // 统一将 soundurl 置为 null，避免音频地址不存在时 soundurl 下发的是源地址
        $this->soundurl = null;
        // TODO: 后续需要移除，在需要获取音频地址的地方处理
        // 转化音频地址为完整地址
        self::getSoundUrl($this);
        // FIXME: 将来考虑换成 hmget，并只对播放页请求实际播放量
        /*
        $redis = Yii::$app->redis;
        $today = strtotime('today');
        $gkey = "counter:sound_views:is_guest:1:time:{$today}";
        $akey = "counter:sound_views:is_guest:0:time:{$today}";
        $this->view_count = $this->view_count + ($redis->hGet($gkey, $this->id) ?: 0)
            + ($redis->hGet($akey, $this->id) ?: 0);
        */
    }

    /**
     * 该音频所属剧集为整剧付费剧集时，判断当前用户是否已购该剧，仅在下载和设置铃声场景下有意义
     * 由于对单集付费剧集，购买了某个单集不等同于购买了剧集，所以该方法不用于单集付费剧集付费状态的判定
     *
     * @return boolean
     * @throws Exception
     */
    public function getIsDramaPaid()
    {
        if ($this->drama_paid === null) {
            throw new Exception('drama_paid 属性不可用');
        }
        return $this->drama_paid;
    }

    // 处理单音上传
    public static function handleSubmission()
    {
        $sound_file = UploadedFile::getInstanceByName('sound');
        if (!$sound_file) {
            throw new HttpException(400, '单音文件错误', 200110101);
        }
        $sound_path = $sound_file->tempName;
        $extension = $sound_file->getExtension();
        if (!in_array($extension, ['mp3', 'wav', 'm4a'], true)) {
            throw new HttpException(400, '单音文件类型错误', 200110101);
        }
        $soundurl = MUtils2::generateFilePath($sound_path, '.' . $extension);
        $sound_all_path = 'sound/' . $soundurl;
        try {
            Yii::$app->storage->upload($sound_path, $sound_all_path, true);
        } catch (Exception $e) {
            throw new HttpException(400, '上传失败', 200110101);
        }
        return $soundurl;
    }

    /**
     * 搜索音频
     *
     * @param string|integer $s 关键词
     * @param integer $page 第几页
     * @param integer $page_size 每页个数
     * @param integer|null $user_id 访问者 ID
     * @param integer $sort 排序规则（可选值：0: 'sound', 1: 'view', 2: 'time'）
     * @param integer $free 是否仅筛选免费音频
     * @param integer|null $view_user_id 音频 UP 主 ID
     * @param integer $search_catalog_id 搜索分区 ID
     * @param integer $scenario 搜索场景（0 主搜（首页、发现页），1 个人搜索（个人主页音频），2 后台搜索（评论搜索后台），3 社团搜索（社团剧集搜索页））
     * @return array
     * @throws
     */
    public static function getSearch($s, int $page = 1, int $page_size = PAGE_SIZE_20, ?int $user_id = 0,
            int $sort = 0, int $free = 0, ?int $view_user_id = 0, int $search_catalog_id = 0,
            int $scenario = Go::SCENARIO_MAIN_SEARCH)
    {
        // 判断当前访问者是否是音频的 UP 主
        $is_owner = $view_user_id > 0 && ($view_user_id === $user_id);
        if ($is_owner) {
            // 如果当前访问者是音频的 UP 主，可以查询出任何状态的音频
            $sensitive = 1;
        } elseif (!Yii::$app->user->isExam) {
            $sensitive = 0;
        } elseif (Yii::$app->user->isLimited) {
            $sensitive = -1;
        } else {
            $sensitive = 1;
        }
        $options = ['sort' => $sort, 'sensitive' => $sensitive, 'scenario' => $scenario];
        // 根据搜索分区 ID 获取对应的音频一级分类 ID
        $cid = Catalog::getSoundCatalogBySearchCatalog($search_catalog_id);
        if ($cid > 0) {
            // 获取音频二级分类 IDs，因为音频表中的 catalog_id 记录的是二级分类
            // 例：搜索一级分类“广播剧”，则需要筛选广播剧分类下的“纯爱（中文），全年龄，言情”的音频
            $catalog_info = Catalog::getSonCatalogs($cid);
            $catalog_ids = array_column($catalog_info, 'id');
            if (!empty($catalog_ids)) {
                // 用法：in(field, "number1|number2") 其中（in 不能变更大小写，且参数必须以双引号括起）
                // 参考文档地址：https://help.aliyun.com/document_detail/29131.html
                $options['extra_filters'] = sprintf('in(catalog_id, "%s")', implode('|', $catalog_ids));
            }
        }
        if ($free) {
            // 如果从直播间搜 BGM 则仅筛选免费
            $options['pay_type'] = [self::SOUND_FREE];
        }
        if ($view_user_id) {
            // 搜索指定用户创建的音频
            $options['user_id'] = $user_id;
            $options['view_user_id'] = $view_user_id;
        }
        // OpenSearch 查询结果不返回 soundurl 字段
        $results = Yii::$app->go->search($s, Discovery::SEARCH_SOUND, $page, $page_size, $options);
        if (empty($results['Datas'])) {
            return $results;
        }
        self::checkNeedPay($results['Datas'], Yii::$app->user->id);
        self::haveVideos($results['Datas']);
        $vip_sound_ids = [];
        if ($free) {
            // 如果从直播间搜 BGM 则仅筛选免费，则需要过滤会员限制音频
            $sound_ids = array_values(array_unique(array_column($results['Datas'], 'id')));
            $sound_dramas = Drama::rpc('api/get-drama-paytype-by-sound', ['sound_ids' => $sound_ids]);
            foreach ($sound_dramas as $sound_id => $sound_drama) {
                if ($sound_drama['episode_vip'] === Drama::EPISODE_VIP_LIMIT) {
                    $vip_sound_ids[] = (int)$sound_id;
                }
            }
        }
        // 组合数据
        $results['Datas'] = array_reduce($results['Datas'], function ($ret, $data) use ($free, $vip_sound_ids) {
            if ($free && in_array($data['id'], $vip_sound_ids)) {
                // 如果从直播间搜 BGM 则仅筛选免费，则需要过滤会员音频
                // FIXME: 此处过滤可能会导致页面上会员音频不满足指定条数，后续考虑在 open search 直接过滤
                return $ret;
            }
            $data['front_cover'] = self::getFrontCoverUrl($data['cover_image']);
            $data['all_comments'] = $data['comment_count'] + $data['comments_count'] + $data['sub_comments_count'];
            if ($data['checked'] === self::CHECKED_POLICE) {
                $data['view_count'] = 0;
            }
            // 过滤客户端不使用的字段
            unset($data['cover_image']);
            $ret[] = $data;
            return $ret;
        }, []);
        if (!empty($results['Datas']) && $is_owner) {
            // 如果当前访问者就是音频的 UP 主，则显示过审后再次被修改的音频
            // 音频详情是被修改后的内容且状态是审核中
            MCheckedSoundReview::showReviewSounds($results['Datas']);
        }
        return $results;
    }

    /**
     * 获取音频相关信息
     *
     * @param int $sound_id 音频 ID
     * @param int $download 是否为下载，0 不是下载，1 是下载
     * @param bool $simple 是否仅获取简单信息（不获取音频插图、标签、分类、用户加 v 标识信息）
     * @param integer $play 播放场景 1：普通播放；2: 优先播放
     * @return int|self 音频信息，返回整型（音频 ID）时表示原音频信息不可获取，使用该音频 ID 对应的音频进行提示
     * @throws HttpException
     */
    public static function singleSound(int $sound_id, int $download = 0, bool $simple = false, int $play = self::PLAY_NORMAL)
    {
        if ($sound_id <= 0) {
            return Yii::$app->params['sounds_for_notice']['not_exist'];
        }
        $user_id = (int)Yii::$app->user->id;
        if (in_array($user_id, [22771060, 2850856]) && in_array($sound_id, [6002091, 6002102])) {
            // WORKAROUND: 对于指定用户，部分音频状态为下架
            return Yii::$app->params['sounds_for_notice']['unchecked'];
        }
        /**
         * @var MSound $sound
         */
        $sound = self::find()
            ->alias('t')
            ->select('t.*, t1.boardiconurl iconurl, t1.soundnumchecked soundnum, t1.fansnum, t1.confirm,
                t1.avatar avatar, t1.icontype')
            ->leftJoin('mowangskuser t1', 't.user_id = t1.id')
            ->where('t.id = :id', [':id' => $sound_id])->one();
        // 音频播放权限检测
        if (!$sound) {
            return Yii::$app->params['sounds_for_notice']['not_exist'];
        }
        // 对于转码中的音频/配音及转码失败的音频（checked 分别为 -1、-2、-3）则跳转至转码的提示音
        if ($sound->checked < self::CHECKED_UNPASS) {
            return Yii::$app->params['sounds_for_notice']['transcoding'];
        }
        // 补充音频所属剧集和单集的信息
        self::fillSoundDramaInfo($sound, $sound->id, $user_id);

        // 获取音频所属互动广播剧节点 ID
        if ($sound->type === self::TYPE_INTERACTIVE && !MSoundNode::isUsableVersion()) {
            // WORKAROUND: 不支持互动广播剧版本，返回升级提示音
            return Yii::$app->params['sounds_for_notice']['upgrade_app'];
        }
        $equip_id = Yii::$app->equip->getEquipId();
        $interactive_sound_node_id = MUserNodeLog::getUserCurrentNodeID((int)$user_id, $sound->id, $equip_id);
        $sound->interactive_node_id = $interactive_sound_node_id;

        // 收听音频时更新用户画像分数
        $buvid = Yii::$app->equip->getBuvid();
        Persona::setPointsFromPlaySound($sound->catalog_id, $equip_id, $buvid, $user_id);
        // UP 主认证信息
        $sound->authenticated = Mowangskuser::getAuthenticated($sound->confirm);
        // UP 主信息
        // TODO: 待客户端将获取用户信息从 user 字段中获取后，去掉其他用户信息多余字段的下发
        $sound->user = [
            'user_id' => $sound->user_id,
            'username' => $sound->username,
            'confirm' => $sound->confirm,
            'authenticated' => $sound->authenticated,
            'is_vip' => (int)MUserVip::isVipUser($sound->user_id),
        ];

        // 音频标签
        $sound->tags = MTag::getSoundTags($sound->id);
        // catalog 字段客户端目前未使用
        $sound->catalog = MCatalogTags::findOne(['id' => $sound->catalog_id]);
        $sound->episode_vip = Drama::EPISODE_VIP_NOT;
        $drama_price_info = Drama::rpc('api/get-drama-price-by-sound', ['sound_id' => $sound_id, 'user_id' => $user_id]);
        if ($drama_price_info) {
            $sound->episode_vip = $drama_price_info['episode']['vip'];
        }
        if ($user_id !== $sound->user_id) {
            if (self::CHECKED_UNPASS === $sound->checked) {
                return Yii::$app->params['sounds_for_notice']['unchecked'];
            }
            if ($sound->checked === self::CHECKED_DISCONTINUED
                    || (($sound->checked === self::CHECKED_POLICE) && !Yii::$app->user->isExam)
                    || $sound->isForeignForbidden()) {
                return Yii::$app->params['sounds_for_notice']['not_exist'];
            }
            if ($sound->isForbiddenInJapan && MUtils::isJapan()) {
                return Yii::$app->params['sounds_for_notice']['forbidden_in_japan'];
            }
            // WORKAROUND: MiMiApp 1.0.2 加入付费支持，对付费音进行升级提示
            if ($sound->pay_type !== self::SOUND_FREE && Equipment::isMiMiAppOlderThan('1.0.2', '1.0.2')) {
                return Yii::$app->params['sounds_for_notice']['upgrade_app'];
            }

            if ($download || self::SOUND_FREE !== $sound->pay_type) {
                // 下载场景会使用到这个属性值，所以需要先赋值避免后续使用时出错
                $sound->drama_paid = false;
                if ($drama_price_info) {
                    $drama_id = $drama_price_info['drama_id'];
                    $sound->discount = $drama_price_info['discount'] ?? null;
                    $sound->price = $drama_price_info['price'];
                    if (Drama::PAY_TYPE_FREE === $drama_price_info['pay_type']) {
                        $sound->need_pay = self::SOUND_FREE;
                    } else {
                        if (array_key_exists('vip_discount', $drama_price_info)) {
                            // 下发剧集折扣信息
                            $sound->vip_discount = $drama_price_info['vip_discount'];
                        }
                        $paid = false;
                        if (Drama::PAY_TYPE_DRAMA === $drama_price_info['pay_type']) {
                            $paid = $sound->drama_paid = $user_id && TransactionLog::checkUserPaidDrama($user_id, $drama_id);
                        } elseif (Drama::PAY_TYPE_EPISODE === $drama_price_info['pay_type'] && self::PAY_BY_SOUND === $sound->pay_type) {
                            // 用户购买了单集付费剧集时，暂不考虑设置剧集付费状态为已购
                            $paid = $user_id && TransactionSoundLog::checkUserPaidDramaSound($user_id, $drama_id, $sound_id);
                        }
                        if (self::SOUND_FREE === $sound->pay_type) {
                            $sound->need_pay = self::SOUND_FREE;
                        } else {
                            $sound->need_pay = $paid ? self::SOUND_PAID : self::SOUND_UNPAID;
                        }
                    }
                }
            }
            if (self::CHECKED_CONTRACT_EXPIRED === $sound->checked) {
                // 下架的付费内容未购不可听
                if (self::SOUND_FREE !== $sound->pay_type && self::SOUND_UNPAID === $sound->need_pay) {
                    return Yii::$app->params['sounds_for_notice']['not_exist'];
                }
                // 下架的免费音：若属于剧集中的试听音则需要对剧集已购方可听；若不属于剧集则不可听
                if (self::SOUND_FREE === $sound->pay_type) {
                    if ($sound->drama_id) {
                        $is_drama_paid = TransactionLog::find()->select('gift_id')
                            ->where([
                                'gift_id' => $sound->drama_id,
                                'from_id' => $user_id,
                                'status' => TransactionLog::STATUS_SUCCESS,
                                'type' => [TransactionLog::TYPE_DRAMA, TransactionLog::TYPE_SOUND],
                            ])->exists();
                        if (!$is_drama_paid) {
                            return Yii::$app->params['sounds_for_notice']['not_exist'];
                        }
                    } else {
                        return Yii::$app->params['sounds_for_notice']['not_exist'];
                    }
                }
            }
            if ($user_id && $play === self::PLAY_PRIORITY) {
                // 保证用户从手机网页打开 App 时，可以直接播放
                $redis = Yii::$app->redis;
                $equip_play_key = $redis->generateKey(KEY_EQUIP_PLAY_USER_ID, $user_id);
                $redis->unlink($equip_play_key);
            }
            // 补充限制播放类型
            $sound->fillLimitType($user_id, $download);
        }
        if (Equipment::isAppOlderThanVipVersion() && $sound->episode_vip === Drama::EPISODE_VIP_LIMIT) {
            // 老版本播放或下载会员音频，提示升级，规则如下：
            // 免费音频不提示，都可以播或下载（episode_vip 为 1 或者 pay_type 为 0 都视作免费）
            // 会员专享剧集付费音频都需要提示升级，不关心用户是否为会员
            // 双模式剧集付费音频，只有购买了才可以播或下载，不关心用户是否为会员
            if ($sound->pay_type === self::SOUND_FREE || $sound->need_pay !== self::SOUND_PAID) {
                return Yii::$app->params['sounds_for_notice']['upgrade_app'];
            }
        }
        if (!$download) {
            // 非下载场景，对临时解锁限免收听情况做处理
            $sound->unlockFree($user_id);
        }
        if ($sound->limit_type) {
            if ($sound->limit_type === self::LIMIT_TYPE_EQUIP_PLAY && Equipment::isAppOlderThanVipVersion()) {
                // WORKAROUND: 6.2.3 之前版本触发播放设备量上限跳转【同时播放设备超限】提示音
                return Yii::$app->params['sounds_for_notice']['equip_play_num_limit'];
            }
            unset($sound->soundurl, $sound->soundurl_32, $sound->soundurl_64, $sound->soundurl_128);
            return $sound;
        }

        // TEMP: 特殊用户访问特殊音频，不返回字幕
        if (!self::isSpecialUser() || !self::getSpecailSoundId($sound_id)) {
            $sound->subtitle_url = MSoundSubtitle::getSubtitleUrl($sound_id);
        }

        if ($simple) {
            MSound::getSoundSignUrls($sound, true);
            // 若仅获取音频简单信息，则直接返回
            return $sound;
        }
        // 付费音频已付费的情况，返回音频插图
        $sound->pics = MSoundImageMap::getSoundPics($sound_id);

        // WORKAROUND: iOS < 4.7.7 版本铃声设置客户端判断是否是 m4a 文件有问题，故不返回音频签名地址
        if ($download === 2 && Equipment::isAppOlderThan('4.7.7', null)) {
            return $sound;
        }
        MSound::getSoundSignUrls($sound, true, 1);
        return $sound;
    }

    /**
     * 对临时解锁限免收听音频的用户做处理
     * 非购买途径解锁的付费音频（如剧集解锁活动），以免费试听音的形式展现给解锁的用户
     *
     * @param int $user_id 登录用户 ID
     * @return void
     */
    public function unlockFree(int $user_id)
    {
        if (!$user_id || !in_array($this->limit_type,
            [self::LIMIT_TYPE_VIP_PLAY, self::LIMIT_TYPE_DRAMA, self::LIMIT_TYPE_EPISODE])) {
            return;
        }
        if (!MUserUnlockElement::isSoundUnlocked($user_id, $this->id)) {
            return;
        }
        if ($this->episode_vip === Drama::EPISODE_VIP_LIMIT) {
            $this->episode_vip = Drama::EPISODE_VIP_NOT_LIMIT;
        }
        if ($this->pay_type !== self::SOUND_FREE) {
            $this->pay_type = self::SOUND_FREE;
            $this->need_pay = self::SOUND_FREE;
        }
        $this->limit_type = null;
    }

    /**
     * 补充音频所属剧集和单集信息
     *
     * @param MSound $sound 音频信息
     * @param int $sound_id 音频 ID
     * @param int $user_id 用户 ID
     */
    public static function fillSoundDramaInfo(MSound $sound, int $sound_id, int $user_id)
    {
        try {
            $drama = Drama::rpc('api/get-drama-details-by-sound',
                ['sound_id' => $sound_id, 'user_id' => $user_id]);
            if (!$drama) {
                return;
            }
            $sound->drama_id = $drama['drama']['id'];
            $sound->episode = [
                'id' => $drama['current_id'],
                'name' => $drama['current_name'],
                'order' => $drama['current_order'],
                'drama_id' => $drama['drama']['id'],
                'drama_name' => $drama['drama']['name'],
            ];
        } catch (\Exception $e) {
            // PASS: 为不影响播放页正常播放，不抛出异常处理
        }
    }

    public static function getLikeMusic(int $id, int $num): array
    {
        $recommend = Recommend::findOne($id);
        $scene = '';
        $sound_like_ids = [];
        if ($recommend) {
            $scene = Recommend::SCENE_RECOMMEND_SIMILAR_SOUND;
            $sound_like_ids_part = $recommend->getSimilarSoundIds($num);
        } else {
            $sound = self::findOne($id);
            if (!$sound) return [];
            $recommend_key = MUtils::generateCacheKey(KEY_SOUND_PLAY_RECOMMEND_STRATEGY_SOUNDS, $id);
            $memcache = Yii::$app->memcache;
            if (!$sound_like_ids = $memcache->get($recommend_key)) {
                // 推荐音频中不可出现非过审或擦边球 1 及擦边球 2 音频
                $same_person_ids = self::find()
                    ->select('id')
                    ->where('user_id = :user_id AND checked = :checked AND id <> :id AND (NOT refined & :not_refined)',
                        [
                            ':user_id' => $sound->user_id,
                            ':checked' => MSound::CHECKED_PASS,
                            ':id' => $id,
                            ':not_refined' => self::REFINED_POLICE_AND_BLOCK,
                        ])
                    ->orderBy('point DESC')
                    ->limit(10)
                    ->column();
                $same_catalog_ids = self::find()
                    ->select('id')
                    ->where('catalog_id = :catalog_id AND checked = :checked AND id <> :id AND (NOT refined & :not_refined)',
                        [
                            ':catalog_id' => $sound->catalog_id,
                            ':checked' => MSound::CHECKED_PASS,
                            ':id' => $id,
                            ':not_refined' => self::REFINED_POLICE_AND_BLOCK
                        ])
                    ->orderBy('point DESC')
                    ->limit(20)
                    ->column();
                $same_tags = MTagSoundMap::find()
                    ->select('tag_id')
                    ->where('sound_id = :sound_id', [':sound_id' => $id])
                    ->column();
                $same_tag_ids = [];
                if (!empty($same_tags)) {
                    $sql = MTagSoundMap::find()
                        ->select('sound_id')
                        ->where(['tag_id' => $same_tags])
                        ->createCommand()
                        ->getRawSql();
                    $same_tag_ids = self::find()
                        ->select('id')
                        ->where("id IN ({$sql}) AND checked = :checked AND id <> :id AND (NOT refined & :not_refined)", [
                            ':id' => $id,
                            ':checked' => MSound::CHECKED_PASS,
                            ':not_refined' => self::REFINED_POLICE_AND_BLOCK,
                        ])
                        ->orderBy('point DESC')
                        ->limit(30)
                        ->column();
                }
                $sound_like_ids = [
                    'same_person_ids' => $same_person_ids,
                    'same_catalog_ids' => $same_catalog_ids,
                    'same_tag_ids' => $same_tag_ids,
                ];
                $memcache->set($recommend_key, $sound_like_ids, TEN_MINUTE);
            }
            $unique_sound_like_ids = array_values(array_unique(array_merge(
                $sound_like_ids['same_person_ids'],
                $sound_like_ids['same_catalog_ids'],
                $sound_like_ids['same_tag_ids']
            )));
            shuffle($unique_sound_like_ids);
            $sound_like_ids_part = array_slice($unique_sound_like_ids, 0, $num);
        }
        $sounds = self::find()
            ->select('id, cover_image, soundstr, view_count, comment_count, comments_count, sub_comments_count,
                checked')
            ->where(['id' => $sound_like_ids_part])
            ->all();
        // 处理相关字段
        $sounds = array_map(function ($sound) use ($scene, $sound_like_ids) {
            // 添加推荐策略 ID
            if ($scene) {
                $sound->strategy_id = Recommend::getStrategyID($scene);
            } elseif (in_array($sound->id, $sound_like_ids['same_person_ids'])) {
                $sound->strategy_id = Recommend::getStrategyID(Recommend::SCENE_RECOMMEND_SAME_PERSON_SOUND);
            } elseif (in_array($sound->id, $sound_like_ids['same_catalog_ids'])) {
                $sound->strategy_id = Recommend::getStrategyID(Recommend::SCENE_RECOMMEND_SAME_CATALOG_SOUND);
            } elseif (in_array($sound->id, $sound_like_ids['same_tag_ids'])) {
                $sound->strategy_id = Recommend::getStrategyID(Recommend::SCENE_RECOMMEND_SAME_TAG_SOUND);
            } else {
                // 音频分类等可能发生变化，此处需要兼容该场景
                $sound->strategy_id = Recommend::getStrategyID(null);
            }
            unset($sound->cover_image, $sound->comments_count, $sound->sub_comments_count, $sound->checked);
            return $sound;
        }, $sounds);
        return $sounds;
    }


    public static function DoYouLike(&$sounds)
    {
        $user_id = Yii::$app->user->id;
        if (!$sounds || !$user_id) return;
        if (is_array($sounds)) {
            $sound_ids = array_column($sounds, 'id');
            $id2Sounds = array_column($sounds, NUll, 'id');
            $sound_ids_with_like = MLikeSound::find()->select('sound_id')
                ->where(['user_id' => $user_id, 'sound_id' => $sound_ids])->column();
            foreach ($sound_ids_with_like as $id_with_like) {
                $id2Sounds[$id_with_like] = 1;
            }
        } else {
            if (MLikeSound::find()->where(['user_id' => $user_id, 'sound_id' => $sounds->id])->exists()) {
                $sounds['liked'] = 1;
            }
        }
    }

    /**
     * 判断单音是否被收藏到自建音单
     * @param object|array $sounds 单音详情引用
     */
    public static function DoYouCollect(&$sounds)
    {
        $user_id = Yii::$app->user->id;
        if (!$sounds || !$user_id) return;
        $colletion_query = MSoundAlbumMap::find()->alias('s')->leftJoin('m_album a', 'a.id = s.album_id')
            ->where(['a.user_id' => $user_id]);
        if (is_array($sounds)) {
            $sound_ids = array_column($sounds, 'id');
            $tmp_sounds = array_column($sounds, null, 'id');
            $collected_ids = $colletion_query->select('s.sound_id')->andWhere(['s.sound_id' => $sound_ids])->column();
            foreach ($collected_ids as $collected_id) {
                $tmp_sound = $tmp_sounds[$collected_id];
                $tmp_sound->collected = 1;
            }
        } else {
            $collected = $colletion_query->andWhere(['s.sound_id' => $sounds->id])->exists();
            if ($collected) {
                $sounds->collected = 1;
            }
        }
    }

    /**
     * 处理音频是否包含对应的视频
     *
     * @param $models
     */
    public static function haveVideos(&$models)
    {
        if (!$models) {
            return;
        }
        // 若为支持原生播放视频版本，则需要支持
        $condition = ['checked' => SoundVideo::CHECKED_PASS];
        // 对象或 Map 的情形
        if (is_object($models) || ArrayHelper::isAssociative($models)) {
            $condition['sid'] = (int)$models['id'];
            $models['video'] = SoundVideo::find()->where($condition)->exists();
        } else {
            // 对象数组或 Map 数组
            $sound_ids = array_column($models, 'id');
            $condition['sid'] = $sound_ids;
            $video_ids = SoundVideo::find()
                ->select('sid')->where($condition)
                ->column();
            $models = array_map(function ($item) use ($video_ids) {
                $item['video'] = in_array($item['id'], $video_ids);
                return $item;
            }, $models);
        }
    }

    public static function Ts(int $sound_id): int
    {
        $sound = self::findOne(['id' => $sound_id, 'checked' => [self::CHECKED_PASS, self::CHECKED_POLICE]]);
        if (!$sound) {
            throw new HttpException(404, '暂不能对当前内容进行此操作', 200110001);
        }
        $user_id = (int)Yii::$app->user->id;
        $user = Mowangskuser::findOne($user_id);
        if (!$user) {
            throw new HttpException(404, '用户不存在', 200020001);
        }
        if ($user->point <= 0) {
            throw new HttpException(403, '没有小鱼干可以投食了 T_T', 200360104);
        }
        $redis = Yii::$app->redis;
        $TS_LOCK = $redis->generateKey(TS_SOUND, $sound_id, $user_id);
        if (!$redis->lock($TS_LOCK, 300)) {
            throw new HttpException(403, '五分钟只能投一次小鱼干');
        }
        $point_logs = [];
        $transaction = Yii::$app->db->beginTransaction();
        try {
            if ((int)$sound->user_id !== $user_id) {
                $pf = new MPointFeed([
                    'sound_id' => $sound_id,
                    'catalog_id' => $sound->catalog_id,
                    'user_id' => $user_id,
                    'num' => 1
                ]);
                if (!$pf->save()) {
                    throw new HttpException(400, MUtils::getFirstError($pf));
                }
                $count = (int)MPointFeed::find()->where(['sound_id' => $sound->id, 'num' => 1])->count();
                // 满 10 UP 主分 5
                $TEN = 10;
                if ($count % $TEN === 0) {
                    Mowangskuser::updateAllCounters(['point' => PointDetailLog::POINT_SOUND_TS_UP_GET],
                        'id = :user_id', [':user_id' => $sound->user_id]);
                    $point_logs[] = [
                        'num' => PointDetailLog::POINT_SOUND_TS_UP_GET,
                        'type' => PointDetailLog::TYPE_SOUND_GAIN_TS,
                        'user_id' => (int)$sound->user_id,
                    ];
                }
            }
            // 小鱼干从用户投食给单音
            $update = Mowangskuser::updateByPk($user_id, [
                'point' => new Expression('GREATEST(point, :num) - :num', [':num' => PointDetailLog::POINT_SOUND_TS])
            ]);
            if (!$update) {
                throw new HttpException(403, '没有小鱼干可以投食了 T_T', 200360104);
            }
            $sound->updateCounters(['point' => PointDetailLog::POINT_SOUND_TS]);
            $transaction->commit();
            $transaction = null;
            $point_logs[] = [
                'num' => -PointDetailLog::POINT_SOUND_TS,
                'type' => PointDetailLog::TYPE_SOUND_TS,
                'user_id' => $user_id,
            ];
            $add_log_more = ['sound_id' => $sound_id];
            foreach ($point_logs as $value) {
                PointDetailLog::addLog($value['num'], $value['type'], $value['user_id'], $add_log_more);
            }
            return (int)$sound->point;
        } catch (Exception $e) {
            if ($transaction) {
                $transaction->rollback();
            }
            throw $e;
        }
    }

    /**
     * 添加音频到“喜欢”或取消对音频的喜欢
     *
     * @param int $sound_id 音频 ID
     * @param int $user_id 用户 ID
     * @param bool $is_liked 用户是否已经喜欢该音频
     */
    public static function likeOrNot(self $sound, int $user_id, bool $is_liked)
    {
        // 加锁
        $sound_id = $sound->id;
        $lock = Yii::$app->redis->generateKey(LOCK_USER_LIKE_SOUND, $user_id, $sound_id);
        if (Yii::$app->redis->lock($lock, ONE_MINUTE)) {
            $transaction = Yii::$app->db->beginTransaction();
            try {
                if ($is_liked) {
                    MLikeSound::deleteAll(['user_id' => $user_id, 'sound_id' => $sound_id]);
                    $uptimes_expression = new Expression('GREATEST(uptimes, 1) - 1');
                    self::updateAll(['uptimes' => $uptimes_expression],
                        'id = :sound_id', [':sound_id' => $sound_id]);
                    $likenum_expression = new Expression('GREATEST(likenum, 1) - 1');
                    Mowangskuser::updateAll(['likenum' => $likenum_expression],
                        'id = :user_id', [':user_id' => $user_id]);
                } else {
                    $like_sound = new MLikeSound();
                    $like_sound->user_id = $user_id;
                    $like_sound->sound_id = $sound_id;
                    $like_sound->ctime = $_SERVER['REQUEST_TIME'];
                    $like_sound->save();
                    self::updateAllCounters(['uptimes' => 1], 'id = :sound_id',
                        [':sound_id' => $sound_id]);
                    Mowangskuser::updateAllCounters(['likenum' => 1], 'id = :user_id',
                        [':user_id' => $user_id]);
                    // 猫耳学院暑期纳新活动点赞投稿音频获得积分
                    NaxinActivity::addLikeTaskPoint($user_id, $sound);
                }
                // 喜欢音频或取消喜欢音频时更新用户画像分数
                Persona::setPointsFromLikeSound($sound->catalog_id, $user_id, !$is_liked);
                $transaction->commit();
            } catch (\yii\db\Exception $e) {
                $transaction->rollBack();
                // 忽略唯一索引抛出的异常
                if (!MUtils2::isUniqueError($e, MLikeSound::getDb())) {
                    throw $e;
                }
            } finally {
                // 解锁
                Yii::$app->redis->unlock($lock);
            }
        } else {
            throw new HttpException(400, '操作过于频繁，请稍候再试');
        }
    }

    public static function getCatalogSounds(array $catalog_ids, int $tag_id, $not_in_ids, $limit, $order = 0, $offset = 0)
    {
        $query = self::find()
            ->alias('t')
            ->select('t.id, t.soundstr, t.duration, t.view_count, t.cover_image');
        if ($catalog_ids) {
            $query->where(['t.catalog_id' => $catalog_ids]);
        }
        switch ($order) {
            case 1:
                $query->orderBy('t.view_count DESC');
                break;
            default:
                $query->orderBy('t.create_time DESC');
                break;
        }
        if ($tag_id) {
            $query->leftJoin(MTagAlbumMap::tableName() . ' AS t2', 't.id = t2.sound_id');
            $query->andWhere('t2.tag_id = :tag_id', [':tag_id' => $tag_id]);
        }
        $catalog_id = (int)Yii::$app->request->get('cid');
        if ($catalog_id === Catalog::CATALOG_ID_MUSIC) {
            // 音乐分区只展示原创内容
            $query->andWhere(['source' => self::SOURCE_ORIGINAL]);
        }
        $query->andWhere(['t.checked' => self::CHECKED_PASS])
            ->andWhere(['NOT IN', 't.id', $not_in_ids])
            ->andWhere('NOT refined & ' . self::REFINED_BLOCK)
            ->offset($offset)->limit($limit);
        $sounds = $query->all();
        return $sounds;
    }

    /**
     * 获取用户追剧情况
     *
     * @param int $view_user_id 被访问个人主页的用户 ID
     * @param int $user_id 当前请求用户 ID
     * @param int $page 当前页
     * @param int $page_size 每页个数
     * @return ReturnModel 用户追剧详情
     * @throws HttpException
     */
    public static function getSubscribedDrama($view_user_id, int $user_id, int $page, int $page_size)
    {
        $thumbnail = (int)Yii::$app->equip->isFromMiMiApp();
        // 获取数据
        $return = Drama::rpc('api/get-subscriptions', [
            'view_user_id' => $view_user_id,
            'user_id' => $user_id,
            'thumbnail' => $thumbnail,
            'page_size' => $page_size,
            'page' => $page
        ]);
        $subscriptions = new ReturnModel($return['Datas'], $return['pagination']);
        if (!empty($subscriptions->Datas)) {
            // WORKAROUND: 当 iOS >= 4.8.8、Android >= 5.7.4 且为自己视角时，返回角标信息，否则不返回角标信息
            if (Equipment::isAppOlderThan('4.8.8', '5.7.4')) {
                // 获取用户的剧集付费情况
                Drama::checkNeedPay($subscriptions->Datas, $view_user_id);
            } elseif ($view_user_id === $user_id) {
                // 自己视角展示角标，他人视角不展示
                Drama::fillCornerMark($subscriptions->Datas, $view_user_id);
            }
        }
        return $subscriptions;
    }

    public static function getReturnModel($query, $page, $page_size)
    {
        try {
            $count = $query->count();
            $pages = new Pagination(['totalCount' => $count, 'pageSize' => $page_size]);
            $models = [];
            $models = $query->offset($pages->offset)
                ->limit($pages->limit)
                ->all();
            $pagination = [
                'p' => $pages->getPage() + 1,
                'count' => $pages->totalCount,
                'maxpage' => $pages->getPageCount(),
                'pagesize' => $pages->pageSize
            ];
            return ['Datas' => $models, 'pagination' => $pagination];
        } catch (Exception $e) {
            throw new HttpException(500, '服务器错误');
        }
    }

    /**
     * 获取用户拥有的声音或用户喜欢的声音
     * @param integer $type 声音类型 0：用户创建的所有音频，1：用户创建的普通音频，2：用户喜欢的声音，\
     * 3：用户创建的直播回放音频
     * @param integer $user_id 用户 ID
     * @param integer $sort 排序方式 1：最新；2：最热
     * @param integer $page_size 每页个数
     * @return ReturnModel
     * @throws HttpException
     */
    public static function getUserSound(int $type, int $user_id, int $sort = self::SORT_NEW, int $page_size = PAGE_SIZE_20)
    {
        $my_id = Yii::$app->user->id;
        $select = 't.id, t.create_time, t.duration, t.user_id, t.username, t.downtimes, t.comment_count, '
            . 't.soundurl_64, t.comments_count, t.sub_comments_count, t.uptimes, t.view_count, '
            . 't.point, t.pay_type';
        $query = self::find()->alias('t')->select($select);
        switch ($type) {
            case self::USER_SOUND_TYPE_All:
                // 获取用户创建的所有音频
                $query->where(['t.user_id' => $user_id]);
                break;
            case self::USER_SOUND_TYPE_OWN:
                // 获取用户创建的普通音频，音乐集音频和互动广播剧音频
                $sound_types = [self::TYPE_NORMAL, self::TYPE_MUSIC, self::TYPE_INTERACTIVE];
                $query->where(['t.user_id' => $user_id, 't.type' => $sound_types]);
                break;
            case self::USER_SOUND_TYPE_LIKED:
                // 获取用户喜欢的音频
                $query->leftJoin(MLikeSound::tableName() . ' AS t1', 't1.sound_id = t.id')
                    ->where(['t1.user_id' => $user_id]);
                break;
            case self::USER_SOUND_TYPE_LIVE:
                // 获取用户创建的直播回放音频
                $query->where(['t.user_id' => $user_id, 't.type' => self::TYPE_LIVE]);
                break;
            default:
                throw new HttpException(400, '参数错误');
        }
        switch ($sort) {
            case self::SORT_NEW:
                if ($type === self::USER_SOUND_TYPE_LIKED) {
                    // 用户喜欢的音频按照喜欢的顺序排序（最新喜欢的音频在最前）
                    $query->orderBy(['t1.id' => SORT_DESC]);
                } else {
                    $query->orderBy(['t.create_time' => SORT_DESC]);
                }
                break;
            case self::SORT_HOT:
                $query->orderBy(['t.view_count' => SORT_DESC]);
                break;
            default:
                throw new HttpException(400, '参数错误');
        }
        if ($user_id !== $my_id) {
            $query->addSelect('t.checked, t.soundstr, t.cover_image, t.intro, t.download');
            $query->andWhere('checked = :checked AND NOT refined & :refined', [
                ':checked' => self::CHECKED_PASS,
                ':refined' => self::REFINED_BLOCK
            ]);
        } elseif (self::USER_SOUND_TYPE_LIKED !== $type) {
            // 个人主页 UP 主视角，再审状态的音频显示修改后的信息，且显示为审核中
            $expression_select = 'IF(t.checked = 1, IF(t2.id IS NULL, 1, 0), t.checked) AS checked, ' .
                'IF(t2.id IS NULL, t.soundstr, t2.soundstr) AS soundstr, ' .
                'IF(t2.id IS NULL, t.cover_image, t2.cover_image) AS cover_image, ' .
                'IF(t2.id IS NULL, t.intro, t2.intro) AS intro, IF(t2.id IS NULL, t.download, t2.download) AS download';
            $expression = new Expression($expression_select);
            $query->addSelect($expression);
            $query->leftJoin(MCheckedSoundReview::tableName() . ' AS t2', 't2.sound_id = t.id');
        } else {
            $query->addSelect('t.checked, t.soundstr, t.cover_image, t.intro, t.download');
        }
        $max_page_size = PaginationParams::MAX_PAGE_SIZE;
        if (MSound::USER_SOUND_TYPE_LIVE === $type && Equipment::isAppVersion(Equipment::iOS, '4.5.8')) {
            // 对 iOS 4.5.8 的直播回放做兼容：一页返回 200 条数据
            $PAGE_SIZE = 200;
            $page_size = $PAGE_SIZE;
            $max_page_size = $PAGE_SIZE;
        }
        $return_model = MUtils::getPaginationModels($query, $page_size, [], -1, false,
            null, $max_page_size);
        self::DoYouLike($return_model->Datas);
        self::checkNeedPay($return_model->Datas, $my_id);
        self::removeNeedPaySoundUrl($return_model->Datas);
        if (!empty($return_model->Datas)) {
            $sound_ids = array_values(array_unique(array_column($return_model->Datas, 'id')));
            $sound_dramas = Drama::rpc('api/get-drama-paytype-by-sound', ['sound_ids' => $sound_ids]);
            foreach ($return_model->Datas as $data) {
                $sound_id = $data->id;
                if (array_key_exists($sound_id, $sound_dramas)) {
                    $data->episode_vip = $sound_dramas[$sound_id]['episode_vip'];
                }
            }
        }
        return $return_model;
    }

    public static function getUserLike($user_id, $page_size, $page = 1)
    {
        $select = 't id, cover_image, create_time, soundstr, view_count, duration, checked,comments_count,
            comment_count, sub_comments_count';
        $query = self::find()->alias('t')->select($select)
            ->leftJoin('m_like_sound t1', 't.id = t1.sound_id')
            ->where("t1.user_id = :owner", [':owner' => $user_id]);
        $return_model = MUtils::getPaginationModels($query, $page_size);
        if ($return_model->Datas) {
            if ($user_id == Yii::$app->user->id) {
                $liked = true;
                $liked_sounds = [];
            } else {
                $liked = false;
                $ids = array_column($return_model->Datas, 'id');
                $liked_sounds = MLikeSound::findAll(['user_id' => Yii::$app->user->id, 'sound_id' => $ids]);
                $liked_sounds = $liked_sounds ? array_column($liked_sounds, 'sound_id') : [];
            }
            foreach ($return_model->Datas as &$sound) {
                $sound->liked = (int)($liked || in_array($sound->id, $liked_sounds));
            }
        }
        return $return_model;
    }

    /**
     * 获取音单数据
     *
     * @param int $album_id 音单 ID
     * @param int|null $user_id 用户 ID
     * @param bool $all 是否获取全部音频（当点击全部播放时传入 true，最多 999 个）
     * @param int $page 分页时页数
     * @param int $page_size 分页时每页音频个数
     * @return array
     * @throws HttpException 音单不存在或不可查看时抛出异常
     */
    public static function getAlbumSound(int $album_id, ?int $user_id, bool $all = false,
            $page = 1, $page_size = PAGE_SIZE_20)
    {
        $album = MAlbum::findOne($album_id);
        MAlbum::checkCanViews($album, $user_id);

        $checked_condition = self::getCheckedInSql($album->user_id === $user_id);
        $select = 't.id, t.duration, t.soundstr, t.view_count, t.pay_type, t.cover_image, t.username,
            t.checked, t.download';
        $query = self::find()
            ->alias('t')->select($select)
            ->leftJoin(MSoundAlbumMap::tableName() . ' AS t1', 't1.sound_id = t.id')
            ->where(['t1.album_id' => $album_id])
            ->andWhere($checked_condition);
        if ($album->user_id !== $user_id && MAllowAlbum::isAllowed($album_id)) {
            // 白名单音单用于推荐展示。用户查看非白名单音单需要可以看到擦边球音频，避免用户查看已收藏的音单体验受到影响
            // 若查看人不是音单创建者且为白名单音单，则擦边球音频不可见
            $query->andWhere('NOT t.refined & :not_refined',
                [':not_refined' => (self::REFINED_BLOCK | self::REFINED_POLICE | self::REFINED_SEARCH_LIMIT)]);
        }
        $query = $query->orderBy('sort ASC');

        $result = [];
        if ($all) {
            // TODO: 客户端音单详情页获取音频由一次获取全部改为分页获取后，此处需要加上 limit 999
            // 当前不加限制是为了避免部分老音单（音频量超过 999）音频显示不全的问题
            $models = $query->all();
        } else {
            $pagination_model = MUtils::getPaginationModels($query, $page_size);
            $models = $pagination_model->Datas;
            $result['pagination'] = $pagination_model->pagination;
        }
        if (!empty($models)) {
            $sound_ids = array_values(array_unique(array_column($models, 'id')));
            $sound_dramas = Drama::rpc('api/get-drama-paytype-by-sound', ['sound_ids' => $sound_ids]);
            foreach ($models as $m) {
                $sound_id = $m->id;
                if (array_key_exists($sound_id, $sound_dramas)) {
                    $m->episode_vip = $sound_dramas[$sound_id]['episode_vip'];
                }
                unset($m->price, $m->all_comments, $m->comments_num, $m->authenticated, $m->checked,
                        $m->iconurl, $m->confirm, $m->liked, $m->followed, $m->collected, $m->cover_image);
            }
            self::checkNeedPay($models, $user_id);
        }
        $album->updateCounters(['view_count' => 1]);

        $result['Datas'] = $models;
        $result['model'] = [
            'tags' => MAlbum::getTags($album_id),
            'collect' => 0,
        ];
        if ($user_id) {
            $result['model']['collect'] = MAlbum::DoYouCollect($album_id);
        }
        return $result;
    }

    public static function getSoundsByCat(int $tag_id, array $catalog_ids = [], $page_size = 20)
    {
        $page = 1;
        $offset = ($page - 1) * $page_size;
        // 获取缓存 key
        sort($catalog_ids);
        $catalog_ids_str = implode('_', $catalog_ids);
        $SENSITIVE = 0;
        $key = MUtils::generateCacheKey(KEY_CATALOG_GLOBAL_SOUNDS, $catalog_ids_str, $SENSITIVE);
        $memcache = Yii::$app->memcache;
        if (!$sounds = $memcache->get($key)) {
            $ids = self::getRecommendSoundsByCat($tag_id, $catalog_ids, $SENSITIVE);
            $id_count = count($ids);
            // 当推荐音频数量满足当前页，直接返回当前推荐音频（推荐音频为加精音频）
            // 当不满足当前页时，以不加精的音频补足当前页所需数量
            if ($id_count >= $offset + $page_size) {
                $in_id = array_slice($ids, $offset, $page_size);
                $sounds = self::getCheckedSoundByIds($in_id);
            } elseif ($id_count <= $offset) {
                $limit = $page_size;
                $offset = $offset - $id_count;
                $sounds = self::getCatalogSounds($catalog_ids, $tag_id, $ids, $limit, 1, $offset);
            } else {
                $sounds1 = [];
                if ($id_count) {
                    $in_id = array_slice($ids, $offset, $id_count - $offset);
                    $sounds1 = self::getCheckedSoundByIds($in_id);
                }
                $limit = $offset + $page_size - $id_count;
                $sounds2 = self::getCatalogSounds($catalog_ids, $tag_id, $ids, $limit, 1);
                $sounds = array_merge($sounds1, $sounds2);
            }
            // 过滤无用的字段
            $sounds = array_map(function ($sound) {
                unset($sound->need_pay, $sound->price);
                return $sound;
            }, $sounds);
            $memcache->set($key, Json::encode($sounds), FIVE_MINUTE);
        } else {
            $sounds = Json::decode($sounds);
        }
        return $sounds;
    }

    /**
     * 获取指定 ID 列表中过审且非擦边球的音频信息
     *
     * @param array $ids 音频 ID 数组
     * @return self[] 查询结果
     */
    private static function getCheckedSoundByIds(array $ids): array
    {
        return self::find()
            ->select('id, soundstr, view_count, cover_image, comment_count, comments_count, sub_comments_count')
            ->where(['id' => $ids, 'checked' => self::CHECKED_PASS])
            ->andWhere('NOT refined & ' . self::REFINED_BLOCK)
            ->all();
    }

    /**
     * 通过小鱼干数获取分类下推荐音频
     *
     * @param int $tag_id 标签 ID
     * @param array $catalog_ids 分类 ID
     * @param int $sensitive 是否含有敏感音频
     * @return array 音频 ID 组成的数组
     */
    public static function getRecommendSoundsByCat(int $tag_id, array $catalog_ids = [], int $sensitive)
    {
        $ids = [];
        sort($catalog_ids);
        foreach ($catalog_ids as $catalog_id) {
            $catalog_sounds_key = MUtils::generateCacheKey(KEY_SOUNDS_CHECKED_CATALOG_ID, $sensitive, $catalog_id);
            if ($cids = Yii::$app->memcache->get($catalog_sounds_key) ?: []) {
                $ids = $ids + (array)$cids;
            }
        }
        if (!$ids) return [];
        if ($tag_id) {
            $tag_sounds_key = MUtils::generateCacheKey(KEY_SOUNDS_CHECKED_TAG_ID, $sensitive, $tag_id);
            $tids = Yii::$app->memcache->get($tag_sounds_key) ?: [];
            if ($tids) {
                arsort($tids);
                $tids = array_keys($tids);
                $ids = array_filter($tids, function ($id) use ($ids) {
                    return isset($ids[$id]);
                });
            } else {
                return [];
            }
        } else {
            arsort($ids);
            $ids = array_keys($ids);
            $len = count($ids);
            $limit = intdiv($len, 3);
            // 获取加精的音
            $query = self::find()
                ->select('id')
                ->where([
                    // FIXME: 这里应该要使用位运算，因为使用位运算无法使用索引，暂时保持 refined = 1 的写法
                    'refined' => self::REFINED_REFINED,
                    'catalog_id' => $catalog_ids,
                    'source' => [self::SOURCE_CARRY, self::SOURCE_ORIGINAL]
                ]);
            if ($sensitive) {
                $query->andWhere(['checked' => [self::CHECKED_PASS, self::CHECKED_POLICE]]);
            } else {
                $query->andWhere(['checked' => self::CHECKED_PASS]);
            }
            $cids = array_column(MUtils::getRandomModels($query, $limit), 'id');
            if ($cids) {
                $cids = array_diff($cids, $ids);
                $j = 0;
                for ($i = 1; $i < $len + 1; ++$i) {
                    if ($i % 3 == 0) {
                        array_splice($ids, $i + $j, 0, (int)array_shift($cids));
                        $j++;
                    }
                }
            }
        }
        return $ids;
    }

    // $type 1 为原图 2 为裁剪图 4 为 mosaic 图
    public function uploadCoverImage(int $type, string $pic_url, string $extension): int
    {
        $cover_types = [
            ['covers', ''],
            ['coversmini', 'editCoverImage'],
            ['mosaic', 'mosaic']
        ];
        $cover_image_path = date('Ym/d/') .
            md5($pic_url) . date('His') . '.' . $extension;
        foreach ($cover_types as $cover_type) {
            if ($type & 1) {
                $full_image_path = $cover_type[0] . '/' . $cover_image_path;
                $image_file_local = $cover_type[1] ? MImage::{$cover_type[1]}($pic_url) : $pic_url;
                $response = Yii::$app->storage->upload($image_file_local, $full_image_path, true);
                if (!$response) {
                    throw new UserException('上传失败,如果多次失败请联系管理员');
                }
            }
            $type >>= 1;
        }
        $this->cover_image = $cover_image_path;
        return 0;
    }

    /**
     * 获取音单中排在前面的单音
     *
     * @param int $album_id 音单 ID
     * @param int $top_count 单音排在前几的个数
     * @return array 音单中排在前面的单音数据
     */
    public static function getTopNthInAlbum($album_id, $top_count)
    {
        $result = self::getSoundsInAlbum($album_id, 1, $top_count);
        return $result;
    }

    /**
     * 获取音单中音频分页数据
     *
     * @param int $album_id 音单 ID
     * @param int $page 第几页
     * @param int $page_size 每页个数
     * @param int $order 排序（0 为正序，1 为倒序）
     * @return array 音单中单音分页数据
     */
    public static function getSoundsInAlbum($album_id, $page, $page_size, $order = 0)
    {
        $checked_condition = self::getCheckedInSql();
        $result = self::find()
            ->alias('t')
            ->select('t.id, t.duration, t.soundstr, t.view_count, t.pay_type, t.checked')
            ->where(['t1.album_id' => $album_id])->andWhere($checked_condition)
            ->leftJoin('m_sound_album_map t1', 't1.sound_id = t.id')
            ->orderBy(['t1.sort' => !$order ? SORT_ASC : SORT_DESC])
            ->offset(($page - 1) * $page_size)->limit($page_size)->all();
        self::checkNeedPay($result, Yii::$app->user->id);
        foreach ($result as $m) {
            unset($m->price, $m->all_comments, $m->comments_num, $m->front_cover, $m->authenticated,
                $m->iconurl, $m->confirm, $m->liked, $m->followed, $m->collected, $m->checked);
        }
        return $result;
    }

    public function uploadCoverImageEdit(int $type, $img_ext): int
    {
        if (!$this->cover_image) return 1;
        $cover_types = [
            ['covers', ''],
            ['coversmini', 'cropCenter'],
            ['mosaic', 'mosaic']
        ];
        $image_file = urldecode(str_replace(
            Yii::$app->request->hostInfo,
            $_SERVER['DOCUMENT_ROOT'],
            $this->cover_image
        ));
        $cover_image_path = date('Ym/d/') . md5($image_file . date('His'))
            . '.' . $img_ext;
        foreach ($cover_types as $cover_type) {
            if ($type & 1) {
                $full_image_path = $cover_type[0] . '/' . $cover_image_path;
                $image_file_local = $cover_type[1] ? MImage::{
                $cover_type[1]}($image_file) : $image_file;
                $response = Yii::$app->storage->upload($image_file_local, $full_image_path, true);
                if (!$response) {
                    throw new UserException('上传失败,如果多次失败请联系管理员');
                }
            }
            $type >>= 1;
        }
        $this->cover_image = $cover_image_path;
        return 0;
    }

    public static function getFrontCoverUrl($image_path)
    {
        if ($image_path) {
            $return = Yii::$app->params['coverUrl'] . $image_path;
        } else {
            $return = Yii::$app->params['defaultCoverUrl'];
        }
        return $return;
    }

    /**
     * 获取音频的完整地址
     *
     * @param self|MSoundNode|array $sound 单音的信息
     * @param bool $is_sound 是否为音频表数据，传入的对象属性或数组键值来源于音频表或互动剧节点表，两者字段有所区别，需要区别处理
     */
    public static function getSoundUrl(&$sound, bool $is_sound = true)
    {
        // 音频地址字段
        $fields = $is_sound ? ['soundurl_32', 'soundurl_64', 'soundurl_128']
            : ['soundurl', 'soundurl_128', 'soundurl_192'];
        foreach ($fields as $field) {
            // $sound 可能为对象，判断属性是否存在使用 isset
            if (isset($sound[$field]) && $sound[$field]) {
                $path = $sound[$field];
                if (MUtils::isUposUrl($path)) {
                    // 对 upos 协议地址不做处理
                    continue;
                }
                $sound[$field] = self::getSoundUrlByEquipment($path);
            }
        }
        if (isset($sound['soundurl_64'])) {
            // 返回的 soundurl 需要重新赋值为最高音质地址
            $sound['soundurl'] = $sound['soundurl_64'];
        }
        // TODO: 之后需要弃用 soundurl_64 字段
    }

    /**
     * 根据设备获取音频完整地址
     *
     * @param string $sound_path 音频地址字段值
     * @return string|null 完整地址，若为协议地址且协议不存在，返回 null
     */
    public static function getSoundUrlByEquipment(string $sound_path): ?string
    {
        $sound_path = self::getSoundPathByEquipment($sound_path);
        if (!$sound_path) {
            return $sound_path;
        }
        return StorageClient::getFileUrl($sound_path);
    }

    /**
     * 根据设备获取可用的音频协议地址
     *
     * @param string|null $sound_path
     * @return string|null
     */
    public static function getSoundPathByEquipment(?string $sound_path): ?string
    {
        if (!$sound_path) {
            return null;
        }
        $parts = explode('://', $sound_path);
        if (2 !== count($parts)) {
            // 错误的音频地址记录到日志中
            Yii::error('错误的音频地址：' . $sound_path, __METHOD__);
            return null;
        }
        $path = $parts[1];
        if ('.flac' === substr($path, -5)) {
            // WORKAROUND: 若为 flac 音频且为 iOS 11 系统以下的 4.9.5 之前版本，返回 192K m4a 格式音频保证用户可以收听
            $equip = Yii::$app->equip;
            if ($equip->isIOS() && Equipment::isAppOlderThan('4.9.5', null)
                    && (int)$equip->osVersion < 11) {
                // oss://aod/ym/d/name.flac 地址变为 oss://aod/ym/d/name-192k.m4a
                $sound_path = str_replace('.flac', '-192k.m4a', $sound_path);
            }
        }
        return $sound_path;
    }

    public static function getPaidSoundIds($sounds, $user_id)
    {
        $ids_paid = [];
        if (!$user_id || empty($sounds)) return $ids_paid;
        $ids_paybysound = $ids_paybydrama = [];
        if (is_object(current($sounds))) {
            foreach ($sounds as $sound) {
                if (self::PAY_BY_SOUND === (int)$sound->pay_type) $ids_paybysound[] = $sound->id;
                if (self::PAY_BY_DRAMA === (int)$sound->pay_type) $ids_paybydrama[] = $sound->id;
            }
        } else {
            foreach ($sounds as $sound) {
                if (self::PAY_BY_SOUND === (int)$sound['pay_type']) $ids_paybysound[] = $sound['id'];
                if (self::PAY_BY_DRAMA === (int)$sound['pay_type']) $ids_paybydrama[] = $sound['id'];
            }
        }
        if ($ids_paybysound) {
            $ids_paid = TransactionSoundLog::find()->select('sound_id')
                ->where([
                    'user_id' => $user_id,
                    'sound_id' => array_map('intval', $ids_paybysound),
                    'status' => TransactionLog::STATUS_SUCCESS,
                ])->column();
            $ids_paid = array_map('intval', $ids_paid);
        }
        if ($ids_paybydrama) {
            $res = Drama::rpc('api/get-dramaid-by-soundid',
                ['sound_ids' => array_map('intval', $ids_paybydrama)]);
            $paid_drama_ids = TransactionLog::find()->select('gift_id')
                ->where([
                    'gift_id' => array_unique(array_column($res, 'drama_id')),
                    'from_id' => $user_id,
                    'type' => TransactionLog::TYPE_DRAMA,
                    'status' => TransactionLog::STATUS_SUCCESS
                ])
                ->column();
            foreach ($res as $re) {
                if (in_array($re['drama_id'], $paid_drama_ids)) {
                    $ids_paid[] = $re['sound_id'];
                }
            }
        }
        return $ids_paid;
    }

    public static function checkNeedPay(&$sounds, $user_id)
    {
        if (empty($sounds)) {
            return true;
        }
        if (is_object($sounds) || ArrayHelper::isAssociative($sounds)) {
            // 如果传进来的是字典或对象的时候，需要变成数组
            $sound_arr = [&$sounds];
            return self::checkNeedPay($sound_arr, $user_id);
        }
        $ids_paid = self::getPaidSoundIds($sounds, $user_id);
        if (is_object(current($sounds))) {
            $sounds = array_map(function ($item) use ($ids_paid) {
                if (self::SOUND_FREE === (int)$item->pay_type) {
                    $item->need_pay = self::SOUND_FREE;
                } else {
                    $item->need_pay = in_array($item->id, $ids_paid) ? self::SOUND_PAID : self::SOUND_UNPAID;
                }
                return $item;
            }, $sounds);
        } else {
            $sounds = array_map(function ($item) use ($ids_paid) {
                if (self::SOUND_FREE === (int)$item['pay_type']) {
                    $item['need_pay'] = self::SOUND_FREE;
                } else {
                    $item['need_pay'] = in_array($item['id'], $ids_paid) ? self::SOUND_PAID : self::SOUND_UNPAID;
                }
                return $item;
            }, $sounds);
        }

        MUserUnlockElement::checkUnLocked($sounds, $user_id);
    }

    public static function removeNeedPaySoundUrl(&$models)
    {
        if (empty($models)) return;

        if (is_object($models) || ArrayHelper::isAssociative($models)) {
            $models_arr = [&$models];
            self::removeNeedPaySoundUrl($models_arr);
        }
        if (is_object(current($models))) {
            foreach ($models as $item) {
                if (is_string($item->pay_type)) $item->pay_type = (int)$item->pay_type;
                if (self::SOUND_FREE !== $item->pay_type) {
                    unset($item->soundurl, $item->soundurl_32, $item->soundurl_64, $item->soundurl_128);
                }
            }
        } else {
            foreach ($models as $item) {
                if (is_string($item['pay_type'])) $item['pay_type'] = (int)$item['pay_type'];
                if (self::SOUND_FREE !== $item['pay_type']) {
                    unset($item['soundurl'], $item['soundurl_32'], $item['soundurl_64'], $item['soundurl_128']);
                }
            }
        }
    }

    public static function checkSoundId($sound_id)
    {
        if (in_array($sound_id, Yii::$app->params['sounds_for_notice'])) {
            return false;
        }
        return true;
    }

    /**
     * 获取用户对单音查看权限的 checked SQL 条件
     * @link https://github.com/MiaoSiLa/requirements-documents/blob/master/用户查看音频权限.md
     * @param boolean $full_access 是否所有音都能显示
     * @param boolean $from_drama 是否为剧集音频列表
     * @return string SQL 条件语句的字符串（例：checked = 1 或 checked IN (1, 2) 等）
     */
    public static function getCheckedInSql($full_access = false, $from_drama = false)
    {
        $checked_array = [self::CHECKED_PASS];
        if ($from_drama) {
            // 若为剧集音频列表，则包含合约期满特殊下架音频
            $checked_array[] = self::CHECKED_CONTRACT_EXPIRED;
        }
        if ($full_access) {
            $checked_array = array_merge($checked_array, [
                self::CHECKED_UNPASS,
                self::CHECKED_POLICE,
                self::CHECKED_DISCONTINUED,
            ]);
            $checked = MUtils::generateIntegerIn('checked', $checked_array);
        } elseif (!Yii::$app->user->isExam) {
            $checked = MUtils::generateIntegerIn('checked', $checked_array);
        } elseif (MUtils::isJapan()) {
            $checked_array[] = self::CHECKED_POLICE;
            $checked = MUtils::generateIntegerIn('checked', $checked_array);
        } elseif (Yii::$app->user->isLimited) {
            $catalog_ids_limited = implode(',', Catalog::LIMITED_CATALOG_IDS);
            if ($from_drama) {
                $checked = sprintf('checked IN (%d, %d) OR (checked = %d AND catalog_id NOT IN (%s))',
                    self::CHECKED_PASS, self::CHECKED_CONTRACT_EXPIRED, self::CHECKED_POLICE, $catalog_ids_limited);
            } else {
                $checked = sprintf('checked = %d OR (checked = %d AND catalog_id NOT IN (%s))',
                    self::CHECKED_PASS, self::CHECKED_POLICE, $catalog_ids_limited);
            }
        } else {
            $checked_array[] = self::CHECKED_POLICE;
            $checked = MUtils::generateIntegerIn('checked', $checked_array);
        }
        return $checked;
    }

    /**
     * 新增音频播放量
     *
     * @param int $number 新增次数
     * @throws \Exception
     */
    public function addPlayTimes(int $number)
    {
        if (Yii::$app->user->inBlacklist()) {
            // 黑名单用户不加播放量
            return;
        }
        // 检查是否为可疑刷播放量音频
        $redis = Yii::$app->redis;
        if ($redis->sIsMember(KEY_ABNORMAL_VIEWS_SOUND, $this->id) || EventVote::find()->where(['eid' => $this->id])->exists()) {
            // 若为疑似刷播放量的音频，则每个 IP 段每天刷的播放量加以限制
            $ip = MUtils2::getIPRange();
            $sound_ip_views_key = $redis->generateKey(KEY_ABNORMAL_IP_SOUND_VIEWS, $this->id);
            $sound_ip_views = (int)$redis->hGet($sound_ip_views_key, $ip);
            if ($sound_ip_views >= self::LIMIT_ABNORMAL_SOUND_VIEWS_MAX_NUM) {
                return;
            }
            $end_today = strtotime('tomorrow');
            $return = $redis->multi()
                ->hIncrBy($sound_ip_views_key, $ip, $number)
                ->expireAt($sound_ip_views_key, $end_today)
                ->exec();

            // 防止并发请求时刷播放量未被拦截
            if ($return[0] > self::LIMIT_ABNORMAL_SOUND_VIEWS_MAX_NUM) {
                return;
            }
        }
        // 正常增加播放量逻辑
        if (defined('ENABLE_ADD_PLAY_TIMES_PER_MINUTE') && ENABLE_ADD_PLAY_TIMES_PER_MINUTE) {
            $redis = Yii::$app->redis_sound_view;
            $time = MUtils::getUnitTime(ONE_MINUTE);
        } else {
            $redis = Yii::$app->redis;
            $time = strtotime('today');
        }
        $is_guest = Yii::$app->user->isGuest;
        $key = $redis->generateKey(
            KEY_COUNTER_SOUND_VIEWS,
            $is_guest ? self::PLAY_SOURCE_GUEST : self::PLAY_SOURCE_USER,
            $time
        );
        $redis->hIncrBy($key, $this->id, $number);
    }

    public static function getSearchSuggest(string $s, int $count = Discovery::SUGGEST_COUNT)
    {
        return Yii::$app->go->suggest($s, Discovery::SEARCH_SOUND, $count);
    }

    public function isForeignForbidden()
    {
        if (!Yii::$app->equip->isFromMissEvanApp()) {
            return false;
        }
        if (MUtils::isChinaMainland()) {
            return false;
        }
        $catalog_forbidden = [
            Catalog::CATALOG_ID_JAPAN_AUDIO_COMICS,
            Catalog::CATALOG_ID_JAPAN_DRAMA_TANBI,
            Catalog::CATALOG_ID_JAPAN_DRAMA_OTOME,
            Catalog::CATALOG_ID_JAPAN_DRAMA_COMMON,
        ];
        if (in_array($this->catalog_id, $catalog_forbidden)) {
            return !Yii::$app->redis->sIsMember(KEY_FOREIGN_FORBIDDEN_SOUND_EXEMPTED_USER_ID, $this->user_id);
        }
        return false;
    }

    public function getIsForbiddenInJapan(): bool
    {
        return (self::REFINED_FORBID_JAPAN & $this->refined) != 0;
    }

    /**
     * 获取推荐模块中音频的数据
     *
     * @param array $ids 音频 ID
     * @return array
     */
    public static function getRecommendedSounds($ids)
    {
        $sounds = self::find()
            ->alias('t')
            ->select('t.id, t.soundstr, t.intro, t.view_count, t.comment_count, t.comments_count, t.sub_comments_count,
                t.cover_image, t.user_id, t1.username')
            ->leftJoin(Mowangskuser::tableName() . ' AS t1', 't.user_id = t1.id')
            ->where(['t.id' => $ids])
            ->all();
        $video_sound_ids = SoundVideo::getVideoSoundIds($ids);
        return array_map(function ($sound) use ($video_sound_ids) {
            $return = [
                'id' => $sound->id,
                'front_cover' => $sound->front_cover,
                'soundstr' => $sound->soundstr,
                'intro' => $sound->intro,
                'view_count' => $sound->view_count,
                'comment_count' => $sound->comment_count,
                'all_comments' => $sound->all_comments,
                'user_id' => $sound->user_id,
                'username' => $sound->username,
            ];
            if (in_array($sound->id, $video_sound_ids)) {
                $return['video'] = true;
            }
            return $return;
        }, $sounds);
    }

    /**
     * 获取铃声设置信息
     *
     * @param self $sound
     * @return int
     */
    public static function getRingtone(self $sound): int
    {
        $ringtone = 0;
        if (in_array($sound->catalog_id, Yii::$app->params['ringtone_catalog_ids'])
                && $sound->type !== self::TYPE_INTERACTIVE) {
            // 若为可设置铃声分类且不为互动剧音频，显示设置铃声按钮
            $ringtone |= self::RINGTONE_CAN_SET;
        }
        // 可下载完整音频分类
        $DOWNLOAD_COMPLETE_CATALOG_IDS = [
            Catalog::CATALOG_ID_RINGTONE_CALL,
            Catalog::CATALOG_ID_RINGTONE_SMS,
            Catalog::CATALOG_ID_RINGTONE_CLOCK,
        ];
        if (in_array($sound->catalog_id, $DOWNLOAD_COMPLETE_CATALOG_IDS)) {
            $ringtone |= self::RINGTONE_COMPLETE_DOWNLOAD;
        }
        return $ringtone;
    }

    /**
     * 获取音频 playurl 签名地址
     *
     * @param array $sound 音频信息
     * @param bool $use_bvc_cdn 是否全量使用 bvc cdn
     * @param int $backup 返回的备用 url 个数，>= 1 时获取列表形式的地址
     */
    public static function getSoundSignUrls(&$sound, $use_bvc_cdn = false, int $backup = 0)
    {
        $handled = false;
        $key_soundurl_128 = self::KEY_SOUNDURL_128;
        $key_soundurl_64 = self::KEY_SOUNDURL_64;
        $soundurl_list = self::KEY_SOUNDURL . '_list';
        $soundurl_128_list = self::KEY_SOUNDURL_128 . '_list';
        // 在 Mowangskuser::getPersonInfo 调用此方法时，传入的参数 $sound 不是 MSound 模型的实例，没有相关 _list 属性，因此需要单独判断
        $has_soundurl_list = property_exists($sound, $soundurl_list);
        $has_soundurl_128_list = property_exists($sound, $soundurl_128_list);
        try {
            // 判断是否需要返回 playurl 签名地址
            if (self::isBVCSoundCDN($use_bvc_cdn)) {
                $upos_uris = [];
                $keys = [];

                // 目前 soundurl_64 存的是源音频码率（不超过转码后最大码率），soundurl_32 为 192k，soundurl_128 为 128k
                $url_types = [self::KEY_SOUNDURL_64, self::KEY_SOUNDURL_128, self::KEY_SOUNDURL_32];

                // TEMP: 特殊用户访问特殊音频，需返回特殊音源
                if (self::isSpecialUser() && $special_sound = self::getSpecialSound($sound->id)) {
                    foreach ($url_types as $url_type) {
                        $sound_url = self::getSoundPathByEquipment($special_sound->getOldAttribute($url_type));
                        if (!$sound_url) {
                            $sound->$url_type = $special_sound->$url_type;
                            continue;
                        }
                        if ($upos_uri = self::getUposURI($sound_url)) {
                            $upos_uris[] = $upos_uri;
                            $keys[] = $url_type;
                        } else {
                            $sound->$url_type = $special_sound->$url_type;
                        }
                    }
                } else {
                    foreach ($url_types as $url_type) {
                        $sound_url = self::getSoundPathByEquipment($sound->getOldAttribute($url_type));
                        if (!$sound_url) {
                            continue;
                        }
                        if ($upos_uri = self::getUposURI($sound_url)) {
                            $upos_uris[] = $upos_uri;
                            $keys[] = $url_type;
                        }
                    }
                }
                if (!empty($upos_uris)) {
                    $sound_sign_urls = self::getSignUrls($upos_uris, $backup);
                    if (!empty($sound_sign_urls)) {
                        if ($has_soundurl_list) {
                            $sound->$soundurl_list = $sound->$key_soundurl_64 ? [$sound->$key_soundurl_64] : null;
                        }
                        if ($has_soundurl_128_list) {
                            $sound->$soundurl_128_list = $sound->$key_soundurl_128 ? [$sound->$key_soundurl_128] : null;
                        }
                        foreach ($keys as $i => $key) {
                            $sound->$key = $sound_sign_urls[$i][0];
                            if ($has_soundurl_list && $key === self::KEY_SOUNDURL_64) {
                                $sound->$soundurl_list = $sound_sign_urls[$i];
                            } elseif ($has_soundurl_128_list && $key === self::KEY_SOUNDURL_128) {
                                $sound->$soundurl_128_list = $sound_sign_urls[$i];
                            }
                        }
                        $key_soundurl = self::KEY_SOUNDURL;
                        $sound->$key_soundurl = $sound->$key_soundurl_64;
                        $handled = true;
                    }
                }
            }
        } catch (\Exception $e) {
            // 若请求出错，将错误记录到日志中
            Yii::error('获取音频 playurl 签名地址错误：' . $e->getMessage(), __METHOD__);
            // PASS
        }
        if (!$handled) {
            $sound->soundurl = $sound->$key_soundurl_64;
            if ($has_soundurl_list) {
                $sound->$soundurl_list = $sound->$key_soundurl_64 ? [$sound->$key_soundurl_64] : null;
            }
            if ($has_soundurl_128_list) {
                $sound->$soundurl_128_list = $sound->$key_soundurl_128 ? [$sound->$key_soundurl_128] : null;
            }
        }
    }

    /**
     * 协议地址替换成 upos 地址
     *
     * @param $sound_url
     * @return false|string
     */
    public static function getUposURI($sound_url)
    {
        // 判断协议地址是否是 sound:// 开头且不是 mp3 结尾
        if (strpos($sound_url, 'sound://') === 0
                && substr($sound_url, -4) !== '.mp3') {
            $protocol_url = Yii::$app->upos->protocolUrl;
            $sound_url = str_replace('sound://', '', $sound_url);
            return $protocol_url . '/mefmxcodeboss/' . $sound_url;
        }
        return false;
    }

    /**
     * 判断是否为特殊用户
     *
     * @return bool
     * @throws \RedisException
     */
    private static function isSpecialUser()
    {
        if ($user_id = Yii::$app->user->id) {
            return Yii::$app->redis->sIsMember('sound:special_user_id', $user_id);
        }
        return false;
    }

    private static function getSpecailSoundId($sound_id)
    {
        $special_id = Yii::$app->redis->hGet('sound:special_sound', $sound_id);
        if (!$special_id) {
            return false;
        }
        return $special_id;
    }

    private static function getSpecialSound($sound_id)
    {
        if ($special_sound_id = self::getSpecailSoundId($sound_id)) {
            return MSound::findOne(['id' => $special_sound_id]);
        }
        return false;
    }

    /**
     * upos 地址添加签名
     *
     * @param array $upos_uris
     * @param int $backup 返回的备用 url 个数
     * @return mixed
     */
    public static function getSignUrls(array $upos_uris, int $backup = 0)
    {
        $equipment = Yii::$app->equip;
        if ($equipment->isAndroidOrHarmonyOS()) {
            $platform = UposClient::PLATFORM_ANDROID;
        } elseif ($equipment->isIOS()) {
            $platform = UposClient::PLATFORM_IPHONE;
        } else {
            // 对于其他情况，都用 html5
            // WORKAROUND: 考虑 Safari 浏览器播放不了音频的问题，暂时使用 html5，后续考虑优化
            $platform = UposClient::PLATFORM_HTML5;
        }
        $force_host = 0;
        if (Equipment::isFromGoogleChannel()) {
            // Google Play 渠道安装的客户端，下发 https 播放地址
            $force_host = UposClient::FORCE_HOST_HTTPS;
        }
        // WORKAROUND: 海外 Android 系统 < 7.1.1 的设备下发 http 播放地址，解决 Akamai 默认证书信任的问题
        // https://community.letsencrypt.org/t/letsencrypt-certificates-fails-on-android-phones-running-android-7-or-older/205686
        if (Yii::$app->equip->isAndroid()
                && version_compare(Yii::$app->equip->osVersion, '7.1.1', '<')
                && !MUtils2::isChinaMainland()) {
            $force_host = UposClient::FORCE_HOST_HTTP;
        }
        return Yii::$app->upos->getSignUrls($upos_uris, $platform, '', $force_host, $backup);
    }

    /**
     * 判断是否为视频云的音频 CDN 允许的灰度地区
     *
     * @param bool $use_bvc_cdn 是否全量使用 bvc cdn
     * @return boolean
     */
    public static function isBVCSoundCDN(bool $use_bvc_cdn = false): bool
    {
        if (!ENABLE_SOUND_BVC_CDN) {
            return false;
        }
        $is_bvc_sound_cdn = true;
        // WORKAROUND: iOS < 4.7.4 版本请求的参数有问题，针对视频云要求的参数会通过不了，故不返回音频签名地址
        // iOS < 4.8.7 版本对签名 url 支持存在问题
        // Android < 5.6.5 版本缓存 key 处理有问题，Android < 5.7.3 版本联通免流包转换签名地址有问题
        if (Equipment::isAppOlderThan('4.8.7', '5.7.3')) {
            $is_bvc_sound_cdn = false;
        }
        $env = getenv('DEPLOY_ENV', true);
        if ($is_bvc_sound_cdn && $env === 'prod' && !$use_bvc_cdn
                && (crc32(Yii::$app->equip->getEquipId()) % 100) >= SOUND_BVC_CDN_TEST_RATIO) {
            // WORKAROUND: 对于线上环境，若不为全量使用 bvc cdn，则进行灰度
            $is_bvc_sound_cdn = false;
        }
        if (!$is_bvc_sound_cdn && self::isUposUser()) {
            $is_bvc_sound_cdn = true;
        }
        return $is_bvc_sound_cdn;
    }

    /**
     * 是否为用户下发视频云音频签名地址
     */
    public static function isUposUser()
    {
        if (!is_null(self::$is_upos_user)) {
            return self::$is_upos_user;
        }
        self::$is_upos_user = false;

        if ($user_id = Yii::$app->user->id) {
            self::$is_upos_user = Yii::$app->redis->sIsMember(KEY_UPOS_USER_IDS_WHITE_LIST, $user_id);
        }
        return self::$is_upos_user;
    }

    /**
     * 批量更新音频的收藏数冗余字段
     *
     * @param array $sound_album_map 音频和对应音单数量的 map，例 [13752 => 2, 13753 => 1]
     * @return int
     * @throws \yii\db\Exception
     */
    public static function updateSoundFavoriteCount(array $sound_album_map): int
    {
        if (!$sound_album_map) {
            return 0;
        }
        $sound_ids = array_keys($sound_album_map);
        $album_count_arr = array_unique(array_values($sound_album_map));
        if (count($album_count_arr) === 1) {
            // 需要批量更新的音频收藏数一致
            $favorite_count = $album_count_arr[0];
            $expression = new Expression('GREATEST(favorite_count, :favorite_count) - :favorite_count',
                [':favorite_count' => $favorite_count]);
            return self::updateAll(['favorite_count' => $expression], ['id' => $sound_ids]);
        }
        // 需要批量更新的音频收藏数不一致
        // 生成的批量更新音频收藏数的 SQL 如下：
        // UPDATE m_sound
        // SET `favorite_count` = CASE id
        //   WHEN 13752 THEN GREATEST(favorite_count, 2) - 2
        //   WHEN 13753 THEN GREATEST(favorite_count, 1) - 1
        // END
        // WHERE id IN (13752, 13753) AND user_id = 1
        $table_name = self::tableName();
        $in_sound_condition = MUtils::generateIntegerIn('id', $sound_ids);
        $case_value = 'CASE id';
        foreach ($sound_album_map as $sound_id => $favorite_count) {
            $case_value .= " WHEN {$sound_id} THEN GREATEST(favorite_count, {$favorite_count}) - {$favorite_count}";
        }
        $case_value .= ' END';
        $sql = <<<SQL
UPDATE {$table_name}
SET `favorite_count` = {$case_value}
WHERE {$in_sound_condition};
SQL;
        return Yii::$app->db->createCommand($sql)->execute();
    }

    /**
     * 检查播放设备量上限
     *
     * @param MSound $sound 音频信息
     * @param int $user_id 用户 ID
     * @param string $equip_id 设备号
     * @param bool $old_version 客户端版本是否为旧版本（不支持心跳上报播放日志的版本）
     * @param int $download 是否为下载。0：不是下载；1：是下载
     * @return boolean true：限制；false：不限制
     */
    public static function checkEquipPlayNumLimit(MSound $sound, int $user_id,
            string $equip_id, bool $old_version, int $download): bool
    {
        if (!ENABLE_EQUIP_PLAY_LIMIT) {
            return false;
        }
        // 未登录用户不受限制
        if (!$user_id) {
            return false;
        }
        // 下载不受限制
        if ($download && Yii::$app->equip->isAndroid()) {
            // WORKAROUND: Android 客户端下载音频不受设备播放数量限制，iOS 待支持下载的音频也能被限制后，再加上下载不受限的规则
            return false;
        }
        $is_vip_limit = $sound->episode_vip === Drama::EPISODE_VIP_LIMIT;
        // 免费音且属于非会员受限制音不受限制
        if (self::SOUND_FREE === $sound->pay_type && !$is_vip_limit) {
            return false;
        }
        // 内网（预发环境）不受限制
        if (MUtils2::isPreEnv()) {
            return false;
        }
        // 用户白名单不受限制
        if (Yii::$app->redis->sIsMember(KEY_EQUIP_PLAY_LIMIT_USER_IDS_ALLOW_LIST, $user_id)) {
            return false;
        }
        $user = Mowangskuser::find()
            ->select('confirm')
            ->where('id = :id', [':id' => $user_id])
            ->one();
        // 蓝 V 用户不受限制
        if ($user && $user->authenticated === Mowangskuser::CONFIRM_BLUE_VIP) {
            return false;
        }

        // 心跳上报间隔（单位：秒）
        $HEARTBEAT_INTERVAL = 300;
        $now = $_SERVER['REQUEST_TIME'];
        $redis = Yii::$app->redis;
        $equip_play_key = $redis->generateKey(KEY_EQUIP_PLAY_USER_ID, $user_id);
        $equip_ids = $redis->zRangeByScore($equip_play_key, $now - $HEARTBEAT_INTERVAL, $now);
        if (count($equip_ids) < self::LIMIT_EQUIP_PLAY_NUM || in_array($equip_id, $equip_ids)) {
            if (!$old_version) {
                // 当用户当前正在播放设备未超过限制数量，或当前设备处于正常播放设备的集合中时，
                // 将设备添加到正常播放设备的集合（当前设备已处于正常播放设备的集合中时，更新该设备开始播放的时间点）
                // WORKAROUND: 客户端为旧版本时无心跳上报播放日志功能，不加入正常播放设备的集合，避免无法持续更新开始播放的时间点
                $redis->multi()
                    ->zAdd($equip_play_key, $now, $equip_id)
                    // 考虑到网络延迟等问题，key 有效期在一个心跳周期的基础上增加 5s 冗余
                    ->expire($equip_play_key, $HEARTBEAT_INTERVAL + 5)
                    ->exec();
            }
            return false;
        }
        return true;
    }

    /**
     * 补充限制播放类型
     * 优先级：会员收听 > 付费 > 播放受限
     *
     * @param int $user_id 用户 ID
     * @param int $download 是否为下载。0：不是下载；1：是下载
     */
    public function fillLimitType(int $user_id, int $download)
    {
        $is_vip_limit = $this->episode_vip === Drama::EPISODE_VIP_LIMIT;
        $is_pay_limit = in_array($this->pay_type, [self::PAY_BY_SOUND, self::PAY_BY_DRAMA]);
        $user_is_vip = false;
        if ($is_vip_limit || $is_pay_limit) {
            $user_is_vip = MUserVip::isVipUser($user_id);
        }
        if ($is_vip_limit && !$user_is_vip && (!$is_pay_limit || $this->need_pay === self::SOUND_UNPAID)) {
            $this->limit_type = self::LIMIT_TYPE_VIP_PLAY;
            return;
        }

        if ($is_pay_limit && $this->need_pay === self::SOUND_UNPAID && !$is_vip_limit) {
            $this->limit_type = $this->pay_type === self::PAY_BY_DRAMA ? self::LIMIT_TYPE_DRAMA : self::LIMIT_TYPE_EPISODE;
            return;
        }

        $old_version = Equipment::isAppOlderThanVipVersion();
        $equip_play_limit = self::checkEquipPlayNumLimit($this, $user_id, Yii::$app->equip->getEquipId(),
            $old_version, $download);
        if ($equip_play_limit) {
            $this->limit_type = self::LIMIT_TYPE_EQUIP_PLAY;
            return;
        }
    }
}
