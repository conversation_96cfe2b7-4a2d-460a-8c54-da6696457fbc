<?php

namespace app\models;

use missevan\util\MUtils;
use Yii;

/**
 * This is the model class for table "m_third_party_task".
 *
 * @property int $id 主键
 * @property int $create_time 创建时间戳，单位：秒
 * @property int $modified_time 更新时间戳，单位：秒
 * @property int $task_time 任务发起时间戳，单位：秒
 * @property int $user_id 用户 ID
 * @property string $token 任务 Token
 * @property int $scene 第三方场景 1：百度；2：携程；3：大众点评
 * @property int $status 任务状态 0：未完成；1：已完成未领取奖励；2：已完成已领取奖励
 */
class MThirdPartyTask extends ActiveRecord
{
    use ActiveRecordTrait;

    // 任务状态
    const TASK_STATUS_ON_GOING = 0;  // 未完成
    const TASK_STATUS_FINISHED = 1;  // 已完成未领取奖励
    const TASK_STATUS_REWARDED = 2;  // 已完成已领取奖励

    // 场景
    const SCENE_BAIDU = 1;  // 百度
    const SCENE_CTRIP = 2;  // 携程
    const SCENE_DIANPING = 3;  // 大众点评
    const SCENE_BAIDUMAP = 4;  // 百度地图
    const SCENE_YOUKU = 5;  // 优酷视频
    const SCENE_QQ_BROWSER = 6;  // QQ 浏览器
    const SCENE_WEIBO = 7;  // 微博
    const SCENE_QUARK = 8;  // 夸克
    const SCENE_WECHAT_OFFIACCOUNT = 9;  // 微信公众号
    const SCENE_LOFTER = 10;  // LOFTER
    const SCENE_QQ_MUSIC = 11;  // QQ 音乐
    const SCENE_QIDIAN = 12;  // 起点

    const SCENE_ARR = [
        self::SCENE_BAIDU,
        self::SCENE_CTRIP,
        self::SCENE_DIANPING,
        self::SCENE_BAIDUMAP,
        self::SCENE_YOUKU,
        self::SCENE_QQ_BROWSER,
        self::SCENE_WEIBO,
        self::SCENE_QUARK,
        self::SCENE_WECHAT_OFFIACCOUNT,
        self::SCENE_LOFTER,
        self::SCENE_QQ_MUSIC,
        self::SCENE_QIDIAN,
    ];

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'm_third_party_task';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['task_time', 'user_id', 'token', 'scene', 'status'], 'required'],
            [['create_time', 'modified_time', 'task_time', 'user_id', 'scene', 'status'], 'integer'],
            [['user_id', 'task_time', 'scene'], 'unique', 'targetAttribute' => ['user_id', 'task_time', 'scene']],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'create_time' => '创建时间',
            'modified_time' => '修改时间',
            'task_time' => '任务发起时间',  // 时间戳，单位：秒
            'user_id' => '用户 ID',
            'token' => '任务 Token',
            'scene' => '任务场景',  // 第三方场景 1：百度；2：携程；3：大众点评
            'status' => '任务状态',  // 0：未完成；1：已完成未领取奖励；2：已完成已领取奖励
        ];
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->create_time = $time;
        }
        $this->modified_time = $time;
        return true;
    }

    /**
     * 生成任务唯一 token
     *
     * @return string 返回唯一 token
     * @throws \Exception
     * @todo 后面可换成 uuid v4 算法
     */
    private static function buildToken(): string
    {
        $now = $_SERVER['REQUEST_TIME'];
        $random_key = MUtils::randomKeys(5, 7);
        return md5(uniqid() . $now . $random_key);
    }

    /**
     * 获取任务 token
     *
     * @param int $user_id 用户 ID
     * @param int $task_time 任务发起时间
     * @param int $scene 任务场景
     * @return null|string 任务 token
     * @throws \Exception
     */
    public static function getToken(int $user_id, int $task_time, int $scene)
    {
        $token = self::find()
            ->select('token')
            ->where('user_id = :user_id AND task_time = :task_time AND scene = :scene AND status = :status', [
                ':user_id' => $user_id,
                ':task_time' => $task_time,
                ':scene' => $scene,
                ':status' => self::TASK_STATUS_ON_GOING,
            ])->scalar();
        if ($token) {
            return $token;
        }
        try {
            $model = new self();
            $model->user_id = $user_id;
            $model->task_time = $task_time;
            $model->scene = $scene;
            $model->token = self::buildToken();
            $model->status = self::TASK_STATUS_ON_GOING;
            if (!$model->save()) {
                Yii::error('创建三方导流任务记录失败，原因：' . MUtils::getFirstError($model), __METHOD__);
                // PASS
                return null;
            }
        } catch (\yii\db\Exception $e) {
            // 忽略唯一索引错误
            if (!MUtils::isUniqueError($e, self::getDb())) {
                Yii::error('创建三方导流任务记录失败，原因：' . $e->getMessage(), __METHOD__);
            }
            return null;
        }
        return $model->token;
    }

    /**
     * 获取任务完成状态
     *
     * @param int $user_id 用户 ID
     * @param int $task_time 任务发起时间
     * @param int $scene 任务场景
     * @return int
     */
    public static function getTaskStatus(int $user_id, int $task_time, int $scene): int
    {
        $conditions = [
            'user_id' => $user_id,
            'scene' => $scene,
        ];
        if ($task_time) {
            $conditions['task_time'] = $task_time;
        }
        return (int)self::find()->select('status')
            ->where($conditions)->scalar();
    }

    /**
     * 更新【已完成未领取奖励】任务状态为【已完成已领取奖励】
     *
     * @param int $user_id 用户 ID
     * @param int $task_time 任务发起时间
     * @param int $scene 任务场景
     * @return int
     */
    public static function updateFinishedTaskStatus(int $user_id, int $task_time, int $scene): int
    {
        $conditions = [
            'user_id' => $user_id,
            'scene' => $scene,
            'status' => self::TASK_STATUS_FINISHED,
        ];
        if ($task_time) {
            $conditions['task_time'] = $task_time;
        }
        return self::updateAll(['status' => self::TASK_STATUS_REWARDED], $conditions);
    }
}
