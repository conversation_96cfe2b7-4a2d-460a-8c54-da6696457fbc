<?php

namespace app\models;

use app\forms\UserContext;
use missevan\util\MUtils as MUtils2;
use Exception;
use Yii;
use yii\db\Expression;

/**
 * @property int $id 主键
 * @property int $create_time 创建时间，单位：秒
 * @property int $modified_time 修改时间，单位：秒
 * @property int $user_id 用户 ID
 * @property int $vip_id 领取时的会员 ID
 * @property int $coin_num 领取的钻石数
 * @property int $receive_time 领取的时间，单位：秒
 * @property array $more 更新详情（JSON 格式）
 */
class VipReceiveCoinLog extends ActiveRecord
{
    use ActiveRecordTrait;

    /**
     * 用户今天是否已领过会员钻石
     *
     * @param int $user_id
     * @return bool
     */
    public static function isReceivedToday(int $user_id): bool
    {
        if ($user_id <= 0) {
            return false;
        }
        $today = strtotime('today', $_SERVER['REQUEST_TIME']);
        $tomorrow = strtotime('midnight +1 day', $_SERVER['REQUEST_TIME']);
        return self::find()
            ->where(['user_id' => $user_id])
            ->andWhere('receive_time >= :today AND receive_time < :tomorrow',
                [':today' => $today, ':tomorrow' => $tomorrow])
            ->exists();
    }

    /**
     * 领取每日会员福利钻石
     *
     * @param int $user_id 用户 ID
     * @param int $coin_num 钻石数量
     * @param int $topup_menu_id topup_menu 表 ID
     * @param int $vip_id 会员 ID
     * @return bool
     * @throws \yii\db\Exception
     */
    public static function receiveCoinToday(int $user_id, int $coin_num, int $topup_menu_id, int $vip_id): void
    {
        RechargeOrder::batchGenerateCashOrder($topup_menu_id, [$user_id], $coin_num,
            UserContext::fromUser(Yii::$app->request, Yii::$app->equip), RechargeOrder::TYPE_VIP, PayAccount::COIN_FIELD_VIP, [],
            null, function (array $order_ids) use ($user_id, $vip_id, $coin_num) {
                $receive_log = new self([
                    'user_id' => $user_id,
                    'vip_id' => $vip_id,
                    'coin_num' => $coin_num,
                    'receive_time' => $_SERVER['REQUEST_TIME'],
                    'more' => [
                        'order_id' => $order_ids[0],
                    ],
                ]);
                if (!$receive_log->save()) {
                    throw new Exception(MUtils2::getFirstError($receive_log));
                }
            });
    }
}
