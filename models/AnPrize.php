<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "an_prize".
 *
 * @property integer $id
 * @property string $name
 * @property string $pic
 * @property integer $probability
 * @property integer $num
 * @property integer $event_id
 */
class AnPrize extends ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'an_prize';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['event_id', 'num', 'probability'], 'integer'],
            [['name', 'pic'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键 ID',
            'name' => '奖品名',
            'pic' => '奖品图片',
            'probability' => '奖品概率',  // 单位：%
            'num' => '奖品数量',
            'event_id' => '活动 ID',
        ];
    }
}
