<?php

namespace app\models;

use Exception;
use Yii;

class AdTrackKua<PERSON><PERSON><PERSON> extends AdTrack implements AdTrackInterface
{

    const CALLBACK_GATEWAY = 'http://ad.partner.gifshow.com/track/activate';

    const CALLBACK_EVENT_TYPE_MAP = [
        self::CALLBACK_EVENT_ACTIVATE => self::CALLBACK_EVENT_TYPE_ACTIVATE,
        self::CALLBACK_EVENT_ONE_DAY_RETENTION => self::CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION,
        self::CALLBACK_EVENT_PAY => self::CALLBACK_EVENT_TYPE_PAY,
        self::CALLBACK_EVENT_REGISTER => self::CALLBACK_EVENT_TYPE_REGISTER,
        // TODO: 收听 5 个音的关键行为已调整为消费 / 充值关键行为，之后可删除此类型
        self::CALLBACK_EVENT_KEY_ACTION => self::CALLBACK_EVENT_TYPE_KEY_ACTION,
        self::CALLBACK_EVENT_TRANSACTION => self::CALLBACK_EVENT_TYPE_KEY_ACTION,  // 消费 / 充值关键行为
    ];

    // 激活
    const CALLBACK_EVENT_TYPE_ACTIVATE = 1;
    // 付费
    const CALLBACK_EVENT_TYPE_PAY = 3;
    // 次日留存
    const CALLBACK_EVENT_TYPE_ONE_DAY_RETENTION = 7;
    // 注册
    const CALLBACK_EVENT_TYPE_REGISTER = 2;
    // 关键行为
    const CALLBACK_EVENT_TYPE_KEY_ACTION = 143;

    // 关键行为类型：其它
    const KEY_ACTION_CATEGORY_OTHERS = 0;

    /**
     * 转化事件回调
     *
     * @link https://yiqixie.qingque.cn/d/home/<USER>

     * @param string $event_type 事件类型
     * @param mixed $arg 付费金额（单位：元），或关键行为参数
     */
    public function callback(string $event_type, $arg = 0)
    {
        try {
            if (!array_key_exists($event_type, self::CALLBACK_EVENT_TYPE_MAP)) {
                throw new Exception('快手广告点击回传事件错误：' . $event_type);
            }
            $event = self::CALLBACK_EVENT_TYPE_MAP[$event_type];
            if (is_null($event)) {
                return true;
            }
            $data = Yii::$app->tools->requestRemote($this->getCallbackUrl($event, $_SERVER['REQUEST_TIME'] * 1000, $arg));
            if (!($data && $data['result'] === 1)) {
                throw new Exception(sprintf('快手广告点击回传失败：code[%d], msg[%s]', $data['result'], $data['error_msg'] ?? ''));
            }
            return true;
        } catch (Exception $e) {
            Yii::error('kuaishou ad error: ' . $e->getMessage(), __METHOD__);
        }
        return false;
    }

    /**
     * 获取回传的 URL
     *
     * @param int $callback_event 事件类型
     * @param int $event_time 转化时间
     * @param mixed $arg 付费金额（单位：元），关键行为参数
     * @return string
     */
    private function getCallbackUrl(int $callback_event, int $event_time, $arg = 0): string
    {
        // track_id 形式：http://ad.partner.gifshow.com/track/activate?callback=xxxxxxxxxx
        if ($callback_event === self::CALLBACK_EVENT_TYPE_PAY) {
            // 单位：元
            $purchase_amount = round($this->getCallbackPayAmount($arg, self::CALLBACK_PAY_UNIT_YUAN), 0);
            // 付费回传文档：https://yiqixie.qingque.cn/d/home/<USER>
            return sprintf('%s&event_type=%d&event_time=%d&purchase_amount=%s',
                $this->track_id,
                $callback_event,
                $event_time,
                $purchase_amount
            );
        }
        if ($callback_event === self::CALLBACK_EVENT_TYPE_KEY_ACTION) {
            return sprintf(
                '%s&event_type=%d&event_time=%d&key_action_category=%d&key_action_threshold=%d',
                $this->track_id, $callback_event, $event_time, self::KEY_ACTION_CATEGORY_OTHERS, $arg
            );
        }

        return sprintf('%s&event_type=%d&event_time=%d', $this->track_id, $callback_event, $event_time);
    }

    /**
     * @inheritdoc
     */
    protected function getAdNameToParse()
    {
        return $this->getAdName(self::AD_LEVEL_ONE);
    }

}
