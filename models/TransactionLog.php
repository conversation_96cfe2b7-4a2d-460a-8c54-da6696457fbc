<?php

namespace app\models;

use app\components\util\Equipment;
use app\components\util\MUtils;
use app\forms\TransactionFormLive;
use Exception;
use Yii;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\web\HttpException;

/**
 * This is the model class for table "transaction_log".
 *
 * @property integer $id
 * @property integer $from_id
 * @property integer $to_id
 * @property integer $c_time
 * @property integer $gift_id
 * @property string $title
 * @property integer $ios_coin
 * @property integer $tmallios_coin
 * @property integer $android_coin
 * @property integer $paypal_coin
 * @property integer $googlepay_coin
 * @property integer $all_common_coin
 * @property integer $all_coin
 * @property integer $revenue
 * @property double $income
 * @property double $tax
 * @property double $rate
 * @property integer $num
 * @property integer $status
 * @property integer $type
 * @property integer $suborders_num
 * @property integer $attr
 * @property integer $create_time
 * @property integer $modified_time
 * @property integer $confirm_time
 *
 * @property integer $guild_id
 * @property array $trade_detail
 * @property integer $vip_coin
 * @property integer $new_ios_coin
 * @property integer $new_googlepay_coin
 */
class TransactionLog extends ActiveRecord
{
    const SCENARIO_PURCHASE = 'purchase';
    const SCENARIO_NEW_PURCHASE = 'new_purchase';
    const SCENARIO_INCOME = 'income';

    // 交易成功
    const STATUS_SUCCESS = 1;
    const STATUS_ALL = 0;
    // 未完成（直播问答提问中、直播间飞镖未确认订单）
    const STATUS_UNDONE = -1;
    // 直播问答取消、飞镖退钻订单
    const STATUS_CANCEL = -2;
    // 已退款（人民币）
    const STATUS_REFUND = -3;
    // 已退款（猫耳钻石）
    const STATUS_REFUND_DIAMOND = -4;
    // 代充取消交易记录
    const STATUS_ILLEGAL_TOPUP = -5;

    const TYPE_ALL = 0;
    const TYPE_LIVE = 1;
    const TYPE_SOUND = 2;
    const TYPE_DRAMA = 3;
    const TYPE_BOYFRIEND = 4;

    // 抽卡
    const TYPE_DRAW_CARD = 5;
    // 购买卡片季包
    const TYPE_CARD_PACKAGE = 6;
    // 剧集打赏
    const TYPE_REWARD = 7;
    // 求签
    const TYPE_OMIKUJI = 8;
    // 公会直播收益
    const TYPE_GUILD_LIVE = 9;
    // 喵喵周卡活动礼包
    const TYPE_EVENT723 = 10;
    // 魔力赏活动
    const TYPE_MAGIC = 11;

    const ATTR_COMMON = 0;
    // 直播续费贵族（type 为 1 或 9 时）
    const ATTR_LIVE_RENEWAL_NOBLE = 1;
    // 直播开通贵族（type 为 1 或 9 时）
    const ATTR_LIVE_REGISTER_NOBLE = 2;
    // 直播间白给礼物、福袋礼物（type 为 1 或 9 时）
    const ATTR_LIVE_REBATE_GIFT = 3;
    // 直播间幸运签礼物（type 为 1 或 9 时）
    const ATTR_LIVE_LUCKY_GIFT = 4;
    // 直播间开通超粉（type 为 1 或 9 时）
    const ATTR_LIVE_REGISTER_SUPER_FAN = 5;
    // 直播间续费超粉（type 为 1 或 9 时）
    const ATTR_LIVE_RENEWAL_SUPER_FAN = 6;
    // 直播间购买福袋
    const ATTR_LIVE_BUY_FUKUBUKURO = 7;
    // 直播间购买超能魔盒
    const ATTR_LIVE_BUY_GASHAPON = 8;
    // 直播间超能魔盒礼物
    const ATTR_LIVE_GASHAPON_GIFT = 9;
    // 直播间许愿池
    const ATTR_LIVE_WISH_POOL = 10;
    // 直播间礼物红包
    const ATTR_LIVE_RED_PACKET = 11;
    // 直播使用贵族体验卡（开通体验贵族）
    const ATTR_LIVE_REGISTER_NOBLE_TRIAL = 12;
    // 直播使用贵族体验卡（续期体验贵族）
    const ATTR_LIVE_RENEWAL_NOBLE_TRIAL = 13;
    // 直播付费弹幕
    const ATTR_LIVE_DANMAKU = 14;
    // 直播间购买喵喵福袋
    const ATTR_LIVE_BUY_LUCKY_BAG = 15;
    // 直播间购买宝盒
    const ATTR_LIVE_BUY_LUCKY_BOX = 16;
    // 直播间代币（飞镖等）
    const ATTR_LIVE_TOKEN = 17;

    // 随机礼物开出的礼物数量
    const LUCKY_GIFT_OPEN_GIFT_NUM = 1;

    // 剧集通过兑换而来（type 为 2 或 3）
    const ATTR_DRAMA_REDEEM = 2;
    // 剧集通过抖店购得（type 为 3）
    const ATTR_DRAMA_DOUDIAN = 3;
    // 剧集通过福袋兑换而来（type 为 3）
    const ATTR_DRAMA_LUCKY_BAG = 4;

    // attr 位运算掩码：mask 之后用于对比上面的 attr 枚举值
    const ATTR_BASE_BIT_MASK = 0xfff;
    // 是否支持退款后再次购买（type 为 2 或 3）
    const ATTR_REPURCHASE_AFTER_REFUND = 0x1000;

    // 剧集订单数据来源
    const SOURCE_BOUGHT = 1;  // 已购管理
    const SOURCE_DELETE = 2;  // 删除的剧集订单

    // 排序方式
    const ORDER_BY_BOUGHT_TIME = 1;  // 按购买时间降序
    const ORDER_BY_DRAMA_NAME = 2;  // 按剧集名 A-Z

    public $coin;
    public $profit;
    public $detail;
    public $transaction_id;
    public $status_msg;

    public $live_noble_balance_cost = 0;
    public $details = [];

    private $_trade_detail = [];

    /**
     * @return \yii\db\Connection the database connection used by this AR class.
     */
    public static function getDb()
    {
        return Yii::$app->paydb;
    }

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'transaction_log';
    }

    public static function coinFields()
    {
        return array_map(function ($field) { return $field . '_coin'; }, Balance::balanceFields());
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['from_id', 'to_id', 'gift_id'], 'required'],
            [
                array_merge(
                    [
                        'from_id', 'to_id', 'c_time', 'gift_id', 'attr'
                    ],
                    self::coinFields(),
                    [
                        'all_coin', 'num', 'status', 'type', 'revenue'
                    ],
                    [
                        'create_time', 'modified_time', 'confirm_time'
                    ]
                ),
                'integer',
            ],
            [['income', 'tax', 'rate'], 'number'],
            [['title'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        $result = [];
        foreach (Balance::balanceFields() as $field) {
            $result[$field . '_coin'] = $field . ' 收入';
        }

        return array_merge([
                'id' => 'ID',
                'from_id' => '消费者',
                'to_id' => '生产者',
                'c_time' => '订单创建时间',
                'gift_id' => '订单元素类型',  // 0 为知识问答 正整数为正常礼物
                'title' => '订单标题',
            ], $result, [
                'all_common_coin' => '钻石总和',  // TODO: 花费的普通钻石（用虚字段）
                'all_coin' => '钻石总和',  // 花费的普通钻石与贵族钻石的总和
                'income' => '总收入',
                'tax' => '税金',
                'rate' => '分成比率',
                'revenue' => '分成后的收益',  // 单位为分
                'num' => '礼物数量',  // 当 type 为 5 或 6 时存储语音包季数
                'status' => '订单状态',
                'type' => '订单类型',  // 1 直播间、2 购买音频、3 购买剧集、4 微信男友、5 语音包抽卡、6 购买语音季包
                // 当 type 为 5 或 6 时存储语音包 work_id
                // type 为 9 时，为 guild.id
                // type 为 10 时，为 m_weekly_memberships.id
                'suborders_num' => '子订单数量',
                // type 为 1 或 9 时，attr 为 1 表示直播续费贵族，为 2 表示直播开通贵族，为 3 表示直播间白给礼物，为 4 表示直播间随机礼物
                // type 为 2 或 3 时，attr 为 1 表示特殊途径购买，为 2 表示剧集兑换
                // type 为 4 时，attr 为 0 微信男友，为 1 表示掌心男友
                'attr' => '订单属性',
                'create_time' => '创建时间',
                'modified_time' => '最后修改时间',
                'confirm_time' => '交易确认时间',
            ]);
    }

    public function afterFind()
    {
        parent::afterFind();
        $this->coin = $this->all_coin;
        $this->profit = round(($this->income - $this->tax) * $this->rate, 2);
        $this->detail = $this->getDetail();
        $this->transaction_id = $this->getTransactionId();
        $this->status_msg = $this->getStatusMsg();
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }
        $time = $_SERVER['REQUEST_TIME'];
        if ($insert) {
            $this->c_time = $time;
            $this->create_time = $time;
            if (is_null($this->confirm_time)) {
                $this->confirm_time = $time;
            }
            // TODO: 计算普通钻石总和 $this->calcCommonCoin();
            // 普通钻石总和字段 all_common_coin 在 MySQL 升级到 8 版本后使用虚字段添加（目前 MySQL 为 5.6 版本）

            $this->calcRevenue();
        }
        $this->modified_time = $time;
        return true;
    }

    public function afterSave($insert, $changed_attributes)
    {
        parent::afterSave($insert, $changed_attributes);
        if ($insert) {
            $detail = new TransactionLogDetail(['id' => $this->id] + $this->trade_detail);
            $detail->ignoreExceptionSave();
        }
    }

    public function fields()
    {
        $fields = parent::fields();
        switch ($this->scenario) {
            case self::SCENARIO_PURCHASE:
                $fields = ['id', 'transaction_id', 'detail', 'c_time', 'gift_id', 'coin', 'num', 'type', 'suborders_num', 'status_msg', 'live_noble_balance_cost', 'create_time'];
                break;
            case self::SCENARIO_NEW_PURCHASE:
                $fields = ['id', 'transaction_id', 'detail', 'c_time', 'gift_id', 'num', 'type', 'suborders_num', 'status_msg', 'details', 'create_time'];
                break;
            case self::SCENARIO_INCOME:
                $fields = ['id', 'transaction_id', 'detail', 'c_time', 'income', 'tax', 'rate', 'revenue', 'profit', 'type', 'create_time'];
                break;
        }
        return $fields;
    }

    public static function newDramaPurchaseHistory(int $user_id, int $page, int $page_size, int $from_time = 0, int $to_time = 0)
    {
        return self::newPurchaseHistory($user_id, [self::TYPE_SOUND, self::TYPE_DRAMA, self::TYPE_REWARD], $page, $page_size, $from_time, $to_time, function ($query) {
            /**
             * @var ActiveQuery $query
             */
            $query->from(self::tableName() . ' AS t FORCE INDEX(idx_fromid_giftid_type)')
                ->select('t.id, t.title, t.gift_id, t.all_coin, t.num, t.type, t.suborders_num, t.status, t.attr, t.create_time, t.confirm_time')
                ->leftJoin(TransactionLogDetail::tableName() . ' AS t2', 't2.id = t.id')
                ->andWhere('(t2.user_hide_time IS NULL OR t2.user_hide_time > 0)')
                ->orderBy(['t.confirm_time' => SORT_DESC, 't.id' => SORT_DESC]);
            return $query;
        });
    }

    public static function newLivePurchaseHistory(int $user_id, int $page, int $page_size, int $from_time = 0, int $to_time = 0)
    {
        return self::newPurchaseHistory($user_id, [self::TYPE_LIVE, self::TYPE_GUILD_LIVE], $page, $page_size, $from_time, $to_time, function ($query) {
            /**
             * @var ActiveQuery $query
             */
            // CHECK:
            $query->andWhere([
                'OR',
                ['attr' => TransactionFormLive::PURCHASE_HISTORY_ATTR, 'status' => self::STATUS_SUCCESS],
                ['attr' => self::ATTR_LIVE_TOKEN, 'status' => self::STATUS_UNDONE],
            ]);
            // 直播相关消费记录仅展示近 3 个自然月的记录
            $start_time = strtotime('first day of -2 month midnight', $_SERVER['REQUEST_TIME']);
            $query->andWhere('confirm_time >= :start_time')->addParams([':start_time' => $start_time]);
            return $query;
        }, [self::STATUS_SUCCESS, self::STATUS_UNDONE]);
    }

    public static function newOtherPurchaseHistory(int $user_id, int $page, int $page_size, int $from_time = 0, int $to_time = 0)
    {
        return self::newPurchaseHistory($user_id,
            [self::TYPE_BOYFRIEND, self::TYPE_DRAW_CARD, self::TYPE_CARD_PACKAGE, self::TYPE_OMIKUJI, self::TYPE_EVENT723, self::TYPE_MAGIC],
            $page, $page_size, $from_time, $to_time);
    }

    /**
     * @param int $user_id
     * @param int|array $type
     * @param int $page
     * @param int $page_size
     * @param int $from_time
     * @param int $to_time
     * @param callable|null $filter_condition
     */
    private static function newPurchaseHistory(int $user_id, $type, int $page, int $page_size = DEFAULT_PAGE_SIZE, int $from_time = 0, int $to_time = 0, ?callable $filter_condition = null, array|int $status = self::STATUS_SUCCESS)
    {
        $query = self::find()
            // 指定使用 idx_fromid_giftid_type 索引，避免使用主键索引导致慢 SQL
            ->from(self::tableName() . ' FORCE INDEX(idx_fromid_giftid_type)')
            ->select('id, title, gift_id, all_coin, num, type, suborders_num, status, attr, create_time, confirm_time')
            ->where(['from_id' => $user_id, 'status' => $status, 'type' => $type])
            ->orderBy(['confirm_time' => SORT_DESC, 'id' => SORT_DESC]);
        if ($from_time > 0) {
            $query->andWhere('confirm_time >= :from_time', [':from_time' => $from_time]);
        }
        if ($to_time > 0) {
            $query->andWhere('confirm_time < :to_time', [':to_time' => $to_time]);
        }

        if (!is_null($filter_condition)) {
            $query = $filter_condition($query);
        }

        $list = MUtils::getPaginationModels(
            $query,
            $page_size,
            ['current_page' => $page]
        );
        $list->Datas = array_map(function ($item) {
            /**
             * @var TransactionLog $item
             */
            return [
                'transaction_id' => $item->transaction_id,
                'detail' => $item->detail,
                'status_msg' => $item->status_msg,
                'coin' => $item->coin,
                'confirm_time' => $item->confirm_time,
            ];
        }, $list->Datas);
        return $list;
    }

    public static function newPurchaseDetail(int $tid, int $user_id)
    {
        /**
         * @var TransactionLog $transaction_log
         */
        $transaction_log = self::find()
            ->select('id, title, to_id, num, type, suborders_num, status, attr, create_time, confirm_time, all_coin')
            ->addSelect(self::coinFields())
            ->where(['from_id' => $user_id, 'id' => $tid])
            ->one();
        if (!$transaction_log) {
            throw new HttpException(404, Yii::t('app/error', 'Order not found'));
        }

        $detail = [
            'transaction_id' => $transaction_log->transaction_id,
            'detail' => $transaction_log->detail,
            'coin' => $transaction_log->getCostByScope(PayAccount::SCOPE_COMMON),
            'status_msg' => $transaction_log->status_msg,
            'confirm_time' => $transaction_log->confirm_time,
        ];
        if ($transaction_log->isLiveProduct()) {
            $detail['fields'] = [];
            $creator_username = Mowangskuser::getUsernameById($transaction_log->to_id) ?: '-';
            $is_non_revenue = in_array(
                $transaction_log->attr,
                [
                    self::ATTR_LIVE_RED_PACKET,
                    self::ATTR_LIVE_RENEWAL_NOBLE,
                    self::ATTR_LIVE_REGISTER_NOBLE_TRIAL,
                    self::ATTR_LIVE_RENEWAL_NOBLE_TRIAL,
                ]
            );
            if ($is_non_revenue) {
                $detail['fields'][] = [
                    '所在直播间',
                    $creator_username,  // 礼物红包、续费贵族和贵族体验卡订单详情“所在直播间”显示主播昵称
                ];
            } elseif (!in_array($transaction_log->attr, [self::ATTR_LIVE_BUY_LUCKY_BAG, self::ATTR_LIVE_TOKEN])) {
                // WORKAROUND: creator_username 用于兼容旧版本使用，新版本使用 fields 字段
                $detail['creator_username'] = $creator_username;
                $detail['fields'][] = [
                    '受益主播',
                    $creator_username,
                ];
            }
            $detail['live_noble_balance_cost'] = $transaction_log->getCostByScope(PayAccount::SCOPE_LIVE);
        }

        return $detail;
    }

    public static function purchaseHistory($uid, $type = self::TYPE_ALL, $status = self::STATUS_SUCCESS, $page = 1)
    {
        $query = self::find()
            // 指定使用 idx_fromid_giftid_type 索引，避免使用主键索引导致慢 SQL
            ->from(self::tableName() . ' FORCE INDEX(idx_fromid_giftid_type)')
            ->select('id, title, c_time, gift_id, all_coin, num, type, suborders_num, status, attr, confirm_time AS create_time')
            ->where(['from_id' => $uid])
            // 用户消费记录不显示超能魔盒中的具体礼物
            ->andWhere('NOT (type IN (:type1, :type2) AND attr = :attr)',
                [':type1' => self::TYPE_LIVE, ':type2' => self::TYPE_GUILD_LIVE, ':attr' => self::ATTR_LIVE_GASHAPON_GIFT])
            ->orderBy(['confirm_time' => SORT_DESC, 'id' => SORT_DESC]);
        if ($type) $query = $query->andwhere(['type' => $type]);
        if ($status) $query = $query->andwhere(['status' => $status]);

        $list = MUtils::getPaginationModels(
            $query,
            DEFAULT_PAGE_SIZE,
            ['scenario' => self::SCENARIO_PURCHASE, 'current_page' => $page]
        );
        foreach ($list->Datas as $item) {
            $item->id = $item->getProcessedID();
        }
        return $list;
    }

    private function getProcessedID()
    {
        // WORKAROUND: 安卓旧版不支持 id 为字符串类型，进行兼容
        if (Equipment::isAppOlderThan(null, '5.3.5')
                || Equipment::isMiMiAppOlderThan(null, '1.0.6')) {
            return $this->id;
        }

        return $this->getTransactionId();
    }

    public static function purchaseDetail($tid, $uid)
    {
        /**
         * @var TransactionLog $order
         */
        $order = self::find()
            ->select('id, title, c_time, gift_id, all_coin, status, num, type, attr, confirm_time AS create_time')
            ->where(['id' => $tid, 'from_id' => $uid])
            ->one();
        if (!$order) {
            throw new HttpException(404, Yii::t('app/error', 'Order not found'));
        }
        if ($order->isLiveProduct()) {
            $order->live_noble_balance_cost = PayAccountPurchaseDetail::getNobleCostDetail($tid);
        }
        $order->coin = $order->all_coin - $order->live_noble_balance_cost;
        $order->setScenario(self::SCENARIO_PURCHASE);
        $order->id = $order->getProcessedID();
        // TODO: 暂未确定版本号，确定之后再调整
        // iOS >= 9.9.8 Android >= 9.9.8 不再返回 coin、live_noble_balance_cost 字段，改为返回 details
        if (!Equipment::isAppOlderThan('9.9.8', '9.9.8')) {
            $order->setScenario(self::SCENARIO_NEW_PURCHASE);
            if ($order->coin) {
                $order->details[] = [
                    'term' => '支付钻石',
                    'diamonds_coin' => $order->coin,
                    'diamonds_unit' => '钻石'
                ];
            }
            if ($order->live_noble_balance_cost) {
                $order->details[] = [
                    'term' => '支付贵族钻石',
                    'diamonds_coin' => $order->live_noble_balance_cost,
                    'diamonds_unit' => '贵族钻石'
                ];
            }
        }
        return $order;
    }

    public function getDetail()
    {
        switch ($this->type) {
            case self::TYPE_LIVE:
            case self::TYPE_GUILD_LIVE:
                return TransactionFormLive::formatOrderTitle($this);
            case self::TYPE_SOUND:
                return Yii::t('app/base', 'Purchase the drama--{drama_title}--episode x{episode_num}',
                    ['drama_title' => $this->title, 'episode_num' => $this->suborders_num]);
            case self::TYPE_DRAMA:
                if ($this->isDramaRedeemed()) {
                    return Yii::t('app/base', 'Redeem the drama--{drama_title}', ['drama_title' => $this->title]);
                } else {
                    $detail = Yii::t('app/base', 'Purchase the drama--{drama_title}', ['drama_title' => $this->title]);
                    if ($this->isPurchasedFromDouDian()) {
                        $detail .= '--购自抖音「猫耳FM旗舰店」';
                    }
                    return $detail;
                }
            case self::TYPE_BOYFRIEND:
                return '手机恋人--' . $this->title;
            case self::TYPE_DRAW_CARD:
                // 抽卡交易记录标题如“全职高手 (1, 10)”表示全职高手语音包第一季十连抽
                $pattern = '/^(.*) \((\d+), (\d+)/u';
                if (!preg_match($pattern, $this->title, $matches)) {
                    return $this->title;
                }
                $title = $matches[1];
                $season_str = MUtils::numToChinese($matches[2]);
                $card_num = $matches[3];
                // 抽卡时消费记录中的文案示例：全职高手语音包--第二季抽语音 x10
                return "{$title}语音包--第{$season_str}季抽语音 x{$card_num}";
            case self::TYPE_CARD_PACKAGE:
            case self::TYPE_MAGIC:
                return $this->title;
            case self::TYPE_REWARD:
                return Yii::t('app/base', 'Reward the drama--{drama_title}', ['drama_title' => $this->title]);
            case self::TYPE_OMIKUJI:
                // 运势语音求签交易记录，标题如：“”“”“”“撒野 (1, 1)“””，表示撒野运势语音第一季单次求签
                $pattern = '/^(.*) \((\d+), (\d+)/u';
                if (!preg_match($pattern, $this->title, $matches)) {
                    return $this->title;
                }
                $title = $matches[1] . '运势语音';
                $season = $matches[2];
                if ($season > 1) {
                    // 若非第一季，则需要表明季度
                    $season_str = MUtils::numToChinese($matches[2]);
                    $title .= " -- 第{$season_str}季";
                }
                return $title;
            case self::TYPE_EVENT723:
                return '活动礼包--' . $this->title;
            default:
                return Yii::t('app/error', 'Unknown kind');
        }
    }

    public function getStatusMsg()
    {
        switch ($this->type) {
            case self::TYPE_REWARD:
                if (self::STATUS_SUCCESS !== $this->status) {
                    return Yii::t('app/error', 'Reward failed');
                }
                return Yii::t('app/base', 'Reward successfully');
                break;
            default:
                if (self::STATUS_SUCCESS === $this->status
                        || (self::STATUS_UNDONE === $this->status
                            && in_array($this->type, [self::TYPE_LIVE, self::TYPE_GUILD_LIVE])
                            && self::ATTR_LIVE_TOKEN === $this->attr)
                    ) {
                    return Yii::t('app/base', 'Purchase successfully');
                }
                return Yii::t('app/error', 'Purchase failed');
        }
    }

    public function getTransactionId()
    {
        return $this->create_time .
            str_pad($this->type, 3, '0', STR_PAD_LEFT) .
            str_pad($this->id, 10, '0', STR_PAD_LEFT);
    }

    public static function getRealId(string $transaction_id)
    {
        return (int)substr($transaction_id, -10);
    }

    /**
     * 获取购买过的某一作品下的季包 ID
     *
     * @param int|null $user_id
     * @param int $work_id
     * @return array
     */
    public static function getPackageIdsPaid(?int $user_id, int $work_id = Work::ID_QUANZHI)
    {
        if (!$user_id) return [];
        return self::find()->select('gift_id')->where([
            'type' => self::TYPE_CARD_PACKAGE,
            'from_id' => $user_id,
            'status' => self::STATUS_SUCCESS,
            'suborders_num' => $work_id,
        ])->column();
    }

    /**
     * 获取语音包各季度的钻石花费
     *
     * @param integer $work_id 作品 ID
     * @param integer $user_id 用户 ID
     * @param integer|null $season 季度
     * @return array|integer 例: [1 => 700, 2 => 299]（其中数组键为季度，值为该季度的钻石）或 299
     */
    public static function getVoiceExpense($work_id, $user_id, $season = null)
    {
        if (!$user_id) {
            return is_null($season) ? [] : 0;
        }
        $query = self::find()->select('SUM(all_coin)')->where([
            'from_id' => $user_id,
            'type' => [self::TYPE_DRAW_CARD, self::TYPE_CARD_PACKAGE],
            'status' => self::STATUS_SUCCESS,
            'suborders_num' => $work_id,  // 该字段存储作品 ID
        ]);
        if (!is_null($season)) {
            // num 字段存储季度
            $expense = (int)$query->andWhere(['num' => $season])->scalar();
        } else {
            $season_expense_map = $query->groupBy('num')
                ->indexBy('num')
                ->column();
            $expense = array_map('intval', $season_expense_map);
        }
        return $expense;
    }

    // 获取打赏用户榜
    public static function getUserRewardRankList($from_time, $to_time, $count, $drama_id)
    {
        $users = self::find()->select('from_id AS user_id, SUM(all_coin) AS coin, MAX(confirm_time) AS create_time')
            // 指定使用 idx_giftid_type_status_fromid_confirmtime 索引，优化打赏用户榜慢 SQL
            ->from(self::tableName() . ' FORCE INDEX(idx_giftid_type_status_fromid_confirmtime)')
            ->where(['type' => self::TYPE_REWARD, 'status' => self::STATUS_SUCCESS, 'gift_id' => $drama_id])
            ->andWhere('confirm_time BETWEEN :from_time AND :to_time', [':from_time' => $from_time, ':to_time' => $to_time])
            ->groupBy('user_id')->orderBy('coin DESC, create_time DESC')->asArray()->indexBy('user_id')
            ->limit($count)->all();

        $users = array_map(function ($item) {
            $item['user_id'] = (int)$item['user_id'];
            $item['coin'] = (int)$item['coin'];
            $item['ctime'] = (int)$item['create_time'];
            unset($item['create_time']);
            return $item;
        }, $users);
        return $users;
    }

    public static function getUserRewardCoin($from_time, $to_time, $user_id, $drama_id)
    {
        return (int)self::find()->select('SUM(all_coin)')
            ->where([
                'type' => self::TYPE_REWARD,
                'status' => self::STATUS_SUCCESS,
                'gift_id' => $drama_id,
                'from_id' => $user_id,
            ])->andWhere('confirm_time BETWEEN :from_time AND :to_time',
                [':from_time' => $from_time, ':to_time' => $to_time])->scalar();
    }

    /**
     * 获取已购的剧集数
     *
     * @param integer $user_id 用户 ID
     * @return integer
     */
    public static function getDramaBoughtCount($user_id)
    {
        return (int)self::find()
            ->select('t1.gift_id')->distinct()
            ->alias('t1')
            ->leftJoin(TransactionLogDetail::tableName() . ' AS t2', 't2.id = t1.id')
            ->where([
                't1.from_id' => $user_id,
                't1.type' => [self::TYPE_SOUND, self::TYPE_DRAMA],
                't1.status' => self::STATUS_SUCCESS,
            ])
            ->andWhere('(t2.user_hide_time IS NULL OR t2.user_hide_time > 0)')
            ->count();
    }

    /**
     * 获取已购的剧集信息
     * 按照字符串首字母的方式排序说明如下
     * 字符串是汉字或英文字母的情况：\
     * 如果第一个字的首字母相同，则再看第二个字的首字母，以此类推 \
     * 字符串首字符为数字的情况：\
     * 排在汉字或英文字母的字符串后面，按数字大小排序 \
     * 字符串首字符为特殊字符的情况：\
     * 排在最后
     *
     * @param integer $user_id 用户 ID
     * @param integer $order_type 排序方式 1：按购买时间降序；2：按剧集名 A-Z；
     * @param integer $source 来源 1：已购管理；2：删除的剧集订单
     * @param string $s 搜索关键字
     * @param integer $page 第几页
     * @param integer $page_size 每页个数
     * @return ReturnModel
     * @throws HttpException
     */
    public static function getDramaBought(int $user_id, int $order_type = self::ORDER_BY_BOUGHT_TIME,
            int $source = self::SOURCE_BOUGHT, string $s = '', int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        if (!in_array($order_type, [self::ORDER_BY_BOUGHT_TIME, self::ORDER_BY_DRAMA_NAME])) {
            throw new HttpException(400, '参数错误');
        }
        $params = [
            'user_id' => $user_id,
            'type' => $order_type,
            'source' => $source,
            's' => $s,
            'thumbnail' => (int)Yii::$app->equip->isFromMiMiApp(),
            'page' => $page,
            'page_size' => $page_size,
        ];
        $dramas = Drama::rpc('drama/get-bought-drama', $params);
        $return = new ReturnModel();
        $return->Datas = $dramas['Datas'];
        $return->pagination = $dramas['pagination'];
        return $return;
    }

    /**
     * 计算普通钻石之和
     */
    public function calcCommonCoin()
    {
        foreach (self::coinFields() as $coin_field) {
            $this->all_common_coin += $this->$coin_field;
        }
    }

    /**
     * 计算分成后的收益
     *
     * @throws HttpException
     */
    private function calcRevenue()
    {
        // revenue 记录该笔订单的收益
        // 单位为分，精确到分（抹去分后的值），例 (15 - 4.71) * 0.45 元 * 100 分/元 = 463.05 分 => 463 分
        // 用于素人统计收益，避免代码算出的收益和 MySQL 计算的收益出现差距
        // 如，MySQL SUM(FLOOR(ROUND((income - tax) * rate * 1000) / 10) / 100) 可能得到 6117.08，而代码得到的是 6117.09
        // TODO: MySQL 8 时可调整为虚拟字段
        $this->revenue = Balance::profitUnitConversion(($this->income - $this->tax) * $this->rate, Balance::CONVERT_YUAN_TO_FEN);
    }

    public function isLiveRebateGift()
    {
        return $this->isLiveProduct() && $this->attr === self::ATTR_LIVE_REBATE_GIFT;
    }

    public function isLiveProduct()
    {
        return in_array($this->type, [self::TYPE_LIVE, self::TYPE_GUILD_LIVE]);
    }

    /**
     * 获取已消费的运势求签包
     *
     * @param int $user_id 用户 ID
     * @param int|int[] $lottery_package_ids
     * @param int|int[] $work_ids
     * @return array
     */
    public static function getPaidLotteryPackages($user_id, $lottery_package_ids, $work_ids): array
    {
        if (!$user_id) {
            return [];
        }
        $ids = self::find()
            ->select('gift_id')
            ->where([
                'from_id' => $user_id,
                'gift_id' => $lottery_package_ids,
                'suborders_num' => $work_ids,
                'type' => self::TYPE_OMIKUJI,
                'status' => self::STATUS_SUCCESS,
            ])->column();
        return array_map('intval', $ids);
    }

    // 剧集是否通过兑换得来的
    public function isDramaRedeemed()
    {
        if ($this->type === self::TYPE_DRAMA) {
            $attr = $this->attr & self::ATTR_BASE_BIT_MASK;
            return $attr === self::ATTR_DRAMA_REDEEM || $attr === self::ATTR_DRAMA_LUCKY_BAG;
        }
        return false;
    }

    // 剧集通过抖店购得
    public function isPurchasedFromDouDian()
    {
        if ($this->type === self::TYPE_DRAMA) {
            return ($this->attr & self::ATTR_BASE_BIT_MASK) === self::ATTR_DRAMA_DOUDIAN;
        }

        return false;
    }

    // 是否支持退款后再次购买
    public function isSupportRePurchaseAfterRefund()
    {
        return MUtils::bitIsSet($this->attr, self::ATTR_REPURCHASE_AFTER_REFUND);
    }

    public function getGuild_id()
    {
        if ($this->type !== self::TYPE_GUILD_LIVE) {
            return 0;
        }
        return $this->suborders_num;
    }

    public function setGuild_id(int $guild_id)
    {
        if ($this->type === self::TYPE_GUILD_LIVE) {
            $this->suborders_num = $guild_id;
        }
    }

    /**
     * @param int $buyer_id
     * @param int $goods_id
     * @param int $goods_type
     * @return bool
     */
    public static function isPurchased(int $buyer_id, int $goods_id, int $goods_type)
    {
        return self::find()
            ->where([
                'status' => self::STATUS_SUCCESS,
                'from_id' => $buyer_id,
                'gift_id' => $goods_id,
                'type' => $goods_type,
            ])->exists();
    }

    public function getTrade_detail()
    {
        return $this->_trade_detail;
    }

    public function setTrade_detail(array $trade_detail)
    {
        $this->_trade_detail = $trade_detail;
    }

    public function __get($name)
    {
        // 新渠道钻石（vip_coin, new_ios_coin, new_googlepay_coin 等）记录在 transaction_log_detail.more 中
        if (str_ends_with($name, '_coin')
            && in_array(
                str_replace('_coin', '', $name),
                array_diff(PayAccount::TYPE_INDEX_COIN_FIELD_MAP, Balance::balanceFields()))
        ) {
            return $this->_trade_detail['more'][$name] ?? 0;
        }
        return parent::__get($name);
    }

    public function __set($name, $value): void
    {
        // ios_coin, googlepay_coin 包含新旧的的 iOS/GooglePay 渠道钻石消耗
        if (in_array($name, ['new_ios_coin', 'new_googlepay_coin'])) {
            $this->{str_replace('new_', '', $name)} += $value;
        }
        // 新渠道钻石（vip_coin, new_ios_coin, new_googlepay_coin 等）记录在 transaction_log_detail.more 中
        if (str_ends_with($name, '_coin')
                && in_array(
                    str_replace('_coin', '', $name),
                    array_diff(PayAccount::TYPE_INDEX_COIN_FIELD_MAP, Balance::balanceFields()))
        ) {
            $this->_trade_detail['more'][$name] = $value;
            return;
        }
        parent::__set($name, $value);
    }

    /**
     * 获取已购隐藏剧集数量
     *
     * @param int $user_id 用户 ID
     * @return int
     */
    public static function getHideDramaBoughtCount(int $user_id)
    {
        return (int)self::find()
            ->alias('a')
            ->select('a.gift_id')
            ->leftJoin(TransactionLogDetail::tableName() . ' AS b', 'a.id = b.id')
            ->where([
                'a.from_id' => $user_id,
                'a.type' => [self::TYPE_SOUND, self::TYPE_DRAMA],
                'a.status' => self::STATUS_SUCCESS,
            ])
            ->andWhere('b.user_hide_time > 0')
            ->groupBy('a.gift_id')
            ->count();
    }

    /**
     * 隐藏用户剧集购买订单
     *
     * @param int $user_id 用户 ID
     * @param array ids IDs
     * @return string
     * @throws HttpException
     * @throws Exception
     */
    public static function hideDramaPurchaseOrder(int $user_id, array $ids)
    {
        $transaction = null;
        try {
            $transaction_log_ids = self::getTransactionLogIds($user_id, $ids);
            $detail_ids = TransactionLogDetail::find()
                ->select('id')
                ->where(['id' => $transaction_log_ids])
                ->column();
            $detail_ids_diff = array_diff($transaction_log_ids, array_map('intval', $detail_ids));

            $transaction = self::getDb()->beginTransaction();
            if (!empty($detail_ids_diff)) {
                // transaction_log_detail 表和 transaction_log 表数据未统一
                // 当删除订单时，为避免不存在 transaction_log_detail 信息导致订单无法删除，需把此次删除订单不存在于 transaction_log_detail 的信息重新生成
                $t_details = TransactionLogDetail::newDetails($detail_ids_diff);
                TransactionLogDetail::getDb()->createCommand()->batchInsert(TransactionLogDetail::tableName(), array_keys(current($t_details)), $t_details)
                    ->execute();
            }
            $time = $_SERVER['REQUEST_TIME'];
            // JSON 字段默认为 NULL 的时候，无法写入数据
            $more = new Expression(
                'JSON_SET(COALESCE(more, "{}"), "$.user_hide_time", :user_hide_time)',
                [
                    ':user_hide_time' => $time
                ]
            );
            $update = TransactionLogDetail::updateAll(['more' => $more, 'modified_time' => $time],
                ['id' => $transaction_log_ids, 'user_hide_time' => NULL]);
            if (!$update) {
                throw new HttpException(404, '未找到对应订单');
            }
            $transaction->commit();
            return true;
        } catch (Exception $e) {
            if ($transaction) {
                $transaction->rollBack();
            }
            throw $e;
        }
    }

    /**
     * 恢复用户剧集购买订单
     *
     * @param int $user_id 用户 ID
     * @param array $ids 剧集 IDs
     * @return string
     * @throws HttpException
     */
    public static function recoverDramaPurchaseOrder(int $user_id, array $ids)
    {
        $transaction_log_ids = self::getTransactionLogIds($user_id, $ids);
        $more = new Expression(
            'JSON_REMOVE(more, "$.user_hide_time")'
        );
        $id_condition_sql = MUtils::generateIntegerIn('id', $transaction_log_ids);
        $hide_condition_sql = TransactionLogDetail::userHideConditionSql();
        $update = TransactionLogDetail::updateAll(['more' => $more, 'modified_time' => $_SERVER['REQUEST_TIME']],
            "$id_condition_sql AND $hide_condition_sql");
        if (!$update) {
            throw new HttpException(404, '未找到对应订单');
        }
        return true;
    }

    /**
     * 获取交易订单 IDs
     *
     * @param int $user_id 用户 ID
     * @param array $ids 剧集 IDs
     * @return array
     * @throws HttpException
     */
    public static function getTransactionLogIds(int $user_id, array $ids)
    {
        $transaction_log_ids = self::find()
            ->select('id')
            ->where([
                'from_id' => $user_id,
                'gift_id' => $ids,
                'type' => [self::TYPE_SOUND, self::TYPE_DRAMA],
                'status' => self::STATUS_SUCCESS,
            ])->column();
        if (empty($transaction_log_ids)) {
            throw new HttpException(404, '未找到对应订单');
        }
        return $transaction_log_ids;
    }

    /**
     * 单集剧集是否存在隐藏剧集购买订单
     *
     * @param int $user_id 用户 ID
     * @param int $drama_id 剧集 ID
     * @return bool
     */
    public static function hasHideDramaPurchaseOrder(int $user_id, int $drama_id)
    {
        $ids = self::find()->select('id')
            ->where([
                'from_id' => $user_id,
                'gift_id' => $drama_id,
                'type' => TransactionLog::TYPE_SOUND,
                'status' => TransactionLog::STATUS_SUCCESS,
            ])->column();
        return TransactionLogDetail::find()
            ->where(['id' => $ids])
            ->andWhere('user_hide_time IS NOT NULL')
            ->exists();
    }

    /**
     * 检查用户是否购买了指定剧集
     *
     * @param int $user_id 用户 ID
     * @param int $drama_id 剧集 ID
     * @return bool
     */
    public static function checkUserPaidDrama(int $user_id, int $drama_id): bool
    {
        return TransactionLog::find()->select('id')
            ->where([
                'gift_id' => $drama_id,
                'from_id' => $user_id,
                'status' => TransactionLog::STATUS_SUCCESS,
                'type' => [TransactionLog::TYPE_SOUND, TransactionLog::TYPE_DRAMA],
            ])->exists();
    }

    /**
     * 获取指定剧集 IDs 中，用户购买过的剧集 IDs
     *
     * @param int $user_id 用户 ID
     * @param array $drama_ids 剧集 IDs
     * @return array
     */
    public static function getPaidDramaIds(int $user_id, array $drama_ids): array
    {
        $paid_drama_ids = self::find()
            ->select('DISTINCT(gift_id)')
            ->where([
                'type' => [self::TYPE_SOUND, self::TYPE_DRAMA],
                'from_id' => $user_id,
                'gift_id' => $drama_ids,
                'status' => self::STATUS_SUCCESS,
            ])
            ->column();
        return array_map('intval', $paid_drama_ids);
    }

    public function getLegacyCommonCoin()
    {
        return array_reduce(self::coinFields(), function ($num, $coin_field) {
            $num += $this->{$coin_field};
            return $num;
        }, 0);
    }

    public function getCostByScope(int $scope)
    {
        $query = PayAccountPurchaseDetail::find()
            ->alias('t1')
            ->innerJoin(PayAccount::tableName() . ' AS t2', 't2.id = t1.account_id')
            ->where([
                't1.tid' => $this->id,
                't1.status' => PayAccountPurchaseDetail::STATUS_CONFIRM,
                't2.status' => PayAccount::STATUS_SUCCESS,
            ])->andWhere('t2.scope = :scope', [':scope' => $scope]);
        $legacy_common_coin = 0;
        if ($scope === PayAccount::SCOPE_COMMON) {
            $legacy_common_coin = $this->getLegacyCommonCoin();
            // ios/android/tmallios/paypal/googlepay 普通钻石消费已记在 transaction_log.xxx_coin 当中，汇总时扣除，避免重复计算
            // new_ios/new_googlepay 普通钻石消费也记在 transaction_log.ios_coin/googlepay_coin 当中，汇总时扣除，避免重复计算
            $query = $query->andWhere(['NOT IN', 'type', array_merge(PayAccount::LEGACY_COIN_TYPE_INDEX, [
                PayAccount::TYPE_COIN_INDEX_NEW_IOS,
                PayAccount::TYPE_COIN_INDEX_NEW_GOOGLEPAY,
            ])]);
        }
        $cost = $query->sum('t1.purchase_amount');
        return $cost + $legacy_common_coin;
    }

    public function calcTax(PayAccounts|array $accounts): float
    {
        if (is_array($accounts)) {
            if (empty($accounts)) {
                return 0;
            }

            if (current($accounts) instanceof PayAccount) {
                $change_accounts = $accounts;
            } else {
                $coin_field_type_index_map = array_flip(PayAccount::TYPE_INDEX_COIN_FIELD_MAP);
                $change_accounts = [];
                foreach ($accounts as $coin_field => $coin_num) {
                    $acc = new PayAccount();
                    $acc->type = $coin_field_type_index_map[$coin_field];
                    $acc->consume_amount = $coin_num;
                    $change_accounts[] = $acc;
                }
            }

            $acc = new PayAccounts([]);
            $acc->setChangeAccounts($change_accounts);
            return $acc->getTotalFee($this);
        }

        return $accounts->getTotalFee($this);
    }

}
