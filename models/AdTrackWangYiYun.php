<?php

namespace app\models;

use Exception;
use Yii;

class AdTrackWangYiYun extends AdTrack implements AdTrackInterface
{
    const WANGYIYUN_AD_GATEWAY = 'https://ad-effect.music.163.com/ad/action';
    // 第⼀⽅平台（⾃研监测）
    const PLAT_TYPE_SELF = 0;
    // 第三⽅平台（借助第三⽅监测）
    const PLAT_TYPE_THIRD_PARTY = 1;

    const CALLBACK_EVENT_TYPE_MAP = [
        self::CALLBACK_EVENT_ACTIVATE => self::CALLBACK_EVENT_TYPE_ACTIVATE,
        self::CALLBACK_EVENT_ONE_DAY_RETENTION => self::CALLBACK_EVENT_TYPE_OTHER_KEY_ACTION,
        self::CALLBACK_EVENT_PAY => self::CALLBACK_EVENT_TYPE_PAY,
        self::CALLBACK_EVENT_REGISTER => self::CALLBACK_EVENT_TYPE_REGISTER,
        self::CALLBACK_EVENT_KEY_ACTION => null,
        self::CALLBACK_EVENT_TRANSACTION => null,  // 无消费 / 充值关键行为
    ];

    // 激活
    const CALLBACK_EVENT_TYPE_ACTIVATE = 0;
    // 付费
    const CALLBACK_EVENT_TYPE_PAY = 4;
    // 注册
    const CALLBACK_EVENT_TYPE_REGISTER = 7;
    // 关键行为类型：其它
    const CALLBACK_EVENT_TYPE_OTHER_KEY_ACTION = 8;

    public function callback(string $event_type, $arg = 0)
    {
        try {
            if (!array_key_exists($event_type, self::CALLBACK_EVENT_TYPE_MAP)) {
                throw new Exception('网易云点击回传事件错误：' . $event_type);
            }
            $event = self::CALLBACK_EVENT_TYPE_MAP[$event_type];
            if (is_null($event)) {
                return true;
            }
            $data = Yii::$app->tools->requestRemote(
                self::WANGYIYUN_AD_GATEWAY,
                $this->getCallbackBody($event, $arg)
            );
            if (!($data && $data['code'] === 200)) {
                throw new Exception(sprintf('网易云点击回传失败：code[%d], msg[%s]', $data['code'], $data['msg']));
            }
            return true;
        } catch (Exception $e) {
            Yii::error('wangyiyun ad error: ' . $e->getMessage(), __METHOD__);
        }
        return false;
    }

    private function getCallbackBody(int $callback_event, $arg = 0)
    {
        $account_id = $this->getAccountID();
        $service = Yii::$app->params['service']['ad']['wangyiyun'];
        if (!array_key_exists($account_id, $service)) {
            Yii::error('网易云配置出错，不存在账号 ID：' . $account_id, __METHOD__);
            return;
        }
        $config = $service[$account_id];
        $body = [
            'platType' => self::PLAT_TYPE_SELF,
            'source' => $config['source'],
            'event' => $callback_event,
            'clickid' => $this->track_id,
            'appkey' => $config['app_key'],
            'timestamp' => $_SERVER['REQUEST_TIME'],
        ];
        if ($ip = Yii::$app->request->userIP) {
            $body['ip'] = $ip;
        }
        $equip = Yii::$app->equip;
        if ($equip->isFromApp()) {
            if ($app_version = $equip->getAppversion()) {
                $body['appversion'] = $app_version;
            }
            if ($os_version = $equip->getOsVersion()) {
                $body['osversion'] = $os_version;
            }
        }
        if ($callback_event === self::CALLBACK_EVENT_TYPE_PAY) {
            $body['money'] = $this->getCallbackPayAmount($arg);
        }
        $body['sign'] = $this->getSign($body, $config['secret_key']);
        return $body;
    }

    private function getSign(array $data, string $secret_key)
    {
        $sign = $secret_key;
        ksort($data);
        foreach ($data as $k => $v) {
            $sign .= $k . $v . $secret_key;
        }
        return strtoupper(md5($sign));
    }
}
