<?php
/**
 * 该 Model 是和猫耳学院暑期纳新积分任务活动相关的 Model，没有和数据库相关联
 * @deprecated 活动已结束，可删除该 Model
 */

namespace app\models;

use Exception;
use yii\web\HttpException;
use yii\helpers\Json;
use Yii;

class NaxinActivity
{
    // 积分任务结束时间（2021-08-31 23:59:59）
    const POINT_TASK_END_TIME = 1630425599;

    // 猫耳学院暑期纳新积分任务活动类型 like：点赞活动投稿音频；
    const POINT_TASK_TYPE_LIKE = 'like';

    // 点赞活动投稿音频每次得到的积分数量
    const LIKE_ONCE_POINT = 2;

    // 点赞活动投稿音频每日次数限制
    const LIKE_POINT_LIMIT = 5;

    /**
     * 获取猫耳学院暑期纳新活动用户抽奖积分详情
     *
     * @param int $user_id 用户 ID
     * @param int $time_offset 时间偏移量，用于测试人员测试使用。线上不使用
     * @return array
     */
    public static function getSummerNaxinTaskData(int $user_id, int $time_offset = 0): array
    {
        $redis = Yii::$app->redis;

        $ymd = date('Y-m-d', $_SERVER['REQUEST_TIME'] + $time_offset);
        $key = $redis->generateKey(KEY_SUMMER_NAXIN_POINT_USER_ID, $user_id);
        $task_json = $redis->hGet($key, $ymd);
        if (!$task_json) {
            return ['like' => 0, 'share' => 0];
        }
        return Json::decode($task_json);
    }

    /**
     * 设置猫耳学院暑期纳新活动用户任务详情
     *
     * @param int $user_id
     * @param array $data 用户任务详情
     * @param int $time_offset 时间偏移量，用于测试人员测试使用。线上不使用
     */
    public static function setSummerNaxinTaskData(int $user_id, array $data, int $time_offset = 0)
    {
        $ymd = date('Y-m-d', $_SERVER['REQUEST_TIME'] + $time_offset);
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_SUMMER_NAXIN_POINT_USER_ID, $user_id);
        $redis->multi()
            ->hSet($key, $ymd, Json::encode($data))  // 增加任务操作的次数
            ->expireAt($key, self::POINT_TASK_END_TIME + ONE_WEEK)  // 设置猫耳学院暑期纳新积分详情过期时间
            ->exec();
    }

    /**
     * 更新用户抽奖积分
     *
     * @param int $user_id 用户 ID
     * @param int $point 新增积分
     * @throws HttpException
     * @return int 更新后用户总积分
     */
    public static function updateEventDrawPoint(int $user_id, int $point): int
    {
        return Yii::$app->go->updateEventDrawPoint(MEvent::EVENT_ID_SUMMER_NAXIN, $user_id, $point);
    }

    /**
     * 添加点赞活动投稿音频积分
     *
     * @param int $user_id 用户 ID
     * @param MSound $sound 音频数据
     * @throws Exception
     */
    public static function addLikeTaskPoint($user_id, $sound)
    {
        $event = MEvent::findOne(MEvent::EVENT_ID_SUMMER_NAXIN);
        if (!$event) {
            return;
        }
        if ($event->isInDrawTime()) {
            $exist = EventVote::find()->where([
                'event_id' => MEvent::EVENT_ID_SUMMER_NAXIN,
                'eid' => (int)$sound->id,
                // TODO: 之后考虑改成元素类型值应该是 EventVote::CATEGORY_SOUND，而不是和活动类型有关
                'category' => MEvent::TYPE_UPLOAD,
            ])->exists();
            // 判断是否为投稿音频并且分类是规定的投稿分区
            if ($exist && Catalog::isGetPoints($sound->catalog_id)) {
                self::addSummerNaxinTaskPoint($user_id, NaxinActivity::POINT_TASK_TYPE_LIKE, $event);
            }
        }
    }

    /**
     * 新增猫耳学院暑期纳新活动用户抽奖积分
     *
     * @param int $user_id 用户 ID
     * @param string $type 任务类型 like：点赞活动投稿音频
     * @param MEvent $event 活动数据
     * @throws Exception
     */
    public static function addSummerNaxinTaskPoint(int $user_id, string $type, $event)
    {
        $redis = Yii::$app->redis;
        $point_task_key = $redis->generateKey(LOCK_SUMMER_NAXIN_POINT_USER_ID, $user_id);
        if (!$redis->lock($point_task_key, ONE_MINUTE)) {
            return;
        }
        try {
            $time_offset = $event->extended_fields['time_offset'] ?? 0;
            $point_info = self::getSummerNaxinTaskData($user_id, $time_offset);
            switch ($type) {
                case self::POINT_TASK_TYPE_LIKE:
                    if ((int)$point_info['like'] >= self::LIKE_POINT_LIMIT) {
                        return;
                    }
                    $point_info['like'] += 1;
                    $point = self::LIKE_ONCE_POINT;
                    $log = '点赞活动投稿音频获得 ' . $point . ' 积分';
                    break;
                default:
                    throw new Exception('参数错误');
            }

            self::setSummerNaxinTaskData($user_id, $point_info, $time_offset);
            // 给用户添加积分
            self::updateEventDrawPoint($user_id, $point);
            Yii::info($log, __METHOD__);
        } catch (Exception $e) {
            Yii::error('积分活动中数据处理异常：' . $e->getMessage(), __METHOD__);
        } finally {
            $redis->unlock($point_task_key);
        }
    }
}
