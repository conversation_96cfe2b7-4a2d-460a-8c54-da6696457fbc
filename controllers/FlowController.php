<?php
/**
 * Created by VSCode.
 * User: tengattack
 * Date: 2018/5/25
 * Time: 13:05
 * Doc: B 站免流接口相关文档地址：https://info.bilibili.co/pages/viewpage.action?pageId=20603090
 * 猫耳FM 免流规则文档地址：https://github.com/MiaoSiLa/missevan-doc/blob/master/backend/application/flow.md
 */

namespace app\controllers;

use app\components\util\Equipment;
use app\components\util\MUtils;
use app\forms\LoginForm;
use app\middlewares\Controller;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use Yii;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use yii\web\HttpException;

class FlowController extends Controller
{
    const API_TIMEOUT = 15.0;
    const UNICOM_API_URL = 'https://csapi.bol.wo.cn/';
    const UNICOM_SUCCESS = 0;
    const BILIBILI_SUCCESS = 0;

    // 运营商，1：联通；2：电信；3：移动
    const OPERATOR_UNICOM = 1;
    const OPERATOR_TELECOM = 2;
    const OPERATOR_MOBILE = 3;

    // 手机伪码免流激活状态
    const USERMOB_STATE_UNACTICVE = 1;  // 未激活
    const USERMOB_STATE_ACTIVE = 2;  // 已激活

    // 请求方式
    const METHOD_POST = 'POST';
    const METHOD_GET = 'GET';

    // 免流类型，0：未开通；1：免流卡；2：免流包
    const FLOW_TYPE_NONE = 0;
    const FLOW_TYPE_CARD = 1;
    const FLOW_TYPE_PACK = 2;

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'activate' => ['post'],
                'send-vcode' => ['post'],
            ],
        ];
        return $behaviors;
    }

    /**
     * @api {get} /flow/available 判断当前用户网络是否可以使用免流服务
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/flow/available
     * @apiSampleRequest /flow/available
     * @apiDescription 判断当前用户网络是否可以使用免流服务，请在移动网络下请求这个 API
     * @apiDescription 移动卡用户客户端需先判断 usermob 是否已激活
     * @apiDescription status 表示卡的激活状态 1：未激活、2：已激活、3：已退订（过期）、4：已退订（未过期）。
     * @apiDescription 当客户端收到 status 不为 2 且不为 4 时可以主动关闭用户打开的免流开关
     * @apiDescription rules 字段当且仅当 available 为 true 时存在
     *
     * @apiVersion 0.1.0
     * @apiName available
     * @apiGroup flow
     *
     * @apiParam {String} usermob 手机伪码
     * @apiParam {number=1,2,3} [operator=1] 运营商，1：联通；2：电信；3：移动
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 返回是否可用、状态以及免流规则 URL。
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "available": true,
     *         "status": 2,
     *         "rules": "http://static.example.com/flow-rules.json"
     *       }
     *     }
     */
    public function actionAvailable(string $usermob, int $operator = self::OPERATOR_UNICOM)
    {
        $usermob = trim($usermob);
        if (!$usermob || !in_array($operator,
            [self::OPERATOR_UNICOM, self::OPERATOR_TELECOM, self::OPERATOR_MOBILE])) {
            throw new HttpException(400, '参数错误');
        }
        if (Equipment::isAppOlderThan('4.4.3', '5.3.4')) {
            // WORKAROUND: 因 B 站免流域名 bili-static.acgvideo.com 下线，
            // iOS 低于 4.4.3 版本，Android 低于 5.3.4 版本不再支持免流
            throw new HttpException(403, '当前 App 版本过低，需升级应用才可享受免流服务');
        }
        $flow_service = Yii::$app->params['service']['bilibili-flow'];
        $available = false;
        // 免流激活状态
        [$state, $flow_type] = $this->getUsermobState($usermob, $operator);
        if (self::isFreeFlowType($state)) {
            if ($operator === self::OPERATOR_UNICOM) {
                // 联通存储于客户端的伪码为解密后的字符串，需要重新加密获取真实伪码
                $usermob = $this->desEncrypt($usermob, $flow_service['cpkey']);
            }
            $result = $this->requestBilibiliApi('x/wall/operator/m/ip', [
                'usermob' => $usermob,
                'ip' => Yii::$app->request->userIP,
            ]);

            if ((int)$result['code'] !== self::BILIBILI_SUCCESS) {
                throw new HttpException(500, '远程调用错误：' . $result['message']);
            }

            $available = $result['data']['is_valide'] ?? false;
        }

        $ret = [
            'available' => $available,
            'status' => $state,
        ];
        if ($available) {
            // 若免流验证通过，则获取免流规则
            $rules_index = $operator;
            if (!Equipment::isAppOlderThan('4.6.2', '5.5.2')) {
                // WORKAROUND: 若 iOS 大于等于 4.6.2 版本或安卓大于等于 5.5.2 版本，走联通包或支持直播的免流规则
                switch ($operator) {
                    case self::OPERATOR_UNICOM:
                        $rules_index = $flow_type === self::FLOW_TYPE_PACK ? 8 : 4;  // 联通包免流
                        break;
                    case self::OPERATOR_TELECOM:
                        $rules_index = 6;  // 电信支持直播免流
                        break;
                    case self::OPERATOR_MOBILE:
                        $rules_index = 7;  // 移动支持直播免流
                        break;
                    default:
                        throw new \Exception('operator 参数错误');
                }
            }
            $equip = Yii::$app->equip;
            if ($flow_type === self::FLOW_TYPE_PACK && $equip->isAndroid() &&
                    (!Equipment::isAppOlderThan('', '5.5.1') && Equipment::isAppOlderThan('', '6.0.8'))) {
                // WORKAROUND: 若为联通免流包，安卓大于等于 5.5.1 且小于 6.0.8 版本，走旧的联通包免流
                $rules_index = 5;
            }
            $ret['rules'] = $flow_service['rules'][$rules_index];
        }
        return $ret;
    }

    /**
     * @api {post} /flow/send-vcode 发送验证码
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/flow/send-vcode
     * @apiSampleRequest /flow/send-vcode
     * @apiDescription 发送联通免流取号验证码到用户手机，用于确认用户身份和获取用户手机伪码
     *
     * @apiVersion 0.1.0
     * @apiName send-vcode
     * @apiGroup flow
     *
     * @apiParam {String} account 手机号
     * @apiParam {number=1,2} operator 运营商，1：联通；2：电信
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 返回离上次发送短信间隔时间（秒）
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "remaintime": 60 // 注意：这个值不是剩余时间，每次成功调用这个 API 都显示下一次操作需要 1 分钟之后即可
     *       }
     *     }
     */
    public function actionSendVcode()
    {
        $account = (int)Yii::$app->request->post('account');
        $operator = (int)Yii::$app->request->post('operator', self::OPERATOR_UNICOM);

        $account_type = LoginForm::getAccountType($account, 'CN');
        if (LoginForm::MOBILE !== $account_type) {
            throw new HttpException(400, '错误的手机号');
        }

        $redis = Yii::$app->redis;
        $lock_account = $redis->generateKey(VCODE . 'lock', $account);
        if (!$redis->lock($lock_account, ONE_MINUTE)) {
            throw new HttpException(403, '不能连续发送验证码');
        }
        $result = null;
        switch ($operator) {
            case self::OPERATOR_UNICOM:
                // 发送联通验证码
                $result = $this->requestUnicomApi('videoif/sendSmsCode.do', $account);
                break;
            case self::OPERATOR_TELECOM:
                // 发送电信验证码
                $flow_service = Yii::$app->params['service']['bilibili-flow'];
                $params = [
                    'usermob' => $this->desEncrypt($account, $flow_service['cpkey']),
                    'ts' => $_SERVER['REQUEST_TIME'],
                    // TODO: 之后接口需要加上签名（sign），目前接口可不加签名访问（B 站尚未给出签名规则）
                    // 'sign' => $this->getBlibiliSign(),
                ];
                $result = $this->requestBilibiliApi('x/wall/telecom/card/sms', $params, self::METHOD_POST);
                break;
            default:
                throw new HttpException(400, '参数错误');
        }
        // 检查短信发送结果
        $this->checkSendSmsResult($result, $account, $operator);
        return [
            'remaintime' => array_key_exists('remaintime', $result) ? (int)$result['remaintime'] : ONE_MINUTE,
        ];
    }

    /**
     * 检测发送短信接口返回结果
     *
     * @param array|null $result 接口响应内容
     * @param integer $operator 账号类型
     * @throws HttpException 接口请求失败或短信发送失败时抛出异常
     */
    private function checkSendSmsResult(?array $result, string $account, int $operator)
    {
        if (!$result) {
            throw new HttpException(400, '免流服务维护中，请稍后再试');
        }
        $code = self::BILIBILI_SUCCESS;
        $msg = '';
        switch ($operator) {
            case self::OPERATOR_UNICOM:
                $code = (int)$result['resultcode'];
                $msg = $result['errorinfo'] ?? '短信发送失败，请稍后再试';
                break;
            case self::OPERATOR_TELECOM:
                $code = (int)$result['code'];
                $msg = $result['message'] ?? '短信发送失败，请稍后再试';
                break;
            default:
                throw new HttpException(400, '参数错误');
        }
        if ($code !== self::BILIBILI_SUCCESS) {
            // 打码手机号
            $mosaic_account = MUtils::mosaicString($account, MUtils::MOSAIC_MOBILE);
            Yii::warning($mosaic_account . ' result: ' . json_encode($result), 'flow/send-vcode');
            throw new HttpException(400, $msg);
        }
    }
    /**
     * @api {post} /flow/activate 验证验证码（或伪码）并激活免流服务
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/flow/activate
     * @apiSampleRequest /flow/activate
     * @apiDescription 验证联通验证码，并激活免流服务
     *
     * @apiVersion 0.1.0
     * @apiName activate
     * @apiGroup flow
     *
     * @apiParam {number=1,2,3} [operator=1] 运营商，1：联通；2：电信；3：移动
     * @apiParam {String} account 手机号，当 operator 为 1 或 2 时传递
     * @apiParam {String} code 验证码，当 operator 为 1 或 2 时传递
     * @apiParam {String} usermob 手机伪码，当 operator 为 3 时传递
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 返回用户手机号伪码，客户端应保存该信息
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "account": "159****0128",
     *         "usermob": "d41d8cd98f00b204e9800998ecf8427e"  // 客户端需要更新本地 usermob 为此 usermob
     *         "flow_type": 1  // 免流类型，1：免流卡；2：免流包
     *       }
     *     }
     */
    public function actionActivate()
    {
        $account = (int)Yii::$app->request->post('account');
        $code = trim(Yii::$app->request->post('code'));
        $operator = (int)Yii::$app->request->post('operator', self::OPERATOR_UNICOM);
        $usermob = trim(Yii::$app->request->post('usermob'));

        if ($operator === self::OPERATOR_MOBILE && !$usermob) {
            // 移动激活时必须传递伪码
            throw new HttpException(400, '参数错误');
        }
        if (in_array($operator, [self::OPERATOR_UNICOM, self::OPERATOR_TELECOM])) {
            // 联通或电信激活时查询手机伪码
            if (!$code) {
                // 联通或电信激活时必须传递验证码
                throw new HttpException(400, '请输入验证码');
            }
            $account_type = LoginForm::getAccountType($account, 'CN');
            if (LoginForm::MOBILE !== $account_type) {
                throw new HttpException(400, '错误的手机号');
            }
            $usermob = $this->getUsermobByCode($operator, $account, $code);
        }

        // 通过伪码检查订购关系
        [$state, $flow_type] = $this->getUsermobState($usermob, $operator, true);
        $mosaic_account = $account ? MUtils::mosaicString((string)$account, MUtils::MOSAIC_MOBILE) : '(null)';
        Yii::info("account: {$mosaic_account}, operator{$operator}, usermob: {$usermob}, state: {$state}",
            'flow/activate');

        if (!self::isFreeFlowType($state)) {
            throw new HttpException(403, '该卡号未开通免流服务或已过期');
        }
        return [
            'account' => $mosaic_account,
            'usermob' => $usermob,
            'flow_type' => $flow_type,
        ];
    }

    /**
     * 通过验证码获取手机伪码
     *
     * @param int $operator 运营商，1：联通；2：电信；
     * @param string $account 手机号
     * @param string $code 短信验证码
     * @return string 手机伪码
     * @throws HttpException 获取伪码接口失败或短信验证失败时抛出异常
     */
    private function getUsermobByCode(int $operator, string $account, $code)
    {
        $usermob = '';
        $flow_service = Yii::$app->params['service']['bilibili-flow'];
        switch ($operator) {
            case self::OPERATOR_UNICOM:
                // 联通短信取号，获取手机伪码
                $result = $this->requestUnicomApi('videoif/smsNumber.do', $account, [
                    'vcode' => $code,
                ]);

                if (!$result) {
                    throw new HttpException(400, '免流服务维护中，请稍后再试');
                }
                // 打码手机号
                $mosaic_account = MUtils::mosaicString((string)$account, MUtils::MOSAIC_MOBILE);
                if ((int)$result['resultcode'] !== self::UNICOM_SUCCESS) {
                    Yii::warning($mosaic_account . ' result: ' . json_encode($result), 'flow/activate');
                    throw new HttpException(400, $result['errorinfo']);
                }
                if (!$result['userid']) {
                    throw new HttpException(403, '验证失败，请重试');
                }
                $usermob = $this->desDecrypt($result['userid'], $flow_service['cpkey']);
                break;
            case self::OPERATOR_TELECOM:
                // 电信获取伪码
                $usermob = $this->desEncrypt($account, $flow_service['cpkey']);
                // 验证短信验证码是否正确
                $params = ['usermob' => $usermob, 'captcha' => $code];
                $result = $this->requestBilibiliApi('x/wall/telecom/card/verification', $params);
                if ((int)$result['code'] !== self::BILIBILI_SUCCESS) {
                    throw new HttpException(400, $result['message']);
                }
                break;
            default:
                throw new HttpException(400, '参数错误');
        }
        return $usermob;
    }

    /**
     * 判断是否为可免流的状态
     *
     * @param int $unicomtype 联通卡状态
     * @return bool 是否可免流
     */
    private static function isFreeFlowType($unicomtype)
    {
        // 2 为已激活、4 为已退订（未过期）
        return $unicomtype === 2 || $unicomtype === 4;
    }

    /**
     * 获取哔哩哔哩流量卡/包状态
     *
     * cardtype（1：哔哩哔哩 22 卡、2：哔哩哔哩 33 卡、3：哔哩哔哩小电视卡、4：流量卡）
     * unicomtype（卡的激活状态 1：未激活、2：已激活、3：已退订（过期）、4：已退订（未过期））
     * ordertime（订购时间，秒级）
     * canceltime（退订时间，如果没有该字段时间字段不返回）
     * endtime（失效时间，如果没有该字段时间字段不返回）
     *
     * @param string $usermob （存储于客户端的）用户手机号伪码
     * @param int $operator 运营商，1：联通；2：电信；3：移动
     * @param bool $no_cache 不从缓存中获取数据，使激活的时候总是可以使用最新的数据
     *
     * @return array [state, flow_type]
     * state 为流量卡激活状态，1：未激活、2：已激活、3：已退订（过期）、4：已退订（未过期）
     * flow_type 为免流类型
     */
    private function getUsermobState(string $usermob, int $operator, bool $no_cache = false): array
    {
        $key = MUtils::generateCacheKey(KEY_USERMOB_STATE, $usermob);
        $usermob_info = null;
        if (!$no_cache) {
            $usermob_info = Yii::$app->memcache->get($key);
        }
        if (!$usermob_info) {
            $flow_service = Yii::$app->params['service']['bilibili-flow'];
            $api = '';
            $params = [];
            switch ($operator) {
                case self::OPERATOR_UNICOM:
                    // 联通获取免流状态
                    $api = 'x/wall/unicom/m/state';
                    $params = ['usermob' => $this->desEncrypt($usermob, $flow_service['cpkey'])];
                    break;
                case self::OPERATOR_TELECOM:
                    // 电信获取免流状态
                    $api = 'x/wall/telecom/card/state';
                    $params = ['usermob' => $usermob];
                    break;
                case self::OPERATOR_MOBILE:
                    // 移动获取免流状态
                    // 当前接口为临时兼容验签使用，后续需要加上签名参数改为调用 x/wall/mobile/status 接口
                    $api = 'x/wall/mobile/user/status';
                    $params = ['usermob' => $usermob];
                    break;
                default:
                    throw new HttpException(400, '参数错误');
            }
            $result = $this->requestBilibiliApi($api, $params);
            if (!$result) {
                throw new HttpException(400, '网络错误，请稍后再试');
            }
            if ((int)$result['code'] !== self::BILIBILI_SUCCESS) {
                throw new HttpException(400, $result['message']);
            }
            $usermob_info = $result['data'];
            // 更新缓存
            Yii::$app->memcache->set($key, $usermob_info, ONE_HOUR);
        }
        $state = self::USERMOB_STATE_UNACTICVE;
        $flow_type = self::FLOW_TYPE_NONE;
        switch ($operator) {
            case self::OPERATOR_UNICOM:
                // 联通获取免流状态
                if (!key_exists('unicomtype', $usermob_info)) {
                    Yii::error('unicom flow usermob info error: ' . json_encode($usermob_info)
                        . '. usermob is ' . $usermob, __METHOD__);
                    throw new HttpException(500, '网络错误，请稍后再试');
                }
                $state = $usermob_info['unicomtype'];
                // 接口在未开通免流时不返回 flowtype 字段
                $flow_type = $usermob_info['flowtype'] ?? self::FLOW_TYPE_NONE;
                if (!in_array($flow_type, [self::FLOW_TYPE_NONE, self::FLOW_TYPE_CARD, self::FLOW_TYPE_PACK])) {
                    Yii::error('unicom flow type error: ' . json_encode($usermob_info)
                        . '. usermob is ' . $usermob, __METHOD__);
                    throw new HttpException(500, '网络错误，请稍后再试');
                }
                break;
            case self::OPERATOR_TELECOM:
                // 电信获取免流状态
                $state = $usermob_info['order_state'];
                break;
            case self::OPERATOR_MOBILE:
                // 移动获取免流状态
                $state = $usermob_info['orderstatus'];
                break;
            default:
                throw new HttpException(400, '参数错误');
        }
        return [(int)$state, $flow_type];
    }

    /**
     * des-ecb 加密
     * @param string $data 要被加密的数据
     * @param string $key 加密密钥
     * @return string 加密后的文本（base64 编码）
     */
    private function desEncrypt($data, $key)
    {
        return openssl_encrypt($data, 'des-ecb', $key);
    }

    /**
     * des-ecb 解密
     * @param string $data 加密数据
     * @param string $key 加密密钥
     * @return string 解密后的文本
     */
    private function desDecrypt($data, $key)
    {
        return openssl_decrypt($data, 'des-ecb', $key);
    }

    /**
     * 请求联通 API
     *
     * @param string $uri API 路径
     * @param integer $mobile_num 用户手机号
     * @param string $params 其他请求参数，默认为空
     * @return array 联通 API 响应的数据
     */
    private function requestUnicomApi($uri, $mobile_num, $params = [])
    {
        $client = new Client([
            'base_uri' => self::UNICOM_API_URL,
            'timeout' => self::API_TIMEOUT,
        ]);
        $flow_service = Yii::$app->params['service']['bilibili-flow'];
        $userid = $this->desEncrypt($mobile_num, $flow_service['cpkey']);
        $query = [
            'cpid' => $flow_service['cpid'],
            'userid' => $userid,
            // apptype 为 2 为返回 json
            'apptype' => 2,
        ];
        try {
            $res = $client->request('GET', $uri, [
                'headers' => [
                    'Accept' => 'application/json',
                    // 'X-Forwarded-For' => Yii::$app->request->userIP,
                ],
                'query' => array_merge($query, $params),
            ]);
        } catch (ClientException $e) {
            $res = $e->getResponse();
        } catch (\Exception $e) {
            return null;
        }
        $body = $res->getBody();
        $content = $body->getContents();
        $result = json_decode($content, true);
        return $result;
    }

    /**
     * 请求 Bilibili 流量服务 API
     *
     * @param string $uri API 路径
     * @param integer $mobile_num 用户手机号
     * @param string $params 其他请求参数，默认为空
     * @return array 联通 API 响应的数据
     */
    private function requestBilibiliApi($uri, $params, $method = self::METHOD_GET)
    {
        $flow_service = Yii::$app->params['service']['bilibili-flow'];
        $client = new Client([
            'base_uri' => $flow_service['url'],
            'timeout' => self::API_TIMEOUT,
        ]);
        try {
            $option = [
                'headers' => [
                    'Accept' => 'application/json',
                    // 'X-Forwarded-For' => Yii::$app->request->userIP,
                ],
            ];
            if ($method === self::METHOD_GET) {
                $option['query'] = $params;
            } else {
                $option['form_params'] = $params;
            }
            $res = $client->request($method, $uri, $option);
        } catch (ClientException $e) {
            Yii::error($e->getMessage(), __METHOD__);
            $res = $e->getResponse();
        } catch (\Exception $e) {
            Yii::error($e->getMessage(), __METHOD__);
            return null;
        }
        $body = $res->getBody();
        $content = $body->getContents();
        $result = json_decode($content, true);
        return $result;
    }

    /**
     * 获取 B 站免流相关接口签名
     *
     * @return string
     * @todo 目前 B 站相关文档未给出签名规则，当前相关请求可忽略签名，之后需要完善
     */
    private function getBlibiliSign()
    {
        return '';
    }
}
