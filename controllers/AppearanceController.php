<?php

namespace app\controllers;

use app\components\base\filter\AccessControl;
use app\middlewares\ControllerV2;
use yii\filters\VerbFilter;

class AppearanceController extends ControllerV2
{
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'create-alipay-order' => ['post'],
                'create-wechat-order' => ['post'],
                'diamond-buy' => ['post'],
                'order-detail' => ['get'],
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'only' => [
                'create-alipay-order',
                'create-wechat-order',
                'diamond-buy',
                'order-detail',
            ],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => [
                        'create-alipay-order',
                        'create-wechat-order',
                        'diamond-buy',
                        'order-detail',
                    ],
                    'roles' => ['@']
                ],
            ]
        ];
        return $behaviors;
    }
    /**
     * @api {post} /appearance/create-alipay-order 创建支付宝支付外观套装订单接口
     * @apiDescription https://opendocs.alipay.com/open/repo-0243dw
     * @apiName create-alipay-order
     * @apiGroup appearance
     *
     * @apiPermission user
     *
     * @apiParam {Number} appreance_id 外观套装 ID
     * @apiParam {Number} num 购买月份数量
     *
     * @apiSuccess {Number} code
     * @apiSuccess {String} message
     * @apiSuccess {Object} data
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "message": "",
     *       "data": {
     *         "trade_no": "appearance_17200000000010123456789",  // 订单编号
     *         "alipay_body": "alipay_sdk=alipay-sdk-php-20161101&app_id=9021000126659465&biz_content=%7B...PqTQ2DdUn8%2BBDw%3D%3D"
     *       }
     *     }
     */
    public function actionCreateAlipayOrder()
    {
        // TODO: not implemented
        return [];
    }

    /**
     * @api {post} /appearance/create-wechat-order 创建微信支付外观套装订单接口
     * @apiDescription https://pay.weixin.qq.com/doc/v3/merchant/4013070347
     * @apiName create-wechat-order
     * @apiGroup appearance
     *
     * @apiPermission user
     *
     * @apiParam {Number} appreance_id 外观套装 ID
     * @apiParam {number{1-24}} num 购买月份数量
     *
     * @apiSuccess {Number} code
     * @apiSuccess {String} message
     * @apiSuccess {Object} data
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "message": "",
     *       "data": {
     *         "trade_no": "appearance_17200000000010123456789",  // 订单编号
     *         "wechatpay_body": {
     *           "appid": "wx143bd0e4a3b523cf",  // 应用 ID
     *           "partnerid": "1482967912",  // 商户号
     *           "prepayid": "wx291408069667805baec1c0fa3046573997",  // 预支付交易会话 ID
     *           "package": "Sign=WXPay",  // 扩展字段（固定值 Sign=WXPay）
     *           "noncestr": "umIsOxffZ100HpbrE2POmc4MArVZIS",  // 随机字符串
     *           "timestamp": 1540793286,  // 时间戳（单位：秒）
     *           "sign": "F57D2F4247DB619631AB1FDF97506CB3"  // 签名
     *         }
     *       }
     *     }
     */
    public function actionCreateWechatOrder()
    {
        // TODO: not implemented
        return [];
    }

    /**
     * @api {post} /appearance/diamond-buy 钻石购买外观套装
     * @apiName diamond-buy
     * @apiGroup appearance
     *
     * @apiPermission user
     *
     * @apiParam {Number} appreance_id 外观套装 ID
     * @apiParam {number{1-24}} num 购买月份数量
     *
     * @apiSuccess {Number} code
     * @apiSuccess {String} message
     * @apiSuccess {Object} data
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "message": "",
     *       "data": {
     *         "deducted_record": {
     *           "id": 222,  // 交易记录 ID
     *           "create_time": 1720000000,
     *           "user_id": 1,
     *           "appreance_id": 1,  // 外观套装 ID
     *           "num": 2,  // 购买的月份数
     *           "price": 100,  // 本次扣款金额（单位：钻）
     *           "status": 2  // 本次扣款状态：1 待扣款，2 扣款成功，3 扣款失败，4 重复支付，5 已退款（现金）
     *         },
     *         "balance": 2467  // 用户剩余的钻石数
     *       }
     *     }
     */
    public function actionDiamondBuy()
    {
        // TODO: not implemented
        return [];
    }

    /**
     * @api {get} /appearance/order-detail 支付订单详情接口
     * @apiName order-detail
     * @apiGroup appearance
     *
     * @apiPermission user
     *
     * @apiParam {String} trade_no 交易编号
     *
     * @apiSuccess {Number} code
     * @apiSuccess {String} message
     * @apiSuccess {Object} data
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "message": "",
     *       "data": {
     *         "deducted_record": {
     *           "id": 222,  // 交易记录 ID
     *           "create_time": 1720000000,
     *           "user_id": 1,
     *           "appreance_id": 1, // 外观套装 ID
     *           "num": 2,  // 购买的月份数
     *           "pay_type": 3,  // 付费方式：1 钻石，2 微信，3：支付宝
     *           "price": 100,  // 本次扣款金额（单位：分）
     *           "status": 2  // 本次扣款状态：1 待扣款，2 扣款成功，3 扣款失败，4 重复支付，5 已退款（现金）
     *         }
     *       }
     *     }
     */
    public function actionOrderDetail(string $trade_no)
    {
        // TODO: not implemented
        return [];
    }
}
