<?php

namespace app\controllers;

use app\components\util\Equipment;
use app\middlewares\Controller;
use app\models\MRadioSound;
use app\models\MSound;
use app\models\MSoundPlayLog;
use app\models\MUserLikeElement;
use app\models\MUserRadioInfo;
use app\components\base\filter\AccessControl;
use Exception;
use missevan\storage\StorageClient;
use Yii;
use yii\filters\VerbFilter;
use yii\web\HttpException;

/**
 * 封装催眠电台相关 Api 接口
 *
 * <AUTHOR> <<EMAIL>>
 */
class RadioController extends Controller
{
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'like-sound' => ['post'],
                'cancel-like-sound' => ['post'],
                'add-play-times' => ['post'],
                'add-play-log' => ['post'],
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'only' => [
                'like-sound',
                'cancel-like-sound',
                'get-like-sounds',
                'history',
            ],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => [
                        'like-sound',
                        'cancel-like-sound',
                        'get-like-sounds',
                        'history',
                    ],
                    'roles' => ['@']
                ],
            ]
        ];
        return $behaviors;
    }

    public function actionSound(int $sound_id)
    {
        if (Equipment::isAppVersion(Equipment::Android, '5.7.3')) {
            // WORKAROUND: 临时兼容安卓 5.7.3 版本错误请求 /radio/sound 接口问题
            // 为避免客户端报错，需要返回固定格式数据，数据的值不影响正常播放
            return [
                'id' => $sound_id,
                'duration' => 0,
                'user_id' => 0,
                'username' => '',
                'soundstr' => '',
                'intro' => '',
                'front_cover' => '',
                'soundurl' => '',
                'soundurl_64' => '',
                'soundurl_128' => '',
                'liked' => 0,
                'interactive_id' => 0,  // 互动剧节点 ID，该接口数据实际会用在 /sound/sound 逻辑，需要返回该值
                'type' => 0,
            ];
        }
        throw new HttpException(404, '接口不存在');
    }

    /**
     * @api {get} /radio/user-info 催眠专享首页用户信息
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/radio/user-info
     * @apiSampleRequest /radio/user-info
     *
     * @apiVersion 0.1.0
     * @apiName user-info
     * @apiGroup radio
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "user": {  // 未登录时值为 null
     *           "username": "加特林",
     *           "listen_duration": 233  // 收听音频累计时长，单位毫秒
     *         },
     *         "share_title": "分享标题",
     *         "share_content": "分享文案内容",
     *         "share_cover": "https://static.test.com/test/test.jpg",
     *         "share_url": "https://www.test.com/radio/hypnosis"
     *       }
     *     }
     */
    public function actionUserInfo()
    {
        $user_id = (int)Yii::$app->user->id;
        $user = null;
        if ($user_id) {
            $user = [
                'username' => Yii::$app->user->name,
                'listen_duration' => MUserRadioInfo::getPlayedDuration($user_id),
            ];
        }
        // 获取首页分享信息
        $share_info = MRadioSound::getHomepageShareInfo();
        return array_merge(['user' => $user], $share_info);
    }

    /**
     * @api {get} /radio/get-catalogs 获取催眠专享电台分类
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/radio/get-catalogs
     * @apiSampleRequest radio/get-catalogs
     *
     * @apiVersion 0.1.0
     * @apiName get-catalogs
     * @apiGroup radio
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [  // 分类列表
     *         {
     *           "id": 156,
     *           "name": "自习室",
     *           "share_title": "分享文案标题",  // 仅父分类有分享文案，若有子分类，使用父分类分享文案
     *           // 仅父分类有分享链接，若有子分类，使用父分类分享链接。其中“__SOUND_ID__”为音频 ID 占位符，使用时需要用正在播放的音频 ID 替换
     *           "share_url": "https://www.test.com/radio/hypnosis/__SOUND_ID__?foo=bar"
     *         },
     *         {
     *           "id": 160,
     *           "name": "哄睡",
     *           "share_title": "分享文案标题",
     *           "share_url": "https://www.test.com/radio/hypnosis/__SOUND_ID__?foo=bar",
     *           "icon": "http://static-test.maoercdn.com/test/test.png",  // 分类左侧图标（包含了未激活及激活图，分别在上下侧），没有时不返回
     *           "sub_catalogs": [  // 子分类，没有时不返回
     *             {
     *               "id": 161,
     *               "name": "小哥哥"
     *             },
     *             {
     *               "id": 162,
     *               "name": "小姐姐"
     *             }
     *           ]
     *         }
     *       ]
     *     }
     */
    public function actionGetCatalogs()
    {
        $catalogs = MRadioSound::getCatalogs();
        if (empty($catalogs)) {
            Yii::error('催眠专享分类未配置');
            throw new HttpException(500, '加载失败，请稍后重试');
        }
        return $catalogs;
    }

    /**
     * @api {get} /radio/get-catalog-sounds{?catalog_id} 获取催眠专享电台分类下的音频列表
     * @apiDescription 每个分类下音频数量不超过 100，不考虑分页操作，列表中出现的擦边球、付费音将被过滤
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/radio/get-catalog-sounds
     * @apiSampleRequest radio/get-catalog-sounds
     *
     * @apiVersion 0.1.0
     * @apiName get-catalog-sounds
     * @apiGroup radio
     *
     * @apiParam {Number} catalog_id 分类 ID，若为子分类，使用子分类 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "id": 233,  // 催眠专享音频 ID
     *           "catalog_id": 156,  // 催眠专享分类 ID
     *           "title": "加特林的安眠屋",
     *           "front_cover": "http://static-test.maoercdn.com/test/test.png", // 封面图
     *           "background_cover": "http://static-test.maoercdn.com/test/test.webp" // 背景图
     *         },
     *         {
     *           "id": 233,  // 催眠专享音频 ID
     *           "catalog_id": 156,
     *           "title": "余额宝的安眠屋",
     *           "front_cover": "http://static-test.maoercdn.com/test/test.png",
     *           "background_cover": "http://static-test.maoercdn.com/test/test.webp"
     *         }
     *       ]
     *     }
     */
    public function actionGetCatalogSounds(int $catalog_id)
    {
        $sounds = MRadioSound::getCatalogSounds($catalog_id);
        if (empty($sounds)) {
            Yii::error("催眠专享分类（{$catalog_id}）下可播音频为空");
            // PASS
        }
        return $sounds;
    }

    /**
     * @api {get} /radio/get-sound{?sound_id} 获取音频信息
     * @apiDescription 页面重新展示的时候（如从后台回到页面），若距离上次接口请求超过 1h，\
     * 应该重新请求接口获取音频信息和播放地址
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/radio/get-sound
     * @apiSampleRequest radio/get-sound
     *
     * @apiVersion 0.1.0
     * @apiName get-sound
     * @apiGroup radio
     *
     * @apiParam {Number} sound_id 催眠专享音频 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 233,  // 催眠专享音频 ID
     *         "catalog_id": 156,  // 催眠专享分类 ID
     *         "title": "加特林的安眠屋",
     *         "duration": 123123,  // 音频时长，单位毫秒
     *         "front_cover": "http://static-test.maoercdn.com/test/test.png",  // 封面图
     *         "background_cover": "http://static-test.maoercdn.com/test/test.webp",  // 背景图
     *         "background_video": "http://static-test.maoercdn.com/test/test.mp4",  // 背景视频，没有时不返回
     *         "soundurl": "http://static-test.maoercdn.com/test/test.m4a?sign=test",
     *         "soundurl_128": "http://static-test.maoercdn.com/test/test-128k.m4a?sign=test",
     *         "user_id": 666,  // 未返回时不显示 UP 信息
     *         "username": "加特林",  // 未返回时不显示 UP 信息
     *         "avatar": "http://static-test.maoercdn.com/test/avatar.png",  // 未返回时不显示 UP 信息
     *         "progress": 1,  // 是否有播放进度条，0：无；1：有
     *         "liked": 0  // 是否已“喜欢”，0：否；1：是  // 未登录时不返回
     *       }
     *     }
     */
    public function actionGetSound(int $sound_id)
    {
        if ($sound_id <= 0) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        $sound = MRadioSound::getSound($sound_id);
        if (!$sound) {
            // 当音频不存在时，需要提示已失效
            throw new HttpException(404, '该音频已失效');
        }
        $user_id = Yii::$app->user->id;
        if ($user_id) {
            $is_like = MUserLikeElement::isLike($user_id, MUserLikeElement::ELEMENT_TYPE_RADIO_SOUND,
                $sound_id);
            $sound['liked'] = (int)$is_like;
        }
        return $sound;
    }

    /**
     * @api {post} /radio/like-sound{?sound_id} “喜欢”音频
     * @apiDescription 接口具有幂等性
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/radio/like-sound
     * @apiSampleRequest /radio/like-sound
     *
     * @apiVersion 0.1.0
     * @apiName like-sound
     * @apiGroup radio
     *
     * @apiParam (Query) {Number} sound_id 催眠专享音频 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "status": true,
     *         "msg": "已添加到喜欢"
     *       }
     *     }
     */
    public function actionLikeSound(int $sound_id)
    {
        if ($sound_id <= 0) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        $radio_sound_exists = MRadioSound::find()
            ->where(['id' => $sound_id, 'delete_time' => 0])
            ->exists();
        if (!$radio_sound_exists) {
            throw new HttpException(404, Yii::t('app/error', 'Audio does not exist'));
        }
        $user_id = Yii::$app->user->id;
        $is_liked = MUserLikeElement::isLike($user_id, MUserLikeElement::ELEMENT_TYPE_RADIO_SOUND,
            $sound_id);
        if (!$is_liked) {
            $transaction = null;
            try {
                // 仅可喜欢一定数量音频，若超过该数量，则最开始喜欢的音频需要取消喜欢
                $old_id = (int)MUserLikeElement::find()
                    ->select('id')
                    ->where([
                        'user_id' => $user_id,
                        'element_type' => MUserLikeElement::ELEMENT_TYPE_RADIO_SOUND,
                    ])
                    ->orderBy(['id' => SORT_DESC])
                    ->offset(MRadioSound::LIKE_SOUNDS_PAGE_SIZE - 1)
                    ->limit(1)->scalar();
                $transaction = Yii::$app->db1->beginTransaction();
                if ($old_id) {
                    MUserLikeElement::deleteAll('id <= :old_id AND user_id = :user_id AND element_type = :element_type', [
                        ':old_id' => $old_id,
                        ':user_id' => $user_id,
                        ':element_type' => MUserLikeElement::ELEMENT_TYPE_RADIO_SOUND,
                    ]);
                }
                // 若未“喜欢”，则添加到“喜欢”
                MUserLikeElement::likeOrNot($user_id, MUserLikeElement::ELEMENT_TYPE_RADIO_SOUND, $sound_id,
                    true);
                $transaction->commit();
            } catch (Exception $e) {
                if ($transaction) {
                    $transaction->rollBack();
                }
                throw $e;
            }
        }
        return ['msg' => '已加入我的喜欢', 'status' => true];
    }

    /**
     * @api {post} /radio/cancel-like-sound{?sound_id} 取消“喜欢”音频
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/radio/cancel-like-sound
     * @apiSampleRequest /radio/cancel-like-sound
     *
     * @apiVersion 0.1.0
     * @apiName cancel-like-sound
     * @apiGroup radio
     *
     * @apiParam (Query) {Number} sound_id 催眠专享音频 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "status": false,
     *         "msg": "已取消喜欢"
     *       }
     *     }
     */
    public function actionCancelLikeSound(int $sound_id)
    {
        if ($sound_id <= 0) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        $user_id = Yii::$app->user->id;
        $is_liked = MUserLikeElement::isLike($user_id, MUserLikeElement::ELEMENT_TYPE_RADIO_SOUND,
            $sound_id);
        if ($is_liked) {
            // 若为喜欢的催眠专享音频，则取消喜欢
            MUserLikeElement::likeOrNot($user_id, MUserLikeElement::ELEMENT_TYPE_RADIO_SOUND, $sound_id,
                false);
        }
        return ['msg' => '已取消喜欢', 'status' => false];
    }

    /**
     * @api {get} /radio/get-like-sounds 获取用户“喜欢”的音频列表
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/radio/get-like-sounds
     * @apiSampleRequest /radio/get-like-sounds
     * @apiDescription 根据用户 ID 获取喜欢的音频
     *
     * @apiVersion 0.1.0
     * @apiName get-like-sounds
     * @apiGroup radio
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "id": 14429,
     *           "title": "音频标题",
     *           "front_cover": "http://static-test.maoercdn.com/test/test.png",  // 封面图
     *           "background_cover": "http://static-test.maoercdn.com/test/test.webp",  // 背景图
     *           "is_invalid": true  // 是否已失效，仅在失效时下发
     *         }
     *       ]
     *     }
     */
    public function actionGetLikeSounds()
    {
        $user_id = Yii::$app->user->id;
        $sound_ids = MUserLikeElement::find()
            ->select('element_id')
            ->where(['user_id' => $user_id, 'element_type' => MUserLikeElement::ELEMENT_TYPE_RADIO_SOUND])
            ->limit(MRadioSound::LIKE_SOUNDS_PAGE_SIZE)
            ->orderBy(['id' => SORT_DESC])
            ->column();
        if (empty($sound_ids)) {
            return [];
        }
        // 由于两张表在不同的库，故不用 JOIN 连查
        // TODO: 之后 m_user_like_element 表需要迁移到同一个库
        $radio_sounds = MRadioSound::find()
            ->select('id, title, cover, background_cover')
            ->where(['id' => $sound_ids, 'delete_time' => 0])
            ->indexBy('id')
            ->all();
        return array_map(function ($sound_id) use ($radio_sounds) {
            $sound_id = (int)$sound_id;
            $title = '音频已失效';
            $front_cover = Yii::$app->params['defaultCoverUrl'];
            $background_cover = '';
            $is_invalid = true;
            if (key_exists($sound_id, $radio_sounds)) {
                $radio_sound = $radio_sounds[$sound_id];
                $title = $radio_sound->title;
                $front_cover = $radio_sound->cover;
                $background_cover = $radio_sound->background_cover;
                $is_invalid = false;
            }
            $item = [
                'id' => $sound_id,
                'title' => $title,
                'front_cover' => $front_cover,
                'background_cover' => $background_cover,
            ];
            if ($is_invalid) {
                $item['is_invalid'] = $is_invalid;
            }
            return $item;
        }, $sound_ids);
    }

    /**
     * @api {get} /radio/history 获取播放记录
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/radio/history
     * @apiSampleRequest /radio/history
     * @apiDescription 获取催眠专享播放记录
     *
     * @apiVersion 0.1.0
     * @apiName history
     * @apiGroup radio
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "id": 1,
     *           "sound_id": 233,  // 催眠专享音频 ID
     *           "title": "标题标题",
     *           "front_cover": "http://static-test.maoercdn.com/test/test.png",  // 封面图
     *           "background_cover": "http://static-test.maoercdn.com/test/test.webp",  // 背景图
     *           "access_time": 1598585889000  // 播放点时间戳（单位：毫秒）
     *         }
     *       ]
     *     }
     */
    public function actionHistory()
    {
        $history = Yii::$app->go->getRadioPlayHistory(Yii::$app->user->id, MRadioSound::HISTORY_PAGE_SIZE);
        return $history['data'];
    }

    /**
     * @api {post} /radio/add-play-times{?sound_id} 增加催眠专享音频播放次数
     * @apiDescription 仅在每次切换或点开音频，开始播放时调用
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/radio/add-play-times?sound_id=1
     * @apiSampleRequest /radio/add-play-times
     *
     * @apiVersion 0.1.0
     * @apiName add-play-times
     * @apiGroup radio
     *
     * @apiParam (Query) {Number} sound_id 催眠专享音频 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Boolean} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": true
     *     }
     */
    public function actionAddPlayTimes(int $sound_id)
    {
        if ($sound_id <= 0) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        $m_sound_id = (int)MRadioSound::find()->select('sound_id')
            ->where('id = :id AND delete_time = 0', [':id' => $sound_id])->scalar();
        if (!$m_sound_id) {
            throw new HttpException(404, Yii::t('app/error', 'Audio does not exist'));
        }
        $sound = MSound::find()->select('id')->where('id = :id', [':id' => $m_sound_id])->one();
        if (!$sound) {
            throw new HttpException(404, Yii::t('app/error', 'Audio does not exist'));
        }
        // TODO: 之后应调整为添加催眠专享音频播放次数
        $sound->addPlayTimes(1);
        return true;
    }

    /**
     * @api {post} /radio/add-play-log{?sound_id,operation_type,played_duration,completion} 添加播放日志
     * @apiDescription 添加播放时长（operation_type 为 3）及播放历史记录（operation_type 为 1 或 3）都在该接口处理
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/radio/add-play-log?sound_id=88771&played_duration=2333&operation_type=3
     * @apiSampleRequest /radio/add-play-log
     *
     * @apiVersion 0.1.0
     * @apiName add-play-log
     * @apiGroup radio
     *
     * @apiParam (Query) {Number} sound_id 催眠专享音频 ID
     * @apiParam (Query) {Number} operation_type 播放行为类型，1：开始；2：暂停；3：播放结束
     * @apiParam (Query) {Number} [played_duration=0] 实际播放时长（单位为毫秒）
     * @apiParam (Query) {number=0~1} [completion=0] 最后的时间点占总时长的比例 \
     * 保留四位小数 0.0000 ~ 1.0000（不是实际播放时长占总时长的比例）
     *
     * @apiSuccess {Boolean} success true or false
     * @apiSuccess {Number} code
     * @apiSuccess {Boolean} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": true
     *     }
     */
    public function actionAddPlayLog(int $sound_id, int $operation_type, int $played_duration = 0,
            float $completion = 0)
    {
        if ($sound_id <= 0 || $played_duration < 0 || !in_array($operation_type, MSoundPlayLog::OPERATION_TYPES)) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        if ($operation_type === MSoundPlayLog::OPERATION_TYPE_PAUSE) {
            // 目前仅处理加时长及历史记录的情况，对于暂停的请求直接返回 true
            return true;
        }
        $m_sound_id = (int)MRadioSound::find()->select('sound_id')
            ->where('id = :id AND delete_time = 0', [':id' => $sound_id])->scalar();
        if (!$m_sound_id) {
            throw new HttpException(404, Yii::t('app/error', 'Audio does not exist'));
        }
        if (!MSound::find()->where('id = :id', [':id' => $m_sound_id])->exists()) {
            throw new HttpException(404, Yii::t('app/error', 'Audio does not exist'));
        }
        $user_id = (int)Yii::$app->user->id;
        if ($user_id) {
            if ($operation_type === MSoundPlayLog::OPERATION_TYPE_END && $played_duration) {
                // 播放结束时，若有播放时长，则添加用户催眠专享播放总时长
                MUserRadioInfo::addPlayedDuration($user_id, $played_duration);
            }
            // 添加或更新历史记录
            Yii::$app->go->addRadioPlayHistory($user_id, $sound_id, $completion);
        }
        // TODO: 添加播放日志（需要新建表）
        return true;
    }
}
