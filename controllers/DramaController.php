<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/8/4
 * Time: 21:09
 */

namespace app\controllers;

use app\components\util\Equipment;
use app\components\util\MUtils;
use app\forms\TransactionForm;
use app\models\Balance;
use app\models\Catalog;
use app\models\DramaBoughtDetailLog;
use app\models\MDramaRewardRanks;
use app\models\Mowangsksoundseiy;
use app\models\Mowangskuser;
use app\models\MPersonDramaPage;
use app\models\MSound;
use app\models\Drama;
use app\models\MUserVip;
use app\models\PaginationParams;
use app\models\Persona;
use app\models\DramaRewardMenu;
use app\models\RewardMessage;
use app\models\SoundVideo;
use app\models\TransactionLog;
use app\models\TransactionSoundLog;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use Yii;
use app\forms\TransactionForm as TForm;
use app\middlewares\Controller;
use app\components\base\filter\AccessControl;
use missevan\util\HttpExceptionI18n;
use missevan\util\MUtils as MUtils2;
use missevan\util\I18nMessage;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use yii\web\HttpException;

class DramaController extends Controller
{

    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'subscribe' => ['post'],
                'buy-drama' => ['post'],
                'buy-drama-episodes' => ['post'],
                'pay-episodes-detail' => ['post'],
                'reward' => ['post'],
                'reward-message' => ['post'],
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'only' => [
                'subscribe',
                'buy-drama',
                'buy-drama-episodes',
                'reward',
                'reward-message',
                'reward-menu',
                'get-feed-num',
            ],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => [
                        'subscribe',
                        'buy-drama',
                        'buy-drama-episodes',
                        'reward',
                        'reward-message',
                        'reward-menu',
                        'get-feed-num',
                    ],
                    'roles' => ['@']
                ],
            ]
        ];
        return $behaviors;
    }

    /**
     * @api {post} /drama/subscribe 追剧
     * @apiSampleRequest drama/subscribe
     * @apiDescription  test.com/drama/subscribe
     *
     * @apiVersion 0.1.0
     * @apiName subscribe
     * @apiGroup drama
     *
     * @apiPermission user
     *
     * @apiParam {Number} drama_id 剧集 ID
     * @apiParam {number=0,1} type 是否追剧 0：取消追剧；1：追剧
     *
     * @apiSuccess {Bool} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 追剧或取消追剧详情
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info": {
     *          "msg": "嘤嘤嘤 主人果真不爱我了",
     *          "subscribe": 0
     *      }
     *
     */
    public function actionSubscribe()
    {
        if (Equipment::isBanVersion()) {
            throw new HttpExceptionI18n(403, 'Please upgrade the App to the latest version for a better experience~');
        }
        $user_id = Yii::$app->user->id;
        $drama_id = (int)Yii::$app->request->post('drama_id');
        $type = (int)Yii::$app->request->post('type');

        if (!$drama_id) {
            throw new HttpExceptionI18n(400, 'Drama id can not be empty');
        }

        if (Drama::DRAMA_SUBSCRIBE === $type
                && Drama::checkDramaRefined($drama_id, Drama::REFINED_TYPE_SPECIAL)) {
            throw new HttpExceptionI18n(403, 'Limited drama does not support this operation');
        }
        $return = Drama::subscribe($drama_id, $user_id, $type);
        return $return;
    }

    /**
     * @api {get} /drama/get-drama-by-sound 根据单音 ID 获取剧集 ID
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/get-drama-by-sound
     * @apiSampleRequest drama/get-drama-by-sound
     *
     * @apiVersion 0.1.0
     * @apiName get-drama-by-sound
     * @apiGroup drama
     *
     * @apiParam {Number} sound_id 单音 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "drama": {
     *           "id": 9888,
     *           "name": "《杀破狼》广播剧  第一季",
     *           "cover": "http://static.missevan.com/dramacoversmini/201708/25/389768874e2f317c57123406.jpg",
     *           "abstract": "本作品为付费广播剧",
     *           "integrity": 1,
     *           "age": 2,
     *           "origin": 1,
     *           "author": "priest",
     *           "birthday": 0,
     *           "cv": null,
     *           "ip": 0,
     *           "ipname": null,
     *           "type": 3,
     *           "newest": "第二期",
     *           "organization_id": 1,
     *           "user_id": 441514,
     *           "username": "729 声工场",
     *           "checked": 1,
     *           "create_time": 1503635635,
     *           "lastupdate_time": 1505899714,
     *           "view_count": 4034,
     *           "catalog": 89,
     *           "alias": null,
     *           "pay_type": 1,
     *           "push": 0,
     *           "refined": 0,
     *           "police": 0,
     *           "type_name": "全年龄",
     *           "need_pay": 1,
     *           "purchased": null,
     *           "price": 13,
     *           "catalog_name": "中文广播剧",
     *           "like": 1,
     *           "saw_episode_id": 67756,
     *           "saw_episode": "第一期",
     *           "style": 1,  // 剧集样式 0：默认样式；1：新音乐集样式
     *           "corner_mark": {  // 无剧集角标时不返回该字段
     *             "text": "已购",
     *             "text_color": "#ffffff",  // 文字颜色
     *             "text_start_color": "#ffffff",  // 文字渐变起始颜色，优先使用 text_start_color 和 text_end_color 来展示文字颜色，不存在的话使用 text_color
     *             "text_end_color": "#ffffff",  // 文字渐变结束颜色，优先使用 text_start_color 和 text_end_color 来展示文字颜色，不存在的话使用 text_color
     *             "bg_start_color": "#e66465",
     *             "bg_end_color": "#e66465",
     *             "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *           }
     *         },
     *         "episodes": {
     *           "episode": [{
     *             "id": 536892,
     *             "eid": 62531,
     *             "sound_id": 536892,
     *             "name": "预告",
     *             "soundstr": "《杀破狼》广播剧 预先",
     *             "duration": 241506,
     *             "pay_type": 0,
     *             "video": false,
     *             "price": 0,
     *             "status": 0,  // 播放页单集显示的状态（0 默认样式，1 显示“NEW"样式）
     *             "need_pay": 0,
     *             "vip": 1  // 会员单集状态。0：非会员单集；1：会员剧下的试听单集；2：会员剧下的非试听单集。使用 vip 字段来判断展示会员限制角标，否则使用 need_pay 字段来判断展示付费限制角标
     *           }, {
     *             "id": 562111,
     *             "eid": 67756,
     *             "sound_id": 562111,
     *             "name": "第一期",
     *             "soundstr": "《杀破狼》广播剧 第一期",
     *             "duration": 241506,
     *             "pay_type": 2,
     *             "video": false,
     *             "price": 0,
     *             "status": 0,
     *             "need_pay": 1,
     *             "vip": 1
     *           }, {
     *             "id": 562158,
     *             "eid": 67757,
     *             "sound_id": 562158,
     *             "name": "第二期",
     *             "soundstr": "《杀破狼》广播剧 第二期",
     *             "duration": 241506,
     *             "pay_type": 2,
     *             "video": false,
     *             "price": 0,
     *             "status": 0,
     *             "need_pay": 1,
     *             "vip": 1
     *           }],
     *           "ft": [],
     *           "music": []
     *         },
     *         "cvs": [
     *           {
     *             "id": 181482,
     *             "episode_id": 67757,
     *             "cv_id": 1013,
     *             "character": "顾昀",
     *             "main": 1,
     *             "drama_id": 9888,
     *             "cvinfo": {
     *               "id": 1013,
     *               "icon": "https://static.maoercdn.com/seiys/201805/31/33d0f6e6de93547dd90ac5c62327e139153016.png",
     *               "name": "阿杰",
     *               "group": "729声工场"
     *             },
     *             "live": {  // 用户没有开播时不返回该字段
     *               "room_id": 11252216,
     *               "status": 1  // 直播间状态，0：没有开启房间；1：房间开启
     *             }
     *           }
     *         ],
     *         "rewardable": 1,
     *         "sound_episode_id": 67756,
     *         "sound_episode_name": "第一期",
     *         "reward_info": {
     *           "reward_num": 7,
     *           "one_week_reward_num": 0,
     *           "rank": 0,
     *           "reward_users": []
     *         },
     *         "derivatives": [
     *           {
     *             "type": 2,  // 周边分类，1：商品；2；求签包；3；语音包；4：通用
     *             "title": "运势求签",
     *             "intro": "来看看今天的运势吧~",
     *             "tag": "求签",
     *             "url": "missevan://omikuji/draw",
     *             "cover": "https://static.missevan.com/derivate/201907/08/cover.jpg"
     *           }
     *         ],
     *         "seasons": [
     *           {
     *             "season": 1,
     *             "drama_id": 9888,
     *             "sound_id": 123,
     *             "season_name": "第一季",
     *             "saw_sound_id": 123,
     *             "sounds": [
     *               {
     *                 "sound_id": 32882,
     *                 "soundstr": "金牌助理之弯弯没想到S01E01",
     *                 "episode_name": "S01E01"
     *               },
     *              {
     *                 "sound_id": 32883,
     *                 "soundstr": "金牌助理之弯弯没想到S01E02",
     *                 "episode_name": "S01E02"
     *               }
     *             ]
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionGetDramaBySound(int $sound_id)
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }
        $user_id = Yii::$app->user->id;
        if (!$sound_id) {
            throw new HttpException(400, '单音 ID 不能为空');
        }
        try {
            $drama = Drama::rpc('api/get-drama-details-by-sound',
                ['sound_id' => $sound_id, 'user_id' => $user_id]);
            if (!$drama) {
                return null;
            }
        } catch (\Exception $e) {
            // PASS: 为不影响播放页正常播放，不抛出异常处理
            return null;
        }
        $memcache = Yii::$app->memcache;
        $drama_details_key = MUtils::generateCacheKey(KEY_MOBILE_DRAMA_DETAILS_BY_SOUND, $sound_id);
        if (!$drama_details_cache = $memcache->get($drama_details_key)) {
            $drama['drama']['like'] = (int)$drama['like'];
            $drama['sound_episode_id'] = $drama['current_id'];
            $drama['sound_episode_name'] = $drama['current_name'];
            unset($drama['like'], $drama['organization'], $drama['current_id'], $drama['current_name']);
            if ($drama['drama']['style'] === Drama::STYLE_INTERACTIVE) {
                // 互动剧不需要返回单集信息
                $drama['episodes'] = [];
            } else {
                $sound_ids = array_column($drama['episodes'], 'sound_id');
                $vids = SoundVideo::find()->select('sid')
                    ->where(['sid' => $sound_ids, 'checked' => SoundVideo::CHECKED_PASS])
                    ->indexBy('sid')->column();
                $sounds = MSound::find()->select('id, soundstr, duration')
                    ->where(['id' => $sound_ids])->indexBy('id')->asArray()->all();
                $drama['episodes'] = array_map(function ($ep) use ($drama, $vids, $sounds) {
                    $item = [
                        'id' => $ep['sound_id'],
                        'eid' => $ep['id'],  // 用于客户端定位上次观看单集的位置
                        'sound_id' => $ep['sound_id'],
                        'name' => $ep['name'],
                        'soundstr' => $sounds[$ep['sound_id']]['soundstr'] ?? '',
                        'duration' => (int)($sounds[$ep['sound_id']]['duration'] ?? 0),
                        'pay_type' => $ep['pay_type'],
                        'video' => array_key_exists($ep['sound_id'], $vids),
                        'price' => 0,
                        'status' => Drama::EPISODE_STATUS_COMMON,
                        'type' => $ep['type'],
                        'subtitle' => $ep['subtitle'],
                        'vip' => $ep['vip'],
                    ];
                    if (Drama::PAY_TYPE_EPISODE === $drama['drama']['pay_type']
                        && MSound::SOUND_FREE !== $ep['pay_type']) {
                        $item['price'] = $drama['drama']['price'];
                    }
                    if (Drama::INTEGRITY_NAME_SERIALIZING === $drama['drama']['integrity']
                            && $item['name'] === $drama['drama']['newest']
                            && $_SERVER['REQUEST_TIME'] - $drama['drama']['lastupdate_time'] < 2 * ONE_WEEK) {
                        // 如果两周内用户尚未观看剧集的最后更新的单集则显示为 "NEW" 的样式
                        $item['status'] = Drama::EPISODE_STATUS_NEW;
                    }
                    return $item;
                }, $drama['episodes']);
                if ($drama['drama']['pay_type'] !== Drama::PAY_TYPE_DRAMA) {
                    $drama['drama']['price'] = 0;
                }
            }
            // 处理 CV 信息
            Mowangsksoundseiy::processCVDetails($drama['cvs']);

            $memcache->set($drama_details_key, Json::encode($drama), FIVE_MINUTE);
        } else {
            $drama = Json::decode($drama_details_cache);
        }
        if ($user_id) {
            // 登录时需要判断用户是否追剧等
            $is_subscribed = Drama::rpc('api/user-subscription',
                ['drama_id' => $drama['drama']['id'], 'user_id' => $user_id]);
            $drama['drama']['like'] = $is_subscribed;
            if ($drama['drama']['ip_id'] && $drama['seasons']) {
                $drama_ids = array_column($drama['seasons'], 'drama_id');
            } else {
                $drama_ids = [$drama['drama']['id']];
            }
            // 获取剧集上次观看记录
            $history_info = Drama::rpc('api/get-saw-history', [
                'drama_ids' => $drama_ids,
                'user_id' => $user_id,
            ]);
            $drama['saw_episode'] = null;
            if ($history_info) {
                $drama_history = array_column($history_info, null, 'drama_id');
                $drama['saw_episode'] = $drama_history[$drama['drama']['id']] ?? [];
                $drama['seasons'] = array_map(function ($item) use ($drama_history) {
                    $item['saw_sound_id'] = $drama_history[$item['drama_id']]['sound_id'] ?? $item['saw_sound_id'];
                    return $item;
                }, $drama['seasons']);
            }
            if (Equipment::isAppOlderThanVipVersion() && $drama['drama']['pay_type'] === Drama::PAY_TYPE_DRAMA
                    && key_exists('vip_discount', $drama['drama']) && MUserVip::isVipUser($user_id)) {
                // WORKAROUND: 对于不支持会员的老版本，若为整剧付费且对当前会员用户打折，则直接返回打折后的价格
                // 对于单集付费的剧集，由于打折价格需要汇总购买单集的总价后再打折，所以老版本只能下发原价，此时显示上会为原价，但在实际发生支付的时候按打折价购买
                $drama['drama']['price'] = $drama['drama']['vip_discount']['price'];
            }
        } else {
            $drama['drama']['like'] = 0;
            $drama['saw_episode'] = null;
        }
        $drama_arr = [$drama['drama']];
        Drama::fillCornerMark($drama_arr, (int)$user_id);
        $drama['drama'] = $drama_arr[0];
        if (Drama::REWARD_DISABLE === $drama['rewardable']) {
            // 若剧集不可被打赏，返回的 reward_info 字段为 null 以便客户端不显示打赏相关界面
            $drama['reward_info'] = null;
        } else {
            $drama['reward_info'] = Drama::getRewardInfo($drama['drama']['id'], $user_id);
        }
        // 获取每季剧集下音频信息
        Drama::getSeasonDramas($drama['seasons'], $user_id, true);

        if ($user_id) {
            // 添加播放记录
            Drama::rpc('api/add-saw-history', [
                'drama_id' => $drama['drama']['id'],
                'user_id' => $user_id,
                'like' => $drama['drama']['like'],
                'episode_id' => $drama['sound_episode_id'],
            ]);
        }

        // 为整剧加入是否付费的参数
        Drama::checkNeedPay($drama['drama'], $user_id);
        // 检查剧集状态
        Drama::checkStatus($drama['drama'], $user_id);
        // 上次观看字段（单集 ID 及名称）位于返回数据的 info.drama 对象当中：
        // {"success": true, "code": 0, "info": {"drama": {"id": "9888", "saw_episode": "第一期",
        // "saw_episode_id": 67756}}}
        // (客户端以 /drama/episodes-detail 结构复用其模型)
        if ($user_id && $drama['saw_episode']) {
            $drama['drama']['saw_episode_id'] = $drama['saw_episode']['episode_id'];
            $drama['drama']['saw_episode'] = $drama['saw_episode']['episode_name'];
        } else {
            $drama['drama']['saw_episode_id'] = 0;
            $drama['drama']['saw_episode'] = '';
        }
        unset($drama['saw_episode']);
        // WORKAROUND: 临时处理。针对未购买整剧用户，移除合约下架音频。
        $buy_drama = $drama['drama']['need_pay'] === Drama::DRAMA_PAID;
        $checked_ids = MSound::find()->select('id')
            ->where(['id' => array_column($drama['episodes'], 'sound_id')])
            ->andWhere(MSound::getCheckedInSql(false, $buy_drama))->column();
        $drama['episodes'] = array_filter($drama['episodes'], function ($item) use ($checked_ids) {
            return in_array($item['sound_id'], $checked_ids);
        });
        MSound::checkNeedPay($drama['episodes'], $user_id);
        $drama['episodes'] = array_reduce($drama['episodes'], function ($ret, $item) {
            switch ($item['type']) {
                case Drama::EPISODE_TYPE_DRAMA:
                    $ret['episode'][] = $item;
                    break;
                case Drama::EPISODE_TYPE_MUSIC:
                    $ret['music'][] = $item;
                    break;
                case Drama::EPISODE_TYPE_INTERVIEW:
                    $ret['ft'][] = $item;
                    break;
            }
            return $ret;
        }, ['episode' => [], 'ft' => [], 'music' => []]);
        $derivatives = $drama['derivatives'] ?? [];
        $drama['derivatives'] = Drama::getDerivativeInfo($derivatives);
        if (!Equipment::isAppOlderThan('4.8.0', '5.6.8')) {
            // WORKAROUND: iOS >= 4.8.0 版本及 Android >= 5.6.8 版本时，置顶两个开播的声优
            $drama['cvs'] = Mowangsksoundseiy::topLiveCvs($drama['cvs']);
        }
        // 过滤不需要返回给客户端的字段
        $drama['cvs'] = array_map(function ($cv) {
            unset($cv['cvinfo']['user_id']);
            return $cv;
        }, $drama['cvs']);
        return $drama;
    }

    /**
     * @api {get} /drama/get-episodes 根据剧集 ID 获取单音详情
     * @apiDeprecated
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/get-episodes
     * @apiSampleRequest drama/get-episodes
     * @apiDescription  test.com/drama/rest/mobile/getepisodes 参数 dramaid => drama_id
     *
     * @apiVersion 0.1.0
     * @apiName get-episodes
     * @apiGroup drama
     *
     * @apiParam {Number} drama 剧集 ID
     * @apiParam {String} [token] 用户 token
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info": {
     *         "drama": {
     *           "id": 28,
     *           "name": "辉子",
     *           "cover": "https://static.missevan.com/mimage/201604/15/640fbc61fb1529e1cce932ba6dcce023065146.jpg",
     *           "abstract": "剧情概要巴拉巴拉",
     *           ...
     *         },
     *         episodes: {
     *           episode: [
     *             {
     *               "id": 66,
     *               "name": "第一期",
     *               "drama_id": 28,
     *               "sound_id": 91768,
     *               "date": 1248134400,
     *               "order": 1,
     *               "type": 0,
     *               "pay_type": 1,
     *               "video": 0,
     *             }
     *           ]
     *         },
     *         tags: [
     *           {
     *             "id": 24,
     *             "name": "竹马",
     *             "drama_num": 14,
     *             "visible_on_create": 1,
     *             "manga_num": 0
     *           }
     *         ]
     *         "reward_info": {
     *           "reward_num": 233,
     *           "one_week_reward_num": 23,
     *           "rank": 2,
     *           "reward_users": [
     *             {
     *               "id": 349525,
     *               "username": "233333a",
     *               "avatar": "https://static.missevan.com/avatars/201807/03/d29b5952b19090745.png",
     *               "message": "TA 傲娇地打赏了本剧…",
     *               "coin": 50,
     *               "ctime": 1533322223
     *             },
     *             {
     *               "id": 349524,
     *               "username": "233333a",
     *               "avatar": "https://static.missevan.com/avatars/201807/03/d229b5952b19090745.png",
     *               "message": "TA 傲娇地打赏了本剧…",
     *               "coin": 50,
     *               "ctime": 1533322222
     *             }
     *           ]
     *         }
     *       }
     *     }
     */
    public function actionGetEpisodes(int $drama_id)
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }

        $user_id = Yii::$app->user->id;
        if (!$drama_id) throw new HttpException(400, '剧集 ID 不能为空');

        $return = Drama::rpc('api/get-drama-detail',
            ['drama_id' => $drama_id, 'user_id' => $user_id]);
        // 为整剧加入是否付费的参数
        Drama::checkNeedPay($return['drama'], $user_id);
        // 排除掉剧集内不应看到的音频，未答题或未登录用户仅可看到过审音频，已答题用户及 UP 主可以看到过审音及报警音
        $checked_statuses = ($return['drama']['user_id'] === $user_id || Yii::$app->user->getIsExam())
            ? [MSound::CHECKED_PASS, MSound::CHECKED_POLICE] : MSound::CHECKED_PASS;
        $episodes = &$return['episodes']['episode'];
        if ($episodes) {
            $sound_ids = array_column($episodes, 'sound_id');
            $pass_sound_ids = MSound::find()
                ->select('id')
                ->where(['id' => $sound_ids, 'checked' => $checked_statuses])
                ->column();
            $episodes = array_values(array_filter($episodes, function ($episode) use ($pass_sound_ids) {
                return in_array($episode['sound_id'], $pass_sound_ids);
            }));
        }
        if (Drama::REWARD_DISABLE === $return['drama']['rewardable']) {
            // 若剧集不可被打赏，返回的 reward_info 字段为 null 以便客户端不显示打赏相关界面
            $return['reward_info'] = null;
        } else {
            $return['reward_info'] = Drama::getRewardInfo($return['drama']['id'], $user_id);
        }

        return $return;
    }

    /**
     * @api {post} /drama/buy-drama-episodes 购买单音
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/buy-drama-episodes
     * @apiSampleRequest drama/buy-drama-episodes
     *
     * @apiVersion 0.1.0
     * @apiName buy-drama-episodes
     * @apiGroup drama
     *
     * @apiPermission user
     *
     * @apiParam {String} sound_ids 音频 IDs 每个音频 ID 用半角逗号分隔，例：1,2,3
     * @apiParam {Number} drama_id 剧集 ID
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 信息
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "transaction_id": 212,
     *         "balance": 1002,
     *         "price": 20
     *       }
     *     }
     */
    public function actionBuyDramaEpisodes()
    {
        $sound_ids = [];
        if (Equipment::isAppOlderThan('6.0.4', '6.0.4')) {
            // WORKAROUND: iOS < 6.0.4 Android < 6.0.4 接收的是 sound_ids 数组
            $sound_ids = Yii::$app->request->post('sound_ids');
        } else {
            $sound_ids_str = trim(Yii::$app->request->post('sound_ids'));
            $sound_ids = explode(',', $sound_ids_str);
        }
        $drama_id = (int)Yii::$app->request->post('drama_id');

        if ($drama_id <= 0 || !MUtils2::isUintArr($sound_ids)) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        if (MUtils::isJapan()
                && Drama::checkDramaRefined($drama_id, Drama::REFINED_NO_JAPAN_SALE)) {
            throw new HttpException(403, '該当地域でのご購入はできません');
        }

        $user_id = Yii::$app->user->id;
        if (Mowangskuser::isTeenagerModeEnabled($user_id)) {
            throw new HttpException(403, Yii::t('app/error', 'No consumption in the teenager mode'));
        }

        $form = new TForm(['scenario' => TForm::SCENARIO_BUY]);
        $form->gift_id = $drama_id;
        $form->from_id = $user_id;
        $form->type = TransactionLog::TYPE_SOUND;
        $sound_ids = array_values(array_unique(array_map('intval', $sound_ids)));

        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_USER_BUY_DRAMA, $user_id, $drama_id);
        if (!$redis->lock($lock, HALF_MINUTE)) {
            throw new HttpException(400, '操作过快，请稍候重试');
        }
        try {
            if ($form->validate() && $msg = $form->buyDramaEpisodes($sound_ids, DramaBoughtDetailLog::ORIGIN_APP)) {
                // 购买成功后剧集自动加入追剧
                Drama::subscribe($drama_id, $user_id, 1);

                // 购买单集剧集时，当存在隐藏的单集剧集购买订单，需要把该剧集的所有单集剧集从隐藏列表中移除
                $exists = TransactionLog::hasHideDramaPurchaseOrder($user_id, $drama_id);
                if ($exists) {
                    TransactionLog::recoverDramaPurchaseOrder($user_id, [$drama_id]);
                }

                return $msg;
            }
            throw new HttpException(400, MUtils::getFirstError($form));
        } catch (Exception $e) {
            throw $e;
        } finally {
            $redis->unlock($lock);
        }
    }

    /**
     * @api {post} /drama/buy-drama 购买剧集
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/buy-drama
     * @apiSampleRequest drama/buy-drama
     *
     * @apiVersion 0.1.0
     * @apiName buy-drama
     * @apiGroup drama
     *
     * @apiParam {number} drama_id 剧集id
     * @apiParam {string} token 用户token
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 信息
     *
     */
    public function actionBuyDrama()
    {
        $drama_id = (int)Yii::$app->request->post('drama_id');
        if ($drama_id <= 0) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }

        if (MUtils::isJapan()
                && Drama::checkDramaRefined($drama_id, Drama::REFINED_NO_JAPAN_SALE)) {
            throw new HttpException(403, '該当地域でのご購入はできません');
        }

        $user_id = Yii::$app->user->id;
        if (Mowangskuser::isTeenagerModeEnabled($user_id)) {
            throw new HttpException(403, Yii::t('app/error', 'No consumption in the teenager mode'));
        }

        $form = new TForm(['scenario' => TForm::SCENARIO_BUY]);
        $form->gift_id = $drama_id;
        $form->from_id = $user_id;
        $form->type = TransactionLog::TYPE_DRAMA;

        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_USER_BUY_DRAMA, $user_id, $drama_id);
        if (!$redis->lock($lock, HALF_MINUTE)) {
            throw new HttpException(400, '操作过快，请稍候重试');
        }
        try {
            if ($form->validate() && $msg = $form->buyDrama(DramaBoughtDetailLog::ORIGIN_APP, false)) {
                // 购买成功后剧集自动加入追剧
                Drama::subscribe($drama_id, $user_id, 1);

                return $msg;
            }
            throw new HttpException(400, MUtils::getFirstError($form));
        } catch (Exception $e) {
            throw $e;
        } finally {
            $redis->unlock($lock);
        }
    }

    private function _request($data, $url)
    {
        ksort($data);
        $data1 = base64_encode(json_encode($data));
        $timestamp = time();
        $sign = hash_hmac('sha256', "$data1 $timestamp", MISSEVAN_PRIVATE_KEY);
        $data['time'] = $timestamp;
        $data['sign'] = $sign;


        $url = Yii::$app->params["dramaDomain"] . $url;

        $client = new Client();
        try {
            $res = $client->request('POST', $url, ['form_params' => $data]);

        } catch (ClientException $e) {
            $res = $e->getResponse();
        }
        $body = $res->getBody();
        $content = $body->getContents();

        return json_decode($content, true);
    }

    /**
     * @api {get} /drama/get-classic 获取推荐M站经典剧集
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/get-classic
     * @apiSampleRequest /drama/get-classic
     *
     * @apiVersion 0.1.0
     * @apiName get-classic
     * @apiGroup drama
     *
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Object} info 剧集信息
     */

    public function actionGetClassic()
    {
        $recommend_url = 'rest/mobile/getdramabyclassic';
        $recommend = $this->_request([], $recommend_url);

        if ($recommend['success']) {
            unset($recommend['success']);
            return $recommend['info'];
        } else {
            throw new HttpException(400, $recommend['info']);
        }
    }

    public function actionEpisodesDetail(int $drama_id)
    {
        // WORKAROUND: 6.1.2 及以上版本，使用 /drama/drama-detail 接口
        if (!Equipment::isAppOlderThan('6.1.2', '6.1.2')) {
            throw new HttpException(404, '咦，内容不见了 _(:зゝ∠)_ 请联系管理员');
        }
        return $this->actionDramaDetail($drama_id);
    }

    /**
     * @api {get} /drama/drama-detail 根据剧集 ID 获取剧集详情
     *
     * @apiVersion 0.1.0
     * @apiName drama-detail
     * @apiGroup drama
     *
     * @apiParam {Number} drama_id 剧集 ID
     * @apiParam {Number} persona_id 画像 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 剧集信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "drama": {
     *           "id": 28,
     *           "name": "辉子",
     *           "cover": "https://static.missevan.com/mimage/201604/15/640fbc61fb1529e1cce93ce023065146.jpg",
     *           "abstract": "剧情概要巴拉巴拉",
     *           "integrity": 1,
     *           "age": 2,
     *           "origin": 1,
     *           "author": "priest",  // 原创作者名称（原著）
     *           "birthday": 0,
     *           "cv": null,
     *           "ip": 0,
     *           "ipname": null,
     *           "type": 4,
     *           "newest": "第三期",
     *           "organization_id": 1,
     *           "user_id": 349524,
     *           "username": "testusername",
     *           "checked": 1,
     *           "create_time": 1503635635,
     *           "lastupdate_time": 1540455026,
     *           "view_count": 131055,
     *           "comment_count": 0,
     *           "catalog": 89,
     *           "alias": null,
     *           "pay_type": 2,  // 客户端需要使用 pay_type 判断用户是否需要付费且通过它来决定进哪种类型的页面（比如单集付费的页面的样子是不一样的）
     *           "push": 0,
     *           "refined": 0,
     *           "police": 0,
     *           "ip_id": 5,
     *           "subscription_num": 504,
     *           "style": 0,
     *           "type_name": "耽美",
     *           "price": 375,  // 整剧付费价格，单位：钻。免费剧返回 0
     *           "episode_price": 5,  // 单集付费价格，单位：钻。免费剧返回 0
     *           "episode_price_info": "单集付费 5 钻 / 期，随你畅听",  // 单集付费条文案（仅单集购买类型的剧集才可能会返回该字段，不返回该字段时不展示单集付费条）
     *           "vip": 1,  // 是否为会员剧集。0：否；1：是
     *           "vip_discount": {  // 剧集折扣信息，当付费剧有会员折扣时返回
     *             "rate": 0.8,  // 会员折扣值。折扣后若出现小数点，则用户仅需支付整数部分金额（小数点后金额直接舍去）
     *                           // 单集付费时，客户端先汇总要购买的单集价格然后使用这个值来计算购买后的价格
     *                           // 剧集的折扣限制由 site/config 下发
     *             "price": 287  // 整剧付费剧集的会员折扣价格，仅在整剧付费时下发。单位：钻
     *           },
     *           "saw_episode": "",
     *           "saw_episode_id": 0,
     *           "rewardable": 1,
     *           "update_period": "每周五更新",  // 更新周期
     *           "like": 1,
     *           "need_pay": 1,  // 客户端需要使用 need_pay 判断用户是否需要付费
     *           "organization": {  // 配音工作室信息
     *             "id": 1,  // 配音工作室 ID
     *             "name": "北斗企鹅工作室",  // 配音工作室名称
     *             "user_id": 77090,  // 配音工作室用户M号
     *             "avatar": "https://test.com/test/test.png",  // 配音工作室用户头像
     *             "cover": "https://test.com/test/test.png"  // 配音工作室用户主页背景图
     *           },
     *           "corner_mark": {  // 无剧集角标时不返回该字段
     *             "text": "已购",
     *             "text_color": "#ffffff",  // 文字颜色
     *             "text_start_color": "#ffffff",  // 文字渐变起始颜色，优先使用 text_start_color 和 text_end_color 来展示文字颜色，不存在的话使用 text_color
     *             "text_end_color": "#ffffff",  // 文字渐变结束颜色，优先使用 text_start_color 和 text_end_color 来展示文字颜色，不存在的话使用 text_color
     *             "bg_start_color": "#e66465",
     *             "bg_end_color": "#e66465",
     *             "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *           },
     *           "rank": {  // 剧集排行榜信息，未上榜时不返回该字段
     *             "name": "打赏总榜",
     *             "url": "https://test.com/aaa?persona_id=2&type=1&navhide=1",  // 跳转链接
     *             "sort": 9  // 排名
     *           },
     *           "labels": [  // 剧集标签，客户端按下发顺序展示，无剧集标签时不返回该字段
     *             {
     *               "style": 1,  // 标签样式（0: 默认；1: 互动剧）
     *               "name": "互动",  // 标签名称
     *               "url": "missevan://xxx"  // 跳转链接，无跳转链接时不返回该字段
     *             },
     *             {
     *               "style": 0,  // 标签样式（0: 默认；1: 互动剧）
     *               "name": "有声漫画",  // 标签名称
     *               "url": "missevan://xxx"  // 跳转链接，无跳转链接时不返回该字段
     *             },
     *             {
     *               "style": 0,  // 标签样式（0: 默认；1: 互动剧）
     *               "name": "纯爱"  // 标签名称
     *             },
     *             {
     *               "style": 0,  // 标签样式（0: 默认；1: 互动剧）
     *               "name": "改编"  // 标签名称
     *             }
     *           ]
     *         },
     *         "episodes": {
     *           "episode": [
     *             {
     *               "id": 66,
     *               "name": "第一期",
     *               "drama_id": 28,
     *               "sound_id": 91768,
     *               "date": 1248134400,
     *               "order": 1,
     *               "type": 0,
     *               "pay_type": 1,
     *               "video": 0,
     *               "view_count": 12345,  // 单集播放量
     *               "need_pay": 1,  // 付费状态。0：免费；1：付费单集未付费；2：付费单集已付费
     *               "vip": 1  // 会员单集状态。0：非会员单集；1：会员剧下的试听单集；2：会员剧下的非试听单集。优先展示会员角标
     *             }
     *           ]
     *         },
     *         "tags": [
     *           {
     *             "id": 24,
     *             "name": "竹马",
     *             "drama_num": 14,
     *             "visible_on_create": 1,
     *             "manga_num": 0
     *           }
     *         ]
     *         "reward_info": {
     *           "reward_num": 233,
     *           "one_week_reward_num": 23,
     *           "rank": 2,
     *           "reward_users": [
     *             {
     *               "id": 349525,
     *               "username": "233333a",
     *               "avatar": "https://static.missevan.com/avatars/201807/03/d29b5952b19090745.png",
     *               "message": "TA 傲娇地打赏了本剧…",
     *               "coin": 50,
     *               "ctime": 1533322223
     *             },
     *             {
     *               "id": 349524,
     *               "username": "233333a",
     *               "avatar": "https://static.missevan.com/avatars/201807/03/d229b5952b19090745.png",
     *               "message": "TA 傲娇地打赏了本剧…",
     *               "coin": 50,
     *               "ctime": 1533322222
     *             }
     *           ],
     *           "derivatives": [
     *             {
     *               "type": 2,  // 周边分类，1：商品；2；求签包；3；语音包；4：通用
     *               "title": "运势求签",
     *               "intro": "来看看今天的运势吧~",
     *               "tag": "求签",
     *               "url": "missevan://omikuji/draw",
     *               "cover": "https://static.missevan.com/derivate/201907/08/cover.jpg"
     *             }
     *           ]
     *         },
     *         "user": {  // UP 主信息，当 UP 主不存在时不返回此字段
     *           "id": 349524,
     *           "username": "吐槽君",
     *           "iconurl": "http://static-test.missevan.com/avatars/202106/18/1cecd029759fe0e85fced67ff65817fc173135.jpg",
     *           "follownum": 5,  // 关注数
     *           "fansnum": 50984,  // 粉丝数
     *           "attention": 0,  // 互关状态，当前用户不存在或是 UP 主本人时不返回此字段 0：双方互相都未关注；1：当前用户已关注被访问用户；2：被访问用户已关注当前用户；3：已互粉
     *           "authenticated": 0,
     *           "live": {  // 当前 UP 主直播信息，当无直播间或未开播时不返回该字段
     *             "status": 1,  // 直播状态，1：直播中
     *             "room_id": 1002  // 直播间 ID
     *           },
     *           "is_vip": 1  // 用户是否为会员。0：否；1：是
     *         }
     *       }
     *     }
     */
    public function actionDramaDetail(int $drama_id, ?int $persona_id = null)
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }
        $persona_module = $persona_id & Persona::PERSONA_MODULE_MASK;
        if (!Equipment::isAppOlderThan('6.1.3', '6.1.3')
                && !Persona::validatePersona($persona_module)) {
            // 6.1.3 及以上版本才会传 persona_id 参数
            // 若用户画像不在定义的范围内，则默认为女用户
            $persona_module = Persona::TYPE_GIRL;
            // 若出现 $persona_id 不为合法值问题，记录到日志
            Yii::warning("Wrong persona_id: {$persona_id}", __METHOD__);
        }

        if (!$drama_id) {
            throw new HttpException(400, '剧集 ID 不能为空');
        }

        $user_id = (int)Yii::$app->user->id;
        $return = Drama::getDramaDetail($drama_id, $user_id, $persona_module);

        return $return;
    }

    /**
     * @api {post} /drama/pay-episodes-detail 根据单音 IDs 获取单音详情
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/pay-episodes-detail
     * @apiSampleRequest drama/pay-episodes-detail
     * @apiDescription  test.com/drama/rest/mobile/getepisodesdetails 参数 sound_ids => sound_ids
     *
     * @apiVersion 0.1.0
     * @apiName pay-episodes-detail
     * @apiGroup drama
     *
     * @apiParam {Number[]} sound_ids 单音 IDs
     * @apiParam {Number} type 操作类型 1：30 钻；2：缓存所选；3：购买所选
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 单音信息
     *
     * @apiSuccessExample Success-Response:
     *     type = 1
     *     {
     *         "success": true,
     *         "code": 200,
     *         "info": {
     *             "sound_name": "第一期",
     *             "sound_price": 30
     *         }
     *     }
     *
     *     type = 2
     *     {
     *         "success": true,
     *         "code": 200,
     *         "info": {
     *             "free_num": 1,
     *             "pay_num": 2,
     *             "price": 60
     *         }
     *     }
     *
     *     type = 3
     *     {
     *         "success": true,
     *         "code": 200,
     *         "info": {
     *             "sound_nums": 3,
     *             "drama_name": "我的王妃是男人",
     *             "price": 90
     *         }
     *     }
     *
     */

    public function actionPayEpisodesDetail()
    {
        $sound_ids = Yii::$app->request->post('sound_ids');
        $type = (int)Yii::$app->request->post('type', 1);
        $user_id = (int)Yii::$app->user->id;

        if (!$sound_ids) {
            throw new HttpException(400, '单集 ID 不能为空');
        }
        $sound_id_type = false;
        if (is_array($sound_ids)) {
            foreach ($sound_ids as $sound_id) {
                if (!is_numeric($sound_id)) throw new HttpException(400, '参数有误');
                $sound_id_type = true;
            }
        }
        if (!$sound_id_type) {
            throw new HttpException(400, '参数有误');
        }
        $sound_info = Drama::rpc('api/get-episodes-details', ['sound_ids' => $sound_ids, 'user_id' => $user_id]);
        $details = [];
        // type 操作类型
        switch ($type) {
            case 1:
                // 30 钻
                $sound_list = $sound_info['sound_details'];
                $sound_names = array_column($sound_list, 'name');
                $details['sound_name'] = $sound_names[0];
                $details['sound_price'] = $sound_info['sound_price'];
                $details['drama_id'] = $sound_info['drama_id'];
                break;
            case 2:
                // 缓存所选
                $paid_num = 0;
                if ($user_id) {
                    $paid_num = (int)TransactionSoundLog::find()
                        ->where([
                            'sound_id' => $sound_ids,
                            'user_id' => $user_id,
                            'status' => TransactionLog::STATUS_SUCCESS,
                        ])->count();
                }
                $details['free_num'] = $sound_info['free_num'];
                $details['pay_num'] = $sound_info['pay_num'] - $paid_num;
                $details['price'] = $sound_info['sound_price'] * $details['pay_num'];
                $details['drama_id'] = $sound_info['drama_id'];
                break;
            case 3:
                // 购买所选
                $details['sound_nums'] = count($sound_ids);
                $details['drama_name'] = $sound_info['drama_name'];
                $details['price'] = $sound_info['sound_price'] * $details['sound_nums'];
                $details['drama_id'] = $sound_info['drama_id'];
                break;
            default:
                throw new HttpException(400, '参数有误');
        }
        return $details;
    }

    /**
     * @api {post} /drama/homepage 获取广播剧首页数据
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/homepage
     * @apiSampleRequest drama/homepage
     * @apiDescription  test.com/drama/rpc/api/homepage
     *
     * @apiVersion 0.1.0
     * @apiName homepage
     * @apiGroup drama
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 单音信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info": [
     *         {
     *           "title": "付费精品",
     *           "datas": [
     *             {
     *               "id": 714,
     *               "name": "无人赴约",
     *               "alias": null,
     *               "newest": "第四期",
     *               "cover": "https://static.missevan.com/dramacoversmini/201606/04/c3c82e206a850ecc543ac27f829061dc052051.jpg",
     *               "pay_type": 2,
     *               "catalog": 89,
     *               "type": 6,
     *               "integrity": 2,
     *               "type_name": "言情",
     *               "price": 0,
     *               "catalog_name": "中文广播剧"
     *             },
     *           ]
     *         },
     *         {
     *           "title": "经典作品",
     *           "datas": [...]
     *         },
     *         {
     *           "title": "新作速递",
     *           "datas": [...]
     *         }
     *       ]
     *     }
     *
     */
    public function actionHomepage()
    {
        $homepage_info = Drama::rpc('api/drama-homepage');
        return $homepage_info;
    }

    /**
     * @api {get} /drama/drama-reward-rank 打赏剧集榜
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/drama-reward-rank
     * @apiSampleRequest drama/drama-reward-rank
     * @apiDescription 打赏剧集榜
     *
     * @apiVersion 0.1.0
     * @apiName drama-reward-rank
     * @apiGroup drama
     *
     * @apiParam {number=1,2,3} period 周期（1 为周榜、2 为月榜、3 为总榜）
     * @apiParam {Number} [page=1] 第几页
     * @apiParam {Number} [page_size=50] 每页个数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "ranks": {
     *           "Datas": [{
     *             "id": 9888,
     *             "name": "《杀破狼》广播剧",
     *             "cover": "https://static.missevan.com/dramacoversmini/201708/25/389768817c57123406.jpg",
     *             "abstract": "729声工场出品，晋江文学城 作者：priest 原著",
     *             "pay_type": 2,
     *             "ctime": 1532060640
     *           }],
     *           "pagination": {
     *             "p": 1,
     *             "maxpage": 19,
     *             "count": 37,
     *             "pagesize": 2
     *           }
     *         },
     *         "rule": "https://link.missevan.com/help/drama-ranks",
     *         "tip": "榜单将于 9 月 5 日零时更新，敬请期待"
     *       }
     *     }
     *
     */
    public function actionDramaRewardRank(int $period = 0, int $page = 1, int $page_size = Drama::RANK_ITEM_LENTH)
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }
        if (!Drama::checkRankPeriod($period)) {
            throw new HttpException(400, '参数错误');
        }
        $page_obj = PaginationParams::process($page, $page_size);

        $return = Drama::getDramaRewardRankList($period, $page_obj->page, $page_obj->page_size, Yii::$app->user->id);
        return [
            'ranks' => $return,
            'rule' => Yii::$app->params['help_links']['drama_reward_ranks'],
            'tip' => Drama::getDramaRankTip($period),
        ];
    }

    /**
     * @api {get} /drama/user-reward-rank 打赏用户榜单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/user-reward-rank
     * @apiSampleRequest user-reward-rank
     * @apiDescription 打赏用户榜单
     *
     * @apiVersion 0.1.0
     * @apiName user-reward-rank
     * @apiGroup drama
     *
     * @apiParam {Number} drama_id 剧集 ID
     * @apiParam {number=1,2,3} period 周期（1 为七日榜、2 为月榜、3 为总榜）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         "data": [
     *           {
     *             "id": 346286,
     *             "username": "InVinCiblezz",
     *             "avatar": "https://static.missevan.com/avatars/201802/24/4c24e0302c2b54c761f079fbb5696b49154707.png",
     *             "message": "TA 傲娇地打赏了本剧…",
     *             "coin": 2000,
     *             "ctime": "2018-07-07"
     *           }
     *         ],
     *         "user_status": {
     *           "position": 2, // 打赏用户排名（0 未上榜，非零整数为所处排名）
     *           "coin_remain": 10 // 距离上一名所需的点数或还差多少点上榜
     *         }
     *       ]
     *     }
     *
     */
    public function actionUserRewardRank(int $drama_id, int $period = 0)
    {
        if (!Drama::checkRankPeriod($period)) {
            throw new HttpException(400, '参数错误');
        }
        $user_id = Yii::$app->user->id;
        $result = Drama::getUserRewardRankList($drama_id, $period, $user_id);
        return [
            'data' => $result['ranks'],
            'user_status' => $result['user_status'],
        ];
    }

    /**
     * @api {get} /drama/reward-menu 打赏价目
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/reward-menu
     * @apiSampleRequest drama/reward-menu
     * @apiDescription 打赏价目
     *
     * @apiVersion 0.1.0
     * @apiName reward-menu
     * @apiGroup drama
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "price": [
     *           {
     *             "id": 12,
     *             "name": "剧集打赏 10 钻",
     *             "price": 10
     *           },
     *           {
     *             "id": 13,
     *             "name": "剧集打赏 15 钻",
     *             "price": 15
     *           },
     *           {
     *             "id": 14,
     *             "name": "剧集打赏 50 钻",
     *             "price": 50
     *           },
     *           {
     *             "id": 15,
     *             "name": "剧集打赏 100 钻",
     *             "price": 50
     *           },
     *           {
     *             "id": 16,
     *             "name": "剧集打赏自定义钻",
     *             "price": 0
     *           }
     *         ],
     *         "min": 10,
     *         "max": 999999,
     *         "rule": {
     *           "title": "用户须知",
     *           "url": "https://link.missevan.com/rule/drama-reward-agreement"
     *         }
     *       }
     *     }
     *
     */
    public function actionRewardMenu()
    {
        $reward_menu = [
            'price' => DramaRewardMenu::getRewardPrice(),
            'min' => DramaRewardMenu::getRewardMinCoinNum(),
            'max' => DramaRewardMenu::getRewardMaxCoinNum(),
            'rule' => [
                'title' => sprintf('<u>%s</u>', I18nMessage::success('Drama reward agreement')),
                'url' => Yii::$app->params['help_links']['drama_reward_agreement'],
                'color' => '#848484',
            ],
        ];
        if (Yii::$app->equip->getOs() === Equipment::Web) {
            if (!($user_id = Yii::$app->user->id)) {
                throw new HttpExceptionI18n(403, 'Login Required', 100010006);
            }
            $reward_menu['balance'] = Balance::getByPk($user_id)->getTotalBalance();
        }
        return $reward_menu;
    }

    /**
     * @api {post} /drama/reward 打赏
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/reward
     * @apiSampleRequest drama/reward
     * @apiDescription 打赏
     *
     * @apiVersion 0.1.0
     * @apiName reward
     * @apiGroup drama
     *
     * @apiParam {Number} drama_id 剧集 ID
     * @apiParam {Number} reward_id 打赏钻石对应 ID
     * @apiParam {Number} price 打赏的钻石数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "transaction_id": 46086,
     *         "message": "打赏成功"
     *       }
     *     }
     *
     */
    public function actionReward()
    {
        $drama_id = (int)Yii::$app->request->post('drama_id');
        $reward_id = (int)Yii::$app->request->post('reward_id');
        $price = (int)Yii::$app->request->post('price');

        $reward_min_coin = DramaRewardMenu::getRewardMinCoinNum();
        if ($reward_min_coin > $price) {
            throw new HttpExceptionI18n(400, ['No less than {num} coin', ['num' => $reward_min_coin]]);
        }
        $reward_max_coin = DramaRewardMenu::getRewardMaxCoinNum();
        if ($reward_max_coin < $price) {
            throw new HttpExceptionI18n(400, ["Can't exceed {num} coin", ['num' => $reward_max_coin]]);
        }
        if (Drama::isForbiddenReward($drama_id)) {
            throw new HttpException(403, '本剧暂不支持打赏');
        }
        if (!$reward = DramaRewardMenu::findOne(['id' => $reward_id])) {
            throw new HttpExceptionI18n(404, 'This reward option does not exist');
        }

        if (Drama::checkDramaRefined($drama_id, Drama::REFINED_TYPE_SPECIAL)) {
            throw new HttpExceptionI18n(403, 'Limited drama does not support this operation');
        }

        $user_id = Yii::$app->user->id;
        if (Mowangskuser::isTeenagerModeEnabled($user_id)) {
            throw new HttpExceptionI18n(403, 'No consumption in the teenager mode');
        }

        $price = $reward->price ?: $price;
        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_REWARD]);
        $form->gift_id = $drama_id;
        $form->from_id = $user_id;
        $form->price = $price;
        $form->type = TransactionLog::TYPE_REWARD;
        if ($form->validate() && $res = $form->reward(false)) {
            // 打赏成功后更新剧集打赏总榜数据
            MDramaRewardRanks::addRankAll($drama_id, $price);
            Drama::updateRewardRankCache($drama_id, $user_id, $price);

            return [
                'transaction_id' => $res['transaction_id'],
                'message' => I18nMessage::success('Reward successfully'),
            ];
        } else {
            throw new HttpException(400, MUtils::getFirstError($form));
        }
    }

    /**
     * @api {get} /drama/reward-info{?drama_id} 剧集打赏信息
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/reward-info
     * @apiSampleRequest drama/reward-info
     *
     * @apiVersion 0.1.0
     * @apiName reward-info
     * @apiGroup drama
     *
     * @apiParam (Query 参数) {Number} drama_id 剧集 ID
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info 留言成功提示信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info": {
     *         "reward_num": 233,
     *         "one_week_reward_num": 23,
     *         "rank": 2,
     *         "reward_users": [
     *            {
     *              "id": 349525,
     *              "username": "233333a",
     *              "avatar": "https://static.missevan.com//avatars/201807/03/d29b5952b19090745.png",
     *              "message": "TA 傲娇地打赏了本剧…",
     *              "coin": 50,
     *              "ctime": 1533322223
     *           },
     *           {
     *              "id": 349524,
     *              "username": "233333a",
     *              "avatar": "https://static.missevan.com//avatars/201807/03/d229b5952b19090745.png",
     *              "message": "TA 傲娇地打赏了本剧…",
     *              "coin": 50,
     *              "ctime": 1533322222
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionRewardInfo(int $drama_id)
    {
        $user_id = (int)Yii::$app->user->id;
        if (!$drama = Drama::rpc('api/get-drama-price', ['drama_id' => $drama_id, 'user_id' => $user_id])) {
            throw new HttpExceptionI18n(404, 'The drama does not exist');
        }
        if (Drama::REWARD_DISABLE === $drama['rewardable']) {
            // 若剧集不可被打赏，HTTP 状态码返回 404 以便客户端不显示相关界面
            throw new HttpExceptionI18n(404, 'This drama does not support reward');
        }
        return Drama::getRewardInfo($drama_id, $user_id);
    }

    /**
     * @api {post} /drama/reward-message 打赏留言
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/reward-message
     * @apiSampleRequest drama/reward-message
     *
     * @apiVersion 0.1.0
     * @apiName reward-message
     * @apiGroup drama
     *
     * @apiParam {Number} drama_id 剧集 ID
     * @apiParam {Number} transaction_id 交易记录 ID
     * @apiParam {Number} message 留言内容
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info 留言成功提示信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info": "留言成功"
     *     }
     */
    public function actionRewardMessage()
    {
        $drama_id = (int)Yii::$app->request->post('drama_id');
        $transaction_id = (int)Yii::$app->request->post('transaction_id');
        $message = trim(Yii::$app->request->post('message'));
        if (!$message) {
            throw new HttpExceptionI18n(400, 'Reward message should not be empty');
        }
        $user_id = Yii::$app->user->id;
        // 添加留言
        RewardMessage::AddMessage($drama_id, $transaction_id, $user_id, $message);
        return I18nMessage::success('Reward message sent successfully');
    }

    /**
     * @api {get} /drama/drama-reward-detail 剧集打赏详情（给 web 用的接口）
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/drama-reward-detail
     * @apiSampleRequest drama/drama-reward-detail
     *
     * @apiVersion 0.1.0
     * @apiName drama-reward-detail
     * @apiGroup drama
     *
     * @apiParam {Number} drama_id 剧集 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "info": {
     *         "data": {
     *           "week": [{
     *             "id": 346286,
     *             "username": "InVinCiblezz",
     *             "avatar": "https://static.missevan.com/avatars/201807/02/abc5461daa51ca4e5648162218.jpg",
     *             "message": "ssssssssssssssssssssssssssssssss",
     *             "coin": 100,
     *             "ctime": 1530522777
     *           }],
     *           "month": [{
     *             "id": 346286,
     *             "username": "InVinCiblezz",
     *             "avatar": "https://static.missevan.com/avatars/201807/02/abc5461daa79a5648162218.jpg",
     *             "message": "ssssssssssssssssssssssssssssssss",
     *             "coin": 100,
     *             "ctime": 1530522777
     *           }],
     *           "all": [{
     *             "id": 346287,
     *             "username": "苍天啊苍天",
     *             "avatar": "https://static.missevan.com/avatars/201803/26/c8ba0f80f9d64c89d170732.png",
     *             "message": "TA 傲娇地打赏了本剧…",
     *             "coin": 15,
     *             "ctime": 1530080516
     *           }]
     *         },
     *         "rank": 2,
     *         "reward_num": 3,
     *         "one_week_reward_num": 25  // 近七日打赏人数
     *       }
     *     }
     */
    public function actionDramaRewardDetail(int $drama_id)
    {
        $memcache = Yii::$app->memcache;
        $key = MUtils::generateCacheKey(KEY_REWARD_DRAMA_DATA, $drama_id);
        if (!$content = json_decode($memcache->get($key), true)) {
            $content = Drama::rpc('api/get-drama-price', ['drama_id' => $drama_id]);
            $memcache->set($key, json_encode($content), FIVE_MINUTE);
        }
        if (!$content || Drama::REWARD_DISABLE === $content['rewardable']) {
            throw new HttpExceptionI18n(403, 'This drama does not support reward');
        }
        $user_id = Yii::$app->user->id;
        $week_ranklist = Drama::getUserRewardRankList($drama_id, Drama::RANK_PERIOD_WEEK, $user_id,
            Drama::REWARD_TOP_USER_COUNT_WEB)['ranks'];
        $month_ranklist = Drama::getUserRewardRankList($drama_id, Drama::RANK_PERIOD_MONTH, $user_id,
            Drama::REWARD_TOP_USER_COUNT_WEB)['ranks'];
        $all_ranklist = Drama::getUserRewardRankList($drama_id, Drama::RANK_PERIOD_ALL, $user_id,
            Drama::REWARD_TOP_USER_COUNT_WEB)['ranks'];
        [$reward_num, $one_week_reward_num] = Drama::getRewardDramaUserCount($drama_id, $user_id);
        return [
            'data' => [
                'week' => $week_ranklist,
                'month' => $month_ranklist,
                'all' => $all_ranklist,
            ],
            'rank' => Drama::getDramaRewardRank($drama_id, Drama::RANK_PERIOD_WEEK),
            'reward_num' => $reward_num,
            'one_week_reward_num' => $one_week_reward_num,
        ];
    }

    /**
     * @api {get} /drama/timeline 剧集时间轴
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com:8081/drama/timeline
     * @apiSampleRequest drama/timeline
     *
     * @apiVersion 0.1.0
     * @apiName timeline
     * @apiGroup drama
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 过滤出来的剧集时间轴信息
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "date_week": "五",
     *           "date_day": 3,
     *           "is_today": 0,
     *           "dramas": [
     *             {
     *               "id": 16635,
     *               "name": "破云 第一季",
     *               "cover": "http://static.missevan.com/dramacoversmini/201806/30/9217a380137d39c8791755140808.jpg",
     *               "integrity": 1,
     *               "newest": "第八期",
     *               "type_name": "纯爱",
     *               "pay_type": 2,
     *               "cover_color": 12434877,
     *               "corner_mark": {  // 无剧集角标时不返回该字段
     *                 "text": "已购",
     *                 "text_color": "#ffffff",
     *                 "bg_start_color": "#e66465",
     *                 "bg_end_color": "#e66465",
     *                 "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *               }
     *             },
     *             {
     *               "id": 2879,
     *               "name": "疯子",
     *               "cover": "http://static-test.missevan.com/dramacoversmini/201610/18/f7f8323fd632d960040637.jpg",
     *               "integrity": 1,
     *               "newest": "预告",
     *               "type_name": "纯爱",
     *               "pay_type": 0,
     *               "cover_color": 12434877
     *             }
     *           ]
     *         },
     *         {
     *           "date_week": "六",
     *           "date_day": 4,
     *           "is_today": 0,
     *           "dramas": [
     *             {
     *               "id": 16635,
     *               "name": "红尾待归",
     *               "cover": "http://static.missevan.com/dramacoversmini/201806/30/9217a380137d39c8791755140808.jpg",
     *               "integrity": 1,
     *               "newest": "全一期",
     *               "type_name": "双女主",
     *               "pay_type": 0,
     *               "cover_color": 12434877
     *             }
     *           ]
     *         },
     *         {
     *           "date_week": "日",
     *           "date_day": 5,
     *           "is_today": 1,
     *           "dramas": []
     *         }
     *       ]
     *     }
     */
    public function actionTimeline()
    {
        $dramas_info = Drama::rpc('api/drama-new-timeline');
        $drama_ids = [];
        foreach ($dramas_info as $drama_info) {
            foreach ($drama_info['dramas'] as $drama) {
                $drama_ids[] = $drama['id'];
            }
        }

        if (empty($drama_ids)) {
            return $dramas_info;
        }

        // 获取剧集角标信息
        $corner_mark_map = Drama::getDramaCornerMark($drama_ids, (int)Yii::$app->user->id);
        if (!$corner_mark_map) {
            return $dramas_info;
        }

        foreach ($dramas_info as &$drama_info) {
            foreach ($drama_info['dramas'] as &$drama) {
                if (isset($corner_mark_map[$drama['id']])) {
                    $drama['corner_mark'] = $corner_mark_map[$drama['id']];
                }
            }
        }
        unset($drama);
        return $dramas_info;
    }

    /**
     * @api {get} /drama/tag 剧集标签
     * @apiDescription 返回的空数组表示不需要显示，传给 filter 接口的对应位置的参数值为 0
     *
     * @apiVersion 0.1.0
     * @apiName tag
     * @apiGroup drama
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 过滤出来的剧集标签信息
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "index": [
     *           [
     *             {
     *               "id": 1,
     *               "name": "最多追剧"
     *             },
     *             {
     *               "id": 2,
     *               "name": "最近更新"
     *             }
     *           ],
     *           [
     *             {
     *               "id": 0,
     *               "name": "全部"
     *             },
     *             {
     *               "id": 5,
     *               "name": "全一期"
     *             },
     *             {
     *               "id": 2,
     *               "name": "完结"
     *             },
     *             {
     *               "id": 1,
     *               "name": "未完结"
     *             }
     *           ],
     *           [
     *             {
     *               "id": 0,
     *               "name": "全部"
     *             },
     *             {
     *               "id": 4,
     *               "name": "纯爱"
     *             },
     *             {
     *               "id": 6,
     *               "name": "言情"
     *             },
     *             {
     *               "id": 3,
     *               "name": "全年龄"
     *             },
     *             {
     *               "id": 5,
     *               "name": "双女主"
     *             }
     *           ],
     *           [
     *             {
     *               "id": 0,
     *               "name": "全部"
     *             },
     *             {
     *               "id": 1,
     *               "name": "现代"
     *             },
     *             {
     *               "id": 2,
     *               "name": "古风"
     *             },
     *             {
     *               "id": 3,
     *               "name": "民国"
     *             },
     *             {
     *               "id": 4,
     *               "name": "其他"
     *             }
     *           ],
     *           [], // 返回的空数组表示不需要显示，传给 filter 接口的对应位置的参数值为 0
     *           [
     *             {
     *               "id": 0,
     *               "name": "全部"
     *             },
     *             {
     *               "id": 1,
     *               "name": "免费"
     *             },
     *             {
     *               "id": 2,
     *               "name": "付费"
     *             }
     *           ]
     *         ]
     *       }
     *     }
     */
    public function actionTag()
    {
        $tags = Drama::getDramaTag();
        return $tags;
    }

    /**
     * @api {get} /drama/filter 剧集筛选
     * @apiVersion 0.1.0
     * @apiName filter
     * @apiGroup drama
     *
     * @apiParam {String} [filters=1_0_0_0_0_0_0] 过滤器 \
     * 第一位是显示顺序 1：最多追剧；2：最近更新；3：最新上架 \
     * 第二位是完结度 0：全部；3：全一期；2：完结；1：未完结 \
     * 第三位是性向 0：全部；4：纯爱；6：言情；3：全年龄；5：双女主 \
     * 第四位是年代 0：全部；1：现代；2：古风；3：民国；4：其他 \
     * 第五位是标签 ID 有多个标签 ID 则用 `,` 分隔 \
     * 第六位是付费类型 0：全部；1：免费；2：付费；3：会员免费 \
     * 第七位是版权类型 0：全部；1：独播；2：其他
     * @apiParam {Number} [page=1] 当前页
     * @apiParam {Number} [page_size=30] 每页个数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 筛选出来的剧集信息
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [
     *           {
     *             "id": 18521,
     *             "name": "慕",
     *             "cover": "https://static.missevan.com/dramacoversmini/201808/17/913581540efb4d4383bb6a378a43e7b1141303.jpg",
     *             "integrity": 3,
     *             "newest": "全一期",
     *             "type_name": "百合",
     *             "corner_mark": {  // 无剧集角标时不返回该字段
     *               "text": "已购",
     *               "text_color": "#ffffff",
     *               "bg_start_color": "#e66465",
     *               "bg_end_color": "#e66465",
     *               "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *             }
     *           },
     *           {
     *             "id": 18519,
     *             "name": "一朝梦",
     *             "cover": "https://static.missevan.com/dramacoversmini/201808/17/d567662198c7963451f3f8c9e216b9ac135146.jpg",
     *             "integrity": 3,
     *             "newest": "全一期",
     *             "type_name": "全年龄"
     *           }
     *         ],
     *         "pagination": {
     *           "p": 1,
     *           "count": 3,
     *           "maxpage": 1
     *           "pagesize": 6,
     *           "has_more": true
     *         }
     *       }
     *     }
     */
    public function actionFilter()
    {
        $default_filters = '1_0_0_0_0_0_0';
        $filters = trim(Yii::$app->request->get('filters', $default_filters));
        $page = (int)Yii::$app->request->get('page', 1);
        $page_size = (int)Yii::$app->request->get('page_size', DEFAULT_PAGE_SIZE);
        $page_obj = PaginationParams::process($page, $page_size);
        // App 端请求来源类型
        $FILTER_TYPE_APP = 2;
        $dramas = Drama::rpc('api/drama-filter', [
            'filters' => $filters,
            'page' => $page_obj->page,
            'page_size' => $page_obj->page_size,
            'sensitive' => (int)Yii::$app->user->isExam,
            'type' => $FILTER_TYPE_APP
        ]);
        // 获取剧集角标信息
        Drama::fillCornerMark($dramas['Datas'], (int)Yii::$app->user->id);
        // 下拉加载
        $dramas['pagination']['has_more'] = $dramas['pagination']['count'] >
            ($page_obj->page * $page_obj->page_size);
        return $dramas;
    }

    /**
     * @api {get} /drama/cv-info CV 详情页
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com:8081/drama/cv-info
     * @apiSampleRequest drama/cv-info
     *
     * @apiVersion 0.1.0
     * @apiName cv-info
     * @apiGroup drama
     *
     * @apiParam {Number} cv_id 声优 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 筛选出来的 CV 剧集信息
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "cv": {
     *           "id": 1013,
     *           "name": "阿杰",
     *           "icon": "201805/31/33d0f6e6de93547dd90ac5c62327e139153016.png",
     *           "profile": "<p>张杰，1978年11月27日出生于北京，中国内地配音男演员。</p>",
     *           "career": 1,
     *           "birthmonth": 11,
     *           "birthyear": 1978,
     *           "seiyalias": "阿杰",
     *           "group": "729声工场",
     *           "bloodtype": 4,
     *           "birthday": 27,
     *           "gender": 1,
     *           "mid": 441514,
     *           "checked": 1
     *         },
     *         "dramas": [
     *           {
     *             "drama": {
     *               "id": 2493,
     *               "name": "魂",
     *               "cover": "https://static.missevan.com/201608/24/6792e68f3082723e3560a1029bd3a46d104102.jpg",
     *               "abstract": null,
     *               "integrity": 3,
     *               "newest": "第一期",
     *               "type_name": "全年龄",
     *               "pay_type": 0,
     *               "catalog_name": "中文广播剧"
     *             },
     *             "episodes": [
     *               {
     *                 "characters": [
     *                   {
     *                     "id": 3894,
     *                     "episode_id": 208,
     *                     "character": "火翼",
     *                     "main": 1
     *                   }
     *                 ],
     *                 "episode": {
     *                   "id": 208,
     *                   "name": "第一期",
     *                   "date": "2016/02/04",
     *                   "drama_id": 2493
     *                 }
     *               }
     *             ]
     *           },
     *           {
     *             "drama": {
     *               "id": 4103,
     *               "name": "寻找克洛托",
     *               "cover": "https://static.missevan.com/201709/07/d140923ed8cde41d956555024e45bd03191244.png",
     *               "abstract": <p>出品方：布卡漫画&amp;Missevan弹幕音频网</p>,
     *               "integrity": 1,
     *               "newest": "第 6 话",
     *               "type_name": "全年龄",
     *               "pay_type": 0,
     *               "catalog_name": "中文有声漫画"
     *             },
     *             "episodes": [
     *               {
     *                 "characters": [
     *                   {
     *                     "id": 82858,
     *                     "episode_id": 21778,
     *                     "character": "叶蜥",
     *                     "main": 1
     *                   }
     *                 ],
     *                 "episode": {
     *                   "id": 21778,
     *                   "name": "第 1 话",
     *                   "date": "2016/11/28",
     *                   "drama_id": 4103
     *                 }
     *               }
     *             ]
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionCvInfo()
    {
        $cv_id = (int)Yii::$app->request->get('cv_id');

        if (!$cv_id) {
            throw new HttpException(400, '参数错误');
        }

        if (!Mowangsksoundseiy::find()->where(['id' => $cv_id])->exists()) {
            throw new HttpException(404, '该声优不存在');
        }

        $cv_info = Drama::rpc('api/cv-info', ['cv_id' => $cv_id]);
        if (Equipment::isAppOlderThan('4.4.7', '5.3.7') && MUtils::hasHttpScheme($cv_info['cv']['icon'])) {
            // WORKAROUND: iOS < 4.4.7 的版本 Android < 5.3.7 的版本返回原路径信息
            $cv_info['cv']['icon'] = preg_replace('/http.*?com\/seiys\//', '', $cv_info['cv']['icon']);
        }

        return $cv_info;
    }

    /**
     * @api {get} /drama/get-recommend-by-id 剧集详情页获取推荐剧集
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/get-recommend-by-id
     * @apiSampleRequest drama/get-recommend-by-id
     * @apiDescription 剧集详情页获取推荐剧集，其中每个推荐剧集有各自的推荐策略 ID
     * 策略定义文档：https://github.com/MiaoSiLa/missevan-doc/blob/master/app/rules/推荐策略.md
     *
     * @apiVersion 0.1.0
     * @apiName get-recommend-by-id
     * @apiGroup drama
     *
     * @apiParam {Number} drama_id 剧集 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "id": 26,
     *           "name": "我的王妃是男人",
     *           "cover": "201807/11/ba854fe2e8496fa91eb66d180d36cc8d114247.png",
     *           "front_cover": "http://static.missevan.com/dramacoversmini/201807/11/baa91eb66d180d36cc114247.png",
     *           "checked": 1,
     *           "police": 1,
     *           "pay_type": 2,
     *           "strategy_id": 1,
     *           "corner_mark": {  // 无剧集角标时不返回该字段
     *             "text": "已购",
     *             "text_color": "#ffffff",
     *             "bg_start_color": "#e66465",
     *             "bg_end_color": "#e66465",
     *             "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *           }
     *         },
     *         {
     *           "id": 27,
     *           "name": "冲撞",
     *           "cover": "201807/02/1cb96a5be3657e3647db40b8eb1e4d6b115143.png",
     *           "front_cover": "http://static.missevan.com/dramacoversmini/201807/02657e3647db40b1e4d6b115143.png",
     *           "checked": 1,
     *           "police": 0,
     *           "pay_type": 2,
     *           "strategy_id": 3
     *         },
     *         {
     *           "id": 1737,
     *           "name": "腹黑哈士奇忽悠黄鸡",
     *           "cover": "201807/02/1cb96a5be3657e3647db40b8eb1e4d6b115143.png",
     *           "front_cover": "http://static.missevan.com/dramacoversmini/201807/02657e3647db40b1e4d6b115143.png",
     *           "checked": 1,
     *           "police": 0,
     *           "pay_type": 0,
     *           "strategy_id": 5
     *         },
     *       ]
     *     }
     */
    public function actionGetRecommendById(int $drama_id)
    {
        if (!$drama_id) {
            throw new HttpException(400, '参数错误');
        }
        $user_id = (int)Yii::$app->user->id;
        // 是否获取最近更新的剧集
        // 老用户获取偏向最近更新的剧集，新用户（最近 3 个月内注册）或未登录用户获取全部推荐剧集
        $scene = Drama::RECOMMEND_SCENE_APP_DRAMA_VIEW_ALL;
        if ($user_id && Yii::$app->user->getRegisterAt() < ($_SERVER['REQUEST_TIME'] - (3 * THIRTY_DAYS))) {
            $scene = Drama::RECOMMEND_SCENE_APP_DRAMA_VIEW_NEW;
        }
        return Drama::getRecommendById($drama_id, $user_id, $scene);
    }

    /**
     * @api {get} /drama/get-outline 获取剧集设定
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/get-outline
     * @apiSampleRequest drama/get-outline
     * @apiDescription 仅海外版使用
     *
     * @apiVersion 0.1.0
     * @apiName get-outline
     * @apiGroup drama
     *
     * @apiParam {Number} drama_id 剧集 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "cover": "https://static.missevan.com/nocover.png",
     *         "url": "https://m.missevan.com/1"
     *       }
     *     }
     */
    public function actionGetOutline(int $drama_id)
    {
        if (!$drama_id) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        $drama_data = current(Drama::getRecommendedDramas([$drama_id], null));
        return $drama_data['outline'] ?? null;
    }

    /**
     * @api {get} /drama/get-feed-num 获取追剧未观看的数量
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/get-feed-num
     * @apiSampleRequest drama/get-feed-num
     * @apiDescription 仅海外版使用
     *
     * @apiVersion 0.1.0
     * @apiName get-feed-num
     * @apiGroup drama
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response: 旧版返回（1.0.1 及其之前版本）
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "drama": 3  //  未读剧集更新数
     *       }
     *     }
     * @apiSuccessExample Success-Response: 新版（1.0.2 及其之后版本）
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "subscribed_num": 10,  // 订阅的剧集数
     *         "feed_num": 3  // 未读剧集更新数
     *       }
     *     }
     */
    public function actionGetFeedNum()
    {
        $ret = MPersonDramaPage::getDramaFeedNum(Yii::$app->user->id);
        // WORKAROUND: 旧版使用 drama 表示未读的剧集更新数，新版用 feed_num 表示并添加订阅数字段
        if (Equipment::isMiMiAppOlderThan('1.0.2', '1.0.2')) {
            return [
                'drama' => $ret['feed_num'],
            ];
        }

        return $ret;
    }

    /**
     * @api {get} /drama/recommend-modules{?catalog_id} 剧集分区自定义模块
     *
     * @apiVersion 0.1.0
     * @apiName recommend-modules
     * @apiGroup drama
     *
     * @apiParam {Number} [catalog_id=86] 分区 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "id": 8,
     *           "sort": 1,
     *           "title": "音单模块标题",
     *           "type": 1,  // 模块类型，1：音单；2：剧集；3：音频
     *           "style": 0,  // 排版方式，0：竖版；1：横版；2：排行榜；3：滑动
     *           "more": {  // 不下发或为空时跳转到默认的原生自定义模块详情页
     *             "url": "https://test.com/aaa?foo=bar"  // 更多按钮跳转链接，不下发或为空时跳转到默认的原生自定义模块详情页
     *           },
     *           "elements": [
     *             {
     *               "id": 142732,
     *               "title": "标题",
     *               "intro": "简介",
     *               "front_cover": "http://static.missevan.com/mimages/201711/15/92eddc64e7121500.jpg",
     *               "type": 1,
     *               "music_count": 16,
     *               "view_count": 135718,
     *               "sort": 1,
     *               "corner_mark": {  // 无剧集角标时不返回该字段
     *                 "text": "已购",
     *                 "text_color": "#ffffff",
     *                 "bg_start_color": "#e66465",
     *                 "bg_end_color": "#e66465",
     *                 "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *               }
     *             }
     *           ]
     *         },
     *         {
     *           "id": 2114,
     *           "sort": 3,
     *           "title": "剧集模块标题",
     *           "type": 2,
     *           "style": 0,
     *           "more": {
     *             "url": "https://test.com/aaa?foo=bar"  // 更多按钮跳转链接，不下发或为空时跳转到默认的原生自定义模块详情页
     *           },
     *           "elements": [
     *             {
     *               "id": 28,  // 剧集 ID
     *               "name": "剧集标题",
     *               "abstract": "剧集简介",
     *               "cover_color": 0,  // 背景图主颜色，十进制表示，在模块样式为堆叠式时使用
     *               "integrity": 2,  // 完结度 1：长篇未完结；2：长篇完结；3：全一期；4：微小剧
     *               "newest": "第五期",  // 更新至
     *               "pay_type": 1,  // 付费类型，0：免费；1：单集付费；2：整剧付费
     *               "view_count": 21926,  // 播放次数
     *               "outline": null,
     *               "front_cover": "http://static.missevan.com/dramacoversmini/201604/15/test.jpg",
     *               "sort": 0,
     *               "module_id": 2114,
     *               "corner_mark": {  // 无剧集角标时不返回该字段
     *                 "text": "已购",
     *                 "text_color": "#ffffff",
     *                 "bg_start_color": "#e66465",
     *                 "bg_end_color": "#e66465",
     *                 "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *               }
     *             }
     *           ]
     *         },
     *         {
     *           "id": 2122,
     *           "sort": 1,
     *           "title": "音频模块标题",
     *           "type": 3,
     *           "style": 1,
     *           "more": {
     *             "url": "https://test.com/aaa?foo=bar"  // 更多按钮跳转链接，不下发或为空时跳转到默认的原生自定义模块详情页
     *           },
     *           "elements": [
     *             {
     *               "id": 5621487,  // 音频 ID
     *               "front_cover": "http://static-test.maoercdn.com/coversmini/201906/10/test.jpg",  // 音频封面
     *               "soundstr": "铁血加特林",  // 音频标题
     *               "intro": "这是一句话简介，请在模块后台设置",
     *               "view_count": 233,  // 播放次数
     *               "comment_count": 0,  // 弹幕数量，模块详情页使用
     *               "all_comments": 233,  // 总评论数量，模块推荐位使用
     *               "sort": 0,
     *               "module_id": 2151,
     *               "user_id": 234,  // UP 主的用户 ID
     *               "username": "UP 主的用户名",  // UP 主的用户名
     *               "video": true  // 绑定了视频返回 true, 未绑定不返回
     *             }
     *           ]
     *         }
     *       ]
     *     }
     */
    public function actionRecommendModules(int $catalog_id = Drama::DRAMA_CATALOG_ID_CN_RADIO_DRAMA)
    {
        if (!in_array($catalog_id, Drama::SOUND_TO_DRAMA_CATALOG_IDS)) {
            throw new HttpException(404, '分类不存在');
        }
        return Catalog::getRecommendModules($catalog_id);
    }

    /**
     * @api {get} /drama/catalog-homepage{?catalog_id} 分区首页推荐
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/catalog-homepage
     *
     * @apiVersion 0.1.0
     * @apiName catalog-homepage
     * @apiGroup drama
     *
     * @apiParam {Number} catalog_id 分区 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *    HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "banners": [  // 分区顶部轮询图
     *           {
     *             "pic_app": "http://static.missevan.com/mimages/202003/09/46f18f0cd2b11e75828b2f120211.jpg",
     *             "url": "https://www.missevan.com/sound/1782142",
     *             "sort": 1
     *           }
     *         ],
     *         "hot_recommend": {
     *           "title": "热门推荐",
     *           "elements": [
     *             {
     *               "id": 9888,
     *               "name": "杀破狼 第一季",
     *               "pay_type": 2,
     *               "front_cover": "http://static.missevan.com/dramacoversmini/201704/19/fe852a7040350.jpg",
     *               "cover_color": 16777215,
     *               "corner_mark": {  // 无剧集角标时不返回该字段
     *                 "text": "已购",
     *                 "text_color": "#ffffff",
     *                 "bg_start_color": "#e66465",
     *                 "bg_end_color": "#e66465",
     *                 "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *               }
     *             },
     *             {
     *               "id": 25032,
     *               "name": "全球高考 第一季",
     *               "pay_type": 2,
     *               "front_cover": "http://static.missevan.com/dramacoversmini/201912/31/709b79e161545.jpg",
     *               "cover_color": 16777215
     *             }
     *           ]
     *         },
     *         "classic_paid": {
     *           "title": "付费精品",
     *           "elements": [
     *             {
     *               "id": 9888,
     *               "name": "杀破狼 第一季",
     *               "pay_type": 2,
     *               "front_cover": "http://static.missevan.com/dramacoversmini/201704/19/fe852a7040350.jpg",
     *               "cover_color": 16777215,
     *               "corner_mark": {  // 无剧集角标时不返回该字段
     *                 "text": "已购",
     *                 "text_color": "#ffffff",
     *                 "bg_start_color": "#e66465",
     *                 "bg_end_color": "#e66465",
     *                 "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *               }
     *             },
     *             {
     *               "id": 25032,
     *               "name": "全球高考 第一季",
     *               "pay_type": 2,
     *               "front_cover": "http://static.missevan.com/dramacoversmini/201912/31/709b79e161545.jpg",
     *               "cover_color": 16777215
     *             }
     *           ],
     *           "more": {  // 点更多时进入「剧集索引」页并选中「付费」标签，需要客户端自己解析匹配对应的 tag
     *             "drama_filters": "0_0_0_0_0_2"
     *           }
     *         },
     *         "weekly_rank": {
     *           "title": "人气周榜",
     *           "elements": [
     *             {
     *               "id": 22602,
     *               "name": "魔道祖师 第三季",
     *               "abstract": "前世今生局中局，真相扑朔迷离，迷雾重重，忘羡二人能否互通心意，并肩携手勘破真相。",
     *               "front_cover": "http://static.missevan.com/mimages/201711/15/92eddc64e7121500.jpg",
     *               "newest": "主创FT<下>",
     *               "pay_type": 2,
     *               "sort": 1,
     *               "view_count": 333279,
     *               "corner_mark": {  // 无剧集角标时不返回该字段
     *                 "text": "已购",
     *                 "text_color": "#ffffff",
     *                 "bg_start_color": "#e66465",
     *                 "bg_end_color": "#e66465",
     *                 "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *               }
     *             },
     *             {
     *               "id": 15861,
     *               "name": "魔道祖师 第一季",,
     *               "abstract": "夷陵老祖重生归来，是会掀起一场腥风血雨？还是与昔日冤家再续前缘，携手破解前世谜团？",
     *               "front_cover": "http://static.missevan.com/mimages/201711/15/92eddc64e7121500.jpg",
     *               "newest": "主创FT<下>",
     *               "pay_type": 2,
     *               "sort": 2,
     *               "need_pay": 1,
     *               "view_count": 333279
     *             }
     *           ],
     *         }
     *         "extra_banners": {  // 轮播通栏
     *           "1": [
     *             {
     *               "cover": "http://static.missevan.com/mimages/202003/09/46f18f0cd2b11e75828b2f120211.jpg",
     *               "url": "missevan://omikuji/draw",
     *               "sort": 1
     *             }
     *           ]
     *         }
     *         "hot_words": ["魔道祖师", "撒野", "二哈和他的白猫师尊"],
     *         "schedule": {  // "时间表"入口信息，不返回或返回 null 时，客户端不显示"时间表"按钮
     *           "open_url": ""  // 如果 open_url 不为空，客户端点击时间表根据 msr-0 规则打开该地址页面，其他情况跳转默认的原生页面
     *         }
     *       }
     *     }
     */
    public function actionCatalogHomepage(int $catalog_id)
    {
        if (Equipment::isAppOlderThan('4.4.6', '5.3.6')) {
            // 若为 PGC 分区之前的版本，则传递的 catalog_id 为音频分类，需要转化为剧集分类
            $catalog_id = Drama::getCatalogBySoundCatalog($catalog_id);
        }
        // 当前仅支持 PGC 分区
        if (!in_array($catalog_id, Drama::SOUND_TO_DRAMA_CATALOG_IDS)) {
            throw new HttpException(400, '参数错误');
        }
        $user_id = (int)Yii::$app->user->id;
        return Drama::getCatalogHomepage($catalog_id, $user_id);
    }

    /**
     * @api {get} /drama/catalog-page{?catalog_id,type,order,page,page_size} 分区分类剧集
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/catalog-page
     *
     * @apiVersion 0.1.0
     * @apiName catalog-page
     * @apiGroup drama
     *
     * @apiParam {Number} [catalog_id=89] 分区 ID
     * @apiParam {Number} type 类型
     * @apiParam {Number} integrity 完结度
     * @apiParam {Number} [order=1] 排序方式 1：综合排序；2：最多播放；3：最多评论；4：最新更新
     * @apiParam {Number} [page=1] 当前页数
     * @apiParam {Number} [page_size=20] 每页个数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "type_name": "全年龄",
     *         "Datas": [
     *           {
     *             "id": 53,
     *             "name": "撞鬼",
     *             "username": "翼之声中文配音社团",
     *             "front_cover": "http://static.missevan.com/dramacoversmini/201604/20/test.jpg",
     *             "cover_color": 16777215,
     *             "last_ord_str": "共 5 期",
     *             "pay_type": 2,
     *             "view_count": 333279,
     *             "corner_mark": {  // 无剧集角标时不返回该字段
     *               "text": "已购",
     *               "text_color": "#ffffff",
     *               "bg_start_color": "#e66465",
     *               "bg_end_color": "#e66465",
     *               "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *             }
     *           },
     *           {
     *             "id": 15861,  // 剧集 ID
     *             "name": "魔道祖师 第一季",  // 剧集名称
     *             "username": "知行天地工作室",  // 用户名
     *             "front_cover": "http://static.missevan.com/dramacoversmini/201604/20/test.jpg",  // 封面图片
     *             "cover_color": 16777215,  // 封面图片 RGB 颜色值
     *             "last_ord_str": "共 5 期",  // 完结情况
     *             "pay_type": 2,  // 剧集付费类型 0：免费；1：单集付费；2：整剧付费
     *             "view_count": 333279  // 收听次数
     *           }
     *         ],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 1,
     *           "count": 20,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     */
    public function actionCatalogPage(int $catalog_id = Drama::DRAMA_CATALOG_ID_CN_RADIO_DRAMA, int $type = 0, int $integrity = 0,
            int $order = 1, int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        if ($catalog_id <= 0 || $type < 0 || $integrity < 0 || $order <= 0) {
            throw new HttpException(400, '参数错误');
        }
        if (Equipment::isAppOlderThan('4.4.6', '5.3.6')) {
            // 若为 PGC 分区之前的版本，则传递的 catalog_id 为音频分类，需要转化为剧集分类
            $catalog_id = Drama::getCatalogBySoundCatalog($catalog_id);
        }
        if (!in_array($catalog_id, Drama::SOUND_TO_DRAMA_CATALOG_IDS)) {
            throw new HttpException(400, '参数错误');
        }
        $tab_title = '';
        $sub_type = Drama::SUB_CATEGORY_BY_TYPE;
        $sub_value = $type;
        if ($type) {
            $drama_type = Drama::getDramaTypes($catalog_id);
            $type_name_map = array_column($drama_type, 'type_name', 'type');
            $tab_title = $type_name_map[$type] ?? '';
        } elseif ($integrity) {
            $tab_title = Drama::getDramaIntegrityName($integrity);
            $sub_type = Drama::SUB_CATEGORY_BY_INTEGRITY;
            $sub_value = $integrity;
        }
        if ($tab_title === '') {
            throw new HttpException(400, '参数错误');
        }
        $user_id = (int)Yii::$app->user->id;
        $pagination = PaginationParams::process($page, $page_size);
        $catalog_drama = Drama::getCatalogDrama($catalog_id, $sub_type, $sub_value, $user_id, $order, $pagination->page, $pagination->page_size);
        return [
            'type_name' => $tab_title,
            'Datas' => $catalog_drama['Datas'],
            'pagination' => $catalog_drama['pagination'],
        ];
    }

    /**
     * @api {get} /drama/weekly-rank{?catalog_id,type,page,page_size} 人气周榜
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/drama/weekly-rank
     * @apiDescription 人气周榜页面加载好之后显示对应 active 为 true 的 tabs
     * @apiVersion 0.1.0
     * @apiName weekly-rank
     * @apiGroup drama
     *
     * @apiParam {Number} catalog_id 分区 ID
     * @apiParam {number=3,4,5,6} [type=4] 性向：3：全年龄；4：纯爱；5：双女主；6：言情。
     * iOS 4.7.6 以下、安卓 5.6.4 以下需要传参
     * @apiParam {number=1,2,5} [integrity=1] 完结度 1：未完结；2：完结；5：全一期和微小剧。
     * iOS 4.7.6 及其以上、安卓 5.6.4 及其以上需要传参
     * @apiParam {Number} [page=1] 当前页数
     * @apiParam {Number} [page_size=20] 每页个数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "tabs": [  // 人气周榜只显示这个接口返回的 tabs
     *           {
     *             "type": 4,
     *             "type_name": "纯爱",
     *             "active": true
     *           },
     *           {
     *             "type": 6,
     *             "type_name": "言情"
     *           },
     *           {
     *             "type": 3,
     *             "type_name": "全年龄"
     *           },
     *           {
     *             "type": 5,
     *             "type_name": "双女主"
     *           }
     *         ],
     *         "Datas": [
     *           {
     *             "id": 22602,
     *             "name": "魔道祖师 第三季",
     *             "abstract": "前世今生局中局，真相扑朔迷离，迷雾重重，忘羡二人能否互通心意，并肩携手勘破真相。",
     *             "front_cover": "http://static.missevan.com/mimages/201711/15/92eddc64e7121500.jpg",
     *             "newest": "主创FT<下>",
     *             "pay_type": 2,
     *             "sort": 1,
     *             "need_pay": 1,
     *             "view_count": 333279
     *           },
     *           {
     *             "id": 15861,
     *             "name": "魔道祖师 第一季",,
     *             "abstract": "夷陵老祖重生归来，是会掀起一场腥风血雨？还是与昔日冤家再续前缘，携手破解前世谜团？",
     *             "front_cover": "http://static.missevan.com/mimages/201711/15/92eddc64e7121500.jpg",
     *             "newest": "主创FT<下>",
     *             "pay_type": 2,
     *             "sort": 2,
     *             "need_pay": 1,
     *             "view_count": 333279
     *           }
     *         ],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 1,
     *           "count": 20,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     */
    public function actionWeeklyRank(int $catalog_id, int $type = Drama::TYPE_END,
            int $integrity = Drama::INTEGRITY_NAME_SERIALIZING, int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        if (Equipment::isAppOlderThan('4.4.6', '5.3.6')) {
            // 若为 PGC 分区之前的版本，则传递的 catalog_id 为音频分类，需要转化为剧集分类
            $catalog_id = Drama::getCatalogBySoundCatalog($catalog_id);
        }
        if (!in_array($catalog_id, Drama::SOUND_TO_DRAMA_CATALOG_IDS)) {
            throw new HttpException(400, '参数错误');
        }
        if (in_array($catalog_id, [Drama::DRAMA_CATALOG_ID_CN_RADIO_DRAMA, Drama::DRAMA_CATALOG_ID_CN_CARTOON])
                && Equipment::isAppOlderThan('4.7.6', '5.6.4')) {
            // WORKAROUND: 中文广播剧和中文有声漫画针对 iOS 4.7.6 以下、安卓 5.6.4 以下将 type 转换成对应的 integrity
            switch ($type) {
                case Drama::TYPE_SERIALIZING:
                    $integrity = Drama::INTEGRITY_NAME_SERIALIZING;
                    break;
                case Drama::TYPE_END:
                    $integrity = Drama::INTEGRITY_NAME_END;
                    break;
                case Drama::TYPE_ONE_AND_MINI:
                    $integrity = Drama::INTEGRITY_NAME_ONE_AND_MINI;
                    break;
            }
        }
        $user_id = (int)Yii::$app->user->id;
        return Drama::getWeeklyRank($catalog_id, $user_id, $integrity, $type, $page, $page_size);
    }
}
