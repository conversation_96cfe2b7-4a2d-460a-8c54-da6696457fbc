<?php

namespace app\controllers;

use app\components\cache\RedisCache;
use app\components\controllers\PersonInterface;
use app\components\util\Equipment;
use app\components\util\Go;
use app\components\util\LiveRpc;
use app\components\util\MUtils;
use app\components\util\Tools;
use app\forms\AnFeedbackForm;
use app\forms\UserContext;
use app\middlewares\Controller;
use app\models\AnFeedback;
use app\models\AnMsg;
use app\models\AnMsgRO;
use app\models\Balance;
use app\models\BlackUser;
use app\models\TopupMenu;
use app\models\Discovery;
use app\models\Drama;
use app\models\Feed;
use app\models\Live;
use app\models\MAlbum;
use app\models\MAttentionUser;
use app\models\MAvatarFrame;
use app\models\MEvent;
use app\models\MThirdPartyTask;
use app\models\MUserVip;
use app\models\PayAccount;
use app\models\RechargeOrder;
use app\models\UserCertification;
use app\models\MCollectAlbum;
use app\models\MDanmaku;
use app\models\MImage;
use app\models\MLikeSound;
use app\models\Mowangskuser;
use app\models\MPersonDramaPage;
use app\models\MPointFeed;
use app\models\MReport;
use app\models\MReportReason;
use app\models\MSobotUser;
use app\models\MSound;
use app\models\MSoundAlbumMap;
use app\models\MSoundCommentRO;
use app\models\MTag;
use app\models\MHomepageIcon;
use app\models\MUserConfig;
use app\models\MUserHistory;
use app\models\PaginationParams;
use app\models\PointDetailLog;
use app\models\ReturnModel;
use app\models\Share;
use app\models\SoundCommentRO;
use app\models\SoundSubCommentRO;
use app\models\TransactionLog;
use app\models\UserAddendum;
use app\models\UserCover;
use app\components\base\filter\AccessControl;
use app\models\UserSignHistory;
use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\web\HttpException;

class PersonController extends Controller implements PersonInterface
{
    const TASK_UNFINISHED = 0;  // 任务未完成
    const TASK_FINISHED = 1;  // 任务已完成
    const TASK_AWARDED = 2;  // 任务已领取奖励，目前仅直播任务下发

    // 鱼干任务（签到、摸鱼、投食、分享、评论、淘宝农场）
    const TASK_TYPE_SIGN = 0;
    const TASK_TYPE_GET_POINT = 1;
    const TASK_TYPE_TS = 4;
    const TASK_TYPE_SHARE = 5;
    const TASK_TYPE_COMMENT = 6;

    // 限时引流任务
    const TASK_TYPE_TB = 7;
    const TASK_TYPE_ZFB = 8;  // 支付宝引流任务
    const TASK_TYPE_BAIDU = 9;  // 百度引流任务
    const TASK_TYPE_CTRIP = 13;  // 携程引流任务
    const TASK_TYPE_WECHAT_OFFIACCOUNT = 19; // 微信公众号任务

    // 直播任务
    const TASK_TYPE_LIVE_VISIT = 10;  // 进入直播间任务
    const TASK_TYPE_LIVE_LISTEN_NEW_USER = 11;  // 收听直播时长任务 - 新人专享
    const TASK_TYPE_LIVE_LISTEN = 12;  // 收听直播时长任务 - 非新人
    const TASK_TYPE_LIVE_SEND_MESSAGE = 15;  // 在直播间发消息抽钻石 - 新人专享
    // 直播任务二级分类
    const LIVE_TASK_TYPE_NORMAL = 1;  // 直播普通任务
    const LIVE_TASK_TYPE_SEND_MESSAGE = 2;  // 直播发消息任务

    // 获取签到次数与签到
    const SIGN_TYPE_GET_TIMES = 0;
    const SIGN_TYPE_SIGN = 1;
    // 通过投食、分享及评论获取鱼干的所需次数
    const TASK_TS_TIMES_LIMIT = 3;
    const TASK_SHARE_TIMES_LIMIT = 3;
    const TASK_COMMENT_TIMES_LIMIT = 3;
    // 分享、评论获小鱼干数量
    const TASK_SHARE_POINT = 5;
    const TASK_COMMENT_POINT = 5;

    // 模块 ID 1：用户信息，2：剧集作品，3：声音，4：直播回放，5：剧集订阅，6：我的收藏，7：图片
    const MODULE_ID_USER_INFO = 1;
    const MODULE_ID_USER_DRAMA = 2;
    const MODULE_ID_USER_SOUND = 3;
    const MODULE_ID_USER_LIVE_SOUND = 4;
    const MODULE_ID_USER_SUBSCRIBE_DRAMA = 5;
    const MODULE_ID_USER_COLLECT = 6;
    const MODULE_ID_USER_IMAGE = 7;

    // 模块类型 1：用户信息，2：剧集作品，3：声音，4：直播回放，5：剧集订阅，6：我的收藏，7：图片
    const MODULE_TYPE_USER_INFO = 1;
    const MODULE_TYPE_USER_DRAMA = 2;
    const MODULE_TYPE_USER_SOUND = 3;
    const MODULE_TYPE_USER_LIVE_SOUND = 4;
    const MODULE_TYPE_USER_SUBSCRIBE_DRAMA = 5;
    const MODULE_TYPE_USER_COLLECT = 6;
    const MODULE_TYPE_USER_IMAGE = 7;

    // 删除历史操作类型
    const DELETE_HISTORY_TYPE_DEL = 0;
    const DELETE_HISTORY_TYPE_CLEAR = 1;

    // 更新智齿信息的更新类型（1: 更新登录用户的智齿 visitor_id; 2: 更新智齿未读消息状态）
    const UPDATE_SOBOT_TYPE_VISITOR_ID = 1;
    const UPDATE_SOBOT_TYPE_MSG_STATUS = 2;

    // 完成手淘任务获得的奖励类型
    // 小鱼干
    const TASK_TB_REWARD_TYPE_POINT = 1;
    // 钻石
    const TASK_TB_REWARD_TYPE_DIAMOND = 2;

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'feed-back' => ['post'],
                'report' => ['post'],
                'follow' => ['post'],
                'drama-bought' => ['get'],
                'drama-recover' => ['get'],
                'hide-drama-purchase-order' => ['post'],
                'recover-drama-purchase-order' => ['post'],
                'del-history' => ['post'],
                'sign' => ['post'],
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'only' => [
                'report',
                'task',
                'task-status',
                'feed',
                'count-action',
                'drama-feed',
                'follow',
                'user-feed',
                'drama-bought',
                'drama-recover',
                'hide-drama-purchase-order',
                'recover-drama-purchase-order',
                'search-attention-users',
                'history',
                'del-history',
                'search-favorite-sound',
                'get-user-point',
                'get-sobot-user',
                'sign-history',
                'sign',
            ],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => [
                        'report',
                        'task',
                        'task-status',
                        'feed',
                        'count-action',
                        'drama-feed',
                        'get-history',
                        'clear-history',
                        'follow',
                        'user-feed',
                        'drama-bought',
                        'drama-recover',
                        'hide-drama-purchase-order',
                        'recover-drama-purchase-order',
                        'search-attention-users',
                        'history',
                        'del-history',
                        'search-favorite-sound',
                        'get-user-point',
                        'get-sobot-user',
                        'sign-history',
                        'sign',
                    ],
                    'roles' => ['@']
                ],
            ]
        ];

        return $behaviors;
    }

    /**
     * @api {post} /person/follow 关注和取消关注
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/follow
     * @apiSampleRequest /person/follow
     * @apiDescription 关注和取消关注（iOS 4.1.2 及其以上、安卓 5.0.1 及其以上调用该接口）
     * @apiVersion 0.1.0
     * @apiName follow
     * @apiGroup person
     *
     * @apiParam {Number} user_id 被关注的用户 ID
     * @apiParam {number=0,1} [type=1] 操作类型（关注 1，取消关注 0）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 操作结果信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "msg": "关注成功",
     *         "attention": true  // true: 关注；false: 取关
     *       }
     *     }
     *
     */

    public function actionFollow()
    {
        $star_id = (int)Yii::$app->request->post('user_id');
        $type = (int)Yii::$app->request->post('type', MUtils::ACTION_DO);
        if (!in_array($type, [MUtils::ACTION_DO, MUtils::ACTION_UNDO])) {
            throw new HttpException(400, '参数错误');
        }
        $fans_id = Yii::$app->user->id;

        switch ($type) {
            case MUtils::ACTION_DO:
                // 关注
                $return = MAttentionUser::follow($fans_id, $star_id);
                break;
            case MUtils::ACTION_UNDO:
                // 取消关注
                $return = MAttentionUser::unfollow($fans_id, $star_id);
                break;
            default:
                throw new HttpException(400, '参数错误');
        }
        if (is_string($return)) {
            // WORKAROUND: 兼容当前 rpc 返回数据类型 string, 此后删除
            $return = ['msg' => $return];
        }
        $return['attention'] = $type === MUtils::ACTION_DO;
        return $return;
    }

    /**
     * @api {get} /person/share{?type,element_id,url} 分享
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/share
     * @apiSampleRequest /person/share
     * @apiDescription  /mobile/personOperation/fx 分享
     * @apiVersion 0.1.0
     * @apiName share
     * @apiGroup person
     *
     * @apiParam {Number} [type=0] 分享主体的类型，1：音频；2：催眠专享；3：盲盒剧场；4：活动；5：直播间
     * 相关分享类型未定义时此参数不传；
     * @apiParam {Number} [element_id=0] 分享元素 ID。相关分享类型未定义时此参数不传；
     * @apiParam {String} url 分享出去的 url
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info 提示文案
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info: "分享成功"
     *     }
     *
     */
    public function actionShare()
    {
        $type = (int)Yii::$app->request->get('type');
        $element_id = (int)Yii::$app->request->get('element_id');
        $url = trim(Yii::$app->request->get('url'));

        // 目前活动页右上角分享按钮没有传入 type 和 element_id，需要根据 url 解析
        // 活动分享 url 示例：https://www.missevan.com/mevent/352?share_channel=wechat
        if ($type === 0 && $element_id === 0) {
            $parsed_url = parse_url($url);
            $domain = ($parsed_url['scheme'] ?? '') . '://' . ($parsed_url['host'] ?? '');
            if ($domain === Yii::$app->params['domainMissevan']) {
                $path_array = explode('/', ($parsed_url['path'] ?? ''));
                if (count($path_array) === 3 && $path_array[1] === 'mevent' && is_numeric($path_array[2])) {
                    $TYPE_EVENT = 4;
                    $type = $TYPE_EVENT;
                    $element_id = (int)$path_array[2];
                }
            }
        }
        $user_id = (int)Yii::$app->user->id;
        if ($user_id) {
            $redis = Yii::$app->redis;
            $today = strtotime(date('Ymd'));
            $key = $redis->generateKey(SHARE, $user_id, $today);
            $at_time = strtotime(date('Ymd', strtotime('+1 day')));
            $redis->incr($key);
            $redis->expireAt($key, $at_time);
        }
        Share::addLog($user_id, $type, $element_id, $url);
        return '分享成功';
    }

    /**
     * @api {post} /person/feed-back 意见反馈
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/feed-back
     * @apiSampleRequest /person/feed-back
     * @apiDescription  /mobile/site/feedbackComment 参数uuid和client不需要提供
     *
     * @apiVersion 0.1.0
     * @apiName feed-back
     * @apiGroup person
     *
     * @apiParam {Number} timestamp unix时间戳，单位为秒
     * @apiParam {String} content 反馈内容
     * @apiParam {String} signature base64.encode(hmac->sha1($key, {uuid}{timestamp}{client}{content}))
     *
     */

    public function actionFeedBack()
    {
        if (!Equipment::isAppOlderThan('4.2.9', '5.2.0')) {
            // iOS App 版本大于 4.2.9，安卓 App 版本大于 5.2.0 调用新控制器里对应的接口
            throw new HttpException(400, '非法请求');
        }
        $signature = Yii::$app->request->post('signature');
        $content = trim(Yii::$app->request->post('content'));
        $timestamp = (int)Yii::$app->request->post('timestamp', 0);
        $client = Yii::$app->equip->getOs();
        $equip_id = Yii::$app->equip->getEquipId();
        if (!Equipment::isAppOlderThan(null, '5.1.7') &&
                Equipment::isAppOlderThan('4.2.9', '5.1.9')) {
            // WORKAROUND: 当安卓客户端版本小于 5.1.7 时，因部分客户端签名的 equip_id 存在问题，不进行验签操作
            // 安卓 5.1.7 和 5.1.8 版本以及 iOS 4.2.9 之前旧版本使用旧的签名验证
            // 安卓 5.1.9 和 iOS 4.2.9 及以后的版本接口使用全局签名验证
            $str = $equip_id . $timestamp . $client . $content;
            if (base64_decode($signature) !== hash_hmac('sha1', $str, Yii::$app->params['feedBackKey'],
                true)) {
                throw new HttpException(400, '验证错误');
            }
        }
        $user_id = Yii::$app->user->id;
        $feedback_form = new AnFeedbackForm(['scenario' => AnFeedbackForm::SCENARIO_REPLY_CONTENT]);
        $feedback_form->content = $content;
        $feedback_form->type = AnFeedback::TYPE_FEEDBACK_DIRECT;
        $feedback_form->ticket_id = AnFeedback::FEEDBACK_DIRECT;
        $feedback_form->equip_id = $equip_id;
        $feedback_form->client = $client;
        $feedback_form->user_id = $user_id;
        $feedback_form->timestamp = $timestamp;
        if (!$feedback_form->validate()) {
            throw new HttpException(400, MUtils::getFirstError($feedback_form));
        }
        $feedback_form->replyTicket();
        return '我们已经收到你的意见';
    }

    /**
     * 是否为直播任务
     *
     * @param integer $gtype 任务类型
     * @return bool
     */
    private static function isLiveTask(int $gtype): bool
    {
        return in_array($gtype, [
            self::TASK_TYPE_LIVE_VISIT,
            self::TASK_TYPE_LIVE_LISTEN_NEW_USER,
            self::TASK_TYPE_LIVE_LISTEN,
            self::TASK_TYPE_LIVE_SEND_MESSAGE,
        ]);
    }

    /**
     * 是否完成直播任务
     *
     * @param array $live_task 直播任务
     * @param array $user_task 用户参与直播任务详情
     * @return bool
     */
    private static function isFinishedLiveTask(array $live_task, array $user_task): bool
    {
        switch ($live_task['gtype']) {
            case self::TASK_TYPE_LIVE_VISIT:
                if (isset($user_task['today_listen_duration'])) {
                    return true;
                }
                break;
            case self::TASK_TYPE_LIVE_LISTEN_NEW_USER:
            case self::TASK_TYPE_LIVE_LISTEN:
                if (isset($user_task['today_listen_duration']) && $user_task['today_listen_duration'] >= $live_task['task_value']) {
                    return true;
                }
                break;
            case self::TASK_TYPE_LIVE_SEND_MESSAGE:
                if (isset($user_task['today_message_count']) && $user_task['today_message_count'] >= $live_task['task_value']) {
                    return true;
                }
                break;
        }
        return false;
    }

    /**
     * 获取完成本次限时任务可获得的随机钻石奖励数量
     *
     * @param integer $gtype 任务类型
     * @param array|null $diamond_random_rule 奖励配置
     * @param integer $reward_num 默认奖励数量
     * @return integer 奖励数量
     */
    private function getRewardDiamondNum(int $gtype, ?array $diamond_random_rule, int $reward_num) : int
    {
        if (!$diamond_random_rule) {
            return $reward_num;
        }
        $redis = Yii::$app->redis;
        $is_live_task = self::isLiveTask($gtype);
        $key = $is_live_task ? $redis->generateKey(COUNTER_LIVE_TASK_DIAMOND, date('Ymd')) :
            $redis->generateKey(COUNTER_ADDITIONAL_TASK_FINISHED, date('Ymd'), $gtype);
        $total_nums = (int)$redis->get($key);
        $random_limit = $diamond_random_rule['random_limit'] ?? 0;
        $num = 0;
        if ($random_limit && $total_nums >= $random_limit) {
            // 超出当日发放限制时，返回固定数量奖励
            $num = $reward_num;
        } else {
            $total_weight = 0;
            $rand = mt_rand(1, array_sum($diamond_random_rule['weights']));
            foreach ($diamond_random_rule['weights'] as $reward => $weight) {
                $total_weight += $weight;
                if ($rand <= $total_weight) {
                    $num = $reward;
                    break;
                }
            }
            $num = ($is_live_task && (($num + $total_nums) > $random_limit)) ? $reward_num : $num;
        }
        [$total_nums,] = $redis->multi()
            ->incrBy($key, $is_live_task ? $num : 1)  // 若为直播任务增加钻石数
            ->expire($key, 2 * ONE_DAY)
            ->exec();
        if ($total_nums > $random_limit && $num > $reward_num) {
            if ($is_live_task) {
                // 回滚超加的钻石数
                $redis->multi()
                    ->incrBy($key, $reward_num - $num)
                    ->expire($key, 2 * ONE_DAY)
                    ->exec();
            }
            // 超出当日发放限制时，返回固定数量奖励
            $num = $reward_num;
        }
        return $num;
    }

    /**
     * 获取完成本次限时任务可获得的随机小鱼干奖励数量和提示语句
     *
     * @param array|null $random_rule 奖励配置
     * @param integer $reward_num 默认奖励数量
     * @param string $finished_info_format 含有占位符的默认奖励提示
     * @return array 奖励数量和提示语句, e.g. ['num' => 3, 'finished_info' => '运气爆棚！摸到 88 个小鱼干！']
     */
    private function getRewardPointInfo(?array $random_rule, int $reward_num, string $finished_info_format) : array
    {
        $default_return = ['num' => $reward_num, 'finished_info' => sprintf($finished_info_format, $reward_num)];
        if (!$random_rule) {
            return $default_return;
        }
        $total_weight = 0;
        $rand = mt_rand(1, array_sum($random_rule['weights']));
        foreach ($random_rule['weights'] as $reward => $weight) {
            $total_weight += $weight;
            if ($rand <= $total_weight) {
                $finished_info_format = $random_rule['finished_info_format'][$reward] ?? $finished_info_format;
                return ['num' => $reward, 'finished_info' => sprintf($finished_info_format, $reward)];
            }
        }
        return $default_return;
    }

    /**
     * 获取直播任务未完成时提示的错误信息
     *
     * @param int $gtype 任务类型
     * @return string
     * @throws HttpException
     */
    private static function getLiveTaskErrorMessage(int $gtype): string
    {
        switch ($gtype) {
            case self::TASK_TYPE_LIVE_VISIT:
                return '必须进入直播间才能完成本任务';
            case self::TASK_TYPE_LIVE_LISTEN_NEW_USER:
            case self::TASK_TYPE_LIVE_LISTEN:
                return '未满足收听时长';
            case self::TASK_TYPE_LIVE_SEND_MESSAGE:
                return '未达到发消息目标次数';
            default:
                throw new HttpException(400, '该任务尚未开放', *********);
        }
    }

    /**
     * @api {post} /person/task 日常任务，签到任务
     *
     * @apiDescription Android >= 5.7.7, iOS >= 4.9.2 调用 /person/sign 签到，低于以上版本继续用此接口签到 \
     * Android 或 iOS >= 6.2.5 时，完成各种任务需要使用 post 传参，在使用 post 传参时仍然用 query 参数 \
     * 仅获取签到状态仍使用 get 传参，后续可以考虑在 task-status 接口获取签到状态
     * @apiVersion 0.1.0
     * @apiName task
     * @apiGroup person
     *
     * @apiParam (Query) {number=0,1} [type=0] 事件类型（只对应签到任务 默认 0 获取签到次数、1 为签到）
     * @apiParam (Query) {number=0-13} [gtype=1] 0 为签到，1 为摸鱼，4 为投食三次抽小鱼干，5 为分享三次抽小鱼干，\
     * 6 为评论三次抽小鱼干，7 ~ 9 或 13 ~ 14 或大于 15 均为限时引流任务，10 进入直播间任务，11 和 12 收听直播时长任务，15 直播间发消息任务，19 关注公众号任务
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "times": 197,  // 连续签到天数
     *         "is_signed": 1,  // 当天是否已经签到
     *         "ears": "签到成功获取 3 个小鱼干"
     *       }
     *     }
     *
     * @apiSuccessExample Success-Response: 三方导流到端任务首次请求（iOS ≥ 6.2.7 Android ≥ 6.2.7）
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "native_url": "tbopen://m.taobao.com/tbopen/index.html?foo=bar?token=xxxxxx",  // 唤起端外应用的链接，不下发时不跳转
     *         "web_url": "https://market.m.test.com/xx?foo=bar?token=xxxxxx"  // 打开的网页链接。当下发了 native_url 时，优先唤起 native_url 的应用，不下发时不跳转
     *       }
     *     }
     *
     * @apiSuccessExample Success-Response: 完成摸鱼、投食、分享、评论任务、额外的限时任务、直播任务、三方导流到端任务完成回调
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "info": "RP 一般，摸到 5 个小鱼干",  // 提示文本
     *         "nums": 5,  // 获得奖励数量（钻石或小鱼干）
     *         "reward_icon_url": "https://abc.png"  // 奖励图标，仅在需要展示奖励图标时下发
     *       }
     *     }
     *
     * @apiSuccessExample Success-Response: 已完成过摸鱼、投食、分享、评论任务、额外的限时任务、直播任务，不可继续做该任务时
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "今天任务已经做完了"
     *     }
     *
     */

    public function actionTask(int $gtype = 1, int $type = 0)
    {
        $user_id = Yii::$app->user->id;
        $user = Mowangskuser::findOne($user_id);
        $tomorrow = strtotime('tomorrow');
        $redis = Yii::$app->redis;
        $gtype_key = (string)$gtype;

        //签到
        if ($gtype === self::TASK_TYPE_SIGN) {
            $is_sign = self::SIGN_TYPE_SIGN === $type;
            if ($is_sign && !Equipment::isAppOlderThan('4.9.2', '5.7.7')) {
                throw new HttpException(400, '非法请求');
            }
            $today = strtotime('today', $_SERVER['REQUEST_TIME']);
            $is_signed = (int)UserSignHistory::isSigned($user_id, $today);
            $user_sign = UserAddendum::getUserSign($user_id);
            $times = $user_sign['current_days'];
            // TODO: 获取状态信息不需要返回该值，需要使用新接口获取签到状态
            $ears = '签到失败';

            if ($is_sign) {
                if (!$is_signed) {
                    $result = $this->sign($user, $today, UserSignHistory::TYPE_SIGN);
                    if ($result) {
                        $is_signed = 1;
                        $times++;
                        $ears = '签到成功获取 ' . PointDetailLog::POINT_TASK_SIGN . ' 个小鱼干';
                    }
                }
            }

            return ['times' => $times, 'is_signed' => $is_signed, 'ears' => $ears];
        }

        // WORKAROUND: 安卓或 iOS 在 < 6.2.5 版本时才允许使用 GET 传参
        if (!Equipment::isAppOlderThan('6.2.5', '6.2.5') && !Yii::$app->request->isPost) {
            throw new HttpException(400, '非法请求');
        }

        $today = strtotime(date('Ymd'));
        $random = false;
        $reward_num = $point_num = $diamond_num = $event_id = $log_type = 0;
        $finished_info_format = $finished_info = $task_name = '';
        $diamond_random_rule = null;
        $is_to_third_party_task = false;
        $third_party_task_scene = 0;
        $is_live_task = self::isLiveTask($gtype);
        $reward_icon_url = '';
        if ($is_live_task) {  // 直播任务
            $live_task_map = Yii::$app->params['live_task_map'] ?? [];
            if (Equipment::isAppOlderThan('6.2.5', '6.2.5') || !isset($live_task_map[$gtype])) {
                throw new HttpException(400, '该任务尚未开放', *********);
            }
            $task = $live_task_map[$gtype];
            // 获取直播每日任务数据
            $user_task = Yii::$app->liveRpc->getLiveDailyTask($user_id);
            $is_finished = self::isFinishedLiveTask($task, $user_task);
            if (!$is_finished) {
                $error_message = self::getLiveTaskErrorMessage($gtype);
                if ($error_message) {
                    throw new HttpException(403, $error_message, 200350101);
                }
            }

            $finished_info_format = $task['finished_info_format'] ?? '';
            $task_name = $task['name'];
            $event_id = $task['event_id'] ?? 0;
            if ($task['reward_type'] === self::TASK_TB_REWARD_TYPE_DIAMOND) {
                $diamond_num = $task['reward_num'];
            } else {
                // 直播任务小鱼干奖励走摸鱼抽奖逻辑
                $random = true;
                $log_type = PointDetailLog::TYPE_TASK_GET_POINT;
            }
            $diamond_random_rule = $task['diamond_random_rule'] ?? null;
            $reward_icon_url = $task['reward_icon_url'] ?? '';
        } elseif ($gtype === self::TASK_TYPE_WECHAT_OFFIACCOUNT) {
            // 关注公众号任务
            $task_info = self::getInEffectWechatOffiaccountTask();
            if (!$task_info) {
                throw new HttpException(400, '该任务尚未开放', *********);
            }
            $task = $task_info['task'];
            $status = MThirdPartyTask::getTaskStatus($user_id, 0, MThirdPartyTask::SCENE_WECHAT_OFFIACCOUNT);
            $log_type = PointDetailLog::TYPE_TASK_WECHAT_OFFIACCOUNT;
            switch ($status) {
                case MThirdPartyTask::TASK_STATUS_ON_GOING:
                    return ['web_url' => $task['web_url'], 'native_url' => $task['native_url']];
                case MThirdPartyTask::TASK_STATUS_REWARDED:
                    // 已完成已领取奖励
                    return '任务已经做完了';
                case MThirdPartyTask::TASK_STATUS_FINISHED:
                    // 已完成但未领取奖励
                    // TODO: 当前仅支持发放小鱼干奖励
                    $random_rule = $task['random_rule'] ?? null;
                    $reward = $this->getRewardPointInfo($random_rule, $task['reward_num'], $task['finished_info_format']);
                    $point_num = $reward['num'];
                    $finished_info = $reward['finished_info'];
                    MThirdPartyTask::updateFinishedTaskStatus($user_id, 0, MThirdPartyTask::SCENE_WECHAT_OFFIACCOUNT);
                    break;
                default:
                    Yii::error('用户' . $user_id . '关注公众号任务状态异常：' . $status, __METHOD__);
                    throw new HttpException(500, '不支持的任务状态');
            }
        } elseif ($gtype >= self::TASK_TYPE_TB) {  // 限时任务
            $older_than_627 = Equipment::isAppOlderThan('6.2.7', '6.2.7');
            $additional_task_list = self::getAdditionalTaskList($older_than_627);
            $found = false;
            $current_task = null;
            foreach ($additional_task_list as $task) {
                if ($gtype === $task['gtype']) {
                    $found = true;
                    $finished_info_format = $task['finished_info_format'];
                    $task_name = $task['name'];
                    $event_id = $task['event_id'] ?? 0;
                    if ($task['reward_type'] === self::TASK_TB_REWARD_TYPE_DIAMOND) {
                        $diamond_num = $task['reward_num'];
                    } else {
                        $point_num = $task['reward_num'];
                    }
                    $diamond_random_rule = $task['diamond_random_rule'] ?? null;
                    $current_task = $task;
                    break;
                }
            }
            // WORKAROUND: iOS ≥ 6.2.7 Android ≥ 6.2.7 到端任务
            if (!$older_than_627 && $found) {
                $is_to_third_party_task = array_key_exists('to_third_party', $current_task) &&
                    $current_task['to_third_party'];
                if ($is_to_third_party_task) {
                    // 兼容用户在是非到端任务的情况下完成后，任务配置被改成了到端任务，此时需要显示已完成
                    if ($redis->get($redis->getLockKey(EARS, $gtype_key, $user_id))) {
                        return '今天任务已经做完了';
                    }
                    $third_party_task_scene = $current_task['scene'];
                    $status = MThirdPartyTask::getTaskStatus($user_id, $today, $third_party_task_scene);
                    switch ($status) {
                        case MThirdPartyTask::TASK_STATUS_ON_GOING:
                            // 首次请求时（去完成），下发拼接 maoer_task_token 的链接
                            $token = MThirdPartyTask::getToken($user_id, $today, $third_party_task_scene);
                            if (!$token) {
                                throw new HttpException(500, '操作过于频繁，请稍后再试');
                            }
                            $task_urls = [
                                'native_url' => $current_task['native_url'],
                                'web_url' => $current_task['web_url'],
                            ];
                            return self::buildThirdPartyUrlQueryToken($task_urls, $token, $third_party_task_scene);
                        case MThirdPartyTask::TASK_STATUS_REWARDED:
                            // 已完成已领取奖励
                            return '今天任务已经做完了';
                        case MThirdPartyTask::TASK_STATUS_FINISHED:
                            // 已完成但未领取奖励的往下执行领取奖励
                            break;
                        default:
                            throw new HttpException(400, '不支持的任务状态');
                    }
                }
            }
            if (Equipment::isAppOlderThan('6.2.3', '6.2.3')) {
                // WORKAROUND: 安卓或 iOS < 6.2.3 版本时，限时任务只能完成淘宝农场任务
                $found = false;
                $old_task_conf = Yii::$app->params['additional_task_older_623'] ?? null;
                if ($old_task_conf && $gtype === self::TASK_TYPE_TB) {
                    $found = true;
                    if ($old_task_conf['reward_type'] === self::TASK_TB_REWARD_TYPE_DIAMOND) {
                        $diamond_num = $old_task_conf['reward_num'];
                    } else {
                        $point_num = $old_task_conf['reward_num'];
                    }
                    $finished_info_format = $old_task_conf['finished_info_format'];
                    $diamond_random_rule = $task['diamond_random_rule'] ?? null;
                    if (Equipment::isAppOlderThan('6.2.2', '6.2.2')) {
                        // WORKAROUND: 安卓或 iOS < 6.2.2 版本时，完成限时任务固定发放小鱼干
                        $diamond_num = 0;
                        $point_num = $old_task_conf['legacy']['reward_num'];
                        $finished_info_format = $old_task_conf['legacy']['finished_info_format'];
                        // 需要单独统计任务进度
                        $gtype_key = $gtype_key . '_old';
                    }
                }
            }
            if (!$found) {
                throw new HttpException(400, '该任务尚未开放', *********);
            }
            $log_type = PointDetailLog::TYPE_TASK_ADDITIONAL;
        } else {
            // 日常任务
            switch ($gtype) {
                case self::TASK_TYPE_GET_POINT:
                    $random = true;
                    $log_type = PointDetailLog::TYPE_TASK_GET_POINT;
                    break;
                case self::TASK_TYPE_TS:
                    $count = (int)MPointFeed::find()
                        ->select('SUM(num) AS num')
                        ->where('user_id = :user_id AND create_time >= :create_time',
                            [':user_id' => $user_id, ':create_time' => $today])
                        ->scalar();
                    if ($count < self::TASK_TS_TIMES_LIMIT) {
                        $msg = sprintf('必须投食 %d 个小鱼干后才能完成本任务，你投食了 %d 个',
                            self::TASK_TS_TIMES_LIMIT, $count);
                        throw new HttpException(403, $msg, 200350101);
                    }
                    $random = true;
                    $log_type = PointDetailLog::TYPE_TASK_TS;
                    break;
                case self::TASK_TYPE_SHARE:
                    $fx_key = $redis->generateKey(SHARE, $user_id, $today);
                    if (($count = $redis->get($fx_key) ?: 0) < self::TASK_SHARE_TIMES_LIMIT) {
                        $msg = sprintf('必须分享 %d 次才可获得小鱼干，你已经分享 %d 次', self::TASK_SHARE_TIMES_LIMIT, $count);
                        throw new HttpException(403, $msg, 200350101);
                    }
                    $point_num = self::TASK_SHARE_POINT;
                    $log_type = PointDetailLog::TYPE_TASK_SHARE;
                    break;
                case self::TASK_TYPE_COMMENT:
                    $lock = $redis->generateKey(KEY_LOCK_USER_COMMENT, $user_id);
                    $count = $redis->get($lock);
                    if ($count < self::TASK_COMMENT_TIMES_LIMIT) {
                        $msg = sprintf('必须发布 %d 条评论才可获得小鱼干，你发布了 %d 条评论', self::TASK_COMMENT_TIMES_LIMIT, $count);
                        throw new HttpException(403, $msg, 200350101);
                    }
                    $point_num = self::TASK_COMMENT_POINT;
                    $log_type = PointDetailLog::TYPE_TASK_COMMENT;
                    break;
                default:
                    throw new HttpException(400, '该任务尚未开放', *********);
            }
        }

        $key = $redis->generateKey(EARS, $gtype_key, $user_id);
        if (!$redis->lockAt($key, $tomorrow)) {
            return '今天任务已经做完了';
        }
        if ($gtype < self::TASK_TYPE_TB || $gtype === self::TASK_TYPE_LIVE_VISIT) {
            [$point_num, $finished_info] = MPointFeed::getTaskRandomPoint($point_num, $random);
        }
        if ($point_num > 0) {
            $user->updateCounters(['point' => $point_num]);
            PointDetailLog::addLog($point_num, $log_type, $user_id);
            $reward_num = $point_num;
        }
        if ($diamond_num > 0) {
            $diamond_num = $this->getRewardDiamondNum($gtype, $diamond_random_rule, $diamond_num);
            $more = ['task_type' => $gtype, 'message' => sprintf('%s任务奖励', $task_name)];
            if ($event_id) {
                $more['event_id'] = $event_id;
            }
            $coin_field = PayAccount::COIN_FIELD_IOS;
            if (RechargeOrder::isIOSGooglePayNewTopupMenuTime()) {
                $coin_field = PayAccount::COIN_FIELD_ANDROID;
            }
            RechargeOrder::batchGenerateCashOrder(TopupMenu::getCustomDiamondNumId(), [$user_id], $diamond_num,
                UserContext::fromUser(Yii::$app->request, Yii::$app->equip), RechargeOrder::TYPE_CASH, $coin_field, $more);
            // 发放钻石成功后，更新到端任务状态
            if ($is_to_third_party_task && in_array($third_party_task_scene, MThirdPartyTask::SCENE_ARR)) {
                MThirdPartyTask::updateFinishedTaskStatus($user_id, $today, $third_party_task_scene);
            }
            // 发放钻石成功后向用户发送系统通知
            $title = '';
            $content = '';
            if ($is_live_task) {
                $title = '直播任务奖励通知';
                $content = sprintf('恭喜您完成每日直播任务，获得【钻石 × %d】！奖励已发放至您的账户中，请注意查收，记得明天再来哦~', $diamond_num);
            } else {
                $title = '活动奖励通知';
                $content = sprintf('恭喜您在【%s】任务中成功获得【钻石 × %d】！奖励已发放至您的账户中，请注意查收。如有疑问可联系客服咨询。', Html::encode($task_name), $diamond_num);
            }
            Yii::$app->tools->sendNotification([
                'user_id' => $user_id,
                'title' => $title,
                'content' => $content,
            ], Tools::SEND_SYS_MSG);
            $reward_num = $diamond_num;
        }
        if ($finished_info_format) {
            $finished_info = sprintf($finished_info_format, $reward_num);
        }
        $return = [
            'info' => $finished_info,
            'nums' => $reward_num
        ];
        if ($reward_icon_url) {
            $return['reward_icon_url'] = $reward_icon_url;
        }
        return $return;
    }

    /**
     * 构建第三方跳转链接带上任务 token
     * 相关文档: https://www.tapd.cn/35612194/prong/stories/view/1135612194004507862
     *
     * @param array $urls 跳转链接（唤起 App scheme 地址和中间页链接地址）
     * @param string $token 猫耳任务 token
     * @param int $scene 任务场景
     * @return array 返回带上了 token 的跳转链接
     * @throws HttpException
     */
    private static function buildThirdPartyUrlQueryToken(array $urls, string $token, int $scene): array
    {
        if (!array_key_exists('native_url', $urls) || !array_key_exists('web_url', $urls)) {
            throw new HttpException(500, '第三方跳转链接配置错误');
        }
        switch ($scene) {
            case MThirdPartyTask::SCENE_BAIDU:
                return [
                    'native_url' => str_replace('!(token)', $token, $urls['native_url']),
                    'web_url' => str_replace('!(token)', $token, $urls['web_url']),
                ];
            case MThirdPartyTask::SCENE_CTRIP:
                $query_param = ['token' => $token];
                return [
                    'native_url' => self::addQueryParamsToUrl($urls['native_url'], $query_param),
                    'web_url' => self::addQueryParamsToUrl($urls['web_url'], $query_param),
                ];
            case MThirdPartyTask::SCENE_BAIDUMAP:
            case MThirdPartyTask::SCENE_YOUKU:
            case MThirdPartyTask::SCENE_QQ_BROWSER:
            case MThirdPartyTask::SCENE_LOFTER:
            case MThirdPartyTask::SCENE_QQ_MUSIC:
            case MThirdPartyTask::SCENE_QUARK:
            case MThirdPartyTask::SCENE_QIDIAN:
                return [
                    'native_url' => str_replace('${token}', $token, $urls['native_url']),
                    'web_url' => str_replace('${token}', $token, $urls['web_url']),
                ];
            case MThirdPartyTask::SCENE_WEIBO:
                // 相关文档：https://info.bilibili.co/pages/viewpage.action?pageId=958111694
                $native_token = $web_token = $token;
                $url_encode_token = urlencode($token);
                if ($token !== $url_encode_token) {
                    $native_token = urlencode($url_encode_token);
                    $web_token = urlencode(urlencode($url_encode_token));
                }
                return [
                    'native_url' => str_replace('${token}', $native_token, $urls['native_url']),
                    'web_url' => str_replace('${token}', $web_token, $urls['web_url']),
                ];
            default:
                throw new HttpException(400, '该任务尚未开放', *********);
        }
    }

    /**
     * 跳转链接支持拼接新参数
     *
     * @param string $url 待拼接的 URL
     * @param array $params 待拼接的参数
     * @return string
     */
    private static function addQueryParamsToUrl(string $url, array $params): string
    {
        $parsed_url = parse_url($url);
        parse_str($parsed_url['query'] ?? '', $query_params);
        $query_params = array_merge($query_params, $params);

        // 重构 URL
        $parsed_url['query'] = http_build_query($query_params);
        return $parsed_url['scheme'] . '://' . $parsed_url['host'] . $parsed_url['path'] . '?' . $parsed_url['query'];
    }

    /**
     * 过滤每日限时任务列表返回值
     *
     * @param array $task 待过滤任务
     * @param bool $older_than_627 当前 App 版本号是否小于 6.2.7 true：< 6.2.7；false：≥ 6.2.7
     * @return array|null 过滤后任务
     */
    private function filterAdditionalTaskResp(array $task, bool $older_than_627 = false) : ?array
    {
        $need_fields = [
            'gtype', 'name', 'info', 'reward_type',
            'icon_url', 'button_text', 'native_url',
            'web_url', 'finish', 'dark_icon_url',
        ];
        if (!$older_than_627) {
            // WORKAROUND: iOS ≥ 6.2.7 Android ≥ 6.2.7 下发是否为到端任务字段
            $need_fields[] = 'to_third_party';
        }

        $filter_task = [];
        foreach ($need_fields as $field) {
            if (!array_key_exists($field, $task)) {
                Yii::error('每日限时任务缺少配置：' . $field . '，配置详情：' . Json::encode($task), __METHOD__);
                return null;
            }
            $filter_task[$field] = $task[$field];
        }
        if (!$older_than_627 && $task['to_third_party']) {
            // WORKAROUND: iOS ≥ 6.2.7 Android ≥ 6.2.7 到端任务不下发跳转链接
            unset($filter_task['native_url'], $filter_task['web_url']);
        }
        return $filter_task;
    }

    /**
     * 过滤每日直播任务列表返回值
     *
     * @param array $tasks 待过滤任务列表
     * @param string $sence 场景
     * @return array 过滤后任务
     */
    private function filterLiveTaskResp(array $tasks, string $sence = ''): array
    {
        // 直播任务需要下发的字段
        $need_fields = ['gtype', 'name', 'reward_type', 'info', 'icon_url', 'dark_icon_url', 'finish', 'url'];
        if ($sence === 'live') {
            $need_fields[] = 'icon_url_scene_live';
        }
        if (!Equipment::isAppOlderThan('6.3.4')) {
            // WORKAROUND: iOS >= 6.3.4 下发 type 字段，< 6.3.4 版本下发的话会导致 iOS 客户端进入任务页闪退
            $need_fields[] = 'type';
        }
        $filter_tasks = [];
        $no_need_send_message_version = Equipment::isAppOlderThan('6.3.4', '6.3.4', '6.2.6');
        foreach ($tasks as $key => $task) {
            if ($no_need_send_message_version && $task['gtype'] === self::TASK_TYPE_LIVE_SEND_MESSAGE) {
                // WORKAROUND: iOS < 6.3.4 Android < 6.3.4 harmony < 6.2.6 不下发直播发消息任务
                continue;
            }
            $filter_task = [];
            foreach ($need_fields as $field) {
                if (!array_key_exists($field, $task)) {
                    Yii::error('每日直播任务缺少配置：' . $field . '，配置详情：' . Json::encode($task), __METHOD__);
                    $filter_task = [];
                    break;
                }
                $filter_task[$field] = $task[$field];
                if ($sence === 'live') {
                    $filter_task['icon_url'] = $task['icon_url_scene_live'];
                    unset($filter_task['icon_url_scene_live']);
                }
            }
            if ($filter_task) {
                // 设置按钮文案
                $finish_status = $task['finish'];
                if ($sence === 'live') {
                    // 在直播间任务半窗场景，使用专用按钮文案
                    $filter_task['button_text'] = $task['button_texts_scene_live'][$finish_status];
                } else {
                    // 在主站任务页场景，使用默认按钮文案
                    $filter_task['button_text'] = $task['button_texts'][$finish_status];
                }
                if (array_key_exists('type', $task) && $task['type'] === self::LIVE_TASK_TYPE_SEND_MESSAGE) {
                    // 直播发消息任务需要显示任务目标值和任务进度
                    $filter_task['name'] .= sprintf('（%d/%d）', min($task['progress'], $task['task_value']),
                        $task['task_value']);
                }
                $filter_tasks[] = $filter_task;
            }
        }
        return $filter_tasks;
    }

    /**
     * @api {get} /person/task-status 日常任务，签到任务状态
     * @apiSampleRequest /person/task-status
     *
     * @apiVersion 0.1.0
     * @apiName task-status
     * @apiGroup person
     *
     * @apiParam (Query) {String} [scene] 任务场景，当传入 live 时只返回直播任务且会根据用户完成状态对返回任务排序，依次下发：已完成、未完成、已领取奖励任务
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "point": 9776,  // 用户当前拥有的小鱼干总量
     *         "banner": {  // 广告条，没有时不下发
     *           "tip": "连签解锁新人专属头像框！了解详情",  // 不包含样式的广告文本
     *           "url": "https://www.test.com/blackboard/bm6w9Tih14rVeq0v.html"  // 广告条跳转链接
     *         },
     *         "tasks": {
     *           "1": {  // 摸鱼
     *             "finish": 1
     *           },
     *           "4": {  // 投食
     *             "finish": 0,  // 是否已完成
     *             "ts": 2  // 投食的小鱼干数量
     *           },
     *           "5": {  // 分享
     *             "finish": 0,
     *             "ts": 0
     *           },
     *           "6": {  // 评论
     *             "finish": 0,
     *             "ts": 2
     *           }
     *         },
     *         "live_task_list": [  // 直播任务列表，固定放在在任务页列表第 2 位之后，未下发时不展示直播任务
     *           {
     *             "gtype": 10,  // 进入直播间任务
     *             "type": 1,  // 直播普通任务
     *             "name": "进入直播间",
     *             "reward_type": 1,  // 奖励类型。1：小鱼干；2：钻石
     *             "info": "每天进入直播间可摸鱼 1 次",  // 任务说明
     *             "icon_url" : "https://abc.png",  // 任务图标
     *             "dark_icon_url": "https://dark.png",  // 夜间模式任务图标
     *             "button_text": "去完成",  // 按钮上展示的文字
     *             "finish": 0,  // 任务完成状态。0：未完成；1：已完成；2：已领取奖励
     *             "url": "missevan://live/9075111"  // 跳转链接。下发直播间链接时，若用户当前正在收听直播，则跳转用户当前收听的直播间，否则跳转链接中的直播间
     *           },
     *           {
     *             "gtype": 11,  // 收听直播时长任务，下发 11（新人）或 12（非新人）
     *             "type": 1,  // 直播普通任务
     *             "name": "【惊喜任务】收听直播",
     *             "reward_type": 2,  // 奖励类型。1：小鱼干；2：钻石
     *             "info": "收听直播满 3min 可随机抽取 1-10 钻石哦~",  // 任务说明
     *             "icon_url": "https://abc.png",  // 任务图标
     *             "dark_icon_url": "https://dark.png",  // 夜间模式任务图标
     *             "button_text": "去完成",  // 按钮上展示的文字
     *             "finish": 0,  // 任务完成状态。0：未完成；1：已完成；2：已领取奖励
     *             "url": "missevan://live/9075111"  // 跳转链接。下发直播间链接时，若用户当前正在收听直播，则跳转用户当前收听的直播间，否则跳转链接中的直播间
     *           },
     *           {
     *             "gtype": 15,  // 去直播间发消息抽钻石
     *             "type": 2,  // 直播发消息任务
     *             "name": "去直播间发消息抽钻石（0/3）",
     *             "reward_type": 2,  // 奖励类型。1：小鱼干；2：钻石
     *             "info": "发送 3 条直播间消息可抽取 1-10 钻",  // 任务说明
     *             "icon_url": "https://abc.png",  // 任务图标
     *             "dark_icon_url": "https://dark.png",  // 夜间模式任务图标
     *             "button_text": "去完成",  // 按钮上展示的文字
     *             "finish": 0,  // 任务完成状态。0：未完成；1：已完成；2：已领取奖励
     *             "url": "missevan://live/9075111"  // 跳转链接。下发直播间链接时，若用户当前正在收听直播，则跳转用户当前收听的直播间，否则跳转链接中的直播间
     *           }
     *         ],
     *         // iOS < 6.2.7 Android < 6.2.7 限时任务列表
     *         "additional_task_list": [  // 限时任务列表，当前无进行中的任务时不下发
     *           {  // 安卓、iOS < 6.2.2 版本时，唤起 native_url 对应的端外应用或打开 web_url 均算做完成该任务；安卓、iOS >= 6.2.2 版本时，唤起 native_url 对应的端外应用才算做完成任务
     *             "gtype": 7,
     *             "name": "淘宝农场",
     *             "reward_type": 2,  // 奖励类型。1：小鱼干，2：钻石
     *             "info": "点击逛逛淘宝农场，必得 10 条小鱼干",  // 任务说明
     *             "icon_url": "https://abc.png",  // 任务图标
     *             "dark_icon_url": "https://dark.png",  // 夜间模式任务图标
     *             "button_text": "去农场",  // 按钮上展示的文字
     *             "finish": 0,  // 任务完成状态。0：未完成；1：已完成
     *             "native_url": "tbopen://m.taobao.com/tbopen/index.html?foo=bar",  // 唤起端外应用的链接，不下发时不跳转
     *             "web_url": "https://market.m.test.com/xx?foo=bar"  // 打开的网页链接。当下发了 native_url 时，优先唤起 native_url 的应用，不下发时不跳转
     *           }
     *         ],
     *         // iOS ≥ 6.2.7 Android ≥ 6.2.7
     *         "additional_task_list": [  // 限时任务列表
     *           {
     *             "gtype": 7,
     *             "name": "淘宝农场",
     *             "reward_type": 2,  // 奖励类型。1：小鱼干，2：钻石
     *             "info": "点击逛逛淘宝农场，必得 10 条小鱼干",  // 任务说明
     *             "icon_url": "https://abc.png",  // 任务图标
     *             "dark_icon_url": "https://dark.png",  // 夜间模式任务图标
     *             "button_text": "去农场",  // 按钮上展示的文字
     *             "finish": 0,  // 任务完成状态。0：未完成；1：已完成未领取奖励；2：已完成已领取奖励
     *             "to_third_party": true // 是否为到端任务，true：是；false：否
     *           },
     *           {
     *             "gtype": 8,
     *             "name": "携程",
     *             "reward_type": 2,  // 奖励类型。1：小鱼干，2：钻石
     *             "info": "点击逛逛淘宝农场，必得 10 条小鱼干",  // 任务说明
     *             "icon_url": "https://abc.png",  // 任务图标
     *             "dark_icon_url": "https://dark.png",  // 夜间模式任务图标
     *             "button_text": "去农场",  // 按钮上展示的文字
     *             "finish": 0,  // 任务完成状态。0：未完成；1：已完成
     *             "to_third_party": false // 是否为到端任务，true：是；false：否
     *             "native_url": "tbopen://m.taobao.com/tbopen/index.html?foo=bar",  // 唤起端外应用的链接，不下发时不跳转（非到端任务下发）
     *             "web_url": "https://market.m.test.com/xx?foo=bar"  // 打开的网页链接。当下发了 native_url 时，优先唤起 native_url 的应用，不下发时不跳转（非到端任务下发）
     *           }
     *         ],
     *         "wechat_offiaccount_task": {  // 微信公众号任务
     *           "gtype": 19, // 关注公众号
     *           "name": "关注猫耳FM公众号",
     *           "reward_type": 1,  // 奖励类型。1：小鱼干，2：钻石
     *           "info": "关注猫耳FM公众号，获得超级多的奖励",  // 任务说明
     *           "icon_url": "https://abc.png",  // 任务图标
     *           "dark_icon_url": "https://dark.png",  // 夜间模式任务图标
     *           "button_text": "去完成",  // 按钮上展示的文字
     *           "finish": 0,  // 任务完成状态。0：未完成；1：已完成未领取奖励；2：已完成已领取奖励
     *           "to_third_party": false, // 是否为到端任务，true：是；false：否
     *           "web_url": "https://market.m.test.com/xx?foo=bar"  // 打开的网页链接。当下发了 native_url 时，优先唤起 native_url 的应用，不下发时不跳转（非到端任务下发）
     *         }
     *       }
     *     }
     */
    public function actionTaskStatus(string $scene = '')
    {
        $today = strtotime(date('Ymd'));
        $user_id = Yii::$app->user->id;
        $redis = Yii::$app->redis;

        $normal_task_list = [self::TASK_TYPE_GET_POINT, self::TASK_TYPE_TS, self::TASK_TYPE_SHARE, self::TASK_TYPE_COMMENT];
        $task_keys = $normal_task_keys = array_map(function ($gtype) use ($redis, $user_id) {
            return $redis->getLockKey(EARS, $gtype, $user_id);
        }, $normal_task_list);
        $older_than_627 = Equipment::isAppOlderThan('6.2.7', '6.2.7');
        $additional_task_list = self::getAdditionalTaskList($older_than_627);
        $has_additional_task_list = !empty($additional_task_list);
        if ($has_additional_task_list) {
            $additional_task_keys = array_map(function ($gtype) use ($redis, $user_id) {
                return $redis->getLockKey(EARS, $gtype, $user_id);
            }, array_column($additional_task_list, 'gtype'));
            $task_keys = array_merge($normal_task_keys, $additional_task_keys);
        }

        // 直播任务模块
        $live_task_list = [];  // 直播任务列表
        $user_task = [];  // 用户参与直播任务详情
        $live_task_map = Yii::$app->params['live_task_map'] ?? null;
        if ($live_task_map) {
            // 获取直播每日任务数据
            $user_task = Yii::$app->liveRpc->getLiveDailyTask($user_id);
            // 根据用户是否为新用户，下发对应的任务
            if ($user_task['is_new']) {
                // 直播新用户去掉任务：收听直播时长任务 - 非新人
                unset($live_task_map[self::TASK_TYPE_LIVE_LISTEN]);
            } else {
                // 非直播新用户去掉任务：收听直播时长任务 - 新人专享、在直播间发消息抽钻石 - 新人专享
                unset($live_task_map[self::TASK_TYPE_LIVE_LISTEN_NEW_USER], $live_task_map[self::TASK_TYPE_LIVE_SEND_MESSAGE]);
            }
            $live_task_list = array_values($live_task_map);
            // 获取直播任务完成进度 key
            $live_task_keys = array_map(function ($gtype) use ($redis, $user_id) {
                return $redis->getLockKey(EARS, $gtype, $user_id);
            }, array_keys($live_task_map));
            $task_keys = array_merge($task_keys, $live_task_keys);
        }
        $old_task_conf = Yii::$app->params['additional_task_older_623'] ?? null;
        $older_than_623 = Equipment::isAppOlderThan('6.2.3', '6.2.3');
        $older_than_622 = Equipment::isAppOlderThan('6.2.2', '6.2.2');
        if ($older_than_623 && $old_task_conf) {
            // WORKAROUND: 安卓或 iOS < 6.2.3 版本时，只下发淘宝农场限时任务
            $gtype_key = (string)self::TASK_TYPE_TB;
            if ($older_than_622) {
                // WORKAROUND: 安卓及 iOS < 6.2.2 版本完成淘宝农场任务仅支持发放小鱼干，需要单独统计任务进度
                $gtype_key = $gtype_key . '_old';
            }
            $older_task_key = $redis->getLockKey(EARS, $gtype_key, $user_id);
            $task_keys[] = $older_task_key;
        }
        $finish = [];
        $finish_status_list = array_map(function ($v) {
            return $v ? 1 : 0;
        }, $redis->mget($task_keys));
        // 普通任务完成状态
        foreach ($normal_task_list as $key => $task_key) {
            $finish[$task_key]['finish'] = $finish_status_list[$key];
        }
        $normal_task_count = count($normal_task_list);
        // 限时任务完成状态
        foreach ($additional_task_list as $key => $task) {
            $additional_finish = $finish_status_list[$key + $normal_task_count];
            if (Equipment::isAppVersion(Equipment::iOS, '6.2.5') &&
                    $task['gtype'] === self::TASK_TYPE_CTRIP && $additional_finish) {
                // WORKAROUND: iOS = 6.2.5 完成携程任务时下发已领取奖励状态
                $additional_finish = self::TASK_AWARDED;
                $additional_task_list[$key]['button_text'] = '已完成';
            } elseif (!$older_than_627 && $task['to_third_party']) {
                // WORKAROUND: iOS ≥ 6.2.7 Android ≥ 6.2.7 到端任务下发任务状态
                // 任务发起时间是当天 0 点
                $task_time = strtotime('today');
                if ($additional_finish) {
                    $additional_finish = MThirdPartyTask::TASK_STATUS_REWARDED;
                } else {
                    $additional_finish = MThirdPartyTask::getTaskStatus($user_id, $task_time, $task['scene']);
                }
            }
            $additional_task_list[$key]['button_text'] = $task['button_texts'][$additional_finish];
            $additional_task_list[$key]['finish'] = $additional_finish;
        }
        $additional_task_count = count($additional_task_list);
        // 直播任务完成状态
        foreach ($live_task_list as $key => $task) {
            $gtype = $task['gtype'];
            if (array_key_exists('type', $task) && $task['type'] === self::LIVE_TASK_TYPE_SEND_MESSAGE) {
                $live_task_list[$key]['progress'] = $user_task['today_message_count'] ?? 0;
            }
            $live_finish = self::TASK_UNFINISHED;
            if ($finish_status_list[$key + $normal_task_count + $additional_task_count]) {
                // 用户已领取直播任务奖励
                $live_finish = self::TASK_AWARDED;
            } elseif (self::isFinishedLiveTask($task, $user_task)) {
                $live_finish = self::TASK_FINISHED;
            }
            $live_task_list[$key]['finish'] = $live_finish;
            $live_task_list[$key]['url'] = $user_task['open_room_url'];
        }
        if ($scene === 'live') {
            // 直播业务中仅需要返回直播任务，且需要按照用户完成任务情况排序
            $live_task_list_map = ArrayHelper::index($live_task_list, null, 'finish');
            $task_unfinished = $live_task_list_map[self::TASK_UNFINISHED] ?? [];
            $task_finished = $live_task_list_map[self::TASK_FINISHED] ?? [];
            $task_awarded = $live_task_list_map[self::TASK_AWARDED] ?? [];
            $sort_live_task_list = array_merge($task_finished, $task_unfinished, $task_awarded);
            return ['live_task_list' => self::filterLiveTaskResp($sort_live_task_list, $scene)];
        }
        if (!$finish[self::TASK_TYPE_TS]['finish']) {
            $finish[self::TASK_TYPE_TS]['ts'] = (int)MPointFeed::find()
                ->select('SUM(num) AS num')
                ->where('user_id = :user_id AND create_time >= :create_time',
                    [':user_id' => $user_id, ':create_time' => $today])->scalar();
        }
        if (!$finish[self::TASK_TYPE_SHARE]['finish']) {
            $key_fx = $redis->generateKey(SHARE, $user_id, $today);
            $finish[self::TASK_TYPE_SHARE]['ts'] = (int)$redis->get($key_fx);
        }
        if (!$finish[self::TASK_TYPE_COMMENT]['finish']) {
            $key_comment = $redis->generateKey(KEY_LOCK_USER_COMMENT, $user_id);
            $finish[self::TASK_TYPE_COMMENT]['ts'] = (int)$redis->get($key_comment);
        }
        if (Equipment::isAppOlderThan('6.1.4', '6.1.4')) {
            // WORKAROUND: iOS 及 Android 低于 6.1.4 时只下发任务进度信息
            return $finish;
        }
        $point = (int)Mowangskuser::find()->select('point')
            ->where(['id' => $user_id])
            ->scalar();
        $return = [
            'point' => $point,
            'tasks' => $finish,
        ];
        if ($live_task_map) {
            $return['live_task_list'] = self::filterLiveTaskResp($live_task_list);
        }

        $resp_additional_task_list = [];
        if ($has_additional_task_list) {
            foreach ($additional_task_list as $key => $task) {
                $task = $this->filterAdditionalTaskResp($task, $older_than_627);
                if ($task) {
                    $resp_additional_task_list[] = $task;
                }
            }
        }
        if ($older_than_623) {
            $resp_additional_task_list = [];
            // WORKAROUND: 安卓或 iOS < 6.2.3 版本时，只允许下发淘宝农场限时任务
            if ($old_task_conf) {
                $old_task_conf['finish'] = end($finish_status_list);  // 老版本任务完成状态为列表中的最后一个 key
                if ($older_than_622) {
                    // WORKAROUND: 安卓或 iOS < 6.2.2 版本时，淘宝农场下发旧的任务说明、任务名、按钮文案
                    // 此处未更新 reward_num、finished_info 字段是因为目前不需要下发，当需要下发时必须更新后再下发
                    $old_task_conf['info'] = $old_task_conf['legacy']['info'];
                    $old_task_conf['name'] = $old_task_conf['legacy']['name'];
                    $old_task_conf['button_text'] = $old_task_conf['legacy']['button_text'];
                    // 固定发放小鱼干
                    $old_task_conf['reward_type'] = self::TASK_TB_REWARD_TYPE_POINT;
                }
                $old_task_conf = $this->filterAdditionalTaskResp($old_task_conf, true);
                if ($old_task_conf) {
                    $resp_additional_task_list = [$old_task_conf];
                }
            }
        }
        $has_additional_task_list = !empty($resp_additional_task_list);
        if ($has_additional_task_list) {
            $return['additional_task_list'] = $resp_additional_task_list;
        }
        $wechat_offiaccount_task = self::getWechatOffiaccountTaskResp($user_id);
        if ($wechat_offiaccount_task) {
            if ($wechat_offiaccount_task['show_alone']) {
                $return['wechat_offiaccount_task'] = $wechat_offiaccount_task['task'];
            } else {
                // WORKAROUND: 低版本客户端下发在限时任务列表中
                if ($has_additional_task_list) {
                    array_unshift($return['additional_task_list'], $wechat_offiaccount_task['task']);
                } else {
                    $return['additional_task_list'] = [$wechat_offiaccount_task['task']];
                }
            }
        }
        $banner = Yii::$app->params['point_task_banner'] ?? null;
        if ($banner) {
            $return['banner'] = Yii::$app->params['point_task_banner'];
        }
        return $return;
    }

    /**
     * 获取微信公众号任务配置信息
     *
     * @return ?array
     */
    private static function getInEffectWechatOffiaccountTask(): ?array
    {
        $task = Yii::$app->params['wechat_offiaccount_task'] ?? null;
        if (!$task) {
            return null;
        }
        $now = $_SERVER['REQUEST_TIME'];
        $in_effect = $task && key_exists('start_time', $task) && $task['start_time'] <= $now
            && key_exists('end_time', $task) && ($task['end_time'] === 0 || $task['end_time'] > $now);
        if (!$in_effect) {
            return null;
        }
        $show_in_list_version = $task['support_version']['show_in_additional_task_list'];
        if (Equipment::isAppOlderThan($show_in_list_version['ios'], $show_in_list_version['android'])) {
            return null;
        }
        $show_alone = $task['support_version']['show_alone'];
        if (Equipment::isAppOlderThan($show_alone['ios'], $show_alone['android'])) {
            return ['show_alone' => false, 'task' => $task];
        }
        return ['show_alone' => true, 'task' => $task];
    }

    /**
     * 获取微信公众号任务返回信息
     *
     * @param int $user_id
     * @return array|null
     */
    private static function getWechatOffiaccountTaskResp(int $user_id) : ?array
    {
        $task_info = self::getInEffectWechatOffiaccountTask();
        if (!$task_info) {
            return null;
        }
        $task = $task_info['task'];
        $status = MThirdPartyTask::getTaskStatus($user_id, 0, MThirdPartyTask::SCENE_WECHAT_OFFIACCOUNT);
        if ($status === MThirdPartyTask::TASK_STATUS_REWARDED) {
            // 一次性任务，已领奖就不再下发
            return null;
        }
        $need_fields = [
            'gtype', 'name', 'info', 'reward_type', 'icon_url', 'web_url', 'dark_icon_url', 'to_third_party'
        ];
        $filter_task = [];
        foreach ($need_fields as $field) {
            if (!array_key_exists($field, $task)) {
                Yii::error('微信公众号任务缺少配置：' . $field . '，配置详情：' . Json::encode($task), __METHOD__);
                return null;
            }
            $filter_task[$field] = $task[$field];
        }
        $filter_task['button_text'] = $task['button_texts'][$status];
        $filter_task['finish'] = $status;
        return ['show_alone' => $task_info['show_alone'], 'task' => $filter_task];
    }

    /**
     * 获取额外（引流）任务配置列表
     *
     * @param bool $older_than_627 当前 App 版本号是否小于 6.2.7（不支持到端任务）
     * @return array 额外（引流）任务配置列表
     */
    private static function getAdditionalTaskList($older_than_627 = false)
    {
        $additional_task_list = Yii::$app->params['additional_task_list'] ?? [];
        if (empty($additional_task_list)) {
            return [];
        }
        if (Equipment::isAppOlderThan('6.2.3', '6.2.3')) {
            // WORKAROUND: iOS 及 Android 低于 6.2.3 时不下发额外任务，这些版本单独获取 additional_task_older_623 配置
            return [];
        }
        $now = $_SERVER['REQUEST_TIME'];
        $os = Yii::$app->equip->getOs();
        return array_reduce($additional_task_list, function ($ret, $item) use ($now, $os, $older_than_627) {
            if ($now < $item['start_time'] || ($item['end_time'] !== 0 && $now >= $item['end_time'])) {
                return $ret;
            }
            if (Equipment::isAppOlderThan($item['support_version']['ios'], $item['support_version']['android'])) {
                return $ret;
            }
            if (is_array($item['native_url'])) {
                $item['native_url'] = $os === Equipment::iOS ? $item['native_url']['ios'] : $item['native_url']['android'];
            }
            if ($older_than_627 && isset($item['legacy'])) {
                $item = array_merge($item, $item['legacy']);
            }
            $ret[] = $item;
            return $ret;
        }, []);
    }

    /**
     * @api {get} /person/sign-history 最近一个月签到记录
     *
     * @apiDescription 本接口返回的时间信息统一以北京时间为准；\
     * 客户端展示的补签日历、漏签天数由 ts 和 history_data 计算，注意应以北京时间计算和展示
     * @apiVersion 0.1.0
     * @apiName sign-history
     * @apiGroup person
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "ts": 1669213925,  // 当前时间戳
     *         "continuous_sign_days": 208,  // 连续签到天数
     *         "patch_required_point": 30,  // 补签需要花费的小鱼干数量
     *         "reward_point": 3,  // 补签成功后奖励的小鱼干数量
     *         "user_point": 9822,  // 用户当前小鱼干数量
     *         "history_data": 9762  // 转为二进制后，从 1 位依次为今天至 30 天前共 31 天的签到记录，1: 已签到；0: 未签到
     *       }
     *     }
     *
     * @apiErrorExample Error-Response:
     *     {
     *       "success": false,
     *       "code": 100010006,
     *       "info": "需要登录"
     *     }
     */
    public function actionSignHistory()
    {
        $user_id = (int)Yii::$app->user->id;
        $point = (int)Mowangskuser::find()->select('point')->where(['id' => $user_id])->scalar();
        $user_sign = UserAddendum::getUserSign($user_id, true);
        return [
            'ts' => $_SERVER['REQUEST_TIME'],
            'continuous_sign_days' => $user_sign['current_days'],
            'patch_required_point' => PointDetailLog::POINT_TASK_PATCH_SIGN_REQUIRED,
            'reward_point' => PointDetailLog::POINT_TASK_SIGN,
            'user_point' => $point,
            'history_data' => $user_sign['history_data'],
        ];
    }

    /**
     * @api {post} /person/sign 签到（补签）
     *
     * @apiDescription Android >= 5.7.7, iOS >= 4.9.2 调用此接口签到；\
     * 本接口参数和返回的时间信息统一以北京时间为准；\
     * 补签成功后，客户端展示的补签日历、漏签天数由 ts 和 history_data 计算，注意应以北京时间计算和展示
     * @apiVersion 0.1.0
     * @apiName sign
     * @apiGroup person
     *
     * @apiParam (Query) {number=1} [type] 签到类型，签到时不传此参数，补签传 1
     * @apiParam {String} [sign_date] 需补签的日期，签到时不传，补签时直接对应补签面板上选中的日期 e.g. 2022-11-27
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "ts": 1669213925,  // 当前时间戳
     *         "continuous_sign_days": 208,  // 连续签到天数
     *         "reward_point": 3,  // 补签成功后奖励的小鱼干数量
     *         "user_point": 9722,  // 用户当前小鱼干数量
     *         "history_data": 761,  // 转为二进制后，从 1 位依次为今天至 30 天前共 31 天的签到记录，1: 已签到；0: 未签到
     *         "msg": "签到成功"  // 或 '补签成功'
     *       }
     *     }
     *
     * @apiErrorExample Error-Response:
     *     {
     *       "success": false,
     *       "code": 200360104,
     *       "info": "小鱼干余额不足 T_T"
     *     }
     */
    public function actionSign()
    {
        if (Equipment::isAppOlderThan('4.9.2', '5.7.7')) {
            throw new HttpException(400, '非法请求');
        }
        $type = (int)Yii::$app->request->post('type');
        if (!in_array($type, [0, 1])) {
            throw new HttpException(400, '签到类型错误');
        }
        $sign_type = $type ? UserSignHistory::TYPE_PATCH_SIGN : UserSignHistory::TYPE_SIGN;
        $today = strtotime('today', $_SERVER['REQUEST_TIME']);
        $is_patch_sign = $sign_type === UserSignHistory::TYPE_PATCH_SIGN;
        if ($is_patch_sign) {
            $sign_date = trim(Yii::$app->request->post('sign_date'));
            $sign_day = explode('-', $sign_date);
            // TODO: 后续判断日期格式是否正确需要在 php-utils 中增加支持
            if (3 !== count($sign_day) || !checkdate($sign_day[1], $sign_day[2], $sign_day[0])) {
                throw new HttpException(400, '签到日期错误');
            }
            $sign_date = strtotime($sign_date);
            if ($sign_date >= $today || $today - $sign_date > THIRTY_DAYS) {
                throw new HttpException(400, '当前日期不在可补签范围内');
            }
            // 获取用户注册当日零点时间
            $register_day_time = strtotime('today', Yii::$app->user->registerAt);
            if ($sign_date < $register_day_time) {
                throw new HttpException(400, '无法补签注册日之前的日期哦 >_<');
            }
        } else {
            $sign_date = $today;
        }
        $user_id = Yii::$app->user->id;
        $is_signed = UserSignHistory::isSigned($user_id, $sign_date);
        if ($is_signed) {
            throw new HttpException(400, '当天已经签到');
        }
        $user = Mowangskuser::find()
            ->select('id, point')
            ->where(['id' => $user_id])
            ->one();
        if ($is_patch_sign && $user->point < PointDetailLog::POINT_TASK_PATCH_SIGN_REQUIRED) {
            throw new HttpException(403, '小鱼干余额不足 T_T', 200360104);
        }
        $result = $this->sign($user, $sign_date, $sign_type);
        if (!$result) {
            $msg = $is_patch_sign ? '补签失败' : '签到失败';
            throw new HttpException(400, $msg);
        }
        return $result;
    }

    /**
     * 签到
     *
     * @param Mowangskuser $user 用户信息
     * @param int $sign_date 签到（补签）日期当天零点时间戳，单位：秒
     * @param int $sign_type 签到类型
     * @return ?array 签到成功后用户最新签到信息，失败时返回 null
     */
    private function sign(Mowangskuser $user, int $sign_date, int $sign_type): ?array
    {
        $is_patch_sign = $sign_type === UserSignHistory::TYPE_PATCH_SIGN;
        if (!$is_patch_sign) {
            // 若为签到，需要使用 redis 加锁
            $tomorrow = strtotime('tomorrow');
            $redis = Yii::$app->redis;
            $lock_key = $redis->generateKey(TASK, $user->id);
            if (!$redis->lockAt($lock_key, $tomorrow)) {
                return null;
            }
        }
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $point = PointDetailLog::POINT_TASK_SIGN;
            $point = $is_patch_sign ? $point - PointDetailLog::POINT_TASK_PATCH_SIGN_REQUIRED : $point;
            $user->updateCounters(['point' => $point]);
            $sign_history_list = UserSignHistory::getUserSignHistoryList($user->id);
            // 将所签到（补签）日期写进签到记录
            $sign_history_list[] = $sign_date;
            $user_continuous_sign = UserAddendum::renewSign($user->id, $sign_history_list);
            UserSignHistory::addSignHistory($user->id, $sign_date, $sign_type);
            $transaction->commit();
        } catch (Exception $e) {
            if (!MUtils2::isUniqueError($e, UserSignHistory::getDb())) {
                if (!$is_patch_sign) {
                    // 签到写库失败时，需要解锁避免用户无法重新签到
                    $redis->unlock($lock_key);
                }
                Yii::error('写入用户签到信息出错：' . $e->getMessage(), __METHOD__);
            }
            $transaction->rollBack();
            return null;
        }
        $task_type = $is_patch_sign ? PointDetailLog::TYPE_TASK_PATCH_SIGN : PointDetailLog::TYPE_TASK_SIGN;
        PointDetailLog::addLog($point, $task_type, $user->id);
        $user_sign = UserAddendum::getUserSign($user->id, true, $user_continuous_sign, $sign_history_list);
        // WORKAROUND: 活动结束后可删除此处调用
        // 十周年钻石折扣包活动连续签到达 3 天后发放活动奖品
        MEvent::exchange10thAnniversaryPrize($user->id, $user_sign['current_days'], $sign_date);
        // 发放新人头像框（出错时不会抛出异常）
        MAvatarFrame::sendNewUserAvatarFrame($user->id, Yii::$app->user->registerAt, $user_sign['current_days']);
        return [
            'ts' => $_SERVER['REQUEST_TIME'],
            'continuous_sign_days' => $user_sign['current_days'],
            'patch_required_point' => PointDetailLog::POINT_TASK_PATCH_SIGN_REQUIRED,
            'reward_point' => PointDetailLog::POINT_TASK_SIGN,
            'user_point' => $user->point,
            'history_data' => $user_sign['history_data'],
            'msg' => $is_patch_sign ? '补签成功' : '签到成功',
        ];
    }

    /**
     * @api {get} /person/get-user-sound{?user_id,type,sort,page,page_size} 根据用户 ID 获取单音
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/get-user-sound
     * @apiSampleRequest /person/get-user-sound
     * @apiDescription 根据用户 ID 获取单音
     *
     * @apiVersion 0.1.0
     * @apiName get-user-sound
     * @apiGroup person
     *
     * @apiParam (Query 参数) {Number} user_id 用户 ID
     * @apiParam (Query 参数) {Number} [type=0] 音频类型 0：所有类型音频，1：普通音频，3：直播回放
     * @apiParam (Query 参数) {Number} [sort=1] 排序方式 1：最新，2：最热
     * @apiParam (Query 参数) {Number} [page=1] 页数
     * @apiParam (Query 参数) {Number} [page_size=20] 每页个数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [{
     *           "id": 5240511,
     *           "create_time": 1502694707,
     *           "duration": 178686,
     *           "soundstr": "小树2",
     *           "user_id": 346286,
     *           "username": "InVinCiblezz",
     *           "cover_image": "201711/23/eb84cf274a12d10aa82a00602bcca61f195706.jpg",
     *           "intro": "<p>小树2</p>",
     *           "downtimes": 0,
     *           "comment_count": 0,
     *           "comments_count": 11,
     *           "sub_comments_count": 3,
     *           "uptimes": 9,
     *           "view_count": 921,
     *           "point": 10,
     *           "download": 0,
     *           "pay_type": 1,
     *           "checked": 1,
     *           "all_comments": 14,
     *           "comments_num": 14,
     *           "front_cover": "https://static.missevan.com/coversmini/201711/23/eb84cf2742bcca61f195706.jpg",
     *           "liked": 0,
     *           "collected": 0,
     *           "followed": 0,
     *           "authenticated": 0,
     *           "confirm": 0,
     *           "iconurl": "https://static.missevan.com/profile/icon01.png",
     *           "need_pay": 1,
     *           "price": 0
     *         }],
     *         "pagination": {
     *           "p": 1,
     *           "count": "43",
     *           "maxpage": 3,
     *           "pagesize": 18
     *         }
     *       }
     *     }
     *
     */
    public function actionGetUserSound(int $user_id, int $type = MSound::USER_SOUND_TYPE_All,
            int $sort = MSound::SORT_NEW, int $page_size = PAGE_SIZE_20)
    {
        self::canViewUser($user_id);
        return MSound::getUserSound($type, $user_id, $sort, $page_size);
    }

    /**
     * @api {get} /person/get-user-like 根据用户 ID 获取点赞单音
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/get-user-like
     * @apiSampleRequest /person/get-user-like
     * @apiDescription 根据用户 ID 获取点赞单音
     *
     * @apiVersion 0.1.0
     * @apiName get-user-like
     * @apiGroup person
     *
     * @apiParam {Number} user_id 用户 ID
     * @apiParam {Number} [page=1] 页数
     * @apiParam {Number} [page_size=20] 每页个数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [{
     *           "id": 14429,
     *           "create_time": 1412098551,
     *           "duration": 2104870,
     *           "soundstr": "让我们一起写作业",
     *           "user_id": 59978,
     *           "username": "麻欧二哲",
     *           "cover_image": "201410/01/98e91603847b696edc852b3bfa9acce5023328.jpg",
     *           "intro": "",
     *           "downtimes": 8,
     *           "soundurl": "201410/01/434b6cd416179f3ee6e1cd32c099d9cb013548.mp3",
     *           "comment_count": 77,
     *           "comments_count": 0,
     *           "sub_comments_count": 0,
     *           "uptimes": 17,
     *           "view_count": 796,
     *           "point": 11,
     *           "download": 0,
     *           "pay_type": 0,
     *           "checked": 1,
     *           "all_comments": 77,
     *           "comments_num": 0,
     *           "front_cover": "https://static.missevan.com/coversmini/201410/01/98e912b3bfa9acce5023328.jpg",
     *           "liked": 0,
     *           "collected": 0,
     *           "followed": 0,
     *           "authenticated": 0,
     *           "confirm": 0,
     *           "iconurl": "https://static.missevan.com/profile/icon01.png",
     *           "need_pay": 0,
     *           "price": 0,
     *           "episode_vip": 2  // 会员单集状态。0：非会员单集；1：会员剧下的试听单集；2：会员剧下的非试听单集。音频不属于剧集时，不返回该字段
     *         }],
     *         "pagination": {
     *           "p": 1,
     *           "count": "8",
     *           "maxpage": 1,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     *
     */
    public function actionGetUserLike(int $user_id, int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        // 判断是否能访问该用户的个人主页
        self::canViewUser($user_id);
        $view_user_id = $user_id;
        $user_id = (int)Yii::$app->user->id;
        // WORKAROUND: 和音单同步开放显示给其他用户（等音单审核后台投入使用时），之后可删除此判断
        if ($view_user_id !== $user_id) {
            return ReturnModel::empty($page, $page_size);
        }
        return MSound::getUserSound(MSound::USER_SOUND_TYPE_LIKED, $user_id, MSound::SORT_NEW, $page_size);
    }

    /**
     * @api {get} /person/get-user-album{?user_id} 根据用户 ID 获取用户自建音单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/get-user-album
     *
     * @apiVersion 0.1.0
     * @apiName get-user-album
     * @apiGroup person
     *
     * @apiParam (Query) {Number} user_id 用户 ID
     * @apiParam (Query) {Number} [page=1] 页数（iOS < 4.7.8 Android < 5.6.7 时使用）
     * @apiParam (Query) {Number} [page_size=20] 每页个数（iOS < 4.7.8 Android < 5.6.7 时使用）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response: iOS < 4.7.8 Android < 5.6.7 分页
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [
     *           {
     *             "username": "InVinCiblezz",
     *             "id": 146017,
     *             "title": "老司机",
     *             "music_count": 9,
     *             "collected": 0,  // 当前登录用户是否收藏该音单 0：未收藏，1：已收藏（游客状态不返回该字段）
     *             "front_cover": "https://static.missevan.com/coversmini/201507/20/77fa8e19b37461a19192935.jpg",
     *             "is_private": false,  // 是否为私密音单 true：是，false：否
     *             "sort": -2097152
     *           },
     *           {
     *             "username": "InVinCiblezz",
     *             "id": 3043673,
     *             "title": "测试音单",
     *             "music_count": 12,
     *             "collected": 1,
     *             "front_cover": "http://static-test.missevan.com/coversmini/private.png",
     *             "is_private": true,
     *             "sort": 0
     *           }
     *         ],
     *         "pagination": {
     *           "p": 1,
     *           "count": "43",
     *           "maxpage": 3,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     * @apiSuccessExample Success-Response: iOS >= 4.7.8 Android >= 5.6.7 不分页
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "data": [
     *           {
     *             "username": "InVinCiblezz",
     *             "id": 146017,
     *             "title": "老司机",
     *             "music_count": 9,
     *             "collected": 0,  // 当前登录用户是否收藏该音单 0：未收藏，1：已收藏（游客状态不返回该字段）
     *             "front_cover": "https://static.missevan.com/coversmini/201507/20/77fa8e19b37461a19192935.jpg",
     *             "is_private": false,  // 是否为私密音单 true：是，false：否
     *             "sort": -2097152
     *           },
     *           {
     *             "username": "InVinCiblezz",
     *             "id": 3043673,
     *             "title": "测试音单",
     *             "music_count": 12,
     *             "collected": 1,
     *             "front_cover": "http://static-test.missevan.com/albumcover/private.png",
     *             "is_private": true,
     *             "sort": 0
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionGetUserAlbum(int $user_id, int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        // 被访问者 ID
        $view_user_id = $user_id;
        // 判断是否能访问该用户的个人主页
        self::canViewUser($view_user_id);
        // 访问者 ID
        $user_id = (int)Yii::$app->user->id;
        // WORKAROUND: iOS < 4.7.8 Android < 5.6.7 获取用户音单列表需要分页
        if (Equipment::isAppOlderThan('4.7.8', '5.6.7')) {
            $pagination_params = PaginationParams::process($page, $page_size);
            return MAlbum::getUserAlbum($view_user_id, $user_id, true, $pagination_params->page_size);
        }
        $data = MAlbum::getUserAlbum($view_user_id, $user_id);
        return ['data' => $data];
    }

    /**
     * @api {get} /person/get-user-all-album 根据用户id获取全部音单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/get-user-all-album
     * @apiSampleRequest /person/get-user-all-album
     * @apiDescription  /mobile/person/personInfos?type=2 param_id => user_id param_id2 => sound_id
     *
     * @apiVersion 0.1.0
     * @apiName get-user-all-album
     * @apiGroup person
     *
     * @apiParam {number} user_id 用户id
     * @apiParam {number=0} [sound_id=0] 用于判断是否已加入音单
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info":
     *           [...]
     *     }
     *
     */
    public function actionGetUserAllAlbum(int $user_id, int $sound_id = 0)
    {
        $albums = MAlbum::getUserAllAlbum($user_id, $sound_id);
        return $albums;
    }

    /**
     * @api {get} /person/get-user-attention 获取用户关注/粉丝
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/get-user-attention
     * @apiSampleRequest /person/get-user-attention
     *
     * @apiVersion 0.1.0
     * @apiName get-user-attention
     * @apiGroup person
     *
     * @apiParam {Number} user_id 用户 ID
     * @apiParam {number=0,1} [type=0] 获取的用户类型 0：获取关注用户；1：获取粉丝用户
     * @apiParam {Number} [page=1] 页数
     * @apiParam {Number} [page_size=20] 每页个数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info": {
     *         "Datas": [{
     *           "id": 346286,
     *           "username": "InVinCiblezz",
     *           "userintro": null,
     *           "fansnum": 9,
     *           "soundnum": "9",
     *           "authenticated": 0,
     *           "iconurl": "https://static.missevan.com/coversmini/201507/20/77fa8e19b37461a19192935.jpg",
     *           "attention": 0,  // 关注状态 0：未关注；1：已关注；2：粉丝；3：已互粉
     *           "live_status": 1,  // 0：没有开启房间；1：房间开启；当用户有直播间且开播时才返回该字段
     *           "room_id": 1  // 当用户有直播间且开播时才返回该字段
     *         }],
     *         "pagination": {
     *           "p": 1,
     *           "count": "43",
     *           "maxpage": 3,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     */
    public function actionGetUserAttention(int $user_id, int $type = 0, int $page = 1, $page_size = PAGE_SIZE_20)
    {
        // 被访问者 ID
        $view_user_id = $user_id;
        if ($view_user_id <= 0) {
            throw new HttpException(400, '参数错误');
        }
        // 访问者 ID
        $user_id = (int)Yii::$app->user->id;
        $pagination_params = PaginationParams::process($page, $page_size);
        return MAttentionUser::getUserAttention($view_user_id, $user_id, $type, $pagination_params->page_size);
    }

    /**
     * @api {get} /person/search-attention-users 搜索我的关注
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/search-attention-users
     * @apiSampleRequest /person/search-attention-users
     *
     * @apiVersion 0.1.0
     * @apiName search-attention-users
     * @apiGroup person
     *
     * @apiParam {String} s 搜索关键字
     * @apiParam {Number} [page=1] 页数
     * @apiParam {Number} [page_size=20] 每页个数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info": {
     *         "Datas": [{
     *           "id": 346286,
     *           "username": "InVinCiblezz",
     *           "userintro": null,
     *           "fansnum": 9,
     *           "authenticated": 0,  // 0 没有 V，1 代表黑 V，2 代表金 V，3 代表蓝 V
     *           "iconurl": "https://static.missevan.com/coversmini/201507/20/77fa8e19b37461a19192935.jpg"
     *         }],
     *         "pagination": {
     *           "p": 1,
     *           "count": "43",
     *           "maxpage": 3,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     */
    public function actionSearchAttentionUsers(string $s, int $page = 1, $page_size = PAGE_SIZE_20)
    {
        if (($s = trim($s)) === '') {
            throw new HttpException(400, '什么都没有找到', 200510002);
        }
        $pagination_params = PaginationParams::process($page, $page_size);
        $user_id = Yii::$app->user->id;
        $query = Mowangskuser::find()
            ->alias('u')
            ->select('u.id, u.username, u.userintro, u.fansnum, u.confirm, u.avatar, u.boardiconurl, u.icontype')
            ->leftJoin(MAttentionUser::tableName() . ' AS a', 'a.user_passtive = u.id')
            ->where('a.user_active = :owner', [':owner' => $user_id])
            ->andFilterWhere(['LIKE', 'u.username', $s])
            ->orderBy(['a.id' => SORT_DESC]);
        $return_model = MUtils::getPaginationModels($query, $pagination_params->page_size);
        $return_model->Datas = array_map(function ($u) {
            return [
                'id' => $u->id,
                'username' => $u->username,
                'userintro' => $u->userintro,
                'fansnum' => $u->fansnum,
                'authenticated' => $u->authenticated,
                'iconurl' => $u->iconurl,
            ];
        }, $return_model->Datas);
        return $return_model;
    }

    /**
     * @api {get} /person/get-collect-album{?user_id} 根据用户 ID 获取用户收藏音单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/get-collect-album
     *
     * @apiVersion 0.1.0
     * @apiName get-collect-album
     * @apiGroup person
     *
     * @apiParam (Query) {Number} user_id 用户 ID
     * @apiParam (Query) {number} [page=1] 页数（iOS < 4.7.8 Android < 5.6.7 时使用）
     * @apiParam (Query) {number} [page_size=20] 每页个数（iOS < 4.7.8 Android < 5.6.7 时使用）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response: iOS < 4.7.8 Android < 5.6.7 分页
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [
     *           {
     *             "username": "广播剧集合",
     *             "user_id": 346286,
     *             "id": 66,
     *             "title": "【忆语】三生三世枕上书",
     *             "music_count": 0,
     *             "collected": 1,
     *             "front_cover": "https://static.missevan.com/coversmini/201409/16/24f3021a16a75103154.jpg",
     *             "is_private": false,
     *             "is_invalid": false,
     *             "sort": 0
     *           },
     *           {
     *             "username": "23336666",
     *             "user_id": 346287,
     *             "id": 146231,
     *             "title": "此音单已被创建者设为私密",
     *             "music_count": 0,
     *             "collected": 1,
     *             "front_cover": "http://static-test.missevan.com/coversmini/album/privatecover.png",
     *             "is_private": true,
     *             "is_invalid": false,
     *             "sort": 1048576
     *           },
     *           {
     *             "username": "23336666",
     *             "user_id": 346287,
     *             "id": 146232,
     *             "title": "已失效音单",
     *             "music_count": 0,
     *             "collected": 1,
     *             "front_cover": "http://static-test.missevan.com/coversmini/album/nocover.png",
     *             "is_private": false,  // 是否为私有音单，true：私有；false：公开
     *             "is_invalid": true,  // 是否为失效音单，true：失效；false：有效
     *             "sort": 2097152
     *           }
     *         ],
     *         "pagination": {
     *           "p": 1,
     *           "count": "4",
     *           "maxpage": 2,
     *           "pagesize": 2
     *         }
     *       }
     *     }
     *  @apiSuccessExample Success-Response: iOS >= 4.7.8 Android >= 5.6.7 不分页
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "data": [
     *           {
     *             "username": "广播剧集合",
     *             "user_id": 346286,
     *             "id": 66,
     *             "title": "【忆语】三生三世枕上书",
     *             "music_count": 0,
     *             "collected": 1,
     *             "front_cover": "https://static.missevan.com/coversmini/201409/16/24f3021a16a75103154.jpg",
     *             "is_private": false,
     *             "is_invalid": false,
     *             "sort": 0
     *           },
     *           {
     *             "username": "23336666",
     *             "user_id": 346287,
     *             "id": 146231,
     *             "title": "此音单已被创建者设为私密",
     *             "music_count": 0,
     *             "collected": 1,
     *             "front_cover": "http://static-test.missevan.com/coversmini/album/privatecover.png",
     *             "is_private": true,
     *             "is_invalid": false,
     *             "sort": 1048576
     *           },
     *           {
     *             "username": "23336666",
     *             "user_id": 346287,
     *             "id": 146232,
     *             "title": "已失效音单",
     *             "music_count": 0,
     *             "collected": 1,
     *             "front_cover": "http://static-test.missevan.com/coversmini/album/nocover.png",
     *             "is_private": false,  // 是否为私有音单，true：私有；false：公开
     *             "is_invalid": true,  // 是否为失效音单，true：失效；false：有效
     *             "sort": 2097152
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionGetCollectAlbum(int $user_id, int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        // 被访问者 ID
        $view_user_id = $user_id;
        // 判断是否能访问该用户的个人主页
        self::canViewUser($view_user_id);
        // 访问者 ID
        $user_id = (int)Yii::$app->user->id;
        // WORKAROUND: iOS < 4.7.8 Android < 5.6.7 获取用户收藏音单列表需要分页
        if (Equipment::isAppOlderThan('4.7.8', '5.6.7')) {
            $pagination_params = PaginationParams::process($page, $page_size);
            return MAlbum::getCollectAlbum($view_user_id, $user_id, true, $pagination_params->page_size);
        }
        $data = MAlbum::getCollectAlbum($view_user_id, $user_id);
        return ['data' => $data];
    }

    /**
     * @api {get} /person/get-subscribed-channel 根据用户id获取用户追的频道
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/get-subscribed-channel
     * @apiSampleRequest /person/get-subscribed-channel
     * @apiDescription  /mobile/person/personInfos?type=8 p=>page pagesize=>page_size param_id => user_id
     *
     * @apiVersion 0.1.0
     * @apiName get-subscribed-channel
     * @apiGroup person
     *
     * @apiParam {number} user_id 用户id
     * @apiParam {number=1} [page=1] 页数
     * @apiParam {number=20} [page_size=20] 每页个数
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info":
     *           {
     *              Datas: [ [Object], [Object], [Object] ],
     *              pagination: { p: 1, count: '24', maxpage: 2, pagesize: 20 }
     *          }
     *     }
     *
     */
    public function actionGetSubscribedChannel(int $user_id, int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        // WORKAROUND: 临时屏蔽频道
        return ReturnModel::empty($page, $page_size);
        $user = Mowangskuser::find()
            ->select('confirm')
            ->where(['id' => $user_id])
            ->one();
        if ($user && Mowangskuser::isPrivacyUser($user->confirm)) {
            // 隐私设置用户不对外展示个人主页
            throw new HttpException(400, 'UP 主设置了隐私，无法访问');
        }
        $ModelDTO = MTag::getSubscribedChannel($user_id, $page_size);
        return $ModelDTO;
    }

    /**
     * @api {get} /person/get-user-pics 根据用户id获取用户图片
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/get-user-pics
     * @apiSampleRequest /person/get-user-pics
     * @apiDescription  /mobile/person/personInfos?type=9 p=>page pagesize=>page_size param_id => user_id
     *
     * @apiVersion 0.1.0
     * @apiName get-user-pics
     * @apiGroup person
     *
     * @apiParam {number} user_id 用户id
     * @apiParam {number=1} [page=1] 页数
     * @apiParam {number=20} [page_size=20] 每页个数
     * @apiParam {String} [token] 用户token 查看checked =0 的图片
     *
     * @apiParam {number} user_id 用户id
     * @apiParam {number=1} [page=1] 页数
     * @apiParam {number=20} [page_size=20] 每页个数
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info":
     *           {
     *              Datas: [ [Object], [Object], [Object] ],
     *              pagination: { p: 1, count: '24', maxpage: 2, pagesize: 20 }
     *          }
     *     }
     *
     */
    public function actionGetUserPics(int $user_id, $page_size = 20)
    {
        $user = Mowangskuser::find()
            ->select('confirm')
            ->where(['id' => $user_id])
            ->one();
        if ($user && Mowangskuser::isPrivacyUser($user->confirm)) {
            // 隐私设置用户不对外展示个人主页
            throw new HttpException(400, 'UP 主设置了隐私，无法访问');
        }
        $owner_id = (int)Yii::$app->user->id;
        // 用户 3287719 在 2019 年 7 月 7 日前曾单独屏蔽。
        if ($user_id !== $owner_id) {
            return [
                'Datas' => [],
                'pagination' => [
                    'p' => 1,
                    'count' => '0',
                    'maxpage' => 0,
                    'pagesize' => $page_size,
                ],
            ];
        }
        $ModelDTO = MImage::getUserPics($user_id, $page_size);
        return $ModelDTO;
    }

    /**
     * @api {get} /person/get-subscribed-drama 根据用户 ID 获取用户追剧信息
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/get-subscribed-drama
     * @apiSampleRequest /person/get-subscribed-drama
     * @apiDescription  /mobile/person/personInfos?type=10 p => page pagesize => page_size param_id => user_id
     *
     * @apiVersion 0.1.0
     * @apiName get-subscribed-drama
     * @apiGroup person
     *
     * @apiParam {Number} user_id 用户 ID
     * @apiParam {number=1} [page=1] 页数
     * @apiParam {number=20} [page_size=20] 每页个数
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info": {
     *         "Datas": [
     *           {
     *             "id": 9889,
     *             "name": "七年之痒的攻突然坏掉了！",
     *             "type": "耽美",
     *             "serialize": false,
     *             "newest": "全一期",
     *             "pay_type": 0,
     *             "checked": 1,
     *             "cover": "http://static.missevan.com/dramacoversmini/201708/25/test.jpg",
     *             "abstract": "简介",
     *             "need_pay": 0,
     *             "corner_mark": {  // 无剧集角标时不返回该字段
     *               "text": "已购",
     *               "text_color": "#ffffff",
     *               "bg_start_color": "#e66465",
     *               "bg_end_color": "#e66465",
     *               "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *             }
     *           }
     *         ],
     *         pagination: {
     *           count: 3,
     *           pagesize: 20,
     *           p: 1,
     *           maxpage: 1
     *         }
     *       }
     *     }
     *
     */
    public function actionGetSubscribedDrama(int $user_id, int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        // 当前被访问个人主页的用户 ID
        $view_user_id = $user_id;
        $confirm = Mowangskuser::find()
            ->select('confirm')
            ->where(['id' => $view_user_id])
            ->scalar();
        if (false !== $confirm && Mowangskuser::isPrivacyUser((int)$confirm)) {
            // 隐私设置用户不对外展示个人主页
            throw new HttpException(400, 'UP 主设置了隐私，无法访问');
        }
        $page_obj = PaginationParams::process($page, $page_size);
        // 发起请求的用户 ID
        $user_id = (int)Yii::$app->user->id;
        $return = MSound::getSubscribedDrama($view_user_id, $user_id, $page_obj->page, $page_obj->page_size);
        return $return;
    }

    /**
     * @api {get} /person/get-user-drama{?user_id,page,page_size} 根据用户 ID 获取用户剧集作品信息
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/get-user-drama
     * @apiDescription 该接口获取 UP 主的剧集作品（UP 主创建及参与配音的作品）\
     * UP 主视角：\
     * UP 主自建的剧集：除下架之外的所有状态剧集；再审状态剧集，显示修改后的剧集信息 \
     * 参与配音的剧集：审核通过的剧集；再审状态剧集，显示修改前的剧集信息 \
     * 他人视角：\
     * UP 主自建的剧集：审核通过的剧集；再审状态剧集，显示修改前的剧集信息 \
     * 参与配音的剧集：审核通过的剧集；再审状态剧集，显示修改前的剧集信息
     * @apiVersion 0.1.0
     * @apiName get-user-drama
     * @apiGroup person
     *
     * @apiParam (Query 参数) {Number} user_id 用户 ID
     * @apiParam (Query 参数) {Number} [page=1] 页数
     * @apiParam (Query 参数) {Number} [page_size=20] 每页个数
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info": {
     *         "Datas": [
     *           {
     *             "id": 9889,
     *             "name": "七年之痒的攻突然坏掉了！",
     *             "abstract": "简介",
     *             "checked": 1,  // 审核状态，0: 未过审；1: 审核通过；3: 报警；4: 合约到期下架
     *             "pay_type": 0,  // 付费类型，0: 免费；1: 单集付费；2: 整剧付费
     *             "cover": "http://static.missevan.com/dramacoversmini/201708/25/test.jpg",
     *             "cover_color": 12434877,
     *             "newest": "全一期",
     *             "serialize": false,  // 完结度，true: 长篇未完结；false: 长篇已完结/全一期/微小剧
     *             "type": 7,
     *             "type_name": "未完结",
     *             "view_count": 968903
     *           }
     *         ],
     *         "pagination": {
     *           "count": 1,
     *           "pagesize": 20,
     *           "p": 1,
     *           "maxpage": 1
     *         }
     *       }
     *     }
     *
     */
    public function actionGetUserDrama(int $user_id, int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        // 判断是否能访问该用户的个人主页
        self::canViewUser($user_id);
        $view_user_id = $user_id;
        $page_obj = PaginationParams::process($page, $page_size);
        return Drama::getUserDrama($view_user_id, Yii::$app->user->id, $page_obj->page, $page_obj->page_size);
    }

    /**
     * @api {get} /person/search-user-drama 在个人主页关键字搜索 UP 主的剧集作品
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/search-user-drama
     * @apiDescription 在个人主页根据关键字搜索 UP 主的剧集作品（UP 主创建及参与配音的作品）\
     * UP 主视角：\
     * UP 主自建的剧集：除下架之外的所有状态剧集；再审状态剧集，显示修改后的剧集信息；剧名再审中的剧集
     * 根据再审中剧名搜索 \
     * 参与配音的剧集：审核通过的剧集；再审状态剧集，显示修改前的剧集信息；剧名再审中的剧集，根据再审前剧名搜索 \
     * 他人视角：\
     * UP 主自建的剧集：审核通过的剧集；再审状态剧集，显示修改前的剧集信息；剧名再审中的剧集，根据再审前剧名搜索 \
     * 参与配音的剧集：审核通过的剧集；再审状态剧集，显示修改前的剧集信息；剧名再审中的剧集，根据再审前剧名搜索
     * @apiVersion 0.1.0
     * @apiName search-user-drama
     * @apiGroup person
     *
     * @apiParam {String} q 搜索关键字
     * @apiParam {Number} user_id UP 主 ID
     * @apiParam {Number} [page=1] 页数
     * @apiParam {Number} [page_size=20] 每页个数
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info": {
     *         "Datas": [
     *           {
     *             "id": 9889,
     *             "name": "七年之痒的攻突然坏掉了！",
     *             "abstract": "简介",
     *             "checked": 1,  // 审核状态，0: 未过审；1: 审核通过；3: 报警；4: 合约到期下架
     *             "pay_type": 0,  // 付费类型，0: 免费；1: 单集付费；2: 整剧付费
     *             "cover": "http://static.missevan.com/dramacoversmini/201708/25/test.jpg",
     *             "cover_color": 12434877,
     *             "newest": "全一期",
     *             "serialize": false,  // 完结度，true: 长篇未完结；false: 长篇已完结/全一期/微小剧
     *             "type": 7,
     *             "type_name": "未完结",
     *             "view_count": 968903
     *           }
     *         ],
     *         "pagination": {
     *           "count": 1,
     *           "pagesize": 20,
     *           "p": 1,
     *           "maxpage": 1
     *         }
     *       }
     *     }
     *
     */
    public function actionSearchUserDrama(string $q, int $user_id, int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        $q = trim(MUtils2::removeEmoji($q));
        if ($q === '') {
            throw new HttpException(400, '搜索关键字不可为空', 200510002);
        }
        // 判断是否能访问该用户的个人主页
        self::canViewUser($user_id);
        $view_user_id = $user_id;
        $page_obj = PaginationParams::process($page, $page_size);
        return Drama::searchUserDrama($q, $view_user_id, (int)Yii::$app->user->id, $page_obj->page,
            $page_obj->page_size);
    }

    /**
     * @api {get} /person/get-user-info{?user_id} 根据用户 ID 获取用户信息
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/get-user-info
     * @apiSampleRequest /person/get-user-info
     * @apiDescription 返回字段中 blacklist 字段代表和用户的黑名单关系
     * 0：未拉黑对方；1：已拉黑对方；
     *
     * @apiVersion 0.1.0
     * @apiName get-user-info
     * @apiGroup person
     *
     * @apiParam {Number} [user_id] 用户 ID（不传时为当前登录的用户 ID）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 346286,
     *         "username": "InVinCiblezzz",
     *         "userintro": "465 465abc",
     *         "iconurl": "https://static.missevan.com/avatars/201812/10/91e6b6ca1344ac90973e61fbcd87df78184702.jpg",
     *         "coverurl_new2": "https://static.missevan.com/usercover/background3.png",  // WORKAROUND: 用户封面图（iOS 4.6.9 和 安卓 5.5.8 以下版本使用此字段）
     *         "coverurl": "https://static.missevan.com/usercover/background3.png",  // 用户封面图
     *         "coverid": 3,  // 封面图位置（0: 其他封面图；1: 默认图 1 位置；2: 默认图 2 位置；3: 默认图 3 位置）
     *         "dark_coverurl": "https://static.missevan.com/usercover/background403-dark.png",  // 黑夜模式用户封面图，不返回该字段说明无黑夜模式
     *         "soundurl": "https://static.missevan.com/32BIT/201505/05/ca57758e803880dfe79fb25982f98348161835.mp3",
     *         "icontype": 1,
     *         "albumnum": 0,
     *         "follownum": 0,
     *         "fansnum": 0,
     *         "soundnum": 75,
     *         "point": 12912,
     *         "userintro_audio": 34197,
     *         "confirm": 1,
     *         "duration": 8725,
     *         "hotSound": 1,
     *         "drama_bought_count": 2,
     *         "balance": 20,
     *         "cvid": 0
     *         "followed": 1,
     *         "blacklist": 0,
     *         "live": {  // 用户没有所属直播间为 null
     *           "room_id": 11,
     *           "title": "直播间的标题",
     *           "status": 1  // 直播间状态，0：没有开启房间；1：房间开启
     *         },
     *         "is_ban": false,  // 用户是否被封禁
     *         "ip_location": "山东",
     *         "auth_info": {  // 用户认证信息，无认证时不下发
     *           "type": 2,  // 认证类型，2：金 V；3：蓝 V
     *           "title": "UP 主认证",  // 认证类型描述
     *           "subtitle": "知名声优椰子酱"  // 认证头衔，无头衔时不下发
     *         },
     *         "avatar_frame": {  // 用户佩戴的头像框信息，没有时不下发
     *           "id": 1,  // 头像框 ID
     *           "image_url": "https://test.com/test.png"  // 头像框地址
     *         },
     *         "is_vip": 1  // 用户是否为会员。0：否；1：是
     *       }
     *     }
     */
    public function actionGetUserInfo(int $user_id = 0)
    {
        if ($user_id === 0) {
            if (Yii::$app->user->isGuest) {
                Yii::$app->user->loginRequired();
            }
            $view_user_id = $user_id = Yii::$app->user->id;
        } elseif ($user_id < 0) {
            throw new HttpException(400, '参数错误');
        } else {
            // 被访问者 ID
            $view_user_id = $user_id;
            // 访问者 ID
            $user_id = (int)Yii::$app->user->id;
        }
        // 判断是否显示用户信息
        self::canViewUser($view_user_id);
        return self::getUserInfo($view_user_id, $user_id);
    }

    /**
     * 获取个人主页用户信息
     *
     * @param int $view_user_id 被查看的用户 ID
     * @param int $user_id 当前用户 ID
     * @return array
     * @throws HttpException
     */
    private static function getUserInfo(int $view_user_id, int $user_id)
    {
        $user_info = Mowangskuser::getPersonInfo($view_user_id);
        // 黑名单状态，关注状态
        $relations = BlackUser::relation($user_id, $view_user_id);
        $followed = $relations['followed'];
        // 接口只返回自己是否拉黑对方的状态
        $blacklist = in_array($relations['blacklist'], [BlackUser::BLACKLIST_NONE, BlackUser::BLACKLIST_PASSIVE]) ?
            BlackUser::BLACKLIST_NONE : BlackUser::BLACKLIST_ACTIVE;
        $info = [
            'id' => $user_info['id'],
            'username' => $user_info['username'],
            'userintro' => $user_info['userintro'],
            'iconurl' => $user_info['iconurl'],
            'coverurl' => $user_info['coverurl'],
            'coverid' => $user_info['coverid'],
            'icontype' => $user_info['icontype'],
            'albumnum' => $user_info['albumnum'],
            'follownum' => $user_info['follownum'],
            'fansnum' => $user_info['fansnum'],
            'soundnum' => $user_info['soundnum'],
            'userintro_audio' => $user_info['userintro_audio'],
            // WORKAROUND: 暂时将所有用户都视为已答题用户，屏蔽答题入口。安卓使用等于 1 进行判断。
            'confirm' => $user_info['confirm'] | Mowangskuser::CONFIRM_PASS_EXAMINATION,
            'duration' => $user_info['duration'],
            'hotSound' => $user_info['hotSound'],
            'cvid' => $user_info['cvid'],
            'followed' => $followed,
            'blacklist' => $blacklist,
            'live' => $user_info->live,
            'is_ban' => $user_info->isShowBan(),
        ];
        if ($user_id === $view_user_id) {
            $info['balance'] = $user_info['balance'];
            $info['point'] = $user_info['point'];
            $info['drama_bought_count'] = $user_info['drama_bought_count'];
        }

        if (Equipment::isAppOlderThan('6.0.8', '6.0.8')) {
            // WORKAROUND: iOS 6.0.8 和安卓 6.0.8 以下版本下发 authenticated、title 字段
            $info['authenticated'] = $user_info['authenticated'];
            $info['title'] = $user_info['authenticated'] === Mowangskuser::CONFIRM_GOLDEN_VIP ? 'UP 主认证' :
                ($user_info['authenticated'] === Mowangskuser::CONFIRM_BLUE_VIP ? '机构官方认证' : '');
        }
        if (in_array($user_info['authenticated'], [Mowangskuser::CONFIRM_GOLDEN_VIP, Mowangskuser::CONFIRM_BLUE_VIP])) {
            $subtitle = UserCertification::find()
                ->select('subtitle')
                ->where(['user_id' => $user_info['id']])
                ->limit(1)
                ->scalar();
            if ($subtitle) {
                $info['auth_info'] = [
                    'type' => $user_info['authenticated'],
                    'title' => $user_info['authenticated'] === Mowangskuser::CONFIRM_GOLDEN_VIP ? '个人认证' : '机构认证',
                    'subtitle' => $subtitle,
                ];
            } else {
                $info['auth_info'] = [
                    'type' => $user_info['authenticated'],
                    // 无具体头衔时，金 V 展示“UP 主认证”，蓝 V 展示“机构官方认证”
                    'title' => $user_info['authenticated'] === Mowangskuser::CONFIRM_GOLDEN_VIP ? 'UP 主认证' : '机构官方认证',
                ];
            }
        }

        // WORKAROUND：iOS 4.6.9 和 安卓 5.5.8 以下版本封面图使用 coverurl_new2 字段
        if (Equipment::isAppOlderThan('4.6.9', '5.5.8')) {
            $info['coverurl_new2'] = $user_info->coverurl_new2;
        }

        // 当存在黑夜模式封面图时需要同时下发
        if (isset($user_info->dark_coverurl)) {
            $info['dark_coverurl'] = $user_info->dark_coverurl;
        }
        MSound::getSoundSignUrls($user_info, true);
        if (Equipment::isAppOlderThan('4.4.6', '5.3.6')) {
            // WORKAROUND: 不支持头像音多个音质的版本，默认使用 128k bit 音质，若无此音质音频则使用原音质音频
            $info['soundurl'] = $user_info['soundurl_128'] ?: $user_info['soundurl_64'];
        } else {
            $info['soundurl'] = $user_info['soundurl'];
            $info['soundurl_64'] = $user_info['soundurl_64'];
            $info['soundurl_128'] = $user_info['soundurl_128'];
        }
        $info['ip_location'] = UserAddendum::showIPLocation($view_user_id);
        if (Equipment::isAppOlderThan('6.0.4', '6.0.4')) {
            // WORKAROUND: 老版本（安卓 < 6.0.4, iOS < 6.0.4）不下发用户头像框
            return $info;
        }
        // 获取用户头像框
        $avatar_frame_map = null;
        try {
            $avatar_frame_map = Yii::$app->serviceRpc->listAvatarFrame([$view_user_id]);
        } catch (Exception $e) {
            Yii::error("获取用户（{$user_id}）头像框信息失败：{$e->getMessage()}", __METHOD__);
            // PASS: 获取头像框出错时，为避免影响展示用户其他信息，记录错误日志后忽略该错误
        }
        if (isset($avatar_frame_map[$view_user_id])) {
            // 有头像框时才下发头像框相关字段
            $avatar_frame = $avatar_frame_map[$view_user_id];
            if (Equipment::isAppOlderThan('6.1.4', '6.1.4')) {
                // WORKAROUND: iOS 及 Android 6.1.4 以下版本，仅下发头像框地址字段
                $info['avatar_frame_url'] = $avatar_frame['avatar_frame_url'];
            } else {
                $info['avatar_frame'] = [
                    'id' => $avatar_frame['id'],
                    'image_url' => $avatar_frame['avatar_frame_url'],
                ];
            }
        }
        // 获取会员信息
        $info['is_vip'] = (int)MUserVip::isVipUser($view_user_id);
        return $info;
    }

    /**
     * @deprecated 该接口已废弃，后续应从 /x/person/get-user-point 获取鱼干数量
     *
     * @api {get} /person/get-user-point 获取当前登录用户拥有的小鱼干数
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/get-user-point
     * @apiDescription 获取当前登录用户拥有的小鱼干数
     *
     * @apiVersion 0.1.0
     * @apiName get-user-point
     * @apiGroup person
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "point": 50
     *       }
     *     }
     */
    public function actionGetUserPoint()
    {
        $point = (int)Mowangskuser::find()->select('point')
            ->where(['id' => Yii::$app->user->id])
            ->scalar();
        return ['point' => $point];
    }

    /**
     * @api {get} /person/get-sobot-user 获取当前用户智齿客服用户信息
     * @apiDescription 本接口仅限已登录用户获取自己智齿客服 partnerId, \
     * 每次进入智齿客服页面时需调用该接口获取当前用户 partnerId 并存入客户端缓存（仅用于当前登录用户使用），\
     * 当无法正常访问该接口时，客户端应从缓存中获取 partnerId, \
     * 当缓存中不存在 partnerId 时，客户端应确保用户可以以游客身份接入智齿客服
     *
     * @apiVersion 0.1.0
     * @apiName get-sobot-user
     * @apiGroup person
     *
     * @apiPermission user
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "partner_id": "34283928dd7cf47d75cb7267953ccc13"  // 跟智齿对接的 ID（旧版为用户 ID, 新版为根据用户 ID 加密后的字符串）
     *       }
     *     }
     */
    public function actionGetSobotUser()
    {
        return ['partner_id' => UserAddendum::getSobotPartnerId(Yii::$app->user->id)];
    }

    /**
     * @api {get} /person/user-feed 获取用户动态
     * @apiDescription 下发的动态类型为 live（直播动态）时没有返回 id 字段，如果分页信息含有下一页的数据，\
     * 客户端应该取最后一条带有 id 字段的数据，用于请求下一页的数据，如果找不到 id，不再请求下一页
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/user-feed
     * @apiSampleRequest /person/user-feed
     *
     * @apiVersion 0.1.0
     * @apiName user-feed
     * @apiGroup person
     *
     * @apiParam {number=0,1,2} [feed_type=0] 动态类型（0 用户及频道的动态，1 剧集的动态，2 直播、用户、频道及剧集的动态）
     * @apiParam {Number} [sound_id=0] 音频 ID（翻页时传单音 ID 可获取此单音之后的动态信息）
     * @apiParam {Number} [page_size=20]
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "data": [{  // iOS 4.8.5，安卓 5.7.2 以下的版本下发 Datas 兼容老的数据类型
     *           "user_id": 1,  // 主播 ID
     *           "title": "直播间标题",  // 直播间标题
     *           "status": 1,  // 直播间状态（1: 开播中）只有开播中的才会在直播动态中展示
     *           "live_start_time": 1653326985,  // 开播时间（单位：秒）
     *           "room_id": 100000,  // 直播间 ID
     *           "cover_url": "https://static-test.missevan.com/icon01.png",  // 直播间封面图
     *           "catalog_id": 14,  // 直播分区 ID
     *           "catalog_name": "直播分区名称",  // 直播分区名称
     *           "catalog_color": "#6FEFEC",  // 直播间 catalog 的颜色
     *           "username": "主播昵称",  // 主播昵称
     *           "iconurl": "https://static-test.missevan.com/avatars/icon01.png",  // 主播头像
     *           "authenticated": 2,  // 加 V 认证标识：1 代表黑 V，2 代表金 V，3 代表蓝 V
     *           "type": "live"  // 动态类型（live: 直播动态），目前只有第一页第一条数据会返回直播动态类型的数据
     *         }, {
     *           "id": 35296,  // 音频 ID
     *           "create_time": 1431690506,
     *           "duration": 1283840,
     *           "soundstr": "【轻小说】穿越时空的少女【7】*终了*",
     *           "view_count": 521,
     *           "front_cover": "http://static.missevan.com/coversmini/201505/15/28eb5fcb89a3d8394825.jpg",
     *           "drama_id": 728,
     *           "drama_name": "穿越时空的少女",
     *           "drama_cover": "http://static.missevan.com/dramacoversmini/201606/06/9c67ab0378fa8e001.jpg",
     *           "episode_name": "【7】终",
     *           "type": "drama",
     *           "video": true  // 音频是否包含对应的视频
     *         }, {
     *           "id": 14250,  // 音频 ID
     *           "create_time": 1411108564,
     *           "duration": 593140,
     *           "soundstr": "西尾维新【上】",
     *           "username": "少伯",
     *           "user_id": 62550,
     *           "view_count": 4530,
     *           "front_cover": "http://static.missevan.com/coversmini/201410/04/da9d638405c91f823df232315.jpg",
     *           "tags": "【推荐向】动漫佳作盘点",
     *           "channel_id": 16408,
     *           "channel_cover": "http://static.missevan.com/mtags/201601/27/da35d9aa7d629ba4a5fcff3b7142738.jpg",
     *           "type": "channel",
     *           "video": true  // 音频是否包含对应的视频
     *         }, {
     *           "id": 80,  // 音频 ID
     *           "user_id": 1,
     *           "username": "违规昵称",
     *           "create_time": 1328612228,
     *           "duration": 1040,
     *           "soundstr": "很便宜哟~",
     *           "view_count": 113,
     *           "front_cover": "http://static.missevan.com/coversmini/201701/24/105406c2543b75dd714bef9091608.png",
     *           "iconurl": "http://static.missevan.com/avatars/201501/16/389141afe72a20230d1c93342202121.png",
     *           "authenticated": 2,
     *           "type": "sound",
     *           "video": true  // 音频是否包含对应的视频
     *         }],
     *         "pagination": {
     *           "has_more": false
     *         }
     *       }
     *     }
     */
    public function actionUserFeed(int $feed_type = Feed::FEED_TYPE_USER_CHANNEL,
            int $page_size = PAGE_SIZE_20, int $sound_id = 0)
    {
        if (!Feed::checkFeedType($feed_type)) {
            throw new HttpException(400, '参数错误');
        }

        $user_id = (int)Yii::$app->user->id;
        $memcache = Yii::$app->memcache;
        $user_feed_key = MUtils::generateCacheKey(KEY_USER_FEED, $user_id, $feed_type, $sound_id);

        // 判断是否有 feed 流未读信息数，有重新读取数据，没有读取缓存
        $feednum = (int)Mowangskuser::find()->select('feednum')->where('id = :id', [':id' => $user_id])->scalar();
        if ($feednum === 0 && $user_feed_cache = $memcache->get($user_feed_key)) {
            $return = Json::decode($user_feed_cache);
        } else {
            $feed = Mowangskuser::getUserFeed($user_id, $feed_type, $sound_id, $page_size);
            Mowangskuser::updateAll(['feednum' => 0], 'id = :id', [':id' => $user_id]);

            $datas = array_slice($feed, 0, $page_size);
            MSound::haveVideos($datas);
            $return = [
                'data' => $datas,
                'pagination' => [
                    'has_more' => count($feed) > $page_size,
                ],
            ];
            $memcache->set($user_feed_key, Json::encode($return), FIVE_MINUTE);
        }
        if ($sound_id === 0
                && $feed_type === Feed::FEED_TYPE_USER_CHANNEL_DRAMA
                && !Equipment::isAppOlderThan('4.8.5', '5.7.2')) {
            // iOS 4.8.5，安卓 5.7.2 版本开始增加直播动态模块
            // 目前只有第一页第一条数据会返回直播动态类型的数据
            $live_feed = Yii::$app->live->getLiveFeed($user_id);
            if ($live_feed) {
                // 获取用户加 V 标识
                $live_feed['authenticated'] = Mowangskuser::getAuthenticated($live_feed['confirm']);
                // 动态类型
                $live_feed['type'] = 'live';
                unset($live_feed['confirm']);
                // 直播动态数据放到最前面展示
                array_unshift($return['data'], $live_feed);
            }
        }

        if (isset($return['data']) && Equipment::isAppOlderThan('4.8.5', '5.7.2')) {
            // iOS 4.8.5，安卓 5.7.2 以下的版本下发 Datas 兼容老的数据类型
            $return['Datas'] = $return['data'];
            unset($return['data']);
        }

        return $return;
    }

    /**
     * @api {get} /person/user-hot-sound 获取用户热门单音
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/user-hot-sound
     * @apiSampleRequest /person/user-hot-sound
     * @apiDescription  /mobile/site/GetUser50 获取用户的热门50单音 参数和现在的接口一样
     *
     * @apiVersion 0.1.0
     * @apiName user-hot-sound
     * @apiGroup person
     *
     * @apiParam {Number} user_id 被获取的用户id
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info":
     *           { hasMore: false, Datas: [ [Object] ] }
     *     }
     *
     */
    public function actionUserHotSound(int $user_id)
    {
        if (!$user = Mowangskuser::findOne($user_id)) throw new HttpException(404, '用户不存在', 200020001);
        // 获取用户前 50 首热门音频（查询音频地址用于分享）
        $sounds = MSound::find()
            ->select('id, cover_image, view_count, duration, soundstr, username, intro, '
                . 'soundurl_64, download, pay_type')
            ->where(['user_id' => $user_id, 'checked' => MSound::CHECKED_PASS])
            ->orderBy('view_count DESC')
            ->limit(50)
            ->all();
        if (!$sounds) throw new HttpException(404, '该用户没有单音', 200110001);
        MSound::haveVideos($sounds);
        MSound::checkNeedPay($sounds, $user_id);
        MSound::removeNeedPaySoundUrl($sounds);
        return $sounds;
    }

    /**
     * @api {get} /person/collect-user-hot-sound 收藏用户热门单音到音单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/collect-user-hot-sound
     * @apiSampleRequest /person/collect-user-hot-sound
     * @apiDescription  /mobile/personOperation/CollectUser50 热门50收藏
     *
     * @apiVersion 0.1.0
     * @apiName collect-user-hot-sound
     * @apiGroup person
     *
     * @apiParam {String} token 用户token
     * @apiParam {Number} user_id 被收藏的用户id
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info":
     *           '收藏单音成功'
     *     }
     *
     */
    public function actionCollectUserHotSound(int $user_id)
    {
        $my_id = (int)Yii::$app->user->id;
        $count = MAlbum::find()->where(['user_id' => $my_id])->count();
        $me = Mowangskuser::findOne($my_id);
        $user = Mowangskuser::findOne($user_id);

        if ($count >= (100 * $me->mlevel))
            throw new HttpException(403, '(′・_・`) 您的音单数量已达上限 ' . ($me->mlevel * 100), 200120003);

        if (!$user) throw new HttpException(404, '用户不存在', 200020001);

        $sound_ids = MSound::find()
            ->select('id')
            ->where(['user_id' => $user_id, 'checked' => 1])
            ->orderBy('view_count desc')
            ->limit(50)
            ->column();

        if (!$sound_ids) throw new HttpException(404, '用户没有单音', 200110001);

        $model = new MAlbum();
        $model->title = $user->username;
        if ($user->iconurl) {
            $model->copyIconUrl($user->iconurl);
        }
        $model->user_id = $me->id;
        $model->username = $me->username;

        if (!$model->save())
            return $model->getErrors() ?: '数据有误';

        $album_id = $model->id;
        //个人专辑数+1
        $me->updateCounters(['albumnum' => 1]);
        MSoundAlbumMap::collectSound($album_id, $sound_ids);
        return '收藏单音成功';

    }

    /**
     * @api {get} /person/history 获取历史记录
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/history
     * @apiSampleRequest /person/history
     * @apiDescription 获取历史记录
     *
     * @apiVersion 0.1.0
     * @apiName history
     * @apiGroup person
     *
     * @apiParam {Number} [page_size=20]
     * @apiParam {Number} [marker] 最后的分页标记
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "data": [{
     *           "id": 5,  // 历史记录 ID（删除历史记录时传此值）
     *           "element_type": 1,  // 元素类型：1 音频、2 剧集、3 直播间
     *           "sound_id": 3,
     *           "soundstr": "来啦!",
     *           "view_count": 2,
     *           "duration": 1120,  // 音频时长（毫秒）
     *           "front_cover": "http://static.missevan.com/coversmini/oss://201701/24/d337b82e692709.png",
     *           "user_id": 1,
     *           "username": "昵称_YG5op67Y",
     *           "video": true  // element_type = 1 且绑定了视频返回 true, 未绑定不返回
     *         },
     *         {
     *           "id": 6,
     *           "element_type": 3,
     *           "room_id": 999777,
     *           "title": "room-title",
     *           "cover": "http://foo.com/bar.png",
     *           "catalog_name": "闲聊",
     *           "catalog_id": 106,
     *           "user_id": 3,
     *           "username": "暗切线",
     *           "status": 1  // 1 开播中，0 未开播
     *         },
     *         {
     *           "id": 4,
     *           "element_type": 2,
     *           "drama_id": 34,
     *           "name": "囧囧有神",
     *           "cover": "http://static.missevan.com/dramacoversmini/201807/02/4d9c68c818509.jpg",
     *           "cover_color": 12434877,
     *           "view_count": 10853,
     *           "pay_type": 2,
     *           "need_pay": 1,
     *           "more": {
     *             "last_play_sound": {
     *               "id": 1,
     *               "video": true,  // 绑定了视频返回 true, 未绑定不返回
     *               "name": "第六期",
     *               "duration": 1120,  // 音频时长（毫秒）
     *               "view_count": 563
     *             },
     *           }
     *         },
     *         {
     *           "id": 9,
     *           "element_type": 2,
     *           "drama_id": 8432,
     *           "name": "互动广播剧《猎场》",
     *           "cover": "http://static.missevan.com/dramacoversmini/201807/02/4d9c68c818509.jpg",
     *           "cover_color": 12434877,
     *           "view_count": 10853,
     *           "pay_type": 2,
     *           "need_pay": 1,
     *           "more": {
     *             "last_play_sound": {
     *               "id": 3674,
     *               "video": true  // 绑定了视频返回 true, 未绑定不返回
     *             },
     *             "node": {
     *               "id": 677,
     *               "title": "序幕",
     *               "duration": 303120
     *             }
     *           }
     *         }],
     *         "has_more": true,  // 是否有更多数据
     *         "marker": "last_ime:1617072073590"  // has_more 为 true 时要获取下一页时传此参数
     *       }
     *     }
     */
    public function actionHistory(string $marker = '', int $page_size = PAGE_SIZE_20)
    {
        return MUserHistory::getList(Yii::$app->user->id, $marker, $page_size);
    }

    /**
     * @api {post} /person/del-history 删除用户播放历史记录
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/del-history
     * @apiSampleRequest /person/del-history
     * @apiDescription 删除用户播放历史记录
     *
     * @apiVersion 0.1.0
     * @apiName del-history
     * @apiGroup person
     *
     * @apiParam {number=0,1} [type=0] 操作类型（0：通过 ID 删除；1：删除全部）
     * @apiParam {Number[]} ids 历史记录 IDs
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "删除成功"
     *     }
     */
    public function actionDelHistory()
    {
        $type = (int)Yii::$app->request->post('type');
        $user_id = (int)Yii::$app->user->id;

        switch ($type) {
            case self::DELETE_HISTORY_TYPE_DEL:
                $ids = Yii::$app->request->post('ids');
                if (!MUtils2::isUintArr($ids)) {
                    throw new HttpException(400, '参数错误');
                }
                $ids = array_map('intval', $ids);
                Yii::$app->serviceRpc->delPlayHistory($user_id, $ids);
                break;
            case self::DELETE_HISTORY_TYPE_CLEAR:
                Yii::$app->serviceRpc->clearPlayHistory($user_id);
                break;
            default:
                throw new HttpException(400, '参数错误');
        }
        return '删除成功';
    }

    /**
     * @api {get} /person/banner 获取所有版头图分页信息
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/banner
     * @apiSampleRequest /person/banner
     * @apiDescription  /mobile/site/banner 获取所有封面图（版头图)
     *
     * @apiVersion 0.1.0
     * @apiName banner
     * @apiGroup person
     *
     * @apiParam {String} [token] 用户token 帮助查看是否已拥有此版头图
     * @apiParam {Number} [page=1] 当前页数
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info":
     *           {
     *              Datas: [ [Object] ],
     *              pagination: { p: 1, count: '48', maxpage: 3, pagesize: 20 }
     *           }
     *     }
     *
     */

    public function actionBanner()
    {
        $user_id = Yii::$app->user->id;
        $page_size = 20;
        $query = MImage::find()
            ->where('catalog_id = 30 AND checked = 1');

        $return_model = MUtils::getPaginationModels($query, $page_size);
        if (!$user_id) return $return_model;

        if ($return_model->Datas) {
            $image_ids = array_column($return_model->Datas, 'id');
            $relations = UserCover::find()
                ->select('cover_id')
                ->where(['cover_id' => $image_ids, 'user_id' => $user_id])
                ->column();
            if ($relations) {
                foreach ($return_model->Datas as &$cover) {
                    if (in_array($cover->id, $relations))
                        $cover->have = 1;
                }

            }
        }
        return $return_model;
    }

    /**
     * @api {post} /person/report 举报
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/report
     * @apiSampleRequest /person/report
     * @apiDescription /mobile/personOperation/Report
     * @apiVersion 0.1.0
     * @apiName Report
     * @apiGroup person
     *
     * @apiParam {Number} id 资源 ID
     * @apiParam {Number} reason 举报原因。1: 广告等垃圾信息；2: 不友善内容；3: 违法侵权内容；4: 不宜公开讨论的政治内容；5: 其他；5 之后的举报原因在/x/report/list-report-reason 接口下发
     * @apiParam {number={1-10}} type 举报类型，1：单音；2：图片；3：音频弹幕；4：直播间用户；5：直播；6：评论；7：子评论；8：私信；9：音单；10：互动剧节点弹幕；11：个人信息
     * @apiParam {String} content 内容
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "info": "举报成功"
     *     }
     */
    public function actionReport()
    {
        $user_id = Yii::$app->user->id;
        $target = (int)Yii::$app->request->post('type');
        $source_id = (int)Yii::$app->request->post('id');
        $reason = (int)Yii::$app->request->post('reason');
        $content = trim(Yii::$app->request->post('content'));
        if ($target <= 0 || $source_id <= 0) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }

        $model = new MReport();
        $model->user_id = $user_id;

        if (!key_exists($target, MReport::$target)) {
            throw new HttpException(400, Yii::t('app/error', 'Report type error'), 200370002);
        }

        if ($target === MReport::TARGET_USER_INFO) {
            // 个人信息举报时，原因必须为其他
            if ($reason !== MReport::REASON_OTHER) {
                throw new HttpException(400, Yii::t('app/error', 'params error'));
            }
            // 个人信息举报时，举报内容为必选
            if ($content === '') {
                throw new HttpException(400, '请选择举报内容');
            }
        }
        $is_valid_reason = false;
        if ($reason) {
            $is_valid_reason = MReportReason::find()->where('id = :id AND parent_id > 0', [':id' => $reason])->exists();
        }
        if (!$is_valid_reason) {
            throw new HttpException(400, Yii::t('app/error', 'Report cause error'), 200370003);
        }
        // 不能举报自己
        if (($target === MReport::TARGET_USER_INFO && $user_id === $source_id)
                || ($target === MReport::TARGET_LIVE
                    && Live::find()->where(['user_id' => $user_id, 'room_id' => $source_id])->exists())) {
            throw new HttpException(403, '不能举报自己哦');
        }
        $has_repeat = MReport::find()
            ->where('target = :target AND source_id = :source_id AND user_id = :user_id AND status = :status', [
                ':target' => $target,
                ':source_id' => $source_id,
                ':user_id' => $user_id,
                ':status' => MReport::STATUS_UNHANDLE,
            ])->exists();
        if ($has_repeat) {
            return Yii::t('app/error', 'Cannot repeat reporting');
        }

        switch ($target) {
            case MReport::TARGET_SOUND:
                $exist = MSound::find()->where(['id' => $source_id])->exists();
                break;
            case MReport::TARGET_IMAGE:
                $exist = MImage::find()->where(['id' => $source_id])->exists();
                break;
            case MReport::TARGET_DANMAKU:
                $exist = MSoundCommentRO::find()->where(['id' => $source_id])->exists();
                break;
            case MReport::TARGET_LIVE_USER:
            case MReport::TARGET_USER_INFO:
                $exist = Mowangskuser::find()->where(['id' => $source_id])->exists();
                break;
            case MReport::TARGET_LIVE:
                $exist = Live::find()->where(['room_id' => $source_id])->exists();
                break;
            case MReport::TARGET_COMMENT:
                $exist = SoundCommentRO::find()->where(['id' => $source_id])->exists();
                break;
            case MReport::TARGET_SUB_COMMENT:
                $exist = SoundSubCommentRO::find()->where(['id' => $source_id])->exists();
                break;
            case MReport::TARGET_PRIVATE_MESSAGE:
                $small_id = $source_id > $user_id ? $user_id : $source_id;
                $big_id = $source_id > $user_id ? $source_id : $user_id;
                $exist = AnMsgRO::find()
                    ->where('small_id = :small_id AND big_id = :big_id', [':small_id' => $small_id, ':big_id' => $big_id])
                    ->exists();
                break;
            case MReport::TARGET_ALBUM:
                $exist = MAlbum::find()->where(['id' => $source_id])->exists();
                break;
            case MReport::TARGET_NODE_DANMAKU:
                $exist = MDanmaku::find()->where(['id' => $source_id])->exists();
                break;
            default:
                throw new HttpException(400, Yii::t('app/error', 'Report content does not exist'), 200370004);
        }
        if (!$exist) throw new HttpException(400, Yii::t('app/error', 'Report content does not exist'), 200370004);

        $model->target = $target;
        $model->source_id = $source_id;
        $model->reason = $reason;
        $model->content = $content;
        if (!$model->save())
            return $model->getErrors() ?: Yii::t('app/error', 'Report failed');

        return Yii::t('app/base', 'Report successfully');

    }

    /**
     * @api {get} /person/default-head-sound 默认头部音频
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/default-head-sound
     * @apiSampleRequest /person/default-head-sound
     * @apiDescription /mobile/site/defaultHeadSound
     * @apiVersion 0.1.0
     * @apiName default-head-sound
     * @apiGroup person
     *
     *
     * @apiSuccess {String} status 请求状态
     * @apiSuccess {Object} info  请求数据详情
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "info": [
     *         {
     *           "id": 233,
     *           "title": "标题",
     *           "soundurl": "http://static-test.missevan.com/sound/201202/08/test1.m4a",
     *           "soundurl_64": "http://static-test.missevan.com/sound/201202/08/test2.m4a",
     *           "soundurl_128": "http://static-test.missevan.com/sound/201202/08/test3.m4a"
     *         }
     *       ]
     *     }
     */
    public function actionDefaultHeadSound()
    {
        $sounds = [];
        $sound_ids = Yii::$app->params['defaultAvatarSounds'] ?? [];
        if (!empty($sound_ids)) {
            $sounds = MSound::find()
                ->select('id, soundstr, soundurl_64, soundurl_128')
                ->allByColumnValues('id', $sound_ids);
        }
        $upos_flag = true;
        // WORKAROUND: iOS < 4.7.8 不返回音频 upos 签名地址
        if (Equipment::isAppOlderThan('4.7.8', null)) {
            $upos_flag = false;
        }

        return array_map(function ($sound) use ($upos_flag) {
            if ($upos_flag) {
                MSound::getSoundSignUrls($sound, true);
            }
            return [
                'id' => $sound->id,
                'title' => $sound->soundstr,
                'soundurl' => $sound->soundurl,
                'soundurl_64' => $sound->soundurl_64,
                'soundurl_128' => $sound->soundurl_128,
            ];
        }, $sounds);
    }

    /**
     * @api {get} /person/count-action 统计收藏，已购的剧集，播放历史的数量
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/count-action
     * @apiSampleRequest /person/count-action
     * @apiVersion 0.1.0
     * @apiName count-action
     * @apiGroup person
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "collection_count": 40,  // 音单收藏数量
     *         "history_count": 89,  // 播放历史中的音频数量
     *         "drama_bought_count": 6  // 已购的剧集数量
     *       }
     *     }
     */
    public function actionCountAction()
    {
        $user_id = Yii::$app->user->id;

        // 收藏（收藏的音单、自建的音单）
        $collection_count = MCollectAlbum::find()->where(['user_id' => $user_id])->count();
        $user_album_count = MAlbum::find()->where(['user_id' => $user_id])->count();
        $count_action['collection_count'] = $collection_count + $user_album_count;
        // 收听历史
        $count_action['history_count'] = MUserHistory::getCount($user_id);
        // 已购
        $count_action['drama_bought_count'] = TransactionLog::getDramaBoughtCount($user_id);
        // 记录用户访问我听页的时间
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_USER_FEED_LAST_REQUEST_TIME, $user_id);
        $redis->set($key, $_SERVER['REQUEST_TIME'], ['ex' => ONE_WEEK]);
        return $count_action;
    }

    /**
     * @api {get} /person/drama-feed 我听-追剧动态
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/drama-feed
     * @apiSampleRequest /person/drama-feed
     *
     * @apiVersion 0.1.0
     * @apiName drama-feed
     * @apiGroup person
     *
     * @apiParam {number=1,2,3} [type=1] 排序方式 1：最近更新；2：最新追剧；3：最近收听
     * @apiParam {Number} [p=1] 页码
     * @apiParam {Number} [pagesize=20] 每页个数
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 动态信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info":{
     *         "Datas": [
     *           {
     *             "id": 725,
     *             "cover": 'https://...',
     *             "name": 2412,
     *             "pay_type": 10110,
     *             "newest": '第六期',
     *             "lastupdate_time": 1234567,
     *             "saw_episode": '第四期',
     *             "is_saw": 1,  // 剧集更新是否已被查看
     *             "corner_mark": {  // 无剧集角标时不返回该字段
     *               "text": "已购",
     *               "text_color": "#ffffff",
     *               "bg_start_color": "#e66465",
     *               "bg_end_color": "#e66465",
     *               "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *             }
     *           }
     *         ],
     *         "pagination": {
     *           "p": "3",
     *           "count": 25,
     *           "pagesize": "1",
     *           "maxpage": 25
     *         }
     *       }
     *     }
     */
    public function actionDramaFeed(int $type = 1, int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        if (!$type) {
            throw new HttpException(400, '参数错误');
        }
        $user_id = Yii::$app->user->id;
        $feed = MPersonDramaPage::getDramaFeed($user_id, $type, $page, $page_size);
        Mowangskuser::updateAll(['feednum' => 0], 'id = :id', [':id' => $user_id]);
        return $feed;
    }

    /**
     * @api {get} /person/drama-bought 用户已购的剧集信息
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/drama-bought
     * @apiSampleRequest drama-bought
     *
     * @apiVersion 0.1.0
     * @apiName drama-bought
     * @apiGroup person
     *
     * @apiParam {number=1,2} [type=1] 排序方式 1：按购买时间降序；2：按剧集名 A-Z；
     * @apiParam {String} [s] 搜索关键字
     * @apiParam {Number} [page=1] 页码
     * @apiParam {Number} [page_size=20] 每页个数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "data": [{
     *           "id": 30,
     *           "name": "望星辰之草原情殇",
     *           "cover_color": 12434877,
     *           "cover": "https://static.missevan.com/dramacoversmini/201807/02/0eb8d2e5fd5b9f112930.jpg",
     *           "newest": "第四期",
     *           "lastupdate_time": 1530502200,
     *           "pay_type": 2,
     *           "saw_episode": ""
     *         },
     *         {
     *           "id": 44,
     *           "name": "爱情猫咪",
     *           "cover_color": 12434877,
     *           "cover": "https://static.missevan.com/dramacoversmini/201604/15/cb390657a26a232b8090920.jpg",
     *           "newest": "第三期",
     *           "lastupdate_time": 1505274934,
     *           "pay_type": 1,
     *           "saw_episode": "",
     *           "suborders_num": 1  // 单集付费类型的剧集，显示已购的单集数量
     *         }],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 1,
     *           "count": 6,
     *           "pagesize": 20
     *         },
     *         "delete_count": 10 // 当前删除的剧集数
     *       }
     *     }
     */
    public function actionDramaBought(int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        $order_type = (int)Yii::$app->request->get('type', TransactionLog::ORDER_BY_BOUGHT_TIME);
        $s = trim(Yii::$app->request->get('s'));
        $page_obj = PaginationParams::process($page, $page_size);
        $user_id = Yii::$app->user->id;

        $result = TransactionLog::getDramaBought($user_id, $order_type, TransactionLog::SOURCE_BOUGHT,
            $s, $page_obj->page, $page_obj->page_size);
        // WORKAROUND：iOS < 4.8.7、Android < 5.7.3 时，返回老结构
        if (Equipment::isAppOlderThan('4.8.7', '5.7.3')) {
            return $result;
        }
        return [
            'data' => $result->Datas,
            'pagination' => $result->pagination,
            'delete_count' => TransactionLog::getHideDramaBoughtCount($user_id),
        ];
    }

    /**
     * @api {get} /person/drama-recover 用户恢复剧集页信息
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/drama-recover
     * @apiSampleRequest drama-recover
     *
     * @apiVersion 0.1.0
     * @apiName drama-recover
     * @apiGroup person
     *
     * @apiParam {Number} [page=1] 页码
     * @apiParam {Number} [page_size=20] 每页个数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "data": [{
     *           "id": 30,
     *           "name": "望星辰之草原情殇",
     *           "cover_color": 12434877,
     *           "cover": "https://static.missevan.com/dramacoversmini/201807/02/0eb8d2e5fd5b9f112930.jpg",
     *           "newest": "第四期",
     *           "lastupdate_time": 1530502200,
     *           "pay_type": 2,
     *           "saw_episode": ""
     *         },
     *         {
     *           "id": 44,
     *           "name": "爱情猫咪",
     *           "cover_color": 12434877,
     *           "cover": "https://static.missevan.com/dramacoversmini/201604/15/cb390657a26a232b8090920.jpg",
     *           "newest": "第三期",
     *           "lastupdate_time": 1505274934,
     *           "pay_type": 1,
     *           "saw_episode": "",
     *           "suborders_num": 1  // 单集付费类型的剧集，显示已购的单集数量
     *         }],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 1,
     *           "count": 6,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     */
    public function actionDramaRecover(int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        $page_obj = PaginationParams::process($page, $page_size);
        $user_id = Yii::$app->user->id;

        $result = TransactionLog::getDramaBought($user_id, TransactionLog::ORDER_BY_BOUGHT_TIME,
            TransactionLog::SOURCE_DELETE, '', $page_obj->page, $page_obj->page_size);
        return [
            'data' => $result->Datas,
            'pagination' => $result->pagination
        ];
    }

    /**
     * @api {post} /person/hide-drama-purchase-order 隐藏用户剧集购买订单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/hide-drama-purchase-order
     * @apiSampleRequest hide-drama-purchase-order
     *
     * @apiVersion 0.1.0
     * @apiName hide-drama-purchase-order
     * @apiGroup person
     *
     * @apiParam {Number[]} ids 剧集 IDs
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "删除成功"
     *     }
     */
    public function actionHideDramaPurchaseOrder()
    {
        $ids = Yii::$app->request->post('ids');
        if (empty($ids) || !MUtils2::isUintArr($ids)) {
            throw new HttpException(400, '参数错误');
        }
        $DELETE_MAX_COUNT = 20;
        if (count($ids) > $DELETE_MAX_COUNT) {
            throw new HttpException(400, '一次删除剧集数量不能超过 ' . $DELETE_MAX_COUNT . ' 部');
        }
        $user_id = Yii::$app->user->id;
        TransactionLog::hideDramaPurchaseOrder($user_id, $ids);
        return '删除成功';
    }

    /**
     * @api {post} /person/recover-drama-purchase-order 恢复用户剧集购买订单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/recover-drama-purchase-order
     * @apiSampleRequest recover-drama-purchase-order
     *
     * @apiVersion 0.1.0
     * @apiName recover-drama-purchase-order
     * @apiGroup person
     *
     * @apiParam {Number[]} ids 剧集 IDs
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "恢复成功"
     *     }
     */
    public function actionRecoverDramaPurchaseOrder()
    {
        $ids = Yii::$app->request->post('ids');
        if (empty($ids) || !MUtils2::isUintArr($ids)) {
            throw new HttpException(400, '参数错误');
        }
        $RECOVER_MAX_COUNT = 1;  // 暂不支持批量操作，单次恢复剧集数量最多为 1 部
        if (count($ids) > $RECOVER_MAX_COUNT) {
            throw new HttpException(400, '一次恢复剧集数量不能超过 ' . $RECOVER_MAX_COUNT . ' 部');
        }
        $user_id = Yii::$app->user->id;
        TransactionLog::recoverDramaPurchaseOrder($user_id, $ids);
        return '恢复成功';
    }

    /**
     * @api {post} /person/image-feed-back 反馈上传图片接口
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/image-feed-back
     * @apiSampleRequest person/image-feed-back
     * @apiVersion 0.1.0
     * @apiName image-feed-back
     * @apiGroup person
     *
     * @apiParam {Number} feedback_id 反馈 ID
     * @apiParam {number=0,1} [retry=0] 是否是重复发送的请求 0：否；1：是
     * @apiParam {Number} [images_num=0] 多图上传时，用户选择上传的图片数量
     * @apiParam {Number} [image_index=0] 多图上传时，图片位置
     * @apiParam {File} image_file 反馈图片
     * @apiParam {String} equipment 小尾巴
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Number} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": 9946
     *     }
     */
    public function actionImageFeedBack()
    {
        if (!Equipment::isAppOlderThan('4.2.9', '5.2.0')) {
            // iOS App 版本大于 4.2.9，安卓 App 版本大于 5.2.0 调用新控制器里对应的接口
            throw new HttpException(400, '非法请求');
        }
        $feedback_id = (int)Yii::$app->request->post('feedback_id');
        $retry = (int)Yii::$app->request->post('retry', 0);
        $images_num = (int)Yii::$app->request->post('images_num', 0);
        $image_index = (int)Yii::$app->request->post('image_index', 0);
        $equipment = trim(Yii::$app->request->post('equipment'));
        $user_id = (int)Yii::$app->user->id;
        if (Equipment::isAppOlderThan('4.2.5', null)) {
            // iOS 4.2.4 版本反馈传图的小尾巴和反馈文本接口的小尾巴保持一致
            $equipment = '---' . $equipment;
        }
        return AnFeedback::createImageFeedback($feedback_id, $images_num, $image_index, $retry, $equipment, $user_id);
    }

    /**
     * @api {get} /person/homepage-icons 我的页图标
     * @apiVersion 0.1.0
     * @apiName homepage-icons
     * @apiGroup person
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Number} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "id": 1,
     *           "title": "测试图标",
     *           "icon": "https://static.missevan.com/test.jpg",
     *           "dark_icon": "https://static.missevan.com/test.jpg",
     *           "url": "missevan://homepage"
     *         },
     *         {
     *           "id": 2,
     *           "title": "测试图标 2",
     *           "icon": "https://static.missevan.com/test.jpg",
     *           "dark_icon": "https://static.missevan.com/test.jpg",
     *           "url": "missevan://message",
     *           "name": "message"  // 用于标记 icon 的唯一标识
     *         },
     *         {
     *           "id": 3,
     *           "title": "我的钱包",
     *           "icon": "https://static.missevan.com/test.jpg",
     *           "dark_icon": "https://static.missevan.com/test.jpg",
     *           "url": "missevan://wallet",
     *           "name": "wallet",
     *           "is_new_user": true  // 标记新人用户，当「name = "wallet"」并且「新用户」或「新设备」时会返回，用于显示「首充福利」角标
     *         }
     *       ]
     *     }
     */
    public function actionHomepageIcons()
    {
        return MHomepageIcon::getIcons();
    }

    /**
     * @api {get} /person/search-user-sound{?user_id,s,page,page_size} 个人主页搜索音频接口
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/search-user-sound
     * @apiVersion 0.1.0
     * @apiName search-user-sound
     * @apiGroup person
     *
     * @apiParam (Query 参数) {Number} user_id 用户 ID
     * @apiParam (Query 参数) {String} s 搜索关键字
     * @apiParam (Query 参数) {Number} [page=1] 页数
     * @apiParam (Query 参数) {Number} [page_size=20] 每页个数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [{
     *           "id": 5240511,
     *           "soundstr": "小树2",
     *           "view_count": 921,
     *           "pay_type": 1,
     *           "checked": 1,
     *           "all_comments": 14,
     *           "front_cover": "https://static.missevan.com/coversmini/201711/23/eb84cf2742bcca61f195706.jpg",
     *           "need_pay": 1,
     *           "price": 0
     *         }],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 1,
     *           "count": 6,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     */
    public function actionSearchUserSound($s, int $user_id, int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        // 判断能否访问该用户个人主页
        self::canViewUser($user_id);
        // 判断分页参数是否合法
        $page_obj = PaginationParams::process($page, $page_size);
        $s = trim($s);
        if ($s === '') {
            throw new HttpException(400, '搜索关键词不可为空', 200510002);
        }
        $view_user_id = $user_id;
        $user_id = Yii::$app->user->id;
        $search_sounds = MSound::getSearch($s, $page_obj->page, $page_obj->page_size, $user_id,
            Discovery::SEARCH_SOUND_SORT_TIME, 0, $view_user_id, 0, Go::SCENARIO_PERSON_SEARCH);
        if (!empty($search_sounds['Datas'])) {
            // 过滤不需要返回给客户端的信息
            $search_sounds['Datas'] = array_map(function ($item) {
                return [
                    'id' => $item['id'],
                    'soundstr' => $item['soundstr'],
                    'view_count' => $item['view_count'],
                    'pay_type' => $item['pay_type'],
                    'checked' => $item['checked'],
                    'all_comments' => $item['all_comments'],
                    'front_cover' => $item['front_cover'],
                    'need_pay' => $item['need_pay'],
                    'price' => $item['price'],
                ];
            }, $search_sounds['Datas']);
        }
        return $search_sounds;
    }

    /**
     * @api {get} /person/homepage{?user_id} 个人主页
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/homepage
     * @apiDescription
     * @apiVersion 0.1.0
     * @apiName homepage
     * @apiGroup person
     *
     * @apiParam (Query 参数) {Number} user_id 用户 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "module_id": 2,
     *           "sort": 2,
     *           "title": "剧集作品",
     *           "type": 2,
     *           "elements_num": 12,  // 剧集作品总数,
     *           "more": "missevan://user/346286/drama",  // 更多协议地址
     *           "elements": [
     *             {
     *               "id": 31552,
     *               "name": "你的距离",
     *               "cover": "http://static.missevan.com/dramacoversmini/test.png",
     *               "cover_color": 1975582
     *             }
     *           ]
     *         },
     *         {
     *           "module_id": 3,
     *           "sort": 3,
     *           "title": "声音",
     *           "type": 3,
     *           "elements_num": 12,  // 声音总数
     *           "more": "missevan://user/346286/sound?type=1",  // 更多协议地址
     *           "elements": [
     *             {
     *               "id": 2768399,
     *               "soundstr": "十二月您好",
     *               "view_count": 15626,
     *               "all_comments": 1276,
     *               "front_cover": "https://static.missevan.com/coversmini/test.jpg"
     *             }
     *           ]
     *         },
     *         {
     *           "module_id": 4,
     *           "sort": 4,
     *           "title": "直播回放",
     *           "type": 4,
     *           "elements_num": 47,
     *           "more": "missevan://user/346286/sound?type=3",
     *           "elements": [
     *             {
     *               "id": 5572,
     *               "name": "全职高手",
     *               "cover": "http://static.missevan.com/dramacoversmini/test.png",
     *               "cover_color": 1975582
     *             }
     *           ]
     *         },
     *         {
     *           "module_id": 5,
     *           "sort": 5,
     *           "title": "追剧",
     *           "type": 5,
     *           "elements_num": 47,
     *           "is_hidden": true,  // UP 主查看自己的个人主页时，显示隐藏图标，其他人查看时不返回该字段
     *           "more": "missevan://user/346286/subscription",
     *           "elements": [
     *             {
     *               "id": 5572,
     *               "name": "全职高手",
     *               "cover": "http://static.missevan.com/dramacoversmini/test.png",
     *               "cover_color": 1975582,
     *               "corner_mark": {  // 无剧集角标时不返回该字段（自己视角展示角标，他人视角不展示）
     *                 "text": "已购",
     *                 "text_color": "#ffffff",
     *                 "bg_start_color": "#e66465",
     *                 "bg_end_color": "#e66465",
     *                 "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *               }
     *             }
     *           ]
     *         },
     *         {
     *           "module_id": 6,
     *           "sort": 6,
     *           "title": "收藏",
     *           "type": 6,
     *           "elements_num": 1,
     *           "is_hidden": true,  // UP 主查看自己的个人主页时，显示隐藏图标，其他人查看时不返回该字段
     *           "more": "missevan://user/346286/album",
     *           "elements": [
     *             {
     *               "id": 5572,
     *               "title": "TA 喜欢的音频",
     *               "view_count": 12,
     *               "music_count": 12,
     *               "front_cover": "http://static.missevan.com/dramacoversmini/test.png"
     *             }
     *           ]
     *         }
     *       ]
     *     }
     */
    public function actionHomepage(int $user_id)
    {
        if ($user_id === 0) {
            if (Yii::$app->user->isGuest) {
                Yii::$app->user->loginRequired();
            }
            $view_user_id = $user_id = (int)Yii::$app->user->id;
        } elseif ($user_id < 0) {
            throw new HttpException(400, '参数错误');
        } else {
            // 被访问者 ID
            $view_user_id = $user_id;
            // 访问者 ID
            $user_id = (int)Yii::$app->user->id;
        }
        // 判断能否访问该用户的个人主页
        self::canViewUser($view_user_id);
        $module_info = [];
        if (Equipment::isAppVersion(Equipment::iOS, '4.5.8') ||
                Equipment::isAppVersion(Equipment::iOS, '4.5.9') ||
                Equipment::isAppVersion(Equipment::Android, '5.4.8')) {
            // 兼容 iOS 4.5.8、4.5.9 和 Android 5.4.8 懒加载问题
            // 第一次请求 position 参数为 1,2,3 直接返回所有模块数据，之后的请求返回空数组
            $POSITION_DEFAULT = '1,2,3';
            $position = trim(Yii::$app->request->get('position', $POSITION_DEFAULT));
            if ($POSITION_DEFAULT !== $position) {
                return [];
            }
            // iOS 4.5.8 和 4.5.9 版本，Android 5.4.8 版本需要返回个人信息
            // 个人信息模块
            $user_info = self::getUserInfo($view_user_id, $user_id);
            $module_info[] = [
                'module_id' => self::MODULE_ID_USER_INFO,
                'sort' => 1,
                'title' => '个人信息',
                'type' => self::MODULE_TYPE_USER_INFO,
                'elements_num' => 1,
                'more' => '',
                'elements' => [$user_info]
            ];
        }
        // 获取模块内容
        return array_merge($module_info, self::getHomepageModule($view_user_id, $user_id));
    }

    /**
     * 判断能否访问该用户的个人主页
     *
     * @param int $user_id
     * @throws HttpException
     */
    private static function canViewUser(int $user_id)
    {
        if ($user_id <= 0) {
            throw new HttpException(400, '参数错误', 201010002);
        }
        $confirm = Mowangskuser::find()->select('confirm')->where(['id' => $user_id])->scalar();
        if ($confirm === false) {
            throw new HttpException(404, '用户不存在', 200020001);
        }
        $confirm = (int)$confirm;
        if (Mowangskuser::isDeleted($confirm) &&
                !Equipment::isAppOlderThan('4.5.7', '5.4.7')) {
            // WORKAROUND: 用户已注销，老版本正常显示（用户名和头像已改为注销后的用户名和头像）
            // 新版本返回指定 code，客户端根据 code 做相应处理
            throw new HttpException(403, '账号已注销', 300010005);
        }
        if (Mowangskuser::isPrivacyUser($confirm)) {
            // 隐私设置用户不对外展示个人主页
            throw new HttpException(403, 'UP 主设置了隐私，无法访问');
        }
    }

    /**
     * 获取个人主页模块信息
     *
     * @param int $view_user_id 个人主页 UP 主 ID
     * @param int $user_id 当前用户 ID
     * @return array
     * @throws \Exception
     */
    private static function getHomepageModule(int $view_user_id, int $user_id = 0)
    {
        $is_self = $view_user_id === $user_id;
        $PAGE_SIZE = 3;
        $module_info = [];
        // 获取用户配置
        $user_config = MUserConfig::getUserConfig($view_user_id);
        $user_config = $user_config->app_config;
        // 生成个人主页数据的函数
        $callable = function () use ($view_user_id, $user_id, $is_self, $module_info, $user_config, $PAGE_SIZE) {
            // 非 UP 主视角，当前用户 ID 给 0 值
            $user_id = $is_self ? $user_id : 0;
            // 剧集作品模块
            $user_dramas = Drama::getUserDrama($view_user_id, $user_id, 1, $PAGE_SIZE);
            $module_info[] = [
                'module_id' => self::MODULE_ID_USER_DRAMA,
                'sort' => 2,
                'title' => '剧集作品',
                'type' => self::MODULE_TYPE_USER_DRAMA,
                'elements_num' => $user_dramas['pagination']['count'],
                'more' => self::getUserEntrance($view_user_id, 'drama'),
                'elements' => $user_dramas['Datas']
            ];
            // 声音模块
            $user_sounds = MSound::getUserSound(MSound::USER_SOUND_TYPE_OWN, $view_user_id, MSound::SORT_NEW, $PAGE_SIZE);
            $sound_own_type = MSound::USER_SOUND_TYPE_OWN;
            $module_info[] = [
                'module_id' => self::MODULE_ID_USER_SOUND,
                'sort' => 3,
                'title' => '声音',
                'type' => self::MODULE_TYPE_USER_SOUND,
                'elements_num' => $user_sounds->pagination['count'],
                'more' => "missevan://user/{$view_user_id}/sound?type={$sound_own_type}",
                'elements' => $user_sounds->Datas
            ];
            // 直播回放模块
            $user_sounds = MSound::getUserSound(MSound::USER_SOUND_TYPE_LIVE, $view_user_id, MSound::SORT_NEW, $PAGE_SIZE);
            $sound_live_type = MSound::USER_SOUND_TYPE_LIVE;
            $module_info[] = [
                'module_id' => self::MODULE_ID_USER_LIVE_SOUND,
                'sort' => 4,
                'title' => '直播回放',
                'type' => self::MODULE_TYPE_USER_LIVE_SOUND,
                'elements_num' => $user_sounds->pagination['count'],
                'more' => "missevan://user/{$view_user_id}/sound?type={$sound_live_type}",
                'elements' => $user_sounds->Datas
            ];
            // 开启了在个人主页公开“我的追剧”，所有人都可以看见追剧模块
            // 关闭了在个人主页公开“我的追剧”，只有 UP 主自己能看到追剧模块
            if ($is_self || $user_config[MUserConfig::APP_CONF_TYPE_SHOW_SUBSCRIBE_DRAMA] === MUserConfig::APP_CONF_ENABLE) {
                // 追剧模块
                // 存在用户同时追了新修剧和老剧，新修剧追剧记录隐藏，导致第一页为空而不显示追剧模块，多查询条数（20）再进行截取
                $user_subscribe_dramas = MSound::getSubscribedDrama($view_user_id, $user_id, 1, 20);
                $user_subscribe_dramas->Datas = array_slice($user_subscribe_dramas->Datas, 0, $PAGE_SIZE);
                $subscribe_dramas_info = [
                    'module_id' => self::MODULE_ID_USER_SUBSCRIBE_DRAMA,
                    'sort' => 5,
                    'title' => '追剧',
                    'type' => self::MODULE_TYPE_USER_SUBSCRIBE_DRAMA,
                    'elements_num' => $user_subscribe_dramas->pagination['count'],
                    'more' => self::getUserEntrance($view_user_id, 'subscription'),
                    'elements' => $user_subscribe_dramas->Datas
                ];
                // 关闭了在个人主页公开“我的追剧”，返回 is_hidden 字段
                if ($is_self && $user_config[MUserConfig::APP_CONF_TYPE_SHOW_SUBSCRIBE_DRAMA] !== MUserConfig::APP_CONF_ENABLE) {
                    // 隐藏图标的标识字段
                    $subscribe_dramas_info['is_hidden'] = true;
                }
                $module_info[] = $subscribe_dramas_info;
            }
            // 开启了在个人主页公开“我的收藏”，所有人都可以看见收藏模块
            // 关闭了在个人主页公开“我的收藏”，只有 UP 主自己能看到收藏模块
            if ($is_self || $user_config[MUserConfig::APP_CONF_TYPE_SHOW_USER_COLLECT] === MUserConfig::APP_CONF_ENABLE) {
                // 收藏模块
                $user_collects = MLikeSound::getHomepageCollects($view_user_id, $user_id);
                $user_collects_info = [
                    'module_id' => self::MODULE_ID_USER_COLLECT,
                    'sort' => 6,
                    'title' => '收藏',
                    'type' => self::MODULE_TYPE_USER_COLLECT,
                    'elements_num' => $user_collects['elements_num'],
                    'more' => self::getUserEntrance($view_user_id, 'album'),
                    'elements' => $user_collects['user_collects'],
                ];
                // 关闭了在个人主页公开“我的收藏”，返回 is_hidden 字段
                if ($is_self && $user_config[MUserConfig::APP_CONF_TYPE_SHOW_USER_COLLECT] !== MUserConfig::APP_CONF_ENABLE) {
                    // 隐藏图标的标识字段
                    $user_collects_info['is_hidden'] = true;
                }
                $module_info[] = $user_collects_info;
            }
            // 图片模块
            $image_module = [
                'module_id' => self::MODULE_ID_USER_IMAGE,
                'sort' => 7,
                'title' => '图片',
                'type' => self::MODULE_TYPE_USER_IMAGE,
                'elements_num' => 0,
                'more' => self::getUserEntrance($view_user_id, 'image'),
                'elements' => []
            ];
            if ($is_self) {
                // 只有 UP 主自己能看到图片模块
                $user_images = MImage::getUserPics($view_user_id, $PAGE_SIZE);
                $image_module['elements_num'] = $user_images->pagination['count'];
                $image_module['elements'] = $user_images->Datas;
            }
            $module_info[] = $image_module;
            return $module_info;
        };
        // 他人视角模块数据使用缓存，UP 主视角不使用缓存
        if (!$is_self) {
            // 他人视角使用缓存
            $key = MUtils2::generateCacheKey(KEY_PERSON_HOMEPAGE, $view_user_id);
            $module_info = MUtils2::getOrSetDistrubutedCache($key, $callable, FIVE_MINUTE, function ($err) {
                // 错误信息记录到日志，返回空数组进行服务降级，避免缓存穿透
                Yii::error('获取个人主页信息出错：' . $err->getMessage(), __METHOD__);
                return [];
            });
        } else {
            $module_info = call_user_func($callable);
        }
        return $module_info;
    }

    public static function getUserEntrance(int $view_user_id, string $name): string
    {
        if (Yii::$app->equip->isFromMiMiApp()) {
            return sprintf('mimi://user/%d/' . $name, $view_user_id);
        }
        return sprintf('missevan://user/%d/' . $name, $view_user_id);
    }

    /**
     * @api {get} /person/search-favorite-sound{?q,page,page_size} 搜索我喜欢的音频
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/person/search-favorite-sound
     * @apiDescription 搜索我喜欢的音频
     *
     * @apiVersion 0.1.0
     * @apiName search-favorite-sound
     * @apiGroup person
     *
     * @apiParam (Query 参数) {String} q 搜索关键字
     * @apiParam (Query 参数) {Number} [page=1] 页数
     * @apiParam (Query 参数) {Number} [page_size=20] 每页个数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [{
     *           "id": 5240511,
     *           "soundstr": "小树2",
     *           "view_count": 921,
     *           "duration": 542770,
     *           "front_cover": "http://static-test.missevan.com/coversmini/201701/24/bddc9233f39bb3093048.png",
     *           "username": "违规昵称_YG5op67Y",
     *           "pay_type": 0,
     *           "need_pay": 0  // 付费状态，0：免费；1：付费剧集未付费；2：付费剧集已付费
     *         }],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 1,
     *           "count": 6,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 400 Not Found
     *     {
     *       "success": false,
     *       "code": 200510002,
     *       "info": "什么都没有找到呢~"
     *     }
     */
    public function actionSearchFavoriteSound(string $q, int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        if (($q = trim($q)) === '') {
            throw new HttpException(400, '什么都没有找到呢~', 200510002);
        }
        $pagination_params = PaginationParams::process($page, $page_size);
        $user_id = Yii::$app->user->id;
        $select = 's.id, s.soundstr, s.duration, s.view_count, s.pay_type, s.cover_image, s.username';
        $query = MSound::find()
            ->alias('s')
            ->select($select)
            ->leftJoin(MLikeSound::tableName() . ' AS l', 'l.sound_id = s.id')
            ->where('l.user_id = :owner', [':owner' => $user_id])
            ->andFilterWhere(['LIKE', 's.soundstr', $q])
            ->orderBy(['s.view_count' => SORT_DESC, 's.id' => SORT_DESC]);
        $return_model = MUtils::getPaginationModels($query, $pagination_params->page_size);
        MSound::checkNeedPay($return_model->Datas, $user_id);
        $return_model->Datas = array_map(function ($sound) {
            return [
                'id' => (int)$sound->id,
                'soundstr' => $sound->soundstr,
                'duration' => (int)$sound->duration,
                'view_count' => (int)$sound->view_count,
                'front_cover' => $sound->front_cover,
                'username' => $sound->username,
                'pay_type' => (int)$sound->pay_type,
                'need_pay' => (int)$sound->need_pay,
            ];
        }, $return_model->Datas);
        return $return_model;
    }

    /**
     * @api {get} /person/sobot-offline-msg-count 智齿客服未读消息数量
     *
     * @apiVersion 0.1.0
     * @apiName sobot-offline-msg-count
     * @apiGroup person
     *
     * @apiParam {String} [sobot_visitor_id] 智齿访客 ID（客户端初始化智齿时获得，没有时不传）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "count": 2  // 未读消息数量
     *       }
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     {
     *       "success": false,
     *       "code": 201010002,
     *       "info": "参数错误"
     *     }
     */
    public function actionSobotOfflineMsgCount(string $sobot_visitor_id = '')
    {
        if (Equipment::isAppOlderThan(null, '6.1.5')) {
            // WORKAROUND: 由于技术方案变更，客户端计划 6.1.5 正式发版，安卓 6.1.0 已经发版过，因此对安卓 < 6.1.5 版本直接返回 count 为 0
            return ['count' => 0];
        }
        $sobot_visitor_id = trim($sobot_visitor_id);
        $user_id = Yii::$app->user->id;
        if ($user_id && $m_sobot_user = MSobotUser::findOne(['user_id' => $user_id])) {
            // 登录用户使用服务端存储的 visitor_id
            $sobot_visitor_id = $m_sobot_user->visitor_id;
        }
        return ['count' => MSobotUser::getSobotOfflineMsgCount($sobot_visitor_id)];
    }

    /**
     * @api {post} /person/update-sobot-status 更新智齿未读消息状态或保存登录用户的 visitor_id
     *
     * @apiDescription 此接口用于更新智齿未读消息状态为已读或保存登录用户的 visitor_id, 游客的 visitor_id 由客户端自行保存 \
     * 此接口的调用时机：
     * 1. 登录用户初始化智齿成功后调用一次，此时 type 传 1, 用于更新登录用户的智齿 visitor_id
     * 2. 用户（游客或登录用户）进入智齿客服聊天页面时调用一次，此时 type 传 2, 用于更新智齿未读消息状态
     * 3. 用户（游客或登录用户）在智齿客服聊天页面接收到对方（机器人或客服）的新消息或发新消息给对方（机器人或客服）后每 10s 合并调用一次，此时 type 传 2, 用于更新智齿未读消息状态
     * （注意：10s 内有多条消息也只调用一次，不是每接收或发送一条新消息都在 10 秒后调用一次，10 秒后没有新消息就不用调用）
     * 4. 用户（游客或登录用户）退出智齿客服聊天页面时调用一次，客户端需要确保此次是退出智齿客服聊天页面后的最后一次调用（即不再触发第 3 种情况的调用），此时 type 传 2, 用于更新智齿未读消息状态
     *
     * @apiVersion 0.1.0
     * @apiName update-sobot-status
     * @apiGroup person
     *
     * @apiParam {number=1,2} type 更新类型（1: 更新登录用户的智齿 visitor_id; 2: 更新智齿未读消息状态）
     * @apiParam {String} partner_id 跟智齿对接的 ID（游客传空字符串即可）
     * @apiParam {String} sobot_visitor_id 智齿访客 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "更新成功"
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     {
     *       "success": false,
     *       "code": 201010002,
     *       "info": "参数错误"
     *     }
     */
    public function actionUpdateSobotStatus()
    {
        if (Equipment::isAppOlderThan(null, '6.1.5')) {
            // WORKAROUND: 由于技术方案变更，客户端计划 6.1.5 正式发版，安卓 6.1.0 已经发版过，因此对安卓 < 6.1.5 版本直接返回更新成功
            return '更新成功';
        }
        $type = (int)Yii::$app->request->post('type');
        $partner_id = trim(Yii::$app->request->post('partner_id'));
        $sobot_visitor_id = trim(Yii::$app->request->post('sobot_visitor_id'));
        if (!in_array($type, [self::UPDATE_SOBOT_TYPE_VISITOR_ID, self::UPDATE_SOBOT_TYPE_MSG_STATUS])
                || $sobot_visitor_id === '') {
            throw new HttpException(400, '参数错误');
        }
        $user_id = Yii::$app->user->id;
        if ($user_id) {
            if (!$partner_id) {
                throw new HttpException(400, '参数错误');
            }
            // 登录用户校验参数 partner_id 是否与服务器生成的一致
            $user_partner_id = UserAddendum::getSobotPartnerId($user_id);
            if ($user_partner_id !== $partner_id) {
                throw new HttpException(400, '参数错误');
            }
            if ($type === self::UPDATE_SOBOT_TYPE_MSG_STATUS && !MSobotUser::isValidVisitorId($user_id, $sobot_visitor_id)) {
                // 登录用户更新智齿未读消息状态时，传入的 visitor_id 无效
                throw new HttpException(400, '参数错误');
            }
            if ($type === self::UPDATE_SOBOT_TYPE_VISITOR_ID) {
                // 更新或保存登录用户的 visitor_id
                MSobotUser::updateVisitorId($user_id, $sobot_visitor_id);
            }
        }
        if ($type === self::UPDATE_SOBOT_TYPE_MSG_STATUS) {
            try {
                Yii::$app->serviceRpc->sobotUpdateStatus($sobot_visitor_id);
            } catch (Exception $e) {
                // 如果 RPC 请求失败，则降级并记录错误日志
                Yii::error(sprintf('更新智齿未读消息状态为已读 rpc 接口出错，sobot_visitor_id: %s, error: %s',
                    $sobot_visitor_id, $e->getMessage()), __METHOD__);
                return '更新失败';
            }
        }
        return '更新成功';
    }
}
