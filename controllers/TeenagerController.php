<?php
/**
 * Created by PhpStorm.
 * User: luoxia
 * Date: 2020/05/12
 * Time: 11:06
 */

namespace app\controllers;

use app\components\util\SSOClient;
use app\middlewares\Controller;
use app\models\Mowangskuser;
use app\models\SoundViewCount;
use app\models\TeenagerRecommendedElements;
use Yii;
use yii\db\Expression;
use app\components\base\filter\AccessControl;
use yii\filters\VerbFilter;
use yii\web\HttpException;

class TeenagerController extends Controller
{
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'recommend' => ['get'],
                'set-status' => ['post'],
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'only' => [
                'set-status'
            ],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => [
                        'set-status',
                    ],
                    'roles' => ['@']
                ]
            ],
        ];
        return $behaviors;
    }

    /**
     * @api {get} /teenager/recommend{?type,order,page,page_size} 根据 type 获取推荐青少年单音/剧集
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/teenager/recommend
     * @apiSampleRequest /teenager/recommend
     * @apiDescription 根据 type 获取推荐青少年单音/剧集
     *
     * @apiVersion 0.1.0
     * @apiName recommend
     * @apiGroup teenager
     *
     * @apiParam {number=0,1} type 类型（0 音频、1 剧集）
     * @apiParam {number=0,1} [order=0] 排序方式（0 随机、1 按播放量）
     * @apiParam {Number} [page=1] 所在页
     * @apiParam {Number} [page_size=20] 每页个数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiError 400 参数错误
     *
     * @apiSuccessExample 剧集:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [{
     *           "id": 32,
     *           "name": "魔道祖师 日本语",
     *           "abstract": "夷陵老祖重生归来 ...",
     *           "cover_color": 15840852,
     *           "pay_type": 1,
     *           "front_cover": "http://foo.png",
     *           "view_count": 3377,
     *           "newest": "全一期",
     *           "integrity": 1,
     *           "play_sound_id": 36652  // 待播放的音频 ID，默认剧集第一集
     *         }],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 4,
     *           "count": 57,
     *           "pagesize": 18
     *         }
     *       }
     *     }
     *
     * @apiSuccessExample 音频:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [{
     *           "id": 1106533,
     *           "soundstr": "【自然 Sound】寒冷湖边的下雨声",
     *           "duration": 3600039,
     *           "front_cover": "http://foo.png",
     *           "view_count": 33847,
     *           "all_comments": 38,
     *           "video": false
     *         }],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 4,
     *           "count": 57,
     *           "pagesize": 18
     *         }
     *       }
     *     }
     */
    public function actionRecommend(int $type, int $order = 0, int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        if (!in_array($type, TeenagerRecommendedElements::$element_types) ||
                !in_array($order, TeenagerRecommendedElements::$element_orders) ||
                $page <= 0 || $page_size <= 0) {
            throw new HttpException(400, '参数错误');
        }
        switch ($type) {
            case TeenagerRecommendedElements::ELEMENT_SOUND:
                $return =
                    TeenagerRecommendedElements::getTeenagerSounds($order, $page, $page_size);
                break;
            case TeenagerRecommendedElements::ELEMENT_DRAMA:
                $return =
                    TeenagerRecommendedElements::getTeenagerDramas(Yii::$app->user->id, $order, $page, $page_size);
                break;
            default:
                throw new HttpException(400, '参数错误');
        }
        return $return;
    }

    /**
     * @api {post} /teenager/set-status 客户端设置青少年模式状态接口
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/teenager/set-status
     * @apiSampleRequest /teenager/set-status
     * @apiDescription 客户端设置青少年模式状态接口
     *
     * @apiVersion 0.1.0
     * @apiName set-status
     * @apiGroup teenager
     *
     * @apiParam {number=0,1,2} type 操作类型（0 关闭，1 开启，2 登录后同步）
     * @apiParam {String} password 密码 长度等于 4，只能是数字
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiError 400 参数错误
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "msg": "开启成功",
     *         "status": 1  // 0 关闭成功，1 开启成功
     *       }
     *     }
     */
    public function actionSetStatus()
    {
        $type = Yii::$app->request->post('type');
        $password = trim(Yii::$app->request->post('password'));
        if (!in_array($type, SSOClient::$teenager_status) ||
                strlen($password) !== 4 || !ctype_digit($password)) {
            throw new HttpException(400, '参数错误');
        }
        $type = (int)$type;
        $teenager_status = Mowangskuser::isTeenagerModeEnabled(Yii::$app->user->id);
        if (($type === SSOClient::TEENAGER_STATUS_CLOSE &&
            $teenager_status === SSOClient::TEENAGER_STATUS_CLOSE) || $teenager_status === $type) {
            return [
                'msg' => '操作成功',
                'status' => 0
            ];
        }
        $sso_client = Yii::$app->sso;
        $transaction = Yii::$app->db->beginTransaction();
        try {
            if ($type === SSOClient::TEENAGER_STATUS_OPEN || $type === SSOClient::TEENAGER_STATUS_SYNC) {
                $attributes['confirm'] = new Expression('confirm | ' . Mowangskuser::CONFIRM_TEENAGER);
            } else {
                $attributes['confirm'] = new Expression('confirm &~ ' . Mowangskuser::CONFIRM_TEENAGER);
            }
            $status = Mowangskuser::updateAll($attributes, ['id' => Yii::$app->user->id]);
            if (!$status) {
                throw new HttpException(500, Yii::t('app/error', 'Change failed'));
            }
            $info = $sso_client->setTeenagerStatus($type, $password, Yii::$app->user->identity->token);
            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }

        if ($type === SSOClient::TEENAGER_STATUS_SYNC) {
            $return = [
                'msg' => '同步成功',
                'status' => 1
            ];
        } elseif ($info['confirm'] & Mowangskuser::CONFIRM_TEENAGER) {
            $return = [
                'msg' => '青少年模式已开启',
                'status' => 1
            ];
        } else {
            $return = [
                'msg' => '青少年模式已关闭',
                'status' => 0
            ];
        }
        return $return;
    }
}
