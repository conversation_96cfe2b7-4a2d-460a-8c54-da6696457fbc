<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/31
 * Time: 20:23
 */

namespace app\controllers;

use app\components\auth\appleservernotification\ServerNotification;
use AopClient;
use app\components\auth\AuthApple;
use app\components\auth\AuthAli;
use app\components\auth\AuthJingDong;
use app\components\auth\AuthJingDongV2;
use app\components\auth\AuthPayPal;
use app\components\auth\AuthQQ;
use app\components\auth\AuthWechat;
use app\components\auth\douyin\AuthDouDian;
use app\components\auth\douyin\DouDianException;
use app\components\auth\douyin\DouDianPushMessage;
use app\components\auth\douyin\DouDianSPIMsgContent;
use app\components\auth\oaflow\Client;
use app\components\auth\oaflow\EventBody;
use app\components\auth\wechat\WechatApiV3;
use app\components\auth\wechat\WechatOffiaccount;
use app\components\service\bililargepay\BiliLargePayCallbackMessage;
use app\components\service\bililargepay\BiliLargePayClient;
use app\components\service\bililargepay\BiliLargePayException;
use app\components\util\Equipment;
use app\components\util\JingDongTopupException;
use app\components\util\MUtils;
use app\components\util\TmallPayException;
use app\components\util\Tools;
use app\forms\DouDianTransactionForm;
use app\forms\JingDongTransactionForm;
use app\forms\RechargeForm;
use app\forms\TmallPayForm;
use app\forms\TopupGoods;
use app\forms\UserContext;
use app\forms\VipIosSubscriptionForm;
use app\models\AdTrack;
use app\models\AppsFlyerBody;
use app\models\Balance;
use app\models\TopupMenu;
use app\models\GuildLiveOrder;
use app\models\InstallLog;
use app\models\IosReceipt;
use app\models\MAdminLogger;
use app\models\Mowangskuser;
use app\models\MVip;
use app\models\PayManualTopupApplication;
use app\models\RechargeOrder;
use app\models\RechargeOrderDetail;
use app\models\VipSubscriptionSignAgreement;
use app\models\VipFeeDeductedRecord;
use app\models\ThirdPartyTaskUtil;
use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\db\Expression;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use yii\web\Controller;
use yii\web\HttpException;
use yii\web\Request;
use yii\web\Response;

class CallbackController extends Controller
{
    // 回调接口响应成功状态码
    const CODE_SUCCESS = 0;

    public $enableCsrfValidation = false;

    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'wechatpay' => ['post'],
                'wechatpay-sign' => ['post'],
                'alipay' => ['post'],
                'qqpay' => ['post'],
                'paypal' => ['post'],
                'guild-live-alipay' => ['post'],
                'ios-iap' => ['post'],
                'appsflyer' => ['post'],
                'doudian' => ['post'],
                'doudian-notify' => ['post'],
                'doudian-query' => ['post'],
                'doudian-cancel' => ['post'],
                'jd-topup' => ['post'],
                'jd-query-order' => ['post'],
                'wechatpay-v3' => ['post'],
                'jd-topup-v2' => ['post'],
                'jd-query-order-v2' => ['post'],
                'thirdparty-task-notify' => ['post'],
                'oa-flow' => ['post'],
            ],
        ];

        return $behaviors;
    }

    public function beforeAction($action)
    {
        // 记录支付回调报文
        // tmallpay 是通过 GET 请求，直接可从 nginx.access.url 中观察到
        $message = '';
        if (in_array($action->id, ['wechatpay', 'qqpay', 'paypal', 'ios-iap', 'doudian', 'appsflyer', 'doudian-notify', 'doudian-query', 'doudian-cancel', 'wechatpay-v3', 'wechatpay-sign', 'oa-flow'])) {
            $message = Yii::$app->request->rawBody;
        } elseif (in_array($action->id, ['alipay', 'guild-live-alipay', 'jd-topup', 'jd-query-order', 'jd-topup-v2', 'jd-query-order-v2', 'thirdparty-task-notify'])) {
            $message = Json::encode($_POST);
        }

        if (!empty($message)) {
            $now = intval(microtime(true)*1000);
            foreach (str_split($message, 800) as $i => $part) {
                Yii::info(sprintf('[%d-%d] callback %s message: %s', $now, $i, $action->id, $part), __METHOD__);
            }
        }
        return parent::beforeAction($action);
    }

    /**
     * @api {post} /callback/wechatpay 微信支付回调（V2 版本）
     * @apiDescription https://pay.weixin.qq.com/wiki/doc/api/app/app.php?chapter=9_7&index=3
     *
     * @apiName wechatpay
     * @apiGroup callback
     *
     * @apiParam RawBody {xml}
     * @apiParamExample RawBody {xml}
     *     <xml>
     *       <appid><![CDATA[wx143bd0e4a3b523cf]]></appid>
     *       <bank_type><![CDATA[OTHERS]]></bank_type>
     *       <cash_fee><![CDATA[10]]></cash_fee>
     *       <fee_type><![CDATA[CNY]]></fee_type>
     *       <is_subscribe><![CDATA[N]]></is_subscribe>
     *       <mch_id><![CDATA[**********]]></mch_id>
     *       <nonce_str><![CDATA[wE6kDvQGJnsFpLtFQTaAfdpkFn2YoE]]></nonce_str>
     *       <openid><![CDATA[ocUpcs1qZxiwuzd9HgR8ZUELSeQw]]></openid>
     *       <out_trade_no><![CDATA[dev15795015250020000000995]]></out_trade_no>
     *       <result_code><![CDATA[SUCCESS]]></result_code>
     *       <return_code><![CDATA[SUCCESS]]></return_code>
     *       <sign><![CDATA[EF0D9006BED8A3300DB78AA2177DA781]]></sign>
     *       <time_end><![CDATA[**************]]></time_end>
     *       <total_fee>10</total_fee>
     *       <trade_type><![CDATA[APP]]></trade_type>
     *       <transaction_id><![CDATA[4200000475202001209686111224]]></transaction_id>
     *     </xml>
     *
     * @apiSuccessExample {xml} Success-Response:
     *     <xml>
     *       <return_code><![CDATA[SUCCESS]]></return_code>
     *       <return_msg><![CDATA[OK]]></return_msg>
     *     </xml>
     *
     * @apiErrorExample {xml} Error-Response:
     *     <xml>
     *       <return_code><![CDATA[FAIL]]></return_code>
     *       <return_msg><![CDATA[failed]]></return_msg>
     *     </xml>
     */
    public function actionWechatpay()
    {
        $body = Yii::$app->request->getRawBody();
        $xml_array = (array)simplexml_load_string($body, "SimpleXMLElement", LIBXML_NOCDATA);
        if (!$xml_array || !isset($xml_array['sign'], $xml_array['result_code'], $xml_array['out_trade_no'], $xml_array['transaction_id'], $xml_array['total_fee'])) {
            Yii::error('微信支付回调报文异常', __METHOD__);
            throw new HttpException(403, '非法请求');
        }

        $sign = $xml_array['sign'];
        unset($xml_array['sign']);
        $csign = AuthWechat::getWechatSign($xml_array);
        if ($sign !== $csign || $xml_array['result_code'] !== 'SUCCESS') {
            return AuthWechat::getWechatPayFailCallbackResponse();
        }
        $out_trade_no = $xml_array['out_trade_no'];  // 商户系统的订单号
        $transaction_id = $xml_array['transaction_id'];  // 微信支付订单号
        $total_fee = $xml_array['total_fee'];  // 订单总金额，单位为分
        $openid = $xml_array['openid'];  // 用户在商户 appid 下的唯一标识
        if (VipFeeDeductedRecord::isVipTradeNoPrefix($out_trade_no)) {
            $res = VipFeeDeductedRecord::updateOrder(VipFeeDeductedRecord::PAY_TYPE_WECHAT,
                $out_trade_no, $transaction_id, $total_fee,
                function (array $more) use ($openid) {
                    $more['wechat_openid'] = $openid;
                    return $more;
                });
        } else {
            $order_id = RechargeOrder::getRealOrderId($out_trade_no);
            $price = $total_fee / 100;
            $res = $this->updateOrder($order_id, $price, $transaction_id, false,
                function (RechargeOrder $order) use ($openid) {
                    RechargeOrderDetail::updateByPk($order->id, [
                        'more' => new Expression(
                            // JSON 字段默认为 NULL 的时候，无法写入数据
                            'JSON_SET(COALESCE(more, "{}"), "$.buyer_id", :buyer_id)',
                            [
                                ':buyer_id' => $openid,
                            ])
                    ]);
                });
        }
        if ($res) {
            return AuthWechat::getWechatPaySuccessCallbackResponse();
        } else {
            return AuthWechat::getWechatPayFailCallbackResponse();
        }
    }

    /**
     * @api {post} /callback/wechatpay-v3 微信支付回调（V3 版本）
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/callback/wechatpay-v3
     * @apiSampleRequest /callback/wechatpay-v3
     * @apiDescription https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_1_5.shtml
     *
     * @apiName wechatpay-v3
     * @apiGroup callback
     *
     * @apiParam RawBody {json}
     * @apiParamExample RawBody {json}
     *     {
     *       "id": "48f70284-38c7-5c7a-8e35-3bf6b78454e1",
     *       "create_time": "2023-05-30T14:55:11+08:00",
     *       "resource_type": "encrypt-resource",
     *       "event_type": "TRANSACTION.SUCCESS",
     *       "summary": "支付成功",
     *       "resource": {
     *         "original_type": "transaction",
     *         "algorithm": "AEAD_AES_256_GCM",
     *         "ciphertext": "LVxM/lD3O95.......0Vg==",
     *         "associated_data": "transaction",
     *         "nonce": "fBYaTiCppazs"
     *       }
     *     }
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "code": "SUCCESS",
     *       "message": "成功"
     *     }
     * @apiErrorExample {json} Error-Response:
     *     {
     *       "code": "FAIL",
     *       "message": "非法请求"
     *     }
     */
    public function actionWechatpayV3()
    {
        $request = Yii::$app->request;
        $wechat = WechatApiV3::newInstance();
        try {
            $body = $wechat->parseCallbackBody($request);
            if ($body->getTradeStatus() !== 'SUCCESS') {
                Yii::error(sprintf('未知的微信报文通知状态 %s', $body->getTradeStatus()), __METHOD__);
                throw new HttpException(403, '非法请求');
            }

            $openid = $body->getOpenid();
            $order_id = RechargeOrder::getRealOrderId($body->getOutTradeNo());
            if (!$this->updateOrder($order_id, $body->getPriceInFen() / 100, $body->getTransactionId(), false, function (RechargeOrder $order) use ($openid) {
                RechargeOrderDetail::updateByPk($order->id, [
                    'more' => new Expression(
                        // JSON 字段默认为 NULL 的时候，无法写入数据
                        'JSON_SET(COALESCE(more, "{}"), "$.buyer_id", :buyer_id)',
                        [
                            ':buyer_id' => $openid,
                        ])
                ]);
            })) {
                throw new HttpException(500, '充值失败');
            }

            return $wechat::getWechatPaySuccessCallbackResponse();
        } catch (Exception $e) {
            return $wechat::getWechatPayFailCallbackResponse($e);
        }
    }

    /**
     * @api {post} /callback/wechat-offiaccount-replay 微信公众号被动回复接口
     * @apiDescription https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Passive_user_reply_message.html
     *
     * @apiName wechat-offiaccount-replay
     * @apiGroup callback
     *
     * @apiParam RawBody {xml}
     * @apiParamExample RawBody {xml}
     *     <xml>
     *       <ToUserName><![CDATA[toUser]]></ToUserName>
     *       <FromUserName><![CDATA[fromUser]]></FromUserName>
     *       <CreateTime>**********</CreateTime>
     *       <MsgType><![CDATA[text]]></MsgType>
     *       <Content><![CDATA[this is a test message]]></Content>
     *       <MsgId>****************</MsgId>
     *       <MsgDataId>xxxx</MsgDataId>
     *       <Idx>xxxx</Idx>
     *     </xml>
     *
     * @apiParam (Query) {String} signature 微信加密签名
     * @apiParam (Query) {String} timestamp 时间戳，单位：秒
     * @apiParam (Query) {String} nonce 随机字符串
     * @apiParam (Query) {String} [echostr] 仅在 GET 请求验证接口时传入
     *
     * @apiSuccessExample {xml} Success-Response: 正常回复
     *     <xml>
     *       <ToUserName><![CDATA[toUser]]></ToUserName>
     *       <FromUserName><![CDATA[fromUser]]></FromUserName>
     *       <CreateTime>********</CreateTime>
     *       <MsgType><![CDATA[text]]></MsgType>
     *       <Content><![CDATA[this is a test reply <a href="https://www.test.com/event/1?foo=bar">测试链接</a>]]></Content> // 支持 HTML
     *     </xml>
     *
     * @apiSuccessExample {text} Success-Response: 微信服务器验证回复
     *     echostr
     *
     * @apiErrorExample {text} Success-Response: 不需要回复时
     *     success
     *
     * @apiErrorExample {xml} Error-Response: 当需要回复错误信息时
     *     <xml>
     *       <ToUserName><![CDATA[toUser]]></ToUserName>
     *       <FromUserName><![CDATA[fromUser]]></FromUserName>
     *       <CreateTime>********</CreateTime>
     *       <MsgType><![CDATA[text]]></MsgType>
     *       <Content><![CDATA[this is a error msg reply]]></Content>
     *     </xml>
     */
    public function actionWechatOffiaccountReplay()
    {
        $wechat_offiaccount = WechatOffiaccount::newInstance();
        $request = Yii::$app->request;
        if (!$wechat_offiaccount->verifySignature($request)) {
            Yii::error('微信公众号请求验证签名失败：' . Json::encode($request->getQueryParams()),
                __METHOD__);
            throw new HttpException(400, '非法请求');
        }
        if (Yii::$app->request->isGet) {
            // 微信公众号服务器验证请求
            return trim(Yii::$app->request->get('echostr'));
        }
        $resp_data = '';
        if ($message_info = $wechat_offiaccount->getMessageInfo($request)) {
            switch ($message_info['msg_type']) {
                case WechatOffiaccount::MSG_TYPE_TEXT:
                    $resp_data = $wechat_offiaccount->handleTextMessage($message_info);
                    break;
                default:
                    $resp_data = WechatOffiaccount::getMsgNoReplyResponse();
            }
        } elseif ($event_info = $wechat_offiaccount->getEventInfo($request)) {
            // NOTICE: 若需要回复用户消息，需要注意该事件是否支持回复
            $resp_data = $wechat_offiaccount->handleEventMessage($event_info);
        } else {
            Yii::error('微信公众号消息内容异常：' . $request->getRawBody(), __METHOD__);
            $resp_data = WechatOffiaccount::getMsgNoReplyResponse();
        }
        return $resp_data;
    }

    /**
     * @api {post} /callback/alipay 支付宝回调验证
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/callback/alipay
     * @apiSampleRequest /callback/alipay
     * @apiDescription https://docs.open.alipay.com/204/105301/
     *
     * @apiName alipay
     * @apiGroup callback
     *
     * @apiSuccessExample Success-Response:
     * success
     * @apiErrorExample Error-Response:
     * error
     */
    public function actionAlipay()
    {
        if (!$_POST || !array_key_exists('sign', $_POST)) {
            Yii::error('支付宝回调报文异常', __METHOD__);
            throw new HttpException(403, '非法请求');
        }

        $ali = new AuthAli();
        $flag = $ali->getAopClient()->rsaCheckV1($_POST, null, 'RSA2');
        if (!$flag) {
            throw new HttpException(403, '非法请求');
        }

        $request = Yii::$app->request;
        $notify_type = $request->post('notify_type');
        switch ($request->post('notify_type')) {
            case 'trade_status_sync':
                $result = $this->alipayPaidNotice($request);
                break;
            case 'dut_user_sign':  // 签约
            case 'dut_user_unsign':  // 解约
                $result = $this->alipaySignNotice($request);
                break;
            default:
                Yii::error(sprintf('支付宝回调报文异常: notify_type=%s', $notify_type), __METHOD__);
                throw new HttpException(403, '非法请求');
        }

        $response = Yii::$app->response;
        $response->data = $result;
        $response->send();
    }

    /**
     * @api {post} 支付宝支付回调
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/callback/alipay
     * @apiSampleRequest /callback/alipay
     * @apiDescription https://docs.open.alipay.com/204/105301/
     *
     * @apiName alipay
     * @apiGroup callback
     *
     * @apiParam {String} subject 订单标题, 例 购买钻石
     * @apiParam {String} body 商品描述, 例 钻石
     *
     * @apiParam {String} out_trade_no 商户订单号, 例 15780225670010000000913
     * @apiParam {string=WAIT_BUYER_PAY,TRADE_CLOSED,TRADE_SUCCESS,TRADE_FINISHED} trade_status 交易状态
     * @apiParam {String} trade_no 支付宝交易号, 例 2020010322001403101000125636
     * @apiParam {String} total_amount 订单金额, 例 1.00
     * @apiParam {String} receipt_amount 实收金额, 例 1.00（交易完成 TRADE_FINISHED，超时未付款 TRADE_CLOSED 时不包含此参数）
     * @apiParam {String} buyer_pay_amount 付款金额, 例 1.00
     * @apiParam {String} point_amount 集分宝金额, 例 0.00
     * @apiParam {String} invoice_amount 开票金额, 例 1.00
     * @apiParam {String} fund_bill_list 支付金额信息, 例 [{"amount":"1.00","fundChannel":"ALIPAYACCOUNT"}]
     *
     * @apiParam {String} buyer_id 买家支付宝用户号（逐步回收，使用 buyer_open_id 代替），例 ****************
     * @apiParam {String} buyer_open_id 买家支付宝用户号, 例 0044exorZgCtnKMT0cge4RKy5YwSBS6TwdwFhuVvwnmx1I3
     * @apiParam {String} seller_id 卖家支付宝用户号, 例 ****************
     * @apiParam {String} auth_app_id 例 ****************
     * @apiParam {String} app_id 支付宝分配给开发者的应用 ID, 例 ****************
     *
     * @apiParam {String} notify_id 通知校验 ID, 例 2020010300222120547003101000672140
     * @apiParam {String} notify_type 通知类型, 例 trade_status_sync
     * @apiParam {String} notify_time 通知时间, 例 2020-01-03 12:05:48
     * @apiParam {String} gmt_create 交易创建时间, 例 2020-01-03 12:05:38
     * @apiParam {String} gmt_payment 交易付款时间, 例 2020-01-03 12:05:47
     *
     * @apiParam {String} sign_type 签名类型, 例 RSA2
     * @apiParam {String} sign 签名, 例 jJ909gWnMYlERCobC....piQ
     * @apiParam {String} charset 编码格式, 例 UTF-8
     * @apiParam {String} version 接口版本, 例 1.0
     *
     * @apiParam RawBody {json} https://opendocs.alipay.com/open/0amki1?pathHash=f6ef64bf
     * @apiParamExample RawBody {json} 支付成功异步通知报文示例
     *     {
     *       "gmt_create": "2025-03-24 17:59:51",
     *       "charset": "UTF-8",
     *       "seller_email": "<EMAIL>",
     *       "subject": "连续包月猫耳FM会员",
     *       "sign": "RhH4sTEK/YKvFZ7zY+jGWTI20rbczuJDY.....k5Wp3/cvRK6IQtmHFuSOwQehVlW6MhbbJ8n72lpNoG/9Q==",
     *       "buyer_id": "****************",
     *       "buyer_open_id": "0044exorZgCtnKMT0cge4RKy5YwSBS6TwdwFhuVvwnmx1I3",
     *       "invoice_amount": "0.10",
     *       "notify_id": "2025032401222175952045041449449029",
     *       "fund_bill_list": "[{\"amount\":\"0.10\",\"fundChannel\":\"ALIPAYACCOUNT\"}]",
     *       "notify_type": "trade_status_sync",
     *       "trade_status": "TRADE_SUCCESS",
     *       "receipt_amount": "0.10",
     *       "buyer_pay_amount": "0.10",
     *       "app_id": "****************",
     *       "sign_type": "RSA2",
     *       "seller_id": "****************",
     *       "gmt_payment": "2025-03-24 17:59:52",
     *       "notify_time": "2025-03-24 18:13:39",
     *       "version": "1.0",
     *       "out_trade_no": "dev_vip_17428103720030000009487",
     *       "total_amount": "0.10",
     *       "trade_no": "2025032422001445041447162071",
     *       "auth_app_id": "****************",
     *       "buyer_logon_id": "che***@163.com",
     *       "point_amount": "0.00"
     *     }
     */
    public function alipayPaidNotice(Request $request)
    {
        $out_trade_no = $request->post('out_trade_no');
        $transaction_id = $request->post('trade_no');
        $price_in_yuan = $request->post('receipt_amount');
        $trade_status = $request->post('trade_status');
        $buyer_id = $request->post('buyer_id');
        $buyer_open_id = $request->post('buyer_open_id');

        if (!$out_trade_no || !$transaction_id || !$trade_status
                || (!in_array($trade_status, ['TRADE_FINISHED', 'TRADE_CLOSED']) && !$price_in_yuan)) {
            Yii::error('支付宝支付回调报文异常', __METHOD__);
            throw new HttpException(403, '非法请求');
        }

        if ('TRADE_SUCCESS' !== $trade_status) {
            // 订单交易成功 3 个月后会收到一次异步通知（交易成功，不可退款）
            // 交易状态 TRADE_FINISHED 的通知触发条件是商户签约的产品不支持退款功能的前提下，买家付款成功；
            // 或者商户签约的产品支持退款功能的前提下，交易已经成功并且已经超过可退款期限。
            // https://docs.open.alipay.com/204/105301/
            if ('TRADE_FINISHED' === $trade_status) {
                return 'success';
            }
            // 超时未付款交易关闭通知
            if ('TRADE_CLOSED' === $trade_status) {
                return 'success';
            }
            // WAIT_BUYER_PAY 不触发通知
            Yii::error(sprintf('支付宝支付失败：%s', Json::encode($_POST)), __METHOD__);
            return 'error';
        }

        if (VipFeeDeductedRecord::isVipTradeNoPrefix($out_trade_no)) {
            $price_in_fen = Balance::profitUnitConversion($price_in_yuan, Balance::CONVERT_YUAN_TO_FEN);
            $res = VipFeeDeductedRecord::updateOrder(VipFeeDeductedRecord::PAY_TYPE_ALIPAY,
                $out_trade_no, $transaction_id, $price_in_fen,
                function (array $more) use ($buyer_id, $buyer_open_id) {
                    if ($buyer_id) {
                        $more['buyer_id'] = $buyer_id;
                    }
                    if ($buyer_open_id) {
                        $more['buyer_open_id'] = $buyer_open_id;
                    }
                    return $more;
                });
        } else {
            $res = $this->updateOrder(RechargeOrder::getRealOrderId($out_trade_no), $price_in_yuan, $transaction_id, false, function (RechargeOrder $order) use ($buyer_id, $buyer_open_id) {
                if ($buyer_id && $buyer_open_id) {
                    // JSON 字段默认为 NULL 的时候，无法写入数据
                    $expression = 'JSON_SET(COALESCE(more, "{}"), "$.buyer_id", :buyer_id, "$.buyer_open_id", :buyer_open_id)';
                    $params = [':buyer_id' => $buyer_id, ':buyer_open_id' => $buyer_open_id];
                } elseif ($buyer_id) {
                    $expression = 'JSON_SET(COALESCE(more, "{}"), "$.buyer_id", :buyer_id)';
                    $params = [':buyer_id' => $buyer_id];
                } elseif ($buyer_open_id) {
                    $expression = 'JSON_SET(COALESCE(more, "{}"), "$.buyer_open_id", :buyer_open_id)';
                    $params = [':buyer_open_id' => $buyer_open_id];
                } else {
                    return;
                }
                RechargeOrderDetail::updateByPk($order->id, [
                    'more' => new Expression($expression, $params)
                ]);
            });
        }
        if ($res) {
            return 'success';
        }
        return 'error';
    }

    /**
     * @api {post} 支付宝签约/解约结果回调
     * @apiDescription https://opendocs.alipay.com/open/02fkan?scene=common
     *
     * @apiName alipay-sign
     * @apiGroup callback
     *
     * @apiParam {String} notify_id 通知校验 ID, 例 91722adff935e8cfa58b3aabf4dead6ibe
     * @apiParam {String} notify_time 通知时间, 例 2020-04-24 11:28:20
     * @apiParam {String} notify_type 通知类型, dut_user_sign: 签约, dut_user_unsign: 解约, 失败不触发通知
     * @apiParam {String} sign_type 签名类型, 例 RSA2
     * @apiParam {String} sign 签名, 例 WcO+t3D8Kg71dTlKwN7r9PzUOXeaBJwp8/FOuSxcuSkXsoVYxBpsAidprySCjHCjmaglNcjoKJQLJ28/Asl93joTW39FX6i07lXhnbPknezAlwmvPdnQuI01HZsZF9V1i6ggZjBiAd5lG8bZtTxZOJ87ub2i9GuJ3Nr/NUc9VeY=
     * @apiParam {String} sign_scene 签约场景, 例 INDUSTRY|CARRENTAL
     * @apiParam {String} sign_time 签约时间, 例 2020-04-24 11:28:20
     * @apiParam {String} alipay_user_id 支付宝用户 ID（逐步回收，使用 alipay_open_id 代替）, 例 ****************
     * @apiParam {String} alipay_open_id 支付宝用户 ID, 例 0044exorZgCtnKMT0cge4RKy5YwSBS6TwdwFhuVvwnmx1I3
     * @apiParam {String} status 协议签约状态, NORMAL: 正常, UNSIGN: 解约
     * @apiParam {String} app_id 应用 ID, 例 2017060101317939
     * @apiParam {String} auth_app_id 授权应用 ID, 例 2017060101317935
     * @apiParam {String} agreement_no 协议号, 例 20205224633661163886
     * @apiParam {String} external_agreement_no 外部协议号, 商家自定义, 例 test
     * @apiParam {String} personal_product_code 个人产品码, 例 GENERAL_WITHHOLDING_P
     * @apiParam {String} valid_time 生效时间, 例 2020-04-24 11:28:20
     * @apiParam {String} invalid_time 失效时间, 例 2115-02-01 00:00:00
     * @apiParam {String} alipay_logon_id 支付宝登录 ID, 例 test***<EMAIL>
     * @apiParam {String} login_token 登录令牌, 例 7ff7664d45c4afe8dccab8f224af9379_07
     *
     * @apiParam RawBody {json} https://opendocs.alipay.com/open/08bpuc?pathHash=74036bac
     * @apiParamExample RawBody {json} 签约异步通知报文示例
     *     {
     *       "charset": "UTF-8",
     *       "notify_time": "2025-03-24 17:59:53",
     *       "alipay_user_id": "****************",
     *       "alipay_open_id": "0044exorZgCtnKMT0cge4RKy5YwSBS6TwdwFhuVvwnmx1I3",
     *       "sign": "cn9Pb9H+qR7NLF9BY.....4LanrPn8M4TnGpMNCZww==",
     *       "external_agreement_no": "dev_vip_agreement_17428103720030000001404",
     *       "version": "1.0",
     *       "sign_time": "2025-03-24 17:59:53",
     *       "notify_id": "2025032401222175953098241445553575",
     *       "notify_type": "dut_user_sign",
     *       "agreement_no": "20255124179225875004",
     *       "invalid_time": "2115-02-01 00:00:00",
     *       "auth_app_id": "****************",
     *       "personal_product_code": "CYCLE_PAY_AUTH_P",
     *       "valid_time": "2025-03-24 17:59:53",
     *       "app_id": "****************",
     *       "next_deduct_time": "2025-04-24",
     *       "sign_type": "RSA2",
     *       "sign_scene": "INDUSTRY|DIGITAL_MEDIA",
     *       "status": "NORMAL",
     *       "alipay_logon_id": "che***@163.com"
     *     }
     * @apiParamExample RawBody {json} 解约异步通知报文示例
     *     {
     *       "charset": "UTF-8",
     *       "notify_time": "2025-03-24 18:03:29",
     *       "unsign_time": "2025-03-24 18:00:18",
     *       "alipay_user_id": "****************",
     *       "sign": "DSaHqC2YjWhafp/BstZGW4Gi6m...xo1H8ULoBkByapwfagA==",
     *       "external_agreement_no": "dev_vip_agreement_17428103720030000001404",
     *       "version": "1.0",
     *       "notify_id": "2025032401222180018084081447911968",
     *       "notify_type": "dut_user_unsign",
     *       "agreement_no": "20255124179225875004",
     *       "auth_app_id": "****************",
     *       "personal_product_code": "CYCLE_PAY_AUTH_P",
     *       "app_id": "****************",
     *       "sign_type": "RSA2",
     *       "alipay_logon_id": "che***@163.com",
     *       "status": "UNSIGN",
     *       "sign_scene": "INDUSTRY|DIGITAL_MEDIA"
     *     }
     */
    public function alipaySignNotice(Request $request)
    {
        $notify_type = $request->post('notify_type');
        $status = $request->post('status');
        $external_agreement_no = $request->post('external_agreement_no');
        $alipay_agreement_no = $request->post('agreement_no');
        $alipay_logon_id = $request->post('alipay_logon_id');
        $alipay_user_id = $request->post('alipay_user_id');
        $alipay_open_id = $request->post('alipay_open_id');
        $sign_scene = $request->post('sign_scene');
        $next_deduct_time = $request->post('next_deduct_time');

        if (!in_array($notify_type, ['dut_user_sign', 'dut_user_unsign'])
                || !in_array($_POST['status'], ['NORMAL', 'UNSIGN'])
                || !$external_agreement_no || !$alipay_agreement_no
        ) {
            Yii::error('支付宝回调报文异常', __METHOD__);
            throw new HttpException(403, '非法请求');
        }

        if (!($notify_type === 'dut_user_sign' && $status === 'NORMAL')
                && !($notify_type === 'dut_user_unsign' && $status === 'UNSIGN')) {
            Yii::error('支付宝回调报文异常', __METHOD__);
            throw new HttpException(403, '非法请求');
        }

        $operation_type = $notify_type === 'dut_user_sign'
            ? VipSubscriptionSignAgreement::OPERATION_TYPE_ADD
            : VipSubscriptionSignAgreement::OPERATION_TYPE_DELETE;
        $res = VipSubscriptionSignAgreement::updateAgreement(
            $operation_type,
            VipSubscriptionSignAgreement::PAY_TYPE_ALIPAY,
            $external_agreement_no,
            $alipay_agreement_no,
            function (VipSubscriptionSignAgreement $agreement, VipFeeDeductedRecord $deducted_record, MVip $vip) use ($operation_type, $alipay_logon_id, $alipay_user_id, $alipay_open_id, $sign_scene, $next_deduct_time) {
                $active_sign_scene = $vip->getAlipaySignScene();
                if ($sign_scene !== $active_sign_scene) {
                    throw new Exception(sprintf('支付宝回调的场景码与协议中的场景码不一致：%s, %s', $sign_scene, $active_sign_scene));
                }
                if ($operation_type !== VipSubscriptionSignAgreement::OPERATION_TYPE_ADD) {
                    return;
                }
                $more = ['sign_scene' => $sign_scene];
                if ($alipay_logon_id) {
                    $more['alipay_logon_id'] = $alipay_logon_id;
                }
                if ($alipay_user_id) {
                    $more['alipay_user_id'] = $alipay_user_id;
                }
                if ($alipay_open_id) {
                    $more['alipay_open_id'] = $alipay_open_id;
                }
                $agreement->more = array_merge($agreement->more ?: [], $more);
                if ($next_deduct_time) {
                    $deducted_record->next_deduct_time = strtotime($next_deduct_time);
                    if (!$deducted_record->save()) {
                        throw new Exception('签约扣费记录更新失败: ' . MUtils2::getFirstError($deducted_record));
                    }
                }
            }
        );

        return $res ? 'success' : 'error';
    }

    /**
     * @api {post} /callback/qqpay QQ 钱包支付回调
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/callback/qqpay
     * @apiSampleRequest /callback/qqpay
     * @apiDescription https://qpay.qq.com/buss/wiki/38/1204
     *
     * @apiName qqpay
     * @apiGroup callback
     *
     * @apiParam RawBody {xml}
     * @apiParamExample RawBody {xml}
     *     <?xml version="1.0" encoding="UTF-8" ?>
     *     <xml>
     *       <appid><![CDATA[**********]]></appid>
     *       <bank_type><![CDATA[BALANCE]]></bank_type>
     *       <cash_fee><![CDATA[10]]></cash_fee>
     *       <fee_type><![CDATA[CNY]]></fee_type>
     *       <mch_id><![CDATA[**********]]></mch_id>
     *       <nonce_str><![CDATA[fbd1e9f5f9627ec5671c95bc838b5b11]]></nonce_str>
     *       <openid><![CDATA[15D0F7078AC88C983D1C53D7865C831F]]></openid>
     *       <out_trade_no><![CDATA[dev15795028980040000000996]]></out_trade_no>
     *       <sign><![CDATA[9FD3A15D7674C942CD233F18C2D6F875]]></sign>
     *       <time_end><![CDATA[**************]]></time_end>
     *       <total_fee><![CDATA[10]]></total_fee>
     *       <trade_state><![CDATA[SUCCESS]]></trade_state>
     *       <trade_type><![CDATA[APP]]></trade_type>
     *       <transaction_id><![CDATA[**********6011202001201309436540]]></transaction_id>
     *     </xml>
     *
     * @apiSuccessExample {xml} Success-Response:
     *     <xml>
     *       <return_code>SUCCESS</return_code>
     *     </xml>
     *
     * @apiErrorExample {xml} Error-Response:
     *     <xml>
     *       <return_code>FAIL</return_code>
     *     </xml>
     */
    public function actionQqpay()
    {
        $body = Yii::$app->request->getRawBody();
        $xml_array = (array)simplexml_load_string($body, 'SimpleXMLElement', LIBXML_NOCDATA);
        if (!$xml_array || !isset($xml_array['sign'], $xml_array['out_trade_no'], $xml_array['transaction_id'], $xml_array['total_fee'])) {
            Yii::error('QQ 钱包回调报文异常', __METHOD__);
            throw new HttpException(403, '非法请求');
        }

        $sign = $xml_array['sign'];
        unset($xml_array['sign']);
        $csign = AuthQQ::getQQPayServerSign($xml_array);
        if ($sign !== $csign) {
            return AuthQQ::getQQPayFailCallbackResponse();
        }
        // $xml_array['trade_state'] 实际报文值为 SUCCESS，但文档里为 Success
        // 为避免意料之外的情况，暂不对 trade_state 进行判断

        $order_id = $xml_array['out_trade_no'];
        $tid = $xml_array['transaction_id'];
        if ($openid = $xml_array['openid'] ?? null) {
            $populate = function (RechargeOrder $order) use ($openid) {
                RechargeOrderDetail::updateByPk($order->id, [
                    'more' => new Expression(
                        // JSON 字段默认为 NULL 的时候，无法写入数据
                        'JSON_SET(COALESCE(more, "{}"), "$.buyer_id", :buyer_id)',
                        [
                            ':buyer_id' => $openid,
                        ])
                ]);
            };
        } else {
            $populate = null;
        }

        $order_id = RechargeOrder::getRealOrderId($order_id);
        $price = $xml_array['total_fee'] / AuthQQ::CNY_YUAN_FEN_RATE;

        if ($this->updateOrder($order_id, $price, $tid, false, $populate)) {
            return AuthQQ::getQQPaySuccessCallbackResponse();
        }
        return AuthQQ::getQQPayFailCallbackResponse();
    }

    /**
     * @api {get} /callback/tmallpay 天猫充值
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/callback/tmallpay
     * @apiSampleRequest /callback/tmallpay
     * @apiDescription 根据参数 method 不同，将对应不同的请求参数
     *
     * @apiName tmallpay
     * @apiGroup callback
     *
     * @apiParam (查询订单) {String} method 请求类型（taobao.game.charge.zc.query）
     * @apiParam (查询订单) {String} tbOrderNo 淘宝单号
     * @apiParam (查询订单) {String} coopId 商户 ID
     * @apiParam (查询订单) {String} version 版本（例 1.0.0）
     * @apiParam (查询订单) {String} timestamp 时间（格式：2019-05-20 19:09:02）
     * @apiParam (查询订单) {String} sign 签名
     * @apiParam (查询订单) {String} app_key 应用 KEY
     *
     * @apiParam (充值订单) {String} method 充值类型（taobao.game.charge.zc.order）
     * @apiParam (充值订单) {String} tbOrderNo 淘宝单号
     * @apiParam (充值订单) {String} customer 被充值帐号
     * @apiParam (充值订单) {Number} coopId 商户 ID
     * @apiParam (充值订单) {Number} gameId 充值游戏编号
     * @apiParam (充值订单) {Number} cardId 商品 ID（ccy 表主键）
     * @apiParam (充值订单) {Number} cardNum 充值数量
     * @apiParam (充值订单) {Number} sum 充值总金额（单位为元）
     * @apiParam (充值订单) {String} tbOrderSnap 商品信息快照
     * @apiParam (充值订单) {String} notifyUrl 异步通知地址
     * @apiParam (充值订单) {String} version 版本（例 1.0.0）
     * @apiParam (充值订单) {String} timestamp 时间（格式：2019-05-20 19:09:02）
     * @apiParam (充值订单) {String} sign 签名
     * @apiParam (充值订单) {String} app_key 应用 KEY
     * @apiParam (充值订单) {number=258686,258687} [section1=258686] 充值途径（258686 安卓，258687 iOS）
     *
     * @apiParam (取消订单) {String} method 充值类型（taobao.game.charge.zc.cancel）
     * @apiParam (取消订单) {String} tbOrderNo 淘宝单号
     * @apiParam (取消订单) {Number} coopId 商户 ID
     * @apiParam (取消订单) {String} version 版本（例 1.0.0）
     *
     * @apiSuccessExample Success-Response:
     * <gamezctopcancel>
     *   <tbOrderNo>451065952864899174</tbOrderNo>
     *   <coopOrderSuccessTime></coopOrderSuccessTime>
     *   <coopOrderStatus>FAILED</coopOrderStatus>
     *   <failedReason>找不到对应的订单</failedReason>
     *   <coopOrderNo></coopOrderNo>
     *   <failedCode>0104</failedCode>
     *   <coopOrderSnap></coopOrderSnap>
     * </gamezctopcancel>
     */
    public function actionTmallpay($app_key, $method, $timestamp, $sign)
    {
        if (!(($tb_order_no = Yii::$app->request->get('tbOrderNo')) && $app_key && $method && $timestamp && $sign)) {
            Yii::error('天猫充值回调报文异常', __METHOD__);
            throw new HttpException(400, '参数错误');
        }
        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_GENERATE_ORDER, $tb_order_no);
        if (!$redis->lock($lock, ONE_MINUTE)) {
            return $this->tmallpayResponse([
                TmallPayForm::getResponseType($method) => [
                    'tbOrderNo' => $tb_order_no,
                    'coopOrderSuccessTime' => '',
                    'coopOrderStatus' => 'UNDERWAY',
                    'failedReason' => '',
                    'coopOrderNo' => '',
                    'failedCode' => '',
                    'coopOrderSnap' => '',
                ],
            ]);
        }

        try {
            if (!TmallPayForm::checkTmallPaySign()) {
                throw new TmallPayException($method, 'GENERAL_ERROR', '0102', '签名失败');
            }
            switch ($method) {
                case TmallPayForm::TMALL_METHOD_QUERY:
                    $return = TmallPayForm::queryTmallPay($tb_order_no);
                    break;
                case TmallPayForm::TMALL_METHOD_ORDER:
                    $return = TmallPayForm::orderTmallPay($tb_order_no, UserContext::fromUser(Yii::$app->request));
                    break;
                case TmallPayForm::TMALL_METHOD_CANCEL:
                    $return = TmallPayForm::cancelTmallPay($tb_order_no);
                    break;
                default:
                    throw new HttpException(400, '非法请求');
            }
        } catch (TmallPayException $e) {
            $return[$e->type] = array_merge([
                'tbOrderNo' => $tb_order_no,
                'coopOrderSuccessTime' => '',
                'coopOrderStatus' => $e->status,
                'failedReason' => $e->getMessage(),
                'coopOrderNo' => '',
                'failedCode' => $e->fail_code,
                'coopOrderSnap' => '',
            ], $e->extra);
        } finally {
            $redis->unlock($lock);
        }

        return $this->tmallpayResponse($return);
    }

    private function tmallpayResponse(array $data)
    {
        $xml = MUtils::arrayToXML($data);
        Yii::$app->response->format = \yii\web\Response::FORMAT_RAW;
        Yii::$app->response->headers->add('Content-Type', 'text/xml; charset=GBK');
        return iconv('UTF-8', 'GBK//TRANSLIT', $xml);
    }

    /**
     * @api {post} /callback/paypal PayPal 支付回调
     * @apiDescription https://developer.paypal.com/api/nvp-soap/ipn/IPNIntro/
     *
     * @apiName paypal
     * @apiGroup callback
     *
     * @apiParam {String} receiver_email 例：<EMAIL>
     * @apiParam {String} receiver_id 例：S8XGHLYDW9T3S
     * @apiParam {String} residence_country 例：US
     *
     * @apiParam {Number} [test_ipn] 沙盒测试时为 1，正式环境含此字段
     * @apiParam {String} transaction_subject
     * @apiParam {String} txn_id 交易小票 ID，例：61E67681CH3238416
     * @apiParam {String} txn_type 交易类型，例：express_checkout
     *
     * @apiParam {String} payer_email 例：<EMAIL>
     * @apiParam {String} payer_id 例：LPLWNMTBWMFAY
     * @apiParam {String} payer_status 例：verified
     * @apiParam {String} first_name 例：Test
     * @apiParam {String} last_name 例：User
     * @apiParam {String} address_city 例：San Jose
     * @apiParam {String} address_country 例：United States
     * @apiParam {String} address_state 例：CA
     * @apiParam {String} address_status 例：confirmed
     * @apiParam {String} address_country_code 例：US
     * @apiParam {String} address_name 例：Test User
     * @apiParam {String} address_street 例：1 Main St
     * @apiParam {String} address_zip 例：95131
     *
     * @apiParam {String} custom Your custom field
     * @apiParam {Number} handling_amount 例：0.00
     * @apiParam {String} item_name 例：猫耳FM-购买钻石
     * @apiParam {String} item_number 例：500 钻
     * @apiParam {String} mc_currency 例：USD
     * @apiParam {Number} mc_fee 例：0.88
     * @apiParam {Number} mc_gross 例：19.95
     * @apiParam {String} payment_date 例：20:12:59 Jan 13, 2009 PST
     * @apiParam {Number} payment_fee 例：0.88
     * @apiParam {Number} payment_gross 例：19.95
     * @apiParam {String} payment_status 交易状态，例：Completed
     * @apiParam {String} payment_type 支付种类，例：instant
     * @apiParam {String} protection_eligibility 例：Eligible
     * @apiParam {Number} quantity 例：1
     * @apiParam {Number} shipping 例：0.00
     * @apiParam {Number} tax 例：0.00
     *
     * @apiParam {String} notify_version IPN 版本，例：2.6:
     * @apiParam {String} charset 例：windows-1252
     * @apiParam {String} verify_sign 例：AtkOfCXbDm2hu0ZELryHFjY-Vb7PAUvS6nMXgysbElEn9v-1XcmSoGtf
     */
    public function actionPaypal()
    {
        $post = Yii::$app->request->post();
        if (!$post || !isset($post['business'])) {
            Yii::error('PayPal 回调报文异常', __METHOD__);
            throw new HttpException(403, '非法请求');
        }
        if (!AuthPayPal::isLegalCallback($post)) {
            // 若为非法的报文，直接抛出异常
            // 若为合法报文，但验证时可能遇到 PayPal 验证服务 Fatal Failure 错误，抛出异常可让 PayPal 重发通知
            throw new HttpException(403, '非法请求');
        }
        $payment_status = trim(Yii::$app->request->post('payment_status'));
        $rmb_price = trim(Yii::$app->request->post('custom'));
        $invoice = trim(Yii::$app->request->post('invoice'));
        $tid = trim(Yii::$app->request->post('txn_id'));
        $payer_id = trim(Yii::$app->request->post('payer_id'));
        $payer_email = trim(Yii::$app->request->post('payer_email'));

        if ('Completed' !== $payment_status) {
            return false;
        }

        $order_id = RechargeOrder::getRealOrderId($invoice);
        return $this->updateOrder($order_id, $rmb_price, $tid, true, function (RechargeOrder $order) use ($payer_id, $payer_email) {
            RechargeOrderDetail::updateByPk($order->id, [
                'more' => new Expression(
                    // JSON 字段默认为 NULL 的时候，无法写入数据
                    'JSON_SET(COALESCE(more, "{}"), "$.buyer_id", :buyer_id, "$.buyer_email", :buyer_email)',
                    [
                        ':buyer_id' => $payer_id,
                        ':buyer_email' => $payer_email,
                    ])
            ]);
        });
    }

    /**
     * 更新订单成功或失败状态
     *
     * @param integer $order_id 充值订单 ID
     * @param float $price 价格（元）
     * @param string $tid 第三方支付账单号
     * @param boolean $is_paypal 是否为 PayPal
     * @param callable $populate
     * @return boolean
     */
    private function updateOrder($order_id, $price, $tid, $is_paypal = false, $populate = null)
    {
        $connection = RechargeOrder::getDb();
        $transaction = $connection->beginTransaction();
        try {
            $order = RechargeOrder::findForUpdate($order_id);
            if (!$order) {
                throw new \Exception('订单不存在');
            }
            switch ($order->status) {
                case RechargeOrder::STATUS_SUCCESS:
                    return true;
                    break;
                case RechargeOrder::STATUS_CREATE:
                    break;
                default:
                    return false;
            }
            $balance = Balance::getByPk($order->uid);
            $user = Mowangskuser::find()->select('id, ctime')->where(['id' => $order->uid])->one();
            if (($ccy = TopupMenu::getItem($order->cid)) && $ccy->isNewUserScope() && !$balance->hasNewUserTopupDiscount($user)) {
                // 用户未达到享受首充福利的条件。比如创建多个首次充值并且待支付的订单，然后支付其中一个订单，剩余的其他订单如果再次完成支付会进入此分支。
                // 这里我们不自动退款，需要找客服退款或补钻（根据产品要求）。
                Yii::error(sprintf('用户 %d 未达到享受首充福利的条件，订单 %d', $order->uid, $order->id), __METHOD__);
                $order->status = RechargeOrder::STATUS_DUPLICATE_PAY;
            } elseif (Balance::profitUnitConversion($order->price, Balance::CONVERT_YUAN_TO_FEN) ===
                Balance::profitUnitConversion($price, Balance::CONVERT_YUAN_TO_FEN)
            ) {
                $order->status = RechargeOrder::STATUS_SUCCESS;
                $order->confirm_time = $_SERVER['REQUEST_TIME'];
                if ($is_paypal) {
                    $balance->updateCounters([
                        'paypal' => $order->num, 'all_topup' => $order->num, 'all_coin' => $order->num
                    ]);
                } else {
                    $balance->updateCounters([
                        'android' => $order->num, 'all_topup' => $order->num, 'all_coin' => $order->num
                    ]);
                }
            } else {
                Yii::error(sprintf('订单 %d 金额不匹配，订单金额 %s，实际支付金额 %s', $order->id, $order->price, $price), __METHOD__);
                $order->status = RechargeOrder::STATUS_ERROR;
            }
            $order->tid = $tid;
            if (!is_null($populate) && is_callable($populate)) {
                $populate($order);
            }
            if (!$order->save()) {
                throw new \Exception('订单更新失败');
            }

            $transaction->commit();
            $transaction = null;

            if ($order->status === RechargeOrder::STATUS_SUCCESS) {
                if ($is_paypal) {
                    $this->checkPayPalTopupSum($order->uid);
                }
                AdTrack::callbackPay($order->uid, $order->price);
            }
            return true;
        } catch (\Exception $e) {
            Yii::error(sprintf('支付回调验证错误：%s，订单 %d', $e->getMessage(), $order_id), __METHOD__);
            if ($transaction) {
                $transaction->rollBack();
            }
            return false;
        }
    }

    private function checkPayPalTopupSum(int $user_id)
    {
        try {
            $sum = RechargeOrder::getTodayAndCurrentMonthTopupSum($user_id, RechargeOrder::TYPE_PAYPAL);
            if ($sum['day_topup'] >= AuthPayPal::DAY_TOPUP_LIMIT) {
                Yii::warning('PayPal 充值达到当天限额：' . $user_id, __METHOD__);
            }
            if ($sum['month_topup'] >= AuthPayPal::MONTH_TOPUP_LIMIT) {
                Yii::warning('PayPal 充值达到当月限额：' . $user_id, __METHOD__);
            }
        } catch (Exception $e) {
            Yii::error('PayPal 检查用户充值总额出错：' . $e->getMessage(), __METHOD__);
        }
    }

    /**
     * @api {post} /callback/guild-live-alipay 公会主播支付
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/callback/guild-live-alipay
     * @apiSampleRequest /callback/guild-live-alipay
     *
     * @apiName guild-live-alipay
     * @apiGroup callback
     *
     * @apiParam {String} out_trade_no 外部订单号 dev15751041490010000000007
     * @apiParam {String} receipt_amount 收款额 180.00（交易完成 TRADE_FINISHED，超时未付款 TRADE_CLOSED 时不包含此参数）
     * @apiParam {String} trade_no 第三方支付账单号 2019113022001403101000104228
     * @apiParam {String} trade_status 交易状态（例：TRADE_SUCCESS）
     *
     * 其它相关参数
     * @apiParam {String} gmt_create 2019-11-30 16:56:49
     * @apiParam {String} gmt_payment 2019-11-30 16:56:57
     * @apiParam {String} notify_time 2019-11-30 16:56:58
     * @apiParam {String} body 公会主播支付
     * @apiParam {String} subject 强制解约违约金
     * @apiParam {String} notify_type trade_status_sync
     * @apiParam {String} fund_bill_list [{"amount":"180.00","fundChannel":"ALIPAYACCOUNT"}]
     * @apiParam {String} invoice_amount 180.00
     * @apiParam {String} buyer_pay_amount 180.00
     * @apiParam {String} point_amount 0.00
     * @apiParam {String} app_id ****************
     * @apiParam {String} auth_app_id ****************
     * @apiParam {String} seller_id ****************
     * @apiParam {String} buyer_id ****************
     * @apiParam {String} notify_id 2019113000222165658003101000647447
     * @apiParam {String} sign_type RSA2
     * @apiParam {String} sign qjvNCIpy2JDxV8+A6g/DI3tidUZMDOw79uevWEY...kGAB+6k
     * @apiParam {String} charset UTF-8
     * @apiParam {String} version 1.0
     *
     * @apiSuccessExample Success-Response:
     * 'success'
     * @apiErrorExample Error-Response:
     * 'error'
     */
    public function actionGuildLiveAlipay()
    {
        if (!$_POST
                || !isset($_POST['sign'], $_POST['out_trade_no'], $_POST['trade_no'], $_POST['trade_status'])
                || (!in_array($_POST['trade_status'], ['TRADE_FINISHED', 'TRADE_CLOSED']) && !isset($_POST['receipt_amount']))
        ) {
            Yii::error('公会主播支付违约金支付宝回调报文异常', __METHOD__);
            throw new HttpException(403, '非法请求');
        }
        $ali = new AuthAli();
        if (!$ali->getAopClient()->rsaCheckV1($_POST, null, 'RSA2')) {
            throw new HttpException(403, '非法请求');
        }
        $request = Yii::$app->request;
        $trade_status = $request->post('trade_status');
        if ($trade_status !== 'TRADE_SUCCESS') {
            // 订单交易成功 3 个月后会收到一次异步通知（交易成功，不可退款）
            if ('TRADE_FINISHED' === $trade_status) {
                return 'success';
            }
            // 超时未付款交易关闭通知
            if ('TRADE_CLOSED' === $trade_status) {
                return 'success';
            }
            // WAIT_BUYER_PAY 不触发通知
            Yii::error(sprintf('主播强制解约支付失败：%s', Json::encode($_POST)), __METHOD__);
            return 'error';
        }

        $out_trade_no = $request->post('out_trade_no');
        $price = $request->post('receipt_amount');
        $tid = $request->post('trade_no');
        $buyer_id = $request->post('buyer_id');

        if (!GuildLiveOrder::updateOrder($out_trade_no, $price, $tid)) {
            return 'error';
        }

        return 'success';
    }

    /**
     * @api {post} /callback/ios-iap 苹果服务端通知回调（退款、订阅等）
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/callback/ios-iap
     * @apiSampleRequest /callback/ios-iap
     * @apiDescription https://developer.apple.com/documentation/storekit/in-app_purchase/subscriptions_and_offers/enabling_server-to-server_notifications
     *
     * @apiName ios-iap
     * @apiGroup callback
     *
     * @apiParam RawBody {json} v1 https://developer.apple.com/documentation/appstoreservernotifications/app-store-server-notifications-version-1
     * @apiParamExample RawBody {json} 退款通知报文
     *     {
     *       "notification_type": "REFUND",
     *       "environment": "PROD",
     *       "latest_receipt": "ewoJInNpZ25hdHVyZSIgPS........jsKfQ==",
     *       "latest_receipt_info": {
     *         "cancellation_reason": "0",
     *         "is_trial_period": "false",
     *         "is_in_intro_offer_period": "false",
     *         "unique_identifier": "ecbb19090c99dbe328b1d650c47553db3d0b5202",
     *         "unique_vendor_identifier": "41ED3955-B41A-4F21-8D76-0554B50A5C17",
     *         "cancellation_date": "2020-08-04 01:06:32 Etc/GMT",
     *         "cancellation_date_ms": "1596503192000",
     *         "cancellation_date_pst": "2020-08-03 18:06:32 America/Los_Angeles",
     *         "purchase_date": "2020-08-01 23:35:58 Etc/GMT",
     *         "purchase_date_ms": "1596324958000",
     *         "purchase_date_pst": "2020-08-01 16:35:58 America/Los_Angeles",
     *         "original_purchase_date": "2020-08-01 23:35:58 Etc/GMT",
     *         "original_purchase_date_ms": "1596324958000",
     *         "original_purchase_date_pst": "2020-08-01 16:35:58 America/Los_Angeles",
     *         "item_id": "1265411383",
     *         "app_item_id": "1148465254",
     *         "version_external_identifier": "836663646",
     *         "bid": "com.missevan.CatEarFM",
     *         "product_id": "com.missevan.CatEarFM0003",
     *         "transaction_id": "730000500509040",
     *         "original_transaction_id": "730000500509040",
     *         "quantity": "1",
     *         "bvrs": "2"
     *       },
     *       "unified_receipt": {
     *         "status": 0,
     *         "environment": "Production",
     *         "latest_receipt_info": [{
     *           "quantity": "1",
     *           "product_id": "com.missevan.CatEarFM0003",
     *           "transaction_id": "730000500509040",
     *           "purchase_date": "2020-08-01 23:35:58 Etc/GMT",
     *           "purchase_date_ms": "1596324958000",
     *           "purchase_date_pst": "2020-08-01 16:35:58 America/Los_Angeles",
     *           "original_purchase_date": "2020-08-01 23:35:58 Etc/GMT",
     *           "original_purchase_date_ms": "1596324958000",
     *           "original_purchase_date_pst": "2020-08-01 16:35:58 America/Los_Angeles",
     *           "is_trial_period": "false",
     *           "original_transaction_id": "730000500509040",
     *           "cancellation_date": "2020-08-04 01:06:32 Etc/GMT",
     *           "cancellation_date_ms": "1596503192000",
     *           "cancellation_date_pst": "2020-08-03 18:06:32 America/Los_Angeles",
     *           "cancellation_reason": "0"
     *         }],
     *         "latest_receipt": "MIISdQYJKoZIhvcNAQc.....QIBAQQDAgEDMAwCAQ4CAQEEBAICAIkwDQIBCIBDQIBAQ"
     *       },
     *       "bid": "com.missevan.CatEarFM",
     *       "bvrs": "2"
     *     }
     *
     * @apiParam RawBody {json} v2 https://developer.apple.com/documentation/appstoreservernotifications/app_store_server_notifications_v2
     * @apiParamExample RawBody {json} 解析前的退款通知报文
     *     {"signedPayload":"eyJhbGciOiJFUzI1NiIsIng1YyI6WyJNSUlFTURDQ...Jakp4QnFTSm9DVENkdHZGtyWnBOMGRnU2cifSwidmVycDE2MDM1NzN9"}
     * @apiParamExample RawBody {json} 解析后的退款通知报文
     *     {
     *       "notificationType": "REFUND",
     *       "notificationUUID": "dd3d0d1a-0a39-46f6-83f7-9fe3ed0fccab",
     *       "data": {
     *         "appAppleId": 1148465254,
     *         "bundleId": "com.missevan.CatEarFM",
     *         "bundleVersion": "124",
     *         "environment": "Production",
     *         "signedTransactionInfo": {
     *           "transactionId": "90002145998663",
     *           "originalTransactionId": "90002145998663",
     *           "bundleId": "com.missevan.CatEarFM",
     *           "productId": "com.missevan.CatEarFM0002",
     *           "purchaseDate": 1726158376000,
     *           "originalPurchaseDate": 1726158376000,
     *           "quantity": 1,
     *           "type": "Consumable",
     *           "inAppOwnershipType": "PURCHASED",
     *           "signedDate": 1726273836637,
     *           "revocationReason": 0,
     *           "revocationDate": 1726272922000,
     *           "environment": "Production",
     *           "transactionReason": "PURCHASE",
     *           "storefront": "CHN",
     *           "storefrontId": "143465",
     *           "price": 18000,
     *           "currency": "CNY"
     *         }
     *       },
     *       "version": "2.0",
     *       "signedDate": 1726273836648
     *     }
     */
    public function actionIosIap()
    {
        $rawbody = Yii::$app->request->rawBody;
        $body = Json::decode($rawbody) ?: [];
        if (!$body) {
            throw new HttpException(403, '非法请求');
        }
        if (array_key_exists('signedPayload', $body)) {
            return $this->iosIapV2($body);
        } else {
            return $this->iosIapV1($body);
        }
    }

    private function iosIapV1(array $body)
    {
        if (!$body
                || $body['notification_type'] !== 'REFUND'
                || $body['environment'] !== 'PROD'
                || $body['bid'] !== 'com.missevan.CatEarFM'
                || !array_key_exists('unified_receipt', $body)
                || !array_key_exists('latest_receipt_info', $body['unified_receipt'])
        ) {
            // TODO: 处理 CONSUMPTION_REQUEST 的通知类型
            if ($body && $body['notification_type'] === 'CONSUMPTION_REQUEST') {
                throw new HttpException(403, '非法请求');
            }
            Yii::error('苹果退款通知回调报文异常', __METHOD__);
            throw new HttpException(403, '非法请求');
        }
        // iOS 退款报文可能丢失 latest_receipt 字段
        if (array_key_exists('latest_receipt', $body)) {
            $latest_receipt = IosReceipt::verifyReceipt($body['latest_receipt']);
        } else if (array_key_exists('unified_receipt', $body) && array_key_exists('latest_receipt', $body['unified_receipt'])) {
            $latest_receipt = IosReceipt::verifyReceipt($body['unified_receipt']['latest_receipt']);
        } else {
            Yii::error('苹果退款通知回调报文异常', __METHOD__);
            throw new HttpException(403, '非法请求');
        }
        if (!$latest_receipt) {
            Yii::error('苹果退款通知回调报文异常', __METHOD__);
            throw new HttpException(403, '非法请求');
        }
        if ($latest_receipt['status'] !== 0) {
            Yii::error(sprintf('苹果退款通知小票异常：receipt[%s]', Json::encode($latest_receipt)), __METHOD__);
            throw new HttpException(400, '不合法的 iOS 小票');
        }
        // iOS 退款小票中可能丢失 original_transaction_id 字段
        if (array_key_exists('original_transaction_id', $latest_receipt['receipt'])
                && array_key_exists('latest_receipt_info', $body)
                && array_key_exists('original_transaction_id', $body['latest_receipt_info'])
                && $latest_receipt['receipt']['original_transaction_id'] !== $body['latest_receipt_info']['original_transaction_id']) {
            Yii::error(sprintf('苹果退款通知小票异常：receipt[%s], raw_body[%s]', Json::encode($latest_receipt), Yii::$app->request->rawBody), __METHOD__);
            throw new HttpException(400, '不合法的 iOS 小票');
        }
        foreach ($body['unified_receipt']['latest_receipt_info'] as $receipt) {
            $this->processIosIAP($receipt['product_id'], $receipt['transaction_id']);
        }
        return;
    }

    /**
     * @link https://info.missevan.com/pages/viewpage.action?pageId=118095008
     * @param array $body
     * @throws HttpException
     */
    public function iosIapV2(array $body)
    {
        $message = new ServerNotification($body['signedPayload']);
        // 记录解析后的报文
        $now = intval(microtime(true)*1000);
        foreach (str_split(Json::encode($message->toArray()), 800) as $i => $part) {
            Yii::info(sprintf('[%d-%d] callback ios-iap message: %s', $now, $i, $part), __METHOD__);
        }
        if (!RechargeForm::isValidAppPackageName($message->data->bundleId)) {
            Yii::error(sprintf('苹果退款通知回调报文异常：bundleId[%s]', $message->data->bundleId), __METHOD__);
            throw new HttpException(403, '非法请求');
        }
        if (YII_ENV_PROD && !$message->data->isProductionEnv()) {
            Yii::error(sprintf('苹果退款通知回调报文异常：environment[%s]', $message->data->environment), __METHOD__);
            throw new HttpException(403, '非法请求');
        }

        try {
            if ($message->notificationType === 'REFUND' && $message->data->signedTransactionInfo->isConsumableType()) {
                $this->iosIapV2ConsumableRefund($message);
            } else {
                $form = new VipIosSubscriptionForm($message);
                $form->processNotification();
            }
        } catch (Exception $e) {
            Yii::error(sprintf('iOS 回调通知处理失败：%s', $e->getMessage()), __METHOD__);
            return;
        }
    }

    public function iosIapV2ConsumableRefund(ServerNotification $message)
    {
        if ($message->notificationType !== 'REFUND') {
            throw new Exception('通知类型错误');
        }
        if ($message->data->signedTransactionInfo->type !== 'Consumable') {
            throw new Exception('商品类型错误');
        }
        $this->processIosIAP($message->data->signedTransactionInfo->productId, $message->data->signedTransactionInfo->transactionId);
        return;
    }

    private function processIosIAP(string $product_id, string $transaction_id)
    {
        $record = IosReceipt::find()->select('id, user_id, transaction_id')->where([
            'product_id' => $product_id,
            'transaction_id' => $transaction_id,
            'status' => IosReceipt::STATUS_SUCCESS,
        ])->limit(1)->one();
        if (!$record) {
            // 存在用户付完款后，钻石未到账（如 iOS 丢单），用户在苹果后台申请退款，又拿之前的小票找过来要钻
            Yii::error(sprintf('ios receipt not found: transaction_id[%s]', $transaction_id), __METHOD__);
            throw new HttpException(404, '不合法的 iOS 小票');
        }
        $order = RechargeOrder::findOne([
            'uid' => $record->user_id,
            'tid' => $record->transaction_id,
            'status' => RechargeOrder::STATUS_SUCCESS,
            'type' => RechargeOrder::TYPE_APPLEPAY,
        ]);
        if (!$order) {
            return;
        }
        $order->refundIOS(function () use ($record) {
            $record->updateAttributes([
                'status' => IosReceipt::STATUS_REFUND,
                'modified_time' => $_SERVER['REQUEST_TIME'],
            ]);
        });
    }

    /**
     * @deprecated 新商品使用充值直连模式发布，存量商品用直连模式发布后，可以移除该方式
     * @api {post} /callback/doudian 抖店购买商品回调（虚拟商品）
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/callback/doudian
     * @apiSampleRequest /callback/doudian
     * @apiDescription https://op.jinritemai.com/docs/guide-docs/10/99
     *
     * @apiName doudian
     * @apiGroup callback
     *
     * @apiParam RawBody {json}
     * @apiParamExample RawBody {json}
     *     [
     *       {
     *         "tag": "200",
     *         "msg_id": "69722500408517524840::200:1631594303:7007240140376524322",
     *         "data": "{\"aftersale_id\":7007644127869288711,\"aftersale_status\":6,\"aftersale_type\":2,\r\n\"apply_time\":1631594303,\"p_id\":*******************,\"reason_code\":1,\"refund_amount\":2,\"refund_post_amount\":0,\"refund_voucher_num\":0,\"s_id\":*******************,\r\n\"shop_id\":4463798}"
     *       }
     *     ]
     *
     * @apiSuccessExample Success-Response: 若非成功响应，抖店平台将重推 3 次（30s、5min、1h）
     *     {
     *       "code": 0,
     *       "msg": "success"
     *     }
     */
    public function actionDoudian()
    {
        if (!$event_sign = Yii::$app->request->headers['event-sign']) {
            throw new HttpException(403, '非法请求');
        }
        if (!$app_key = Yii::$app->request->headers['app-id']) {
            throw new HttpException(403, '非法请求');
        }
        $raw_body = Yii::$app->request->rawBody;
        $client = AuthDouDian::client();
        if (!$client->isLegalPushMessage($app_key, $event_sign, $raw_body)) {
            throw new HttpException(403, '非法请求');
        }
        $body = Json::decode($raw_body);
        if (empty($body)) {
            throw new HttpException(403, '非法请求');
        }

        $form = new DouDianTransactionForm($client);
        foreach ($body as $item) {
            if ($item['tag'] === DouDianPushMessage::TAG_PUSH_MESSAGE_OPEN) {
                break;
            }
            $msg = new DouDianPushMessage($item);
            $content = $msg->getContent();
            try {
                switch ($msg->getTag()) {
                    case DouDianPushMessage::TAG_ORDER_CREATE:
                        $form->createOrder($content, UserContext::fromUser(Yii::$app->request));
                        break;
                    case DouDianPushMessage::TAG_ORDER_PAID:
                        $form->pay($content);
                        break;
                    case DouDianPushMessage::TAG_ORDER_REFUND_CREATE:
                        $form->refund($content);
                        break;
                    default:
                        Yii::error('doudian message error: ' . $item, __METHOD__);
                }
            } catch (Exception $e) {
                Yii::error(
                    sprintf('doudian process error: order_id[%s], error_message[%s]',
                        $content->getShopOrderId(), $e->getMessage()),
                    __METHOD__
                );
                // 若为自定义的抖店异常类，则抛出该异常，让抖店重发通知
                if ($e instanceof DouDianException) {
                    throw $e;
                }
            }
        }

        return DouDianPushMessage::successfulResponse();
    }

    /**
     * @api {post} /callback/doudian-notify{?app_key,sign,timestamp,sign_method} 抖店充值直连订单充值
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/callback/doudian-notify
     * @apiSampleRequest /callback/doudian-notify
     * @apiDescription 收到用户订单信息，通知商家充值，超时时间: 5000ms
     *
     * @apiName doudian-notify
     * @apiGroup callback
     *
     * @apiParam (Query) {String} app_key 应用创建完成后被分配的 key
     * @apiParam (Query) {String} sign 输入参数签名结果
     * @apiParam (Query) {String} timestamp 时间戳，格式为 yyyy-MM-dd HH:mm:ss，时区为 GMT+8，例如：2016-01-01 12:00:00
     * @apiParam (Query) {String} [sign_method=md5] 签名算法
     *
     * @apiParam RawBody {json}
     * @apiParamExample RawBody {json}
     *     {
     *       "trade_order_no": "*******************",  // 充值平台交易单号
     *       "topup_biz": "MOBILE_TOPUP",  // 业务类型，充值请求参数原样返回即可
     *       "time_start": "20211013160340",  // 整个充值流程的计时起点，格式（yyyyMMddHHmmss）
     *       "time_limit": "10800",  // 充值请求截止时延，单位为秒，以 time_start 为起点
     *       "buy_num": "1",  // 购买数量
     *       "amount_unit": "10000",  // 单个商品充值面额（单位：分）。注：总充值金额为 buy_num*amount_unit，单位分，流量充值为商品价格
     *       "sku_id": "*********",  // 在电商平台商品的 skuId
     *       "shop_id": "12345",  //商家店铺 id
     *       "account_list": [{  // 充值账号列表
     *         "account_val": "***********",  // 充值账号
     *         "account_type": "MOBILE",  // 账号类型
     *         "account_name": "绑定手机号"  // 账号名称
     *       }],
     *       "code": "DY8123123",  // 商品编码（抖店后台）
     *       "pay_amount": "10000"  // 支付金额（单位：分）
     *     }
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "data": {
     *         "trade_order_no": "*******************",  // 充值平台交易单号 按照入参数返回
     *         "topup_biz": "MOBILE_TOPUP",  // 业务类型 按照入参数返回
     *         "seller_order_no": "**************",  // 商家自有充值系统单号，与 trade_order_no 保持一一对应
     *         // 订单状态，可选范围：SUCCESS 订单充值成功，FAILED 订单充值失败，IN_PROCESS 订单充值中（如果返回状态非以上状态则将进行接口重试）
     *         "seller_order_status": "SUCCESS",
     *         "err_code": "0",
     *         "err_desc": ""  // 错误描述 当 seller_order_status 为 FAILED 时，需要填写充值失败的原因
     *       },
     *       "code": 0,  // 0 业务处理成功，100001 验签失败，100002 参数错误，100003 系统错误
     *       "message": "success"
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     {
     *       "data": {
     *         "trade_order_no": "*******************",
     *         "topup_biz": "MOBILE_TOPUP",
     *         "seller_order_no": "**************",
     *         "seller_order_status": "SUCCESS",
     *         "err_code": "2001",
     *         "err_desc": "携号转网失败"
     *       },
     *       "code": 0,
     *       "message": "success"
     *     }
     */
    public function actionDoudianNotify()
    {
        return $this->doudianMobileTopup(self::DOUDIAN_MOBILE_TOPUP_ACTION_NOTIFY, Yii::$app->request);
    }

    /**
     * @api {post} /callback/doudian-query{?app_key,sign,timestamp,sign_method} 抖店充值直连订单查询
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/callback/doudian-query
     * @apiSampleRequest /callback/doudian-query
     * @apiDescription 查询商家订单充值状态。超时时间: 5000ms
     *
     * @apiName doudian-query
     * @apiGroup callback
     *
     * @apiParam (Query) {String} app_key 应用创建完成后被分配的 key
     * @apiParam (Query) {String} sign 输入参数签名结果
     * @apiParam (Query) {String} timestamp 时间戳，格式为 yyyy-MM-dd HH:mm:ss，时区为 GMT+8，例如：2016-01-01 12:00:00
     * @apiParam (Query) {String} [sign_method=md5] 签名算法
     *
     * @apiParam RawBody {json}
     * @apiParamExample RawBody {json}
     *     {
     *       "shop_id": "12345",  //商家店铺 id
     *       "topup_biz": "MOBILE_TOPUP",  // 业务类型
     *       "trade_order_no": "*******************"  // 充值平台交易单号
     *     }
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "data": {
     *         "trade_order_no": "*******************",  // 充值平台交易单号 按照入参数返回
     *         "topup_biz": "MOBILE_TOPUP",  // 业务类型 按照入参数返回
     *         "seller_order_no": "**************",  // 商家自有充值系统单号，与 trade_order_no 保持一一对应
     *         // 订单状态，可选范围：SUCCESS 订单充值成功，FAILED 订单充值失败，IN_PROCESS 订单充值中（如果返回状态非以上状态则将进行接口重试）
     *         "seller_order_status": "SUCCESS",
     *         "err_code": "0",
     *         "err_desc": ""  // 错误描述 当 seller_order_status 为 FAILED 时，需要填写充值失败的原因
     *       },
     *       "code": 0,  // 0 业务处理成功，100001 验签失败，100002 参数错误，100003 系统错误
     *       "message": "success"
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     {
     *       "data": {
     *         "trade_order_no": "*******************",
     *         "topup_biz": "MOBILE_TOPUP",
     *         "seller_order_no": "**************",
     *         "seller_order_status": "SUCCESS",
     *         "err_code": "2001",
     *         "err_desc": "携号转网失败"
     *       },
     *       "code": 0,
     *       "message": "success"
     *     }
     */
    public function actionDoudianQuery()
    {
        return $this->doudianMobileTopup(self::DOUDIAN_MOBILE_TOPUP_ACTION_QUERY, Yii::$app->request);
    }

    /**
     * @api {post} /callback/doudian-cancel{?app_key,sign,timestamp,sign_method} 抖店充值直连订单取消
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/callback/doudian-cancel
     * @apiSampleRequest /callback/doudian-cancel
     * @apiDescription 通知商家取消充值 \
     * 充值时间超过时间限制，回调用此接口，商家侧再保证该接口功能正常可用的同时，平台建议商家系统也根据充值时效，自行控制充值取消，\
     * 避免因该接口短暂不可用导致平台给用户退款，但商家系统并没有取消充值导致的资损问题。\
     * 超时时间: 5000ms
     *
     * @apiName doudian-cancel
     * @apiGroup callback
     *
     * @apiParam (Query) {String} app_key 应用创建完成后被分配的 key
     * @apiParam (Query) {String} sign 输入参数签名结果
     * @apiParam (Query) {String} timestamp 时间戳，格式为 yyyy-MM-dd HH:mm:ss，时区为 GMT+8，例如：2016-01-01 12:00:00
     * @apiParam (Query) {String} [sign_method=md5] 签名算法
     *
     * @apiParam RawBody {json}
     * @apiParamExample RawBody {json}
     *     {
     *       "shop_id": "12345",  //商家店铺 id
     *       "topup_biz": "MOBILE_TOPUP",  // 业务类型
     *       "trade_order_no": "*******************"  // 充值平台交易单号
     *     }
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "data": {
     *         "trade_order_no": "*******************",  // 充值平台交易单号 按照入参数返回
     *         "topup_biz": "MOBILE_TOPUP",  // 业务类型 按照入参数返回
     *         "seller_order_no": "**************",  // 商家自有充值系统单号，与 trade_order_no 保持一一对应
     *         // 订单状态，可选范围：SUCCESS 订单充值成功，FAILED 订单充值失败，IN_PROCESS 订单充值中（如果返回状态非以上状态则将进行接口重试）
     *         "seller_order_status": "SUCCESS",
     *         "err_code": "0",
     *         "err_desc": ""  // 错误描述 当 seller_order_status 为 FAILED 时，需要填写充值失败的原因
     *       },
     *       "code": 0,  // 0 业务处理成功，100001 验签失败，100002 参数错误，100003 系统错误
     *       "message": "success"
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     {
     *       "data": {
     *         "trade_order_no": "*******************",
     *         "topup_biz": "MOBILE_TOPUP",
     *         "seller_order_no": "**************",
     *         "seller_order_status": "SUCCESS",
     *         "err_code": "2001",
     *         "err_desc": "携号转网失败"
     *       },
     *       "code": 0,
     *       "message": "success"
     *     }
     */
    public function actionDoudianCancel()
    {
        return $this->doudianMobileTopup(self::DOUDIAN_MOBILE_TOPUP_ACTION_CANCEL, Yii::$app->request);
    }

    // 抖店 SPI 通知类型
    const DOUDIAN_MOBILE_TOPUP_ACTION_NOTIFY = 0;
    const DOUDIAN_MOBILE_TOPUP_ACTION_QUERY = 1;
    const DOUDIAN_MOBILE_TOPUP_ACTION_CANCEL = 2;

    // 抖店 SPI 错误码
    const DOUDIAN_SPI_CODE_SUCCESS = 0;
    const DOUDIAN_SPI_CODE_ERROR_SIGN = 100001;
    const DOUDIAN_SPI_CODE_ERROR_PARAMS = 100002;
    const DOUDIAN_SPI_CODE_SERVER_ERROR = 100003;

    /**
     * 抖店充值直连处理
     *
     * @link https://bytedance.feishu.cn/docx/doxcnWFiFFSClgJw0jHKhiLwtSh
     * @link https://op.jinritemai.com/docs/guide-docs/api-log
     *
     * @param int $action 通知类型
     * @param Request $request 请求体
     * @return array
     * @throws HttpException
     */
    private function doudianMobileTopup(int $action, Request $request)
    {
        $app_key = $request->get('app_key');
        $timestamp = $request->get('timestamp');
        $sign_method = $request->get('sign_method', 'md5');
        $sign = $request->get('sign');
        $body = $request->getRawBody();
        $params = Json::decode($body);

        if (!$app_key || !$timestamp || !$sign_method || !$sign || !$body || !$params || !array_key_exists('trade_order_no', $params)) {
            throw new HttpException(403, '非法请求');
        }
        $client = AuthDouDian::client();
        if ($client->getSPISign($params, $app_key, $timestamp, $sign_method) !== $sign) {
            return $this->renderDouDianMobileTopupResp($params['trade_order_no'], null, self::DOUDIAN_SPI_CODE_ERROR_SIGN, '验签失败');
        }

        if ($params['topup_biz'] !== AuthDouDian::TOPUP_BIZ_MOBILE_TOPUP) {
            return $this->renderDouDianMobileTopupResp($params['trade_order_no'], null, self::DOUDIAN_SPI_CODE_ERROR_PARAMS, '不支持的业务类型：' . $params['topup_biz']);
        }

        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_GENERATE_ORDER, $params['trade_order_no']);
        if (!$redis->lock($lock, ONE_MINUTE)) {
            return $this->renderDouDianMobileTopupResp($params['trade_order_no'], null, self::DOUDIAN_SPI_CODE_SUCCESS, '订单正在更新中');
        }

        try {
            $msg = new DouDianSPIMsgContent($params);
            if (!$client->isLegalShopId($msg->getShopId())) {
                return $this->renderDouDianMobileTopupResp($msg->getShopOrderId(), null, self::DOUDIAN_SPI_CODE_ERROR_PARAMS, '店铺 ID 错误');
            }

            $form = new DouDianTransactionForm($client);
            /**
             * @var RechargeOrder $order
             */
            $order = RechargeOrder::find()->where([
                'tid' => $msg->getShopOrderId(),
                'type' => RechargeOrder::TYPE_DOUDIAN,
            ])->limit(1)->one();
            switch ($action) {
                case self::DOUDIAN_MOBILE_TOPUP_ACTION_NOTIFY:
                    if ($order) {
                        return $this->renderDouDianMobileTopupResp($msg->getShopOrderId(), $order);
                    }
                    $form->createOrder($msg, UserContext::fromUser(Yii::$app->request));
                    $form->pay($msg);

                    $attrs = array_merge($msg->getSellerOrder()->attributes, ['status' => RechargeOrder::STATUS_SUCCESS]);
                    return $this->renderDouDianMobileTopupResp($msg->getShopOrderId(), new RechargeOrder($attrs));
                case self::DOUDIAN_MOBILE_TOPUP_ACTION_QUERY:
                    return $this->renderDouDianMobileTopupResp($msg->getShopOrderId(), $order);
                case self::DOUDIAN_MOBILE_TOPUP_ACTION_CANCEL:
                    if ($order && $order->isSuccess()) {
                        $msg->setSellerOrder($order);
                        $form->refund($msg);
                    }
                    return $this->renderDouDianMobileTopupResp($msg->getShopOrderId(), $order);
                default:
                    throw new HttpException(403, '不支持的通知类型：' . $action);
            }
        } catch (Exception $e) {
            Yii::error(
                sprintf('doudian process error: order_id[%s], error_message[%s]',
                    $params['trade_order_no'], $e->getMessage()),
                __METHOD__
            );
            return $this->renderDouDianMobileTopupResp($params['trade_order_no'], null, self::DOUDIAN_SPI_CODE_SERVER_ERROR, $e->getMessage());
        } finally {
            $redis->unlock($lock);
        }
    }

    /**
     * 返回抖店响应
     *
     * @param string $shop_order_id 抖店订单号
     * @param RechargeOrder|null $seller_order 充值订单
     * @param int $code 错误码
     * @param string $message 错误信息描述
     * @return array
     */
    private function renderDouDianMobileTopupResp(string $shop_order_id, ?RechargeOrder $seller_order = null,
            int $code = self::DOUDIAN_SPI_CODE_SUCCESS, string $message = 'success')
    {
        $seller_order_no = '';
        $seller_order_status = AuthDouDian::SELLER_ORDER_STATUS_IN_PROCESS;
        if ($seller_order) {
            $seller_order_no = $seller_order->getOrderId();
            switch ($seller_order->status) {
                case RechargeOrder::STATUS_CREATE:
                    $seller_order_status = AuthDouDian::SELLER_ORDER_STATUS_IN_PROCESS;
                    break;
                case RechargeOrder::STATUS_SUCCESS:
                    $seller_order_status = AuthDouDian::SELLER_ORDER_STATUS_SUCCESS;
                    break;
                default:
                    $seller_order_status = AuthDouDian::SELLER_ORDER_STATUS_FAILED;
            }
        }

        Yii::$app->response->format = Response::FORMAT_JSON;
        Yii::$app->response->headers->add('Content-Type', 'application/json; charset=UTF-8');
        return [
            'data' => [
                'trade_order_no' => $shop_order_id,
                'topup_biz' => AuthDouDian::TOPUP_BIZ_MOBILE_TOPUP,
                'seller_order_no' => $seller_order_no,
                'seller_order_status' => $seller_order_status,
                'err_code' => '0',  // 自定义错误码，暂时不做区分
                'err_desc' => $message,
            ],
            'code' => $code,  // 抖店根据 code 判断是否通知成功
            'message' => $message
        ];
    }

    /**
     * @api {post} /callback/appsflyer AppsFlyer Push API 消息回调
     * @apiDescription https://support.appsflyer.com/hc/en-us/articles/207034356 \
     * https://support.appsflyer.com/hc/en-us/articles/208387843-Raw-data-field-dictionary-V5-#data-fields-dictionary
     * @apiVersion 0.1.0
     * @apiGroup callback
     * @apiName appsflyer
     *
     * @apiParam RawBody {json}
     * @apiParamExample RawBody {json}
     *     {
     *       "advertising_id": null,
     *       "af_ad": null,
     *       "af_ad_id": null,
     *       "af_ad_type": null,
     *       "af_adset": null,
     *       "af_adset_id": null,
     *       "af_attribution_lookback": "7d",
     *       "af_c_id": null,
     *       "af_channel": null,
     *       "af_cost_currency": null,
     *       "af_cost_model": null,
     *       "af_cost_value": null,
     *       "af_keywords": null,
     *       "af_prt": null,
     *       "af_reengagement_window": null,
     *       "af_siteid": null,
     *       "af_sub1": null,
     *       "af_sub2": null,
     *       "af_sub3": null,
     *       "af_sub4": null,
     *       "af_sub5": null,
     *       "af_sub_siteid": null,
     *       "amazon_aid": null,
     *       "android_id": null,
     *       "api_version": "2.0",
     *       "app_id": "id1148465254",
     *       "app_name": "猫耳FM(M站) - 让广播剧流行起来",
     *       "app_type": "full_app",
     *       "app_version": "4.7.6"
     *       "appsflyer_id": "1641386415480-4835762",
     *       "att": "af_authorized",
     *       "attributed_touch_time": "2022-01-05 12:42:42.127",
     *       "attributed_touch_time_selected_timezone": "2022-01-05 20:42:42.127+0800",
     *       "attributed_touch_type": "click",
     *       "bundle_id": "com.missevan.CatEarFM",
     *       "campaign": "None",
     *       "campaign_type": "ua",
     *       "carrier": null,
     *       "city": "Beijing",
     *       "contributor_1_af_prt": null,
     *       "contributor_1_campaign": null,
     *       "contributor_1_match_type": null,
     *       "contributor_1_media_source": null,
     *       "contributor_1_touch_time": null,
     *       "contributor_1_touch_type": null,
     *       "contributor_2_af_prt": null,
     *       "contributor_2_campaign": null,
     *       "contributor_2_match_type": null,
     *       "contributor_2_media_source": null,
     *       "contributor_2_touch_time": null,
     *       "contributor_2_touch_type": null,
     *       "contributor_3_af_prt": null,
     *       "contributor_3_campaign": null,
     *       "contributor_3_match_type": null,
     *       "contributor_3_media_source": null,
     *       "contributor_3_touch_time": null,
     *       "contributor_3_touch_type": null,
     *       "conversion_type": "install",
     *       "cost_in_selected_currency": null,
     *       "country_code": "CN",
     *       "custom_data": "{\"equip_id\":\"228236f1-5b4c-4aa0-b653-e587ee6f8bc5\",\"buvid\":\"Z848228236F15B4C4AA0B653E587EE6F8BC5\"}",
     *       "custom_dimension": null,
     *       "customer_user_id": "Z848228236F15B4C4AA0B653E587EE6F8BC5",
     *       "deeplink_url": null,
     *       "device_category": "phone",
     *       "device_download_time": "2022-01-05 12:40:15.000",
     *       "device_download_time_selected_timezone": "2022-01-05 20:40:15.000+0800",
     *       "device_model": "iPhone",
     *       "device_type": "iPhone8",
     *       "dma": "156001",
     *       "event_name": "install",
     *       "event_revenue": null,
     *       "event_revenue_currency": "USD",
     *       "event_revenue_usd": null,
     *       "event_source": "SDK",
     *       "event_time": "2022-01-05 12:43:27.267",
     *       "event_time_selected_timezone": "2022-01-05 20:43:27.267+0800",
     *       "event_value": "{}",
     *       "gp_broadcast_referrer": "",
     *       "gp_click_time": null,
     *       "gp_install_begin": null,
     *       "gp_referrer": null,
     *       "http_referrer": "http://sdktest.appsflyer.com/sdk-integration-test/install/click-launch?appId=id1148465254&sid=f5781a52-62dc-4d39-ad4e-77005930c78b&platform=ios&store=appStore&ts=1641386372&idfa=228236F1-5B4C-4AA0-B653-E587EE6F8BC5",
     *       "idfa": "228236F1-5B4C-4AA0-B653-E587EE6F8BC5",
     *       "idfv": "BF483576-B2CD-4547-97B7-22490782501C",
     *       "imei": null,
     *       "install_app_store": null,
     *       "install_time": "2022-01-05 12:43:27.267",
     *       "install_time_selected_timezone": "2022-01-05 20:43:27.267+0800",
     *       "ip": "***********",
     *       "is_lat": null,
     *       "is_primary_attribution": true,
     *       "is_receipt_validated": null,
     *       "is_retargeting": false,
     *       "keyword_id": null,
     *       "keyword_match_type": null,
     *       "language": "en-US",
     *       "match_type": "id_matching",
     *       "media_source": "appsflyer_sdk_test_int",
     *       "network_account_id": null,
     *       "oaid": null,
     *       "operator": null,
     *       "original_url": "https://app.appsflyer.com/id1148465254?pid=appsflyer_sdk_test_int&clickid=f5781a52-62dc-4d39-ad4e-77005930c78b&idfa=228236F1-5B4C-4AA0-B653-E587EE6F8BC5",
     *       "os_version": "13.5.1",
     *       "platform": "ios",
     *       "postal_code": "100000",
     *       "region": "AS",
     *       "retargeting_conversion_type": null,
     *       "revenue_in_selected_currency": null,
     *       "sdk_version": "v6.5.0",
     *       "selected_currency": "CNY",
     *       "selected_timezone": "Asia/Shanghai",
     *       "state": "BJ",
     *       "store_reinstall": null,
     *       "user_agent": "MissEvanApp/17 CFNetwork/1126 Darwin/19.5.0",
     *       "wifi": false
     *     }
     */
    public function actionAppsflyer()
    {
        $rawdata = Yii::$app->request->rawBody;
        $data = Json::decode($rawdata);
        if (empty($data)) {
            throw new HttpException(403, '非法请求');
        }

        $body = new AppsFlyerBody($data);
        if (!$body->isFromIOS() && !$body->isFromAndroid()) {
            Yii::error(sprintf('appsflyer: unsupported platform[%s]', $body->platform), __METHOD__);
            throw new HttpException(403, '非法请求');
        }
        if (!$body->isInstallEvent()) {
            Yii::error(sprintf('appsflyer: unsupported event[%s]', $body->event_name), __METHOD__);
            throw new HttpException(403, '非法请求');
        }

        $buvid = $body->customer_user_id;
        if (!$buvid) {
            // AF Android SDK 文档：https://dev.appsflyer.com/hc/docs/android-sdk-reference-appsflyerlib
            // AF iOS SDK 文档：https://dev.appsflyer.com/hc/docs/ios-sdk-reference-appsflyerlib
            $event_value = $body->event_value ? Json::decode($body->event_value) : [];
            if (array_key_exists('af_customer_user_id', $event_value)) {
                $buvid = $event_value['af_customer_user_id'];
            } else {
                $custom_data = $body->custom_data ? Json::decode($body->custom_data) : [];
                $buvid = $custom_data['buvid'];
            }
        }

        // 自然流量（非广告流量）来源不进行处理
        if ($body->isOrganicSource()) {
            return;
        }

        // 查询安装记录
        /**
         * @var InstallLog $install_log
         */
        $install_log = InstallLog::find()->where([
            'buvid' => $buvid,
            'device_type' => [Equipment::iOS, Equipment::Android]
        ])->orderBy('id DESC')->limit(1)->one();
        if (!$install_log) {
            throw new HttpException(404, '未找到安装记录');
        }

        // 判断是否归因
        /**
         * @var AdTrack $ad_track
         */
        $ad_track = AdTrack::find()
            ->where(['buvid' => $buvid, 'converted' => AdTrack::CONVERTED])
            ->andWhere('create_time > :from_time', [':from_time' => $_SERVER['REQUEST_TIME'] - AdTrack::TRACK_TIME_PERIOD])
            ->orderBy('id DESC')
            ->limit(1)
            ->one();
        if ($ad_track) {
            // AF Push 推送消息会晚于 Apple Search Ads（ASA）自归因上报时间，导致 AF 渠道数据先被归因在 ASA 上
            // 需要将此部分数据更新为 AF 渠道
            if ($ad_track->os === Equipment::iOS && $ad_track->vendor === AdTrack::VENDOR_APPLE) {
                $ad_track->updateAttributes([
                    'vendor' => AdTrack::VENDOR_APPSFLYER,
                    'more' => new Expression(
                        'JSON_SET(more, "$.media_source", :media_source, "$.af_channel", :af_channel)',
                        [
                            ':media_source' => $body->media_source,
                            ':af_channel' => $body->af_channel,
                        ]
                    )
                ]);
            }
            return;
        }

        AdTrack::track($install_log, AdTrack::VENDOR_APPSFLYER, $body);
        return;
    }

    /**
     * @apiDeprecated 京东旧类目接口
     *
     * @api {post} /callback/jd-topup 京东旗舰店充值
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/callback/jd-topup
     * @apiSampleRequest /callback/jd-topup
     * @apiDescription 接入文档：https://docs.qq.com/doc/DT2dtQlBIcE9XcVZs \
     * 商家接口性能要求：商家接口的 TP99 要在 600ms 以内
     *
     * @apiName jd-topup
     * @apiGroup callback
     *
     * @apiParam (公共参数) {String} sign 签名
     * @apiParam (公共参数) {string="DSA","MD5"} signType 签名方式（目前只支持 MD5）
     * @apiParam (公共参数) {String} timestamp 访问时间戳（京东发起请求的时间，格式：yyyyMMddHHmmss，例：**************）
     *
     * @apiParam (业务参数) {Number} jdOrderNo 京东订单号
     * @apiParam (业务参数) {String} payTime 支付时间（时间格式 yyyyMMddHHmmss）
     * @apiParam (业务参数) {String} notifyUrl 回调通知地址（代理商充值成功后回调通知地址）
     * @apiParam (业务参数) {String} [produceAccount] 充值号码（例：***********），卡密类型商品不传，注意：联调前请提供此字段的长度范围（如 5-30）！
     * @apiParam (业务参数) {Number} quantity 数量（直充类型商品只能买一个）
     * @apiParam (业务参数) {String} wareNo 商品编码（商家在京东创建商品时维护的商家商品编码）
     * @apiParam (业务参数) {Number} totalPrice 订单总价格（该笔订单总价格，单位分）
     * @apiParam (业务参数) {Number} vendorId 商家 ID（商家在京东入驻时维护的唯一编号，由业务人员提供）
     * @apiParam (业务参数) {String} [expand] 特殊属性（业务特定传输字段，如无特殊传输，该字段可不传）\
     * 需加密，加密方式：AES，加密字符串 URL encode 后传输 \
     * system（系统）：1（苹果）、2（安卓）\
     * mobile（手机）：值可为空，例如：*********** \
     * name（姓名）：值可为空，例如：张三 \
     * address（地址）：值可为空，例如：x 市 x 区 \
     * invoiceType（发票类型）：固定选项，例如：普通发票 \
     * invoiceTitle（发票抬头）：必填项，个人或公司名 \
     * taxNumber（税号）：必填项，例如：************
     *
     * @apiParamExample application/x-www-form-urlencoded
     *     {
     *       "wareNo": "45001",
     *       "quantity": "1",
     *       "jdOrderNo": "10001",
     *       "produceAccount": "***********",
     *       "totalPrice": "10000",
     *       "payTime": "**************",
     *       "sign": "10c1c32a49e2a520ef59ca97cefc73f9",
     *       "notifyUrl": "http://tsp.shop.jd.com/vtp/produce/notify",
     *       "vendorId": "1201",
     *       "signType": "MD5",
     *       "timestamp": "**************"
     *     }
     *
     * @apiSuccessExample {json} Success-Response: 生产成功
     *     {
     *       "agentOrderNo": "45001",
     *       "code": "JDO_200",
     *       "jdOrderNo": 10001,
     *       "produceStatus": 1,
     *       "product": "zr75dzeGdwf4MqR/6Y9oGkGYq0CBSHwhOhyCvEBK18g=",
     *       "sign": "c915ab42acdb11d6c9dd559faecef319",
     *       "signType": "MD5",
     *       "timestamp": "**************"
     *     }
     *
     * @apiSuccessExample {json} Success-Response: 生产失败
     *     {
     *       "agentOrderNo": "45001",
     *       "code": "JDO_302",
     *       "jdOrderNo": 10001,
     *       "produceStatus": 2,
     *       "sign": "fce0726bc671b1e4e7dd517f54c52a2c",
     *       "signType": "MD5",
     *       "timestamp": "20210208110820"
     *     }
     *
     * @apiSuccessExample {json} Success-Response: 生产中
     *     {
     *       "agentOrderNo": "45001",
     *       "code": "JDO_201",
     *       "jdOrderNo": 10001,
     *       "produceStatus": 3,
     *       "sign": "6c639f4f0f083943e8adae3d38d40253",
     *       "signType": "MD5",
     *       "timestamp": "**************"
     *     }
     */
    public function actionJdTopup()
    {
        $sign = trim(Yii::$app->request->post('sign'));
        $sign_type = trim(Yii::$app->request->post('signType', AuthJingDong::SIGN_TYPE_DEFAULT));
        $vendor_id = (int)Yii::$app->request->post('vendorId');
        $mobile_num = trim(Yii::$app->request->post('produceAccount'));
        $notify_url = trim(Yii::$app->request->post('notifyUrl'));

        $jd_order_no = trim(Yii::$app->request->post('jdOrderNo'));
        $real_price_in_fen = (int)Yii::$app->request->post('totalPrice');
        $quantity = (int)Yii::$app->request->post('quantity');
        $ccy_id = (int)Yii::$app->request->post('wareNo');

        $jd_client = new AuthJingDong();
        if ($sign_type !== AuthJingDong::SIGN_TYPE_DEFAULT) {
            return $jd_client->renderResponse($jd_order_no, AuthJingDong::PRODUCE_CODE_ERROR_PARAMS);
        }

        if ($jd_client->makeSign(Yii::$app->request->post()) !== $sign) {
            return $jd_client->renderResponse($jd_order_no, AuthJingDong::PRODUCE_CODE_ERROR_SIGN);
        }
        if (!$jd_client->isLegalVendorId($vendor_id)) {
            return $jd_client->renderResponse($jd_order_no, AuthJingDong::PRODUCE_CODE_ERROR_PARAMS);
        }

        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_GENERATE_ORDER, $jd_order_no);
        if (!$redis->lock($lock, ONE_MINUTE)) {
            return $jd_client->renderResponse($jd_order_no, AuthJingDong::PRODUCE_CODE_PROCESSING);
        }

        try {
            if (!$ccy = TopupMenu::findOne(['id' => $ccy_id, 'device' => TopupMenu::DEVICE_JINGDONG])) {
                throw new JingDongTopupException('未找到对应的充值项', AuthJingDong::PRODUCE_CODE_GOODS_NOT_FOUND);
            }
            /**
             * @var RechargeOrder $order
             */
            if ($order = RechargeOrder::findOne(['tid' => $jd_order_no, 'type' => RechargeOrder::TYPE_JINGDONG])) {
                switch ($order->status) {
                    case RechargeOrder::STATUS_SUCCESS:
                        return $jd_client->renderResponse($jd_order_no, AuthJingDong::PRODUCE_CODE_SUCCESS, $order->getOrderId());
                    case RechargeOrder::STATUS_CREATE:
                        throw new JingDongTopupException('订单正在处理中', AuthJingDong::PRODUCE_CODE_PROCESSING);
                    default:
                        throw new JingDongTopupException('订单状态错误', AuthJingDong::PRODUCE_CODE_SYSTEM_ERROR);
                }
            }

            $mobile_info = MUtils2::getMobileNumber($mobile_num);
            $topup_goods = new TopupGoods($jd_order_no, $ccy, $quantity, $real_price_in_fen);
            if ($topup_goods->getRealTotalPrice() !== $topup_goods->getTotalPrice()) {
                Yii::error(sprintf('京东旗舰店充值实际销售价格 %d（单位：分）与预期价格 %d（单位：分）不一致，订单号：%s', $topup_goods->getRealTotalPrice(), $topup_goods->getTotalPrice(), $topup_goods->getTransactionId()), __METHOD__);
                throw new JingDongTopupException('商品销售价格设置错误', AuthJingDong::PRODUCE_CODE_GOODS_NOT_FOUND);
            }

            $form = new JingDongTransactionForm($jd_client);
            $out_trade_no = $form->topup($topup_goods, $mobile_info, $notify_url, UserContext::fromUser(Yii::$app->request));

            return $jd_client->renderResponse($jd_order_no, AuthJingDong::PRODUCE_CODE_SUCCESS, $out_trade_no);
        } catch (Exception $e) {
            $code = AuthJingDong::PRODUCE_CODE_SYSTEM_ERROR;
            if ($e instanceof JingDongTopupException) {
                $code = $e->fail_code;
            }
            Yii::error(sprintf('京东旗舰店充值错误：error[%s], code[%s], jdOrderNo[%s]', $e->getMessage(), $code, $jd_order_no));
            return $jd_client->renderResponse($jd_order_no, $code);
        } finally {
            $redis->unlock($lock);
        }
    }

    /**
     * @apiDeprecated 京东旧类目接口
     *
     * @api {post} /callback/jd-query-order 京东旗舰店查询充值结果
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/callback/jd-query-order
     * @apiSampleRequest /callback/jd-query-order
     * @apiDescription 接入文档：https://docs.qq.com/doc/DT2dtQlBIcE9XcVZs
     *
     * @apiName jd-query-order
     * @apiGroup callback
     *
     * @apiParam (公共参数) {String} sign 签名
     * @apiParam (公共参数) {string="DSA","MD5"} signType 签名方式（目前只支持 MD5）
     * @apiParam (公共参数) {String} timestamp 访问时间戳（京东发起请求的时间，格式：yyyyMMddHHmmss，例：**************）
     *
     * @apiParam (业务参数) {Number} jdOrderNo 京东订单号
     *
     * @apiParamExample application/x-www-form-urlencoded
     *     {
     *       "jdOrderNo": "10001",
     *       "sign": "cca110f77ea9c6e87fc01aad0f0d62f4",
     *       "signType": "MD5",
     *       "timestamp": "**************"
     *     }
     *
     * @apiSuccessExample {json} Success-Response: 生产成功
     *     {
     *       "agentOrderNo": "45001",
     *       "code": "JDO_200",
     *       "jdOrderNo": 10001,
     *       "produceStatus": 1,
     *       "product": "zr75dzeGdwf4MqR/6Y9oGkGYq0CBSHwhOhyCvEBK18g=",
     *       "sign": "c915ab42acdb11d6c9dd559faecef319",
     *       "signType": "MD5",
     *       "timestamp": "**************"
     *     }
     *
     * @apiSuccessExample {json} Success-Response: 生产失败
     *     {
     *       "agentOrderNo": "45001",
     *       "code": "JDO_302",
     *       "jdOrderNo": 10001,
     *       "produceStatus": 2,
     *       "sign": "fce0726bc671b1e4e7dd517f54c52a2c",
     *       "signType": "MD5",
     *       "timestamp": "20210208110820"
     *     }
     *
     * @apiSuccessExample {json} Success-Response: 生产中
     *     {
     *       "agentOrderNo": "45001",
     *       "code": "JDO_201",
     *       "jdOrderNo": 10001,
     *       "produceStatus": 3,
     *       "sign": "6c639f4f0f083943e8adae3d38d40253",
     *       "signType": "MD5",
     *       "timestamp": "**************"
     *     }
     *
     * @apiSuccessExample {json} Success-Response: 生产中
     *     {
     *       "agentOrderNo": "",
     *       "code": "JDO_201",
     *       "jdOrderNo": 10001,
     *       "produceStatus": 3,
     *       "sign": "6c639f4f0f083943e8adae3d38d40253",
     *       "signType": "MD5",
     *       "timestamp": "**************"
     *     }
     */
    public function actionJdQueryOrder()
    {
        $sign = trim(Yii::$app->request->post('sign'));
        $sign_type = trim(Yii::$app->request->post('signType', AuthJingDong::SIGN_TYPE_DEFAULT));
        $jd_order_no = trim(Yii::$app->request->post('jdOrderNo'));

        $jd_client = new AuthJingDong();
        if ($sign_type !== AuthJingDong::SIGN_TYPE_DEFAULT) {
            return $jd_client->renderResponse($jd_order_no, AuthJingDong::PRODUCE_CODE_ERROR_PARAMS);
        }

        if ($jd_client->makeSign(Yii::$app->request->post()) !== $sign) {
            return $jd_client->renderResponse($jd_order_no, AuthJingDong::PRODUCE_CODE_ERROR_SIGN);
        }
        $order = RechargeOrder::findOne(['tid' => $jd_order_no, 'type' => RechargeOrder::TYPE_JINGDONG]);
        if (!$order) {
            return $jd_client->renderResponse($jd_order_no, AuthJingDong::PRODUCE_CODE_PROCESSING);
        }
        switch ($order->status) {
            case RechargeOrder::STATUS_CREATE:
                return $jd_client->renderResponse($jd_order_no, AuthJingDong::PRODUCE_CODE_PROCESSING, $order->getOrderId());
            case RechargeOrder::STATUS_SUCCESS:
                return $jd_client->renderResponse($jd_order_no, AuthJingDong::PRODUCE_CODE_SUCCESS, $order->getOrderId());
        }

        return $jd_client->renderResponse($jd_order_no, AuthJingDong::PRODUCE_CODE_SYSTEM_ERROR, $order->getOrderId());
    }

    /**
     * @api {post} /callback/jd-topup-v2 京东旗舰店直充接口（请求及响应均为 GBK 编码）
     * @apiDescription 接入文档：https://info.missevan.com/pages/viewpage.action?pageId=109702879
     *
     * @apiName jd-topup-v2
     * @apiGroup callback
     *
     * @apiParam {String} customerId 合作商编号
     * @apiParam {String} sign MD5 签名摘要
     * @apiParam {String} timestamp 当前时间戳（格式为 yyyyMMddHHmmss，例：**************）
     * @apiParam {String} [version] 接口版本
     * @apiParam {String} data 业务数据（对 json 格式的业务数据 进行 base64 编码后的值） \
     * 例：base64({ \
     *      "orderId": "*********", \
     *      "pin": "zhangsan", \
     *      "buyNum": "10", \
     *      "totalPrice": "150", \
     *      "gameAccount": "游戏账号", \
     *      "permit": "", \
     *      "skuId": "**********", \
     *      "brandId": "**********", \
     *      "gameAccountType": { \
     *        "id": "51yx ", \
     *        "name": "神雕账号" \
     *      }, \
     *      "gameArea": { \
     *        "id": "1", \
     *        "name": "电信一区（PVP）" \
     *      }, \
     *      "gameServer": { \
     *        "id": "11", \
     *        "name": "傲视" \
     *      }, \
     *      "chargeType": { \
     *        "id": "yuanbao", \
     *        "name": "元宝" \
     *      }, \
     *      "features": [{ \
     *        "expand1": { \
     *          "id": "1", \
     *          "name": "名称1" \
     *        } \
     *      }], \
     *      "userIp": "127.0.0.1" \
     *    })
     * @apiParam (data) {String} orderId 京东订单号（客户下单在京东生成的京东订单号 **********）
     * @apiParam (data) {String} [pin] 下单用户 pin
     * @apiParam (data) {String} buyNum 购买数量
     * @apiParam (data) {String} skuId 对应京东的商品 skuId
     * @apiParam (data) {String} outId 商家 SKU ID
     * @apiParam (data) {String} brandId 对应京东游戏品牌 ID
     * @apiParam (data) {String} userIp 用户 ip 地址，如：*************
     * @apiParam (data) {String} totalPrice 订单总价（单位：元），商家应收款（cost_price*buyNum）
     * @apiParam (data) {String} [gameAccount] 游戏帐号，例：gitluochao
     * @apiParam (data) {String} [permit] 通行证，例：魔兽世界的战网通行证
     * @apiParam (data) {String} [gameAccountType] 账号类型，例：{"id":"51yx","name":"神雕账号"}
     * @apiParam (data) {String} [chargeType] 充值类型，例：{"id":"Yuanbao","name":"元宝"}
     * @apiParam (data) {String} [gameArea] 游戏区，例：{"id":"298","name":"61 区巴山蜀水"}
     * @apiParam (data) {String} [gameServer] 游戏所在服，例：{"id":"10","name":"巴蜀万里"}
     * @apiParam (data) {String} [features] 其它属性（K/V 类型的 json 串）
     *
     * @apiSuccess {String} retCode 返回码
     * @apiSuccess {String} retMessage 成功返回成功，若是失败则返回错误信息
     *
     * @apiSuccessExample {json} Success-Response: 成功
     *     {
     *       "retCode": "100",
     *       "retMessage": "成功"
     *     }
     *
     * @apiErrorExample {json} Error-Response: 失败
     *     {
     *       "retCode": "111",
     *       "retMessage": "订单号不允许重复"
     *     }
     */
    public function actionJdTopupV2()
    {
        $vendor_id = trim(Yii::$app->request->post('customerId'));
        $data = trim(Yii::$app->request->post('data'));
        $sign = trim(Yii::$app->request->post('sign'));

        $jd_client = new AuthJingDongV2();
        if ($jd_client->makeSign(Yii::$app->request->post()) !== $sign) {
            return $jd_client->renderResponse(AuthJingDongV2::RET_CODE_ERROR_SIGN, '签名错误');
        }
        if (!$jd_client->isLegalVendorId($vendor_id)) {
            return $jd_client->renderResponse(AuthJingDongV2::RET_CODE_ERROR_PARAMS, '商家编号错误');
        }

        $params = Json::decode(iconv('GBK', 'UTF-8//IGNORE', base64_decode($data)));
        $jd_order_no = strval($params['orderId']);
        $ccy_id = (int)$params['outId'];
        $buy_num = (int)$params['buyNum'];
        $user_ip = $params['userIp'] ?? null;
        $total_price = floatval($params['totalPrice']);
        $mobile_num = trim($params['gameAccount'] ?? null);
        if (!$mobile_num) {
            return $jd_client->renderResponse(AuthJingDongV2::RET_CODE_ACCOUNT_NOT_EXISTS, '账号不存在');
        }

        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_GENERATE_ORDER, $jd_order_no);
        if (!$redis->lock($lock, ONE_MINUTE)) {
            return $jd_client->renderResponse(AuthJingDongV2::RET_CODE_SYSTEM_ERROR, '订单正在处理中', AuthJingDongV2::ORDER_STATUS_PROCESSING);
        }

        try {
            if (!$ccy = TopupMenu::findOne(['id' => $ccy_id, 'device' => TopupMenu::DEVICE_JINGDONG])) {
                throw new JingDongTopupException('未找到对应的充值项', AuthJingDongV2::RET_CODE_GOODS_NOT_EXISTS);
            }
            /**
             * @var RechargeOrder $order
             */
            if ($order = RechargeOrder::findOne(['tid' => $jd_order_no, 'type' => RechargeOrder::TYPE_JINGDONG])) {
                switch ($order->status) {
                    case RechargeOrder::STATUS_SUCCESS:
                        return $jd_client->renderResponse(AuthJingDongV2::RET_CODE_SUCCESS, '成功');
                    case RechargeOrder::STATUS_CREATE:
                        return $jd_client->renderResponse(AuthJingDongV2::RET_CODE_SYSTEM_ERROR, '订单正在处理中', AuthJingDongV2::ORDER_STATUS_PROCESSING);
                    default:
                        throw new JingDongTopupException('订单状态错误', AuthJingDongV2::RET_CODE_SYSTEM_ERROR);
                }
            }

            $mobile_info = MUtils2::getMobileNumber($mobile_num);
            $topup_goods = new TopupGoods($jd_order_no, $ccy, $buy_num, Balance::profitUnitConversion($total_price, Balance::CONVERT_YUAN_TO_FEN));
            if ($topup_goods->getRealTotalPrice() !== $topup_goods->getTotalPrice()) {
                Yii::error(sprintf('京东旗舰店充值实际销售价格 %d（单位：分）与预期价格 %d（单位：分）不一致，订单号：%s', $topup_goods->getRealTotalPrice(), $topup_goods->getTotalPrice(), $topup_goods->getTransactionId()), __METHOD__);
                throw new JingDongTopupException('商品销售价格设置错误', AuthJingDongV2::RET_CODE_PRICE_NOT_CONSISTENT);
            }

            $user_context = UserContext::fromUser(Yii::$app->request);
            if ($user_ip) {
                $user_context->ip = $user_ip;
            }
            $form = new JingDongTransactionForm($jd_client);
            $form->topup($topup_goods, $mobile_info, null, $user_context);

            return $jd_client->renderResponse(AuthJingDongV2::RET_CODE_SUCCESS, '成功', AuthJingDongV2::ORDER_STATUS_SUCCESS);
        } catch (Exception $e) {
            $code = AuthJingDongV2::RET_CODE_SYSTEM_ERROR;
            if ($e instanceof JingDongTopupException) {
                $code = $e->fail_code;
            }
            Yii::error(sprintf('京东旗舰店充值错误：error[%s], code[%s], jdOrderNo[%s]', $e->getMessage(), $code, $jd_order_no));
            return $jd_client->renderResponse($code, $e->getMessage());
        } finally {
            $redis->unlock($lock);
        }
    }

    /**
     * @api {post} /callback/jd-query-order-v2 京东旗舰店订单状态查询（请求及响应均为 GBK 编码）
     * @apiDescription 接入文档：https://info.missevan.com/pages/viewpage.action?pageId=109702879
     *
     * @apiName jd-query-order-v2
     * @apiGroup callback
     *
     * @apiParam {String} customerId 合作商编号
     * @apiParam {String} sign MD5 签名摘要
     * @apiParam {String} timestamp 当前时间戳（格式为 yyyyMMddHHmmss，例：**************）
     * @apiParam {String} [version] 接口版本
     * @apiParam {String} data 业务数据（对 json 格式的业务数据 进行 base64 编码后的值） \
     * 例：base64({ \
     *      "orderId": ********* \
     *    })
     * @apiParam (data) {Number} orderId 京东订单号（客户下单在京东生成的京东订单号 **********）
     *
     * @apiSuccess {String} retCode 返回码
     * @apiSuccess {String} retMessage 成功返回成功，若是失败则返回错误信息
     * @apiSuccess {String} data base64 后的数据，例：base64({"orderStatus":"0"})
     * @apiSuccess (data) {Number} orderStatus 订单状态（0 充值成功，1 充值中，2 充值失败）
     *
     * @apiSuccessExample {json} Success-Response: 充值成功
     *     {
     *       "retCode": "100",
     *       "retMessage": "查询成功",
     *       "data": "eyJvcmRlclN0YXR1cyI6MH0="
     *     }
     */
    public function actionJdQueryOrderV2()
    {
        $sign = trim(Yii::$app->request->post('sign'));
        $vendor_id = trim(Yii::$app->request->post('customerId'));
        $data = trim(Yii::$app->request->post('data'));

        $params = Json::decode(iconv('GBK', 'UTF-8//IGNORE', base64_decode($data)));
        // NOTICE: 此处 jd-query-order-v2 京东查询商家订单状态，京东回调的参数是整型数字。上面 jd-topup-v2 京东充值，回调的参数是字符串
        $jd_order_no = strval($params['orderId']);

        $jd_client = new AuthJingDongV2();
        try {
            if ($jd_client->makeSign(Yii::$app->request->post()) !== $sign) {
                return $jd_client->renderResponse(AuthJingDongV2::RET_CODE_ERROR_SIGN, '签名错误');
            }
            if (!$jd_client->isLegalVendorId($vendor_id)) {
                throw new JingDongTopupException('商家编号错误', AuthJingDongV2::RET_CODE_ERROR_PARAMS);
            }

            $order = RechargeOrder::findOne(['tid' => $jd_order_no, 'type' => RechargeOrder::TYPE_JINGDONG]);
            if (!$order) {
                return $jd_client->renderResponse(AuthJingDongV2::RET_CODE_ORDER_NOT_EXISTS, '订单不存在');
            }
            switch ($order->status) {
                case RechargeOrder::STATUS_CREATE:
                    return $jd_client->renderResponse(AuthJingDongV2::RET_CODE_SYSTEM_ERROR, '订单处理中', AuthJingDongV2::ORDER_STATUS_PROCESSING);
                case RechargeOrder::STATUS_SUCCESS:
                    return $jd_client->renderResponse(AuthJingDongV2::RET_CODE_SUCCESS, '成功', AuthJingDongV2::ORDER_STATUS_SUCCESS);
                default:
                    throw new JingDongTopupException('订单状态错误', AuthJingDongV2::RET_CODE_SYSTEM_ERROR);
            }
        } catch (Exception $e) {
            $code = AuthJingDongV2::RET_CODE_SYSTEM_ERROR;
            if ($e instanceof JingDongTopupException) {
                $code = $e->fail_code;
            }
            return $jd_client->renderResponse($code, $e->getMessage());
        }
    }

    /**
     * @api {get} /callback/bili-large-pay{?msgId,msgContent} 哔哩哔哩大额支付回调
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/callback/bili-large-pay
     * @apiSampleRequest /callback/bili-large-pay
     * @apiDescription 接入文档：https://info.bilibili.co/pages/viewpage.action?pageId=102957975 \
     * 重试策略为 1, 10, 20, 60, 60, 180, 360, 600, 600, 3600, 7200, 7200, 代表回调未得到业务方成功响应后延迟重试时间（秒）
     *
     * @apiName bilibili-large-pay
     * @apiGroup callback
     *
     * @apiParam {String} msgId 回调消息 ID
     * @apiParam {String} msgContent 回调消息内容
     * @apiParam (msgContent) {Number} customerId 业务 ID
     * @apiParam (msgContent) {Number} [serviceType] 业务方业务类型
     * @apiParam (msgContent) {String} txId 支付平台支付 ID
     * @apiParam (msgContent) {String} orderId 业务方订单 ID
     * @apiParam (msgContent) {Number} deviceType 支付设备渠道类型 \
     * 1 pc, 2 h5, 3 app, 4 jsapi, 5 server, 6 小程序支付, 7 聚合二维码支付
     * @apiParam (msgContent) {String} payStatus 支付状态 \
     * SUCCESS（成功），CONFIRMED（已确认），FAIL（失败），PAY_CANCEL（超时未支付） \
     * 注：除了微信信用分用户确认后会通知 CONFIRMED，其它渠道暂时仅通知 SUCCESS。
     * @apiParam (msgContent) {String} payChannelId 支付渠道 ID, 用户实际选择的支付实体渠道。\
     * (payChannel 代表笼统的微信、支付宝等第三方渠道，payChannelId 代表实际签约的实体渠道 ID)
     * @apiParam (msgContent) {String} payChannel 支付渠道，alipay（支付宝）、open_alipay（支付宝 2.0）、ali_global（支付宝跨境）、wechat（微信) \
     * wx_global（微信跨境）、paypal（paypal）、iap（In App Purchase）、qpay（QQ支付）、huabei（花呗支付）、ali_bank（网银支付） \
     * bocom（交行信用卡支付）、bp（B币支付）、ott（云视听小电视支付）、ali_withhold（支付宝代扣）、ali_period_withhold（支付宝周期性代扣） \
     * wechat_score（微信信用分）、ali_huabei（花呗）、ali_score（支付宝预授权）、cmbPay（招行一网通支付）、wechat_partner（微信服务商）
     * @apiParam (msgContent) {String} payChannelName 支付渠道名称，如支付宝、微信、PayPal、IAP、QQ、花呗分期、网银支付、B币支付、花呗、招行一网通支付
     * @apiParam (msgContent) {String} [payAccount] 支付渠道账号
     * @apiParam (msgContent) {String} [payBank] 支付银行
     * @apiParam (msgContent) {String} [feeType=CNY] 货币类型，默认人民币 CNY
     * @apiParam (msgContent) {Number} payAmount 实际支付金额（如果是虚拟币，则乘以 100），单位为：分
     * @apiParam (msgContent) {String} payMsgContent 支付返回的额外信息，json 格式字符串，比如：\
     * payCounponAmount：使用B币券金额（单位 分），\
     * defaultBpAmount：支付非 iOS B币金额（单位 分），\
     * iosBpAmount：支付 iOS B币金额（分），\
     * payBpAmount：B币金额，包含 iOS 和非 iOS，不包含B币券（分），\
     * cashierAmount: 现金支付金额，单位：分，\
     * productId：商品 ID, \
     * uid：用户 ID（String）\
     * originalTransactionId：苹果自动续费的 originalTransactionId（String） \
     * thirdCouponAmount：第三方优惠金额（单位 分） \
     * iapTrialPeriod：false 是否是免费试用（Boolean）, true：表示是免费试用，false：表示不是免费试用 \
     * failReason：支付失败原因 \
     * iapDiscountFlag：此值根据 iap 的票据 promotional_offer_id 解析获取到的，若有值，则返回 true；否则返回 false
     * @apiParam (msgContent) {String} [extData] 支付请求时的扩展 json 串
     * @apiParam (msgContent) {Number} [expiredTime] IAP 代扣过期时间，毫秒值，业务方需要判断 expiredTime 的值，因为重复通知返回的 expiredTime 是一样的
     * @apiParam (msgContent) {String} [orderPayTime] 订单支付时间，格式：0000-00-00 00:00:00
     * @apiParam (msgContent) {Number} timestamp 请求时间戳，毫秒
     * @apiParam (msgContent) {String} traceId 追踪 id
     * @apiParam (msgContent) {String} signType 签名类型，默认 MD5
     * @apiParam (msgContent) {String} sign 签名（应当支持支付平台这边新增返回字段）
     *
     * @apiParamExample msgContent 示例：
     *     {
     *       "customerId": 1,
     *       "serviceType": 0,
     *       "txId": "***************",
     *       "orderId": "****************",
     *       "feeType": "CNY",
     *       "payStatus": "SUCCESS",
     *       "payChannel": "bp",
     *       "payChannelName": "B币",
     *       "payChannelId": 99,
     *       "payAmount": 9,
     *       "payMsgContent": "{\"payCounponAmount\":0,\"payBpAmount\":9,\"payBpAmount\":0,\"defaultBpAmount\":6,\"iosBpAmount\":3}",
     *       "payAccountId": "********",
     *       "deviceType": 2,
     *       "orderPayTime": "2018-09-07 17:39:37",
     *       "timestamp": "*************",
     *       "traceId": "3027145809363013632",
     *       "extData": "{}",
     *       "signType": "MD5",
     *       "sign": "5e32095c9a3f49a8001a90a5c162461b",
     *       "expiredTime": 0
     *     }
     */
    public function actionBiliLargePay()
    {
        $msg_content = trim(Yii::$app->request->get('msgContent'));
        $msg = new BiliLargePayCallbackMessage($msg_content);

        $transaction = null;
        $client = new BiliLargePayClient();
        try {
            if (!$client->isValidSign($msg)) {
                throw new BiliLargePayException('签名错误', BiliLargePayClient::CALLBACK_RESP_FAIL);
            }

            if (!$msg->isValidCustomerId() || !$msg->isValidServiceType()) {
                throw new BiliLargePayException('业务 ID 或业务类型错误', BiliLargePayClient::CALLBACK_RESP_FAIL);
            }

            if (!$msg->isValidPayChannelId()) {
                throw new BiliLargePayException('支付渠道错误', BiliLargePayClient::CALLBACK_RESP_FAIL);
            }

            if (!$msg->isValidFeeType()) {
                throw new BiliLargePayException('货币类型错误', BiliLargePayClient::CALLBACK_RESP_FAIL);
            }

            $order_id = RechargeOrder::getRealOrderId($msg->getOrderId());
            if (!$order = RechargeOrder::findOne(['id' => $order_id, 'type' => RechargeOrder::TYPE_BILI_LARGE_PAY])) {
                throw new BiliLargePayException('未找到匹配的订单', BiliLargePayClient::CALLBACK_RESP_REPUBLISH);
            }

            $pay_amount_in_yuan = Balance::profitUnitConversion($msg->getPayAmountInFen(), Balance::CONVERT_FEN_TO_YUAN);
            $diamond_num = Balance::profitUnitConversion($pay_amount_in_yuan, Balance::CONVERT_YUAN_TO_DIAMOND);

            switch ($msg->getPayStatus()) {
                case 'SUCCESS':
                    if (!$order->isProcessing()) {
                        if ($order->isSuccess()) {
                            return BiliLargePayClient::CALLBACK_RESP_SUCCESS;
                        }
                        throw new BiliLargePayException('订单状态错误', BiliLargePayClient::CALLBACK_RESP_FAIL);
                    }
                    $transaction = Yii::$app->paydb->beginTransaction();
                    $order->updateAttributes([
                        'tid' => $msg->getTransactionId(),
                        'price' => $pay_amount_in_yuan,
                        'num' => $diamond_num,
                        'status' => RechargeOrder::STATUS_SUCCESS,
                        'confirm_time' => $_SERVER['REQUEST_TIME'],
                        'modified_time' => $_SERVER['REQUEST_TIME'],
                    ]);
                    $balance = Balance::getByPk($order->uid);
                    $balance->updateCounters(['android' => $diamond_num, 'all_coin' => $diamond_num, 'all_topup' => $diamond_num]);

                    $time = date('Y-m-d H:i:s', $order->create_time);
                    Yii::$app->tools->sendNotification([
                        'user_id' => $order->uid,
                        'title' => '专属充值成功',
                        'content' => sprintf(
                            BiliLargePayClient::TOPUP_SUCCESS_NOTICE_MESSAGE_TPL,
                            $time,
                            $diamond_num,
                            Yii::$app->params['domainMissevan']
                        ),
                    ], Tools::SEND_SYS_MSG);

                    $user = Yii::$app->sso->getUser($order->uid);
                    if ($user['mobile']) {
                        if (!$user['region']) {
                            $user['region'] = '86';
                        }
                        Yii::$app->tools->sendNotification([
                            'to' => '+' . $user['region'] . $user['mobile'],
                            'region_code' => $user['region'],
                            'scene' => 'bili_large_pay_success',
                            'payload' => [
                                'time' => $time,
                                'amount' => $diamond_num,
                            ],
                        ], Tools::SEND_SMS);
                    } else {
                        Yii::error(sprintf('用户手机号为空：user_id[%d]', $user['id']), __METHOD__);
                    }
                    $transaction->commit();
                    break;
                case 'PAY_CANCEL':
                    $time = date('Y-m-d H:i:s', $order->create_time);
                    Yii::$app->tools->sendNotification([
                        'user_id' => $order->uid,
                        'title' => '专属充值失败',
                        'content' => sprintf(
                            BiliLargePayClient::TOPUP_FAIL_NOTICE_MESSAGE_TPL,
                            $time
                        ),
                    ], Tools::SEND_SYS_MSG);

                    $user = Yii::$app->sso->getUser($order->uid);
                    if ($user['mobile']) {
                        if (!$user['region']) {
                            $user['region'] = '86';
                        }
                        Yii::$app->tools->sendNotification([
                            'to' => '+' . $user['region'] . $user['mobile'],
                            'region_code' => $user['region'],
                            'scene' => 'bili_large_pay_failure',
                            'payload' => ['time' => $time],
                        ], Tools::SEND_SMS);
                    } else {
                        Yii::error(sprintf('用户手机号为空：user_id[%d]', $user['id']), __METHOD__);
                    }
                    break;
                default:
                    throw new BiliLargePayException('不支持的支付状态', BiliLargePayClient::CALLBACK_RESP_FAIL);
            }
        } catch (BiliLargePayException $e) {
            if ($transaction) {
                $transaction->rollBack();
            }
            Yii::error(sprintf('B站大额充值失败：error[%s], body[%s]', $e->getMessage(), $msg_content), __METHOD__);
            return $e->callback_resp_code;
        } catch (Exception $e) {
            if ($transaction) {
                $transaction->rollBack();
            }
            Yii::error(sprintf('B站大额充值失败：error[%s], body[%s]', $e->getMessage(), $msg_content), __METHOD__);
            return BiliLargePayClient::CALLBACK_RESP_REPUBLISH;
        }

        return BiliLargePayClient::CALLBACK_RESP_SUCCESS;
    }

    // 第三方到端后通知接口相关状态码
    const THIRD_PARTY_TASK_RESPONSE_STATUS_FAILED = -1;
    const THIRD_PARTY_TASK_RESPONSE_STATUS_SUCCESS = 1;

    /**
     * @api {post} /callback/wechatpay-sign 微信支付签约/解约结果回调接口
     * @apiDescription https://pay.weixin.qq.com/wiki/doc/api/wxpay_v2/papay/chapter3_6.shtml \
     * https://pay.weixin.qq.com/doc/global/v3/zh/4012354183
     *
     * @apiName wechatpay-sign
     * @apiGroup callback
     *
     * @apiParam RawBody {xml}
     * @apiParamExample RawBody {xml}
     *     <xml>
     *       <return_code><![CDATA[SUCCESS]]></return_code>
     *       <result_code><![CDATA[SUCCESS]]></result_code>
     *       <sign><![CDATA[C380BEC2BFD727A4B6845133519F3AD6]]></sign>
     *       <mch_id>10010404</mch_id>
     *       <contract_code>100001256</contract_code>
     *       <openid><![CDATA[onqOjjmM1tad-3ROpncN-yUfa6ua]]></openid>
     *       <plan_id><![CDATA[123]]></plan_id>
     *       <change_type><![CDATA[ADD]]></change_type>
     *       <operate_time><![CDATA[2015-07-01 10:00:00]]></operate_time>
     *       <contract_id><![CDATA[Wx15463511252015071056489715]]></contract_id>
     *     </xml>
     *
     * @apiSuccessExample {xml} Success-Response:
     *     <xml>
     *       <return_code><![CDATA[SUCCESS]]></return_code>
     *       <return_msg><![CDATA[OK]]></return_msg>
     *     </xml>
     *
     * @apiErrorExample {xml} Error-Response:
     *     <xml>
     *       <return_code><![CDATA[FAIL]]></return_code>
     *       <return_msg><![CDATA[failed]]></return_msg>
     *     </xml>
     */
    public function actionWechatpaySign()
    {
        $body = Yii::$app->request->getRawBody();
        $xml_arr = (array)simplexml_load_string($body, 'SimpleXMLElement', LIBXML_NOCDATA);
        $sign = $xml_arr['sign'] ?? null;
        $result_code = $xml_arr['result_code'] ?? null;
        $out_agreement_no = $xml_arr['contract_code'] ?? null;  // 商户自定义签约协议号
        $change_type = $xml_arr['change_type'] ?? null;  // 变更类型
        $openid = $xml_arr['openid'] ?? null;
        $plan_id = $xml_arr['plan_id'] ?? null;  // 协议模板 ID
        $contract_id = $xml_arr['contract_id'] ?? null;  // 微信委托代扣协议 ID
        $contract_termination_mode = $xml_arr['contract_termination_mode'] ?? null;  // 协议解约方式
        if (!$sign || !$result_code || !$out_agreement_no || !$change_type || !$openid || !$plan_id || !$contract_id
                || !in_array($change_type, [AuthWechat::AGREEMENT_CHANGE_TYPE_ADD, AuthWechat::AGREEMENT_CHANGE_TYPE_DELETE])
                || ($change_type === AuthWechat::AGREEMENT_CHANGE_TYPE_DELETE && !$contract_termination_mode)) {
            Yii::error(sprintf('微信签约/解约回调报文异常: %s', Json::encode($xml_arr)), __METHOD__);
            throw new HttpException(403, '非法请求');
        }

        $sign = $xml_arr['sign'];
        unset($xml_arr['sign']);
        $csign = AuthWechat::getWechatSign($xml_arr);
        if ($sign !== $csign || $xml_arr['result_code'] !== 'SUCCESS') {
            Yii::error(sprintf('微信签约/解约回调报文异常: %s', Json::encode($xml_arr)), __METHOD__);
            return AuthWechat::getWechatPayFailCallbackResponse();
        }

        if ($change_type === AuthWechat::AGREEMENT_CHANGE_TYPE_ADD) {
            $operation_type = VipSubscriptionSignAgreement::OPERATION_TYPE_ADD;
        } else {
            $operation_type = VipSubscriptionSignAgreement::OPERATION_TYPE_DELETE;
        }
        $res = VipSubscriptionSignAgreement::updateAgreement($operation_type,
            VipSubscriptionSignAgreement::PAY_TYPE_WECHAT, $out_agreement_no, $contract_id,
            function (VipSubscriptionSignAgreement $agreement, VipFeeDeductedRecord $deducted_record, MVip $vip) use ($plan_id, $openid, $contract_termination_mode, $operation_type) {
                // 微信回调的协议模板 ID 为 string 类型，需要转 int 后使用
                $plan_id = (int)$plan_id;
                $active_plan_id = $vip->getWechatPlanId();
                if ($plan_id !== $active_plan_id) {
                    throw new Exception(sprintf('微信回调的协议模板 ID 与协议中的协议模板 ID 不一致：%d, %d', $active_plan_id, $plan_id));
                }
                if ($operation_type === VipSubscriptionSignAgreement::OPERATION_TYPE_ADD) {
                    // 签约时记录微信 openid
                    $agreement->more = array_merge($agreement->more ?: [], [
                        'wechat_openid' => $openid,
                    ]);
                }
                if ($contract_termination_mode) {
                    $agreement->more = array_merge($agreement->more ?: [], [
                        'wechat_contract_termination_mode' => (int)$contract_termination_mode,
                    ]);
                }
            });
        if ($res) {
            return AuthWechat::getWechatPaySuccessCallbackResponse();
        } else {
            return AuthWechat::getWechatPayFailCallbackResponse();
        }
    }

    /*
     * @api {post} /callback/thirdparty-task-notify 第三方到端后通知接口
     *
     * @apiName thirdparty-task-notify
     * @apiGroup callback
     *
     * @apiParam {String} request_id 请求唯一标识，生成规则请求方可自定
     * @apiParam {String} maoer_task_token 猫耳侧生成的用户任务 token
     * @apiParam {String} scene 场景（由猫耳侧分配）
     * @apiParam {Number} ts 用户完成任务时间（时间戳，单位：秒）
     * @apiParam {String} sign 签名 \
     * sign 签名生成方式：
     * 1. 按照参数名（不包含 sign 参数）升序排序拼接作为待签名的字符串，sign_key（由猫耳侧分配）拼在最后，
     * 参数名和参数值都需要 urlencode 处理（按 RFC 3986 编码），空格要编码成 %20 \
     * 例：sign_key = test，待签名的字符串 \
     *    参数名、参数值中无空格时：maoer_task_token=test_token&request_id=abc&scene=1&ts=1735660800test \
     *    参数名、参数值中有空格时：maoer_task_token=test_token&request_id=a%20bc&scene=1&ts=1735660800test
     * 2. 待签名的字符串使用 MD5 进行加密（hex 小写） \
     * 例：以上举例的请求参数生成的签名 \
     *    参数名、参数值中无空格时：sign = d745aa8159becad3c546b09a17c1d789 \
     *    参数名、参数值中有空格时：sign = 3e843efeaa9a13d4f9c8450ff5053038
     *
     * @apiParamExample {application/x-www-form-urlencoded} Request-Example:
     *     request_id=abc&maoer_task_token=test_token&scene=1&ts=1735660800&sign=d745aa8159becad3c546b09a17c1d789
     *
     * @apiSuccess {Number} code 返回码，为 0 时表示成功
     * @apiSuccess {String} message 成功返回空字符串，若是失败则返回错误信息
     * @apiSuccess {Object} data 具体数据响应
     *
     * @apiSuccessExample {json} Success-Response: 响应成功
     *     HTTP/1.1 200 OK
     *     {
     *       "code": 0,
     *       "message": "",
     *       "data": {
     *         "status": 1  // 0：奖励发放失败；1：奖励发放成功
     *       }
     *     }
     *
     * @apiErrorExample {json} Error-Response: 参数错误
     *     HTTP/1.1 400 Bad Request
     *     {
     *       "code": 201010001,
     *       "message": "参数错误",
     *       "data": null
     *     }
     *
     * @apiErrorExample {json} Error-Response: 验签失败
     *     HTTP/1.1 403 Forbidden
     *     {
     *       "code": 100010002,
     *       "message": "验签失败",
     *       "data": null
     *     }
     *
     * @apiErrorExample {json} Error-Response: 服务错误
     *     HTTP/1.1 500 Internal Server Error
     *     {
     *       "code": 100010023,
     *       "message": "服务器内部错误",
     *       "data": null
     *     }
     */
    public function actionThirdpartyTaskNotify()
    {
        $params = Yii::$app->request->post();

        try {
            if (!isset($params['request_id'], $params['maoer_task_token'], $params['scene'], $params['ts'], $params['sign'])) {
                throw new HttpException(400, '参数错误', 201010001);
            }
            if (!in_array($params['scene'], ThirdPartyTaskUtil::SCENE_ARR)) {
                throw new HttpException(403, '不支持的第三方', 201010001);
            }
            if (!ThirdPartyTaskUtil::isValidSign($params)) {
                throw new HttpException(403, '验签失败', 100010002);
            }
            $data = [
                'status' => self::THIRD_PARTY_TASK_RESPONSE_STATUS_FAILED,
            ];
            if (ThirdPartyTaskUtil::finishTask($params['maoer_task_token'], $params['scene']) > 0) {
                $data['status'] = self::THIRD_PARTY_TASK_RESPONSE_STATUS_SUCCESS;
            }
            return $this->renderSuccessResponse($data);
        } catch (Exception $e) {
            return $this->renderErrorResponse($e);
        }
    }

    /**
     * 成功返回
     *
     * @param array $data 具体数据响应
     * @param int $code 返回码
     * @param string $message 响应信息
     * @return string
     */
    private function renderSuccessResponse(array $data, int $code = self::CODE_SUCCESS, string $message = ''): string
    {
        return Json::encode(['code' => $code, 'message' => $message, 'data' => $data]);
    }

    /**
     * 异常返回
     *
     * @param Exception $e 异常
     * @return string
     */
    private function renderErrorResponse(Exception $e): string
    {
        Yii::$app->response->setStatusCodeByException($e);
        return Json::encode(['code' => $e->getCode(), 'message' => $e->getMessage(), 'data' => null]);
    }

    /**
     * @api {post} /callback/oa-flow
     *
     * @apiName oa-flow
     * @apiGroup callback
     * @apiDescription 接入文档：https://info.missevan.com/pages/viewpage.action?pageId=132093540
     *
     * @apiParam RawBody {json} https://info.missevan.com/pages/viewpage.action?pageId=132093540
     *
     * @apiSuccess {Number} code 返回码，为 0 时表示成功，非 0 时流程中台会进行重试（最多 3 次，第 1 次立即、第 2 次 1 分钟后、第 3 次 3 分钟后）
     * @apiSuccess {String} message 若是失败则返回错误信息
     *
     * @apiSuccessExample {json} Success-Response: 响应成功
     *     HTTP/1.1 200 OK
     *     {
     *       "code": 0,
     *       "message": "成功"
     *     }
     */
    public function actionOaFlow()
    {
        $request = Yii::$app->request;
        $config = Yii::$app->params['service']['oa_flow_coin_topup'];
        $oaflow = new Client($config['app_id'], $config['app_secret']);
        if (!$oaflow->verifyEventSignature(Yii::$app->request)) {
            throw new HttpException(403, '签名错误');
        }
        if (!$body = Json::decode($request->getRawBody())) {
            throw new HttpException(400, '参数为空');
        }

        $event = new EventBody($body);
        if ($event->isTestNotify()) {
            return $event->response(200, '成功');
        }

        if (!$application = PayManualTopupApplication::findByOrderId($event->getOrderId())) {
            // TODO: 不在后台发起充钻申请，直接在 OA 发起充值充钻申请
            return $event->response(500, '暂不支持从 OA 后台发起的工单');
        }
        if (in_array($application->checked, [PayManualTopupApplication::CHECKED_APPROVED, PayManualTopupApplication::CHECKED_REJECTED])) {
            return $event->response(200, '工单已完结');
        }
        // 审批完结或下一节点为申请人确认节点
        $is_ready_to_process = $event->isSuccessfullyFinished()
            || ($event->isApprovedEvent() && $event->getNextTasks()[0]['taskName'] === 'creator_confirm_node');
        if (!$is_ready_to_process && !$event->isTerminated()) {
            // PASS: 其它事件暂不处理
            return $event->response(200, '未触发待处理的事件');
        }

        ['cid' => $cid, 'coin_type' => $coin_type, 'order_type' => $order_type] = $application->audit_info;
        ['ip' => $ip, 'user_agent' => $user_agent] = $application->more;
        $user_context = new UserContext($user_agent, $ip);
        $more = [];
        if (array_key_exists('event_id', $application->audit_info)) {
            $more['event_id'] = $application->audit_info['event_id'];
        }
        if (array_key_exists('biz_type', $application->audit_info)) {
            $more['biz_type'] = $application->audit_info['biz_type'];
        }
        if (array_key_exists('play_type', $application->audit_info)) {
            $more['play_type'] = $application->audit_info['play_type'];
        }

        $transaction = null;
        try {
            $application->checked = $is_ready_to_process
                ? PayManualTopupApplication::CHECKED_APPROVED
                : PayManualTopupApplication::CHECKED_TERMINATED;

            $biz_action = function () use ($application, $cid, $order_type, $coin_type) {
                if (!$application->save()) {
                    throw new HttpException(500, MUtils2::getFirstError($application));
                }
                MAdminLogger::addOne([
                    'intro' => sprintf(
                        '共充入 %d 钻石，充值类型 ID：%d，订单类型：%d，充值货币类型：%s',
                        $application->coin_num * count($application->user_ids), $cid, $order_type, $coin_type),
                    'catalog' => MAdminLogger::CATALOG_TOPUP,
                    'channel_id' => $application->id,
                ]);
            };

            if ($event->isApprovedEvent()) {
                RechargeOrder::batchGenerateCashOrder($cid, $application->user_ids, $application->coin_num,
                    $user_context, $order_type, $coin_type, $more,
                    PayManualTopupApplication::getDb()->beginTransaction(), $biz_action);
            } else {
                $transaction = PayManualTopupApplication::getDb()->beginTransaction();
                $biz_action();
                $transaction->commit();
            }
            return $event->response(200, '成功');
        } catch (Exception $e) {
            if ($transaction) {
                $transaction->rollBack();
            }
            $status_code = $e instanceof HttpException ? $e->statusCode : 500;
            return $event->response($status_code, $e->getMessage());
        }
    }

}
