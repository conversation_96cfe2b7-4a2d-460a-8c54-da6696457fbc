<?php

namespace app\controllers;

use app\components\util\Equipment;
use app\components\util\MUtils;
use app\middlewares\Controller;
use app\models\Balance;
use app\models\Goods;
use app\models\GoodsLike;
use app\models\GoodsSpecification;
use app\models\PaginationParams;
use missevan\storage\StorageClient;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\db\Expression;
use app\components\base\filter\AccessControl;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use yii\web\HttpException;

class MallController extends Controller
{
    // 商品详情页购买状态
    private const BUY_BTN_STATUS_OFFTHESHELVE = 0;
    private const BUY_BTN_STATUS_GOTOBUY = 1;
    private const BUY_BTN_STATUS_GOTOBOOK = 2;
    private const BUY_BTN_STATUS_NOTOPEN = 3;
    private const BUY_BTN_STATUS_EXPIRED = 4;

    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'like-goods' => ['post'],
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'only' => [
                'like-goods',
            ],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => [
                        'like-goods',
                    ],
                    'roles' => ['@']
                ],
            ]
        ];
        return $behaviors;
    }

    /**
     * @api {get} /mall/banners 版头图
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/mall/banners
     * @apiSampleRequest /mall/banners
     * @apiDescription 若返回为空数组，则客户端将不显示轮播图控件
     *
     * @apiVersion 0.1.0
     * @apiName banners
     * @apiGroup mall
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [{
     *         "url": "http://bing.com",  // 跳转链接地址
     *         "pic": "http://static.missevan.com/shop/images/201907/08/3d46dc9cdbbdc8e6bdf0d5c832.png",
     *       },
     *       {
     *         "url": "missevan://voice/1",  // missevan 协议地址
     *         "pic": "http://static.missevan.com/shop/images/201907/08/246d444dccc74e6b230a33.jpg",
     *       },
     *       {
     *         "url": "",  // 为空字符串时轮播图只作预告作用，不可点击
     *         "pic": "http://static.missevan.com/shop/images/201907/09/59f3097a4891ea6165019.jpg",
     *       },
     *       {
     *         "url": "taobao://item.taobao.com/item.htm?id=567778897448",  // 唤醒淘宝的协议地址（若客户端未安装淘宝应用，则以浏览器网页方式打开）
     *         "pic": "http://static.missevan.com/shop/images/201907/09/59f3097a4891ea6165019.jpg",
     *       }]
     *     }
     */
    public function actionBanners()
    {
        $banners = [];
        $redis = Yii::$app->redis;
        $shop_banner_cache = $redis->get(KEY_APP_MAOER_SHOP_HOMEPAGE_BANNER);
        $data = $shop_banner_cache ? Json::decode($shop_banner_cache) : [];
        if ($data) {
            $equipment = Yii::$app->equip;
            switch ($equipment->getOs()) {
                case Equipment::iOS:
                    $banners = $data['ios'] ?? [];
                    break;
                default:
                    $banners = $data['android'] ?? [];
                    break;
            }
            $banners = array_map(function ($item) {
                $item['pic'] = StorageClient::getFileUrl($item['pic']);
                unset($item['type'], $item['sort']);
                return $item;
            }, $banners);
        }

        return $banners;
    }

    /**
     * @api {get} /mall/goods-list 商品列表
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/mall/goods-list
     * @apiSampleRequest /mall/goods-list
     *
     * @apiVersion 0.1.0
     * @apiName goods-list
     * @apiGroup mall
     *
     * @apiParam {Number} [page=1] 所在页
     * @apiParam {Number} [page_size=20] 每页个数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [{
     *           "id": 2,
     *           "title": "书签",
     *           "intro": "正版书签",
     *           "cover": "http://static.missevan.com/shop/images/201907/08/49af6c513862949c124836.png",
     *           "price": "9.9",
     *           "like_num": 0,
     *           "attr": 0,  // 0 预售 1 现货 2 余量预售
     *           "more": {
     *             "expired_time": 1567321200,  // 预售结束时间
     *             "delivery_time": 1562731300,  // 预计发货时间
     *             "opening_time": 1562731397  // 开售时间
     *           },
     *           "is_new": true,  // 是否为新品
     *           "url": "https://m.missevan.com/mall/detail/2"
     *         },
     *         {
     *           "id": 1,
     *           "title": "清蒸鱼",
     *           "intro": "正宗清蒸鱼",
     *           "cover": "http://static.missevan.com/shop/images/201907/08/9ae2cef75c67a5a90a124828.jpg",
     *           "price": "99.9",
     *           "like_num": 0,
     *           "attr": 1,
     *           "more": {
     *             "expired_time": 1564588800,
     *             "delivery_time": 0,
     *             "opening_time": 1562730931
     *           },
     *           "is_new": true,
     *           "url": "https://m.missevan.com/mall/detail/1"
     *         }],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 1,
     *           "count": 2,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     */
    public function actionGoodsList(int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        $page_obj = PaginationParams::process($page, $page_size);
        $query = Goods::find()->select('id, title, intro, cover, like_num, attr, more')
            ->where(['status' => Goods::STATUS_SALE])
            ->orderBy('sort DESC');
        $result = MUtils::getPaginationModels($query, $page_obj->page_size);

        $goods_ids = array_column($result->Datas, 'id');
        $specs = GoodsSpecification::find()->select('MIN(price) AS price')
            ->where([
                'goods_id' => $goods_ids,
                'status' => [GoodsSpecification::STATUS_SALE, GoodsSpecification::STATUS_OFF_THE_SHELVE]
            ])->groupBy('goods_id')->indexBy('goods_id')->column();

        $result->Datas = array_map(function ($item) use ($specs) {
            $more = $item['more'];
            unset($more['tags']);
            return [
                'id' => $item['id'],
                'title' => $item['title'],
                'intro' => $item['intro'],
                'cover' => $item['cover'],
                // 给出字符串类型客户端直接显示
                // 若为浮点型由于客户端使用的控件差异，可能会出现 98.90000000001 的问题
                'price' => (string)Balance::profitUnitConversion(($specs[$item['id']] ?? 100), Balance::CONVERT_FEN_TO_YUAN),
                'like_num' => $item['like_num'],
                'attr' => $item['attr'],
                'more' => $more,
                'is_new' => $item->is_new,
                // 手机网页商品详情地址（客户端从原生的商品列表点击后跳转地址）
                'url' => MUtils::getMobileWebUrl(sprintf('/mall/detail/%d', $item['id'])),
            ];
        }, $result->Datas);

        return $result;
    }

    /**
     * @api {get} /mall/goods-detail{?goods_id} 商品详情
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/mall/goods-detail
     * @apiSampleRequest /mall/goods-detail
     *
     * @apiVersion 0.1.0
     * @apiName goods-detail
     * @apiGroup mall
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 7,
     *         "title": "红烧鱼",
     *         "liked": 0,  // 0 未喜欢，1 已喜欢
     *         "cover": "http://static.missevan.com/shop/images/201907/08/a4e05017223b59c2124834.jpg",
     *         "url": "https://m.missevan.com/mall/detail/2",  // 客户端去请求的网页地址
     *         "buy_btn_status": 1,  // 0 已下架，1 去购买，2 去预订，3 预售未开始，4 预售已结束（若为其它值则不显示购买按钮）
     *         "specifications": [{
     *           "id": 1,
     *           "title": "白色",
     *           "cover": "http://static.missevan.com/shop/images/201907/08/a4e05017223b59c2124834.jpg",
     *           "price": "99.90",
     *           "url": "597375406695",
     *           "status": 1,  // -1 下架、1 在售
     *         }]
     *       }
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 404 Not Found
     *     {
     *       "success": false,
     *       "code": 100010007,
     *       // 目前 iOS 不支持 HTTP Code 非 200 情况下 info 为 map 的形式，故先给出 string 类型跳转地址
     *       "info": "https://m.missevan.com/mall/detail/100"
     *     }
     */
    public function actionGoodsDetail(int $goods_id)
    {
        $goods = Goods::find()->select('id, title, cover, attr, more, status')
            ->where(['id' => $goods_id, 'status' => [Goods::STATUS_OFF_THE_SHELVE, Goods::STATUS_SALE]])
            ->one();
        if (!$goods) {
            // 商品不存在时，客户端跳转到该地址（该地址的页面会显示商品不存在的样式）
            throw new HttpException(404, MUtils::getMobileWebUrl(sprintf('/mall/detail/%d', $goods_id)));
        }

        $specs = GoodsSpecification::find()->select('id, title, cover, price, url, status')->where([
            'goods_id' => $goods_id,
            'status' => [GoodsSpecification::STATUS_SALE, GoodsSpecification::STATUS_OFF_THE_SHELVE],
        ])->orderBy('sort DESC')->all();
        $liked = 0;
        if ($user_id = Yii::$app->user->id) {
            $liked = GoodsLike::find()
                ->where(['user_id' => $user_id, 'goods_id' => $goods_id, 'status' => GoodsLike::STATUS_LIKE])
                ->limit(1)->exists() ? 1 : 0;
        }

        if (Goods::STATUS_SALE === $goods->status) {
            $buy_btn_status = self::BUY_BTN_STATUS_GOTOBUY;

            if (Goods::ATTR_BOOKING === $goods->attr) {
                $buy_btn_status = self::BUY_BTN_STATUS_GOTOBOOK;
                if ($goods->more['opening_time'] > $_SERVER['REQUEST_TIME']) {
                    $buy_btn_status = self::BUY_BTN_STATUS_NOTOPEN;
                }
                if ($goods->more['expired_time'] < $_SERVER['REQUEST_TIME']) {
                    $buy_btn_status = self::BUY_BTN_STATUS_EXPIRED;
                }
            }
        } else {
            $buy_btn_status = self::BUY_BTN_STATUS_OFFTHESHELVE;
        }

        return [
            'id' => $goods->id,
            'title' => $goods->title,
            'liked' => $liked,
            'cover' => $goods->cover,
            // 用于当客户端从 missevan://mall/detail/{goods_id} 进来时，去请求的网页地址
            // 避免客户端本地拼接写死 https://m.misseva.com 和 /mall/detail/{goods_id} 来组成 URL
            'url' => MUtils::getMobileWebUrl(sprintf('/mall/detail/%d', $goods_id)),
            'buy_btn_status' => $buy_btn_status,
            'specifications' => $specs,
        ];
    }

    /**
     * @api {post} /mall/like-goods 喜欢或取消喜欢商品
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/mall/like-goods
     * @apiSampleRequest /mall/like-goods
     * @apiDescription 该接口未被使用（客户端通过访问手机网页来喜欢或取消喜欢商品）
     *
     * @apiVersion 0.1.0
     * @apiName like-goods
     * @apiGroup mall
     *
     * @apiParam {Number} goods_id 商品 ID
     * @apiParam {Number} like 点击类型（0 取消喜欢，1 点击喜欢）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "status": true,
     *         "msg": "成功添加到喜欢"
     *       }
     *     }
     *
     * @apiError (400) {Number} code 501010000
     * @apiError (400) {String} info 操作过于频繁，请稍候再试
     * @apiError (404) {Number} code 501010000
     * @apiError (404) {String} info 不存在该商品
     */
    public function actionLikeGoods()
    {
        $goods_id = (int)Yii::$app->request->post('goods_id');
        $like = (int)Yii::$app->request->post('like');
        $user_id = Yii::$app->user->id;

        if (!Goods::find()->where(['id' => $goods_id, 'status' => Goods::STATUS_SALE])->limit(1)->exists()) {
            throw new HttpException(404, '不存在该商品');
        }
        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_MAOER_SHOP_USER_LIKE_GOODS, $user_id, $goods_id);
        if (!$redis->lock($lock, ONE_MINUTE)) {
            throw new HttpException(400, '操作过于频繁，请稍候再试');
        }

        $resp = ['status' => true, 'msg' => $like ? '成功添加到喜欢' : '已取消喜欢'];
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $model = GoodsLike::find()->where(['user_id' => $user_id, 'goods_id' => $goods_id])->one();
            if ($like) {
                // 点击喜欢
                if ($model) {
                    if (GoodsLike::STATUS_LIKE === $model->status) {
                        return $resp;
                    }
                } else {
                    $model = new GoodsLike([
                        'goods_id' => $goods_id,
                        'user_id' => $user_id,
                    ]);
                }
                $model->status = GoodsLike::STATUS_LIKE;
                if (!$model->save()) {
                    throw new \Exception(MUtils::getFirstError($model));
                }
                Goods::updateAllCounters(['like_num' => 1], ['id' => $goods_id]);
            } else {
                // 取消喜欢
                if (!$model || GoodsLike::STATUS_CANCEL_LIKE === $model->status) {
                    return $resp;
                }
                if ($model->create_time === $model->modified_time
                        && $_SERVER['REQUEST_TIME'] - $model->modified_time < HALF_MINUTE) {
                    // 如果点击喜欢后 30 秒内取消，则不保留该记录
                    if (!$model->delete()) {
                        throw new \Exception(MUtils::getFirstError($model));
                    }
                } else {
                    $model->status = GoodsLike::STATUS_CANCEL_LIKE;
                    if (!$model->save()) {
                        throw new \Exception(MUtils::getFirstError($model));
                    }
                }
                Goods::updateAll(['like_num' => new Expression('GREATEST(like_num, 1) - 1')],
                    'id = :goods_id', [':goods_id' => $goods_id]);
            }
            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            // 忽略唯一索引抛出的异常
            if (!($e instanceof yii\db\Exception && MUtils2::isUniqueError($e, GoodsLike::getDb()))) {
                return [
                    'status' => false,
                    'msg' => $e->getMessage(),
                ];
            }
        } finally {
            $redis->unlock($lock);
        }

        return $resp;
    }

}
