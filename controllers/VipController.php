<?php

namespace app\controllers;

use app\components\base\filter\AccessControl;
use app\components\util\SuccessResponseWithMessage;
use app\forms\UserContext;
use app\forms\VipIosSubscriptionForm;
use app\middlewares\ControllerV2;
use app\models\AdTrack;
use app\models\Balance;
use app\models\TopupMenu;
use app\models\Mowangskuser;
use app\models\MUserVip;
use app\models\MVip;
use app\models\VipFeeDeductedRecord as DeductedRecord;
use app\models\VipReceiveCoinLog;
use app\models\VipSubscriptionSignAgreement as SignAgreement;
use Exception;
use missevan\util\MUtils;
use Yii;
use yii\filters\VerbFilter;
use yii\web\HttpException;

class VipController extends ControllerV2
{

    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'create-alipay-order' => ['post'],
                'create-wechat-order' => ['post'],
                'order-detail' => ['get'],
                'claim-diamonds' => ['post'],
                'create-ios-order' => ['post'],
                'verify-ios-order' => ['post'],
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'only' => [
                'create-alipay-order',
                'create-wechat-order',
                'order-detail',
                'claim-diamonds',
                'create-ios-order',
                'verify-ios-order',
            ],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => [
                        'create-alipay-order',
                        'create-wechat-order',
                        'order-detail',
                        'claim-diamonds',
                        'create-ios-order',
                        'verify-ios-order',
                    ],
                    'roles' => ['@']
                ],
            ]
        ];
        return $behaviors;
    }

    /**
     * @api {post} /vip/create-alipay-order 支付宝开通会员，支持单次付费及支付并签约
     * @apiDescription https://opendocs.alipay.com/open/repo-0243dw
     * @apiName create-alipay-order
     * @apiGroup vip
     *
     * @apiPermission user
     *
     * @apiParam {Number} vip_id 会员价目 ID
     *
     * @apiSuccess {Number} code
     * @apiSuccess {String} message
     * @apiSuccess {Object} data
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "message": "",
     *       "data": {
     *         "trade_no": "vip_deduct_17200000000010123456789",
     *         "alipay_body": "alipay_sdk=alipay-sdk-php-20161101&app_id=9021000126659465&biz_content=%7B...PqTQ2DdUn8%2BBDw%3D%3D"
     *       }
     *     }
     */
    public function actionCreateAlipayOrder()
    {
        throw new HttpException(400, '服务器繁忙');
        $vip_id = (int)Yii::$app->request->post('vip_id');
        return DeductedRecord::generateAlipayOrder($vip_id, Yii::$app->user->id, UserContext::fromUser(Yii::$app->request));
    }

    /**
     * @api {get} /vip/order-detail 获取开通会员详情
     * @apiGroup vip
     *
     * @apiParam {String} trade_no 交易编号
     *
     * @apiSuccess {Number} code
     * @apiSuccess {String} message
     * @apiSuccess {Object} data
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "message": "",
     *       "data": {
     *         "sign_agreement": { // 仅连续订阅（非单次付费）时返回
     *           "id": 111,
     *           "create_time": 1720000000,
     *           "modified_time": 1720000000,
     *           "user_id": 1,
     *           "pay_type": 3, // 付费方式：1 iOS、2 微信、3 支付宝、4 Google Play
     *           "vip_id": 1, // 会员价目 ID
     *           "status": 1, // 协议签约状态：1 待签约，2 签约生效中，3 签约到期
     *           "start_time": 1720000000, // 协议生效时间（秒级时间戳）
     *           "expire_time": 0 // 协议失效时间（秒级时间戳），含此时刻，待签约和签约生效中时返回 0
     *         },
     *         "deducted_record": {
     *           "id": 222,
     *           "create_time": 1720000000,
     *           "modified_time": 1720000000,
     *           "user_id": 1,
     *           "vip_id": 1, // 会员价目 ID
     *           "sign_agreement_id": 111, // 签约协议 ID（0 为单次付费）
     *           "pay_type": 3, // 付费方式：1 iOS、2 微信、3 支付宝、4 Google Play
     *           "price": 100, // 本次扣款金额（单位：分）
     *           "status": 2, // 本次扣款状态：1 待扣款，2 扣款成功，3 扣款失败，4 重复支付
     *           "next_deduct_time": 1722678400 // 下次扣款时间（秒级时间戳，0 为单次付费）
     *         }
     *       }
     *     }
     */
    public function actionOrderDetail(string $trade_no)
    {
        $user_id = Yii::$app->user->id;
        $trade_no_info = DeductedRecord::parseTradeNo($trade_no);
        $order_id = $trade_no_info['id'] ?? null;
        $pay_type = $trade_no_info['pay_type'] ?? null;
        if (!$trade_no_info || !$order_id || !$pay_type) {
            throw new HttpException(400, '参数错误');
        }
        $deducted_record = DeductedRecord::findOne([
            'id' => $order_id,
            'user_id' => $user_id,
            'pay_type' => $pay_type,
        ]);
        if (!$deducted_record) {
            throw new HttpException(404, '扣费记录不存在');
        }
        $sign_agreement_id = $deducted_record->sign_agreement_id;
        $res = [
            'deducted_record' => [
                'id' => $deducted_record->id,
                'create_time' => $deducted_record->create_time,
                'modified_time' => $deducted_record->modified_time,
                'user_id' => $deducted_record->user_id,
                'vip_id' => $deducted_record->vip_id,
                'sign_agreement_id' => $sign_agreement_id,
                'pay_type' => $deducted_record->pay_type,
                'price' => $deducted_record->price,
                'status' => $deducted_record->status,
                'next_deduct_time' => $deducted_record->next_deduct_time,
            ],
        ];
        if ($sign_agreement_id) {
            $sign_agreement = SignAgreement::findOne([
                'id' => $sign_agreement_id,
                'user_id' => $user_id,
                'pay_type' => $pay_type,
            ]);
            if (!$sign_agreement) {
                throw new HttpException(500, '签约协议不存在');
            }
            $res['sign_agreement'] = [
                'id' => $sign_agreement->id,
                'create_time' => $sign_agreement->create_time,
                'modified_time' => $sign_agreement->modified_time,
                'user_id' => $sign_agreement->user_id,
                'vip_id' => $sign_agreement->vip_id,
                'pay_type' => $sign_agreement->pay_type,
                'status' => $sign_agreement->status,
                'start_time' => $sign_agreement->start_time,
                'expire_time' => $sign_agreement->expire_time,
            ];
        }
        return $res;
    }

    /**
     * @api {post} /vip/create-wechat-order 微信开通会员，支持单次付费及支付并签约
     * @apiDescription https://pay.weixin.qq.com/wiki/doc/api/wxpay_v2/papay/chapter3_5.shtml
     * @apiName create-wechat-order
     * @apiGroup vip
     *
     * @apiPermission user
     *
     * @apiParam {Number} vip_id 会员价目 ID
     *
     * @apiSuccess {Number} code
     * @apiSuccess {String} message
     * @apiSuccess {Object} data
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "message": "",
     *       "data": {
     *         "trade_no": "vip_deduct_17200000000010123456789",  // 订单编号
     *         "wechatpay_body": {
     *           "appid": "wx143bd0e4a3b523cf",  // 应用 ID
     *           "partnerid": "1482967912",  // 商户号
     *           "prepayid": "wx291408069667805baec1c0fa3046573997",  // 预支付交易会话 ID
     *           "package": "Sign=WXPay",  // 扩展字段（固定值 Sign=WXPay）
     *           "noncestr": "umIsOxffZ100HpbrE2POmc4MArVZIS",  // 随机字符串
     *           "timestamp": 1540793286,  // 时间戳（单位：秒）
     *           "sign": "F57D2F4247DB619631AB1FDF97506CB3"  // 签名
     *         }
     *       }
     *     }
     */
    public function actionCreateWechatOrder()
    {
        throw new HttpException(400, '服务器繁忙');
        $vip_id = (int)Yii::$app->request->post('vip_id');
        return DeductedRecord::generateWechatOrder($vip_id, Yii::$app->user->id,
            UserContext::fromUser(Yii::$app->request));
    }

    /**
     * @api {post} /vip/claim-diamonds 领取会员福利钻石
     *
     * @apiVersion 0.1.0
     * @apiName claim-diamonds
     * @apiGroup vip
     *
     * @apiPermission user
     *
     * @apiSuccess {Number} code
     * @apiSuccess {String} message
     * @apiSuccess {Object} data
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "message": "领取成功",
     *       "data": null
     *     }
     *
     * @apiErrorExample Error-Response:
     *     {
     *       "code": 100010007,
     *       "message": "开通/续费会员享受专属权益",
     *       "data": null
     *     }
     */
    public function actionClaimDiamonds()
    {
        $user_id = Yii::$app->user->id;
        $vip_id = MUserVip::getVipID($user_id);
        if (!$vip_id) {
            throw new HttpException(403, '开通/续费会员享受专属权益');
        }
        $received = VipReceiveCoinLog::isReceivedToday($user_id);
        if ($received) {
            throw new HttpException(400, '今天已经领过了');
        }
        $list = TopupMenu::getCoinPrice(TopupMenu::DEVICE_VIP, TopupMenu::SCOPE_VIP);
        if (empty($list) || $list[0]['num'] === 0) {
            throw new HttpException(403, '当前无此权益');
        }
        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_CLAIM_VIP_DIAMONDS, $user_id, date('Ymd'));
        if (!$redis->lock($lock, ONE_DAY)) {
            throw new HttpException(400, '今天已经领过了');
        }
        try {
            $equip_limit = Yii::$app->params['vip']['claim_diamonds_limit']['equip'] ?? 0;
            $ip_limit = Yii::$app->params['vip']['claim_diamonds_limit']['ip'] ?? 0;
            $equip_key = $ip_key = '';
            if ($equip_limit) {
                $equip_id = Yii::$app->equip->getEquipId();
                $equip_key = $redis->generateKey(COUNTER_CLAIM_VIP_DIAMONDS_EQUIP, $equip_id, date('Ymd'));
                $equip_claim_nums = $redis->incr($equip_key);
                if ($equip_claim_nums > $equip_limit) {
                    throw new HttpException(400, '已达到每日领取上限');
                }
            }
            if ($ip_limit) {
                $ip = Yii::$app->request->userIP;
                $ip_key = $redis->generateKey(COUNTER_CLAIM_VIP_DIAMONDS_IP, $ip, date('Ymd'));
                $ip_claim_nums = $redis->incr($ip_key);
                if ($ip_claim_nums > $ip_limit) {
                    throw new HttpException(400, '已达到每日领取上限');
                }
            }
            VipReceiveCoinLog::receiveCoinToday($user_id, $list[0]['num'], $list[0]['id'], $vip_id);
        } catch (Exception $e) {
            $redis->unlock($lock);
            if ($equip_limit) {
                $redis->decr($equip_key);
            }
            if ($ip_limit) {
                $redis->decr($ip_key);
            }
            if ($e instanceof HttpException) {
                throw $e;
            }
            Yii::error('用户 ' . $user_id . ' 领取会员权益钻石出错: ' . $e->getMessage(), __METHOD__);
            throw new HttpException(500, '领取失败，请稍后重试');
        }
        return new SuccessResponseWithMessage(null, '领取成功');
    }

    /**
     * @api {post} /vip/create-ios-order iOS 创建会员订单
     * @apiName create-ios-order
     * @apiGroup vip
     *
     * @apiParam {Number} vip_id 会员价目 ID
     * *
     * @apiSuccess {String} message
     * @apiSuccess {Number} code
     * @apiSuccess {Object} data
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "message": "",
     *       "code": 0,
     *       "data": {
     *         "order_id": "17259570630000066104322"
     *       }
     *     }
     */
    public function actionCreateIosOrder()
    {
        throw new HttpException(400, '服务器繁忙');
        $vip_id = (int)Yii::$app->request->post('vip_id');
        $equip = Yii::$app->equip;
        $user_id = Yii::$app->user->id;
        if (!$equip->isIOS()) {
            throw new HttpException(403, '暂不支持该设备创建订单');
        }
        if (!$user = Mowangskuser::getSimpleInfoById($user_id)) {
            throw new HttpException(404, '用户不存在');
        }
        if ($user->isBanTopupAndConsume()) {
            throw new HttpException(403, '您的账号暂被系统停封，不可进行充值消费操作');
        }

        if (!$vip = MVip::findOne(['id' => $vip_id, 'platform' => MVip::PLATFORM_IOS])) {
            throw new HttpException(404, '该会员项不存在');
        }
        if ($vip->isSubscription() && MUserVip::isDuplicateVip($user_id, $vip->id)) {
            // 苹果服务端通知后（如续订），客户端本地也能收到小票，此时客户端会调用 create-ios-order 生成订单 ID，然后用小票信息请求 verify-ios-order
            // 返回特定的订单 ID，避免生成较多的重复订单记录
            return [
                'order_id' => DeductedRecord::getIosDefaultOrderId(),
            ];
        }

        $deduct_record = DeductedRecord::newRecord(null, $user_id, $vip, DeductedRecord::PAY_TYPE_IOS, null, DeductedRecord::STATUS_PENDING);
        $deduct_record->more = ($deduct_record->more ?: []) + DeductedRecord::getMoreExtraInfo($equip, UserContext::fromUser(Yii::$app->request));
        if (!$deduct_record->save()) {
            Yii::error(sprintf('VipFeeDeductedRecord save error: user_id=%d, vip_id=%d, error=%s', $user_id, $vip->id, MUtils::getFirstError($deduct_record)), __METHOD__);
            throw new HttpException(500, '服务器繁忙，请稍候重试');
        }

        return [
            'order_id' => $deduct_record->getOutTradeNo(),
        ];
    }

    /**
     * @api {post} /vip/verify-ios-order{?recovery} 验证 iOS 订单接口
     * @apiName verify-ios-order
     * @apiGroup vip
     *
     * @apiParam (Query 参数) {number=0,1} [recovery] 是否为恢复购买（1 为恢复购买）
     * @apiParam (POST 参数) {String} transaction_id 交易凭证 ID
     * @apiParam (POST 参数) {String} receipt 收据
     * @apiParam (POST 参数) {String} order_id 后台生成的订单号
     *
     * @apiSuccess {String} message
     * @apiSuccess {Number} code
     * @apiSuccess {Object} data
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "message": "成功",
     *       "code": 0,
     *       "data": null
     *     }
     */
    public function actionVerifyIosOrder()
    {
        $transaction_id = trim(Yii::$app->request->post('transaction_id'));
        $receipt = trim(Yii::$app->request->post('receipt'));
        $out_order_no = trim(Yii::$app->request->post('order_id'));
        $user_id = Yii::$app->user->id;
        if (!$user = Mowangskuser::getSimpleInfoById($user_id)) {
            throw new HttpException(404, '用户不存在');
        }
        if ($user->isBanTopupAndConsume()) {
            throw new HttpException(403, '您的账号暂被系统停封，不可进行充值消费操作');
        }

        $is_renew = MUserVip::isVipUser($user_id);
        ['receipt_list' => $receipt_list, 'renewal_info' => $renewal_info] = VipIosSubscriptionForm::parseReceipt($receipt, $transaction_id, $user_id);

        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_VIP_IOS_SUBSCRIPTION, $receipt_list[count($receipt_list) - 1]['original_transaction_id']);
        $transaction = null;
        try {
            if (!$redis->lock($lock, ONE_MINUTE)) {
                throw new HttpException(400, '操作过于频繁，请稍候再试');
            }

            $transaction = DeductedRecord::getDb()->beginTransaction();
            $deduct_records = VipIosSubscriptionForm::checkIosDeductedRecords($user_id, $receipt_list, $out_order_no);

            $agreement = $agreement_no = null;
            if ($renewal_info) {
                ['vip_id' => $vip_id, 'next_term_vip_id' => $next_term_vip_id, 'agreement_no' => $agreement_no, 'auto_renew_status' => $auto_renew_status] = $renewal_info;
                $agreement = VipIosSubscriptionForm::checkIosAgreement($user_id, $agreement_no, $vip_id, $next_term_vip_id, $auto_renew_status);
                if ($agreement && ($agreement->isNewRecord || $agreement->getDirtyAttributes()) && !$agreement->save()) {
                    throw new Exception(MUtils::getFirstError($agreement));
                }
            }

            /**
             * @var DeductedRecord[] $deduct_records
             * @var DeductedRecord $pre_deduct_record
             * @var MVip $vip
             * @var MVip $pre_vip
             * @var int $purchase_time
             */
            $pre_deduct_record = null;
            $price_sum = 0;
            foreach ($deduct_records as $deduct_record) {
                [$vip, $purchase_time] = $deduct_record->getCarryData();
                if ($vip->isSubscription() && $agreement) {
                    if ($agreement->agreement_no !== $agreement_no) {
                        Yii::error(sprintf('使用不同 AppleId 开通会员：user_id=%d', $user_id), __METHOD__);
                        continue;
                    }
                    if ($deduct_record->vip_id === $agreement->vip_id && !$deduct_record->sign_agreement_id) {
                        $deduct_record->sign_agreement_id = $agreement->id;
                    }
                }

                if (!$deduct_record->save()) {
                    throw new Exception(MUtils::getFirstError($deduct_record));
                }
                if ($vip->isSubscription() && $pre_deduct_record) {
                    [$pre_vip] = $pre_deduct_record->getCarryData();
                    // 如果当前订阅在之前的订阅过期前开通，且当前订阅的套餐相比于之前订阅的套餐等级更高，即发生了升级（包月升包季）
                    // 则需要对未使用的包月扣除对应比例的时长
                    if ($purchase_time < $pre_deduct_record->next_deduct_time
                            && $vip->id !== $pre_vip->id
                            && $vip->isScheduleLevelGreaterThan($pre_vip->deduct_fee_schedule)) {
                        MUserVip::deductLeftDuration($pre_deduct_record, $purchase_time);
                    }
                }
                MUserVip::createOrExtend($deduct_record->user_id, $vip, $deduct_record->id);
                if ($vip->isSubscription()) {
                    $pre_deduct_record = $deduct_record;
                }
                $price_sum += $deduct_record->price;
            }

            $transaction->commit();
            $transaction = null;

            if (count($deduct_records) > 0) {
                MUserVip::sendSuccessfulNotification($user_id, $is_renew);
                AdTrack::callbackPay($user_id, Balance::profitUnitConversion($price_sum, Balance::CONVERT_FEN_TO_YUAN));
            }
            return new SuccessResponseWithMessage(null, '成功');
        } catch (Exception $e) {
            if ($e instanceof HttpException && $e->statusCode >= 500) {
                Yii::error(sprintf('iOS 订阅处理失败：error=%s', $e->getMessage()), __METHOD__);
            }
            if ($transaction) {
                $transaction->rollBack();
            }
            throw $e;
        } finally {
            $redis->unlock($lock);
        }
    }

}
