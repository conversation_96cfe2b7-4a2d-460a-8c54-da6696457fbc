<?php
/**
 * Created by PhpStorm.
 * User: Tom<PERSON>ao
 * Date: 2018/4/11
 * Time: 上午10:48
 */

namespace app\controllers;

use app\components\base\filter\AccessControl;
use app\components\util\Equipment;
use app\components\util\MUtils;
use app\forms\TransactionForm;
use app\middlewares\Controller;
use app\models\Balance;
use app\models\Card;
use app\models\CardPackage;
use app\models\Danmaku;
use app\models\FreeNotice;
use app\models\FreeNoticeListened;
use app\models\GetCard;
use app\models\Hot;
use app\models\LotteryPackage;
use app\models\MsgBox;
use app\models\MSoundComment;
use app\models\ReturnModel;
use app\models\Role;
use app\models\SoundComment;
use app\models\SkinPackage;
use app\models\TransactionLog;
use app\models\Work;
use app\models\WorkUserInfo;
use missevan\storage\StorageClient;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\filters\VerbFilter;
use yii\web\HttpException;

class VoiceController extends Controller
{
    const INFO_TYPE_COUPON = 0;
    const INFO_TYPE_DRAW = 1;
    const INFO_TYPE_EPISODE = 2;
    const INFO_TYPE_PUSH_RULE = 3;
    const INFO_TYPE_WELFARE = 4;
    const INFO_TYPE_BUY_RULE = 5;

    // 公告弹窗状态（未开启、开启且不可关闭、开启且可关闭）
    const OPEN_UNACTIVE = 0;
    const OPEN_ACTIVE = 1;
    const OPEN_CLOSABLE = 2;

    private static $alert;

    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $work_id = Yii::$app->request->get('work_id');
        if (self::OPEN_ACTIVE === ($this->getAlert($work_id)['open'] ?? 0)) {
            $behaviors['access'] = [
                'class' => AccessControl::class,
                'except' => ['homepage'],
                'rules' => [
                    [
                        'allow' => false,
                        'roles' => ['*'],
                    ],
                ]
            ];
            return $behaviors;
        }

        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'sync-free' => ['post'],
                'send-dm' => ['post'],
                'buy-package' => ['post'],
                'exchange' => ['post'],
                'unlock' => ['post'],
                'draw-card' => ['post'],
                'draw-ten-cards' => ['post']
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'only' => [
                'send-dm',
                'buy-package',
                'exchange',
                'unlock',
                'draw-card',
                'draw-ten-cards',
                'get-free',
                'get-episode-card',
                'hot-cards'
            ],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => [
                        'send-dm',
                        'buy-package',
                        'exchange',
                        'unlock',
                        'draw-card',
                        'draw-ten-cards',
                        'get-free',
                        'get-episode-card',
                        'hot-cards'
                    ],
                    'roles' => ['@']
                ],
            ]
        ];
        return $behaviors;
    }

    private function getAlert($work_id)
    {
        if (!self::$alert) {
            $equipment = Yii::$app->equip;
            $os = $equipment->isAndroidOrHarmonyOS() ? Equipment::Android : Equipment::iOS;
            $version = $equipment->getAppVersion();
            self::$alert = Work::getAnnouncement($work_id, $os, $version);
        }
        return self::$alert;
    }

    /**
     * @api {get} /voice/homepage 语音包首页
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/homepage
     * @apiSampleRequest voice/homepage
     *
     * @apiVersion 0.1.0
     * @apiName homepage
     * @apiGroup voice
     *
     * @apiParam {Number} work_id 作品 ID
     * @apiParam {String} resolution 屏幕分辨率
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "alert": {
     *           "open": 1,
     *           "alert": "<p>服务器维护中 ...</p>"
     *         },
     *         "banners": [
     *           {
     *             "pic": "http://static.missevan.com/mimages/201804/19/297ce5ff59667467512c15917c18d162174934.png"
     *           }
     *         ],
     *         "balance": 24,
     *         "coupon": 12,
     *         "is_free": true,
     *         "hot": "01,201,424",
     *         "msg_num": 3,
     *         "notices": [
     *           {
     *             "role_id": 2,
     *             "role_icon": "http://static.missevan.com/image/roles/201804/20/e74de04ade0ebe3e3aaaec9182028.jpg",
     *             "role_name": "叶修",
     *             "notice": 2,
     *             "last_time": 1724477234,
     *             "free_cards": [55, 61]
     *           },
     *           {
     *             "role_id": 1,
     *             "role_icon": "http://static.missevan.com/image/roles/201804/20/d6706fdf898199877421182123.png",
     *             "role_name": "黄少天",
     *             "notice": 2,
     *             "last_time": 1525454651,
     *             "free_cards": [112]
     *           },
     *           {
     *             "role_id": 3,
     *             "role_icon": "http://static.missevan.com/image/roles/201804/20/028caf44f28e277121d9e1988185306.jpg",
     *             "role_name": "周泽楷",
     *             "notice": 2,
     *             "last_time": 1524570000,
     *             "free_cards": [92]
     *           },
     *           {
     *             "role_id": 0,
     *             "role_icon": "http://static.missevan.com/image/roles/201804/20/028caf44f28e277121d9e1988185306.jpg",
     *             "role_name": "小剧场",
     *             "notice": 2,
     *             "last_time": 1524570000,
     *             "free_cards": []
     *           }
     *         ],
     *         "works": [
     *           {
     *             "id": 1,
     *             "title": "全职高手",
     *             "skin": "http://static.missevan.com/voice/work/1/skin/2019-05-14/os-1-3/skin.zip"
     *           },
     *           {
     *             "id": 2,
     *             "title": "剑网三",
     *             "skin": "http://static.missevan.com/voice/work/2/skin/2019-05-14/os-1-3/skin.zip"
     *           }
     *         ],
     *         "show_switch_ip_btn": 1  // 是否显示 IP 按钮（0 否，1 是）
     *       }
     *     }
     */
    public function actionHomepage(int $work_id = Work::ID_QUANZHI, ?string $resolution = null)
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }
        if (!$work = Work::getWork($work_id, Work::TYPE_VOICE)) {
            throw new HttpException(404, '未找到该作品');
        }
        // 用户钻石、经验值点数
        $balance = $coupon = 0;
        $user_id = (int)Yii::$app->user->id;
        if ($user_id) {
            $user_info = WorkUserInfo::getUserInfo($user_id, $work_id);
            // 入口的语音提醒计数清零
            FreeNoticeListened::cleanNotice($user_id, $work_id, FreeNotice::getSum($work_id));
            if ($user_info->notice) {
                // 将用户提醒数清零
                $user_info->notice = 0;
                $user_info->update();
            }
            $balance = Balance::getByPk($user_id)->getTotalBalance();
            $coupon = (int)$user_info->coupon;
        }
        // 首页 banner、抽卡是否免费
        [$banners, $is_free] = $this->getHomepageTop($work, $user_id);
        $announcement = $this->getAlert($work_id);
        $return = [
            'alert' => $announcement,
            'banners' => $banners,
            'balance' => $balance,
            'coupon' => $coupon,
            'is_free' => $is_free,
            'notices' => GetCard::getMsgList($work_id, $user_id),
        ];
        if (Work::hasHotCard($work_id)) {
            // 获得作品格式化后的热度值
            $return['hot'] = Hot::formatValue($work_id);
        }
        $equip_id = Yii::$app->equip->getEquipId();
        $return['msg_num'] = MsgBox::getNoticeNum($work_id, $equip_id, $user_id);
        $redis = Yii::$app->redis;
        if ($return['show_switch_ip_btn'] = (int)$redis->hGet(KEY_VOICE_WORK_CONTROL_ITEMS, 'show_switch_ip_btn')) {
            $os = Yii::$app->equip->isAndroidOrHarmonyOS() ? Equipment::Android : Equipment::iOS;
            $return['works'] = Work::getAllWorks($os, $resolution);
        }
        return $return;
    }

    private function getHomepageTop(Work $work, int $user_id): array
    {
        $banners = Work::getWorkPic($work->id, Work::TYPE_HOMEPAGE_BANNERS);
        $is_free = $this->isFreeDraw($work, $user_id);
        return [$banners, $is_free];
    }

    /**
     * @api {get} /voice/msg-box 信箱消息
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/msg-box
     * @apiSampleRequest voice/msg-box
     *
     * @apiVersion 0.1.0
     * @apiName msg-box
     * @apiGroup voice
     *
     * @apiParam {Number} work_id 作品 ID
     * @apiParam {Number} [page=1] 所在页
     * @apiParam {Number} [is_all=0] 是否获取所有（点击信箱按钮时为 1）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "title": "信箱",
     *         "content": {
     *           "Datas": [{
     *             "id": 4,
     *             "title": "信件 04",
     *             "content": "信件 04",
     *             "status": 1  // 0 为未读，1 为已读
     *           }],
     *           "pagination": {
     *             "p": 3,
     *             "maxpage": 3,
     *             "count": 3,
     *             "pagesize": 1
     *           }
     *         }
     *       }
     *     }
     */
    public function actionMsgBox(int $work_id, int $is_all = 0, int $page = 1)
    {
        $equip_id = Yii::$app->equip->getEquipId();
        $user_id = Yii::$app->user->id;
        if ($is_all) {
            $msg_box = MsgBox::getAllMsgs($work_id, $equip_id, $user_id)['msgs'];
            $count = count($msg_box);
            $return = ReturnModel::getPaginationData($msg_box, $count, 1, $count ?: 1);
        } else {
            $return = MsgBox::getMsgs($work_id, $page, $equip_id, $user_id);
        }
        if (empty($return->Datas)) {
            // 对于信箱消息已都过期，或后台未设置信箱消息的情形，暂时显示错误提示
            throw new HttpException(404, '暂无信箱消息');
        }
        // WORKAROUND: 新版本添加信箱标题字段
        if (Equipment::isAppOlderThan('4.3.6', '5.2.6')) {
            return $return;
        }
        return [
            'title' => '信 箱',
            'content' => $return,
        ];
    }

    /**
     * @api {get} /voice/info 弹窗接口（经验值、抽卡、小剧场、推送、热度福利剧场规则说明）
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/info
     * @apiSampleRequest voice/info
     *
     * @apiVersion 0.1.0
     * @apiName info
     * @apiGroup voice
     *
     * @apiParam {Number} work_id 作品 ID
     * @apiParam {number=0,1,2,3,4} type 类型（0 经验值、1 抽卡、2 小剧场、3 推送规则、4 热度福利剧场）
     * @apiParam {number=0,1} [special=0] 是否为特殊事件季包（0 为否，1 为是）
     * @apiParam {Number} [season=1] 季度
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "title": "荣耀点规则",
     *         "content": "<div style=\"color: #3d3d3d\"><p><b>荣耀点怎么使用</b></p>..."
     *       }
     *     }
     */
    public function actionInfo()
    {
        $work_id = (int)Yii::$app->request->get('work_id');
        $type = (int)Yii::$app->request->get('type');
        if (!$work = Work::getWork($work_id, Work::TYPE_VOICE)) {
            throw new HttpException(404, '未找到该作品');
        }

        switch ($type) {
            case self::INFO_TYPE_COUPON:
                $return = $this->couponInfo($work);
                $title = $work->coupon_name . '规则';
                break;
            case self::INFO_TYPE_DRAW:
                $return = $this->drawInfo($work);
                $title = '抽卡概率说明';
                break;
            case self::INFO_TYPE_EPISODE:  // 暂未使用
                $season = (int)Yii::$app->request->get('season', 1);
                $return = $this->episodeInfo($work_id, $season);
                $title = '规则说明';
                break;
            case self::INFO_TYPE_PUSH_RULE:
                $special = (int)Yii::$app->request->get('special');
                $card_package_id = (int)Yii::$app->request->get('card_package_id');
                $return = $this->pushRule($work, $special, $card_package_id);
                $title = '角色包规则说明';
                break;
            case self::INFO_TYPE_WELFARE:  // 暂未使用
                $return = $this->welfareInfo($work_id);
                $title = '福利剧场规则';
                break;
            case self::INFO_TYPE_BUY_RULE:
                $return = $this->packageInfo();
                $title = '角色包购买规则';
                break;
            default:
                $return = null;
                $title = '';
        }
        // WORKAROUND: 新版本添加弹窗标题字段
        if (Equipment::isAppOlderThan('4.3.6', '5.2.6')) {
            return $return;
        }
        return [
            'title' => $title,
            'content' => $return,
        ];
    }

    private function couponInfo(Work $work)
    {
        return $this->renderPartial('couponinfo', [
            'work' => $work,
        ]);
    }

    private function drawInfo(Work $work)
    {
        return $this->renderPartial('drawinfo', [
            'work' => $work,
        ]);
    }

    private function episodeInfo($work_id, $season)
    {
        $user_id = Yii::$app->user->id;
        if (!($work = Work::findOne($work_id))) throw new HttpException(404, '没有该作品');

        // card 表中当卡片为小剧场时字段 card_package_id 存储所属季度
        $episode_cards = Card::find()
            ->select('id')
            ->where(['special' => Card::SPECIAL_EPISODE, 'work_id' => $work_id, 'card_package_id' => $season])
            ->orderBy('rank ASC')->asArray()->column();
        $user_cards = GetCard::userCards($episode_cards, $user_id);
        $is_collected = count($episode_cards) === count($user_cards);

        $expense = TransactionLog::getVoiceExpense($work_id, $user_id, $season);
        $unlock_coin_limit = GetCard::UNLOCK_EPISODE_COIN_LIMIT;
        if (!$is_collected) {
            $is_collected = intdiv($expense, $unlock_coin_limit) >= count($episode_cards);
        }
        $need_expense = $unlock_coin_limit - $expense % $unlock_coin_limit;
        return $this->renderPartial('episodeinfo', [
            'expense' => $expense,
            'is_collected' => $is_collected,
            'unlock_coin_limit' => $unlock_coin_limit,
            'need_expense' => $need_expense,
            'card_count' => count($episode_cards),
            'work_id' => $work_id,
        ]);
    }

    private function pushRule(Work $work, $special, $card_package_id)
    {
        return $this->renderPartial('pushrule', [
            'work' => $work,
            'special' => $special,
            'card_package_id' => $card_package_id,
        ]);
    }

    private function welfareInfo($work_id)
    {
        return $this->renderPartial('welfareinfo', [
            'work_id' => $work_id,
        ]);
    }

    /**
     * 判断当前用户是否拥有免费抽卡资格
     * 仅判断用户是否有资格而不判断是否可以免费抽
     *
     * @param Work $work 作品
     * @param int $user_id 用户 ID
     * @return bool 当前用户是否拥有免费抽卡资格
     */
    private function isFreeDraw(Work $work, int $user_id): bool
    {
        // 当作品当前正使用免费抽卡包或用户为抽卡活动的新用户，则用户拥有免费抽卡资格
        return $this->hasFreePackage($work->id) || WorkUserInfo::isNewUser($user_id, $work);
    }

    /**
     * 判断当前是否存在免费活动卡包
     *
     * @param int $work_id 作品 ID
     * @return bool 当前是否有免费活动卡包
     */
    private function hasFreePackage(int $work_id): bool
    {
        return LotteryPackage::find()
            ->where(['mark' => LotteryPackage::MARK_ONE_DRAW, 'work_id' => $work_id, 'price' => 0])
            ->andWhere(':time BETWEEN start_time AND end_time', [':time' => $_SERVER['REQUEST_TIME']])
            ->exists();
    }

    /**
     * @api {get} /voice/unread-msg 未读消息弹窗（角色或小剧场、热度福利）
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/unread-msg
     * @apiSampleRequest voice/unread-msg
     *
     * @apiVersion 0.1.0
     * @apiName unread-msg
     * @apiGroup voice
     *
     * @apiParam {Number} work_id 作品 ID
     * @apiParam {Number} role_id 角色 ID（当小剧场时 role_id=0，当热度福利时 role_id=-1）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "id": 34,
     *           "title": "某某卡",
     *           "level": 0
     *         }
     *       ]
     *     }
     */
    public function actionUnreadMsg()
    {
        $role_id = (int)Yii::$app->request->get('role_id');
        $work_id = (int)Yii::$app->request->get('work_id');
        if (!Work::checkWork($work_id)) throw new HttpException(404, '未找到该作品');

        $user_id = Yii::$app->user->id;
        return GetCard::getUnreadMsg($work_id, $role_id, $user_id);
    }

    /**
     * @api {get} /voice/role-list 角色列表
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/role-list
     * @apiSampleRequest voice/role-list
     *
     * @apiVersion 0.1.0
     * @apiName role-list
     * @apiGroup voice
     *
     * @apiParam {Number} work_id 作品 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "id": 3,
     *           "name": "黄少天",
     *           "cover": "http://static.missevan.com/image/roles/201804/18/eb187d1ae5432353be170742.jpg",
     *           "intro": "黄少天"
     *           "special": 0  // 按位运算：0 普通角色、第一位为特殊角色、第二位为预告角色
     *         }
     *       ]
     *     }
     */
    public function actionRoleList()
    {
        $work_id = (int)Yii::$app->request->get('work_id');
        if (!Work::checkWork($work_id)) throw new HttpException(404, '未找到该作品');

        $results = Role::getAllRoles($work_id);
        // WORKAROUND: 老版本 app 不显示特殊角色（生日语音/快问快答）、预告角色
        if (Equipment::isAppOlderThan('4.2.3', '5.1.2')) {
            $results = array_values(array_filter($results, function ($item) {
                return $item['special'] === Role::ATTR_COMMON;
            }));
        }

        // WORKAROUND: 老版本 app 不显示预告角色
        if (Equipment::isAppOlderThan('4.3.6', '5.2.6')) {
            $results = array_values(array_filter($results, function ($item) {
                return ($item['special'] & Role::ATTR_ADVANCE) === 0;
            }));
        }
        if (Work::hasEpisode($work_id)) {
            array_unshift($results, [
                'id' => 0,
                'cover' => Work::getWorkPic($work_id, Work::TYPE_EPISODE_COVER),
            ]);
        }
        return $results;
    }

    /**
     * @api {get} /voice/role-detail 角色详情页
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/role-detail
     * @apiSampleRequest voice/role-detail
     *
     * @apiVersion 0.1.0
     * @apiName role-detail
     * @apiGroup voice
     *
     * @apiParam {Number} role_id 角色 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *      {
     *        "success": true,
     *        "code": 0,
     *        "info": {
     *          "coupon": 80,
     *          "get_coupon_info": {
     *            "attr": 4,  // IP 包含的季包状态（attr & 2 != 0 不可购买、attr & 4 != 0 不可抽）
     *            "text_insufficient": "* 荣耀点不足，抽语音或购买季包可得",
     *            "text_sufficient": "* 点击兑换后可获得该语音"
     *           },
     *          "role": {
     *            "id": 2,
     *            "name": "叶修",
     *            "intro": "叶修",
     *            "cover_detail": "http://static.missevan.com/image/roles/201804/20/01f03c8ab33f39046a6e122182030.jpg",
     *            "special": 0,  // 是否为特殊角色
     *            "work_id": 1
     *          },
     *          "package_cards": [
     *            {
     *              "card_package_id": 1,
     *              "season": 1,
     *              "season_name": "第一季",
     *              "status": 0, // 0 未付费未集齐、1 已付费未集齐（承包中）、2 已集齐、3 下架
     *              "attr": 0, // attr & 1 != 0 特殊角色包、attr & 2 != 0 不可购买、attr & 4 != 0 不可抽、attr & 8 != 0 为多角色模式、attr & 16 != 0 开启等级筛选模块
     *              "cards": [
     *               {
     *                 "card_id": 19,
     *                 "title": "叶修咳嗽",
     *                 "icon": "http://static.missevan.com/image/roles/201804/20/ca5252368757177c3fcc84c5882028.png",
     *                 "intro": "卡片",  // 多角色模式时的卡片角色名
     *                 "price": 10,
     *                 "level": 2,
     *                 "special": 0,
     *                 "card_package_id": 1,
     *                 "status": 0 // 0 为未解锁、1 为待推送、2 未收听、3 已收听、4 下架
     *               }
     *             ]
     *           }
     *         ],
     *         "festival": [
     *           {
     *             "card_id": 53,
     *             "title": "我是卡片4444",
     *             "icon": "http://static.missevan.com/image/roles/201804/20/ca5252368757177c3fcc84c5882028.png",
     *             "intro": "4545",
     *             "price": 0,
     *             "level": 0,
     *             "special": 1,
     *             "card_package_id": 1
     *             "status": 1
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionRoleDetail()
    {
        $role_id = (int)Yii::$app->request->get('role_id');
        $user_id = (int)Yii::$app->user->id;
        if ($role_id === -1) {
            throw new HttpException(403, '请将 App 升级到最新版本');
        }
        $role = Role::find()->select('id, name, intro, cover_detail, work_id')
            ->where(['id' => $role_id])->asArray()->one();
        if (!$role) {
            throw new HttpException(404, '未找到该角色');
        }
        $role['id'] = (int)$role['id'];
        $role['work_id'] = (int)$role['work_id'];
        $role['cover_detail'] = StorageClient::getFileUrl($role['cover_detail']);

        $all_cards = Card::getAllCards($role_id, $user_id);
        $festival_cards = Card::getFestivalCards($all_cards);
        $package_cards = CardPackage::getPackageCards($user_id, $role, $all_cards);
        // WORKAROUND: 安卓 5.1.1 及其之前版本，对于第二个及其后的全职季包点击后会闪退（客户端数组越界），对此则只显示第一季的季包
        if (Equipment::isAppOlderThan(null, '5.1.2')) {
            $package_cards = array_values(array_filter($package_cards, function ($item) {
                return $item['season'] === 1;
            }));
        }
        // WORKAROUND： 新版本季包含不可购买的状态，旧版本显示为未购（价格设置为 99999 以上数值）
        if (Equipment::isAppOlderThan('4.3.6', '5.2.6')) {
            $package_cards = array_map(function ($item) {
                if (CardPackage::ATTR_NOT_PURCHASABLE & $item['attr']) {
                    $item['status'] = CardPackage::STATUS_NEEDPAY;
                }
                return $item;
            }, $package_cards);
        }

        return [
            'coupon' => WorkUserInfo::getCoupon($user_id, $role['work_id']),
            'get_coupon_info' => Work::getCouponInfo($role['work_id']),
            'role' => $role,
            'package_cards' => $package_cards,
            'festival' => $festival_cards,
        ];
    }

    /**
     * @api {get} /voice/episode 小剧场页面
     * @apiDeprecated 仅旧版本（iOS 4.2.2 及其之前，Android 5.1.1 及其之前）使用，新版本由 /voice/episodes 接口所代替
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/episode
     * @apiSampleRequest voice/episode
     *
     * @apiVersion 0.1.0
     * @apiName episode
     * @apiGroup voice
     *
     * @apiParam {Number} work_id 作品 ID
     * @apiParam {Number} [season=1] 季度
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "banner": "http://static.missevan.com/mimages/201804/19/297ce5ff59667467512c15917c18d162174934.png",
     *         "coupon": 112,
     *         "star": {
     *           "lighted_count": 2,
     *           "all_count": 10
     *         },
     *         "cards": [
     *           {
     *             "id": 67,
     *             "title": "小剧场卡3",
     *             "intro": "卡片",
     *             "status": 3, // 0 未解锁、1 为待领取、2 未收听、3 已收听、4 下架
     *             "price": 100 // 兑换所需要点数
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionEpisode()
    {
        $work_id = (int)Yii::$app->request->get('work_id');
        $season = (int)Yii::$app->request->get('season', 1);
        if (!Work::checkWork($work_id)) throw new HttpException(404, '未找到该作品');
        $user_id = (int)Yii::$app->user->id;

        $unlocked_count = GetCard::getEpisodeUnlockedCount($work_id, $user_id, $season);
        $cards = GetCard::getEpisodeCards($work_id, $season, $user_id, $unlocked_count);
        $all_count = count($cards);
        return [
            'banner' => Work::getWorkPic($work_id, Work::TYPE_EPISODE_BANNER),
            'coupon' => WorkUserInfo::getCoupon($user_id, $work_id),
            'star' => [
                'lighted_count' => ($unlocked_count > $all_count) ? $all_count : $unlocked_count,
                'all_count' => $all_count,
            ],
            'cards' => $cards,
        ];
    }

    /**
     * @api {get} /voice/episodes 小剧场新页面
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/episodes
     * @apiSampleRequest voice/episodes
     *
     * @apiVersion 0.1.0
     * @apiName episodes
     * @apiGroup voice
     *
     * @apiParam {Number} work_id 作品 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "banner": "http://static.missevan.com/mimages/201805/25/1220bb93b03759d911023.png",
     *         "coupon": 3115,
     *         "episodes": [{
     *           "season": 2,
     *           "season_name": "第二季",
     *           "all_count": 2,
     *           "unlocked_count": 2,
     *           "expense": 1278,
     *           "expense_limit": 1000,
     *           "cards": [{
     *             "id": 573,
     *             "title": "WOSHI",
     *             "intro": "9999",
     *             "price": 0,
     *             "is_online": 1,
     *             "card_package_id": 2,
     *             "status": 1  // 0 未解锁、1 为待领取、2 未收听、3 已收听、4 下架
     *           },
     *           {
     *             "id": 569,
     *             "title": "风云",
     *             "intro": "简介",
     *             "price": 0,
     *             "is_online": 1,
     *             "card_package_id": 2,
     *             "status": 1
     *           }]
     *         },
     *         {
     *           "season": 1,
     *           "season_name": "第一季",
     *           "all_count": 5,
     *           "unlocked_count": 5,
     *           "expense": 6690,
     *           "expense_limit": 2500,
     *           "cards": [{
     *             "id": 426,
     *             "title": "欢聚一堂",
     *             "intro": "欢聚一堂",
     *             "price": 0,
     *             "is_online": 1,
     *             "card_package_id": 1,
     *             "status": 1
     *           },
     *           {
     *             "id": 428,
     *             "title": "K歌之王",
     *             "intro": "K歌之王",
     *             "price": 0,
     *             "is_online": 1,
     *             "card_package_id": 1,
     *             "status": 2
     *           },
     *           {
     *             "id": 430,
     *             "title": "火锅风云",
     *             "intro": "火锅风云",
     *             "price": 0,
     *             "is_online": 1,
     *             "card_package_id": 1,
     *             "status": 1
     *           }]
     *         }]
     *       }
     *     }
     */
    public function actionEpisodes()
    {
        $work_id = (int)Yii::$app->request->get('work_id');
        if (!Work::checkWork($work_id)) throw new HttpException(404, '未找到该作品');
        $user_id = (int)Yii::$app->user->id;
        $episodes = GetCard::getEpisodes($work_id, $user_id);

        return [
            'banner' => Work::getWorkPic($work_id, Work::TYPE_EPISODE_BANNER),
            'coupon' => WorkUserInfo::getCoupon($user_id, $work_id),
            'episodes' => $episodes,
            'rule' => $this->getEpisodesRule($work_id, $episodes),
        ];
    }

    private function getEpisodesRule($work_id, $episodes)
    {
        $expenses = array_column($episodes, 'expense', 'season');
        $lacks = array_map(function ($v) {
            return GetCard::UNLOCK_EPISODE_COIN_LIMIT - $v % GetCard::UNLOCK_EPISODE_COIN_LIMIT;
        }, $expenses);

        $is_collecteds = array_map(function ($v) {
            return $v['unlocked_count'] >= $v['all_count'];
        }, array_column($episodes, null, 'season'));
        ksort($is_collecteds);
        return $this->renderPartial('aboutepisodes', [
            'work_id' => $work_id,
            'unlock_coin_limit' => GetCard::UNLOCK_EPISODE_COIN_LIMIT,
            'expenses' => $expenses,
            'lacks' => $lacks,
            'is_collecteds' => $is_collecteds,
        ]);
    }

    /**
     * @api {get} /voice/hot-cards 热度卡（福利小剧场卡）页面
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/hot-cards
     * @apiSampleRequest voice/hot-cards
     *
     * @apiVersion 0.1.0
     * @apiName hot-cards
     * @apiGroup voice
     *
     * @apiParam {Number} work_id 作品 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "banner": "http://static.missevan.com/mimages/201804/19/297ce5467512c15917c18d162174934.png",
     *         "all_hot": "01,201,424",
     *         "user_hot": 669,
     *         "hot_cards_info": "<div>规则介绍</div>"
     *         "cards": [
     *           {
     *             "id": 67,
     *             "title": "卡片名称",
     *             "intro": "卡片简介",
     *             "status": 3, // 0：未解锁；2：（已解锁）未收听；3：（已解锁）已收听；4：（已解锁）下架
     *             "hot": 1500000 // 卡片解锁需要的热度值
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionHotCards()
    {
        $work_id = (int)Yii::$app->request->get('work_id');
        if (!Work::checkWork($work_id)) throw new HttpException(404, '未找到该作品');
        $user_id = Yii::$app->user->id;
        // 获取用户在作品下的热度卡
        $cards = Card::getHotCards($work_id, $user_id);
        // 获得格式化后的作品热度值（将返回的数据变为“00,233,312”字样）
        $all_hot = Hot::formatValue($work_id);
        // 获得格式化后的登录用户贡献的热度值
        $user_hot = Hot::formatValue($work_id, $user_id);
        $hot_cards_info = '';
        if ($cards) {
            $hot_str = $this->formatCardsHot($cards);
            $hot_cards_info = $this->renderPartial('aboutwelfare', [
                'work_id' => $work_id,
                'hot_draw' => Hot::HOT_ONE_DRAW,
                'hot_ten_draw' => Hot::HOT_TEN_DRAW,
                'hot_buy' => Hot::HOT_BUY,
                'hot_listen' => Hot::HOT_LISTEN,
                'cards_num' => count($cards),
                'hot_str' => $hot_str,
            ]);
        }
        return [
            'banner' => Work::getWorkPic($work_id, Work::TYPE_WELFARE_BANNER),
            'all_hot' => $all_hot,
            'user_hot' => $user_hot,
            'hot_cards_info' => $hot_cards_info,
            'cards' => $cards,
        ];
    }

    /**
     * 格式化卡片热度值
     *
     * @param array $cards
     * @return string
     */
    private function formatCardsHot(array $cards): string
    {
        $arr = [];
        foreach ($cards as $key => $card) {
            if (strlen($card['hot']) > 4) {
                // 热度超过 4 位数，则以万为单位，舍弃后边的数量
                $arr[] = substr($card['hot'], 0, -4) . 'W';
            } else {
                $arr[] = $card['hot'];
            }
        }
        return implode('/', $arr);
    }

    private function packageInfo()
    {
        $card_package_id = (int)Yii::$app->request->get('card_package_id');
        $special = (int)Yii::$app->request->get('special');
        $user_id = (int)Yii::$app->user->id;
        if (!($package = CardPackage::findOne($card_package_id))) throw new HttpException(404, '没有该季包');

        // 季包包含的卡片
        $cards = Card::find()->select('id, coupon, level')
            ->where(['card_package_id' => $card_package_id, 'special' => Card::SPECIAL_NORMAL])
            ->indexBy('id')->all();
        $card_ids = array_column($cards, 'id');
        $level_num_map = array_reduce($cards, function ($level_num_map, $item) {
            $level = $item['level'];
            $level_num_map[$level] = ($level_num_map[$level] ?? 0) + 1;
            return $level_num_map;
        }, []);
        // 已获得的卡片
        $card_ids_user = array_column(GetCard::userCards($card_ids, $user_id), 'card_id');

        // 获得的重复卡片加季包每张卡片将返还的经验值
        $coupon_refund = array_sum(array_map(function ($item) use ($card_ids_user) {
            if (in_array($item['id'], $card_ids_user)) {
                return $item['coupon'];
            }
        }, $cards)) + $package->given_coupon;
        // 已获得的卡片数量
        $count_own = count($card_ids_user);

        $role = Role::findOne($package->role_id);
        if ($special) {
            $package_title = $role->name;
        } else {
            $package_title = $role->name . CardPackage::SEASON_NAMES[$package->season] . '角色语音包';
        }

        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_VOICE_WORK_PACKAGE_FESTIVAL_EVENT_DATE, $role->work_id);
        $festival_dates = $redis->hGetAll($key);
        if ($festival_dates) {
            $event_season = $festival_dates['season'];
            $from_time = $festival_dates['from_time'];
            $to_time = $festival_dates['to_time'];
        } else {
            $event_season = 1;
            $from_time = strtotime(Card::EVENT_INTERVAL_1['from']);
            $to_time = strtotime(Card::EVENT_INTERVAL_1['to']);
        }

        return $this->renderPartial('packageinfo', [
            'count_own' => $count_own,
            'coupon_refund' => $coupon_refund,
            'price' => $package->price,
            'package_title' => $package_title,
            'from_date' => date('Y 年 n 月 d 日', $from_time),
            'to_date' => date('Y 年 n 月 d 日', $to_time),
            'event_season' => CardPackage::SEASON_NAMES[$event_season],
            'special' => $special,
            'level_num_map' => $level_num_map,
        ]);
    }

    /**
     * @api {get} /voice/package-info 购买季包时的弹窗说明
     * @apiDeprecated 仅旧版本（iOS 4.3.5 及其之前，Android 5.2.5 及其之前），新版本统一使用 /voice/info?type=5 来代替
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/package-info
     * @apiSampleRequest voice/package-info
     *
     * @apiVersion 0.1.0
     * @apiName package-info
     * @apiGroup voice
     *
     * @apiParam {Number} card_package_id 季包 ID
     * @apiParam {number=0,1} [special=0] 是否为特殊事件季包（0 为否，1 为是）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "<div style=\"color: ##3d3d3d\">\n <p><b>季包： 角色3第一季语音包</b></p>\n"
     *     }
     */
    public function actionPackageInfo()
    {
        return $this->packageInfo();
    }

    /**
     * @api {post} /voice/sync-free 登录后同步本地的免费音的收听状态
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/sync-free
     * @apiSampleRequest voice/sync-free
     *
     * @apiVersion 0.1.0
     * @apiName sync-free
     * @apiGroup voice
     *
     * @apiParam {Number[]} card_ids 卡片 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Boolean} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": true
     *     }
     */
    public function actionSyncFree()
    {
        $card_ids = Yii::$app->request->post('card_ids', []);
        if (!MUtils2::isUintArr($card_ids)) {
            throw new HttpException(400, '参数错误');
        }
        $card_ids = array_map('intval', $card_ids);
        return FreeNoticeListened::syncFree(Yii::$app->user->id, $card_ids);
    }

    /**
     * @api {post} /voice/get-free 退出前获取收听过的免费用于同步到本地
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/get-free
     * @apiSampleRequest voice/get-free
     *
     * @apiVersion 0.1.0
     * @apiName get-free
     * @apiGroup voice
     *
     * @apiParam {Number} work_id 作品 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "role_id": 1,
     *           "card_ids": [1, 3]
     *         },
     *         {
     *           "role_id": 2,
     *           "card_ids": [49]
     *         }
     *       ]
     *     }
     */
    public function actionGetFree()
    {
        $work_id = (int)Yii::$app->request->post('work_id');
        if (!Work::checkWork($work_id)) throw new HttpException(404, '未找到该作品');
        $cards = MUtils::groupArray(FreeNoticeListened::getHistoryByWork($work_id, Yii::$app->user->id,
                FreeNoticeListened::HAS_NOTICE_YES),
            'role_id', 'card_id');
        $return = [];
        foreach ($cards as $role_id => $card_ids) {
            $return[] = [
                'role_id' => $role_id,
                'card_ids' => $card_ids
            ];
        }
        return $return;
    }

    /**
     * @api {get} /voice/play 播放页
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/play
     * @apiSampleRequest voice/play
     *
     * @apiVersion 0.1.0
     * @apiName play
     * @apiGroup voice
     *
     * @apiParam {Number} card_id 卡片 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "role_name": "角色名称",
     *         "card": {
     *           "id": 28,
     *           "create_time": 1234567890,
     *           "modified_time": 123456790,
     *           "title": "卡片",
     *           "icon": "http://static.missevan.com/coversmini/nocover.png",
     *           "cover": "http://static.missevan.com/coversmini/nocover.png",
     *           "intro": "简介",
     *           "voice": "http://static.missevan.com/coversmini/mp3",
     *           "level": 0,
     *           "special": 1,
     *           "rank": 1,
     *           "push": 5,
     *           "card_package_id": 0,
     *           "coupon": 0,
     *           "price": 0,
     *           "work_id": 1,
     *           "role_id": 4,
     *           "is_online": 1,
     *           "play_cover": "http://static.missevan.com/coversmini/nocover.png",
     *           "subtitles": ""
     *           "pics": [
     *             {
     *               "img_url": "http://static.missevan.com/coversmini/nocover.png",
     *               "stime": "12.2",
     *             },
     *             {
     *               "img_url": "http://static.missevan.com/coversmini/nocover.png",
     *               "stime": "26.9",
     *             }
     *           ]
     *         },
     *         "watermark": "http://static.missevan.com/coversmini/nocover.png",
     *         "comment_count": 356
     *       }
     *     }
     */
    public function actionPlay()
    {
        $card_id = (int)Yii::$app->request->get('card_id');
        $card = Card::findOne([
            'id' => $card_id,
            'special' => [
                Card::SPECIAL_NORMAL,
                Card::SPECIAL_FESTIVAL,
                Card::SPECIAL_EPISODE,
                Card::SPECIAL_FREE,
                Card::SPECIAL_HOTCARD,
            ]
        ]);
        if (!$card) throw new HttpException(404, '未找到该卡片');
        if (Card::OFFLINE === $card->is_online) throw new HttpException(403, '应版权方要求，此语音暂已下架');

        if ($card->role_id) {
            $role = Role::findOne($card->role_id);
        }
        $user_id = Yii::$app->user->id;

        if (!$user_id && !in_array($card->special, [Card::SPECIAL_FREE, Card::SPECIAL_HOTCARD])) {
            throw new HttpException(403, '您还未获得此卡，请先去兑换或购买');
        }

        $hot_value = Hot::getHot($card->work_id);
        if (Card::SPECIAL_HOTCARD === $card->special && $hot_value < $card->price) {
            throw new HttpException(403, "热度达到 {$card->price} 时才可收听该语音哦~");
        }

        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_VOICE_WATERMARK, $card->work_id);
        $watermark = '';  // 有的 IP 没有水印
        if ($file = $redis->get($key)) {
            $watermark = StorageClient::getFileUrl($file);
        }

        if (in_array($card->special, [Card::SPECIAL_FREE, Card::SPECIAL_HOTCARD])) {
            if (!FreeNoticeListened::getHistoryByCard($card_id, $user_id)) {
                $listened = new FreeNoticeListened;
                $listened->work_id = $card->work_id;
                $listened->role_id = -1; // 听过的热度福利卡存 role = -1
                $listened->card_id = $card_id;
                $listened->user_id = (int)$user_id;
                if (Card::SPECIAL_FREE === $card->special) {
                    $listened->role_id = $role->id;
                    $listened->has_notice = Card::PRICE_FREE_CARD_HAVE_NOTICE === $card->price
                        ? FreeNoticeListened::HAS_NOTICE_YES : FreeNoticeListened::HAS_NOTICE_NO;
                }
                $listened->save();
            }
        } else {
            if (!($get_card = GetCard::findOne(['card_id' => $card_id, 'user_id' => $user_id]))) {
                throw new HttpException(403, '您还未获得此卡，请先去兑换或购买');
            }
            if (GetCard::STATUS_LOCK === $get_card->status || $_SERVER['REQUEST_TIME'] < $get_card->appear_time) {
                throw new HttpException(403, "不要急嘛，{$role->name}会主动联系你的~");
            } elseif (GetCard::STATUS_UNLOCK === $get_card->status) {
                GetCard::updateAll(['status' => GetCard::STATUS_LISTENED], 'card_id = :card_id AND user_id = :user_id',
                    [':card_id' => $card_id, ':user_id' => $user_id]);
                WorkUserInfo::updateAllCounters(['notice' => -1],
                    'user_id = :user_id AND work_id = :work_id AND notice > 0',
                    [':user_id' => $user_id, 'work_id' => $card->work_id]);
            }
        }
        if ($user_id) {
            // 更新用户贡献的热度值
            Hot::updateHotByListen($card->work_id, $user_id);
        }
        switch ($card->special) {
            case Card::SPECIAL_EPISODE:
                $role_name = '小剧场';
                break;
            case Card::SPECIAL_HOTCARD:
                $role_name = '福利剧场';
                break;
            default:
                $role_name = $card->isMultiRole() ? $card->intro : $role->name;
                break;
        }

        $card->voice = Card::getSoundSignUrl($card->voice);
        $return = ['role_name' => $role_name, 'card' => $card];
        // WORKAROUND: 新版本改用 watermark 字段名
        if (Equipment::isAppOlderThan('4.3.6', '5.2.6')) {
            $return['water_mark'] = $watermark;
        } else {
            $return['watermark'] = $watermark;
        }
        $return['comment_count'] = $card->getCommentCount(Work::TYPE_VOICE);

        return $return;
    }

    /**
     * @api {get} /voice/dm 弹幕接口
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/dm
     * @apiSampleRequest voice/dm
     *
     * @apiVersion 0.1.0
     * @apiName dm
     * @apiGroup voice
     *
     * @apiParam {Number} card_id 卡片 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><i>\r\n<d p='3,1,1,123,1234567,346286'>12</d></i>\r\n"
     *     }
     */
    public function actionDm()
    {
        $card_id = Yii::$app->request->get('card_id');
        $dm = Danmaku::find()
            ->select('stime, mode, size, color, date, user_id, text')
            ->where(['card_id' => $card_id])
            ->orderBy('id DESC')
            ->limit(MSoundComment::DM_MAX_NUM)
            ->asArray()
            ->all();
        return $this->renderPartial('dm', [
            'dm' => $dm
        ]);
    }

    /**
     * @api {post} /voice/send-dm 发送弹幕
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/send-dm
     * @apiSampleRequest voice/send-dm
     *
     * @apiVersion 0.1.0
     * @apiName send-dm
     * @apiGroup voice
     *
     * @apiParam {Number} card_id 卡片 ID
     * @apiParam {Number} stime 弹幕出现时间
     * @apiParam {Number} text 弹幕内容
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "发送成功"
     *     }
     */
    public function actionSendDm()
    {
        $card_id = (int)Yii::$app->request->post('card_id');
        $stime = (int)Yii::$app->request->post('stime');
        $text = trim(Yii::$app->request->post('text'));
        $user_id = Yii::$app->user->id;

        if (!$text) throw new HttpException(400, '弹幕不可为空');
        if (!Yii::$app->user->isBindMobile) {
            throw new HttpException(403, '绑定手机就可以发送弹幕了哦', 100010008);
        }
        if (!($card = Card::find()->select('special, price, work_id')->where(['id' => $card_id])->one())) {
            throw new HttpException(404, '未找到该卡片');
        }

        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_FORBIDDEN_COMMENT_ELEMENT, SoundComment::TYPE_VOICE_CARD);
        if ($redis->sIsMember($key, $card_id)) {
            throw new HttpException(403, '本语音禁止发弹幕');
        }
        $hot_value = Hot::getHot($card->work_id);
        if (Card::SPECIAL_HOTCARD === $card->special && $hot_value < $card->price) {
            throw new HttpException(403, '该卡片还未开启，请等待');
        }
        if (!in_array($card->special, [Card::SPECIAL_FREE, Card::SPECIAL_HOTCARD])
                && !GetCard::findOne(['card_id' => $card_id, 'user_id' => $user_id])) {
            throw new HttpException(403, '您还未获得此卡，请先去兑换或购买');
        }
        $counter = $redis->generateKey(KEY_VOICE_DANMUKU_LIMIT_COUNTER, $user_id);
        if (!$redis->counter($counter, ONE_DAY, Danmaku::ONE_DAY_LIMIT)) {
            throw new HttpException(403, '今日弹幕额度已用完');
        }

        $dm = new Danmaku;
        $dm->card_id = $card_id;
        $dm->user_id = $user_id;
        $dm->stime = $stime;
        $dm->text = $text;
        if (!$dm->save()) {
            throw new HttpException(400, MUtils::getFirstError($dm));
        }
        return '发送成功';
    }

    /**
     * @api {post} /voice/buy-package 购买季包
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/buy-package
     * @apiSampleRequest voice/buy-package
     *
     * @apiVersion 0.1.0
     * @apiName buy-package
     * @apiGroup voice
     *
     * @apiParam {Number} card_package_id 季包 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Number} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "恭喜你已成功解锁叶修语音包"
     *     }
     */
    public function actionBuyPackage()
    {
        $card_package_id = (int)Yii::$app->request->post('card_package_id');
        $user_id = Yii::$app->user->id;
        if (!$card_package = CardPackage::findOne($card_package_id)) {
            throw new HttpException(404, '未找到该季包');
        }
        $role = Role::findOne($card_package->role_id);
        $work = Work::findOne($card_package->work_id);

        $form = new TransactionForm(['scenario' => TransactionForm::SCENARIO_BOYFRIEND]);
        $form->gift_id = $card_package_id;
        $form->from_id = $user_id;
        // 购买季包时消费记录中的文案示例：全职高手语音包--叶修--第一季语音
        $form->title = $work->title . '语音包--' . $role->name . '--'
            . ($card_package->title ?: CardPackage::SEASON_NAMES[$card_package->season]) . '语音';
        $form->price = $card_package->price;
        $form->work_id = $work->id;
        $form->num = $card_package->season;
        if ($form->validate() && $form->buyPackage()) {
            GetCard::buySeasonCards($card_package_id, $user_id);
            Card::insertFestivalCards($work->id, $user_id, $role->id);
            // 更新用户贡献的热度值
            Hot::updateUserHot($work->id, $user_id, Hot::HOT_BUY);
            return '成功解锁' . $role->name . CardPackage::SEASON_NAMES[$card_package->season] . '语音包';
        } else {
            throw new HttpException(400, MUtils::getFirstError($form));
        }
    }

    /**
     * @api {get} /voice/draw-pages 用户抽卡界面
     * @apiDescription 此接口用于获取某作品下全部已上线季度的抽卡页面信息
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/draw-pages
     * @apiSampleRequest voice/draw-pages
     *
     * @apiVersion 0.1.0
     * @apiName draw-page
     * @apiGroup voice
     *
     * @apiParam {Number=1} work_id 作品 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "work_id": 1,
     *         "coupon": 240,
     *         "balance": 840,
     *         "seasons": [
     *           {
     *             "season": 2,
     *             "subject": "第二季",
     *             "is_free": false,
     *             "next_free_time": 134556,
     *             "one_draw_price": 10,
     *             "ten_draw_price": 100,
     *             "banner": "http://www.test.com/image/201710/24/22f770fc6c91b1222603.jpg",
     *             "draw_type": 1  // 抽卡支持的类型（按位运算：1 位代表单抽，2 位代表 10 连抽）
     *           },
     *           {
     *             "season": 1,
     *             "subject": "第一季",
     *             "is_free": false,
     *             "next_free_time": 134556,
     *             "one_draw_price": 10,
     *             "ten_draw_price": 100,
     *             "banner": "http://www.test.com/image/201710/24/22f770fc6c91b1222603.jpg",
     *             "draw_type": 2
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionDrawPages()
    {
        $work_id = (int)Yii::$app->request->get('work_id');
        if (!$work = Work::getWork($work_id, Work::TYPE_VOICE)) {
            throw new HttpException(404, '未找到该作品');
        }
        $user_id = (int)Yii::$app->user->id;
        $coupon = $balance = 0;
        if ($user_id) {
            $coupon = WorkUserInfo::getCoupon($user_id, $work_id);
            $balance = Balance::getByPk($user_id)->getTotalBalance();
        }
        // 获取已上线季度
        $seasons = Work::getSeasons($work_id);
        $is_new_user = WorkUserInfo::isNewUser($user_id, $work);
        $free_draw_expires = WorkUserInfo::FreeDrawExpires($work_id, $user_id);
        $seasons_info = [];
        foreach ($seasons as $season) {
            $seasons_info[] = $this->getDrawPageInfo($work_id, $season, $is_new_user, $free_draw_expires);
        }
        return [
            'work_id' => $work_id,
            'coupon' => $coupon,
            'balance' => $balance,
            'seasons' => $seasons_info
        ];
    }

    private function getDrawPageInfo(int $work_id, int $season, bool $is_new_user, int $free_draw_expires)
    {
        $is_free = true;
        $next_free_time = 0;
        $has_free_drew = !in_array($free_draw_expires,
            [WorkUserInfo::HAS_FREE_DRAW_CHANCE, WorkUserInfo::FREE_DRAW_NOT_LOGGED]);
        if ($is_new_user && !$has_free_drew) {
            $one_draw_package = LotteryPackage::findByMark(LotteryPackage::MARK_FREE_DRAW, $work_id, $season);
        } elseif ($has_free_drew) {
            // 当天已经免费抽了，得到收费卡包
            $one_draw_package = LotteryPackage::findByMark(LotteryPackage::MARK_ONE_DRAW, $work_id, $season, false);
            $is_free = false;
            $next_free_time = $free_draw_expires > 0 ? $free_draw_expires - time() : 0;
        } else {
            // 当天还未免费抽，得到免费或者付费卡包
            $one_draw_package = LotteryPackage::findByMark(LotteryPackage::MARK_ONE_DRAW, $work_id, $season);
            if ($one_draw_package && $one_draw_package->price !== 0) {
                $is_free = false;
            }
        }
        if (!$one_draw_package) {
            throw new HttpException(404, '单抽卡包好像有些问题呢~');
        }
        // 获取 Banner 图
        $banner = Work::getWorkPic($work_id, Work::TYPE_DRAWPAGE_BANNER, $season);
        $return = [
            'season' => $season,
            'subject' => $one_draw_package->getSeasonTitle(),
            'is_free' => $is_free,
            'next_free_time' => $next_free_time,
            'one_draw_price' => $one_draw_package->price,
            'banner' => $banner,
            'draw_type' => 1,  // 抽卡支持的类型（按位运算：1 位代表单抽，2 位代表 10 连抽）
        ];
        if ($ten_draw_package = LotteryPackage::findByMark(LotteryPackage::MARK_TEN_DRAW, $work_id, $season)) {
            $return['ten_draw_price'] = $ten_draw_package->price;
            $return['draw_type'] |= 2;
        }
        return $return;
    }

    /**
     * @api {post} /voice/exchange 兑换卡片
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/exchange
     * @apiSampleRequest voice/exchange
     *
     * @apiVersion 0.1.0
     * @apiName exchange
     * @apiGroup voice
     *
     * @apiParam {Number} card_id 卡片 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 1,
     *         "title": "小峰",
     *         "cover": "http://test.com/files/2018-04-24/364d57473075f9e368ad2f3304c72d9e.jpg",
     *         "role": "叶修",
     *         "level": 1,
     *         "special": 0,
     *         "is_new": false
     *       }
     *     }
     */
    public function actionExchange()
    {
        $card_id = (int)Yii::$app->request->post('card_id');
        if (!$card_id) {
            throw new HttpException(400, '没有告诉 M 娘要兑换哪个语音呢~');
        }
        $user_id = Yii::$app->user->id;
        $card = Card::find()->where(['id' => $card_id, 'is_online' => Card::ONLINE])->one();
        if (!$card) {
            throw new HttpException(404, '本语音已下架或不存在 T_T');
        }
        // 判断卡片是否可以兑换
        if ($card->special !== Card::SPECIAL_NORMAL) {
            throw new HttpException(403, '本语音不可被兑换 T_T');
        }
        if ($card->price <= 0) {
            // 兑换价格需要大于 0 才可兑换
            throw new HttpException(403, '很抱歉，本语音暂时不可被兑换 T_T');
        }
        // 判断卡片是否已拥有
        if (GetCard::find()->where(['card_id' => $card->id, 'user_id' => $user_id])->exists()) {
            throw new HttpException(403, '已获得本语音~');
        }
        // 判断经验值是否足够兑换
        $coupon = WorkUserInfo::getCoupon($user_id, $card->work_id);
        if ($coupon < $card->price) {
            throw new HttpException(403, Work::getCouponName($card->work_id) . '不足嘤嘤嘤 T_T');
        }
        GetCard::exchangeCard($card, $user_id);
        $return = [
            'id' => $card->id,
            'title' => $card->title,
            'cover' => $card->cover,
            'role' =>  $card->isMultiRole() ? $card->intro : current(Role::getRolesName([$card->role_id])),
            'level' => $card->level,
            'special' => $card->special,
            'is_new' => true
        ];
        return $return;
    }

    /**
     * @api {get} /voice/exchange-info 兑换待推送的卡片的弹窗规则
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/exchange-info
     * @apiSampleRequest voice/exchange-info
     *
     * @apiVersion 0.1.0
     * @apiName exchange-info
     * @apiGroup voice
     *
     * @apiParam {Number} card_id 卡片 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "tip": "<div style=\"color: #3d3d3d\">\n <p><b>·</b>为了更真</p>\n</div>\n",
     *         "price": 20
     *       }
     *     }
     */
    public function actionExchangeInfo()
    {
        $card_id = (int)Yii::$app->request->get('card_id');
        $card = Card::find()->select('work_id, title, price')->where(['id' => $card_id])->one();

        if (!$card) throw new HttpException(404, '未找到该卡片');
        return [
            'tip' => $this->renderPartial('exchangeinfo', ['work_id' => $card->work_id, 'title' => $card->title]),
            'price' => ceil($card->price * GetCard::UNLOCK_CARD_DISCOUNT),
        ];
    }

    /**
     * @api {post} /voice/unlock 兑换解锁卡片
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/unlock
     * @apiSampleRequest voice/unlock
     *
     * @apiVersion 0.1.0
     * @apiName unlock
     * @apiGroup voice
     *
     * @apiDescription 此处行为虽然为解锁，但提示文案仍然定义为“兑换”
     *
     * @apiParam {Number} card_id 卡片 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "兑换成功~"
     *     }
     */
    public function actionUnlock()
    {
        $card_id = (int)Yii::$app->request->post('card_id');
        if (!$card_id) {
            throw new HttpException(400, '没有告诉 M 娘要兑换哪个语音呢~');
        }
        $user_id = Yii::$app->user->id;
        $card = Card::find()->where(['id' => $card_id, 'is_online' => Card::ONLINE])->one();
        if (!$card) {
            throw new HttpException(404, '本语音已下架或不存在 T_T');
        }
        // 判断卡片是否可以兑换
        if ($card->special !== Card::SPECIAL_NORMAL || $card->price <= 0) {
            throw new HttpException(403, '本语音不可被兑换 T_T');
        }
        $user_card = GetCard::find()->where(['card_id' => $card->id, 'user_id' => $user_id])->one();
        // 判断卡片是否已拥有
        if (!$user_card) {
            throw new HttpException(403, '还未获得该语音，不可提前兑换解锁哦~');
        }
        // 判断卡片是否可解锁
        if ($user_card->status !== GetCard::STATUS_LOCK) {
            throw new HttpException(403, '只有待推送的卡片才能提前解锁 T_T');
        }
        // 判断经验值是否足够兑换
        $need_coupon = ceil($card->price * GetCard::UNLOCK_CARD_DISCOUNT);
        $user_coupon = WorkUserInfo::getCoupon($user_id, $card->work_id);
        if ($user_coupon < $need_coupon) {
            throw new HttpException(403, Work::getCouponName($card->work_id) . '不足嘤嘤嘤 T_T');
        }
        $card->unlock_price = $need_coupon;
        GetCard::unlockCard($card, $user_id);
        return '兑换成功~';
    }

    /**
     * @api {post} /voice/get-episode-card 领取小剧场卡片
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/get-episode-card
     * @apiSampleRequest voice/get-episode-card
     *
     * @apiVersion 0.1.0
     * @apiName get-episode-card
     * @apiGroup voice
     *
     * @apiParam {Number} card_id 卡片 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 1,
     *         "title": "荣耀全明星",
     *         "role": "荣耀全员",
     *         "cover": "https://test.com/files/2018-04-24/364d57473075f9e368ad2f3304c72d9e.jpg",
     *         "intro": "卡片介绍",
     *         "level": 0,
     *         "special": 2,
     *         "is_new": false
     *       }
     *     }
     */
    public function actionGetEpisodeCard()
    {
        $card_id = (int)Yii::$app->request->post('card_id');
        if (!$card_id) {
            throw new HttpException(400, '没有告诉 M 娘要领取哪个语音呢~');
        }
        $user_id = Yii::$app->user->id;
        $card = Card::find()->where(['id' => $card_id, 'is_online' => Card::ONLINE])->one();
        if (!$card) {
            throw new HttpException(404, '本语音已下架或不存在 T_T');
        }
        // 判断卡片是否为小剧场卡
        if ($card->special !== Card::SPECIAL_EPISODE) {
            throw new HttpException(403, '只能领取小剧场卡片哦 T_T');
        }
        // card 表中当卡片为小剧场时字段 card_package_id 存储所属季度
        $season = $card->card_package_id;
        // 查找用户已拥有的小剧场卡
        $user_episode_cards = GetCard::find()
            ->where([
                'work_id' => $card->work_id,
                'user_id' => $user_id,
                'role_id' => 0,
                'card_package_id' => $season,  // get_card 表中当卡片为小剧场时字段 card_package_id 存储所属季度
                'special' => Card::SPECIAL_EPISODE,
            ])->all();
        $user_episode_card_ids = [];
        if ($user_episode_cards) {
            $user_episode_card_ids = array_column($user_episode_cards, 'card_id');
            if (in_array($card->id, $user_episode_card_ids)) {
                throw new HttpException(403, '已获得本语音~');
            }
        }
        // 判断充值的钻石是否满足兑换条件
        $diamonds = TransactionLog::getVoiceExpense($card->work_id, $user_id, $season);
        $user_cards_num = count($user_episode_cards);
        // 可领取的卡片数
        $get_num = intdiv($diamonds, GetCard::UNLOCK_EPISODE_COIN_LIMIT) - $user_cards_num;
        if ($get_num <= 0) {
            throw new HttpException(403, '还没满足解锁条件嘤嘤嘤');
        }
        // 获取可以领取的卡片
        $episode_cards = Card::find()
            ->where([
                'work_id' => $card->work_id,
                'special' => Card::SPECIAL_EPISODE,
                'is_online' => Card::ONLINE,
                'card_package_id' => $season,  // card 表中当卡片为小剧场时字段 card_package_id 存储所属季度
            ])
            ->orderBy('rank ASC')
            ->all();
        if (!$episode_cards) {
            throw new HttpException(404, '暂无可领取的剧场卡 T_T');
        }
        // 获取能够领取的卡片 ID
        $can_get_card_ids = [];
        foreach ($episode_cards as $key => $episode_card) {
            if (!in_array($episode_card->id, $user_episode_card_ids)) {
                $can_get_card_ids[] = $episode_card->id;
                if (count($can_get_card_ids) === $get_num) break;
            }
        }
        if (!in_array($card->id, $can_get_card_ids)) {
            throw new HttpException(404, '这张语音卡还不能领取哦~');
        }
        // 领取卡片
        GetCard::getEpisodeCard($card, $user_id);
        $return = [
            'id' => $card->id,
            'title' => $card->title,
            'role' => Card::EPISODE_CARD_NAME[Work::ID_QUANZHI],
            'cover' => $card->cover,
            'intro' => $card->intro,
            'level' => $card->level,
            'special' => $card->special,
            'is_new' => true
        ];
        return $return;

    }

    /**
     * @api {post} /voice/draw-card 单次抽卡接口
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/draw-card
     * @apiSampleRequest voice/draw-card
     *
     * @apiVersion 0.1.0
     * @apiName draw-card
     * @apiGroup voice
     *
     * @apiParam {Number} work_id 作品 ID
     * @apiParam {number=0,1} [free=0] free 是否为免费抽
     * @apiParam {Number} [season=1] 季度
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 1,
     *         "title": "教你游戏",
     *         "cover": "http://test.com/files/2018-04-24/364d57473075f9e368ad2f3304c72d9e.jpg",
     *         "role": "叶修",
     *         "level": 1,
     *         "special": 0,
     *         "is_new": false,
     *         "given_coupon": 6
     *       }
     *     }
     */
    public function actionDrawCard()
    {
        $user_id = Yii::$app->user->id;
        $work_id = (int)Yii::$app->request->post('work_id');
        $season = (int)Yii::$app->request->post('season', 1);
        $free = ((int)Yii::$app->request->post('free', 0)) !== 0;
        if (!$work = Work::getWork($work_id, Work::TYPE_VOICE)) {
            throw new HttpException(404, '未找到该作品');
        }

        $has_free_drew = WorkUserInfo::hasFreeDrew($user_id, $work_id);
        if ($free && $has_free_drew) {
            throw new HttpException(403, '今日免费抽卡次数已用尽 T_T');
        }
        $lottery_package = LotteryPackage::findByMark(LotteryPackage::MARK_ONE_DRAW, $work_id, $season, $free);
        if (!$lottery_package) {
            throw new HttpException(404, '卡包已下架或不存在 T_T');
        }
        // 用户是否为新用户
        $is_new_user = WorkUserInfo::isNewUser($user_id, $work);
        if ($free && $is_new_user && !$has_free_drew && $lottery_package->price > 0) {
            // 当用户为当天未抽卡的新用户并且不存在免费活动卡包时，使用新人卡包
            $lottery_package = LotteryPackage::findByMark(LotteryPackage::MARK_FREE_DRAW, $work_id, $season);
            if (!$lottery_package) {
                throw new HttpException(404, '新人卡包已下架或不存在 T_T');
            }
        }

        if ($free && $lottery_package->price !== 0 && !$is_new_user) {
            throw new HttpException(403, '免费抽卡活动已过期 T_T');
        }
        // 使用免费抽奖次数或钻石获取抽卡机会
        $draw_chance = WorkUserInfo::getDrawChance($user_id, $lottery_package, $season, $free);
        if (!$draw_chance) {
            $info = $free ? '今日免费抽卡次数已用尽 T_T' : '钻石余额不足';
            throw new HttpException(403, $info);
        }
        // 抽取卡片
        $card = current(GetCard::drawCards($lottery_package->id, $user_id));

        // 更新用户贡献的热度值
        Hot::updateUserHot($work_id, $user_id, Hot::HOT_ONE_DRAW);
        $return = [
            'id' => $card->id,
            'title' => $card->title,
            'cover' => $card->cover,
            'role' => $card->isMultiRole() ? $card->intro : current(Role::getRolesName([$card->role_id])),
            'level' => $card->level,
            'special' => $card->special,
            'given_coupon' => $card->given_coupon,
            'is_new' => $card->is_new
        ];
        return $return;
    }

    /**
     * @api {post} /voice/draw-ten-cards 十连抽卡接口
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/draw-ten-cards
     * @apiSampleRequest voice/draw-ten-cards
     *
     * @apiVersion 0.1.0
     * @apiName draw-ten-cards
     * @apiGroup voice
     *
     * @apiParam {Number} work_id 作品 ID
     * @apiParam {Number} [season=1] 季度
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "id": 1,
     *           "title": "小峰",
     *           "cover": "http://test.com/files/2018-04-24/364d57473075f9e368ad2f3304c72d9e.jpg",
     *           "role": "叶修",
     *           "level": 1,
     *           "special": 0,
     *           "is_new": false,
     *           "given_coupon": 6
     *         },
     *         {
     *           "id": 2,
     *           "title": "小峰2",
     *           "cover": "http://test.com/files/2018-04-24/364d57473075f9e368ad2f3304c72d9e.jpg",
     *           "role": "叶修",
     *           "level": 1,
     *           "special": 0,
     *           "is_new": false,
     *           "given_coupon": 6
     *         },
     *         ...
     *       ]
     *     }
     */
    public function actionDrawTenCards()
    {
        $work_id = (int)Yii::$app->request->post('work_id');
        $season = (int)Yii::$app->request->post('season', 1);
        $user_id = Yii::$app->user->id;
        if (!Work::checkWork($work_id)) throw new HttpException(404, '未找到该作品');

        // 获取卡包
        $lottery_package = LotteryPackage::findByMark(LotteryPackage::MARK_TEN_DRAW, $work_id, $season);
        if (!$lottery_package) {
            throw new HttpException(403, '卡包已下架或不存在 T_T');
        }
        // 获取抽卡机会（花费钻石或免费抽奖次数）
        $draw_chance = WorkUserInfo::getDrawChance($user_id, $lottery_package, $season);
        if (!$draw_chance) {
            throw new HttpException(403, '钻石余额不足');
        }
        // 抽卡
        $cards = GetCard::drawCards($lottery_package->id, $user_id);

        $card_ids = array_column($cards, 'role_id');
        // 更新用户贡献的热度值
        Hot::updateUserHot($work_id, $user_id, Hot::HOT_TEN_DRAW);
        $role_names = Role::getRolesName($card_ids);
        $return = array_map(function ($card) use ($role_names) {
            $card_arr = [
                'id' => $card->id,
                'title' => $card->title,
                'cover' => $card->cover,
                'role' => $card->isMultiRole() ? $card->intro : $role_names[$card->role_id],
                'level' => $card->level,
                'special' => $card->special,
                'given_coupon' => $card->given_coupon,
                'is_new' => $card->is_new
            ];
            return $card_arr;
        }, $cards);
        return $return;
    }

    /**
     * @api {get} /voice/get-skin 获取皮肤包
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/voice/get-skin
     * @apiSampleRequest voice/get-skin
     *
     * @apiVersion 0.1.0
     * @apiName get-skin
     * @apiGroup voice
     *
     * @apiParam {Number} work_id 作品 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "url": "http://static.missevan.com/voice/work/1/skin/2019-08-06/os-2-3x-5d4950471bdda/skin.zip"
     *       }
     *     }
     */
    public function actionGetSkin(int $work_id, ?string $resolution = null)
    {
        return [
            'url' => SkinPackage::getSkinPackage($work_id, Yii::$app->equip->getOs(), $resolution),
        ];
    }

}
