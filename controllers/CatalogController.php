<?php

namespace app\controllers;

use app\components\util\MUtils;
use app\models\Catalog;
use app\middlewares\Controller;
use app\models\Drama;
use app\models\MPersonaModuleElement;
use app\models\MSound;
use app\models\ReturnModel;
use app\models\YouMightLikeModule;
use Yii;
use yii\web\HttpException;

class CatalogController extends Controller
{

    public function behaviors()
    {
        $behaviors = parent::behaviors();
        return $behaviors;
    }

    /**
     * @api {get} /catalog/sound-homepage{?catalog_id} 音频分区首页推荐
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/catalog/sound-homepage
     *
     * @apiVersion 0.1.0
     * @apiName sound-homepage
     * @apiGroup catalog
     *
     * @apiParam {Number} catalog_id 分区 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *    HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "catalog_name": "铃声",
     *         "hot_recommend": {
     *           "title": "热门推荐",
     *           "elements": [
     *             {
     *               "id": 120,
     *               "title": "不用担心~",
     *               "view_count": 731,
     *               "duration": 2510,
     *               "front_cover": "http://static-test.missevan.com/coversmini/201701/24/test.png",
     *               "video": true  // 绑定了视频返回 true, 未绑定不返回
     *             }
     *           ]
     *         },
     *         "classic_album": {
     *           "title": "精选音单",
     *           "module_id": 1,
     *           "elements": [
     *             {
     *               "id": 154187,
     *               "title": "测试音单",
     *               "intro": "测试音单",
     *               "front_cover": "http://static-test.missevan.com/coversmini/nocover.png",
     *               "music_count": 0,
     *               "view_count": 0,
     *               "sort": 0,
     *               "module_id": 2614
     *             }
     *           ]
     *         },
     *         "recommend_up": {
     *           "title": "推荐 UP 主",
     *           "elements": [
     *             {
     *               "user_id": 2,
     *               "avatar": "http://static-test.missevan.com/avatars/201706/14/test.jpg",
     *               "username": "大用户_2",
     *               "soundurl": "http://static-test.missevan.com/sound/201202/08/test.m4a",
     *               "soundurl_64": "http://static-test.missevan.com/MP3/201202/08/test.m4a",
     *               "soundurl_128": ""
     *             }
     *           ]
     *         },
     *         "weekly_rank": {
     *           "title": "人气周榜",
     *           "ranks": [
     *             {
     *               "name": "来电",
     *               "catalog_id": 66,
     *               "elements": [
     *                 {
     *                   "id": 110,
     *                   "title": "皮卡皮~皮卡丘~",
     *                   "view_count": 3911,
     *                   "duration": 2190,
     *                   "front_cover": "http://static-test.missevan.com/coversmini/201701/24/test.png"
     *                 },
     *                 {
     *                   "id": 100,
     *                   "title": "来我这里吧",
     *                   "view_count": 3911,
     *                   "duration": 2190,
     *                   "front_cover": "http://static-test.missevan.com/coversmini/201701/24/test.png"
     *                 }
     *               ]
     *             }
     *           ]
     *         },
     *         "banners": [
     *           {
     *             "sort": 0,
     *             "url": "http://www.missevan.com/sound/1788057",
     *             "pic_app": "https://static-test.missevan.com/image/recommend_elements/202003/10/test.png"
     *           }
     *         ],
     *         "extra_banners": {
     *           "1": [
     *             {
     *               "url": "missevan://omikuji/draw",
     *               "pic": "https://static-test.missevan.com/image/recommend_elements/202003/10/test.png",
     *               "sort": 0
     *             },
     *           ]
     *         },
     *         "hot_words": ["加特林", "支付宝", "余额宝"]
     *       }
     *     }
     */
    public function actionSoundHomepage(int $catalog_id)
    {
        // 当前仅支持音频分区
        if (!in_array($catalog_id, Catalog::UGC_CATALOG_IDS)) {
            throw new HttpException(404, '分类不存在');
        }
        return Catalog::getSoundHomepage($catalog_id);
    }

    /**
     * @api {get} /catalog/weekly-rank{?catalog_id,page,page_size} 人气周榜
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/catalog/weekly-rank
     * @apiDescription 人气周榜页面加载好之后显示对应 active 为 true 的 tabs
     * @apiVersion 0.1.0
     * @apiName weekly-rank
     * @apiGroup catalog
     *
     * @apiParam {Number} catalog_id 子分类 ID
     * @apiParam {Number} [page=1] 当前页数
     * @apiParam {Number} [page_size=20] 每页个数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "tabs": [  // 人气周榜只显示这个接口返回的 tabs
     *           {
     *             "catalog_id": 66,
     *             "name": "来电",
     *             "active": true
     *           },
     *           {
     *             "catalog_id": 67,
     *             "name": "短信"
     *           }
     *         ],
     *         "Datas": [
     *           {
     *             "id": 22602,
     *             "title": "金克拉是我的",
     *             "front_cover": "http://static.missevan.com/mimages/201711/15/92eddc64e7121500.jpg",
     *             "duration": 2333,
     *             "view_count": 333279,
     *             "video": true  // 绑定了视频返回 true, 未绑定不返回
     *           }
     *         ],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 1,
     *           "count": 1,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     */
    public function actionWeeklyRank(int $catalog_id, int $page = 1, int $page_size = PAGE_SIZE_20)
    {
        if (!in_array($catalog_id, Catalog::getSoundCatalogIds())) {
            throw new HttpException(404, '分类不存在');
        }
        return Catalog::getWeeklyRank($catalog_id, $page, $page_size);
    }

    /**
     * @api {get} /catalog/get-hot-sounds{?catalog_id,page,page_size} 全区动态
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/catalog/get-hot-sounds
     * @apiVersion 0.1.0
     * @apiName get-hot-sounds
     * @apiGroup catalog
     *
     * @apiParam {Number} catalog_id 子分类 ID
     * @apiParam {Number} [page=1] 当前页数
     * @apiParam {Number} [page_size=18] 每页个数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [
     *           {
     *             "id": 22602,
     *             "title": "金坷拉是我的",
     *             "front_cover": "http://static.missevan.com/mimages/201711/15/92eddc64e7121500.jpg",
     *             "all_comments": 2333,
     *             "view_count": 333279
     *           }
     *         ],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 1,
     *           "count": 1,
     *           "pagesize": 18
     *         }
     *       }
     *     }
     */
    public function actionGetHotSounds(int $catalog_id, int $page = 1, int $page_size = PAGE_SIZE_18)
    {
        if (!in_array($catalog_id, Catalog::UGC_CATALOG_IDS)) {
            throw new HttpException(404, '分类不存在');
        }
        // 音频总量较少且使用了缓存，取出来之后再进行分页处理
        $catalog_ids = [$catalog_id];
        $sub_catalogs = Catalog::lite($catalog_id);
        if (!empty($sub_catalogs) && is_array($sub_catalogs)) {
            $catalog_ids = array_column($sub_catalogs, 'id');
        }
        // WORKAROUND: 如果是催眠，则显示自然音和白噪音
        if (in_array($catalog_id, [Catalog::CATALOG_ID_ASMR, Catalog::CATALOG_ID_ASMR_CN, Catalog::CATALOG_ID_ASMR_OVERSEAS,
            Catalog::CATALOG_ID_ASMR_GIRL])) {
            $catalog_ids = [Catalog::CATALOG_ID_SOUND_NATURAL, Catalog::CATALOG_ID_ASMR_NOVOICE];
        }
        // 分区动态总量仅需 90 个
        $COUNT = 90;
        $all_sounds = MSound::getSoundsByCat(0, $catalog_ids, $COUNT, $page);
        $sounds = [];
        $offset = ($page - 1) * $page_size;
        $all_count = count($all_sounds);
        if ($all_count >= $offset) {
            $sounds = array_slice($all_sounds, $offset, $page_size);
            $sounds = array_map(function ($sound) {
                return [
                    'id' => $sound['id'],
                    'title' => $sound['soundstr'],
                    'all_comments' => $sound['all_comments'],
                    'view_count' => $sound['view_count'],
                    'front_cover' => $sound['front_cover'],
                ];
            }, $sounds);
        }
        return ReturnModel::getPaginationData($sounds, $all_count, $page, $page_size);
    }

    /**
     * @api {get} /catalog/recommend-modules{?catalog_id} 音频分区自定义模块
     *
     * @apiVersion 0.1.0
     * @apiName recommend-modules
     * @apiGroup catalog
     *
     * @apiParam {Number} catalog_id 分区 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "id": 8,
     *           "sort": 1,
     *           "title": "音单模块标题",
     *           "type": 1,  // 模块类型，1：音单；2：剧集；3：音频
     *           "style": 0,  // 排版方式，0：竖版；1：横版；2：排行榜；3：滑动
     *           "more": {  // 不下发或为空时跳转到默认的原生自定义模块详情页
     *             "url": "https://test.com/aaa?foo=bar"  // 更多按钮跳转链接，不下发或为空时跳转到默认的原生自定义模块详情页
     *           },
     *           "elements": [
     *             {
     *               "id": 142732,
     *               "title": "标题",
     *               "intro": "简介",
     *               "front_cover": "http://static.missevan.com/mimages/201711/15/92eddc64e7121500.jpg",
     *               "type": 1,
     *               "music_count": 16,
     *               "view_count": 135718,
     *               "sort": 1
     *             }
     *           ]
     *         },
     *         {
     *           "id": 2114,
     *           "sort": 3,
     *           "title": "剧集模块标题",
     *           "type": 2,
     *           "style": 0,
     *           "more": {
     *             "url": "https://test.com/aaa?foo=bar"  // 更多按钮跳转链接，不下发或为空时跳转到默认的原生自定义模块详情页
     *           },
     *           "elements": [
     *             {
     *               "id": 28,  // 剧集 ID
     *               "name": "剧集标题",
     *               "abstract": "剧集简介",
     *               "cover_color": 0,  // 背景图主颜色，十进制表示，在模块样式为堆叠式时使用
     *               "integrity": 2,  // 完结度 1：长篇未完结；2：长篇完结；3：全一期；4：微小剧
     *               "newest": "第五期",  // 更新至
     *               "pay_type": 1,  // 付费类型，0：免费；1：单集付费；2：整剧付费
     *               "view_count": 21926,  // 播放次数
     *               "outline": null,
     *               "front_cover": "http://static.missevan.com/dramacoversmini/201604/15/test.jpg",
     *               "need_pay": 1,  // 付费状态，0：免费；1：付费剧集未付费；2：付费剧集已付费
     *               "sort": 0,
     *               "module_id": 2114
     *             }
     *           ]
     *         },
     *         {
     *           "id": 2122,
     *           "sort": 1,
     *           "title": "音频模块标题",
     *           "type": 3,
     *           "style": 1,
     *           "more": {
     *             "url": "https://test.com/aaa?foo=bar"  // 更多按钮跳转链接，不下发或为空时跳转到默认的原生自定义模块详情页
     *           },
     *           "elements": [
     *             {
     *               "id": 5621487,  // 音频 ID
     *               "front_cover": "http://static-test.maoercdn.com/coversmini/201906/10/test.jpg",  // 音频封面
     *               "soundstr": "铁血加特林",  // 音频标题
     *               "intro": "这是一句话简介，请在模块后台设置",
     *               "view_count": 233,  // 播放次数
     *               "comment_count": 0,  // 弹幕数量，模块详情页使用
     *               "all_comments": 233,  // 总评论数量，模块推荐位使用
     *               "sort": 0,
     *               "module_id": 2151,
     *               "user_id": 234,  // UP 主的用户 ID
     *               "username": "UP 主的用户名",  // UP 主的用户名
     *               "video": true  // 绑定了视频返回 true, 未绑定不返回
     *             }
     *           ]
     *         }
     *       ]
     *     }
     */
    public function actionRecommendModules(int $catalog_id)
    {
        if (!in_array($catalog_id, Catalog::getSoundCatalogIds())) {
            throw new HttpException(404, '分类不存在');
        }
        return Catalog::getRecommendModules($catalog_id);
    }
}
