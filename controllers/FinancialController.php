<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/8/6
 * Time: 13:17
 */

namespace app\controllers;

use app\components\base\filter\AccessControl;
use app\components\util\Equipment;
use app\components\util\MUtils;
use app\forms\RechargeForm;
use app\middlewares\Controller;
use app\models\AdTrack;
use app\models\Balance;
use app\models\TopupMenu;
use app\models\IosReceipt;
use app\models\MTopNotification;
use app\models\MTopNotificationLog;
use app\models\PayMethod;
use app\models\RechargeOrder;
use app\models\StatisticDownloadId;
use app\models\TransactionLog;
use app\forms\UserContext;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\filters\VerbFilter;
use yii\web\HttpException;

class FinancialController extends Controller
{

    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'balance' => ['get'],
                'create-alipay-order' => ['post'],
                'create-google-pay-order' => ['post'],
                'create-ios-order' => ['post'],
                'create-paypal-order' => ['post'],
                'create-qqpay-order' => ['post'],
                'create-wechat-order' => ['post'],
                'get-recharges' => ['get'],
                'get-topup-history' => ['get'],
                'ios-recharge' => ['post'],
                'purchase-detail' => ['get'],
                'purchase-history' => ['get'],
                'recharge-detail' => ['get'],
                'topup-detail' => ['get'],
                'verify-google-pay-order' => ['post'],
                'verify-ios-order' => ['post'],
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'only' => [
                'balance',
                'create-alipay-order',
                'create-google-pay-order',
                'create-ios-order',
                'create-paypal-order',
                'create-qqpay-order',
                'create-wechat-order',
                'get-recharges',
                'get-topup-history',
                'ios-recharge',
                'purchase-detail',
                'purchase-history',
                'recharge-detail',
                'topup-detail',
                'verify-google-pay-order',
                'verify-ios-order',
            ],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => [
                        'balance',
                        'create-alipay-order',
                        'create-google-pay-order',
                        'create-ios-order',
                        'create-paypal-order',
                        'create-qqpay-order',
                        'create-wechat-order',
                        'get-recharges',
                        'get-topup-history',
                        'ios-recharge',
                        'purchase-detail',
                        'purchase-history',
                        'recharge-detail',
                        'topup-detail',
                        'verify-google-pay-order',
                        'verify-ios-order',
                    ],
                    'roles' => ['@']
                ],
            ]
        ];
        return $behaviors;
    }

    /**
     * @api {post} /financial/create-alipay-order 创建支付宝订单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/financial/create-alipay-order
     * @apiSampleRequest financial/create-alipay-order
     * @apiName create-alipay-order
     * @apiGroup financial
     *
     * @apiParam {Number} cid 充值类型 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "order": {
     *           "uid": 346286,
     *           "ctime": 1553221651,
     *           "cid": 8,
     *           "price": "18.00",
     *           "ccy": 1,
     *           "num": 180,
     *           "status": 0,
     *           "type": 4,
     *           "id": 54128
     *         },
     *         "orderString": {
     *           "appId": "1103599281",
     *           "bargainorId": "1529373471",
     *           "nonce": "9c93db4a0fb414fe50192700952714d2",
     *           "pubAcc": "",
     *           "sig": "E90D2AA84E98B748A98543BDCB58CCB7",
     *           "timeStamp": 1553221651
     *         }
     *       }
     *     }
     */
    public function actionCreateAlipayOrder()
    {
        $cid = (int)Yii::$app->request->post('cid');

        $user_id = (int)Yii::$app->user->id;
        $equip = Yii::$app->equip;

        if (!$equip->isAndroidOrHarmonyOS()) {
            throw new HttpException(400, '暂不支持该设备创建订单', 200010002);
        }
        $order = RechargeOrder::generateAlipayOrder($cid, $user_id, RechargeOrder::ORIGIN_APP,
            UserContext::fromUser(Yii::$app->request, $equip));
        return $order;
    }

    /**
     * @api {post} /financial/create-wechat-order 创建微信支付订单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/financial/create-wechat-order
     * @apiSampleRequest financial/create-wechat-order
     * @apiName create-wechat-order
     * @apiGroup financial
     *
     * @apiParam {Number} cid 充值类型 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "order": {
     *           "uid": 346286,
     *           "ctime": 1540793286,
     *           "cid": 20,
     *           "price": "0.1",
     *           "ccy": 1,
     *           "num": 1,
     *           "status": 0,
     *           "type": 2,
     *           "id": 1443051
     *         },
     *         "orderString": {
     *           "appid": "wx143bd0e4a3b523cf",
     *           "noncestr": "umIsOxffZ100HpbrE2POmc4MArVZIS",
     *           "package": "Sign=WXPay",
     *           "partnerid": "1482967912",
     *           "prepayid": "wx291408069667805baec1c0fa3046573997",
     *           "timestamp": 1540793286,
     *           "sign": "F57D2F4247DB619631AB1FDF97506CB3"
     *         }
     *       }
     *     }
     */
    public function actionCreateWechatOrder()
    {
        $cid = (int)Yii::$app->request->post('cid');

        $user_id = (int)Yii::$app->user->id;
        $equip = Yii::$app->equip;

        if (!$equip->isAndroidOrHarmonyOS()) {
            throw new HttpException(400, '暂不支持该设备创建订单', 200010002);
        }
        $order = RechargeOrder::generateWechatpayOrder($cid, $user_id, RechargeOrder::ORIGIN_APP,
            UserContext::fromUser(Yii::$app->request, $equip));
        return $order;
    }

    /**
     * @api {post} /financial/create-qqpay-order 创建 QQ 支付订单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/financial/create-qqpay-order
     * @apiSampleRequest financial/create-qqpay-order
     * @apiName create-qqpay-order
     * @apiGroup financial
     *
     * @apiParam {Number} cid 充值类型 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "order": {
     *           "uid": 346286,
     *           "ctime": 1553221651,
     *           "cid": 8,
     *           "price": "18.00",
     *           "ccy": 1,
     *           "num": 180,
     *           "status": 0,
     *           "type": 4,
     *           "id": 54128
     *         },
     *         "orderString": {
     *           "appId": "1103599281",
     *           "bargainorId": "1529373471",
     *           "nonce": "9c93db4a0fb414fe50192700952714d2",
     *           "pubAcc": "",
     *           "sig": "E90D2AA84E98B748A98543BDCB58CCB7",
     *           "timeStamp": 1553221651
     *         }
     *       }
     *     }
     */
    public function actionCreateQqpayOrder()
    {
        $cid = (int)Yii::$app->request->post('cid');

        $user_id = (int)Yii::$app->user->id;
        $equip = Yii::$app->equip;

        if (!$equip->isAndroidOrHarmonyOS()) {
            throw new HttpException(400, '暂不支持该设备创建订单', 200010002);
        }
        $order = RechargeOrder::generateQQPayOrder($cid, $user_id, RechargeOrder::ORIGIN_APP,
            UserContext::fromUser(Yii::$app->request, $equip));

        return $order;
    }

    /**
     * @api {post} /financial/create-paypal-order 创建 paypal 支付订单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/financial/create-paypal-order
     * @apiSampleRequest financial/create-paypal-order
     * @apiDescription 充值后的跳转地址客户端需要解析 URL 中的参数 \
     * 例 ?out_trade_no=xxx&action=return 或 ?out_trade_no=xxx&action=cancel&type=xx 需要解析出 action 的值 \
     * 进行返回或取消的处理
     *
     * @apiName create-paypal-order
     * @apiGroup financial
     *
     * @apiParam {Number} cid 充值类型 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "order": {
     *           "uid": 346286,
     *           "ctime": 1554961024,
     *           "cid": 7,
     *           "price": "600.00",
     *           "ccy": 1,
     *           "num": 60,
     *           "status": 0,
     *           "type": 5,
     *           "origin": 2,
     *           "id": 54224
     *         },
     *         "form": "<form id="payform" name="payform">...</form>"
     *       }
     *     }
     */
    public function actionCreatePaypalOrder()
    {
        $cid = (int)Yii::$app->request->post('cid');

        $user_id = (int)Yii::$app->user->id;
        $equip = Yii::$app->equip;

        if (!$equip->isAndroidOrHarmonyOS()) {
            throw new HttpException(400, '暂不支持该设备创建订单', 200010002);
        }
        $order = RechargeOrder::generatePayPalOrder($cid, $user_id, RechargeOrder::ORIGIN_APP,
            UserContext::fromUser(Yii::$app->request, $equip));

        return $order;
    }

    /**
     * @api {post} /financial/verify-ios-order{?retry} 验证 iOS 充值接口
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/financial/verify-ios-order
     * @apiName verify-ios-order
     * @apiGroup financial
     *
     * @apiParam {number=0,1,2} [retry=0] 重试标记，手动重试为 1，自动重试为 2，此参数为 Query 里的参数
     * @apiParam {String} transaction_id 交易凭证 ID
     * @apiParam {String} receipt 收据
     * @apiParam {String} [idfv] 苹果设备的 IDFV
     * @apiParam {String} order_id 后台生成的订单号
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Number} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": 50
     *     }
     */
    public function actionVerifyIosOrder(int $retry = 0)
    {
        if (Equipment::isAppOlderThan('4.2.4')) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }
        $order_id = trim(Yii::$app->request->post('order_id'));
        if (!$order_id && !$retry) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        $recharge = new RechargeForm(['scenario' => RechargeForm::SCENARIO_IOS]);
        if ($recharge->load(Yii::$app->request->post(), '')
            && $recharge->validate()
        ) {
            $user_id = Yii::$app->user->id;
            if ($recharge->isSuspicious) {
                // 可疑充值，假装充值成功
                $buyer = Balance::getByPk($user_id);
                return $buyer->getTotalBalance() + $recharge->ccy->num;
            }
            try {
                // 新版本 iOS 4.2.4 及其以后采取先创建订单充值后更新订单方式
                // 由于充值后更新订单时可能传递过来的 order_id 参数丢失，则在丢失的情况沿用老的方式（充值后创建并更新订单）补充订单
                // 正常未丢失 order_id 参数情况，则更新先前所创建的订单
                if ($order_id) {
                    $real_order_id = RechargeOrder::getRealOrderId($order_id);
                    $result = RechargeOrder::updateIOSOrder($recharge->ccy, $recharge->transaction_id, $user_id, $real_order_id);
                } else {
                    $result = RechargeOrder::generateIOSOrder($recharge->ccy, $recharge->transaction_id, $user_id,
                        $recharge->getReceiptBody(), UserContext::fromUser(Yii::$app->request, Yii::$app->equip));
                }
                $recharge->setLogStatus(IosReceipt::STATUS_SUCCESS);
                AdTrack::callbackPay($user_id, $recharge->ccy->price);
                return $result;
            } catch (\Exception $e) {
                $recharge->setLogStatus(IosReceipt::STATUS_FAILED);
                throw $e;
            }
        } else {
            $errors = $recharge->getErrors();
            if (array_key_exists('in_app', $errors)) {
                throw new HttpException(400, current($errors['in_app']), 200360007);
            }
            // WORKAROUND: 旧版本 iOS 返回的错误信息结构为 ["xxx" => ["xxxxx", "xxxx"]]，新版本直接返回第一个错误信息字符串
            if (Equipment::isAppOlderThan('6.0.7', null)) {
                return $errors;
            }

            throw new HttpException(400, MUtils2::getFirstError($recharge));
        }
    }

    /*
    * MissEvanApp iOS 旧版使用，新版调整为上述的 /financial/verify-ios-order{?retry}
    */
    public function actionIosRecharge(int $retry = 0)
    {
        return $this->actionVerifyIosOrder($retry);
    }

    /**
     * @api {get} /financial/get-topup-history 获取充值记录
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/financial/get-topup-history
     * @apiSampleRequest financial/get-topup-history
     * @apiName get-topup-history
     * @apiGroup financial
     *
     * @apiParam {Number} [page=1] 页数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object[]} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [
     *           {
     *             "id": "15767414120030000001703",  // 充值订单号
     *             "title": "活动奖励",  // 充值订单名称
     *             "status_msg": "发放成功",  // 充值订单状态
     *             "uid": 2985440,  // 充值用户 ID
     *             "tid": "GPA.**************-13544",  // 充值订单号，iOS 或 Android Google Pay 用于比对本地的订单以显示是否重试
     *             "price": "120.00",  // 充值金额。单位：元
     *             "num": 120,  // 充值钻石数量
     *             "status": 1,  // 充值状态。1：成功；-1：已退款
     *             "type": 8,  // 充值平台。0：苹果充值；1：支付宝；2：微信充值；3：现金；4：QQ 钱包；5：天猫（iOS）；6：天猫（安卓）
     *                         // 7：PayPal；8：Google Pay；9：iOS 补单；10：公对公；11：抖店；12：京东；13：B站大额支付；14：点播会员领取福利钻石
     *             "ctime": 1581042196  // 创建时间戳。单位：秒
     *           },
     *           {
     *             "id": "15799257060050009460977",
     *             "title": "购买钻石",
     *             "status_msg": "购买成功",
     *             "uid": 4207964,
     *             "tid": "828331682396121227",
     *             "price": "180.00",
     *             "num": 2020,
     *             "status": 1,
     *             "type": 5,
     *             "ctime": 1579925706
     *           }
     *         ],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 1,
     *           "count": 21,
     *           "pagesize": 10
     *         }
     *       }
     *     }
     */
    public function actionGetTopupHistory()
    {
        $user_id = Yii::$app->user->id;
        $page = (int)Yii::$app->request->get('page', 1);

        $list = RechargeOrder::getList(
            $user_id,
            [RechargeOrder::STATUS_SUCCESS, RechargeOrder::STATUS_CANCELED],
            $page
        );
        // WORKAROUND: 新版客户端显示的订单号为 id 字段，兼容使用 tid 的安卓旧版
        if (Equipment::isAppOlderThan(null, '5.3.5')) {
            foreach ($list->Datas as $item) {
                $item->tid = $item->id;
            }
        }
        if (Equipment::isMiMiAppOlderThan(null, '1.0.6')) {
            foreach ($list->Datas as $item) {
                $item->tid = $item->id;
                $item->id = RechargeOrder::getRealOrderId($item->id);
            }
        }
        return $list;
    }

    /*
    * MissEvanApp iOS 旧版使用，新版调整为上述的 /financial/get-topup-history
    */
    public function actionGetRecharges()
    {
        return $this->actionGetTopupHistory();
    }

    /**
     * @api {get} /financial/topup-detail 获取充值记录详情
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/financial/topup-detail
     * @apiSampleRequest financial/topup-detail
     * @apiName topup-detail
     * @apiGroup financial
     *
     * @apiParam {String} order_id 交易记录 ID
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": "15767414120030000001703",
     *         "title": "活动奖励",  // 充值订单名称
     *         "status_msg": "发放成功",  // 充值订单状态
     *         "uid": 2985440,
     *         "tid": "GPA.**************-13544",
     *         "ctime": 1581042196,
     *         "price": "120",
     *         "num": 120,
     *         "status": 1,
     *         "type": 8
     *       }
     *     }
     */
    public function actionTopupDetail()
    {
        $uid = Yii::$app->user->id;
        $order_id = trim(Yii::$app->request->get('order_id'));
        // NOTICE: 该方法可兼容旧版本传的数字的形式
        // 例: "15767414120030000001703" 或 1703 都可得到 1703
        $order_id = RechargeOrder::getRealOrderId($order_id);
        // WORKAROUND: MissEvan 安卓旧版及 MiMi 双端充值后调用该接口判断是否充值成功，需要 id 为整型，新版调整为字符型
        if (Equipment::isAppOlderThan(null, '5.3.5')
                || Equipment::isMiMiAppOlderThan('1.0.4', '1.0.5')) {
            $order = RechargeOrder::findOne(['uid' => $uid, 'id' => $order_id]);
        } else {
            $order = RechargeOrder::getOutOrder(['uid' => $uid, 'id' => $order_id]);
        }
        if (!$order) {
            throw new HttpException(404, Yii::t('app/error', 'Order not found'));
        }
        $order_detail = RechargeOrder::queryDetail($order_id);
        if ($order_detail) {
            $order->more = $order_detail->more;
            $order->setOrderTitleAndStatusMsg();
            unset($order->more);
        }
        return $order;
    }

    /*
    * MissEvanApp iOS 旧版使用，新版调整为上述的 /financial/recharge-detail
    */
    public function actionRechargeDetail()
    {
        return $this->actionTopupDetail();
    }

    /**
     * @api {get} /financial/purchase-history 购买历史
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/financial/purchase-history
     * @apiSampleRequest financial/purchase-history
     * @apiName purchase-history
     * @apiGroup financial
     *
     * @apiParam {Number} [page=1] 页数
     * @apiParam {number=0,1,2} [type=0] 类型（0 剧集，1 直播，2 其他）
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [{
     *           "transaction_id": "15767414120030000001703",
     *           "detail": "礼物--喵头虎脑 × 1",
     *           "status_msg": "购买成功",
     *           "coin": 336,
     *           "confirm_time": 1646901420
     *         }],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 1,
     *           "count": 28,
     *           "pagesize": 30
     *         },
     *         "tabs": [{
     *           "title": "剧集",
     *           "type": 0
     *         },
     *         {
     *           "title": "直播",
     *           "type": 1,
     *           "no_more_tip": "仅展示最近三个月记录"
     *         },
     *         {
     *           "title": "其他",
     *           "type": 2
     *         }]
     *       }
     *     }
     */
    public function actionPurchaseHistory(int $type = 0, int $page = 1)
    {
        $user_id = Yii::$app->user->id;
        // WORKAROUND: 新版本返回新的消费记录历史，兼容旧版本
        if (Equipment::isAppOlderThan('4.8.1', '5.6.9')) {
            return TransactionLog::purchaseHistory($user_id, TransactionLog::TYPE_ALL, TransactionLog::STATUS_SUCCESS, $page);
        }

        switch ($type) {
            case 0:
                $resp = TransactionLog::newDramaPurchaseHistory($user_id, $page, DEFAULT_PAGE_SIZE);
                break;
            case 1:
                $resp = TransactionLog::newLivePurchaseHistory($user_id, $page, DEFAULT_PAGE_SIZE);
                break;
            case 2:
                $resp = TransactionLog::newOtherPurchaseHistory($user_id, $page, DEFAULT_PAGE_SIZE);
                break;
            default:
                throw new HttpException(400, '参数错误');
        }

        $resp->tabs = [
            [
                'title' => '剧集',
                'type' => 0,
            ],
            [
                'title' => '直播',
                'type' => 1,
                'no_more_tip' => '仅展示最近三个月记录',
            ],
            [
                'title' => '其他',
                'type' => 2,
            ],
        ];
        unset($resp->has_more);

        return (array)$resp;
    }

    /**
     * @api {get} /financial/purchase-detail 获取消费详情
     * @apiName purchase-detail
     * @apiGroup financial
     *
     * @apiParam {Number} user_id 用户 ID
     * @apiParam {String} transaction_id 交易记录 ID
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "transaction_id": "16527622590010001566889",
     *         "detail": "礼物--福气满满（十全十美） × 1",
     *         "status_msg": "购买成功",
     *         "coin": 0,  // 普通钻石消耗
     *         "live_noble_balance_cost": 60,  // 贵族钻石消耗
     *         "confirm_time": 1652762259,
     *         "fields": [
     *           ["受益主播", "我的力量无人能及"],
     *           ["所在直播间", "我的力量无人能及"]
     *         ]
     *       }
     *     }
     */
    public function actionPurchaseDetail()
    {
        $user_id = Yii::$app->user->id;

        // WORKAROUND: 新版本返回新的消费记录详情，兼容旧版本
        if (Equipment::isAppOlderThan('4.8.1', '5.6.9')) {
            $tid = trim(Yii::$app->request->get('tid'));
            $tid = TransactionLog::getRealId($tid);
            return TransactionLog::purchaseDetail($tid, $user_id);
        }

        $transaction_id = trim(Yii::$app->request->get('transaction_id'));
        $tid = TransactionLog::getRealId($transaction_id);
        $detail = TransactionLog::newPurchaseDetail($tid, $user_id);
        // WORKAROUND: 新版本以 fields 字来显示各行对应内容
        if (!Equipment::isAppOlderThan('4.9.1', '5.7.6')) {
            unset($detail['creator_username']);
        }
        return $detail;
    }

    /**
     * @api {get} /financial/balance 充值余额或主播收益
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/financial/balance
     * @apiSampleRequest financial/balance
     * @apiName balance
     * @apiGroup financial
     *
     * @apiParam {Number} [type=1] 1 为充值余额，2 为直播收益
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "code": 0,
     *       "info": {
     *         "balance": 60,
     *         "live_noble_balance": 60,
     *         "live_noble_balance_status": 1
     *       }
     *     }
     */
    public function actionBalance(int $type = Balance::BALANCE_TYPE_TOPUP)
    {
        $user_id = Yii::$app->user->id;
        $user = Balance::getByPk($user_id);
        return $user->getBalanceDetail($type, true);
    }

    /**
     * @api {get} /financial/topup-menu 钻石余额、钻石价目及消息提示
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/financial/topup-menu
     * @apiSampleRequest financial/topup-menu
     * @apiDescription 需持久化保存上次请求的结果（避免网络问题而无法显示该充值列表，可请求时以服务端最新数据为准）
     *
     * @apiName topup-menu
     * @apiGroup financial
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code Error code
     * @apiSuccess {Object} info Result of request
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "tip": {
     *           "msg": "近期出现了大量第三方代充猫耳 FM 钻石的行为。目前猫耳 FM...",
     *           "url": "",
     *           "bg_color" : "#fff0d9",
     *           "font_color": "#fcb651"
     *         },
     *         "guidance": "<div style='color: #cccccc'><p>1. 购买钻石成功后无法退款，不可提现</p>....</div>",  // 客户端需要支持 HTML 格式（目前只有 iOS 会下发）
     *         "menu": [
     *           {
     *             "id": 1,
     *             "num": 66,
     *             "price": "6.00",
     *             "product_id": "com.missevan.CatEarFM0001",
     *             "corner_mark": "首充加赠 10%",  // 右上角角标
     *             "original_num": 60  // 原来的钻石数
     *           },
     *           {
     *             "id": 2,
     *             "num": 180,
     *             "price": "18.00",
     *             "product_id": "com.missevan.CatEarFM0002"
     *           },
     *           {
     *             "id": 3,
     *             "num": 500,
     *             "price": "50.00",
     *             "product_id": "com.missevan.CatEarFM0003"
     *           },
     *           {
     *             "id": 4,
     *             "num": 1280,
     *             "price": "128.00",
     *             "product_id": "com.missevan.CatEarFM0004"
     *           },
     *           {
     *             "id": 5,
     *             "num": 2580,
     *             "price": "258.00",
     *             "product_id": "com.missevan.CatEarFM0005"
     *           },
     *           {
     *             "id": 6,
     *             "num": 5180,
     *             "price": "518.00",
     *             "product_id": "com.missevan.CatEarFM0006"
     *           }
     *         ],
     *         "payment_method": [
     *           {
     *             "icon": "https://xxx.png",
     *             "title": "支付宝",
     *             "checked": 1  // 选中状态,
     *             "tip": "",  // 选中后的提示信息
     *             "disabled": false,  // 是否可用
     *             "type": "alipay"
     *           },
     *           {
     *             "icon": "https://xxx.png",
     *             "title": "微信支付",
     *             "checked": 0,
     *             "tip": "",
     *             "disabled": true,
     *             "type": "wechatpay"
     *           },
     *           {
     *             "icon": "https://xxx.png",
     *             "title": "PayPal",
     *             "checked": 0,
     *             "tip": "PayPal 海外支付向每单交易收取 $0.3 交易费",
     *             "disabled": false,
     *             "type": "paypal"
     *           }
     *         ],
     *         "trade_agreement": "https://link.missevan.com/rule/diamond-agreement",
     *         "default_position": 3,
     *         "top_banner": {  // 仅 iOS 客户端下发，无通知时不下发该字段
     *           "title": "关注猫耳FM公众号有惊喜",
     *           "subtitle": "资讯不迷路，听剧更省心",
     *           "button_text": "立即前往",
     *           "link": "https://m.uat.missevan.com/app_notification",
     *           "icon_url": "https://test/test.jpg"
     *         }
     *       }
     *     }
     */
    public function actionTopupMenu()
    {
        $client = Yii::$app->equip->getOs();
        $redis = Yii::$app->redis;
        $tip_key = $redis->generateKey(KEY_TOPUP_TIP, $client);
        $tip = json_decode($redis->get($tip_key), true) ?: null;
        if (Equipment::isBanVersion()) {
            $tip = [
                'msg' => '当前 App 版本过低，请更新至最新版本，保障您的充值安全',
                'url' => '',
                'bg_color' => '#FFEFEF',
                'font_color' => '#EC6262',
            ];
        }

        $user = Yii::$app->user;
        $is_new_user = false;
        if (!$user->isGuest) {
            $balance = Balance::getByPk($user->id);
            $is_new_user = $balance->hasNewUserTopupDiscount($user)
                && !Equipment::isAppOlderThan('6.0.4', '6.0.4');
        }

        $is_google_play_new_version = Equipment::isFromGoogleChannel()
            && !Equipment::isAppOlderThan(null, '6.0.1');
        switch ($client) {
            case Equipment::Android:
            case Equipment::HarmonyOS:
                $ccy_device = TopupMenu::DEVICE_ANDROID;
                if ($is_google_play_new_version) {
                    $ccy_device = TopupMenu::DEVICE_GOOGLE_PLAY;
                }
                $coin_list = TopupMenu::getCoinPrice($ccy_device, TopupMenu::getScope($is_new_user));
                break;
            case Equipment::iOS:
                $coin_list = TopupMenu::getCoinPrice(Equipment::iOS, TopupMenu::getScope($is_new_user));
                break;
            default:
                throw new HttpException(403, '不支持设备');
        }
        $menu = [
            'tip' => $tip,
            'menu' => $coin_list,
            'trade_agreement' => Yii::$app->params['help_links']['trade_agreement'],
        ];
        if (Yii::$app->equip->isIOS()) {
            $menu['guidance'] = Yii::$app->params['ios_topup_guidance'];
        }
        if (Equipment::isFromGoogleChannel()) {
            // 没有单独配置 Google 渠道的链接，先简单拼接后缀
            $menu['trade_agreement'] .= '-gp';
        }
        if (Yii::$app->equip->isAndroidOrHarmonyOS()) {
            $excluded_types = [];
            // 中国大陆地区不显示 PayPal
            if (MUtils::isChinaMainland()) {
                $excluded_types[] = RechargeOrder::TOPUP_METHOD_PAYPAL;
            }
            // 猫耳概念版暂时不对 QQ 钱包进行支持
            if (Equipment::isConceptVersion()) {
                $excluded_types[] = RechargeOrder::TOPUP_METHOD_QQPAY;
            }
            // 旧版本不显示 Google Pay 支付方式
            if (!$is_google_play_new_version) {
                $excluded_types[] = RechargeOrder::TOPUP_METHOD_GOOGLEPAY;
            }
            // 谷歌旧版本不显示微信支付方式
            if (Equipment::isFromGoogleChannel() && Equipment::isAppOlderThan(null, '5.6.9')) {
                $excluded_types[] = RechargeOrder::TOPUP_METHOD_WECHATPAY;
            }

            $menu['payment_method'] = PayMethod::getAndroidPayMethod($excluded_types, $is_google_play_new_version);
        }

        $menu['default_position'] = TopupMenu::DEFAULT_POSITION;
        foreach ($coin_list as $i => $item) {
            /**
             * @var TopupMenu $item
             */
            if ($item->checked) {
                $menu['default_position'] = $i;
                unset($item->checked);
                break;
            }
        }
        if ($user->id && Yii::$app->equip->isIOS() && !Equipment::isAppOlderThan('6.3.3', null)) {
            $top_banner = MTopNotificationLog::addLogAndSendSysMsg(MTopNotification::IOS_TOPUP_MENU, (int)$user->id);
            if ($top_banner) {
                $menu['top_banner'] = $top_banner;
            }
        }
        return $menu;
    }

    /**
     * @api {post} /financial/create-ios-order 创建 iOS 充值订单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/financial/create-ios-order
     * @apiSampleRequest financial/create-ios-order
     * @apiName create-ios-order
     * @apiGroup financial
     *
     * @apiParam {Number} cid 钻石价目表 ccy 中的主键
     * @apiParam {String} idfv 苹果设备的 IDFV
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code Error code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "dev15398330260010000054056"
     *     }
     */
    public function actionCreateIosOrder()
    {
        $cid = (int)Yii::$app->request->post('cid');
        if (!trim(Yii::$app->request->post('idfv'))) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }

        $user_id = (int)Yii::$app->user->id;
        $equip = Yii::$app->equip;

        if (!$equip->isIOS()) {
            throw new HttpException(400, Yii::t('app/error', 'This kind of device is not supported to create order'), 200010002);
        }
        if (!StatisticDownloadId::checkDownloadID($user_id)) {
            if ($equip->isFromMiMiApp()) {
                throw new HttpException(403, 'https://link.mimifm.jp/help/topup-blocked-ios', 100010013);
            }
            throw new HttpException(403, 'https://link.missevan.com/help/topup-blocked-ios', 100010013);
        }
        $order_id = RechargeOrder::generateIOSOrderNew($cid, $user_id,
            UserContext::fromUser(Yii::$app->request, $equip));
        return $order_id;
    }

    /**
     * @api {post} /financial/create-google-pay-order 创建 Google Pay 充值订单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/financial/create-google-pay-order
     * @apiSampleRequest financial/create-google-pay-order
     * @apiName create-google-pay-order
     * @apiGroup financial
     *
     * @apiParam {Number} cid 钻石价目表 ccy 中的主键
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code Error code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "dev15398330260010000054056"
     *     }
     */
    public function actionCreateGooglePayOrder()
    {
        $cid = (int)Yii::$app->request->post('cid');

        $user_id = (int)Yii::$app->user->id;
        $equip = Yii::$app->equip;

        if (!$equip->isAndroid()) {
            throw new HttpException(400, '暂不支持该设备创建订单', 200010002);
        }
        $order = RechargeOrder::generateGooglePayOrder($cid, $user_id,
            UserContext::fromUser(Yii::$app->request, $equip));
        return $order;
    }

    /**
     * @api {post} /financial/verify-google-pay-order{?retry} 验证 Google Pay 充值订单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/financial/verify-google-pay-order
     * @apiSampleRequest financial/verify-google-pay-order
     * @apiName verify-google-pay-order
     * @apiGroup financial
     *
     * @apiParam {number=0,1,2} [retry=0] 重试标记，手动重试为 1，自动重试为 2，此参数为 Query 里的参数
     * @apiParam {String} purchase_token 支付后生成的 Token
     * @apiParam {String} purchase_id 商品 ID（例 jp.mimifm.app.item0007）
     * @apiParam {String} order_id 订单 ID（例 15398330260010000054056）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code Error code
     * @apiSuccess {Number} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "balance": 10,  // 用户钻石余额
     *         "status": 0,  // 状态：-1 失败（禁止重试），1 充值成功
     *         "error": ""  // 重试失败原因
     *       }
     *     }
     */
    public function actionVerifyGooglePayOrder(int $retry = 0)
    {
        $purchase_token = trim(Yii::$app->request->post('purchase_token'));
        $purchase_id = trim(Yii::$app->request->post('purchase_id'));
        $out_trade_no = trim(Yii::$app->request->post('order_id'));
        $user_id = Yii::$app->user->id;
        if (!$out_trade_no && !$retry) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }

        return RechargeOrder::verifyGooglePayOrder($user_id, $out_trade_no, $purchase_id, $purchase_token);
    }

}
