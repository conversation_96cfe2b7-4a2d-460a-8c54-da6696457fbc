<?php

namespace app\controllers;

use app\components\base\filter\AccessControl;
use app\components\util\Equipment;
use app\components\util\MUtils;
use app\middlewares\Controller;
use app\models\Balance;
use app\models\Card;
use app\models\Danmaku;
use app\models\GetCard;
use app\models\GetCardLog;
use app\models\LotteryPackage;
use app\models\SkinPackage;
use app\models\SoundComment;
use app\models\SoundCommentRO;
use app\models\TransactionLog;
use app\models\UserVoiceInfo;
use app\models\Work;
use app\models\WorkUserInfo;
use missevan\storage\StorageClient;
use Yii;
use yii\filters\VerbFilter;
use yii\web\HttpException;

/**
 * 封装求签语音相关 Api 接口
 *
 * <AUTHOR> <<EMAIL>>
 */
class OmikujiController extends Controller
{
    const INFO_TYPE_COUPON = 0;
    const INFO_TYPE_DRAW = 1;
    const INFO_TYPE_EPISODE = 2;
    const INFO_TYPE_WORK = 3;

    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'send-dm' => ['post'],
                'exchange' => ['post'],
                'draw-card' => ['post'],
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'only' => [
                'send-dm',
                'exchange',
                'draw-card',
                'play',
            ],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => [
                        'send-dm',
                        'exchange',
                        'draw-card',
                        'play',
                    ],
                    'roles' => ['@']
                ],
            ]
        ];
        return $behaviors;
    }

    /**
     * @api {get} /omikuji/get-works 获取所有求签作品基本信息
     *
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/omikuji/get-works
     * @apiSampleRequest omikuji/get-works
     *
     * @apiVersion 0.1.0
     * @apiName get-works
     * @apiGroup omikuji
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "advance": {
     *           "name": "敬请期待",
     *           "url": "https://static.missevan.com/image/works/201805/22/762233d2f45512.png",
     *         },
     *         "works": [
     *           {
     *             "id": 3,
     *             "title": "全职高手求签",
     *             "seasons": [
     *               {
     *                 "season": 1,
     *                 "subject": "第一季",
     *                 "banner": "https://static.missevan.com/image/works/201805/22/762f97d223333.png"
     *               }
     *             ]
     *           },
     *           {
     *             "id": 3,
     *             "title": "全职高手求签",
     *             "seasons": [
     *               {
     *                 "season": 1,
     *                 "subject": "第一季",
     *                 "banner": "https://static.missevan.com/image/works/201805/22/762f97d223333.png"
     *               }
     *             ]
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionGetWorks()
    {
        $omikuji_works = Work::getWorksByType(Work::TYPE_OMIKUJI);
        $work_ids = array_column($omikuji_works, 'id');

        $seasons = Work::getLotteryPackages($work_ids, Yii::$app->user->id);
        $work_seasons = MUtils::groupArray($seasons, 'work_id');
        $works = [];
        foreach ($omikuji_works as $work) {
            $banners = $work->drawpage_banner;
            $seasons = array_map(function ($season) use ($banners) {
                return [
                    'season' => $season['season'],
                    'subject' => Work::getSeasonSubject($season['season']),
                    'banner' => $banners[$season['season']] ?? '',  // 未设置求签页背景图时，返回空字符串
                ];
            }, ($work_seasons[$work->id] ?? []));
            if (!empty($seasons)) {
                // 作品下有季度上线时，认定作品上线
                $works[] = [
                    'id' => $work->id,
                    'title' => $work->title,
                    'seasons' => $seasons,
                ];
            }
        }
        $advance = null;  // 预告作品为 null 时客户端不显示
        $advance_banner_storage_url = Yii::$app->redis->hGet(KEY_OMIKUJI_COVER, 'advance');
        if ($advance_banner_storage_url) {
            $advance_banner = StorageClient::getFileUrl($advance_banner_storage_url);
            $advance = [
                'title' => '即将上线',  // 相关文案暂无改动需求，暂时写死
                'banner' => $advance_banner,
            ];
        }
        return [
            'works' => $works,
            'advance' => $advance,
        ];
    }

    /**
     * @api {get} /omikuji/season-info{?work_id,season} 用户求签界面季度（签筒）信息
     *
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/omikuji/season-info
     * @apiSampleRequest omikuji/season-info
     *
     * @apiVersion 0.1.0
     * @apiName season-info
     * @apiGroup omikuji
     *
     * @apiParam work_id 作品 ID
     * @apiParam season 季度
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "work_id": 1,
     *         "season": 1,
     *         "subject": "第一季",
     *         "not_listen_num": 1,
     *         "is_free": false,
     *         "next_free_time": 64299,
     *         "price": 25,
     *         "banner": "https://static.missevan.com/mimages/201810/31/108263638.png",
     *         "wait_unlock_card_num": 2
     *       }
     *     }
     */
    public function actionSeasonInfo(int $work_id, int $season)
    {
        $work = Work::find()
            ->select('id, icon, title, type')
            ->where('id = :work_id AND type = :type',
                [':work_id' => $work_id, ':type' => Work::TYPE_OMIKUJI])
            ->one();
        if (!$work) {
            throw new HttpException(404, '未找到该作品');
        }
        // 获取作品下季度 ID
        $user_id = (int)Yii::$app->user->id;
        $season_ids = Work::getOmikujiSeasons($work_id, $user_id);
        if (!in_array($season, $season_ids)) {
            // 若无季度，视为作品未上线
            throw new HttpException(404, '签筒还未上线，敬请期待~');
        }
        $is_new_user = WorkUserInfo::isNewUser($user_id, $work);
        $free_draw_expires = WorkUserInfo::FreeDrawExpires($work_id, $user_id);
        $seasons_info = $this->getDrawPageInfo($work_id, $season, $is_new_user, $free_draw_expires);
        // 获取剧场卡带解锁语音签数量
        // 该值用于客户端是否显示小剧场消息提醒的判断依据
        $wait_unlock_card_num = $this->getUnlockEpisodeNum($user_id, $work_id, $season);
        $seasons_info['wait_unlock_card_num'] = $wait_unlock_card_num;
        $seasons_info['work_id'] = $work_id;
        return $seasons_info;
    }

    /**
     * @api {get} /omikuji/get-user-info 获取用户钻石数和幸运点
     *
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/omikuji/get-user-info
     * @apiSampleRequest /omikuji/get-user-info
     *
     * @apiVersion 0.1.0
     * @apiName get-user-info
     * @apiGroup omikuji
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "coupon": 65,
     *         "balance": 141
     *       }
     *     }
     */
    public function actionGetUserInfo()
    {
        $user_id = (int)Yii::$app->user->id;
        $coupon = $balance = 0;
        if ($user_id) {
            $coupon = UserVoiceInfo::getOmikujiCoupon($user_id);
            $balance = Balance::getByPk($user_id)->getTotalBalance();
        }
        return [
            'coupon' => $coupon,
            'balance' => $balance,
        ];
    }

    /**
     * @api {post} /omikuji/draw-card 求签接口
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/omikuji/draw-card
     * @apiSampleRequest omikuji/draw-card
     *
     * @apiVersion 0.1.0
     * @apiName draw-card
     * @apiGroup omikuji
     *
     * @apiParam {Number} work_id 作品 ID
     * @apiParam {number=0,1} [free=0] free 是否为免费抽
     * @apiParam {Number} [season=1] 季度
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 233,
     *         "work_title": "全职高手求签",
     *         "title": "今日掉钱",
     *         "intro": "解签词",
     *         "cover": "https://static.missevan.com/covers/201903/01/e7bf92fc2151502.jpg",
     *         "level": 2,  // 1：N；2：R；3：SR；4：SSR；
     *         "blessing": "凶",
     *         "special": 5,  // 5：求签普通卡；
     *         "given_coupon": 10,
     *         "is_new": false
     *       }
     *     }
     */
    public function actionDrawCard()
    {
        $work_id = (int)Yii::$app->request->post('work_id');
        $season = (int)Yii::$app->request->post('season', 1);
        $is_free = ((int)Yii::$app->request->post('free', 0)) !== 0;
        if ($work_id <= 0 || $season <= 0) {
            throw new HttpException(400, '参数错误');
        }
        $user_id = Yii::$app->user->id;
        if (!$work = Work::getWork($work_id, Work::TYPE_OMIKUJI)) {
            throw new HttpException(404, '作品不存在');
        }
        $lottery_package = LotteryPackage::getOmikujiPackage($work_id, $season, false);
        if (!$lottery_package) {
            throw new HttpException(404, '签筒不存在 T_T');
        }
        if ($lottery_package->price <= 0) {
            // 求签包价格设置不可小于等于 0，否则记录错误日志并且提示操作失败
            Yii::error("求签包（{$lottery_package->id}）设置的求签价格非法：{$lottery_package->price}");
            throw new HttpException(500, '操作失败');
        }
        if ($lottery_package->isExpired()) {
            throw new HttpException(403, '很抱歉，由于版权到期，您仅能收听已获得的语音签');
        }
        // 用户是否为新用户
        $is_new_user = WorkUserInfo::isNewUser($user_id, $work);
        if ($is_free && !$is_new_user) {
            // 若不为新用户想要进行免费抽，提示相关错误信息
            throw new HttpException(403, '免费求签活动已结束 T_T');
        }
        $has_free_drew = WorkUserInfo::hasFreeDrew($user_id, $work_id);
        if ($is_free && $has_free_drew) {
            throw new HttpException(403, '今日免费求签次数已用尽 T_T');
        }
        // 使用免费抽奖次数或钻石购买方式获取抽卡机会
        $draw_chance = WorkUserInfo::getDrawChance($user_id, $lottery_package, $season, $is_free);
        if (!$draw_chance) {
            $info = $is_free ? '今日免费求签次数已用尽 T_T' : '钻石余额不足';
            throw new HttpException(403, $info);
        }
        // 抽取卡片
        $card = GetCard::getOmikujiCard($lottery_package->id, $user_id);
        if (isset($draw_chance['transaction_id'])) {
            // 由于交易记录入库在抽卡之前，交易记录文案需记录所抽卡片信息，故抽卡后再更新交易记录文案
            // 交易记录文案格式：{IP 名称}运势语音 - {等级} {语音标题}
            $level_str = Card::LEVEL_STR[$card->level];
            $title = "{$work->title}运势语音 - $level_str {$card->title}";
            try {
                $res = TransactionLog::updateByPk($draw_chance['transaction_id'], ['title' => $title]);
                if (!$res) {
                    // 更新运势语音求签交易记录文案失败，只记录错误日志，需手动更新交易记录文案
                    Yii::error("更新运势语音求签交易记录文案失败，交易记录 ID：{$draw_chance['transaction_id']}，文案：{$title}",
                        __METHOD__);
                    // PASS
                }
            } catch (\Exception $e) {
                // 更新运势语音求签交易记录文案异常，只记录错误日志，需手动更新交易记录文案
                $message = $e->getMessage();
                Yii::error("更新运势语音求签交易记录文案异常，异常信息：{$message}，交易记录 ID：{$draw_chance['transaction_id']}，文案：{$title}",
                    __METHOD__);
                // PASS
            }
        }
        $return = [
            'work_title' => $work->title,
            'id' => $card->id,
            'title' => $card->title,
            'intro' => $card->intro,
            'cover' => $card->cover,
            'level' => $card->level,
            'blessing' => Card::getBlessingName($card->role_id),
            'special' => $card->special,
            'given_coupon' => $card->given_coupon,
            'is_new' => $card->is_new
        ];
        return $return;
    }

    /**
     * @api {get} /omikuji/episodes 小剧场页面
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/omikuji/episodes
     * @apiSampleRequest omikuji/episodes
     *
     * @apiVersion 0.1.0
     * @apiName episodes
     * @apiGroup omikuji
     *
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "banner": "http://static.missevan.com/mimages/201805/25/1220bb93b03759d911023.png",
     *         "coupon": 3115,
     *         "episodes": [{
     *           "work_id": 2,
     *           "work_name": "撒野",
     *           "seasons": [{
     *             "season": 1,
     *             "subject": "第一季",
     *             "cards": [{
     *               "id": 573,
     *               "title": "卡片名称",
     *               "intro": "9999",
     *               "price": 0,
     *               "is_online": 1,
     *               "card_package_id": 2,
     *               "status": 1  // 0 未解锁、1 为待兑换、2 未收听、3 已收听、4 下架
     *             },
     *             {
     *               "id": 569,
     *               "title": "卡片名称",
     *               "intro": "简介",
     *               "price": 0,
     *               "is_online": 1,
     *               "card_package_id": 2,
     *               "status": 1
     *             }]
     *           }]
     *         }
     *       }
     *     }
     */
    public function actionEpisodes()
    {
        $banner_storage_url = Yii::$app->redis->hGet(KEY_OMIKUJI_COVER, 'episode') ?: '';
        $banner = StorageClient::getFileUrl($banner_storage_url);
        $user_id = (int)Yii::$app->user->id;
        $coupon = UserVoiceInfo::getOmikujiCoupon($user_id);
        $episodes = GetCard::getOmikujiEpisodes($user_id, $coupon);
        return [
            'banner' => $banner,
            'coupon' => $coupon,
            'episodes' => $episodes,
        ];
    }

    /**
     * @api {post} /omikuji/exchange 兑换求签小剧场卡片
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/omikuji/exchange
     * @apiSampleRequest omikuji/exchange
     *
     * @apiVersion 0.1.0
     * @apiName exchange
     * @apiGroup omikuji
     *
     * @apiParam {Number} card_id 卡片 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 233,
     *         "work_title": "全职高手求签",
     *         "title": "今日掉钱",
     *         "intro": "解签词",
     *         "cover": "https://static.missevan.com/covers/201903/01/e7bf92fc2151502.jpg",
     *         "special": 6,  // 5：求签普通卡；6：求签小剧场
     *         "is_new": true,
     *       }
     *     }
     */
    public function actionExchange()
    {
        $card_id = (int)Yii::$app->request->post('card_id');
        if (!$card_id) {
            throw new HttpException(400, '没有告诉M娘要兑换哪个语音呢~');
        }
        $user_id = Yii::$app->user->id;
        $card = Card::find()
            ->alias('c')
            ->select('c.*, w.title AS work_title')
            ->leftJoin(Work::tableName() . ' AS w', 'c.work_id = w.id')
            ->where(['c.id' => $card_id, 'c.is_online' => Card::ONLINE])
            ->one();
        if (!$card) {
            throw new HttpException(404, '语音不存在 T_T');
        }
        // 判断卡片是否可以兑换
        if ($card->special !== Card::SPECIAL_OMIKUJI_EPISODE) {
            throw new HttpException(403, '本语音不可被兑换 T_T');
        }
        if ($card->price <= 0) {
            // 兑换价格需要大于 0 才可兑换
            throw new HttpException(403, '很抱歉，本语音暂时不可被兑换 T_T');
        }
        $package = LotteryPackage::find()
            ->where('id = :id AND :now_time >= start_time', [
                ':id' => $card->card_package_id,
                ':now_time' => $_SERVER['REQUEST_TIME'],
            ])
            ->one();
        if (!$package) {
            throw new HttpException(404, '语音不存在 T_T');
        }
        if ($package->isExpired()) {
            throw new HttpException(403, '很抱歉，由于版权到期，您仅能收听已获得的语音签');
        }
        // 获得作品上线的剧场卡 ID
        $work_card_ids = Card::find()
            ->select('id')
            ->where([
                'work_id' => $card->work_id,
                'card_package_id' => $package->id,
                'special' => Card::SPECIAL_OMIKUJI_EPISODE,
                'is_online' => Card::ONLINE
            ])
            ->orderBy('`rank` ASC')
            ->column();
        $user_episode_cards = GetCard::userCards($work_card_ids, $user_id);
        $user_episode_card_ids = array_column($user_episode_cards, 'card_id');
        if (in_array($card->id, $user_episode_card_ids)) {
            throw new HttpException(403, '已获得本语音~');
        }
        $unlock_num = count($user_episode_cards);
        $card_index = (int)array_search($card->id, $work_card_ids);
        $need_unlock_num = $card_index - $unlock_num;
        if ($need_unlock_num !== 0) {
            throw new HttpException(403, "需要解锁前 {$need_unlock_num} 个小剧场才可以唤醒我哦");
        }
        // 判断幸运点是否足够兑换
        $coupon = UserVoiceInfo::getOmikujiCoupon($user_id);
        if ($coupon < $card->price) {
            throw new HttpException(403, '幸运点不足，可通过求签获得幸运点哦');
        }
        GetCard::exchangeCard($card, $user_id, GetCardLog::TYPE_OMIKUJI_EPISODE);
        $return = [
            'work_title' => $card->work_title,
            'id' => $card->id,
            'title' => $card->title,
            'cover' => $card->cover,
            'intro' => $card->intro,
            'special' => $card->special,
            'is_new' => true,  // 小剧场卡仅可通过兑换方式获得，必为新卡
        ];
        return $return;
    }

    /**
     * @api {get} /omikuji/play{?card_id} 播放页
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/omikuji/play
     * @apiSampleRequest omikuji/play
     *
     * @apiVersion 0.1.0
     * @apiName play
     * @apiGroup omikuji
     *
     * @apiParam {Number} card_id 卡片 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 28,
     *         "title": "卡片",
     *         "intro": "简介",
     *         "level": 0,  // 0：无等级；1：N；2：R；3：SR；4：SSR；
     *         "special": 5,  // 5：求签普通卡；6：求签小剧场
     *         "comment_count": 23,
     *         "duration": 36000,
     *         "voice": "http://s2.missevan.com/audio/cards/201905/13/test.mp3?auth_key=test-key",  // 音频文件存在 m4a 及 mp3 两种格式，客户端都需要支持播放
     *         "play_cover": "http://static.missevan.com/coversmini/nocover.png",
     *         "subtitles": "http://s2.missevan.com/text/test.lrc?auth_key=test-key"
     *         "pics": [
     *           {
     *             "img_url": "http://static.missevan.com/coversmini/nocover.png",
     *             "stime": "12.2",
     *           },
     *           {
     *             "img_url": "http://static.missevan.com/coversmini/nocover.png",
     *             "stime": "26.9",
     *           }
     *         ],
     *         "watermark": "http://static.missevan.com/coversmini/nocover.png"
     *       }
     *     }
     */
    public function actionPlay(int $card_id)
    {
        $card = Card::findOne([
            'id' => $card_id,
            'special' => [Card::SPECIAL_OMIKUJI, Card::SPECIAL_OMIKUJI_EPISODE]
        ]);
        if (!$card) {
            throw new HttpException(404, '未找到该语音签');
        }
        $user_id = Yii::$app->user->id;
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_VOICE_WATERMARK, $card->work_id);
        $watermark = StorageClient::getFileUrl($redis->get($key) ?: Card::DEFAULT_WATERMARK);
        if (!($get_card = GetCard::findOne(['card_id' => $card_id, 'user_id' => $user_id]))) {
            throw new HttpException(403, '您还未获得此卡');
        }
        if (GetCard::STATUS_UNLOCK === $get_card->status) {
            // 修改语音收听状态
            GetCard::updateAll(['status' => GetCard::STATUS_LISTENED],
                ['card_id' => $card_id, 'user_id' => $user_id]);
        }
        // 获得评论数
        // TODO: 考虑在 card 表中新增评论数冗余字段来快速获取总评论数
        $comment_count = SoundCommentRO::find()
            ->where('c_type = :type AND element_id = :card_id',
                [':type' => SoundComment::TYPE_OMIKUJI_CARD, ':card_id' => $card_id])
            ->count();
        $sub_comment_count = SoundComment::getAllSubCommentNum(SoundComment::TYPE_OMIKUJI_CARD, $card_id);
        $blessing = $card->special === Card::SPECIAL_OMIKUJI ? Card::getBlessingName($card->role_id) : '小剧场';
        return [
            'id' => $card->id,
            'title' => $card->title,
            'intro' => $card->intro,
            'level' => $card->level,
            'blessing' => $blessing,
            'special' => $card->special,
            'duration' => $card->duration,
            'comment_count' => $comment_count + $sub_comment_count,
            'play_cover' => $card->play_cover,
            'voice' => Card::getSoundSignUrl($card->voice),
            'subtitles' => $card->subtitles,
            'pics' => $card->pics,
            'watermark' => $watermark,
        ];
    }

    /**
     * @api {get} /omikuji/dm{?card_id} 弹幕接口
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/omikuji/dm
     * @apiSampleRequest omikuji/dm
     *
     * @apiVersion 0.1.0
     * @apiName dm
     * @apiGroup omikuji
     *
     * @apiParam {Number} card_id 卡片 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><i>\r\n<d p='3,1,1,123,1234,3466'>12</d></i>\r\n"
     *     }
     */
    public function actionDm(int $card_id)
    {
        $dm = Danmaku::find()
            ->select('stime, mode, size, color, date, user_id, text')
            ->where(['card_id' => $card_id])
            ->asArray()
            ->all();
        return $this->renderPartial('/voice/dm', [
            'dm' => $dm
        ]);
    }

    /**
     * @api {post} /omikuji/send-dm 发送弹幕
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/omikuji/send-dm
     * @apiSampleRequest omikuji/send-dm
     *
     * @apiVersion 0.1.0
     * @apiName send-dm
     * @apiGroup omikuji
     *
     * @apiParam {Number} card_id 卡片 ID
     * @apiParam {Number} stime 弹幕出现时间
     * @apiParam {Number} text 弹幕内容
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "发送成功"
     *     }
     */
    public function actionSendDm()
    {
        $card_id = (int)Yii::$app->request->post('card_id');
        $stime = (int)Yii::$app->request->post('stime');
        $text = trim(Yii::$app->request->post('text'));
        $user_id = Yii::$app->user->id;
        if ($card_id <= 0 || $stime < 0) {
            throw new HttpException(400, '参数错误');
        }
        if (!$text) throw new HttpException(400, '弹幕不可为空');
        if (!Yii::$app->user->isBindMobile) {
            throw new HttpException(403, '绑定手机就可以发送弹幕了哦', 100010008);
        }
        if (!($card = Card::find()->select('special, price, work_id')->where(['id' => $card_id])->one())) {
            throw new HttpException(404, '未找到该语音');
        }

        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_FORBIDDEN_COMMENT_ELEMENT, SoundComment::TYPE_OMIKUJI_CARD);
        if ($redis->sIsMember($key, $card_id)) {
            throw new HttpException(403, '本语音禁止发弹幕');
        }
        $has_card = GetCard::find()->where(['card_id' => $card_id, 'user_id' => $user_id])->exists();
        if (!$has_card) {
            throw new HttpException(403, '您还未获得此语音');
        }
        $counter = $redis->generateKey(KEY_VOICE_DANMUKU_LIMIT_COUNTER, $user_id);
        if (!$redis->counter($counter, ONE_DAY, Danmaku::ONE_DAY_LIMIT)) {
            throw new HttpException(403, '今日弹幕额度已用完');
        }
        $dm = new Danmaku();
        $dm->card_id = $card_id;
        $dm->user_id = $user_id;
        $dm->stime = $stime;
        $dm->text = $text;
        if (!$dm->validate()) {
            throw new HttpException(400, MUtils::getFirstError($dm));
        }
        if ($dm->getIsShamSend()) {
            return '发送成功';
        }
        if (!$dm->save(false)) {
            throw new HttpException(400, MUtils::getFirstError($dm));
        }
        return '发送成功';
    }

    /**
     * @api {get} /omikuji/work-info{?work_id} 获取作品信息
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/omikuji/work-info
     * @apiSampleRequest omikuji/work-info
     *
     * @apiVersion 0.1.0
     * @apiName work-info
     * @apiGroup omikuji
     *
     * @apiParam {Number} work_id 作品 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 1,
     *         "title": "撒野",
     *         "banner": "https://static.missevan.com/covers/201903/01/e7bf92fc2151502.jpg",
     *         "season": [
     *           {
     *             "season": 1,
     *             "subject": "第一季"
     *           },
     *           {
     *             "season": 2,
     *             "subject": "第二季"
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionWorkInfo(int $work_id)
    {
        $work = Work::findOne(['id' => $work_id, 'type' => Work::TYPE_OMIKUJI]);
        if (!$work) {
            throw new HttpException(404, '作品未找到');
        }
        $user_id = Yii::$app->user->id;
        $user_has_drew_package_ids = [];
        if ($user_id) {
            $user_has_drew_package_ids = GetCard::find()
                ->distinct()
                ->select('card_package_id')
                ->where('user_id = :user_id AND work_id = :work_id AND special = :special', [
                    ':user_id' => $user_id,
                    ':work_id' => $work_id,
                    ':special' => Card::SPECIAL_OMIKUJI,
                ])
                ->column();
        }
        // 获取作品下季度（获取已上线季度及用户有求过签的季度）
        $packages = LotteryPackage::find()
            ->select('id, season, end_time')
            ->where('work_id = :work_id AND :now_time >= start_time', [
                ':work_id' => $work_id,
                ':now_time' => $_SERVER['REQUEST_TIME'],
            ])->orWhere(['id' => $user_has_drew_package_ids])
            ->orderBy('season ASC')
            ->all();
        $packages = LotteryPackage::processExpiredPackages($packages, $user_id, $work_id);
        $seasons = array_map(function ($pkg) {
            return [
                'season' => $pkg->season,
                'subject' => Work::getSeasonSubject($pkg->season),
            ];
        }, $packages);
        return [
            'id' => $work->id,
            'title' => $work->title,
            // 作品主页 banner 相关字段值结构为多图，求签作品取第一张图
            'banner' => $work->homepage_banners[0]['pic'] ?? '',
            'seasons' => $seasons,
        ];
    }

    /**
     * @api {get} /omikuji/package-info{?work_id,season} 获取签筒信息
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/omikuji/package-info
     * @apiSampleRequest omikuji/package-info
     *
     * @apiVersion 0.1.0
     * @apiName package-info
     * @apiGroup omikuji
     *
     * @apiParam {Number} work_id 作品 ID
     * @apiParam {Number} season 季度
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "card_count": 2,
     *         "user_card_count": 0,
     *         "cards": [
     *           {
     *             "id": 817,
     *             "title": "卡片2",
     *             "intro": "你今天要踩狗屎",
     *             "level": 3,
     *             "blessing": "末吉",
     *             "status": 0,
     *             "mini_cover": "https://static.missevan.com/covers/201903/01/e7bf91502.jpg",
     *             "cover": "https://static.missevan.com/covers/201903/01/e7bf9232f79718da1502.jpg"
     *           },
     *           {
     *             "id": 816,
     *             "title": "卡片1",
     *             "intro": "你今天要踩狗屎",
     *             "level": 1,
     *             "blessing": "凶",
     *             "status": 0,
     *             "mini_cover": "",
     *             "cover": ""
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionPackageInfo(int $work_id, int $season = 1)
    {
        $lottery_package = LotteryPackage::getOmikujiPackage($work_id, $season, false);
        if (!$lottery_package) {
            throw new HttpException(404, '签筒不存在 T_T');
        }
        // 查询该季度下的所有语音签（包含下架语音签）
        $cards = Card::find()
            ->where('card_package_id = :card_package_id AND special = :special',
                [':card_package_id' => $lottery_package->id, ':special' => Card::SPECIAL_OMIKUJI])
            ->orderBy('level DESC, rank ASC')
            ->all();
        $card_count = count($cards);
        $user_card_count = 0;
        // 用户在此签筒内已获得的语音签 ID
        $user_cards = [];
        $user_id = Yii::$app->user->id;
        if ($user_id && !empty($cards)) {
            $card_ids = array_column($cards, 'id');
            // 用户在此签筒内已获得的语音签
            $user_cards = GetCard::userCards($card_ids, $user_id);
            $user_card_count = count($user_cards);
            $user_cards = MUtils::groupArray($user_cards, 'card_id');
        }
        $now = $_SERVER['REQUEST_TIME'];
        if (($lottery_package->start_time > $now) && empty($user_cards)) {
            // 若用户未抽到过下架签筒内的卡片，不可获得签筒信息
            throw new HttpException(404, '签筒已下架 T_T');
        }
        // 给客户端的卡片显示状态：0：未解锁；2：未收听；3：已收听；4：下架
        $redis = Yii::$app->redis;
        $levels_key = $redis->generateKey(KEY_OMIKUJI_SHOW_COVER_LEVELS, $work_id);
        $show_levels = $redis->sMembers($levels_key) ?: [
            Card::LEVEL_SSR,
            Card::LEVEL_SR,
        ];
        $cards = array_map(function ($card) use ($user_cards, $show_levels, $lottery_package) {
            $cover = $mini_cover = '';
            if (!empty($show_levels) && in_array($card->level, $show_levels)) {
                $cover = $card->cover;
                $mini_cover = $card->icon ?: $cover . '?x-oss-process=image/resize,p_50';
            }
            if (!array_key_exists($card->id, $user_cards)) {
                // 未获得
                $status = Card::STATUS_LOCK;
                if ($card->isOffline() || $lottery_package->isExpired()) {
                    // 下架
                    $status = Card::STATUS_OFFLINE;
                }
            } elseif (current($user_cards[$card->id])->status === GetCard::STATUS_UNLOCK) {
                // 已获得但未听
                $status = Card::STATUS_NOTICE;
            } else {
                // 已获得且已听过
                $status = Card::STATUS_LISTENED;
            }
            if (Equipment::isAppVersion(Equipment::iOS, '4.3.1') && $status === Card::STATUS_NOTICE) {
                // WORKAROUND：修复 iOS 客户端卡片信息返回“未收听”状态时闪退的 BUG
                $status = Card::STATUS_LISTENED;
            }
            return [
                'id' => $card->id,
                'title' => $card->title,
                'intro' => $card->intro,
                'level' => $card->level,
                'blessing' => Card::getBlessingName($card->role_id),
                'status' => $status,
                'mini_cover' => $mini_cover,
                'cover' => $cover,
            ];
        }, $cards);
        return [
            'card_count' => $card_count,
            'user_card_count' => $user_card_count,
            'cards' => $cards,
        ];
    }

    /**
     * @api {get} /omikuji/skin 获取求签相关资源下载地址
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/omikuji/skin
     * @apiSampleRequest omikuji/skin
     * @apiDescription 返回的资源地址中会有一个实时变动的 Query 参数 auth_key（用于访问资源的验证），
     * 判断资源地址是否变动需要忽略该参数
     *
     * @apiVersion 0.1.0
     * @apiName skin
     * @apiGroup omikuji
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "font": "https://static.missevan.com/font/test.zip?auth_key=test-key"  // 压缩包中 title.ttf（喜鹊招牌体）用于作品及卡片标题，content.ttf（喜鹊聚珍体）用于台词及解签词
     *       }
     *     }
     *
     *     安卓 5.5.5 及其之后版本返回后台生成的皮肤包
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "url": "https://static-test.missevan.com/voice/work/0/skin/2021-06-22/os-1-new_all-6088e58e86e01/skin.zip?auth_key=1624351020-60d1a12c79afc-0-21c7112759c1310ab07a01cf5802e66e"
     *       }
     *     }
     */
    public function actionSkin()
    {
        if (Equipment::isAppOlderThan('4.6.8', '5.5.5')) {
            $font_url = StorageClient::getFileUrl(Work::OMIKUJI_FONT_OSS_URL);
            return ['font' => $font_url];
        }
        $os = Equipment::iOS;
        if (Yii::$app->equip->isAndroidOrHarmonyOS()) {
            $os = Equipment::Android;
        }
        $screen = $os === Equipment::Android
            ? SkinPackage::ANDROID_SCREEN_NEW_ALL
            : SkinPackage::IOS_SCREEN_HIGHEST;
        $skin_url = SkinPackage::find()
            ->select('url')
            ->where(['work_id' => Work::OMIKUJI_SKIN_WORK_ID, 'screen' => $screen, 'os' => $os])
            ->limit(1)
            ->scalar();
        return ['url' => StorageClient::getFileUrl($skin_url)];
    }

    /**
     * @api {get} /omikuji/info{?type} 规则信息接口（幸运点、求签、小剧场、作品情况说明）
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/omikuji/info
     * @apiSampleRequest omikuji/info
     *
     * @apiVersion 0.1.0
     * @apiName info
     * @apiGroup omikuji
     *
     * @apiParam {number=0,1,2,3} type 类型（0：幸运点说明；1：求签说明；2：小剧场说明；3：作品（IP）情况说明）
     * @apiParam {Number} work_id 作品 ID（type 为 3 时需要传入）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "<div><p><b>幸运点怎么使用</b></p>..."
     *     }
     */
    public function actionInfo(int $type)
    {
        switch ($type) {
            case self::INFO_TYPE_COUPON:
                $return = $this->renderPartial('couponinfo');
                break;
            case self::INFO_TYPE_DRAW:
                $return = $this->renderPartial('drawinfo');
                break;
            case self::INFO_TYPE_EPISODE:
                $return = $this->renderPartial('episodeinfo');
                break;
            case self::INFO_TYPE_WORK:
                $work_id = (int)Yii::$app->request->get('work_id');
                if ($work_id <= 0) {
                    throw new HttpException(400, '参数错误');
                }
                if (!$work = Work::findOne($work_id)) {
                    throw new HttpException(404, '作品不存在 T_T');
                }
                // 查询作品下不同季包的语音签数量（包含下架语音签）
                $now = $_SERVER['REQUEST_TIME'];
                $packages = LotteryPackage::find()
                    ->alias('t1')
                    ->select('t1.id, t1.season, COUNT(t2.id) AS card_count')
                    ->leftJoin(Card::tableName() . ' AS t2', 't1.id = t2.card_package_id')
                    ->where('t1.work_id = :work_id AND t2.special = :special',
                        [
                            ':work_id' => $work_id,
                            ':special' => Card::SPECIAL_OMIKUJI
                        ])
                    ->andWhere(':now >= t1.start_time', [':now' => $now])
                    ->groupBy('t1.id')
                    ->orderBy('t1.season ASC')
                    ->asArray()
                    ->all();
                $packages_user_card_count = [];
                $user_id = Yii::$app->user->id;
                if ($user_id && !empty($packages)) {
                    $packages_user_card_count = GetCard::find()
                        ->select('card_package_id, COUNT(*) AS user_card_count')
                        ->where('user_id = :user_id AND work_id = :work_id AND special = :special',
                            [
                                ':user_id' => $user_id,
                                ':work_id' => $work_id,
                                ':special' => Card::SPECIAL_OMIKUJI,
                            ])
                        ->groupBy('card_package_id')
                        ->indexBy('card_package_id')
                        ->asArray()
                        ->all();
                }
                $packages = array_map(function ($package) use ($packages_user_card_count) {
                    $package['user_card_count'] = $packages_user_card_count[$package['id']]['user_card_count']
                        ?? 0;
                    return $package;
                }, $packages);
                $return = $this->renderPartial('packageinfo', [
                    'work' => $work,
                    'packages' => $packages,
                ]);
                break;
            default:
                throw new HttpException(400, '参数错误');
        }
        return $return;
    }

    private function getDrawPageInfo(int $work_id, int $season, bool $is_new_user, int $free_draw_expires)
    {
        $is_free = false;
        $next_free_time = 0;
        $has_free_drew = !in_array($free_draw_expires,
            [WorkUserInfo::HAS_FREE_DRAW_CHANCE, WorkUserInfo::FREE_DRAW_NOT_LOGGED]);
        // 获取签筒
        $draw_package = LotteryPackage::getOmikujiPackage($work_id, $season, false);
        if (!$draw_package) {
            throw new HttpException(404, '签筒不存在或已下架 T_T');
        }
        if ($is_new_user && !$has_free_drew) {
            // 若为新用户（未登录用户也被视为新用户）并且还未免费抽过，则对用户显示“免费”
            $is_free = true;
        } else {
            $next_free_time = $free_draw_expires > 0 ? $free_draw_expires - time() : 0;
        }
        // 获取已拥有未收听求签卡数量及未解锁求签剧场卡数量
        $user_id = Yii::$app->user->id;
        $not_listen_num = GetCard::find()
            ->where([
                'user_id' => $user_id,
                'work_id' => $work_id,
                'card_package_id' => $draw_package->id,
                'status' => GetCard::STATUS_UNLOCK,
                'special' => Card::SPECIAL_OMIKUJI,
            ])
            ->count();
        // 获取 Banner 图
        $banner = Work::getWorkPic($work_id, Work::TYPE_DRAWPAGE_BANNER, $season);
        $subject = Work::getSeasonSubject($season);
        return [
            'season' => $season,
            'subject' => $subject,
            'is_free' => $is_free,
            'next_free_time' => $next_free_time,
            'price' => $is_free ? 0 : $draw_package->price,
            'banner' => $banner,
            'not_listen_num' => $not_listen_num,
        ];
    }

    private function getUnlockEpisodeNum(int $user_id, int $work_id, int $season): int
    {
        $user_coupon = UserVoiceInfo::getOmikujiCoupon($user_id);
        if (!$user_coupon) return 0;
        // 获取作品求签剧场卡
        $episode_cards = Card::find()
            ->alias('t1')
            ->select('t1.id, t1.price, t1.card_package_id, t2.season')
            ->leftJoin(LotteryPackage::tableName() . ' AS t2', 't1.card_package_id = t2.id')
            ->where([
                't1.work_id' => $work_id,
                't1.special' => Card::SPECIAL_OMIKUJI_EPISODE,
                't1.is_online' => Card::ONLINE,
                't2.season' => $season,
            ])
            ->andWhere(':now_time >= t2.start_time')
            ->params([':now_time' => $_SERVER['REQUEST_TIME']])
            ->orderBy('t1.`rank` ASC')
            ->asArray()
            ->all();
        $episodes_cards = MUtils::groupArray($episode_cards, 'card_package_id');
        if (!$episode_cards) return 0;
        $user_episode_card_ids = GetCard::find()
            ->select('card_id')
            ->where([
                'work_id' => $work_id,
                'user_id' => $user_id,
                'special' => Card::SPECIAL_OMIKUJI_EPISODE,
            ])
            ->column();
        $wait_unlock_num = 0;
        foreach ($episodes_cards as $episode_cards) {
            foreach ($episode_cards as $card) {
                if (!in_array($card['id'], $user_episode_card_ids)) {
                    if ((int)$card['price'] <= $user_coupon) {
                        $wait_unlock_num += 1;
                    }
                    break;
                }
            }
        }
        return $wait_unlock_num;
    }
}
