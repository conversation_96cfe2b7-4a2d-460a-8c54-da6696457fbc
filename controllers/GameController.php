<?php

namespace app\controllers;

use app\components\base\filter\AccessControl;
use app\components\util\Captcha;
use app\components\util\MUtils;
use app\models\MGameCenter;
use app\middlewares\Controller;
use app\models\MGameSubscribe;
use missevan\storage\StorageClient;
use Yii;
use yii\helpers\Json;
use yii\filters\VerbFilter;
use yii\web\HttpException;

class GameController extends Controller
{
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'subscribe' => ['post'],
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'only' => [
                'subscribe',
            ],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => [
                        'subscribe',
                    ],
                    'roles' => ['@']
                ],
            ]
        ];
        return $behaviors;
    }

    /**
     * @api {post} /game/subscribe 游戏预约
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/game/subscribe
     * @apiSampleRequest /game/subscribe
     *
     * @apiVersion 0.1.0
     * @apiName subscribe
     * @apiGroup game
     *
     * @apiParam {Number} game_id 游戏 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "预约成功"
     *     }
     */
    public function actionSubscribe()
    {
        $game_id = (int)Yii::$app->request->post('game_id');
        if ($game_id <= 0) {
            throw new HttpException(400, '参数错误');
        }
        if (ENABLE_NOCAPTCHA && !defined('YII_CAPTCHA_TEST')) {
            $captcha_arr = Captcha::getSlideParams();
            if (empty($captcha_arr)) {
                throw new HttpException(403, Yii::$app->params['standaloneUrl']
                    . '403/normal.html?scene=' . Captcha::SCENEORIGINAL_OTHER, 100010017);
            } else {
                [$session_id, $token, $sig] = $captcha_arr;
                $result = Yii::$app->captcha->verifyCaptcha($session_id, $token, $sig, Captcha::SCENEORIGINAL_OTHER);
                // 判断滑动验证是否成功
                if (Captcha::SUCCESS !== $result->Code) {
                    throw new HttpException(403, '预约失败');
                }
            }
        }
        $game = MGameCenter::find()
            ->select('id, publish_time, shutdown_time, extended_fields')
            ->where(['id' => $game_id])->one();
        if (!$game) {
            throw new HttpException(404, '未找到该游戏');
        }
        if ($game->shutdown_time > 0 && $game->shutdown_time <= $_SERVER['REQUEST_TIME']) {
            throw new HttpException(403, '游戏已下架，暂时无法操作');
        }
        $game->subscribe(Yii::$app->user->id);

        return '预约成功';
    }

    /**
     * @api {get} /game/list{?page_size} 游戏中心
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com:8081/game/list?page_size=20
     * @apiSampleRequest /game/list
     *
     * @apiVersion 0.1.0
     * @apiName list
     * @apiGroup game
     *
     * @apiParam {Number} [page_size=20] 每页个数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [
     *           {
     *             "id": 2,
     *             "url": "http://test.com/mevent/102718",
     *             "cover": "https://static.missevan.com/game/images/cover.jpg",
     *             "icon": "https://static.missevan.com/game/images/icon.jpg",
     *             "name": "游戏 2",
     *             "tag": "二次元,养成",
     *             "intro": "简介 2",
     *             "sort": 2,
     *             "status": 1,  // 状态，1：未预约；2：已预约；3：开放下载
     *             "download_url": "https://www.missevan.com/x/gamecenter/download?game_id=2&os=1",
     *             "package_name": "com.missevan.app",
     *             "package_version_code": 1
     *           },
     *           {
     *             "id": 1,
     *             "url": "http://test.com/mevent/102718",
     *             "cover": "https://static.missevan.com/game/images/cover.jpg",
     *             "icon": "https://static.missevan.com/game/images/icon.jpg",
     *             "name": "游戏 1",
     *             "tag": "二次元,养成",
     *             "intro": "简介 1",
     *             "sort": 1,
     *             "status": 2,
     *             "download_url": "https://www.missevan.com/x/gamecenter/download?game_id=1&os=2",
     *             "package_name": "com.missevan.app",
     *             "package_version_code": 1
     *           }
     *         ],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 1,
     *           "count": 2
     *           "pagesize": 20
     *         }
     *       }
     *     }
     */
    public function actionList(int $page_size = PAGE_SIZE_20)
    {
        $query = MGameCenter::find()
            ->alias('t1')
            ->select('t1.id, t1.url, t1.cover, t1.icon, t1.name, t1.tag, t1.intro, t1.extended_fields, t1.sort')
            ->where('t1.publish_time > 0 AND t1.publish_time <= :now_time AND (t1.shutdown_time > :now_time OR t1.shutdown_time = :shutdown_time)',
                [':now_time' => $_SERVER['REQUEST_TIME'], ':shutdown_time' => MGameCenter::SHUTDOWN_TIME_NONE])
            ->orderBy('t1.sort DESC, t1.id DESC');
        $user_id = Yii::$app->user->id;
        if ($user_id) {
            $query->addSelect('t2.user_id AS subscribed_user_id')
                ->leftJoin(MGameSubscribe::tableName() . ' AS t2', 't1.id = t2.game_id
                    AND t2.user_id = :user_id AND t2.delete_time = 0')
                ->addParams([':user_id' => $user_id]);
        }
        $return_model = MUtils::getPaginationModels($query, $page_size);
        $return_model->Datas = array_map(function ($item) use ($user_id) {
            // 默认游戏未预约
            $status = MGameCenter::STATUS_UNSUBSCRIBED;
            $extended_fields_arr = $item->extended_fields;
            if (isset($extended_fields_arr['next_stage']['time'])
                    && $_SERVER['REQUEST_TIME'] >= $extended_fields_arr['next_stage']['time']) {
                // NOTICE: next_stage.extended_fields 里没有开发厂商、权限用途等字段值，要使用 merge，不能直接赋值
                $extended_fields_arr = array_merge($extended_fields_arr,
                    $extended_fields_arr['next_stage']['extended_fields']);
                unset($extended_fields_arr['next_stage']);
                // 定时更新 extended_fields 字段
                MGameCenter::updateAll(['extended_fields' => Json::encode($extended_fields_arr)],
                    ['id' => $item['id']]);
            }
            $download_open_time = $extended_fields_arr['download_open_time'] ?? 0;
            if ($download_open_time && $_SERVER['REQUEST_TIME'] >= $download_open_time) {
                $status = MGameCenter::STATUS_OPEN_DOWNLOAD;
            } elseif ($user_id && $user_id === (int)$item->subscribed_user_id) {
                $status = MGameCenter::STATUS_SUBSCRIBED;
            }
            $tags = explode(',', $item->tag);
            if (count($tags) > 1) {
                // 标签最多展示 2 个
                $item->tag = implode(',', array_slice($tags, 0, 2));
            }
            $os = Yii::$app->equip->getOs();
            $download_query = "?game_id={$item->id}&os={$os}";
            return [
                'id' => $item->id,
                'url' => $item->url,
                'cover' => StorageClient::getFileUrl($item->cover),
                'icon' => StorageClient::getFileUrl($item->icon),
                'name' => $item->name,
                'tag' => $item->tag,
                'intro' => $item->intro,
                'sort' => $item->sort,
                // 1：预约；2：已预约；3：下载
                'status' => $status,
                'download_url' => Yii::$app->params['domainMissevan'] . '/x/gamecenter/download'
                    . $download_query,
                'package_name' => $extended_fields_arr['package_name'] ?? '',
                'package_version_code' => $extended_fields_arr['package_version_code'] ?? 0,
            ];
        }, $return_model->Datas);
        return $return_model;
    }

    /**
     * @api {get} /game/detail{?game_id} 游戏详情
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com:8081/game/detail?game_id=1
     * @apiSampleRequest /game/detail
     *
     * @apiVersion 0.1.0
     * @apiName detail
     * @apiGroup game
     *
     * @apiParam {Number} game_id 游戏 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 1,
     *         "icon": "https://static.missevan.com/game/images/icon.jpg",
     *         "name": "游戏 1",
     *         "download_url": "https://www.missevan.com/x/gamecenter/download?game_id=1&os=1",
     *         "package_name": "com.missevan.app",
     *         "package_version_code": 1
     *       }
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     {
     *       "success": false,
     *       "code": 201010002,
     *       "info": "参数错误"
     *     }
     */
    public function actionDetail(int $game_id)
    {
        if (!$game_id) throw new HttpException(400, '参数错误');

        $game = MGameCenter::find()
            ->select('id, icon, name, extended_fields')
            ->where(['id' => $game_id])
            ->one();
        if (!$game) {
            return [];
        }
        $os = Yii::$app->equip->getOs();
        $download_query = "?game_id={$game->id}&os={$os}";
        return [
            'id' => $game->id,
            'icon' => StorageClient::getFileUrl($game->icon),
            'name' => $game->name,
            'download_url' => Yii::$app->params['domainMissevan'] . '/x/gamecenter/download'
                . $download_query,
            'package_name' => $game->extended_fields['package_name'] ?? '',
            'package_version_code' => $game->extended_fields['package_version_code'] ?? 0,
        ];
    }
}
