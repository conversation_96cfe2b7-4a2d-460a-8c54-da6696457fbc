<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/18
 * Time: 18:52
 */

namespace app\controllers;

use app\components\controllers\EventInterface;
use app\components\util\Equipment;
use app\components\util\MUtils;
use app\middlewares\Controller;
use app\models\AnPrize;
use app\models\Blacklist;
use app\models\Catalog;
use app\models\Drama;
use app\models\Dub;
use app\models\EventVote;
use app\models\EventVoteDetail;
use app\models\Live;
use app\models\MEvent;
use app\models\MGameCenter;
use app\models\MGameElement;
use app\models\MSound;
use app\models\SpecialEventElem;
use app\models\Subtitle;
use Exception;
use missevan\rpc\LiveRpc;
use missevan\storage\StorageClient;
use Yii;
use yii\db\Expression;
use app\components\base\filter\AccessControl;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use yii\web\HttpException;
use yii\web\UploadedFile;

class EventController extends Controller implements EventInterface
{
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'submission' => ['post'],
                'dub-submission' => ['post'],
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'only' => ['dub-submission', 'submission', 'vote'],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => ['dub-submission', 'submission', 'vote'],
                    'roles' => ['@'],
                ],
            ],
        ];
        return $behaviors;
    }

    /**
     * @api {get} /event/list{?page,pagesize} 活动列表
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/event/list
     * @apiSampleRequest /event/list
     * @apiDescription ios:/mobile/site/NewEventLIst 安卓:/mobile/site/eventList p=>page
     * @apiVersion 0.1.0
     * @apiName list
     * @apiGroup event
     *
     * @apiParam Query {Number} [page=1] 页数
     * @apiParam Query {Number} [pagesize=30] 页数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *        "success": true,
     *        "code": 0,
     *        "info": {
     *          "Datas": [
     *            {
     *              "id": 104518,
     *              "title": "催眠季活动",
     *              "mini_cover": "https://static-test.missevan.acom/event/201903/14/f38120d59f1ca155933.png",
     *              "start_time": 1560873600,
     *              "end_time": 1624118399,
     *              "status": 7,
     *              "is_end": 1,
     *              "url": "https://www.missevan.com/mevent/104518"
     *            },
     *            {
     *              "id": 104970,
     *              "title": "催眠季活动",
     *              "mini_cover": "https://static-test.missevan.acom/event/201903/14/f381507d59f1ca155933.png",
     *              "start_time": 1560873600,
     *              "end_time": 1624118399,
     *              "status": 7,
     *              "is_end": 1,
     *              "url": "https://www.missevan.com/mevent/104970"
     *            }
     *          ],
     *          "pagination": {
     *            "p": 1,
     *            "maxpage": 7,
     *            "count": 122,
     *            "pagesize": 20
     *          }
     *        }
     *     }
     */
    public function actionList(int $page = 1, int $pagesize = DEFAULT_PAGE_SIZE)
    {
        $expression = new Expression('IF(end_time > UNIX_TIMESTAMP(NOW()), 0, 1) AS is_end');
        // NOTICE: 由于活动数量少，暂时没有大的性能问题
        $query = MEvent::find()
            ->select('id, title, mini_cover, start_time, end_time, status')
            ->addSelect($expression)
            // 筛选出只在 app 上显示并且未在列表页隐藏的活动
            ->where('(status & :status_app) AND (status & :status_list_hidden = 0)',
                [':status_app' => MEvent::STATUS_APP, ':status_list_hidden' => MEvent::STATUS_LIST_HIDDEN])
            ->orderBy('is_end ASC, create_time DESC');
        if (Blacklist::model()->isGameCenterBlocked()) {
            $query->andWhere('type <> :type', [':type' => MEvent::TYPE_GAME]);
        }
        $return_model = MUtils::getPaginationModels($query, $pagesize);
        foreach ($return_model->Datas as &$event) {
            $event->is_end = ($event->start_time > $_SERVER['REQUEST_TIME']) ?
                0 : ($event->end_time > $_SERVER['REQUEST_TIME'] ? 1 : 2);
            $event['url'] = Yii::$app->params['domainMissevan'] . '/mevent/' . $event['id'];
        }
        return $return_model;
    }

    /**
     * @api {get} /event/detail{?event_id,version} 活动详细信息
     * WORKAROUND: Android 没有版本调用过此接口，iOS 4.6.5 之后不再调用
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com:8081/event/detail?event_id=1&version=1
     * @apiSampleRequest /event/detail

     * @apiVersion 0.1.0
     * @apiName detail
     * @apiGroup event
     *
     * @apiParam {Number} event_id 活动id
     * @apiParam {Number} [version=0] 默认过滤 =\+# 符号
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *        success: true,
     *        code: 0,
     *       "info": {
     *         "event": {
     *           "id": 1,
     *           "title": "EVA 声优补全计划",
     *           "mobile_cover": "http://static.missevan.com/mimages/201603/29/04f3422edc05672bb43333beee1fbdce153602.jpg",
     *           "main_cover": "http://static.missevan.com/mimages/201411/21/6d65f7552945d3a30906e3bb6e8df053144902.png",
     *           "share_cover": "http://static.missevan.com/event/202006/15/091dd17e9499bd2d89ba5ddaee5ac497124754.png",
     *           "intro": "<p>简介</p>",
     *           "short_intro": "永远的 EVA 永远的凌波丽",
     *           "bilibili_url_pc": null,
     *           "bilibili_url_h5": null,
     *           "tag_id": 2132,
     *           "tag": "EVA 声优补全计划",
     *           "type": 0,
     *           "vote_start_time": 0,
     *           "vote_end_time": 0,
     *           "draw_start_time": 0,
     *           "draw_end_time": 0,
     *           "create_time": 1416499200,
     *           "start_time": 1416499200,
     *           "end_time": 1419782399,
     *           "head": "http://static.missevan.com/sound/201202/07/af27751b6385158680356cad71054682.mp3",
     *           "tail": "http://static.missevan.com/sound/201202/07/af27751b6385158680356cad71054682.mp3",
     *           "extended_fields": null,
     *           "status": 9,
     *           "limit": 0,
     *           "mini_cover": "http://static.missevan.com/mimages/201603/29/04f3422edc05672bb43333beee1fbdce153602.jpg",
     *           "limit_work": 0,
     *           "limit_vote": 0,
     *           "do_comment": 0,
     *           "attr": 1
     *         },
     *         "prizes": [{
     *           "id": 822,
     *           "name": "福利 1",
     *           "pic": "http://static.missevan.com/mimages/201412/22/3673b0324db8189b967f1215799ae75b170558.png",
     *           "probability": 10,
     *           "num": 10,
     *           "event_id": 1
     *         }, {
     *           "id": 823,
     *           "name": "福利 2",
     *           "pic": "http://static.missevan.com/mimages/201412/22/9e39fefbde07db6f8a323a2b8671c522170558.png",
     *           "probability": 10,
     *           "num": 10,
     *           "event_id": 1
     *        }],
     *        "pics": []
     *       }
     *     }
     */
    public function actionDetail(int $event_id, int $version = 0)
    {
        $event = MEvent::findOne(['id' => $event_id]);
        if (!$event) {
            throw new HttpException(404, '活动不存在', 200410001);
        }
        $event->intro = preg_replace(['/[\n\r\t]/', '/[ ]{2,}/'], '', $event->intro);
        // DEPRECATED: 待客户端改用 start_time 字段之后考虑废弃
        $event->create_time = $event->start_time;
        // REVIEW: 需要确认一下 version 参数使用的原因和目的
        if (!$version) {
            $event->intro = preg_filter('/[=\+#]{3,}/', '', $event->intro);
        }
        $prizes = AnPrize::findAll(['event_id' => $event_id]);

        return
            [
                'event' => $event,
                'prizes' => $prizes,
                'pics' => [],
            ];
    }

    /**
     * @api {get} /event/works 活动作品
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/event/works
     * @apiSampleRequest /event/works
     * @apiDescription /mobile/site/getWorks 参数p=>page pagesize=>page_size
     * @apiParam {String} [token] token.帮助判断是否投票
     * @apiParam {int} event_id 活動id.
     * @apiParam {int} [page=1] 页数.
     * @apiParam {int} [page_size=20] 单页数目.
     * @apiParam {int} [order=1] 排序0最新1最热.
     * @apiVersion 0.1.0
     * @apiName works
     * @apiGroup event
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *          success: true,
     *          code: 200,
     *          info:
     *              {
     *                  Datas: [ [Object], [Object], [Object] ],
     *                  pagination: { p: 1, count: '3', maxpage: 1, pagesize: 20 }
     *              }
     *     }
     *
     */
    public function actionWorks(int $event_id, int $order = 0, int $page_size = 20)
    {
        $works = EventVote::getWorks($event_id, $order, $page_size);

        return $works;
    }

    /**
     * @api {get} /event/in-work{?id,type} 检查元素（音频、剧集等）是否参加活动
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/event/in-work
     * @apiSampleRequest /event/in-work
     *
     * @apiVersion 0.1.0
     * @apiName in-work
     * @apiGroup event
     *
     * @apiParam {Number} id 元素 ID
     * @apiParam {number=0,1} [type=0] 元素类型（0 为音频，1 为剧集）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": null
     *     }
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 57,
     *         "title": "ASMR 活动",
     *         "vote_start_time": 1551024000,
     *         "voted": 1,  // 用户当前是否可投票（0：否；1：可投票，用户今日未投票）
     *         "do_vote": true,  // 活动是否支持投票
     *         "url": "https://www.missevan.com/mevent/85"
     *       }
     *     }
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 85,
     *         "title": "本剧正在参加众筹活动，快去众筹榜看看吧",
     *         "url": "https://www.missevan.com/mevent/85"  // 需要支持 msr-0 协议地址
     *       }
     *     }
     */
    public function actionInWork(int $id, int $type = 0)
    {
        if (!Equipment::isAppOlderThan('4.9.3', '5.7.8')) {
            // WORKAROUND: Android >= 5.7.8、iOS >= 4.9.3 不再调用该接口获取相关信息（改为使用 /event/card 接口）
            Yii::warning('高版本 App 请求了过时的 /event/in-work 接口', __METHOD__);
            throw new HttpException(400, '非法请求');
        }
        $user_id = Yii::$app->user->id;
        if (!MEvent::checkElementType($type)) {
            throw new HttpException(400, '参数错误');
        }

        return MEvent::getEventByElement($id, $type, $user_id);
    }

    /**
     * @api {get} /event/card{?sound_id,drama_id} 获取音频或剧集关联的活动、游戏、直播预告、福袋信息
     *
     * @apiVersion 0.1.0
     * @apiName card
     * @apiGroup event
     *
     * @apiParam {Number} [sound_id=0] 音频 ID
     * @apiParam {Number} [drama_id=0] 剧集 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {  // 无相关信息时返回 null
     *         "event": {  // 没有时不返回
     *           "id": 57,
     *           "title": "ASMR 活动",
     *           "vote_start_time": 1551024000,  // 投票开始时间戳，单位：秒
     *           "voted": 1,  // 用户当前是否可投票（0：否；1：可投票，用户今日未投票）
     *           // 根据是否返回 do_vote 字段区分活动条展示样式：
     *           // 1. 返回 do_vote 客户端展示投稿活动活动条（do_vote 为 false 展示投稿评论活动条，为 true 展示投稿投票活动条）
     *           // 2. 不返回 do_vote 客户端展示广告条样式
     *           "do_vote": true,  // 活动是否支持投票
     *           "url": "https://www.missevan.com/mevent/85"
     *         },
     *         "game": {  // 没有时不返回
     *           "id": 2,
     *           "url": "http://test.com/mevent/102718",  // 点击卡片跳转链接
     *           "cover": "https://static.missevan.com/game/images/cover.jpg",  // 卡片背景图
     *           "dark_cover": "https://static.missevan.com/game/images/cover.jpg",  // 黑夜模式下卡片背景图
     *           "icon": "https://static.missevan.com/game/images/icon.jpg",  // 游戏图标
     *           "btn_color": "#000000",  // 按钮颜色
     *           "dark_btn_color": "#ffffff",  // 黑夜模式下按钮颜色
     *           "name": "游戏 2",  // 游戏名
     *           "tag": "二次元,养成",  // 游戏标签，按半角逗号分隔
     *           "intro": "简介 2",
     *           "status": 1,  // 状态，1：未预约；2：已预约；3：开放下载
     *           "download_url": "https://www.test.com/x/gamecenter/download?game_id=2&os=1",  // 仅 Android 返回
     *           "package_name": "com.missevan.app",  // 仅 Android 返回
     *           "package_version_code": 1,  // 仅 Android 返回
     *           "download_auto_follow": 1  // 1：下载时自动关注游戏官方账号；不需要自动关注游戏官方账号时不下发该字段。仅 Android 返回
     *         },
     *         "live_preview": {  // 直播预告，没有时不返回
     *           "id": 1,  // 预告 ID
     *           "title": "卡片标题",
     *           "live_start_time": 1703001600,  // 开始直播时间点。单位：秒
     *           "room_id": 233,  // 直播间 ID
     *           "creator_id": 11, // 主播 ID。用于在播放页判断是否要刷新预约之后的音频 UP 主关注状态（当 UP 主为预告直播的主播时）
     *           "live_status": 0,  // 直播间状态。0：未开播；1：直播中。仅会在预计开播时间点之后返回
     *           "reservation_status": 1  // 预约直播状态。0：未预约直播；1：已预约直播。未登录用户不返回
     *         },
     *         "live_lucky_bag": {  // 直播福袋广告条，没有时不下发
     *           "ipr_id": 2333,  // 剧集所属 IPR ID，当剧集所属 IPR 下有福袋时下发该字段，没有时不下发
     *           "ipr_name": "魔道祖师",  // 剧集所属 IPR 名，当剧集所属 IPR 下有福袋时下发该字段，没有时不下发
     *           "drama_id": 2334,  // 剧集 ID，当剧集不属于 IPR 且剧集下有福袋时下发该字段，没有时不下发，不会和 IPR 信息同时下发
     *           "drama_name": "烟火",  // 剧名，当剧集不属于 IPR 且剧集下有福袋时下发该字段，没有时不下发，不会和 IPR 信息同时下发
     *           "num": 5,  // 当前正在发放此剧集福袋的直播间数量
     *           "data": [  // 排名前三的直播间信息
     *             {
     *               "room_id": 100000,  // 直播间 ID
     *               "name": "直播间标题",  // 直播间标题
     *               "creator_id": 11,  // 主播 ID
     *               "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"  // 主播头像
     *             },
     *             {
     *               "room_id": 100001,
     *               "name": "直播间标题",
     *               "creator_id": 12,
     *               "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"
     *             },
     *             {
     *               "room_id": 100002,
     *               "name": "直播间标题",
     *               "creator_id": 13,
     *               "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"
     *             }
     *           ]
     *         }
     *       }
     *     }
     */
    public function actionCard(int $sound_id = 0, int $drama_id = 0)
    {
        if ($sound_id <= 0 && $drama_id <= 0) {
            throw new HttpException(400, '参数错误');
        }
        $return = [];
        $user_id = (int)Yii::$app->user->id;
        $event_card = null;
        if (Equipment::isAppOlderThan('6.0.0', '6.0.2')) {
            // WORKAROUND: iOS 版本小于 6.0.0 或 Android 版本小于 6.0.2 时，传入剧集 ID（音频属于剧集时），仅支持展示广告条样式，不支持展示投稿活动活动条
            // 传入剧集 ID（音频属于剧集时），如果剧集配置了活动，就返回活动信息，否则不返回活动信息
            // 未传入剧集 ID（音频不属于剧集时），如果音频配置了活动，就返回活动信息，否则不返回活动信息
            $event_card = $drama_id
                ? MEvent::getEventByElement($drama_id, SpecialEventElem::ELEM_TYPE_DRAMA, $user_id)
                : MEvent::getEventByElement($sound_id, SpecialEventElem::ELEM_TYPE_SOUND, $user_id);
        } else {
            // 音频 ID 和剧集 ID 均存在（音频属于剧集），优先展示音频配置的活动信息，音频活动不存在时展示剧集活动信息
            if ($sound_id) {
                $event_card = MEvent::getEventByElement($sound_id, SpecialEventElem::ELEM_TYPE_SOUND, $user_id);
            }
            if (!$event_card && $drama_id) {
                $event_card = MEvent::getEventByElement($drama_id, SpecialEventElem::ELEM_TYPE_DRAMA, $user_id);
            }
        }
        if ($event_card) {
            $return['event'] = $event_card;
        }
        // 优先使用剧集配置的游戏信息
        $game = null;
        if ($drama_id) {
            $game = MGameCenter::getGameByElement(MGameElement::ELEMENT_TYPE_DRAMA, $drama_id);
        }
        if (!$game && $sound_id) {
            $game = MGameCenter::getGameByElement(MGameElement::ELEMENT_TYPE_SOUND, $sound_id);
        }
        if ($game) {
            // 获取关联游戏信息
            $game_card = [
                'id' => $game->id,
                'url' => $game->url,
                'cover' => StorageClient::getFileUrl($game->extended_fields['card']['cover'] ?? ''),
                'dark_cover' => StorageClient::getFileUrl($game->extended_fields['card']['dark_cover'] ?? ''),
                'icon' => StorageClient::getFileUrl($game->icon),
                'btn_color' => $game->extended_fields['card']['btn_color'] ?? '#000000',
                'dark_btn_color' => $game->extended_fields['card']['dark_btn_color'] ?? '#ffffff',
                'name' => $game->name,
                'tag' => $game->tag,
                'intro' => $game->intro,
                'status' => $game->getCardStatus($user_id),
            ];
            if (Yii::$app->equip->isAndroid()) {
                // 对于 Android 客户端，需要额外返回安装包下载地址等信息
                $download_query = "?game_id={$game->id}&os=" . Equipment::Android;
                $game_card = $game_card + [
                    'download_url' => Yii::$app->params['domainMissevan'] . '/x/gamecenter/download'
                        . $download_query,
                    'package_name' => $game->extended_fields['package_name'] ?? '',
                    'package_version_code' => $game->extended_fields['package_version_code'] ?? 0,
                ];
                $auto_follow = $game->extended_fields['download_auto_follow'] ?? 0;
                if ($auto_follow) {
                    $game_card['download_auto_follow'] = $auto_follow;
                }
            }
            $return['game'] = $game_card;
        }
        if ($drama_id) {
            $live_preview = Live::getDramaLivePreview($drama_id, $user_id);
            if ($live_preview) {
                $return['live_preview'] = $live_preview;
            }
            $ipr_id = Drama::getDramaIprId($drama_id);
            $live_lucky_bag = Drama::getDramaLuckybag([$drama_id], $ipr_id);
            if ($live_lucky_bag) {
                $return['live_lucky_bag'] = $live_lucky_bag;
            }
        }
        return $return ?: null;
    }

    /**
     * @api {get} /event/vote 活动投票
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/event/vote
     * @apiSampleRequest /event/vote
     * @apiDescription /mobile/personOperation/vote
     * @apiParam {String} token token.
     * @apiParam {int} event_id 活动id
     * @apiParam {int} sound_id 作品id
     * @apiVersion 0.1.0
     * @apiName vote
     * @apiGroup event
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *          success: true,
     *          code: 200,
     *          info: '投票成功!'
     *     }
     *
     */
    public function actionVote(int $event_id, int $sound_id)
    {
        if (!Yii::$app->user->isBindMobile) {
            throw new HttpException(403, '您需要先绑定手机，才可以献出宝贵的一票哦！', 100010008);
        }
        if (Yii::$app->user->inBlacklist()) {
            // 黑名单用户禁止投票
            throw new HttpException(403, '投票失败');
        }
        EventVoteDetail::vote($event_id, $sound_id, Yii::$app->user->id);
        return '投票成功!';
    }

    /**
     * @api {get} /event/dub-list 获取活动下的配音秀素材
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/event/dub-list
     * @apiSampleRequest /event/dub-list
     * @apiDescription 获取活动下的配音秀素材
     * @apiVersion 0.1.0
     * @apiName dub-list
     * @apiGroup /event
     *
     * @apiParam {Number} eid 活动 ID
     *
     * @apiSuccess {String} success true or false
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "id": 21,
     *           "title": "表情包配音",
     *           "video_url": "https://static.missevan.com/dub/201707/6db1eae0b6068fcd6ec40540bc4d5246.mp4",
     *           "cover_url": "https://static.missevan.com/dub/201707/06/51e2f2b8738e5f3f423fa3e49771a3b0.png",
     *           "from": "网络",
     *           "duration": 20
     *         },
     *         {
     *           "id": 22,
     *           "title": "表情包2",
     *           "video_url": "https://static.missevan.com/dub/201707/c21e36219b3c77f6730005b9dc60fb49.mp4",
     *           "cover_url": "https://static.missevan.com/dub/201707/21/5421824676507c4256616ae79f0d7b11.png",
     *           "from": "网络",
     *           "duration": 50
     *         },
     *         {
     *           "id": 23,
     *           "title": "表情包3",
     *           "video_url": "https://static.missevan.com/dub/201707/1f8f7eab87ad8de040847d307eebe517.mp4",
     *           "cover_url": "https://static.missevan.com/dub/201707/21/c2f80b0c0bfea6f43e6afb0111ab7e38.png",
     *           "from": "网络",
     *           "duration": 20
     *         }
     *       ]
     *     }
     */
    public function actionDubList(int $eid)
    {
        $dubs = Dub::find()->select('id, title, video_url, cover_url, `from`, duration')
            ->where(['eid' => $eid])->all();
        // WORKAROUND: Android 5.3.5 及其以上使用全路径地址
        if (!Equipment::isAppOlderThan(null, '5.3.5')) {
            return array_map(function ($data) {
                $data['video_url'] = Yii::$app->params['static_domain'] . $data['video_url'];
                $data['cover_url'] = Yii::$app->params['static_domain'] . $data['cover_url'];
                return $data;
            }, $dubs);
        }
        return $dubs;
    }

    /**
     * @api {get} /event/dub 获取配音秀素材详情
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}//event/dub
     * @apiSampleRequest /event/dub
     * @apiDescription 获取配音秀素材详情
     * @apiVersion 0.1.0
     * @apiName dub
     * @apiGroup /event
     *
     * @apiParam {Number} did 素材 ID
     *
     * @apiSuccess {String} success true or false
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "video": "https://static.missevan.com/dub/202003/315fc38835632fa638de49c85bbfddea.mp4",
     *         "audios": [
     *           "https://static.missevan.com/dub/202003/271ba28e7733d24002c5ec44f8ab4f5f.mp3"
     *         ],
     *         "subtitle": [
     *           {
     *             "context": "我考验你",
     *             "stime": 1752,
     *             "etime": 2974,
     *             "role": "唐晶"
     *           },
     *           {
     *             "context": "是因为我心里还存有",
     *             "stime": 4234,
     *             "etime": 5510,
     *             "role": "唐晶"
     *           }
     *         ]
     *       }
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     {
     *       "success": false,
     *       "code": 200410103,
     *       "info": "该素材不存在"
     *     }
     */
    public function actionDub(int $did)
    {
        $dub = Dub::findOne($did);
        if (!$dub) {
            throw new HttpException(404, '该素材不存在', 200410103);
        }
        $more = $dub->more ? Json::decode($dub->more) : [];

        $subtitles = Subtitle::getDubSubtitle($did);

        $dub->video_url = Yii::$app->params['static_domain'] . $dub->video_url;
        $more['audios'] = array_map(function ($url) {
            return Yii::$app->params['static_domain'] . $url;
        }, $more['audios'] ?? []);
        return [
            'video' => $dub->video_url,
            'audios' => $more['audios'],
            'subtitle' => $subtitles,
        ];
    }

    /**
     * @api {post} /event/dub-submission 配音秀投稿
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/event/dub-submission
     * @apiSampleRequest /event/dub-submission
     * @apiDescription  /mobile/personOperation/CreateDubbing
     * @apiVersion 0.1.0
     * @apiName dub-submission
     * @apiGroup event
     *
     * @apiParam {Number} did 素材id.
     * @apiParam {String} soundstr 标题.
     * @apiParam {String} [intro] 配音秀介绍
     * @apiParam {String[]} [srt] 字幕
     * @apiParam {float=0-2} vol1 录音声音大小
     * @apiParam {float=0-2} vol2 背景音声音大小
     * @apiParam {File} sound 单音文件
     * @apiParam {File} img 封面图
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *          success: true,
     *          code: 200,
     *          info:
     *              {id : 511423}
     *     }
     *
     */
    public function actionDubSubmission()
    {
        $did = (int)Yii::$app->request->post('did');
        $sound_str = Yii::$app->request->post('soundstr');
        $intro = Yii::$app->request->post('intro');
        $srt = Yii::$app->request->post('srt', '');
        $vol1 = (float)Yii::$app->request->post('vol1', 1);
        $vol2 = (float)Yii::$app->request->post('vol2', 1);

        // WORKAROUND:（预计在 Android 5.3.8 版本之后删除）
        if (Equipment::isAppOlderThan('', '5.3.4')) {
            $vol2 = (float)Yii::$app->request->post('vol1', 1);
            $vol1 = (float)Yii::$app->request->post('vol2', 1);
        }

        $dub = Dub::findOne($did);
        if (!$dub) {
            throw new HttpException(404, '该素材不存在', 200410103);
        }
        if (!$event = MEvent::findOne($dub->eid)) {
            throw new HttpException(404, '活动不存在', 200410001);
        }
        $event->check();
        if ($srt) {
            $arr_srt = Json::decode($srt);
        } else {
            $arr_srt = Subtitle::getDubSubtitle($did);
        }
        if (!is_array($arr_srt)) {
            throw new HttpException(400, '字幕格式不正确', 200410104);
        }
        $srt = Subtitle::arrayToSrt($arr_srt);
        $img = UploadedFile::getInstanceByName('img');
        if (!($i_path = $img->tempName ?? '')) {
            throw new HttpException(400, '封面图存在问题', 200410105);
        }
        if (!$extension = MUtils::getFileExtension($img->tempName)) {
            throw new HttpException(400, '图片格式错误');
        }

        $soundurl = MSound::handleSubmission();

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $new_sound = new MSound();
            $new_sound->user_id = Yii::$app->user->id;
            $new_sound->username = Yii::$app->user->name;
            $new_sound->catalog_id = Catalog::CATALOG_ID_DUB;
            $new_sound->soundstr = $sound_str;
            $new_sound->intro = $intro;
            $new_sound->download = MSound::SOUND_DOWNLOAD_ALLOW;
            $new_sound->soundurl = $soundurl;
            $new_sound->checked = MSound::CHECKED_DUB_TRANSCODE;
            $new_sound->cover_image = $i_path;
            $new_sound->uploadCoverImageEdit(7, $extension);

            if (!$new_sound->save()) {
                throw new HttpException(400, '录入数据有误', 200410107);
            }

            $sound_id = $new_sound->id;
            $event_vote = new EventVote();
            $event_vote->event_id = $dub->eid;
            $event_vote->eid = $sound_id;
            $event_vote->vote_num = 0;
            $event_vote->category = $event->type;

            if (!$event_vote->save()) {
                throw new HttpException(400, '录入数据有误', 200410108);
            }
            $data = serialize([
                'sid' => $sound_id,
                'did' => $did,
                'srt' => $srt,
                'vol1' => $vol1,
                'vol2' => $vol2,
            ]);

            $redis = Yii::$app->redis;
            // 暂时使用 redis 替代
            $status = $redis->pipeline()
                ->lPush('list:dub', $data)
                ->hSet('cache:dub', $sound_id, $data)
                ->exec();
            if (!$status) {
                throw new HttpException(400, '录入数据有误', 200410109);
            }
            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
        return ['id' => $sound_id];
    }
}
