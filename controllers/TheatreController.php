<?php

namespace app\controllers;

use app\components\util\Equipment;
use app\components\util\MUtils;
use app\middlewares\Controller;
use app\models\Drama;
use app\models\MSound;
use app\models\MTheatreDrawHistory;
use app\models\MTheatrePickedComment;
use app\models\MPersonaModuleElement;
use app\models\MAttentionUser;
use app\models\MRecommendedElements;
use app\models\MTheatreBlindBox;
use app\models\PaginationParams;
use app\models\SoundVideo;
use app\models\TheatreDrawEvent;
use Exception;
use missevan\storage\StorageClient;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use yii\web\HttpException;

class TheatreController extends Controller
{
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'draw-blind-box' => ['post'],
            ],
        ];
        return $behaviors;
    }

    /**
     * @api {get} /theatre/get-picked-comments 获取盲盒剧场的精选评论
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/theatre/get-picked-comments
     * @apiSampleRequest /theatre/get-picked-comments
     *
     * @apiVersion 0.1.0
     * @apiName get-picked-comments
     * @apiGroup theatre
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "comments": [
     *           {
     *             "comment_id": 1,
     *             "sound_id": 233,
     *             "name": "剧集标题",
     *             "username": "用户昵称",
     *             "iconurl": "http://static-test.maoercdn.com/avatars/201812/10/test.jpg",
     *             "comment_content": "评论内容"
     *           },
     *           {
     *             "comment_id": 2,
     *             "sound_id": 234,
     *             "name": "剧集标题2",
     *             "username": "用户昵称2",
     *             "iconurl": "http://static-test.maoercdn.com/avatars/201812/10/test2.jpg",
     *             "comment_content": "这是一部好剧集"
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionGetPickedComments()
    {
        $memcache = Yii::$app->memcache;
        try {
            if ($picked_comments_json = $memcache->get(KEY_THEATRE_PICKED_COMMENTS)) {
                $return = ['comments' => Json::decode($picked_comments_json, true)];
                return $return;
            }
            $picked_comments = MTheatrePickedComment::getPickedComments();
            $memcache->set(KEY_THEATRE_PICKED_COMMENTS, Json::encode($picked_comments), ONE_HOUR);
            $return = ['comments' => $picked_comments];
            return $return;
        } catch (Exception $e) {
            // 记录错误日志
            Yii::error("获取盲盒剧场的精选评论异常：{$e->getMessage()}", __METHOD__);
            // 返回空数据，保证客户端无异常
            return ['comments' => []];
        }
    }

    /**
     * @api {get} /theatre/get-theatre-homepage 获取剧场首页版头图、剧场预告播放视频、banner、广告条、官方账号数据
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/theatre/get-theatre-homepage
     * @apiSampleRequest /theatre/get-theatre-homepage
     *
     * @apiVersion 0.1.0
     * @apiName get-theatre-homepage
     * @apiGroup theatre
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         // 剧场首页版头图
     *         "header_cover": "https://static-test.maoercdn.com/test/test.jpeg",
     *         // 分享图
     *         "share_image": "https://static-test.maoercdn.com/test/share.jpeg",
     *         // 剧场预告视频播放
     *         "video": {
     *           "duration": 459541,
     *           "width": 1080,
     *           "height": 1920,
     *           "videourl": "http://static.missevan.com/video/201702/13/216132.mp4",
     *           "cover": "https://static-test.maoercdn.com/coversmini/201701/24/ab067534604b0fcaf8ebe79.png",
     *         },
     *         // banner
     *         "banners": [
     *           {
     *             "url": "https://www.uat.missevan.com/blackboard/activity-Z21BP24cEY.html",
     *             "pic": "https://static-test.maoercdn.com/app/homepage/banners/202111/04/9bb70c60a0401b230186.jpeg"
     *           },
     *           {
     *             "url": "https://www.uat.missevan.com/blackboard/activity-Z21BP24cEY.html",
     *             "pic": "https://static-test.maoercdn.com/app/homepage/banners/202111/04/9bb70c60a0401b230186.jpeg"
     *           },
     *           {
     *             "url": "https://www.uat.missevan.com/blackboard/activity-Z21BP24cEY.html",
     *             "pic": "https://static-test.maoercdn.com/app/homepage/banners/202111/04/9bb70c60a0401b230186.jpeg"
     *           }
     *         ],
     *         // 广告条
     *         "ad": {  // 无广告时返回 null
     *           "url": "https://www.uat.missevan.com/blackboard/activity-Z21BP24cEY.html",
     *           "pic": "https://static-test.maoercdn.com/app/homepage/banners/202111/04/9bb70c60a0401b2301866.jpeg"
     *         },
     *         // 官方账号
     *         "official_user": {
     *           "user_id": 5,
     *           "attention": 0  // 关注状态 0：未关注；1：已关注
     *         }
     *       }
     *     }
     */
    public function actionGetTheatreHomepage()
    {
        $result = [
            'header_cover' => '',
            'share_image' => '',
            'video' => [
                'duration' => 0,
                'width' => 0,
                'height' => 0,
                'videourl' => '',
                'cover' => '',
            ],
            'banners' => [],
            'ad' => null,
            'official_user' => [
                'user_id' => 0,
                'attention' => MAttentionUser::TYPE_STRANGER
            ]
        ];
        // 获取首页版头图、剧场预告视频、官方账号信息
        $redis = Yii::$app->redis;
        // 该 key 中仅 5 个 field，故可用 hGetAll 一次性获取
        $homepage_info = $redis->hGetAll(KEY_THEATRE_HOMEPAGE);
        if ($homepage_info) {
            // 首页版头图
            $result['header_cover'] = StorageClient::getFileUrl($homepage_info['header_cover']);
            // 分享图
            $result['share_image'] = StorageClient::getFileUrl($homepage_info['share_image']);
            // 获取预告视频信息
            $video_sound_id = (int)$homepage_info['video_sound_id'];
            $sound = MSound::find()->select('cover_image')
                // 仅获取音频封面（后台设置），不需要判断 checked 状态
                ->where('id = :id', [':id' => $video_sound_id])->one();
            $video_cover = $sound->front_cover ?? '';
            $video = SoundVideo::getHDVideo($video_sound_id);
            if ($video) {
                $result['video'] = [
                    'duration' => $video['duration'],
                    'width' => $video['width'],
                    'height' => $video['height'],
                    'videourl' => $video['videourl'],
                    'cover' => $video_cover,
                ];
            }
            // 获取官方账号信息
            $official_user_id = (int)$homepage_info['official_user_id'];
            // 关注状态
            $user_id = Yii::$app->user->id;
            $attention = $user_id ? MAttentionUser::getAttention($official_user_id, $user_id)
                : MAttentionUser::TYPE_STRANGER;
            $result['official_user'] = [
                'user_id' => $official_user_id,
                'attention' => $attention,
            ];
        } else {
            Yii::error('盲盒剧场未设置首页信息', __METHOD__);
            // PASS
        }
        // 获取 banner
        $banner_elements = [];
        $banner_key = KEY_THEATRE_HOMEPAGE_BANNER;
        $memcache = Yii::$app->memcache;
        if (!($data = $memcache->get($banner_key))) {
            $banner_elements = MRecommendedElements::getOnlineOrMaxEndTimeElements(MRecommendedElements::CLIENT_APP_ALL,
                [MRecommendedElements::MODULE_TYPE_BLIND_BOX_HOMEPAGE_BANNER, MRecommendedElements::MODULE_TYPE_BLIND_BOX_AD_BANNER], true);
            $data = Json::encode($banner_elements);
            // 盲盒剧场 banner 目前无下线时间，故缓存可使用固定值
            $duration = FIVE_MINUTE;
            $memcache->set($banner_key, $data, $duration);
        } else {
            $banner_elements = Json::decode($data);
        }
        foreach ($banner_elements as $element) {
            $link = [
                'url' => MUtils::getUsableAppLink($element['url']),
                'pic' => $element['pic'],
            ];
            if ($element['module_type'] === MRecommendedElements::MODULE_TYPE_BLIND_BOX_HOMEPAGE_BANNER) {
                $result['banners'][] = $link;
            } else {
                // ad 仅允许有一个（只设置一个），可直接赋值
                $result['ad'] = $link;
            }
        }
        return $result;
    }

    /**
     * @api {get} /theatre/recommend-modules 获取推荐的（剧集）模块
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/theatre/recommend-modules
     * @apiSampleRequest /theatre/recommend-modules
     *
     * @apiVersion 0.1.0
     * @apiName recommend-modules
     * @apiGroup theatre
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "id": 8,
     *           "title": "即将上线",
     *           "elements_num": 1,  // 数量
     *           "elements": [
     *             {
     *               "id": 233,  // 剧集 ID
     *               "name": "剧集标题",
     *               "abstract": "剧集简介",
     *               "front_cover": "http://static-test.maoercdn.com/mimages/201711/15/test.jpg",
     *               "is_subscribe": true  // 用户未登录状态不存在该字段，表示用户是否追剧
     *             }
     *           ]
     *         },
     *         {
     *           "id": 8,
     *           "title": "热播中",
     *           "elements_num": 1,  // 数量
     *           "elements": [
     *             {
     *               "id": 234,  // 剧集 ID
     *               "name": "剧集标题",
     *               "abstract": "剧集简介",
     *               "front_cover": "http://static-test.maoercdn.com/mimages/201711/15/test.jpg",
     *               "is_subscribe": true  // 用户未登录状态不存在该字段，表示用户是否追剧
     *             }
     *           ]
     *         }
     *       ]
     *     }
     */
    public function actionRecommendModules()
    {
        $module_persona = MPersonaModuleElement::PERSONA_ID_THEATRE;
        $memcache = Yii::$app->memcache;
        $modules_data_key = MUtils::generateCacheKey(KEY_GUESSYOURLIKES_MODULES, $module_persona);
        if ($modules = $memcache->get($modules_data_key)) {
            $modules = Json::decode($modules);
        } else {
            // 获取模块数据
            $modules = MPersonaModuleElement::getModules($module_persona);
            if (empty($modules)) {
                return $modules;
            }
            // 获取模块包含的元素
            $module_ids = array_unique(array_column($modules, 'module_id'));
            $modules_elements_count = MPersonaModuleElement::countModulesElements($module_ids);
            $elements = MPersonaModuleElement::getElementsRecommended($module_ids);
            $drama_data = MPersonaModuleElement::getDramas($elements);
            $elems_module_group = MUtils::groupArray($drama_data, 'module_id');
            // 调整需要返回的字段
            $modules = array_map(function ($module) use ($elems_module_group, $modules_elements_count) {
                $elements = $elems_module_group[$module['module_id']] ?? [];
                $elements = array_map(function ($drama) {
                    return [
                        'id' => $drama['id'],
                        'name' => $drama['name'],
                        'abstract' => $drama['abstract'],
                        // 优先返回长方形封面图
                        'front_cover' => $drama['thumbnail'] ?: $drama['cover'],
                    ];
                }, $elements);
                return [
                    'id' => $module['module_id'],
                    'title' => $module['title'],
                    'elements_num' => $modules_elements_count[$module['module_id']] ?? 0,
                    'elements' => $elements,
                ];
            }, $modules);
            $memcache->set($modules_data_key, Json::encode($modules), FIVE_MINUTE);
        }
        $user_id = Yii::$app->user->id;
        if ($user_id && !empty($modules)) {
            // 若用户登录，则模块中的剧集需要添加用户追剧状态
            $drama_ids = [];
            foreach ($modules as $module) {
                $drama_ids = array_merge($drama_ids, array_column($module['elements'], 'id'));
            }
            $subscribed_drama_ids = [];
            if (!empty($drama_ids)) {
                $drama_ids = array_values(array_unique($drama_ids));
                $subscribed_drama_ids = Drama::getSubscribedDramaIds($drama_ids, $user_id);
            }
            foreach ($modules as &$module) {
                foreach ($module['elements'] as $key => $drama) {
                    $module['elements'][$key]['is_subscribe'] = in_array($drama['id'], $subscribed_drama_ids);
                }
            }
            // 去掉数组最后一个元素的引用
            unset($module);
        }
        return $modules;
    }

    /**
     * @api {get} /theatre/module-details 获取模块详情页
     *
     * @apiVersion 0.1.0
     * @apiName module-details
     * @apiGroup theatre
     *
     * @apiParam {Number} module_id 模块 ID
     * @apiParam {Number} [page_size=30] 每页数量
     * @apiParam {Number} [page=1]  当前页
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 8,
     *         "title": "即将上线",
     *         "module_style": 2,  // 2：排行榜
     *         "module_type": 2,  // 2：剧集
     *         "elements": {
     *           "Datas": [
     *             {
     *               "id": 26,
     *               "name": "我的王妃是男人",
     *               "abstract": "剧集简介",
     *               "front_cover": "http://static-test.maoercdn.com/dramacoversmini/201708/25/3894e2f317c57123406.jpg",
     *               "is_subscribe": true  // 用户未登录状态不存在该字段，表示用户是否追剧
     *             },
     *             {
     *               "id": 27,
     *               "name": "替身",
     *               "abstract": "剧集简介",
     *               "front_cover": "http://static-test.maoercdn.com/dramacoversmini/201708/25/3894e2f317c57123406.jpg",
     *               "is_subscribe": false  // 用户未登录状态不存在该字段，表示用户是否追剧
     *             }
     *           ],
     *           "pagination": {
     *             "p": 1,
     *             "count": 40,
     *             "maxpage": 2,
     *             "pagesize": 20,
     *             "hasMore": true
     *           }
     *         }
     *       }
     *     }
     */
    public function actionModuleDetails()
    {
        $module_id = (int)Yii::$app->request->get('module_id');
        if (!$module_id) {
            throw new HttpException(400, '参数错误');
        }
        $page = (int)Yii::$app->request->get('page', 1);
        $page_size = (int)Yii::$app->request->get('page_size', DEFAULT_PAGE_SIZE);
        $pagination = PaginationParams::process($page, $page_size);
        $details = MPersonaModuleElement::getFavorDetails($module_id, $pagination, null);
        if (!$details) {
            throw new HttpException(404, '咦，内容不见了 _(:зゝ∠)_ 请刷新重试');
        }
        if (!empty($details['elements']->Datas)) {
            $subscribed_drama_ids = [];
            $user_id = Yii::$app->user->id;
            if ($user_id) {
                // 若用户登录，则模块中的剧集需要添加用户追剧状态
                $drama_ids = array_column($details['elements']->Datas, 'id');
                if (!empty($drama_ids)) {
                    $subscribed_drama_ids = Drama::getSubscribedDramaIds($drama_ids, $user_id);
                }
            }
            // 返回所需字段
            $details['elements']->Datas = array_map(function ($drama) use ($user_id, $subscribed_drama_ids) {
                $drama = [
                    'id' => $drama['id'],
                    'name' => $drama['name'],
                    'abstract' => $drama['abstract'],
                    'front_cover' => $drama['front_cover'],
                ];
                if ($user_id) {
                    // 登录状态下需要返回追剧状态
                    $drama['is_subscribe'] = in_array($drama['id'], $subscribed_drama_ids);
                }
                return $drama;
            }, $details['elements']->Datas);
        }
        return $details;
    }

    /**
     * @api {get} /theatre/get-drama-rank 获取剧场排行
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/theatre/get-drama-rank
     * @apiSampleRequest /theatre/get-drama-rank
     *
     * @apiVersion 0.1.0
     * @apiName get-drama-rank
     * @apiGroup theatre
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {  // 返回 null 时隐藏排行榜模块
     *         "ranks": [
     *           {
     *             "title": "当季榜",
     *             "data": [
     *               {
     *                 "id": 15861,  // 剧集 ID
     *                 "name": "《魔道祖师》第一季",
     *                 "cover": "http://static-test.maoercdn.com/dramacoversmini/201708/25/3894e2f317c57123406.jpg",
     *               },
     *               {
     *                 "id": 19059,
     *                 "name": "《魔道祖师》第二季",
     *                 "cover": "http://static-test.maoercdn.com/dramacoversmini/201708/25/3894e2f317c57123406.jpg"
     *               }
     *             ]
     *           },
     *           {
     *             "title": "总榜",
     *             "data": [
     *               {
     *                 "id": 26,
     *                 "name": "我的王妃是男人",
     *                 "cover": "http://static-test.maoercdn.com/dramacoversmini/201708/25/3894e2f317c57123406.jpg"
     *               },
     *               {
     *                 "id": 27,
     *                 "name": "替身",
     *                 "cover": "http://static-test.maoercdn.com/dramacoversmini/201708/25/3894e2f317c57123406.jpg"
     *               }
     *             ]
     *           }
     *         ],
     *         "tip": "这是剧场排行规则说明"  // HTML 格式文本
     *       }
     *     }
     */
    public function actionGetDramaRank()
    {
        $ranks = Yii::$app->memcache->get(KEY_THEATRE_RANKS);
        if (!$ranks) {
            Yii::error('盲盒剧场榜单缓存不存在');
            // PASS: 缓存生成失败或未配置排行榜时，返回 null 让客户端隐藏该模块
            return null;
        }
        $ranks = json_decode($ranks, true);
        if (!$ranks) {
            Yii::error('盲盒剧场榜单缓存数据错误');
            // PASS: 返回 null 让客户端隐藏该模块
            return null;
        }
        $ranks = array_map(function ($rank) {
            // 仅返回所需字段（过滤掉排行榜类型和排序等字段）
            return [
                'title' => $rank['title'],
                'data' => $rank['data'],
            ];
        }, $ranks);
        $tip = Yii::$app->redis->hGet(KEY_THEATRE_HOMEPAGE, 'rank_tip') ?: '';
        // WORKAROUND: iOS 低于 4.9.1 版本时不支持 HTML 标签，需要将 <br /> 替换为 \n
        if (Equipment::isAppOlderThan('4.9.1')) {
            $tip = preg_replace('/<br.*?>/', "\n", $tip);
        }
        return [
            'ranks' => $ranks,
            'tip' => $tip,
        ];
    }

    /**
     * @api {post} /theatre/draw-blind-box 抽盲盒
     * @apiDescription 播放音频需要添加播放量，使用 /sound/add-play-times 接口添加播放量，player_mode 参数值为 3 \
     * e.g. /sound/add-play-times?sound_id=233&add=1&player_mode=3
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/theatre/draw-blind-box
     * @apiSampleRequest /theatre/draw-blind-box
     *
     * @apiVersion 0.1.0
     * @apiName draw-blind-box
     * @apiGroup theatre
     *
     * @apiParam {String} marker 抽取标识，格式为“随机数:当前序列索引” \
     * 如“50:1”表示以 50 作为随机因子生成随机序列，然后取第 2 个盲盒，每次刷新页面后请求时不传该值
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "sound": {
     *           "id": 48040,
     *           "duration": 459541,
     *           "soundstr": "抽盲盒抽中的音频",
     *           "soundurl": "https://sound-ks-cdn-cn.maoercdn.com/test/test.m4a"
     *         },
     *         "drama": {
     *           "id": 15861,
     *           "name": "抽中的音频所属的剧集",
     *           "cover": "http://static-test.maoercdn.com/test/test.jpg",
     *           "cat_image": "http://static-test.maoercdn.com/test/cat.jpg",  // 剧集猫猫图，没有时不返回（不显示）
     *           "is_subscribe": true  // 用户未登录状态不存在该字段，表示用户是否追剧
     *         },
     *         "effect": {
     *           "effect_url": "http://static-test.maoercdn.com/test/test.mp4",
     *           "result_sound_url": "http://static-test.maoercdn.com/test/test.m4a",
     *           // 客户端需要在特效开始播放后的该时间（单位：毫秒）时，显示弹窗，另外这个时间之后不允许打断播放特效
     *           "result_time": 1000
     *         },
     *         "marker": "233:1"  // 抽取标识，返回该值时客户端保存并作为下一次连续请求的参数
     *       }
     *     }
     */
    public function actionDrawBlindBox()
    {
        // 每次重新进入盲盒剧场页面，marker 都会为空（不传该参数或传空）
        $marker = trim(Yii::$app->request->post('marker', ''));
        $user_id = (int)Yii::$app->user->id;
        // 已登录用户每次重新进入盲盒剧场页面，都需要重新查询已抽盲盒，不使用缓存
        $no_cache = ($user_id && !$marker);
        [$not_draw_boxs, $draw_boxs] = MTheatreBlindBox::getBlindBoxPool($user_id, $no_cache);
        $not_draw_boxs_count = count($not_draw_boxs);
        $blind_box_pool_size = $not_draw_boxs_count + count($draw_boxs);
        if (!$blind_box_pool_size) {
            Yii::error('盲盒剧场未设置盲盒池', __METHOD__);
            throw new HttpException(404, '抽取失败，请稍后重试');
        }
        // 抽取盲盒，每个用户使用一个随机数，按该随机数生成各自固定的抽取序列，然后按顺序抽取
        [$random_seed, $current_position, $next_marker] = MUtils::markerInfo($marker, $blind_box_pool_size);
        // 按固定的随机种子，打乱数组
        mt_srand($random_seed);
        if (!empty($not_draw_boxs)) {
            shuffle($not_draw_boxs);
        }
        if (!empty($draw_boxs)) {
            shuffle($draw_boxs);
        }
        $blind_box_pool = array_merge($not_draw_boxs, $draw_boxs);
        $blind_box = $blind_box_pool[$current_position];

        // 获取音频信息
        $sound_id = $blind_box['sound_id'];
        $sound = MSound::find()->select('id, duration, soundstr, soundurl_64')
            ->where('id = :id AND checked = :checked',
                [':id' => $sound_id, ':checked' => MSound::CHECKED_PASS])
            ->one();
        if (!$sound) {
            Yii::error("抽取的盲盒音频（{$sound_id}）不存在", __METHOD__);
            throw new HttpException(404, '抽取失败，请稍后重试');
        }
        // 使用 upos 签名地址
        MSound::getSoundSignUrls($sound, true);

        // 获取剧集信息
        try {
            $drama_details = Drama::rpc('api/get-drama-details-by-id', [
                'drama_id' => $blind_box['drama_id'],
                'user_id' => $user_id,
                'extras' => ['theatre'],
            ]);
        } catch (Exception $e) {
            Yii::error('抽取的盲盒音频所属剧集错误: ' . $e->getMessage(), __METHOD__);
            throw new HttpException(500, '抽取失败，请稍后重试');
        }
        $drama = [
            'id' => $drama_details['drama']['id'],
            'name' => $drama_details['drama']['name'],
            'cover' => $drama_details['theatre']['cover'] ?? $drama_details['drama']['cover'],
        ];
        if ($user_id && key_exists('like', $drama_details)) {
            // 登录用户需要返回追剧状态
            $drama['is_subscribe'] = (bool)$drama_details['like'];
        }
        if (isset($drama_details['theatre']['cat_image'])) {
            $drama['cat_image'] = $drama_details['theatre']['cat_image'];
        }

        // 获取抽取特效信息（目前仅 3 个 field，可使用 hGetAll 一次性获取）
        $draw_effect = Yii::$app->redis->hGetAll(KEY_THEATRE_BLIND_BOX_DRAW_INFO);
        if (!$draw_effect) {
            Yii::error('盲盒剧场抽取特效未设置', __METHOD__);
            throw new HttpException(500, '抽取失败，请稍后重试');
        }
        $effect = [
            'effect_url' => StorageClient::getFileUrl($draw_effect['effect_url']),
            'result_sound_url' => StorageClient::getFileUrl($draw_effect['result_sound_url']),
            'result_time' => (int)$draw_effect['result_time'],
        ];
        if ($user_id) {
            // WORKAROUND: 盲盒抽奖活动加积分，活动结束后（2023.04.29 后）可删除
            TheatreDrawEvent::addDrawBlindBoxPoint($user_id);
            if ($current_position < $not_draw_boxs_count) {
                // 若抽到了未抽取过的音频，需要添加抽取记录
                MTheatreDrawHistory::addHistory($user_id, $sound_id);
            }
        }
        return [
            'sound' => [
                'id' => $sound->id,
                'duration' => $sound->duration,
                'soundstr' => $sound->soundstr,
                'soundurl' => $sound->soundurl,
            ],
            'drama' => $drama,
            'effect' => $effect,
            'marker' => $next_marker,
        ];
    }
}
