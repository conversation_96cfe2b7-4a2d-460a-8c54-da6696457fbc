<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2017/7/31
 * Time: 20:35
 */

namespace app\controllers;

use app\components\util\Equipment;
use app\components\util\MUtils;
use app\models\Catalog;
use app\models\Discovery;
use app\models\Drama;
use app\models\MAlbum;
use app\models\Mowangsksoundseiy;
use app\models\Mowangskuser;
use app\models\MRecommendedElements;
use app\models\MSound;
use app\models\Live;
use app\models\SpecialSearchItems;
use app\models\Topic;
use app\components\controllers\DiscoveryInterface;
use app\middlewares\Controller;
use Exception;
use Yii;
use yii\web\HttpException;
use yii\helpers\Json;

class DiscoveryController extends Controller implements DiscoveryInterface
{
    /**
     * @api {get} /discovery/list 发现页面
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/discovery/list
     * @apiSampleRequest /discovery/list
     * @apiDescription WARNING: dark 参数将在 Android 4.5.0 及 IOS 5.4.0 版本后废弃
     * @apiVersion 0.1.0
     * @apiName list
     * @apiGroup discovery
     * @apiParam {number=0,1} [dark=0] 主题模式：0 为白天模式，1 为夜间模式
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code Error code
     * @apiSuccess {Object} info Result of request
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         [{
     *           "id": 12,
     *           "title": "直播间",
     *           "icon": "http://static.missevan.com/profile/icon01.png",  // 老版正常使用，新版日间图标
     *           "dark_icon": "http://static.missevan.com/profile/icon01.png",  // 黑夜图标
     *           "intro": "M娘正在直播，快去围观吧",
     *           "link": {
     *             "scene": 3,
     *             "url": "/live"
     *           }
     *         }, {
     *           "id": 1,
     *           "title": "排行榜",
     *           "icon": "http://static.missevan.com/profile/icon01.png",  // 老版正常使用，新版日间图标
     *           "dark_icon": "http://static.missevan.com/profile/icon01.png",  // 黑夜图标
     *           "intro": "",
     *           "link": {
     *             "scene": 1,
     *             "url": "/rank"
     *           }
     *         }],
     *         [{
     *           "id": 2,
     *           "title": "频道",
     *           "icon": "http://static.missevan.com/profile/icon01.png",  // 老版正常使用，新版日间图标
     *           "dark_icon": "http://static.missevan.com/profile/icon01.png",  // 黑夜图标
     *           "intro": "",
     *           "link": {
     *             "scene": 1,
     *             "url": "/channel"
     *           }
     *         }, {
     *           "id": 3,
     *           "title": "广播剧",
     *           "icon": "http://static.missevan.com/profile/icon01.png",  // 老版正常使用，新版日间图标
     *           "dark_icon": "http://static.missevan.com/profile/icon01.png",  // 黑夜图标
     *           "intro": "",
     *           "link": {
     *             "scene": 1,
     *             "url": "/drama"
     *           }
     *         }, {
     *           "id": 4,
     *           "title": "鱼干任务",
     *           "icon": "http://static.missevan.com/profile/icon01.png",  // 老版正常使用，新版日间图标
     *           "dark_icon": "http://static.missevan.com/profile/icon01.png",  // 黑夜图标
     *           "intro": "",
     *           "link": {
     *             "scene": 3,
     *             "url": "/task"
     *           }
     *         }],
     *         [{
     *           "id": 5,
     *           "title": "活动",
     *           "icon": "http://static.missevan.com/profile/icon01.png",  // 老版正常使用，新版日间图标
     *           "dark_icon": "http://static.missevan.com/profile/icon01.png",  // 黑夜图标
     *           "intro": "",
     *           "link": {
     *             "scene": 1,
     *             "url": "/event"
     *           }
     *         }, {
     *           "id": 6,
     *           "title": "专题",
     *           "icon": "http://static.missevan.com/profile/icon01.png",  // 老版正常使用，新版日间图标
     *           "dark_icon": "http://static.missevan.com/profile/icon01.png",  // 黑夜图标
     *           "intro": "",
     *           "link": {
     *             "scene": 1,
     *             "url": "/topic"
     *           }
     *         }],
     *         [{
     *           "id": 10,
     *           "title": "新闻站",
     *           "icon": "http://static.missevan.com/profile/icon01.png",  // 老版正常使用，新版日间图标
     *           "dark_icon": "http://static.missevan.com/profile/icon01.png",  // 黑夜图标
     *           "intro": "",
     *           "link": {
     *             "scene": 0,
     *             "url": "http://m.missevan.com/news"
     *           }
     *         }, {
     *           "id": 13,
     *           "title": "微信男友",
     *           "icon": "http://static.missevan.com/profile/icon01.png",  // 老版正常使用，新版日间图标
     *           "dark_icon": "http://static.missevan.com/profile/icon01.png",  // 黑夜图标
     *           "intro": "   ",
     *           "link": {
     *             "scene": 0,
     *             "url": "http://www.uat.missevan.com/mgame/aiboyfriend?webview=1"
     *           }
     *         }, {
     *           "id": 14,
     *           "title": "兑换码",
     *           "icon": "http://static.missevan.com/profile/icon01.png",  // 老版正常使用，新版日间图标
     *           "dark_icon": "http://static.missevan.com/profile/icon01.png",  // 黑夜图标
     *           "intro": "",
     *           "link": {
     *             "scene": 0,
     *             "url": "https://m.missevan.com/wallet/redeem"
     *           }
     *         }],
     *         [{
     *           "id": 15,
     *           "title": "免流服务",
     *           "icon": "http://static.missevan.com/profile/icon01.png",  // 老版正常使用，新版日间图标
     *           "dark_icon": "http://static.missevan.com/profile/icon01.png",  // 黑夜图标
     *           "intro": "",
     *           "link": {
     *             "scene": 1,
     *             "url": "https://www.bilibili.com/blackboard/activity-unicomopenbeta-m2.html"
     *           }
     *         }]
     *       ]
     *     }
     */
    public function actionList()
    {
        return Discovery::getIconsGroup();
    }

    /**
     * @api {get} /discovery/search-config 综合搜索分区列表
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/discovery/search-config
     *
     * @apiVersion 0.1.0
     * @apiName search-config
     * @apiGroup discovery
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code Error code
     * @apiSuccess {Object} info Result of request
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "search_catalogs": [
     *           {
     *             "id": 0,
     *             "name": "全部分区"
     *           },
     *           {
     *             "id": 1,
     *             "name": "有声漫画"
     *           },
     *           {
     *             "id": 2,
     *             "name": "广播剧"
     *           },
     *           {
     *             "id": 3,
     *             "name": "音乐"
     *           },
     *           {
     *             "id": 4,
     *             "name": "声音恋人"
     *           },
     *           {
     *             "id": 5,
     *             "name": "电台"
     *           },
     *           {
     *             "id": 6,
     *             "name": "日抓"
     *           },
     *           {
     *             "id": 7,
     *             "name": "听书"
     *           },
     *           {
     *             "id": 8,
     *             "name": "铃声"
     *           },
     *           {
     *             "id": 9,
     *             "name": "放松"
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionSearchConfig()
    {
        return self::getSearchConfig();
    }

    /**
     * @api {get} /discovery/search 搜索
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/discovery/search
     * @apiSampleRequest /discovery/search
     * @apiDescription 综合搜索下综合排序、全部分区、第一页且命中关键词时会召回特型卡片、专题卡片、UP 主卡片和游戏卡片其中的一种或多种卡片
     * @apiVersion 0.1.0
     * @apiName search
     * @apiGroup discovery
     *
     * @apiParam {number={1-6}} type 搜索类型 1：UP 主；2：音单；3：综合；4：声优；5：剧集；6：直播
     * @apiParam {String} s 实际搜索关键字
     * @apiParam {String} input_word 用户输入的搜索关键字，在热门搜索和历史搜索中传空字符串，参数必须传
     * @apiParam {Number} [page=1] 页数
     * @apiParam {Number} [page_size=8] 每页个数
     * @apiParam {number={0-2}} [sort=0] 排序方式（各类型单独定义，暂时只有综合搜索有用） 0：综合排序，1：最多播放，2：最新发布
     * @apiParam {Number} [search_catalog_id=0] 分区 ID（暂时只有综合搜索有用） 0：全部分区
     * @apiParam {Number} [p=1] 搜索特型的页数
     * @apiParam {number=0,1} [free=0] （是否仅筛选免费音频：0 为否，1 为是）直播间搜索 BGM 时会用到
     * @apiParam {String} [suggest_request_id] 联想词的 request_id
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code Error code
     * @apiSuccess {Object} info Result of request
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "navs": [
     *           {
     *             "name": "剧集",
     *             "total": 433,
     *             "type": 5
     *           },
     *           {
     *             "name": "音单",
     *             "total": 1348,
     *             "type": 2
     *           },
     *           {
     *             "name": "UP 主",
     *             "total": 3539,
     *             "type": 1
     *           },
     *           {
     *             "name": "声优",
     *             "total": 62,
     *             "type": 4
     *           },
     *           {
     *             "name": "直播",
     *             "total": 5,
     *             "type": 6
     *           }
     *         ],
     *         "sounds": {
     *           "Datas": [{
     *             "catalog_id": 19,
     *             "checked": 1,
     *             "comment_count": 8047,
     *             "comments_count": 2221,
     *             "create_time": 1548392617,
     *             "duration": 1455046,
     *             "id": 1153663,
     *             "pay_type": 0,
     *             "soundstr": "【翼之声】《二哈和他的白猫师尊》楔子(彩虹)",
     *             "sub_comments_count": 366,
     *             "user_id": 75350,
     *             "username": "翼之声中文配音社团",
     *             "view_count": 126376,
     *             "all_comments": 10634,
     *             "comments_num": 2587,
     *             "front_cover": "http://static.missevan.com/coversmini/201901/25/c8f0a8bf854f2b0751354050333.png",
     *             "liked": 0,
     *             "collected": 0,
     *             "followed": 0,
     *             "authenticated": 0,
     *             "confirm": 0,
     *             "iconurl": "https://static.missevan.com/profile/icon01.png",
     *             "video": false,
     *             "need_pay": 0,
     *             "price": 0
     *           }],
     *           "pagination": {
     *             "p": 1,
     *             "pagesize": 1,
     *             "count": 5,
     *             "maxpage": 5
     *           },
     *           "ops_request_misc": "%7B%22request%5Fid%22%3A%22155436886819722120641390%22%2C%22scm%22%3A%2220140713.110018887..%22%7D"
     *         },
     *         "dramas": {
     *           "Datas": [{
     *             "id": 13259,
     *             "name": "哈佛优等生最欣赏的200个人生故事",
     *             "newest": "第01集_哈佛优等生",
     *             "catalog": "儿童",
     *             "type": 3,
     *             "type_name": "一般",
     *             "cover": "http://static.missevan.com/dramacoversmini/201712/26/988778c938011ace875fe615648.png",
     *             "serialize": false,
     *             "pay_type": 0,
     *             "price": 0,
     *             "checked": 1,
     *             "view_count": 1,
     *             "corner_mark": {  // 无剧集角标时不返回该字段
     *               "text": "已购",
     *               "text_color": "#ffffff",
     *               "bg_start_color": "#e66465",
     *               "bg_end_color": "#e66465",
     *               "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *             },
     *             "is_insert": 1,  // 是否被人工干预（0：否，1：是），未被人工干预不返回此字段
     *             "live_lucky_bag": {  // 直播福袋广告条，没有时不下发
     *               "ipr_id": 2333,  // 剧集所属 IPR ID，当剧集所属 IPR 下有福袋时下发该字段，没有时不下发
     *               "ipr_name": "魔道祖师",  // 剧集所属 IPR 名，当剧集所属 IPR 下有福袋时下发该字段，没有时不下发
     *               "drama_id": 2334,  // 剧集 ID，当剧集不属于 IPR 且剧集下有福袋时下发该字段，没有时不下发，不会和 IPR 信息同时下发
     *               "drama_name": "烟火",  // 剧名，当剧集不属于 IPR 且剧集下有福袋时下发该字段，没有时不下发，不会和 IPR 信息同时下发
     *               "num": 5,  // 当前正在发放此剧集福袋的直播间数量
     *               "data": [  // 排名前三的直播间信息
     *                 {
     *                   "room_id": 100000,  // 直播间 ID
     *                   "name": "直播间标题",  // 直播间标题
     *                   "creator_id": 11,  // 主播 ID
     *                   "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"  // 主播头像
     *                 },
     *                 {
     *                   "room_id": 100001,
     *                   "name": "直播间标题",
     *                   "creator_id": 12,
     *                   "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"
     *                 },
     *                 {
     *                   "room_id": 100001,
     *                   "name": "直播间标题",
     *                   "creator_id": 13,
     *                   "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"
     *                 }
     *               ]
     *             }
     *           }],
     *           "pagination": {
     *             "p": 1,
     *             "pagesize": 1,
     *             "count": 5,
     *             "maxpage": 5
     *           }
     *         },
     *         "special": {  // 关键词未命中不返回此字段
     *           "Datas": [{  // iOS >= 4.9.3 或 Android >= 5.7.7，不再下发此字段改为下发 data
     *             "id": 13259,
     *             "url": "missevan://omikuji/draw?work_id=4",
     *             "cover": "http://static-test.maoercdn.com/search/covers/202205/27/daa14798f83fc4a2a165531.jpg"
     *           }],
     *           "data": [{
     *             "id": 13259,
     *             "url": "missevan://omikuji/draw?work_id=4",
     *             "cover": "http://static-test.maoercdn.com/search/covers/202205/27/daa14798f83fc4a2a165531.jpg"
     *           }],
     *           "pagination": {
     *             "p": 1,
     *             "pagesize": 1,
     *             "count": 5,
     *             "maxpage": 5
     *           },
     *           "ops_request_misc": "%7B%22request%5Fid%22%3A%22155436886819722120641390%22%2C%22scm%22%3A%2220140713.110018887..%22%7D"
     *         },
     *         "topic_card": {  // 关键词未命中不返回此字段；当 topic_card 存在时，info.dramas 不再返回
     *           "title": "ceshi",
     *           "dramas": [{
     *             "id": 13259,
     *             "name": "哈佛优等生最欣赏的200个人生故事",
     *             "pay_type": 2,
     *             "cover_color": 16777215,
     *             "username": "UP 主",
     *             "view_count": 123,
     *             "cover": "http://static.maoercdn.com/dramacoversmini/201912/31/709b79e161545.jpg",
     *             "corner_mark": {  // 无剧集角标时不返回该字段
     *               "text": "已购",
     *               "text_color": "#ffffff",
     *               "bg_start_color": "#e66465",
     *               "bg_end_color": "#e66465",
     *               "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *             }
     *           }],
     *           "derivatives": [{  // 当 ip_id 未填写或者不存在相关周边时，不返回 derivatives
     *             "type": 2,  // 周边分类，1：商品；2；求签包；3；语音包；4：通用
     *             "title": "运势求签",
     *             "intro": "来看看今天的运势吧~",
     *             "type_name": "求签",
     *             "url": "missevan://omikuji/draw",
     *             "cover": "https://static.maoercdn.com/derivate/201907/08/cover.jpg"
     *           }],
     *           "url": "https://fm.missevan.com/",  // 未填写 url 或者剧集数量未满足条件时，不返回 url 字段
     *           "background": "https://fm.missevan.com/image/background.jpg",
     *           "cover": "https://fm.missevan.com/image/cover.jpg",
     *           "start_time": 1549337649,
     *           "color": "#ffffff",
     *           "view_count_total": "11 万",
     *           "ops_request_misc": "%7B%22request%5Fid%22%3A%2C%22scm%22%3A%2220140713.110018887..%22%7D",
     *           "live_lucky_bag": {  // 直播福袋广告条，没有时不下发
     *             "ipr_id": 2333,  // 专题卡关联 IPR ID，没有时不下发
     *             "ipr_name": "魔道祖师",  // 专题卡关联 IPR 名，没有时不下发
     *             "drama_id": 2334,  // 剧集 ID，当专题卡未关联 IPR 且剧集下有福袋时下发该字段，没有时不下发，不会和 IPR 信息同时下发
     *             "drama_name": "烟火",  // 剧名，当专题卡未关联 IPR 且剧集下有福袋时下发该字段，没有时不下发，不会和 IPR 信息同时下发
     *             "num": 5,  // 当前正在发放此剧集福袋的直播间数量
     *             "data": [  // 排名前三的直播间信息
     *               {
     *                 "room_id": 100000,  // 直播间 ID
     *                 "name": "直播间标题",  // 直播间标题
     *                 "creator_id": 11,  // 主播 ID
     *                 "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"  // 主播头像
     *               },
     *               {
     *                 "room_id": 100001,
     *                 "name": "直播间标题",
     *                 "creator_id": 12,
     *                 "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"
     *               },
     *               {
     *                 "room_id": 100001,
     *                 "name": "直播间标题",
     *                 "creator_id": 13,
     *                 "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"
     *               }
     *             ]
     *           }
     *         }
     *       },
     *       "up_card": {  // 关键词未命中不返回此字段
     *         "id": 1,
     *         "username": "InVinCiblezzz",
     *         "iconurl": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png",
     *         "authenticated": 0,  // 加 V 认证标识：1 代表黑 V，2 代表金 V，3 代表蓝 V
     *         "fansnum": 11,
     *         "work_summary": {
     *           "type": 1,  // 1：声音，2：剧集作品
     *           "num": 10  // 作品数量
     *         },
     *         "intro": "知名主播 ｜ 配音演员",
     *         "followed": 0,  // 游客或 UP 主自己视角，不返回 followed（0：未关注，1：已关注）
     *         "work_list": [{  // 作品列表，若可展示内容不足三个，不返回 work_list
     *           "title": "直播间标题",  // 直播间标题
     *           "room_id": 100000,  // 直播间 ID
     *           "cover_url": "https://static-test.maoercdn.com/icon01.png",  // 直播间封面图
     *           "iconurl": "https://static-test.maoercdn.com/avatars/icon01.png",  // 主播头像
     *           "tag": "直播",  // 显示在标题前面的标签
     *           "type": 5  // 动态类型（2：剧集，3：音频，5：直播动态）
     *         }, {
     *           "id": 13259,
     *           "name": "哈佛优等生最欣赏的200个人生故事",
     *           "pay_type": 2,
     *           "cover_color": 16777215,
     *           "username": "UP 主",
     *           "view_count": 123,
     *           "cover": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png",
     *           "corner_mark": {  // 无剧集角标时不返回该字段
     *             "text": "已购",
     *             "text_color": "#ffffff",
     *             "bg_start_color": "#e66465",
     *             "bg_end_color": "#e66465",
     *             "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *           },
     *           "tag": "热剧",
     *           "type": 2
     *         }, {
     *           "id": 83185,
     *           "soundstr": "小黄人版iphone铃声",
     *           "duration": 28447,
     *           "view_count": 46409,
     *           "front_cover": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png",
     *           "video": true,
     *           "tag": "音频",
     *           "type": 3
     *         }]
     *       },
     *       "game_card": {  // 关键词未命中不返回此字段
     *         "id": 2,  // 游戏 ID
     *         "url": "http://test.com/mevent/102718",  // 点击卡片跳转链接
     *         "cover": "http://static-test.maoercdn.com/game/images/cover.jpg",  // 日间模式下卡片背景图
     *         "dark_cover": "http://static-test.maoercdn.com/game/images/dark_cover.jpg",  // 黑夜模式下卡片背景图
     *         "icon": "https://static-test.missevan.com/game/images/icon.jpg",  // 游戏图标
     *         "btn_color": "#000000",  // 日间模式下按钮颜色
     *         "dark_btn_color": "#FFFFFF",  // 黑夜模式下按钮颜色
     *         "name": "游戏 2", // 游戏名
     *         "tag": "二次元,养成",  // 游戏标签，按半角逗号分隔
     *         "intro": "简介 2",  // 简介
     *         "status": 1,  // 状态，1：未预约；2：已预约；3：开放下载
     *         "download_url": "https://www.test.com/x/gamecenter/download?game_id=2&os=1",  // 游戏安卓客户端下载路径，仅 Android 返回
     *         "package_name": "com.missevan.app",  // 游戏安卓安装包，仅 Android 返回
     *         "package_version_code": 1  // 游戏安卓安装包版本号，仅 Android 返回
     *       }
     *     }
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [{
     *           "id": 2,
     *           "url": "https://m.missevan.com/event/1",
     *           "cover": "https://static.missevan.com/search/covers/201612/14/ee19bcaa512123dfa121608.png",
     *           "cover_color": 12434877,
     *           "create_time": 1531213891
     *         }],
     *         "pagination": {
     *           "p": 1,
     *           "pagesize": 1,
     *           "count": 5,
     *           "maxpage": 5
     *         },
     *         "ops_request_misc": "%7B%22request%5Fid%22%3A%22155436886819722120641390%22%2C%22scm%22%3A%2220140713.110018887..%22%7D"
     *       }
     *     }
     */
    public function actionSearch($s, $input_word = '', int $type, int $page = 1, int $page_size = 8, int $sort = 0,
            int $search_catalog_id = 0)
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }
        if (($s = trim($s)) === '') {
            throw new HttpException(400, '木有找到相关内容诶 T_T', 200510002);
        }
        $user_id = Yii::$app->user->id;
        switch ($type) {
            case Discovery::SEARCH_USER:
                $model_dto = Mowangskuser::getSearch($s, $page, $page_size);
                break;
            case Discovery::SEARCH_MALBUM:
                $model_dto = MAlbum::getSearch($s, $page, $page_size);
                break;
            case Discovery::SEARCH_GENERAL:
                // TODO: 迁移综合搜索逻辑至 go 项目
                $model_dto['navs'] = Yii::$app->go->searchCount($s, $user_id);
                if ($sort > 3 || $sort < 0) {
                    $sort = 0;
                }
                $free = (int)Yii::$app->request->get('free', 0);
                // $sort 排序方式 [0 => 'sound', 1 => 'view', 2 => 'time']
                $model_dto['sounds'] = MSound::getSearch($s, $page, $page_size, $user_id, $sort, $free, 0,
                    $search_catalog_id);

                // 综合搜索下综合排序、全部分区且第一页时搜索特型卡片、专题卡片、UP 主卡片和游戏卡片
                if ($page === 1 && $sort === 0 && $search_catalog_id === 0) {
                    $p = (int)Yii::$app->request->get('p', 1);
                    $search_card = SpecialSearchItems::getSearchCard($s, (int)$user_id, $p);
                    if ($special_card = $search_card['special_card']) {
                        $model_dto['special'] = $special_card;
                    }
                    // WORKAROUND: iOS >= 4.8.3 或 Android >= 5.6.9，当存在专题卡信息时返回 topic_card
                    if (!Equipment::isAppOlderThan('4.8.3', '5.6.9')
                            && ($search_topic_card = SpecialSearchItems::getSearchTopicCard($s, $page, 3))) {
                        $model_dto['topic_card'] = $search_topic_card;
                    }
                    // WORKAROUND: iOS >= 4.9.2 或 Android >= 5.7.7，当存在 UP 主卡信息时返回 up_card
                    if (!Equipment::isAppOlderThan('4.9.2', '5.7.7')
                            && $up_card = $search_card['up_card']) {
                        $model_dto['up_card'] = $up_card;
                    }
                    // WORKAROUND: iOS >= 4.9.6 或 Android >= 5.8.0，当存在游戏卡片信息时返回 game_card
                    if (!Equipment::isAppOlderThan('4.9.6', '5.8.0')
                            && $game_card = $search_card['game_card']) {
                        $model_dto['game_card'] = $game_card;
                    }
                }

                // 不存在搜索专题卡返回 dramas 信息
                if ((1 === $page) && !key_exists('topic_card', $model_dto)
                        && ($dramas = Drama::getSearch($s, $page, 3, $user_id, $sort, $search_catalog_id, true))
                        && ($dramas['Datas'] ?? [])) {
                    $model_dto['dramas'] = $dramas;
                }
                break;
            case Discovery::SEARCH_SEIY:
                $model_dto = Mowangsksoundseiy::getSearch($s, $page, $page_size);
                break;
            case Discovery::SEARCH_DRAMA:
                $model_dto = Drama::getSearch($s, $page, $page_size, $user_id);
                break;
            // 直播间的搜索是请求 fm.missevan.com/api/chatroom/search 的接口
            /*
            case Discovery::SEARCH_LIVE:
                $model_dto = Live::getSearch($s, $page, $page_size, $user_id);
                break;
            */
            default:
                throw new HttpException(400, '搜索类型不存在', 200510001);
        }
        return $model_dto;
    }

    /**
     * @api {get} /discovery/hot-search 最热搜索
     *
     * @apiVersion 0.1.0
     * @apiName hot-search
     * @apiGroup discovery
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code Error code
     * @apiSuccess {Object[]} info Result of request
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "key": "热搜词名",
     *           "level": 1,  // 热搜等级。0：普通热搜；1：超热门（超热门热搜词左侧需要显示火苗 icon，颜色也和普通热搜词不同。若同时下发了 icon_url，则优先展示 icon_url 的图标）
     *           "url": "missevan://drama/22693"  // 点击热搜词后的跳转地址
     *         },
     *         {
     *           "key": "热门好剧免费送",
     *           "level": 1,
     *           "url": "missevan://test",
     *           "icon_url": "https://static-test.test.com/icon.png",  // 热搜词左侧的 icon，下发时需要展示
     *           "trace": "{\"type\":\"luckybag\"}"  // 埋点上报字段，上报时直接透传该字段及值。未下发时该热搜词不做上报行为
     *         }
     *       ]
     *     }
     */
    public function actionHotSearch()
    {
        // 在热门搜索后台设置
        $hot_search_words = MRecommendedElements::getHotSearchWords();
        if (!$hot_search_words) {
            return [];
        }
        $hot_keywords = array_map(function ($keyword) {
            // 处理协议地址为可用地址，自动生成的热搜词链接（url）不存在，需要填充为空字符串
            $keyword['url'] = MUtils::getUsableAppLink($keyword['url'] ?? '');
            return $keyword;
        }, $hot_search_words);
        try {
            $lucky_bag_hot_word = Yii::$app->params['drama_luckybag_hot_word'] ?? null;
            if ($lucky_bag_hot_word) {
                $luckybags = Yii::$app->liveRpc->getLuckyBagDramaList(1);
                // 广播剧福袋列表不为空时，在热搜词模块的最后位置显示福袋热搜词
                if (!empty($luckybags['data'])) {
                    $level = 1;
                    // WORKAROUND: level 对 Android < 6.1.8 及 iOS < 6.1.8 下发普通热搜，是因为 < 6.1.8 下发超热门热搜词时，会在搜索词左侧展示非预期的火苗图标
                    if (Equipment::isAppOlderThan('6.1.8', '6.1.8')) {
                        $level = 0;
                    }
                    // TODO: 之后 trace 中的 ID 需要使用热搜词数据表中的 ID
                    $lucky_bag_hot_word = array_merge($lucky_bag_hot_word,
                        ['level' => $level, 'trace' => Json::encode(['type' => 'luckybag'])]);
                    $hot_keywords[] = $lucky_bag_hot_word;
                }
            }
        } catch (Exception $e) {
            Yii::error('热搜词接口获取广播剧福袋列表出错：' . $e->getMessage(), __METHOD__);
            // PASS
        }
        return $hot_keywords;
    }

    /**
     * @api {get} /discovery/topic-list{?page_size} 专题列表
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/discovery/topic-list
     * @apiSampleRequest /discovery/topic-list
     * @apiVersion 0.1.0
     * @apiName topic-list
     * @apiGroup discovery
     *
     * @apiParam {Number} [page_size=30] 每页个数
     *
     * @apiSuccess {Boolean} success true or false
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 专题列表
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [
     *           {
     *             "id": 52,
     *             "title": "广播剧铃声大放送：让男神唤醒你的清晨！",
     *             "mobile_pic_url": "http://static.missevan.com/mimages/201911/29/1a6f9c3f68c17d13f955ce945e3e4e78140647.jpg",
     *             "url": "https://www.missevan.com/mtopic/52"
     *           },
     *           {
     *             "id": 51,
     *             "title": "声音恋人：十二位星座男友你最爱哪位？",
     *             "mobile_pic_url": "http://static.missevan.com/mimages/201911/22/692738db7f1c1af6c439025589421181191258.jpg",
     *             "url": "https://www.missevan.com/mtopic/51"
     *           }
     *         ],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 1,
     *           "count": 2,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     */
    public function actionTopicList(int $page_size = DEFAULT_PAGE_SIZE)
    {
        // WORKAROUND: iOS 4.4.0 及 Android 5.3.1 版本以下使用 pagesize
        if (Equipment::isAppOlderThan('4.4.0', '5.3.1')) {
            $page_size = (int)Yii::$app->request->get('pagesize', DEFAULT_PAGE_SIZE);
        }
        $query = Topic::find()->select('id, title, mobile_pic_url')
            // 筛选出已发布并且未在列表页隐藏的专题
            ->where('(status & :status_published) AND (status & :status_list_hidden = 0)',
                [':status_published' => Topic::STATUS_PUBLISHED, ':status_list_hidden' => Topic::STATUS_LIST_HIDDEN])
            ->orderBy(['id' => SORT_DESC]);
        return MUtils::getPaginationModels($query, $page_size);
    }

    /**
     * @api {get} /discovery/suggest 搜索词联想
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/discovery/suggest
     * @apiSampleRequest /discovery/suggest
     * @apiVersion 0.1.0
     * @apiName suggest
     * @apiGroup discovery
     *
     * @apiParam {String} s 搜索关键字
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code Error code
     * @apiSuccess {String[]} info Result of request
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "suggestions": [
     *           {
     *             "word": "杀人动机",
     *             "type": 5,  // 0 音频、1 用户、2 音单、4 声优、5 剧集、6 直播间、7 搜索特型、8 频道
     *             "request_id": "158252696319723301310983"  // 阿里云搜索建议点击采集 ID
     *           },
     *           {
     *             "word": "杀死这个世界-荒城",
     *             "type": 5,
     *             "request_id": "158252696319723301310983"
     *           },
     *           {
     *             "word": "杀生院天上楽土",
     *             "type": 0,
     *             "request_id": "158252696319722133806042"
     *           },
     *           {
     *             "word": "杀死那个石家庄人",
     *             "type": 0,
     *             "request_id": "158252696319722133806042"
     *           },
     *           {
     *             "word": "杀生丸と父",
     *             "type": 0,
     *             "request_id": "158252696319722133806042"
     *           }
     *         }
     *       }
     *     }
     */
    public function actionSuggest(string $s)
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }
        if (($s = trim($s)) === '') {
            throw new HttpException(400, '木有找到相关内容诶 T_T', 200510002);
        }

        return [
            'suggestions' => Discovery::getGeneralSuggest($s),
        ];
    }

    /**
     * 获取搜索配置
     *
     * @return array
     * @throws HttpException
     */
    public static function getSearchConfig(): array
    {
        // 获取音频的一级分类
        $catalogs = Catalog::getSonCatalogs(Catalog::CATALOG_SOUND_ID);
        $catalog_names = array_column($catalogs, 'catalog_name');
        array_unshift($catalog_names, '全部分区');
        $search_catalogs = [];
        foreach ($catalog_names as $id => $catalog) {
            $search_catalogs[] = [
                'id' => $id,
                'name' => $catalog,
            ];
        }
        return ['search_catalogs' => $search_catalogs];
    }
}
