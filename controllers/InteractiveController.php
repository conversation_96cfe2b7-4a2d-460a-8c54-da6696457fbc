<?php

namespace app\controllers;

use app\components\util\Equipment;
use app\components\util\MUtils;
use app\middlewares\Controller;
use app\models\MDanmaku;
use app\models\MDanmakuRO;
use app\models\MSound;
use app\models\MSoundComment;
use app\models\MSoundNode;
use app\models\MUserLikeDanmaku;
use app\models\MUserNodeLog;
use app\components\base\filter\AccessControl;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use yii\web\HttpException;

/**
 * 封装互动广播剧相关接口
 *
 * <AUTHOR> <<EMAIL>>
 */
class InteractiveController extends Controller
{
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'send-dm' => ['post'],
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'only' => [
                'send-dm',
            ],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => [
                        'send-dm',
                    ],
                    'roles' => ['@']
                ],
            ]
        ];
        return $behaviors;
    }

    /**
     * @api {get} /interactive/node{?sound_id,node_id,node_list} 获取互动广播剧节点信息
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/interactive/node?sound_id=3674&node_id=1&node_list=1,2
     * @apiSampleRequest /interactive/node
     *
     * @apiVersion 0.1.0
     * @apiName node
     * @apiGroup interactive
     *
     * @apiParam {Number} sound_id 音频 ID
     * @apiParam {Number} node_id 节点 ID
     * @apiParam {String} [node_list=''] 历史节点 ID，以半角逗号拼接，如：1,2
     *
     * @apiSuccess {Boolean} success true or false
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "node_id": 1,
     *         "title": "加特林的价格是多少",  // 问题的标题
     *         "stay_duration": -1,  // 选择页面停留时长，单位毫秒，-1 为未做选择前一直停留
     *         "soundurl": "http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test.m4a?sign=test0",  // 原音质音频地址
     *         "soundurl_128": "http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test_128k.m4a?sign=test0",  // 128K 音质音频地址，用户选择低音质时使用
     *         "soundurl_list": [  // 开启原音音质时使用的播放地址，优先使用列表中第一个地址，若无法播放，依次尝试后续地址（没有该音质的音频时不返回该字段）
     *           "http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test.m4a?sign=test0",
     *           "http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test.m4a?sign=test1"
     *         ],
     *         "soundurl_128_list": [  // 未开启原音音质时使用的播放地址，优先使用列表中第一个地址，若无法播放，依次尝试后续地址（没有该音质的音频时不返回该字段）
     *           "http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test_128k.m4a?sign=test0",
     *           "http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test_128k.m4a?sign=test1"
     *         ],
     *         "duration": 23333,  // 音频时长，单位毫秒
     *         "front_cover": "https://test/foo.png",
     *         "node_type" 1,  // 1 代表根结点（起始），2 代表普通节点，3 代表子叶节点（结局）
     *         "lock": 0,  // 选项加锁状态，0：未加锁（可选），1：付费加锁，2：分数不够时加锁
     *         "price": 233,  // 价格（单位：钻石）
     *         "story_list": [
     *           {
     *             "id": 2,
     *             "title": "加特林大战余额宝",
     *             "front_cover": "https:://test/foo.png",
     *             "duration": 233,
     *             "node_type" 2
     *           }
     *         ],
     *         "choices": [
     *           {
     *             "id": 2,  // node ID
     *             "option": "A 不低于 50 元",  // 按钮标题
     *             "is_default": true,  // 是否为默认播放，若 stay_duration > 0，则时间到达后自动选择该选项
     *             "lock": 0  // 选项加锁状态，0：未加锁（可选），1 付费加锁，2 分数不够时加锁
     *           },
     *           {
     *             "id": 3,
     *             "option": "B 几百元",
     *             "is_default": false,
     *             "lock": 0
     *           },
     *         ],
     *         "skin": {
     *           "title_text_color": 148859,  // 标题文字颜色，十进制表示
     *           "button_text_color": 148859,  // 按钮文字颜色
     *           "button_image": "https://test/test.png"  // 按钮边框/背景图，空字符串时不使用
     *         },
     *         "pics": [  // 插图
     *           {
     *             "stime": "1.233",  // 插入时间点，单位：秒
     *             "img_url": "https://test/test.jpg",  // 图片地址
     *             "size": 102,
     *             "img_width": 690,
     *             "img_height": 976
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionNode(int $sound_id, int $node_id, string $node_list = '')
    {
        if ($sound_id <= 0 || $node_id <= 0) {
            throw new HttpException(400, '参数错误');
        }
        $node_info_key = MUtils::generateCacheKey(KEY_INTERACTIVE_NODE_INFO, $node_id);
        $node_info = MUtils2::getOrSetDistrubutedCache($node_info_key, function () use ($sound_id, $node_id) {
            $node_info['sound_node'] = MSoundNode::getNode($sound_id, $node_id);
            if (!$node_info['sound_node']) {
                throw new HttpException(404, '互动剧集节点不存在或已下线 T_T');
            }
            $node_info['node_front_cover'] = $node_info['sound_node']->front_cover;
            // 缓存中保存原协议地址，避免下发的完整资源地址不能因人而异（如某些用户需要下发海外地址、下发 mp3 等）
            $node_info[MSound::KEY_SOUNDURL] = $node_info['sound_node']->oldAttributes[MSound::KEY_SOUNDURL];
            $node_info[MSound::KEY_SOUNDURL_128] = $node_info['sound_node']->oldAttributes[MSound::KEY_SOUNDURL_128];
            // 不为子叶节点（结局）时，获取子节点
            $node_info['son_nodes'] = $node_info['sound_node']->node_type !== MSoundNode::TYPE_LEAF
                ? MSoundNode::getChoices($node_info['sound_node']) : [];
            // 获取皮肤
            $node_info['skin'] = MSoundNode::getSkin($node_info['sound_node']->sound_id);
            // 获取插图
            $node_info['pics'] = MSoundNode::getPics($node_id);
            return $node_info;
        }, TEN_MINUTE);
        // 节点音频地址调整为完整地址
        MSoundNode::getSoundSignUrls($node_info);

        $user_id = (int)Yii::$app->user->id;
        [$price, $need_pay] = MSoundNode::dramaPayInfo($node_info['sound_node']['sound_id'], $user_id);
        $lock = MSoundNode::LOCK_NO;
        $choices = [];
        if ($need_pay === MSoundNode::NODE_UNPAID && $node_info['sound_node']['pay_type'] !== MSoundNode::NODE_FREE) {
            $lock = MSoundNode::LOCK_UNPAID;
            // 付费节点未付费时，音频地址返回空字符串，播放地址列表返回空数组
            $node_info[MSound::KEY_SOUNDURL] = $node_info[MSound::KEY_SOUNDURL_64] = $node_info[MSound::KEY_SOUNDURL_128] = '';
            $node_info[MSound::KEY_SOUNDURL . '_list'] = $node_info[MSound::KEY_SOUNDURL_128 . '_list'] = [];
        } else {
            $choices = array_map(function ($son_node) use ($need_pay) {
                $lock = MSoundNode::LOCK_NO;
                if ($need_pay === MSoundNode::NODE_UNPAID && $son_node['pay_type'] !== MSoundNode::NODE_FREE) {
                    // 若剧集未付费且该子节点不免费，则锁住该选项
                    $lock = MSoundNode::LOCK_UNPAID;
                }
                return [
                    'id' => $son_node['id'],
                    'option' => $son_node['option'],
                    'is_default' => (bool)($son_node['attr'] & MSoundNode::ATTR_DEFAULT_OPTION),
                    'lock' => $lock,
                ];
            }, $node_info['son_nodes']);
        }
        $transaction = null;
        if ($node_list) {
            $transaction = Yii::$app->db->beginTransaction();
            // 若传入了故事线参数，则更新用户故事线记录
            try {
                $story_node_ids = explode(',', $node_list);
                MUserNodeLog::saveStoryList($node_id, $user_id, $sound_id, Yii::$app->equip->getEquipId(),
                    Yii::$app->equip->getBuvid() ?? '', $story_node_ids);
            } catch (\Exception $e) {
                $transaction->rollBack();
                $transaction = null;
                // 若故事线保存错误，记录到错误日志，不抛出异常避免影响用户正常收听
                // model->addError() 时 Http 状态码将被设置为 400，需要修改为 200
                Yii::$app->response->setStatusCode(200);
                Yii::error("更新用户互动广播剧故事线错误，错误原因：{$e->getMessage()}", __METHOD__);
                // PASS
            }
        }
        // 为了避免读写延迟导致查询的故事线非最新，将保存与查询放入一个事务中
        $story_list = MUserNodeLog::getStoryList($user_id, $sound_id, Yii::$app->equip->getEquipId());
        if ($transaction) {
            $transaction->commit();
        }
        $res = [
            'id' => $node_id,
            'title' => $node_info['sound_node']['title'],
            'question' => $node_info['sound_node']['question'],
            'stay_duration' => $node_info['sound_node']['stay_duration'],
            MSound::KEY_SOUNDURL => $node_info[MSound::KEY_SOUNDURL],
            MSound::KEY_SOUNDURL_64 => $node_info[MSound::KEY_SOUNDURL],
            MSound::KEY_SOUNDURL_128 => $node_info[MSound::KEY_SOUNDURL_128],
            'front_cover' => $node_info['node_front_cover'],
            'duration' => $node_info['sound_node']['duration'],
            'node_type' => $node_info['sound_node']['node_type'],
            'pay_type' => $node_info['sound_node']['pay_type'],
            'lock' => $lock,
            'price' => $price,
            'choices' => $choices,
            'story_list' => $story_list,
            'skin' => $node_info['skin'],
            'pics' => $node_info['pics'],
        ];
        if ($node_info[MSound::KEY_SOUNDURL . '_list']) {
            $res[MSound::KEY_SOUNDURL . '_list'] = $node_info[MSound::KEY_SOUNDURL . '_list'];
        }
        if ($node_info[MSound::KEY_SOUNDURL_128 . '_list']) {
            $res[MSound::KEY_SOUNDURL_128 . '_list'] = $node_info[MSound::KEY_SOUNDURL_128 . '_list'];
        }
        return $res;
    }

    /**
     * @api {post} /interactive/send-dm 发送弹幕
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/interactive/send-dm
     * @apiSampleRequest interactive/send-dm
     *
     * @apiVersion 0.1.0
     * @apiName send-dm
     * @apiGroup interactive
     *
     * @apiParam {Number} node_id 节点 ID
     * @apiParam {String} stime 弹幕出现时间
     * @apiParam {String} text 弹幕内容
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "发送成功"
     *     }
     */
    public function actionSendDm()
    {
        $node_id = (int)Yii::$app->request->post('node_id');
        $stime = Yii::$app->request->post('stime');
        $text = trim(Yii::$app->request->post('text'));
        if ($node_id <= 0 || $stime < 0) {
            throw new HttpException(400, '参数错误');
        }
        if (!$text) throw new HttpException(400, '弹幕不可为空');
        if (mb_strlen($text) > MDanmaku::MAX_DANMAKU_LENGTH) {
            throw new HttpException(400, '长度不能超过 35 个字哦 _(:з」∠)_');
        }
        if (!Yii::$app->user->isBindMobile) {
            throw new HttpException(403, '绑定手机就可以发送弹幕了哦', 100010008);
        }
        $node = MSoundNode::find()->select('id, sound_id, pay_type')->where(['id' => $node_id])->one();
        if (!$node) {
            throw new HttpException(404, '未找到该互动剧节点');
        }
        $user_id = Yii::$app->user->id;
        if (MSoundNode::PAY_TYPE_FREE !== $node->pay_type) {
            [$price, $need_pay] = MSoundNode::dramaPayInfo($node->sound_id, $user_id);
            if (MSoundNode::LOCK_UNPAID === $need_pay) {
                // 付费节点需要购买后才可发送弹幕
                throw new HttpException(403, '购买互动剧后才可以发送弹幕哦');
            }
        }
        // 与音频发送弹幕共用限制规则
        MSoundComment::limitCount($user_id);
        // 保存弹幕
        $danmaku = MDanmaku::saveDanmaku($node->sound_id, MDanmaku::ELEMENT_TYPE_NODE, $node_id, $user_id,
            $stime, $text);
        if (Equipment::isAppOlderThan('4.7.2', '5.6.1')) {
            // WORKAROUND: 旧版播放页版本（iOS < 4.7.2，Android < 5.6.1）不返回新增弹幕信息
            return Yii::t('app/base', 'Send danmaku successfully');
        }
        return [
            'id' => $danmaku->id,
            'color' => $danmaku->color,
            'mode' => $danmaku->mode,
            'pool' => $danmaku->pool,
            'size' => $danmaku->size,
            'stime' => $danmaku->stime,
            'text' => $danmaku->text,
            'element_id' => $danmaku->element_id,
            'element_type' => $danmaku->element_type,
            'user_id' => $danmaku->user_id,
        ];
    }

    /**
     * @api {get} /interactive/dm{?node_id} 弹幕接口
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/interactive/dm
     * @apiSampleRequest interactive/dm
     *
     * @apiVersion 0.1.0
     * @apiName dm
     * @apiGroup interactive
     *
     * @apiParam {Number} node_id 节点 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "like_danmaku_ids": [1, 2, 3],  // 用户点赞过的弹幕 ID
     *         "danmaku": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>
     *                       <i>
     *                         <source>mowangsk</source>
     *                         <d p=\"0,1,25,16777215,1498545816,0,10,708281\">弹幕 1</d>
     *                         <d p=\"0.56,1,25,16777215,1498545816,0,6,708278\">弹幕 2</d>
     *                       </i>
     *                     </xml>"
     *       }
     *     }
     */
    public function actionDm(int $node_id)
    {
        if ($node_id <= 0) {
            throw new HttpException(400, '参数错误');
        }
        $key = MUtils2::generateCacheKey(KEY_INTERACTIVE_DM_NODE_ID, $node_id);
        $danmaku = MUtils2::getOrSetDistrubutedCache(
            $key,
            function () use ($node_id) {
                $dm = MDanmakuRO::find()
                    ->select('id, stime, text, mode, size, color, pool, user_id, create_time')
                    ->where(['element_id' => $node_id, 'element_type' => MDanmaku::ELEMENT_TYPE_NODE])
                    ->asArray()
                    ->all();
                return $this->renderPartial('/interactive/dm', [
                    'dm' => $dm
                ]);
            },
            FIVE_MINUTE,
            function ($err) use ($node_id) {
                // 错误信息记录到日志，弹幕返回空数组进行服务降级，避免缓存穿透
                Yii::error("获取互动剧节点（{$node_id}）弹幕出错：" . $err->getMessage(), __METHOD__);
                return $this->renderPartial('/interactive/dm', [
                    'dm' => []
                ]);
            }
        );
        if (Equipment::isAppOlderThan('4.7.2', '5.6.1')) {
            // WORKAROUND: 旧版播放页版本（iOS < 4.7.2，Android < 5.6.1）不返回用户点赞弹幕信息
            return $danmaku;
        }
        $like_danmaku_ids = [];
        if ($user_id = Yii::$app->user->id) {
            $like_danmaku_ids = MUserLikeDanmaku::getUserLikeDanmakuIds(MUserLikeDanmaku::ELEMENT_TYPE_NODE,
                $node_id, $user_id);
        }
        return [
            'danmaku' => $danmaku,
            'like_danmaku_ids' => $like_danmaku_ids
        ];
    }
}
