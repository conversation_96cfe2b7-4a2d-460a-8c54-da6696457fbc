<?php

namespace app\controllers;

use app\components\util\Equipment;
use app\components\util\MUtils;
use app\forms\AnFeedbackForm;
use app\middlewares\Controller;
use app\models\AnFeedback;
use app\models\AnFeedbackTicket;
use Yii;
use yii\filters\VerbFilter;
use yii\web\HttpException;
use yii\web\UploadedFile;

class FeedbackController extends Controller
{
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'add-image' => ['post'],
                'reply-ticket' => ['post'],
                'create-ticket' => ['post'],
            ],
        ];
        return $behaviors;
    }

    /**
     * @api {post} /feedback/reply-ticket{?ticked_id} 意见反馈
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/feedback/reply-ticket
     * @apiSampleRequest /feedback/reply-ticket
     * @apiDescription ticket_id = 0 是与客服私信（原意见反馈）
     *
     * @apiVersion 0.1.0
     * @apiName reply-ticket
     * @apiGroup feedback
     *
     * @apiParam {Files} [image_files[]] 反馈图片
     * @apiParam {String} [content] 补充的反馈内容，补充上传反馈图片时，这个内容是小尾巴的内容
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "我们已经收到你的意见"
     *     }
     */
    public function actionReplyTicket(int $ticket_id = AnFeedback::FEEDBACK_DIRECT)
    {
        $content = trim(Yii::$app->request->post('content'));
        $timestamp = strtotime(Yii::$app->request->headers['X-M-Date']);
        // 1：安卓；2：iOS
        $equip = Yii::$app->equip;
        $equip_id = $equip->getEquipId();
        $client = $equip->getOs();
        $user_id = (int)Yii::$app->user->id;
        $images = UploadedFile::getInstancesByName('image_files');
        if (empty($images)) {
            $feedback_form = new AnFeedbackForm(['scenario' => AnFeedbackForm::SCENARIO_REPLY_CONTENT]);
        } else {
            $feedback_form = new AnFeedbackForm(['scenario' => AnFeedbackForm::SCENARIO_REPLY_IMAGES]);
            $feedback_form->images = $images;
        }
        $feedback_form->content = $content;
        $feedback_form->ticket_id = $ticket_id;
        $feedback_form->equip_id = $equip_id;
        $feedback_form->buvid = $equip->getBuvid() ?? '';
        $feedback_form->client = $client;
        $feedback_form->user_id = $user_id;
        $feedback_form->timestamp = $timestamp;
        if (!$feedback_form->validate()) {
            throw new HttpException(400, MUtils::getFirstError($feedback_form));
        }
        $feedback_form->replyTicket();
        return '我们已经收到你的意见';
    }

    /**
     * @api {post} /feedback/add-image 反馈上传图片接口
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/feedback/add-image
     * @apiSampleRequest feedback/add-image
     * @apiVersion 0.1.0
     * @apiName add-image
     * @apiGroup feedback
     *
     * @apiParam {Number} feedback_id 反馈 ID
     * @apiParam {number=0,1} [retry=0] 是否是重复发送的请求 0：否；1：是
     * @apiParam {Number} [images_num=0] 多图上传时，用户选择上传的图片数量
     * @apiParam {Number} [image_index=0] 多图上传时，图片位置
     * @apiParam {File} image_file 反馈图片
     * @apiParam {String} equipment 小尾巴
     * @apiParam {Number} [ticket_id=0] 针对补充的工单 ID
     * @apiParam {number=0,1,2,3,4,5,6,7,8} [type=0] 反馈类型，默认是与客服私信（原意见反馈）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Number} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": 9946
     *     }
     */
    public function actionAddImage()
    {
        $feedback_id = (int)Yii::$app->request->post('feedback_id');
        $retry = (int)Yii::$app->request->post('retry', 0);
        $images_num = (int)Yii::$app->request->post('images_num', 0);
        $image_index = (int)Yii::$app->request->post('image_index', 0);
        $equipment = trim(Yii::$app->request->post('equipment'));
        $user_id = (int)Yii::$app->user->id;
        return AnFeedback::createImageFeedback($feedback_id, $images_num, $image_index, $retry, $equipment, $user_id);
    }

    /**
     * @api {post} /feedback/create-ticket BUG 反馈接口
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/feedback/create-ticket
     * @apiSampleRequest feedback/create-ticket
     * @apiVersion 0.1.0
     * @apiName create-ticket
     * @apiGroup feedback
     *
     * @apiParam {Number} type 反馈类型 \
     * 1：其他；\
     * 2：播放问题；\
     * 3：闪退；\
     * 4：注册登录问题；\
     * 5：购买支付；\
     * 6：下载问题；\
     * 7：首页；\
     * 8：直播问题
     * @apiParam {String} content 反馈内容
     * @apiParam {Files} [image_files[]] 反馈图片
     * @apiParam {String} [qq] QQ 联系方式
     * @apiParam {String} [mobile] 手机联系方式
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Number} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": 1
     *     }
     * @apiErrorExample {json} Error-Response:
     *     {
     *       "success": false,
     *       "code": 201010002,
     *       "info": "问题描述不能少于 10 个字哦"
     *     }
     */
    public function actionCreateTicket()
    {
        $type = Yii::$app->request->post('type');
        $content = trim(Yii::$app->request->post('content'));
        $qq = trim(Yii::$app->request->post('qq'));
        $mobile = trim(Yii::$app->request->post('mobile'));
        $timestamp = strtotime(Yii::$app->request->headers['X-M-Date']);
        $client = Yii::$app->equip->getOs();
        $equip_id = Yii::$app->equip->getEquipId();
        $user_id = (int)Yii::$app->user->id;
        $images = UploadedFile::getInstancesByName('image_files');
        $feedback_form = new AnFeedbackForm(['scenario' => AnFeedbackForm::SCENARIO_CREATE]);
        $feedback_form->images = $images;
        $feedback_form->content = $content;
        $feedback_form->type = $type;
        $feedback_form->equip_id = $equip_id;
        $feedback_form->buvid = Yii::$app->equip->getBuvid() ?? '';
        $feedback_form->client = $client;
        $feedback_form->user_id = $user_id;
        $feedback_form->timestamp = $timestamp;
        $feedback_form->qq = $qq;
        $feedback_form->mobile = $mobile;
        if (!$feedback_form->validate()) {
            throw new HttpException(400, MUtils::getFirstError($feedback_form));
        }
        $ticket_id = $feedback_form->createTicket();
        return $ticket_id;
    }

    /**
     * @api {get} /feedback/list-ticket 获取反馈记录
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/feedback/list-ticket
     * @apiSampleRequest feedback/list-ticket
     * @apiVersion 0.1.0
     * @apiName list-ticket
     * @apiGroup feedback
     *
     * @apiParam {Number} [page_size=20] 每页个数
     * @apiParam {Number} [page=1] 页数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [
     *           {
     *             "id": 10095,
     *             "content": "播放yuytyu了啊怎么办1111",
     *             "status_name": "待回复",
     *             "status_notice": 0,
     *             "create_time": 1551088498
     *           },
     *           {
     *             "id": 10094,
     *             "content": "播放不了啊怎gjhgj么办",
     *             "status_name": "已回复",
     *             "status_notice": 1,
     *             "create_time": 1551087015
     *           },
     *           {
     *             "id": 10089,
     *             "content": "播放不了啊111",
     *             "status_name": "1 条新消息",
     *             "status_notice": 1,
     *             "create_time": 1551085608
     *           }
     *         ],
     *         "pagination": {
     *           "p": 1,
     *           "count": 7,
     *           "maxpage": 1,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     */
    public function actionListTicket(int $page_size = PAGE_SIZE_20)
    {
        $user_id = Yii::$app->user->id;
        return $user_id ? AnFeedbackTicket::listTicketByUserId($user_id, $page_size)
            : AnFeedbackTicket::listTicketByEquipId(Yii::$app->equip->getEquipId(), $page_size);
    }

    /**
     * @api {get} /feedback/get-ticket 根据反馈 ID 获取反馈详情
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/feedback/get-ticket
     * @apiSampleRequest feedback/get-ticket
     * @apiVersion 0.1.0
     * @apiName get-ticket
     * @apiGroup feedback
     *
     * @apiParam {Number} ticket_id 工单 ID
     * @apiParam {Number} [page_size=20] 每页个数
     * @apiParam {Number} [page=1] 页数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [
     *           {
     *             "id": 10089,
     *             "content": "播放不了啊111",
     *             "create_time": 1551085608,
     *             "status": 0,
     *             "ticket_id": 3,
     *             "type": 4,
     *             "images": [],
     *             "type_name": "播放问题",
     *             "username": "23336666",
     *             "iconurl": "http://static.missevan.com/avatars/201810/15/abd71a372a869d1c5045a920c38111c2119.jpg"
     *           },
     *           {
     *             "id": 1,
     *             "content": "您好~M娘已经接收到了您的反馈意见，并把您的反馈......化和改进~希望您继续支持M站",
     *             "create_time": 1551085608,
     *             "username": "M娘",
     *             "iconurl": "http://static.missevan.com//mimagesmini/201903/04/adde6a13f2d1c50d1c50c2cc172400.png"
     *           },
     *           {
     *             "id": 10093,
     *             "content": "播放不了啊怎ghjhjgh么办",
     *             "create_time": 1551086365,
     *             "status": 0,
     *             "ticket_id": 3,
     *             "type": 4,
     *             "images": [
     *               {
     *                 "origin": "https://static.missevan.com/mimages/201809/27/cd5b92b83731d380b490ea2e9105159.jpg",
     *                 "mini": "https://static.missevan.com/mimagesmini/201809/27/7b92b83731d380b490ea2e9105159.jpg",
     *                 "width": 500,
     *                 "height": 500
     *               },
     *               {
     *                 "origin": "https://static.missevan.com/mimages/201810/08/05d6d6d5577f804280d28560f093652.jpg",
     *                 "mini": "https://static.missevan.com/mimagesmini/201810/08/5d6d6d5577f804280d28560f03652.jpg",
     *                 "width": 500,
     *                 "height": 303
     *               }
     *             ],
     *             "type_name": "播放问题",
     *             "username": "23336666",
     *             "iconurl": "http://static.missevan.com/avatars/201810/15/abd71a372a869d1c504c1e20c381b112119.jpg"
     *           },
     *           {
     *             "id": 10097,
     *             "content": "回复回复",
     *             "create_time": 1551172012,
     *             "status": 4,
     *             "ticket_id": 3,
     *             "type": 0,
     *             "username": "M娘",
     *             "iconurl": "http://static.missevan.com//mimagesmini/201903/04/adde6a13f2c2c17d1c50d1c502400.png"
     *           }
     *         ],
     *         "pagination": {
     *           "p": 1,
     *           "count": 4,
     *           "maxpage": 1,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     */
    public function actionGetTicket(int $ticket_id, int $page_size = PAGE_SIZE_20)
    {
        $user_id = Yii::$app->user->id;
        $result = $user_id ? AnFeedback::getTicketByUserId($ticket_id, $user_id, $page_size)
            : AnFeedback::getTicketByEquipId($ticket_id, Yii::$app->equip->getEquipId(), $page_size);
        if (!$result) {
            throw new HttpException(404, '反馈记录不存在');
        }
        return $result;
    }

    /**
     * @api {get} /feedback/get-notice 获取入口消息提醒
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/feedback/get-notice
     * @apiSampleRequest feedback/get-notice
     * @apiDescription notice 为 0 时，对应的入口不显示消息提示；notice 由 0 变为 1 时，客户端需在记录 last_time，
     * 下次请求获取的 last_time 和上次记录的一样则对应的入口不显示消息提示，反之显示
     * @apiVersion 0.1.0
     * @apiName get-notice
     * @apiGroup feedback
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "direct": {
     *           "notice": 0,
     *           "last_time": 0
     *         },
     *         "bug": {
     *           "notice": 1,
     *           "last_time": 1556593814
     *         }
     *       }
     *     }
     */
    public function actionGetNotice()
    {
        $user_id = Yii::$app->user->id;
        return $user_id ? AnFeedbackForm::getNoticeByUserId($user_id)
            : AnFeedbackForm::getNoticeByEquipId(Yii::$app->equip->getEquipId());
    }
}
