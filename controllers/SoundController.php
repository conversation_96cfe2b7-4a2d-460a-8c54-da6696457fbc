<?php
/**
 * Created by PhpStorm.
 * User: zz
 * Date: 2017/7/19
 * Time: 14:38
 */

namespace app\controllers;

use app\components\controllers\SoundInterface;
use app\components\util\Equipment;
use app\components\util\MUtils;
use app\middlewares\Controller;
use app\models\Blacklist;
use app\models\CarnivalActivity;
use app\models\Catalog;
use app\models\Drama;
use app\models\MAlbum;
use app\models\MAttentionUser;
use app\models\MLikeSound;
use app\models\Mowangskuser;
use app\models\MPowerSound;
use app\models\MSound;
use app\models\MSoundAlbumMap;
use app\models\MSoundComment;
use app\models\MTag;
use app\models\MTagSoundMap;
use app\models\MUserHistory;
use app\models\MUserNodeLog;
use app\models\MUserLikeDanmaku;
use app\models\MUserUnlockElement;
use app\models\MUserVip;
use app\models\ReturnModel;
use app\models\MSoundPlayLog;
use app\components\base\filter\AccessControl;
use app\models\SoundVideo;
use app\models\TransactionLog;
use app\models\TransactionSoundLog;
use Exception;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use yii\web\HttpException;
use yii\web\NotFoundHttpException;

class SoundController extends Controller implements SoundInterface
{

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'like' => ['post'],
                'cancel-like' => ['post'],
                'batch-like' => ['post'],
                'batch-cancel-like' => ['post'],
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'only' => ['collect', 'like', 'cancel-like', 'ts', 'batch-like', 'batch-cancel-like'],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => ['collect', 'like', 'cancel-like', 'ts', 'batch-like', 'batch-cancel-like'],
                    'roles' => ['@']
                ],
            ]
        ];
        return $behaviors;
    }

    /**
     * @api {get} /sound/get-dm{?sound_id} 根据单音 ID 获取弹幕
     * @apiDescription 获取音频下的弹幕
     * 弹幕需求文档地址：https://github.com/MiaoSiLa/requirements-documents/blob/master/弹幕功能/弹幕优化文档.md
     *
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/sound/get-dm
     * @apiSampleRequest /sound/get-dm
     *
     * @apiVersion 0.1.0
     * @apiName get-dm
     * @apiGroup sound
     *
     * @apiParam {Number} sound_id 单音 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "like_danmaku_ids": [1, 2, 3],  // 用户点赞过的弹幕 ID
     *         "danmaku": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>
     *                       <i>
     *                         <source>mowangsk</source>
     *                         <d p=\"0,1,25,16777215,1498545816,0,10,708281\">弹幕 1</d>
     *                         <d p=\"0.56,1,25,16777215,1498545816,0,6,708278\">弹幕 2</d>
     *                       </i>
     *                     </xml>"
     *       }
     *     }
     */
    public function actionGetDm(int $sound_id)
    {
        $danmaku = '';
        if ($sound_id <= 0) {
            throw new HttpException(400, '参数错误');
        }
        $get_dm = false;
        $user_id = 0;
        if ($sound_id && MSound::checkSoundId($sound_id)) {
            $sound = $this->getOrSetDmSoundCache($sound_id);
            if ($sound) {
                $user_id = (int)Yii::$app->user->id;
                $sound_free = MSound::SOUND_FREE === $sound['pay_type'];
                $sound_expired = MSound::CHECKED_CONTRACT_EXPIRED === $sound['checked'];
                if ($sound_free && !$sound_expired) {
                    // 审核通过的免费音频可获取弹幕
                    $get_dm = true;
                } elseif ($user_id) {
                    if ($user_id === $sound['user_id']) {
                        // 音频 UP 主可获取弹幕
                        $get_dm = true;
                    } else {
                        $drama_id = $sound['drama_id'] ?? 0;
                        if ($drama_id) {
                            if ($sound['pay_type'] === MSound::PAY_BY_SOUND) {
                                // 已购的单集付费音频可获取弹幕
                                $get_dm = TransactionSoundLog::checkUserPaidDramaSound($user_id, $drama_id, $sound['id']);
                            } else {
                                // 已购的整剧付费音频或已购的合约期满下架免费音频可获取弹幕
                                $get_dm = TransactionLog::checkUserPaidDrama($user_id, $drama_id);
                            }
                            if (!$get_dm) {
                                $get_dm = MUserUnlockElement::isSoundUnlocked($user_id, $sound['id']);
                            }
                        }
                    }
                }
            }
            if ($get_dm) {
                $danmaku = $this->getSoundDm($sound_id);
            }
        } else {
            $danmaku = $this->renderPartial('dm', [
                'has_subtitle' => MSoundComment::HAS_NOT_SUBTITLE,
                'sound_id' => $sound_id,
                'sound_dms' => [],
            ]);
        }
        if (Yii::$app->equip->isFromMiMiApp() || Equipment::isAppOlderThan('4.7.2', '5.6.1')) {
            // WORKAROUND: MiMi 或旧版播放页版本（iOS < 4.7.2，Android < 5.6.1）不返回用户点赞弹幕信息
            return $danmaku;
        }
        $like_danmaku_ids = [];
        if ($get_dm && $user_id) {
            $like_danmaku_ids = MUserLikeDanmaku::getUserLikeDanmakuIds(MUserLikeDanmaku::ELEMENT_TYPE_SOUND,
                $sound_id, $user_id);
        }
        return [
            'danmaku' => $danmaku,
            'like_danmaku_ids' => $like_danmaku_ids
        ];
    }

    /**
     * 获取或设置 xml 格式音频弹幕
     *
     * @param int $sound_id 音频 ID
     * @return string 弹幕信息
     */
    private function getSoundDm(int $sound_id): string
    {
        // TODO: 去掉 key 中无用的占位符
        $key = MUtils2::generateCacheKey(KEY_DM_SOUND_ID_EXMINED, $sound_id, '');
        return MUtils2::getOrSetDistrubutedCache(
            $key,
            function () use ($sound_id) {
                $sound_dms = MSoundComment::getDm($sound_id);
                // 获取是否含有字幕级别的弹幕
                $has_subtitle = MSoundComment::HAS_NOT_SUBTITLE;
                foreach ($sound_dms as $dm) {
                    if ((int)$dm['pool'] === MSoundComment::POOL_SUBTITLE) {
                        $has_subtitle = MSoundComment::HAS_SUBTITLE;
                        break;
                    }
                }
                return $this->renderPartial('dm', [
                    'has_subtitle' => $has_subtitle,
                    'sound_id' => $sound_id,
                    'sound_dms' => $sound_dms,
                ]);
            },
            FIVE_MINUTE,
            function ($err) use ($sound_id) {
                // 错误信息记录到日志，弹幕返回空数组进行服务降级，避免缓存穿透
                Yii::error('获取弹幕出错：' . $err->getMessage(), __METHOD__);
                return $this->renderPartial('dm', [
                    'has_subtitle' => MSoundComment::HAS_NOT_SUBTITLE,
                    'sound_id' => $sound_id,
                    'sound_dms' => [],
                ]);
            }
        );
    }

    /**
     * 获取或设置获取弹幕要用到的音频信息缓存
     *
     * @param int $sound_id 音频 ID
     * @return ?array 音频信息，付费音和合约期满下架的免费音会返回其所在剧集 ID
     */
    private function getOrSetDmSoundCache(int $sound_id): ?array
    {
        $memcache = Yii::$app->memcache;
        $key = MUtils::generateCacheKey(KEY_DM_SOUND_INFO, $sound_id);
        $sound = $memcache->get($key);
        if ($sound !== false) {
            return Json::decode($sound);
        } else {
            $checked = [MSound::CHECKED_PASS, MSound::CHECKED_CONTRACT_EXPIRED];
            $sound = MSound::find()
                ->select('id, pay_type, checked, user_id')
                ->where(['id' => $sound_id, 'checked' => $checked])
                ->asArray()
                ->one();
            $duration = ONE_MINUTE;  // 查不到音频信息时缓存为一分钟
            if ($sound) {
                $sound = array_map('intval', $sound);
                $sound_free = MSound::SOUND_FREE === $sound['pay_type'];
                $sound_expired = MSound::CHECKED_CONTRACT_EXPIRED === $sound['checked'];
                if (!$sound_free || $sound_expired) {
                    // 付费音或合约期满下架音需要判断付费状态，因此需获取其所在剧集 ID
                    $sound['drama_id'] = (int)Drama::getDramaIdBySoundId($sound_id);
                }
                $duration = FIVE_MINUTE;
            }
            $memcache->set($key, Json::encode($sound), $duration);
            return $sound;
        }
    }

    /**
     * @api {get} /sound/sound 根据 ID 获取单音
     * @apiDescription  /mobile/site/singleSound 使用 ID 获取单音 参数：用户 token 和 soundId
     * @apiDescription  comments_count 父评论数
     * @apiDescription  sub_comments_count 子评论数
     * @apiDescription  comment_count 弹幕数
     * @apiDescription  all_comments 弹幕 评论 子评论
     * @apiDescription  comments_num 评论 子评论
     *
     * @apiVersion 0.1.0
     * @apiName sound
     * @apiGroup sound
     *
     * @apiParam {Number} sound_id 单音 ID
     * @apiParam {Number} [download=0] 是否下载 0：音频播放（音频播放功能的时候不传此参数）；1：下载音频；2：下载铃声；
     * @apiParam {number=1,2} [play=1] 播放场景 1：普通播放；2: 优先播放
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info": {
     *         "id": 13811,
     *         "catalog_id": 4,
     *         "create_time": 1410787957,
     *         "last_update_time": 1499737998,
     *         "duration": 542770,
     *         "user_id": 62550,
     *         "username": "少伯",
     *         "animationid": 0,
     *         "characterid": 0,
     *         "seiyid": 0,
     *         "soundstr": "少伯的电台·第一期·是时候暴露真正的年龄了",
     *         "intro": "",
     *         "soundurl": "http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test.m4a?sign=test0",  // 音频不可播放时（如需要付费、播放设备受限等情况）不下发
     *         "soundurl_32": "http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test_32k.m4a?sign=test0",  // 音频不可播放时（如需要付费、播放设备受限等情况）不下发
     *         "soundurl_64": "http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test.m4a?sign=test0",  // 音频不可播放时（如需要付费、播放设备受限等情况）不下发
     *         "soundurl_128": "http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test_128k.m4a?sign=test0",  // 音频不可播放时（如需要付费、播放设备受限等情况）不下发
     *         "soundurl_list": [  // 开启原音音质时使用的播放地址，优先使用列表中第一个地址，若无法播放，依次尝试后续地址（没有该音质的音频或音频不可播放时不返回该字段）
     *           "http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test.m4a?sign=test0",
     *           "http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test.m4a?sign=test1"
     *         ],
     *         "soundurl_128_list": [  // 未开启原音音质时使用的播放地址，优先使用列表中第一个地址，若无法播放，依次尝试后续地址（没有该音质的音频或音频不可播放时不返回该字段）
     *           "http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test_128k.m4a?sign=test0",
     *           "http://upos.test.com/maoerplayurl/mefmxcodeboss/aod/test_128k.m4a?sign=test1"
     *         ],
     *         "subtitle_url": "https://static-test.missevan.com/subtitle/202103/30/cb18f9826e6572377f006485708a2379203344.json",  // 字幕 URL，没有字幕下发空字符串或音频不可播放时（如需要付费、播放设备受限等情况）不下发
     *         "downtimes": 4,
     *         "uptimes": 31,
     *         "checked": 1,
     *         "source": 1,
     *         "download": 0,
     *         "view_count": 9410,
     *         "comment_count": 186,
     *         "favorite_count": 8,
     *         "point": 41,
     *         "push": 2,
     *         "refined": 1,
     *         "comments_count": 2,
     *         "sub_comments_count": 0,
     *         "pay_type": 0,
     *         "all_comments": 188,
     *         "comments_num": 2,
     *         "front_cover": "https://static.missevan.com/coversmini/201409/16/ed5034bdca744dfde2e188a968b7102c210953.jpg",
     *         "liked": 0,
     *         "collected": 1,
     *         "followed": 0,
     *         "authenticated": 1,
     *         "confirm": 5,
     *         "type": 2,  // 音频类型，0：普通音频；1：音乐集音频；2：互动广播剧音频
     *         "interactive_node_id": 233,  // 互动广播剧根节点 ID
     *         "catalog": {
     *           "id": 4,
     *           "catalog_name_alias": "character",
     *           "tags": "逗比"
     *         },
     *         "tags": [
     *           {
     *             "id": 59,
     *             "name": "数码宝贝"
     *           }
     *         ],
     *         "pics": [
     *           {
     *             "stime": "19.133",
     *             "date": 1410788434,
     *             "size": 771,
     *             "img_url": "https://static.missevan.com/201409/15/aff17dc828353322d248e3100c352f62214028.jpg",
     *             "img_width": "1920",
     *             "img_height": "1200"
     *           }
     *         ],
     *         "iconurl": "https://static.missevan.com/avatars/201506/15/ce759afba298329f6ade7e01db087f53121245.jpg",
     *         "need_pay": 0,
     *         "price": 0,  // 整剧付费或单集付费的价格。单位：钻
     *         "vip_discount": {  // 剧集折扣信息，当付费剧有会员折扣时返回
     *           "rate": 0.8,  // 会员折扣值。折扣后若出现小数点，则用户仅需支付整数部分金额（小数点后金额直接舍去）
     *                         // 单集付费时，客户端先汇总要购买的单集价格然后使用这个值来计算购买后的价格
     *                         // 剧集的折扣限制由 site/config 下发
     *           "price": 287  // 整剧付费剧集的会员折扣价格，仅在整剧付费时下发。单位：钻
     *         },
     *         "episode_vip": 2,  // 会员单集状态。0：非会员单集；1：会员剧下的试听单集；2：会员剧下的非试听单集
     *         "fansnum": 25056,
     *         "drama_id": 9888,
     *         "episode": {  // 仅音频属于剧集时下发
     *           "id": 233,
     *           "name": "第一集",  // 单集名称
     *           "order": 1,  // 单集排序
     *           "drama_id": 52400,  // 剧集 ID
     *           "drama_name": "天官赐福"  // 剧集名称
     *         },
     *         "ringtone": 0,  // 铃声设置，第一位为 1 时显示设置铃声按钮，第二位为 1 表示下载时在铃声目录下单独保存一份完整的未加密音频
     *         "video_info": {
     *           "priority": 1,  // 是否优先于音频播放，0：否；1：是
     *           "duration": 1000,  // 视频时长，单位 ms
     *           "resources": [
     *             {
     *               "quality": 128,  // 视频质量，16：360P；32：480P；64：720P；128：1080P
     *               "name": "1080P 高清",
     *               "url": "https://www.test.con/xxx/xxx.mp4",
     *               "size": 1024,  // 视频大小，单位 Bytes
     *               "status": 1,  // 视频播放状态，比特位第一位为 1 时表示需要登录后才能播放
     *               "width": 1920,  // 视频宽度
     *               "height": 1080  // 视频高度
     *             }
     *           ]
     *         },
     *         "limit_type": 1,  // 限制播放类型。1：超过同时播放设备上限限制；2：会员收听限制；3：整剧付费限制；4：单集付费限制；无限制时不下发
     *         "user": {  // UP 主信息
     *           "user_id": 62550,
     *           "username": "少伯",
     *           "confirm": 5,
     *           "authenticated": 1,
     *           "is_vip": 1  // UP 主是否为会员。0：否；1：是
     *         }
     *       }
     *     }
     *
     * @apiErrorExample {json} Error-Response: 下载或设置铃声时，同时播放设备超限错误
     *     HTTP/1.1 400
     *     {
     *       "success": false,
     *       "code": 200110204,
     *       "info": "同时播放设备超限"
     *     }
     */
    public function actionSound(int $sound_id, int $download = 0, int $play = MSound::PLAY_NORMAL)
    {
        // TODO: 后续需要将下载的相关逻辑，迁移到新的接口
        // 音频播放
        $PLAY_SOUND = 0;
        // 下载音频
        $DOWNLOAD_SOUND = 1;
        // 设置铃声
        $DOWNLOAD_RINGTONE = 2;

        if ($sound_id <= 0 || !in_array($download, [$PLAY_SOUND, $DOWNLOAD_SOUND, $DOWNLOAD_RINGTONE])) {
            throw new HttpException(400, '参数错误');
        }
        if (Equipment::isBanVersion()) {
            // WORKAROUND: 老版本返回提示需要升级的音频
            $sound_id = Yii::$app->params['sounds_for_notice']['upgrade_app'];
        } elseif (Yii::$app->user->inBlacklist(Blacklist::BAN_TIME_FOREVER_LISTEN)) {  // 检查黑名单用户
            $sound_id = Yii::$app->params['sounds_for_notice']['user_in_blacklist'];
        }
        $download_origin = $download;
        if (Equipment::isAppOlderThan(null, '5.0.6')) {
            // WORKAROUND: 安卓 5.0.6 之前的版本需要将下载参数设置默认 1
            $download = $DOWNLOAD_SOUND;
        }
        $sound = MSound::singleSound($sound_id, $download, false, $play);
        if (is_int($sound)) {
            // WORKAROUND: mimi 旧版本 Android 及 iOS 302 跳转失败，直接用跳转音的数据进行替换
            if (Equipment::isMiMiAppOlderThan('1.0.2', '1.0.2')) {
                $sound = MSound::find()
                    ->alias('t')
                    ->select('t.*, t1.boardiconurl iconurl, t1.soundnumchecked soundnum, t1.fansnum, t1.confirm,
                        t1.avatar avatar, t1.icontype')
                    ->leftJoin(Mowangskuser::tableName() . ' AS t1', 't.user_id = t1.id')
                    ->where('t.id = :id', [':id' => $sound])->one();
                unset($sound->cover_image);
                return $sound;
            }
            if ($download) {
                if ($sound === Yii::$app->params['sounds_for_notice']['equip_play_num_limit']) {
                    throw new HttpException(400, '同时播放设备超限', 200110204);
                }
                if ($sound === Yii::$app->params['sounds_for_notice']['upgrade_app']) {
                    throw new HttpException(400, '请更新 App 后重试');
                }
                // 下载音频或设置铃声时，抛出异常
                $error_msg = $download === $DOWNLOAD_SOUND ? '音频下载失败' : '铃声设置失败';
                throw new HttpException(400, $error_msg, 200110203);
            }
            header(sprintf('Location: /sound/sound?sound_id=%d', $sound));
            return;
        }
        if ($download === $DOWNLOAD_SOUND && Equipment::isAppOlderThan('', '6.1.2')
                && MSound::SOUND_FREE === $sound->need_pay && $sound->isDramaPaid) {
            // WORKAROUND: 在下载场景 Android < 6.1.2 时，免费音频 need_pay 属性值需要兼容客户端下载权限判定：
            // 音频属于免费剧集或单集付费剧集：不需要更改
            // 音频属于整剧付费剧集：仅对已购该剧用户，下发 need_pay 为 MSound::SOUND_PAID
            $sound->need_pay = MSound::SOUND_PAID;
        }
        if ($download === $DOWNLOAD_RINGTONE) {
            $this->checkDownloadRingtone($sound);
        } elseif ($download && !Equipment::isAppOlderThan('6.2.6', '6.2.6')) {  // WORKAROUND: iOS < 6.2.6、Android < 6.2.6 需要返回音频信息
            if ($sound->type === MSound::TYPE_INTERACTIVE) {
                // iOS >= 6.2.6、Android >= 6.2.6 客户端版本下载互动剧音频提示错误
                throw new HttpException(403, '互动剧暂不支持下载');
            }
            if ($sound->limit_type === MSound::LIMIT_TYPE_VIP_PLAY) {
                // iOS >= 6.2.6、Android >= 6.2.6 客户端版本下载会员音提示错误
                throw new HttpException(403, '请开通会员后下载会员内容', 200110206);
            }
            if (in_array($sound->limit_type, [MSound::LIMIT_TYPE_DRAMA, MSound::LIMIT_TYPE_EPISODE])) {
                // iOS >= 6.2.6、Android >= 6.2.6 客户端版本下载未付费音频提示错误
                throw new HttpException(403, '请购买后继续下载付费内容', 200110205);
            }
            if ($sound->limit_type === MSound::LIMIT_TYPE_EQUIP_PLAY) {
                // 未购付费音需要提示购买，已购后触发下载受限提示
                throw new HttpException(400, '同时播放设备超限', 200110204);
            }
        }

        // WORKAROUND: 如果是催眠，则取消标签推荐
        if (in_array($sound->catalog_id, [Catalog::CATALOG_ID_ASMR_CN, Catalog::CATALOG_ID_ASMR_OVERSEAS, Catalog::CATALOG_ID_ASMR_NOVOICE,
            Catalog::CATALOG_ID_SOUND_NATURAL])) {
            $sound->tags = [];
        }

        // 特殊的单音不添加到播放记录
        if (!in_array($sound['id'], Yii::$app->params['sounds_for_notice']) && !$download_origin) {
            if (Equipment::isAppVersion(Equipment::Android, '5.6.0')) {
                // WORKAROUND: 安卓 5.6.0 会在进入播放页时，多次调用 sound/sound 接口，加播放量时需要特殊处理
                $redis = Yii::$app->redis;
                $lock = $redis->generateKey(LOCK_SOUND_ADD_PLAY_TIMES_EQUIP_ID_SOUND_ID, Yii::$app->equip->getEquipId(), $sound_id);
                if ($redis->lock($lock, 2)) {
                    // 添加音频播放量
                    $sound->addPlayTimes(1);
                }
            } else {
                // 添加音频播放量
                $sound->addPlayTimes(1);
            }
            if (Yii::$app->user->id) {
                MSound::DoYouLike($sound);
                // 自建音单里是否收藏该单音
                MSound::DoYouCollect($sound);
                $sound->followed = MAttentionUser::DoYouAttention($sound->user_id);
            }
        }
        // 删除 cover_image, iOS >= 4.4.1 没有问题，Android >= 5.3.1 没有问题
        unset($sound->cover_image);
        // 铃声设置
        $sound->ringtone = MSound::getRingtone($sound);
        if ($sound->video && SoundVideo::isUsableVersion()) {
            // 支持原生视频播放版本需要返回视频信息
            // TODO: 视频播放地址可以与音频地址一起去做签名
            $sound->video_info = SoundVideo::getVideoInfo($sound, (int)Yii::$app->user->id);
        }

        if (in_array($download, [$DOWNLOAD_SOUND, $DOWNLOAD_RINGTONE])) {
            MSound::updateAllCounters(['downtimes' => 1], ['id' => $sound_id]);
            $sound->downtimes++;
        }

        return $sound;
    }

    /**
     * 检查音频是否可以下载设为铃声
     *
     * @param MSound $sound
     * @return void
     * @throws HttpException 音频不可设置为铃声时抛出异常
     */
    private function checkDownloadRingtone(MSound $sound)
    {
        if (Equipment::isAppOlderThan('', '6.1.2')
                && $sound->download === MSound::SOUND_DOWNLOAD_REFUSE && MSound::SOUND_FREE === $sound->need_pay
                && !$sound->isDramaPaid) {
            // WORKAROUND: Android < 6.1.2 并且设置了禁止下载时，免费音频设置铃声需要以下限制：
            // 音频属于免费剧集或单集付费剧集：所有用户均不能将其设为铃声
            // 音频属于整剧付费剧集：仅已购该剧用户能将其设为铃声
            throw new HttpException(403, '该音频不支持设为铃声');
        }
        if ($sound->limit_type === MSound::LIMIT_TYPE_VIP_PLAY) {
            if (MSound::SOUND_FREE === $sound->pay_type) {
                // 若为会员限制音，且为免费音频，则视作会员专享剧集
                throw new HttpException(403, '开通会员后才能设为铃声哦~', 200110206);
            } elseif (MSound::PAY_BY_SOUND === $sound->pay_type) {
                throw new HttpException(403, '开通会员或购买本集后才能设为铃声哦~', 200110206);
            } else {
                throw new HttpException(403, '开通会员或购买本剧后才能设为铃声哦~', 200110206);
            }
        }
        if ($sound->limit_type === MSound::LIMIT_TYPE_EPISODE) {
            throw new HttpException(403, '购买本集后才能设置为铃声哦~', 200110206);
        }
        if ($sound->limit_type === MSound::LIMIT_TYPE_DRAMA) {
            throw new HttpException(403, '购买剧集后才能设置为铃声哦~', 200110206);
        }
        if ($sound->limit_type === MSound::LIMIT_TYPE_EQUIP_PLAY) {
            throw new HttpException(400, '同时播放设备超限', 200110204);
        }
    }

    /**
     * @api {get} /sound/collect 根据id收藏单音
     * @apiDeprecated
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/sound/collect
     * @apiSampleRequest /sound/collect
     * @apiDescription  /mobile/personOperation/collectSound 收藏单音或取消收藏 参数：soundId=>sound_id和albumId=>album_id
     *
     * @apiVersion 0.1.0
     * @apiName collect
     * @apiGroup sound
     *
     * @apiParam {number} sound_id 单音id
     * @apiParam {number} album_id 音单id
     * @apiParam {string} token 用户token
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info":
     *          '收藏单音成功'
     *     }
     *
     */
    public function actionCollect(int $sound_id, int $album_id)
    {
        throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
    }

    /**
     * @api {post} /sound/like{?sound_id} 根据音频 ID 点赞音频
     *
     * @apiVersion 0.1.0
     * @apiName like
     * @apiGroup sound
     *
     * @apiParam (Query) {Number} sound_id 音频 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "likestatus": true,
     *         "msg": "成功添加到喜欢"
     *       }
     *     }
     */
    public function actionLike(int $sound_id)
    {
        if ($sound_id <= 0) {
            throw new HttpException(400, '参数错误');
        }
        $sound = MSound::find()
            ->select('id, checked, catalog_id')
            ->where([
                'id' => $sound_id,
                'checked' => [
                    MSound::CHECKED_PASS,
                    MSound::CHECKED_POLICE,
                    MSound::CHECKED_DISCONTINUED,
                    MSound::CHECKED_CONTRACT_EXPIRED,
                ],
            ])->one();
        if (!$sound) {
            throw new HttpException(404, '该单音不存在或未通过审核');
        }

        // TODO: 此处会调用两次 RPC, 之后需要优化为只调用一次 RPC
        if ($res = Drama::rpc('api/get-dramaid-by-soundid', ['sound_ids' => [$sound_id]])) {
            if (Drama::checkDramaRefined(current($res)['drama_id'], Drama::REFINED_TYPE_SPECIAL)) {
                throw new HttpException(403, '当前内容是限定音 暂不能进行此操作');
            }
        }

        $user_id = Yii::$app->user->id;
        $is_liked = MLikeSound::find()
            ->where(
                'user_id = :user_id AND sound_id = :sound_id',
                [':user_id' => $user_id, ':sound_id' => $sound_id]
            )
            ->exists();
        // 对于如果音频被特殊下架（合约期满）则音频可被取消喜欢，但不能再继续喜欢
        if (!$is_liked && MSound::CHECKED_CONTRACT_EXPIRED === $sound->checked) {
            throw new HttpException(403, '当前内容暂不能进行此操作');
        }
        if (!$is_liked && $sound->checked === MSound::CHECKED_DISCONTINUED) {
            // 禁止添加下架音到“喜欢”
            throw new HttpException(403, '该单音已下架');
        }
        if (Equipment::isAppOlderThan('6.0.0', '6.0.0')) {
            // WORKAROUND: 老版本（安卓 < 6.0.0, iOS < 6.0.0）添加和取消喜欢音频都使用此接口，喜欢音频的提示文案为固定文案
            // 添加或取消喜欢音频
            MSound::likeOrNot($sound, $user_id, $is_liked);
            $msg = $is_liked ? '已取消喜欢' : '成功添加到喜欢';
            return ['likestatus' => !$is_liked, 'msg' => $msg];
        }
        if (!$is_liked) {
            // 添加喜欢
            MSound::likeOrNot($sound, $user_id, false);
        }
        // 喜欢音频的提示文案从多个文案中随机选取一个
        return ['likestatus' => true, 'msg' => MUtils2::arrayRandValue(MLikeSound::LIKE_MSGS, 1)[0]];
    }

    /**
     * @api {post} /sound/cancel-like{?sound_id} 根据音频 ID 取消点赞音频
     *
     * @apiVersion 0.1.0
     * @apiName cancel-like
     * @apiGroup sound
     *
     * @apiParam (Query) {Number} sound_id 音频 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "likestatus": false,
     *         "msg": "已取消喜欢"
     *       }
     *     }
     */
    public function actionCancelLike(int $sound_id)
    {
        if ($sound_id <= 0) {
            throw new HttpException(400, '参数错误');
        }
        $sound = MSound::find()
            ->select('id, checked, catalog_id')
            ->where([
                'id' => $sound_id,
                'checked' => [
                    MSound::CHECKED_PASS,
                    MSound::CHECKED_POLICE,
                    MSound::CHECKED_DISCONTINUED,
                    MSound::CHECKED_CONTRACT_EXPIRED,
                ],
            ])->one();
        if (!$sound) {
            throw new HttpException(404, '该单音不存在或未通过审核');
        }
        $user_id = Yii::$app->user->id;
        $is_liked = MLikeSound::find()
            ->where('user_id = :user_id AND sound_id = :sound_id',
                [':user_id' => $user_id, ':sound_id' => $sound_id])
            ->exists();
        if ($is_liked) {
            MSound::likeOrNot($sound, $user_id, true);
        }
        return ['likestatus' => false, 'msg' => '已取消喜欢'];
    }

    /**
     * @api {post} /sound/batch-like 批量喜欢音频
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/sound/batch-like
     *
     * @apiVersion 0.1.0
     * @apiName batch-like
     * @apiGroup sound
     *
     * @apiParam {Number[]} sound_ids 音频 IDs
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "喜欢成功"
     *     }
     */
    public function actionBatchLike()
    {
        $sound_ids = Yii::$app->request->post('sound_ids');
        if (empty($sound_ids) || !MUtils2::isUintArr($sound_ids)) {
            throw new HttpException(400, '参数错误');
        }
        $sound_ids = array_map('intval', $sound_ids);
        if (MLikeSound::MAX_LIKE_NUM < count($sound_ids)) {
            throw new HttpException(403, '喜欢的音频数不能超过 ' . MLikeSound::MAX_LIKE_NUM);
        }
        // 过滤重复喜欢的音频 ID
        $sound_ids = array_unique($sound_ids);
        // 过滤非法音频
        $check_sound_ids = MSound::find()
            ->select('id')
            ->where(['id' => $sound_ids, 'checked' => [MSound::CHECKED_PASS, MSound::CHECKED_POLICE]])->column();
        if (empty($check_sound_ids)) {
            throw new HttpException(404, '所选音频不存在 π_π');
        }
        // 兑换码兑换的音频不允许被用户喜欢
        // 兑换音频相关文档：https://github.com/MiaoSiLa/requirements-doc/issues/714
        $check_sound_ids = array_map('intval', $check_sound_ids);
        if ($res = Drama::rpc('api/get-dramaid-by-soundid', ['sound_ids' => $check_sound_ids])) {
            $drama_ids = array_column($res, 'drama_id');
            if (Drama::checkDramaRefined($drama_ids, Drama::REFINED_TYPE_SPECIAL)) {
                throw new HttpException(403, '当前内容是限定音 暂不能进行此操作');
            }
        }
        // 添加喜欢
        $user_id = Yii::$app->user->id;
        if (!MLikeSound::batchLikeOrNot($check_sound_ids, $user_id, MLikeSound::TYPE_LIKE_SOUND)) {
            throw new HttpException(500, '喜欢失败');
        }
        return '喜欢成功';
    }

    /**
     * @api {post} /sound/batch-cancel-like 批量取消喜欢音频
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/sound/batch-cancel-like
     *
     * @apiVersion 0.1.0
     * @apiName batch-cancel-like
     * @apiGroup sound
     *
     * @apiParam {Number[]} sound_ids 音频 IDs
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "已取消喜欢"
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     {
     *       "success": false,
     *       "code": 100010007,
     *       "info": "取消喜欢失败"
     *     }
     */
    public function actionBatchCancelLike()
    {
        $sound_ids = Yii::$app->request->post('sound_ids');
        if (empty($sound_ids) || !MUtils2::isUintArr($sound_ids)) {
            throw new HttpException(400, '参数错误');
        }
        $sound_ids = array_unique(array_map('intval', $sound_ids));
        $user_id = Yii::$app->user->id;
        if (!MLikeSound::batchLikeOrNot($sound_ids, $user_id, MLikeSound::TYPE_CANCEL_LIKE_SOUND)) {
            throw new HttpException(500, '取消喜欢失败');
        }
        return '已取消喜欢';
    }

    /**
     * @api {get} /sound/ts 根据单音 ID 投食小鱼干
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/sound/ts
     * @apiSampleRequest /sound/ts
     * @apiDescription  根据单音 ID 投食小鱼干
     *
     * @apiVersion 0.1.0
     * @apiName ts
     * @apiGroup sound
     *
     * @apiParam {Number} sound_id 单音 ID
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response: iOS < 4.8.3 Android < 5.7.1
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "投食成功，此音共收到 50 个小鱼干"
     *     }
     * @apiSuccessExample Success-Response: iOS >= 4.8.3 Android >= 5.7.1
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "point": 50,
     *         "msg": "投食成功，此音共收到 50 个小鱼干"
     *       }
     *     }
     * @apiErrorExample Error-Response:
     *     {
     *       "success": false,
     *       "code": 200360104,
     *       "info": "没有小鱼干可以投食了 T_T"
     *     }
     *
     */

    public function actionTs(int $sound_id)
    {
        $point = MSound::Ts($sound_id);
        $msg = '投食成功，此音共收到 ' . ($point) . ' 个小鱼干';
        if (Equipment::isAppOlderThan('4.8.3', '5.7.1')) {
            // WORKAROUND: 对低于 iOS 4.8.3, Android 5.7.1 的版本返回文本信息
            return $msg;
        }
        return ['point' => $point, 'msg' => $msg];
    }

    /**
     * @api {get} /sound/recommend 根据单音 ID 获取推荐单音、音单、频道
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/sound/recommend
     * @apiSampleRequest /sound/recommend
     * @apiDescription 根据单音 ID 获取推荐单音、音单、频道，live 字段为播放页 UP 主的直播状态
     *
     * @apiVersion 0.1.0
     * @apiName recommend
     * @apiGroup sound
     *
     * @apiParam {Number} sound_id 单音 ID
     * @apiParam {Number} [album_id] 来源音单 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiError HttpException 400 参数错误
     * @apiError NotFoundHttpException 404 音频不存在
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "sound": [
     *           {
     *             "id": 9,
     *             "soundstr": "嘭嘭!",
     *             "view_count": 8183,
     *             "comment_count": 286,
     *             "comments_count": 0,
     *             "sub_comments_count": 0,
     *             "all_comments": 286,
     *             "comments_num": 0,
     *             "front_cover": "http://static.missevan.com/coversmini/201701/24/af0b137c95b597a02d67c0b091425.png",
     *             "liked": 0,
     *             "collected": 0,
     *             "followed": 0,
     *             "authenticated": 0,
     *             "confirm": 0,
     *             "iconurl": "http://static.missevan.com/profile/icon01.png",
     *             "need_pay": 0,
     *             "price": 0
     *           }
     *         ],
     *         "album": [
     *           {
     *             "id": 73536,
     *             "title": "声感☆爱的信息声优语音剪辑",
     *             "music_count": 12,
     *             "origin": 1,
     *             "front_cover": "http://static.missevan.com/coversmini/201510/29/f15a695e86950bc2ea7b160954.jpeg"
     *           }
     *         ],
     *         "channel": [
     *           {
     *             "id": 16393,
     *             "name": "一人一句名台词",
     *             "follow_num": 295,
     *             "bigpic": "http://static.missevan.com/mlongtags/201512/14/851624e7dd2e8d596516f46b59e92bfd181057.jpg",
     *             "smallpic": "http://static.missevan.com/mtags/201512/14/851624e7dd2e8d596516f46b59e92bfd181057.jpg"
     *           }
     *         ],
     *         "drama": [
     *           {
     *             "id": 32,
     *             "name": "重生之国民女神",
     *             "front_cover": "http://static.missevan.com/dramacoversmini/201604/15/ab40ddcdce4b9dc2786081935.jpg",
     *             "pay_type": 2,
     *             "need_pay": 1,
     *             "view_count": 300,
     *             "corner_mark": {  // 无剧集角标时不返回该字段
     *               "text": "已购",
     *               "text_color": "#ffffff",
     *               "bg_start_color": "#e66465",
     *               "bg_end_color": "#e66465",
     *               "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *             }
     *           }
     *         ],
     *         "live": {
     *           "id": 346287,
     *           "username": "23336666",
     *           "iconurl": "http://static.missevan.com/avatars/201602/21/b918afee2fcd3b54f7505b0934106.jpg",
     *           "room_id": 4381915,
     *           "title": "功能测试直播间 2",
     *           "status": 0  // 客户端判断 UP 主直播间状态
     *         }
     *       }
     *     }
     */
    public function actionRecommend(int $sound_id)
    {
        if ($sound_id <= 0) throw new HttpException(400, '参数错误');
        $sound_user_id = (int)MSound::find()->select('user_id')->where('id = :id', ['id' => $sound_id])
            ->limit(1)->scalar();
        if (!$sound_user_id) {
            throw new NotFoundHttpException('音频不存在');
        }
        $user_id = (int)Yii::$app->user->id;
        $origin_album_id = (int)Yii::$app->request->get('album_id');
        $albums = []; //MAlbum::getLikeAlbum($sound_id, MAlbum::PLAY_PAGE_RECOMMEND_NUM, $origin_album_id);
        // WORKAROUND: 新版本播放页“包含该音频的音单”若从音单页点击进来，则显示“来源”标识
        if (Equipment::isAppOlderThan('4.2.7', '5.1.8')) {
            $albums = array_map(function ($item) {
                unset($item['origin']);
                return $item;
            }, $albums);
        }
        $live = [
            'id' => $sound_user_id,
            'username' => '',
            'iconurl' => Yii::$app->params['defaultAvatarUrl'],
            'room_id' => 0,
            'status' => 0,
            'title' => ''
        ];
        $user = Mowangskuser::find()->select('id, username, iconurl, avatar, icontype, boardiconurl')
            ->where('id = :id', [':id' => $sound_user_id])->one();
        if ($user) {
            $live['username'] = $user->username;
            $live['iconurl'] = $user->iconurl;
            if ($info = $user->live) {
                $live['room_id'] = $info->room_id;
                $live['status'] = $info->status;
                $live['title'] = $info->title;
            }
        }
        $drama_id = Drama::getDramaIdBySoundId($sound_id);
        if (Drama::isSensitiveDrama($drama_id)) {
            // WORKAROUND: 特殊剧集不显示推荐内容
            return [
                'sound' => [],
                'album' => [],
                'channel' => [],
                'drama' => [],
                'live' => $live,
            ];
        }
        $sound = [];
        // TEMP: 剧集音频播放页相似音频的推荐结果不太合理。先暂时屏蔽掉，后续优化好了再展示出来。
        if (!$drama_id) {
            // 获取 3 个推荐音频
            $sound = MSound::getLikeMusic($sound_id, 3);
        }
        // 获取两个付费推荐剧集
        $PAID_NUM = 2;
        return [
            'sound' => $sound,
            'album' => $albums,
            'channel' => MTag::getLikeChannel($sound_id, 2),
            'drama' => Drama::getRecommendDrama($sound_id, $user_id, Drama::PLAY_PAGE_RECOMMEND_FREE_NUM,
                $PAID_NUM),
            'live' => $live,
        ];
    }

    /**
     * @api {get} /sound/catalog-sounds 子分类 ID 获取单音
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/sound/catalog-sounds
     * @apiSampleRequest /sound/catalog-sounds
     * @apiDescription 子分类 ID 获取单音
     * @apiVersion 0.1.0
     * @apiName catalog-sounds
     * @apiGroup sound
     *
     * @apiParam {Number} cid 分类 ID
     * @apiParam {number=0,1,2,3,4,5,6} order 音频排序：0 为最新，1 为猫耳，2 为热度，3 为点赞数
     * 4 为评论数，5 为播放量，6 为综合排序
     * @apiParam {Number} [page_size=16] 每页数量
     * @apiParam {Number} [page=1] 页数
     *
     * @apiSuccess {Boolean} success
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "catalog_name": "有声漫画",
     *         "Datas": [{
     *           "id": 83435,
     *           "user_id": 177922,
     *           "username": "隔壁村的王大妈",
     *           "duration": 1106233,
     *           "soundstr": "[樱日梯子][年下彼氏の恋愛管理癖]Drama3 Free Talk",
     *           "view_count": 102833,
     *           "cover_image": "201512/12/c127d09472487ae485bed10f0dfbe436061850.png",
     *           "soundurl": "https://static.missevan.com/sound/201512/12/4d71b64f16fcaf9a5d0b4ced5dfc01ac061849.mp3",
     *           "pay_type": 0,
     *           "all_comments": 0,
     *           "comments_num": 0,
     *           "front_cover": "https://static.missevan.com/coversmini/201512/12/c127d09472487ae461850.png",
     *           "liked": 0,
     *           "collected": 0,
     *           "followed": 0,
     *           "authenticated": 0,
     *           "confirm": 0,
     *           "iconurl": "https://static.missevan.com/profile/icon01.png",
     *           "need_pay": 0,
     *           "price": 0
     *         }],
     *         "pagination": {
     *           "p": "1",
     *           "count": "17992",
     *           "maxpage": 900,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     *
     */
    public function actionCatalogSounds(int $cid, int $order, $page_size = 16, $page = 1)
    {
        if ($cid <= 0) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        if (!$catalog_name = Catalog::find()->select('catalog_name')->where(['id' => $cid])->column()) {
            throw new HttpException(404, '分类不存在', 200110201);
        }
        // 查询音频信息（此处查询音频地址用于分享）
        $query = MSound::find()
            ->select('id, user_id, username, duration, soundstr, view_count, cover_image, '
                . 'soundurl_64, pay_type, checked')
            ->where(['catalog_id' => $cid, 'checked' => MSound::CHECKED_PASS])
            ->andWhere('NOT refined & ' . MSound::REFINED_BLOCK);
        switch ($order) {
            case Catalog::ORDER_HOT:
                // TODO: 之后需要完善 getSoundsByCat 方法，让其返回 ReturnModel 对象
                return $this->getSoundsByCat($cid, $page_size, $page);
            case Catalog::ORDER_RECENTLY_VIEWS:
                // 使用后台定时任务生成好的 6 小时内音频新增播放量榜单的数据 missevan-backend: catalog/sound-view-rank
                $redis = Yii::$app->redis;
                $key = $redis->generateKey(KEY_SOUND_VIEW_RANK, $cid);
                $count = $redis->zcard($key);
                // 分页
                $offset = ($page - 1) * $page_size;
                $sound_ids = $redis->zRevRange($key, $offset, $offset + $page_size - 1);
                $data = [];
                if (!empty($sound_ids)) {
                    $sound_ids = array_map('intval', $sound_ids);
                    $data = $query->allByColumnValues('id', $sound_ids);
                }
                $model_dto = ReturnModel::getPaginationData($data, $count, $page, $page_size);
                break;
            case Catalog::ORDER_POINTS:
                $query->orderBy('point DESC');
                $model_dto = MUtils::getPaginationModels($query, $page_size);
                break;
            case Catalog::ORDER_LIKES:
                $query->orderBy('uptimes DESC');
                $model_dto = MUtils::getPaginationModels($query, $page_size);
                break;
            case Catalog::ORDER_COMMENTS:
                $query->orderBy('comment_count DESC');
                $model_dto = MUtils::getPaginationModels($query, $page_size);
                break;
            case Catalog::ORDER_VIEWS:
                // 按播放量进行排序时，若音频为报警音（即播放量对用户显示为 0），也按真实的播放量进行排序
                $query->orderBy('view_count DESC');
                $model_dto = MUtils::getPaginationModels($query, $page_size);
                break;
            case Catalog::ORDER_NEWEST:
                $query->orderBy('create_time DESC');
                $model_dto = MUtils::getPaginationModels($query, $page_size);
                break;
            default:
                throw new HttpException(400, '参数错误');
        }
        MSound::checkNeedPay($model_dto->Datas, Yii::$app->user->id);
        MSound::removeNeedPaySoundUrl($model_dto->Datas);
        // 去掉无用字段
        foreach ($model_dto->Datas as $sound) {
            unset($sound->checked, $sound->cover_image);
        }
        return [
            'catalog_name' => $catalog_name[0],
            'Datas' => $model_dto->Datas,
            'pagination' => $model_dto->pagination,
        ];
    }

    private function getSoundsByCat($catalog_id, $page_size, $page = 1)
    {
        $offset = ($page - 1) * $page_size;
        // WORKAROUND: 对 ASMR 的中文、无人声、女性向分类，被限制用户不显示报警音
        if (!Yii::$app->user->isExam || (Yii::$app->user->isLimited && in_array($catalog_id,
            [Catalog::CATALOG_ID_ASMR_CN, Catalog::CATALOG_ID_ASMR_NOVOICE, Catalog::CATALOG_ID_ASMR_GIRL]))) {
            $sensitive = 0;
        } else {
            $sensitive = 1;
        }
        $ids = MSound::getRecommendSoundsByCat(0, (array)$catalog_id, $sensitive);

        $id_count = count($ids);

        $query = MSound::find()->where(['catalog_id' => $catalog_id]);

        if ($sensitive) {
            $query->andWhere(['checked' => [MSound::CHECKED_PASS, MSound::CHECKED_POLICE]]);
        } else {
            $query->andWhere(['checked' => MSound::CHECKED_PASS]);
        }
        $query->andWhere('NOT refined & ' . MSound::REFINED_BLOCK);

        $count = $query->count();
        $pagesize = intval($page_size);
        $pagesize < 1 && $pagesize = 1;
        $pagesize > 51 && $pagesize = 50;
        $maxpage = ceil($count / $pagesize);

        $return['pagination']['p'] = $page;
        $return['pagination']['count'] = $count;
        $return['pagination']['maxpage'] = $maxpage;
        $return['pagination']['pagesize'] = $pagesize;

        // 查询音频信息（此处查询音频地址用于分享）
        $select = 'id, user_id, username, duration, soundstr, view_count, cover_image, '
            . 'soundurl_64, pay_type, checked';
        // 按播放量进行排序时，若音频为报警音（即播放量对用户显示为 0），也按真实的播放量进行排序
        $query = $query->select($select)->orderBy('view_count DESC')->andWhere(['NOT IN', 'id', $ids]);
        if ($id_count >= $offset + $page_size) {
            $in_id = array_slice($ids, $offset, $page_size);
            $msounds = MSound::find()->select($select)->allByColumnValues('id', $in_id);
        } elseif ($id_count <= $offset) {
            $msounds = $query->offset($offset - $id_count)
                ->limit($page_size)->all();
        } else {
            $msounds1 = [];
            if ($id_count) {
                $in_id = array_slice($ids, $offset, $id_count - $offset);
                $msounds1 = MSound::find()->select($select)->allByColumnValues('id', $in_id);
            }

            $msounds2 = $query->offset($id_count)
                ->limit($offset + $page_size - $id_count)->all();
            $msounds = array_merge($msounds1, $msounds2);
        }
        // 去掉无用字段
        $msounds = array_map(function ($sound) {
            unset($sound->checked, $sound->cover_image);
            return $sound;
        }, $msounds);
        $return['Datas'] = $msounds;
        MSound::checkNeedPay($return['Datas'], Yii::$app->user->id);
        MSound::removeNeedPaySoundUrl($return['Datas']);
        return $return;
    }


    /**
     * @api {get} /sound/get-album-sound 根据音单 ID 获取全部单音
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/sound/get-album-sound
     * @apiSampleRequest /sound/get-album-sound
     * @apiDescription  /mobile/person/personInfos?type=62
     *
     * @apiVersion 0.1.0
     * @apiName get-album-sound
     * @apiGroup sound
     *
     * @apiParam {Number} album_id 音单 ID
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 音单下音频及标签等信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [
     *           {
     *             "id": 280964,
     *             "duration": 757648,
     *             "soundstr": "【带耳机】镜中少女",
     *             "view_count": 1451,
     *             "pay_type": 0,
     *             "username": "加特林",
     *             "download": 1,  // 为 1 时禁止下载
     *             "front_cover": "http://static.missevan.com/coversmini/201704/24/test.jpg",
     *             "video": false,
     *             "need_pay": 0,
     *             "episode_vip": 2  // 会员单集状态。0：非会员单集；1：会员剧下的试听单集；2：会员剧下的非试听单集。音频不属于剧集时，不返回该字段
     *           }
     *         ],
     *         "model": {
     *           "tags": [
     *             {
     *               "id": 146,
     *               "name": "同人"
     *             }
     *           ],
     *           "collect": 0
     *         }
     *       }
     *     }
     */
    public function actionGetAlbumSound(int $album_id)
    {
        $result = MSound::getAlbumSound($album_id, Yii::$app->user->id, true);
        return $result;
    }

    /**
     * @api {get} /sound/get-album-sounds 根据音单 ID 获取音频
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/sound/get-album-sounds
     * @apiSampleRequest /sound/get-album-sounds
     *
     * @apiVersion 0.1.0
     * @apiName get-album-sounds
     * @apiGroup sound
     *
     * @apiParam {Number} album_id 音单 ID
     * @apiParam {Number} page 第几页
     * @apiParam {Number} page_size 每页个数
     * @apiParam {number=0,1} [order=0] 排序（0 为正序，1 为倒序）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "sounds": {
     *           "Datas": [{
     *             "id": 135845,
     *             "catalog_id": 50,
     *             "create_time": 1470649773,
     *             "last_update_time": 1470653897,
     *             "duration": 265770,
     *             "user_id": 8,
     *             "username": "圣骑士·Saber",
     *             "cover_image": "",
     *             "animationid": 0,
     *             "characterid": 0,
     *             "seiyid": 0,
     *             "soundstr": "玻璃花与毁坏的世界 主题歌 - 夢の蕾",
     *             "intro": "",
     *             "soundurl": "https://static.missevan.com/sound/201608/08/d0b03e4e8c19d19bf2fb3eb3d67d9d5e174932.mp3",
     *             "soundurl_32": "https://static.missevan.com/32BIT/201608/08/d0b03e4e8c19d19bf2fb3eb3d67d9d5e174932.mp3",
     *             "soundurl_64": "https://static.missevan.com/MP3/201608/08/d0b03e4e8c19d19bf2fb3eb3d67d9d5e174932.mp3",
     *             "soundurl_128": "https://static.missevan.com/128BIT/201608/08/d0b03e4e8c19d19bf2fb3eb3d67d9d5e174932.mp3",
     *             "downtimes": 0,
     *             "uptimes": 0,
     *             "checked": 1,
     *             "source": 0,
     *             "download": 0,
     *             "view_count": 512,
     *             "comment_count": 280,
     *             "favorite_count": 1,
     *             "point": 0,
     *             "push": 0,
     *             "refined": 48,
     *             "comments_count": 0,
     *             "sub_comments_count": 0,
     *             "pay_type": 0,
     *             "all_comments": 280,
     *             "comments_num": 0,
     *             "front_cover": "https://static.missevan.com/coversmini/nocover.png",
     *             "liked": 0,
     *             "collected": 0,
     *             "followed": 0,
     *             "authenticated": 0,
     *             "confirm": 0,
     *             "iconurl": "https://static.missevan.com/profile/icon01.png",
     *             "need_pay": 0,
     *             "price": 0
     *           }],
     *           "pagination": {
     *             "p": 1,
     *             "maxpage": 198,
     *             "count": 198,
     *             "pagesize": 1
     *           }
     *         },
     *         "tags": [],
     *         "collected": 0,
     *         "last_update_time": 1520924140
     *       }
     *     }
     */
    public function actionGetAlbumSounds(int $album_id, int $page = 1, int $page_size = PAGE_SIZE_20, int $order = 0)
    {
        if (!$album = MAlbum::findOne($album_id)) {
            throw new HttpException(404, '音单不存在');
        }
        $album->updateCounters(['view_count' => 1]);

        $total_count = (int)MSoundAlbumMap::find()->where(['album_id' => $album_id])->count();
        $sounds = MSound::getSoundsInAlbum($album_id, $page, $page_size, $order);
        $sounds = ReturnModel::getPaginationData($sounds, $total_count, $page, $page_size);

        $return = [
            'sounds' => $sounds,
            'tags' => MAlbum::getTags($album_id),
            'collected' => 0,
            'last_update_time' => $album->last_update_time,
        ];

        if ($user_id = Yii::$app->user->id) {
            MSound::DoYouLike($return['sounds']->Datas);
            $return['collected'] = MAlbum::DoYouCollect($album_id);
        }

        return $return;
    }

    /**
     * @api {get} /sound/is-album-changed 排序前检查音单中的音频位置是否变动
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/sound/is-album-changed
     * @apiSampleRequest /sound/is-album-changed
     *
     * @apiVersion 0.1.0
     * @apiName is-album-changed
     * @apiGroup /sound
     *
     * @apiParam {Number} album_id 音单 ID
     * @apiParam {Number} last_update_time 音单最后更新时间
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Boolean} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": false
     *     }
     */
    public function actionIsAlbumChanged(int $album_id, int $last_update_time)
    {
        return MAlbum::isAlbumChanged($album_id, $last_update_time);
    }

    /**
     * @api {get} /sound/sound-by-tag 根据标签/频道 ID 获取音频
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/sound/sound-by-tag
     * @apiSampleRequest /sound/sound-by-tag
     * @apiDescription  /mobile/site/soundByTag 推荐单音 tid=>tag_id p=>page pagesize=>page_size
     *
     * @apiVersion 0.1.0
     * @apiName sound-by-tag
     * @apiGroup sound
     *
     * @apiParam {Number} tag_id 标签/频道 ID
     * @apiParam {Number} [page=1] 页数
     * @apiParam {Number} [page_size=10] 每页个数
     * @apiParam {Number} [order=0] 排序方法 0: 创建时间；1：查看数；2：投食鱼干数；3：热度；4：收藏数；5：评论数；6：点赞数；降序排序
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info": {
     *         "Datas": [{
     *           "id": 250302,
     *           "username": "千音绪音乐工作室",
     *           "front_cover": "http://static-test.maoercdn.com/coversmini/201704/04/9393771985a3d428707a165651.png",
     *           "soundstr": "天使之名",
     *           "duration": 241294,
     *           "view_count": 1443,
     *           "intro": "<p><strong>这首作品是为纪念芭蕾舞剧《天鹅湖》首演成功140周年而创作的。</strong></p>",
     *           "refined": 0,
     *           "download": 0
     *         }],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 1,
     *           "count": 16,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     */
    public function actionSoundByTag(int $tag_id, int $page = 1, int $page_size = PAGE_SIZE_20,
            int $order = MTagSoundMap::ORDER_BY_HOT)
    {
        $tag = MTag::find()
            ->select('id, recommended')
            ->where('id = :id', [':id' => $tag_id])
            ->one();
        if (!$tag) {
            throw new HttpException(404, '频道标签不存在');
        }
        if ($tag->recommended === MTag::RECOMMENDED_CHANNEL) {
            // WORKAROUND: 临时屏蔽获取频道下音频信息
            return ReturnModel::empty($page, $page_size);
        }
        return MTagSoundMap::getTagSound($tag_id, $page_size, $order);
    }

    public function actionSoundViewCount(int $sound_id, $start_time, $end_time)
    {
        $start_time = strtotime("$start_time");
        $end_time = strtotime("$end_time + 1 day + 3 hours");

        $sound_view_count = SoundViewCount::find()->select('view_count')->where(['sound_id' => $sound_id])
            ->andWhere(['>=', 'time', $start_time])
            ->andWhere(['<=', 'time', $end_time])
            ->asArray()
            ->all();

        $view_count = (int)$sound_view_count[count($sound_view_count)-1]['view_count'] - (int)$sound_view_count[0]['view_count'];

        return $view_count;
    }

    /**
     * @api {post} /sound/add-play-times{?sound_id,duration,completion,played_duration,mode,last_play_position,drama_id,node_id,operation_type} 添加播放日志/播放量
     * @apiDescription https://info.missevan.com/pages/viewpage.action?pageId=35127747
     * @apiSampleRequest /sound/add-play-times
     *
     * @apiVersion 0.1.0
     * @apiName add-play-times
     * @apiGroup sound
     *
     * @apiParam (Query) {Number} sound_id 音频 ID
     * @apiParam (Query) {number=0,1} [add=0] 是否为加播放量请求，0：否；1：是；
     *
     * @apiParam (Query) {number=0~1} [completion=0] 最后的时间点占总时长的比例，保留四位小数 0.0000~0.xxxx~1.0000（不是实际播放时长占总时长的比例）
     * @apiParam (Query) {Number} [played_duration=0] 实际播放时长（单位为毫秒）
     * @apiParam (Query) {number=0,1,2,3} [mode=0] 播放模式 \
     * 正常播放: 0 (默认);
     * 单曲循环: 1 (单曲循环的第一次算正常播放);
     * 列表循环: 2 (列表循环的第一次算正常播放，列表中只有一个音时算单曲循环);
     * 随机循环: 3 (随机播放的第一次算正常播放，列表中只有一首音时算单曲循环);
     * @apiParam (Query) {number=1,3,4} [player_mode=1] 播放器模式 \
     * 正常播放器：1；
     * 盲盒剧场语音条：3；
     * 视频大卡：4；
     * @apiParam (Query) {Number} sound_duration 音频时长（单位：毫秒）
     * @apiParam (Query) {Number} last_play_position 最后播放位置（单位毫秒）
     * @apiParam (Query) {Number} [drama_id=0] 剧集 ID
     * @apiParam (Query) {Number} [node_id=0] 互动剧节点 ID
     * @apiParam (Query) {Number} operation_type 标识是开始播放的时间点还是播放中断，1：开始；2：暂停；3：播放结束
     *
     * 字段说明：
     * https://info.missevan.com/pages/viewpage.action?pageId=109686026
     * https://github.com/MiaoSiLa/requirements-doc/blob/master/%E6%92%AD%E6%94%BE%E6%97%A5%E5%BF%97%E8%AF%B4%E6%98%8E%E6%96%87%E6%A1%A3.md
     * @apiParam (Rawbody) {String} session_id 播放周期标识
     * @apiParam (Rawbody) {Number} start_time 每条日志开始播放时（切到该歌曲时）的时间戳（单位：毫秒）
     * @apiParam (Rawbody) {Number} end_time 每条日志结束记录时的时间戳（单位：毫秒）
     * @apiParam (Rawbody) {String} playing_status 播放状态
     * @apiParam (Rawbody) {Number} player_mode 播放器模式
     * @apiParam (Rawbody) {Number} quality 清晰度
     * @apiParam (Rawbody) {Number} operation_type 标识是开始播放的时间点还是播放中断 / 结束日志
     * @apiParam (Rawbody) {Number} network 网络状态
     * @apiParam (Rawbody) {Number} mode 播放模式
     * @apiParam (Rawbody) {Number} loop_times 循环次数（累计播放次数），从 1 开始
     * @apiParam (Rawbody) {Object} referer 进入音频播放页的来源
     * @apiParam (Rawbody) {Number} from_event_id 播放来源
     * @apiParam (Rawbody) {String} context 上下文，AB test 时使用
     * @apiParam (Rawbody) {Number} session_duration 播放停留时长（单位：毫秒）
     * @apiParam (Rawbody) {Number} played_duration 用户（设备）播放该音频的时长（单位：毫秒）
     * @apiParam (Rawbody) {Number} paused_duration 音频暂停时长（单位：毫秒）
     * @apiParam (Rawbody) {Number} last_play_position 播放进度，指用户停止收听时的播放进度
     * @apiParam (Rawbody) {Number} max_play_position 最大播放进度（单位：毫秒）
     * @apiParam (Rawbody) {Number} danmaku_duration 弹幕可见的时长（单位：毫秒）
     * @apiParam (Rawbody) {Number} sound_id 播放音频 ID
     * @apiParam (Rawbody) {Number} sound_duration 音频的总时长（单位：毫秒）
     * @apiParam (Rawbody) {Number} drama_id 音频所属剧集 ID
     * @apiParam (Rawbody) {Number} node_id 互动广播剧的节点 ID
     * @apiParam (Rawbody) {Number} catalog_id 音频所属分类 ID
     * @apiParam (Rawbody) {Number} payment_type 音频付费类型
     * @apiParam (Rawbody) {Number} purchased 该音频是否已购买
     * @apiParam (Rawbody) {Number} user_id 用户 ID
     * @apiParam (Rawbody) {Number} operation_type 标识是开始播放的时间点还是播放中断，1：开始；2：暂停；3：播放结束
     * @apiParam (Rawbody) {number=0,1} [error=0] 播放器播放期间是否出错（0 否，1 是）
     * @apiParam (Rawbody) {Number} session_start_time 本次 session（播放周期）开始的时间戳（单位：毫秒），从起播时间开始计算
     * @apiParam (Rawbody) {String} track_id 一次数据请求的 ID，仅在推荐场景上报，否则为空
     *
     * @apiSuccess {Boolean} success true or false
     * @apiSuccess {Number} code
     * @apiSuccess {Boolean} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": true
     *     }
     */
    public function actionAddPlayTimes(int $sound_id, float $completion = 0, int $played_duration = 0, int $mode = 0,
            int $player_mode = MSound::PLAYER_MODE_NORMAL, int $add = 0, int $drama_id = 0, int $node_id = 0,
            int $operation_type = 0)
    {
        if (!$sound_id) {
            throw new HttpException(400, 'sound_id 参数错误');
        }
        if ($drama_id < 0 || $node_id < 0) {
            throw new HttpException(400, 'drama_id 或 node_id 参数错误');
        }

        if (!in_array($mode, [0, 1, 2, 3])) {
            throw new HttpException(400, 'mode 参数错误');
        }

        if ($completion < 0 || $completion > 1) {
            throw new HttpException(400, 'completion 参数错误');
        }
        $sound = MSound::find()->select('id, pay_type')->where('id = :id', [':id' => $sound_id])->one();
        if (!$sound) {
            throw new HttpException(400, '音频不存在');
        }
        $user_id = (int)Yii::$app->user->id;
        // WORKAROUND: 兼容安卓 6.0.1 至 6.0.6 版本传 drama_id 有误的情况
        $get_drama_id = Equipment::isAppOlderThan(null, '6.0.7')
            && !Equipment::isAppOlderThan(null, '6.0.1');
        if (!$add && Yii::$app->request->getIsPost()) {
            $log_data = Json::decode(Yii::$app->request->rawBody);
            if (!$log_data) {
                throw new HttpException(400, '日志内容有误');
            }
            $query_params = MSoundPlayLog::formatParamsType(Yii::$app->request->getQueryParams());
            $query_params['pay_type'] = $sound->pay_type;
            if ($get_drama_id) {
                [$drama_id, $node_id] = MUserHistory::getDramaIdAndNodeId($sound_id, $user_id);
                $get_drama_id = false;
                $query_params['drama_id'] = $log_data['drama_id'] = $drama_id;
                $query_params['node_id'] = $node_id;
            }
            if (array_key_exists('drama_id', $log_data) &&
                    $drama_catalog_id = Drama::getDramaCatalogId($log_data['drama_id'])) {
                // 获取剧集分区
                $log_data['more']['drama_catalog_id'] = $drama_catalog_id;
            }
            $drama_price_info = Drama::rpc('api/get-drama-price-by-sound',
                ['sound_id' => $sound_id, 'user_id' => $user_id]);
            if ($drama_price_info) {
                $log_data['vip'] = $drama_price_info['episode']['vip'];
            }
            if ($user_id) {
                try {
                    $vip_wears_info = Yii::$app->serviceRpc->getVipWears($user_id);
                    $theme_skin = $vip_wears_info['theme_skin'] ?? null;
                    if ($theme_skin) {
                        $log_data['more']['theme_skin_id'] = $theme_skin['id'];
                        $log_data['more']['theme_skin_wear_time'] = $theme_skin['first_wear_time'];
                    }
                    $avatar_frame = $vip_wears_info['avatar_frame'] ?? null;
                    if ($avatar_frame) {
                        $log_data['more']['avatar_frame_id'] = $avatar_frame['id'];
                        $log_data['more']['avatar_frame_wear_time'] = $avatar_frame['first_wear_time'];
                    }
                } catch (Exception $e) {
                    // 记录错误日志
                    Yii::error(sprintf('获取用户（%d）正在使用的会员装扮信息出错：%s', $user_id, $e->getMessage()),
                        __METHOD__);
                    // PASS
                }
            }

            MSoundPlayLog::add(Yii::$app->equip, array_merge($log_data, $query_params));
        }

        if ($add && $player_mode === MSound::PLAYER_MODE_THEATRE) {
            // 目前只有盲盒剧场语音条需要加播放量
            $sound->addPlayTimes(1);
        }
        if (!$add && $user_id && $player_mode === MSound::PLAYER_MODE_NORMAL) {
            try {
                // WORKAROUND: 兼容安卓 6.0.1 至 6.0.6 版本传 drama_id 有误及旧版本没有传 drama_id 或 node_id 的情况
                if ($get_drama_id) {
                    [$drama_id, $node_id] = MUserHistory::getDramaIdAndNodeId($sound_id, $user_id);
                }
                $completion = round($completion, 4);
                $access_time = intval(microtime(true) * 1000);
                if ($drama_id) {
                    MUserHistory::newDrama($user_id, $access_time, $drama_id, $sound_id, $completion, $node_id);
                } else {
                    MUserHistory::newSound($user_id, $access_time, $sound_id, $completion);
                }
            } catch (\Exception $e) {
                Yii::error('添加历史记录异常：' . $e->getMessage(), __METHOD__);
                // PASS
            }
        }

        return true;
    }

    /**
     * @api {get} /sound/power-sound 获取单个启动音接口
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/sound/power-sound
     * @apiSampleRequest /sound/power-sound
     * @apiDescription 此接口为客户端启动音播放失败后重试时使用，\
     * 客户端可能缓存的是老的音频列表，这段期间内，如果启动音或启动音对应的音频下架了，\
     * 客户端重试请求的时候，发现启动音不存在或启动音对应的音频不存在（接口请求失败），不用更新启动音列表，正常不播放即可
     *
     * @apiVersion 0.1.0
     * @apiName power-sound
     * @apiGroup sound
     *
     * @apiParam {Number} id 启动音 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 3055660,  // 启动音 ID
     *         "cv": "百里玄策",  // 声优
     *         "role_name": "电耗子",  // 角色名
     *         "cover": "http://static-test.maoercdn.com/launch/images/202103/03/4c6a86d751539b93f615dcffa368fc89150744.png",  // 封面地址
     *         "icon": "http://static-test.maoercdn.com/system/app/powersound/202103/03/e5c77e67837e595cc3fadd46316fc6ef150744.png",  // 图标地址
     *         "intro": "测试文字说明",  // 文字说明
     *         "soundurl": "https://sound-test-ks.cdn.missevan.com/aod/202208/11/_0a5af228e54e42a9a6dd356ab05f30b.m4a"  // 音频地址
     *       }
     *     }
     */
    public function actionPowerSound(int $id)
    {
        if ($id <= 0) {
            throw new HttpException(400, '参数错误');
        }
        return MPowerSound::getPowerSoundById($id);
    }

    /**
     * @api {get} /sound/get-ring-sound{?sound_id} 获取铃声音频信息
     *
     * @apiVersion 0.1.0
     * @apiName get-ring-sound
     * @apiGroup sound
     *
     * @apiParam {Number} sound_id 音频 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 233,  // 音频 ID
     *         "soundstr": "标题",
     *         "front_cover": "http://static-test.maoercdn.com/test/test.png",  // 封面地址
     *         "soundurl_list": [  // 开启原音音质时使用的播放地址，优先使用列表中第一个地址，若无法播放，依次尝试后续地址（没有该音质的音频时不返回该字段）
     *           "http://static-test.maoercdn.com/test/test.m4a?sign=test1",
     *           "http://static-test.maoercdn.com/test/test.m4a?sign=test2"
     *         ],
     *         "soundurl_128_list": [  // 未开启原音音质时使用的播放地址，优先使用列表中第一个地址，若无法播放，依次尝试后续地址（没有该音质的音频时不返回该字段）
     *           "http://static-test.maoercdn.com/test/test-128k.m4a?sign=test1",
     *           "http://static-test.maoercdn.com/test/test-128k.m4a?sign=test2"
     *         ]
     *       }
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 404
     *     {
     *       "success": false,
     *       "code": 100010007,
     *       "info": "铃声不存在"
     *     }
     */
    public function actionGetRingSound(int $sound_id)
    {
        // TODO: 后续铃声信息需要存入数据库
        $result = Yii::$app->redis->mget([RINGING_1, RINGING_2]);
        $ring_sound_ids = array_unique(array_merge(explode(',', $result[0]), explode(',', $result[1])));
        if (!$ring_sound_ids || !in_array($sound_id, $ring_sound_ids)) {
            throw new HttpException(404, '铃声不存在');
        }
        $sound = MSound::find()
            ->select('id, soundstr, cover_image, soundurl_64, soundurl_128')
            ->where(['id' => $sound_id, 'checked' => MSound::CHECKED_PASS])
            ->one();
        if (!$sound) {
            Yii::error('铃声音频 ID: ' . $sound_id . ' 不存在或状态异常', __METHOD__);
            throw new HttpException(404, '铃声不存在');
        }
        // 音频地址加签
        MSound::getSoundSignUrls($sound, true, 1);
        $key_soundurl_list = MSound::KEY_SOUNDURL . '_list';
        $key_soundurl_128_list = MSound::KEY_SOUNDURL_128 . '_list';
        $res = [
            'id' => $sound->id,
            'soundstr' => $sound->soundstr,
            'front_cover' => $sound->front_cover,
        ];
        if ($sound->$key_soundurl_list) {
            $res['soundurl_list'] = $sound->$key_soundurl_list;
        }
        if ($sound->$key_soundurl_128_list) {
            $res['soundurl_128_list'] = $sound->$key_soundurl_128_list;
        }
        return $res;
    }
}
