<?php
namespace app\controllers;

use app\components\controllers\CollectionInterface;
use app\components\util\Equipment;
use app\components\util\MUtils;
use app\middlewares\Controller;
use app\models\AvAttentionTag;
use app\models\Catalog;
use app\models\Drama;
use app\models\MAlbum;
use app\models\MAttentionUser;
use app\models\MCollectAlbum;
use app\models\Mowangskuser;
use app\models\MSound;
use app\models\MSoundAlbumMap;
use app\models\MTag;
use app\models\MTagAlbumMap;
use app\models\ReturnModel;
use missevan\util\MUtils as MUtils2;
use Yii;
use app\components\base\filter\AccessControl;
use Exception;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use yii\web\HttpException;
use yii\web\UploadedFile;

class CollectionController extends Controller implements CollectionInterface
{
    // 收藏和取消收藏的模式：1：播放页收藏或取消收藏单音；2：批量收藏或取消收藏单音
    const SOUND_COLLECT_SCENARIO_PLAY = 1;
    const SOUND_COLLECT_SCENARIO_DEFAULT = 2;
    // 创建音单时，是否创建私有音单：0 公有，1 私有
    const SET_ALBUM_PUBLIC = 0;
    const SET_ALBUM_PRIVATE = 1;

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'logout' => ['post'],
                'album-create' => ['post'],
                'album-update' => ['post'],
                'collect-album' => ['post'],
                'sound-collect' => ['post'],
                'subscribe-channel' => ['post'],
                'sort-sound-in-album' => ['post'],
                'album-delete' => ['post'],
                'sort-user-albums' => ['post'],
                'sort-collect-albums' => ['post'],
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'only' => [
                'subscribe-channel',
                'album-collect',
                'album-create',
                'album-update',
                'sort-sound-in-album',
                'sound-collect',
                'album-delete',
                'collect-album',
                'sort-user-albums',
                'sort-collect-albums',
            ],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => [
                        'subscribe-channel',
                        'album-collect',
                        'album-create',
                        'album-update',
                        'sort-sound-in-album',
                        'sound-collect',
                        'album-delete',
                        'collect-album',
                        'sort-user-albums',
                        'sort-collect-albums',
                    ],
                    'roles' => ['@']
                ]

            ],
        ];
        return $behaviors;
    }

    /**
     * @inheritdoc
     */
    public function actions()
    {
        $actions = parent::actions();
        return $actions;
    }

    /**
     * @api {get} /collection/channel-detail 频道详情
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/collection/channel-detail
     * @apiSampleRequest /collection/channel-detail
     * @apiDescription  /mobile/site/getTagDetail
     *
     * @apiVersion 0.1.0
     * @apiName channel-detail
     * @apiGroup collection
     *
     * @apiParam {number} tid 频道id
     *
     *
     */
    public function actionChannelDetail(int $tid)
    {
        // WORKAROUND: 临时屏蔽频道
        throw new HttpException(404, '频道已失效', 200430001);
        $tag = MTag::find()->select('id, name, cover, icon, sintro, intro')
            ->where(['id' => $tid])->one();

        if (!$tag)
            throw new HttpException(404, '没有这个频道', 200430001);

        if (Yii::$app->user->id)
            MTag::isSubscribed($tag);

        return $tag;
    }

    /**
     * @api {get} /collection/channel-list 频道列表
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/collection/channel-list
     * @apiSampleRequest /collection/channel-list
     * @apiDescription  /mobile/site/channels 频道列表分页
     *
     * @apiVersion 0.1.0
     * @apiName channel-list
     * @apiGroup collection
     *
     * @apiParam {number =0, 1, 2, 3} type = 0 频道排序类型 0是M音,1为铃声频道, 2是全部 ,3声优
     * @apiParam {String} page = 1 当前页数
     * @apiParam {String} page_size = 30
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info":
     *           {
     *              Datas: [ [Object], [Object], [Object] ],
     *              pagination: { p: 1, count: '72', maxpage: 3, pagesize: 30 }
     *          }
     *     }
     *
     */
    public function actionChannelList(int $type = 0, int $page = 1, int $page_size = DEFAULT_PAGE_SIZE)
    {
        // WORKAROUND: 临时屏蔽频道
        return ReturnModel::empty($page, $page_size);
        $channel_ids = Catalog::find()
            ->select('id')
            ->where(['parent_id' => 65])
            ->column();

        $query = MTag::find()
            ->select('id, name, cover, icon, follow_num')
            ->where(['recommended' => 1])
            ->andWhere('sort_channel != 0')
            ->orderBy('sort_channel DESC');

        switch ($type) {
            case 0:
                $query->andWhere(['not in', 'catalogid', $channel_ids]);
                break;
            case 1:
                $query->andWhere(['in', 'catalogid', $channel_ids]);
                break;
            case 3:
                $query->andWhere(['in', 'catalogid', [24, 25]]);
                break;
            default://目前只有2 全部
                break;
        }

        return MUtils::getPaginationModels($query, $page_size);
    }

    /**
     * @api {get} /collection/channel-admin 频道管理员
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/collection/channel-admin
     * @apiSampleRequest /collection/channel-admin
     * @apiDescription  /mobile/site/channelAdmin  频道管理员 参数和现在所用接口一致
     *
     * @apiVersion 0.1.0
     * @apiName channel-admin
     * @apiGroup collection
     *
     * @apiParam {String} channel_id = 0 频道id
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info":
     *           [
     *              {
     *                 id: 2,
     *                 qquid: '9C693351D8B02474A5D4B4870F74BA08',
     *                 weibouid: '2134401290',
     *                 confirm: 1,
     *                 username: '六天七夜',
     *                  ......
     *              }
     *          ]
     *     }
     *
     */

    public function actionChannelAdmin(int $channel_id)
    {
        $channel_admins = Mowangskuser::find()
            ->alias('t')
            ->select('t.id, t.username, t.boardiconurl, t.avatar, t.icontype, t.confirm')
            ->leftJoin('m_channel_admin_map t1', 't.id = t1.admin_id')
            ->andWhere(['t1.channel_id' => $channel_id])
            ->limit(5)
            ->orderBy('level desc')
            ->all();
        if ($channel_admins) {
            return $channel_admins;
        } else {
            return '管理员不存在';
        }
    }

    /**
     * @api {get} /collection/rank 音单排行榜
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/collection/rank
     * @apiSampleRequest /collection/rank
     * @apiDescription  /mobile/site/albumList
     *
     * @apiVersion 0.1.0
     * @apiName rank
     * @apiGroup collection
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code Error code
     * @apiSuccess {String} info Result of request
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "album": {
     *             "id": "58",
     *             "title": "少伯的电台",
     *             "intro": "正儿八经的个人电台，说一些正儿八经的内容",
     *             "catalog_id": "12",
     *             "create_time": "1510786766",
     *             "last_update_time": "1439869592",
     *             "user_id": "346286",
     *             "username": "InVinCiblezz",
     *             "cover_image": "201409/15/37b464d1d314976846d723f415517fe3211246.jpg",
     *             "uptimes": "0",
     *             "refined": "0",
     *             "view_count": "2982",
     *             "comment_count": "0",
     *             "favorite_count": "4",
     *             "music_count": "10",
     *             "source": "0",
     *             "front_cover": "http://static.missevan.com/coversmini/201409/15/37b464d1d314976846d723f415517fe3211246.jpg"
     *           },
     *           "sounds": [
     *             {
     *               "id": "108354",
     *               "soundstr": "山桥羊车球猫枪·Steam七大名著！（害怕）",
     *               "view_count": "0",
     *               "checked": 2,
     *               "front_cover": "http://static.missevan.com/coversmini/nocover.png",
     *               "all_comments": 0,
     *               "view_count_formatted": 0
     *             },
     *           ]
     *         },
     *       ]
     *     }
     */
    public function actionRank()
    {
        $album_sound_ids = Yii::$app->redis->get(KEY_LIST_ALBUM_SOUND_IDS);
        if (!$album_sound_ids) {
            $album_sound_ids = Yii::$app->storage->download(OSS_PATH_ALBUM_SOUND_IDS);
        }
        $album_sound_ids = Json::decode($album_sound_ids) ?: OPTIONAL_ALBUM_IDS;

        $return = [];
        foreach ($album_sound_ids as $index => $album_id) {
            $return[$index]['album'] = MAlbum::findOne($album_id);
            $return[$index]['sounds'] = MSound::getTopNthInAlbum($album_id, 3);
        }

        return $return;
    }

    /**
     * @api {post} /collection/subscribe-channel 频道订阅
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/collection/subscribe-channel
     * @apiSampleRequest /collection/subscribe-channel
     * @apiDescription 频道订阅
     *
     * @apiVersion 0.1.0
     * @apiName subscribe-channel
     * @apiGroup collection
     *
     * @apiParam {Number} channel_id 频道 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code Error code
     * @apiSuccess {String} info Result of request
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "订阅成功"
     *     }
     */

    public function actionSubscribeChannel()
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }

        $channel_id = (int)Yii::$app->request->post('channel_id');
        $user_id = Yii::$app->user->id;
        $channel_exist = MTag::find()
            ->where('id = :channel_id', [':channel_id' => $channel_id])
            ->andWhere('recommended = 1')->exists();

        if (!$channel_exist)
            throw new HttpException(400, '该频道不存在', 200430001);

        $tag_exist = AvAttentionTag::find()
            ->where('user_id = :user_id', [':user_id' => $user_id])
            ->andwhere('tag_id = :tag_id', [':tag_id' => $channel_id])
            ->exists();

        $at = new AvAttentionTag();

        if ($tag_exist) {
            $at->deleteAll(['user_id' => $user_id, 'tag_id' => $channel_id]);
            return '取消订阅';
        } else {
            $at->user_id = $user_id;
            $at->tag_id = $channel_id;
            $at->time = $_SERVER['REQUEST_TIME'];
            $at->save();
            return '订阅成功';

        }
    }

    /**
     * @api {get} /collection/album-by-tag 根据标签获取音单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/collection/album-by-tag
     * @apiSampleRequest /collection/album-by-tag
     * @apiDescription  /mobile/site/soundByTag  根据标签获取单音或音单 p=>page pagesize => page_size
     *
     * @apiVersion 0.1.0
     * @apiName album-by-tag
     * @apiGroup collection
     *
     * @apiParam {Number} [tid=0]  标签id 0为获取全部推荐音单
     * @apiParam {Number} [page_size=20] 每页个数
     * @apiParam {Number} [page=1] 页数
     * @apiParam {Number=0, 1} [order=0] 0最热1最新
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info":
     *              {
     *                  Datas: [ [Object] ],
     *                  pagination: { p: 1, count: '1', maxpage: 1, pagesize: 20 }
     *              }
     *     }
     *
     */
    public function actionAlbumByTag(int $tid = 0, int $order = 0, int $page_size = 20, $page = 1)
    {
        if ($tid && !MTag::findOne($tid))
            throw new HttpException(404, '标签不存在', 200150001);

        $MTagAlbumMap = new MTagAlbumMap();

        $ModelDTO = $MTagAlbumMap->getTagAlbum($tid, $order, $page_size, $page);

        return $ModelDTO;
    }

    /**
     * @api {get} /collection/album{?album_id} 根据 ID 获取音单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/collection/album
     *
     * @apiVersion 0.1.0
     * @apiName album
     * @apiGroup collection
     *
     * @apiParam (Query) {Number} album_id 音单 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 1101,
     *         "title": "onijiang",
     *         "intro": "<p>喵特电台</p>",
     *         "catalog_id": "18",
     *         "create_time": 1415964839,
     *         "user_id": 66268,
     *         "username": "喵特电台",
     *         "uptimes": 0,
     *         "is_private": false,
     *         "view_count": 460,
     *         "comment_count": 0,
     *         "favorite_count": 0,
     *         "music_count": 5,
     *         "collected": 0,
     *         "front_cover": "https://static.missevan.com/coversmini/201411/14/bc22e2a46b6eaac6511ceaf0f34f0f60193358.jpg",
     *         "followed": 1  // 当前用户是否关注音单 UP 主 1：已关注或者是查看自己音单（不显示关注按钮）；0：未关注（显示关注按钮）
     *       }
     *     }
     */
    public function actionAlbum(int $album_id)
    {
        if ($album_id <= 0) {
            throw new HttpException(400, '参数错误');
        }
        $album = MAlbum::findOne($album_id);
        $user_id = (int)Yii::$app->user->id;
        MAlbum::checkCanViews($album, $user_id);
        MAlbum::isCollected($album);
        $followed = MAttentionUser::TYPE_STRANGER;
        if ($user_id) {
            $followed = $album->user_id === $user_id ? MAttentionUser::TYPE_FOLLOWING :
                MAttentionUser::DoYouAttention($album->user_id);
        }
        $album_info = [
            'id' => $album->id,
            'title' => $album->title,
            'intro' => $album->intro,
            'create_time' => $album->create_time,
            'user_id' => $album->user_id,
            'username' => $album->username,
            'uptimes' => $album->uptimes,
            // 是否为私有音单
            'is_private' => $album->isPrivate(),
            'view_count' => $album->view_count,
            'comment_count' => $album->comment_count,
            'favorite_count' => $album->favorite_count,
            'music_count' => $album->music_count,
            // 当前用户是否收藏
            'collected' => $album->collected,
            'front_cover' => $album->front_cover,
            // 当前用户是否关注音单 UP 主
            'followed' => $followed,
        ];
        return $album_info;
    }

    /**
     * @api {post} /collection/sort-sound-in-album 重新排列音单中音频顺序
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/collection/sort-sound-in-album
     * @apiSampleRequest /collection/sort-sound-in-album
     * @apiDescription 重新排列音单中音频顺序
     * @apiVersion 0.1.0
     * @apiName sort-sound-in-album
     * @apiGroup collection
     *
     * @apiParam {Number[]} sound_ids 需要修改位置的音频 ID
     * @apiParam {Number} album_id 将要收藏进的音单 ID
     * @apiParam {Number[]} positions 插入的位置（从 0 开始）
     * @apiParam {number=0,1} [order=0] 显示方式（0 为正排，1 为倒排）
     *
     * @apiSuccess {Boolean} success Status of request.
     * @apiSuccess {Number} code Error code
     * @apiSuccess {String} info Result of request.
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "排序成功"
     *     }
     */
    public function actionSortSoundInAlbum()
    {
        $album_id = (int)Yii::$app->request->post('album_id');
        $order = (int)Yii::$app->request->post('order');
        $sound_ids = Yii::$app->request->post('sound_ids') ?: [(int)Yii::$app->request->post('sound_id')];
        $positions = Yii::$app->request->post('positions') ?: [0];
        if (count($sound_ids) !== count($positions) || !MUtils2::isUintArr($sound_ids) || !MUtils2::isUintArr($positions)) {
            throw new HttpException(400, '参数错误');
        }
        $sound_ids = array_map('intval', $sound_ids);
        $positions = array_map('intval', $positions);

        if (!($album = MAlbum::findOne($album_id))) {
            throw new HttpException(404, '该音单不存在', 200120001);
        }
        if ($album->user_id !== Yii::$app->user->id) {
            throw new HttpException(403, '您未拥有此音单', 200120005);
        }
        $transaction = Yii::$app->db->beginTransaction();
        try {
            foreach ($sound_ids as $i => $sound_id) {
                $this->orderSound($album_id, $sound_id, $positions[$i], $order);
            }
            MAlbum::updateAll(['last_update_time' => $_SERVER['REQUEST_TIME']], 'id = :id', [':id' => $album_id]);
            $transaction->commit();
            return '排序成功';
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * 对音单中音频进行排序
     *
     * @param integer $album_id 音单 ID
     * @param integer $sound_id 音频 ID
     * @param integer $position 排入的位置（从 0 开始）
     * @param integer $order 显示方式（0 为正序，1 为倒序）
     * @return boolean
     * @throws HttpException
     */
    private function orderSound(int $album_id, int $sound_id, int $position, int $order)
    {
        if (!($sound = MSoundAlbumMap::findOne(['sound_id' => $sound_id, 'album_id' => $album_id]))) {
            throw new HttpException(404, '该音单中没有该音频', 200120004);
        }
        // WORKAROUND: 新版本传递音频移入位置的参数 positions，兼容线上版本
        if (Equipment::isAppOlderThan('4.2.5', '5.1.4')) {
            $b_sound_id = (int)Yii::$app->request->post('b_sound_id');

            $b_sound = null;
            if ($b_sound_id
                && !($b_sound = MSoundAlbumMap::findOne(['sound_id' => $b_sound_id, 'album_id' => $album_id]))) {
                throw new HttpException(404, '音频不存在，请刷新', 200120001);
            }

            $c_sound = MSoundAlbumMap::find()
                ->where(['album_id' => $album_id])
                ->andWhere('sort > :sort', [':sort' => $b_sound ? $b_sound->sort : -MSoundAlbumMap::SORT_MAX])
                ->orderBy('sort ASC')
                ->limit(1)->one();
        } else {
            $sounds = MSoundAlbumMap::find()->where('album_id = :album_id AND sound_id <> :sound_id')
                ->params([':album_id' => $album_id, ':sound_id' => $sound_id])
                ->orderBy(['sort' => !$order ? SORT_ASC : SORT_DESC])->offset($position - 1)->limit(2)->all();
            if (empty($sounds)) throw new HttpException(404, '音频不存在，请刷新', 200120001);
            if ($position === 0) {
                // 排在首位
                $b_sound = null;
                $c_sound = current($sounds);
            } elseif (count($sounds) === 1) {
                // 排在末尾
                $b_sound = current($sounds);
                $c_sound = null;
            } else {
                // 排在中间
                $b_sound = $sounds[0];
                $c_sound = $sounds[1];
            }
        }
        // b_sound 为插入位置前的一个音、c_sound 为插入位置后的一个音
        $margin = !$order ? MSoundAlbumMap::SORT_INTERVAL : -MSoundAlbumMap::SORT_INTERVAL;
        if ($b_sound && $c_sound) {
            // 排列在中间
            $new_sort = $b_sound->sort + (($c_sound->sort - $b_sound->sort) >> 1);
            $need_reorder = ($c_sound->sort - $new_sort) < MSoundAlbumMap::SORT_MIN_THRESHOLD;
        } elseif ($b_sound) {
            // 排列在最后一个
            $new_sort = $b_sound->sort + $margin;
            $need_reorder = abs($new_sort) > MSoundAlbumMap::SORT_MAX;
        } else {
            // 排列在第一个
            $new_sort = $c_sound->sort - $margin;
            $need_reorder = abs($new_sort) > MSoundAlbumMap::SORT_MAX;
        }
        MSoundAlbumMap::updateAll(['sort' => $new_sort], 'id = :id', [':id' => $sound->id]);
        if ($need_reorder) MSoundAlbumMap::reorderSounds($album_id);
        return true;
    }

    /**
     * @api {get} /collection/album-collect 切换音单收藏状态
     * @apiDeprecated
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/collection/album-collect
     * @apiSampleRequest /collection/album-collect
     * @apiDescription 切换音单收藏状态（iOS 4.1.1 及其以下、安卓 3.7.0 及其以下、安卓 5.0.0 调用了该接口）
     *
     * @apiVersion 0.1.0
     * @apiName album-collect
     * @apiGroup collection
     *
     * @apiParam {Number} album_id 专辑 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code Error code
     * @apiSuccess {String} info Result of request
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "收藏音单成功"
     *     }
     */
    public function actionAlbumCollect()
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }
        $album_id = (int)Yii::$app->request->get('album_id');

        $user_id = Yii::$app->user->id;
        $album = MAlbum::findOne(['id' => $album_id]);
        if (!$album) throw new HttpException(404, '该音单不存在', 200120001);
        if ($album->user_id === $user_id) throw new HttpException(400, '不能收藏自己的音单', 200120002);

        $mcollect = new MCollectAlbum();
        $mcollect->user_id = $user_id;
        $mcollect->album_id = $album_id;
        $mcollect->type = 0;

        if (!$collect = $mcollect->findOne(['user_id' => $user_id, 'album_id' => $album_id, 'type' => 0])) {
            $mcollect->time = $_SERVER['REQUEST_TIME'];
            $mcollect->save();
            return '收藏音单成功';
        } else {
            $collect->delete();
            return '取消收藏音单成功';
        }
    }

    /**
     * @api {post} /collection/collect-album 收藏或取消收藏音单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/collection/collect-album
     * @apiSampleRequest /collection/collect-album
     * @apiDescription 收藏或取消收藏音单（iOS 4.1.2 及其以上、安卓 5.0.1 及其以上调用该接口）
     *
     * @apiVersion 0.1.0
     * @apiName collect-album
     * @apiGroup collection
     *
     * @apiParam {Number} album_id 音单 ID （iOS < 4.7.8 Android < 5.6.7 时传递）
     * @apiParam {Number[]} album_ids 音单 IDs（iOS >= 4.7.8 Android >= 5.6.7 时传递）
     * @apiParam {number=0,1} [type=1] 操作类型（收藏 1，取消收藏 0）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code Error code
     * @apiSuccess {String} info Result of request
     *
     * @apiSuccessExample Success-Response: 收藏音单成功
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "收藏音单成功"
     *     }
     *
     * @apiSuccessExample Success-Response: 取消收藏音单成功
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "取消收藏音单成功"
     *     }
     */
    public function actionCollectAlbum()
    {
        if (Equipment::isAppOlderThan('4.7.8', '5.6.7')) {
            // WORKAROUND: iOS < 4.7.8 Android < 5.6.7 不支持批量删除音单
            $album_id = (int)Yii::$app->request->post('album_id');
            if ($album_id <= 0) {
                throw new HttpException(400, '参数错误');
            }
            $album_ids = [$album_id];
        } else {
            $album_ids = Yii::$app->request->post('album_ids');
        }
        $type = (int)Yii::$app->request->post('type', MUtils::ACTION_DO);
        if (!in_array($type, [MUtils::ACTION_DO, MUtils::ACTION_UNDO]) || empty($album_ids) ||
                !MUtils2::isUintArr($album_ids)) {
            throw new HttpException(400, '参数错误');
        }
        $MAX_ALBUM_NUM = 100;
        if (count($album_ids) > $MAX_ALBUM_NUM) {
            $msg = $type ? "单次收藏的音单数量不能超过 $MAX_ALBUM_NUM" :
                "单次取消收藏的音单数量不能超过 $MAX_ALBUM_NUM";
            throw new HttpException(403, $msg);
        }
        $album_ids = array_unique(array_map('intval', $album_ids));
        $user_id = Yii::$app->user->id;
        return MCollectAlbum::batchCollectOrNot($album_ids, $user_id, $type);
    }

    /**
     * @api {get} /collection/album-tags 获取音单标签
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/collection/album-tags
     * @apiSampleRequest /collection/album-tags
     * @apiDescription  /mobile/site/AlbumTags 标签音单
     *
     * @apiVersion 0.1.0
     * @apiName album-tags
     * @apiGroup collection
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info":
     *           [
     *              {'tag' => ..., 'name' => ..., 'pic' => ... }
     *              ......
     *          ]
     *     }
     *
     */
    public function actionAlbumTags(int $dark = 0)
    {
        $pic = [
            [Yii::$app->params['mimagesbigUrl'] . '201706/23/47728483ba53032c3fef2e7f897b4643152611.png',
                Yii::$app->params['mimagesbigUrl'] . '201706/23/55ce69d428311d957ad9bbc52dbf32b1152611.png',
                Yii::$app->params['mimagesbigUrl'] . '201706/23/8844554d1cbb8f7d62f1eb9a38cb8d73152611.png'],
            [Yii::$app->params['mimagesbigUrl'] . '201706/23/41c1345682683e3f11e60e30257b4b7e152612.png',
                Yii::$app->params['mimagesbigUrl'] . '201706/23/712369f9268bfffaa439272b5cc164cf152612.png',
                Yii::$app->params['mimagesbigUrl'] . '201706/23/dfe7e906a094ec64affd4393cfa78c8c152611.png']
        ];
        $tag1[] = ['name' => '热血', 'id' => 170];
        $tag1[] = ['name' => '治愈', 'id' => 28];
        $tag1[] = ['name' => '抖腿', 'id' => 4421];
        $tag2[] = ['name' => '催眠', 'id' => 2674];
        $tag2[] = ['name' => '玩游戏', 'id' => 26310];
        $tag2[] = ['name' => '运动听', 'id' => 26311];
        $tag2[] = ['name' => '作业向', 'id' => 25];
        $tag3[] = ['name' => 'OP', 'id' => 370];
        $tag3[] = ['name' => 'ED', 'id' => 376];
        $tag3[] = ['name' => '翻唱', 'id' => 273];
        $tag3[] = ['name' => '古风', 'id' => 5];
        $tag3[] = ['name' => '游戏原声', 'id' => 13349];
        $tag3[] = ['name' => '偶像团体', 'id' => 26312];
        $tag3[] = ['name' => '同人音乐', 'id' => 850];
        $tag3[] = ['name' => '广播剧', 'id' => 4];
        $tag = [
            ['tag' => $tag1, 'name' => '情感', 'pic' => $pic[$dark][0]],
            ['tag' => $tag2, 'name' => '场景', 'pic' => $pic[$dark][1]],
            ['tag' => $tag3, 'name' => '主题', 'pic' => $pic[$dark][2]],

        ];
        return $tag;
    }

    /**
     * @api {post} /collection/album-create 创建音单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/collection/album-create
     *
     * @apiVersion 0.1.0
     * @apiName album-create
     * @apiGroup collection
     *
     * @apiParam {String} MAlbum[title] 标题
     * @apiParam {String} MAlbum[intro] 专辑介绍
     * @apiParam {Number} MAlbum[is_private] 是否创建私有音单 0 公有，1 私有
     * @apiParam {String} [tags=[]] 专辑标签，使用逗号分隔
     * @apiParam {file} [img] 封面
     *
     * @apiSuccess {Boolean} success true
     * @apiSuccess {Number} code
     * @apiSuccess {Number} info
     *
     * @apiSuccessExample iOS < 4.7.8 或 Android < 5.6.7:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": 119
     *     }
     *
     * @apiSuccess {Boolean} success true
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample iOS >= 4.7.8 或 Android >= 5.6.7:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 119,
     *         "username": "233366666",
     *         "title": "测试创建音单",
     *         "music_count": 0,
     *         "collected": 0,
     *         "front_cover": "https://static.missevan.com/coversmini/nocover.png",
     *         "is_private": false,
     *         "sort": 1048576
     *       }
     *     }
     *
     * @apiError {Boolean} success false.
     * @apiError {Number} code
     * @apiError {String} info
     *
     * @apiErrorExample {json} 音单标题重复:
     *     HTTP/1.1 400
     *     {
     *       "success": true,
     *       "code": 200120002,
     *       "info": "音单标题重复"
     *     }
     * @apiErrorExample {json} 含有非法字符:
     *     HTTP/1.1 400
     *     {
     *       "success": true,
     *       "code": 200120002,
     *       "info": "含有非法字符"
     *     }
     * @apiErrorExample {json} 音单标题超过 30 个字符:
     *     HTTP/1.1 400
     *     {
     *       "success": true,
     *       "code": 200120002,
     *       "info": "音单标题超过 30 个字符"
     *     }
     * @apiErrorExample {json} 音单数量已达上限:
     *     HTTP/1.1 403
     *     {
     *       "success": true,
     *       "code": 200020007,
     *       "info": "(′・_・`) 您的音单数量已达上限 1000"
     *     }
     *
     */
    public function actionAlbumCreate()
    {
        $user_id = Yii::$app->user->id;
        $album = Yii::$app->request->post('MAlbum');
        $tags = Yii::$app->request->post('tags', []);
        if (!$album || !array_key_exists('title', $album) || !array_key_exists('intro', $album)) {
            throw new HttpException(400, '参数错误');
        }
        $user_info = Mowangskuser::findOne($user_id);
        if ($user_info->albumnum >= MAlbum::MAX_CREATE_ALBUM_NUM) {
            throw new HttpException(403, '(′・_・`) 您的音单数量已达上限 ' . MAlbum::MAX_CREATE_ALBUM_NUM,
                200020007);
        }
        $load_param = [
            'title' => $album['title'],
            'intro' => $album['intro'],
            // 用户 ID 需要在 validate 前赋值，因为 checkTitle 验证规则需要判断该用户是否有重名的音单
            'user_id' => $user_id,
            'username' => $user_info->username,
        ];
        $model = new MAlbum();
        if (!$model->load($load_param, '')) {
            throw new HttpException(400, '参数错误');
        }
        if (!$model->validate()) {
            throw new HttpException(400, MUtils2::getFirstError($model));
        }
        // 处理图片
        $img = UploadedFile::getInstanceByName('img');
        // 设置属性为无封面
        $model->refined = MAlbum::REFINED_NO_COVER;
        if ($img && $img->tempName) {
            $model->uploadCoverImage($img->tempName);
            // 设置属性为默认（有封面且非私密）
            $model->refined = MAlbum::REFINED_DEFAULT;
        }
        // 设置音单私有性
        if (isset($album['is_private']) && $album['is_private']) {
            $model->refined |= MAlbum::REFINED_PRIVATE;
        }
        $transaction = Yii::$app->db->beginTransaction();
        try {
            if (!$model->save()) {
                throw new HttpException(400, MUtils2::getFirstError($model));
            }
            $album_id = $model->id;
            if (!empty($tags)) {
                // 保存标签
                try {
                    $tag = new MTag(['scenario' => MTag::SCENARIO_ALBUM_TAG]);
                    $tag->build('create', $tags, $album_id, 'album');
                } catch (\yii\db\Exception $e) {
                    // 忽略唯一索引抛出的异常
                    if (!MUtils2::isUniqueError($e, MTag::getDb())) {
                        throw $e;
                    }
                }
            }
            // 个人专辑数 +1
            $user_info->updateCounters(['albumnum' => 1]);
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
        if (Equipment::isAppOlderThan('4.7.8', '5.6.7')) {
            // WORKAROUND: iOS < 4.7.8 或 Android < 5.6.7 只返回新建音单 ID
            return $album_id;
        }
        // 获取封面图
        $model->assignAlbumFrontCover();
        return [
            'id' => $model->id,
            'username' => $model->username,
            'title' => $model->title,
            'intro' => $model->intro,
            'music_count' => $model->music_count,
            'front_cover' => $model->front_cover,
            'collected' => $model->collected,
            'is_private' => $model->isPrivate(),
            'sort' => $model->sort,
        ];
    }

    /**
     * @api {post} /collection/album-update 更新音单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/collection/album-update
     *
     * @apiVersion 0.1.0
     * @apiName album-update
     * @apiGroup collection
     *
     * @apiParam {Number} album_id 音单 ID
     * @apiParam {String} MAlbum[title] 标题
     * @apiParam {String} MAlbum[intro] 专辑介绍
     * @apiParam {Number} MAlbum[is_private] 是否更新成私有音单 0 公有，1 私有
     * @apiParam {String[]} [tags] 专辑标签 \
     * iOS >= 4.7.9 Android >= 5.6.6 \
     *   清空标签：tags 需要传 [''] 来表示，因为 form urlencoded 不太好表示空数组，这里实际上代表的是 tags[]= 的形式； \
     *   不更新标签：不传值
     * @apiParam {file} [img] 封面
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code Error Code
     * @apiSuccess {Number} info Result of request
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "修改成功"
     *     }
     */
    public function actionAlbumUpdate()
    {
        $user_id = Yii::$app->user->id;
        $param_album = Yii::$app->request->post('MAlbum');
        $tags = Yii::$app->request->post('tags');
        $album_id = (int)Yii::$app->request->post('album_id');
        if (!$param_album) {
            throw new HttpException(400, '参数错误');
        }
        if (!Equipment::isAppOlderThan('4.7.9', '5.6.6')) {
            // iOS >= 4.7.9 Android >= 5.6.6 时，tags 为 [''] 表示清空标签
            $tags = ((!is_null($tags) && '' === current($tags)) ? [] : $tags);
        } elseif (Equipment::isAppOlderThan('4.7.9')) {
            // iOS < 4.7.9 时，tags 为 null 表示清空标签
            $tags = (is_null($tags) ? [] : $tags);
        }

        if (!$malbum = MAlbum::findOne(['id' => $album_id, 'user_id' => $user_id])) {
            throw new HttpException(404, '该音单不存在');
        }
        $load_param = [];
        // 判断是否需要更新剧集信息
        $need_update = false;
        // 因为客户端存在只传用户修改了的参数情况，所以需要判断参数是否存在
        if (array_key_exists('title', $param_album) && $param_album['title']) {
            $load_param['title'] = $param_album['title'];
            $need_update = true;
        }
        if (array_key_exists('intro', $param_album) && $param_album['intro']) {
            $load_param['intro'] = $param_album['intro'];
            $need_update = true;
        }
        // 用户没有修改 title 和 intro 的情况下，load_param 是空数组，此时不需要进行 load 和 validate 操作
        if ($load_param && $malbum->load($load_param, '') && !$malbum->validate()) {
            throw new HttpException(400, MUtils2::getFirstError($malbum));
        }
        if (array_key_exists('is_private', $param_album)) {
            switch ($param_album['is_private']) {
                case self::SET_ALBUM_PUBLIC:
                    $malbum->refined &= ~MAlbum::REFINED_PRIVATE;
                    $need_update = true;
                    break;
                case self::SET_ALBUM_PRIVATE:
                    if (MAlbum::isRecommendAlbum($album_id)) {
                        throw new HttpException(403, '音单为推荐音单，不能设为私密状态哦~');
                    }
                    $malbum->refined |= MAlbum::REFINED_PRIVATE;
                    $need_update = true;
                    break;
                default:
                    throw new HttpException(400, '参数错误');
            }
        }
        // 处理图片
        $img = UploadedFile::getInstanceByName('img');
        if ($img && $img->tempName) {
            $malbum->uploadCoverImage($img->tempName);
            if ($malbum->refined & MAlbum::REFINED_NO_COVER) {
                $malbum->refined &= ~MAlbum::REFINED_NO_COVER;
            }
            $need_update = true;
        }
        if (!$need_update && is_null($tags)) {
            // 没有修改音单信息和音单标签时，直接返回修改成功
            return '修改成功';
        }
        $transaction = Yii::$app->db->beginTransaction();
        try {
            // 修改了音单属性再执行更新音单操作
            if ($need_update && !$malbum->save()) {
                throw new HttpException(400, MUtils2::getFirstError($malbum));
            }
            if (!is_null($tags)) {
                try {
                    // 更新标签信息
                    $tag = new MTag(['scenario' => MTag::SCENARIO_ALBUM_TAG]);
                    $tag->build('update', $tags, $album_id, 'album');
                } catch (\yii\db\Exception $e) {
                    // 忽略唯一索引抛出的异常
                    if (!MUtils2::isUniqueError($e, MTag::getDb())) {
                        throw $e;
                    }
                }
            }
            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
        return '修改成功';
    }

    /**
     * @api {post} /collection/album-delete 删除音单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/collection/album-delete
     * @apiSampleRequest /collection/album-delete
     * @apiDescription 删除音单
     *
     * @apiVersion 0.1.0
     * @apiName album-delete
     * @apiGroup collection
     *
     * @apiParam {Number} album_id 音单 ID（iOS < 4.7.8 Android < 5.6.7 时传递）
     * @apiParam {Number[]} album_ids 音单 IDs（iOS >= 4.7.8 Android >= 5.6.7 时传递）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info Result of request
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "音单删除成功"
     *     }
     */
    public function actionAlbumDelete()
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }
        if (Equipment::isAppOlderThan('4.7.8', '5.6.7')) {
            // WORKAROUND: iOS < 4.7.8 Android < 5.6.7 不支持批量删除音单
            $album_id = (int)Yii::$app->request->post('album_id');
            if ($album_id <= 0) {
                throw new HttpException(400, '参数错误');
            }
            $album_ids = [$album_id];
        } else {
            $album_ids = Yii::$app->request->post('album_ids');
        }
        if (empty($album_ids) || !MUtils2::isUintArr($album_ids)) {
            throw new HttpException(400, '参数错误');
        }
        if (count($album_ids) > MAlbum::MAX_DELETE_ALBUM_NUM) {
            throw new HttpException(403, '单次删除音单数量不能超过 ' . MAlbum::MAX_DELETE_ALBUM_NUM);
        }
        $album_ids = array_unique(array_map('intval', $album_ids));
        $user_id = Yii::$app->user->id;
        $exist_album_ids = MAlbum::find()->select('id')
            ->where(['id' => $album_ids, 'user_id' => $user_id])
            ->column();
        if (count($exist_album_ids) !== count($album_ids)) {
            throw new HttpException(404, '音单不存在');
        }
        if (MAlbum::isRecommendAlbum($exist_album_ids)) {
            throw new HttpException(403, '删除音单中有推荐音单，无法进行删除，可联系客服解决');
        }
        MAlbum::deleteAlbums($exist_album_ids, $user_id);
        return '音单删除成功';
    }

    /**
     * @api {post} /collection/sound-collect 批量收藏单音到音单或取消收藏
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/collection/sound-collect
     * @apiSampleRequest /collection/sound-collect
     * @apiDescription 批量收藏单音到音单或取消收藏
     *
     * @apiVersion 0.1.0
     * @apiName sound-collect
     * @apiGroup collection
     *
     * @apiParam {Number[]} sound_ids 需要收藏的单音 ID
     * @apiParam {Number} album_id 将要收藏进的音单 ID
     * @apiParam {number=0,1} [type=1] 0：取消收藏；1：收藏
     * @apiParam {number=1,2} [scenario=2] 1：播放页收藏或取消收藏；2：批量收藏或者取消收藏
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code Error code
     * @apiSuccess {String} info Result of request
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "msg": "收藏音频成功",
     *         "collection_status": true,
     *         "collect_num": 1
     *       }
     *     }
     */
    public function actionSoundCollect()
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }

        $user_id = Yii::$app->user->id;
        $sound_ids = Yii::$app->request->post('sound_ids');
        $album_id = (int)Yii::$app->request->post('album_id');
        $type = (int)Yii::$app->request->post('type', MAlbum::SOUND_COLLECT);
        $scenario = (int)Yii::$app->request->post('scenario', self::SOUND_COLLECT_SCENARIO_DEFAULT);

        if ($album_id <= 0 || empty($sound_ids) || !MUtils2::isUintArr($sound_ids)) {
            throw new HttpException(400, '参数错误');
        }
        $sound_ids = array_unique(array_map('intval', $sound_ids));
        $count = count($sound_ids);
        if (self::SOUND_COLLECT_SCENARIO_PLAY === $scenario && 1 !== $count) {
            throw new HttpException(400, '参数错误');
        }
        if ($count > MAlbum::MAX_COLLECT_NUM) {
            throw new HttpException(400, '操作的音频数不能超过 ' . MAlbum::MAX_COLLECT_NUM);
        }
        if (!$album = MAlbum::findOne(['id' => $album_id])) throw new HttpException(404, '该音单不存在');
        if ($album->user_id !== $user_id) throw new HttpException(403, '该音单不归你所有');
        // 对于如果音频被特殊下架（合约期满）则音频可被取消收藏，但不能再继续收藏
        if (MAlbum::SOUND_COLLECT === $type) {
            if ($res = Drama::rpc('api/get-dramaid-by-soundid', ['sound_ids' => $sound_ids])) {
                if (Drama::checkDramaRefined(current($res)['drama_id'], Drama::REFINED_TYPE_SPECIAL)) {
                    throw new HttpException(403, '当前内容是限定音 暂不能进行此操作');
                }
            }
            $checked_array = MSound::find()->select('checked')
                ->where([
                    'id' => $sound_ids,
                    'checked' => [MSound::CHECKED_PASS, MSound::CHECKED_POLICE, MSound::CHECKED_CONTRACT_EXPIRED],
                ])->asArray()->column();
            $count_r = count($checked_array);
            if (1 === count($checked_array) && in_array(MSound::CHECKED_CONTRACT_EXPIRED, $checked_array)) {
                throw new HttpException(403, '当前内容暂不能进行此操作');
            }
            if ($count !== $count_r || in_array(MSound::CHECKED_CONTRACT_EXPIRED, $checked_array)) {
                throw new HttpException(404, '有 ' . ($count - $count_r) . ' 个不存在或合约到期的音频');
            }
        } else {
            $count_r = (int)MSoundAlbumMap::find()
                ->where(['album_id' => $album_id, 'sound_id' => $sound_ids])->count();
            if ($count !== $count_r) {
                throw new HttpException(404, '有 ' . ($count - $count_r) . ' 个音频未被收藏');
            }
        }
        $return = MSoundAlbumMap::collectSoundOrNot($sound_ids, $album, $type);
        if (self::SOUND_COLLECT_SCENARIO_PLAY === $scenario) {
            $sound_id = $sound_ids[0];
            // 播放页的收藏操作需要返回执行收藏操作用户的包含该音频的音单总数
            $collect_num = MAlbum::find()->alias('a')
                ->leftJoin('m_sound_album_map s', 'a.id = s.album_id')
                ->where(['s.sound_id' => $sound_id, 'a.user_id' => $user_id])
                ->count();
            $collect_num = (int)$collect_num;
            $return['collect_num'] = $collect_num;
        }
        return $return;
    }

    /**
     * @api {post} /collection/sort-user-albums 自建音单用户自定义排序
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/collection/sort-user-albums
     *
     * @apiVersion 0.1.0
     * @apiName sort-user-albums
     * @apiGroup collection
     *
     * @apiParam {number=0,1} [reorder=0] 是否需要重新排序 0：否；1：是
     * @apiParam {Object} positions 音单和对应的排序值，例：['5218' => 0, '5219' => 2097152] \
     * 客户端需要使用 int64 长度的字段表示排序值 \
     * （Android java 使用 long, iOS Objective-C 使用 long long）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "排序成功"
     *     }
     */
    public function actionSortUserAlbums()
    {
        $positions = Yii::$app->request->post('positions');
        $reorder = (int)Yii::$app->request->post('reorder', 0);
        if (!is_array($positions) || !$positions) {
            throw new HttpException(400, '参数错误');
        }
        $album_ids = array_keys($positions);
        if (!MUtils2::isUintArr($album_ids)) {
            throw new HttpException(400, '参数错误');
        }
        // 音单排序值可能是负整数，所以需要单独判断
        $album_sorts = array_values($positions);
        if (!self::checkSortsValue($album_sorts)) {
            throw new HttpException(400, '参数错误');
        }
        $user_id = Yii::$app->user->id;
        $album_count = (int)MAlbum::find()
            ->where('user_id = :user_id', [':user_id' => $user_id])
            ->andWhere(['id' => $album_ids])
            ->count();
        if ($album_count !== count($album_ids)) {
            throw new HttpException(403, '您没有更改权限', 200120005);
        }
        MAlbum::sortAlbums($reorder, $user_id, $positions);
        return '排序成功';
    }

    /**
     * @api {post} /collection/sort-collect-albums 收藏音单用户自定义排序
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/collection/sort-collect-albums
     *
     * @apiVersion 0.1.0
     * @apiName sort-collect-albums
     * @apiGroup collection
     *
     * @apiParam {number=0,1} [reorder=0] 是否需要重新排序 0：否；1：是
     * @apiParam {Object} positions 音单和对应的排序值，例：['5218' => 0, '5219' => 2097152] \
     * 客户端需要使用 int64 长度的字段表示排序值 \
     * （Android java 使用 long, iOS Objective-C 使用 long long）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "排序成功"
     *     }
     */
    public function actionSortCollectAlbums()
    {
        $positions = Yii::$app->request->post('positions');
        $reorder = (int)Yii::$app->request->post('reorder', 0);
        if (!is_array($positions) || !$positions) {
            throw new HttpException(400, '参数错误');
        }
        $album_ids = array_keys($positions);
        if (!MUtils2::isUintArr($album_ids)) {
            throw new HttpException(400, '参数错误');
        }
        // 音单排序值可能是负整数，所以需要单独判断
        $album_sorts = array_values($positions);
        if (!self::checkSortsValue($album_sorts)) {
            throw new HttpException(400, '参数错误');
        }
        $user_id = Yii::$app->user->id;
        $album_count = (int)MCollectAlbum::find()
            ->where('user_id = :user_id', [':user_id' => $user_id])
            ->andWhere(['album_id' => $album_ids])
            ->count();
        if ($album_count !== count($album_ids)) {
            throw new HttpException(403, '您没有更改权限', 200120005);
        }
        MCollectAlbum::sortAlbums($reorder, $user_id, $positions);
        return '排序成功';
    }

    /**
     * 判断客户端传过来的 sort 值是否合法
     *
     * @param array $sorts
     * @return bool
     * @todo 之后可以考虑迁移到 php-utils 项目中
     */
    private static function checkSortsValue(array $sorts)
    {
        if (empty($sorts)) {
            return false;
        }
        foreach ($sorts as $sort) {
            if (!is_numeric($sort) || strpos($sort, '.') !== false) {
                // 非数字或者非整型
                return false;
            }
        }
        // 判断 sort 值是否重复
        $unique_sorts = array_unique($sorts);
        if (count($unique_sorts) !== count($sorts)) {
            return false;
        }
        return true;
    }
}
