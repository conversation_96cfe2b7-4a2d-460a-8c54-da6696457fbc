<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/17
 * Time: 18:43
 */

namespace app\controllers;

use app\components\auth\AuthAli;
use app\components\auth\AuthQQ;
use app\components\auth\AuthWechat;
use app\components\auth\AuthWeibo;
use app\components\base\filter\AccessControl;
use app\components\util\MUtils;
use app\components\util\Captcha;
use app\components\util\SSOClient;
use app\forms\LoginForm;
use app\forms\VcodeForm;
use app\middlewares\Controller;
use app\models\BiliVipMaoerBenefitMenu;
use app\models\BiliVipMaoerUserBenefit;
use app\models\Certification;
use app\models\MAlbum;
use app\models\MImage;
use app\models\Mowangskuser;
use app\models\MSound;
use app\models\MUserCover;
use app\models\UserAddendum;
use app\components\util\Equipment;
use missevan\storage\StorageClient;
use missevan\support\Country;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\filters\VerbFilter;
use yii\web\HttpException;
use yii\web\UploadedFile;

class MemberController extends Controller
{
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'login' => ['post'],
                'logout' => ['post'],
                'forget-password' => ['post'],
                'confirm-account' => ['post'],
                'bind-account' => ['post'],
                'third-bind' => ['post'],
                'change-password' => ['post'],
                'zhima' => ['post'],
                'zhima-confirm' => ['post'],
                'youzan-login' => ['post'],
                'fast-login' => ['post'],
                'auth-login2' => ['post'],
                'fast-auth-bind' => ['post'],
                'sms-auth-bind' => ['post'],
                'sms-login' => ['post'],
                'check-fast-login' => ['post'],
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'only' => ['logout', 'info', 'set-sex', 'set-birthday', 'update-info', 'confirm-account', 'bind-account',
                'third-bind', 'change-password', 'zhima', 'zhima-confirm', 'youzan-login'],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => ['logout', 'set-sex', 'set-birthday', 'info', 'update-info', 'confirm-account',
                        'bind-account', 'third-bind', 'change-password', 'zhima', 'zhima-confirm', 'youzan-login'],
                    'roles' => ['@']
                ],
            ]
        ];
        return $behaviors;
    }

    /**
     * @api {post} /member/login 登录
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/login
     * @apiSampleRequest member/login
     * @apiDescription  /mobile/site/login 登录
     * old_params: {String} email 手机号或者email,{String} password 密码
     *
     * @apiVersion 0.1.0
     * @apiName login
     * @apiGroup member
     *
     * @apiParam {String} account 邮箱或者手机号
     * @apiParam {String} password 密码
     * @apiParam {String} [region='CN'] 地区
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "id": 346287,
     *       "token": "5d2c696c1536a85b42f85255|3433a977e24d36e7|**********|82ab9807fadf3676",
     *       "expire_at": **********,
     *       "user": {
     *         "id": 346287,
     *         "username": "********",
     *         "vip_info": {
     *           "status": 1,  // 会员状态。0：未开通过正式会员；1：正式会员；2：体验会员（暂不下发）；3：正式会员已过期
     *           "end_time": **********  // 会员过期时间戳（含最后一秒），单位：秒
     *         },
     *         ...
     *       }
     *     }
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 500
     *     {
     *       success: false, code: 500, info: '密码错误'
     *     }
     *
     */
    public function actionLogin()
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }
        if (!Yii::$app->equip->getBuvid()) {
            // iOS 8 会兼容到不支持 buvid 的版本，这里提示联系客服
            throw new HttpException(400, '客户端异常，请联系客服');
        }
        if (ENABLE_NOCAPTCHA && !defined('YII_CAPTCHA_TEST')) {
            MUtils::checkCaptcha(Captcha::SCENEORIGINAL_LOGIN);
        }
        $login_form = new LoginForm(['scenario' => LoginForm::SCENARIO_LOGIN]);
        if ($login_form->load(Yii::$app->request->post(), '') && ($info = $login_form->login())) {
            $user = Mowangskuser::getPersonInfo($info->user_id);
            $IS_BAN_LOGIN = false;
            // 判断用户是否是永久禁止登录
            if ($user && $user->isBanLogin()) {
                $IS_BAN_LOGIN = true;
                // 清空用户所有 session
                Yii::$app->sso->clearSession($user->id);
            }
            if ($IS_BAN_LOGIN) {
                Mowangskuser::throwBanLoginException();
            } elseif (Equipment::isAppOlderThan('4.4.6', '5.3.6')) {
                // WORKAROUND: 不支持头像音多个音质的版本，默认使用 128k bit 音质，若无此音质音频则使用原音质音频
                $user->soundurl = $user->soundurl_128 ?: $user->soundurl_64;
                unset($user->soundurl_128, $user->soundurl_64);
            }
            $return = [
                'id' => $info->user_id,
                'token' => $info->token,
                'user' => $user,
                'expire_at' => $info->expire_at,
            ];
            return $return;
        }
        return $login_form->getErrors() ?: Yii::t('app/error', 'Login failed');
    }

    /**
     * @api {post} /member/auth-login 第三方账号登录
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/auth-login
     * @apiSampleRequest member/auth-login
     * @apiDescription Bilibili 登录传 auth_type 与 auth_code（后续 QQ/Weibo/Wechat 调整成同样的处理）
     *
     * @apiVersion 0.1.0
     * @apiName auth-login
     * @apiGroup member
     *
     * @apiParam {String} uuid 用户唯一标识（微信为 unionid, QQ 或微博为 openid, Apple 为 userID）
     * @apiParam {String} openid 微信登录需要传递时的 openid
     * @apiParam {String} access_token 微信、QQ 或微博、Apple 的 token
     * @apiParam {number=3,4,5,6,7} auth_type 3：QQ；4：Weibo；5：Wechat；6：Bilibili；7：Apple
     * @apiParam {String} auth_code 第三方授权 code（Bilibili）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 2994166,
     *         "token": "5bcd7957d602601d70c35850|57c645af822b8248|1540192599|5c60a9c04d0bc231",
     *         "expire_at": 15688484848,
     *         "user": {
     *           "id": 2994166,
     *           "username": "哦耶55555",
     *           "boardiconurl": "icon01.png",
     *           "iconurl": "http://static.missevan.com/avatars/201804/29/b02729e0c20906c67687182befb09fae204316.jpg",
     *           "userintro": null,
     *           "coverurl": "index/eyes1.jpg",
     *           "coverurl_new": "",
     *           "avatar": "201804/29/b02729e0c20906c67687182befb09fae204316.jpg",
     *           "icontype": 1,
     *           "albumnum": 1,
     *           "follownum": 3,
     *           "fansnum": 0,
     *           "point": 57,
     *           "userintro_audio": null,
     *           "confirm": 1,
     *           "soundnum": 0,
     *           "avatar2": "http://static.missevan.com/avatars/201804/29/b02729e0c20906c67687182befb09fae204316.jpg",
     *           "boardiconurl2": "http://static.missevan.com/profile/icon01.png",
     *           "coverurl2": "http://static.missevan.com/mimages/index/eyes1.jpg",
     *           "coverurl_new2": "",
     *           "duration": 9613,
     *           "authenticated": 0,
     *           "hotSound": 0,
     *           "cvid": 0,
     *           "soundurl": "sound/201409/21/b9aacb77c84c25be601a866b4c8ac893201829.mp3",
     *           "drama_bought_count": 0,
     *           "balance": 0,
     *           "vip_info": {
     *             "status": 1,  // 会员状态。0：未开通过正式会员；1：正式会员；2：体验会员（暂不下发）；3：正式会员已过期
     *             "end_time": 1734883999  // 会员过期时间戳（含最后一秒），单位：秒
     *           }
     *         },
     *         "sso": {
     *           "token": "5bcd7957d602601d70c35850|57c645af822b8248|1540192599|5c60a9c04d0bc231",
     *           "expire": 1571728599
     *         }
     *       }
     *     }
     */
    public function actionAuthLogin()
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }
        if (!Yii::$app->equip->getBuvid()) {
            // iOS 8 会兼容到不支持 buvid 的版本，这里提示联系客服
            throw new HttpException(400, '客户端异常，请联系客服');
        }
        $auth_type = Yii::$app->request->post('auth_type');

        $login_form = new LoginForm(['scenario' => LoginForm::SCENARIO_AUTH]);
        try {
            if ($login_form->load(Yii::$app->request->post(), '') && ($info = $login_form->login())) {
                $user = Mowangskuser::getPersonInfo($info->user_id);
                $IS_BAN_LOGIN = false;
                // 判断用户是否是永久禁止登录
                if ($user && $user->isBanLogin()) {
                    $IS_BAN_LOGIN = true;
                    // 清空用户所有 session
                    Yii::$app->sso->clearSession($user->id);
                }
                if ($IS_BAN_LOGIN) {
                    Mowangskuser::throwBanLoginException();
                } elseif (Equipment::isAppOlderThan('4.4.6', '5.3.6')) {
                    // WORKAROUND: 不支持头像音多个音质的版本，默认使用 128k bit 音质，若无此音质音频则使用原音质音频
                    $user->soundurl = $user->soundurl_128 ?: $user->soundurl_64;
                    unset($user->soundurl_128, $user->soundurl_64);
                }
                $return = [
                    'id' => $info->user_id,
                    'token' => $info->token,
                    'expire_at' => $info->expire_at,
                    'user' => $user,
                ];
                return $return;
            }
            return $login_form->getErrors() ?: '登录失败';
        } catch (\Exception $e) {
            if (LoginForm::BILIBILI === (int)$login_form->auth_type && false !== $login_form->third_name
                    // 没有第三方账户对应的登录用户 code
                    && $e->getCode() === 200010001) {
                // 若没有第三方账号对应的登录用户，则返回第三方账号的昵称与头像用于客户端注册时使用
                // 后续 QQ/Weibo/Wechat 调整成相同的处理
                return [
                    'nickname' => $login_form->third_name,
                    'iconurl' => $login_form->iconurl,
                ];
            }
            throw $e;
        }
    }

    /**
     * @api {post} /member/regist 注册一个新用户
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/regist
     * @apiSampleRequest member/regist
     * @apiDescription  /mobile/site/regist 注册
     * old_params: {String} loginName email或者手机,{String} username 用户昵称,{String} password 密码,{Number} identifyCode 验证码
     *
     * @apiVersion 0.1.0
     * @apiName regist
     * @apiGroup member
     *
     * @apiParam {String} account email或者手机
     * @apiParam {String} username 用户昵称
     * @apiParam {String} password 密码
     * @apiParam {String} code 验证码
     * @apiParam {String} [region='CN'] 地区
     * @apiParam {File} [iconurl] 自定义头像
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       success: true, code: 200, info: '注册成功'
     *     }
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 400 Not Found
     *     {
     *       success: false, code: 400, info: { username: [ '该用户名已经存在' ] }
     *     }
     *
     */
    public function actionRegist()
    {
        $login_form = new LoginForm(['scenario' => LoginForm::SCENARIO_REGISTER]);
        if ($login_form->load(Yii::$app->request->post(), '') && $login_form->regist()) {
            return Yii::t('app/base', 'Registration is successful');
        } else {
            return $login_form->getErrors() ?: Yii::t('app/error', 'Registration failed');
        }
    }

    /**
     * @api {post} /member/regist-bind 第三方账号注册绑定
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/regist-bind
     * @apiSampleRequest member/regist-bind
     * @apiDescription Bilibili 登录传 auth_type, auth_code（后续 QQ/Weibo/Wechat 调整成同样的处理）
     *
     * @apiVersion 0.1.0
     * @apiName regist-bind
     * @apiGroup member
     *
     * @apiParam {String} uuid 用户唯一标识（微信的 unionid, QQ 或者微博的 openid, Apple 为 userID）
     * @apiParam {String} access_token QQ 或者微信、微博、Apple 的 token
     * @apiParam {String} openid 微信登录需要传递时的 openid
     * @apiParam {number=3,4,5,6,7} auth_type 3：QQ；4：Weibo；5：Wechat；6：Bilibili；7：Apple
     * @apiParam {String} auth_code 第三方授权 code（Bilibili）
     * @apiParam {String} code 验证码
     * @apiParam {String} account 用户 email 或手机号
     * @apiParam {String} password 用户密码
     * @apiParam {String} [region='CN'] 地区
     * @apiParam {String} username 用户昵称
     * @apiParam {File} [iconurl] 自定义头像（若不传则使用第三方头像，若第三方头像违规则使用默认的猫图）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "注册成功"
     *     }
     */
    public function actionRegistBind()
    {
        $auth_type = Yii::$app->request->post('auth_type');
        $login_form = new LoginForm(['scenario' => LoginForm::SCENARIO_REGISTER_AUTH]);
        if ($login_form->load(Yii::$app->request->post(), '') && $login_form->regist()) {
            return '注册成功';
        } else {
            return $login_form->getErrors() ?: '注册失败';
        }
    }

    /**
     * @api {post} /member/third-bind 第三方账号绑定
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/third-bind
     * @apiSampleRequest member/third-bind
     * @apiDescription Bilibili 登录传 auth_type, auth_code（后续 QQ/Weibo/Wechat 调整成同样的处理）
     *
     * @apiVersion 0.1.0
     * @apiName third-bind
     * @apiGroup member
     *
     * @apiParam {String} uuid 用户唯一标识（微信的 unionid, QQ 或者微博的 openid, Apple 为 userID）
     * @apiParam {String} access_token QQ 或者微信、微博、Apple 的 token
     * @apiParam {String} openid 微信登录需要传递时的 openid
     * @apiParam {number=3,4,5,6,7} auth_type 3：QQ；4：Weibo；5：Wechat；6：Bilibili；7：Apple
     * @apiParam {String} auth_code 第三方授权 code（Bilibili）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "绑定成功"
     *     }
     */
    public function actionThirdBind()
    {
        $loginForm = new LoginForm(['scenario' => LoginForm::SCENARIO_BIND_AUTH]);
        if ($loginForm->load(Yii::$app->request->post(), '') && $loginForm->bind()) {
            return '绑定成功';
        } else {
            return $loginForm->getErrors() ?: '绑定失败';
        }
    }

    /**
     * @api {post} /member/third-bind2 第三方账号绑定（点击第三方登录，该第三方账号未绑定 M 号情况下调用）
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/third-bind2
     * @apiSampleRequest member/third-bind2
     * @apiDescription Bilibili 登录传 auth_type, auth_code（后续 QQ/Weibo/Wechat 调整成同样的处理）
     *
     * @apiVersion 0.1.0
     * @apiName third-bind2
     * @apiGroup member
     *
     * @apiParam {String} uuid 用户唯一标识（微信的 unionid, QQ 或者微博的 openid, Apple 为 userID）
     * @apiParam {String} access_token QQ 或者微信、微博、Apple 的 token
     * @apiParam {String} openid 微信登录需要传递时的 openid
     * @apiParam {number=3,4,5,6,7} auth_type 3：QQ；4：Weibo；5：Wechat；6：Bilibili；7：Apple
     * @apiParam {String} auth_code 第三方授权 code（Bilibili）
     * @apiParam {String} code 验证码
     * @apiParam {String} account 用户 email 或手机号
     * @apiParam {String} [region='CN'] 地区
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "绑定成功"
     *     }
     */
    public function actionThirdBind2()
    {
        $loginForm = new LoginForm(['scenario' => LoginForm::SCENARIO_BIND_AUTH2]);
        if ($loginForm->load(Yii::$app->request->post(), '') && $loginForm->registBind()) {
            return '绑定成功';
        } else {
            return $loginForm->getErrors() ?: '绑定失败';
        }
    }

    /**
     * @api {post} /member/cancel-bind 第三方账号解绑
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/cancel-bind
     * @apiSampleRequest member/cancel-bind
     *
     * @apiVersion 0.1.0
     * @apiName cancel-bind
     * @apiGroup member
     *
     * @apiParam {number=3,4,5,6,7} auth_type 3：QQ；4：Weibo；5：Wechat；6：Bilibili；7：Apple
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "解绑成功"
     *     }
     */
    public function actionCancelBind()
    {
        $loginForm = new LoginForm(['scenario' => LoginForm::SCENARIO_CANCEL_AUTH]);
        if ($loginForm->load(Yii::$app->request->post(), '') && $loginForm->cancelBind()) {
            return '解绑成功';
        } else {
            return $loginForm->getErrors() ?: '解绑失败';
        }
    }

    /**
     * @api {post} /member/logout 登出
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/logout
     * @apiSampleRequest member/logout
     *
     * @apiVersion 0.1.0
     * @apiName logout
     * @apiGroup member
     *
     * @apiParam {String} token 用户token.
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       success: true, code: 200, info: '登出成功'
     *     }
     *
     */

    public function actionLogout()
    {
        Yii::$app->user->logout();
        return Yii::t('app/error', 'Sign out successfully');
    }

    /**
     * @api {get} /member/info 用户基本信息
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/info
     * @apiSampleRequest member/info
     *
     * @apiVersion 0.1.0
     * @apiName info
     * @apiGroup member
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "email": "4***@q***.com",
     *         "mobile": "139***51",
     *         "weibo": null,
     *         "qq": "aaa",
     *         "wechat": null,
     *         "apple": null,
     *         "birthday": "保密",
     *         "sex": "保密",
     *         "region": 86
     *       }
     *     }
     */
    public function actionInfo()
    {
        $user_id = Yii::$app->user->id;
        $accounts = Mowangskuser::getAccounts($user_id);
        $user_add = UserAddendum::getByPk($user_id);
        // 未绑定时返回 null，此时客户端显示“未绑定”
        $not_bind = null;
        return [
            'email' => $accounts['email'] ?: $not_bind,
            'mobile' => $accounts['mobile'] ?: $not_bind,
            'weibo' => $this->getThirdNickname($accounts['weibouid'], $user_add->weibo),
            'qq' => $this->getThirdNickname($accounts['qquid'], $user_add->qq),
            'wechat' => $this->getThirdNickname($accounts['wechatuid'], $user_add->wechat),
            'bilibili' => $this->getThirdNickname($accounts['bilibiliuid'], $user_add->bilibili),
            'apple' => $this->getThirdNickname($accounts['appleuid'], $user_add->apple),
            'birthday' => $user_add->birthday ?: '保密',
            'sex' => $user_add->getSex(),
            'region' => $accounts['region'] ?: $not_bind,
        ];
    }

    /**
     * 获取第三方绑定账号用户昵称
     *
     * @param string|null $third_uid 第三方绑定账号 UID
     * @param string|null $nickname 第三方绑定账号昵称
     * @return string|null 第三方绑定账号用户昵称，未绑定时返回 null
     */
    private function getThirdNickname(?string $third_uid, ?string $nickname): ?string
    {
        if (!$third_uid) {
            // 未绑定第三方账号时返回 null，此时客户端显示“未绑定”
            return null;
        }
        // 部分绑定的第三方账号昵称为空，此时返回“已绑定”作为客户端显示内容
        return $nickname ?: '已绑定';
    }

    /**
     * @api {post} /member/set-sex 设置性别
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/set-sex
     * @apiSampleRequest member/set-sex
     *
     * @apiVersion 0.1.0
     * @apiName set-sex
     * @apiGroup member
     *
     * @apiParam {String} sex 性别
     * @apiParam {String} token 用户token.
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       success: true, code: 200, info: '修改成功'
     *     }
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 400 Not Found
     *     {
     *       success: false, code: 400, info: '修改失败'
     *     }
     *
     */

    public function actionSetSex()
    {
        $user_id = Yii::$app->user->id;
        $sex = Yii::$app->request->post('sex');
        if (UserAddendum::setSex($user_id, $sex)) {
            return '修改成功';
        } else {
            return '修改失败';
        }
    }

    /**
     * @api {post} /member/set-birthday 设置生日
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/set-birthday
     * @apiSampleRequest member/set-birthday
     *
     * @apiVersion 0.1.0
     * @apiName set-birthday
     * @apiGroup member
     *
     * @apiParam {String} birthday 生日，格式：YYYY-mm-dd
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "修改成功"
     *     }
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "修改失败"
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     {
     *       "success": false,
     *       "code": *********,
     *       "info": "参数错误"
     *     }
     */
    public function actionSetBirthday()
    {
        $user_id = Yii::$app->user->id;
        $birthday = Yii::$app->request->post('birthday');

        $format = 'Y-m-d';
        // WORKAROUND: Android < 6.1.2 版本时，允许生日参数格式不是 YYYY-mm-dd
        if (Equipment::isAppOlderThan(null, '6.1.2')) {
            $format = 'Y-n-j';
        }
        if (!MUtils2::isValidDateTime($birthday, $format)) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }

        if (UserAddendum::setBirthday($user_id, $birthday)) {
            return '修改成功';
        } else {
            return '修改失败';
        }
    }

    /**
     * @api {post} /member/update-info 更新用户基本信息
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/update-info
     * @apiSampleRequest member/update-info
     * @apiDescription  /mobile/personOperation/updateUserInfo
     * @apiVersion 0.1.0
     * @apiName update-info
     * @apiGroup member
     *
     * @apiParam {String} username 用户名
     * @apiParam {String} userintro 介绍
     * @apiParam {Number} userintro_audio 头像音 ID
     * @apiParam {Number} cover_id 版头图 ID
     * @apiParam {File} avatar_file 头像
     * @apiParam {File} MImage 用户上传的封面
     * @apiParam {string=default_img1,default_img2,default_img3} default_img 默认封面图
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 346286, // 用户 ID
     *         "username": "InVinCiblezzz",  // 用户名称
     *         "userintro": "465 465abc",  // 用户简介
     *         "iconurl": "http://static.missevan.com/avatars/201812/10/91e6b6ca1344ac90973e61fbcd87df78184702.jpg",
     *         "coverurl_new2": "https://static.missevan.com/usercover/background3.png",  // WORKAROUND: 用户封面图（iOS 4.6.9 和 安卓 5.5.8 以下版本使用此字段）
     *         "coverurl": "https://static.missevan.com/usercover/background3.png",  // 用户封面图
     *         "dark_coverurl": "https://static.missevan.com/usercover/background403-dark.png",  // 黑夜模式用户封面图，不返回该字段说明无黑夜模式
     *         "soundurl": "http://static.missevan.com/32BIT/201505/05/ca57758e803880dfe79fb25982f98348161835.mp3",
     *         "icontype": 1,
     *         "albumnum": 0,
     *         "follownum": 0,
     *         "fansnum": 0,
     *         "soundnum": 75,
     *         "point": 12912,
     *         "userintro_audio": 34197,
     *         "confirm": 1,
     *         "duration": 8725,
     *         "authenticated": 1,
     *         "hotSound": 1,
     *         "followed": 1,
     *         "blacklist": 0
     *       }
     *     }
     */
    public function actionUpdateInfo()
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }

        $user_id = Yii::$app->user->id;
        $redis = Yii::$app->redis;
        $lock_key = $redis->generateKey(LOCK_UPDATE_USER_COVER, $user_id);
        if (!$redis->lock($lock_key, ONE_MINUTE)) {
            throw new HttpException(400, '操作过于频繁，请稍候再试');
        }
        $connection = Yii::$app->db;
        $transaction = null;
        try {
            $user = Mowangskuser::find()
                ->select('id, username, userintro, userintro_audio, point, avatar, coverid, coverurl, coverurl_new, boardiconurl')
                ->where(['id' => $user_id])
                ->one();
            $user->setScenario(Mowangskuser::SCENARIO_INFO);
            if ($post = Yii::$app->request->post()) {
                if (isset($post['userintro'])) {
                    // 若存在个性签名参数，则清除昵称参数，避免同时提交绕过违规检测
                    unset($post['username']);
                    $user->setScenario(Mowangskuser::SCENARIO_USERINTRO);
                    // 个性签名过滤掉 HTML 标签，换行符替换为一个空格
                    $post['userintro'] = htmlspecialchars_decode(str_replace(["\r\n", "\r", "\n"], ' ',
                        MUtils::formatHtml($post['userintro'], ['HTML.Allowed' => ''])));
                }
                if (isset($post['username'])) {
                    $user->setScenario(Mowangskuser::SCENARIO_USERNAME);
                    $post['username'] = str_replace(["\r", "\n"], '', trim($post['username']));
                }
                $user->load($post, '');
            }
            $avatar_file = UploadedFile::getInstanceByName('avatar_file');
            if ($avatar_file && $avatar_file->tempName) {
                $user->avatar_file = $avatar_file->tempName;
            }
            if ($_FILES && isset($_FILES['MImage']) && $_FILES['MImage']) {  // 用户上传封面
                $file = $_FILES['MImage'];
                $path = MImage::uploadCover($file);
                // 转为协议地址
                $coverurl_new = Yii::$app->storage->protocolUrl . '/' . OSS_DIR_USER_COVER . '/' . $path;
                $transaction = $connection->beginTransaction();
                MUserCover::changeUserCover($user_id, $coverurl_new);
                $user->coverurl_new2 = StorageClient::getFileUrl($coverurl_new);
                $cover_position = MUserCover::POSITION_DEFAULT_USER_OTHER;
            } elseif (isset($post['default_img'])) {
                // 默认图不需要审核直接更新 coverurl_new 冗余字段
                switch ($post['default_img']) {
                    case 'default_img1':
                        $coverurl_new = MUserCover::DEFAULT_USER_COVER_URL1;
                        $user->coverurl_new = $coverurl_new;
                        $cover_position = MUserCover::POSITION_DEFAULT_USER_COVER1;
                        break;
                    case 'default_img2':
                        $coverurl_new = MUserCover::DEFAULT_USER_COVER_URL2;
                        $user->coverurl_new = $coverurl_new;
                        $cover_position = MUserCover::POSITION_DEFAULT_USER_COVER2;
                        break;
                    case 'default_img3':
                        $coverurl_new = MUserCover::DEFAULT_USER_COVER_URL3;
                        $user->coverurl_new = $coverurl_new;
                        $cover_position = MUserCover::POSITION_DEFAULT_USER_COVER3;
                        $user->dark_coverurl = StorageClient::getFileUrl(MUserCover::DEFAULT_USER_COVER_URL3_DARK);
                        break;
                    default:
                        throw new HttpException(400, '参数错误');
                }
                $user->coverurl_new2 = StorageClient::getFileUrl($coverurl_new);
                // 失效原有的未审核的封面图片
                $transaction = $connection->beginTransaction();
                MUserCover::updateAll(['checked' => MUserCover::CHECKED_INVALID],
                    'user_id = :user_id AND checked = :checked',
                    [':user_id' => $user_id, ':checked' => MUserCover::CHECKED_NO]);
            }
            if (!$transaction) {
                $transaction = $connection->beginTransaction();
            }
            if (!$user->save()) {
                $errs = $user->getErrors();
                if (Equipment::isAppOlderThan('6.0.1', '6.0.4')
                        || (Equipment::isAppVersion(Equipment::iOS, '6.0.1') && isset($post['userintro_audio']))) {
                    // WORKAROUND: Android 客户端 < 6.0.4 或 iOS 客户端 < 6.0.1，返回结构化的错误
                    // iOS 6.0.1 版本并且为修改头像音时的错误，返回结构化的错误
                    $transaction->rollBack();
                    return $errs;
                }
                throw new HttpException(400, MUtils2::getFirstError($user));
            }
            if ($user->getScenario() === Mowangskuser::SCENARIO_USERNAME) {
                // 相关表的用户昵称同步修改
                Mowangskuser::updateRelationUsername($user_id, $user->username);
            }
            $transaction->commit();
            Mowangskuser::setExtraInfo($user);
            // 返回数据
            if (!isset($cover_position)) {
                $user_cover_url = MUserCover::getUserCoverByUserId($user_id, true, $user->coverurl_new);
                $user->coverurl_new2 = $user_cover_url;
                $res = MUserCover::getDarkCoverAndPositionByCoverUrl($user_cover_url);
                if ($res['dark_cover_url']) {
                    $user->dark_coverurl = $res['dark_cover_url'];
                }
                $user->coverid = $res['cover_id'];
            } else {
                $user->coverid = $cover_position;
            }
            $user->coverurl = $user->coverurl_new2;

            // iOS 4.6.9 和 安卓 5.5.8 以上版本封面图统一使用 coverurl，不下发 coverurl_new2
            if (!Equipment::isAppOlderThan('4.6.9', '5.5.8')) {
                unset($user->coverurl_new2);
            }
            if (isset($user->avatar_file)) {
                unset($user->avatar_file);
            }
            if (!Equipment::isAppOlderThan(null, '5.6.2')) {
                // WORKAROUND: Android >= 5.6.2 删除 avatar 字段，改用 iconurl
                unset($user->avatar);
            }
            unset($user->avatar2, $user->boardiconurl, $user->boardiconurl2);
            return $user;
        } catch (\Exception $e) {
            if ($transaction) {
                $transaction->rollBack();
            }
            throw $e;
        } finally {
            $redis->unlock($lock_key);
        }
    }

    /**
     * @api {post} /member/forget-password 忘记密码
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/forget-password
     * @apiSampleRequest /member/forget-password
     * @apiDescription  /mobile/site/mobileChangePassword
     *
     * @apiVersion 0.1.0
     * @apiName forget-password
     * @apiGroup member
     *
     * @apiParam {String} code 验证码
     * @apiParam {string} account 账号
     * @apiParam {string} password 密码
     * @apiParam {string} [region='CN'] 地区
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       success: true, code: 200, info: '修改成功'
     *     }
     *
     */
    public function actionForgetPassword()
    {
        $account = Yii::$app->request->post('account');
        $password = Yii::$app->request->post('password');
        $code = (int)Yii::$app->request->post('code');
        $region = Yii::$app->request->post('region', 'CN');

        $account_type = LoginForm::getAccountType($account, $region);
        switch ($account_type) {
            case LoginForm::EMAIL:
                $user = Yii::$app->sso->getUserInfoByEmail($account);
                if (!$user) {
                    throw new HttpException(404, Yii::t('app/error', 'Account does not exist'));
                }
                $objective_account = $account;
                $data = ['email' => $user['email']];
                break;
            case LoginForm::MOBILE:
                if (!Country::get($region)) {
                    throw new HttpException(400,
                        Yii::t('app/error', 'Mobile numbers in this country are not supported'));
                }
                $mobile_info = MUtils2::getMobileNumber($account, $region);
                if (!$mobile_info) {
                    throw new HttpException(404, Yii::t('app/error', 'Account does not exist'));
                }
                $account = $mobile_info->mobile_num;
                $region_num = $mobile_info->region_num;
                $objective_account = $region_num . $account;
                $user = Yii::$app->sso->getUserInfoByMobile($account, $region_num);
                if (!$user) {
                    throw new HttpException(404, Yii::t('app/error', 'Account does not exist'));
                }
                $data = ['mobile' => (int)$account, 'region' => (int)$region_num];
                break;
            default:
                throw new HttpException(400, Yii::t('app/error', 'Account type error'));
        }

        // 验证验证码是否用于忘记密码的操作
        $post_objective_key = Yii::$app->redis->generateKey(POST_OBJECTIVE_TYPE, $objective_account);
        $post_objective_type = (int)Yii::$app->redis->get($post_objective_key);
        if (Equipment::isAppOlderThan('4.2.4', '5.1.2') &&
                VcodeForm::TYPE_CHANGE_PASSWORD !== $post_objective_type) {
            // WORKAROUND: 安卓 5.1.2 和 iOS 4.2.4 之前的版本是修改密码的参数
            throw new HttpException(403, Yii::t('app/error', 'Verification code error'));
        } elseif (!Equipment::isAppOlderThan('4.2.4', '5.1.2') &&
                VcodeForm::TYPE_FORGET_PASSWORD !== $post_objective_type) {
            throw new HttpException(403, Yii::t('app/error', 'Verification code error'));
        }
        if (!VcodeForm::checkIdentifyCode($account, $code)) {
            throw new HttpException(403, Yii::t('app/error', 'Verification code error'));
        }
        // 忘记密码
        $data_map = [
            'type' => SSOClient::TYPE_FORGET_PASSWORD,
            'new_password' => $password,
        ];
        $data = array_merge($data_map, $data);
        $result = Yii::$app->sso->updatePassword($data);
        if (SSOClient::CODE_SUCCESS !== $result['code']) {
            $info = Yii::$app->sso->codeToMessage($result['code']);
            throw new HttpException($result['status'], $info, $result['code']);
        }
        // 操作成功后，删除验证码和输入错误密码限制计数器
        VcodeForm::delIdentifyCode($account);
        VcodeForm::delCounterLogin($account);
        return Yii::t('app/base', 'Change successfully');
    }

    /**
     * @api {post} /member/change-password 修改密码
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/change-password
     * @apiSampleRequest /member/change-password
     * @apiDescription  /mobile/site/mobileChangePassword
     *
     * @apiVersion 0.1.0
     * @apiName change-password
     * @apiGroup member
     *
     * @apiParam {String} account 用户账号
     * @apiParam {String} password 旧密码
     * @apiParam {String} new_password 密码
     * @apiParam {number=1, 2} account_type 账号类型 1邮箱2手机 此为验证的账号类型
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "修改成功"
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 400
     *     {
     *       "success": false,
     *       "code": *********,
     *       "info": "密码长度不得超过 16 位"
     *     }
     */
    public function actionChangePassword()
    {
        $login_form = new LoginForm(['scenario' => LoginForm::SCENARIO_CONFIRM]);
        $login_form2 = new LoginForm(['scenario' => LoginForm::SCENARIO_CONFIRM_PASSWORD]);

        // 传了 password 并且为空字符串抛出异常
        if (Yii::$app->request->post('password') === '') {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        if (!$login_form->load(Yii::$app->request->post(), '') || !$login_form->confirmOldAccount()) {
            $error_msg = MUtils::getFirstError($login_form);
            if ($error_msg) {
                throw new HttpException(400, $error_msg);
            }
            throw new HttpException(400, Yii::t('app/error', 'Change failed'));
        }

        if (!$login_form2->load(Yii::$app->request->post(), '') || !$login_form2->changePassword()) {
            $error_msg = MUtils::getFirstError($login_form2);
            if ($error_msg) {
                throw new HttpException(400, $error_msg);
            }
            throw new HttpException(400, Yii::t('app/error', 'Change failed'));
        }
        // 删除登录次数计数器
        VcodeForm::delCounterLogin($login_form2->account);
        // TODO: 需即时清除其他设备在服务器的登录缓存
        return Yii::t('app/base', 'Change successfully');
    }

    /**
     * @api {post} /member/confirm-account 验证旧手机/邮箱
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/confirm-account
     * @apiSampleRequest /member/confirm-account
     *
     * @apiVersion 0.1.0
     * @apiName confirm-account
     * @apiGroup member
     *
     * @apiParam {String} code 验证码
     * @apiParam {number=1, 2} account_type 账号类型 1邮箱2手机
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     { success: true, code: 200, info: 2 }
     *
     *
     */
    public function actionConfirmAccount()
    {
        $code = (int)Yii::$app->request->post('code');
        $account_type = (int)Yii::$app->request->post('account_type');
        $user_id = Yii::$app->user->id;

        switch ($account_type) {
            case LoginForm::EMAIL:
                $field = 'email';
                break;
            case LoginForm::MOBILE:
                $field = 'mobile';
                break;
            default:
                throw new HttpException(400, Yii::t('app/error', 'Account type error'));
        }
        $user = Yii::$app->sso->getUserAccount($user_id);
        if (!$user[$field]) {
            throw new HttpException(400, Yii::t('app/error', 'Account error'));
        }

        if (!VcodeForm::checkIdentifyCode($user[$field], $code)) {
            throw new HttpException(403, Yii::t('app/error', 'Verification code error'));
        }

        $redis = Yii::$app->redis;
        $key = $redis->generateKey(CHANGE_ACCOUNT, $user[$field]);
        $redis->setex($key, TEN_MINUTE, 1);

        return $account_type;
    }

    /**
     * @api {post} /member/check-account 验证手机/邮箱的验证码
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/check-account
     * @apiSampleRequest /mobile/site/checkIdentitycode
     *
     * @apiVersion 0.1.0
     * @apiName check-account
     * @apiGroup member
     *
     * @apiParam {String} code 验证码
     * @apiParam {String} account 账号
     * @apiParam {String} [region='CN'] 地区
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     { success: true, code: 200, info: 2 }
     *
     *
     */
    public function actionCheckAccount()
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }

        $code = (int)Yii::$app->request->post('code');
        $account = Yii::$app->request->post('account');
        $region = Yii::$app->request->post('region', 'CN');

        // 判断是否是手机账号
        if ((!MUtils2::isEmail($account))) {
            $mobile_info = MUtils2::getMobileNumber($account, $region);
            if (!$mobile_info) {
                throw new HttpException(404, Yii::t('app/error', 'Please input correct phone number'));
            }
            $account = $mobile_info->mobile_num;
        }

        if (!VcodeForm::checkIdentifyCode($account, $code)) {
            throw new HttpException(403, Yii::t('app/error', 'Verification code error'));
        }

        $redis = Yii::$app->redis;
        $key = $redis->generateKey(CHANGE_ACCOUNT, $account);
        $redis->setex($key, TEN_MINUTE, 1);

        return Yii::t('app/base', 'Verified');

    }

    /**
     * @api {post} /member/bind-account 绑定手机/邮箱
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/member/bind-account
     * @apiSampleRequest /member/bind-account
     *
     * @apiVersion 0.1.0
     * @apiName bind-account
     * @apiGroup member
     *
     * @apiParam {String} account 绑定的手机/邮箱
     * @apiParam {String} [region='CN'] 地区
     * @apiParam {Number} account_type 账号校验时的方式 1：邮箱；2：手机
     * @apiParam {String} code 验证码
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "修改成功"
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     HTTP/1.1 400
     *     {
     *       "success": false,
     *       "code": *********,
     *       "info": "不支持该类型邮箱"
     *     }
     */
    public function actionBindAccount()
    {
        $region = Yii::$app->request->post('region', 'CN');
        $code = (int)Yii::$app->request->post('code');
        $user_id = Yii::$app->user->id;

        $login_form = new LoginForm(['scenario' => LoginForm::SCENARIO_BIND_ACCOUNT]);
        if ($login_form->load(Yii::$app->request->post(), '') && $login_form->confirmOldAccount()) {
            $user = Mowangskuser::findOne($user_id);
            if (!$user) {
                throw new HttpException(404, '用户不存在', *********);
            }
            // 使用 getAccountType 方法拿到 account_type，而不是通过接口参数直接获取
            $account_type = LoginForm::getAccountType($login_form->account, $region);
            // account_type 账号类型 1：邮箱；2：手机；此处为校验时的方式
            switch ($account_type) {
                case LoginForm::EMAIL:
                    $objective_type = VcodeForm::TYPE_BIND_EMAIL;
                    $objective_account = $login_form->account;
                    $update_fields = ['email' => $login_form->account];
                    break;
                case LoginForm::MOBILE:
                    $region_item = Country::get($region);
                    if (!$region_item) {
                        throw new HttpException(400,
                            Yii::t('app/error', 'Mobile numbers in this country are not supported'));
                    }
                    $region_num = $region_item[0];
                    $objective_type = VcodeForm::TYPE_BIND_MOBILE;
                    $objective_account = $region_num . $login_form->account;
                    $update_fields = ['mobile' => (int)$login_form->account, 'region' => $region_num];
                    break;
                default:
                    throw new HttpException(404, Yii::t('app/error', 'Account error'));
            }
            if ($objective_type && !VcodeForm::checkObjective($objective_type, $objective_account)) {
                throw new HttpException(403, Yii::t('app/error', 'Verification code error'));
            }
            if (!VcodeForm::checkIdentifyCode($login_form->account, $code)) {
                throw new HttpException(403, Yii::t('app/error', 'Verification code error'));
            }
            // 更新 SSO 数据
            Yii::$app->sso->update($user->id, $update_fields, Yii::$app->user->identity->token);
            // 操作成功后，删除验证码
            VcodeForm::delObjectiveKey($objective_account);
            VcodeForm::delIdentifyCode($login_form->account);
            return Yii::t('app/base', 'Change successfully');
        } else {
            $error_msg = MUtils::getFirstError($login_form);
            if ($error_msg) {
                throw new HttpException(400, $error_msg);
            }
            throw new HttpException(400, Yii::t('app/error', 'Change failed'));
        }
    }

    /**
     * @api {post} /member/send-vcode 发送验证码
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/send-vcode
     * @apiSampleRequest /member/send-vcode
     *
     * @apiVersion 0.3.0
     * @apiName send-vcode
     * @apiGroup member
     *
     * @apiParam {number=0,1,2,3,4,5,6,7,8,9,10,11} post_type 发送验证码用于什么操作 \
     * 0：注册；\
     * 1：修改密码；\
     * 2：修改手机号（新）；\
     * 3：验证旧手机号；\
     * 4：修改邮箱（新）；\
     * 5：验证旧邮箱；\
     * 6：异地登录验证；\
     * 7：支付；\
     * 8：第三方绑定；\
     * 9：忘记密码；\
     * 10：验证码登录；\
     * 11：绑定手机号（第三方账号）；
     * @apiParam {string} [account] 账号
     * post_type 为 0，2，4，8，9，10，11 时，传用户输入的 account；post_type 为 1，3，5 时，要传打码的 account
     * @apiParam {number=1,3,5} [confirm_type] 验证方式 1：修改密码；3：验证旧手机号；5：验证旧邮箱；
     * post_type 为 1，3，5 时传入，目前 post_type 为其他值的情况下不需要传 confirm_type 参数 \
     * 3：手机验证方式；\
     * 5：邮箱验证方式。
     * @apiParam {string} [region='CN'] 地区 post_type 为 0，2，8，9（使用手机号找回密码）, 10, 11 时传入
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "发送成功"
     *     }
     */
    public function actionSendVcode()
    {
        if (Equipment::isBanVersion()) {
            // WORKAROUND: 老版本禁止注册账号
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }

        $post_type = (int)Yii::$app->request->post('post_type');

        // 有些场景不需要跳出人机交互
        if (ENABLE_NOCAPTCHA && !defined('YII_CAPTCHA_TEST') && in_array($post_type, [VcodeForm::TYPE_REGISTER,
            VcodeForm::TYPE_BIND_THIRD, VcodeForm::TYPE_FORGET_PASSWORD])) {
            MUtils::checkCaptcha(Captcha::SCENEORIGINAL_MESSAGE);
        }
        $account = trim(Yii::$app->request->post('account'));
        $confirm_type = (int)Yii::$app->request->post('confirm_type');
        if (VcodeForm::TYPE_FORGET_PASSWORD === $post_type && $confirm_type) {
            // 忘记密码时获取 confirm_type 提示参数错误
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }

        // WORKARROUND : account 是手机号而 post_type 为 4 时 post_type 换成绑定新手机号的参数 2
        $account_type = LoginForm::getAccountType($account, 'CN');
        if (4 === $post_type && LoginForm::MOBILE === $account_type) {
            $post_type = 2;
        }
        $vcode_form = new VcodeForm();
        $mosaic_account = false;

        if ($confirm_type) {
            // 传入 confirm_type 时，account 为脱敏的手机或邮箱
            $mosaic_account = true;
        }
        if ($account && !$mosaic_account) {
            if ($vcode_form->load(Yii::$app->request->post(), '')) {
                // WORKARROUND : 因为是 load 所以赋值在 form 里的 post_type 需要重新赋正确的值
                $vcode_form->post_type = $post_type;
                if ($vcode_form->validate()) {
                    $vcode_form->sendVcode();
                    return Yii::t('app/base', 'Send successfully');
                } else {
                    $errors = $vcode_form->getErrors();
                    foreach ($errors as $error) {
                        throw new HttpException(400, $error[0]);
                    }
                }
            }
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        } else {
            $user_id = Yii::$app->user->id;
            if (!$user_id) throw new HttpException(403, Yii::t('app/error', 'Please log in first'));
            $user_account = Yii::$app->sso->getUserAccount($user_id);

            $vcode_form->post_type = $post_type;
            switch ($confirm_type) {
                case VcodeForm::TYPE_CONFIRM_MOBILE:
                    $mobile = $user_account['mobile'] ?: false;
                    $region_num = $user_account['region'] ?: false;
                    if (!$mobile || !$region_num) {
                        throw new HttpException(404,
                            Yii::t('app/error', 'You have no available phone number'));
                    }

                    // 判断支持发信
                    $country = Country::items();
                    $area_code = [];
                    foreach ($country as $key => $item) {
                        $area_code[$key] = $item[0];
                    }
                    if (!array_search($region_num, $area_code)) {
                        throw new HttpException(400,
                            Yii::t('app/error', 'Mobile numbers in this country are not supported'));
                    }

                    $vcode_form->account = $mobile;
                    $vcode_form->region = $region_num;
                    // 用户在未绑定邮箱的情况下进行换绑操作，只能选择已绑定的手机号进行账号验证
                    // 此时的验证码场景需为绑定邮箱
                    if (VcodeForm::TYPE_CONFIRM_EMAIL === $vcode_form->post_type && is_null($user_account['email'])) {
                        $vcode_form->scene = VcodeForm::SCENE_BIND_EMAIL;
                    }
                    if (!$vcode_form->validate() || !$vcode_form->sendSms()) {
                        $errors = $vcode_form->getErrors();
                        foreach ($errors as $error) {
                            throw new HttpException(400, $error[0]);
                        }
                    }
                    return Yii::t('app/base', 'Send successfully');
                case VcodeForm::TYPE_CONFIRM_EMAIL:
                    $email = $user_account['email'] ?: false;
                    if (!$email) {
                        throw new HttpException(404,
                            Yii::t('app/error', 'You have no available email'));
                    }
                    $vcode_form->account = $email;
                    if (!$vcode_form->validate() || !$vcode_form->sendEmail()) {
                        $errors = $vcode_form->getErrors();
                        foreach ($errors as $error) {
                            throw new HttpException(400, $error[0]);
                        }
                    }
                    return Yii::t('app/base', 'Send successfully');
                default:
                    throw new HttpException(400, Yii::t('app/error', 'params error'));
            }
        }
    }

    /**
     * @api {post} /member/zhima 进行芝麻实名认证
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/zhima
     * @apiSampleRequest /member/zhima
     *
     * @apiVersion 0.3.0
     * @apiName zhima
     * @apiGroup member
     *
     * @apiParam {String} name 真实姓名
     * @apiParam {String} id_no 身份证号
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "biz_no": "ZM201906053000000252500047756056",
     *         "url": "https://openapi.alipay.com/gateway.do?alipay_sdk=alipay-sdk-php-20161101&app_id=20177490.."
     *       }
     *     }
     */
    public function actionZhima()
    {
        $real_name = trim(Yii::$app->request->post('name'));
        $id_no = trim(Yii::$app->request->post('id_no'));
        if (!$real_name || !$id_no) {
            throw new HttpException(400, '姓名或身份证不能为空');
        }
        // 最后一位为 X 的身份证号，保证 X 为大写
        $id_no = strtoupper($id_no);
        // 校验身份证号
        Certification::checkIdcard($id_no);
        $gender = Certification::getGenderByIdcard($id_no);

        $user_id = Yii::$app->user->id;
        $cert = Certification::find()
            ->where(['checked' => Certification::CHECKED_SUCCEED])
            ->andWhere('user_id = :user_id OR id_number = :id_number OR id_number = :id_number_enc',
                [':user_id' => $user_id, ':id_number' => $id_no,
                    ':id_number_enc' => MUtils::encrypt($id_no, SENSITIVE_FIXED_IV_KEY)])->one();
        if ($cert) {
            if ($cert->user_id === $user_id) {
                // WORKAROUND: 服务端这边数据同步存在问题之后需要改回成该用户已经通过验证
                throw new HttpException(403, '您已完成实名认证，请重新登录');
            } else {
                throw new HttpException(403, '该身份证号已绑定于其它用户');
            }
        }
        $username = Yii::$app->user->name;
        if (!YII_ENV_PROD && in_array($id_no, Yii::$app->params['special_id_number'])) {
            $biz_no = 'debug_test';
            Certification::certifyCreate($user_id, $username, $id_no, $real_name, $biz_no, $gender);
            return [
                'biz_no' => $biz_no,
                // 输入真实姓名和身份证后点击“下一步”，url 返回空字符串，此时会跳到支付宝的一个空白页，再返回到 App 可以实名认证成功
                'url' => ''
            ];
        }

        $Ali = new AuthAli();
        $biz_no = $Ali->alipayUserCertifyInit($real_name, $id_no);
        $url = $Ali->alipayUserCertifyStart($biz_no);

        // 创建实名认证记录
        Certification::certifyCreate($user_id, $username, $id_no, $real_name, $biz_no, $gender);

        return [
            'biz_no' => $biz_no,
            'url' => $url
        ];
    }

    /**
     * @api {post} /member/zhima-confirm 芝麻实名认证确认
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/zhima-confirm
     * @apiSampleRequest /member/zhima-confirm
     *
     * @apiVersion 0.3.0
     * @apiName zhima-confirm
     * @apiGroup member
     *
     * @apiParam {String} biz_no 芝麻认证唯一码
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "认证通过"
     *     }
     */
    public function actionZhimaConfirm()
    {
        $biz_no = Yii::$app->request->post('biz_no');
        $certification = Certification::findOne(['id_people' => $biz_no, 'user_id' => Yii::$app->user->id]);
        if (!$certification) {
            throw new HttpException(400, '参数不合法');
        }
        if (!YII_ENV_PROD && in_array($certification->getDecryptIdNumber(), Yii::$app->params['special_id_number'])) {
            if (!$certification->certifyPass()) {
                throw new HttpException(417, '认证失败');
            }
            return '认证通过';
        }
        $ali = new AuthAli();
        if (!$ali->alipayUserCertifyPass($biz_no)) {
            if ($certification->checked !== $certification::CHECKED_FAILED) {
                $certification->checked = $certification::CHECKED_FAILED;
                $certification->save();
            }
            throw new HttpException(417, '认证失败');
        }
        if (!$certification->certifyPass()) {
            throw new HttpException(417, '认证失败');
        }
        return '认证通过';
    }

    /**
     * @api {post} /member/check-username 验证用户名是否不可用
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/check-username
     * @apiSampleRequest /member/check-username
     *
     * @apiVersion 0.3.0
     * @apiName check-username
     * @apiGroup member
     *
     * @apiParam {string} username 用户名
     * @apiDescription 可用返回 false，不可用返回具体原因
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": false
     *     }
     */
    public function actionCheckUsername()
    {
        $user_name = trim(Yii::$app->request->post('username'));

        if (!$user_name) {
            $result = Yii::t('app/error', 'Missing parameters');
        } else {
            $result = Mowangskuser::checkUsername($user_name, true);
        }
        if (!$result && Mowangskuser::find()->where(['username' => $user_name])->exists()) {
            $result = Yii::t('app/error', 'Nickname has already been taken');
        }

        // WORKAROUND: 针对 iOS < 4.2.6、安卓 < 5.1.5，返回布尔值
        if (Equipment::isAppOlderThan('4.2.6', '5.1.5')) {
            return (bool)$result;
        }

        return $result;
    }

    /**
     * @api {post} /member/check-mobile 第三方绑定登录和注册时验证手机
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/member/check-mobile
     * @apiSampleRequest /member/check-mobile
     *
     * @apiVersion 0.3.0
     * @apiName check-mobile
     * @apiGroup member
     *
     * @apiParam {string} mobile 手机号
     * @apiParam {String} [region='CN'] 地区
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "exist": false,
     *         "have_send": true
     *       }
     *     }
     */
    public function actionCheckMobile()
    {
        $mobile = trim(Yii::$app->request->post('mobile'));
        $region = trim(Yii::$app->request->post('region', 'CN'));

        if (ENABLE_NOCAPTCHA && !defined('YII_CAPTCHA_TEST')) {
            MUtils::checkCaptcha(Captcha::SCENEORIGINAL_MESSAGE);
        }

        if (!$mobile_info = MUtils2::getMobileNumber($mobile, $region)) {
            throw new HttpException(400, '请输入正确的手机号');
        }
        $vcode_form = new VcodeForm();
        $vcode_form->account = $mobile;
        $vcode_form->region = $region;
        $exist = false;
        if (Yii::$app->sso->checkMobileExists($mobile_info->mobile_num, $mobile_info->region_num)) {
            // 第三方绑定登录
            $vcode_form->post_type = VcodeForm::TYPE_BIND_THIRD;
            $exist = true;
        } else {
            // 第三方绑定注册
            $vcode_form->post_type = VcodeForm::TYPE_REGISTER;
        }

        if ($vcode_form->validate()) {
            $vcode_form->sendVcode();
            return [
                'exist' => $exist,
                'have_send' => true
            ];
        } else {
            return $vcode_form->getErrors();
        }
    }

    /**
     * @api {post} /member/fast-login 一键登录
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/member/fast-login
     * @apiSampleRequest /member/fast-login
     *
     * @apiVersion 0.1.0
     * @apiName fast-login
     * @apiGroup member
     *
     * @apiParam {String} uverify_token 友盟登录 token
     *
     * @apiSuccess {Boolean} success true or false
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 346286,
     *         "login_at": **********,
     *         "expire_at": **********,
     *         "update_at": **********,
     *         "is_new": true,  // 是否是新账号
     *         "token": "5ca17e7f831ecc43b003a36f|bd2dba22e67b6e7a|**********|9e8fb45558b965bc",
     *         "user": {
     *           "id": 346286,
     *           "username": "测试用户_76616",
     *           "vip_info": {
     *             "status": 1,  // 会员状态。0：未开通过正式会员；1：正式会员；2：体验会员（暂不下发）；3：正式会员已过期
     *             "end_time": **********  // 会员过期时间戳（含最后一秒），单位：秒
     *           },
     *           ......
     *         }
     *       }
     *     }
     *
     * @apiErrorExample {json} Error-Response: 降级到验证码登录
     *     HTTP/1.1 200 OK
     *     {
     *       "success": false,
     *       "code": 100010019,
     *       "info": "请唤起验证码登录"
     *     }
     */
    public function actionFastLogin()
    {
        $available = $this->actionCheckFastLogin();
        if (!$available['available']) {
            throw new HttpException(403, '请唤起验证码登录', 100010019);
        }

        $uverify_token = trim(Yii::$app->request->post('uverify_token'));
        $data = [
            'type' => SSOClient::TYPE_FAST_LOGIN,
            'uverify_token' => $uverify_token,
            'maxAgeType' => SSOClient::MAX_AGE_ONE_YEAR,
        ];
        try {
            $user_info = Mowangskuser::fastLogin($data);
        } catch (HttpException $e) {
            if ($e->getCode() === SSOClient::CODE_HIGH_RISK) {
                Mowangskuser::throwHighRiskAccountException($e->statusCode, $e->getMessage());
            }
            throw $e;
        }
        // 登录成功后记录一键登录的使用次数
        if ($user_info) {
            Mowangskuser::setFastLoginUsedTimes();
        }
        return $user_info;
    }

    /**
     * @api {post} /member/auth-login2 新第三方账号登录
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/member/auth-login2
     * @apiSampleRequest /member/auth-login2
     *
     * @apiVersion 0.1.0
     * @apiName auth-login2
     * @apiGroup member
     *
     * @apiParam {number=3,4,5,6,7} auth_type 3：QQ；4：Weibo；5：Wechat；6：Bilibili；7：Apple
     * @apiParam {String} [auth_code] 第三方账号授权 code，只有 Bilibili 需要传，其他都不用传
     * 因为友盟的原因，客户端无法取到实际的 auth code
     * @apiParam {String} [access_token] 第三方账号授权 token，Bilibili 不需要传，其他都需要传
     * @apiParam {String} uid 用户唯一标识
     * @apiParam {String} [openid] QQ 登录和微信登录需要传递 openid，其他都不用传
     * @apiParam {String} username 用户昵称
     * @apiParam {File} iconurl 自定义头像（若不传则使用第三方头像，若第三方头像违规则使用默认的猫图）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 346286,
     *         "login_at": **********,
     *         "expire_at": **********,
     *         "update_at": **********,
     *         "token": "5ca17e7f831ecc43b003a36f|bd2dba22e67b6e7a|**********|9e8fb45558b965bc",
     *         "user": {
     *           "id": 346286,
     *           "username": "测试用户_76616",
     *           ......
     *         }
     *       }
     *     }
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": false,
     *       "code": 300010002,
     *       "info": {
     *         "msg": "第三方账号需要绑定手机号",
     *         "error_code": 300010002,
     *         "auth_token": "test_auth_token",
     *         "nickname": "test",
     *         "iconurl": "https://static.missevan.com/avatars/201901/03/9faf5e3fbde55ab43f163ae7b0c36b0f113055.jpeg"
     *       }
     *     }
     */
    public function actionAuthLogin2()
    {
        $auth_type = (int)Yii::$app->request->post('auth_type');
        $auth_code = trim(Yii::$app->request->post('auth_code'));
        $access_token = trim(Yii::$app->request->post('access_token'));
        $openid = trim(Yii::$app->request->post('openid'));
        $uid = trim(Yii::$app->request->post('uid'));
        // TODO: 可以通过第三方的接口获取到，后续不再需要客户端传 username 和 iconurl 两个参数，考虑之后删除
        $username = trim(Yii::$app->request->post('username'));
        $iconurl = trim(Yii::$app->request->post('iconurl'));

        if (($auth_type === LoginForm::BILIBILI && !$auth_code) || !in_array($auth_type, [LoginForm::QQ,
            LoginForm::WEIBO, LoginForm::WECHAT, LoginForm::BILIBILI, LoginForm::APPLE])) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }

        // Bilibili 通过 auth_code 换 access_token，再通过 access_token 获取用户信息
        if (LoginForm::BILIBILI === $auth_type) {
            $bilibli_info = LoginForm::getBilibiliInfo($auth_code);
            [$access_token, $uid, $username, $iconurl] = $bilibli_info;
        }

        switch ($auth_type) {
            case LoginForm::QQ:
                $auth = new AuthQQ();
                break;
            case LoginForm::WEIBO:
                $auth = new AuthWeibo();
                // 验证 access_token 与 uid 是否合法
                // FIXME: 之后所有第三方都需要进行验证
                if (!$auth->validateUuid($uid, $access_token)) {
                    throw new HttpException(400, '第三方登录授权不合法');
                }
                break;
            case LoginForm::WECHAT:
                $auth = new AuthWechat();
                break;
            case LoginForm::BILIBILI:
                // B站在上面已经获取过了
                $auth = null;
                break;
            case LoginForm::APPLE:
                // 苹果暂不支持
                $auth = null;
                break;
            default:
                return false;
        }
        if ($auth) {
            $os = Yii::$app->equip->getOs();
            $return = $auth->auth($uid, $access_token, $os);
            if ($return) {
                $username = $return['nickname'];
                $iconurl = $return['iconurl'];
            }
        }

        // TODO: 后续 SSO 支持第三方验证后去掉 openid，uid，username，iconurl 等代码兼容
        $data = [
            'auth_type' => $auth_type,
            'auth_code' => $auth_code,
            'access_token' => $access_token,
            'openid' => $openid,
            'uid' => $uid,
            'username' => $username,
            'iconurl' => $iconurl,
            'maxAgeType' => SSOClient::MAX_AGE_ONE_YEAR,
        ];
        return Mowangskuser::authLogin($data);
    }

    /**
     * @api {post} /member/fast-auth-bind 第三方账号绑定登录（点击第三方登录，该第三方账号未绑定手机号或该第三方账号未注册过账号时调用）
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/member/fast-auth-bind
     * @apiSampleRequest /member/fast-auth-bind
     *
     * @apiVersion 0.1.0
     * @apiName fast-auth-bind
     * @apiGroup member
     *
     * @apiParam {String} uverify_token 友盟登录 token
     * @apiParam {String} auth_token 第三方账号绑定临时鉴权 token
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 346286,
     *         "login_at": **********,
     *         "expire_at": **********,
     *         "update_at": **********,
     *         "is_new": true,  // 是否是新账号
     *         "token": "5ca17e7f831ecc43b003a36f|bd2dba22e67b6e7a|**********|9e8fb45558b965bc",
     *         "user": {
     *           "id": 346286,
     *           "username": "测试用户_76616",
     *           ......
     *         }
     *       }
     *     }
     *
     * @apiErrorExample {json} Error-Response: 降级到验证码登录
     *     HTTP/1.1 200 OK
     *     {
     *       "success": false,
     *       "code": 100010019,
     *       "info": "请唤起验证码登录"
     *     }
     */
    public function actionFastAuthBind()
    {
        $available = $this->actionCheckFastLogin();
        if (!$available['available']) {
            throw new HttpException(403, '请唤起验证码登录', 100010019);
        }
        $uverify_token = trim(Yii::$app->request->post('uverify_token'));
        $auth_token = trim(Yii::$app->request->post('auth_token'));

        if (!$auth_token) {
            throw new HttpException(403, '参数错误');
        }

        $data = [
            'type' => SSOClient::TYPE_FAST_AUTH_BIND,
            'uverify_token' => $uverify_token,
            'auth_token' => $auth_token,
            'maxAgeType' => SSOClient::MAX_AGE_ONE_YEAR,
        ];
        try {
            $user_info = Mowangskuser::fastLogin($data);
        } catch (HttpException $e) {
            if ($e->getCode() === SSOClient::CODE_HIGH_RISK) {
                Mowangskuser::throwHighRiskAccountException($e->statusCode, $e->getMessage());
            }
            throw $e;
        }
        // 登录成功后记录一键登录的使用次数
        if ($user_info) {
            Mowangskuser::setFastLoginUsedTimes();
        }
        return $user_info;
    }

    /**
     * @api {post} /member/sms-auth-bind 第三方账号验证码绑定手机号
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/member/sms-auth-bind
     * @apiSampleRequest /member/sms-auth-bind
     *
     * @apiVersion 0.1.0
     * @apiName sms-auth-bind
     * @apiGroup member
     *
     * @apiParam {String} code 验证码
     * @apiParam {Number} mobile 手机
     * @apiParam {String} [region='CN'] 区号
     * @apiParam {String} auth_token 第三方账号绑定临时鉴权 token
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 346286,
     *         "login_at": **********,
     *         "expire_at": **********,
     *         "update_at": **********,
     *         "is_new": true,  // 是否是新账号
     *         "token": "5ca17e7f831ecc43b003a36f|bd2dba22e67b6e7a|**********|9e8fb45558b965bc",
     *         "user": {
     *           "id": 346286,
     *           "username": "测试用户_76616",
     *           ......
     *         }
     *       }
     *     }
     */
    public function actionSmsAuthBind()
    {
        $code = trim(Yii::$app->request->post('code'));
        $mobile = (int)Yii::$app->request->post('mobile');
        $region = trim(Yii::$app->request->post('region'));
        $auth_token = trim(Yii::$app->request->post('auth_token'));

        if (!$auth_token) {
            throw new HttpException(403, '参数错误');
        }

        $mobile_info = MUtils2::getMobileNumber($mobile, $region);
        if (!$mobile_info) {
            throw new HttpException(404, Yii::t('app/error', 'Account does not exist'));
        }

        $mobile = (int)$mobile_info->mobile_num;
        $region_num = $mobile_info->region_num;
        $objective_account = $region_num . $mobile;
        // WORKAROUND: iOS 4.6.0 版本及以上判断 objective
        if (!Equipment::isAppOlderThan('4.6.0', null)) {
            if (!VcodeForm::checkObjective(VcodeForm::TYPE_AUTH_BIND_MOBILE, $objective_account)) {
                throw new HttpException(403, Yii::t('app/error', 'Verification code error'));
            }
        }
        if (!VcodeForm::checkIdentifyCode($mobile, $code)) {
            throw new HttpException(403, Yii::t('app/error', 'Verification code error'));
        }
        $data = [
            'type' => SSOClient::TYPE_SMS_AUTH_BIND,
            'mobile' => $mobile,
            'region' => $region_num,
            'auth_token' => $auth_token,
            'maxAgeType' => SSOClient::MAX_AGE_ONE_YEAR,
        ];
        try {
            $user_info = Mowangskuser::fastLogin($data);
        } catch (HttpException $e) {
            if ($e->getCode() === SSOClient::CODE_HIGH_RISK) {
                Mowangskuser::throwHighRiskAccountException($e->statusCode, $e->getMessage());
            }
            // 定位 mowangskuser 与 m_user 用户数据不同步的问题
            Yii::warning(
                sprintf('第三方账号验证码绑定手机号失败，错误位置：%s:%d，错误信息：%s', $e->getFile(), $e->getLine(), $e->getMessage()),
                __METHOD__
            );
            throw $e;
        }
        // 操作成功后，删除验证码
        VcodeForm::delObjectiveKey($objective_account);
        VcodeForm::delIdentifyCode($mobile);
        return $user_info;
    }

    /**
     * @api {post} /member/sms-login 验证码登录
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/member/sms-login
     * @apiSampleRequest /member/sms-login
     *
     * @apiVersion 0.1.0
     * @apiName sms-login
     * @apiGroup member
     *
     * @apiParam {String} code 验证码
     * @apiParam {Number} mobile 手机
     * @apiParam {String} region 区号
     *
     * @apiSuccess {Boolean} success true or false
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 346286,
     *         "login_at": **********,
     *         "expire_at": **********,
     *         "update_at": **********,
     *         "is_new": true,  // 是否是新账号
     *         "token": "5ca17e7f831ecc43b003a36f|bd2dba22e67b6e7a|**********|9e8fb45558b965bc",
     *         "user": {
     *           "id": 346286,
     *           "username": "测试用户_76616",
     *           "vip_info": {
     *             "status": 1,  // 会员状态。0：未开通过正式会员；1：正式会员；2：体验会员（暂不下发）；3：正式会员已过期
     *             "end_time": **********  // 会员过期时间戳（含最后一秒），单位：秒
     *           },
     *           ......
     *         }
     *       }
     *     }
     */
    public function actionSmsLogin()
    {
        $code = trim(Yii::$app->request->post('code'));
        $mobile = (int)Yii::$app->request->post('mobile');
        $region = trim(Yii::$app->request->post('region'));

        $mobile_info = MUtils2::getMobileNumber($mobile, $region);
        if (!$mobile_info) {
            throw new HttpException(404, Yii::t('app/error', 'Account does not exist'));
        }

        $mobile = (int)$mobile_info->mobile_num;
        $region_num = $mobile_info->region_num;
        $objective_account = $region_num . $mobile;
        if (!VcodeForm::checkObjective(VcodeForm::TYPE_SMS_LOGIN, $objective_account)) {
            throw new HttpException(403, Yii::t('app/error', 'Verification code error'));
        }
        if (!VcodeForm::checkIdentifyCode($mobile, $code)) {
            throw new HttpException(403, Yii::t('app/error', 'Verification code error'));
        }

        $data = [
            'type' => SSOClient::TYPE_SMS_LOGIN,
            'mobile' => $mobile,
            'region' => $region_num,
            'maxAgeType' => SSOClient::MAX_AGE_ONE_YEAR,
        ];
        try {
            $user_info = Mowangskuser::fastLogin($data);
        } catch (HttpException $e) {
            if ($e->getCode() === SSOClient::CODE_HIGH_RISK) {
                Mowangskuser::throwHighRiskAccountException($e->statusCode, $e->getMessage());
            }
            throw $e;
        }
        // 操作成功后，删除验证码
        VcodeForm::delObjectiveKey($objective_account);
        VcodeForm::delIdentifyCode($mobile);
        return $user_info;
    }

    /**
     * @api {post} /member/check-fast-login 检查是否可以唤起一键登录
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/member/check-fast-login
     * @apiSampleRequest /member/check-fast-login
     *
     * @apiVersion 0.1.0
     * @apiName check-fast-login
     * @apiGroup member
     *
     * @apiSuccess {Boolean} success true or false
     * @apiSuccess {Number} code
     * @apiSuccess {Boolean} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "available": true  // true：可以唤起；false：无法唤起，改用密码登录
     *       }
     *     }
     */
    public function actionCheckFastLogin()
    {
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_COUNTER_FASTLOGIN_EQUIP, Yii::$app->equip->getEquipId());

        $available = true;
        // 允许一键登录的次数
        $LIMIT_FAST_LOGIN = 5;
        if ((int)$redis->get($key) >= $LIMIT_FAST_LOGIN) {
            $available = false;
        }
        return ['available' => $available];
    }

    /**
     * @api {post} /member/youzan-login 有赞登录
     * @apiExample {curl} Example usage:
     *     curl -i http://{{host}}/member/youzan-login
     * @apiSampleRequest /member/youzan-login
     *
     * @apiVersion 0.1.0
     * @apiName youzan-login
     * @apiGroup member
     *
     * @apiSuccess {Boolean} success true or false
     * @apiSuccess {Number} code
     * @apiSuccess {Boolean} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         // cookie_key 及 cookie_value 用于旧版有赞 SDK
     *         // 新版 SDK 使用 open_user_id 登录，其它他参数（如头像、昵称等）传空即可
     *         "cookie_key": "open_cookie_563abcf14f8b6501c6",
     *         "cookie_value": "YZ791359191830507520YZqlISbIVL",
     *         "open_user_id": "919qlISb14f8b6501"
     *       }
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     {
     *       "success": false,
     *       "code": 100010006,
     *       "info": "Token 过期或不存在"
     *     }
     */
    public function actionYouzanLogin()
    {
        try {
            $youzan_info = Yii::$app->sso->youzanLogin();
            $this->deliverYouZanCoupon(Yii::$app->user->id);
        } catch (HttpException $e) {
            if ($e->getCode() === SSOClient::CODE_USER_TOKEN_EXPIRED) {
                throw new HttpException($e->statusCode, $e->getMessage(), 100010006);
            }
            throw $e;
        }
        return $youzan_info;
    }

    private function deliverYouZanCoupon(int $user_id)
    {
        try {
            $benefits = BiliVipMaoerUserBenefit::getPendingBenefits($user_id, BiliVipMaoerBenefitMenu::BENEFIT_TYPE_YOUZAN_MALL_COUPON);
            if (empty($benefits)) {
                return;
            }
            foreach ($benefits as $benefit) {
                if ($benefit->bili_vip_period_end <= $_SERVER['REQUEST_TIME']) {
                    // 未及时进入有赞商城领取，则过期处理
                    $benefit->status = BiliVipMaoerUserBenefit::STATUS_EXPIRED;
                    if (!$benefit->save()) {
                        Yii::error('BiliVipMaoerUserBenefit save error: ' . MUtils2::getFirstError($benefit), __METHOD__);
                    }
                    continue;
                }

                $item = new BiliVipMaoerBenefitMenu([
                    'benefit_type' => BiliVipMaoerBenefitMenu::BENEFIT_TYPE_YOUZAN_MALL_COUPON,
                    'more' => ['coupon_ids' => $benefit->more['coupon_ids']],
                ]);
                $benefit->deliver($item, function (BiliVipMaoerUserBenefit $benefit, string|null $error_msg) {
                    if ($error_msg) {
                        $benefit->status = BiliVipMaoerUserBenefit::STATUS_FAILED;
                        $benefit->more += ['error' => $error_msg];
                        Yii::error("用户领取有赞优惠券失败：{$error_msg}", __METHOD__);
                    } else {
                        $benefit->status = BiliVipMaoerUserBenefit::STATUS_SUCCESS;
                        $benefit->more += ['receive_time' => $_SERVER['REQUEST_TIME']];
                    }
                    if (!$benefit->save()) {
                        Yii::error('BiliVipMaoerUserBenefit save error: ' . MUtils2::getFirstError($benefit), __METHOD__);
                    }
                });
            }
        } catch (\Exception $e) {
            Yii::error("用户领取有赞优惠券失败：{$e->getMessage()}", __METHOD__);
        }
    }
}
