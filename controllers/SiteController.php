<?php

namespace app\controllers;

use app\components\base\filter\AccessControl;
use app\components\controllers\SiteInterface;
use app\components\tree\Catalogs;
use app\components\util\AliHttpDns;
use app\components\util\Equipment;
use app\components\util\MUtils;
use app\middlewares\Controller;
use app\models\AdTrack;
use app\models\AdTrackAppsFlyer;
use app\models\AdTrackThirdparty;
use app\models\Blacklist;
use app\models\Catalog;
use app\models\DeviceCAIDInfo;
use app\models\Drama;
use app\models\InstallBuvid;
use app\models\InstallLog;
use app\models\LaunchReportLog;
use app\models\Live;
use app\models\MAlbum;
use app\models\MAppIcon;
use app\models\MAppupdate;
use app\models\MHomepageFlow;
use app\models\MHomepageIcon;
use app\models\MLaunch;
use app\models\Mowangskuser;
use app\models\MPersonaModuleElement;
use app\models\MPowerSoundIp;
use app\models\MRecommendedElements;
use app\models\MRecommendPopup;
use app\models\MSound;
use app\models\MSoundAlbumMap;
use app\models\Msr0;
use app\models\MTab;
use app\models\MPowerSound;
use app\models\MTabBarPackage;
use app\models\MTag;
use app\models\MUserConfig;
use app\models\Persona;
use app\models\ReturnModel;
use app\models\SoundVideo;
use app\models\ToThirdPartyParams;
use app\models\UserAddendum;
use app\models\Work;
use app\models\YouMightLikeModule;
use Exception;
use GuzzleHttp\Client;
use missevan\support\Country;
use missevan\util\MUtils as MUtils2;
use missevan\storage\StorageClient;
use Yii;
use yii\db\Expression;
use yii\filters\VerbFilter;
use yii\helpers\Html;
use yii\helpers\Json;
use yii\web\HttpException;

class SiteController extends Controller implements SiteInterface
{
    // 分类下音频换一批时获取音频数量
    const CATALOG_SOUNDS_NUM = 6;

    // App 首页剧集打赏榜单数目
    const HOMEPAGE_RANK_NUM = 10;

    // 白天或黑夜样式 0 为白天，1 为黑夜
    const ICON_DARK = 1;
    const ICON_NORMAL = 0;

    // 客户端请求服务端时机
    const NOT_BOOT = 0;  // 非启动客户端后请求
    const BOOT = 1;  // 启动客户端后请求

    // 新人 tab 页面模块类型，1：游客广告条；2：登录用户广告条；3：自定义模块；4：剧集福袋模块
    const NEW_USER_BLOCK_TYPE_GUEST_BANNER = 1;
    const NEW_USER_BLOCK_TYPE_USER_BANNER = 2;
    const NEW_USER_BLOCK_TYPE_CUSTOM_MODULE = 3;
    const NEW_USER_BLOCK_TYPE_LUCKYBAG = 4;

    // 全职高手跳转 URL（网页版 URL 待定）
    public static $QUANZHI_ICON_URL;

    public function init()
    {
        parent::init();
        $quanzhi_web_url = Yii::$app->params['domainMobileWeb'] . '/voice/' . Work::ID_QUANZHI . '?webview=1';
        $quanzhi_app_url = 'missevan://voice/' . Work::ID_QUANZHI;
        self::$QUANZHI_ICON_URL = self::$QUANZHI_ICON_URL ?: [
            Equipment::DEBUG_USER_AGENT => $quanzhi_web_url,
            Equipment::Android => $quanzhi_app_url,
            Equipment::iOS => $quanzhi_app_url,
            Equipment::HarmonyOS => $quanzhi_app_url,
            Equipment::Web => $quanzhi_web_url,
        ];
    }

    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'logout' => ['post'],
                'install' => ['post'],
                'set-message-config' => ['post'],
                'set-user-config' => ['post'],
                'ad-track' => ['post'],
                'track-consume-pay' => ['post'],
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'only' => [
                'set-message-config',
                'get-message-config',
            ],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => [
                        'set-message-config',
                        'get-message-config',
                    ],
                    'roles' => ['@'],
                ],
            ],
        ];
        return $behaviors;
    }

    /**
     * @api {get} /site/launch 启动接口 是否升级，启动图
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/launch
     * @apiSampleRequest /site/launch
     * @apiDescription 灰度测试：Web 后台发布 APP 时，选择版本类型为“测试/正式版”。若存在测试版，则进行测试版本逻辑
     * @apiVersion 0.1.0
     * @apiName launch
     * @apiGroup site
     *
     * @apiParam {String} app_sign App 应用签名
     * @apiParam {Number} version_code 版本号
     * @apiParam {String} supported_abis 支持的 abi
     * @apiParam {String} runtime_abi 当前的 abi（暂不使用）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "sound_url": "http://static.missevan.com/MP3/201605/14/297966dceaf85625530bad6e682dda47120400.mp3",
     *         "privacy_data": "http://static.missevan.com/app/privacy.json",  // 隐私协议地址
     *         "new_version": {
     *           "download": "http://static.missevan.com/app/201704/04/MissEvan.apk",
     *           "intro": "3.4.0",
     *           "log": "1添加设置启动音功能\n2添加广播剧追剧功能\n2添加发现界面\n3添加分类首页功能\n4修复部分新注册用户无法答题的bug",
     *           "v": 42,
     *           "is_beta": true,
     *           "force_download": 0,
     *           "app_sign": ""  // 应用签名
     *         },
     *         "version": "4.0.7",  // 仅在 iOS 版本中存在
     *         "teenager_status": 1,  // 用户青少年状态：1 开启 0 关闭 -1 未登录
     *         "season": 3,  // 1：春季；2：夏季；3：秋季；4：冬季
     *         "splash": [  // 使用中闪屏图
     *           {
     *             "id": 1, // 当时间范围内没有启动图数据时，启动图为默认图，id 为 0
     *             "pic_url": "http://static.missevan.com/mimages/201703/28/3ab641f736366f4306b9bf73220e436c141357.png",
     *             "redirect_url": "https://www.uat.missevan.com/albuminfo/69506", // 没有跳转链接的时候，字段可能不存在
     *             "label": "广告", // 认证标志
     *             "no_skip": 0, // 是否不可跳过 0：否；1：是
     *             "launch_type": 0, // 闪屏类型 0：图片；1：视频，当闪屏类型为视频时，url 为 MP4 格式
     *             "message": "测试数据" // 没有跳转按钮文案的时候，字段可能不存在
     *           }
     *         ],
     *         "splash_ready": [  // 待上线闪屏图
     *           {
     *             "pic_url": "http://static.missevan.com/mimages/201703/28/3ab641f736366f4306b9bf73220e436c141357.png",  // 可能会和使用中的闪屏图素材地址相同，客户端本地需要做好去重处理
     *             "launch_type": 0
     *           },
     *           {
     *             "pic_url": "http://static.missevan.com/mimages/201703/28/3ab641f736366f4306b9bf73220e436c141357.mp4",
     *             "launch_type": 1
     *           }
     *         ],
     *         "app_icon": {  // 应用图标配置，未下发时使用默认图标。目前仅 iOS 会下发，安卓采用发版的形式变更图标
     *           "id": 1,  // 当前使用的应用图标 ID（需在打包前跟客户端约定好 ID 和图标的对应关系）
     *           "expire_duration": 86400  // 该图标剩余（展示）时长，为 0 时表示永久展示。单位：秒
     *         },
     *         "tab_bar_package": {  // 首页底部图标配置，未下发时使用默认图标
     *           "id": 2,  // 当前使用的底部图标包 ID
     *           "expire_duration": 86400  // 该图标包剩余（展示）时长，为 0 时表示永久展示。单位：秒
     *         },
     *         "theme_skin": {  // 主题皮肤，未下发或下发 null 时使用默认主题
     *           "id": 1,  // 当前使用的主题皮肤 ID
     *           "package_url": "https://static-test.maoercdn.com/app/themeskin/202412/12/tcu8461ccs2497829dbe9541b55bdc8d170256.zip"  // 主题皮肤压缩包地址，其中 package.json 中的 package_id 与下发的 theme_skin.id 字段值一致
     *         }
     *       }
     *     }
     */
    public function actionLaunch()
    {
        $version_code = (int)Yii::$app->request->get('version_code');
        $supported_abis = trim(Yii::$app->request->get('supported_abis'));
        $user_id = (int)Yii::$app->user->id;
        if ($user_id) {
            $user = Mowangskuser::getPersonInfo($user_id);
            // 判断用户是否是永久禁止登录
            if ($user && $user->isBanLogin()) {
                // 清空用户所有 session
                Yii::$app->sso->clearSession($user->id);
            }
        }
        $data = MAppupdate::getUpdate(Yii::$app->equip, $version_code, true, $supported_abis);
        $data += MLaunch::getLaunchPicAndSound();
        $data['user_id'] = $user_id;

        // 强制升级过老版本
        if (Equipment::isBanVersion()) {
            $data['new_version']['force_download'] = MAppupdate::FORCE_DOWNLOAD_YES;
        }

        $data['teenager_status'] = $user_id ? Mowangskuser::isTeenagerModeEnabled($user_id) : -1;
        $data['privacy_data'] = MAppupdate::getPrivacyDataUrl(Yii::$app->equip->getChannel());

        // 获取当前季度
        $data['season'] = MUtils::getSeason();  // 1：春季；2：夏季；3：秋季；4：冬季

        // 增加用户 IP databus 日志
        UserAddendum::addIPLog($user_id);

        // 对 iOS 下发应用图标，安卓采用发版的形式变更图标
        if (Yii::$app->equip->isIOS() && $app_icon = MAppIcon::getAppIcon()) {
            $data['app_icon'] = $app_icon;
        }
        // 下发应用底部图标
        if ($tab_bar_package = MTabBarPackage::getTabBarPackage()) {
            $data['tab_bar_package'] = $tab_bar_package;
        }
        // 主题皮肤
        $data['theme_skin'] = null;
        if ($user_id) {
            try {
                $info = Yii::$app->serviceRpc->getThemeSkin($user_id);
                $data['theme_skin'] = $info['theme_skin'] ?? null;
            } catch (Exception $e) {
                // 记录错误日志
                Yii::error(sprintf('获取用户（%d）主题皮肤出错：%s', $user_id, $e->getMessage()),
                    __METHOD__);
                // PASS
            }
        }
        return $data;
    }

    /**
     * @api {get} /site/check-update 检查新版本
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/check-update
     * @apiSampleRequest /site/check-update
     *
     * @apiVersion 0.1.0
     * @apiName check-update
     * @apiGroup site
     *
     * @apiParam {String} [app_sign] App 应用签名
     * @apiParam {Number} version_code 版本号
     * @apiParam {String} supported_abis 支持的 abi
     * @apiParam {String} runtime_abi 当前的 abi（暂不使用）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "new_version": {
     *           "download": "http://static.missevan.com/app/201704/04/MissEvan.apk",
     *           "intro": "3.4.0",
     *           "log": "1添加设置启动音功能\n2添加广播剧追剧功能\n2添加发现界面\n3添加分类首页功能",
     *           "v": 42,
     *           "is_beta": true,
     *           "force_download": 0,
     *           "app_sign": ""  // 应用签名
     *         },
     *         "version": "4.0.7"  // 仅在 iOS 版本中存在
     *       }
     *     }
     */
    public function actionCheckUpdate()
    {
        $version_code = (int)Yii::$app->request->get('version_code');
        $supported_abis = trim(Yii::$app->request->get('supported_abis'));
        return MAppupdate::getUpdate(Yii::$app->equip, $version_code, false, $supported_abis);
    }

    /**
     * @api {get} /site/support-country 获取手机号的支持国家、国际区号
     * @apiVersion 0.1.0
     * @apiName support-country
     * @apiGroup site
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info": {
     *         "#": [  // 常用
     *           {
     *             "code": "CN",
     *             "value": 86,
     *             "name": "中国大陆"
     *           },
     *           {
     *             "code": "HK",
     *             "value": 852,
     *             "name": "中国香港特别行政区"
     *           }
     *         ],
     *         "A": [
     *           {
     *             "code": "AL",
     *             "value": 355,
     *             "name": "阿尔巴尼亚"
     *           },
     *           {
     *             "code": "DZ",
     *             "value": 213,
     *             "name": "阿尔及利亚"
     *           },
     *           {
     *             "code": "AF",
     *             "value": 93,
     *             "name": "阿富汗"
     *           }
     *         ],
     *         "B": [
     *           {
     *             "code": "BB",
     *             "value": 1246,
     *             "name": "巴巴多斯"
     *           },
     *           {
     *             "code": "PG",
     *             "value": 675,
     *             "name": "巴布亚新几内亚"
     *           },
     *           {
     *             "code": "BS",
     *             "value": 1242,
     *             "name": "巴哈马"
     *           }
     *         ],
     *         "C": [
     *         ],
     *         "D": [
     *           {
     *             "code": "DK",
     *             "value": 45,
     *             "name": "丹麦"
     *           }
     *         ],
     *         ...
     *         "Z": [
     *         ],
     *       }
     *     }
     */
    public function actionSupportCountry()
    {
        $country = Country::items();
        $result = [];

        // 常用
        $common_countries = [
            'CN', 'HK', 'MO', 'TW', 'US', 'BE', 'AU', 'FR', 'CA', 'JP', 'SG', 'KR', 'MY', 'GB', 'IT', 'DE', 'RU', 'NZ',
        ];

        // 初始化 A-Z
        $cn_initial = require __DIR__ . '/../sdk/CN_pinyin.php';
        foreach (range('A', 'Z') as $initial) {
            $result[$initial] = [];
        }

        $is_old_app = Equipment::isAppOlderThan('4.9.9', '5.8.2');
        $common = [];
        // 格式化
        $exception = ['塔', '不', '汤'];
        foreach ($country as $key => $value) {
            $first = mb_substr($value[1], 0, 1);
            if (!array_key_exists($first, $cn_initial)) {
                // TODO: 增加报警日志 - 该汉字不存在
                continue;
            }

            // WORKAROUND: 矫正特定多音字（$exception 仅用于 WORKAROUND，解决时请一并移除）
            if (in_array($first, $exception)) {
                $first = $cn_initial[$first];
                $initial = explode(',', $first)[1];
            } else {
                $initial = $cn_initial[$first];
            }
            $info = [
                'code' => $key,
                'value' => $value[0],
                'name' => $value[1],
            ];
            $is_common = in_array($key, $common_countries);
            if ($is_old_app) {
                // 安卓 < 5.8.1, iOS < 4.9.8 版本返回 is_common 字段
                $info['is_common'] = (int)$is_common;  // 是否是常用区号
            }
            if ($is_common) {
                $common[] = $info;
            }
            $result[strtoupper(mb_substr($initial, 0, 1))][] = $info;
        }
        if (!$is_old_app) {
            // 安卓 >= 5.8.1, iOS >= 4.9.8 版本按照 $common_countries 顺序返回 # 常用区号字段
            usort($common, function ($i, $j) use ($common_countries) {
                return array_search($i['code'], $common_countries) - array_search($j['code'], $common_countries);
            });
            $result = array_merge(['#' => $common], $result);
        }

        return $result;
    }

    /**
     * @api {get} /site/new-power-sound 新版启动音
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/new-power-sound
     * @apiSampleRequest /site/new-power-sound
     * @apiDescription Android 大于 5.5.0 iOS 大于 4.6.1 使用新版启动音
     *
     * @apiVersion 0.1.0
     * @apiName new-power-sound
     * @apiGroup site
     *
     * @apiParam {String} keyword 关键字
     * @apiParam {Number} page 当前页
     * @apiParam {Number} page_size 每页显示条数
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [
     *           {
     *             "list": [{
     *               "id": 1,
     *               "sound_id": 1,
     *               "cv": "路行知",
     *               "role_name": "魏无羡",
     *               "ip_name": "魔道祖师",
     *               "cover": "http://static.missevan.com/system/app/powersound/zhuren1.png",  // 封面地址
     *               "icon": "http://static.missevan.com/system/app/powersound/zhuren_s.png",  // 图标链接
     *               "intro": "这是我的台词",  // 台词
     *               "soundurl": "http://static.missevan.com/MP3/201412/03/244aed721247a203174217b5b48f4906122925.mp3",  // 启动音链接
     *               "sound_sort_order": 0  // 排序，倒序
     *             }],
     *             "ip_name": "魔道祖师",
     *             "ip_sort_order": 1,  // 排序，倒序
     *             "support_default_selection": true  // 是否支持默认选中，true：是；false：否
     *           }
     *         ],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 25,
     *           "count": 1,
     *           "pagesize": 30
     *         },
     *       }
     *     }
     */
    public function actionNewPowerSound(string $keyword = '', int $page = 1, int $page_size = MPowerSound::DEFAULT_PAGE_SIZE)
    {
        if (!$keyword) {
            $key = MUtils2::generateCacheKey(KEY_NEW_POWER_SOUND, ...[$page, $page_size]);
            [$sounds, $count] = MUtils2::getOrSetDistrubutedCache($key,
                function () use ($page, $page_size, $keyword) {
                    // 缓存中没有数据则从表中获取
                    return MPowerSound::getPowerSoundList($page, $page_size, $keyword);
                }, TEN_MINUTE);
        } else {
            [$sounds, $count] = MPowerSound::getPowerSoundList($page, $page_size, $keyword);
        }

        if (empty($sounds)) {
            return ReturnModel::getPaginationData([], $count, $page, $page_size);
        }

        $ip_list = [];
        foreach ($sounds as $val) {
            $ip_list[$val['ip_name']]['list'][] = [
                'id' => (int)$val['id'],
                'cv' => $val['cv'],
                'role_name' => $val['role_name'],
                'ip_name' => $val['ip_name'],
                'cover' => $val['cover'],
                'icon' => $val['icon'],
                'intro' => $val['intro'],
                'soundurl' => $val['playurl'],
                'sound_sort_order' => $val['sound_sort_order'],
            ];
            $ip_list[$val['ip_name']]['ip_name'] = $val['ip_name'];
            $ip_list[$val['ip_name']]['ip_sort_order'] = $val['ip_sort_order'];
            $ip_list[$val['ip_name']]['support_default_selection'] = MPowerSoundIp::isSupportDefaultSelection($val['ip_attr']);
        }

        return ReturnModel::getPaginationData(array_values($ip_list), $count, $page, $page_size);
    }

    /**
     * @api {get} /site/editor-choice app 首页顶部编辑推荐部分，包括版头图，自定义图标，今日推荐，频道
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/editor-choice
     * @apiSampleRequest /site/editor-choice
     * @apiDescription  /mobile/site/newHomepage2 首页
     *
     * @apiVersion 0.1.0
     * @apiName editor-choice
     * @apiGroup site
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *      "info":
     *          {
     *               { music:
     *                  [
     *                      [Object],
     *                      [Object],
     *                      [Object],
     *                      ......
     *                  ]
     *              }
     *          }
     *     }
     *
     */
    public function actionEditorChoice()
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }

        $redis = Yii::$app->redis;
        $paths = $redis->hGetAll(APP_HOMEPAGE_KEY);
        if (empty($paths)) {
            Yii::error('App 首页顶部编辑推荐无数据', __METHOD__);
            return null;
        }
        $path = $paths['app_recommend'] ?? 'home/sounds/201708/23/recommend_599d33a9d5290.json';
        $domain = Yii::$app->params['static_domain'];
        return $this->redirect($domain . $path);
    }

    /**
     * @api {get} /site/icons 首页小图标
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/icons
     * @apiSampleRequest /site/icons
     * @apiDescription 首页小图标
     * <br />WARNING: dark 参数将在 Android 4.5.0 及 IOS 5.4.0 版本后废弃
     * @apiVersion 0.3.0
     * @apiName icons
     * @apiGroup site
     *
     * @apiParam {number=0,1} [sync=0] 同步设备与账号标志（1 为开始同步，0 为否）
     * @apiParam {Number} persona_id 用户画像
     *
     * @apiSuccess {Boolean} success 请求状态
     * @apiSuccess {Number} code 状态码
     * @apiSuccess {Object} info  请求数据详情
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "live_module": {  // 模块被隐藏或不存在时返回 null
     *           "title": "正在直播"
     *         },
     *         "icons": [
     *           {
     *             "id": 3, // icon 唯一标识
     *             "url": "missevan://event",
     *             "title": "活动",
     *             "icon": "http://static.missevan.com/profile/icon01.png",  // 老版正常使用，新版日间图标
     *             "dark_icon": "http://static.missevan.com/profile/icon01.png"  // 黑夜图标
     *           },
     *           {
     *             "id": 4,
     *             "url": "https://m.missevan.com/summerdrama",
     *             "title": "精品周更",
     *             "icon": "http://static.missevan.com/profile/icon01.png",  // 老版正常使用，新版日间图标
     *             "dark_icon": "http://static.missevan.com/profile/icon01.png"  // 黑夜图标
     *           },
     *           {
     *             "id": 1,
     *             "url": "missevan://drama",
     *             "title": "广播剧",
     *             "icon": "http://static.missevan.com/profile/icon01.png",  // 老版正常使用，新版日间图标
     *             "dark_icon": "http://static.missevan.com/profile/icon01.png"  // 黑夜图标
     *           }
     *         ]
     *     }
     */
    public function actionIcons(?int $persona_id = null)
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }
        $os = Equipment::iOS;
        if (Yii::$app->equip->isAndroidOrHarmonyOS()) {
            $os = Equipment::Android;
        }
        $redis = Yii::$app->redis;
        $icons = $redis->get(KEY_APP_HOMEPAGE_ICONS);
        $icons = $icons ? Json::decode($icons) : [];
        if (!empty($icons)) {
            $is_black_channel = Blacklist::model()->isBlackChannel();
            $is_game_center_blocked = Blacklist::model()->isGameCenterBlocked();
            $user_id = Yii::$app->user->id;
            $icons = array_reduce($icons, function ($ret, $icon) use ($os, $is_game_center_blocked, $is_black_channel, $redis, $user_id, &$persona_id) {
                if (!self::isEquipMatchedWithIcon($icon['select'], $os)) {
                    // 仅在需要的 OS 平台显示
                    return $ret;
                }
                if ($is_game_center_blocked && preg_match('/^missevan:\/\/game(\/.*)?$/', $icon['url'])) {
                    // 隐藏对应渠道的“游戏中心”图标
                    return $ret;
                }
                if ($is_black_channel && preg_match('/^missevan:\/\/theatre(\/.*)?$/', $icon['url'])) {
                    // 隐藏对应渠道的“盲盒剧场”图标
                    return $ret;
                }
                $icon['icon'] = self::getHomepageIcon($icon, self::ICON_NORMAL);
                $icon['dark_icon'] = self::getHomepageIcon($icon, self::ICON_DARK);
                $rank_link = Yii::$app->params['web_links']['homepage_rank_details'];
                // 将榜单跳转链接中的正则表达式特殊字符进行转义，以便可以直接用于匹配操作
                $rank_link_regex = str_replace(['http\:', 'https\:'], 'https?\:', preg_quote($rank_link, '/'));
                if (preg_match("/^{$rank_link_regex}\?.*/", $icon['url'])) {
                    // WORKAROUND: 针对 iOS 4.9.6, 安卓 5.8.0 以下版本没有 persona_id 参数时，
                    // 需要查询 persona_id 并在排行榜跳转链接拼接 persona_id 参数
                    if (!$persona_id) {
                        // 后台最多只会配置一个排行榜 icon，不会存在多次查库获取 persona_id 的情况
                        $persona_id = Persona::getPersonaType();
                    }
                    $persona_module = $persona_id & Persona::PERSONA_MODULE_MASK;
                    $query = parse_url($icon['url'], PHP_URL_QUERY);
                    parse_str($query, $query_arr);
                    $query_arr['persona_id'] = $persona_module;
                    $query = http_build_query($query_arr);
                    $icon['url'] = "{$rank_link}?{$query}";
                }
                $icon['url'] = MUtils::getUsableAppLink($icon['url']);
                $icon['title'] = self::getIconTitle($icon);
                unset($icon['select'], $icon['dark'], $icon['normal']);
                $ret[] = $icon;
                return $ret;
            }, []);
            // TODO: 后续需要完善对类似语音包这样带消息红点的图标调整（包括传入 sync 参数时同步红点信息的逻辑）
        }
        // 从 iOS 4.9.6, 安卓 5.8.0 开始客户端正常情况都会传 persona_id
        // 没有 persona_id 参数并且配置了排行榜图标时，使用数据库里获取的 persona_id
        // 没有 persona_id 参数并且没有配置排行榜图标时，默认使用女性画像
        if (!$persona_id) {
            $persona_id = Persona::TYPE_GIRL;
        }
        // 接口返回信息
        $info = ['icons' => $icons];
        $persona_module = $persona_id & Persona::PERSONA_MODULE_MASK;
        $live_module = MPersonaModuleElement::getLiveModule($persona_module);
        $live_module_info = null;
        if ($live_module) {
            $live_module_info = ['title' => $live_module->title ?: '正在直播'];
        }
        $info['live_module'] = $live_module_info;
        return $info;
    }

    /**
     * 获取 icon 标题
     *
     * @param array $icon icon 信息
     * @return string icon 标题
     */
    private static function getIconTitle(array $icon)
    {
        $title = $icon['title'] ?? '';
        if ($title === '') {
            Yii::error('icon 未配置名称：' . Json::encode($icon), __METHOD__);
            // PASS
        }
        return $title;
    }

    /**
     * @param $icon
     * @param integer $dark 黑夜或白天模式（0 白天，1 为黑夜）
     *
     * @return string
     */
    private static function getHomepageIcon($icon, $dark): string
    {
        $hash_key = $dark ? 'dark' : 'normal';
        return Yii::$app->storage->getUrl(OSS_DIR_APP_HOMEPAGE_ICONS,
            "{$hash_key}/{$icon[$hash_key]}");
    }

    /**
     * 设备是否与图标匹配
     *
     * @param integer $icon_select 图标标志（1 位属于安卓，2 位属于 iOS）
     * @param integer $os 设备类型（1 安卓，2 iOS）
     *
     * @return boolean
     */
    private static function isEquipMatchedWithIcon($icon_select, $os)
    {
        // icon_select 的 1 位为安卓图标（icon_select & 1 != 0），2 位为 iOS 图标（icon_select & 2 != 0）
        if ($os <= 0) {
            return false;
        }
        return ($icon_select & (1 << ($os - 1))) !== 0;
    }

    /**
     * @api {get} /site/catalogs 分类信息
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/catalogs
     * @apiSampleRequest /site/catalogs
     * @apiDescription 分类信息
     * @apiVersion 0.1.0
     * @apiName catalogs
     * @apiGroup site
     *
     * @apiParam {Number} [cid=1] 父级分类 ID
     *
     * @apiSuccess {Boolean} success 请求状态
     * @apiSuccess {Number} code 状态码
     * @apiSuccess {Object} info  请求数据详情
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 52,
     *         "name": "配音",
     *         "pid": 1,
     *         "sort": 0,
     *         "level": 0,
     *         "sons": [
     *           {
     *             "id": 14,
     *             "name": "中文配音",
     *             "parent_id": 52,
     *             "sort": 5,
     *             "level": 1
     *           },
     *           {
     *             "id": 23,
     *             "name": "角色录音",
     *             "parent_id": 52,
     *             "sort": 3,
     *             "level": 1
     *           },
     *           {
     *             "id": 24,
     *             "name": "声优录音",
     *             "parent_id": 52,
     *             "sort": 2,
     *             "level": 1
     *           },
     *           {
     *             "id": 25,
     *             "name": "作品录音",
     *             "parent_id": 52,
     *             "sort": 1,
     *             "level": 1
     *           },
     *           {
     *             "id": 53,
     *             "name": "日文及其他",
     *             "parent_id": 52,
     *             "sort": 4,
     *             "level": 1
     *           }
     *         ]
     *       }
     *     }
     *
     */
    public function actionCatalogs(int $cid = Catalogs::SOUND_CATALOG_ID)
    {
        $catalogs = new Catalogs($cid, SORT_DESC);
        $info = $catalogs->getTree();
        return $info;
    }

    /**
     * @api {get} /site/editor-catalog 首页下方数据
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/editor-catalog
     * @apiSampleRequest /site/editor-catalog
     * @apiDescription /mobile/site/newHomepage
     * @apiVersion 0.1.0
     * @apiName catalogs
     * @apiGroup site
     *
     * @apiSuccess (302) {Object} null
     * 重定向至数据资源文件，例 http://static.missevan.com/home/<USER>/201708/14/app_catalog_1502707204.json
     */
    public function actionEditorCatalog()
    {
        $redis = Yii::$app->redis;
        $paths = $redis->hMGet(APP_HOMEPAGE_KEY, ['catalog_sounds', 'foreign_catalog_sounds']);
        if (empty($paths)) {
            Yii::error('首页下方无数据', __METHOD__);
            return null;
        }
        if (MUtils::isChinaMainland()) {
            $path = $paths['catalog_sounds'] ?: 'home/sounds/202008/12/app_catalog_1597200005.json';
            // WORKAROUND: 华为渠道且同时也是黑名单渠道包过滤【声音恋人】分类
            if (Yii::$app->equip->getChannel() === Equipment::CHANNEL_HUAWEI && Blacklist::model()->isBlackChannel()) {
                $data = Yii::$app->storage->download($path);
                if (!$data) {
                    return null;
                }
                $res = Json::decode($data);
                if (!isset($res['info']['music'])) {
                    return null;
                }
                $res['info']['music'] = array_values(array_filter($res['info']['music'], function ($item) {
                    return (int)$item['id'] !== Catalog::CATALOG_ID_SOUND_LOVER;
                }));
                Yii::$app->response->data = $res;
                Yii::$app->response->off(Yii::$app->response::EVENT_BEFORE_SEND);
                Yii::$app->response->send();
                return;
            }
        } else {
            $path = $paths['foreign_catalog_sounds'] ?: 'home/sounds/202008/12/app_foreign_catalog_1597200005.json';
        }

        $info = Yii::$app->params['static_domain'] . $path;
        return $this->redirect($info);
    }

    /**
     * @api {get} /site/catalog-change 首页换一批
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/catalog-change
     * @apiSampleRequest /site/catalog-change
     * @apiDescription 首页换一批
     * @apiVersion 0.1.0
     * @apiName catalog-change
     * @apiGroup site
     * @apiParam {Number}  cid 分类 ID
     * @apiParam {Number}  [page=1] 当前页数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [{
     *         "id": 840191,
     *         "create_time": 1519225067,
     *         "duration": 459541,
     *         "soundstr": "【有声漫画】《肆拾杂货店》第一期 原画：所长别开枪是我+wuli爸爸爸爸",
     *         "view_count": 5842,
     *         "comment_count": 145,
     *         "comments_count": 67,
     *         "sub_comments_count": 0,
     *         "all_comments": 212,
     *         "comments_num": 67,
     *         "front_cover": "https://static.missevan.com/coversmini/201802/21/e48293362ab64b663145743.png",
     *         "liked": 0,
     *         "followed": 0,
     *         "authenticated": 0,
     *         "confirm": 0,
     *         "iconurl": "https://static.missevan.com/profile/icon01.png"
     *       }]
     *     }
     */
    public function actionCatalogChange(int $cid, $page = 1)
    {
        $catalog_sounds_group = Json::decode(Yii::$app->redis->get(KEY_HOMEPAGE_CATALOG_TOP_SOUNDS)) ?: [];
        if (!$catalog_sounds_group || !isset($catalog_sounds_group[$cid])) {
            throw new HttpException(404, '分类不存在', 200110201);
        }
        $sound_ids_recommended = $catalog_sounds_group[$cid];
        $offset = $page % 5 * self::CATALOG_SOUNDS_NUM;
        $ids = array_slice($sound_ids_recommended, $offset, self::CATALOG_SOUNDS_NUM);
        // $ids 为已经过审及过滤擦边球处理后的结果
        $sounds = MSound::find()
            ->select('id, create_time, duration, cover_image, soundstr, view_count, comment_count,
                comments_count, sub_comments_count, checked')
            ->allByColumnValues('id', $ids);
        // 过滤掉不下发的字段
        $sounds = array_map(function ($sound) {
            unset($sound->checked, $sound->cover_image, $sound->need_pay, $sound->price);
            return $sound;
        }, $sounds);
        return $sounds;
    }

    /**
     * @api {post} /site/custom-catalog 获取 GET 或设置 POST 首页自定义栏目及所有栏目
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/custom-catalog
     * @apiSampleRequest /site/custom-catalog
     * @apiDescription  /mobile/personOperation/customClassification 自定义模块 params:{String} token 用户token , {String} [classfications] 分类ids，使用逗号分隔
     * /mobile/site/newHomepage
     *
     * @apiVersion 0.1.0
     * @apiName custom-catalog
     * @apiGroup site
     *
     * @apiParam {String} token 用户token
     * @apiParam {String} [classfications] 分类ids，使用逗号分隔
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     * POST:
     *     {
     *       "success": true,
     *       "code": 200,
     *      "info":
     *          ['5','6']
     *     }
     * GET:
     *
     */

    public function actionCustomCatalog()
    {
        $user_id = Yii::$app->user->id;
        if (!$user_id) {
            return [];
        }

        $redis = Yii::$app->redis;
        $key = $redis->generateKey(CUSTOM, $user_id);

        // 自定义模块
        if ($classfications = Yii::$app->request->post('classfications')) {
            if ($classfications) {
                $classfication_arrs = explode(',', $classfications);

                $catalogs = Catalog::findAll(['id' => $classfication_arrs, 'parent_id' => 1]);

                $ids = array_column($catalogs, 'id');

                $ids = array_values(array_intersect($classfication_arrs, $ids));
                $ids_json = json_encode($ids);
                $redis->set($key, $ids_json);
            }
        }

        $ids = isset($ids) ? $ids : json_decode($redis->get($key));
        // WORKAROUND: 华为渠道且同时也是黑名单渠道包过滤【声音恋人模块】
        if (!is_null($ids) && Yii::$app->equip->getChannel() === Equipment::CHANNEL_HUAWEI && Blacklist::model()->isBlackChannel()) {
            if (in_array(Catalog::CATALOG_ID_SOUND_LOVER, $ids)) {
                $ids = array_values(array_filter($ids, function ($id) {
                    return (int)$id !== Catalog::CATALOG_ID_SOUND_LOVER;
                }));
            }
        }

        return $ids;
    }

    /**
     * @api {get} /site/catalog-root 分类根信息
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/catalog-root
     * @apiSampleRequest /site/catalog-root
     * @apiDescription WARNING: dark 参数将在 Android 4.5.0 及 IOS 5.4.0 版本后废弃
     *
     * @apiVersion 0.1.0
     * @apiName catalog-root
     * @apiGroup site
     *
     * @apiParam {number=0,1} [dark=0] 白天或黑夜样式（0 为白天，1 为黑夜）
     * @apiParam {number=0,1} [extra=1] 是否显示除分类外的小图标（如：直播、活动、新闻等）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "title": "有声漫画",
     *           "id": 46,  // 分类 ID
     *           "type": 0,  // 普通类型
     *           "icon": "http://static.missevan.com/app/catalog/icon/46.png",  // 新版日间图标
     *           "dark_icon": "http://static.missevan.com/app/catalog/icon/46-dark.png",  // 新版黑夜图标
     *           "url": "missevan://catalog/drama/96"
     *         },
     *         {
     *           "title": "铃声",
     *           "id": 65,  // 分类 ID
     *           "type": 0,  // 普通类型
     *           "icon": "http://static.missevan.com/app/catalog/icon/56.png",  // 新版日间图标
     *           "dark_icon": "http://static.missevan.com/app/catalog/icon/56-dark.png",  // 新版黑夜图标
     *           "url": "missevan://catalog/sound/65"
     *         },
     *         {
     *           "title": "直播",
     *           "id": "missevan://live",
     *           "type": 1,  // 特殊类型（如直播、活动、新闻）
     *           "icon": "http://static.missevan.com/coversmini/202004/05/465654d7d54f9734a234548.png",  // 新版日间图标
     *           "dark_icon": "http://static.missevan.com/coversmini/201801/06/04e40337766d006902.png",  // 新版黑夜图标
     *           "url": "missevan://live"
     *         }
     *       ]
     *     }
     *
     */
    public function actionCatalogRoot(int $extra = 1)
    {
        $catalogs = Catalog::find()
            ->select('id, catalog_name AS name')
            ->where('parent_id = 1 and status_is = "Y"')
            ->orderBy('sort_order DESC')
            ->asArray()->all();
        if ($extra) {
            $icons = unserialize(Yii::$app->redis->get(CATALOG_ICONS)) ?: [];
            $icons = array_filter($icons, function ($value) {
                return !(key_exists('disabled', $value) && $value['disabled']);
            });
            $info = array_merge($catalogs, $icons);
        } else {
            $info = $catalogs;
        }
        $is_chinamainland = MUtils::isChinaMainland();
        $is_pgc_version = !Equipment::isAppOlderThan('4.4.6', '5.3.6');
        $is_ugc_version = !Equipment::isAppOlderThan('4.4.7', '5.3.7');
        $is_blocked_channel = Blacklist::model()->isBlackChannel();
        $channel = Yii::$app->equip->getChannel();
        $info = array_map(function ($icon) use ($is_chinamainland, $is_pgc_version, $is_ugc_version, $is_blocked_channel, $channel) {
            $type = $icon['path'] ?? null;
            // 非中国大陆地区不显示日抓分类
            if (!$type && Catalog::CATALOG_ID_JAPAN_DRAMA === (int)$icon['id'] && !$is_chinamainland) {
                return null;
            }
            if ($is_blocked_channel && Blacklist::isChannelRecommendModuleBlocked($channel, $icon['name'])) {
                return null;
            }
            $elem = [
                'title' => $icon['name'],
                'type' => $type ? 1 : 0,
                'id' => $type ? $icon['path'] : (int)$icon['id'],
            ];
            $elem['icon'] = $type ? $icon['icon'] : Catalog::getIcon($icon['id'], self::ICON_NORMAL);
            $elem['dark_icon'] = $type ? $icon['icon_night'] : Catalog::getIcon($icon['id'], self::ICON_DARK);
            if (Equipment::isAppOlderThan('4.5.0', '5.4.0')) {
                // NOTICE: 放循环内，方便后面删除
                $dark = (int)Yii::$app->request->get('dark');
                $elem['pic'] = $dark ? $elem['dark_icon'] : $elem['icon'];
                unset($elem['icon'], $elem['dark_icon']);
            }
            if ($is_pgc_version) {
                // WORKAROUND: 若为加了 PGC 分区的版本，则跳转地址使用 url 标识
                $elem['url'] = $type ? $icon['path'] : '';
                if (key_exists($elem['id'], Drama::SOUND_TO_DRAMA_CATALOG_IDS)) {
                    // 若为 PGC 分区，则添加跳转地址
                    $drama_catalog_id = Drama::getCatalogBySoundCatalog($elem['id']);
                    $elem['url'] = "missevan://catalog/drama/{$drama_catalog_id}";
                }
            }
            if ($is_ugc_version && in_array($elem['id'], Catalog::UGC_CATALOG_IDS)) {
                // WORKAROUND: 若为加了 UGC 分区的版本，则跳转地址使用 url 标识
                $elem['url'] = "missevan://catalog/sound/{$elem['id']}";
            }
            return $elem;
        }, $info);
        $icons = array_values(array_filter($info));
        // WORKAROUND: Android 5.2.9 及其以下分类页图标只支持固定直播、活动及新闻地址，新版本需支持其它地址
        if (Equipment::isAppOlderThan(null, '5.3.0')) {
            foreach ($icons as &$icon) {
                if ($icon['type'] && $icon['id'] === 'missevan://topic') {
                    $icon['id'] = Yii::$app->params['domainMobileWeb'] . '/news';
                    $icon['title'] = '新闻';

                    // NOTICE: 放循环内，方便后面删除
                    $dark = (int)Yii::$app->request->get('dark');
                    $icon['pic'] = StorageClient::getFileUrl($dark ? Catalog::ICON_NEWS_DARK : Catalog::ICON_NEWS);
                }
            }
        }

        return $icons;
    }

    /**
     * @api {get} /site/catalog-homepage 根据分类 ID 获取分类数据
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/catalog-homepage
     * @apiSampleRequest /site/catalog-homepage
     * @apiDescription 根据分类 ID 获取分类数据
     *
     * @apiVersion 0.1.0
     * @apiName catalog-homepage
     * @apiGroup site
     *
     * @apiParam {Number} cid 分类 ID
     * @apiParam {Number} [tag_id=0] 标签 ID
     * @apiParam {Number} [limit=6]
     * @apiParam {number=0,1} [dark=0]
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "banner": [{
     *           "pic_app": "http://static.missevan.com/mimages/201803/20/9b3498c2aafa9bb69070505.png",
     *           "title": "猫妖的诱惑 第8集.就放纵这一次",
     *           "pic": "http://static.missevan.com/mimages/201803/20/9b3c229aafa9bb69070505.png",
     *           "url": "http://www.missevan.com/sound/893074?webview=1"
     *          }],
     *         "sounds": [{
     *           "name": "热门推荐",
     *           "item": [{
     *             "id": 1,
     *             "soundstr": "阿嘞,一个人都不在呢",
     *             "comment_count": 68,
     *             "comments_count": 111,
     *             "sub_comments_count": 107,
     *             "cover_image": "201701/24/92610e061e2c05937f8c823f93857c57091415.png",
     *             "view_count": 42411,
     *             "all_comments": 286,
     *             "comments_num": 218,
     *             "front_cover": "http://static.missevan.com/coversmini/201701/24/92610c57091415.png",
     *             "liked": 0,
     *             "collected": 0,
     *             "followed": 0,
     *             "authenticated": 0,
     *             "confirm": 0,
     *             "iconurl": "http://static.missevan.com/profile/icon01.png",
     *             "video": true
     *           }],
     *           "icon": "http://static.missevan.com/profile/icon01.png"
     *         },
     *         "tag": [{
     *           "id": 4,
     *           "name": "广播剧"
     *         }]
     *       }
     *     }
     *
     */
    public function actionCatalogHomepage(int $cid, int $tag_id = 0, int $limit = 6, int $dark = 0)
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }
        if ($tag_id && !MTag::findOne($tag_id)) {
            throw new HttpException(404, '标签不存在', 200150001);
        }

        $catalog = Catalog::getAllCatalog($cid);
        $banner = $catalog['banner'];

        $items = Catalog::getCatalogHomepageDynamic($tag_id, $catalog, $limit, $dark);
        return [
            'banner' => $banner,
            'sounds' => $items,
            'tag' => $catalog['tag'],
        ];
    }

    /**
     * @api {get} /site/ringing-catalog 获取闹铃分类
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/ringing-catalog
     * @apiSampleRequest /site/ringing-catalog
     * @apiDescription /mobile/site/RingingCatalog 获取闹铃分类
     *
     * @apiVersion 0.1.0
     * @apiName ringing-catalog
     * @apiGroup site
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info": [ { id: 1, name: '女性向' }, { id: 2, name: '男性向' } ] }
     *     }
     *
     */
    public function actionRingingCatalog()
    {
        //@todo
        $catalog = [
            ['id' => 1, 'name' => '女性向'],
            ['id' => 2, 'name' => '男性向'],
        ];
        return $catalog;
    }

    /**
     * @api {get} /site/ringing-sound 获取闹铃单音
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/ringing-sound
     * @apiSampleRequest /site/ringing-sound
     * @apiDescription /mobile/site/RingingSounds 闹铃音
     *
     * @apiVersion 0.1.0
     * @apiName ringing-sound
     * @apiGroup site
     *
     * @apiParam {Number = 1,2} [id=1] 闹铃分类id
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info": [{
     *         "id": 23,
     *         "soundstr": "可以吗?",
     *         "user_id": 1,
     *         "username": "昵称_Jyj78Nmo",
     *         "front_cover": "http://static-test.maoercdn.com/coversmini/201701/24/test.png",
     *         "iconurl": "http://static-test.maoercdn.com/profile/201506/15/test.png",
     *         "soundurl": "https://sound-test-ks.cdn.missevan.com/aod/202205/09/test.m4a",  // iOS >= 4.9.6, Android >= 5.8.0 不再下发 soundurl soundurl_32 soundurl_64 soundurl_128
     *         "soundurl_32": "https://sound-test-ks.cdn.missevan.com/aod/202205/09/test-192k.m4a",
     *         "soundurl_64": "https://sound-test-ks.cdn.missevan.com/aod/202205/09/test.m4a",
     *         "soundurl_128": "https://sound-test-ks.cdn.missevan.com/aod/202205/09/test-128k.m4a"
     *       }]
     *     }
     *
     */
    public function actionRingingSound($id = 1)
    {
        if ($id == 1) {
            $key = RINGING_1;
        } else {
            $key = RINGING_2;
        }
        $sound_ids = Yii::$app->redis->get($key);
        $sound_ids = explode(',', $sound_ids);
        $select = 'id, soundstr, user_id, username, cover_image';
        // WORKAROUND: iOS 低于 4.9.6, Android 低于 5.8.0 时需要查询音频播放地址
        $old_version = Equipment::isAppOlderThan('4.9.6', '5.8.0');
        if ($old_version) {
            $select .= ', soundurl_32, soundurl_64, soundurl_128';
        }
        $sounds = MSound::find()->select($select)->allByColumnValues('id', $sound_ids);
        if ($sounds) {
            $user_ids = array_column($sounds, 'user_id');
            $users = Mowangskuser::findAll(['id' => $user_ids]);
            $users = array_column($users, null, 'id');
            $sounds = array_map(function ($sound) use ($users, $old_version) {
                $user = $users[$sound->user_id] ?? null;
                $return = [
                    'id' => $sound->id,
                    'soundstr' => $sound->soundstr,
                    'user_id' => $sound->user_id,
                    'username' => $user->username ?? $sound->username,
                    'iconurl' => $user->iconurl ?? '',
                    'front_cover' => $sound->front_cover,
                ];
                // WORKAROUND: iOS 低于 4.9.6, Android 低于 5.8.0 时返回音频播放地址
                if ($old_version) {
                    $return['soundurl'] = $sound->soundurl;
                    $return['soundurl_32'] = $sound->soundurl_32;
                    $return['soundurl_64'] = $sound->soundurl_64;
                    $return['soundurl_128'] = $sound->soundurl_128;
                }
                return $return;
            }, $sounds);
        }

        return $sounds;
    }

    public function actionAfterPassed()
    {
        // TODO: Implement actionAfterPassed() method.
    }

    /**
     * @api {get} /site/all-classics 全部经典必听分类 (20171011)
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/all-classics
     * @apiSampleRequest /site/all-classics
     * @apiDescription /mobile/site/AllClassics
     *
     * @apiVersion 0.1.0
     * @apiName all-classics
     * @apiGroup site
     *
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info":
     *          [
     *              [
     *                  name: 百合,
     *                  alias: '', //别名
     *                  cid:1 //分类id
     *                  data: [
     *                      {
     *                          id:44,
     *                          type:1, //1为剧集，2为音单
     *                          drama_id:44,
     *                          name:光
     *                          cover:
     *                          abstract:
     *                          view_count:23
     *                          sound_count:4
     *                      }
     *                  ],
     *              ],
     *          ]
     *     }
     */
    public function actionAllClassics()
    {
        $user_id = Yii::$app->user->id;
        /****若缓存中有数据则取出并返回*****/
        $memcache = Yii::$app->memcache;
        $catalog_all_data = unserialize($memcache->get(CLASSIC_CATALOG_ALL_DATA)) ?: [];
        if ($catalog_all_data) {
            return $catalog_all_data;
        }
        /*****************************/

        $redis = Yii::$app->redis;
        // 从 redis 取出分类数据
        $catalog_data = $redis->get(CLASSIC_CATALOG_INDEX);
        // 反序列化该分类数据
        $catalog_data = unserialize($catalog_data) ?: [];
        // 声明要返回的数据数组
        $return_data = [];

        /*获取对应数据并组合*/
        foreach ($catalog_data as $cata_item_key => $cata_item) {
            // 分类的 ID
            $catalog_data[$cata_item_key]['cid'] = $cata_item_key + 1;
            $catalog_data[$cata_item_key]['data'] = [];

            // 分割单音 ID 字符串为数组
            $album_id_arr = array_filter(explode(',', $cata_item['album']));
            // 分割剧集 ID 字符串为数组，如 [26, 27]
            $drama_id_arr = array_filter(array_map('intval', explode(',', $cata_item['drama'])));
            $drama_abstract_arr = explode('|', trim(Html::decode($cata_item['drama_abstracts'])));
            $album_abstract_arr = explode('|', trim(Html::decode($cata_item['album_abstracts'])));
            if ($album_id_arr) {
                $album_abstract_arr_new = [];
                foreach ($album_abstract_arr as $index => $abstract) {//使简介与音单id对应
                    $album_abstract_arr_new[$album_id_arr[$index]] = $abstract;
                }
            }

            /*处理剧集*/
            foreach ($drama_id_arr as $k => $drama_id) {
                // 获取数据
                $client = new Client;
                $res = $client->request('GET', DRAMA_INFO_API, ['query' => ['drama_id' => $drama_id, 'user_id' => $user_id]]);
                $drama_item_data = $res->getStatusCode() == 200 ? json_decode($res->getBody(), true) : [];
                if (!$drama_item_data) {
                    continue;
                }

                $drama_item_data['id'] = $drama_item_data['drama_id'];
                $drama_item_data['type'] = 1;//1为剧集（2为音单）
                $drama_item_data['abstract'] = isset($drama_abstract_arr[$k]) ? $drama_abstract_arr[$k] : '';//后台输入的简介
                $catalog_data[$cata_item_key]['data'][] = $drama_item_data;
            }

            /*处理音单*/
            // 查询对应的音单 ID 数据
            $album_arr = MAlbum::findAll(['id' => $album_id_arr]);
            foreach ($album_arr as $key => $album_item) {
                // 获取音单的播放量（该音单下的所有单音播放量总和）
                $album_view_count = MSound::find()
                    ->alias('s')
                    ->select('SUM(s.view_count) view_count_sum')
                    ->innerJoin('m_sound_album_map a', 'a.sound_id = s.id')
                    ->where(['a.album_id' => $album_item->id])
                    ->asArray()
                    ->all();

                $album_view_count = (int)$album_view_count[0]['view_count_sum'];
                // 组合数据
                $catalog_data[$cata_item_key]['data'][] = [
                    'album_id' => $album_item->id,
                    'id' => $album_item->id,
                    'type' => 2, //1为剧集（2为音单）
                    'title' => $album_item->title,
                    'cover' => $album_item->front_cover,
                    'abstract' => isset($album_abstract_arr_new[$album_item->id]) ? $album_abstract_arr_new[$album_item->id] : '',
                    'view_count' => $album_view_count,
                    'sound_count' => $album_item->music_count,
                ];
            }
            // 删除冗余字段
            unset($catalog_data[$cata_item_key]['album'], $catalog_data[$cata_item_key]['drama']);
            unset($catalog_data[$cata_item_key]['album_abstracts'], $catalog_data[$cata_item_key]['drama_abstracts']);
            // 形成随机的效果
            shuffle($catalog_data[$cata_item_key]['data']);
            $catalog_data[$cata_item_key]['data'] = array_slice($catalog_data[$cata_item_key]['data'], 0, 4);
            $return_data[] = $catalog_data[$cata_item_key];
        }

        $memcache->set(CLASSIC_CATALOG_ALL_DATA, serialize($return_data), HALF_HOUR);
        return $return_data;
    }

    /**
     * @api {get} /site/classic-details 所点击经典必听分类中的全部内容 (20171011)
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/classic-details
     * @apiSampleRequest /site/classic-details
     * @apiDescription /mobile/site/ClassicDetails
     *
     * @apiVersion 0.1.0
     * @apiName classic
     * @apiGroup site
     *
     * @apiParam {String} cid 分类id
     * @apiParam {Number} p 当前页
     * @apiParam {Number} pagesize 每页显示多少条分类中的剧集/音单数据
     *
     * @apiSuccess {String} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info":
     *              [
     *                  Datas: [
     *                      {
     *                          id:44,
     *                          type:1, //1为剧集，2为音单
     *                          drama_id:44,
     *                          name:光
     *                          cover:
     *                          abstract:
     *                          view_count:23
     *                          sound_count:4
     *                      }
     *                ],
     *              pagination:{
     *                  p:1
     *                  count:
     *                  maxpage
     *                  pagesize
     *             }
     *          ]
     *     }
     */
    public function actionClassicDetails()
    {
        $classic_id = intval(Yii::$app->request->get('cid'));
        if (!$classic_id) {
            throw new HttpException(400, '分类id不能为空', 201010001);
        }

        /****** 缓存若有数据则取出并返回******/
        $memcache = Yii::$app->memcache;
        $classic_name_all_data = unserialize($memcache->get(CLASSIC_CATALOG_DETAILS . $classic_id)) ?: [];
        if ($classic_name_all_data) {
            $cata_item = $classic_name_all_data;
            //分页
            $page_size = Yii::$app->request->get('pagesize', 10);//每个多少条数据
            $count = count($cata_item);//分类总数
            $maxpage = ceil($count / $page_size); //计算总页面数
            $p = Yii::$app->request->get('p', 1);//当前页
            $data = $this->array_pagination($page_size, $p, $cata_item);//数据分页
            $cata_item = $data;
            return [
                'Datas' => $cata_item,
                'pagination' => ['p' => $p, 'count' => $count, 'pagesize' => $page_size, 'maxpage' => $maxpage],
            ];
        }
        /********************/

        $redis = Yii::$app->redis;
        $catalog_data = unserialize($redis->get(CLASSIC_CATALOG_INDEX)) ?: [];
        $cata_item = [];//声明要返回的数组
        foreach ($catalog_data as $k => $v) {//找出所点击的经典分类
            $catalog_data[$k]['cid'] = $k + 1;
            if ($classic_id == $k + 1) {
                $cata_item = $v;
            }
        }
        if (!$cata_item) {
            throw new HttpException(404, '该分类不存在', 200510001);
        }

        $drama_id_arr = array_filter(array_map('intval', explode(',', $cata_item['drama'])));//分割剧集id字符串为数组
        $album_id_arr = array_filter(explode(',', $cata_item['album']));//分割单音id字符串为数组
        $drama_abstract_arr = explode('|', trim(Html::decode($cata_item['drama_abstracts'])));
        $album_abstract_arr = explode('|', trim(Html::decode($cata_item['album_abstracts'])));
        if ($album_id_arr) {
            $album_abstract_arr_new = [];
            foreach ($album_abstract_arr as $index => $abstract) {//使简介与音单id对应
                $album_abstract_arr_new[$album_id_arr[$index]] = $abstract;
            }
        }

        //处理剧集
        foreach ($drama_id_arr as $k => $drama_id) {
            $client = new Client;//获取数据
            $res = $client->request('GET', DRAMA_INFO_API, ['query' => ['drama_id' => $drama_id]]);
            $drama_item_data = json_decode($res->getBody(), true);
            $drama_item_data['id'] = $drama_item_data['drama_id'];//组合数据
            $drama_item_data['type'] = 1;
            $drama_item_data['abstract'] = isset($drama_abstract_arr[$k]) ? $drama_abstract_arr[$k] : '';
            $cata_item[] = $drama_item_data;
        }

        /*处理音单*/
        $album_arr = MAlbum::findAll(['id' => $album_id_arr]);//查询对应的音单id数据
        foreach ($album_arr as $key => $album_item) {
            //获取音单的播放量（该音单下的所有单音播放量总和）
            $sound_album_map = MSoundAlbumMap::find()->where(['album_id' => $album_item->id])->all();//查询音单-单音中间表
            $sound_ids = array_column($sound_album_map, 'sound_id');//获取音单下单音集合
            $sounds = MSound::findAll(['id' => $sound_ids]);//查询单音表
            $sounds_view_count = array_column($sounds, 'view_count');//获取各个单音播放量集合
            $album_view_count = 0;
            foreach ($sounds_view_count as $sound_view_count) {
                $album_view_count += $sound_view_count;//得到音单的总播放量
            }

            //组合数据
            $cata_item[] = [
                'id' => $album_item->id,
                'type' => 2,
                'album_id' => $album_item->id,
                'title' => $album_item->title,
                'cover' => $album_item->front_cover,
                'abstract' => isset($album_abstract_arr_new[$album_item->id]) ? $album_abstract_arr_new[$album_item->id] : '',
                'view_count' => $album_view_count,
                'sound_count' => $album_item->music_count,
            ];
        }

        //剔除冗余字段
        unset($cata_item['name'], $cata_item['alias'], $cata_item['album'], $cata_item['drama']);
        unset($cata_item['drama_abstracts'], $cata_item['album_abstracts']);
        //将分类数据存入缓存
        $memcache->set(CLASSIC_CATALOG_DETAILS . $classic_id, serialize($cata_item), HALF_HOUR);

        //分页
        $page_size = Yii::$app->request->get('pagesize', 10);//每个多少条数据
        $count = count($cata_item);//分类总数
        $maxpage = ceil($count / $page_size); //计算总页面数
        $p = Yii::$app->request->get('p', 1);//当前页
        $data = $this->array_pagination($page_size, $p, $cata_item);//数据分页
        $cata_item = $data;
        shuffle($cata_item);
        return [
            'Datas' => $cata_item,
            'pagination' => ['p' => $p, 'count' => $count, 'pagesize' => $page_size, 'maxpage' => $maxpage],
        ];
    }

    private function array_pagination($count, $page, $array)
    {
        $page = (empty($page)) ? '1' : $page; #判断当前页面是否为空 如果为空就表示为第一页面
        $start = ($page - 1) * $count; #计算每次分页的开始位置
        $totals = count($array);
        $countpage = ceil($totals / $count); #计算总页面数
        $pagedata = array_slice($array, $start, $count);
        return $pagedata;  #返回查询数据
    }

    /**
     * 根据限制策略，过滤 banners
     */
    private static function limitBanners(array $links)
    {
        $found = false;
        foreach ($links as $key => $link) {
            $limit = $link['more']['limit'] ?? 0;
            if ($limit) {
                $found = true;
                break;
            }
        }
        if (!$found) {
            return $links;
        }

        $limit_location = MUtils::isLimitLocation();
        foreach ($links as $key => $link) {
            $limit = $link['more']['limit'] ?? 0;
            if ($limit === 1) {
                // limit: 1 限制只在特定地区露出
                if (!$limit_location) {
                    unset($links[$key]);
                }
            }
        }
        $links = array_values($links);
        return $links;
    }

    /**
     * 根据用户收听进度返回当前用户专属 banner 链接
     */
    private static function userBanners(array $links)
    {
        $user_id = (int)Yii::$app->user->id;
        $get_lastviewed = [];
        foreach ($links as $key => $link) {
            if ($link['element_type'] === MRecommendedElements::ELEMENT_TYPE_DRAMA) {
                $lastviewed = $link['more']['lastviewed'] ?? 0;
                if ($lastviewed) {
                    $get_lastviewed[$key] = $link['element_id'];
                }
            }
        }
        if (!$get_lastviewed) {
            return $links;
        }
        sort($get_lastviewed);
        $get_lastviewed_drama_ids = array_values($get_lastviewed);
        $memcache = Yii::$app->memcache;
        $key = MUtils::generateCacheKey(KEY_USER_DRAMA_EPISODE, $user_id, Json::encode($get_lastviewed_drama_ids));
        $dramas = $memcache->get($key);
        if ($dramas !== false) {
            $dramas = Json::decode($dramas);
        } else {
            try {
                $dramas = Yii::$app->serviceRpc->getUserLastviewed($get_lastviewed_drama_ids, $user_id);
                $dramas = $dramas['dramas'];
                $duration = 2 * ONE_MINUTE;
                $memcache->set($key, Json::encode($dramas), $duration);
            } catch (Exception $e) {
                Yii::error('获取用户收听首页 banner 中剧集进度出错: ' . $e->getMessage(), __METHOD__);
                return $links;
            }
        }
        $checked_sound_ids = array_column($dramas, 'sound_id');
        $has_video_sound_ids = SoundVideo::getVideoSoundIds($checked_sound_ids);
        foreach ($links as $key => $link) {
            if ($link['element_type'] === MRecommendedElements::ELEMENT_TYPE_DRAMA && in_array($link['element_id'], $get_lastviewed_drama_ids)) {
                $sound_id = $dramas[$link['element_id']]['sound_id'] ?? 0;
                if ($sound_id) {
                    $links[$key]['url'] = Yii::$app->params['domainMissevan'] . '/sound/' . $sound_id;
                    if (in_array($sound_id, $has_video_sound_ids)) {
                        $links[$key]['url'] .= '?video=1';
                    }
                } else {
                    // 需要跳转到音频播放页的剧集未过审或剧集下无过审单集时，不展示对应 banner
                    unset($links[$key]);
                }
            }
        }
        $links = array_values($links);
        return $links;
    }

    private static function banners()
    {
        $equipment = Yii::$app->equip;
        $client = Equipment::iOS;
        if ($equipment->isAndroidOrHarmonyOS()) {
            $client = Equipment::Android;
        }
        $banner_key = MUtils::generateCacheKey(KEY_APP_HOMEPAGE_BANNER_CLIENT, $client);
        $memcache = Yii::$app->memcache;
        if (!($data = $memcache->get($banner_key))) {
            $links = MRecommendedElements::getOnlineOrMaxEndTimeElements($client,
                MRecommendedElements::MODULE_TYPE_BANNER, true);
            if (!empty($links)) {
                // 获取 App 版头图缓存时长
                $duration = MRecommendedElements::getDuration($client,
                    MRecommendedElements::MODULE_TYPE_BANNER,
                    MRecommendedElements::MODULE_ID_NONE, $links);
            } else {
                Yii::error('APP 首页 Banner 全部下线', __METHOD__);
                // Banner 都自动下线时，取最后自动下线的一张图展示，若最后自动下线有多张，取排序靠前的一张
                $links = MRecommendedElements::getOnlineOrMaxEndTimeElements($client,
                    MRecommendedElements::MODULE_TYPE_BANNER);
                $duration = HALF_MINUTE;
            }
            foreach ($links as $key => $link) {
                $links[$key]['trace'] = Json::encode(['original_url' => $link['url']]);
            }
            $data = Json::encode($links);
            // 设置缓存和过期时间，默认过期时间加 1s，错开时间上线
            $memcache->set($banner_key, $data, $duration + 1);
        } else {
            $links = Json::decode($data);
        }
        $links = self::limitBanners($links);
        $links = self::userBanners($links);
        return array_map(function ($link) {
            // 仅返回所需字段
            return [
                'url' => MUtils::getUsableAppLink($link['url']),
                'pic' => $link['pic'],
                'trace' => $link['trace'],
            ];
        }, $links);

    }

    /**
     * 获取 App 首页轮播通栏
     *
     * @return array|mixed
     * @todo 目前 iOS 与 Android 客户端拿的图片不同，若之后不需要单独设置，可优化此处代码
     */
    private function getExtraBanners()
    {
        $client = Equipment::iOS;
        if (Yii::$app->equip->isAndroidOrHarmonyOS()) {
            $client = Equipment::Android;
        }
        $banner_key = MUtils::generateCacheKey(KEY_APP_HOMEPAGE_EXTRA_BANNER_CLIENT, $client);
        $memcache = Yii::$app->memcache;
        if (!($data = $memcache->get($banner_key))) {
            $links = MRecommendedElements::getOnlineElements($client);
            if (!empty($links)) {
                // 获取 App 轮播通栏缓存时长
                $duration = MRecommendedElements::getDuration($client,
                    MRecommendedElements::MODULE_TYPE_EXTRA_BANNER,
                    MRecommendedElements::MODULE_POSITION_EXTRA_BANNER, $links);
            } else {
                // 获取最近要上线的 App 轮播通栏图
                $start_time = MRecommendedElements::getNextElementStartTime($client,
                    MRecommendedElements::MODULE_TYPE_EXTRA_BANNER,
                    MRecommendedElements::MODULE_POSITION_EXTRA_BANNER);
                if ($start_time) {
                    $duration = $start_time - $_SERVER['REQUEST_TIME'];
                    if ($duration <= 0) {
                        // 避免设置负数值，负数值 MemCache Set 会转换成永久不过期
                        // 设置 1 秒的过期时间，避免在缓存失效的那个时间点产生调用峰值的问题
                        $duration = 1;
                    } elseif ($duration > HALF_MINUTE) {
                        // FIXME: App 首页轮播通栏定时上线不及时，之后需要修复
                        $duration = HALF_MINUTE;
                    }
                } else {
                    $duration = HALF_MINUTE;
                }
            }
            // 设置缓存和过期时间，默认过期时间加 1s，错开时间上线
            $memcache->set($banner_key, Json::encode($links ?: new \stdClass()), $duration + 1);
        } else {
            $links = Json::decode($data);
        }

        $user_id = (int)Yii::$app->user->id;
        $live_banners = Yii::$app->liveRpc->getLiveExtraBanners(MRecommendedElements::MODULE_POSITION_EXTRA_BANNER, $user_id);
        if (!$links && !$live_banners) {
            // 当无轮播通栏时，直接返回一个空对象
            return new \stdClass();
        }

        $result = [];
        foreach (MRecommendedElements::MODULE_POSITION_EXTRA_BANNER as $position) {
            // 直播通栏放在其它通栏前
            $banners = array_merge($live_banners[$position] ?? [], $links[$position] ?? []);
            if (empty($banners)) {
                continue;
            }
            $result[$position] = array_map(function ($item) {
                return [
                    'url' => MUtils::getUsableAppLink($item['url']),
                    'pic' => $item['pic'],
                ];
            }, $banners);
        }
        return $result;
    }

    private function weeklyDrama($length)
    {
        $WEEK_DAYS = 7;
        if (0 > $length || $WEEK_DAYS < $length) {
            throw new HttpException(400, '参数不合法');
        }
        $dramas = Drama::getWeeklyDrama();
        if (empty($dramas)) {
            return [];
        }
        $days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        $dramas = array_map(function ($item) use ($days) {
            $item['day'] = $days[$item['date']];
            return $item;
        }, $dramas);
        return array_slice($dramas, 0, $length);
    }

    /**
     * @api {get} /site/get-top 版头图及首页精品周更
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/get-top
     * @apiSampleRequest site/get-top
     *
     * @apiVersion 0.1.0
     * @apiName get-top
     * @apiGroup site
     *
     * @apiParam {Number} [page_size=2] 精品周更的个数
     * @apiParam {number=1} [type] Android 5.7.3 以下版本，支持隐藏精品周更模块时，传递 type 为 1
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "search_words": [
     *           {
     *             "word": "杀破狼",
     *             "url": "missevan://drama/9888"
     *           },
     *           {
     *             "word": "运势语音",
     *             "url": "missevan://omikuji/4/draw/1"
     *           },
     *           {
     *             "word": "xx活动",
     *             "url": "missevan://mevent/4"
     *           },
     *           {
     *             "word": "热搜词",
     *             "url": ""
     *           }
     *         ],
     *         "banners": [
     *           {
     *             "url": "http://www.missevan.com/sound/665152",  // 跳转地址
     *             "pic": "http://test.missevan.com/mimages/201712/08/ffe84a1e8da7babac115617.jpg",
     *             "trace": "{\"original_url\":\"https://www.missevan.com/mdrama/71321\"}"  // 用于埋点上报的字段，客户端透传该值
     *           },
     *           {
     *             "url": "https://www.missevan.com/sound/223736?webview=1",
     *             "pic": "http://test.missevan.com/mimages/201712/07/ffd8b9dd3498cd100453.jpg",
     *             "trace": "{\"original_url\":\"https://www.missevan.com/mdrama/71321\"}"
     *           }
     *         ],
     *         "extra_banners": {
     *           "1": [
     *             {
     *               "url": "http://www.missevan/voice/1",
     *               "pic": "http://static.missevan.com/dramacoversmini/201604dd9566b6083011.jpg"
     *             },
     *             {
     *               "url": "http://www.missevan/voice/1",
     *               "pic": "http://static.missevan.com/dramacoversmini/201604dd9566b6083011.jpg"
     *             }
     *           ],
     *           "2": [
     *             {
     *               "url": "http://www.missevan/voice/1",
     *               "pic": "http://static.missevan.com/dramacoversmini/201604/15/996083d011.jpg"
     *             },
     *             {
     *               "url": "http://www.missevan/voice/1",
     *               "pic": "http://static.missevan.com/dramacoversmini/201604/15/99646083011.jpg"
     *             }
     *           ],
     *         },
     *         "weeklydrama": [  // iOS >= 4.8.6、安卓 >= 5.7.3，未返回该字段时隐藏此模块
     *           [
     *             {
     *               "id": 29,
     *               "name": "奋斗在盛唐",
     *               "cover": "http://static.missevan.com/mimages/201712/08/ffe84a1e8da7babac115617.jpg",
     *               "author": "柴少鸿工作室",
     *               "catalog": "古代穿越",
     *               "pay_type": 0,
     *               "day": "周三"
     *             }
     *           ],
     *           [
     *             {
     *               "id": 30,
     *               "name": "吸血鬼与猎人",
     *               "cover": "http://static.missevan.com/mimages/201712/07/ffd8b9dd3498cd100453.jpg",
     *               "author": "马猴烧酒工作室",
     *               "catalog": "纯爱",
     *               "pay_type": 2,
     *               "day": "周四"
     *             }
     *           ]
     *         ],
     *         "reward_rank": {  // 未返回该字段时隐藏此模块
     *           "title": "广播剧打赏周榜",
     *           "rank": [
     *             {
     *               "id": 30,
     *               "name": "吸血鬼与猎人",
     *               "cover": "http://static.test.com/mimages/201712/07/test.jpg",
     *               "abstract": "剧集简介",
     *               "author": "马猴烧酒工作室",
     *               "integrity": 1,
     *               "pay_type": 2
     *             }
     *           ]
     *         }
     *       }
     *     }
     */
    public function actionGetTop()
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }
        $banners = self::banners();
        // 首页轮播通栏
        $extra_banners = $this->getExtraBanners();
        // 搜索框推荐词
        $search_words = MRecommendedElements::getSearchWords();
        $return = [
            'search_words' => $search_words,
            'banners' => $banners,
            'extra_banners' => $extra_banners,
        ];
        $length = (int)Yii::$app->request->get('page_size', 2);
        $weekly_drama = $this->weeklyDrama($length);
        if (!empty($weekly_drama)) {
            $return['weeklydrama'] = $weekly_drama;
        }
        $persona_id = (int)Yii::$app->request->get('persona_id');
        if (!$persona_id) {
            // WORKAROUND: 旧版本客户端（iOS 4.3.0、Android 5.2.2 及以下版本）未传递用户画像 ID，查找服务端中保存的画像
            $is_girl = (Persona::getPersonaType(Persona::PERSONA_MODULE_MASK) % Persona::TYPE_GIRL) === 0;
        } else {
            $is_girl = (($persona_id & Persona::PERSONA_MODULE_MASK) % Persona::TYPE_GIRL) === 0;
        }
        if (false && $is_girl) {
            // WORKAROUND: 临时隐藏广播剧打赏榜周榜
            $reward_rank = Drama::getHomepageDramaRewardRank(self::HOMEPAGE_RANK_NUM);
            if (!empty($reward_rank)) {
                // 若为女性用户且配置了广播剧打赏榜周榜，则返回该模块
                $return['reward_rank'] = [
                    'title' => '广播剧打赏榜周榜',
                    'rank' => $reward_rank,
                ];
            }
        }
        self::checkAdTrackOneDayRetention(Yii::$app->equip->getBuvid());

        return $return;
    }

    /**
     * 检查广告归因的次日留存
     *
     * @param null|string $buvid
     */
    private static function checkAdTrackOneDayRetention(?string $buvid)
    {
        try {
            [$is_in, $unique_tracked_id] = InstallLog::isInOneDayRetentionPool($buvid);
            if (!$is_in) {
                return;
            }
            if ($adtrack = AdTrack::getRecordByUniqueTrackedId($unique_tracked_id)) {
                $adtrack->callbackOneDayRetention();
            } else {
                Yii::error(
                    sprintf('未找到昨日广告归因的记录：buvid[%s]', $buvid),
                    __METHOD__
                );
            }
        } catch (\Exception $e) {
            Yii::error(sprintf('广告归因次日留存检查出错：%s', $e->getMessage()), __METHOD__);
            // PASS
        }
    }

    /**
     * @api {post} /site/install 安装 App 成功调用的接口
     *
     * @apiDescription 调用该接口用于统计客户端安装来源
     *
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/install
     * @apiSampleRequest site/install
     *
     * @apiVersion 0.1.0
     * @apiName install
     * @apiGroup site
     *
     * @apiParam (Android Query 参数) {number=1,2} type 安装类型，1：新装；2：升级、重装（以后需要细分到重装类型）
     * @apiParam (Android Query 参数) {number=0,1} is_root 是否越狱（1 是，0 否）
     * @apiParam (Android Query 参数) {String} ct 渠道标识
     * @apiParam (Android Query 参数) {String} mac 网卡地址
     * @apiParam (Android Query 参数) {String} imei IMEI
     * @apiParam (Android Query 参数) {String} android_id Android ID
     * @apiParam (Android Query 参数) {String} adid 广告 ID
     * @apiParam (Android Query 参数) {String} buvid 唯一设备标识
     * @apiParam (Android Query 参数) {String} oaid Android 匿名设备标识
     * @apiParam (Android Query 参数) {String} drm_id 基于 Android DRM 生成的 ID
     * @apiParam (Android Post 参数) {String} user_agent 设备 User Agent（系统 webview 默认的 User Agent）
     * @apiParam (Android Post 参数) {String} screen_resolution 手机屏幕分辨率（宽x高），例：1080x1920（注：是屏幕的宽高，如果画中画模式或平板分屏的话，仍需要传完整的屏幕尺寸）
     * @apiParam (Android Post 参数) {String} screen_native_resolution 设备原始分辨率（宽x高）。单位：px
     * @apiParam (Android Post 参数) {Number} screen_dpr 设备像素比（为浮点数），例 1.75
     *
     * @apiParam (iOS Query 参数) {number=0,1} idfa iOS 是否取到 IDFA（1 是，0 否）
     * @apiParam (iOS Post 参数) {String} idfv iOS IDFV
     * @apiParam (iOS Post 参数) {String} user_agent 设备 User Agent（系统 webview 默认的 User Agent）
     * @apiParam (iOS Post 参数) {String} buvid 唯一设备标识（iOS 版本大于 4.5.5）
     * @apiParam (iOS Post 参数) {String} screen_resolution 手机屏幕分辨率（宽x高），例：1080x1920（注：是屏幕的宽高，如果画中画模式或平板分屏的话，仍需要传完整的屏幕尺寸）
     * @apiParam (iOS Post 参数) {String} screen_native_resolution 设备原始分辨率（宽x高）。单位：px
     * @apiParam (iOS Post 参数) {Number} screen_dpr 设备像素比（为浮点数），例 1.75
     *
     * @apiParam (iOS Post 参数) {String} extra 额外参数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "msg": "安装成功",
     *         "enable_appsflyer": true,  // 无论新装、升级及重装，总会下发，客户端仅保存第一次成功获取到的值
     *         "device_token": "v1|ahsuidasdkauhjkasdnjkashhdajkdssjshdjkashd"
     *       }
     *     }
     */
    public function actionInstall()
    {
        $equipment = Yii::$app->equip;
        $type = (int)Yii::$app->request->get('type', InstallLog::TYPE_NEW);
        $is_root = (int)Yii::$app->request->get('is_root', InstallLog::IS_NOT_ROOT);
        if (!in_array($type, [InstallLog::TYPE_NEW, InstallLog::TYPE_REPORT])
            || !in_array($is_root, [InstallLog::IS_NOT_ROOT, InstallLog::IS_ROOT])) {
            throw new HttpException(400, '参数错误');
        }
        $equip_id = $equipment->getEquipId();
        [$is_installed, $equip_activate_time] = $this->installEquipInfo($equipment);
        $device_token = Equipment::generateDeviceToken($equip_activate_time, $equip_id);
        if ($type !== InstallLog::TYPE_NEW) {
            // 若不为新安装，则不新增安装日志
            return [
                'msg' => '重装成功',
                'enable_appsflyer' => AdTrackAppsFlyer::isEnabled($equipment),
                'device_token' => $device_token,
            ];
        }
        $equipment->assertEquipIdValid();

        // WORKAROUND: iOS >= 4.6.0 Android >= 5.4.9 版本 cookie 中带 buvid
        // 待迭代若干版本后，更换为直接取 cookie 中的 buvid
        if ($equipment->isAndroidOrHarmonyOS()) {
            $buvid = Yii::$app->request->get('buvid');
        } else {
            $buvid = Yii::$app->request->post('buvid');
        }
        if (!$buvid) {
            $buvid = Yii::$app->request->cookies->getValue('buvid');
        }
        if ($buvid) {
            $equipment->setBuvid($buvid);
        }

        $install_log = InstallLog::newInstance($equipment);
        $user_agent = Yii::$app->request->post('user_agent');
        $data = [
            'equip_id' => $equip_id,
            'device_type' => $equipment->getOs(),
            'is_root' => $is_root,
            'user_agent' => $user_agent,
            'ct' => Yii::$app->request->get('ct'),
            'ip' => Yii::$app->request->userIP,
            'version' => $equipment->getAppVersion(),
        ];
        $more = [];
        if ($screen_resolution = Yii::$app->request->post('screen_resolution', '')) {
            // 手机屏幕分辨率（宽x高）
            $more['screen_resolution'] = $screen_resolution;
        }
        $screen_native_resolution = Yii::$app->request->post('screen_native_resolution', '');
        if ($screen_native_resolution) {
            $more['screen_native_resolution'] = $screen_native_resolution;
        }
        if ($screen_dpr = Yii::$app->request->post('screen_dpr', 0)) {
            // 设备像素比（为浮点数，四舍五入保留两位小数），例 1.75
            $more['screen_dpr'] = (string)round($screen_dpr, 2);
        }
        if ($user_agent) {
            // 解析 user_agent
            $more['device_info'] = MUtils2::parseUserAgent($user_agent);
        }
        switch ($equipment->getOs()) {
            case Equipment::Android:
            case Equipment::HarmonyOS:
                $mac = Yii::$app->request->get('mac');
                if ($mac) {
                    if (preg_match('/([A-Fa-f0-9]{2}:){5}[A-Fa-f0-9]{2}/', $mac)) {
                        $data['mac'] = strtoupper($mac);
                        // 用户终端的 eth0 接口的 MAC 地址（大写保留冒号分隔符）以 MD5 加密
                        $data['mac_md5'] = md5(strtoupper($mac));
                    }
                }
                $imei = Yii::$app->request->get('imei');
                $android_id = Yii::$app->request->get('android_id');

                $data['imei'] = $imei;
                $data['imei_md5'] = $imei ? md5($imei) : null;
                $data['android_id'] = $android_id;
                $data['android_id_md5'] = $android_id ? md5($android_id) : null;
                $data['adid'] = Yii::$app->request->get('adid');
                if ($buvid) {
                    $data['buvid'] = $buvid;
                }
                if ($oaid = Yii::$app->request->get('oaid')) {
                    $data['oaid'] = $oaid;
                    $data['oaid_md5'] = md5($oaid);
                }
                if ($drm_id = Yii::$app->request->get('drm_id')) {
                    $more['drm_id'] = $drm_id;
                }
                break;
            case Equipment::iOS:
                if (Equipment::isAppOlderThan('4.5.5', null)) {
                    if ((int)Yii::$app->request->get('idfa')) {
                        $data['adid'] = $data['equip_id'];
                    } else {
                        $data['idfv'] = Yii::$app->request->post('idfv') ?: $data['equip_id'];
                    }
                } else {
                    $data['adid'] = Yii::$app->request->post('idfa');
                    $data['idfv'] = Yii::$app->request->post('idfv');
                    $data['buvid'] = $buvid;
                }
                break;
            default:
                throw new HttpException(403, '该设备类型暂不纳入统计');
        }
        if ($more) {
            $data['more'] = $more;
        }
        $install_log->attributes = $data;
        if (!$install_log->validate()) {
            throw new HttpException(400, MUtils::getFirstError($install_log) ?: '参数有误');
        }

        if ($equipment->isIOS() && !Equipment::isAppOlderThan('4.7.6')) {
            $extra = Yii::$app->request->post('extra');
            if ($caid_params = Json::decode(base64_decode($extra))) {
                $caid_info = new DeviceCAIDInfo($caid_params);
                if ($caid_map = $caid_info->generateCAID()) {
                    $install_log->more = array_merge($install_log->more ?: [], [
                        'caid' => $caid_map,
                    ]);
                }
            }
        }

        $return = ['msg' => '安装成功', 'enable_appsflyer' => AdTrackAppsFlyer::isEnabled($equipment)];
        if (!$is_installed) {
            try {
                [$tracked, $recommend_pop] = AdTrack::track($install_log);
                if (!$tracked) {
                    // 没有归因成功时，再归因一次，用于归因从内部落地页上报的第三方渠道点击（手淘芭芭农场和B漫等）
                    $tracked = AdTrack::thirdPartyTrack($install_log);
                }
                if ($tracked) {
                    $install_log->converted = AdTrack::CONVERTED;
                }
                if ($recommend_pop) {
                    $return = array_merge($return, $recommend_pop);
                }
            } catch (Exception $e) {
                Yii::error('ad track error: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), __METHOD__);
            }

            try {
                $redis = Yii::$app->redis;
                $object_key = $redis->generateKey(KEY_NEW_DEVICE_EQUIP_ID, $equip_id);
                $redis->setex($object_key, ONE_WEEK, $_SERVER['REQUEST_TIME']);
            } catch (Exception $e) {
                Yii::error(sprintf('新人锁添加失败，equip_id: %s, error: %s', $equip_id, $e->getMessage()),
                    __METHOD__);
                // PASS
            }
        }
        if (!$install_log->ignoreExceptionSave(false, '安装接口保存记录失败')) {
            throw new HttpException(500, '服务器内部错误');
        }
        $return['device_token'] = $device_token;
        return $return;
    }

    /**
     * @api {post} /site/ad-track 点击广告归因
     * @apiDescription 1）促活广告归因 \
     * 2）苹果搜索广告归因：仅供 iOS 调用（在 /site/install 之后调用），iOS 可能会在 30 天内多次重试 \
     * 3）第三方导流换量归因
     *
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/ad-track
     * @apiSampleRequest site/ad-track
     *
     * @apiVersion 0.1.0
     * @apiName ad-track
     * @apiGroup site
     *
     * @apiParam RawBody {json}
     * @apiParamExample RawBody {json} iOS 广告促活上报
     *     {
     *       "idfv": "eb20c9be-ab73-4df3-999e-856a6ce57719",  // IDFV
     *       "user_agent": "Mozilla\/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit\/610.*********(KHTML,like Gecko) Mobile\/18A8395",  // 设备 User Agent
     *       "buvid": "Y0496F0A8F724F6845818E2BCD9C9D20B4D6",  // buvid
     *       "idfa": "f1828e51-4701-41b6-8ad5-29a7a4478fce",  // IDFA
     *       "track_from_url": "missevan:\/\/drama\/38226?ad_source_from=bili&app_key=bili&track_id=pbaes.2sSpe0iggeSilIJ6ji9_mHgcd..."  // 广告回传 URL
     *     }
     *
     * @apiParamExample RawBody {json} iOS 第三方导流换量上报
     *     {
     *       "idfv": "eb20c9be-ab73-4df3-999e-856a6ce57719",  // IDFV
     *       "user_agent": "Mozilla\/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit\/610.*********(KHTML,like Gecko) Mobile\/18A8395",  // 设备 User Agent
     *       "buvid": "Y0496F0A8F724F6845818E2BCD9C9D20B4D6",  // buvid
     *       "idfa": "f1828e51-4701-41b6-8ad5-29a7a4478fce",  // IDFA
     *       "track_from_url": "missevan:\/\/?ad_source_from=ctrip_task&track_id=xxxxxxxxx"  // ad_source_from 上报第三方渠道任务类型，track_id 上报第三方任务 Token
     *     }
     *
     * @apiParamExample RawBody {json} Android 广告促活上报
     *     {
     *       "android_id": "d517f85fbf127a96",  // Android ID
     *       "adid": "d517f85fbf127a96",  // 广告 ID
     *       "buvid": "XY216D524F4E70B5E1E1C24BE2163430C3198",  // 唯一设备标识
     *       "ct": "missevan",  // 渠道标识
     *       "drm_id": "2BCE75C41C01956690674E1E58721908",  // 基于 Android DRM 生成的 ID
     *       "imei": "",  // IMEI
     *       "is_root": 0,  // 是否越狱（1 是，0 否）
     *       "mac": "f2:b6:43:a2:50:40",  // 网卡地址
     *       "oaid": "ef7e2efb-fcf9-90b3-d42f-feffe95706f2",  // Android 匿名设备标识
     *       "track_from_url": "missevan://drama/38226?ad_source_from=bili&app_key=bili&track_id=pbaes.hd87IjXUU...",  // 广告回传 URL
     *       "user_agent": "Mozilla/5.0 (Linux; Android 10; OXF-AN00 Build/HUAWEIOXF-AN00; wv) AppleWebKit/537.36(KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Mobile Safari/537.36"  // 设备 User Agent
     *     }
     *
     * @apiParamExample RawBody {json} Android 第三方导流换量上报
     *     {
     *       "android_id": "d517f85fbf127a96",  // Android ID
     *       "adid": "d517f85fbf127a96",  // 广告 ID
     *       "buvid": "XY216D524F4E70B5E1E1C24BE2163430C3198",  // 唯一设备标识
     *       "ct": "missevan",  // 渠道标识
     *       "drm_id": "2BCE75C41C01956690674E1E58721908",  // 基于 Android DRM 生成的 ID
     *       "imei": "",  // IMEI
     *       "is_root": 0,  // 是否越狱（1 是，0 否）
     *       "mac": "f2:b6:43:a2:50:40",  // 网卡地址
     *       "oaid": "ef7e2efb-fcf9-90b3-d42f-feffe95706f2",  // Android 匿名设备标识
     *       "track_from_url": "missevan:\/\/?ad_source_from=baidu_task&track_id=xxxxxxxxx"  // ad_source_from 上报第三方渠道任务类型，track_id 上报第三方任务 Token
     *       "user_agent": "Mozilla/5.0 (Linux; Android 10; OXF-AN00 Build/HUAWEIOXF-AN00; wv) AppleWebKit/537.36(KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Mobile Safari/537.36"  // 设备 User Agent
     *     }
     *
     * @apiParamExample RawBody {json} AdServices Framework（iOS 14.3 及其之后）
     *     {
     *       "idfa": "2cc60d08-d8b2-4052-b781-2bb73f99fa31",
     *       "user_agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
     *       "attribution_data": {
     *         "token": "dkw5rLKX7bA7BpGk2kOKasyC1O....FxVzJOwUUXAAVADAAAA5wAAAIA="
     *       }
     *     }
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response: 苹果搜索广告 ASA 归因（若接口没有请求成功，客户端需要进行重试）
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "new_equip": true,  // 是否为新设备：若为 false 则代表旧的设备，不再进行重试
     *         "ad_tracked": false  // 是否归因上：若为 true 则代表归因成功，不再进行重试
     *       }
     *     }
     *
     * @apiSuccessExample Success-Response: 促活广告归因 | 第三方导流换量归因
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "ad_tracked": false  // 是否归因上
     *       }
     *     }
     */
    public function actionAdTrack()
    {
        $rawbody = Yii::$app->request->rawBody;
        $equipment = Yii::$app->equip;
        if (!$equipment->getBuvid()
                || !$rawbody
                || !($data = Json::decode($rawbody))
                || !isset($data['user_agent'])) {
            throw new HttpException(403, '非法请求');
        }
        if (isset($data['track_from_url'])) {
            $query_arr = self::parseTrackFromUrl($data['track_from_url']);
            $is_track_id_exist = array_key_exists('track_id', $query_arr);
            $ad_source_from = $query_arr['ad_source_from'] ?? '';
            if (!$is_track_id_exist) {
                // track_id 不存在时，不进行促活广告归因
                return ['ad_tracked' => false];
            }
            // 促活广告归因和第三方导流换量归因
            $data['track_id'] = $query_arr['track_id'];
            if ($ad_source_from) {
                $data['ad_source_from'] = $ad_source_from;
            }
            $res = $this->appCallupAttribution($data, self::getAppCallupVendor($ad_source_from));
            return $res;
        } elseif ($equipment->isIOS() && isset($data['attribution_data'])) {
            // 苹果搜索广告 ASA 归因
            $res = $this->appleAdAttribution($data);
            return $res;
        }

        throw new HttpException(403, '非法请求');
    }

    /**
     * 解析回传的 url
     *
     * @param string $track_from_url 回传 url
     * @return array 解析后的参数数组，例：['ad_source_from' => 'baidu_task', 'track_id' => 'test']
     */
    private static function parseTrackFromUrl($track_from_url = ''): array
    {
        if (!$track_from_url) {
            return [];
        }
        $homepage_path = Msr0::SCHEMA_PREFIX . '?';
        if (str_starts_with($track_from_url, $homepage_path)) {
            $query_str = str_replace($homepage_path, '', $track_from_url);
        } else {
            $query_str = parse_url($track_from_url, PHP_URL_QUERY);
            if (!$query_str) {
                return [];
            }
        }
        parse_str($query_str, $query_arr);
        return $query_arr;
    }

    /**
     * 获取促活广告平台
     *
     * @param string $ad_source_from 广告来源
     * @return int 广告平台
     * @throws HttpException
     */
    private function getAppCallupVendor(string $ad_source_from)
    {
        switch ($ad_source_from) {
            case 'bili':
                return AdTrack::VENDOR_BILIBILI;
            case 'douyin':
                return AdTrack::VENDOR_DOUYIN;
            case 'tengxun':
                return AdTrack::VENDOR_TENGXUN;
            case 'baidu':
                return AdTrack::VENDOR_BAIDU_INFO_FLOW;
            case in_array($ad_source_from, AdTrackThirdparty::TO_THIRD_PARTY_LIST):
                return AdTrack::VENDOR_THIRDPARTY;
            default:
                Yii::error('不支持的第三方到端任务：' . $ad_source_from, __METHOD__);
                throw new HttpException(403, '非法请求');
        }
    }

    /**
     * 促活广告归因
     *
     * @param array $rawbody_data 上报参数
     * @param int $vendor 广告平台
     * @return array
     */
    private function appCallupAttribution(array $rawbody_data, int $vendor)
    {
        $equipment = Yii::$app->equip;
        $buvid = $equipment->getBuvid();
        // 没有安装记录的用户，不会进行归因
        // 很早之前的用户安装后没有安装记录 buvid，目前广告投放选取的人群包中，这类用户占比很少，对促活率基本无影响
        $exists = InstallBuvid::find()
            ->where(['buvid' => $buvid])
            ->exists();
        if (!$exists) {
            return ['ad_tracked' => false];
        }

        $install_log = InstallLog::newInstance($equipment);
        $data = [
            'equip_id' => $equipment->getEquipId(),
            'device_type' => $equipment->getOs(),
            'version' => $equipment->getAppVersion(),
            'buvid' => $buvid,
            'user_agent' => $rawbody_data['user_agent'],
        ];
        if (isset($rawbody_data['ct'])) {
            $data['ct'] = $rawbody_data['ct'];
        }
        switch ($equipment->getOs()) {
            case Equipment::Android:
            case Equipment::HarmonyOS:
                foreach ($rawbody_data as $k => $v) {
                    if ($k === 'mac' && Equipment::checkMac($rawbody_data['mac'])) {
                        $data['mac'] = strtoupper($v);
                        // 用户终端的 eth0 接口的 MAC 地址（大写保留冒号分隔符）以 MD5 加密
                        $data['mac_md5'] = md5(strtoupper($v));
                    } elseif (in_array($k, ['imei', 'android_id', 'oaid'])) {
                        $data[$k] = $v;
                        $data[$k . '_md5'] = md5($v);
                    } elseif ($k === 'adid') {
                        $data['adid'] = $v;
                    } elseif ($k === 'drm_id') {
                        $data['more'] = ['drm_id' => $v];
                    }
                }
                break;
            case Equipment::iOS:
                if (isset($rawbody_data['idfa'])) {
                    $data['adid'] = $rawbody_data['idfa'];
                }
                if (isset($rawbody_data['idfv'])) {
                    $data['idfv'] = $rawbody_data['idfv'];
                }
                break;
            default:
                throw new HttpException(403, '该设备类型暂不纳入统计');
        }
        $install_log->attributes = $data;
        // 生成的 $install_log 不需要保存，统一使用 InstallLog 对象作为参数进行广告归因
        if (!$install_log->validate()) {
            throw new HttpException(400, MUtils::getFirstError($install_log) ?: '参数有误');
        }

        /**
         * @var AdTrack $ad_track_already_callup
         */
        $ad_track_already_callup = AdTrack::find()
            ->where(['converted' => AdTrack::CONVERTED, 'os' => $install_log->device_type, 'buvid' => $install_log->buvid])
            ->andWhere('JSON_EXTRACT(more, "$.ad_event_type") = :ad_event_type', [':ad_event_type' => AdTrack::AD_EVENT_TYPE_CALLUP])
            ->andWhere('create_time >= :from_time', [':from_time' => strtotime('today')])
            ->limit(1)
            ->one();
        if ($ad_track_already_callup) {
            if ($ad_track_already_callup->vendor === AdTrack::VENDOR_THIRDPARTY && in_array($ad_track_already_callup->more['channel'] ?? '', AdTrackThirdparty::TO_THIRD_PARTY_LIST)) {
                // 到端任务已回传过的设备可再次回传
                // PASS
            } else {
                // 广告拉活已回传过的设备不再次回传（此处不进行渠道限制，当天内在某个渠道拉活唤起回传过时，在其它渠道拉活唤起时不再回传）
                return ['ad_tracked' => true];
            }
        }
        $is_to_third_party = array_key_exists('ad_source_from', $rawbody_data) &&
            in_array($rawbody_data['ad_source_from'], AdTrackThirdparty::TO_THIRD_PARTY_LIST);
        if (!$is_to_third_party) {
            $ad_track = AdTrack::getRecord($install_log, AdTrack::AD_ATTRIBUTION_TYPE_APP_CALLUP, $vendor);
        } else {
            // 第三方到端任务需要新增归因数据
            $ad_track = AdTrackThirdparty::initiateRecord($install_log, $rawbody_data['ad_source_from'], $rawbody_data['track_id']);
        }
        if (!$ad_track) {
            return ['ad_tracked' => false];
        }
        $ad_track->markAsConverted($ad_track->currentTableName,
            $install_log,
            ['more' => new Expression(
                'JSON_SET(more, "$.track_from_url", :track_from_url)',
                [':track_from_url' => $rawbody_data['track_from_url']]
            )]);
        $app_callup_ok = false;
        if ($is_to_third_party) {
            $params = new ToThirdPartyParams();
            $params->ad_source_from = $rawbody_data['ad_source_from'];
            $params->track_id = $rawbody_data['track_id'] ?? null;
            $app_callup_ok = $ad_track->callbackAppCallup($params);
        } else {
            $app_callup_ok = $ad_track->callbackAppCallup();
        }
        if ($app_callup_ok) {
            $install_log->addToConvertedPool($ad_track->getUniqueTrackedId());
        }
        $ad_track->matchPopup();

        return ['ad_tracked' => true];
    }

    /**
     * 苹果搜索广告 ASA 归因
     *
     * @param array $rawbody_data 上报参数
     * @return array
     */
    private function appleAdAttribution(array $rawbody_data)
    {
        $equipment = Yii::$app->equip;
        $exists = InstallBuvid::find()
            ->where(['buvid' => $equipment->getBuvid()])
            ->andWhere('create_time < :install_time', [':install_time' => $_SERVER['REQUEST_TIME'] - THIRTY_DAYS])
            ->exists();
        if ($exists) {
            return ['new_equip' => false, 'ad_tracked' => false];
        }

        $exists = AdTrack::find()
            ->where(['buvid' => $equipment->getBuvid(), 'converted' => AdTrack::CONVERTED])
            ->exists();
        if ($exists) {
            return ['new_equip' => false, 'ad_tracked' => true];
        }

        [$ad_tracked] = AdTrack::track(new InstallLog([
            'equip_id' => $equipment->getEquipId(),
            'buvid' => $equipment->getBuvid(),
            'device_type' => $equipment->getOs(),
            'adid' => $rawbody_data['idfa'] ?? null,
            'user_agent' => $rawbody_data['user_agent'],
            'ip' => Yii::$app->request->userIP,
        ]), AdTrack::VENDOR_APPLE, $rawbody_data['attribution_data']);

        return ['new_equip' => true, 'ad_tracked' => $ad_tracked];
    }

    /**
     * 获取设备安装信息
     * buvid 第一次入库时间视作设备初次安装激活时间
     *
     * @param Equipment $equip
     * @return array 返回是否安装过以及初次安装时间戳（单位：秒），如：[true, 1708787777]
     * @throws HttpException
     * @throws Yii\db\IntegrityException
     */
    private function installEquipInfo(Equipment $equip): array
    {
        if (!$buvid = $equip->getBuvid()) {
            // 忽略不带 buvid 的旧版本（Android <= 5.3.7, iOS <= 4.5.4）
            // 老版本都当作已安装过，安装激活时间返回 0
            return [true, 0];
        }

        // 在历史设备中匹配不到则计为新增
        // http://berserker.bilibili.co/#/quotaWiki/quotaDetail?id=167&name=新增设备数
        $install_buvid = InstallBuvid::find()
            ->select('id, create_time')
            ->andWhere('buvid = :buvid', [':buvid' => $buvid])
            ->limit(1)
            ->one();
        if ($install_buvid) {
            return [true, $install_buvid->create_time];
        }
        try {
            // 写入设备信息
            $model = new InstallBuvid();
            $model->setAttributes([
                'buvid' => $equip->getBuvid(),
            ]);
            if (!$model->save()) {
                throw new HttpException(400, MUtils::getFirstError($model));
            }
        } catch (yii\db\IntegrityException $e) {
            // 可能存在并发问题，忽略唯一索引抛出的异常
            if (!MUtils2::isUniqueError($e, InstallBuvid::getDb())) {
                // 当为其他错误时，记录日志，返回错误，以便客户端重新请求安装接口
                Yii::error('install_buvid add log error: ' . $e->getMessage(), __METHOD__);
                throw $e;
            }
        }
        return [false, $model->create_time];
    }

    /**
     * @deprecated 客户端没有再调用
     * @api {get} /site/get-new-version 获取 App 最新版本号
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/get-new-version
     * @apiSampleRequest site/get-new-version
     *
     * @apiVersion 0.1.0
     * @apiName get-new-version
     * @apiGroup site
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "version": "4.1.7"
     *       }
     *     }
     */
    public function actionGetNewVersion()
    {
        $equipment = Yii::$app->equip;
        switch ($equipment->getOs()) {
            case $equipment::Android:
                $version = MAppupdate::find()
                    ->select('version')
                    ->where(
                        'status = :status AND device = :device',
                        [':status' => MAppupdate::STATUS_PUBLISHED, ':device' => MAppupdate::DEVICE_ANDROID]
                    )
                    ->orderBy('update_time DESC')
                    ->limit(1)
                    ->scalar();
                break;
            case $equipment::iOS:
                $redis = Yii::$app->redis;
                $version = $redis->get(KEY_IOS_VERSION);
                break;
            default:
                throw new HttpException(404, '该设备不能获取最新版本号');
        }
        if (!$version) {
            throw new HttpException(500, '版本号获取失败');
        }
        return ['version' => $version];
    }

    /**
     * @api {get} /site/get-sign-key 通过 DH（迪菲－赫尔曼密钥交换算法）获取生成签名的 key 的公钥
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/get-sign-key
     * @apiSampleRequest /site/get-sign-key
     *
     * @apiVersion 0.1.0
     * @apiName get-sign-key
     * @apiGroup site
     *
     * @apiParam {Number} public_key 客户端使用约定的 DH 算法得到的公钥
     *
     * @apiSuccess {Boolean} success 请求成功或失败
     * @apiSuccess {Number} code 请求成功或失败返回码
     * @apiSuccess {String} info 生成签名的 key 的公钥
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info": "9311944372197310564554456445454"
     *     }
     */
    public function actionGetSignKey()
    {
        $public_key = (int)Yii::$app->request->post('public_key');
        if ($public_key < 0) {
            throw new HttpException(400, '参数错误');
        }
        $cache_key_name = MUtils::generateCacheKey(KEY_REQUEST_SIGN_UUID, Yii::$app->equip->getEquipId());
        return MUtils::getSignKey($public_key, $cache_key_name);
    }

    /**
     * @api {get} /site/config 获取下发的配置
     * @apiDescription 下发的字段名和字段值都是字符串类型
     *
     * @apiVersion 0.1.0
     * @apiName config
     * @apiGroup site
     *
     * @apiSuccess {Boolean} success 请求成功或失败
     * @apiSuccess {Number} code 请求成功或失败返回码
     * @apiSuccess {Object} info 配置详情
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 200,
     *       "info": {
     *         "omikuji_share_text": "{\"5\":{\"凶\":\"#撒野# #猫....求音里听一听~\"}}",
     *         "radio_catalogs": "[{\"id\":118,\"name\":\"催眠\"},{\"id\":119,\"name\":\"音乐\"}]",
     *         // 如果有 provider 为 google，客户端需要开启使用 google 的 httpdns，当 provider 为 local 时，表示禁用 httpdns（优先判断 provider）
     *         // 若无 provider，表示要使用阿里云的 httpdns（service_ip 为其服务器 IP 列表），service_ip 为空数组时禁用 httpdns，
     *         // 如果没有下发 service_ip，表示使用默认的中国大陆的阿里云 httpdns 服务器 IP 列表
     *         "httpdns": "{\"hosts\":[\"app.uat.missevan.com\"],\"service_ip\":[\"*************\"],\"provider\":\"google\"}",
     *         ···
     *         "tab_bar_live": "1"  // 【直播】Tab 灰度实验标识 0: 对照组；1：实验组 - 双列直播流；2：实验组 - 全屏直播流
     *       }
     *     }
     */
    public function actionConfig()
    {
        $client = Equipment::iOS;
        if (Yii::$app->equip->isAndroidOrHarmonyOS()) {
            $client = Equipment::Android;
        }
        $redis = Yii::$app->redis;
        $config_key = $redis->generateKey(KEY_CONFIG_CLIENT, $client);
        $config_info = $redis->hGetAll($config_key);
        if (!$config_info) {
            // 无下发配置时，直接返回一个空对象
            return null;
        }
        // 内网地址和大陆地区不作处理
        // 香港和澳门地区，给出具体的 service ip（字符串数组），其余海外的地区会直接下发空数组，对应关闭 httpdns 的逻辑
        $service_ip = null;
        // NOTICE: isChinaMainland 包含内网地址
        if (!MUtils::isChinaMainland()) {
            $service_ip = [];
            $country_code = MUtils::getCountryCode();
            if ($country_code === 'HK' || $country_code === 'MO') {
                $alihttpdns = new AliHttpDns();
                $service_ip = $alihttpdns->getServiceIP();
            }
        }

        foreach ($config_info as $key => $value) {
            // 判断配置值是否需要执行
            if (substr($value, 0, 1) === '@') {
                $expression = substr($value, 1);
                try {
                    $config_info[$key] = eval("return $expression;");
                    if (is_null($config_info[$key])) {
                        // 返回 NULL 时，过滤此配置的下发
                        unset($config_info[$key]);
                    }
                } catch (\Throwable $e) {
                    unset($config_info[$key]);
                    // 记录错误日志
                    Yii::error('下发的配置 ' . $key . ' 运行错误：' . $e->getMessage(), __METHOD__);
                }
            }
            if ($service_ip !== null && $key === 'httpdns') {
                $data = Json::decode($value);
                // 此处使用 +=，不覆盖原有信息，以后台配置的 httpdns 的信息为主
                $data += ['service_ip' => $service_ip];
                // 当 service_ip 为空数组时表示非香港和澳门的其它海外地区，增加返回 provider 信息
                if (empty($service_ip)) {
                    $data += ['provider' => 'google'];
                }
                $config_info[$key] = Json::encode($data);
            }
        }
        unset($key, $value);
        return $config_info;
    }

    /**
     * @api {get} /site/get-message-config 获取消息设置
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/get-message-config
     * @apiSampleRequest /site/get-message-config
     *
     * @apiVersion 0.1.0
     * @apiName get-message-config
     * @apiGroup site
     *
     * @apiSuccess {Boolean} success 请求成功或失败
     * @apiSuccess {Number} code 请求成功或失败返回码
     * @apiSuccess {Object} info 开关状态
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "receive": 1,
     *         "fold": 1
     *       }
     *     }
     */
    public function actionGetMessageConfig()
    {
        $user_id = Yii::$app->user->id;
        $info = UserAddendum::getByPk($user_id);
        return $info->message_config;
    }

    /**
     * @api {post} /site/set-message-config 消息设置
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/set-message-config
     * @apiSampleRequest /site/set-message-config
     *
     * @apiVersion 0.1.0
     * @apiName set-message-config
     * @apiGroup site
     *
     * @apiParam {String} type 设置的开关类型 receive：是否接收未关注人消息；fold：是否收起未关注人消息
     * @apiParam {Number} value 开关状态 0：关闭；1：打开
     *
     * @apiSuccess {Boolean} success 请求成功或失败
     * @apiSuccess {Number} code 请求成功或失败返回码
     * @apiSuccess {Number} info 开关状态
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": 0
     *     }
     */
    public function actionSetMessageConfig()
    {
        $type = trim(Yii::$app->request->post('type'));
        $value = (int)Yii::$app->request->post('value');
        $user_id = Yii::$app->user->id;
        $info = UserAddendum::getByPk($user_id);
        $message_config = $info->message_config;
        if (!$message_config) {
            // 默认设置为接收未关注人消息和展开未关注人消息
            $message_config = [
                UserAddendum::MSG_CFG_TYPE_RECEIVE => UserAddendum::MESSAGE_RECEIVE,
                UserAddendum::MSG_CFG_TYPE_FOLD => UserAddendum::MESSAGE_EXPAND,
            ];
        }
        switch ($type) {
            case UserAddendum::MSG_CFG_TYPE_RECEIVE:
                if ($value && UserAddendum::MESSAGE_RECEIVE !== $message_config[$type]) {
                    // 设置接收未关注人消息
                    $message_config[$type] = UserAddendum::MESSAGE_RECEIVE;
                } elseif (!$value && UserAddendum::MESSAGE_REJECT !== $message_config[$type]) {
                    // 设置拒绝接收未关注人消息
                    $message_config[$type] = UserAddendum::MESSAGE_REJECT;
                }
                break;
            case UserAddendum::MSG_CFG_TYPE_FOLD:
                if ($value && UserAddendum::MESSAGE_FOLD !== $message_config[$type]) {
                    // 设置收起未关注人消息
                    $message_config[$type] = UserAddendum::MESSAGE_FOLD;
                } elseif (!$value && UserAddendum::MESSAGE_EXPAND !== $message_config[$type]) {
                    // 设置展开未关注人消息
                    $message_config[$type] = UserAddendum::MESSAGE_EXPAND;
                }
                break;
            default:
                throw new HttpException(400, '参数错误');
        }
        $info->message_config = $message_config;
        if (!$info->save(false, ['message_config'])) {
            // 记录错误日志
            Yii::error('消息设置失败：' . MUtils::getFirstError($info), __METHOD__);
            throw new HttpException(500, '设置失败');
        }
        return $value;
    }

    /**
     * @api {get} /site/homepage-flow 首页推荐瀑布流
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/homepage-flow
     * @apiSampleRequest site/homepage-flow
     * @apiDescription 仅海外版使用
     *
     * @apiVersion 0.1.0
     * @apiName homepage-flow
     * @apiGroup site
     *
     * @apiParam {Number} [last_id=0] 上次浏览最后的 ID
     * @apiParam {Number} [page_size=20] 每页个数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [{
     *           "id": 10,
     *           "elem_id": 3014423,
     *           "elem_type": 0,
     *           "title": "魔道祖师",
     *           "more": {
     *             "newest": "第五话",  // 仅 1.0.0 版本使用（后续移除该字段）
     *             "subtitle": "第五话 更新"  // 1.0.1 及其后的版本使用
     *           },
     *           "cover": "http://static.missevan.com/nocover.png",
     *           "url": "missevan://sound/655",
     *           "outline": {  // 剧集设定
     *             "cover": "https://static.missevan.com/nocover.png",
     *             "url": "https://m.missevan.com/1"
     *           }
     *         }],
     *         "pagination": {
     *           "has_more": true
     *         }
     *       }
     *     }
     */
    public function actionHomepageFlow(int $last_id = 0, int $page_size = PAGE_SIZE_20)
    {
        return MHomepageFlow::getData($last_id, $page_size, Yii::$app->user->id);
    }

    /**
     * @api {get} /site/catalog-tabs 分区子分类 tabs
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/catalog-tabs
     * @apiSampleRequest site/catalog-tabs
     *
     * @apiVersion 0.1.0
     * @apiName catalog-tabs
     * @apiGroup site
     *
     * @apiParam {Number} catalog_id 分区 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "type": 0,
     *           "type_name": "推荐"
     *         },
     *         {
     *           "type": 4,
     *           "type_name": "纯爱"
     *         },
     *         {
     *           "type": 6,
     *           "type_name": "言情"
     *         }
     *       ]
     *     }
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "integrity": 0,
     *           "integrity_name": "推荐"
     *         },
     *         {
     *           "integrity": 2,
     *           "integrity_name": "完结"
     *         },
     *         {
     *           "integrity": 1,
     *           "integrity_name": "未完结"
     *         },
     *         {
     *           "integrity": 5,
     *           "integrity_name": "全一期"
     *         }
     *       ]
     *     }
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         {
     *           "id": 65,
     *           "name": "推荐"
     *         },
     *         {
     *           "id": 66,
     *           "name": "来电"
     *         },
     *         {
     *           "id": 67,
     *           "name": "短信"
     *         }
     *       ]
     *     }
     */
    public function actionCatalogTabs(int $catalog_id)
    {
        if (Equipment::isAppOlderThan('4.4.6', '5.3.6')) {
            // 若为 PGC 分区之前的版本，则传递的 catalog_id 为音频分类，需要转化为剧集分类
            $catalog_id = Drama::getCatalogBySoundCatalog($catalog_id);
        }
        if (in_array($catalog_id, Drama::SOUND_TO_DRAMA_CATALOG_IDS)) {
            // 若为 PGC 分区，则返回剧集分区下的 type
            if (in_array($catalog_id, [Drama::DRAMA_CATALOG_ID_CN_RADIO_DRAMA, Drama::DRAMA_CATALOG_ID_CN_CARTOON])
                    && !Equipment::isAppOlderThan('4.7.6', '5.6.4')) {
                // WORKAROUND: 中文广播剧和中文有声漫画针对 iOS >= 4.7.6、Android >= 5.6.4 返回新结构
                $tabs = Drama::getDramaIntegrity($catalog_id);
                array_unshift($tabs, ['integrity' => 0, 'integrity_name' => '推荐']);
            } else {
                // WORKAROUND: iOS < 4.7.6、Android < 5.6.4 返回旧结构
                $tabs = Drama::getDramaTypes($catalog_id);
                array_unshift($tabs, ['type' => 0, 'type_name' => '推荐']);
            }
            return $tabs;
        } elseif (in_array($catalog_id, Catalog::UGC_CATALOG_IDS)) {
            // 若为 UGC 分区，则返回剧集分区下的 type
            $tabs = Catalog::getCatalogTabs($catalog_id);
            return array_merge([['id' => $catalog_id, 'name' => '推荐']], $tabs);
        } else {
            throw new HttpException(404, '分类不存在', 200110201);
        }
    }

    /**
     * @api {get} /site/tabs 获取 App 首页 tabs
     * @apiDescription App 首页 Tab 推荐（ID = 1），分类（ID = 2），直播（ID = 3）的 ID 是固定的 \
     * 客户端需要使用 ID 来区分这三个 Tab 的对应页面，其他的使用 URL 来分区对应页面 \
     * 如果接口返回了这三个固定的 Tab，客户端按接口顺序来显示 \
     * 如果接口没有返回，那客户端写死这三个 Tab，顺序为推荐、分类、直播，其他 Tab 的显隐藏走接口 \
     * 接口返回的 url，目前只支持 PGC 和 UGC 的协议地址 \
     * 例：PGC：missevan://catalog/drama/89；UGC：missevan://catalog/sound/108 \
     * 当接口返回值里，多个 Tab 字段里有 active = 1（默认选择）的情况，客户端会选取最后一个作为默认显示
     *
     * @apiVersion 0.1.0
     * @apiName tabs
     * @apiGroup site
     *
     * @apiParam {number=0,1} [boot=0] 是否为启动客户端后请求，0：否，1：是（iOS >= 6.0.3 和安卓 >= 6.0.3 版本传入）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "tabs": [
     *           {
     *             "id": 3,
     *             "title": "直播",
     *             "url": "missevan://live",
     *             "icon": "https://static.maoercdn.com/test/a.png",  // tab 图标，未下发时展示默认文字名称
     *             "dark_icon": "https://static.maoercdn.com/test/b.png"  // 夜间模式 tab 图标，未下发时展示默认文字名称
     *           },
     *           {
     *             "id": 4,
     *             "title": "新人",
     *             "url": "missevan://homepage/newuser",
     *             "page_mark": "main.new_user"  // 埋点上报的页面标识，下发时上报该字段值
     *           },
     *           {
     *             "id": 1,
     *             "title": "推荐",
     *             "active": 1,
     *             "url": "missevan://homepage"
     *           },
     *           {
     *             "id": 101,
     *             "title": "广播剧",
     *             "url": "missevan://catalog/drama/89?homepage=1",
     *             "page_mark": "drama.drama_homepage"  // 埋点上报的页面标识，下发时上报该字段值
     *           },
     *           {
     *             "id": 102,
     *             "title": "声音恋人",
     *             "url": "missevan://catalog/sound/108?homepage=1",
     *             "page_mark": "main.voicelover"
     *           },
     *           {
     *             "id": 2,
     *             "title": "分类",
     *             "url": "missevan://catalogs"
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionTabs()
    {
        $boot = (int)Yii::$app->request->get('boot');
        if (!in_array($boot, [self::NOT_BOOT, self::BOOT])) {
            throw new HttpException(400, '参数错误');
        }

        $return = MTab::getHomepageTabs();
        // 云游戏渠道包默认显示直播 Tab
        $default_tab_id = Equipment::isFromYunYouXiChannel() ? MTab::TAB_ID_LIVE : 0;
        if (!$default_tab_id) {
            // 获取渠道承接位置信息
            $equip_id = Yii::$app->equip->getEquipId();
            $recommend_data = MRecommendPopup::getRecommendData(MRecommendPopup::ORIGIN_SITE_TABS, $equip_id);
            if (!empty($recommend_data) && $recommend_data[0] === MRecommendPopup::TAB_LIVE) {
                // 渠道承接位置为直播时，默认定位到直播 Tab, 渠道承接位置为非首页固定页面或推荐时，不设置默认显示 Tab
                $default_tab_id = MTab::TAB_ID_LIVE;
            }
        }
        [$is_show_new_user_tab, $default_new_user_tab] = self::showNewUserTab($boot);

        $tabs = [];
        $new_user_tab = null;
        $enable_tab_bar_live = MHomepageIcon::enableTabBarLive();
        // WORKAROUND: 双端低于 6.3.1 版本时无法支持打开 h5 页面，需要屏蔽
        $older_than_630 = Equipment::isAppOlderThan('6.3.1', '6.3.1');
        foreach ($return['tabs'] as $tab) {
            if ($older_than_630 && !str_starts_with($tab['url'], 'missevan://')) {
                continue;
            }
            if (str_starts_with($tab['url'], 'missevan://homepage/newuser')) {
                if (!$is_show_new_user_tab) {
                    continue;
                }
                // 若配置了渠道 Tab 优先定位到渠道 Tab 页；若未配置渠道 Tab 且默认定位新人 Tab，则定位到新人 Tab 页
                if (!$default_tab_id && $default_new_user_tab) {
                    $default_tab_id = $tab['id'];
                    $new_user_tab = $tab;
                    continue;
                }
            } elseif ($tab['url'] === 'missevan://homepage' && $enable_tab_bar_live) {
                // WORKAROUND: 首页启用了底部 tab bar 直播按钮时，将顶部推荐 tab 标题改为"发现"
                $tab['title'] = '发现';
            }
            $tabs[] = $tab;
        }
        if ($new_user_tab) {
            // 定位新人 Tab 时将新人 Tab 插入第二位
            array_splice($tabs, 1, 0, [$new_user_tab]);
        }

        // 若没有指定显示的 Tab 页，则默认显示数据库中 active 字段为 1 对应的 Tab 页
        if ($default_tab_id) {
            $tabs = array_map(function ($tab) use ($default_tab_id) {
                if (array_key_exists('active', $tab)) {
                    unset($tab['active']);
                }
                if ($default_tab_id === $tab['id']) {
                    $tab['active'] = MTab::ACTIVE;
                }
                return $tab;
            }, $tabs);
        }
        $return['tabs'] = $tabs;
        return $return;
    }

    /**
     * 是否显示首页新人 Tab
     *
     * @param int $boot 是否为启动客户端后请求，0：否，1：是
     * @return array 例：[true, false]，第一个值表示是否显示新人 Tab，第二个值表示是否默认定位到新人 Tab
     */
    private static function showNewUserTab(int $boot)
    {
        try {
            if (Equipment::isAppOlderThan('6.0.3', '6.0.3')) {
                // WORKAROUND: iOS < 6.0.3 或 Android < 6.0.3 不显示新人 Tab
                return [false, false];
            }
            $user_id = Yii::$app->user->id;
            $equipment = Yii::$app->equip;
            // 是否显示新人 Tab 页
            $is_show_new_user_tab = false;
            // 是否默认定位新人 Tab 页
            $default_new_user_tab = false;
            if ($user_id) {
                $register_time = Yii::$app->user->registerAt;
                // 若用户在新人用户白名单中或用户注册时间小于 7 天显示新人 Tab
                $is_show_new_user_tab = in_array($user_id, Yii::$app->params['newuser_user_allowlist']) ||
                    ($_SERVER['REQUEST_TIME'] - $register_time) <= ONE_WEEK;
                // 检查并删除新人锁（当前设备为新设备但设备登录了老用户时删除）
                $equipment->checkAndRemoveNewEquipmentFlag();
            } elseif (!$user_id && $equipment->isNewEquipment()) {
                // 游客状态下且当前设备为新设备，首页显示新人 Tab
                $is_show_new_user_tab = true;
                // 新设备激活后 30s 内启动，默认定位新人 Tab
                $default_new_user_tab = $boot && (($_SERVER['REQUEST_TIME'] - $equipment->getActivateTime()) <= HALF_MINUTE);
            }
            $now = $_SERVER['REQUEST_TIME'];
            if ($default_new_user_tab && $now >= 1738807200 && $now < 1740326400) {
                // TEMP: 声优纪期间（2025-02-06 10:00:00 到 2025-02-24 00:00:00），不默认定位到新人 Tab 页
                $default_new_user_tab = false;
            }
            return [$is_show_new_user_tab, $default_new_user_tab];
        } catch (Exception $e) {
            Yii::error(sprintf('判断首页是否显示新人 Tab 页出错，error: %s', $e->getMessage()), __METHOD__);
            // PASS
            return [false, false];
        }
    }

    /**
     * @api {get} /site/tab-icons{?tab_id} 获取首页 tabs 下对应的 Icons
     *
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/tab-icons
     * @apiSampleRequest site/tab-icons
     *
     * @apiVersion 0.1.0
     * @apiName tab-icons
     * @apiGroup site
     *
     * @apiParam Query {Number} tab_id Tab ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "icons": [
     *           {
     *             "id": 2,
     *             "title": "索引",
     *             "url": "missevan://drama/filter",
     *             "icon": "http://static.missevan.com/profile/icon01.png",
     *             "dark_icon": "http://static.missevan.com/profile/icon01.png"
     *           },
     *           {
     *             "id": 1,
     *             "title": "时间表",
     *             "url": "missevan://drama/timeline",
     *             "icon": "http://static.missevan.com/profile/icon01.png",
     *             "dark_icon": "http://static.missevan.com/profile/icon01.png"
     *           }
     *       }
     *     }
     */
    public function actionTabIcons(int $tab_id)
    {
        // TODO: Icons 之后调整成和页面内容一起返回
        if ($tab_id <= 0) {
            throw new HttpException(400, '参数错误');
        }
        $return = MHomepageIcon::getTabIcons($tab_id);
        if (!empty($return['icons']) && Equipment::isAppOlderThan(null, '5.5.5')) {
            // WORKAROUND: 解决安卓 < 5.5.5 版本，Icon 图片模糊的问题
            $return['icons'] = array_map(function ($item) {
                $item['icon'] .= MHomepageIcon::ICON_WEBP_PARAM;
                $item['dark_icon'] .= MHomepageIcon::ICON_WEBP_PARAM;
                return $item;
            }, $return['icons']);
        }
        return $return;
    }

    /**
     * @api {post} /site/set-user-config 用户 APP 配置项设置
     * @apiDescription 接口具有幂等性
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/set-user-config
     * @apiSampleRequest /site/set-user-config
     *
     * @apiVersion 0.1.0
     * @apiName set-user-config
     * @apiGroup site
     *
     * @apiParam {string=personalized_recommend,show_subscribe_drama,show_user_collect,message_notification} type
     * 设置的开关类型 \
     * personalized_recommend：是否接收个性化推荐 \
     * show_subscribe_drama：是否在个人主页公开“我的追剧” \
     * show_user_collect：是否在个人主页公开“我的收藏” \
     * message_notification: 消息推送设置，未登录不能设置推送消息 \
     *
     * @apiParam {String/Number} value 开关状态 0：关闭；1：打开；\
     * 当 type 不为 message_notification 时传入 int \
     * 当 type 为 message_notification 时传入所有子项开关状态并转为 JSON 字符串
     *
     * @apiSuccess {Boolean} success 请求成功或失败
     * @apiSuccess {Number} code 请求成功或失败返回码
     * @apiSuccess {String/Number} info 开关状态
     *
     * @apiSuccessExample 配置推送消息成功：
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": '{
     *         "at_me": 0,  // @ 我 \
     *         "like": 0,  // 点赞 \
     *         "comment": 1,  // 评论 \
     *         "private_message": 1,  // 私信 \
     *         "live": 0,  // 主播开播提醒 \
     *         "interest_recommend": 1  // 推送我可能感兴趣的内容 \
     *       }'
     *     }
     *
     * @apiSuccessExample 配置非推送消息成功：
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": 1
     *     }
     */
    public function actionSetUserConfig()
    {
        $type = trim(Yii::$app->request->post('type'));
        $status = $value = trim(Yii::$app->request->post('value'));
        $conf_values = [MUserConfig::APP_CONF_ENABLE, MUserConfig::APP_CONF_DISABLE];
        if ($type === MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION) {
            try {
                $value = Json::decode($value);
            } catch (Exception $e) {
                throw new HttpException(400, '参数错误');
            }
            if (!is_array($value) || !$value) {
                throw new HttpException(400, '参数错误');
            }
            $value = array_map('intval', $value);
            if (count($value) !== count(MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION_CONF)
                    || array_diff_key(MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION_CONF, $value)
                    || array_diff(array_values($value), $conf_values)) {
                throw new HttpException(400, '参数错误');
            }
        } else {
            $status = $value = (int)$value;
            if (!in_array($value, $conf_values)
                    || (!key_exists($type, array_merge(MUserConfig::APP_DEFAULT_USER_CONF,
                        MUserConfig::APP_DEFAULT_EQUIP_CONF)))) {
                throw new HttpException(400, '参数错误');
            }
        }
        $user_id = (int)Yii::$app->user->id;

        // 未登录情况不能设置追剧和收藏配置开关项、推送消息
        if (!$user_id && key_exists($type, MUserConfig::APP_DEFAULT_USER_CONF)) {
            throw new HttpException(403, '请先登录');
        }

        $buvid = Yii::$app->equip->getBuvid();
        $user_config = MUserConfig::getUserConfig($user_id, $buvid);
        if ($user_config->app_config[$type] === $value && !$user_config->isNewRecord) {
            // 若已有记录且配置项值相同，则直接返回
            return $status;
        }
        $user_config->app_config = array_merge($user_config->app_config, [$type => $value]);

        // 设置追剧或用户收藏开关时，需要清除用户的个人主页缓存，使之立即生效
        if (in_array($type, [MUserConfig::APP_CONF_TYPE_SHOW_SUBSCRIBE_DRAMA,
            MUserConfig::APP_CONF_TYPE_SHOW_USER_COLLECT])) {
            $key = MUtils2::generateCacheKey(KEY_PERSON_HOMEPAGE, $user_id);
            Yii::$app->redis->del($key);
        }

        try {
            if (!$user_config->save()) {
                // 记录错误日志
                Yii::error('用户 APP 配置项设置失败：' . MUtils::getFirstError($user_config), __METHOD__);
                throw new HttpException(500, '设置失败');
            }
        } catch (yii\db\IntegrityException $e) {
            // 可能存在并发问题，忽略唯一索引抛出的异常
            if (!MUtils2::isUniqueError($e, MUserConfig::getDb())) {
                throw $e;
            }
        }
        return $status;
    }

    /**
     * @api {get} /site/get-user-config 获取用户设置
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/get-user-config
     * @apiSampleRequest /site/get-user-config
     *
     * @apiVersion 0.1.0
     * @apiName get-user-config
     * @apiGroup site
     *
     * @apiSuccess {Boolean} success 请求成功或失败
     * @apiSuccess {Number} code 请求成功或失败返回码
     * @apiSuccess {Object} info 开关状态
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "personalized_recommend": 1,  // 表示个性化推荐开关，0 为关闭，1 为开启
     *         "show_subscribe_drama": 1,  // 表示个人主页公开“我的追剧”开关，0 为关闭，1 为开启；未登录不返回该字段
     *         "show_user_collect": 1  // 表示个人主页公开“我的收藏”开关，0 为关闭，1 为开启；未登录不返回该字段
     *         "message_notification": '{  // 消息推送设置 JSON 字符串，0 为关闭，1 为开启；未登录不返回该字段
     *           "at_me": 0,  // @ 我 \
     *           "like": 0,  // 点赞 \
     *           "comment": 1,  // 评论 \
     *           "private_message": 1,  // 私信 \
     *           "live": 0,  // 主播开播提醒 \
     *           "interest_recommend": 1  // 推送我可能感兴趣的内容 \
     *         }'
     *       }
     *     }
     */
    public function actionGetUserConfig()
    {
        $user_id = (int)Yii::$app->user->id;
        $buvid = Yii::$app->equip->getBuvid();
        $user_config = MUserConfig::getUserConfig($user_id, $buvid);
        $return = $user_config->app_config;
        if (array_key_exists(MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION, $return)) {
            $return[MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION] = Json::encode($return[MUserConfig::APP_CONF_TYPE_MESSAGE_NOTIFICATION]);
        }
        return $return;
    }

    /**
     * @api {get} /site/get-emote 获取表情包
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/get-emote
     * @apiSampleRequest site/get-emote
     *
     * @apiVersion 0.1.0
     * @apiName get-emote
     * @apiGroup site
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "package_url": "http://static.missevan.com/emote/emote.zip"
     *       }
     *     }
     */
    public function actionGetEmote()
    {
        $emote_url = Yii::$app->params['emote']['package_url'];
        if (Equipment::isAppOlderThan('4.9.9', '5.8.2')) {
            // WORKAROUND: iOS < 4.9.9、Android < 5.8.2 不支持专属表情包，下发无专属表情包的地址
            $emote_url = Yii::$app->params['emote']['no_exclusive_package_url'];
        }
        return [
            'package_url' => StorageClient::getFileUrl($emote_url),
        ];
    }

    /**
     * @api {post} /site/track-consume-pay 消费 / 充值关键行为回传
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/site/track-consume-pay
     *
     * @apiSampleRequest /site/track-consume-pay
     * @apiDescription 消费 / 充值关键行为回传，点击关键消费 / 充值关键行为按钮后就回传（回传只计首次） \
     *     主站用户消费关键行为文档：https://info.missevan.com/pages/viewpage.action?pageId=53186535 \
     *     直播用户消费关键行为文档：https://info.missevan.com/pages/viewpage.action?pageId=53183422 \
     *     消费 / 充值关键行为调用时机清单：https://info.missevan.com/pages/viewpage.action?pageId=62163105
     * @apiVersion 0.1.0
     * @apiName track-consume-pay
     * @apiGroup site
     *
     * @apiParam {number=1,2} type 回传事件类型（1: 充值；2：消费）
     * @apiParam {number=1,2} status 充值、消费状态（1: 已成功；2：尚未成功）
     * @apiParam {Number} diamond 消费或充值的钻石数（若单位不是钻石, 客户端需转为相应钻石数）
     * @apiParam {String} event_id 事件 ID
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": true  // 是否回传成功
     *     }
     */
    public function actionTrackConsumePay()
    {
        $diamond = (int)Yii::$app->request->post('diamond');
        $type = (int)Yii::$app->request->post('type');
        $status = (int)Yii::$app->request->post('status');
        $event_id = (string)Yii::$app->request->post('event_id');
        $buvid = Yii::$app->equip->getBuvid();
        // 消费 / 充值状态（1: 已成功；2: 尚未成功）
        $STATUS_SUCCEEDED = 1;
        $STATUS_NOT_SUCCEEDED = 2;
        if (!in_array($type, [AdTrack::CONVERSION_TRANSACTION_TYPE_PAY, AdTrack::CONVERSION_TRANSACTION_TYPE_CONSUME])
                || !in_array($status, [$STATUS_SUCCEEDED, $STATUS_NOT_SUCCEEDED])
                || $event_id === ''
                || $diamond < 0) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        try {
            AdTrack::callbackTransaction($buvid, $event_id);
            return true;
        } catch (Exception $e) {
            Yii::error(sprintf('消费 / 充值关键行为回传错误：%s', $e->getMessage()), __METHOD__);
            return false;
        }
    }

    /**
     * @api {get} /site/get-ip-detail 获取用户 IP 信息接口
     *
     * @apiVersion 0.1.0
     * @apiName get-ip-detail
     * @apiGroup /site
     *
     * @apiSuccess {Boolean} success true or false
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "ip": "**************",
     *         "ip_location": "北京",
     *         "isp": "电信"
     *       }
     *     }
     */
    public function actionGetIpDetail()
    {
        $ip = Yii::$app->request->userIP;
        $record = Yii::$app->serviceRpc->getLocationInfo($ip, 'zh-cn');
        if (is_null($record)) {
            $ip_location = '未知';
        } else {
            $ip_location = MUtils2::getIPLocationFromIPDetail($record);
        }
        $isp = $record['isp'] ?? '未知';
        if (!$isp) {
            $isp = '未知';
        }
        return [
            'ip' => $ip,
            'ip_location' => $ip_location,
            'isp' => $isp,
        ];
    }

    /**
     * @api {get} /site/new-user 新人 tab 页接口
     *
     * @apiVersion 0.1.0
     * @apiName new-user
     * @apiGroup site
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "blocks": [
     *           {
     *             "block_type": 1,  // 页面模块类型，1：游客广告条；2：登录用户广告条；3：自定义模块；4：剧集福袋模块。不同类型的 block 下发字段有所不同。
     *             "intro": "广告条描述内容"
     *           },
     *           {
     *             "block_type": 2,  // block 同时只会下发 1 和 2 其中一个（或都不下发）
     *             "title": "广告条标题",
     *             "intro": "广告条描述内容",
     *             "btn_title": "立即前往",  // 按钮文案
     *             "btn_url": "missevan://sound/1"  // 按钮跳转链接
     *           },
     *           {
     *             "block_type": 3,  // 页面模块类型，1：游客登录广告条；2：用户登录广告条；3：自定义模块；4：剧集福袋模块
     *             "module_id": 7,  // 自定义模块 ID
     *             "title": "燃爆全场",
     *             "type": 1,  // 模块类型。1：音单；2：剧集；3：音频；5：直播
     *             "style": 0,  // 样式。0：竖版（默认）；1：横版；2：排行榜；3：滑动
     *             "more": {  // 不下发或为空时跳转到默认的原生自定义模块详情页
     *               "url": "https://test.com/aaa?foo=bar"  // 更多按钮跳转链接，不下发或为空时跳转到默认的原生自定义模块详情页
     *             },
     *             "elements": [
     *               {
     *                 "id": 142732,  // 音单 ID
     *                 "title": "3D燃起来",  // 音单标题
     *                 "intro": "音单简介",
     *                 "front_cover": "https://static-test.maoercdn.com/test.png",  // 音单封面图
     *                 "music_count": 16,  // 音单中的音频数量
     *                 "view_count": 135718  // 音单播放量
     *               }
     *             ]
     *           },
     *           {
     *             "block_type": 3,  // 页面模块类型，1：游客登录广告条；2：用户登录广告条；3：自定义模块；4：剧集福袋模块
     *             "module_id": 8,  // 自定义模块 ID
     *             "title": "燃爆全场",
     *             "type": 2,  // 模块类型。1：音单；2：剧集；3：音频；5：直播
     *             "style": 0,  // 样式。0：竖版（默认）；1：横版；2：排行榜；3：滑动
     *             "more": {  // 不下发或为空时跳转到默认的原生自定义模块详情页
     *               "url": "https://test.com/aaa?foo=bar"  // 更多按钮跳转链接，不下发或为空时跳转到默认的原生自定义模块详情页
     *             },
     *             "elements": [
     *               {
     *                 "id": 142732,  // 剧集 ID
     *                 "name": "剧集名称",
     *                 "abstract": "剧集简介",
     *                 "cover_color": 6911652,  // 背景图主颜色，十进制表示
     *                 "integrity": 1,  // 完结度（1: 长篇未完结；2: 长篇完结；3: 全一期）
     *                 "newest": "第十八卷-725：看似稳如老狗，心里慌得一批",
     *                 "pay_type": 1,  // 付费类型（0: 免费；1: 单集付费；2: 整剧付费）
     *                 "view_count": 492021,  // 播放量
     *                 "need_pay": 1,  // 是否需要付费
     *                 "front_cover": "https://static-test.maoercdn.com/test.png",
     *                 "corner_mark": {  // 无剧集角标时不返回该字段
     *                   "text": "已购",
     *                   "text_color": "#ffffff",
     *                   "bg_start_color": "#e66465",
     *                   "bg_end_color": "#e66465",
     *                   "left_icon_url": "https://static-test.maoercdn.com/test.png"  // 无左侧图标时不返回该字段
     *                 }
     *               }
     *             ]
     *           },
     *           {
     *             "block_type": 3,
     *             "module_id": 10,
     *             "title": "音频模块标题",
     *             "type": 3,  // 模块类型，1: 音单；2: 剧集；3: 音频
     *             "style": 3,  // 排版方式，0：竖版（默认）；1：横版；2：排行榜；3：滑动
     *             "more": {
     *               "url": "https://test.com/aaa?foo=bar"  // 更多按钮跳转链接，不下发或为空时跳转到默认的原生自定义模块详情页
     *             },
     *             "elements": [
     *               {
     *                 "id": 5621487,  // 音频 ID
     *                 "front_cover": "http://static-test.maoercdn.com/coversmini/201906/10/test.jpg",  // 音频封面
     *                 "soundstr": "铁血加特林",  // 音频标题
     *                 "intro": "这是一句话简介，请在模块后台设置",
     *                 "view_count": 233,  // 播放次数
     *                 "comment_count": 0,  // 弹幕数量，模块详情页使用
     *                 "all_comments": 233,  // 总评论数量，模块推荐位使用
     *                 "user_id": 234,  // UP 主的用户 ID
     *                 "username": "UP 主的用户名",  // UP 主的用户名
     *                 "video": true  // 绑定了视频返回 true, 未绑定不返回
     *               }
     *             ]
     *           },
     *           {
     *             "block_type": 3,
     *             "module_id": 9,
     *             "title": "直播声优",
     *             "type": 5,
     *             "style": 0,
     *             "more": {
     *               "url": "https://test.com/aaa?foo=bar"
     *             },
     *             "elements": [
     *               {
     *                 "room_id": 100000,  // 直播间 ID
     *                 "title": "直播间标题",  // 直播间标题
     *                 "cover_url": "https://static-test.missevan.com/icon01.png",  // 直播间封面图
     *                 "catalog_id": 233,  // 直播间分类 ID
     *                 "catalog_name": "配音",  // 直播间分类名称
     *                 "catalog_color": "#D68DFE",  // 直播间分类颜色
     *                 "custom_tag": {  // 个性词条信息，无个性词条时不下发
     *                   "tag_id": 1000001,  // 个性词条 ID
     *                   "tag_name": "腹黑青叔"  // 个性词条名称
     *                 },
     *                 "status": 1,  // 直播间状态。0：未开播；1：开播中
     *                 "user_id": 233,  // 主播 ID
     *                 "username": "主播昵称",  // 主播昵称
     *                 "iconurl": "https://static-test.missevan.com/avatars/icon01.png"  // 主播头像
     *               }
     *             ]
     *           },
     *           {
     *             "block_type": 4,  // 页面模块类型，1：游客登录广告条；2：用户登录广告条；3：自定义模块；4：剧集福袋模块
     *             "title": "好剧免费送",
     *             "more": {  // 不下发时不展示【更多】按钮（不展示的逻辑目前仅针对 block_type 为 4 的情况）
     *               "url": "https://test.com/aaa?foo=bar"  // 更多按钮跳转链接
     *             },
     *             "data": [
     *               {
     *                 "ipr_id": 2333,  // 剧集所属 IPR ID，当剧集所属 IPR 下有福袋时下发该字段，没有时不下发
     *                 "ipr_name": "魔道祖师",  // 剧集所属 IPR 名，当剧集所属 IPR 下有福袋时下发该字段，没有时不下发
     *                 "drama_id": 2334,  // 剧集 ID，当剧集不属于 IPR 且剧集下有福袋时下发该字段，没有时不下发，不会和 IPR 信息同时下发
     *                 "drama_name": "烟火",  // 剧名，当剧集不属于 IPR 且剧集下有福袋时下发该字段，没有时不下发，不会和 IPR 信息同时下发
     *                 "cover_url": "https://static-test.maoercdn/cover.png",  // 剧集封面
     *                 "num": 5,  // 当前正在发放此剧集福袋的直播间数量
     *                 "trace": "{\"drama_id\":2334,\"ipr_id\":0,\"num\":5,\"enter_room_id\":100000}",
     *                 "rooms": [  // 排名前三的直播间信息
     *                   {
     *                     "room_id": 100000,  // 直播间 ID
     *                     "name": "直播间标题",  // 直播间标题
     *                     "creator_id": 11,  // 主播 ID
     *                     "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"  // 主播头像
     *                   },
     *                   {
     *                     "room_id": 100001,
     *                     "name": "直播间标题",
     *                     "creator_id": 12,
     *                     "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"
     *                   },
     *                   {
     *                     "room_id": 100002,
     *                     "name": "直播间标题",
     *                     "creator_id": 13,
     *                     "creator_iconurl": "https://static-test.maoercdn.com/icon01.png"
     *                   }
     *                 ]
     *               }
     *             ]
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionNewUser()
    {
        $blocks = [];
        // 获取新人广告条
        $banner = [];
        $guest_banner = Yii::$app->params['new_user']['banner']['guest'] ?? null;
        $user_banner = Yii::$app->params['new_user']['banner']['user'] ?? null;
        $user_id = (int)Yii::$app->user->id;
        if (!$user_id && $guest_banner) {
            // 游客广告条内容（若未配置，则不展示）
            $banner = $guest_banner;
            $banner['block_type'] = self::NEW_USER_BLOCK_TYPE_GUEST_BANNER;
        } elseif ($user_id && $user_banner && !Equipment::isFromGoogleChannel()
                && !Equipment::isAppOlderThan('6.0.4', '6.0.4')) {
            // 登录用户广告条内容（若未配置，则不展示）
            // 登录用户广告条不对谷歌渠道下发
            // WORKAROUND: 登录用户广告条不对小于 6.0.4 的版本下发
            $banner = $user_banner;
            $banner['block_type'] = self::NEW_USER_BLOCK_TYPE_USER_BANNER;
        }
        if ($banner) {
            $blocks[] = $banner;
        }

        // 获取福袋模块信息
        $luckybags_module = self::getNewUserLuckybagsModule();
        if ($luckybags_module) {
            $blocks[] = $luckybags_module;
        }

        // 获取自定义模块
        $custom_modules_data = YouMightLikeModule::getPersonModules(MPersonaModuleElement::PERSONA_ID_NEW_USER);
        if (!empty($custom_modules_data[0])) {
            $custom_modules = array_map(function ($module) {
                $module['block_type'] = self::NEW_USER_BLOCK_TYPE_CUSTOM_MODULE;
                if (isset($module['more']['url'])) {
                    $module['more']['url'] = MUtils::getUsableAppLink($module['more']['url']);
                }
                // 根据模块样式截取模块数据
                YouMightLikeModule::truncateModule($module, MPersonaModuleElement::PERSONA_ID_NEW_USER);
                if (!$module) {
                    return null;
                }
                // 剧集模块，根据模块样式选择使用不同的封面图
                YouMightLikeModule::processDramaModuleCover($module);
                return $module;
            }, $custom_modules_data[0]);
            $custom_modules = array_values(array_filter($custom_modules));
            if (!empty($custom_modules)) {
                // 给剧集模块补充剧集角标信息
                Drama::fillCornerMarkInDramaModules($custom_modules, $user_id);
                array_push($blocks, ...$custom_modules);
            }
        }
        return ['blocks' => $blocks];
    }

    /**
     * 获取新人 Tab 页福袋模块信息
     *
     * @return array|null 福袋模块信息。若无福袋或者获取福袋信息出错，返回 null
     */
    private static function getNewUserLuckybagsModule(): ?array
    {
        if (Equipment::isAppOlderThan('6.1.8', '6.1.8')) {
            // WORKAROUND: Android < 6.1.8、iOS < 6.1.8 的客户端版本不支持福袋模块，故不下发
            return null;
        }
        try {
            $luckybag_config = Yii::$app->params['new_user']['luckybag'] ?? null;
            if (!$luckybag_config) {
                return null;
            }
            $block = [
                'block_type' => self::NEW_USER_BLOCK_TYPE_LUCKYBAG,
                'title' => $luckybag_config['title'],
            ];
            // 新人 Tab 页仅展示 2 个剧集福袋的信息
            $luckybags_info = Yii::$app->liveRpc->getLuckyBagDramaList(2);
            if (empty($luckybags_info['data'])) {
                return null;
            }
            if ($luckybags_info['has_more']) {
                // 若有更多福袋，则下发"更多"按钮跳转地址
                $block['more'] = ['url' => $luckybag_config['more_url']];
            }
            $block['data'] = array_map(function ($luckybag_info) {
                $trace = [
                    'drama_id' => $luckybag_info['drama_id'] ?? 0,
                    'ipr_id' => $luckybag_info['ipr_id'] ?? 0,
                    'num' => $luckybag_info['num'],
                    'enter_room_id' => $luckybag_info['rooms'][0]['room_id'],
                ];
                $luckybag_info['trace'] = Json::encode($trace);
                return $luckybag_info;
            }, $luckybags_info['data']);
            return $block;
        } catch (Exception $e) {
            Yii::error("获取福袋列表出错：{$e->getMessage()}", __METHOD__);
            // PASS: 若获取剧集福袋列表出错，记录日志后忽略该错误，避免影响用户查看新人 Tab 页其他信息
            return null;
        }
    }

    /**
     * @api {post} /site/launch-report 启动上报接口
     *
     * @apiDescription 客户端冷启动时调用此接口上报设备参数
     *
     * @apiVersion 0.1.0
     * @apiName launch-report
     * @apiGroup site
     *
     * @apiParam {String} data 参数（json 格式数据进行 base64 编码后的值）
     *
     * @apiParam (Android data 参数) {number=0,1} is_root 是否越狱（0 否；1 是）
     * @apiParam (Android data 参数) {String} ct 渠道标识
     * @apiParam (Android data 参数) {String} mac 网卡地址
     * @apiParam (Android data 参数) {String} imei IMEI
     * @apiParam (Android data 参数) {String} android_id Android ID
     * @apiParam (Android data 参数) {String} adid 广告 ID
     * @apiParam (Android data 参数) {String} buvid 唯一设备标识
     * @apiParam (Android data 参数) {String} oaid Android 匿名设备标识
     * @apiParam (Android data 参数) {String} drm_id 基于 Android DRM 生成的 ID
     * @apiParam (Android data 参数) {String} user_agent 设备 User Agent（系统 webview 默认的 User Agent）
     * @apiParam (Android data 参数) {String} screen_resolution 手机屏幕分辨率（宽x高），例：1080x1920（注：是屏幕的宽高，如果画中画模式或平板分屏的话，仍需要传完整的屏幕尺寸）
     * @apiParam (Android data 参数) {String} screen_native_resolution 设备原始分辨率（宽x高）。单位：px
     * @apiParam (Android data 参数) {Number} screen_dpr 设备像素比（为浮点数），例 1.75
     *
     * @apiParam (iOS data 参数) {String} idfa iOS IDFA
     * @apiParam (iOS data 参数) {String} idfv iOS IDFV
     * @apiParam (iOS data 参数) {String} user_agent 设备 User Agent（系统 webview 默认的 User Agent）
     * @apiParam (iOS data 参数) {String} buvid 唯一设备标识
     * @apiParam (iOS data 参数) {String} caid iOS caid 参数（json 格式，按原值传，不需要单独进行 base64 编码）
     * @apiParam (iOS data 参数) {String} screen_resolution 手机屏幕分辨率（宽x高），例：1080x1920（注：是屏幕的宽高，如果画中画模式或平板分屏的话，仍需要传完整的屏幕尺寸）
     * @apiParam (iOS data 参数) {String} screen_native_resolution 设备原始分辨率（宽x高）。单位：px
     * @apiParam (iOS data 参数) {Number} screen_dpr 设备像素比（为浮点数），例 1.75
     *
     * @apiParamExample {String} Android data 参数（json 数据进行 base64 编码后的值）
     *     base64({
     *       "is_root": 0,  // 是否越狱（0 否；1 是）
     *       "ct": "missevan",  // 渠道标识
     *       "mac": "f2:b6:43:a2:50:40",  // 网卡地址
     *       "imei": "",  // IMEI
     *       "android_id": "d517f85fbf127a96",  // Android ID
     *       "adid": "d517f85fbf127a96",  // 广告 ID
     *       "buvid": "XY216D524F4E70B5E1E1C24BE2163430C3198",  // 唯一设备标识
     *       "oaid": "ef7e2efb-fcf9-90b3-d42f-feffe95706f2",  // Android 匿名设备标识
     *       "drm_id": "2BCE75C41C01956690674E1E58721908",  // 基于 Android DRM 生成的 ID
     *       "user_agent": "Mozilla/5.0 (Linux; Android 10; OXF-AN00 Build/HUAWEIOXF-AN00; wv) AppleWebKit/537.36(KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.93 Mobile Safari/537.36",  // 设备 User Agent（系统 webview 默认的 User Agent）
     *       "screen_resolution": "1080x1920",  // 手机屏幕分辨率（宽x高）
     *       "screen_dpr": 1.75  // 设备像素比
     *     })
     *
     * @apiParamExample {String} iOS data 参数（json 数据进行 base64 编码后的值）
     *     base64({
     *       "idfa": "2cc60d08-d8b2-4052-b781-2bb73f99fa31",  // IDFA
     *       "idfv": "eb20c9be-ab73-4df3-999e-856a6ce57719",  // IDFV
     *       "user_agent": "Mozilla\/5.0 (iPhone; CPU iPhone OS 14_1 like Mac OS X) AppleWebKit\/610.*********(KHTML,like Gecko) Mobile\/18A8395",  // 设备 User Agent（系统 webview 默认的 User Agent）
     *       "buvid": "Y0496F0A8F724F6845818E2BCD9C9D20B4D6",  // buvid
     *       "caid": "{\"carrierInfo\":\"中国联通\",\"machine\":\"iPhone10,3\",\"sysFileTime\":\"1613802564.660653\", ......}",  // iOS caid 参数（json 格式，按原值传，不需要单独进行 base64 编码）
     *       "screen_resolution": "1080x1920",  // 手机屏幕分辨率（宽x高）
     *       "screen_dpr": 1.75  // 设备像素比
     *     })
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "msg": "上报成功"
     *       }
     *     }
     */
    public function actionLaunchReport()
    {
        $data = Yii::$app->request->post('data');
        if (!$data || !$params = Json::decode(base64_decode($data))) {
            throw new HttpException(400, '参数错误');
        }
        $equipment = Yii::$app->equip;
        $equipment->assertEquipIdValid();

        $os = $equipment->getOs();
        $ip = Yii::$app->request->userIP;
        $user_agent = $params['user_agent'] ?? '';
        $launch_report_log = new LaunchReportLog();
        $launch_report_log->device_type = $os;
        $launch_report_log->equip_id = $equipment->getEquipId();
        $launch_report_log->user_agent = $user_agent;
        $launch_report_log->ip = $ip;
        $launch_report_log->buvid = $params['buvid'] ?? '';
        $launch_report_log->version = $equipment->getAppVersion();
        $more = [];

        $screen_resolution = $params['screen_resolution'] ?? '';
        if ($screen_resolution) {
            // 手机屏幕分辨率（宽x高）
            $more['screen_resolution'] = $screen_resolution;
        }
        $screen_native_resolution = $params['screen_native_resolution'] ?? '';
        if ($screen_native_resolution) {
            $more['screen_native_resolution'] = $screen_native_resolution;
        }
        $screen_dpr = $params['screen_dpr'] ?? null;
        if ($screen_dpr) {
            // 设备像素比（为浮点数，四舍五入保留两位小数），例 1.75
            $more['screen_dpr'] = (string)round($screen_dpr, 2);
        }
        if ($user_agent) {
            // 解析 user_agent
            $device_info = MUtils2::parseUserAgent($user_agent);
            $more['device_info'] = $device_info;
        }
        switch ($os) {
            case Equipment::Android:
            case Equipment::HarmonyOS:
                $is_root = $params['is_root'] ?? LaunchReportLog::IS_NOT_ROOT;
                if (!in_array($is_root, [LaunchReportLog::IS_NOT_ROOT, LaunchReportLog::IS_ROOT])) {
                    throw new HttpException(400, '参数错误');
                }
                $launch_report_log->is_root = $is_root;
                $launch_report_log->ct = $params['ct'] ?? '';
                $launch_report_log->adid = $params['adid'] ?? '';

                $mac = $params['mac'] ?? null;
                if ($mac && preg_match('/([A-Fa-f0-9]{2}:){5}[A-Fa-f0-9]{2}/', $mac)) {
                    $launch_report_log->mac = strtoupper($mac);
                    // 用户终端的 eth0 接口的 MAC 地址（大写保留冒号分隔符）以 MD5 加密
                    $more['mac_md5'] = md5($launch_report_log->mac);
                }

                $imei = $params['imei'] ?? null;
                if ($imei) {
                    $launch_report_log->imei = $imei;
                    $more['imei_md5'] = md5($imei);
                }

                $android_id = $params['android_id'] ?? null;
                if ($android_id) {
                    $launch_report_log->android_id = $android_id;
                    $more['android_id_md5'] = md5($android_id);
                }

                $oaid = $params['oaid'] ?? null;
                if ($oaid) {
                    $launch_report_log->oaid = $oaid;
                    $more['oaid_md5'] = md5($oaid);
                }

                $drm_id = $params['drm_id'] ?? null;
                if ($drm_id) {
                    $more['drm_id'] = $drm_id;
                }
                break;
            case Equipment::iOS:
                $launch_report_log->idfa = $params['idfa'] ?? '';
                $launch_report_log->idfv = $params['idfv'] ?? '';
                $caid = $params['caid'] ?? '';
                if ($caid && $caid_params = Json::decode($caid)) {
                    $caid_info = new DeviceCAIDInfo($caid_params);
                    if ($caid_map = $caid_info->generateCAID()) {
                        $more['caid'] = $caid_map;
                    }
                }
                break;
            default:
                throw new HttpException(400, '不支持的系统类型');
        }
        if ($location_info = Yii::$app->serviceRpc->getLocationInfo($ip)) {
            $more['location_info'] = $location_info;
        }
        if ($more) {
            $launch_report_log->more = $more;
        }
        if (!$launch_report_log->validate()) {
            throw new HttpException(400, MUtils2::getFirstError($launch_report_log) ?: '参数有误');
        }
        if (!$launch_report_log->ignoreExceptionSave(false, '启动上报接口保存记录失败')) {
            throw new HttpException(500, '服务器内部错误');
        }
        return ['msg' => '上报成功'];
    }
}
