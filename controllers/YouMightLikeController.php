<?php

namespace app\controllers;

use app\components\service\biliai\RecommendParams;
use app\components\util\Equipment;
use app\components\util\MUtils;
use app\models\Blacklist;
use app\models\Drama;
use app\models\GuessYourLikesClicks;
use app\models\Live;
use app\models\MEvent;
use app\models\MGameCenter;
use app\models\MHomepageRank;
use app\models\MPersonaRank;
use app\models\MRecommendPopup;
use app\models\MUserConfig;
use app\models\MVideoCard;
use app\models\PaginationParams;
use app\models\RecommendBase;
use app\models\SoundVideo;
use app\models\UserAddendum;
use app\models\YouMightLikeModule;
use Exception;
use missevan\storage\StorageClient;
use Yii;
use app\middlewares\Controller;
use app\models\FavorTags;
use app\models\MPersonaModuleElement;
use app\models\Persona;
use app\components\base\filter\AccessControl;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use yii\web\HttpException;

class YouMightLikeController extends Controller
{
    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'collect-clicks' => ['post'],
                'save-my-favor' => ['post'],
                'sync-persona' => ['post'],
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'only' => ['sync-persona'],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => ['sync-persona'],
                    'roles' => ['@']
                ],
            ]
        ];
        return $behaviors;
    }

    /**
     * @api {get} /you-might-like/choose-my-favor 用户选择性别及标签
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/you-might-like/choose-my-favor
     * @apiSampleRequest /you-might-like/choose-my-favor
     *
     * @apiVersion 0.1.0
     * @apiName choose-my-favor
     * @apiGroup you-might-like
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": [
     *         "male": [],
     *         "female": []
     *       ]
     *     }
     */
    public function actionChooseMyFavor()
    {
        $tags = FavorTags::getTags();
        return $tags;
    }

    /**
     * @api {post} /you-might-like/save-my-favor 保存所选择的性别标签
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/you-might-like/save-my-favor
     * @apiSampleRequest /you-might-like/save-my-favor
     *
     * @apiVersion 0.1.0
     * @apiName save-my-favor
     * @apiGroup you-might-like
     *
     * @apiParam {Number} gender 性别（1：男，2：女）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": 2 // 画像 ID
     *     }
     */
    public function actionSaveMyFavor()
    {
        $gender = (int)Yii::$app->request->post('gender');
        $equip_id = Yii::$app->equip->getEquipId();
        $buvid = Yii::$app->equip->getBuvid();

        if (!UserAddendum::checkGender($gender)) {
            throw new HttpException(400, '参数错误', 201010002);
        }
        $user_id = (int)Yii::$app->user->id;
        $persona_id = UserAddendum::MALE === $gender ? Persona::TYPE_BOY : Persona::TYPE_GIRL;
        $persona = Yii::$app->serviceRpc->setPersona($persona_id, $equip_id, $buvid, $user_id);
        if ($user_id) {
            UserAddendum::setSex($user_id, $gender);
        }
        return $persona['persona'];
    }

    /**
     * @api {post} /you-might-like/sync-persona 登录后同步账号画像
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/you-might-like/sync-persona
     * @apiSampleRequest /you-might-like/sync-persona
     *
     * @apiVersion 0.1.0
     * @apiName sync-persona
     * @apiGroup you-might-like
     *
     * @apiParam {Number} persona_id 画像 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": 2 // 画像 ID
     *     }
     */
    public function actionSyncPersona()
    {
        $persona_id = (int)Yii::$app->request->post('persona_id');
        if ($persona_id === -1) {
            // 用户首次安装去登录的时候 persona_id 参数为 -1
            $persona_id = Persona::TYPE_GIRL;
        } elseif ($persona_id === 0 || $persona_id < -1) {
            throw new HttpException(400, '参数错误', 201010002);
        }
        $user_id = (int)Yii::$app->user->id;
        $equip_id = Yii::$app->equip->getEquipId();
        $buvid = Yii::$app->equip->getBuvid();
        $persona = Yii::$app->serviceRpc->getPersona($equip_id, $buvid, $user_id);
        // 若用户已有画像，则画像分数不同步
        if ($persona['is_new_user']) {
            $persona = Yii::$app->serviceRpc->setPersona($persona_id, $equip_id, $buvid, $user_id, true);
        }
        return $persona['persona'];
    }

    /**
     * @api {get} /you-might-like/get-persona 获取画像 ID
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/you-might-like/get-persona
     * @apiSampleRequest you-might-like/get-persona
     *
     * @apiVersion 0.1.0
     * @apiName get-persona
     * @apiGroup you-might-like
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": 2 // 画像 ID，默认返回女用户画像
     *     }
     */
    public function actionGetPersona()
    {
        // WORKAROUND: AB Test 策略分组，目前无对应策略，暂不执行
        // Persona::abTestGetLikeSounds();
        return Persona::getPersonaType();
    }

    /**
     * @api {get} /you-might-like/get-recommends 猜你喜欢推荐音
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/you-might-like/get-recommends
     * @apiSampleRequest you-might-like/get-recommends
     *
     * @apiVersion 0.1.0
     * @apiName get-recommends
     * @apiGroup you-might-like
     *
     * @apiParam {Number} persona_id 画像 ID
     * @apiParam {String} marker 直播或广告位标识，如：live:1,ad:2，为默认值的时候注意不需要补充对应的 marker，
     * 如没有直播有 ad 时候为 marker=ad:2，都没有的时候不传递该参数，该参数值为上次请求本接口响应的 marker 值，
     * 客户端持久保存并在每次请求后更新，若为安装后初次请求不传该值
     * @apiParam {number=0,1} [boot=0] 是否为启动客户端后请求，0：否，1：是， iOS 4.5.3 及安卓 5.4.3 版本之后传入
     * @apiParam {number{0-4}} [network=0] 用户当前网络状况（同埋点上报参数约定值，即：0: UNKNOWN; 1: WIFI; 2: CELLULAR; 3: OFFLINE; 4: OTHERNET）
     * @apiParam {number{0-3}} refresh_type 刷新方式，取值说明：\
     * 0：默认值，冷启动时也为 0 \
     * 1：自动刷新 \
     * 2：下拉刷新 \
     * 3：点击换一批
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info":
     *         "data": [
     *           {
     *             "id": 37858,
     *             "soundstr": "ten countのdrama 番外 西装",  // 猜你喜欢或小编推荐模块元素（剧集或音频）标题
     *             "catalog_id": 33,
     *             "create_time": 1432973383,
     *             "duration": 132754,
     *             "view_count": 47819,  // 猜你喜欢或小编推荐模块元素（剧集或音频）播放量
     *             "all_comments": 518,  // 猜你喜欢或小编推荐模块元素（剧集或音频）评论数，不下发该字段时客户端不展示评论数
     *             "video": false,
     *             "type": 3,  // 音频类型为 3
     *             "front_cover": "https://static.missevan.com/coversmini/201505/30/1dfc88c8b80872a03a6160943.png",  // 猜你喜欢或小编推荐模块元素（剧集或音频）封面
     *             "track_id": "ott_pegasus_0.jscs-ai-dev-15::kapu.1731056303651.0",
     *             "trace": "{\"refresh_type\":1,\"refresh_num\":3,\"attr\":1}",
     *           },
     *           {
     *             "marker_id": 233,  // 当前推荐直播标记，iOS 低于 4.4.0 版本，Android 低于 5.3.1 版本时返回
     *             "room_id": 20,
     *             "name": "你还哈哈",
     *             "creator_username": "加特林",
     *             "creator_id": 233,
     *             "type": 5,  // 直播间类型为 5
     *             "cover_url": "https://static.missevan.com/coversmini/201701/24/244f2d77942e3a8469092720.png",
     *             "track_id": "ott_pegasus_0.jscs-ai-dev-15::kapu.1731056303651.0",
     *             "trace": "{\"refresh_type\":1,\"refresh_num\":3,\"attr\":3}"
     *           }
     *         ],
     *         "marker": "live:233,ad:666,refresh_num:1",  // 无对应 marker_id 时不含该参数，如无直播推荐则为：ad:666,refresh_num:1
     *         "popup_url": "http://static.missevan.com/standalone/app/test/index.html",  // 无推荐弹窗的情况下不含该参数
     *         "open_url": "missevan://live/463640018?from=ad.0.1.80000_0_0_0&event_id_from=ad.0.1.80000_0_0_0",  // msr-0 地址，无跳转地址时不含该参数，同时支持直接打开网页，与 popup_url 不同的是这边是正常跳转到 webview，而不是首页推荐的 webview 弹窗
     *         "title": "猜你喜欢"
     *       }
     *     }
     */
    public function actionGetRecommends(int $persona_id, string $marker = '', int $boot = 0)
    {
        $return = [];
        $buvid = Yii::$app->equip->getBuvid();
        $network = (int)Yii::$app->request->get('network', -1);
        $refresh_type = (int)Yii::$app->request->get('refresh_type', -1);
        if ($persona_id !== 0) {
            // 按用户画像获取猜你喜欢音频
            $persona_module = $persona_id & Persona::PERSONA_MODULE_MASK;
            $user_id = (int)Yii::$app->user->id;
            $enable_personalized = MUserConfig::isAppConfigEnable($user_id, $buvid,
                MUserConfig::APP_CONF_TYPE_PERSONALIZED_RECOMMEND);
            if (!$enable_personalized) {
                // 若未开启自定义推荐，则使用固定画像
                // TODO: 之后考虑将非自定义推荐池和画像脱钩
                // WORKAROUND: 暂时使用已设置的画像推荐池
                $persona_module = Persona::isFemale($persona_id) ? Persona::TYPE_MANUAL_GIRL : Persona::TYPE_MANUAL_BOY;
            } elseif (!Persona::validatePersona($persona_module)) {
                // 若用户画像不在定义的范围内，则默认为女用户
                $persona_module = Persona::TYPE_GIRL;
            }
            $marker = $this->markerDecode($marker);
            // 冷启时重置 refresh_num
            if ($boot) {
                $marker['refresh_num'] = 0;
            }
            if ($refresh_type !== -1) {
                $marker['refresh_num']++;
            } else {
                unset($marker['refresh_num']);
            }

            $params = new RecommendParams(Yii::$app->request, Yii::$app->equip, Yii::$app->user, $persona_module,
                $network, $refresh_type, $marker['refresh_num'] ?? -1);

            $recommends = RecommendBase::getRecommends($params, $marker, $boot, $network, $enable_personalized);
            $return = [
                'data' => $recommends,
                'marker' => $this->markerEncode($marker),
                'title' => $enable_personalized ? '猜你喜欢' : '小编推荐',
            ];
        }
        if (Equipment::Web !== Yii::$app->equip->getOs()) {
            $channel = Yii::$app->equip->getChannel();
            $popup_urls = MRecommendPopup::getPopupUrls($channel, $buvid, Yii::$app->equip->getEquipId());
            $return = array_merge($return, $popup_urls);
        }
        return $return ?: new \stdClass();
    }

    /**
     * 解析“猜你喜欢”marker 标记值
     *
     * @param array $marker_arr 待编码的标记字符串，如： ['live' => 1, 'ad' => 233]
     * @return string 返回标记值，格式为：live:{live_id},ad:{sound_id}，如：live:1,ad:233
     */
    private function markerDecode(string $marker_str): array
    {
        $return = [
            'live' => 0,
            'ad' => 0,
            'refresh_num' => 0,
        ];
        if ($marker_str) {
            $markers = explode(',', $marker_str);
            $marker_arr = [];
            foreach ($markers as $marker) {
                $marker = explode(':', $marker);
                if (!empty($marker)) {
                    $marker_arr[$marker[0]] = (int)$marker[1];
                }
            }
            $return = array_merge($return, $marker_arr);
        }
        return $return;
    }

    /**
     * 编码“猜你喜欢”marker 标记值
     *
     * @param string $marker_str 待解析的标记数组，格式为：live:{live_id},ad:{sound_id}，如：live:1,ad:2
     * @return array 返回标记值，如 ['live' => 1, 'ad' => 1]
     */
    private function markerEncode(array $marker_arr): string
    {
        $markers = [];
        foreach ($marker_arr as $marker_name => $value) {
            if ($value) {
                // 仅在 marker 有值时返回该标记
                $markers[] = "{$marker_name}:{$value}";
            }
        }
        $marker_str = implode(',', $markers);
        return $marker_str;
    }

    /**
     * @api {get} /you-might-like/my-favors 用户的推荐模块
     *
     * @apiVersion 0.1.0
     * @apiName my-favors
     * @apiGroup you-might-like
     *
     * @apiParam {Number} persona_id 画像 ID
     * @apiParam {number=0,1} [is_all=0] 是否获取所有数据 0：返回所需数据；1：返回所有数据
     * @apiParam {number=0,1} [is_mrpc=0] 是否为 mrpc 请求 0：否；1：是
     * @apiParam {String} marker 随机标识，格式为“随机数:当前序列索引”，第一次请求时不传该值
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "video_card": {  // 推荐模块中的视频大卡信息，无视频大卡时不返回该字段
     *           "1": {  // 位置，没有数据时不返回该字段，1: “猜你喜欢”模块下方；2: 自定义模块第一个模块下方
     *             "type": 1,  // 视频大卡类型，1: 普通视频大卡；2: 游戏视频大卡
     *             "id": 21,  // 视频大卡 ID
     *             "title": "视频标题",
     *             "cover": "http://static-test.maoercdn.com/test/test.jpg",  // 封面
     *             "sound_id": 2333,  // 音频 ID（客户端播放失败后可以用该参数请求 sound/sound 接口获取新的播放地址重试，如果没下发该字段，客户端对于播放失败的场景无需请求接口重试）
     *             "url": "http://www.test.com",  // 视频落地页链接，该字段不存在时，跳转到视频播放页
     *             "priority": 1,  // 是否优先于音频播放，0: 否；1: 是（从视频大卡进入播放页时，直接续播视频）
     *             "duration": 1000,  // 视频时长，单位 ms
     *             "resources": [
     *               {
     *                 "quality": 32,  // 视频质量，16: 360P；32: 480P；64: 720P；128: 1080P
     *                 "name": "480P 标清",
     *                 "short_name": "480P",
     *                 "url": "https://www.test.com/xxx/xxx.mp4?foo=bar",  // 视频地址为签名地址，有过期时间
     *                 "size": 1024,  // 视频大小，单位 Bytes
     *                 "width": 854,  // 视频宽度
     *                 "height": 480,  // 视频高度
     *                 "status": 1  // 视频播放状态，比特位第一位为 1 时表示需要登录后才能播放
     *               },
     *               {
     *                 "quality": 16,
     *                 "name": "360P 标清",
     *                 "short_name": "480P",
     *                 "url": "https://www.test.com/xxx/xxx.mp4?foo=bar",
     *                 "size": 1024,
     *                 "width": 640,
     *                 "height": 360,
     *                 "status": 1
     *               }
     *             ]
     *           },
     *           "2": {
     *             "type": 2,  // 视频大卡类型，1: 普通视频大卡；2: 游戏视频大卡
     *             "id": 21,  // 视频大卡 ID
     *             "title": "视频标题",
     *             "cover": "http://static-test.maoercdn.com/test/test.jpg",  // 封面
     *             "url": "http://www.test.com",  // 视频落地页链接，该字段不存在时，跳转到视频播放页
     *             "priority": 1,  // 是否优先于音频播放，0: 否；1: 是（从视频大卡进入播放页时，直接续播视频）
     *             "duration": 1000,  // 视频时长，单位 ms
     *             "resources": [
     *               {
     *                 "quality": 32,  // 视频质量，16: 360P；32: 480P；64: 720P；128: 1080P
     *                 "name": "480P 标清",
     *                 "short_name": "480P",
     *                 "url": "https://www.test.com/xxx/xxx.mp4?foo=bar",  // 视频地址为签名地址，有过期时间
     *                 "size": 1024,  // 视频大小，单位 Bytes
     *                 "width": 854,  // 视频宽度
     *                 "height": 480,  // 视频高度
     *                 "status": 1  // 视频播放状态，比特位第一位为 1 时表示需要登录后才能播放
     *               },
     *               {
     *                 "quality": 16,
     *                 "name": "360P 标清",
     *                 "short_name": "480P",
     *                 "url": "https://www.test.com/xxx/xxx.mp4?foo=bar",
     *                 "size": 1024,
     *                 "width": 640,
     *                 "height": 360,
     *                 "status": 1
     *               }
     *             ],
     *             "game_card": {
     *               "id": 2,
     *               "name": "游戏名称",  // 下载时用于通知栏显示
     *               "url": "http://test.com/mevent/102718",  // 点击游戏预约模块（及“查看”按钮）跳转链接
     *               "icon": "https://static.missevan.com/game/images/icon.jpg",  // 游戏图标
     *               "title": "游戏xxx",  // 标题
     *               "subtitle": "游戏xxx预约中",  // 副标题
     *               "status": 1,  // 状态，1: 未预约；2: 已预约；3: 开放下载
     *               "download_url": "https://www.test.com/x/gamecenter/download?game_id=2&os=1",  // 仅 Android 返回
     *               "package_name": "com.missevan.app",  // 仅 Android 返回
     *               "package_version_code": 1  // 仅 Android 返回
     *             }
     *           }
     *         },
     *         "ranks": {  // 排行榜模块数据，无排行榜模块数据时不返回该字段
     *           "title": "排行榜",  // 模块标题
     *           "more": {  // 没有 more 字段的时候不显示更多按钮
     *             "url": "https://test.com/aaa?type=1"  // 更多跳转链接
     *           },
     *           "data": [
     *             {
     *               "type": 1,  // 榜单类型（1: 新品榜；2: 人气榜；3: 打赏榜；4: 免费榜；5: 言情榜；6: 声音恋人榜；7：直播榜）
     *               "name": "新品榜",  // 榜单名称
     *               "active": 1,  // 默认定位到该榜单，不默认定位到该榜单时不下发
     *               "open_url": "https://test.com/aaa?type=1",  // 跳转链接（跳转到榜单详情页，定位到相应的榜单的第一个子类型榜单）
     *               "element_type": 2,  // 榜单内容类型（2: 剧集；3: 音频；5: 直播）
     *               "elements": [  // 榜单数据前三个
     *                 {
     *                   "id": 9888,  // 剧集 ID
     *                   "name": "《杀破狼》广播剧",  // 剧集名称
     *                   "cover": "https://test.com/test.jpg",  // 剧集封面
     *                   "abstract": "729声工场出品，晋江文学城 作者：priest 原著",  // 剧集简介
     *                   "cover_color": 5402961,  // 背景图主颜色，十进制表示
     *                   "pay_type": 2,  // 付费类型（0: 免费；1: 单集付费；2: 整剧付费）
     *                   "view_count": 12345,  // 播放量
     *                   "integrity": 1,  // 完结度（1: 长篇未完结；2: 长篇完结；3: 全一期）
     *                   "newest": "第四期",  // 最近更新
     *                   "corner_mark": {  // 无剧集角标时不返回该字段
     *                     "text": "已购",
     *                     "text_color": "#ffffff",
     *                     "bg_start_color": "#e66465",
     *                     "bg_end_color": "#e66465",
     *                     "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *                   }
     *                 }
     *               ]
     *             },
     *             {
     *               "type": 6,  // 榜单类型（6: 声音恋人榜）
     *               "name": "声音恋人榜",  // 榜单名称
     *               "open_url": "https://test.com/aaa?type=6",  // 跳转链接（跳转到榜单详情页，定位到相应的榜单的第一个子类型榜单）
     *               "element_type": 3,  // 榜单内容类型（2: 剧集；3: 音频；5: 直播）
     *               "elements": [  // 榜单数据前三个
     *                 {
     *                   "id": 1462573,  // 音频 ID
     *                   "duration": 21326,  // 音频时长（单位：毫秒）
     *                   "soundstr": "迪奥光宗登场铃声",  // 音频标题
     *                   "view_count": 12345,  // 播放量
     *                   "front_cover": "https://test.com/test.jpg",  // 音频封面
     *                   "pay_type": 1,  // 付费类型（0: 免费；1: 单集付费；2: 整剧付费）
     *                   "user_id": 4983837,  // UP 主用户 ID
     *                   "username": "扶暖文化",  // UP 主用户名称
     *                   "iconurl": "https://test.com/profile/icon01.png",  // UP 主头像
     *                   "video": true  // 音频是否包含对应的视频
     *                 }
     *               ]
     *             },
     *             {
     *               "type": 7,  // 榜单类型（7：直播榜）
     *               "name": "直播榜",
     *               "open_url": "https://test.com/aaa?type=7",  // 跳转链接（跳转到榜单详情页，定位到直播榜）
     *               "element_type": 5,  // 榜单内容类型（2: 剧集；3: 音频；5: 直播）
     *               "elements": [  // 榜单数据前三个
     *                 {
     *                   "user_id": 9467681,  // 用户 ID
     *                   "username": "2Fire",  // 用户名称
     *                   "iconurl": "https://test.com/profile/icon01.png",  // 用户头像
     *                   "revenue": 60927,  // 收益（分数值）
     *                   "rank_up": 10131,  // 前一名的收益 - 自己的收益 + 1
     *                   "room": {  // 直播间信息
     *                     "room_id": 868858629,
     *                     "catalog_id": 145,
     *                     "name": "听歌进",
     *                     "announcement": "欢迎来到我的直播间！",
     *                     "creator_id": 9467681,
     *                     "creator_username": "2Fire",
     *                     "status": {
     *                       "open": 1
     *                     },
     *                     "cover_url": "https://test.com/fmcovers/test.jpg"  // 直播间封面
     *                   }
     *                 }
     *               ]
     *             }
     *           ]
     *         },
     *         "modules": [
     *           {
     *             "module_id": 9,
     *             "title": "音频模块标题",
     *             "type": 3,  // 模块类型，1: 音单；2: 剧集；3: 音频
     *             "style": 3,  // 排版方式，0：竖版；1：横版；2：排行榜；3：滑动
     *             "more": {
     *               "url": "https://test.com/aaa?foo=bar"  // 更多按钮跳转链接，不下发或为空时跳转到默认的原生自定义模块详情页
     *             },
     *             "elements": [
     *               {
     *                 "id": 5621487,  // 音频 ID
     *                 "front_cover": "http://static-test.maoercdn.com/coversmini/201906/10/test.jpg",  // 音频封面
     *                 "soundstr": "铁血加特林",  // 音频标题
     *                 "intro": "这是一句话简介，请在模块后台设置",
     *                 "view_count": 233,  // 播放次数
     *                 "comment_count": 0,  // 弹幕数量，模块详情页使用
     *                 "all_comments": 233,  // 总评论数量，模块推荐位使用
     *                 "sort": 1,
     *                 "module_id": 9,
     *                 "user_id": 234,  // UP 主的用户 ID
     *                 "username": "UP 主的用户名",  // UP 主的用户名
     *                 "video": true  // 绑定了视频返回 true, 未绑定不返回
     *               }
     *             ]
     *           },
     *           {
     *             "module_id": 8,
     *             "title": "燃爆全场",
     *             "type": 1,
     *             "style": 0,
     *             "more": {  // 不下发或为空时跳转到默认的原生自定义模块详情页
     *               "url": "https://test.com/aaa?foo=bar"  // 更多按钮跳转链接，不下发或为空时跳转到默认的原生自定义模块详情页
     *             },
     *             "elements": [
     *               {
     *                 "id": 142732,
     *                 "title": "3D燃起来",
     *                 "intro": "",
     *                 "front_cover": "http://static.missevan.com/mimages/201711/15/92eddc64e7121500.jpg",
     *                 "type": 1,
     *                 "music_count": 16,
     *                 "view_count": 135718,
     *                 "sort": 2,
     *                 "module_id": 8,
     *                 "corner_mark": {  // 无剧集角标时不返回该字段
     *                   "text": "已购",
     *                   "text_color": "#ffffff",
     *                   "bg_start_color": "#e66465",
     *                   "bg_end_color": "#e66465",
     *                   "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *                 }
     *               }
     *             ]
     *           }
     *         ],
     *         "marker": "233:1,234:1"  // 标识值，返回该值时客户端需要把它作为下一次请求的 marker 参数值
     *       }
     *     }
     */
    public function actionMyFavors(int $persona_id, int $is_all = 0, int $is_mrpc = 0, string $marker = '')
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }

        if (!in_array($is_all, [0, 1])) {
            throw new HttpException(400, '参数错误');
        }

        if (!in_array($is_mrpc, [0, 1])) {
            throw new HttpException(400, '参数错误');
        }

        $persona_module = $persona_id & Persona::PERSONA_MODULE_MASK;
        if (!Persona::validatePersona($persona_module)) {
            // 若用户画像不在定义的范围内，则默认为女用户
            $persona_module = Persona::TYPE_GIRL;
            // 若出现 $persona_id 不为合法值问题，记录到日志
            Yii::warning("Wrong persona_id: {$persona_id}", __METHOD__);
        }
        if ($persona_module === Persona::TYPE_FUJOSHI) {
            // WORKAROUND: 腐女画像推荐模块返回默认女性画像下的模块
            $persona_module = Persona::TYPE_GIRL;
        }
        [$modules, $from_cache] = YouMightLikeModule::getPersonModules($persona_module);
        // 从 iOS >= 6.0.1, Android >= 6.0.1 开始支持滑动样式的音频模块
        $is_old_than_601 = Equipment::isAppOlderThan('6.0.1', '6.0.1');
        $is_old_than_602 = Equipment::isAppOlderThan('6.0.2', '6.0.2');
        $is_web = Equipment::Web === Yii::$app->equip->getOs();
        $is_blocked_channel = Blacklist::model()->isBlackChannel();
        $channel = Yii::$app->equip->getChannel();
        $modules = array_map(function ($module) use ($is_all, $is_mrpc, $is_old_than_601, $is_old_than_602, $is_web, $persona_module, $is_blocked_channel, $channel) {
            if ($is_blocked_channel && Blacklist::isChannelRecommendModuleBlocked($channel, $module['title'])) {
                return null;
            }
            if (isset($module['more']['url'])) {
                $module['more']['url'] = MUtils::getUsableAppLink($module['more']['url']);
            }
            if ($is_old_than_602) {
                // WORKAROUND: 兼容旧版本（iOS < 6.0.2, Android < 6.0.2）下发 direction 字段
                $module['direction'] = $module['style'];
            }
            if (($is_old_than_601 || $is_web) && !$is_mrpc && $module['type'] === YouMightLikeModule::TYPE_SOUND) {
                // WORKAROUND: 老版本（iOS < 6.0.1，Android < 6.0.1）和 Web 不支持音频类型的模块
                return null;
            }
            $elements = $module['elements'];
            if ($is_all) {
                $elements = array_map(function ($element) use ($module) {
                    if (MPersonaModuleElement::MODULE_TYPE_DRAMA === $module['type']) {
                        unset($element['abstract'], $element['integrity'], $element['pay_type'],
                            $element['need_pay']);
                        return $element;
                    } elseif (MPersonaModuleElement::MODULE_TYPE_ALBUM === $module['type']) {
                        unset($element['intro']);
                        return $element;
                    }
                }, $elements);
                // 排行榜模块，截取所需数据
                if ($module['style'] === MPersonaModuleElement::MODULE_STYLE_TOP_PLAY_STYLE) {
                    $module['elements'] = array_slice($elements, 0,
                        MPersonaModuleElement::WEB_ELEMENT_RECOMMENDED_COUNT);
                }
            } elseif ($is_mrpc) {
                $elements = $module['elements'];
                if (count($elements) < MPersonaModuleElement::APP_HOMEPAGE_FEED_ELEMENT_RECOMMENDED_MIN_COUNT) {
                    return null;
                }
                $module['elements'] = array_slice($elements, 0, MPersonaModuleElement::APP_HOMEPAGE_FEED_ELEMENT_RECOMMENDED_MAX_COUNT);
            } else {
                // 根据模块样式截取模块数据
                YouMightLikeModule::truncateModule($module, $persona_module);
                if (!$module) {
                    return null;
                }
            }

            // 剧集模块，根据模块样式选择使用不同的封面图
            YouMightLikeModule::processDramaModuleCover($module);
            return $module;
        }, $modules);

        $modules = array_values(array_filter($modules));
        $user_id = (int)Yii::$app->user->id;
        $new_version_flag = false;
        // WORKAROUND: 如果返回的是非所有数据并且 iOS >= 4.8.8，Android >= 5.7.4，给剧集模块补充剧集角标信息
        if ($is_mrpc || (!$is_all && !Equipment::isAppOlderThan('4.8.8', '5.7.4'))) {
            $new_version_flag = true;
            Drama::fillCornerMarkInDramaModules($modules, $user_id);
        }

        // 如果返回的是非所有数据且缓存存在是旧版本，则实时获取 need_pay 字段的值，用于显示用户剧集购买的状态
        if ($is_mrpc || (!$is_all && $from_cache && !$new_version_flag)) {
            $modules = array_map(function ($module) {
                if (MPersonaModuleElement::MODULE_TYPE_DRAMA === $module['type']) {
                    Drama::checkNeedPay($module['elements'], Yii::$app->user->id);
                }
                return $module;
            }, $modules);
        }

        // WORKAROUND: 老版本（iOS < 4.9.0，Android < 5.7.6）和 Web 不支持返回视频大卡和首页榜单
        if (!$is_mrpc && ($is_web || Equipment::isAppOlderThan('4.9.0', '5.7.6'))) {
            return $modules;
        }
        $return = ['modules' => $modules, 'marker' => ''];
        [$video_card, $next_marker] = self::getVideoCard($marker, $persona_module, $user_id);
        if ($video_card) {
            $return['video_card'] = $video_card;
            $return['marker'] = $next_marker;
        }
        // iOS >= 4.9.8, Android >= 5.8.1 支持返回首页榜单
        if ($is_mrpc || !Equipment::isAppOlderThan('4.9.8', '5.8.1')) {
            $ranks = self::getRanks($persona_module, $user_id);
            if ($ranks) {
                // 没有排行榜模块数据时不返回 ranks 字段
                $return['ranks'] = $ranks;
            }
        }
        return $return;
    }

    /**
     * 获取视频大卡和随机标识信息
     *
     * @param string $marker 当前抽取标识
     * @param int $persona_id 画像 ID
     * @param int $user_id 用户 ID
     * @return array
     * @throws Exception
     */
    private static function getVideoCard(string $marker, int $persona_id, int $user_id)
    {
        try {
            $video_card_pool = MVideoCard::getVideoCardPool($persona_id);
            if (Equipment::isAppOlderThan('4.9.8', '5.8.1')) {
                // WORKAROUND: iOS < 4.9.8, Android < 5.8.1 仅下发第一个自定义推荐模块下方的视频大卡，且不展示游戏视频大卡
                $video_card_pool = array_values(array_filter($video_card_pool[MVideoCard::POSITION_2], function ($item) {
                    return $item['type'] === MVideoCard::TYPE_NORMAL;
                }));
                return self::getVideoCardByMarker($marker, $video_card_pool, $user_id);
            }
            $return_card = [];
            $return_marker = [];
            // 获取猜你喜欢模块视频大卡
            $marker = explode(',', $marker);
            $position_1_marker = $marker[0] ?? '';
            [$position_1_video_card, $next_position_1_marker] = self::getVideoCardByMarker($position_1_marker,
                $video_card_pool[MVideoCard::POSITION_1], $user_id);
            if ($position_1_video_card) {
                $return_card[MVideoCard::POSITION_1] = $position_1_video_card;
            }
            // position_1 位置下若没有大卡数据，$return_marker[0] 也要赋值为 ''，保证客户端下次请求的 marker 数据正常解析
            // 例：marker:",233:1"，解析得到的 $position_1_marker 为 ''，$position_2_marker 为 '233:1'
            $return_marker[] = $next_position_1_marker;
            // 获取推荐模块视频大卡
            $position_2_marker = $marker[1] ?? '';
            [$position_2_video_card, $next_position_2_marker] = self::getVideoCardByMarker($position_2_marker,
                $video_card_pool[MVideoCard::POSITION_2], $user_id);
            if ($position_2_video_card) {
                $return_card[MVideoCard::POSITION_2] = $position_2_video_card;
                $return_marker[] = $next_position_2_marker;
            }
            return [empty($return_card) ? null : $return_card, implode(',', $return_marker)];
        } catch (Exception $e) {
            // 获取首页视频大卡出错，记录错误日志，降级为不返回视频大卡，不直接报错
            Yii::error("获取首页视频大卡出错，画像 ID: {$persona_id}, 错误信息：{$e->getMessage()}",
                __METHOD__);
            // PASS
            return [null, ''];
        }
    }

    /**
     * 按下发位置获取视频大卡和随机标识信息
     *
     * @param string $marker 当前抽取标识
     * @param array $pool 视频大卡内容数组
     * @param int $user_id 用户 ID
     * @return array
     * @throws Exception
     */
    private static function getVideoCardByMarker(string $marker, array $pool, int $user_id)
    {
        if (($pool_size = count($pool)) === 0) {
            return [null, ''];
        }
        // 从视频大卡池中随机抽取视频大卡，每个用户使用一个随机数，按该随机数生成各自固定的抽取序列，然后按顺序抽取
        [$random_seed, $current_position, $next_marker] = MUtils::markerInfo($marker, $pool_size);
        // 按固定的随机种子，打乱数组
        mt_srand($random_seed);
        shuffle($pool);
        $video_card = self::dealVideoCard($pool[$current_position], $user_id);
        // 预约游戏状态
        if ($video_card['type'] === MVideoCard::TYPE_GAME) {
            // TODO: 多个位置有游戏卡片时会出现多次查询，之后可以优化为一次查询
            if (!$game = MGameCenter::find()
                ->select('id, extended_fields')
                ->where(['id' => $video_card['game_card']['id']])
                ->one()) {
                Yii::error("未查询到游戏信息，游戏 ID: {$video_card['game_card']['id']}", __METHOD__);
                return [null, ''];
            }
            $video_card['game_card']['status'] = $game->getCardStatus($user_id);
            if (!Yii::$app->equip->isAndroid()) {
                // 对于 iOS、HarmonyOS 客户端，不需要额外返回安装包下载地址等信息
                unset($video_card['game_card']['download_url']);
                unset($video_card['game_card']['package_name']);
                unset($video_card['game_card']['package_version_code']);
            }
        }
        return [$video_card, $next_marker];
    }

    /**
     * 获取首页榜单
     *
     * @param int $persona_id 画像 ID
     * @param int $user_id 用户 ID
     * @return array|null
     * @throws Exception
     */
    private static function getRanks(int $persona_id, int $user_id)
    {
        try {
            $ranks_key = MUtils::generateCacheKey(KEY_APP_HOMEPAGE_RANKS, $persona_id);
            $memcache = Yii::$app->memcache;
            if ($data = $memcache->get($ranks_key)) {
                $ranks_detail = Json::decode($data);
            } else {
                $ranks_detail = MPersonaRank::getRanksByPersonaId($persona_id);
                $memcache->set($ranks_key, Json::encode($ranks_detail), ONE_MINUTE);
            }
            if (empty($ranks_detail)) {
                return null;
            }
            if (Equipment::isAppOlderThan('6.1.2', '6.1.2')) {
                // WORKAROUND: iOS 或安卓低于 6.1.2 时需要将直播榜挪到末位
                $rank_live_index = array_search(MHomepageRank::TYPE_RANK_LIVE, array_column($ranks_detail, 'type'));
                if ($rank_live_index !== false) {
                    $rank_live = $ranks_detail[$rank_live_index];
                    unset($ranks_detail[$rank_live_index]);
                    $ranks_detail = array_values($ranks_detail);
                    $ranks_detail[] = $rank_live;
                }
            }
            // 缓存的榜单数据中未填充剧集角标信息
            $dramas = [];
            foreach ($ranks_detail as $rank) {
                if ($rank['element_type'] === MPersonaModuleElement::ELEMENT_TYPE_DRAMA) {
                    $dramas = array_merge($dramas, $rank['elements']);
                }
            }
            // 获取剧集角标（所有剧集一起获取一次）
            Drama::fillCornerMark($dramas, $user_id);
            $drama_map = array_column($dramas, null, 'id');
            foreach ($ranks_detail as &$rank) {
                if ($rank['element_type'] !== MPersonaModuleElement::ELEMENT_TYPE_DRAMA) {
                    continue;
                }
                $fill_corner_mark_dramas = [];
                foreach ($rank['elements'] as $drama) {
                    $fill_corner_mark_dramas[] = $drama_map[$drama['id']];
                }
                $rank['elements'] = $fill_corner_mark_dramas;
            }
            unset($rank);
            $homepage_rank_details_link = Yii::$app->params['web_links']['homepage_rank_details'];
            // navhide 用途参考文档：https://info.missevan.com/pages/viewpage.action?pageId=67240173
            $query = http_build_query(['persona_id' => $persona_id, 'navhide' => 1]);
            $ranks = [
                'title' => '排行榜',
                // 更多跳转链接
                'more' => ['url' => "{$homepage_rank_details_link}?{$query}"],
                'data' => $ranks_detail,
            ];
            return $ranks;
        } catch (Exception $e) {
            // 获取首页榜单出错，记录错误日志，降级为返回 null，不直接报错
            Yii::error("获取首页榜单出错，画像 ID: {$persona_id}, 错误信息：{$e->getMessage()}",
                __METHOD__);
            // PASS
            return null;
        }
    }

    /**
     * 处理视频大卡
     *
     * @param array $video_card 视频大卡内容数组
     * @param int $user_id 用户 ID
     * @return array
     * @throws Exception
     */
    private static function dealVideoCard(array $video_card, int $user_id)
    {
        // 将封面和视频地址转化为完整地址
        $video_card['cover'] = StorageClient::getFileUrl($video_card['cover']);
        foreach ($video_card['resources'] as &$item) {
            if (!$user_id && ($item['status'] & SoundVideo::PLAY_STATUS_LOGIN_REQUIRED)) {
                // 若用户未登录且该字段的视频需要登录才可播放，则播放地址等信息置零值
                $item['url'] = '';
                $item['size'] = $item['width'] = $item['height'] = 0;
            }
        }
        unset($item);
        // 获取视频播放地址
        SoundVideo::getResourcePlayUrls($video_card['resources']);
        if (!$video_card['url']) {
            // 没有配置跳转链接则不返回 url 字段
            unset($video_card['url']);
        }
        return $video_card;
    }

    /**
     * @api {get} /you-might-like/favor-detail 推荐模块的更多详情
     *
     * @apiVersion 0.1.0
     * @apiName favor-detail
     * @apiGroup you-might-like
     *
     * @apiParam {Number} module_id 模块 ID
     * @apiParam {Number} [page_size=30] 每页数量
     * @apiParam {Number} [page=1]  当前页
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [
     *           {
     *             "id": 145728,
     *             "title": "asmr",
     *             "intro": "",
     *             "front_cover": "http://static.missevan.com/mimages/201711/15/92eddc64e7121500.jpg",
     *             "sort": 3,
     *             "type": 1,
     *             "music_count": 333279,
     *             "view_count": 333279,
     *             "corner_mark": {  // 无剧集角标时不返回该字段
     *               "text": "已购",
     *               "text_color": "#ffffff",
     *               "bg_start_color": "#e66465",
     *               "bg_end_color": "#e66465",
     *               "left_icon_url": "https://static-test.maoercdn.com/dramacoversmini/201701/24/ab0fcaf8ebe79.png"  // 无左侧图标时不返回该字段
     *             }
     *           }
     *         ],
     *         "pagination": {
     *           "p": 3,
     *           "maxpage": 6,
     *           "count": 6,
     *           "pagesize": 1
     *         },
     *         "title": "精品周更",  // 模块名称
     *         "style": 1,  // 0：竖版，1：横版
     *         "type": 2  // 1：音单，2：剧集，3：音频
     *       }
     *     }
     */
    public function actionFavorDetail()
    {
        $module_id = (int)Yii::$app->request->get('module_id');
        $page = (int)Yii::$app->request->get('page', 1);
        $page_size = (int)Yii::$app->request->get('page_size', 30);
        $pagination = PaginationParams::process($page, $page_size);

        if (!$module_id) throw new HttpException(400, '参数不能为空');
        $module = MPersonaModuleElement::getFavorDetails($module_id, $pagination);
        if (!$module) {
            throw new HttpException(404, '咦，内容不见了 _(:зゝ∠)_ 请刷新重试');
        }
        $elements = $module['elements'];
        if (MPersonaModuleElement::MODULE_TYPE_DRAMA === $module['module_type']) {
            $user_id = (int)Yii::$app->user->id;
            // WORKAROUND: iOS >= 4.8.8，Android >= 5.7.4，补充剧集角标信息以及移除 need_pay
            if (!Equipment::isAppOlderThan('4.8.8', '5.7.4')) {
                Drama::fillCornerMark($elements->Datas, $user_id);
            } else {
                Drama::checkNeedPay($elements->Datas, Yii::$app->user->id);
            }
        }
        $return = [
            'Datas' => $elements->Datas,
            'pagination' => $elements->pagination,
            'title' => $module['title'],
            'style' => $module['module_style'],
            'type' => $module['module_type'],
        ];
        if (Equipment::isAppOlderThan('6.0.2', '6.0.2')) {
            // WORKAROUND: 兼容旧版本（iOS < 6.0.2, Android < 6.0.2）下发 direction 字段
            $return['direction'] = $module['module_style'];
        }
        if (Equipment::isAppOlderThan('6.0.1', '6.0.1')
                && $return['style'] === MPersonaModuleElement::MODULE_STYLE_SLIDE) {
            // WORKAROUND: 滑动模块，旧版本（iOS < 6.0.1, Android < 6.0.1）下发为横版音频，跟分区页下发的 direction 保持一致
            $return['direction'] = MPersonaModuleElement::MODULE_STYLE_HORIZONTAL;
        }
        return $return;
    }

    /**
     * @api {get} /you-might-like/recommended-dramas 精品推荐
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/you-might-like/recommended-dramas
     * @apiSampleRequest you-might-like/recommended-dramas
     *
     * @apiVersion 0.1.0
     * @apiName recommended-dramas
     * @apiGroup you-might-like
     *
     * @apiParam {Number} [page_size=30] 每页数量
     * @apiParam {Number} [page=1]  当前页
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [
     *           {
     *             "id": 145728,
     *             "title": "asmr",
     *             "intro": "",
     *             "front_cover": "http://static.missevan.com/mimages/201711/15/92eddc64e7121500.jpg",
     *             "sort": 3,
     *             "type": 1,
     *             "music_count": 333279,
     *             "view_count": 333279
     *           }
     *         ],
     *         "pagination": {
     *           "p": 3,
     *           "maxpage": 6,
     *           "count": 6,
     *           "pagesize": 1
     *         }
     *       }
     *     }
     */
    public function actionRecommendedDramas()
    {
        $page = (int)Yii::$app->request->get('page', 1);
        $page_size = (int)Yii::$app->request->get('page_size', 30);
        $pagination = PaginationParams::process($page, $page_size);
        // FIXME: 获取大众画像下”精品推荐“模块数据，若模块下架或被删除，数据将取到空值，之后需要获取更稳定的数据
        $recommended_dramas = MPersonaModuleElement::getRecommendedDramas(Yii::$app->user->id, $pagination);
        if (!$recommended_dramas) {
            throw new HttpException(404, '咦，内容不见了 _(:зゝ∠)_ 请刷新重试');
        }
        return $recommended_dramas;
    }

    /**
     * @api {post} /you-might-like/collect-clicks 采集点击模块情况
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/you-might-like/collect-clicks
     * @apiSampleRequest you-might-like/collect-clicks
     *
     * @apiVersion 0.1.0
     * @apiName collect-clicks
     * @apiGroup you-might-like
     *
     * @apiDescription
     *     点击首页推荐音 view=1、module_type=3、elem_type=3、elem_id、elem_position；
     *     点击首页推荐模块中音单或剧集 view=1、module_type=5、module_id、module_position、
     *         elem_type、elem_id、elem_position；
     *     点击模块更多时 view=1、module_type=5、module_id、module_position、is_more=1；
     *     点击模块更多页面中的音单或剧集 view=151、module_type=5、module_id、module_position、
     *         elem_type、elem_id、elem_position、is_more=1
     *
     * @apiParam {Number} view 点击视图位置（首页推荐为 1、推荐模块更多页面 151）
     * @apiParam {Number} module_type 模块类型
     * @apiParam {Number} [module_id=0] 模块 ID （当点击首页推荐音时为 0）
     * @apiParam {Number} [module_position=0] 模块位置（排序值）（当点击首页推荐音时为 0）
     * @apiParam {Number} elem_type 元素类型（0 其它、1 音单、2 剧集、3 单音）
     * @apiParam {Number} elem_id 元素 ID（当点击模块更多时为 0）
     * @apiParam {Number} [elem_position=0] 音单或剧集在模块中的位置、猜你喜欢音的位置
     * @apiParam {Number} [is_more=0] 是否属于模块的更多（0 为否，1 为是）
     *     （当点击首页推荐音、点击首页推荐模块中音单或剧集为 0）
     *
     * @apiParamExample {String} 点击首页推荐音:
     *     view=1&module_type=3&elem_type=3&elem_id=88778&elem_position=2
     *
     * @apiParamExample {String} 点击首页推荐模块中音单或剧集:
     *     view=1&module_type=5&module_id=15&module_position=3&elem_type=2&elem_id=26&elem_position=3
     *
     * @apiParamExample {String} 点击模块更多时:
     *     view=1&module_type=5&module_id=15&module_position=3&is_more=1
     *
     * @apiParamExample {String} 点击模块更多页面中的音单或剧集:
     *     view=151&module_type=5&module_id=15&module_position=3&elem_type=2&elem_id=26&elem_position=3&is_more=1
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Boolean} info true or false
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": true
     *     }
     */
    public function actionCollectClicks()
    {
        $view = (int)Yii::$app->request->post('view');
        $module_type = (int)Yii::$app->request->post('module_type');
        $module_id = (int)Yii::$app->request->post('module_id');
        $module_position = (int)Yii::$app->request->post('module_position');
        $elem_type = (int)Yii::$app->request->post('elem_type');
        $elem_id = (int)Yii::$app->request->post('elem_id');
        $elem_position = (int)Yii::$app->request->post('elem_position');
        $is_more = (int)Yii::$app->request->post('is_more');

        if (!GuessYourLikesClicks::checkElemType($elem_type)) throw new HttpException(400, '参数错误');

        // WORKAROUND: 增加参数 view、elem_position、module_type 并将 sort 调整为 module_position，兼容线上的 iOS 4.1.1
        // iOS 4.1.2 已经上线，调整后的元素类型值（单音的 elem_type 由 0 调整为 3）在 4.1.3 及其之后的版本调整过来
        if (Equipment::isAppVersion(Equipment::iOS, '4.1.1')) {
            $module_position = (int)Yii::$app->request->post('sort');
            $view = $is_more && $module_id && $elem_id ? GuessYourLikesClicks::VIEW_HOMEPAGE_MODULE_MORE
                : GuessYourLikesClicks::VIEW_HOMEPAGE_RECOMMEND;
            $elem_type = !$elem_type && $elem_id ? GuessYourLikesClicks::ELEM_TYPE_SOUND : $elem_type;
            $elem_position = $elem_type ? MPersonaModuleElement::getElemPosition($module_id, $elem_type, $elem_id) : 0;
            $module_type = $module_id ? GuessYourLikesClicks::MODULE_TYPE_RECOMMENDED_MODULE
                : GuessYourLikesClicks::MODULE_TYPE_RECOMMENDED_SOUNDS;
        } elseif (Equipment::isAppOlderThan('4.2.4')
                && $module_type === GuessYourLikesClicks::MODULE_TYPE_RECOMMENDED_SOUNDS) {
            // WORKAROUND: iOS 旧版本 elem_type 应传 3（传了 0）予以修复
            $elem_type = GuessYourLikesClicks::ELEM_TYPE_SOUND;
        }

        if (!GuessYourLikesClicks::collectClick(
            $view, $is_more, $module_type, $module_id, $module_position, $elem_type, $elem_id, $elem_position)) {
            return false;
        }
        return true;
    }
}
