<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2017/7/25
 * Time: 10:28
 */

namespace app\controllers;

use app\components\util\Equipment;
use app\components\util\Tools;
use app\models\Card;
use app\models\CarnivalActivity;
use app\models\Live;
use app\models\MAttentionUser;
use app\models\MHomepageIcon;
use app\models\MMessageText;
use app\models\MPersonaModuleElement;
use app\models\MPersonHomePage;
use app\models\MSobotUser;
use app\models\MUserLikeDanmaku;
use app\models\MUserVip;
use app\models\PaginationParams;
use app\models\ReturnModel;
use app\models\SoundCommentRO;
use app\models\SoundSubCommentRO;
use app\models\Work;
use app\components\util\MUtils;
use app\components\controllers\MessageInterface;
use app\models\AnFeedback;
use app\models\AnMsg;
use app\models\BlackUser;
use app\models\CommentLike;
use app\models\LikeNotice;
use app\models\MAlbum;
use app\models\MEvent;
use app\models\MMessageAssign;
use app\models\Mowangskuser;
use app\models\MSound;
use app\models\MSoundComment;
use app\models\MTag;
use app\models\Topic;
use app\models\SoundComment;
use app\models\SoundSubComment;
use app\models\Commentnotice;
use app\middlewares\Controller;
use app\components\base\filter\AccessControl;
use Exception;
use missevan\rpc\ServiceRpc;
use missevan\storage\StorageClient;
use Yii;
use yii\data\Pagination;
use yii\db\Query;
use yii\filters\VerbFilter;
use yii\helpers\Json;
use yii\web\HttpException;


class MessageController extends Controller implements MessageInterface
{
    // 获取评论时的排序方式，1：按时间排序（父评论倒序/子评论升序）；2：按子评论数倒序；3：按热度倒序
    const GET_COMMENTS_ORDER_BY_TIME = 1;
    const GET_COMMENTS_ORDER_BY_COMMENT_NUM = 2;
    const GET_COMMENTS_ORDER_BY_HOT = 3;

    // 获取评论时热门评论数量
    const GET_COMMENTS_RECOMMEND_NUM = 10;
    // 热门评论的最低点赞数
    const GET_COMMENTS_RECOMMEND_MIN_LIKE_NUM = 5;
    // 热门评论的最低热度 5 * 点赞点踩分（1e2）* 评论基础分（1e2）* 黑名单分数基础分（1e2）
    const GET_COMMENTS_RECOMMEND_MIN_HOT = 5e6;
    // 热门评论出现时的相关元素最低评论数
    const GET_COMMENTS_RECOMMEND_MIN_COMMENT_NUM = 15;

    // 每条评论下带的子评论数
    const GET_COMMENTS_SUB_COMMENTS_NUM = 3;

    static $GET_COMMENTS_ORDER_TYPES = [
        self::GET_COMMENTS_ORDER_BY_TIME,
        self::GET_COMMENTS_ORDER_BY_COMMENT_NUM,
        self::GET_COMMENTS_ORDER_BY_HOT
    ];
    // 评论提醒类型（全部，未读、已读）
    const COMMENT_NOTICE_READ_TYPE_ALL = 0;
    const COMMENT_NOTICE_READ_TYPE_UNREAD = 1;
    const COMMENT_NOTICE_READ_TYPE_ALREADY_READ = 2;

    // 专属表情状态
    const STATUS_OFFLINE = -1;  // 专属表情已下线
    const STATUS_LOCKED = 0;  // 专属表情未解锁
    const STATUS_UNLOCK = 1;  // 专属表情已解锁

    // 我听图标上的动态提醒类型 1：投稿动态；2：追剧动态；3：直播动态
    const ICON_NOTICE_TYPE_SOUND = 1;
    const ICON_NOTICE_TYPE_DRAMA = 2;
    const ICON_NOTICE_TYPE_LIVE = 3;

    public function behaviors()
    {
        $behaviors = parent::behaviors();
        $behaviors['verbs'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'remove-room' => ['post'],
                'send-message' => ['post'],
                'add' => ['post'],
                'add-sub' => ['post'],
                'add-dm' => ['post'],
                'like-dm' => ['post'],
                'like' => ['post'],
                'dislike' => ['post'],
                'del' => ['post'],
                'set-blacklist' => ['post'],
                'set-message-status' => ['post'],
            ],
        ];
        $behaviors['access'] = [
            'class' => AccessControl::class,
            'only' => [
                'message-detail',
                'get-likes',
                'remove-room',
                'send-message',
                'add',
                'add-sub',
                'del',
                'add-dm',
                'like-dm',
                'like',
                'dislike',
                'get-notice-num',
                'get-sys-msg',
                'get-at-notice',
                'get-comment-notice',
                'get-blacklist',
                'set-blacklist',
                'message-list',
                'set-message-status',
            ],
            'rules' => [
                [
                    'allow' => true,
                    'actions' => [
                        'message-detail',
                        'get-likes',
                        'remove-room',
                        'send-message',
                        'add',
                        'add-sub',
                        'del',
                        'add-dm',
                        'like-dm',
                        'like',
                        'dislike',
                        'get-notice-num',
                        'get-sys-msg',
                        'get-at-notice',
                        'get-comment-notice',
                        'get-blacklist',
                        'set-blacklist',
                        'message-list',
                        'set-message-status',
                    ],
                    'roles' => ['@']
                ],
            ]
        ];

        return $behaviors;
    }

    /**
     * @api {get} /message/get-comments 获取评论列表（可包含热门评论）
     * @apiDescription 热度排序规则参考：https://github.com/MiaoSiLa/requirements-doc/issues/753
     *
     * @apiVersion 0.1.0
     * @apiName get-comments
     * @apiGroup message
     *
     * @apiParam {Number=1,2,3} [order=1] 评论排序，1：时间倒序，2：子评论数倒序，3：热度倒序
     * @apiParam {Number=1,2,4} [type=1] 评论类型，1：单音，2：音单，4：频道
     * @apiParam {Number} e_id 对象 ID，当 type = 1 时，e_id 为音频 ID，以此类推
     * @apiParam {Number} last_comment_id 上一页最后一条评论的 ID
     * @apiParam {number} [page=1] 页数
     * @apiParam {number} [page_size=30] 每页条数
     * @apiParam {number} [recommend=1] 是否查询热门评论
     * @apiParam {number} [recommend_page_size=10] 热门评论数量
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "recommends": {
     *           "Datas": [
     *             {
     *               "id": 32232,
     *               "comment_content": "剧集跳转链接 https://www.missevan.com/mdrama/77631，单集跳转链接 https://www.missevan.com/sound/player?id=9811365",
     *               "jump_url": {  // 评论内容中的跳转链接信息（仅有站内跳转链接时下发）
     *                 "https://www.missevan.com/mdrama/77631": {  // 跳转链接地址（客户端需要将对应跳转链接替换为相应文案的可点击的跳转链接格式）
     *                   "title": "十年对手，一朝占有",  // 跳转链接文案
     *                   "prefix_icon": "http://static-test.maoercdn.com/test.jpg"  // 跳转链接前缀图标
     *                 },
     *                 "https://www.missevan.com/sound/player?id=9811365": {  // 跳转链接地址（客户端需要将对应跳转链接替换为相应文案的可点击的跳转链接格式）
     *                   "title": "十年对手，一朝占有 . 第一集",  // 跳转链接文案
     *                   "prefix_icon": "http://static-test.maoercdn.com/test.jpg"  // 跳转链接前缀图标
     *                 }
     *               },
     *               "c_type": 1,
     *               "element_id": 569120,
     *               "ip_location": "山东",
     *               ...
     *               "user": {
     *                 "user_id": 9075893,  // 用户 ID
     *                 "username": "加特林",  // 用户昵称
     *                 "iconurl": "http://test.com/avatar.jpg",  // 用户头像
     *                 "avatar_frame_url": "http://test.com/frame.webp",  // 用户头像框，没有时不下发
     *                 "authenticated": 0,  // 加 V 认证标识：0 没有 V，1 代表黑 V，2 代表金 V，3 代表蓝 V
     *                 "is_vip": 1  // 用户是否为会员。0：否；1：是
     *               }
     *             }
     *           ],
     *           "has_more": false
     *         },
     *         "comments": {
     *           "Datas": [
     *             {
     *               "id": 32292,
     *               "comment_content": "剧集跳转链接 https://www.missevan.com/mdrama/77631，单集跳转链接 https://www.missevan.com/sound/player?id=9811365",
     *               "jump_url": {  // 评论内容中的跳转链接信息（仅有站内跳转链接时下发）
     *                 "https://www.missevan.com/mdrama/77631": {  // 跳转链接地址（客户端需要将对应跳转链接替换为相应文案的可点击的跳转链接格式）
     *                   "title": "十年对手，一朝占有",  // 跳转链接文案
     *                   "prefix_icon": "http://static-test.maoercdn.com/test.jpg"  // 跳转链接前缀图标
     *                 },
     *                 "https://www.missevan.com/sound/player?id=9811365": {  // 跳转链接地址（客户端需要将对应跳转链接替换为相应文案的可点击的跳转链接格式）
     *                   "title": "十年对手，一朝占有 . 第一集",  // 跳转链接文案
     *                   "prefix_icon": "http://static-test.maoercdn.com/test.jpg"  // 跳转链接前缀图标
     *                 }
     *               },
     *               "ctime": 1621934613,
     *               "sub_comment_num": 17,
     *               "user": {
     *                 "user_id": 11916985,  // 用户 ID
     *                 "username": "一个想不出昵称的家伙",  // 用户昵称
     *                 "iconurl": "http://test.com/avatar.jpg",  // 用户头像
     *                 "avatar_frame_url": "http://test.com/frame.webp",  // 用户头像框，没有时不下发
     *                 "authenticated": 0,  // 加 V 认证标识：0 没有 V，1 代表黑 V，2 代表金 V，3 代表蓝 V
     *                 "is_vip": 1  // 用户是否为会员。0：否；1：是
     *               },
     *               "like_num": 274,
     *               "subcomments": [
     *                 {
     *                   "id": 4666,
     *                   "comment_content": "剧集跳转链接 https://www.missevan.com/mdrama/77631，单集跳转链接 https://www.missevan.com/sound/player?id=9811365",
     *                   "jump_url": {  // 评论内容中的跳转链接信息（仅有站内跳转链接时下发）
     *                     "https://www.missevan.com/mdrama/77631": {  // 跳转链接地址（客户端需要将对应跳转链接替换为相应文案的可点击的跳转链接格式）
     *                       "title": "十年对手，一朝占有",  // 跳转链接文案
     *                       "prefix_icon": "http://static-test.maoercdn.com/test.jpg"  // 跳转链接前缀图标
     *                     },
     *                     "https://www.missevan.com/sound/player?id=9811365": {  // 跳转链接地址（客户端需要将对应跳转链接替换为相应文案的可点击的跳转链接格式）
     *                       "title": "十年对手，一朝占有 . 第一集",  // 跳转链接文案
     *                       "prefix_icon": "http://static-test.maoercdn.com/test.jpg"  // 跳转链接前缀图标
     *                     }
     *                   },
     *                   "comment_id": 39528226,
     *                   "ctime": 1667882198,
     *                   "user": {
     *                     "user_id": 9075893,  // 用户 ID
     *                     "username": "加特林",  // 用户昵称
     *                     "iconurl": "http://test.com/avatar.jpg",  // 用户头像
     *                     "avatar_frame_url": "http://test.com/frame.webp",  // 用户头像框，没有时不下发
     *                     "authenticated": 0,  // 加 V 认证标识：0 没有 V，1 代表黑 V，2 代表金 V，3 代表蓝 V
     *                     "is_vip": 1  // 用户是否为会员。0：否；1：是
     *                   },
     *                   "like_num": 4
     *                 }
     *               ],
     *               "liked": 0,
     *               "disliked": 0,
     *               "ip_location": "山东"
     *             }
     *           ],
     *           "pagination": {
     *             "p": 1,
     *             "count": 25,
     *             "all_count": 30,
     *             "maxpage": 1,
     *             "pagesize": 30
     *           },
     *           "has_more": false
     *         },
     *         "emotes": [  // 专属表情包，没有时不下发
     *           {
     *             "package_id": 3,  // 表情包 ID
     *             "lock": true,  // 是否解锁，解锁后才可发送，否则表情包发送窗口展示该表情包中的锁定信息（tip 信息）
     *             // 解锁说明，支持 HTML。仅在 lock 为 true 时下发
     *             "tip": "《我在无限游戏里封神》广播剧全站播放量达到 1000W 时可解锁该表情哦！<br /><a href=\"https://www.test.com\">超链接</a>",
     *             // 客户端是否展示评论内的专属表情，只需判断该表情的评论发送时间（ctime 字段）是否在展示时间内，即使该专属表情包 order 为 0 或 lock 为 true
     *             // start_time 和 end_time 不下发时表示永久展示，若只存在 start_time 表示只判断历史评论展示范围的开始时间，或者只存在 end_time 表示只判断历史评论展示范围的结束时间
     *             "start_time": 1684737121,  // 评论中专属表情展示开始时间戳，单位：秒
     *             "end_time": 1694737121  // 评论中专属表情展示截止时间戳，单位：秒
     *           }
     *         ],
     *         "comment_ad": {  // 评论区小黄条，仅请求第一页评论且有小黄条广告时才下发
     *           "id": 1,  // 小黄条广告 ID
     *           "title": "惊！超好听的广播剧~",
     *           "url": "http://www.test.com/drama/1",  // 跳转链接
     *           "element_type": 2,  // 广告关联元素类型。2：剧集。用于埋点上报（此处类型值含义和接口的 type 参数值含义不同，不要混淆）
     *           "element_id": 233  // 广告关联元素 ID。用于埋点上报
     *         }
     *       }
     *     }
     *
     * @apiSuccessExample Success-Response: iOS 4.9.9、Android 5.8.2 下发
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "recommends": {
     *           ...
     *         },
     *         "comments": {
     *           ...
     *         },
     *         "emote": {  // 专属表情包，没有时不下发
     *           "package_id": 3,  // 表情包 ID
     *           "lock": true,  // 是否解锁，解锁后才可发送，否则表情包发送窗口展示该表情包中的锁定信息（tip 信息）
     *           // 解锁说明，支持 HTML。仅在 lock 为 true 时下发
     *           "tip": "《我在无限游戏里封神》广播剧全站播放量达到 1000W 时可解锁该表情哦！<br /><a href=\"https://www.test.com\">超链接</a>",
     *           // 客户端是否展示评论内的专属表情，只需判断该表情的评论发送时间（ctime 字段）是否在展示时间内，即使该专属表情包 order 为 0 或 lock 为 true
     *           // start_time 和 end_time 不下发时表示永久展示，若只存在 start_time 表示只判断历史评论展示范围的开始时间，或者只存在 end_time 表示只判断历史评论展示范围的结束时间
     *           "start_time": 1684737121,  // 评论中专属表情展示开始时间戳，单位：秒
     *           "end_time": 1694737121  // 评论中专属表情展示截止时间戳，单位：秒
     *         }
     *       }
     *     }
     */
    public function actionGetComments(int $e_id, int $type = SoundComment::TYPE_SOUND, int $page = 1,
            int $last_comment_id = 0, int $page_size = DEFAULT_PAGE_SIZE,
            int $order = self::GET_COMMENTS_ORDER_BY_TIME,
            int $recommend_page_size = self::GET_COMMENTS_RECOMMEND_NUM)
    {
        if ($type === SoundComment::TYPE_SOUND
                && (!MSound::checkSoundId($e_id)
                    || ($sound = MSound::find()->select('catalog_id, comments_count, sub_comments_count, create_time')
                        ->where(['id' => $e_id])->one())
                    && $sound && $sound->isForeignForbidden())) {
            $comments = ReturnModel::empty($page, $page_size);
            $comments->pagination['all_count'] = 0;
            return [
                'recommends' => [
                    'Datas' => [],
                    'has_more' => false,
                ],
                'comments' => $comments,
            ];
        }
        // 检查分页参数是否合法
        if ($last_comment_id && $page !== 1) {
            // 传入了上一页的最后一条评论 ID 时，请求的 page 只能为第一页的数据
            throw new HttpException(400, '分页参数错误');
        }
        $pagination_info = PaginationParams::process($page, $page_size);
        // 检测排序参数是否正确
        if (!in_array($order, self::$GET_COMMENTS_ORDER_TYPES)) {
            throw new HttpException(400, '参数错误');
        }
        // 检查请求热门评论是否合法
        $recommend = (int)Yii::$app->request->get('recommend');
        if (($order !== self::GET_COMMENTS_ORDER_BY_TIME
            || $pagination_info->page !== 1) && $recommend) {
            // 若在非指定条件下（默认排序下的第一页评论）传入 $recommend 参数，抛出异常
            throw new HttpException(400, '参数错误');
        }
        $check_user_ids = [];
        $up_user_id = 0;
        // 检查元素是否存在，获取需要过滤黑名单用户评论的 UP 主 ID 和当前登录用户 ID
        SoundComment::checkAndGetElementUpId($type, $e_id, $up_user_id);
        if ($up_user_id) {
            $check_user_ids[] = $up_user_id;
        }
        if ($user_id = (int)Yii::$app->user->id) {
            $check_user_ids[] = $user_id;
        }
        // 发送者能看到自己发送的违规评论
        $exist_violation_comment = false;
        $query = SoundCommentRO::find();
        if ($order === self::GET_COMMENTS_ORDER_BY_TIME) {
            // 指定使用 ctype_element_id 索引来优化按时间排序的 SQL
            $query = $query->from(SoundCommentRO::tableName() . ' AS t FORCE INDEX(ctype_element_id)');
        } elseif ($order === self::GET_COMMENTS_ORDER_BY_COMMENT_NUM) {
            // 指定使用 i_sound_comment_order_sub_comment_num 索引来优化按子评论数排序的 SQL
            $query = $query->from(SoundCommentRO::tableName() . ' AS t FORCE INDEX(i_sound_comment_order_sub_comment_num)');
        } elseif ($order === self::GET_COMMENTS_ORDER_BY_HOT) {
            // 指定使用 idx_ctype_elementid_score 索引来优化按热度查询
            $query = $query->from(SoundCommentRO::tableName() . ' AS t FORCE INDEX(idx_ctype_elementid_score)');
        } else {
            $query = $query->alias('t');
        }
        $query = $query->select('t.id, t.comment_content, t.ctime, t.userid, t.username, t.sub_comment_num, t.like_num, t.ip_detail')
            ->where(['t.c_type' => $type, 't.element_id' => $e_id]);
        // 过滤黑名单用户评论
        $query = BlackUser::getFilterBlacklistQuery($query, $check_user_ids, 't.userid');
        if ($user_id) {
            $exist_violation_comment = SoundCommentRO::hasViolationComment($type, $user_id, $e_id);
            if ($exist_violation_comment) {
                $query->andWhere('(t.userid = :user_id AND t.checked = :checked_violation) OR (t.checked = :checked_common)', [
                    ':user_id' => $user_id,
                    ':checked_violation' => SoundComment::CHECKED_VIOLATION,
                    ':checked_common' => SoundComment::CHECKED_COMMON,
                ]);
            } else {
                $query->andWhere(['t.checked' => SoundComment::CHECKED_COMMON]);
            }
        } else {
            $query->andWhere(['t.checked' => SoundComment::CHECKED_COMMON]);
        }
        if ($order === self::GET_COMMENTS_ORDER_BY_HOT) {
            // 按热度排序并非仅获取热评，所以不对热度或点赞数进行限制
            $query->orderBy(['score' => SORT_DESC, 'id' => SORT_DESC]);
        } elseif ($order === self::GET_COMMENTS_ORDER_BY_COMMENT_NUM) {
            $query->orderBy(['sub_comment_num' => SORT_DESC, 'id' => SORT_DESC]);
        } elseif ($order === self::GET_COMMENTS_ORDER_BY_TIME) {
            if ($last_comment_id) {
                $query->andWhere('id < :comment_id', [':comment_id' => $last_comment_id]);
            }
            $query->orderBy(['id' => SORT_DESC]);
        } else {
            throw new HttpException(400, '排序参数错误');
        }
        // -1 表示从数据库获取评论和子评论数据
        $comments_count = $sub_comments_count = -1;
        if ($type === SoundComment::TYPE_SOUND) {
            // TODO: 将评论数迁移至单独表进行维护
            $comments_count = $sound->comments_count;
            $sub_comments_count = $sound->sub_comments_count;
        }
        // 对评论进行分页处理
        $return_model = MUtils::getPaginationModels(
            $query,
            $pagination_info->page_size,
            [],
            $comments_count
        );
        // 评论用户 ID 数组
        $user_ids = [];
        // 相关元素的评论信息
        $result = [];
        if ($recommend) {
            // 是否有更多热评
            $has_more = false;
            if ($return_model->pagination['count'] < self::GET_COMMENTS_RECOMMEND_MIN_COMMENT_NUM) {
                // 若元素父评论数小于出现热评条件的数目，则返回 0 条热评
                $recommend_comments = [];
            } else {
                // 满足条件时查询热门评论
                // 热度排序规则参考：https://info.missevan.com/pages/viewpage.action?pageId=15370540
                $query = SoundCommentRO::find()
                    ->alias('t')
                    ->select('t.id, t.comment_content, t.c_type, t.element_id, t.ctime, t.userid, t.username, t.sub_comment_num, t.like_num, t.floor, t.ip_detail')
                    ->where(['t.c_type' => $type, 't.element_id' => $e_id])
                    ->andWhere('score >= :min_hot', [':min_hot' => self::GET_COMMENTS_RECOMMEND_MIN_HOT]);
                // 过滤黑名单用户评论
                $query = BlackUser::getFilterBlacklistQuery($query, $check_user_ids, 't.userid');
                if ($exist_violation_comment) {
                    $query->andWhere('(t.userid = :user_id AND t.checked = :checked_violation) OR (t.checked = :checked_common)', [
                        ':user_id' => $user_id,
                        ':checked_violation' => SoundComment::CHECKED_VIOLATION,
                        ':checked_common' => SoundComment::CHECKED_COMMON,
                    ]);
                } else {
                    $query->andWhere(['t.checked' => SoundComment::CHECKED_COMMON]);
                }
                $recommend_comments = $query->orderBy(['score' => SORT_DESC, 'id' => SORT_DESC])
                    ->limit($recommend_page_size + 1)->all();
                $recommend_conments_num = count($recommend_comments);
                if ($recommend_conments_num > $recommend_page_size) {
                    // 若热评数量超过要显示的条数，去掉多余的条数，并给标识是否存在热评的参数赋值为真
                    array_pop($recommend_comments);
                    $has_more = true;
                }
                if ($recommend_comments) {
                    // 若存在热评，则将热评用户 ID 加入需要更新个人信息的用户 ID 数组中
                    $user_ids = array_column($recommend_comments, 'userid');
                }
            }
            $result = [
                'recommends' => [
                    'Datas' => $recommend_comments,
                    'has_more' => $has_more
                ]
            ];
        }
        if (!empty($return_model->Datas)) {
            $user_ids = array_merge($user_ids, array_column($return_model->Datas, 'userid'));
            $comment_ids = array_column($return_model->Datas, 'id');
            if (!empty($comment_ids)) {
                $query = SoundSubCommentRO::find()
                    ->alias('t')
                    ->select('t.id, t.comment_content, t.comment_id, t.ctime, t.userid, t.username, t.like_num')
                    ->where(['t.comment_id' => $comment_ids])
                    ->andWhere('t.floor <= :floor', [':floor' => self::GET_COMMENTS_SUB_COMMENTS_NUM]);
                // 过滤黑名单用户评论
                $query = BlackUser::getFilterBlacklistQuery($query, $check_user_ids, 't.userid');
                if ($user_id) {
                    // 仅发送者能看到自己发送的违规子评论
                    if (SoundSubCommentRO::hasViolationSubComment($user_id, $comment_ids)) {
                        $query->andWhere('(t.userid = :user_id AND t.checked = :checked_violation)' .
                            ' OR (t.checked = :checked_common)', [
                                ':user_id' => $user_id,
                                ':checked_violation' => SoundSubComment::CHECKED_VIOLATION,
                                ':checked_common' => SoundSubComment::CHECKED_COMMON,
                            ]);
                    } else {
                        $query->andWhere(['t.checked' => SoundSubComment::CHECKED_COMMON]);
                    }
                } else {
                    $query->andWhere(['t.checked' => SoundSubComment::CHECKED_COMMON]);
                }
                $sub_comments = $query->orderBy(['t.ctime' => SORT_ASC])->all();
                foreach ($return_model->Datas as &$comment) {
                    $comment['subcomments'] = array_values(array_filter(
                        $sub_comments,
                        function ($sub_comment) use ($comment) {
                            if ($sub_comment->comment_id === $comment->id) {
                                return true;
                            }
                        }
                    ));
                    if ($comment['subcomments']) {
                        $user_ids = array_merge($user_ids, array_column($comment['subcomments'], 'userid'));
                    }
                }
            }
            if ($sub_comments_count === -1) {
                $sub_comments_count = SoundComment::getAllSubCommentNum($type, $e_id);
            }
            // 查询元素子评论数并加入到总评论数中
            $return_model->pagination['all_count'] =
                $return_model->pagination['count'] + $sub_comments_count;
        }
        unset($comment);
        $update_comments = isset($result['recommends']['Datas'])
            ? array_merge($result['recommends']['Datas'], $return_model->Datas) : $return_model->Datas;
        // 更新评论中的用户信息
        self::updateCommentUserInfo($user_ids, $update_comments, $type, $e_id);
        // 查询用户是否已经点赞
        CommentLike::DoYouLike($update_comments, $user_id, false);
        // 获取用户 IP 属地
        SoundComment::showIPLocation($update_comments);
        // 更新消息提示
        if ($user_id) {
            Commentnotice::updateAll(
                ['isread' => Commentnotice::HAS_READ],
                "type = $type AND eId = $e_id AND a_user_id = $user_id"
            );
        }
        if ($order === self::GET_COMMENTS_ORDER_BY_TIME) {
            // 当按时间倒序排序时，当前页数总是为 1，所以不能通过判断当前页数与最大页数来给 has_more 赋值
            // 此时当获取的评论大于或等于指定数量时，has_more 为 true
            // FIXME: 此处可能出现获得最后一页的评论数量正好为指定数量，此时 has_more 为 true，客户端会额外多请求一次
            $return_model->has_more = count($return_model->Datas) >= $pagination_info->page_size;
        }
        $result['comments'] = $return_model;
        // TODO: 之后可能不需要在每次请求时都下发
        // 获取专属表情包
        self::getExclusiveEmotes($result, $type, $e_id);
        if ($page === 1 && $type === SoundComment::TYPE_SOUND) {
            // 音频播放评论第一页获取小黄条广告
            try {
                $os = Yii::$app->equip->isAndroidOrHarmonyOS() ? Equipment::Android : Equipment::iOS;
                // 这里请求 rpc 接口的类型参数和 $type 含义和数值都不同，因此不能用 $type
                $ad = Yii::$app->serviceRpc->getCommentAdInfo($os, MPersonaModuleElement::ELEMENT_TYPE_SOUND, $e_id);
                if ($ad['comment_ad']) {
                    $result['comment_ad'] = $ad['comment_ad'];
                }
            } catch (Exception $e) {
                Yii::error(sprintf('获取音频 %d 评论区小黄条信息出错: %s', $e_id, $e->getMessage()), __METHOD__);
            }
        }
        return $result;
    }

    /**
     * @api {get} /message/get-sub-comment 获取子评论（分页）
     * @apiDescription /mobile/site/getSubComment c_id => comment_id p=> page pagesize => page_size
     *
     * @apiVersion 0.1.0
     * @apiName get-sub-comment
     * @apiGroup message
     *
     * @apiParam {Number} comment_id 评论 ID
     * @apiParam {number=0,1} [get=0] 是否获取父评论（0：不获取；1：获取）
     * @apiParam {number=0,1} [sub=0] 按子评论 ID 获取（0：不获取；1：获取。get 和 sub 同为 1 时，comment_id 为子评论 ID)
     * @apiParam {number=1,3} [order=1] 子评论排序，1：时间升序，3：热度倒序
     * @apiParam {Number} [page=1] 页数
     * @apiParam {Number} [page_size=20] 每页个数
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "info": {
     *           "Datas": [{
     *             "id": 423673,
     *             "comment_content": "剧集跳转链接 https://www.missevan.com/mdrama/77631，单集跳转链接 https://www.missevan.com/sound/player?id=9811365",
     *             "jump_url": {  // 评论内容中的跳转链接信息（仅有站内跳转链接时下发）
     *               "https://www.missevan.com/mdrama/77631": {  // 跳转链接地址（客户端需要将对应跳转链接替换为相应文案的可点击的跳转链接格式）
     *                 "title": "十年对手，一朝占有",  // 跳转链接文案
     *                 "prefix_icon": "http://static-test.maoercdn.com/test.jpg"  // 跳转链接前缀图标
     *               },
     *               "https://www.missevan.com/sound/player?id=9811365": {  // 跳转链接地址（客户端需要将对应跳转链接替换为相应文案的可点击的跳转链接格式）
     *                 "title": "十年对手，一朝占有 . 第一集",  // 跳转链接文案
     *                 "prefix_icon": "http://static-test.maoercdn.com/test.jpg"  // 跳转链接前缀图标
     *               }
     *             },
     *             "ctime": 1642505812,
     *             "user": {
     *               "user_id": 9075893,  // 用户 ID
     *               "username": "原啊啊",  // 用户昵称
     *               "iconurl": "http://test.com/avatar.jpg",  // 用户头像
     *               "avatar_frame_url": "http://test.com/frame.webp",  // 用户头像框，没有时不下发
     *               "authenticated": 0,  // 加 V 认证标识：0 没有 V，1 代表黑 V，2 代表金 V，3 代表蓝 V
     *               "is_vip": 1  // 用户是否为会员。0：否；1：是
     *             },
     *             "like_num": 0,
     *             "liked": 0,
     *             "disliked": 0,
     *             "ip_location": "山东"
     *           }],
     *           "pagination": {
     *             "p": 1,
     *             "maxpage": 1,
     *             "count": 13,
     *             "pagesize": 20
     *           }
     *         },
     *         "comment": {
     *           "id": 1254531,
     *           "comment_content": "剧集跳转链接 https://www.missevan.com/mdrama/77631，单集跳转链接 https://www.missevan.com/sound/player?id=9811365",
     *           "jump_url": {  // 评论内容中的跳转链接信息（仅有站内跳转链接时下发）
     *             "https://www.missevan.com/mdrama/77631": {  // 跳转链接地址（客户端需要将对应跳转链接替换为相应文案的可点击的跳转链接格式）
     *               "title": "十年对手，一朝占有",  // 跳转链接文案
     *               "prefix_icon": "http://static-test.maoercdn.com/test.jpg"  // 跳转链接前缀图标
     *             },
     *             "https://www.missevan.com/sound/player?id=9811365": {  // 跳转链接地址（客户端需要将对应跳转链接替换为相应文案的可点击的跳转链接格式）
     *               "title": "十年对手，一朝占有 . 第一集",  // 跳转链接文案
     *               "prefix_icon": "http://static-test.maoercdn.com/test.jpg"  // 跳转链接前缀图标
     *             }
     *           },
     *           "c_type": 1,
     *           "element_id": 90602,
     *           "ctime": 1642505803,
     *           "user": {
     *             "user_id": 9075893,  // 用户 ID
     *             "username": "原啊啊",  // 用户昵称
     *             "iconurl": "http://test.com/avatar.jpg",  // 用户头像
     *             "avatar_frame_url": "http://test.com/frame.webp",  // 用户头像框，没有时不下发
     *             "authenticated": 0,  // 加 V 认证标识：0 没有 V，1 代表黑 V，2 代表金 V，3 代表蓝 V
     *             "is_vip": 1  // 用户是否为会员。0：否；1：是
     *           },
     *           "sub_comment_num": 13,
     *           "like_num": 0,
     *           "floor": 0,
     *           "checked": 1,
     *           "ip_location": "山东"
     *         },
     *         "emotes": [  // 专属表情包，没有时不下发
     *           {
     *             "package_id": 3,  // 表情包 ID
     *             "lock": true,  // 是否解锁，解锁后才可发送，否则表情包发送窗口展示该表情包中的锁定信息（tip 信息）
     *             // 解锁说明，支持 HTML。仅在 lock 为 true 时下发
     *             "tip": "《我在无限游戏里封神》广播剧全站播放量达到 1000W 时可解锁该表情哦！<br /><a href=\"https://www.test.com\">超链接</a>",
     *             // 客户端是否展示评论内的专属表情，只需判断该表情的评论发送时间（ctime 字段）是否在展示时间内，即使该专属表情包 order 为 0 或 lock 为 true
     *             // start_time 和 end_time 不下发时表示永久展示，若只存在 start_time 表示只判断历史评论展示范围的开始时间，或者只存在 end_time 表示只判断历史评论展示范围的结束时间
     *             "start_time": 1684737121,  // 评论中专属表情展示开始时间戳，单位：秒
     *             "end_time": 1694737121  // 评论中专属表情展示截止时间戳，单位：秒
     *           }
     *         ]
     *       }
     *     }
     *
     * @apiSuccessExample Success-Response: iOS 4.9.9、Android 5.8.2 下发
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "info": {
     *           ...
     *         },
     *         "comment": {
     *           ...
     *         },
     *         "emote": {  // 专属表情包，没有时不下发
     *           "package_id": 3,  // 表情包 ID
     *           "lock": true,  // 是否解锁，解锁后才可发送，否则表情包发送窗口展示该表情包中的锁定信息（tip 信息）
     *           // 解锁说明，支持 HTML。仅在 lock 为 true 时下发
     *           "tip": "《我在无限游戏里封神》广播剧全站播放量达到 1000W 时可解锁该表情哦！<br /><a href=\"https://www.test.com\">超链接</a>",
     *           // 客户端是否展示评论内的专属表情，只需判断该表情的评论发送时间（ctime 字段）是否在展示时间内，即使该专属表情包 order 为 0 或 lock 为 true
     *           // start_time 和 end_time 不下发时表示永久展示，若只存在 start_time 表示只判断历史评论展示范围的开始时间，或者只存在 end_time 表示只判断历史评论展示范围的结束时间
     *           "start_time": 1684737121,  // 评论中专属表情展示开始时间戳，单位：秒
     *           "end_time": 1694737121  // 评论中专属表情展示截止时间戳，单位：秒
     *         }
     *       }
     *     }
     */
    public function actionGetSubComment(int $comment_id, int $sub = 0, int $get = 0,
            int $order = self::GET_COMMENTS_ORDER_BY_TIME, int $page_size = PAGE_SIZE_20)
    {
        if (!in_array($order, [self::GET_COMMENTS_ORDER_BY_TIME, self::GET_COMMENTS_ORDER_BY_HOT])) {
            throw new HttpException(400, '排序参数错误');
        }
        if ($sub && $get) {
            $sub_comment = SoundSubCommentRO::find()
                ->select('comment_id')
                ->where('id = :id', [':id' => $comment_id])
                ->one();
            if (!$sub_comment) {
                throw new HttpException(404, Yii::t('app/error', 'Original comment is missing'), 200310001);
            }
            $comment_id = $sub_comment->comment_id;
        }
        if ($comment_id <= 0) {
            throw new HttpException(404, Yii::t('app/error', 'Original comment is missing'), 200310001);
        }
        // 获取评论对象的 UP 主 ID
        $element = SoundCommentRO::find()
            ->select('element_id, c_type')
            ->where('id = :id', ['id' => $comment_id])
            ->one();
        if (!$element) {
            throw new HttpException(404, Yii::t('app/error', 'Original comment is missing'), 200310001);
        }
        // 需要过滤评论对象 UP 主和当前用户的黑名单评论
        $check_user_ids = [];
        $up_user_id = 0;
        // 检查元素是否存在，并且获取需要过滤黑名单用户评论的 UP 主 ID 和当前登录用户 ID
        SoundComment::checkAndGetElementUpId($element->c_type, $element->element_id, $up_user_id);
        if ($up_user_id) {
            $check_user_ids[] = $up_user_id;
        }
        if ($user_id = (int)Yii::$app->user->id) {
            $check_user_ids[] = $user_id;
        }
        $comment = null;
        $comment_element_type = $element->c_type;
        $comment_element_id = $element->element_id;
        if ($get) {
            $query = SoundCommentRO::find()
                ->alias('t')
                ->select('t.id, t.comment_content, t.c_type, t.element_id, t.ctime, t.userid, t.username,'
                    . ' t.sub_comment_num, t.like_num, t.floor, t.checked, t.ip_detail')
                ->where(['t.id' => $comment_id]);
            // 过滤黑名单用户评论
            $query = BlackUser::getFilterBlacklistQuery($query, $check_user_ids, 't.userid');
            $comment = $query->one();
            if (!$comment) {
                throw new HttpException(404, Yii::t('app/error', 'Original comment is missing'), 200310001);
            }
            CommentLike::DoYouLike($comment, $user_id, false);
            SoundComment::showIPLocation($comment);
            $user = Mowangskuser::findOne($comment->userid);
            if (!$user) {
                throw new HttpException(404, Yii::t('app/error', 'User does not exist'), 200020001);
            }
            if (Equipment::isAppOlderThan('6.0.4', '6.0.4')) {
                // 兼容旧版本（安卓 < 6.0.4, iOS < 6.0.4）的数据格式
                $comment->authenticated = $user->authenticated ?? 0;
                $comment->icon = $user->iconurl;
                $comment->username = $user->username;
            } else {
                // 获取用户信息和用户头像框
                $user_avatar_frame_map = [];
                try {
                    $user_avatar_frame_map = Yii::$app->serviceRpc->listAvatarFrame([$comment->userid],
                        ServiceRpc::LIST_AVATAR_FRAME_SCENE_COMMENT, $comment_element_type, $comment_element_id);
                } catch (Exception $e) {
                    Yii::error("评论区获取用户头像框信息失败，element_type: {$comment_element_type}, element_id: {$comment_element_id}, error: "
                        . $e->getMessage(), __METHOD__);
                    // PASS
                }
                $user_avatar_frame = $user_avatar_frame_map[$comment->userid]['avatar_frame_url'] ?? null;
                $comment->user = self::getCommentUserInfo($comment->userid, $user, $user_avatar_frame,
                    MUserVip::isVipUser($comment->userid));
                // 删除未使用的字段
                unset($comment->userid, $comment->username);
            }
            unset($user);
        }
        $query = SoundSubCommentRO::find()
            ->alias('t')
            ->select('t.id, t.comment_content, t.ctime, t.userid, t.username, t.like_num, t.ip_detail')
            ->where(['t.comment_id' => $comment_id]);
        // 过滤黑名单用户子评论
        $query = BlackUser::getFilterBlacklistQuery($query, $check_user_ids, 't.userid');
        if ($order === self::GET_COMMENTS_ORDER_BY_TIME) {
            $query = $query->orderBy(['t.ctime' => SORT_ASC]);
        } elseif ($order === self::GET_COMMENTS_ORDER_BY_HOT) {
            $query = $query->orderBy(['t.score' => SORT_DESC, 't.ctime' => SORT_ASC]);
        }
        if ($user_id) {
            // TODO: 之后也整合成一个公用的方法
            // 仅发送者能看到自己发送的违规评论
            if (SoundSubCommentRO::hasViolationSubComment($user_id, $comment_id)) {
                $query->andWhere('(t.userid = :user_id AND t.checked = :checked_violation) OR ' .
                    '(t.checked = :checked_common)', [
                        ':user_id' => $user_id,
                        ':checked_violation' => SoundSubComment::CHECKED_VIOLATION,
                        ':checked_common' => SoundSubComment::CHECKED_COMMON,
                    ]);
            } else {
                $query->andWhere(['t.checked' => SoundSubComment::CHECKED_COMMON]);
            }
        } else {
            $query->andWhere(['t.checked' => SoundSubComment::CHECKED_COMMON]);
        }
        $return_model = MUtils::getPaginationModels($query, $page_size, []);
        CommentLike::DoYouLike($return_model->Datas, $user_id, true);
        SoundComment::showIPLocation($return_model->Datas);
        if ($return_model->Datas) {
            $user_ids = array_column($return_model->Datas, 'userid');
            self::updateCommentUserInfo($user_ids, $return_model->Datas, $comment_element_type, $comment_element_id);
        }
        $return = [
            'info' => $return_model,
            'comment' => $comment
        ];
        // 获取专属表情包
        self::getExclusiveEmotes($return, $element->c_type, $element->element_id);

        return $return;
    }

    /**
     * 获取专属表情包
     *
     * @param array $return 需要兼容的返回数据
     * @param int $element_type 专属表情元素类型
     * @param int $element_id 专属表情元素 ID
     */
    private static function getExclusiveEmotes(array &$return, int $element_type, int $element_id)
    {
        $result = Yii::$app->serviceRpc->getExclusiveEmotes($element_id, $element_type);
        $emotes = $result['emotes'] ?? [];
        if (!empty($emotes)) {
            if (Equipment::isAppOlderThan('6.0.9', '6.0.9')) {
                // WORKAROUND: iOS < 6.0.9 或 Android < 6.0.9 仅支持描述专属表情是否解锁
                $emotes = array_map(function ($item) use ($element_type, $element_id) {
                    $status = $item['status'];
                    if ($status === self::STATUS_LOCKED) {
                        $item['lock'] = true;
                    } elseif ($status === self::STATUS_UNLOCK) {
                        $item['lock'] = false;
                    } elseif ($status === self::STATUS_OFFLINE) {
                        // status 为专属表情已下线时，返回 lock 字段为 false (专属表情已解锁)，将来不兼容老版本后，此处调整成未解锁
                        $item['lock'] = false;
                    } else {
                        Yii::error("专属表情状态错误，status: $status, element_type: $element_type, element_id: $element_id", __METHOD__);
                        $item['lock'] = true;
                        // PASS
                    }
                    unset($item['status']);
                    return $item;
                }, $emotes);
            }

            if (Equipment::isAppVersion(Equipment::iOS, '4.9.9')
                    || Equipment::isAppVersion(Equipment::Android, '5.8.2')) {
                // WORKAROUND: iOS 4.9.9、Android 5.8.2 仅支持下发单个专属表情
                $return['emote'] = $emotes[0];
            } else {
                $return['emotes'] = $emotes;
            }
        }
    }

    /**
     * @api {get} /message/get-comment-notice 评论提醒
     * @apiDescription 评论提醒
     *
     * @apiVersion 0.1.0
     * @apiName get-comment-notice
     * @apiGroup message
     *
     * @apiParam {Number} [page_size=20] 一页大小.
     * @apiParam {Number} [page=1] 第几页
     * @apiParam {number=0,1,2} [type=1] 类型：0 为所有，1 为未读，2 为已读
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [{
     *           "id": 48709,
     *           "c_user_id": 7777777,
     *           "c_user_name": "chenhao123",
     *           "a_user_id": 346286,
     *           "a_user_name": "InVinCiblezz",
     *           "type": 1,
     *           "eId": 548027,  // iOS < 4.6.6，Android < 5.5.5
     *           "element_id": 548027,  // iOS >= 4.6.6，Android >= 5.5.5
     *           "url": "",  // url 字段不为空说明点击需要打开这个 URL
     *           "title": "",
     *           "sub": 0,
     *           "comment_id": 32069,
     *           "isread": 1,
     *           "front_cover": "",
     *           "comment": {
     *             "id": 32069,
     *             "comment_content": "@23336666(346287) 剧集跳转链接 https://www.missevan.com/mdrama/77631，单集跳转链接 https://www.missevan.com/sound/player?id=9811365",
     *             "jump_url": {  // 评论内容中的跳转链接信息（仅有站内跳转链接时下发）
     *               "https://www.missevan.com/mdrama/77631": {  // 跳转链接地址（客户端需要将对应跳转链接替换为相应文案的可点击的跳转链接格式）
     *                 "title": "十年对手，一朝占有",  // 跳转链接文案
     *                 "prefix_icon": "http://static-test.maoercdn.com/test.jpg"  // 跳转链接前缀图标
     *               },
     *               "https://www.missevan.com/sound/player?id=9811365": {  // 跳转链接地址（客户端需要将对应跳转链接替换为相应文案的可点击的跳转链接格式）
     *                 "title": "十年对手，一朝占有 . 第一集",  // 跳转链接文案
     *                 "prefix_icon": "http://static-test.maoercdn.com/test.jpg"  // 跳转链接前缀图标
     *               }
     *             },
     *             "user": {
     *               "user_id": 9075893,  // 用户 ID
     *               "username": "原啊啊",  // 用户昵称
     *               "iconurl": "http://test.com/avatar.jpg",  // 用户头像
     *               "avatar_frame_url": "http://test.com/frame.webp",  // 用户头像框，没有时不下发
     *               "authenticated": 0  // 加 V 认证标识：0 没有 V，1 代表黑 V，2 代表金 V，3 代表蓝 V
     *             },
     *             "userid": 7777777,  // 请使用 user 中的用户信息，后续会移除该字段
     *             "username": "chenhao123",  // 请使用 user 中的用户信息，后续会移除该字段
     *             "icon": "http://static.missevan.com/avatars/icon01.png"  // 请使用 user 中的用户信息，后续会移除该字段
     *           },
     *           "work_id": 614,  // 消息提醒为语音包或运势语音时，返回作品 ID（其他情况不返回）
     *           "user_id": 346286,  // 消息提醒为音频或音单时，返回对应的用户 ID、用户名（其他情况不返回，当其不存在时返回的用户 ID 为 0、用户名为空字符串）
     *           "username": "暗切线"
     *         }],
     *         "pagination": {
     *           "p": 1,
     *           "count": "149",
     *           "maxpage": 8,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     */
    public function actionGetCommentNotice(int $type = self::COMMENT_NOTICE_READ_TYPE_UNREAD, int $page_size = PAGE_SIZE_20)
    {
        $user_id = Yii::$app->user->id;
        $comment_notices = $this->getCommentOrAtNotice(
            $user_id,
            Commentnotice::NOTICE_TYPE_COMMENT,
            $type,
            $page_size
        );
        // 设置评论的未读消息数为 0
        self::setReadNotice($user_id, ['comment' => 0]);
        if (Equipment::isAppOlderThan('4.6.6', '5.5.5')) {
            return $comment_notices;
        }
        // WORKAROUND: iOS >= 4.6.6，Android >= 5.5.5，评论的对象 ID 字段修改为 element_id
        // 活动或专题下的评论返回对应的活动或专题链接
        $comment_notices->Datas = array_map(function ($item) {
            $return = [
                'id' => $item->id,
                'c_user_id' => $item->c_user_id,
                'c_user_name' => $item->c_user_name,
                'a_user_id' => $item->a_user_id,
                'a_user_name' => $item->a_user_name,
                'type' => $item->type,
                // TODO: 之后会调整 app_missevan.commentnotice 表中的 eId 字段为 element_id
                'element_id' => $item->eId,
                'title' => $item->title,
                'sub' => $item->sub,
                'comment_id' => $item->comment_id,
                'isread' => $item->isread,
                'front_cover' => $item->front_cover,
                'comment' => $item->comment,
                'url' => '',
            ];
            // 活动或专题评论则返回对应的活动或专题链接
            switch ($return['type']) {
                case Commentnotice::COMMENT_NOTICE_TYPE_SOUND:
                case Commentnotice::COMMENT_NOTICE_TYPE_ALBUM:
                    $return['user_id'] = $item->user_id;
                    $return['username'] = $item->username;
                    break;
                case Commentnotice::COMMENT_NOTICE_TYPE_EVENT:
                    $return['url'] = Yii::$app->params['domainMissevan'] . '/mevent/' . $return['element_id'];
                    break;
                case Commentnotice::COMMENT_NOTICE_TYPE_TOPIC:
                    $return['url'] = Yii::$app->params['domainMissevan'] . '/mtopic/' . $return['element_id'];
                    break;
                case Commentnotice::COMMENT_NOTICE_TYPE_VOICE_CARD:
                case Commentnotice::COMMENT_NOTICE_TYPE_OMIKUJI_CARD:
                    // 消息提醒为语音包或运势语音时的作品 ID
                    $return['work_id'] = $item->work_id;
                    break;
            }
            return $return;
        }, $comment_notices->Datas);
        return $comment_notices;
    }

    /**
     * @api {get} /message/unread-notice 获取未读消息
     *
     * @apiDescription 获取未读消息（评论，点赞消息，私信，意见反馈，动态）\
     * 获取小红点状态 \
     * remind 字段表示是否有新的消息：有为 1，否则为 0 \
     * 建议缓存时间: 1 分钟 \
     * 客户端应在本地缓存该 API 数据，并在用户点击对应红点位置后，能本地去除对应位置的红点 \
     * total 总消息数量（缓存本地 1 分钟）\
     * 若用户点击评论（私信、@、赞、系统通知）后则扣除评论（私信、@、赞、系统通知）数量 \
     * live_last_time 关注主播最新开播时间，需要客户端本地判断新获取的这个值比之前获取的值大则显示红点 \
     * 说明文档地址 https://github.com/MiaoSiLa/requirements-documents/blob/master/2018-08-07%20我的消息/README.md
     *
     * @apiVersion 1.0.0
     * @apiName unread-notice
     * @apiGroup message
     *
     * @apiParam {String} [sobot_visitor_id] 智齿访客 ID（客户端初始化智齿时获得，没有时不传）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "comment": 0,  // 评论未读提醒数量
     *         "at_me": 0,  // @ 未读提醒数量
     *         "like": 0,  // 点赞未读提醒数量
     *         "sys": 0,  // 系统通知未读提醒数量
     *         "feedback": 0,  // 用户反馈客服回复未读提醒数量
     *         "feedback_last_time": 1538192177,  // 最新的用户反馈客服回复时间，单位：秒
     *         "sobot": 0,  // 智齿客服回复未读消息数量
     *         "feed": 0,  // 投稿动态未读提醒数量，iOS < 6.1.0、Android < 6.1.0 下发
     *         "msg": 0,  // 私信未读提醒数量
     *         "remind": 0,  // 是否有新的未读消息（有为 1，否则为 0）
     *         "special": 0,  // 特殊系统通知未读提醒数量
     *         "total": 0,  // 总消息数量（comment + at_me + like + sys + msg）
     *         "live_last_time": 1566183833,  // iOS < 6.1.0、Android < 6.1.0 下发，关注主播最新开播时间，单位：秒
     *         // 直播 tab 测试实验：site/config 接口下发的 tab_bar_live 值来区分对照组和实验组。0: 对照组；1：实验组 - 双列直播流；2：实验组 - 全屏直播流
     *         "feed_notice": {  // iOS ≥ 6.1.0、Android ≥ 6.1.0 下发，显示在我听图标上的动态未读提醒；iOS ≥ 6.2.2、Android ≥ 6.2.2 的直播 tab 测试实验组时，该字段不下发【直播动态提醒】
     *           "type": 2,  // 类型 1：投稿动态；2：追剧动态；3：直播动态
     *           "id": 25032,  // 音频 ID、剧集 ID 或直播间 ID
     *           "icon": "http://static-test.maoercdn.com/dramacoversmini/201912/31/709b79e161545.jpg",  // 主播头像
     *           // 直播 tab 测试对照组，显示【发现】图标；直播 tab 测试实验组，显示【直播】图标或直播动态提醒
     *           "live_notice" {  // iOS ≥ 6.2.2、Android ≥ 6.2.2 的直播 tab 测试实验组下发，显示在直播图标上；没有直播动态提醒时下发为 null
     *             "id": 65261414,  // 直播间 ID
     *             "icon": "https://static-test.maoercdn.com/avatars/icon01.png"  // 主播头像
     *           }
     *         }
     *       }
     *     }
     */
    public function actionUnreadNotice(string $sobot_visitor_id = '')
    {
        $sobot_visitor_id = trim($sobot_visitor_id);
        $return_num = self::noticeNum();
        $total_msg_num = $return_num['comment'] + $return_num['at_me'] + $return_num['like'] + $return_num['sys'] +
            $return_num['msg'];
        // 是否有新的消息（有为 1，否则为 0）
        $return_num['remind'] = $total_msg_num > 0 ? 1 : 0;
        // 反馈提醒
        $user_id = Yii::$app->user->id;
        $feedback = $user_id
            ? AnFeedback::getFeedbackNoticeByUserId($user_id)
            : AnFeedback::getFeedbackNoticeByEquipId(Yii::$app->equip->getEquipId());
        $return_num['feedback'] = $feedback['notice'];
        $return_num['feedback_last_time'] = $feedback['last_time'];
        $return_num['total'] = $total_msg_num;
        if ($user_id && $m_sobot_user = MSobotUser::findOne(['user_id' => $user_id])) {
            // 登录用户使用服务端存储的 visitor_id
            $sobot_visitor_id = $m_sobot_user->visitor_id;
        }
        $return_num['sobot'] = MSobotUser::getSobotOfflineMsgCount($sobot_visitor_id);
        if (Equipment::isAppOlderThan(null, '6.1.5')) {
            // WORKAROUND: 由于技术方案变更，客户端计划 6.1.5 正式发版，安卓 6.1.0 已经发版过，因此对安卓 < 6.1.5 版本直接返回 sobot 为 0
            $return_num['sobot'] = 0;
        }
        if (Equipment::isAppVersion(Equipment::iOS, '6.1.0') && is_null($return_num['feed_notice'])) {
            // WORKAROUND: iOS = 6.1.0 时，没有更新提醒则不下发 feed-notice 字段（解决 iOS 6.1.0 版本没有空值判断的问题）
            unset($return_num['feed_notice']);
        }

        return $return_num;
    }

    /**
     * @api {get} /message/message-list{?type,page,page_size} 私信列表及未读数目
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/message/message-list
     * @apiSampleRequest message/message-list
     * @apiDescription  /mobile/personOperation/messageList 私信列表 p=>page pagesize => page_size
     *
     * @apiVersion 0.1.0
     * @apiName message-list
     * @apiGroup message
     *
     * @apiParam {Number} [type=0] 私信类型 0：全部；1：未关注用户的私信
     * @apiParam {Number} [page=1] 当前页数
     * @apiParam {Number} [page_size=30] 每页个数
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [
     *           {
     *             "receive_id": 248506,
     *             "receive_name": "Ruii",
     *             "receive_icon": "http://static.missevan.com/avatars/201709/06/e8cd3ca395d1101214.jpg",
     *             "authenticated": 0,
     *             "msg": "这是协议链接私信哦 <a href=\"missevan://1474888\">网页链接</a>",
     *             "type": 1,  // 私信文本类型，0：纯文本，1：HTML
     *             "ctime": 1576223840,
     *             "not_read": 0,
     *             "is_official": true,  // 是否为官方账号
     *             "is_vip": 1  // 用户是否为会员。0：否；1：是
     *           },
     *           {
     *             "receive_id": 0,
     *             "receive_name": "未关注人消息",
     *             "receive_icon": "http://static.missevan.com/mimagesmini/201912/13/f9d69da7180311.png",
     *             "authenticated": 0,
     *             "msg": "[ 1 条 ] 有新的未关注人消息",
     *             "type": 0,
     *             "ctime": 1576156338,
     *             "not_read": 0
     *             "is_official": true
     *           },
     *           {
     *             "receive_id": 26,
     *             "receive_name": "M娘",
     *             "receive_icon": "http://static.missevan.com/avatars/201608/25/4e8d635b68cd181847.gif",
     *             "authenticated": 0,
     *             "msg": "非常抱歉！您的提现申请没有通过审核，拒绝理由：gjhgfjgj",
     *             "type": 0,
     *             "ctime": 1573096253,
     *             "not_read": 0
     *             "is_official": false,
     *             "is_vip": 1  // 用户是否为会员。0：否；1：是
     *           }
     *         ],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 1,
     *           "count": 6,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     */
    public function actionMessageList(int $type = AnMsg::TYPE_MESSAGE_ALL, int $page = 1,
            int $page_size = DEFAULT_PAGE_SIZE)
    {
        $user_id = Yii::$app->user->id;
        $page_params = PaginationParams::process($page, $page_size);
        return AnMsg::getMessageList($user_id, $type, $page_params->page, $page_params->page_size);
    }

    /**
     * @api {get} /message/message-detail{?user_id,page_size,page} 私信详情
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/message/message-detail
     * @apiSampleRequest message/message-detail
     * @apiDescription 返回字段中 blacklist 字段代表和用户的黑名单关系
     * 0：未拉黑对方；1：已拉黑对方；
     *
     * @apiVersion 0.1.0
     * @apiName message-detail
     * @apiGroup message
     *
     * @apiParam {Number} user_id 私信用户 ID
     * @apiParam {Number} [page_size=30]  每页个数
     * @apiParam {Number} [page=1]  当前页数
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "receive_user": {
     *           "user_id": 1,
     *           "username": 21264,
     *           "authenticated": 0,
     *           "is_official": true  // 是否为官方账号
     *         }
     *         "Datas": [
     *           {
     *             "id": 21264,
     *             "post_name": "加特林",
     *             "post_icon": "http://static.missevan.com/avatars/201909/17/d6c8a7e5b4b2f1184123.png",
     *             "post_color": "#B1B1B1m#CECECEm#B1B1B1m#6A6A6Am#B1B1B1",
     *             "status": 4,
     *             "msg": "啦啦啦啦啦啦啦33333",
     *             "ctime": 1576319974,
     *             "post_id": 349524,
     *             "authenticated": 0,
     *             "type": 0  // 私信文本类型，0：纯文本，1：HTML
     *           },
     *           {
     *             "id": 21265,
     *             "post_name": "加特林",
     *             "post_icon": "http://static.missevan.com/avatars/201909/17/d6c8a7e5b4b2f1184123.png",
     *             "post_color": "#B1B1B1m#CECECEm#B1B1B1m#6A6A6Am#B1B1B1",
     *             "status": 4,
     *             "msg": "这是私信内容 <a href="https://www.test.com/sound/1">网页链接</a>",
     *             "ctime": 1576319974,
     *             "post_id": 349524,
     *             "authenticated": 0,
     *             "type": 1
     *           }
     *         ],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 1,
     *           "count": 6,
     *           "pagesize": 20
     *         },
     *         "relation": {
     *           "blacklist": 0,
     *           "followed": 1
     *         }
     *       }
     *     }
     */
    public function actionMessageDetail(int $user_id, int $page_size = DEFAULT_PAGE_SIZE)
    {
        $from_user_id = Yii::$app->user->id;
        $msg_detail = AnMsg::getMessageDetail($user_id, $from_user_id, $page_size);
        // 设置私信的未读消息数为 0
        self::setReadNotice($user_id, ['msg' => 0]);
        return $msg_detail;
    }

    /**
     * @api {post} /message/remove-room 关闭私信聊天
     *
     * @apiPermission user
     *
     * @apiVersion 0.1.0
     * @apiName remove-room
     * @apiGroup message
     *
     * @apiParam {Number} user_id 对方用户 ID
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "info": "移除成功"
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     {
     *       "success": false,
     *       "code": 200020001,
     *       "info": "用户不存在"
     *     }
     */
    public function actionRemoveRoom()
    {
        $user_id = (int)Yii::$app->request->post('user_id');

        if ($user_id <= 0) {
            throw new HttpException(400, '参数错误');
        }
        $result = Yii::$app->serviceRpc->closePM(Yii::$app->user->id, $user_id);
        return $result['result'] ? '移除成功' : '移除失败';
    }

    /**
     * @api {get} /message/get-blacklist{?page,page_size} 黑名单列表
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/message/get-blacklist
     * @apiSampleRequest message/get-blacklist
     *
     * @apiVersion 0.1.0
     * @apiName get-blacklist
     * @apiGroup message
     *
     * @apiParam {Number} [page=1] 当前页
     * @apiParam {Number} [page_size=30]  每页个数
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [
     *           {
     *              "id": 14,
     *              "username": "贵族购买测试",
     *              "iconurl": "http://static.missevan.com/avatars/icon01.png",
     *              "authenticated": 0,
     *              "create_time": 1576392831
     *           }
     *         ],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 1,
     *           "count": 1,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     */
    public function actionGetBlacklist(int $page_size = DEFAULT_PAGE_SIZE)
    {
        $user_id = Yii::$app->user->id;
        return BlackUser::getBlacklist($user_id, $page_size);
    }

    /**
     * @api {post} /message/set-blacklist 加入或移除黑名单
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/message/set-blacklist
     * @apiSampleRequest message/set-blacklist
     *
     * @apiVersion 0.1.0
     * @apiName set-blacklist
     * @apiGroup message
     *
     * @apiParam {Number} user_id 被拉黑用户 ID
     * @apiParam {number=0,1} is_del 移除或加入黑名单 0：加入黑名单；1：移除黑名单
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "blacklist": 1,
     *         "message": "成功加入黑名单！"
     *       }
     *     }
     */
    public function actionSetBlacklist()
    {
        $user_id = Yii::$app->user->id;
        $blacklist_user_id = (int)Yii::$app->request->post('user_id');
        $is_del = (int)Yii::$app->request->post('is_del');
        BlackUser::checkSetBlacklist($blacklist_user_id, $user_id);
        BlackUser::blacklistOrNot($user_id, $blacklist_user_id, $is_del);
        return ['blacklist' => (int)!$is_del, 'message' => $is_del ? '移除黑名单成功' : '成功加入黑名单！'];
    }

    /**
     * @api {post} /message/send-message 发送私信(20171026-修改：绑定手机才能发私信-chenhao)
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/message/send-message
     * @apiSampleRequest message/send-message
     * @apiDescription  /mobile/personOperation/sendMessage 发送私信 参数userid和content和用户token
     *
     * @apiVersion 0.1.0
     * @apiName send-message
     * @apiGroup message
     *
     * @apiParam {Number} user_id 接收者用户 ID
     * @apiParam {Number} content 私信内容
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "type": 0,  // 私信文本类型，0：纯文本，1：HTML
     *         "content": "abc",
     *         "message_id": 233  // 私信 ID
     *       }
     *     }
     */
    public function actionSendMessage()
    {
        $to_user_id = (int)Yii::$app->request->post('user_id');
        $content = trim(Yii::$app->request->post('content'));

        // 验证信息是否为空
        if (!$content) {
            throw new HttpException(400, '私信不可为空', 201010001);
        }

        $from_user_id = Yii::$app->user->id;
        // 验证私信是否合法
        $isBigger = $from_user_id >= $to_user_id;
        $status = $isBigger ? 4 : 0;
        $status |= 1;
        $msg = new AnMsg(['scenario' => AnMsg::SCENARIO_ADD_MSG]);
        if ($isBigger) {
            $msg->small_id = $to_user_id;
            $msg->big_id = $from_user_id;
        } else {
            $msg->big_id = $to_user_id;
            $msg->small_id = $from_user_id;
        }
        $msg->status = $status;
        $msg->msg = $content;
        $msg->ctime = $_SERVER['REQUEST_TIME'];
        $msg->type = AnMsg::MSG_TYPE_TEXT;
        if (!$msg->validate()) {
            throw new HttpException(400, MUtils::getFirstError($msg));
        }
        // RPC 发送私信
        $pm = [
            'from_user_id' => $from_user_id,
            'to_user_id' => $to_user_id,
            'type' => $msg->type,
            'content' => $content,
            'role' => AnMsg::ROLE_NORMAL,
        ];
        $data = Yii::$app->tools->sendNotification($pm, Tools::SEND_PM);
        if (!$data) {
            throw new HttpException(500, '私信发送失败，请稍后再试');
        }
        $message = $data['info']['logs'][0];
        if ($message['type'] === Tools::PM_TYPE_FAIL) {
            throw new HttpException(403, $message['error']);
        }
        $content = $message['message'];
        $type = $message['message_type'];
        if ($message['message_type'] === AnMsg::MSG_TYPE_HTML) {
            [$content, $type] = AnMsg::formatMessageLink($content);
        }
        return [
            'type' => $type,
            'content' => $content,
            'message_id' => $message['message_id'],
        ];
    }

    /**
     * @api {post} /message/add 添加评论
     *
     * @apiVersion 0.1.0
     * @apiName add
     * @apiGroup message
     *
     * @apiParam {number=1,2,4,8,9} c_type=1 评论类型 1：单音；2：音单；4：频道；8：语音包；9：求签语音
     * @apiParam {Number} element_id 对象 ID
     * @apiParam {String} comment_content 评论内容
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 1294033,
     *         "comment_content": "剧集跳转链接 https://www.missevan.com/mdrama/77631，单集跳转链接 https://www.missevan.com/sound/player?id=9811365",
     *         "jump_url": {  // 评论内容中的跳转链接信息（仅有站内跳转链接时下发）
     *           "https://www.missevan.com/mdrama/77631": {  // 跳转链接地址（客户端需要将对应跳转链接替换为相应文案的可点击的跳转链接格式）
     *             "title": "十年对手，一朝占有",  // 跳转链接文案
     *             "prefix_icon": "http://static-test.maoercdn.com/test.jpg"  // 跳转链接前缀图标
     *           },
     *           "https://www.missevan.com/sound/player?id=9811365": {  // 跳转链接地址（客户端需要将对应跳转链接替换为相应文案的可点击的跳转链接格式）
     *             "title": "十年对手，一朝占有 . 第一集",  // 跳转链接文案
     *             "prefix_icon": "http://static-test.maoercdn.com/test.jpg"  // 跳转链接前缀图标
     *           }
     *         },
     *         "c_type": 1,
     *         "element_id": 3610,
     *         "ctime": 1614251172,
     *         "user": {
     *           "user_id": 9075893,  // 用户 ID
     *           "username": "加特林",  // 用户昵称
     *           "iconurl": "http://test.com/avatar.jpg",  // 用户头像
     *           "avatar_frame_url": "http://test.com/frame.webp",  // 用户头像框，没有时不下发
     *           "authenticated": 0  // 加 V 认证标识：0 没有 V，1 代表黑 V，2 代表金 V，3 代表蓝 V
     *         },
     *         "sub_comment_num": 0,
     *         "like_num": 0,
     *         "floor": 0,
     *         "ip_location": "山东"
     *       }
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     {
     *       "success": false,
     *       "code": 100010008,
     *       "info": "绑定手机就可以发送评论了哦"
     *     }
     */
    public function actionAdd()
    {
        $c_type = (int)Yii::$app->request->post('c_type');
        $element_id = (int)Yii::$app->request->post('element_id');
        $comment_content = trim(Yii::$app->request->post('comment_content'));

        $user_id = Yii::$app->user->id;
        // 设置满足条件的抽奖用户，仅限制音频评论
        if ($c_type === SoundComment::TYPE_SOUND) {
            CarnivalActivity::setMeetConditionsUser($user_id, $element_id,
                CarnivalActivity::ORIGIN_ADD_COMMENT, true);
        }

        $return = Yii::$app->go->addComment($user_id, $c_type, $element_id, $comment_content);
        $equip = Yii::$app->equip;
        if ($equip->isFromMiMiApp()
                || Equipment::isAppOlderThan('4.6.6', '5.6.1')) {
            // WORKAROUND: MiMi、iOS < 4.6.6 或 Android < 5.6.1 不返回新增的评论信息
            return '评论成功';
        }
        self::compatibleOlderThan604($return, $user_id);
        return $return;
    }

    /**
     * @api {post} /message/add-sub 添加子评论
     * @apiDescription 添加子评论
     *
     * @apiVersion 0.1.0
     * @apiName add-sub
     * @apiGroup message
     *
     * @apiParam {String} comment_content 评论内容
     * @apiParam {Number} comment_id 评论 ID
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "id": 1294033,
     *         "comment_content": "剧集跳转链接 https://www.missevan.com/mdrama/77631，单集跳转链接 https://www.missevan.com/sound/player?id=9811365",
     *         "jump_url": {  // 评论内容中的跳转链接信息（仅有站内跳转链接时下发）
     *           "https://www.missevan.com/mdrama/77631": {  // 跳转链接地址（客户端需要将对应跳转链接替换为相应文案的可点击的跳转链接格式）
     *             "title": "十年对手，一朝占有",  // 跳转链接文案
     *             "prefix_icon": "http://static-test.maoercdn.com/test.jpg"  // 跳转链接前缀图标
     *           },
     *           "https://www.missevan.com/sound/player?id=9811365": {  // 跳转链接地址（客户端需要将对应跳转链接替换为相应文案的可点击的跳转链接格式）
     *             "title": "十年对手，一朝占有 . 第一集",  // 跳转链接文案
     *             "prefix_icon": "http://static-test.maoercdn.com/test.jpg"  // 跳转链接前缀图标
     *           }
     *         },
     *         "c_type": 1,
     *         "element_id": 3610,
     *         "ctime": 1614251172,
     *         "user": {
     *           "user_id": 9075893,  // 用户 ID
     *           "username": "加特林",  // 用户昵称
     *           "iconurl": "http://test.com/avatar.jpg",  // 用户头像
     *           "avatar_frame_url": "http://test.com/frame.webp",  // 用户头像框，没有时不下发
     *           "authenticated": 0  // 加 V 认证标识：0 没有 V，1 代表黑 V，2 代表金 V，3 代表蓝 V
     *         },
     *         "sub_comment_num": 0,
     *         "like_num": 0,
     *         "floor": 0
     *       }
     *     }
     *
     * @apiErrorExample {json} Error-Response:
     *     {
     *       "success": false,
     *       "code": 100010008,
     *       "info": "绑定手机就可以发送评论了哦"
     *     }
     */
    public function actionAddSub()
    {
        $comment_content = trim(Yii::$app->request->post('comment_content'));
        $comment_id = (int)Yii::$app->request->post('comment_id');
        $user_id = Yii::$app->user->id;

        $return = Yii::$app->go->addSubComment($user_id, $comment_id, $comment_content);
        $equip = Yii::$app->equip;
        if ($equip->isFromMiMiApp()
                || Equipment::isAppOlderThan('4.6.6', '5.6.1')) {
            // WORKAROUND: MiMi、iOS < 4.6.6 或 Android < 5.6.1 不返回新增的评论信息
            return '发送成功';
        }
        self::compatibleOlderThan604($return, $user_id, true);
        return $return;
    }

    /**
     * 兼容添加评论和子评论老版本返回值
     *
     * @param array $return 需要兼容的返回数据
     * @param int $user_id 用户 ID
     * @param bool $is_sub_comment 是否是子评论
     */
    private static function compatibleOlderThan604(array &$return, int $user_id, bool $is_sub_comment = false)
    {
        if (!Equipment::isAppOlderThan('6.0.4', '6.0.4') || !isset($return['user'])) {
            // missevan-go rpc 没上线前 $return['user'] 不存在，需要判断 $return['user'] 是否存在，否则相关用户信息会被替换为默认值
            return;
        }
        // WORKAROUND: 兼容旧版本（安卓 < 6.0.4, iOS < 6.0.4）的数据格式
        if ($is_sub_comment) {
            // 子评论旧版本返回值为 userid
            $return['userid'] = $return['user']['user_id'] ?? $user_id;
        } else {
            // 父评论旧版本返回值为 user_id
            $return['user_id'] = $return['user']['user_id'] ?? $user_id;
        }
        $return['username'] = $return['user']['username'] ?? Mowangskuser::DELETED_USERNAME;
        $return['icon'] = $return['user']['iconurl'] ?? Yii::$app->params['defaultAvatarUrl'];
        $return['authenticated'] = $return['user']['authenticated'] ?? 0;
        unset($return['user']);
    }

    /**
     * @api {post} /mobile/message/del{?comment_id,sub} 删除评论
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/message/del?comment_id=66&sub=1
     * @apiSampleRequest message/del
     * @apiDescription 删除评论
     *
     * @apiVersion 0.1.0
     * @apiName del
     * @apiGroup message
     *
     * @apiParam (GET 参数) {Number} comment_id 评论或子评论 ID
     * @apiParam (GET 参数) {Number} [sub=0] 是否为子评论（1 为是，0 为否）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "删除成功"
     *     }
     *
     */
    public function actionDel(int $comment_id, int $sub = 0)
    {
        return SoundComment::del($comment_id, $sub);
    }

    /**
     * @api {post} /message/add-dm 添加弹幕
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/message/add-dm
     * @apiSampleRequest message/add_dm
     *
     * @apiVersion 0.1.0
     * @apiName add-dm
     * @apiGroup message
     *
     * @apiParam {Number} color 弹幕颜色（十进制）
     * @apiParam {Number} sound_id 音频 ID
     * @apiParam {Number} stime 发送时间（单位为秒）
     * @apiParam {String} text 发送内容
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {String} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "color": 16777215,
     *         "date": "1615199883",
     *         "id": 21093043,
     *         "mode": 1,
     *         "pool": 160,
     *         "size": 25,
     *         "sound_id": 65851,
     *         "stime", 0,
     *         "text": "测试数据",
     *         "user_id": 3457158
     *       }
     *     }
     */
    public function actionAddDm()
    {
        $sound_id = (int)Yii::$app->request->post('sound_id');
        $color = (int)Yii::$app->request->post('color');
        $text = trim(Yii::$app->request->post('text'));
        $stime = trim(Yii::$app->request->post('stime'));
        $user_id = Yii::$app->user->id;
        $size = MSoundComment::SIZE_NORMAL;
        $mode = MSoundComment::MODE_SLIDE;

        $dm = Yii::$app->go->addDm($user_id, $sound_id, $color, $size, $mode, $text, $stime);
        $equip = Yii::$app->equip;
        if ($equip->isFromMiMiApp() || Equipment::isAppOlderThan('4.6.6', '5.6.1')) {
            // WORKAROUND: MiMi、iOS < 4.6.6 或 Android < 5.6.1 时不返回新增弹幕信息
            return Yii::t('app/base', 'Send danmaku successfully');
        }
        return $dm;
    }

    /**
     * @api {post} /message/like{?comment_id,sub,action} 评论点赞
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/message/like?comment_id=66&sub=1&action=1
     * @apiSampleRequest message/like
     * @apiDescription 评论点赞
     *
     * @apiVersion 0.1.0
     * @apiName like
     * @apiGroup message
     *
     * @apiParam (GET 参数) {Number} comment_id 评论或子评论 ID
     * @apiParam (GET 参数) {number=0,1} [sub=0] 是否为子评论（1 为是，0 为否）
     * @apiParam (GET 参数) {number=0,1} [action=1] 行为，0：取消点赞；1：点赞
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "msg": "取消成功",
     *         "status": false
     *       }
     *     }
     *
     */
    public function actionLike(int $comment_id, int $sub = 0, int $action = self::ACTION_TYPE_ADD)
    {
        if ($comment_id <= 0 || !in_array($sub, [CommentLike::IS_NOT_SUB, CommentLike::IS_SUB])) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        $user_id = Yii::$app->user->id;
        // 是否为未传递 action 参数的老版本
        $is_old_version = Equipment::isAppOlderThan('4.5.4', '5.4.3');
        if ($is_old_version) {
            // WORKAROUND: 若为未传递 action 参数版本，则通过数据库数据判断当前行为是点赞还是取消点赞
            $action = CommentLike::find()
                ->where(['sub' => $sub, 'cid' => $comment_id, 'userid' => $user_id, 'type' => CommentLike::TYPE_LIKE])
                ->exists() ? self::ACTION_TYPE_CANCEL : self::ACTION_TYPE_ADD;
        }
        $like_status = CommentLike::commentLike($comment_id, $user_id, $sub, $action);
        $msg = Yii::t('app/base', $like_status ? 'Like successfully' : 'Cancel successfully');
        if ($is_old_version) {
            // WORKAROUND: 若为未传递 action 参数版本，状态字段名称为 like_status
            return [
                'msg' => $msg,
                'like_status' => $like_status,
            ];
        }
        return [
            'msg' => $msg,
            'status' => $like_status,
        ];
    }

    /**
     * @api {post} /message/dislike{?comment_id,sub,action} 评论点踩
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/message/dislike?comment_id=66&sub=1&action=1
     * @apiSampleRequest message/dislike
     * @apiDescription 评论点踩
     *
     * @apiVersion 0.1.0
     * @apiName dislike
     * @apiGroup message
     *
     * @apiParam (GET 参数) {Number} comment_id 评论或子评论 ID
     * @apiParam (GET 参数) {number=0,1} [sub=0] 是否为子评论（1 为是，0 为否）
     * @apiParam (GET 参数) {number=0,1} [action=1] 行为，0：取消点踩；1：点踩
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info 分类信息
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "msg": "取消成功",
     *         "status": false
     *       }
     *     }
     *
     */
    public function actionDislike(int $comment_id, int $action, int $sub = 0)
    {
        if ($comment_id <= 0 || !in_array($sub, [CommentLike::IS_NOT_SUB, CommentLike::IS_SUB])
                || !in_array($action, [self::ACTION_TYPE_CANCEL, self::ACTION_TYPE_ADD])) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        $user_id = Yii::$app->user->id;
        $like_status = CommentLike::commentDislike($comment_id, $user_id, $sub, $action);
        return [
            'msg' => Yii::t('app/base',
                $like_status ? 'Dislike successfully' : 'Cancel successfully'),
            'status' => $like_status,
        ];
    }

    /**
     * 更新评论中的用户信息为用户最新信息
     *
     * @param array $user_ids 用户 ID 组成的数组
     * @param array $comments 评论对象组成的数组
     * @param int $element_type 评论元素类型
     * @param int $element_id 评论元素 ID
     * @todo 之后应该在用户信息被修改时将对应的评论中相关用户信息同步更新
     */
    private static function updateCommentUserInfo(array $user_ids, array &$comments, int $element_type, int $element_id)
    {
        if ($user_ids) {
            $user_ids = array_values(array_unique($user_ids));
            $users = Mowangskuser::find()->where(['id' => $user_ids])
                ->select('id, username, iconurl, boardiconurl, icontype, avatar, confirm')->all();
            $users = array_column($users, NULL, 'id');

            if (Equipment::isAppOlderThan('6.0.4', '6.0.4')) {
                // WORKAROUND: 兼容旧版本（安卓 < 6.0.4, iOS < 6.0.4）的数据格式
                foreach ($comments as &$comment) {
                    $user = $users[$comment->userid] ?? null;
                    $comment->authenticated = $user ? $user->authenticated : 0;
                    $comment->icon = $user ? $user->iconurl : Yii::$app->params['defaultAvatarUrl'];
                    $comment->username = $user ? $user->username : Mowangskuser::DELETED_USERNAME;

                    if (isset($comment->subcomments)) {
                        foreach ($comment->subcomments as &$subcomment) {
                            $user = $users[$subcomment->userid] ?? null;
                            $subcomment->authenticated = $user ? $user->authenticated : 0;
                            $subcomment->icon = $user ? $user->iconurl : Yii::$app->params['defaultAvatarUrl'];
                            $subcomment->username = $user ? $user->username : Mowangskuser::DELETED_USERNAME;
                        }
                    }
                }
                return;
            }
            // 获取用户信息和用户头像框
            $user_avatar_frame_map = [];
            try {
                $user_avatar_frame_map = Yii::$app->serviceRpc->listAvatarFrame($user_ids,
                    ServiceRpc::LIST_AVATAR_FRAME_SCENE_COMMENT, $element_type, $element_id);
            } catch (Exception $e) {
                Yii::error("评论区获取用户头像框信息失败，element_type: {$element_type}, element_id: {$element_id}, error: "
                    . $e->getMessage(), __METHOD__);
                // PASS
            }
            $vip_user_ids = MUserVip::getVipUserIds($user_ids);

            foreach ($comments as &$comment) {
                $user = $users[$comment->userid] ?? null;
                $user_avatar_frame = $user_avatar_frame_map[$comment->userid]['avatar_frame_url'] ?? null;
                $comment->user = self::getCommentUserInfo($comment->userid, $user, $user_avatar_frame,
                    in_array($comment->userid, $vip_user_ids));
                // 删除未使用的字段
                unset($comment->userid, $comment->username);
                if (isset($comment->subcomments)) {
                    foreach ($comment->subcomments as &$subcomment) {
                        $user = $users[$subcomment->userid] ?? null;
                        $user_avatar_frame = $user_avatar_frame_map[$subcomment->userid]['avatar_frame_url'] ?? null;
                        $subcomment->user = self::getCommentUserInfo($subcomment->userid, $user, $user_avatar_frame,
                            in_array($subcomment->userid, $vip_user_ids));
                        // 删除未使用的字段
                        unset($subcomment->userid, $subcomment->username);
                    }
                }
            }
        }
    }

    /**
     * 获取评论用户信息
     *
     * @param int $user_id 用户 ID
     * @param Mowangskuser|null $user 用户信息
     * @param string|null $user_avatar_frame 用户头像框
     * @param boolean $is_vip 是否为 vip 用户
     * @return array
     */
    private static function getCommentUserInfo(int $user_id, ?Mowangskuser $user, ?string $user_avatar_frame, bool $is_vip)
    {
        $user_info = [
            'user_id' => $user_id,
            'authenticated' => $user ? $user->authenticated : 0,
            'iconurl' => $user ? $user->iconurl : Yii::$app->params['defaultAvatarUrl'],
            'username' => $user ? $user->username : Mowangskuser::DELETED_USERNAME,
            'is_vip' => (int)$is_vip,
        ];
        if ($user_avatar_frame) {
            // 没有头像框时不下发 avatar_frame_url 字段
            $user_info['avatar_frame_url'] = $user_avatar_frame;
        }
        return $user_info;
    }

    private function getPicOfCommentNotice(&$comments)
    {
        $sound_ids = $album_ids = $channel_ids = $topic_ids = $event_ids = $card_ids = [];
        $commentIds = $subcommentIds = $user_ids = [];

        foreach ($comments as $comment) {
            $user_ids[] = $comment->c_user_id;
            switch ($comment->type) {
                case SoundComment::TYPE_SOUND:
                    $sound_ids[] = $comment->eId;
                    break;
                case SoundComment::TYPE_ALBUM:
                    $album_ids[] = $comment->eId;
                    break;
                case SoundComment::TYPE_TAG:
                    $channel_ids[] = $comment->eId;
                    break;
                case SoundComment::TYPE_TOPIC:
                    $topic_ids[] = $comment->eId;
                    break;
                case SoundComment::TYPE_EVENT:
                    $event_ids[] = $comment->eId;
                    break;
                case SoundComment::TYPE_VOICE_CARD:
                case SoundComment::TYPE_OMIKUJI_CARD:
                    $card_ids[] = $comment->eId;
                    break;
            }
            if ($comment->sub) {
                $subcommentIds[] = $comment->comment_id;
            } else {
                $commentIds[] = $comment->comment_id;
            }
        }
        // 单音
        if ($sound_ids) {
            $sounds = MSound::find()->select('id, user_id, username, soundstr, cover_image')->where(['id' => $sound_ids])->all();
            if ($sounds) $sound_map = array_column($sounds, NULL, 'id');
            unset($sounds, $sound_ids);
        }

        // 音单
        if ($album_ids) {
            $albums = MAlbum::find()
                ->select('id, title, cover_image, user_id, username, refined')
                ->where(['id' => $album_ids])
                ->all();
            if ($albums) $album_map = array_column($albums, NULL, 'id');
            unset($albums, $album_ids);
        }

        // 频道
        if ($channel_ids) {
            $channels = MTag::find()->select('id, name, cover')->where(['id' => $channel_ids])->all();
            if ($channels) $channel_map = array_column($channels, NULL, 'id');
            unset($channels, $channel_ids);
        }

        // 活动
        if ($event_ids) {
            $events = MEvent::find()->select('id, title, mini_cover')->where(['id' => $event_ids])->all();
            if ($events) $event_map = array_column($events, NULL, 'id');
            unset($events, $event_ids);
        }

        // 专题
        if ($topic_ids) {
            $topics = Topic::find()->select('id, title, mobile_pic_url')->where(['id' => $topic_ids])->all();
            if ($topics) $topic_map = array_column($topics, NULL, 'id');
            unset($topics, $topic_ids);
        }
        // 语音卡
        if (!empty($card_ids)) {
            $cards = Card::find()
                ->alias('c')
                ->select('c.id, c.work_id, c.title, w.title AS work_title, w.icon AS work_icon')
                ->leftJoin(Work::tableName() . ' AS w', 'c.work_id = w.id')
                ->where(['c.id' => $card_ids])
                ->all();
            if ($cards) $cards_map = array_column($cards, NULL, 'id');
            unset($cards, $card_ids);
        }
        $mcomments = SoundCommentRO::find()->select('id, comment_content, userid, username, ctime')
            ->where(['id' => $commentIds])->all();
        if ($mcomments)
            $mcomment_map = array_column($mcomments, NULL, 'id');

        $msubcomments = SoundSubCommentRO::find()->select('id, comment_id, comment_content, userid, username, ctime')
            ->where(['id' => $subcommentIds])->all();
        if ($msubcomments)
            $msubcomment_map = array_column($msubcomments, NULL, 'id');

        if ($user_ids) {
            $user_ids = array_unique($user_ids);
            $users = Mowangskuser::findAll($user_ids);
            $users = array_column($users, NULL, 'id');
            unset($user_ids);
        }

        $delete = [];
        foreach ($comments as $k => &$comment) {
            switch ($comment->type) {
                case 1:
                    if (!isset($sound_map[$comment->eId])) {
                        // 若评论对象不存在（已被删除），则对象 ID 给 null 便于客户端判断评论对象是否已失效
                        $comment->eId = null;
                    } else {
                        $comment->front_cover = $sound_map[$comment->eId]->front_cover ?? '';
                        $comment->title = $sound_map[$comment->eId]->soundstr ?? '';
                    }
                    $comment->user_id = $sound_map[$comment->eId]->user_id ?? 0;
                    $comment->username = $sound_map[$comment->eId]->username ?? '';
                    break;
                case 2:
                    $user_id = Yii::$app->user->id;
                    if (!isset($album_map[$comment->eId]) || ($album_map[$comment->eId]->user_id === $user_id
                        && ($album_map[$comment->eId]->refined & MAlbum::REFINED_PRIVATE))) {
                        // 若评论对象不存在（已被删除）或是不属于查看用户的私有音单
                        // 则对象 ID 给 null 便于客户端判断评论对象是否已失效
                        $comment->eId = null;
                    } else {
                        $comment->front_cover = $album_map[$comment->eId]->front_cover ?? '';
                        $comment->title = $album_map[$comment->eId]->title ?? '';
                    }
                    $comment->user_id = $album_map[$comment->eId]->user_id ?? 0;
                    $comment->username = $album_map[$comment->eId]->username ?? '';
                    break;
                case 4:
                    if (!isset($channel_map[$comment->eId])) {
                        // 若评论对象不存在（已被删除），则对象 ID 给 null 便于客户端判断评论对象是否已失效
                        $comment->eId = null;
                    } else {
                        $comment->title = $channel_map[$comment->eId]->name ?? '';
                        $comment->front_cover = $channel_map[$comment->eId]->bigpic ?? '';
                    }
                    break;
                case 6:
                    if (!isset($topic_map[$comment->eId])) {
                        // 若评论对象不存在（已被删除），则对象 ID 给 null 便于客户端判断评论对象是否已失效
                        $comment->eId = null;
                    } else {
                        $comment->title = $topic_map[$comment->eId]->title ?? '';
                        $comment->front_cover = $topic_map[$comment->eId]->mobile_pic_url ?? '';
                    }
                    break;
                case 7:
                    if (!isset($event_map[$comment->eId])) {
                        // 若评论对象不存在（已被删除），则对象 ID 给 null 便于客户端判断评论对象是否已失效
                        $comment->eId = null;
                    } else {
                        $comment->title = $event_map[$comment->eId]->title ?? '';
                        $comment->front_cover = $event_map[$comment->eId]->mini_cover ?? '';
                    }
                    break;
                case SoundComment::TYPE_VOICE_CARD:
                case SoundComment::TYPE_OMIKUJI_CARD:
                    if (!isset($cards_map[$comment->eId])) {
                        // 若评论对象不存在（已被删除），则对象 ID 给 null 便于客户端判断评论对象是否已失效
                        $comment->eId = null;
                    } else {
                        $type_name = $comment->type === SoundComment::TYPE_VOICE_CARD ? '语音包' : '运势语音';
                        $card = $cards_map[$comment->eId];
                        $comment->title = "{$card->work_title}{$type_name} {$card->title}";
                        $comment->work_id = $card->work_id;
                        // 剑网 3 的白天黑夜图标与评论提醒的图标不同，进行区分
                        // (全职及求签的两者相同)
                        if (Work::ID_JIANWANG3 === $card->work_id) {
                            $comment->front_cover = StorageClient::getFileUrl(Work::ICON_COMMENT_NOTICE_JIANWANG3);
                        } else {
                            $comment->front_cover = StorageClient::getFileUrl($card->work_icon);
                        }
                    }
                    break;
            }
            if (isset($mcomment_map[$comment->comment_id]) || isset($msubcomment_map[$comment->comment_id])) {
                $comment->comment = $comment->sub ? $msubcomment_map[$comment->comment_id] : $mcomment_map[$comment->comment_id];
                if (isset($users[$comment->comment->userid])) {
                    $comment->comment->authenticated = $users[$comment->comment->userid]->authenticated ?? '';
                    $comment->comment->username = $users[$comment->comment->userid]->username;
                    $comment->comment->icon = $users[$comment->comment->userid]->iconurl;
                } else {
                    $comment->comment->authenticated = 0;
                    $comment->comment->username = Mowangskuser::DELETED_USERNAME;
                    $comment->comment->icon = Yii::$app->params['defaultAvatarUrl'];
                }
            } else {
                $delete[] = $k;
            }
        }
        if ($delete) {
            foreach ($delete as $d) {
                // 可能会导致空数组，外面调用该方法之后需要判断数组是否为空
                // 否则可能会有 Undefined offset 语法错误
                unset($comments[$d]);
            }
            $comments = array_values($comments);
        }
    }

    /**
     * @api {get} /message/get-likes 收到的赞
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/message/get-likes
     * @apiSampleRequest message/get-likes
     *
     * @apiVersion 0.1.0
     * @apiName get-likes
     * @apiGroup message
     *
     * @apiParam {Number} [page_size=20] 每页个数
     * @apiParam {Number} [page=1] 页码
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [{
     *           "type": 1,
     *           "eId": 493572,
     *           "sub": 0,
     *           "comment_id": 32445,
     *           "title": "1111111",
     *           "time": 1533545485,
     *           "content": "等1人赞了你的回复",
     *           "c_user_id": 346287,
     *           "isread": 0,
     *           "icon": "https://static.missevan.com/avatars/201803/26/c8ba0f80f0732.png",
     *           "username": "苍天啊红天",
     *           "authenticated": 0
     *         }, {
     *           "type": 1,
     *           "eId": 537093,
     *           "sub": 1,
     *           "comment_id": 4510,
     *           "title": "小伙伴们！最近的TV动画《独占我的英雄》你",
     *           "time": 1529981009,
     *           "content": "赞了这条评论",
     *           "c_user_id": 346287,
     *           "isread": 1,
     *           "icon": "https://static.missevan.com/avatars/201803/26/c8ba0f80fa02932.png",
     *           "username": "苍天啊红天",
     *           "authenticated": 0
     *         }],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 3,
     *           "count": 47,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     */
    public function actionGetLikes(int $page_size = 20)
    {
        $user_id = Yii::$app->user->id;
        $query = LikeNotice::find()
            ->select('type, eId, sub, comment_id, title, time, content, c_user_id, isread')
            ->where(['a_user_id' => $user_id])
            ->orderBy('isread, id DESC');

        $comment_notices = MUtils::getPaginationModels($query, $page_size);

        if (!empty($comment_notices->Datas)) {
            LikeNotice::getPicOfCommentNotice($comment_notices->Datas);
            // 未读的提醒须排在最前
            $first_notice = $comment_notices->Datas[0];
            if (LikeNotice::NOT_READ === $first_notice['isread']) {
                LikeNotice::updateAll(
                    ['isread' => LikeNotice::ALREADY_READ],
                    'a_user_id = :a_user_id AND isread = :unread',
                    [':a_user_id' => $user_id, ':unread' => LikeNotice::NOT_READ]
                );
            }
        }
        // 设置收到的赞未读消息数为 0
        self::setReadNotice($user_id, ['like' => 0]);
        return $comment_notices;
    }

    /**
     * @api {get} /message/get-notice-num 获取未读消息数量
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/message/get-notice-num
     * @apiSampleRequest /message/get-notice-num
     * @apiDescription 获取未读消息数量（评论，@我，赞，系统消息，反馈，动态，私信数量）\
     * live_last_time 关注主播最新开播时间，需要客户端本地判断新获取的这个值比之前获取的值大则显示红点
     * @apiVersion 0.1.0
     * @apiName get-notice-num
     * @apiGroup message
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "comment": 2, // 评论
     *         "at_me": 3, // @ 我
     *         "like": 5, // 赞
     *         "sys": 7, // 系统消息
     *         "feed": 0, // 动态
     *         "msg": 2, // 私信
     *         "live_last_time": 1566183833 // 最新开播时间
     *       }
     *     }
     */
    public function actionGetNoticeNum()
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }
        if (!Equipment::isAppOlderThan('4.3.6', '5.2.8')) {
            // 安卓版本大于 5.2.8，iOS 版本号大于 4.3.6 不再请求该接口
            throw new HttpException(403, '非法请求');
        }
        $return_num = self::noticeNum();
        return $return_num;
    }

    /**
     * 获取未读提醒
     *
     * @return array
     */
    public static function noticeNum()
    {
        $user_id = Yii::$app->user->id;
        $is_old_than_610 = Equipment::isAppOlderThan('6.1.0', '6.1.0');
        $is_new_than_622 = !Equipment::isAppOlderThan('6.2.2', '6.2.2');

        if (!$user_id) {
            // 非登录用户无需获取具体数据
            $return = [
                'comment' => 0,
                'at_me' => 0,
                'like' => 0,
                'sys' => 0,
                'msg' => 0,
                'special' => 0,
            ];
            if ($is_old_than_610) {
                // WORKAROUND: iOS < 6.1.0、Android < 6.1.0 获取 feed 流未读提醒和关注主播最新开播时间
                $return['feed'] = $return['live_last_time'] = 0;
            } else {
                // WORKAROUND: iOS ≥ 6.1.0、Android ≥ 6.1.0 获取我听图标上的未读提醒
                $return['feed_notice'] = null;
                if ($is_new_than_622 && Yii::$app->equip->isBeta(ENABLE_TAB_BAR_LIVE_RATIO)) {
                    // WORKAROUND: iOS ≥ 6.2.2、Android ≥ 6.2.2 【直播】Tab 灰度实验组获取直播图标上的未读提醒
                    $return['live_notice'] = null;
                }
            }
            return $return;
        }
        // 使用反向缓存，当对应的未读消息已读时，生成未读消息数为 0 的缓存
        // 对应的缓存不存在时，则重新获取未读消息数
        $redis = Yii::$app->redis;
        $key = self::getUnreadNoticeKey($user_id, $redis);
        $notices = $redis->hGetAll($key);
        foreach ($notices as $name => $value) {
            if (in_array($name, ['feed_notice', 'live_notice'])) {
                $notices[$name] = $value ? Json::decode($value) : null;
            } else {
                $notices[$name] = (int)$value;
            }
        }
        $update_notices = [];
        if (!array_key_exists('comment', $notices) || !array_key_exists('at_me', $notices)) {
            // 评论未读提醒或 at 未读提醒
            $comment_notices = Commentnotice::getUnreadNotice($user_id);
            $notices['at_me'] = $update_notices['at_me'] = $comment_notices['at'];
            $notices['comment'] = $update_notices['comment'] = $comment_notices['comment'];
        }
        if (!array_key_exists('like', $notices)) {
            // 点赞未读提醒
            $notices['like'] = $update_notices['like'] = LikeNotice::getUserLikeNoticeCount($user_id);
        }
        if (!array_key_exists('sys', $notices)) {
            // 系统通知未读提醒
            $notices['sys'] = $update_notices['sys'] = MMessageAssign::getUserSysNoticeCount($user_id);
        }
        if (!array_key_exists('msg', $notices)) {
            // 私信未读提醒
            $notices['msg'] = $update_notices['msg'] = AnMsg::getUnreadFollowedNum($user_id);
        }
        if (!array_key_exists('special', $notices)) {
            // 特殊系统消息未读提醒
            $notices['special'] = $update_notices['special'] = MMessageText::getUnreadSpecialMsgCount($user_id);
        }
        if ($is_old_than_610) {
            // WORKAROUND: iOS < 6.1.0、Android < 6.1.0 获取 feed 流未读提醒和关注主播最新开播时间
            if (!array_key_exists('feed', $notices)) {
                // feed 流未读提醒
                // 若用户在客户端“我的”页面点击注册完成后，将立刻请求未读信息接口获取相关数据
                // 此处可能因为 SSO 同步问题导致用户数据在数据表中不存在，此时反馈信息数返回 0
                $notices['feed'] = $update_notices['feed'] = (int)Mowangskuser::find()
                    ->select('feednum')
                    ->where('id = :id', [':id' => $user_id])
                    ->scalar();
            }
            if (!array_key_exists('live_last_time', $notices)) {
                // 关注主播最新开播时间
                $notices['live_last_time'] = $update_notices['live_last_time'] = Live::getLiveLastTime($user_id);
            }
        } elseif (!array_key_exists('feed_notice', $notices)) {
            // WORKAROUND: iOS ≥ 6.1.0、Android ≥ 6.1.0 获取我听图标上的未读提醒
            $feed_notice = self::getFeedNotice($user_id);
            $notices['feed_notice'] = $feed_notice;
            $update_notices['feed_notice'] = $feed_notice
                ? Json::encode($feed_notice)
                : '';
            if ($is_new_than_622
                    && ($is_beta = Yii::$app->equip->isBeta(ENABLE_TAB_BAR_LIVE_RATIO))
                    && !array_key_exists('live_notice', $notices)) {
                // WORKAROUND: iOS ≥ 6.2.2、Android ≥ 6.2.2 【直播】Tab 灰度实验组获取直播图标上的未读提醒
                $key = $redis->generateKey(KEY_USER_TAB_BAR_LIVE_LAST_REQUEST_TIME, $user_id);
                $start_time = (int)$redis->get($key);
                $live_notice = self::getLiveNotice($user_id, $start_time);
                $notices['live_notice'] = $live_notice;
                $update_notices['live_notice'] = $live_notice
                    ? Json::encode($live_notice)
                    : '';
            }
        }
        if ($update_notices) {
            // 设置未读消息数至缓存
            self::setReadNotice($user_id, $update_notices);
        }
        return $notices;
    }

    /**
     * 获取未读消息缓存 key（不同灰度实验使用不同的 key）
     *
     * @param int $user_id
     * @param $redis
     * @return string
     */
    private static function getUnreadNoticeKey(int $user_id, $redis): string
    {
        if (Equipment::isAppOlderThan('6.1.0', '6.1.0')) {
            $key = $redis->generateKey(KEY_USER_NOTICE, $user_id);
        } elseif (Equipment::isAppOlderThan('6.2.2', '6.2.2')) {
            // WORKAROUND: 6.1.0 ≤ iOS < 6.2.2、6.1.0 ≤ Android < 6.2.2 使用 v4 版本缓存
            $key = $redis->generateKey(KEY_USER_NOTICE_V4, $user_id);
        } elseif (Equipment::isAppOlderThan('6.2.4', '6.2.4')) {
            // WORKAROUND: 6.2.2 ≤ iOS < 6.2.4、6.2.2 ≤ Android < 6.2.4 使用 v3 版本缓存
            $key = MHomepageIcon::enableTabBarLive()
                // 实验组 - 直播 Tab 策略
                ? $redis->generateKey(KEY_USER_NOTICE_V3, $user_id)
                // 对照组 - 发现 Tab 策略
                : $redis->generateKey(KEY_USER_NOTICE_V3_OLD, $user_id);
        } else {
            // iOS ≥ 6.2.4、Android ≥ 6.2.4 使用 v5 版本缓存
            $key = MHomepageIcon::enableTabBarLive()
                // 实验组 - 直播 Tab 策略
                ? $redis->generateKey(KEY_USER_NOTICE_V5, $user_id)
                // 对照组 - 发现 Tab 策略
                : $redis->generateKey(KEY_USER_NOTICE_V5_OLD, $user_id);
        }
        return $key;
    }

    /**
     * 获取我听图标上的动态未读提醒
     *
     * @param int $user_id 用户 ID
     * @return array|null
     */
    private static function getFeedNotice(int $user_id): ?array
    {
        if ($user_id <= 0) {
            return null;
        }
        // 获取上次访问我听动态页时间（该缓存失效后，获取历史动态中最新的一条显示提醒，用户访问我听页后提醒正常消失即可）
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(KEY_USER_FEED_LAST_REQUEST_TIME, $user_id);
        $start_time = (int)$redis->get($key);
        // 动态提醒显示优先级：直播动态 > 追剧动态 > 投稿动态
        // 不是【直播】Tab 灰度实验组时，【我听】图标上展示直播动态提醒
        if (!MHomepageIcon::enableTabBarLive()) {
            // 获取直播动态提醒
            $live_feed_notice = self::getLiveNotice($user_id, $start_time);
            if ($live_feed_notice) {
                return [
                    'type' => self::ICON_NOTICE_TYPE_LIVE,
                    'id' => $live_feed_notice['id'],
                    'icon' => $live_feed_notice['icon'],
                ];
            }
        }
        // 获取追剧动态提醒
        $drama_feed_notice = null;
        try {
            $drama_feed_notice = Yii::$app->serviceRpc->getDramaFeedNotice($user_id, $start_time);
        } catch (Exception $e) {
            // 记录错误日志
            Yii::error(sprintf('获取用户（ID：%d）的追剧动态提醒失败：%s', $user_id, $e->getMessage()), __METHOD__);
            // PASS
        }
        $old_version = Equipment::isAppOlderThan('6.2.4', '6.2.4');
        if ($drama_feed_notice) {
            if ($old_version) {
                // WORKAROUND: iOS < 6.2.4、Android < 6.2.4 客户端【我听】图标上追剧提醒只展示红点
                return ['type' => 0];
            }
            // iOS >= 6.2.4、Android >= 6.2.4 客户端处理追剧提醒展示红点，返回结构用于记录埋点信息
            return [
                'type' => self::ICON_NOTICE_TYPE_DRAMA,
                'id' => $drama_feed_notice['id'],
            ];
        }
        // 获取投稿动态提醒
        $sound_feed_notice = MPersonHomePage::getFeedNotice($user_id, $start_time);
        if ($sound_feed_notice) {
            if ($old_version) {
                // WORKAROUND: iOS < 6.2.4、Android < 6.2.4 客户端【我听】图标上投稿动态提醒只展示红点
                return ['type' => 0];
            }
            // iOS >= 6.2.4、Android >= 6.2.4 客户端处理投稿动态提醒展示红点，返回结构用于记录埋点信息
            return [
                'type' => self::ICON_NOTICE_TYPE_SOUND,
                'id' => $sound_feed_notice,
            ];
        }
        return null;
    }

    /**
     * 获取底部导航栏【我听】【直播】图标上展示的直播动态提醒
     *
     * @param int $user_id 用户 ID
     * @param int $start_time 起始时间戳，单位：秒
     * @return array|null
     */
    private static function getLiveNotice(int $user_id, int $start_time): ?array
    {
        $live_feed_notice = null;
        try {
            $live_feed_notice = Yii::$app->liveRpc->getFeedNotice($user_id, $start_time);
        } catch (Exception $e) {
            // 记录错误日志
            Yii::error(sprintf('获取用户（ID：%d）的直播动态提醒失败：%s', $user_id, $e->getMessage()), __METHOD__);
            // PASS
        }
        if (!isset($live_feed_notice['room'])) {
            return null;
        }
        return [
            'id' => $live_feed_notice['room']['room_id'],
            'icon' => $live_feed_notice['room']['creator_iconurl'],
        ];
    }

    /**
     * @api {get} /message/get-at-notice at 提醒
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/message/get-at-notice
     * @apiSampleRequest /message/get-at-notice
     * @apiVersion 0.1.0
     * @apiName get-at-notice
     * @apiGroup message
     *
     * @apiParam {Number} [page_size=20] 每页个数
     * @apiParam {Number} [page=1] 第几页
     * @apiParam {number=0,1,2} [type=1] 类型：0 为所有，1 为未读，2 为已读
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [{
     *           "id": 17628477,
     *           "c_user_id": 3857777,
     *           "c_user_name": "chenhao123",
     *           "a_user_id": 346286,
     *           "a_user_name": "白夏",
     *           "type": 1,
     *           "eId": 548027,  // iOS < 4.6.6，Android < 5.5.5
     *           "element_id": 548027,  // iOS >= 4.6.6，Android >= 5.5.5
     *           "url": "",  // url 字段不为空说明点击需要打开这个 URL
     *           "title": "冒険の旅",
     *           "sub": 0,
     *           "comment_id": 14373082,
     *           "isread": 1,
     *           "front_cover": "http://static.missevan.com/coversmini/201806/29/a90ae20f1556870fb075229.png",
     *           "comment": {
     *             "id": 14373082,
     *             "comment_content": "哈哈哈哈",
     *             "userid": 3857777,
     *             "username": "chenhao123",
     *             "icon": "http://static.missevan.com/avatars/201911/13/89eff09caf7fde02ffdfcbf163815.jpg",
     *             "ctime": 1591941521,
     *             "authenticated": 0
     *           },
     *           "work_id": 614  // 消息提醒为语音包或运势语音时，返回作品 ID（其他情况不返回）
     *         }],
     *         "pagination": {
     *           "p": 1,
     *           "count": 12,
     *           "maxpage": 229,
     *           "pagesize": 20
     *         }
     *       }
     *     }
     */
    public function actionGetAtNotice(int $type = self::COMMENT_NOTICE_READ_TYPE_UNREAD, int $page_size = PAGE_SIZE_20)
    {
        $user_id = Yii::$app->user->id;
        $at_notices = $this->getCommentOrAtNotice(
            $user_id,
            Commentnotice::NOTICE_TYPE_AT,
            $type,
            $page_size
        );
        // 设置 at 未读消息提醒为 0
        self::setReadNotice($user_id, ['at_me' => 0]);
        if (Equipment::isAppOlderThan('4.6.6', '5.5.5')) {
            return $at_notices;
        }
        // WORKAROUND: iOS >= 4.6.6，Android >= 5.5.5，评论的对象 ID 字段修改为 element_id
        // 活动或专题下的评论返回对应的活动或专题链接
        $at_notices->Datas = array_map(function ($item) {
            $return = [
                'id' => $item->id,
                'c_user_id' => $item->c_user_id,
                'c_user_name' => $item->c_user_name,
                'a_user_id' => $item->a_user_id,
                'a_user_name' => $item->a_user_name,
                'type' => $item->type,
                // TODO: 之后会调整 app_missevan.commentnotice 表中的 eId 字段为 element_id
                'element_id' => $item->eId,
                'title' => $item->title,
                'sub' => $item->sub,
                'comment_id' => $item->comment_id,
                'isread' => $item->isread,
                'front_cover' => $item->front_cover,
                'comment' => $item->comment,
                'url' => '',
            ];
            // 活动或专题评论则返回对应的活动或专题链接
            switch ($return['type']) {
                case Commentnotice::COMMENT_NOTICE_TYPE_EVENT:
                    $return['url'] = Yii::$app->params['domainMissevan'] . '/mevent/' . $return['element_id'];
                    break;
                case Commentnotice::COMMENT_NOTICE_TYPE_TOPIC:
                    $return['url'] = Yii::$app->params['domainMissevan'] . '/mtopic/' . $return['element_id'];
                    break;
                case Commentnotice::COMMENT_NOTICE_TYPE_VOICE_CARD:
                case Commentnotice::COMMENT_NOTICE_TYPE_OMIKUJI_CARD:
                    // 消息提醒为语音包或运势语音时的作品 ID
                    $return['work_id'] = $item->work_id;
            }
            return $return;
        }, $at_notices->Datas);
        return $at_notices;
    }

    /**
     * 获取评论或 at 提醒
     *
     * @param int $user_id 用户 ID
     * @param int $notice_type 提醒类型（0 评论提醒、1 at 提醒）
     * @param int $read_type 获取的已读状态（0 所有、1 未读、2 已读）
     * @param int $page_size 每页个数
     * @return ReturnModel
     */
    private function getCommentOrAtNotice(int $user_id, int $notice_type, int $read_type, int $page_size)
    {
        $condition = ['a_user_id' => $user_id, 'notice_type' => $notice_type];
        $select = 'id, c_user_id, c_user_name, a_user_id' .
            ', a_user_name, type, eId, title, sub, comment_id, isread';
        $query = Commentnotice::find()
            ->select($select)
            ->where($condition);
        if (!Commentnotice::isNoticeCardComment()) {
            $query->andWhere(['NOT IN', 'type', [SoundComment::TYPE_VOICE_CARD, SoundComment::TYPE_OMIKUJI_CARD]]);
        }
        $current_page = (int)Yii::$app->request->get('page', 1);
        if (in_array($read_type, [self::COMMENT_NOTICE_READ_TYPE_UNREAD, self::COMMENT_NOTICE_READ_TYPE_ALREADY_READ])
            // 未读消息在第一页获取时会被转为已读，非第一页只获取已读消息（未读已读混合查询会慢），加速查询
            || $current_page > 1) {
            if ($read_type === self::COMMENT_NOTICE_READ_TYPE_UNREAD) {
                $query->andWhere(['isread' => Commentnotice::NOT_READ]);
            } else {
                $query->andWhere(['isread' => Commentnotice::ALREADY_READ]);
            }
            $query->orderBy('id DESC');
            $comment_notices = MUtils::getPaginationModels($query, $page_size);
        } else {
            // 第一页需要有未读+已读的消息列表，通过 union 加速查询
            $total_count = (int)$query->count();
            $page = new Pagination([
                'totalCount' => $total_count,
                'defaultPageSize' => $page_size,
            ]);
            $page->setPage($current_page - 1, true);
            // 分页条数最大值限制为 50（Yii 默认值）
            $page->setPageSize($page_size, true);
            $list = [];
            if ($total_count > 0) {
                $rows = new Query();
                // 全部未读在第一次查询后，会转为已读
                $not_read_sql = (clone $query)
                    ->andWhere(['isread' => Commentnotice::NOT_READ])
                    ->orderBy('id DESC')
                    ->offset($page->offset)
                    ->limit($page->limit);
                $already_read_sql = (clone $query)
                    ->andWhere(['isread' => Commentnotice::ALREADY_READ])
                    ->orderBy('id DESC')
                    ->offset($page->offset)
                    ->limit($page->limit);
                $sql = $rows->select($select)
                    ->from($not_read_sql->union($already_read_sql, true))
                    ->orderBy('isread ASC, id DESC')
                    ->limit($page->limit)->createCommand()->getRawSql();
                $list = Commentnotice::findBySql($sql)->all();
            }
            $comment_notices = ReturnModel::getPaginationData(
                $list, $page->totalCount, $page->getPage() + 1, $page->pageSize
            );
        }

        if (!empty($comment_notices->Datas)) {
            // 获取评论或 at 提醒内容和被评论或 at 的对象信息
            $this->getPicOfCommentNotice($comment_notices->Datas);
            // 未读的提醒须排在最前
            // 因为上面获取相关信息时可能会重新处理数组，所以需要重新判断是否为空
            if (!empty($comment_notices->Datas)) {
                $first_notice = $comment_notices->Datas[0];
                if (Commentnotice::NOT_READ === $first_notice['isread']) {
                    Commentnotice::updateAll(
                        ['isread' => Commentnotice::ALREADY_READ],
                        $condition + ['isread' => Commentnotice::NOT_READ]
                    );
                }
            }
        }
        return $comment_notices;
    }

    /**
     * @api {get} /message/get-sys-msg 系统消息（20171017）
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/message/get-sys-msg
     * @apiSampleRequest /message/get-sys-msg
     * @apiVersion 0.1.0
     * @apiName get-sys-msg
     * @apiGroup message
     *
     * @apiParam {Number} [page_size=20] 每页个数.
     * @apiParam {Number} [page=1] 第几页
     * @apiParam {number=0,1,2} [type=1] 类型（0 为所有，1 为未读，2 为已读）
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "Datas": [{
     *           "id": 4,
     *           "type": 1,
     *           "title": "hello4",
     *           "content": "887冰晶",
     *           "time": 1987654324,
     *           "isread": 0
     *         }, {
     *           "id": 3,
     *           "type": 1,
     *           "title": "hello3",
     *           "content": "系统消息来了品吕",
     *           "time": 1987654323,
     *           "isread": 0
     *         }],
     *         "pagination": {
     *           "p": 1,
     *           "maxpage": 11,
     *           "count": 22,
     *           "pagesize": 2
     *         }
     *       }
     *     }
     */
    public function actionGetSysMsg(int $type = 1, int $page_size = PAGE_SIZE_20, int $page = 1)
    {
        if (!in_array($type,
            [MMessageAssign::TYPE_GET_ALL, MMessageAssign::TYPE_GET_UNREAD, MMessageAssign::TYPE_GET_READ])) {
            throw new HttpException(400, '参数错误');
        }
        $pagination_params = PaginationParams::process($page, $page_size);
        $user_id = Yii::$app->user->id;
        $message_info = MMessageAssign::getSysMsg($user_id, $type, $pagination_params->page_size,
            $pagination_params->page);
        if ($message_info->Datas) {
            // 若存在通知，则更新所有未读通知的状态
            MMessageAssign::updateMsgStatus($user_id);
        }
        // 设置系统未读消息数，特殊未读消息数为 0
        self::setReadNotice($user_id, ['sys' => 0, 'special' => 0]);
        return $message_info;
    }

    /**
     * @api {post} /message/set-message-status 清空或已读私信
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com/message/set-message-status
     * @apiSampleRequest message/set-message-status
     *
     * @apiVersion 0.1.0
     * @apiName set-message-status
     * @apiGroup message
     *
     * @apiPermission user
     *
     * @apiParam {Number} type 操作类型 1：已读；2：清空
     *
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": "所有消息已标记为已读"
     *     }
     */
    public function actionSetMessageStatus()
    {
        $type = (int)Yii::$app->request->post('type');
        if (!in_array($type, [AnMsg::TYPE_SET_READ, AnMsg::TYPE_SET_CLEAN])) {
            throw new HttpException(400, '参数错误');
        }
        $user_id = Yii::$app->user->id;
        $info = '';
        switch ($type) {
            case AnMsg::TYPE_SET_READ:
                // 设置已读
                AnMsg::readUnfollowedMsg($user_id);
                $info = '所有消息已标记为已读';
                break;
            case AnMsg::TYPE_SET_CLEAN:
                // 设置清空
                AnMsg::cleanUnfollowedMsg($user_id);
                $info = '已清空列表';
                break;
            default:
                throw new HttpException(400, '参数错误');
        }
        return $info;
    }

    /**
     * 设置我的消息提醒，未读消息数的反向缓存
     *
     * @param int $user_id
     * @param array 需要存入缓存的数据
     */
    public static function setReadNotice(int $user_id, array $value)
    {
        if ($user_id && $value) {
            $redis = Yii::$app->redis;
            $key = self::getUnreadNoticeKey($user_id, $redis);
            if ($redis->ttl($key) > 0) {
                // 在有效时间内时，直接设置
                $redis->hMSet($key, $value);
            } else {
                $redis->pipeline()
                    ->multi()
                    ->hMSet($key, $value)
                    ->expire($key, ONE_MINUTE)
                    ->exec()
                    ->exec();  // 可参考 redis 管道使用方法 https://github.com/phpredis/phpredis/pull/931
            }
        }
    }

    /**
     * @api {post} /message/like-dm 弹幕点赞或取消点赞
     * @apiDescription 该接口拥有幂等性
     * @apiVersion 0.1.0
     * @apiGroup message
     *
     * @apiParam {Number} danmaku_id 弹幕 ID
     * @apiParam {number=1,2} element_type 弹幕所属元素类型，1：音频；2：互动剧节点
     * @apiParam {number=0,1} [action=1] 点赞或取消点赞（0：取消点赞；1：点赞）
     * @apiParamExample {json} Request-Example:
     *     {
     *       "danmaku_id": 1,
     *       "element_type": 1,
     *       "action": 1
     *     }
     *
     * @apiSuccess (200) {number} code CodeSuccess = 0.
     * @apiSuccess (200) {object} info
     * @apiSuccessExample Success-Response:
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "status": true  // 当前点赞状态，true 为已点赞，false 为未点赞
     *       }
     *     }
     */
    public function actionLikeDm()
    {
        $element_type = (int)Yii::$app->request->post('element_type');
        $danmaku_id = (int)Yii::$app->request->post('danmaku_id');
        $action = (int)Yii::$app->request->post('action', self::ACTION_TYPE_ADD);
        if ($danmaku_id <= 0 || !in_array($action, [self::ACTION_TYPE_ADD, self::ACTION_TYPE_CANCEL])
                || !in_array($element_type, [MUserLikeDanmaku::ELEMENT_TYPE_SOUND, MUserLikeDanmaku::ELEMENT_TYPE_NODE])) {
            throw new HttpException(400, '参数错误');
        }
        return Yii::$app->go->likeDm($element_type, $danmaku_id, Yii::$app->user->id, $action);
    }
}
