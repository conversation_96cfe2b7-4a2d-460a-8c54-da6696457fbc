<?php

namespace app\controllers;

use app\components\util\Equipment;
use app\components\util\MUtils;
use app\models\MAppupdate;
use app\models\MHomepageIcon;
use missevan\storage\StorageClient;
use yii\helpers\Json;
use yii\web\Controller;
use yii\web\HttpException;
use yii\web\Response;
use Yii;

class CiController extends Controller
{
    /**
     * @api {get} /ci/build-params{?client,channel} 构建参数
     * @apiExample {curl} Example usage:
     *     curl -i http://test.com:8081/ci/build-params?client=1
     * @apiSampleRequest /ci/build-params
     *
     * @apiVersion 0.1.0
     * @apiName build-params
     * @apiGroup ci
     *
     * @apiParam {number=1,2,6} client App 操作系统 1：Android；2：iOS；6：HarmonyOS
     * @apiParam {String} [channel] 渠道信息，仅 Android 客户端需要传递，接口是给打包平台那边用的，请求头里没有 channel，需要 query 参数传
     *
     * @apiSuccess {Boolean} success true or false.
     * @apiSuccess {Number} code
     * @apiSuccess {Object} info
     *
     * @apiSuccessExample {json} Success-Response:
     *     HTTP/1.1 200 OK
     *     {
     *       "success": true,
     *       "code": 0,
     *       "info": {
     *         "config": {
     *           "omikuji_share_text": "{\"2\":{\"平\":\"#撒野# 1111\",\"末吉\":\"#撒野# 1111\"}"
     *         },
     *         "search_config": {
     *           "search_catalogs": [
     *             {
     *               "id": 0,
     *               "name": "全部分区"
     *             },
     *             {
     *               "id": 1,
     *               "name": "有声漫画"
     *             },
     *             {
     *               "id": 2,
     *               "name": "广播剧"
     *             },
     *             {
     *               "id": 3,
     *               "name": "音乐"
     *             },
     *             {
     *               "id": 4,
     *               "name": "声音恋人"
     *             },
     *             {
     *               "id": 5,
     *               "name": "电台"
     *             },
     *             {
     *               "id": 6,
     *               "name": "日抓"
     *             },
     *             {
     *               "id": 7,
     *               "name": "听书"
     *             },
     *             {
     *               "id": 8,
     *               "name": "铃声"
     *             },
     *             {
     *               "id": 9,
     *               "name": "放松"
     *             }
     *           ]
     *         },
     *         "privacy": {
     *           "data": {
     *             "title": "猫耳FM隐私政策",
     *             "title_for_update": "隐私政策更新提示",
     *             "content": "感谢您信任并使用猫耳FM的产品和服务。在您使用猫耳FM产品或服务前，请您认真阅读并充分理解猫耳FM隐私政策。您点击“同意”，即表示您已阅读并同意上述条款，猫耳FM将尽全力保障您的合法权益并继续为您提供优质的产品和服务。如您点击“不同意”，将可能导致无法继续使用我们的产品和服务。",
     *             "content_for_update": "感谢您信任并使用猫耳FM的产品和服务。我们依据最新的法律法规、监管政策要求，更新了《猫耳FM隐私政策》部分条款，特向您推送本提示。\n本次我们更新的条款主要包括：修改运营主体注册与联系地址；增加用户使用我们的产品与服务需要的设备权限、信息收集；增加《猫耳FM第三方 SDK 目录》。请您在使用 / 继续使用我们的产品与 / 或服务前仔细阅读并充分理解更新后的隐私政策。\n您点击“同意”，即表示您已阅读并同意上述条款，猫耳FM将尽全力保障您的合法权益并继续为您提供优质的产品和服务。如您点击“不同意”，将可能导致无法继续使用我们的产品和服务。",
     *             "content_for_decline": "猫耳FM将严格按照《猫耳FM隐私政策》向您提供服务。如您不同意本隐私政策，您可以点击“不同意”后退出应用。",
     *             "url": "https://link.missevan.com/rule/privacy",
     *             "modified_time": 1611676800
     *           },
     *           "last_data_url": "https://static.missevan.com/app/privacy.json"
     *         },
     *         "season": 3,  // 1：春季；2：夏季；3：秋季；4：冬季
     *         "emote_package_url": "http://static.missevan.com/emote/emote.zip",
     *         "person_icons": [
     *           {
     *             "id": 1,
     *             "title": "启动音",
     *             "icon": "http://static.maoercdn.com/mimages/202004/23/2bd0d87e8bb960046b71f12aa534a673171552.png",
     *             "dark_icon": "http://static.maoercdn.com/mimages/202004/23/49005fc6066a7e25f1a800416b814fe3171556.png",
     *             "url": "missevan://powersound",
     *             "name": "powersound"
     *           },
     *           {
     *             "id": 2,
     *             "title": "闹钟",
     *             "icon": "http://static.maoercdn.com/homepage/icons/202303/29/0dbffafc5d0a82dce879090a03fa60a3140839.png",
     *             "dark_icon": "http://static.maoercdn.com/homepage/icons/202303/29/ec52d98740749d91b0486539afcfda61140839.png",
     *             "url": "missevan://alarm"
     *           }
     *         ]
     *       }
     *     }
     */
    public function actionBuildParams(int $client = Equipment::Android, string $channel = '')
    {
        if (!in_array($client, [Equipment::Android, Equipment::iOS, Equipment::HarmonyOS])) {
            throw new HttpException(400, '参数错误');
        }
        $redis = Yii::$app->redis;
        if ($client === Equipment::iOS) {
            $config_key = $redis->generateKey(KEY_CONFIG_CLIENT, Equipment::iOS);
        } else {
            // HarmonyOS 暂时先用 Android 的打包配置
            $config_key = $redis->generateKey(KEY_CONFIG_CLIENT, Equipment::Android);
        }
        $config_info = $redis->hGetAll($config_key);
        foreach ($config_info as $key => $value) {
            // 判断是否是 @ 开头的动态配置
            if (substr($value, 0, 1) === '@') {
                // @ 开头的动态配置, 构建时不下发
                unset($config_info[$key]);
                continue;
            }
            // 判断是否为谷歌渠道下的 httpdns 配置。若数据库中没有配置 httpdns，则不设置 provider 字段
            if ($key === 'httpdns' && $channel === 'missevan_google') {
                $data = Json::decode($value);
                // provider 为 local 时客户端关闭 httpdns；若不下发 provider 默认使用阿里云 DNS
                $data = array_merge($data, ['provider' => 'local']);
                $config_info[$key] = Json::encode($data);
            }
        }
        $search_config = DiscoveryController::getSearchConfig();
        $data = null;
        $privacy_data = MAppupdate::getPrivacyDataUrl($channel);
        if ($privacy_data) {
            $json_string = file_get_contents($privacy_data);
            $data = Json::decode($json_string)['info'] ?: null;
        }
        $info = [
            'config' => $config_info ?: new \stdClass(),
            'search_config' => $search_config ?: new \stdClass(),
            'privacy' => [
                'data' => $data,
                'last_data_url' => $privacy_data,
            ],
            'season' => MUtils::getSeason(),
            'emote_package_url' => StorageClient::getFileUrl(Yii::$app->params['emote']['package_url']),
            'person_icons' => MHomepageIcon::getIcons(),
        ];
        Yii::$app->response->format = Response::FORMAT_JSON;
        return [
            'success' => true,
            'code' => 0,
            'info' => $info
        ];
    }
}
