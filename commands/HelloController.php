<?php
/**
 * @link http://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 */

namespace app\commands;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use yii\console\Controller;

/**
 * This command echoes the first argument that you have entered.
 *
 * This command is provided as an example for you to learn how to create console commands.
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2.0
 */
class HelloController extends Controller
{
    /**
     * This command echoes what you have entered as the message.
     * @param string $message the message to be echoed.
     */
    public function actionIndex($message = 'hello world')
    {
        $url = 'https://api.bilibili.com/x/relation/followers?vmid=423895&ps=100&order=desc&jsonp=jsonp&pn=';
        $n = 0;
        $p = 1;
        $client = new Client();
        while ($n < 1000000) {
            try {
                $res = $client->request('GET', $url . $p);

            } catch (ClientException $e) {
                $res = $e->getResponse();
            }
            $body = $res->getBody();
            $content = $body->getContents();

            $data = json_decode($content, true)['data']['list'] ?? [];

            foreach ($data as $d) {
                if (($d['face'] && $d['uname']) && ($d['face'] !== "http://static.hdslb.com/images/member/noface.gif")) {
                    $name = './img/' . base64_encode($d['uname']) . '.png';
                    if (@file_put_contents($name, fopen($d['face'], 'r')))
                        $n += 1;
                }
            }
            echo 'num:' . $n . 'page:' . $p;
            echo '<br/>';
            $p += 1;
        }
        echo 'ok';
    }
}
