<?php

namespace app\commands;

use yii\console\Controller;
use app\models\MSound;
use app\components\tree\Catalogs;

class TopUpsController extends Controller
{
    public function actionIndex()
    {
        $fp = fopen("./top-ups.csv", 'w');
        fputcsv($fp, ['分类名', 'up主id', 'up主昵称', '单音id', '单音播放量']);
        // 获取一级分类及其下的子分类
        $catalogs = (new Catalogs)->getTree()['sons'];

        foreach ($catalogs as $catalog) {
            // 获取大类下的各子类id
            $sub_ids = array_column($catalog['sons'], 'id');
            // 查询子类中的单音播放量前20的up信息
            $top_ups = MSound::find()->select('user_id, username, id sound_id, view_count')
                ->where(['catalog_id' => $sub_ids])
                ->orderBy(['view_count' => SORT_DESC])->limit(20)->asArray()->all();
            // 写入csv文件
            foreach ($top_ups as $line) {
                array_unshift($line, $catalog['name']);
                fputcsv($fp, $line);
            }
        }
        fclose($fp);
    }
}