<?php
/**
 * Created by PhpStorm.
 * User: TomCao
 * Date: 2018/5/23
 * Time: 下午2:41
 */

namespace app\commands;

use app\components\util\MUtils;
use app\models\Card;
use app\models\CardPackage;
use app\models\GetCard;
use app\models\GetCardLog;
use app\models\LotteryPackage;
use app\models\Work;
use \Yii;
use yii\console\Controller;
use yii\console\ExitCode;
use yii\db\Expression;

class VoiceController extends Controller
{
    public function actionPush()
    {
        $time = time();
        $work_ids = Work::find()->select('id')->where(['type' => Work::TYPE_VOICE])->column();
        $work_ids = array_map('intval', $work_ids);
        $sql = <<<SQL
UPDATE work_user_info t1,
    (SELECT user_id, COUNT(*) count
        FROM get_card
        WHERE appear_time <= :time_now AND status = :status_lock AND work_id = :work_id GROUP BY user_id
    ) t2
SET t1.notice = t1.notice + t2.count
WHERE t1.id = t2.user_id AND t1.work_id = :work_id;
UPDATE get_card
SET status = :status_unlock
WHERE appear_time <= :time_now AND status = :status_lock AND work_id = :work_id
SQL;
        foreach ($work_ids as $work_id) {
            $result = Yii::$app->db2->createCommand($sql, [
                ':work_id' => $work_id,
                ':time_now' => $time,
                ':status_lock' => GetCard::STATUS_LOCK,
                ':status_unlock' => GetCard::STATUS_UNLOCK,
            ])->execute();
            $this->stdout("作品（{$work_id}）语音卡片推送成功，共推送了 {$result} 个用户");
        }
        return ExitCode::OK;
    }

    // 修复 get_card 及 get_card_log 表中的 card_package_id
    public function actionFixPackageId()
    {
        $cards = Card::find()->select('id, card_package_id')->where(['special' => Card::SPECIAL_NORMAL])->all();
        $cards_group = MUtils::groupArray($cards, 'card_package_id', 'id');
        foreach ($cards_group as $card_package_id => $card_ids) {
            GetCard::updateAll(['card_package_id' => $card_package_id], [
                'special' => Card::SPECIAL_NORMAL,
                'card_package_id' => 0,
                'card_id' => $card_ids,
            ]);
            GetCardLog::updateAll(['card_package_id' => $card_package_id], [
                'special' => Card::SPECIAL_NORMAL,
                'card_package_id' => 0,
                'method_of_obtaining' => [GetCardLog::TYPE_UNLOCK, GetCardLog::TYPE_EXCHANGE, GetCardLog::TYPE_DRAW],
                'card_id' => $card_ids,
            ]);
        }
        return ExitCode::OK;
    }

    // 对通过非购买方式（兑换或抽卡）集齐的用户赠送节日卡
    public function actionGetGift()
    {
        $now = $_SERVER['REQUEST_TIME'];
        $NORMAL_CARD = Card::SPECIAL_NORMAL;
        $FESTIVAL_CARD = Card::SPECIAL_FESTIVAL;
        $NORMAL_CARD_COUNT = CardPackage::COMMON_CARD_COUNT;
        $METHOD_OF_OBTAINING = 0;
        $COUPON = 0;
        $UNREAD = GetCard::STATUS_UNLOCK;
        $INACTIVE = GetCard::STATUS_LOCK;
        $INTERVAL = 0;

        $sql_log = <<<SQL
INSERT INTO app_missevan_voice.get_card_log(create_time, modified_time, user_id, card_id, work_id, role_id, special,
 level, method_of_obtaining, card_package_id, coupon) SELECT {$now}, {$now}, t2.user_id, t1.id,
 t1.work_id, t1.role_id, t1.special, t1.`level`, {$METHOD_OF_OBTAINING}, t1.card_package_id, {$COUPON}
 FROM card AS t1,
 (SELECT `user_id`, `role_id`, COUNT(card_id) AS card_count FROM `get_card`
  WHERE `special` IN ({$NORMAL_CARD}, {$FESTIVAL_CARD})
  GROUP BY `user_id`, `role_id` HAVING card_count = {$NORMAL_CARD_COUNT}) AS t2
  WHERE t1.role_id = t2.role_id AND t1.special = {$FESTIVAL_CARD}; 
SQL;
        $sql_card = <<<SQL
INSERT INTO app_missevan_voice.get_card(create_time, modified_time, appear_time, user_id, card_id, work_id, role_id,
 card_package_id, `interval`, level, status, special) SELECT {$now}, {$now}, t1.push, t2.user_id,
 t1.id, t1.work_id, t1.role_id, t1.card_package_id, {$INTERVAL}, t1.`level`,
 IF(t1.push > {$now}, {$INACTIVE}, {$UNREAD}), t1.special
 FROM card AS t1,
 (SELECT `user_id`, `role_id`, COUNT(card_id) AS card_count FROM `get_card`
  WHERE `special` IN ({$NORMAL_CARD}, {$FESTIVAL_CARD})
  GROUP BY `user_id`, `role_id` HAVING card_count = {$NORMAL_CARD_COUNT}) AS t2
  WHERE t1.role_id = t2.role_id AND t1.special = {$FESTIVAL_CARD}; 
SQL;
        $row_num_log = Yii::$app->db2->createCommand($sql_log)->execute();
        $row_num_card = Yii::$app->db2->createCommand($sql_card)->execute();
        echo "Success: {$row_num_log}, {$row_num_card} rows were inserted into get_card_log, get_card respectively.";
        return ExitCode::OK;
    }

    /**
     * 更改抽卡卡池键值名
     * 原卡池键名为“set:package_key:*”（* 为卡片等级），现因为不同作品不同季度的卡池要区别开，所以键名需要重新定义
     * 新键名将定义成 “voice:package:work_id:*:season:*:level:*”，其中 * 分别代表作品 ID，季度和卡片等级
     */
    public function actionUpdatePackageCacheName()
    {
        $redis = Yii::$app->redis;
        // 原有全职高手第一季抽卡 4 个等级的卡池
        $levels = [1, 2, 3, 4];
        foreach ($levels as $level) {
            // 更新原全职高手第一季抽卡卡池键名
            $OLD_KEY = 'set:package_key:*';
            $old_name = $redis->generateKey($OLD_KEY, $level);
            $new_name = $redis->generateKey(KEY_VOICE_PACKAGE_WORK_ID_SEASON_LEVEL, 1, 1, $level);
            $redis->rename($old_name, $new_name);
            // 更新数据表中保存的键名
            $expression_update = new Expression("REPLACE(rule, '{$old_name}', '{$new_name}')");
            LotteryPackage::updateAll(['rule' => $expression_update]);
        }
        $this->stdout('更新成功');
        return ExitCode::OK;
    }
}
