<?php
/**
 * Created by PhpStorm.
 * User: jiepe
 * Date: 2017/9/5
 * Time: 20:20
 */

namespace app\commands;

use app\models\Mowangskuser;
use app\models\MSound;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use Yii;
use yii\console\Controller;
use yii\helpers\Json;

class LiveController extends Controller
{
    /**
     * @var \GuzzleHttp\Client
     */
    private static $CLIENT = false;
    private static $sleep = 1;

    private $video_dir = './tmp/videos/';
    private $mp3_dir = './tmp/mp3/';
    private $img_dir = './tmp/imgs/';

    public function init()
    {
        if (self::$CLIENT === false) {
            $client = new Client([
                'base_uri' => FM_DOMAIN,
                'timeout' => 15.0,
            ]);
            self::$CLIENT = $client;
        }

        // 创建文件夹
        $this->make_dir($this->video_dir);
        $this->make_dir($this->mp3_dir);
        $this->make_dir($this->img_dir);

        self::$sleep = 1;
    }

    public function actionToMp3()
    {
        $redis = Yii::$app->redis;
        $storage = Yii::$app->storage;
        $archive_storage = Yii::$app->archiveStorage;

        $ffmpeg = FFMPEG;

        while (true) {
            $params = $redis->rPop('list:live-videos');
            if ($params) {
                $params = Json::decode($params);
                // 视频地址
                $remote_path = trim($params['record'], " \t\n\r \v/");

                // 图片地址
                $cover_img = trim($params['cover'] ?? '', " \t\n\r \v/");

                $file_info = pathinfo($remote_path);
                $file_name = $file_info['filename'];
                $new_file_name = md5($file_name) . date('His');

                $download_path = $this->video_dir . $new_file_name . '.flv';
                $mp3_path = $this->mp3_dir . $new_file_name . '.mp3';


                $archive_storage->download($remote_path, $download_path);
                echo `$ffmpeg -v quiet -i $download_path $mp3_path`;

                $sound_url = date('Ym/d/') . $new_file_name . '.mp3';
                $sound_all_path = 'sound/' . $sound_url;

                $storage->upload($mp3_path, $sound_all_path, true);

                $user_id = $params['user_id'];
                $user_name = $params['username'] ?? Mowangskuser::findOne($user_id)->username; //$params['user_name'];
                $soundstr = $params['title'];
                $intro = $params['description'];

                $new_sound = new MSound();
                $new_sound->user_id = $user_id;
                $new_sound->username = $user_name;
                $new_sound->catalog_id = 80;
                $new_sound->soundstr = $soundstr;
                $new_sound->intro = $intro;
                $new_sound->download = 0;
                $new_sound->soundurl = $sound_url;


                if ($cover_img) {
                    $img_path = $this->img_dir . md5($cover_img . time()) . '.jpg';
                    $storage->download($cover_img, $img_path);
                    $new_sound->uploadCoverImage(7, $img_path, 'jpg');
                }
                $new_sound->save();

                $playback_id = $params['playback_id'];
                $data = [
                    'type' => 'playback',
                    'event' => 'updated',
                    'playback' => [
                        'playback_id' => $playback_id,
                        'sound_id' => $new_sound->id,
                    ]
                ];
                $url = '/api/channel/playback/notify';
                $this->_request($url, $data);
                echo $new_sound->id . "\n";
                self::$sleep = 1;
            } else {
                sleep(self::$sleep);
                if (self::$sleep <= TEN_MINUTE) {
                    self::$sleep *= 2;
                } else {
                    $this->clearDir($this->video_dir);
                    $this->clearDir($this->mp3_dir);
                    $this->clearDir($this->img_dir);
                }
                echo self::$sleep . "\n";
            }
        }
    }

    private function make_dir($dir)
    {
        if (!is_dir($dir)) {
            // PHP 有个默认的 umask 是 0022，文件最终的权限为 0777 &~ umask = 0755
            // 将 umask 设置为 0，文件最终的权限为 0777
            $mask = umask(0);
            mkdir($dir, 0777, true);
            umask($mask);
        }
    }

    private function _request($url, $data = [])
    {
        $data = base64_encode(json_encode($data));
        $timestamp = time();
        $sign = hash_hmac('sha256', "$data $timestamp", MISSEVAN_PRIVATE_KEY);
        $body = "$data $sign $timestamp";

        try {
            $res = self::$CLIENT->request('POST', $url, [
                'headers' => [
                    'Content-Type' => 'text/plain',
                ],
                'body' => $body,
            ]);
        } catch (ClientException $e) {
            $res = $e->getResponse();
        }
        $body = $res->getBody();
        $content = $body->getContents();
        return json_decode($content, true);
    }

    private function clearDir($dir)
    {
        $dh = opendir($dir);
        while ($file = readdir($dh)) {
            if ('.' !== $file && '..' !== $file) {
                $full_path = $dir . '/' . $file;
                if (is_dir($full_path)) {
                    clearDir($full_path);
                } else {
                    unlink($full_path);
                }
            }
        }
        closedir($dh);
    }
}
