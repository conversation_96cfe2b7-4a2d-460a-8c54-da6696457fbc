{"name": "miaosila/missevan-app", "description": "the app project of MissEvan", "keywords": ["yii2", "<PERSON><PERSON><PERSON>"], "homepage": "http://www.missevan.com/", "type": "project", "version": "2.5.0", "time": "2025-06-24", "license": "BSD-3-<PERSON><PERSON>", "support": {"issues": "https://github.com/yiisoft/yii2/issues?state=open", "forum": "http://www.yiiframework.com/forum/", "wiki": "http://www.yiiframework.com/wiki/", "irc": "irc://irc.freenode.net/yii", "source": "https://github.com/yiisoft/yii2"}, "minimum-stability": "stable", "require": {"php": ">=7.2.0", "yiisoft/yii2": "2.0.38", "yiisoft/yii2-bootstrap": "~2.0.0", "aliyuncs/oss-sdk-php": "^2.6", "aws/aws-sdk-php": "^3.199", "guzzlehttp/guzzle": "^7.2", "miaosila/php-utils": "dev-master", "index0h/yii2-log": "^0.0.3", "pear/crypt_diffiehellman": "^0.2.6", "google/apiclient": "^2.15", "firebase/php-jwt": "^6.9", "wechatpay/wechatpay": "^1.4", "ext-bcmath": "*", "ext-ctype": "*", "ext-curl": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-intl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-mysqli": "*", "ext-openssl": "*", "ext-pdo_mysql": "*", "ext-Phar": "*", "ext-SimpleXML": "*", "ext-tokenizer": "*", "ext-xml": "*", "ext-xmlwriter": "*", "ext-imagick": "*", "ext-redis": "*", "ext-zend-opcache": "*", "tuupola/base62": "^2.1"}, "require-dev": {"yiisoft/yii2-debug": "*", "yiisoft/yii2-gii": "*", "yiisoft/yii2-faker": "*", "codeception/verify": "^1.0", "codeception/specify": "^1.1", "codeception/assert-throws": "^1.0", "codeception/robo-paracept": "dev-master", "codeception/codeception": "^4.1.22", "codeception/module-asserts": "^1.0", "codeception/module-yii2": "^1.1", "codeception/module-filesystem": "2.0.1"}, "repositories": {"miaosila/php-utils": {"type": "vcs", "url": "**************:miaosila/php-utils.git"}}, "config": {"process-timeout": 1800, "fxp-asset": {"installer-paths": {"npm-asset-library": "vendor/npm", "bower-asset-library": "vendor/bower"}}}, "scripts": {"post-create-project-cmd": ["yii\\composer\\Installer::postCreateProject"], "test": "codecept run"}, "extra": {"yii\\composer\\Installer::postCreateProject": {"setPermission": [{"runtime": "0777", "web/assets": "0777", "yii": "0755"}], "generateCookieValidationKey": ["config/web.php"]}}}