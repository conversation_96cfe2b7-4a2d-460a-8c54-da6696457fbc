<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/14
 * Time: 11:27
 */

// CACHE
// Homepage
// App 顶部编辑推荐部分，包括自定义图标
define('APP_HOMEPAGE_KEY', 'app_home_page_paths');

// COUNTER
// 登录次数记录
define('COUNTER_LOGIN', 'counter:account:*');
define('COUNTER_VCODE_EID', 'counter:equip_id:*');
define('COUNTER_VCODE_IP', 'counter:ip:*');

// BETA TEST
define('KEY_BETA_RELEASE', 'beta_release:*');
define('KEY_BETA_RELEASE_SPECIAL_IDS', 'beta_release:special_ids:*');

// LOCK
// 生成订单
define('LOCK_GENERATE_ORDER', 'lock:order:tid:*');
// 生成对象
define('LOCK_GENERATE_CLASS', 'lock:class:*:id:*');
// 创建 app_missevan_voice.work_user_info 锁
define('LOCK_GENERATE_WORK_USER_INFO', 'lock:class:work_user_info:work_id:*:user_id:*');

// 用户上传封面图锁
define('LOCK_UPDATE_USER_COVER', 'lock:update_user_cover:user_id:*');

// 兑换剧集
// scenario 场景：default 或 lucky-bag
// uid 唯一 ID：scenario 为 default 时，表示用户 ID，scenario 为 lucky-bag 时为交易记录 ID
// drama_id 剧集 ID
define('LOCK_REDEEM_DRAMA', 'lock:redeem:drama:scenario:*:uid:*:drama_id:*');

// 确认账号
define('CHANGE_ACCOUNT', 'lock:user:account:*');

// Ringing
// 闹铃单音按 ID 分 具体单音类型的名称都可编辑
define('RINGING_1', 'RINGING_SOUND_ID');
define('RINGING_2', 'RINGING_SOUND_SLEEP_ID');

// 用户黑名单
define('KEY_BLACK_LIST', 'black_list');
// IP 黑名单
define('KEY_BLACK_LIST_IP', 'black_list_ip');
// 设备号黑名单（Set 类型）
define('KEY_BLACK_LIST_EQUIP_ID', 'black_list_equip_id');
// 成功进行广告归因的设备号（buvid）（用于次日留存）（Hash 类型）
// 例：HGET ad_tracked_equip_list:one_day_retention:2021-03-18 XY6EFC963FC7A96F2EFBCABAF29FE2F38A686
//    ad_track_202103.52882
define('KEY_AD_TRACKED_EQUIP_BUVID_LIST_ONE_DAY_RETENTION', 'ad_tracked_equip_list:one_day_retention:*');
// 成功进行广告归因的设备（String 类型）
// 例：GET ad_tracked_buvid:XY6EFC963FC7A96F2EFBCABAF29FE2F38A686
//    ad_track_202103.52882
define('KEY_AD_TRACKED_BUVID', 'ad_tracked:buvid:*');
// 成功进行广告归因的用户（String 类型）
// 例：SET ad_tracked:user_id:346286
//    ad_track_202103.52882
define('KEY_AD_TRACKED_USER_ID', 'ad_tracked:user_id:*');
// 回传用户访问到指定数量的音频时，做付费回传（Set 类型）
define('KEY_AD_TRACKED_BUVID_SOUND_ID', 'ad_tracked:buvid:*:sound_id');

// iOS 客户端版本
define('KEY_IOS_VERSION', 'iosversion');

// 分享
define('SHARE', 'fx_*_*');

// 私信
define('MSG_IP', 'lock:msg:ip:*:time:*');
define('MSG_USER', 'lock:msg:uid:*:time:*');

// 签到
define('TASK', 'Temp_*');

// 小鱼干
define('EARS', 'ear_mission_*_*');
define('TS_SOUND', 'log:ts:s_id:*:user_id:*');

// 自定义模块
define('CUSTOM', 'classfications_*');

// 获取验证码
define('VCODE', 'vcode:account:*');
// 验证码比对次数 key
define('VCODE_CHECK_TIMER', 'vcode:account:*:timer');
// 验证码比对次限制次数
define('VCODE_CHECK_TIMES', 20);
// 区分不同操作的验证码
define('POST_OBJECTIVE_TYPE', 'validate:objective:*');

// 短信验证码获取时间间隔 key
define('VCODE_GET_INTERVAL_KEY', 'vcode:account:*:interval');
// 短信验证码获取时间间隔
define('VCODE_GET_INTERVAL', 60);

// 用户每日发送弹幕限制锁
define('KEY_LOCK_USER_DM', 'lock:dm:user_id:*');
// 用户每日发送评论限制锁
define('KEY_LOCK_USER_COMMENT', 'lock:comment:user_id:*');

// 后台存入经典必听（分类名及剧集、音单 ID）的键名
define('CLASSIC_CATALOG_INDEX', 'catalog:classic');

// 经典必听-需获取的剧集数据接口
define('DRAMA_INFO_API', 'http://www.missevan.com/drama/rest/mobile/getdramainfo');

// 每日发送私信人数限制 key
define('DAILY_SEND_MESSAGE_USER', 'message:user:*');

// 每日发送私信人数限制（每日每人最多给 50 个人发送私信）
define('DAILY_SEND_MESSAGE_USER_COUNT_LIMIT', 50);

// 存储信息（标题、简介等）编辑审核通过后的单音 ID
define('CHECKED_SOUND_INFO_PASS', 'bmsync:channel');

// App “猜你喜欢”模块推荐直播间（String 类型）
// e.g. { "id": 1, "room_id": 1, "creator_id": 1, "creator_username": "xx", "cover_url": "http://test/xx.jpg"}
define('KEY_APP_HOMEPAGE_RECOMMEND_LIVE', 'app_homepage:recommend_live');

// 分类界面补充小图标
define('CATALOG_ICONS', 'catalog:icons');

// 推荐音单（全部）
define('ALBUMS_RECOMMENDED_ALL', 'album_tag:all_album_ids');
// 推荐音单（指定分类的全部音单）
define('ALBUMS_RECOMMENDED_SPECIAL_TAG_ALL', 'album_tag:all_album_ids:*');
// 推荐音单（指定分类）
define('ALBUMS_RECOMMENDED_SPECIAL_TAG', 'album_tag:tag_id:*');

// 播放量计数， 第一个 * 为播放来源，1：来自游客；2：来自用户；3：来自管理员；第二个 * 为播放的时间戳
define('KEY_COUNTER_SOUND_VIEWS', 'counter:sound_views:source:*:time:*');

// 出现异常播放量（刷播放量）的音频 ID 集合（数据类型为 set）
define('KEY_ABNORMAL_VIEWS_SOUND', 'abnormal_views:sound_id:*');
// 播放量异常的音频每 IP 段播放量计数（数据类型为 hash stored））
define('KEY_ABNORMAL_IP_SOUND_VIEWS', 'abnormal_ip_views:sound_id:*');

// 热门搜索
define('KEY_HOT_SEARCH', 'hot_search_key_word');

// 音单排行的 key
define('KEY_LIST_ALBUM_SOUND_IDS', 'album_list_with_sound_ids');

// 启动音
define('KEY_POWER_SOUND', 'sound_power');

// 分类首页版头图（第一个 * 代表分类别名，第二 * 代表序号即第几个版本图）
define('CATALOG_HOMEPAGE_BANNER', '*_link_*');
// 分类首页推荐标签（* 代表分类别名）
define('CATALOG_HOMEPAGE_TAG', '*_tag_ids');
// 分类首页推荐音（* 代表分类别名）
define('CATALOG_HOMEPAGE_RECOMMENDED_SOUND', '*_sound_ids');
// 分类首页分区（热门推荐、全区动态、最新投稿）排序（* 代表分类别名）
define('CATALOG_HOMEPAGE_CATALOG_SORT', '*_catalog_sort');
// 分类首页分区的频道或活动
define('CATALOG_HOMEPAGE_CHANNEL_OR_EVENT', '*_channel_or_event');
// App 首页四个小图标
define('KEY_APP_HOMEPAGE_ICONS', 'app_homepage:icons');
// 概念版 App 首页图标
define('KEY_APP_CONCEPT_HOMEPAGE_ICONS', 'app_concept_homepage:icons');
// App 首页新用户猜你喜欢音的 ID，* 为音单 ID
define('KEY_NOOB_LIKE_SOUND_IDS', 'key:noob_like_sounds:album_id:*');
// 分类推荐音换一批数量
define('KEY_HOMEPAGE_CATALOG_TOP_SOUNDS', 'homepage_catalog_top_sounds');

// 广播剧分区首页数据：版头图，热门推荐，大家都在听，人气周榜，付费精品，轮播通栏（* 代表分类 ID）（Hash 类型）
define('KEY_CATALOG_DRAMA_HOMEPAGE', 'drama_homepage:catalog:*');
// 音频分区首页数据：版头图，大家都在听，精选音单，人气周榜，热门推荐，轮播通栏（* 代表分类 ID）（Hash 类型）
define('KEY_CATALOG_SOUND_HOMEPAGE', 'sound_homepage:catalog:*');

// 图片涉黄涉政异步检测的任务列表
define('IMAGE_CHECK_TASKS', 'list:image_check:tasks');

// 语音包新用户免费抽时长（HASH 类型）
define('KEY_VOICE_FREE_DURATION', 'voice:free:duration');
// 用户每日免费抽卡次数（第一个 * 为作品 ID，第二个 * 为用户 ID）
define('KEY_VOICE_LOCK_DRAW_USER', 'card:lock:draw_free:work:*user:*');
// 语音包抽卡页 Banner
define('KEY_VOICE_DRAW_PAGE_BANNER_PATH', 'voice:draw_page_banner_path');

// 语音包免费抽卡活动
define('KEY_VOICE_DRAW_CARD_EVENT', 'voice:work_id:*:draw_card_event');
// 求签新用户每季度活动时长（HASH 类型，字段为“event_days”（免费抽迎新时长）与“expiry_time”（免费活动截止日期））
define('KEY_OMIKUJI_WORK_SEASON_EVENT_TIME', 'omikuji:work:*:season:*:event:time');
// 语音包弹幕计数限制
define('KEY_VOICE_DANMUKU_LIMIT_COUNTER', 'voice:user_id:*:danmuku');
// 语音包入口图标跳转指向原生 App 还是手机网页及公告内容
define('KEY_VOICE_ICON_GO', 'voice:work_id:*:go');
// 语音包播放页水印
define('KEY_VOICE_WATERMARK', 'voice:work_id:*:watermark');
// 用户每日首次播放语音包中的语音时的锁
define('LOCK_VOICE_LISTENED_WORK_USER', 'voice:listened:work_id:*:user_id:*');
// 语音包的热度值
define('KEY_VOICE_HOT_VALUE_WORK_ID', 'voice:hot_value:work_id:*');
// 语音包首页提示标签
define('KEY_VOICE_HOMEPAGE_TIP', 'voice:work_id:*:homepage_tip');
// 角色季包赠送节日卡活动起止日期（Hashes）
// 例 ['from_time' => 1556150000, 'to_time' => 1556156000, 'season' => 1]
define('KEY_VOICE_WORK_PACKAGE_FESTIVAL_EVENT_DATE', 'voice:work:*:package:festival_event_date');

// 语音包卡池（其中 * 分别代表作品 ID，季度和卡片等级）
define('KEY_VOICE_PACKAGE_WORK_ID_SEASON_LEVEL', 'voice:package:work_id:*:season:*:level:*');
// 语音包控制项（Hash）：IP 切换按钮是否显示
define('KEY_VOICE_WORK_CONTROL_ITEMS', 'voice:work:control_items');

// 求签包签池（类型为集合，键名中 * 分别代表作品 ID 和季度）
define('KEY_OMIKUJI_PACKAGE_WORK_ID_SEASON', 'omikuji:package:work_id:*:season:*');
// 求签包相关背景图设置（类型为 Hash，存放了预告位图片及小剧场页版头图）
define('KEY_OMIKUJI_COVER', 'omikuji:cover');
// 求签语音用户在作品签筒下抽到重复卡计数器
define('COUNTER_OMIKUJI_DRAW', 'counter:omikuji:draw:work_id:*:season:*:user_id:*');
// 求签包显示图鉴的卡片等级（Set 类型）
define('KEY_OMIKUJI_SHOW_COVER_LEVELS', 'omikuji:work_id:*:show_cover_levels');

// iOS 设备充值用户白名单（Set 类型）
define('KEY_IOS_DEVICE_TOPUP_USER_ID_WHITELIST', 'ios_device_topup:user_id_whitelist');

// iOS 设备访问路径跟踪
define('KEY_IOS_DEVICE_TRACK', 'ios_device_track:*');

// PayPal 充值用户名单（Set 类型）
// 充值金额上限白名单（白名单中的用户没有最高金额的限制）
define('KEY_TOPUP_PAYPAL_WHITELIST_USER_IDS', 'topup:paypal:whitelist_user_ids');
// 充值黑名单（黑名单中的用户禁止使用 PayPal 充值）
define('KEY_TOPUP_PAYPAL_BLACKLIST_USER_IDS', 'topup:paypal:blacklist_user_ids');
// PayPal 充值用户白名单（Set 类型）（白名单中的用户可使用 PayPal 充值）
define('KEY_PAYPAL_WHITELIST_USER_IDS', 'paypal:whitelist_user_ids');

// 屏蔽的关键词
define('KEY_FORBIDDEN_WORDS', 'forbidden_words:*');

// 充值页面提示内容
define('KEY_TOPUP_TIP', 'topup_tip:client:*');
// 充值支付方式
define('KEY_TOPUP_PAYMENT_METHOD', 'topup_payment_method:client:*');
// 充值商品（ccy）库存（类型为 String（数字型），* 为 ccy 表主键）
define('COUNTER_TOPUP_GOODS_STOCK', 'counter:topup_goods:*');
// 充值商品（ccy）用户购买数量（类型为 Sorted Set，用户 ID 为集合元素，购买次数为元素关联分数），* 为 ccy 表主键
define('COUNTER_SET_TOPUP_GOODS_USER_BUY_NUMBER', 'counter:topup_goods:*:user_buy_num');

// 剧集打赏周榜、月榜数据 CDN 文件地址
define('KEY_REWARD_DRAMA_RANKS_OSS_PATH', 'reward_drama_ranks:period:*:oss_path');
// 剧集打赏金额计数限制（若突破金额限制则更新缓存）
define('KEY_REWARD_DRAMA_COIN_LIMIT_COUNTER', 'reward_drama_coin_limit_counter:drama_id:*');
// 剧集打赏人数
define('KEY_REWARD_DRAMA_USER_COUNT', 'reward_drama_user_count:drama_id:*');
// 用户打赏榜单生成缓存失效时间锁
define('LOCK_REWARD_USER_RANKS_TIME', 'lock:reward_user_ranks:drama_id:*:time');
// 用户打赏榜单正在生成缓存锁
define('LOCK_CREATING_REWARD_USER_RANKS', 'lock:create_reward_user_ranks:drama_id:*');

// API 测试 step
define('KEY_API_TEST_STEP', 'api_test_step');

// 活动投票限制 (HASH)
define('KEY_EVENT_USER_VOTE', 'event:event_id:*:user_id:*');
// 设备给活动作品投票限制 (HASH)
define('KEY_EQUIP_EVENT_SOUND_VOTE', 'event:event_id:*:sound_id:*');

// 人机互交使用，用户 IP 请求登录、发送验证码等接口次数记录
define('KEY_COUNTER_CAPTCHA_SCENE_IP', 'counter:captcha:scene:*:ip:*');
// 人机互交使用，用户设备 ID 请求登录等接口次数记录
define('KEY_COUNTER_CAPTCHA_SCENE_EQUIP_ID', 'counter:captcha:scene:*:equip_id:*');

// 生成设备发送请求的签名的 key
define('KEY_REQUEST_SIGN_UUID', 'request:sign:uuid:*');

// 喜欢音频锁
define('LOCK_USER_LIKE_SOUND', 'user:*:like:sound_id:*');
// 批量喜欢音频锁
define('LOCK_SOUND_BATCH_LIKE', 'sound:batch_like:user:*');
// 喜欢催眠专享音频或音单锁
define('LOCK_USER_LIKE_ELEMENT', 'user:*:like:element_type:*:element_id:*');

// “猜你喜欢”接口 AB Test 计数器（HyperLogLog 类型）
define('COUNTER_PF_LIKE_SOUND_AB_TEST', 'counter:pf:like_sound:ab_test');

// 关注用户的锁
define('LOCK_USER_ID_ATTENTION_ID', 'user_id:*:attention_id:*');

// App 剧集分区首页版头图（String 类型）
define('KEY_APP_DRAMA_HOMEPAGE_BANNER', 'app_drama_homepage:banner');
// App 剧集分区首页轮播通栏（String 类型）
define('KEY_APP_DRAMA_HOMEPAGE_EXTRA_BANNER', 'app_drama_homepage:extra_banner');
// App 猫耳商城首页轮播图（json 字符串）
define('KEY_APP_MAOER_SHOP_HOMEPAGE_BANNER', 'app_maoer_shop_banner');
// 点击喜欢商品锁
define('LOCK_MAOER_SHOP_USER_LIKE_GOODS', 'lock:like_goods:user_id:*:goods_id:*');

// App 下发的配置（hash）
define('KEY_CONFIG_CLIENT', 'config:client:*');

// 禁止评论的元素（音频、音单、频道等，Set 类型）
define('KEY_FORBIDDEN_COMMENT_ELEMENT', 'forbidden_comment:element_type:*');

// 海外屏蔽音豁免的用户（Set 类型）
define('KEY_FOREIGN_FORBIDDEN_SOUND_EXEMPTED_USER_ID', 'foreign_sound_exempted_user_id');

// 游戏预约锁
define('KEY_GAME_SUBSCRIBE_LOCK', 'game:*:user:*:subscribe');

// 禁用特定渠道的功能（Set 类型）
define('KEY_CHANNEL_BLACK_LIST', 'channel_black_list:*');

// 评论计数，* 为评论的时间戳
define('KEY_COUNTER_SOUND_COMMENTS', 'counter:sound_comments:time:*');

// 广播剧分区人气周榜剧集，* 代表分类 ID，Hash key 为完结度（integrity）值，Hash key 为 0 时为总榜（Hash）
define('KEY_CATALOG_HOMEPAGE_WEEKLY_RANK_DRAMA', 'drama_weekly_rank:catalog:*');
// 音频分区人气周榜剧集，* 代表分类 ID，Hash key 为子分类 ID，（Hash）
define('KEY_CATALOG_HOMEPAGE_WEEKLY_RANK_SOUND', 'sound_weekly_rank:catalog:*');

// 音频新增播放量榜单,综合排序使用（ZSET）
define('KEY_SOUND_VIEW_RANK', 'sound_view_rank:catalog:*');

// 记录各类型未读消息提醒数量 * 为用户 ID（Hash 类型，有效时间为一分钟）
define('KEY_USER_NOTICE', 'user_notice:*');
// TEMP: 直播 Tab 灰度临时使用，灰度实验结束后删除
// 实验组 - 直播 Tab 策略
define('KEY_USER_NOTICE_V3', 'user_notice:v3:user_id:*');
// 对照组 - 发现 Tab 策略
define('KEY_USER_NOTICE_V3_OLD', 'user_notice:v3:user_id:*:old');
// 未读消息提醒
define('KEY_USER_NOTICE_V4', 'user_notice:v4:user_id:*');
define('KEY_USER_NOTICE_V5', 'user_notice:v5:user_id:*');
define('KEY_USER_NOTICE_V5_OLD', 'user_notice:v5:user_id:*:old');

// “猜你喜欢”广告位出现广告音频概率，取值范围 0 到 100（String 类型）
define('KEY_SHOW_AD_SOUND_PROBABILITY', 'show_ad_sound_probability');

// 搜索框是否显示推荐词（Hash 类型）
define('KEY_SEARCH_RECOMMEND_CONFIG', 'search_recommend_config');

// 推荐弹窗，* 为设备号（String 类型）
define('KEY_RECOMMEND_POPUP_EQUIP', 'recommend_popup_equip:*');

// 一键登录次数记录（String 类型）
define('KEY_COUNTER_FASTLOGIN_EQUIP', 'counter:fastlogin:equip:*');

/**
 * 10.1 国庆学习挑战活动，满足抽奖条件用户数据 * 表示日期（例如：20200921）（hSet 类型）
 * hSet meet:conditions:user:<date> <user_id> <num>
 *
 * hSet meet:conditions:user:<date> <user_id> 1 用户满抽奖条件
 * hSet meet:conditions:user:<date> <user_id> 2 用户当天已经抽奖
 */
define('KEY_EVENT_MEET_CONDITIONS_USER', 'meet:conditions:user:*');

// 言情乙女向广告 ID，类型为 SET，元素为广告 ID
define('KEY_AD_TRACK_ROMANTIC_ID', 'ad_track_romantic_id');

// 直播待转码音频，类型为 LIST
define('KEY_LIVE_VIDEOS', 'list:live-videos');

/**
 * WORKAROUND: 2021.08.31 之后积分抽奖结束考虑删除
 * 猫耳学院暑期纳新积分详情（类型为 HASH，键名中 * 为用户 ID，field 为 Y-m-d 格式的当日时间，field 对应的 val 为 json）
 *
 *    {
 *      "2021-07-25": {  // 积分完成次数
 *        "like": 1,  // 点赞活动投稿音频当前完成的次数
 *        "share": 0  // 分享活动当前完成的次数
 *      }
 *    }
 */
define('KEY_SUMMER_NAXIN_POINT_USER_ID', 'summer_naxin_point:user_id:*');

// 猫耳学院暑期纳新，用户积分任务锁（String 类型，键名中第一个 * 用户 ID）
define('LOCK_SUMMER_NAXIN_POINT_USER_ID', 'lock:summer_naxin_point:user_id:*');

/**
 * WORKAROUND: 天涯明月刀配音活动临时调整，2021.12.24 投票结束后考虑删除
 * 天涯明月刀配音活动，满足投票条件用户数据 * 表示日期（例如：20211210）（HASH 类型）
 */
define('KEY_GLORY_OF_KINGS_CONDITIONS_USER', 'glory_of_kings:conditions:user:*');

// 安卓 5.6.0 播放量增加异常，设置 2 秒锁，避免重复增加播放量
define('LOCK_SOUND_ADD_PLAY_TIMES_EQUIP_ID_SOUND_ID', 'lock_sound_add_play_times:equip_id:*:sound_id:*');

// 收藏音频锁（* 为音单 ID）
define('LOCK_ALBUM_COLLECT_SOUND', 'lock:album:*:collect:sound');

// 视频云用户 ID 白名单（Set 类型，用于存放需要下发音频签名地址的用户 ID）
define('KEY_UPOS_USER_IDS_WHITE_LIST', 'upos_user_ids_white_list');

// 收藏音单锁（* 为用户 ID）
define('LOCK_ALBUM_COLLECT', 'lock:album:collect:user:*');

// 盲盒剧场首页数据：首页版头图、分享图、剧场预告视频、官方账号信息、排行榜说明文案（Hash 类型）
define('KEY_THEATRE_HOMEPAGE', 'theatre:homepage');

// 盲盒剧场抽盲盒特效信息：特效视频地址，抽出效果音频地址，抽中结果弹窗显示时间点（Hash 类型）
define('KEY_THEATRE_BLIND_BOX_DRAW_INFO', 'theatre:blind_box:draw_info');

/**
 * WORKAROUND: 2022.07.13 之后积分抽奖结束后删除
 * 猫猫星球用户积分详情（类型为 HASH，键名中 * 为用户 ID）
 *
 *    {
 *      "drama_consume_num": 1,  // 剧集消费送积分当前完成次数
 *      "drama_consume_total_balance": 1200  // 剧集消费送积分当前剧集消费钻石
 *    }
 */
define('KEY_MAOMAO_POINT_USER_ID', 'maomao_point:user_id:*');

/**
 * WORKAROUND: 2022.08.01 之后积分抽奖结束后可删除
 * 盲盒剧场抽奖活动用户积分详情（类型为 HASH，键名中第一个 * 活动 ID，第二个 * 用户 ID）
 *
 *    {
 *      "theatre_draw_20220711": 1,  // 抽盲盒积分，field 会带上任务开始日的后缀（格式为 _Ymd），即活动日那一周周一的日期
 *    }
 */
define('KEY_DRAW_POINT_QUESTS_EVENT_ID_USER_ID', 'draw_point:quests:event_id:*:user_id:*');

// vivo refresh_token（365 天 - 1 小时）（String 类型）
define('KEY_VIVO_AD_REFRESH_TOKEN', 'vivo_ad_refresh_token');

// 记录用户访问我听页的时间，值为时间戳，单位：秒（String 类型，* 为用户 ID，有效时间为一周）
define('KEY_USER_FEED_LAST_REQUEST_TIME', 'user:*:feed:last_request_time');

// 特别设置启动音的设备 buvid 列表（Set 类型）
define('KEY_SPECIAL_LAUNCH_BUVID_LIST', 'special_launch_buvid_list');

// 新人锁（安装时设置，登录了老用户时删除），值为安装成功时的时间戳（单位：秒）（String 类型，* 为设备 ID, 有效时间为 7 天）
define('KEY_NEW_DEVICE_EQUIP_ID', 'new_device:equip_id:*');

// 渠道包已启动过的设备 buvid 列表（Set 类型），有效时间至：2025-10-01 00:00:00
define('KEY_CHANNEL_LAUNCHED_BUVID_LIST', 'channel_launched_buvid_list:index:*');

// 音频播放限制用户 ID 白名单（Set 类型，用于存放音频播放设备不受限制的用户 ID）
define('KEY_EQUIP_PLAY_LIMIT_USER_IDS_ALLOW_LIST', 'equip_play_limit_user_ids_allow_list');

// 用户正在播放的设备（有序）集合（Sorted Set 类型，* 为 用户 ID）
define('KEY_EQUIP_PLAY_USER_ID', 'equip_play:user_id:*');

// 记录用户访问底部导航栏直播 Tab 页的时间，值为时间戳，单位：秒（String 类型，* 为用户 ID，有效时间为一周）
// 写入用户访问时间的相关接口：
// api/v2/chatroom/open/feed-list 导航栏显示直播开播全屏直播流列表
// api/v2/chatroom/open/recommend-list 导航栏显示直播开播推荐列表
define('KEY_USER_TAB_BAR_LIVE_LAST_REQUEST_TIME', 'user:*:tab_bar_live:last_request_time');

// 指定限时任务当天已发放奖励总人次，String 类型，第一个 * 为日期，格式为 20060102，第二个 * 为任务类型，缓存有效期 48 小时
define('COUNTER_ADDITIONAL_TASK_FINISHED', 'counter:additional_task_finished:date:*:gtype:*');

// 点播会员下单锁（* 为用户 ID）
define('LOCK_GENERATE_VIP_ORDER', 'lock:generate_vip_order:user_id:*');

// 点播会员签约/解约锁（* 为协议 ID）
define('LOCK_SIGN_TERMINATION_VIP_AGREEMENT', 'lock:sign_termination_vip_agreement:agreement_id:*');

// 点播会员更新订单锁（* 为订单 ID）
define('LOCK_UPDATE_VIP_ORDER', 'lock:update_vip_order:order_id:*');

// 直播任务当天已发放钻石总数，String 类型，第一个 * 为日期，格式为 20060102，缓存有效期 48 小时
define('COUNTER_LIVE_TASK_DIAMOND', 'counter:live_task_diamond:date:*');

// vip 用户每天领取福利钻石锁，有效期 24 小时，String 类型，* 依次为用户 ID、领钻石当天时间日期，日期格式为：20260102
define('LOCK_CLAIM_VIP_DIAMONDS', 'claim_vip_diamonds:user_id:*:date:*');

// vip 用户每天领取福利钻石设备计数器，有效期 24 小时，String 类型，* 依次为 equip_id、领钻石当天时间日期，日期格式为：20260102
define('COUNTER_CLAIM_VIP_DIAMONDS_EQUIP', 'counter:claim_vip_diamonds:equip_id:*:date:*');

// vip 用户每天领取福利钻石 IP 计数器，有效期 24 小时，String 类型，* 依次为 IP、领钻石当天时间日期，日期格式为：20260102
define('COUNTER_CLAIM_VIP_DIAMONDS_IP', 'counter:claim_vip_diamonds:ip:*:date:*');

// iOS 会员订阅锁（* 为协议编号）
define('LOCK_VIP_IOS_SUBSCRIPTION', 'lock:vip_ios_subscription:agreement_no:*');

// 用户任务 token 集合，有效期 24 小时，Set 类型，* 依次为第三方场景、完成任务当天时间日期，日期格式为：20260102
define('KEY_THIRD_TASK_TOKEN', 'third_task_token:scene:*:date:*');

// 用户购买剧集锁
define('LOCK_USER_BUY_DRAMA', 'lock:buy_drama:user_id:*:drama_id:*');

// 直播间飞镖退款锁
define('LOCK_LIVE_TOKEN_REFUND', 'lock:live_token_refund:user_id:*');

// 大会员x猫耳权益当日发放数量
define('KEY_BILI_VIP_MAOER_BENEFIT_TODAY_DELIVERED_NUM', 'bili_vip_maoer_benefit:benefit_type:*:benefit_id:*:today_delivered_num');
// 大会员x猫耳权益发放锁
define('LOCK_BILI_VIP_MAOER_BENEFIT_DELIVERY', 'lock:bili_vip_maoer_benefit_delivery:benefit_type:*:benefit_id:*:user_id:*');
