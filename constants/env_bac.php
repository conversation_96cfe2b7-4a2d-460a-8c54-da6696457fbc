<?php

// comment out the following two lines when deployed to production
defined('YII_DEBUG') or define('YII_DEBUG', true);
defined('YII_ENV') or define('YII_ENV', 'dev');

// 需要进行 API TEST 的接口配置，使用 * 作为通配符，如 ['user/*'] 标识 user 控制器下的所有接口
defined('YII_API_TEST') or define('YII_API_TEST', ['controller/action']);
// 触发兼容测试的概率，取值范围 0 ~ 100，即 0% ~ 100%
defined('YII_API_TEST_PR') or define('YII_API_TEST_PR', 0);
// 是否进行 API 验签
defined('ENABLE_API_VALIDATE') or define('ENABLE_API_VALIDATE', false);

// 滑动验证开关设置 true：开启；false：关闭；默认开启
defined('ENABLE_NOCAPTCHA') or define('ENABLE_NOCAPTCHA', true);
// 开启 API 滑动验证测试，生产环境不配置此项
defined('YII_CAPTCHA_TEST') or define('YII_CAPTCHA_TEST', true);

// 是否开启手机号绑定与否检查
defined('ENABLE_MOBILE_CHECK') or define('ENABLE_MOBILE_CHECK', true);

// 是否走 IAP 代理访问苹果 IAP 服务器
defined('ENABLE_IAP_GATEWAY') or define('ENABLE_IAP_GATEWAY', true);

// 是否开启 B 站风控检查
defined('ENABLE_BILIBILI_RISK_CHECK') or define('ENABLE_BILIBILI_RISK_CHECK', true);

// 开启以每分钟进行播放量持久化
defined('ENABLE_ADD_PLAY_TIMES_PER_MINUTE') or define('ENABLE_ADD_PLAY_TIMES_PER_MINUTE', true);

// 配置路径
defined('CONFIG_PATH') or define('CONFIG_PATH', dirname(__DIR__) . '/config');

// 是否开启压力测试
defined('ENABLE_LOAD_TEST') or define('ENABLE_LOAD_TEST', false);

// 下发视频云签名 CDN 开关，true: 开启；false: 关闭；默认开启
defined('ENABLE_SOUND_BVC_CDN') or define('ENABLE_SOUND_BVC_CDN', true);
// 触发音频下发视频云签名 CDN 地址概率，取值范围 0 ~ 100，即 0% ~ 100%
defined('SOUND_BVC_CDN_TEST_RATIO') or define('SOUND_BVC_CDN_TEST_RATIO', 20);

// 音频同时播放限制开关，true: 开启；false: 关闭；默认开启
defined('ENABLE_EQUIP_PLAY_LIMIT') or define('ENABLE_EQUIP_PLAY_LIMIT', true);

// 启用 tab bar 直播按钮的概率（未启用时为"发现"按钮），取值范围 0 ~ 100，即 0% ~ 100%
// 需求文档：https://info.missevan.com/pages/viewpage.action?pageId=118093283
defined('ENABLE_TAB_BAR_LIVE_RATIO') or define('ENABLE_TAB_BAR_LIVE_RATIO', 20);
