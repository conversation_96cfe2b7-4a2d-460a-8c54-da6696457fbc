<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/8/1
 * Time: 10:06
 */

// 微信支付相关
define('WECHATPAY_APP_ID', '');  // 应用 ID
define('WECHATPAY_MCH_ID', '');  // 商户号
define('WECHATPAY_APP_SIGN', '');  // 应用签名
define('WECHATPAY_KEY', '');
define('WECHATPAY_CALLBACK', 'http://127.0.0.1:8017/callback/wechatpay');
// 会员签约结果回调接口地址
define('WECHATPAY_SIGN_VIP_CALLBACK', 'http://127.0.0.1:8017/callback/wechatpay-sign');

// 猫耳概念版微信支付配置
define('WECHATPAY_APP_ID_CONCEPT_CHANNEL', '');
define('WECHATPAY_APP_SIGN_CONCEPT_CHANNEL', '');

// QQ 钱包支付相关
define('QQPAY_APP_ID', '');  // 应用 ID
define('QQPAY_MCH_ID', '');  // 商户号
define('QQPAY_KEY', '');  // 密钥
define('QQPAY_CALLBACK', 'http://127.0.0.1:8017/callback/qqpay');  // 支付回调
define('QQPAY_APP_SIGN', '');  // 应用签名

// 天猫充值相关
define('TMALL_APPKEY', '');
define('TMALL_APPSECRET', '');
define('TMALL_COOPID', '');  // 商户 ID

// PayPal 支付相关
define('PAYPAL_ACCOUNT', '');
define('PAYPAL_CALLBACK', 'http://127.0.0.1:8017/callback/paypal');
// TODO: 单位美元转人民币汇率调整成取实时汇率
define('PAYPAL_USD_TO_CNY_RATE', 6.00);

// 应用包名
define('APP_PACKAGE_NAMES', [
    'missevan_ios' => 'com.missevan.CatEarFM',
    'missevan_android' => 'cn.missevan',
    'missevan_concept' => 'com.missevan.conceptapp',
    'mimi' => 'jp.mimifm.app',
]);

// 内购商品标识
define('IAP_PRODUCT_IDS', [
    'missevan_ios' => 'com.missevan.CatEarFM',  // Apple Store
    'missevan_android' => 'cn.missevan.item',  // Google Pay
    'missevan_concept' => '',
    'mimi' => 'jp.mimifm.app.item',
]);

// Google Pay 认证密钥配置
define('GOOGLE_PAY_AUTH_CONFIG', [
    'type' => 'service_account',
    'project_id' => 'test',
    'private_key_id' => 'test',
    'private_key' => 'test',
    'client_email' => 'test',
    'client_id' => 'test',
    'auth_uri' => 'https://accounts.google.com/o/oauth2/auth',
    'token_uri' => 'https://oauth2.googleapis.com/token',
    'auth_provider_x509_cert_url' => 'https://www.googleapis.com/oauth2/v1/certs',
    'client_x509_cert_url' => 'https://www.googleapis.com/robot/v1/metadata/x509/xxxx',
]);

define('BILIBILI_LARGE_PAY_CUSTOMER_ID', 123456);
define('BILIBILI_LARGE_PAY_TOKEN', 'xxxxxx');
define('BILIBILI_LARGE_PAY_NOTIFY_URL', 'http://127.0.0.1:8017/callback/bili-large-pay');

define('FM_DOMAIN', '');

// M站内网签名私钥
define('MISSEVAN_PRIVATE_KEY', '');

// 单位货币可兑换的钻石
define('DIAMOND_EXRATE', 10);

// AUTH
define('QQ_AUTH_ANDROID', '**********');
define('QQ_AUTH_IOS', '**********');
define('WEIBO_AUTH_ANDROID', '');
define('WEIBO_AUTH_IOS', '');
define('BILIBILI_AUTH_ANDROID_ID', '');
define('BILIBILI_AUTH_ANDROID_SECRET', '');
define('BILIBILI_AUTH_IOS_ID', '');
define('BILIBILI_AUTH_IOS_SECRET', '');

define('FFMPEG', '');

// 使用 HD 交换密钥的 P、G 值
define('REQUEST_SIGN_DH_P', '***********');
define('REQUEST_SIGN_DH_G', '*');

// API 签名密钥
define('APP_API_SIGN_KEY', 'testkey');

define('SENSITIVE_INFORMATION_KEY', 'openssl_aes_256_cbc_testpassword');
// 加密 IV 初始化向量值，在需要数据库进行查询的地方使用到这个固定常量
define('SENSITIVE_FIXED_IV_KEY', 'testiv');

// 用户智齿客服 partnerId 加密参数
define('USER_SOBOT_PARTNERID_SECRET_KEY', 'test');

// device token 签名密钥
define('DEVICE_TOKEN_KEY', 'testkey');

// 第三方到端回调 API 签名密钥
define('APP_THIRD_PARTY_TASK_API_SIGN_KEY', [
    'baidu' => 'baidu_test_key',
    'ctrip' => 'ctrip_test_key',
    'dianping' => 'dianping_test_key',
    'baidumap' => 'baidumap_test_key',
    'youku' => 'youku_test_key',
    'qq_browser' => 'qq_browser_test_key',
    'weibo' => 'weibo_tesk_key',
    'quark' => 'quark_tesk_key',
]);
