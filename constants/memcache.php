<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/17
 * Time: 16:45
 */

// Cache
// 登录Token前缀
define('Pre_Token', 'token_');
// 相似音
define('Like_Sound', 'sound_like_*_ids');

// 全区动态中子分类下的用户所见的推荐音频 ID，sensitive 为是否包含敏感音频，1 为包含，0 为不包含，cid 为分类 ID
define('KEY_SOUNDS_CHECKED_CATALOG_ID', 'cache:c_sounds_byp:sensitive:*:cid:*');
// 标签下的用户所见的推荐音频 ID，sensitive 为是否包含敏感音频，1 为包含，0 为不包含，tid 为标签 ID
define('KEY_SOUNDS_CHECKED_TAG_ID', 'cache:t_sounds_byp:sensitive:*:tid:*');

// 经典必听某个分类的全部数据
define('CLASSIC_CATALOG_DETAILS', 'catalog_classic_details_');
// 经典必听的全部分类数据
define('CLASSIC_CATALOG_ALL_DATA', 'catalog_classic_all');

// 用户是否有新消息提醒
define('NEW_MSG_REMIND', 'new_msg_remind_uid_');

// 猜你喜欢用户点击数据
define('KEY_GUESSYOURLIKES_CLICKS', 'guess_your_likes_clicks');

// 新版本（iOS >= 6.0.9、安卓 >= 6.0.9）首页自定义推荐模块
define('KEY_GUESSYOURLIKES_MODULES', 'guess_your_likes_modules_*_persona_v5');

// 旧版本（iOS < 6.0.9、安卓 < 6.0.9）首页自定义推荐模块
define('KEY_GUESSYOURLIKES_MODULES_OLD', 'guess_your_likes_modules_*_persona_old');

// 推荐模块详情（* 为模块 ID）
define('KEY_RECOMMEND_MODULE_DETAIL', 'recommend_module_*_detail_v3');

// APP 首页猜你喜欢
define('KEY_GUESS_YOU_LIKE', 'guess_you_like_*');

// 分类首页数据
define('CATALOG_HOMEPAGE', 'catalog_*_homepage');

// App首页 - 分类 - 子分类 - 全区动态中的单音数据
define('KEY_CATALOG_GLOBAL_SOUNDS', 'catalog_global_sounds:catalog_ids:*:sensitive:*');

// 音频弹幕缓存
define('KEY_DM_SOUND_ID_EXMINED', 'dm**');
// 获取弹幕需要用到的音频信息缓存，时长五分钟（* 为音频 ID）
define('KEY_DM_SOUND_INFO', 'dm_sound_info:id:*');
// 互动剧节点弹幕缓存（* 为节点 ID）
define('KEY_INTERACTIVE_DM_NODE_ID', 'dm:interactive:node_id:*');

// 语音包提醒
define('KEY_VOICE_NOTICE', 'voice_notice:*');
// 语音包解锁福利语音卡的阈值
define('KEY_VOICE_HOT_UNLOCK_THRESHOLD_WORK_ID', 'voice_hot_unlock_threshold_work_id:*');
// 语音包信箱消息（JSON 字符串）
define('KEY_VOICE_MSG_BOX', 'voice_msg_box:work_id:*:user_id:*:uuid:*');

// 剧集打赏榜单
define('KEY_REWARD_DRAMA_RANKS', 'reward_drama_ranks:period:*:v2');
// 用户打赏榜单
define('KEY_REWARD_USER_RANKS', 'reward_user_ranks:period:*:drama_id:*');
// 要打赏的剧集数据
define('KEY_REWARD_DRAMA_DATA', 'reward_drama_data:drama_id:*');

// 音频播放页的推荐音频 ID（按推荐策略分类），* 为当前播放音频 ID
define('KEY_SOUND_PLAY_RECOMMEND_STRATEGY_SOUNDS', 'sound_play_recommend_strategy:id:*');

// App 启动图（String 类型）
define('KEY_APP_LAUNCH_SPLASH', 'app_launch_splash:v2');
// App 启动音（String 类型）
define('KEY_APP_LAUNCH_SOUND', 'app_launch_sound');
// App 猜你喜欢广告位（String 类型）
define('KEY_APP_YOU_MIGHT_LIKE_ADV', 'app_you_might_like_adv');

// 剧集分类缓存，* 为分类 ID（String 类型）
define('KEY_DRAMA_TYPE', 'drama_type:catalog_id:*');
// 剧集完结度缓存，* 为分类 ID（String 类型）
define('KEY_DRAMA_INTEGRITY', 'drama_integrity:catalog_id:*');
// 音频分类缓存，* 为父分类 ID（String 类型）
define('KEY_SOUND_CATALOG', 'sound:catalog_id:*');
// 青少年音频随机播放清单（JSON 字符串）
define('KEY_TEENAGER_SOUND_IDS', 'teenager_sound_ids');
// 青少年剧集缓存（JSON 字符串）
define('KEY_TEENAGER_DRAMAS', 'teenager_dramas');

// 互动剧集节点信息缓存（JSON 字符串），* 为节点 ID
define('KEY_INTERACTIVE_NODE_INFO', 'interactive_node_id:v2:*');

// 播放页剧集相关详情缓存
define('KEY_MOBILE_DRAMA_DETAILS_BY_SOUND', 'mobile_drama_details:v2:sound:*');

// 免流卡状态缓存
define('KEY_USERMOB_STATE', 'flow:usermob:*');

// 搜索框推荐词（JSON 字符串）
define('KEY_SEARCH_RECOMMEND_WORDS', 'search_recommend_words');

// 用户动态缓存
define('KEY_USER_FEED', 'user_feed:user_id:*:feed_type:*:sound_id:*');

// 催眠专享电台分类下音单 ID 缓存
// WORKAROUND: 新版催眠专享客户端上线后需要删除
define('KEY_ASMR_RADIO_ALBUM_ID', 'asmr_radio_catalogs_album_ids');
// 催眠专享分类缓存
define('KEY_ASMR_RADIO_CATALOGS', 'asmr_radio_catalogs');

// 首页版头图，保存还未下线 Banner 数据，String 类型，JSON 字符串，*（1 代表安卓 2 代表 iOS）
// 缓存每 10 分钟过期或后台修改数据时删除
define('KEY_APP_HOMEPAGE_BANNER_CLIENT', 'app_homepage:v2:banner:*');

// 分区版头图，保存还未下线 Banner 数据，* 为分区 ID
define('KEY_APP_CATALOG_BANNER', 'app_catalog:banner:*');

// 首页轮播通栏图，保存还未下线轮播通栏图数据，其中 * (1 代表安卓 2 代表 iOS)
// 缓存每 10 分钟过期或后台修改数据时删除
define('KEY_APP_HOMEPAGE_EXTRA_BANNER_CLIENT', 'app_homepage:extra_banner_v2:*');

// 盲盒剧场 Banner 图
// 缓存每 1 小时过期或后台修改数据时删除
define('KEY_THEATRE_HOMEPAGE_BANNER', 'theatre:homepage:banner');

// App 首页 tabs
define('KEY_SITE_TAB_LIST', 'site_tab_list:v2');

// App Tab 下的 icons
define('KEY_TAB_ICON_LIST', 'tab_id:*:icon_list');

// App 个人主页缓存（* 代表用户 ID）
// 缓存有效期为 5 分钟，用户订阅或取消订阅剧集，
// 剧集后台过审剧集，
// 音频后台过审音频
// 后会更新缓存
define('KEY_PERSON_HOMEPAGE', 'person:homepage:user:*');

// 新启动音列表缓存，第一个 * 为页码，第二个 * 为显示数量
// 缓存有效期为 10 分钟
// 后台启动音添加修改音频后会更新缓存
define('KEY_NEW_POWER_SOUND', 'new_power_sound:v4:page:*:pagesize:*');

// 播放日志上报音频信息缓存，* 为音频 ID，缓存有效期 10 分钟
define('KEY_ADD_PLAY_TIMES_SOUND_INFO', 'app:add_play_times:sound_info:*');

// 苹果搜索广告 client_secret
// 缓存有效期为 7 天
// 其中 * 代表 client_id
define('KEY_APPLE_SEARCH_ADS_CLIENT_SECRET', 'apple_search_ads_client_secret:client_id:*');
// 苹果搜索广告 access_token
// 缓存有效期为 30 分钟
// 其中 * 代表 client_id
define('KEY_APPLE_SEARCH_ADS_ACCESS_TOKEN', 'apple_search_ads_access_token:client_id:*');
// 苹果搜索广告 ACL 数据
// 缓存有效期为 7 天
// 其中 * 代表 client_id
define('KEY_APPLE_SEARCH_ADS_ACL_DATA', 'apple_search_ads_acl_data:client_id:*');

// 抖店 access_token（7 天 - 1 小时）
define('KEY_DOUDIAN_ACCESS_TOKEN', 'doudian_access_token');
// 抖店 refresh_token（14 天 - 1 小时）
define('KEY_DOUDIAN_REFRESH_TOKEN', 'doudian_refresh_token');

// vivo access_token（1 天 - 1 小时）
define('KEY_VIVO_AD_ACCESS_TOKEN', 'vivo_ad_access_token');

// Aliyun Service IP（2 小时），其中 * 代表国家或地区
define('KEY_ALIYUN_SERVICE_IP', 'aliyun_service_ip:*');

// 子分类缓存，* 为父分类 ID
define('KEY_SON_CATALOG', 'parent_catalog_id:*:son_catalog');

// 默认头像音数据（JSON 字符串）
define('KEY_USERINTRO_AUDIO', 'userintro_audio_v2');

// 盲盒剧场榜单缓存（missevan-backend command 更新）
define('KEY_THEATRE_RANKS', 'theatre_ranks');

// 盲盒剧场精选评论缓存（JSON 字符串）
// 缓存有效期为 1 小时
define('KEY_THEATRE_PICKED_COMMENTS', 'theatre_picked_comments');

// 盲盒剧场用户抽取盲盒池缓存，* 为用户 ID，游客时为 0（JSON 字符串，有效期最大为 30 分钟）
define('THEATRE_USER_BIND_BOX_POOL', 'theatre_blind_box_pool:user_id:*');

// 视频大卡缓存，* 为用户画像 ID（JSON 字符串，有效期最大为 5 分钟）
define('KEY_VIDEO_CARD_POOL', 'video_card_pool:v2:persona_id:*');

// 首页榜单缓存，* 为用户画像 ID（JSON 字符串，缓存有效期 1 分钟）
define('KEY_APP_HOMEPAGE_RANKS', 'app_homepage_v2:ranks:persona_id:*');

// 微信公众号静默授权 URL state（缓存 5 分钟）
define('KEY_WECHAT_PUBLIC_ACCOUNT_AUTHORIZE_URL_STATE', 'wechat_public_account_authorize_url_state:user_id:*');

// 剧集详情页该画像下该剧集上榜的所有榜单信息，第一个 * 为剧集 ID，第二个 * 为画像 ID（JSON 字符串，缓存有效期 1 分钟）
define('KEY_DRAMA_DETAIL_RANKS', 'drama_detail_ranks:drama_id:*:persona_module:*');

// 应用图标缓存
// 缓存有效期为 10 秒 ~ 5 分钟
define('KEY_APP_ICON', 'app_icon');

// 应用底部图标缓存
// 缓存有效期为 10 秒 ~ 5 分钟
define('KEY_TAB_BAR_PACKAGE', 'tab_bar_package');

// 剧集的 IPR 信息，String 类型，JSON 字符串，* 为剧集 ID，缓存有效期 10 分钟
define('KEY_DRAMA_IPR_INFO', 'drama_ipr_info:drama_id:*');

// 用户在指定剧集中的收听进度，String 类型，JSON 字符串，第一个 * 为用户 ID，第二个 * 为剧集 IDs，缓存有效期 2 分钟
define('KEY_USER_DRAMA_EPISODE', 'app_banner_episode:user_id:*drama_ids:*');
