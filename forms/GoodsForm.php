<?php

namespace app\forms;

use app\components\base\Model;
use Exception;
use yii\helpers\Json;

/**
 * Class GoodsForm
 * @package app\forms
 *
 * @property $id;
 * @property $title
 * @property $price
 * @property $num
 * @property $context
 */
class GoodsForm extends Model
{
    const SCENARIO_DEFAULT = 'default';
    const SCENARIO_FUKUBUKURO = 'fukubukuro';
    const SCENARIO_REBATE_GIFT = 'rebate-gift';
    const SCENARIO_LUCKY_BAG = 'lucky-bag';
    const SCENARIO_LIVE_TOKEN = 'live-token';

    // 交易所属类型
    const TRANSACTION_TYPE_LIVE = 1;  // 直播间
    const TRANSACTION_TYPE_DRAMA = 2;  // 广播剧

    /**
     * 商品 ID
     * @var int
     */
    public $id;

    /**
     * 商品标题
     * @var string
     */
    public $title;

    /**
     * 商品单价（单位：钻石）
     * @var int
     */
    public $price;

    /**
     * 商品数量
     * @var int
     */
    public $num = 1;

    /**
     * 交易类型
     * @var int
     */
    public $transaction_type = self::TRANSACTION_TYPE_LIVE;

    /**
     * @var string
     */
    public $context;

    /**
     * @var string|null
     */
    private $context_class;

    /**
     * @var Object
     */
    private $_context_obj;

    public function __construct($scenario = self::SCENARIO_DEFAULT, $context_class = null)
    {
        parent::__construct(['scenario' => $scenario]);
        $this->context_class = $context_class;
    }

    public function scenarios()
    {
        return [
            self::SCENARIO_DEFAULT => ['id', 'title', 'price', 'num'],
            self::SCENARIO_FUKUBUKURO => ['id', 'title', 'price', 'num', 'context'],
            self::SCENARIO_REBATE_GIFT => ['id', 'title', 'price', 'num', 'context'],
            self::SCENARIO_LUCKY_BAG => ['id', 'title', 'price', 'num', 'transaction_type'],
            self::SCENARIO_LIVE_TOKEN => ['id', 'title', 'price', 'num'],
        ];
    }

    public function rules()
    {
        return [
            [['id', 'title', 'price'], 'required'],
            [['id', 'price'], 'number', 'integerOnly' => true, 'min' => 1],
            [['num'], 'number', 'integerOnly' => true, 'min' => 1, 'except' => self::SCENARIO_LIVE_TOKEN],
            [['num'], 'number', 'integerOnly' => true, 'min' => 0, 'on' => self::SCENARIO_LIVE_TOKEN],
            [['transaction_type'], 'in', 'range' => [self::TRANSACTION_TYPE_LIVE, self::TRANSACTION_TYPE_DRAMA]],
            [['context'], 'checkContext'],
        ];
    }

    public function checkContext()
    {
        if (!$this->context) {
            return;
        }
        $context = Json::decode($this->context);
        if ($this->context_class) {
            try {
                $context_obj = new $this->context_class($context);
            } catch (Exception $e) {
                return $this->addError('context', $e->getMessage());
            }
            $this->_context_obj = $context_obj;
        } else {
            $this->_context_obj = (object)$context;
        }
    }

    /**
     * @return Object
     */
    public function getContextInfo()
    {
        return $this->_context_obj;
    }

    public function attributeLabels()
    {
        return [
            'id' => '商品 ID',
            'title' => '商品标题',
            'price' => '商品价格',
            'num' => '商品数量',
        ];
    }

    public function getTotalPrice()
    {
        return $this->num * $this->price;
    }

    public function getTotalPriceInCurrency()
    {
        return $this->getTotalPrice() / DIAMOND_EXRATE;
    }

}
