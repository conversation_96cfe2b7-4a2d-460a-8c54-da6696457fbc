<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/14
 * Time: 10:09
 */

namespace app\forms;

use app\components\base\Model;
use app\components\auth\AuthApple;
use app\components\auth\AuthBiliBili;
use app\components\auth\AuthQQ;
use app\components\auth\AuthWechat;
use app\components\auth\AuthWeibo;
use app\components\util\Equipment;
use app\components\util\MUtils;
use app\components\util\SSOClient;
use app\models\Mowangskuser;
use app\models\UserAddendum;
use Exception;
use libphonenumber\NumberParseException;
use libphonenumber\PhoneNumberUtil;
use missevan\util\MUtils as MUtils2;
use Yii;
use app\models\User;
use yii\db\IntegrityException;
use yii\web\HttpException;
use yii\web\UploadedFile;

class LoginForm extends Model
{
    const SCENARIO_LOGIN = 'login';
    const SCENARIO_FAST_LOGIN = 'fast_login';
    const SCENARIO_FAST_AUTH_BIND = 'fast_auth_bind';
    const SCENARIO_REGISTER = 'register';
    const SCENARIO_PRE_REGISTER = 'pre-register';
    const SCENARIO_AUTH = 'auth';
    const SCENARIO_REGISTER_AUTH = 'register_auth';
    const SCENARIO_BIND_AUTH = 'bind_auth';
    const SCENARIO_BIND_AUTH2 = 'bind_auth2';
    const SCENARIO_CANCEL_AUTH = 'cancel_auth';
    const SCENARIO_CONFIRM = 'confirm';
    const SCENARIO_CONFIRM_PASSWORD = 'confirm_password';
    const SCENARIO_BIND_ACCOUNT = 'bind_account';
    // 新用户静默注册，目前新用户通过抖店购买商品会使用到该场景
    const SCENARIO_ENSURE_REGISTER = 'ensure_register';

    const UNKNOW_ACCOUNT = -1;
    const UNKNOW_MOBILE = -2;
    const EMAIL = 1;
    const MOBILE = 2;
    const QQ = 3;
    const WEIBO = 4;
    const WECHAT = 5;
    const BILIBILI = 6;
    const APPLE = 7;

    // 允许密码输错次数
    const LIMIT_WRONG_PASSWORD = 5;

    /**
     * @var string | int 账号 可以为邮箱或者手机号
     */
    public $account;

    /**
     * @var string 密码只能由数字，字母和下划线组成，长度为6～64
     */
    public $password;

    public $new_password;

    /**
     * @var string 用户名
     */
    public $username;

    /**
     * @var string 验证码
     */
    public $code;

    /**
     * @var string 第三方登录uuid
     */
    public $uuid;

    /**
     * @var string 第三方登录token
     */
    public $access_token;

    /**
     * @var string 第三方授权 code
     */
    public $auth_code;

    /**
     * @var string 微信登录时的 openid
     */
    public $openid;

    /**
     * @var string 第三方账号头像地址
     */
    public $iconurl;

    /**
     * @var int type 账号类型 -1为未知
     */
    public $type = -1;

    public $auth_type = -1;

    public $account_type = -1;

    public $region = 'CN';

    /**
     * @var string 阿里云滑动验证的会话 ID
     */
    public $session_id;

    private $_user = false;

    public $third_name = false;

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios[self::SCENARIO_LOGIN] = ['account', 'password', 'region'];
        $scenarios[self::SCENARIO_REGISTER] = ['account', 'password', 'username', 'code', 'region'];
        $scenarios[self::SCENARIO_PRE_REGISTER] = ['account', 'username', 'region'];
        $scenarios[self::SCENARIO_AUTH] = ['uuid', 'openid', 'auth_type', 'access_token', 'auth_code'];
        $scenarios[self::SCENARIO_REGISTER_AUTH] = ['account', 'password', 'uuid', 'openid', 'auth_type', 'access_token', 'code', 'username', 'region', 'iconurl', 'auth_code'];
        $scenarios[self::SCENARIO_BIND_AUTH] = ['uuid', 'openid', 'auth_type', 'access_token', 'auth_code'];
        $scenarios[self::SCENARIO_BIND_AUTH2] = [
            'uuid', 'openid', 'auth_type', 'access_token', 'account', 'region', 'code', 'auth_code'
        ];
        $scenarios[self::SCENARIO_CANCEL_AUTH] = ['auth_type'];
        $scenarios[self::SCENARIO_CONFIRM] = ['account_type'];
        $scenarios[self::SCENARIO_CONFIRM_PASSWORD] = ['account_type', 'new_password'];
        $scenarios[self::SCENARIO_BIND_ACCOUNT] = ['account', 'region', 'account_type'];

        return $scenarios;
    }

    public function rules()
    {
        $rules = [
            [['account', 'password', 'username', 'code', 'uuid', 'access_token', 'auth_type'], 'required'],
            [['openid', 'auth_code'], 'string'],
            [['iconurl'], 'url'],
            [['type', 'auth_type', 'account_type'], 'integer'],
            [
                ['new_password'],
                'string',
                'min' => Mowangskuser::PASSWORD_MIN_LENGTH,
                'max' => Mowangskuser::PASSWORD_MAX_LENGTH,
                'tooLong' => Yii::t('app/error', 'The password should be no more than {max_length} characters',
                    ['max_length' => Mowangskuser::PASSWORD_MAX_LENGTH]),
                'tooShort' => Yii::t('app/error', 'The password should be no less than {min_length} characters',
                    ['min_length' => Mowangskuser::PASSWORD_MIN_LENGTH]),
                'on' => [self::SCENARIO_REGISTER, self::SCENARIO_CONFIRM_PASSWORD],
            ],
            ['access_token', 'checkAccessToken'],
            ['account', 'checkLoginAccount', 'on' => self::SCENARIO_LOGIN],
            ['account', 'checkNewEmail', 'on' => self::SCENARIO_BIND_ACCOUNT],
            [
                'account',
                'checkAccount',
                'on' => [
                    self::SCENARIO_CONFIRM,
                    self::SCENARIO_BIND_ACCOUNT,
                    self::SCENARIO_BIND_AUTH2,
                    self::SCENARIO_REGISTER,
                    self::SCENARIO_REGISTER_AUTH,
                    self::SCENARIO_PRE_REGISTER,
                ],
            ],
            [
                ['account', 'username'],
                'checkUnique',
                'on' => [
                    self::SCENARIO_REGISTER,
                    self::SCENARIO_REGISTER_AUTH,
                    self::SCENARIO_PRE_REGISTER,
                ],
            ],
            ['code', 'checkCode'],
        ];
        return $rules;
    }

    public function checkAccount($attribute)
    {
        if (!$this->hasErrors()) {
            if (Yii::$app->equip->isFromMiMiApp()) {
                // MiMiApp 暂时只开放邮箱注册/登录/验证码验证等操作
                if (!MUtils2::isEmail($this->$attribute)) {
                    $this->addError($attribute, Yii::t('app/error', 'Please input correct email address'));
                }
                $type = self::EMAIL;
            } else {
                $type = self::getAccountType($this->$attribute, $this->region);
            }

            if (self::UNKNOW_ACCOUNT === $type) {
                $this->addError($attribute, Yii::t('app/error', 'Please input correct phone number'));
            } elseif (self::UNKNOW_MOBILE === $type) {
                $this->addError($attribute, Yii::t('app/error', 'Please input correct phone number'));
            } else {
                $this->type = $type;
            }
            if (self::MOBILE === $this->type) {
                if ($mobile_info = MUtils2::getMobileNumber($this->account, $this->region)) {
                    $this->account = $mobile_info->mobile_num;
                    $this->region = $mobile_info->region_num;
                } else {
                    $this->addError($attribute, Yii::t('app/error', 'Please input correct phone number'));
                }
            }
        }
    }

    public function checkNewEmail($attribute)
    {
        if (self::EMAIL === $this->type && !MUtils2::isSupportEmail($this->account)) {
            // 绑定新邮箱时过滤特殊的邮箱后缀名
            $this->addError($attribute, Yii::t('app/error', 'Current email address is not supported'));
        }
    }

    public function checkLoginAccount($attribute)
    {
        if (!$this->hasErrors()) {
            $type = self::getAccountType($this->$attribute, $this->region);

            if (self::UNKNOW_ACCOUNT === $type) {
                $this->addError($attribute, Yii::t('app/error', 'Please input correct account'));
            } elseif (self::UNKNOW_MOBILE === $type) {
                $this->addError($attribute, Yii::t('app/error', 'Incorrect username or password'));
            } else {
                $this->type = $type;
            }
            if (self::MOBILE === $this->type) {
                if ($mobile_info = MUtils2::getMobileNumber($this->account, $this->region)) {
                    $this->account = $mobile_info->mobile_num;
                    $this->region = $mobile_info->region_num;
                } else {
                    $this->addError($attribute, Yii::t('app/error', 'Please input correct phone number'));
                }
            }

            if (!YII_DEBUG) {
                $redis = Yii::$app->redis;
                $lock_login_key = $redis->generateKey(COUNTER_LOGIN, $this->account);
                $err_notice = Yii::t('app/error',
                    'Entered wrong password {max_fail_times} times, please try again in {retry_interval} hour',
                    ['max_fail_times' => 5, 'retry_interval' => 1]);
                if ((int)$redis->get($lock_login_key) >= self::LIMIT_WRONG_PASSWORD) {
                    $this->addError('password', $err_notice);
                } else {
                    // 开始事务
                    $result = $redis->multi()
                        ->incr($lock_login_key)
                        ->expire($lock_login_key, ONE_HOUR)
                        ->exec();
                    if ($result[0] > self::LIMIT_WRONG_PASSWORD) {
                        $this->addError('password', $err_notice);
                    }
                }
            }
        }
    }

    public function checkCode($attribute)
    {
        if (!$this->hasErrors()) {
            if ($this->getScenario() === self::SCENARIO_BIND_AUTH2 && self::UNKNOW_ACCOUNT === $this->type
                    && Equipment::isAppOlderThan('4.5.1', ' 5.4.1')
                    && ('0' === substr($this->account, 0, 1))) {
                // WORKAROUND: 临时修复 Android < 5.4.1，iOS < 4.5.1 时第三方绑定注册失败问题
                // 老版本未传递 region 参数导致海外 0 开头的手机短信验证码验证失败（Redis 缓存键名匹配不上）
                // 未传递 region，获取的账号类型（$this->type）为未知
                // 手机号首位数字为 0 时，需要去掉首位数字 0
                $this->account = substr($this->account, 1);
            }
            if (!VcodeForm::checkIdentifyCode($this->account, $this->code)) {
                $this->addError($attribute, Yii::t('app/error', 'Verification code error'));
            }
        }
    }

    public function checkAccessToken($attribute)
    {
        if (!$this->hasErrors()) {
            $auth = false;
            $this->auth_type = (int)$this->auth_type;
            switch ($this->auth_type) {
                case self::QQ:
                    $auth = new AuthQQ();
                    break;
                case self::WEIBO:
                    $auth = new AuthWeibo();
                    break;
                case self::WECHAT:
                    $auth = new AuthWechat();
                    break;
                case self::BILIBILI:
                    $auth = new AuthBiliBili();
                    break;
                case self::APPLE:
                    $auth = new AuthApple();
                    break;
                default:
                    return $this->addError($attribute, '验证信息非法');
            }
            if ($auth) {
                $os = Yii::$app->equip->getOs();
                $uuid = self::WECHAT === $this->auth_type ? $this->openid : $this->uuid;
                if (self::WEIBO === $this->auth_type && !$auth->validateUuid($uuid, $this->access_token)) {
                    // FIXME: 第三方登录需验证客户端传递 uuid 与 access_token 是否匹配（防篡改）
                    // 先修复微博登录相关问题，其他第三方登录后续修复
                    return $this->addError($attribute, '第三方登录授权不合法');
                }
                $return = $auth->auth($uuid, $this->access_token, $os);
                if ($return !== false) {
                    if (self::WECHAT === $this->auth_type && $this->uuid !== $return['unionid']) {
                        return $this->addError($attribute, '第三方登录授权不合法');
                    }
                    if (self::APPLE === $this->auth_type && $this->uuid !== $return['sub']) {
                        return $this->addError($attribute, '第三方登录授权不合法');
                    }
                    $this->third_name = $return['nickname'];
                    $this->iconurl = $return['iconurl'];
                } else {
                    return $this->addError($attribute, '第三方登录授权不合法');
                }
            }
        }
    }

    public function checkUnique($attribute)
    {
        if (!$this->hasErrors()) {
            if ('account' === $attribute) {
                switch ($this->type) {
                    case self::EMAIL:
                        if (Yii::$app->sso->checkEmailExists($this->$attribute)) {
                            throw new HttpException(400, Yii::t('app/error', 'The email address already exists'),
                                *********);
                        }
                        break;
                    case self::MOBILE:
                        if (Yii::$app->sso->checkMobileExists($this->$attribute, $this->region)) {
                            throw new HttpException(400, Yii::t('app/error', 'The phone number already exists'),
                                *********);
                        }
                        break;
                    default:
                        throw new Exception(Yii::t('app/error',
                            'This type of validation is not supported: {type}', ['type' => $this->type]));
                }
            } elseif ('username' === $attribute) {
                if ($result = Mowangskuser::checkUsername($this->$attribute)) {
                    throw new HttpException(400, $result, 201010002);
                } elseif (Mowangskuser::find()->where(['username' => $this->$attribute])->exists()) {
                    throw new HttpException(400, Yii::t('app/error', 'This username already exists'), *********);
                }
            } else {
                throw new Exception(Yii::t('app/error',
                    'This field could not be validated: {field}', ['field' => $attribute]));
            }
        }
    }

    public function login()
    {
        // TODO: 后续 QQ/Wechat/Weibo 也调整成客户端传 auth_code，由服务端获取 access_token 等用户相关信息
        if (self::BILIBILI === (int)$this->auth_type) {
            if (!$this->auth_code) {
                throw new HttpException(400, Yii::t('app/error', 'params error'));
            }
            $token_info = AuthBiliBili::getAccessToken($this->auth_code, Yii::$app->equip->getOs())['token_info'];
            $this->access_token = $token_info['access_token'];
            $this->uuid = (string)$token_info['mid'];
        }
        if ($this->validate()) {
            $user = $this->getUserFromSSO();
            if ($user && isset($user['user_id']) && !Mowangskuser::find()->where('id = :id',
                [':id' => $user['user_id']])->exists()) {
                // SSO 用户表注册成功，mowangskuser 表注册失败时，重新在 mowangskuser 表注册
                $this->retryRegist($user);
            }
            $token = Yii::$app->user->login($user);

            // 删除计数器
            VcodeForm::delCounterLogin($this->account);
            return $token;
        }
        return false;
    }

    public function changePassword()
    {
        $user_id = Yii::$app->user->id;
        $this->type = (int)$this->account_type;
        $user = Yii::$app->sso->getUserAccount($user_id);
        if (self::EMAIL === $this->type) {
            $this->account = $user['email'];
            $objective_account = $this->account;
        } elseif (self::MOBILE === $this->type) {
            $this->account = $user['mobile'];
            $objective_account = $user['region'] . $this->account;
        } else {
            throw new HttpException(400, Yii::t('app/error', 'Account type error'));
        }
        if (!VcodeForm::checkObjective(VcodeForm::TYPE_CHANGE_PASSWORD, $objective_account)) {
            throw new HttpException(403, Yii::t('app/error', 'Verification code error'));
        }
        if (self::MOBILE === $this->type) {
            $this->region = (int)$user['region'];
        }
        // 验证未通过
        if (!$this->validate()) {
            return false;
        }
        // 修改密码
        $data = [
            'type' => SSOClient::TYPE_CHANGE_PASSWORD,
            // WORKAROUND: 这边由于 rules 删掉了对应的 password 规则，所以 iOS 4.5.6 以下、Android 5.4.6 以下
            // 使用 post 参数绕过模型的校验，新版本不传 password
            'old_password' => Yii::$app->request->post('password'),
            'new_password' => $this->new_password,
        ];
        $result = Yii::$app->sso->updatePassword($data);
        if (SSOClient::CODE_SUCCESS !== $result['code']) {
            $info = Yii::$app->sso->codeToMessage($result['code']);
            throw new HttpException($result['status'], $info, $result['code']);
        }
        // 操作成功后，删除验证码
        VcodeForm::delObjectiveKey($objective_account);
        VcodeForm::delIdentifyCode($this->account);
        return true;
    }

    public function confirmOldAccount(int $user_id = 0)
    {
        if (!$user_id) {
            $user_id = Yii::$app->user->id;
        }
        if ($this->validate()) {
            switch ((int)$this->account_type) {
                case LoginForm::EMAIL:
                    $old_field = 'email';
                    break;
                case LoginForm::MOBILE:
                    $old_field = 'mobile';
                    break;
                default:
                    throw new HttpException(400, Yii::t('app/error', 'Account type error'), *********);
            }
            $user = Yii::$app->sso->getUserAccount($user_id);
            if (!$user[$old_field]) {
                throw new HttpException(404, Yii::t('app/error', 'Account does not exist'), *********);
            }

            $redis = Yii::$app->redis;
            $key = $redis->generateKey(CHANGE_ACCOUNT, $user[$old_field]);
            if (!$redis->get($key)) {
                throw new HttpException(403,
                    Yii::t('app/error', 'Please verify your phone number or email address firstly'), *********);
            }
            return true;
        }
        return false;
    }

    public function cancelBind()
    {
        if ($this->validate()) {
            $user_id = Yii::$app->user->id;
            $user = Mowangskuser::findOne($user_id);
            if (!$user) throw new HttpException(404, Yii::t('app/error', 'User does not exist'));

            $add_user = UserAddendum::getByPk($user->id);
            switch ($this->auth_type) {
                case self::QQ:
                    $auth_type = 'qquid';
                    $add_user->qq = '';
                    break;
                case self::WEIBO:
                    $auth_type = 'weibouid';
                    $add_user->weibo = '';
                    break;
                case self::WECHAT:
                    $auth_type = 'wechatuid';
                    $add_user->wechat = '';
                    break;
                case self::BILIBILI:
                    $auth_type = 'bilibiliuid';
                    $add_user->bilibili = '';
                    break;
                case self::APPLE:
                    $auth_type = 'appleuid';
                    $add_user->apple = '';
                    break;
                default:
                    throw new HttpException(400, Yii::t('app/error', 'Account type error'));
            }
            $add_user->save();
            // 更新 SSO 上第三方账号信息
            Yii::$app->sso->update($user->id, [$auth_type => null], Yii::$app->user->identity->token);
            return true;
        }
        return false;
    }

    public function bind()
    {
        // TODO: 后续 QQ/Wechat/Weibo 也调整成客户端传 auth_code，由服务端获取 access_token 等用户相关信息
        if (self::BILIBILI === (int)$this->auth_type) {
            if (!$this->auth_code) {
                throw new HttpException(400, Yii::t('app/error', 'params error'));
            }
            $token_info = AuthBiliBili::getAccessToken($this->auth_code, Yii::$app->equip->getOs())['token_info'];
            $this->access_token = $token_info['access_token'];
            $this->uuid = (string)$token_info['mid'];
        }
        if ($this->validate()) {
            $user_id = Yii::$app->user->id;
            $user = Mowangskuser::findOne($user_id);
            if (!$user) {
                throw new HttpException(404, Yii::t('app/error', 'User does not exist'));
            }

            $extra_arr = [];
            switch ($this->auth_type) {
                case self::QQ:
                    $auth_type = 'qquid';
                    break;
                case self::WEIBO:
                    $auth_type = 'weibouid';
                    break;
                case self::WECHAT:
                    $auth_type = 'wechatuid';
                    $extra_arr = ['wechatopenid' => $this->openid];
                    break;
                case self::BILIBILI:
                    $auth_type = 'bilibiliuid';
                    break;
                case self::APPLE:
                    $auth_type = 'appleuid';
                    break;
                default:
                    throw new HttpException(400, Yii::t('app/error', 'Account type error'));
            }
            // 更新 SSO 上第三方账号信息
            Yii::$app->sso->update($user->id, array_merge([$auth_type => $this->uuid], $extra_arr),
                Yii::$app->user->identity->token);
            if ($this->third_name !== false) {
                $add_user = UserAddendum::getByPk($user_id);
                switch ($this->auth_type) {
                    case self::QQ:
                        $add_user->qq = $this->third_name;
                        break;
                    case self::WEIBO:
                        $add_user->weibo = $this->third_name;
                        break;
                    case self::WECHAT:
                        $add_user->wechat = $this->third_name;
                        break;
                    case self::BILIBILI:
                        $add_user->bilibili = $this->third_name;
                        break;
                    case self::APPLE:
                        $add_user->apple = $this->third_name;
                        break;
                    default:
                        throw new HttpException(400, Yii::t('app/error', 'Account type error'));
                }
                $add_user->save();
            }
            return true;
        }
        return false;
    }

    public function registBind()
    {
        // TODO: 后续 QQ/Wechat/Weibo 也调整成客户端传 auth_code，由服务端获取 access_token 等用户相关信息
        if (self::BILIBILI === (int)$this->auth_type) {
            if (!$this->auth_code) {
                throw new HttpException(400, '参数错误');
            }
            $token_info = AuthBiliBili::getAccessToken($this->auth_code, Yii::$app->equip->getOs())['token_info'];
            $this->access_token = $token_info['access_token'];
            $this->uuid = (string)$token_info['mid'];
        }
        if ($this->validate()) {
            $user = Yii::$app->sso->getUserInfoByMobile($this->account, $this->region);
            if (!$user) throw new HttpException(404, '用户不存在');

            $transaction = Yii::$app->db->beginTransaction();
            $extra_arr = [];
            try {
                switch ($this->auth_type) {
                    case self::QQ:
                        $auth_type = 'qquid';
                        break;
                    case self::WEIBO:
                        $auth_type = 'weibouid';
                        break;
                    case self::WECHAT:
                        $auth_type = 'wechatuid';
                        $extra_arr = ['wechatopenid' => $this->openid];
                        break;
                    case self::BILIBILI:
                        $auth_type = 'bilibiliuid';
                        break;
                    case self::APPLE:
                        $auth_type = 'appleuid';
                        break;
                    default:
                        throw new HttpException(400, '账号类型不存在');
                }
                // 对于微信绑定存在的问题：
                // Android 与 iOS 的微信 AppKey 不同，若用户原先使用 iOS 绑定 M 号并登录，换到 Android 后
                // 需要先在 iOS 设备上对 M 号解绑微信，再用 Android 绑定微信
                if ($user[$auth_type] ?? false) {
                    throw new HttpException(400, '该账号已被绑定，请先解绑或更换其它账号');
                }
                // TODO: 换成 token 方式更新
                // 更新 SSO 上第三方账号信息
                Yii::$app->sso->update($user['id'], array_merge([$auth_type => $this->uuid], $extra_arr));
                if ($this->third_name !== false) {
                    $add_user = UserAddendum::getByPk($user['id']);
                    switch ($this->auth_type) {
                        case self::QQ:
                            $add_user->qq = $this->third_name;
                            break;
                        case self::WEIBO:
                            $add_user->weibo = $this->third_name;
                            break;
                        case self::WECHAT:
                            $add_user->wechat = $this->third_name;
                            break;
                        case self::BILIBILI:
                            $add_user->bilibili = $this->third_name;
                            break;
                        case self::APPLE:
                            $add_user->apple = $this->third_name;
                            break;
                    }
                    if (!$add_user->save()) {
                        return false;
                    }
                }
                $transaction->commit();
                // 操作成功后，删除验证码
                VcodeForm::delIdentifyCode($this->account);
                return true;
            } catch (\Exception $e) {
                $transaction->rollBack();
                throw $e;
            }
        }
        return false;
    }

    private function isThirdParty()
    {
        return in_array($this->auth_type, [self::QQ, self::WEIBO, self::WECHAT, self::BILIBILI, self::APPLE]);
    }

    public function regist()
    {
        if (Equipment::isBanVersion()) {
            throw new HttpException(403, '请升级 App 至最新版本，获得更优质体验~');
        }
        // TODO: 后续 QQ/Wechat/Weibo 也调整成客户端传 auth_code，由服务端获取 access_token 等用户相关信息
        if (self::BILIBILI === (int)$this->auth_type) {
            if (!$this->auth_code) {
                throw new HttpException(400, Yii::t('app/error', 'params error'));
            }
            $token_info = AuthBiliBili::getAccessToken($this->auth_code, Yii::$app->equip->getOs())['token_info'];
            $this->access_token = $token_info['access_token'];
            $this->uuid = (string)$token_info['mid'];
        }
        if ($this->validate()) {
            $user = new Mowangskuser();
            if ($this->isThirdParty()) {
                // 第三方注册
                $user->setScenario(Mowangskuser::SCENARIO_THIRD_REGIST);
            } else {
                // 普通注册
                $user->setScenario(Mowangskuser::SCENARIO_REGIST);
            }
            if (($icon = UploadedFile::getInstanceByName('iconurl')) || $this->iconurl) {
                // 1. 客户端去获取第三方的头像在客户端本地显示出来
                // 2. 但在提交给服务端的时候不把 iconurl 提交过来
                //   i. 因为客户端所用的封装 SDK 只能获取到一种尺寸的图片
                //   ii. 而服务端能获取到第三方给的所有尺寸的头像，因而由服务端来获取需要保存的头像
                // 3. 只有在用户手动自定义上传头像时才会将所上传的 iconurl 图片提交给服务端
                $local_file_path = $icon ? $icon->tempName : MUtils::getRemoteFile($this->iconurl);
                if ($local_file_path) {
                    $user->avatar_file = $local_file_path;
                }
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                switch ($this->type) {
                    case self::EMAIL:
                        $user->email2 = $this->account;
                        break;
                    case self::MOBILE:
                        $user->region2 = $this->region;
                        $user->mobile2 = (int)$this->account;
                        break;
                }
                $user->username = $this->username;
                $user->password = $this->password;
                // 设置传给 SSO 注册接口，注册账号类型参数
                $user->operate_type = $this->getOperateType();
                if (!$user->save()) {
                    throw new HttpException(400, MUtils::getFirstError($user));
                }
                // WORKAROUND: 第三方绑定注册时，新记录才会返回头像信息
                $iconurl = $user->iconurl;
                $extra_arr = [];
                if ($this->auth_type > 0) {
                    switch ($this->auth_type) {
                        case self::QQ:
                            $auth_type = 'qquid';
                            break;
                        case self::WEIBO:
                            $auth_type = 'weibouid';
                            break;
                        case self::WECHAT:
                            $auth_type = 'wechatuid';
                            $extra_arr = ['wechatopenid' => $this->openid];
                            break;
                        case self::BILIBILI:
                            $auth_type = 'bilibiliuid';
                            break;
                        case self::APPLE:
                            $auth_type = 'appleuid';
                            break;
                    }
                    if (!$user->save()) {
                        throw new HttpException(400, MUtils::getFirstError($user));
                    }
                    // TODO: 换成 token 方式更新
                    // 更新 SSO 上第三方账号信息
                    Yii::$app->sso->update($user->id, array_merge([$auth_type => $this->uuid], $extra_arr));
                    if ($this->third_name !== false) {
                        $add_user = UserAddendum::getByPk($user->id);
                        switch ($this->auth_type) {
                            case self::QQ:
                                $add_user->qq = $this->third_name;
                                break;
                            case self::WEIBO:
                                $add_user->weibo = $this->third_name;
                                break;
                            case self::WECHAT:
                                $add_user->wechat = $this->third_name;
                                break;
                            case self::BILIBILI:
                                $add_user->bilibili = $this->third_name;
                                break;
                            case self::APPLE:
                                $add_user->apple = $this->third_name;
                                break;
                        }
                        if (!$add_user->save()) {
                            throw new HttpException(400, MUtils::getFirstError($add_user));
                        }
                    }
                }
                $transaction->commit();
                VcodeForm::delIdentifyCode($this->account);
                return [$user->id, $user->username, $iconurl];
            } catch (\Exception $e) {
                $transaction->rollBack();
                throw $e;
            }
        }
        return false;
    }

    private function getUserFromSSO()
    {
        if ($this->_user === false) {
            switch ($this->type) {
                case self::EMAIL:
                    $this->_user = User::Login($this->account, $this->type, $this->password);
                    break;
                case self::MOBILE:
                    $this->_user = User::Login($this->account, $this->type, $this->password, $this->region);
                    break;
                default:
                    $this->_user = User::Login($this->access_token, $this->auth_type, $this->uuid, 86, $this->openid);
            }
        }
        return $this->_user;
    }

    /**
     * @todo 通过 region 区分手机号和邮箱
     */
    public static function getAccountType($account, $region = 'CN')
    {
        if (MUtils2::isEmail($account)) {
            return self::EMAIL;
        }

        if ('CN' === $region) {
            if (preg_match('/^1[3-9]\d{9}$/', $account)) {
                return self::MOBILE;
            } elseif (is_numeric($account)) {
                return self::UNKNOW_MOBILE;
            }
        } elseif (preg_match('/^[+-]?\d+$/', $account)) {
            $phone_util = PhoneNumberUtil::getInstance();
            try {
                $num = $phone_util->parse($account, $region);
                if ($phone_util->isValidNumber($num)) {
                    return self::MOBILE;
                } else {
                    return self::UNKNOW_MOBILE;
                }
            } catch (NumberParseException $e) {
                // PASS
                Yii::error("账号类型出错：{$e->getMessage()}，account: {$account}，region: {$region}", __METHOD__);
            }
        }

        return self::UNKNOW_ACCOUNT;
    }

    /**
     * 将 SSO 表的用户信息同步至 mowangskuser 表
     *
     * @param array $user_info 用户信息
     * @param boolean $need_update 是否需要更新 SSO 第三方账号 true 需要；false 不需要
     * 在第三方账号绑定等接口需要使用到，SSO 注册绑定成功同步 mowangskuser 表时会多更新一次第三方账号，此时接口没有传递 uid，导致更新为 null 的情况
     * @throws IntegrityException
     */
    public function retryRegist($user_info = [], bool $need_update = true)
    {
        // 重新注册 mowangskuser 表时，不需要再次验证属性
        // 因为重新注册时，需要验证的属性值都是从 SSO 获取的
        if (!$user_info) {
            throw new \Exception(Yii::t('app/error', 'params error'));
        }

        $scenario = $this->getScenario();
        if (!in_array($scenario, [self::SCENARIO_FAST_LOGIN, self::SCENARIO_FAST_AUTH_BIND])) {
            // 重新注册的账号写入 WARNING 日志
            Yii::warning('用户 ID：' . $user_info['user_id'] . ' 在主用户表不存在', __METHOD__);
        }
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $user = new Mowangskuser();
            $user->setScenario(Mowangskuser::SCENARIO_RETRYREGIST);
            $auth_type = '';
            $extra_arr = [];
            switch ($scenario) {
                case self::SCENARIO_LOGIN:
                case self::SCENARIO_FAST_LOGIN:
                case self::SCENARIO_ENSURE_REGISTER:
                    if (!in_array($this->type, [self::EMAIL, self::MOBILE])) {
                        throw new HttpException(400, Yii::t('app/error', 'Account type error'));
                    }
                    break;
                case self::SCENARIO_AUTH:
                case self::SCENARIO_FAST_AUTH_BIND:
                    break;
                default:
                    throw new HttpException(400, Yii::t('app/error', 'Login scene error'));
            }
            $user->id = $user_info['user_id'];
            $user->username = $user_info['username'];
            $user->iconid = $user_info['iconid'];
            $user->ctime = $user_info['ctime'];
            if (isset($user_info['point'])) {
                $user->point = $user_info['point'];
            }
            // 处理用户头像
            // FIXME: 用户头像需要换成协议地址
            // 举例：https://static.missevan.com/avatars/201901/03/9faf5e3fbde55ab43f163ae7b0c36b0f113055.jpeg
            [, $icon_name] = explode('/avatars/', $user_info['iconurl']);
            // 正常的注册流程中，用户未上传头像，使用默认头像的情况下，
            // 注册成功后，mowangskuser 表的 iconurl 字段值是默认头像的地址，avatar 字段是空
            // 而用户上传头像的情况下，iconurl 字段值是空字符串，avatar 字段是处理过的头像地址
            // 重新注册产生的数据保证和正常注册流程产生的数据一致
            // TODO: 之后统一处理这两个字段值
            if (Mowangskuser::ICON_NAME_DEFAULT === $icon_name) {
                // 注册时用户未上传头像，使用默认头像
                $user->iconurl = $user_info['iconurl'];
            } else {
                // 注册时用户上传头像
                $user->avatar = $icon_name;
                $user->iconurl = '';
            }
            $user->icontype = Mowangskuser::TYPE_REALITY_ICON;
            $user->iconcolor = $user_info['iconcolor'];
            if (!$user->save()) {
                throw new HttpException(400, MUtils::getFirstError($user));
            }
            // 重新注册 mowangskuser 表完成后，第三方的 uuid 和昵称存入用户附表
            if (in_array($scenario, [self::SCENARIO_AUTH, self::SCENARIO_FAST_AUTH_BIND])) {
                $auth_info = ['auth_type' => $this->auth_type, 'username' => $this->third_name];
                UserAddendum::saveAuthAccount($user_info['user_id'], $auth_info);
                [$auth_type, $extra_arr] = $this->getAuthTypeColumn();
            }
            if ($auth_type && $need_update) {
                // 更新 SSO 数据
                // TODO: 换成 token 方式更新
                Yii::$app->sso->update($user->id, array_merge([$auth_type => $this->uuid], $extra_arr));
            }
            $transaction->commit();
        } catch (IntegrityException $e) {
            $transaction->rollBack();
            Yii::error(sprintf('同步用户信息（ID: %d）失败：%s', $user_info['user_id'], $e->getMessage()),
                __METHOD__);
            if (!MUtils2::isUniqueError($e, Mowangskuser::getDb())) {
                // 抛出除唯一索引以外的异常
                throw $e;
            }
        } catch (\Exception $e) {
            $transaction->rollBack();
            Yii::error(sprintf('同步用户信息（ID: %d）失败：%s', $user_info['user_id'], $e->getMessage()),
                __METHOD__);
            throw $e;
        }
    }

    /**
     * 设置传给 SSO 注册接口的注册账号类型参数
     *
     * @throws HttpException
     * @return int
     */
    private function getOperateType()
    {
        $type = $this->auth_type === self::UNKNOW_ACCOUNT ? $this->type : $this->auth_type;
        switch ($type) {
            case self::EMAIL:
                $operate_type = SSOClient::REGIST_AUTH_EMAIL;
                break;
            case self::MOBILE:
                $operate_type = SSOClient::REGIST_AUTH_MOBILE;
                break;
            case self::QQ:
                $operate_type = SSOClient::REGIST_AUTH_QQ;
                break;
            case self::WEIBO:
                $operate_type = SSOClient::REGIST_AUTH_WEIBO;
                break;
            case self::WECHAT:
                $operate_type = SSOClient::REGIST_AUTH_WECHAT;
                break;
            case self::BILIBILI:
                $operate_type = SSOClient::REGIST_AUTH_BILIBILI;
                break;
            case self::APPLE:
                $operate_type = SSOClient::REGIST_AUTH_APPLE;
                break;
            default:
                throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        return $operate_type;
    }

    /**
     * 获取要更新的第三方 uuid 字段
     *
     * @return array 返回第三方 uuid 字段和额外信息，例：['wechatuid', ['wechatopenid' => 'test']]
     * @throws HttpException
     * @todo 因 openid 在 Mowangskuser::authLogin 方法中已传递给 sso 处理，之后额外信息 $extra_arr 的赋值可删除
     */
    public function getAuthTypeColumn()
    {
        $auth_column = '';
        $extra_arr = [];
        switch ($this->auth_type) {
            case self::QQ:
                $auth_column = 'qquid';
                $extra_arr = ['qqopenid' => $this->openid];
                break;
            case self::WEIBO:
                $auth_column = 'weibouid';
                break;
            case self::WECHAT:
                $auth_column = 'wechatuid';
                $extra_arr = ['wechatopenid' => $this->openid];
                break;
            case self::BILIBILI:
                $auth_column = 'bilibiliuid';
                break;
            case self::APPLE:
                $auth_column = 'appleuid';
                break;
            default:
                throw new HttpException(400, Yii::t('app/error', 'Incorrect third-party account type'));
        }
        return [$auth_column, $extra_arr];
    }

    /**
     * 获取 Bilibili 的信息
     *
     * @param string $auth_code 第三方账号授权 code
     * @return array
     */
    public static function getBilibiliInfo(string $auth_code)
    {
        if (!$auth_code) {
            throw new HttpException(400, Yii::t('app/error', 'params error'));
        }
        $token_info = AuthBiliBili::getAccessToken($auth_code, Yii::$app->equip->getOs())['token_info'];
        $info = AuthBiliBili::getUserInfo($token_info['access_token'], Yii::$app->equip->getOs());

        return [
            $token_info['access_token'],
            (string)$info['mid'],
            $info['name'],
            $info['face']
        ];
    }
}
