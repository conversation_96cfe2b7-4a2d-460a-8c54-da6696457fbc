<?php

namespace app\forms;

use Exception;

/**
 * Class LiveGiftContext
 * @package app\forms
 *
 * @property $transaction_id
 * @property $tax
 * @property $price
 * @property $attr
 * @property $type
 * @property $common_coins
 * @property $noble_coins
 */
class LiveGiftContext
{
    /**
     * 购买时交易 ID
     * @var int
     */
    public $transaction_id = 0;

    /**
     * 购买时的税费
     * @var float
     */
    public $tax = 0;

    /**
     * 福袋价格（单位：钻石）
     * @var int
     */
    public $price = 0;

    /**
     * 购买时的礼物类型（福袋、许愿池、红包等），同 transaction_log.attr
     * @var int
     */
    public $attr;

    /**
     * 商品大类（直播、剧集等），同 transaction_log.type
     * @var int
     */
    public $type;

    /**
     * 购买消耗的普通钻石（例：['ios' => 250, 'android' => 150]）
     * @var array
     */
    public $common_coins = [];

    /**
     * 购买消耗的贵族钻石及贵族免费钻石（例：['noble_ios' => 250, 'noble_free_android' => 150]）
     * @var array
     */
    public $noble_coins = [];

    public function __construct($context)
    {
        if (!array_key_exists('transaction_id', $context)) {
            throw new Exception(sprintf('context 缺少 transaction_id 字段'));
        }
        if (!array_key_exists('tax', $context)) {
            throw new Exception(sprintf('context 缺少 tax 字段'));
        }
        if (!array_key_exists('price', $context)) {
            throw new Exception(sprintf('context 缺少 price 字段'));
        }
        if (!array_key_exists('common_coins', $context)) {
            throw new Exception(sprintf('context 缺少 common_coin 字段'));
        }
        foreach ($context as $field => $value) {
            $this->$field = $value;
        }
    }

    private function giftConsumeRatio(GoodsForm $gift)
    {
        return $gift->getTotalPrice() / $this->price;
    }

    public function giftTax(GoodsForm $gift)
    {
        $ratio = $this->giftConsumeRatio($gift);
        return ceil($ratio * $this->tax * 100) / 100;
    }

    public function giftCostCoins(GoodsForm $gift)
    {
        $ratio = $this->giftConsumeRatio($gift);
        if (!$this->common_coins) {
            return [];
        }

        return array_map(function ($coin_num) use ($ratio) {
            // NOTICE: 不能总是满足求和和原来消费的一致
            return floor($coin_num * $ratio);
        }, $this->common_coins);
    }

    public function giftCostNobleCoins(GoodsForm $gift)
    {
        $ratio = $this->giftConsumeRatio($gift);
        if (!$this->noble_coins) {
            return [];
        }

        return array_map(function ($coin_num) use ($ratio) {
            // NOTICE: 不能总是满足求和和原来消费的一致
            return floor($coin_num * $ratio);
        }, $this->noble_coins);
    }

}
