<?php

namespace app\forms;

use app\components\auth\douyin\DouDianException;
use app\models\Drama;
use app\models\TransactionLog;
use Exception;
use Yii;
use yii\web\HttpException;

class DouDianGoodsForm
{
    /**
     * @var int 商品 ID
     */
    private $goods_id;
    /**
     * @var string 商品标题
     */
    private $goods_title;
    /**
     * @var int 商品所有者 ID
     */
    private $owner_id;
    /**
     * @var int 售价（单位：钻）
     */
    private $price;
    /**
     * @var double 分成比例
     */
    private $revenue_rate;
    /**
     * @var int 交易记录类型
     */
    private $transaction_log_type;
    /**
     * @var int 交易记录属性
     */
    private $transaction_log_attr;

    private function __construct($goods_id, $goods_title, $owner_id, $price, $revenue_rate, $transaction_log_type, $transaction_log_attr)
    {
        $this->goods_id = $goods_id;
        $this->goods_title = $goods_title;
        $this->owner_id = $owner_id;
        $this->price = $price;
        $this->revenue_rate = $revenue_rate;
        $this->transaction_log_type = $transaction_log_type;
        $this->transaction_log_attr = $transaction_log_attr;
    }

    public static function initiateByDramaId(int $drama_id, int $user_id = 0)
    {
        $drama = null;
        try {
            $drama = Drama::rpc('api/get-drama-price', ['drama_id' => $drama_id, 'user_id' => $user_id]);
        } catch (Exception $e) {
            Yii::error('doudian get drama price error: ' . $e->getMessage(), __METHOD__);
            throw new DouDianException(500, '服务器暂时维护中，请稍候再试');
        }
        if (!$drama) {
            throw new HttpException(404, '未找到对应付费剧集');
        }

        return new self($drama['drama_id'], $drama['name'], $drama['user_id'], $drama['price'], $drama['rate'],
            TransactionLog::TYPE_DRAMA, TransactionLog::ATTR_DRAMA_DOUDIAN);
    }

    public function getGoodsID()
    {
        return $this->goods_id;
    }

    public function getGoodsTitle()
    {
        return $this->goods_title;
    }

    public function getOwnerID()
    {
        return $this->owner_id;
    }

    public function getPrice()
    {
        return $this->price;
    }

    public function getRevenueRate()
    {
        return $this->revenue_rate;
    }

    public function getTransactionLogType()
    {
        return $this->transaction_log_type;
    }

    public function getTransactionLogAttr()
    {
        return $this->transaction_log_attr;
    }

}
