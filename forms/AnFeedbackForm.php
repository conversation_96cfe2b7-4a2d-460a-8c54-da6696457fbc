<?php

namespace app\forms;

use app\components\base\Model;
use app\components\util\Image;
use app\models\AnFeedback;
use app\models\AnFeedbackTicket;
use app\models\Mowangskuser;
use Yii;
use yii\helpers\Json;
use yii\web\HttpException;

class AnFeedbackForm extends Model
{
    const SCENARIO_CREATE = 'create';
    const SCENARIO_REPLY_IMAGES = 'reply_images';
    const SCENARIO_REPLY_CONTENT = 'reply_content';
    const CONTENT_MAX_LENGTH = 1000;
    // QQ 最大长度限制
    const QQ_MAX_LENGTH = 20;
    // 手机号的最大长度限制
    const MOBILE_MAX_LENGTH = 20;
    // 提醒类型：0：全部；1：BUG 反馈；2：与客服私信
    const NOTICE_TYPE_ALL = 0;
    const NOTICE_TYPE_BUG = 1;
    const NOTICE_TYPE_DIRECT = 2;

    // 手机联系方式正则（允许数字、“+”、“-”和空格）
    const MOBILE_REGEX = '/^[0-9\s\-\+]*$/';

    public $type;
    public $content;
    public $images;
    public $equip_id;
    public $client;
    public $user_id;
    public $ticket_id;
    public $timestamp;
    public $buvid;
    public $qq;
    public $mobile;

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios[self::SCENARIO_CREATE] = ['type', 'content', 'equip_id', 'client', 'timestamp', 'qq', 'mobile'];
        $scenarios[self::SCENARIO_REPLY_IMAGES] = ['images', 'content', 'ticket_id', 'equip_id', 'client', 'timestamp'];
        $scenarios[self::SCENARIO_REPLY_CONTENT] = ['content', 'ticket_id', 'equip_id', 'client', 'timestamp'];
        return $scenarios;
    }

    public function rules()
    {
        return [
            [['equip_id', 'client'], 'required'],
            [['type'], 'integer'],
            [['content'], 'required', 'message' => '问题描述不能少于 10 个字哦'],
            [['type'], 'required', 'message' => '请先选择问题类别', 'on' => self::SCENARIO_CREATE],
            [['type'], 'checkType', 'on' => self::SCENARIO_CREATE],
            [['images', 'ticket_id'], 'required', 'on' => self::SCENARIO_REPLY_IMAGES],
            [['ticket_id'], 'required', 'on' => self::SCENARIO_REPLY_CONTENT],
            [['timestamp'], 'checkTimestamp', 'on' =>
                [self::SCENARIO_CREATE, self::SCENARIO_REPLY_CONTENT, self::SCENARIO_REPLY_IMAGES]],
            [['content'], 'checkContent', 'on' =>
                [self::SCENARIO_CREATE, self::SCENARIO_REPLY_CONTENT, self::SCENARIO_REPLY_IMAGES]],
            [['qq', 'mobile'], 'checkContacts', 'on' => self::SCENARIO_CREATE],
        ];
    }

    public function checkTimestamp($attribute)
    {
        if (!$this->hasErrors()) {
            if (abs($this->$attribute - $_SERVER['REQUEST_TIME']) > HALF_HOUR) {
                $this->addError($attribute, '请校准你的时间，与真实时间偏差太大');
            }
        }
    }

    public function checkContent($attribute)
    {
        if (!$this->hasErrors()) {
            $scenario = $this->getScenario();
            if ($this->content) {
                $content = preg_replace(AnFeedback::EQUIPMENT_INFO_REGEX, '', $this->$attribute);
                $content_length = mb_strlen($content, 'utf-8');
                if (self::SCENARIO_CREATE === $scenario && self::CONTENT_MAX_LENGTH < $content_length) {
                    // 创建反馈时，反馈内容去掉小尾巴之后的长度不能超过 1000
                    $this->addError($attribute, '问题描述不能超过 ' . self::CONTENT_MAX_LENGTH . ' 个字哦');
                } elseif (self::SCENARIO_CREATE !== $scenario && AnFeedback::CONTENT_MAX_LENGTH < $content_length) {
                    // 补充反馈或回复客服时，回复内容去掉小尾巴之后的长度不能超过 255
                    $this->addError($attribute, '回复不可超过 ' . AnFeedback::CONTENT_MAX_LENGTH . ' 个字哦');
                }
            }

        }
    }

    public function checkType($attribute)
    {
        if (!$this->hasErrors()) {
            if (!in_array($this->$attribute, array_keys(AnFeedback::TYPE_FEEDBACK_ARR))) {
                $this->addError($attribute, '问题类别不存在');
            }
            $this->type = (int)$this->type;
        }
    }

    public function checkContacts()
    {
        if (!$this->hasErrors()) {
            if ($this->qq && (self::QQ_MAX_LENGTH < strlen($this->qq) || !is_numeric($this->qq) ||
                    intval(substr($this->qq, 0, 1)) === 0)) {
                return $this->addError('qq', 'QQ 号格式错误');
            }
            if ($this->mobile && (self::MOBILE_MAX_LENGTH < strlen($this->mobile) ||
                    !preg_match(self::MOBILE_REGEX, $this->mobile))) {
                return $this->addError('mobile', '手机号格式错误');
            }
        }
    }

    /**
     * 处理上传的反馈图片
     *
     * @return array|null 返回图片地址数组
     * @throws HttpException
     */
    public function uploadFeedbackImages()
    {
        if (empty($this->images)) {
            throw new HttpException(400, '图片上传失败，请重试');
        }
        $image_content = null;
        foreach ($this->images as $key => $image) {
            if (!$image->tempName) {
                throw new HttpException(400, '图片上传失败，请重试');
            }
            $pic_path = $image->tempName;
            // 缩略图文件地址
            list($mini_image_path, $extension, $image_width, $image_height) = Image::resizeImage($pic_path,
                AnFeedback::FEED_BACK_IMAGE_MIN, AnFeedback::FEED_BACK_IMAGE_MAX);
            $image_path = date('Ym/d/') . md5($pic_path) . date('His') . $extension;
            // 原文件 storage 存放地址
            $origin_save_name = 'mimages/' . $image_path;
            // 缩略图 storage 存放地址
            $mini_save_name = 'mimagesmini/' . $image_path;
            // 原图和缩略图都上传到 storage
            $origin_image_url = Yii::$app->storage->upload($pic_path, $origin_save_name, true, [], true);
            $mini_image_url = Yii::$app->storage->upload($mini_image_path, $mini_save_name, true, [], true);
            $image_content[] = [
                'origin' => $origin_image_url,
                'mini' => $mini_image_url,
                'width' => $image_width,
                'height' => $image_height,
                'index' => $key
            ];
        }
        return $image_content;
    }

    /**
     * 提交反馈信息
     *
     * @return int
     * @throws \Exception
     */
    public function createTicket()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            // 首先生成 ticket
            $ticket = new AnFeedbackTicket();
            $ticket->equip_id = $this->equip_id;
            $ticket->buvid = $this->buvid;
            $ticket->content = self::getSubContent($this->content);
            $ticket->client = $this->client;
            $ticket->type = $this->type;
            $ticket->status = AnFeedback::STATUS_STAFF_UNREAD;
            $ticket->user_id = $this->user_id;
            $contact = [];
            if ($this->qq) {
                $contact['qq'] = $this->qq;
            }
            if ($this->mobile) {
                $contact['mobile'] = $this->mobile;
            }
            if ($contact) {
                $ticket->contact = Json::encode($contact);
            }
            if (!$ticket->save()) {
                throw new HttpException(500, '提交反馈失败');
            }
            // 生成反馈记录
            $feedback = new AnFeedback();
            $feedback->equip_id = $this->equip_id;
            $feedback->buvid = $ticket->buvid;
            $feedback->client = $this->client;
            $feedback->status = AnFeedback::STATUS_STAFF_UNREAD;
            $feedback->content = $this->content;
            if (!empty($this->images)) {
                $feedback->images = $this->uploadFeedbackImages();
            }
            $feedback->user_id = $this->user_id;
            $feedback->type = $this->type;
            $feedback->ip = Yii::$app->request->userIP;
            $feedback->ticket_id = $ticket->id;
            if (!$feedback->save()) {
                throw new HttpException(500, '提交反馈失败');
            }
            $transaction->commit();
            return $ticket->id;
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * 补充反馈
     *
     * @return array
     * @throws \Exception
     */
    public function replyTicket()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $ticket_id = AnFeedback::FEEDBACK_DIRECT;
            $type = AnFeedback::TYPE_FEEDBACK_DIRECT;
            if (AnFeedback::FEEDBACK_DIRECT !== $this->ticket_id) {
                // 补充 BUG 反馈
                $condition = ['id' => $this->ticket_id];
                if ($this->user_id) {
                    $condition['user_id'] = $this->user_id;
                } else {
                    $condition['user_id'] = 0;
                    $condition['equip_id'] = $this->equip_id;
                }
                $ticket = AnFeedbackTicket::find()
                    ->where($condition)
                    ->one();
                if (!$ticket) {
                    // 只能回复该用户或该设备的 ticket
                    throw new HttpException(403, '没有权限补充该条 BUG 反馈');
                }
                $ticket->status = AnFeedback::STATUS_STAFF_UNREAD;
                if (!$ticket->save()) {
                    throw new HttpException(500, '提交反馈失败');
                }
                $ticket_id = $ticket->id;
                $type = $ticket->type;
            }
            // 生成反馈记录
            $feedback = new AnFeedback();
            $feedback->equip_id = $this->equip_id;
            $feedback->buvid = $this->buvid;
            $feedback->client = $this->client;
            $feedback->status = AnFeedback::STATUS_STAFF_UNREAD;
            if (!empty($this->images)) {
                $feedback->images = $this->uploadFeedbackImages();
            }
            $feedback->content = $this->content;
            $feedback->user_id = $this->user_id;
            $feedback->ip = Yii::$app->request->userIP;
            $feedback->type = $type;
            $feedback->ticket_id = $ticket_id;
            if (!$feedback->save()) {
                throw new HttpException(500, '提交反馈失败');
            }
            $transaction->commit();
            return self::getFeedbackInfo($feedback);
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * 获取摘要
     *
     * @param string $content 反馈内容
     * @return string
     */
    public static function getSubContent(string $content)
    {
        $CONTENT_MAX_LENGTH = 40;
        $sub_content = preg_replace(AnFeedback::EQUIPMENT_INFO_REGEX, '', $content);
        // 获取设备信息
        preg_match(AnFeedback::EQUIPMENT_INFO_REGEX, $content, $match);
        $result = mb_strlen($sub_content) > $CONTENT_MAX_LENGTH ?
            mb_substr($sub_content, 0, $CONTENT_MAX_LENGTH) . '...' . $match[0] : $content;
        return $result;
    }

    /**
     * 根据设备号获取最后一条客服回复的时间
     *
     * @param string $equip_id 设备 ID
     * @return array 示例：
     * [
     *   'bug' => ['notice' => 1, 'last_time' => 1556183514],
     *   'direct' => ['notice' => 0, 'last_time' => 0]
     * ]
     */
    public static function getNoticeByEquipId(string $equip_id)
    {
        $notice = [
            // 意见反馈入口处提醒
            // WORKAROUND: 原意见反馈已弃用，所以返回默认值兼容
            'direct' => ['notice' => 0, 'last_time' => 0],
            // BUG 反馈入口处提醒
            'bug' => AnFeedback::getFeedbackNoticeByEquipId($equip_id)
        ];
        return $notice;
    }

    /**
     * 根据用户 ID 获取最后一条客服回复的时间
     *
     * @param integer $user_id 用户 ID
     * @return array 示例：
     * [
     *   'bug' => ['notice' => 1, 'last_time' => 1556183514],
     *   'direct' => ['notice' => 0, 'last_time' => 0]
     * ]
     */
    public static function getNoticeByUserId(int $user_id)
    {
        $notice = [
            // 意见反馈入口处提醒
            // WORKAROUND: 原意见反馈已弃用，所以返回默认值兼容
            'direct' => ['notice' => 0, 'last_time' => 0],
            // BUG 反馈入口处提醒
            'bug' => AnFeedback::getFeedbackNoticeByUserId($user_id)
        ];
        return $notice;
    }

    /**
     * @param AnFeedback $feedback
     * @return array
     */
    public static function getFeedbackInfo(AnFeedback $feedback)
    {
        if (!$feedback) {
            return [];
        }
        $return = [
            'id' => $feedback->id,
            'content' => preg_replace(AnFeedback::EQUIPMENT_INFO_REGEX, '', $feedback->content),
            'create_time' => $feedback->create_time,
            'status' => $feedback->status,
            'ticket_id' => $feedback->ticket_id,
            'type' => $feedback->type,
            'username' => '一个有想法的游客 _(:з」∠)_',
            'iconurl' => Yii::$app->params['avatarUrl'] . 'icon01.png',
            'images' => []
        ];
        if (AnFeedback::FEEDBACK_DIRECT !== $feedback->ticket_id) {
            // 非客服私信类型显示反馈类型名称
            $return['type_name'] = AnFeedback::TYPE_FEEDBACK_ARR[$feedback->type];
        }
        if ($feedback->user_id) {
            $user_info = Mowangskuser::find()
                ->select('username, avatar')
                ->where('id = :id', [':id' => $feedback->user_id])->one();
            $return['username'] = $user_info->username;
            if ($user_info->avatar) {
                $return['iconurl'] = Yii::$app->params['avatarUrl'] . $user_info->avatar;
            }
        }
        if ($feedback->images) {
            $return['images'] = AnFeedback::getFeedbackImages($feedback->images);
        }
        return $return;
    }
}
