<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/20
 * Time: 14:41
 */

namespace app\forms;

use app\components\base\Model;
use app\components\util\Equipment;
use app\models\TopupMenu;
use app\models\IosReceipt;
use app\models\StatisticDownloadId;
use app\models\TopupRiskLevel;
use Exception;
use missevan\util\I18nMessage;
use Yii;

/**
 * Class RechargeForm
 * @package app\forms
 * @property-read bool isSuspicious
 */
class RechargeForm extends Model
{
    public $order_id;
    public $transaction_id;
    public $receipt;
    private $_receipt_body;
    private $_suspiciousLevel;

    /**
     * @var IosReceipt|null
     */
    private $_receiptlog;
    /**
     * @var TopupMenu|null
     */
    public $ccy;

    const SCENARIO_IOS = Equipment::iOS;

    const IOS_MAX_TOPUP = 5;

    const SUSPICIOUS_LEVEL_COMMON = 0;
    const SUSPICIOUS_LEVEL_SUSPECTED = 1;
    const SUSPICIOUS_LEVEL_SUSPECTED_IOS8 = 2;
    const SUSPICIOUS_LEVEL_BILIBILI_RISK = 3;
    const SUSPICIOUS_LEVEL_DOWNLOAD_ID = 4;
    const SUSPICIOUS_LEVEL_SUSPECTED_IOS11 = 5;

    public function rules()
    {
        return [
            [['receipt', 'transaction_id', 'ccy_id'], 'required'],
            ['receipt', 'checkReceipt'],
            [['order_id'], 'string'],
        ];
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        $scenarios[self::SCENARIO_IOS] = ['transaction_id', 'receipt', 'order_id'];
        return $scenarios;
    }

    public function checkReceipt($attribute)
    {
        if ($this->hasErrors()) {
            return $this;
        }
        $user_id = Yii::$app->user->id;
        try {
            $this->_receiptlog = IosReceipt::newRecord($user_id, $this->transaction_id, $this->receipt);

            $this->_receipt_body = IosReceipt::verifyReceipt($this->receipt);
            if ($this->_receipt_body) {
                $this->checkReceiptStatus($attribute, $this->_receipt_body)
                    ->checkReceiptBundleId($attribute, $this->_receipt_body)
                    ->checkReceiptInApp($attribute, $this->_receipt_body)
                    ->checkReceiptDownloadId($attribute, $this->_receipt_body);
                $this->_receiptlog->ignoreExceptionSave(true, 'iOS 充值日志数据记录异常');
            } else {
                $this->setLogStatus(IosReceipt::STATUS_NETWORK_ERROR);
                $this->addError($attribute, I18nMessage::error('The network seems not good, please try again later'));
            }
        } catch (Exception $e) {
            Yii::error(sprintf('iOS 充值小票验证异常：用户 %d，订单 %s，错误：%s', $user_id, $this->order_id, $e->getMessage()));
            $this->addError($attribute, I18nMessage::error('Apple Pay system is currently under maintenance'));
        }

        return $this;
    }

    private function checkReceiptStatus($attribute, $body)
    {
        if ($this->hasErrors($attribute)) {
            return $this;
        }

        $status = $body['status'] ?? IosReceipt::STATUS_INVALID;
        if (0 !== $status) {
            $this->_receiptlog->status = $status;
            $this->addError($attribute, I18nMessage::error(['The receipt of transaction is illegal, please contact customer service. Code: {code_num}', ['code_num' => '0001']]));
        }

        return $this;
    }

    public static function isValidAppPackageName(string $app_package_name)
    {
        return array_search($app_package_name, APP_PACKAGE_NAMES) !== false;
    }

    public static function getAppPackageName()
    {
        $equip = Yii::$app->equip;
        if ($equip->isFromMiMiApp()) {
            return APP_PACKAGE_NAMES['mimi'];
        }
        if ($equip::isConceptVersion()) {
            return APP_PACKAGE_NAMES['missevan_concept'];
        }
        if ($equip->isIOS()) {
            return APP_PACKAGE_NAMES['missevan_ios'];
        }

        return APP_PACKAGE_NAMES['missevan_android'];
    }

    private function checkReceiptBundleId($attribute, $body)
    {
        if ($this->hasErrors($attribute)) {
            return $this;
        }

        if (!isset($body['receipt']['bundle_id']) || !self::isValidAppPackageName($body['receipt']['bundle_id'])) {
            // iOS 系统版本小于 iOS8 时苹果返回的收据信息中 bundle_id 参数名为 bid
            // 因为我们的 iOS 客户端不兼容 iOS8 以下的系统，所以此处不做兼容
            $this->_receiptlog->status = IosReceipt::STATUS_INVALID;
            $this->addError($attribute, I18nMessage::error(['The receipt of transaction is illegal, please contact customer service. Code: {code_num}', ['code_num' => '0002']]));
        }

        return $this;
    }

    private function checkReceiptInApp($attribute, $body)
    {
        if ($this->hasErrors($attribute)) {
            return $this;
        }

        $in_app = false;
        foreach ($body['receipt']['in_app'] as $in_app_data) {
            if ($this->transaction_id === $in_app_data['transaction_id']) {
                $in_app = $in_app_data;
                break;
            }
        }

        if ($in_app) {
            $this->_receiptlog->product_id = $in_app['product_id'];
            $this->checkProductId($attribute, $in_app['product_id']);
        } else {
            $this->_receiptlog->status = IosReceipt::STATUS_INVALID;
            // 当 in_app 数据为空时，需要通知客户端进行交易凭证的刷新，详见：
            // https://stackoverflow.com/questions/36010595/itunes-reciept-validation-in-app-empty
            // 可能触发条件：
            //   1) iOS 8 系统
            //   2) 充值后将 App 卸载重装可能触发
            Yii::error(sprintf('iOS 充值小票验证异常：用户 %d，订单 %s', $this->_receiptlog->user_id, $this->order_id));
            $this->addError('in_app', I18nMessage::error('The receipt of transaction needs to be refreshed'));
        }

        return $this;
    }

    private function checkProductId($attribute, $product_id)
    {
        if ($this->hasErrors($attribute)) {
            return $this;
        }

        if ($this->ccy = TopupMenu::findProduct($product_id, TopupMenu::DEVICE_IOS)) {
            $this->_receiptlog->detail->price = $this->ccy->price;
        } else {
            $this->addError($attribute, I18nMessage::error('This type of topup does not exist'));
        }

        return $this;
    }

    private function checkReceiptDownloadId($attribute, $body)
    {
        if ($this->hasErrors($attribute)) {
            return $this;
        }

        $download_id = (int)$body['receipt']['download_id'];
        StatisticDownloadId::updateStatistics($this->_receiptlog->user_id, $download_id);
        $this->_receiptlog->detail->download_id = $download_id;

        $this->_suspiciousLevel = self::getSuspiciousLevel($this->_receiptlog->user_id, $download_id, $this->order_id, $body, $this->transaction_id);
        $this->_receiptlog->setStatusFromLevel($this->_suspiciousLevel);

        if ($this->_receiptlog->isSuspicious()) {
            Yii::error(sprintf('iOS 充值被拦截：用户 %d，订单 %s，风险等级 %d', $this->_receiptlog->user_id, $this->order_id, $this->_suspiciousLevel));
            $this->addError($attribute, I18nMessage::error('Apple Pay system is currently under maintenance'));
        }

        return $this;
    }

    /**
     * 获取用户可疑程度值
     * 0 为正常的用户
     * 1 为可疑用户
     *
     * @param integer $user_id
     * @param integer $download_id
     * @param integer $order_id
     * @param array $receipt_body
     * @param string $transaction_id
     * @return integer
     */
    public static function getSuspiciousLevel($user_id, $download_id, $order_id, $receipt_body, $transaction_id)
    {
        // 判断是否属于白名单用户
        if (Yii::$app->redis->sIsMember(KEY_IOS_DEVICE_TOPUP_USER_ID_WHITELIST, $user_id)) {
            return self::SUSPICIOUS_LEVEL_COMMON;
        }

        if (ENABLE_BILIBILI_RISK_CHECK && $order_id) {
            $level = TopupRiskLevel::checkRisk($order_id, $receipt_body, $transaction_id);
            // 订单高风险（直接拒绝）
            if (TopupRiskLevel::LEVEL_HIGH_RISK_REJECT_DIRECTLY === $level) {
                return self::SUSPICIOUS_LEVEL_BILIBILI_RISK;
            }
        }
        if (!StatisticDownloadId::checkDownloadID($user_id, $download_id)) {
            return self::SUSPICIOUS_LEVEL_DOWNLOAD_ID;
        }
        if ($download_id === 0 && version_compare(Yii::$app->equip->getOsVersion(), '12', '<')) {
            return self::SUSPICIOUS_LEVEL_SUSPECTED_IOS11;
        }

        return self::SUSPICIOUS_LEVEL_COMMON;
    }

    public function getIsSuspicious()
    {
        return $this->_suspiciousLevel > 0;
    }

    public function setLogStatus($status)
    {
        if ($this->_receiptlog) {
            $this->_receiptlog->status = $status;
            return $this->_receiptlog->ignoreExceptionSave(true, 'iOS 充值日志数据记录异常');
        }
        return false;
    }

    public function getReceiptBody()
    {
        return $this->_receipt_body;
    }

}
