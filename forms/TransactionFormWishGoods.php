<?php

namespace app\forms;

use app\models\TransactionLog;

final class TransactionFormWishGoods extends TransactionFormLive
{

    public function scenarios()
    {
        return array_merge(parent::scenarios(), [
            self::SCENARIO_BUY => ['from_id', 'gift_id', 'price', 'num', 'title', 'noble', 'live_open_log_id'],
        ]);
    }

    public function beforeBuy()
    {
        if (!parent::beforeBuy()) {
            return false;
        }

        $this->attr = TransactionLog::ATTR_LIVE_WISH_POOL;
        return true;
    }

}
