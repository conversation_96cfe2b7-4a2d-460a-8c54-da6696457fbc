<?php

namespace app\forms;

use app\models\Balance;
use app\models\Drama;
use app\models\PayAccount;
use app\models\PayAccounts;
use app\models\TransactionItemsLog;
use app\models\TransactionLog;
use Exception;
use missevan\util\Logger;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\helpers\Json;
use yii\web\HttpException;

final class TransactionFormRedeemDrama extends TransactionForm
{
    const SCENARIO_DEFAULT = 'default';
    const SCENARIO_LUCKY_BAG = 'lucky-bag';

    /**
     * @var int
     */
    public $context_transaction_id;

    /**
     * @var int
     */
    public $operator_id;

    /**
     * @var array
     */
    private $drama_info;

    /**
     * @var TransactionLog
     */
    private $context_transaction_log;

    public function scenarios()
    {
        return [
            self::SCENARIO_DEFAULT => ['from_id', 'gift_id', 'operator_id'],
            self::SCENARIO_LUCKY_BAG => ['from_id', 'gift_id', 'operator_id', 'context_transaction_id'],
        ];
    }

    public function rules()
    {
        $rules = parent::rules();
        $rules[] = ['operator_id', 'integer'];
        $rules[] = ['context_transaction_id', 'checkContextTransaction', 'on' => self::SCENARIO_LUCKY_BAG];
        return $rules;
    }

    public function checkGoods($attribute)
    {
        $drama_id = $this->$attribute;
        switch ($this->getScenario()) {
            case self::SCENARIO_DEFAULT:
                $drama = Drama::rpc('drama/get-dramas', ['drama_id' => $this->gift_id]);
                if (empty($drama)) {
                    return $this->addError($attribute, '剧集不存在');
                }
                if (($drama[0]['refined'] & Drama::REFINED_SPECIAL) === 0) {
                    return $this->addError($attribute, '该剧为非可兑换剧集');
                }
                $this->drama_info = ['drama_id' => $drama[0]['id'], 'name' => $drama[0]['name'], 'user_id' => $drama[0]['user_id']];
                break;
            case self::SCENARIO_LUCKY_BAG:
                // 业务侧会判断剧集是否合约期下架等状态，此处不进行额外判断
                $drama = Drama::rpc('api/get-drama-price', ['drama_id' => $drama_id, 'user_id' => $this->from_id]);
                if (!$drama) {
                    return $this->addError($attribute, '剧集不存在或为免费剧');
                }
                if ($drama['type'] !== Drama::PAY_TYPE_DRAMA) {
                    return $this->addError($attribute, '剧集非整剧付费类型');
                }
                $this->drama_info = ['drama_id' => $drama['drama_id'], 'name' => $drama['name'], 'user_id' => $drama['user_id'], 'rate' => $drama['rate']];
                break;
        }

    }

    public function checkContextTransaction($attribute)
    {
        $context_transaction_id = $this->$attribute;
        $this->context_transaction_log = TransactionLog::findOne([
            'id' => $context_transaction_id,
            'type' => [TransactionLog::TYPE_LIVE, TransactionLog::TYPE_GUILD_LIVE],
            'attr' => TransactionLog::ATTR_LIVE_BUY_LUCKY_BAG,
            'status' => TransactionLog::STATUS_SUCCESS,
        ]);
        if (!$this->context_transaction_log) {
            $this->addError($attribute, '未找到对应的交易记录');
        }
    }

    /**
     * 兑换剧集
     * 注：必须在事务中使用
     *
     * @throws Exception
     */
    public function redeem()
    {
        if (!$this->validate()) {
            return false;
        }

        if (TransactionLog::isPurchased($this->from_id, $this->drama_info['drama_id'], TransactionLog::TYPE_DRAMA)) {
            throw new HttpException(403, '该M号已拥有本剧，无法兑换！');
        }

        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(
            LOCK_REDEEM_DRAMA,
            $this->getScenario(),
            $this->getScenario() === self::SCENARIO_DEFAULT ? $this->from_id : $this->context_transaction_log->id,
            $this->drama_info['drama_id']
        );

        try {
            if (!$redis->lock($lock, ONE_MINUTE)) {
                Logger::warningf('剧集正在兑换中, user_id[%d], drama_id[%d]', [$this->from_id, $this->drama_info['drama_id']], __METHOD__);
                throw new HttpException(403, '正在处理中');
            }

            $transaction_log = new TransactionLog([
                'from_id' => $this->from_id,
                'to_id' => $this->drama_info['user_id'],
                'gift_id' => $this->drama_info['drama_id'],
                'title' => $this->drama_info['name'],
                'all_coin' => 0,
                'rate' => $this->drama_info['rate'] ?? 0,
                'type' => TransactionLog::TYPE_DRAMA,
                'status' => TransactionLog::STATUS_SUCCESS,
            ]);
            $this->setTransactionLogDetail($transaction_log);
            $trade_detail = $transaction_log->trade_detail;
            if ($this->operator_id) {
                $trade_detail['more']['operator_id'] = $this->operator_id;
            }

            switch ($this->getScenario()) {
                case self::SCENARIO_DEFAULT:
                    $transaction_log->income = 0;
                    $transaction_log->tax = 0;
                    $transaction_log->attr = TransactionLog::ATTR_DRAMA_REDEEM;
                    $transaction_log->trade_detail = $trade_detail;
                    if (!$transaction_log->save()) {
                        throw new Exception(MUtils2::getFirstError($transaction_log));
                    }
                    break;
                case self::SCENARIO_LUCKY_BAG:
                    /**
                     * @var TransactionItemsLog $itemslog
                     */
                    $itemslog = TransactionItemsLog::find()
                        ->where(['tid' => $this->context_transaction_log->id, 'status' => TransactionLog::STATUS_SUCCESS, 'goods_id' => $this->drama_info['drama_id']])
                        ->andWhere('goods_num > 0')
                        ->andWhere('JSON_EXTRACT(more, "$.transaction_type") = :transaction_type', [':transaction_type' => GoodsForm::TRANSACTION_TYPE_DRAMA])
                        ->one();
                    if (!$itemslog) {
                        throw new HttpException(404, '未找到匹配的交易记录');
                    }
                    // 兑换剧集的钻石价值
                    $pay_accounts = new PayAccounts(PayAccounts::legacyBalanceToAccounts($itemslog->more_detail['common_coins'], $itemslog->user_id));
                    $pay_accounts->cost($itemslog->goods_price);
                    $tax = $transaction_log->calcTax($pay_accounts);
                    $common_coins_redeem = [];
                    foreach ($pay_accounts->getChangeAccounts() as $acc) {
                        $common_coins_redeem[$acc->getCoinField()] = ($common_coins_redeem[$acc->getCoinField()] ?? 0) + $acc->consume_amount;
                    }

                    $transaction_log->tax = $tax;
                    $transaction_log->attr = TransactionLog::ATTR_DRAMA_LUCKY_BAG;
                    $transaction_log->income = $itemslog->goods_price / DIAMOND_EXRATE;

                    $trade_detail['more']['context_transaction_id'] = $this->context_transaction_log->id;
                    $trade_detail['more']['context_transaction_items_log_id'] = $itemslog->id;
                    $trade_detail['more']['common_coins'] = $common_coins_redeem;
                    $transaction_log->trade_detail = $trade_detail;
                    if (!$transaction_log->save()) {
                        throw new Exception(MUtils2::getFirstError($transaction_log));
                    }

                    $itemslog_more_detail = $itemslog->more_detail;
                    if (!array_key_exists('redeem_transaction_ids', $itemslog_more_detail)) {
                        $itemslog_more_detail['redeem_transaction_ids'] = [];
                    }
                    $itemslog_more_detail['redeem_transaction_ids'][] = $transaction_log->id;
                    $itemslog_more_detail['redeem_goods_num'] = 1 + ($itemslog_more_detail['redeem_goods_num'] ?? 0);
                    foreach ($common_coins_redeem as $coin_type => $coin_num) {
                        $itemslog_more_detail['common_coins'][$coin_type] = $itemslog_more_detail['common_coins'][$coin_type] - $coin_num;
                        $itemslog_more_detail['common_coins_redeem'][$coin_type] = $coin_num + ($itemslog_more_detail['common_coins_redeem'][$coin_type] ?? 0);
                    }

                    $updated_num = TransactionItemsLog::updateAll([
                        'goods_num' => $itemslog->goods_num - 1,
                        'more' => Json::encode($itemslog_more_detail),
                        'modified_time' => $_SERVER['REQUEST_TIME'],
                    ], ['id' => $itemslog->id, 'goods_num' => $itemslog->goods_num, 'modified_time' => $itemslog->modified_time]);
                    if ($updated_num <= 0) {
                        throw new HttpException(500, '正在处理中');
                    }
                    break;
                default:
                    throw new Exception('不支持的场景 ' . $this->getScenario());
            }

            return $transaction_log->id;
        } catch (Exception $e) {
            throw $e;
        } finally {
            $redis->unlock($lock);
        }
    }
}
