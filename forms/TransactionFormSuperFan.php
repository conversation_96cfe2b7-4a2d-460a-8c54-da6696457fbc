<?php

namespace app\forms;

use app\models\Balance;
use app\models\GuildBalance;
use app\models\PayAccount;
use app\models\PayAccounts;
use app\models\TransactionLog;
use Exception;
use yii\web\HttpException;
use Yii;

final class TransactionFormSuperFan extends TransactionForm
{
    const SCENARIO_BUY = 'buy';

    public $renew = 0;

    public function __construct($scenario = self::SCENARIO_BUY)
    {
        parent::__construct(['scenario' => $scenario]);
    }

    public function scenarios()
    {
        return array_merge(
            parent::scenarios(),
            [self::SCENARIO_BUY => ['from_id', 'to_id', 'gift_id', 'price', 'title', 'renew', 'num', 'live_open_log_id']]
        );
    }

    public function rules()
    {
        return array_merge(parent::rules(), [
            ['renew', 'checkRenew'],
        ]);
    }

    public function checkRenew()
    {
        switch ($this->renew) {
            case 0:
                $this->attr = TransactionLog::ATTR_LIVE_REGISTER_SUPER_FAN;
                break;
            case 1:
                $this->attr = TransactionLog::ATTR_LIVE_RENEWAL_SUPER_FAN;
                break;
            default:
                $this->addError('renew', '参数错误');
        }
    }

    public function buy()
    {
        if (self::SCENARIO_BUY !== $this->getScenario()) {
            throw new Exception('场景错误');
        }

        if (!$this->validate()) {
            return false;
        }
        if ($this->from_id === $this->to_id) {
            throw new HttpException(403, '不能开通自己房间的超粉哦~', *********);
        }

        /**
         * @var $seller Balance|GuildBalance
         */
        [$seller, $rate] = $this->getSellerAndRate($this->to_id);
        $db_transaction = TransactionLog::getDb()->beginTransaction();
        try {
            [$transaction_id, $balance, $tax, $costs] = $this->generateTransaction($this->price, $rate, TransactionLog::STATUS_SUCCESS);
            $this->calculateLiveRevenue($seller, $this->price, $tax, $rate);
            $db_transaction->commit();
        } catch (Exception $e) {
            $db_transaction->rollBack();
            throw $e;
        }

        return [
            'transaction_id' => $transaction_id,
            'balance' => $balance,
            'live_noble_balance' => PayAccounts::getAccounts($this->from_id, PayAccount::SCOPE_LIVE)->getTotalBalance(),
            'price' => $this->price,
        ];
    }

}
