<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/8/9
 * Time: 17:57
 */

namespace app\forms;

use app\components\base\Model;
use app\components\util\MUtils;
use app\components\util\Tools;
use Exception;
use missevan\util\MUtils as MUtils2;
use yii\web\HttpException;
use Yii;

class VcodeForm extends Model
{
    // 发送验证码针对 IP 的限制次数
    const LIMIT_IP_COUNT = 10;
    // 发送验证码针对用设备的限制次数
    const LIMIT_EQUIP_ID_COUNT = 100;

    public $account;
    // 0 为注册， 1为忘记密码 3
    public $post_type;
    public $region = 'CN';
    public $scene;

    private $type = LoginForm::UNKNOW_ACCOUNT;

    // 0：注册；1：修改密码；2：绑定新手机号；3：验证旧手机号；4：绑定新邮箱；
    // 5：验证旧邮箱；6：异地登录验证；7：支付；8：第三方绑定；9：忘记密码
    // 10：验证码登录；11：绑定手机号（第三方账号）；
    const TYPE_REGISTER = 0;
    const TYPE_CHANGE_PASSWORD = 1;
    const TYPE_BIND_MOBILE = 2;
    const TYPE_CONFIRM_MOBILE = 3;
    const TYPE_BIND_EMAIL = 4;
    const TYPE_CONFIRM_EMAIL = 5;
    const TYPE_REMOTE_LOGIN = 6;
    const TYPE_PAYMENT = 7;
    const TYPE_BIND_THIRD = 8;
    const TYPE_FORGET_PASSWORD = 9;
    const TYPE_SMS_LOGIN = 10;
    const TYPE_AUTH_BIND_MOBILE = 11;

    // 验证码 scene
    const SCENE_REGISTER = 'register';
    const SCENE_CHANGE_PASSWORD = 'change_password';
    const SCENE_BIND_MOBILE = 'bind_mobile';
    const SCENE_CHANGE_MOBILE = 'change_mobile';
    const SCENE_BIND_EMAIL = 'bind_email';
    const SCENE_CHANGE_EMAIL = 'change_email';
    const SCENE_FORGET_PASSWORD = 'forget_password';
    const SCENE_SMS_LOGIN = 'sms_login';
    const SCENE_AUTH_BIND_MOBILE = 'auth_bind_mobile';

    public function rules()
    {
        return [
            [['account', 'post_type', 'region'], 'required'],
            ['account', 'checkAccount']
        ];
    }

    public function checkAccountExists($attribute)
    {
        $type = LoginForm::getAccountType($this->account, $this->region);
        $exists = NULL;
        switch ($type) {
            case LoginForm::EMAIL:
                if (self::TYPE_BIND_EMAIL === $this->post_type && !MUtils2::isSupportEmail($this->account)) {
                    // 绑定新邮箱时过滤特殊的邮箱后缀名
                    $this->addError($attribute, Yii::t('app/error', 'Current email address is not supported'));
                }
                $exists = Yii::$app->sso->checkEmailExists($this->account);
                break;
            case LoginForm::MOBILE:
                if ($mobile_info = MUtils2::getMobileNumber($this->account, $this->region)) {
                    $this->account = (int)$mobile_info->mobile_num;
                    $this->region = (int)$mobile_info->region_num;
                    $exists = Yii::$app->sso->checkMobileExists($this->account, $this->region);
                } else {
                    $this->addError($attribute, Yii::t('app/error', 'Please input correct phone number'));
                }
                break;
            case LoginForm::UNKNOW_MOBILE:
                $this->addError($attribute, Yii::t('app/error', 'Please input correct phone number'));
                break;
            default:
                $this->addError($attribute, Yii::t('app/error', 'This account type is not supported currently'));
        }
        $this->type = $type;
        return $exists;
    }

    public function checkAccount($attribute)
    {
        $redis = Yii::$app->redis;
        // 短信验证码时间间隔 key
        $vcode_interval_key = $redis->generateKey(VCODE_GET_INTERVAL_KEY, $this->account);
        // 规定时间间隔内请求发送则提示发送验证码间隔过短
        if ($redis->get($vcode_interval_key)) {
            throw new HttpException(429,
                Yii::t('app/error', 'Verification code sending interval is too short'), *********);
        }
        // 判断验证码是否达到发送上限
        $eid = Yii::$app->equip->getEquipId();
        $ip = Yii::$app->request->userIP;
        $count_eid = $redis->generateKey(COUNTER_VCODE_EID, $eid);
        $count_ip = $redis->generateKey(COUNTER_VCODE_IP, $ip);
        if (!$redis->counter($count_eid, HALF_HOUR, self::LIMIT_IP_COUNT) ||
                !$redis->counter($count_ip, HALF_DAY, self::LIMIT_EQUIP_ID_COUNT)) {
            throw new HttpException(429,
                Yii::t('app/error', "You've reached the maximum number of attempts", *********));
        }

        switch ($this->post_type) {
            // 0：注册；1：修改密码；2：绑定新手机号；3：验证旧手机号；4：绑定新邮箱；
            // 5：验证旧邮箱；6：异地登录验证；7：支付；8：第三方绑定；9：忘记密码；
            // 10：验证码登录；11：绑定手机号（第三方账号）；
            case self::TYPE_REGISTER:
                $exists = $this->checkAccountExists($attribute);
                if ($exists) {
                    throw new HttpException(400, Yii::t('app/error', 'Account already exists'));
                }
                break;
            case self::TYPE_BIND_MOBILE:
                // region 会在函数内部使用
                $exists = $this->checkAccountExists($attribute);
                if ($exists) {
                    throw new HttpException(400,
                        Yii::t('app/error', 'The phone number has been used, please use another phone number'));
                }
                break;
            case self::TYPE_BIND_EMAIL:
                $exists = $this->checkAccountExists($attribute);
                if ($exists) {
                    throw new HttpException(400,
                        Yii::t('app/error', 'The email has been used, please use another email'));
                }
                break;
            case self::TYPE_CONFIRM_MOBILE:
            case self::TYPE_CONFIRM_EMAIL:
            case self::TYPE_CHANGE_PASSWORD:
                if (MUtils2::isEmail($this->account)) {
                    $this->type = LoginForm::EMAIL;
                } else {
                    $this->type = LoginForm::MOBILE;
                }
                break;
            case self::TYPE_FORGET_PASSWORD:
                $exists = $this->checkAccountExists($attribute);
                if (!$exists) {
                    throw new HttpException(400,
                        Yii::t('app/error', 'The account has not been registered, no need to retrieve the password'));
                }
                break;
            case self::TYPE_REMOTE_LOGIN:
            case self::TYPE_PAYMENT:
            case self::TYPE_BIND_THIRD:
                $exists = $this->checkAccountExists($attribute);
                if (!$exists) {
                    throw new HttpException(400, Yii::t('app/error', 'Account does not exist'));
                }
                break;
            case self::TYPE_SMS_LOGIN:
            case self::TYPE_AUTH_BIND_MOBILE:
                // 验证码登录和绑定手机号（第三方账号）不需要对返回值做处理抛出异常，正常发送短信
                $this->checkAccountExists($attribute);
                // WORKAROUND: 安卓客户端传参有点问题，临时阻断一下
                if ($this->post_type === VcodeForm::TYPE_SMS_LOGIN && $this->type === LoginForm::EMAIL) {
                    throw new HttpException(400, Yii::t('app/error', 'params error'));
                }
                break;
            default:
                $this->addError($attribute, Yii::t('app/error', 'Not supported yet'));
                break;
        }
    }

    public function sendVcode()
    {
        switch ($this->type) {
            case LoginForm::EMAIL:
                $this->sendEmail();
                break;
            case LoginForm::MOBILE:
                $this->sendSms();
                break;
            default:
                throw new HttpException(400,
                    Yii::t('app/error', 'Invalid email or mobile phone number'));
        }
    }

    public function sendEmail()
    {
        $scene = $this->getSendScene();
        $email_content = [
            'code' => $this->getIdentifyCode(),
            'confirm_content' => self::emailContent($scene),
        ];
        $emails = [
            'to' => $this->account,
            'subject' => Yii::t('app/base', '[MissEvan] Verification Code'),
            'template' => 'vcode',
            'payload' => $email_content,
        ];
        return Yii::$app->tools->sendNotification($emails, Tools::SEND_EMAIL);
    }

    public function sendSms()
    {
        $redis = Yii::$app->redis;
        $lock_account = $redis->generateKey(VCODE . 'lock', $this->account);
        if (!$redis->lock($lock_account, 60)) {
            throw new HttpException(403, Yii::t('app/error', 'Could not send verification code continuously'));
        }

        // 设置发送验证码的时间间隔
        $vcode_interval_key = $redis->generateKey(VCODE_GET_INTERVAL_KEY, $this->account);
        $redis->setex($vcode_interval_key, VCODE_GET_INTERVAL, 1);

        $scene = $this->getSendScene();
        $mobile = '+' . $this->region . $this->account;
        $smses = [
            'to' => $mobile,
            'region_code' => $this->region,
            'scene' => $scene,
            'code' => $this->getIdentifyCode(),
        ];
        return Yii::$app->tools->sendNotification($smses, Tools::SEND_SMS);
    }

    private function getIdentifyKey()
    {
        if (LoginForm::MOBILE === $this->type) {
            $identify_info_key = $this->region . $this->account;
        } else {
            $identify_info_key = $this->account;
        }
        return $identify_info_key;
    }

    private function getIdentifyCode()
    {
        // 生成随机的 6 位验证码
        $identify = MUtils::randomKeys(6);

        // 将验证码存入缓存
        $redis = Yii::$app->redis;
        $post_objective_key = $redis->generateKey(POST_OBJECTIVE_TYPE, $this->getIdentifyKey());
        // 1：用于修改密码的验证码；2：用于绑定新手机的验证码；4：用于绑定新邮箱的验证码
        $redis->setex($post_objective_key, TEN_MINUTE, $this->post_type);
        $key = $redis->generateKey(VCODE, $this->account);
        $redis->setex($key, TEN_MINUTE, $identify);
        $vcode_timer_key = $redis->generateKey(VCODE_CHECK_TIMER, $this->account);
        // 获取验证码后，设置比对 check 验证码次数初始值为 0
        $redis->setex($vcode_timer_key, TEN_MINUTE, 0);
        return $identify;
    }

    public static function checkIdentifyCode($account, $code)
    {
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(VCODE, $account);
        $check_code = $redis->get($key);
        //每比对check一次则变量加一，避免用户短时间内多次重复比对而试出验证码，
        //如果超过指定次数（默认为20）则删除之前的验证码，需重新获取
        $vcode_timer_key = $redis->generateKey(VCODE_CHECK_TIMER, $account);
        if ($redis->incr($vcode_timer_key) > VCODE_CHECK_TIMES) {
            $redis->del($key);
            return false;
        }
        if ($check_code) {
            return $code == $check_code;
        }
        return false;
    }

    public static function checkObjective($post_objective_type, $objective_account)
    {
        $redis = Yii::$app->redis;
        $key = $redis->generateKey(POST_OBJECTIVE_TYPE, $objective_account);
        if ($post_objective_type !== (int)$redis->get($key)) {
            return false;
        }
        return true;
    }

    /**
     * 发送验证码的场景
     */
    protected function getSendScene()
    {
        if ($this->scene) {
            return $this->scene;
        }
        switch ($this->post_type) {
            case self::TYPE_REGISTER:
                $scene = self::SCENE_REGISTER;
                break;
            case self::TYPE_CHANGE_PASSWORD:
                $scene = self::SCENE_CHANGE_PASSWORD;
                break;
            case self::TYPE_BIND_MOBILE:
                $scene = self::SCENE_BIND_MOBILE;
                break;
            case self::TYPE_CONFIRM_MOBILE:
                $scene = self::SCENE_CHANGE_MOBILE;
                break;
            case self::TYPE_BIND_EMAIL:
                $scene = self::SCENE_BIND_EMAIL;
                break;
            case self::TYPE_CONFIRM_EMAIL:
                $scene = self::SCENE_CHANGE_EMAIL;
                break;
            case self::TYPE_BIND_THIRD:
                $scene = self::SCENE_BIND_MOBILE;
                break;
            case self::TYPE_FORGET_PASSWORD:
                $scene = self::SCENE_FORGET_PASSWORD;
                break;
            case self::TYPE_SMS_LOGIN:
                $scene = self::SCENE_SMS_LOGIN;
                break;
            case self::TYPE_AUTH_BIND_MOBILE:
                $scene = self::SCENE_AUTH_BIND_MOBILE;
                break;
            default:
                return false;
        }
        return $scene;
    }

    /**
     * 根据场景获取邮件内容
     *
     * @param string $scene 邮件场景
     * @return string
     * @throws Exception
     */
    protected static function emailContent(string $scene): string
    {
        switch ($scene) {
            case 'register':
                $content = Yii::t('app/base', 'You are registering an account');
                break;
            case 'bind_mobile':
                $content = Yii::t('app/base', 'You are binding a new phone number');
                break;
            case 'bind_email':
                $content = Yii::t('app/base', 'You are binding a new email address');
                break;
            case 'change_password':
                $content = Yii::t('app/base', 'You are changing your password');
                break;
            case 'change_mobile':
                // 绑定新手机号和换绑手机号用的是同一个 type，这边给个模糊的提示
                $content = Yii::t('app/base', 'You are binding the new phone number');
                break;
            case 'change_email':
                $content = Yii::t('app/base', 'You are changing the bound email address');
                break;
            case 'forget_password':
                $content = Yii::t('app/base', 'You are trying to reset your password');
                break;
            default:
                Yii::error('无法找到对应的邮件场景：' . $scene . '，account: ' . Yii::$app->request->post('account') . '，post_type: ' . (int)Yii::$app->request->post('post_type'), __METHOD__);
                throw new Exception(Yii::t('app/error', 'Could not find the corresponding email scenario'));
        }
        return $content;
    }

    /**
     * 删除验证码
     * @param string $account 用户手机号或者邮箱
     */
    public static function delIdentifyCode($account)
    {
        $redis = Yii::$app->redis;
        $vcode_key = $redis->generateKey(VCODE, $account);
        $redis->del($vcode_key);
    }

    public static function delObjectiveKey($objective_account)
    {
        $redis = Yii::$app->redis;
        $objective_key = $redis->generateKey(POST_OBJECTIVE_TYPE, $objective_account);
        $redis->del($objective_key);
    }

    /**
     * 删除输入错误密码限制计数器
     * @param string $account 用户手机号或者邮箱
     */
    public static function delCounterLogin($account)
    {
        $redis = Yii::$app->redis;
        $lock_login_key = $redis->generateKey(COUNTER_LOGIN, $account);
        $redis->del($lock_login_key);
    }
}
