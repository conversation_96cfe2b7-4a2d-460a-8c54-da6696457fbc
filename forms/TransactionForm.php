<?php
/**
 * Created by PhpStorm.
 * User: tomcao
 * Date: 2017/7/24
 * Time: 18:28
 */

namespace app\forms;

use app\components\base\Model;
use app\components\models\BalanceInterface;
use app\components\util\Equipment;
use app\components\util\MUtils;
use app\models\Balance;
use app\models\Drama;
use app\models\DramaBoughtDetailLog;
use app\models\Gift;
use app\models\GuildBalance;
use app\models\GuildLiveContract;
use app\models\Live;
use app\models\LuckyGift;
use app\models\MGameAibfCharacter;
use app\models\Mowangskuser;
use app\models\MUserVip;
use app\models\PayAccount;
use app\models\PayAccountPurchaseDetail;
use app\models\PayAccounts;
use app\models\TransactionItemsLog;
use app\models\TransactionLog;
use app\models\TransactionLogDetail;
use app\models\TransactionSoundLog;
use app\models\UserNoble;
use Exception;
use missevan\util\HttpExceptionI18n;
use missevan\util\I18nMessage;
use Yii;
use yii\helpers\Json;
use yii\web\HttpException;

class TransactionForm extends Model
{
    // 能享受会员折扣价的最小钻石数（原价）（单位：钻）
    const MIN_VIP_DISCOUNT_PRICE = 5;

    /**
     * @var int 交易买家 ID
     */
    public $from_id;

    /**
     * @var int 交易卖家 ID
     */
    public $to_id;

    /**
     * @var boolean 贵族开通/续费时，true 表示开通，false 表示续费；购买礼物时表示能否使用贵族钻石，true 表示能，false 表示否
     */
    public $noble = false;

    /**
     * @var int 对应的商品 id
     */
    public $gift_id;

    /**
     * @var int 购买的商品数量
     */
    public $num = 1;

    /**
     * @var int 价格（单位为钻石）
     */
    public $price;

    /**
     * @var int 交易 id
     */
    public $transaction_id;
    public $sure;

    public $title;
    public $type;

    public $attr = 0;

    // 语音包作品 ID
    public $work_id;
    // 直播公会 ID
    public $guild_id;

    // 订单数量，当 type = 10 时，为 m_weekly_memberships.id
    public $suborders_num;

    // 是否为已经退款的交易
    private $is_refund;

    /**
     * 终端
     *
     * @var UserContext
     */
    public $user_context;

    /**
     * 直播场次
     *
     * @var string
     */
    public $live_open_log_id;

    // 购买者是否是会员
    public $is_vip_buyer = false;

    // 商品会员折扣信息
    public $vip_discount_info;

    /**
     * 额外信息
     *
     * @var array
     */
    public $more;

    /**
     * @var LuckyGift|null
     */
    private $luck_gift = null;

    // 整剧付费类型
    const TYPE_DRAMA = 2;
    // 单集付费类型
    const TYPE_EPISODES = 1;

    const SCENARIO_LUCKY_GIFT = 'lucky-gift';
    const SCENARIO_GIFT = 'gift';
    const SCENARIO_BUY = 'buy';
    const SCENARIO_FREEZE = 'freeze';
    const SCENARIO_CONFIRM = 'sure';
    const SCENARIO_BOYFRIEND = 'boy-friend';
    const SCENARIO_THINGS = 'things';
    const SCENARIO_VOICE = 'voice';
    const SCENARIO_REWARD = 'reward';
    const SCENARIO_NOBLE = 'noble';
    const SCENARIO_REBATE_GIFT = 'rebate_gift';

    // 会员折扣类型（1：会员购剧折扣）
    const VIP_DISCOUNT_TYPE_BUY_DRAMA = 1;

    public function rules()
    {
        return [
            [['from_id', 'title', 'type', 'to_id', 'gift_id', 'price',
                'rebate', 'sure', 'transaction_id', 'noble'], 'required'],
            [['num', 'price', 'rebate', 'transaction_id', 'attr', 'expire_time', 'suborders_num'], 'integer'],
            [['sure', 'noble'], 'boolean'],
            [['live_open_log_id'], 'string'],
            ['num', 'checkNum'],
            ['price', 'checkPrice'],
            ['from_id', 'checkBuyer'],
            ['gift_id', 'checkGoods'],
            ['more', 'checkMore'],
        ];
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        // 送出幸运签时，gift_id 表示宝箱 id，price 为幸运签价格（钻石）、num 为幸运签数量，noble 为可否使用贵族钻石
        // title 为具体的宝箱礼物，例：魔王王冠（幸运签）
        $scenarios[self::SCENARIO_LUCKY_GIFT] = ['from_id', 'to_id', 'gift_id', 'title', 'price', 'num', 'noble', 'live_open_log_id', 'more'];
        $scenarios[self::SCENARIO_GIFT] = ['from_id', 'to_id', 'gift_id', 'num', 'noble', 'live_open_log_id'];
        $scenarios[self::SCENARIO_FREEZE] = ['from_id', 'to_id', 'price', 'noble', 'live_open_log_id'];
        $scenarios[self::SCENARIO_CONFIRM] = ['transaction_id', 'sure', 'to_id'];
        $scenarios[self::SCENARIO_BOYFRIEND] = ['from_id', 'gift_id'];
        $scenarios[self::SCENARIO_BUY] = ['from_id', 'gift_id'];
        $scenarios[self::SCENARIO_THINGS] = ['from_id', 'title', 'type', 'price', 'gift_id', 'attr', 'suborders_num'];
        $scenarios[self::SCENARIO_VOICE] = ['from_id', 'gift_id'];
        $scenarios[self::SCENARIO_REWARD] = ['from_id', 'gift_id'];
        $scenarios[self::SCENARIO_REBATE_GIFT] = ['from_id', 'to_id', 'gift_id', 'num', 'live_open_log_id'];
        return $scenarios;
    }

    public function checkNum($attribute)
    {
        if ($this->$attribute <= 0) {
            $this->addError($attribute, '数量必须为正整数');
        }
    }

    public function checkPrice($attribute)
    {
        switch ($this->getScenario()) {
            case self::SCENARIO_FREEZE:
                $THRESHOLD = 30;
                $error = "付费问答最少需付 $THRESHOLD 个钻石";
                break;
            default:
                $THRESHOLD = 1;
                $error = '价格不能小于 ' . $THRESHOLD;
        }
        if ((int)$this->$attribute < $THRESHOLD) {
            return $this->addError($attribute, $error);
        }
    }

    public function checkBuyer($attribute)
    {
        /**
         * @var $user Mowangskuser
         */
        $user = Mowangskuser::find()
            ->select('confirm')
            ->where(['id' => $this->$attribute])
            ->one();
        if (!$user) {
            return $this->addError($attribute, I18nMessage::error('User does not exist'));
        }
        if ($user->isBanTopupAndConsume()) {
            return $this->addError($attribute, I18nMessage::error('Your account is temporarily suspended by the system and cannot top up and consume'));
        }

        $this->is_vip_buyer = MUserVip::isVipUser($this->$attribute);
    }

    public function checkMore($attribute)
    {
        if (!is_array($this->$attribute)) {
            $this->addError($attribute, 'The attribute must be an array.');
        }
    }

    public function isBuyerAndSellerSame()
    {
        return $this->from_id === $this->to_id;
    }

    public function checkGoods($attribute)
    {
        // PASS
    }

    public function generateTransaction(int $price, float $rate, int $status, int|array $scope = PayAccount::SCOPE_COMMON, callable|null $func = null)
    {
        $pay_accounts = PayAccounts::getAccounts($this->from_id, $scope);
        $balance = $pay_accounts->getTotalBalance();
        if ($balance < $price) {
            Yii::info(
                sprintf('balance not enough: user_id=%d, balance=%d, price=%d', $this->from_id, $balance, $price),
                __METHOD__
            );
            throw new HttpExceptionI18n(400, 'Insufficient balance, please top up first', *********);
        }

        $remaining = $pay_accounts->cost($price);
        if ($remaining !== 0) {
            throw new HttpException(400, '您的余额不足，请充值后购买', *********);
        }

        $common_coin_costs = $noble_coin_costs = [];
        if ($pay_accounts->hasChanged()) {
            $pay_accounts->updateAccounts();
            foreach ($pay_accounts->getChangeAccounts() as $acc) {
                /**
                 * @var PayAccount $acc
                 */
                $coin_field = $acc->getCoinField();
                if (str_starts_with($coin_field, 'noble')) {
                    $noble_coin_costs[$coin_field] = ($noble_coin_costs[$coin_field] ?? 0) + $acc->consume_amount;
                } else {
                    $common_coin_costs[$coin_field] = ($common_coin_costs[$coin_field] ?? 0) + $acc->consume_amount;
                }
            }
        }

        $transaction_log = new TransactionLog();
        $transaction_log->load($this->toArray(), '');
        $transaction_log->setAttributes([
            'all_coin' => $price,
            'rate' => $rate,
            'tax' => $transaction_log->calcTax($pay_accounts),
            'income' => Balance::profitUnitConversion($price, Balance::CONVERT_DIAMOND_TO_YUAN),
            'status' => $status,
        ]);
        foreach ($common_coin_costs as $key => $cost) {
            $coin_key = $key . '_coin';
            $transaction_log->$coin_key += $cost;
        }
        $this->setTransactionLogDetail($transaction_log);
        if (is_callable($func)) {
            $func($transaction_log, $pay_accounts);
        }
        if (!$transaction_log->save()) {
            throw new HttpException(500, MUtils::getFirstError($transaction_log));
        }

        if ($pay_accounts->hasChanged()) {
            $detail_status = [
                TransactionLog::STATUS_UNDONE => PayAccountPurchaseDetail::STATUS_PENDING,
                TransactionLog::STATUS_CANCEL => PayAccountPurchaseDetail::STATUS_CANCEL,
                TransactionLog::STATUS_SUCCESS => PayAccountPurchaseDetail::STATUS_CONFIRM,
            ];
            $pay_accounts->updatePurchaseDetails($transaction_log, $detail_status[$status]);
        }

        return [
            (int)$transaction_log->id,
            PayAccounts::getAccounts($this->from_id, PayAccount::SCOPE_COMMON)->getBalance(),
            $transaction_log->tax,
            $common_coin_costs,
            $noble_coin_costs,
        ];
    }

    /**
     * 计算直播收益
     *
     * @param BalanceInterface $seller Balance 或 GuildBalance
     * @param int $price 价格（单位：钻石）
     * @param float $tax 税费（单位：元）
     * @param float $revenue_rate 分成比例
     *
     * @throws HttpException
     */
    protected function calculateLiveRevenue(BalanceInterface $seller, int $price, $tax, $revenue_rate)
    {
        if ($revenue_rate <= 0 || $seller->isGuild()) {
            return 0;
        }

        $profit_in_fen = self::calcProfit($price / DIAMOND_EXRATE, $tax, $revenue_rate);
        $seller->updateCounters([
            'new_live_profit' => $profit_in_fen,
            'new_all_live_profit' => $profit_in_fen,
        ]);
    }

    protected function setTransactionLogDetail(TransactionLog $trade_log)
    {
        $trade_detail = $trade_log->trade_detail;
        if ($this->user_context) {
            $trade_detail += $this->user_context->toArray();
        }
        if (is_array($this->more) && $this->more) {
            $trade_detail['more'] = $this->more;
        }
        if ($this->live_open_log_id) {
            $trade_detail['more'] = array_merge($trade_detail['more'] ?? [], [
                'live_open_log_id' => $this->live_open_log_id,
            ]);
        }
        if ($this->vip_discount_info) {
             $trade_detail['more']['vip_discount'] = $this->vip_discount_info;
        }
        if ($trade_detail) {
            $trade_log->trade_detail = $trade_detail;
        }

        switch ($trade_log->type) {
            case TransactionLog::TYPE_GUILD_LIVE:
                $trade_log->guild_id = $this->guild_id;
            case TransactionLog::TYPE_LIVE:
                // 付费问答创建时 confirm_time 指定为 0
                if ($trade_log->gift_id === 0) {
                    $trade_log->confirm_time = 0;
                }
                break;
            case TransactionLog::TYPE_SOUND:
            case TransactionLog::TYPE_EVENT723:
            case TransactionLog::TYPE_MAGIC:
                $trade_log->suborders_num = $this->suborders_num;
                break;
            case TransactionLog::TYPE_CARD_PACKAGE:
            case TransactionLog::TYPE_DRAW_CARD:
            case TransactionLog::TYPE_OMIKUJI:
                $trade_log->suborders_num = $this->work_id;
                break;
        }
    }

    public function buyLuckyGift()
    {
        if (self::SCENARIO_LUCKY_GIFT !== $this->getScenario()) {
            throw new Exception('场景错误');
        }
        if (!$this->validate()) {
            return false;
        }
        if ($this->from_id === $this->to_id) {
            throw new HttpException(403, '不可以打赏自己', *********);
        }
        $this->luck_gift = LuckyGift::newInstance(
            $this->gift_id, $this->price, $this->num,
            (int)Yii::$app->request->post('income')
        );
        $this->attr = TransactionLog::ATTR_LIVE_LUCKY_GIFT;
        $price = $this->luck_gift->getTotalPrice();

        /**
         * @var $seller Balance|GuildBalance
         */
        [$seller, $rate] = $this->getSellerAndRate();

        $db_transaction = TransactionLog::getDb()->beginTransaction();
        try {
            $scope = $this->getPayAccountScope();
            [$transaction_id, $balance, $tax, $costs] = $this->generateTransaction($price, $rate, TransactionLog::STATUS_SUCCESS, $scope, function (TransactionLog $transaction_log, PayAccounts $pay_acc) use ($price) {
                // 计算幸运签开箱礼物的主播收益、渠道费
                $ratio = $this->luck_gift->getCreatorIncome() / Balance::profitUnitConversion($price, Balance::CONVERT_DIAMOND_TO_FEN);
                $change_accounts = array_map(function ($acc) use ($ratio) {
                    $a = new PayAccount();
                    $a->type = $acc->type;
                    $a->consume_amount = $ratio * $acc->consume_amount;
                    return $a;
                }, $pay_acc->getChangeAccounts());
                $transaction_log->tax = $transaction_log->calcTax($change_accounts);
                $transaction_log->income = Balance::profitUnitConversion($this->luck_gift->getCreatorIncome(), Balance::CONVERT_FEN_TO_YUAN);
            });
            $price = Balance::profitUnitConversion($this->luck_gift->getCreatorIncome(), Balance::CONVERT_FEN_TO_DIAMOND);
            $this->calculateLiveRevenue($seller, $price, $tax, $rate);

            $db_transaction->commit();
            $live_noble_balance = PayAccounts::getAccounts($this->from_id, PayAccount::SCOPE_LIVE)->getTotalBalance();
            return [
                'transaction_id' => $transaction_id,
                'balance' => $balance,
                'live_noble_balance' => $live_noble_balance,
                'price' => $price,
            ];
        } catch (Exception $e) {
            $db_transaction->rollBack();
            throw $e;
        }
    }

    public function buyGift($run_validation = true)
    {
        if (self::SCENARIO_GIFT !== $this->getScenario()) {
            throw new Exception('场景错误');
        }
        if ($run_validation) {
            // 触发 rules 规则，避免出现直接使用绕过规则的情况
            if (!$this->validate()) return false;
        }
        if ($this->from_id === $this->to_id) throw new HttpException(403, '不可以打赏自己', *********);

        $gift = Gift::findOne(['id' => $this->gift_id, 'type' => Gift::TYPE_LIVE_GIFT]);
        if (!$gift) throw new HttpException(404, '该礼物不存在', *********);
        $this->title = $gift->name;
        $price = $gift->price * $this->num;

        // 确认收益者和分成比例 (该功能有副作用，会修改成员变量 guild_id)
        /**
         * @var $seller Balance|GuildBalance
         */
        [$seller, $rate] = $this->getSellerAndRate();

        $db_transaction = TransactionLog::getDb()->beginTransaction();
        try {
            $scope = $this->getPayAccountScope();
            [$transaction_id, $balance, $tax, $costs] = $this->generateTransaction($price, $rate, TransactionLog::STATUS_SUCCESS, $scope);

            // 计算主播收益
            $this->calculateLiveRevenue($seller, $price, $tax, $rate);

            $db_transaction->commit();
            $live_noble_balance = PayAccounts::getAccounts($this->from_id, PayAccount::SCOPE_LIVE)->getTotalBalance();
            return [
                'transaction_id' => $transaction_id,
                'balance' => $balance,
                'live_noble_balance' => $live_noble_balance,
                'price' => $price,
            ];
        } catch (Exception $e) {
            $db_transaction->rollBack();
            throw $e;
        }
    }

    public function freeze($run_validation = true)
    {
        if (self::SCENARIO_FREEZE !== $this->getScenario()) {
            throw new Exception('场景错误');
        }
        if ($run_validation) {
            // 触发 rules 规则，避免出现直接使用绕过规则的情况
            if (!$this->validate()) return false;
        }
        if ($this->from_id === $this->to_id) throw new HttpException(403, '不可以打赏自己', *********);

        $this->gift_id = 0;
        $username = Mowangskuser::find()->select('username')
            ->where(['id' => $this->to_id])->scalar();
        $this->title = $username;
        $price = $this->price;

        // 确认收益者和分成比例 (该功能有副作用，会修改成员变量 guild_id)
        [$seller, $rate] = $this->getSellerAndRate($this->to_id);
        $db_transaction = TransactionLog::getDb()->beginTransaction();
        try {
            $scope = $this->getPayAccountScope();
            [$transaction_id, $balance] = $this->generateTransaction($price, $rate, TransactionLog::STATUS_UNDONE, $scope);

            $db_transaction->commit();
            $live_noble_balance = PayAccounts::getAccounts($this->from_id, PayAccount::SCOPE_LIVE)->getTotalBalance();
            return [
                'transaction_id' => $transaction_id,
                'balance' => $balance,
                'live_noble_balance' => $live_noble_balance,
                'price' => $price,
            ];
        } catch (Exception $e) {
            $db_transaction->rollBack();
            throw $e;
        }
    }

    public function confirmTransaction($run_validation = true)
    {
        if (self::SCENARIO_CONFIRM !== $this->getScenario()) {
            throw new Exception('场景错误');
        }
        if ($run_validation) {
            // 触发 rules 规则，避免出现直接使用绕过规则的情况
            if (!$this->validate()) return false;
        }
        $this->type = TransactionLog::TYPE_LIVE;
        $connection = TransactionLog::getDb();
        $db_transaction = $connection->beginTransaction();
        try {
            /**
             * @var TransactionLog $t_log
             */
            $t_log = TransactionLog::findOneForUpdate(
                'id = :id AND gift_id = 0 AND `type` IN (:type_live, :type_guild) AND status = :status',
                [
                    ':id' => $this->transaction_id,
                    ':type_live' => TransactionLog::TYPE_LIVE,
                    ':type_guild' => TransactionLog::TYPE_GUILD_LIVE,
                    ':status' => TransactionLog::STATUS_UNDONE,
                ]);
            if (!$t_log) throw new HttpException(404, '该问答不存在', *********);
            if ($t_log->to_id !== $this->to_id) {
                throw new HttpException(403, '你没有回答或取消该问答的权限', *********);
            }

            if ($this->sure) {
                if ($t_log->type === TransactionLog::TYPE_LIVE) {
                    /**
                     * @var $seller Balance
                     */
                    $seller = Balance::getByPk($t_log->to_id);
                    $this->calculateLiveRevenue($seller, $t_log->income * DIAMOND_EXRATE, $t_log->tax, $t_log->rate);
                }
                $t_log->status = TransactionLog::STATUS_SUCCESS;
                PayAccountPurchaseDetail::confirm($t_log);
            } else {
                // 取消交易，退回用户余额
                $t_log->status = TransactionLog::STATUS_CANCEL;
                PayAccountPurchaseDetail::cancel($t_log);
            }
            // 付费问答确认
            $t_log->confirm_time = $_SERVER['REQUEST_TIME'];
            $t_log->save();
            $db_transaction->commit();
            return [
                'transaction_id' => (int)$t_log->id,
                'price' => $t_log->all_coin,
            ];
        } catch (Exception $e) {
            if ($db_transaction) {
                $db_transaction->rollBack();
            }
            throw $e;
        }
    }

    /**
     * 购买微信男友
     *
     * @return array 订单 ID、用户钻石余额及购买价格组成的数组
     * @throws HttpException 男友角色不存在、重复购买或余额不足时抛出异常
     */
    public function buyBoyFriend($run_validation = true)
    {
        if (self::SCENARIO_BOYFRIEND !== $this->getScenario()) {
            throw new Exception('场景错误');
        }
        if ($run_validation) {
            // 触发 rules 规则，避免出现直接使用绕过规则的情况
            if (!$this->validate()) return false;
        }
        $character = MGameAibfCharacter::findOne($this->gift_id);
        if (!$character) {
            throw new HttpException(404, '角色不存在', *********);
        }
        $this->to_id = 0;  // 收益方为猫耳平台，to_id 默认为 0
        $this->title = '微信男友 - ' . $character->name;
        $this->type = TransactionLog::TYPE_BOYFRIEND;
        $price = $character->price;
        return $this->transaction($price, 1, function () {
            return TransactionLog::find()->where([
                'from_id' => $this->from_id,
                'gift_id' => $this->gift_id,
                'type' => TransactionLog::TYPE_BOYFRIEND,
                'status' => TransactionLog::STATUS_SUCCESS,
            ])->exists();
        });
    }

    public function buyThings($run_validation = true)
    {
        if (self::SCENARIO_THINGS !== $this->getScenario()) {
            throw new Exception('场景错误');
        }
        if ($run_validation) {
            // 触发 rules 规则，避免出现直接使用绕过规则的情况
            if (!$this->validate()) return false;
        }
        $this->to_id = 0;
        return $this->transaction($this->price);
    }

    public function buyDrama($origin, $run_validation = true)
    {
        if (self::SCENARIO_BUY !== $this->getScenario()) {
            throw new Exception(Yii::t('app/error', 'scene error'));
        }
        if ($run_validation) {
            // 触发 rules 规则，避免出现直接使用绕过规则的情况
            if (!$this->validate()) return false;
        }

        // 返回值示例：['drama_id' => 1, 'user_id' => 2, 'price' => 3, 'rate' => 0, 'name' => '233',
        // 'type' => 2, 'is_lossless' => false, 'discount' => ['original_price' => 1, 'discount' => '3 折']]
        $content = Drama::rpc('api/get-drama-price', ['drama_id' => $this->gift_id, 'user_id' => $this->from_id]);
        if (0 !== $content['price']) {
            if ($content['type'] !== self::TYPE_DRAMA) {
                throw new HttpException(403, Yii::t('app/error', 'Payment type of goods is error'), 200360005);
            }
            if (key_exists('is_lossless', $content) && $content['is_lossless']
                    && Equipment::isAppOlderThan('4.5.4') && (int)Yii::$app->equip->osVersion < 11) {
                // WORKAROUND: 此处判断是否存在 is_lossless 键名用于兼容 app 项目先上线的情况，
                // 相关兼容在 rpc 接口项目上线之后可删除
                // WORKAROUND：iOS 系统版本小于 11 且客户端版本小于 4.5.4 时（无法播放）需要阻断购买无损音质剧集
                if (Equipment::isAppOlderThan('4.5.2')) {
                    // 客户端版本小于 4.5.2 时，通过 toast 提示错误，返回对应错误码
                    throw new HttpException(403, 'iOS 11 以下设备暂不支持 flac 格式剧集的播放和购买',
                        200360008);
                }
                // 剧集为无损音质且版本小于 4.5.4（大于 4.5.1）时，通过弹窗提示错误
                throw new HttpException(403,
                    '您当前的系统版本较低，iOS 版本 11 以下设备暂不支持无损 flac 格式的音频播放，请升级系统完成购买',
                    100010018);
            }
            $this->to_id = $content['user_id'];
            $this->title = $content['name'];
            if (!is_null($content['discount'] ?? null)) {
                $this->title = $this->title . '--' . $content['discount']['discount'];
            }
            $vip_discount = $content['vip_discount'] ?? null;
            $price = self::getDramaPrice($this->is_vip_buyer, Drama::PAY_TYPE_DRAMA, $content['price'], $vip_discount);
            if (self::hasVipDiscount($this->is_vip_buyer, $content['price'], $vip_discount)) {
                // 会员折扣价购买时，记录会员折扣信息（原价、折扣和购买数量）
                $this->vip_discount_info = [
                    'discount_price' => $price,
                    'original_price' => $content['price'],
                    'rate' => $vip_discount['rate'],
                    'num' => 1,
                    'type' => self::VIP_DISCOUNT_TYPE_BUY_DRAMA,
                ];
            }
            $result = $this->transaction($price, $content['rate'],
                function () {
                    /**
                     * @var $transaction_log TransactionLog
                     */
                    $transaction_log = TransactionLog::find()
                        ->select('status, attr')
                        ->where([
                            'from_id' => $this->from_id,
                            'gift_id' => $this->gift_id,
                            'type' => TransactionLog::TYPE_DRAMA,
                        ])->orderBy('id DESC')->limit(1)->one();
                    // 未购买过
                    if (!$transaction_log) {
                        return false;
                    }
                    // 已购买过：曾经代充被取消交易情况
                    if ($transaction_log->status === TransactionLog::STATUS_ILLEGAL_TOPUP) {
                        return false;
                    }
                    // 已购买过：退还钻石/人民币情况
                    if (in_array($transaction_log->status,
                        [TransactionLog::STATUS_REFUND, TransactionLog::STATUS_REFUND_DIAMOND])) {
                        $this->is_refund = true;
                    }
                    if ($transaction_log->isSupportRePurchaseAfterRefund()) {
                        return false;
                    }
                    return true;
                });
        } else {
            throw new HttpException(404, Yii::t('app/error', 'The drama to pay does not exist'), 200360005);
        }
        if ($result) {
            // 添加购买整剧 databus 消息
            DramaBoughtDetailLog::addLog($this->from_id, $this->gift_id, $origin);
        }
        return $result;
    }

    /**
     * 钻石抽卡
     *
     * @param $run_validation 是否需要触发 validate 验证
     * @return array 消费记录 ID、钻石余额、花费钻石数组成的数组
     */
    public function buyCard(int $price, $run_validation = true)
    {
        if (self::SCENARIO_VOICE !== $this->getScenario()) {
            throw new Exception('场景错误');
        }
        if ($run_validation) {
            // 触发 rules 规则，避免出现直接使用绕过规则的情况
            if (!$this->validate()) {
                throw new HttpException(400, MUtils::getFirstError($this));
            }
        }
        $this->to_id = 0;
        return $this->transaction($price);
    }

    public function buyDramaEpisodes($sound_ids, $origin)
    {
        $sound_details = Drama::rpc('api/get-episodes-details',
            ['sound_ids' => $sound_ids, $this->from_id]);
        // 判断剧集的付费类型
        if (self::TYPE_EPISODES !== $sound_details['drama_pay_type']) {
            throw new HttpException(404,
                Yii::t('app/error', 'The drama could not paid by a single episode'), 200360005);
        }
        if (!$sound_details['sound_price']) {
            throw new HttpException(404, Yii::t('app/error', 'The audio is no need to pay'), 200360005);
        }

        $drama_id = $this->gift_id;
        if ($drama_id !== (int)$sound_details['drama_id']) {
            throw new HttpException(404, Yii::t('app/error', 'The drama does not contain this episode'), 200360005);
        }
        if (key_exists('is_lossless', $sound_details) && $sound_details['is_lossless']
                && Equipment::isAppOlderThan('4.5.4') && (int)Yii::$app->equip->osVersion < 11) {
            // WORKAROUND: 此处判断是否存在 is_lossless 键名用于兼容 app 项目先上线的情况，
            // 相关兼容在 rpc 接口项目上线之后可删除
            // WORKAROUND：iOS 系统版本小于 11 且客户端版本小于 4.5.4 时（无法播放）需要阻断购买无损音质剧集
            if (Equipment::isAppOlderThan('4.5.2')) {
                // 客户端版本小于 4.5.2 时，通过 toast 提示错误，返回对应错误码
                throw new HttpException(403, 'iOS 11 以下设备暂不支持 flac 格式剧集的播放和购买',
                    200360008);
            }
            // 剧集为无损音质且版本小于 4.5.4（大于 4.5.1）时，通过弹窗提示错误
            throw new HttpException(403,
                '您当前的系统版本较低，iOS 版本 11 以下设备暂不支持无损 flac 格式的音频播放，请升级系统完成购买',
                100010018);
        }
        $sound_num = count($sound_ids);
        $this->to_id = $sound_details['sound_user'];
        $this->title = $sound_details['drama_name'];
        $original_total_price = $sound_details['sound_price'] * $sound_num;
        if (!is_null($sound_details['discount'] ?? null)) {
            $this->title = $this->title . '--' . $sound_details['discount']['discount'];
        }
        $user_id = $this->from_id;
        $vip_discount = $sound_details['vip_discount'] ?? null;
        $total_price = self::getDramaPrice($this->is_vip_buyer, Drama::PAY_TYPE_EPISODE, $original_total_price, $vip_discount);
        if (self::hasVipDiscount($this->is_vip_buyer, $total_price, $vip_discount)) {
            // 会员折扣价购买时，记录会员折扣信息（原价、折扣和购买数量）
            $this->vip_discount_info = [
                'discount_price' => $total_price,
                'original_price' => $original_total_price,
                'rate' => $vip_discount['rate'],
                'num' => $sound_num,
                'type' => self::VIP_DISCOUNT_TYPE_BUY_DRAMA,
            ];
        }
        $result = $this->transaction($total_price, $sound_details['sound_price_rate'], function () use ($sound_ids, $user_id) {
                /**
                 * @var TransactionSoundLog[] $trade_logs
                 */
                $trade_logs = TransactionSoundLog::find()
                    ->select('status, attr')
                    ->andWhere([
                        'sound_id' => $sound_ids,
                        'user_id' => $user_id,
                        'status' => [
                            TransactionLog::STATUS_SUCCESS,
                            TransactionLog::STATUS_REFUND,
                            TransactionLog::STATUS_REFUND_DIAMOND,
                        ],
                    ])->all();
                if (in_array(TransactionLog::STATUS_SUCCESS, array_column($trade_logs, 'status'))) {
                    return true;
                }
                foreach ($trade_logs as $log) {
                    if (!$log->isSupportRePurchaseAfterRefund()) {
                        $this->is_refund = true;
                        return true;
                    }
                }

                return false;
            }, $sound_ids, $drama_id);
        if ($result) {
            // 添加购买单集 databus 消息
            DramaBoughtDetailLog::addLog($user_id, $drama_id, $origin, Drama::PAY_TYPE_EPISODE,
                ['sound_ids' => $sound_ids]);
        }
        return $result;
    }

    /**
     * 是否满足会员折扣条件
     *
     * @param bool $is_vip_user 是否是会员用户
     * @param int $total_price 总价
     * @param array|null $vip_discount 剧集的会员折扣信息
     * @return bool
     */
    public static function hasVipDiscount(bool $is_vip_user, int $total_price, ?array $vip_discount): bool
    {
        return $is_vip_user && $total_price >= self::MIN_VIP_DISCOUNT_PRICE && $vip_discount;
    }

    /**
     * 获取购买剧集实际支付价格
     *
     * @param bool $is_vip_user 是否是会员用户
     * @param int $pay_type 付费方式（1：单集付费；2：整剧付费）
     * @param int $total_price 总价
     * @param array|null $vip_discount 剧集的会员折扣信息
     * @return int 剧集实际支付价格
     * @throws Exception
     */
    public static function getDramaPrice(bool $is_vip_user, int $pay_type, int $total_price, ?array $vip_discount)
    {
        if (!self::hasVipDiscount($is_vip_user, $total_price, $vip_discount)) {
            return $total_price;
        }
        // 用户是会员且总价大于等于能享受会员折扣价的最小钻石数时，价格为会员折扣价格
        switch ($pay_type) {
            case Drama::PAY_TYPE_EPISODE:
                return self::getDramaEpisodesVipPrice($total_price, $vip_discount['rate']);
            case Drama::PAY_TYPE_DRAMA:
                return $vip_discount['price'];
            default:
                throw new Exception('未知的付费方式');
        }
    }

    /**
     * 获取单集会员折扣价格
     *
     * @param int $total_price 单集总价格 (单位：钻)
     * @param float $vip_discount_rate 单集折扣，e.g. 0.5 表示打 5 折
     * @return int 单集会员折扣价格，保底 1 钻
     */
    public static function getDramaEpisodesVipPrice(int $total_price, float $vip_discount_rate)
    {
        // 单集付费时，先计算总价，再计算总价的折扣价格（折扣后若出现小数点，则用户仅需支付整数部分金额）
        $vip_price = (int)floor($total_price * $vip_discount_rate);
        // 保底 1 钻
        return $vip_price <= 0 ? 1 : $vip_price;
    }

    private function transaction($price, $rate = 1, callable $is_paid_fn = null, $sound_ids = [], $drama_id = null)
    {
        $db_transaction = TransactionLog::getDb()->beginTransaction();
        try {
            if ($is_paid_fn && $is_paid_fn()) {
                // 因为只读数据库可能有延迟，所以在事务开启时进行查询购买记录的操作
                throw new HttpException(403, $this->getErrorMessage(), *********);
            }
            if (TransactionLog::TYPE_SOUND === $this->type) {
                $this->suborders_num = count($sound_ids);
            }
            [$transaction_id, $balance] = $this->generateTransaction($price, $rate, TransactionLog::STATUS_SUCCESS, PayAccount::SCOPE_COMMON);
            if ($drama_id && $sound_ids) {
                TransactionSoundLog::buyDramaEpisodes($sound_ids, $drama_id, $this->from_id, $transaction_id);
            }
            $db_transaction->commit();
            return [
                'transaction_id' => $transaction_id,
                'balance' => $balance,
                'price' => $price,
            ];
        } catch (Exception $e) {
            $db_transaction->rollBack();
            throw $e;
        }
    }

    public function buyPackage()
    {
        $this->to_id = 0;
        $this->type = TransactionLog::TYPE_CARD_PACKAGE;
        return $this->transaction($this->price, 1, function () {
            return TransactionLog::find()->where([
                'from_id' => $this->from_id,
                'gift_id' => $this->gift_id,
                'type' => TransactionLog::TYPE_CARD_PACKAGE,
                'status' => TransactionLog::STATUS_SUCCESS,
            ])->exists();
        });
    }

    public function reward($run_validation = true)
    {
        if (self::SCENARIO_REWARD !== $this->getScenario()) {
            throw new Exception(I18nMessage::error('scene error'));
        }
        if ($run_validation) {
            // 触发 rules 规则，避免出现直接使用绕过规则的情况
            if (!$this->validate()) return false;
        }
        $memcache = Yii::$app->memcache;
        $key = MUtils::generateCacheKey(KEY_REWARD_DRAMA_DATA, $this->gift_id);
        if (!$content = json_decode($memcache->get($key), true)) {
            $content = Drama::rpc('api/get-drama-price', ['drama_id' => $this->gift_id]);
            $memcache->set($key, Json::encode($content), HALF_HOUR);
        }
        if (!$content || !$content['rewardable']) {
            throw new HttpExceptionI18n(403, 'This drama does not support reward');
        }
        $this->to_id = $content['user_id'];
        $this->title = $content['name'];
        return $this->transaction($this->price, $content['rate']);
    }

    /**
     * 获取反馈给用户的错误信息
     *
     * @return string
     */
    private function getErrorMessage()
    {
        switch ($this->type) {
            case TransactionLog::TYPE_SOUND:
                $error_msg = $this->is_refund
                    ? Yii::t('app/error', 'You have received refund for this drama, and the drama could not be purchased twice')
                    : Yii::t('app/error', 'You have purchased this episode');
                break;
            case TransactionLog::TYPE_DRAMA:
                $error_msg = $this->is_refund
                    ? Yii::t('app/error', 'You have received refund for this drama, and the drama could not be purchased twice')
                    : Yii::t('app/error', 'You have purchased this drama');
                break;
            case TransactionLog::TYPE_BOYFRIEND:
                $error_msg = '您已经购买过该男友';
                break;
            case TransactionLog::TYPE_CARD_PACKAGE:
                $error_msg = '您已经购买过该季包';
                break;
            default:
                $error_msg = Yii::t('app/error', 'You have purchased this goods');
        }
        return $error_msg;
    }

    /**
     * @deprecated 该方法会更改 type、guild_id 相关值，使用 getCreatorTradeInfo 代替
     * @return array
     */
    protected function getSellerAndRate()
    {
        // 购买贵族时，如果不在主播房间，将不进行分成处理
        if (self::SCENARIO_NOBLE === $this->getScenario() && 0 === $this->to_id) {
            $seller = Balance::getByPk($this->to_id);
            $this->type = TransactionLog::TYPE_LIVE;
            return [$seller, 0];
        }
        $guild_id = (int)GuildLiveContract::find()
            ->select('guild_id')
            ->where([
                'live_id' => $this->to_id,
                'status' => GuildLiveContract::CONTRACT_EFFECTIVE,
            ])->limit(1)->scalar();
        if ($guild_id) {
            // 若主播属于公会，则直播收益属于公会
            $seller = GuildBalance::getByPk($guild_id);
            $rate = (float)$seller->rate;
            $this->type = TransactionLog::TYPE_GUILD_LIVE;
        } else {
            $seller = Balance::getByPk($this->to_id);
            $rate = Live::getRate($this->to_id);
            $this->type = TransactionLog::TYPE_LIVE;
        }
        $this->guild_id = $guild_id;
        return [$seller, $rate];
    }

    protected function getCreatorTradeInfo(int $creator_id)
    {
        // 购买贵族时，如果不在主播房间，将不进行分成处理
        // （目前会记录到 user_id = 0 的 balance 记录里）
        if (self::SCENARIO_NOBLE === $this->getScenario() && 0 === $creator_id) {
            return [
                'seller' => Balance::getByPk($creator_id),
                'rate' => 0,
                'type' => TransactionLog::TYPE_LIVE,
                'guild_id' => 0,
            ];
        }

        $guild_id = GuildLiveContract::getGuildId($creator_id);
        if ($guild_id) {
            // 若主播属于公会，则直播收益属于公会
            $seller = GuildBalance::getByPk($guild_id);
            $rate = (float)$seller->rate;
            $type = TransactionLog::TYPE_GUILD_LIVE;
        } else {
            $seller = Balance::getByPk($creator_id);
            $rate = Live::getRate($creator_id);
            $type = TransactionLog::TYPE_LIVE;
        }

        return [
            'seller' => $seller,
            'rate' => $rate,
            'type' => $type,
            'guild_id' => $guild_id,
        ];
    }

    /**
     * 主播最终可以获取的收益数
     *
     * @param float $income 收入（单位为元）
     * @param float $tax 平台费
     * @param float $rate 分成比例
     * @return int 主播收益（分）
     * @throws HttpException
     */
    public static function calcProfit(float $income, float $tax, float $rate)
    {
        $profit = ($income - $tax) * $rate;
        return Balance::profitUnitConversion($profit, Balance::CONVERT_YUAN_TO_FEN);
    }

    public function getPayAccountScope()
    {
        if (!$this->noble) {
            return PayAccount::SCOPE_COMMON;
        }
        if (!$this->from_id) {
            return [PayAccount::SCOPE_LIVE, PayAccount::SCOPE_COMMON];
        }
        ['is_valid' => $is_noble_valid] = UserNoble::isNobleValid($this->from_id);
        if ($is_noble_valid) {
            return [PayAccount::SCOPE_LIVE, PayAccount::SCOPE_COMMON];
        }
        return PayAccount::SCOPE_COMMON;
    }

}
