<?php

namespace app\forms;

use app\models\Gift;
use app\models\MMessageAssign;
use app\models\PayAccount;
use app\models\PayAccountPurchaseDetail;
use app\models\PayAccounts;
use app\models\TransactionItemsLog;
use app\models\TransactionLog;
use app\models\TransactionLogDetail;
use Exception;
use missevan\util\MUtils as MUtils2;
use yii\helpers\Json;
use yii\web\HttpException;
use Yii;

final class TransactionFormLiveToken extends TransactionFormLive
{
    /**
     * @var array|GoodsForm[]
     */
    public $goods = [];

    /**
     * @var int[]
     */
    public $transaction_ids;

    /**
     * @var TransactionLog[]
     */
    private $transaction_logs;

    public function scenarios()
    {
        return array_merge(parent::scenarios(), [
            self::SCENARIO_BUY => ['from_id', 'gift_id', 'price', 'num', 'title', 'live_open_log_id', 'noble'],
            self::SCENARIO_REFUND => ['transaction_ids', 'goods', 'noble'],
        ]);
    }

    public function rules()
    {
        return array_merge(parent::rules(), [
            ['goods', 'checkGoods'],
            [['transaction_ids'], 'checkTransactionIDs'],
        ]);
    }

    public function checkGoods($attribute)
    {
        switch ($attribute) {
            case 'gift_id':
                $gift_info = Gift::findOne([
                    'id' => $this->$attribute,
                    'type' => [Gift::TYPE_LIVE_TOKEN_REFUNDABLE, Gift::TYPE_LIVE_TOKEN_UNREFUNDABLE]
                ]);
                if (!$gift_info) {
                    return $this->addError($attribute, '未找到对应的礼物 ID');
                }
                if ($this->price !== $gift_info->price * $this->num) {
                    return $this->addError($attribute, '价格错误');
                }
                $this->title = $gift_info->name;
                break;
            case 'goods':
                if (empty($this->$attribute)) {
                    $this->$attribute = [];
                    return;
                }
                $goods = [];
                foreach ($this->$attribute as $i => $good) {
                    $goods_form = new GoodsForm(GoodsForm::SCENARIO_LIVE_TOKEN);
                    $goods_form->load($good, '');
                    if (!$goods_form->validate()) {
                        return $this->addError($attribute, MUtils2::getFirstError($goods_form));
                    }
                    // 如果多个同样的商品，则合并为一项，便于后面退款处理
                    $goods_form->num += ($goods[$goods_form->id]['num'] ?? 0);
                    $goods[$goods_form->id] = $goods_form;
                }
                if (count($goods) > 1) {
                    return $this->addError($attribute, '只支持同一种礼物类型');
                }
                $this->$attribute = array_values($goods);
                $gift_info = Gift::findOne(['id' => $this->$attribute[0]['id'], 'type' => Gift::TYPE_LIVE_TOKEN_REFUNDABLE]);
                if (!$gift_info) {
                    return $this->addError($attribute, '未找到对应的礼物 ID');
                }
                if ($this->$attribute[0]['price'] !== $gift_info->price) {
                    return $this->addError($attribute, '价格错误');
                }
                $this->$attribute[0]['title'] = $gift_info->name;
                break;
        }
    }

    public function checkTransactionIDs($attribute)
    {
        $transaction_ids = $this->$attribute;
        if (empty($transaction_ids) || !MUtils2::isUintArr($transaction_ids)) {
            return $this->addError($attribute, '交易 ID 列表');
        }
        $this->transaction_logs = TransactionLog::findAll([
            'id' => $transaction_ids,
            'status' => TransactionLog::STATUS_UNDONE,
            'attr' => TransactionLog::ATTR_LIVE_TOKEN,
            'type' => [TransactionLog::TYPE_LIVE, TransactionLog::TYPE_GUILD_LIVE],
        ]);
        if (empty($this->transaction_logs)) {
            return $this->addError($attribute, '未找到待确认的交易订单');
        }
        $from_ids = array_unique(array_column($this->transaction_logs, 'from_id'));
        if (count($from_ids) > 1) {
            return $this->addError($attribute, '单次只支持单个用户的交易订单退款');
        }
        $this->from_id = $from_ids[0];
    }

    public function generateTransaction(int $price, float $rate, int $status, int|array $scope = PayAccount::SCOPE_COMMON, callable|null $func = null)
    {
        $this->attr = TransactionLog::ATTR_LIVE_TOKEN;
        [$transaction_id, $balance, $tax, $common_coin_costs, $noble_coin_costs] = parent::generateTransaction($price, $rate, $status, $scope);
        $items_log = new TransactionItemsLog([
            'goods_id' => $this->gift_id,
            'goods_title' => $this->title,
            'goods_price' => $price / $this->num,
            'goods_num' => $this->num,
            'tid' => $transaction_id,
            'user_id' => $this->from_id,
            'status' => $status,
            'type' => $this->type,
        ]);
        if (!$items_log->save()) {
            throw new Exception(MUtils2::getFirstError($items_log));
        }

        return [$transaction_id, $balance, $tax, $common_coin_costs, $noble_coin_costs];
    }

    public function buy()
    {
        if (!$this->beforeBuy()) {
            return false;
        }

        [, $rate] = $this->getSellerAndRate();
        $db_transaction = TransactionLog::getDb()->beginTransaction();
        try {
            $scope = $this->getPayAccountScope();
            $total_price = $this->price;
            [$transaction_id, $balance, $tax, $common_coin_costs, $noble_coin_costs] = $this->generateTransaction($total_price, $rate, TransactionLog::STATUS_UNDONE, $scope);
            $db_transaction->commit();
        } catch (Exception $e) {
            $db_transaction->rollBack();
            throw $e;
        }

        return [
            'transaction_id' => $transaction_id,
            'balance' => $balance,
            'live_noble_balance' => PayAccounts::getAccounts($this->from_id, PayAccount::SCOPE_LIVE)->getTotalBalance(),
            'price' => $total_price,
            'context' => Json::encode([
                'type' => $this->type,
                'attr' => $this->attr,
                'transaction_id' => $transaction_id,
                'tax' => $tax,
                'common_coins' => $common_coin_costs,
                'noble_coins' => $noble_coin_costs,
                'price' => $total_price,
            ]),
        ];
    }

    public function refund()
    {
        if (!$this->validate()) {
            return;
        }

        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_LIVE_TOKEN_REFUND, $this->from_id);
        if (!$redis->lock($lock, HALF_MINUTE)) {
            throw new HttpException(403, '多个订单正在处理中，请稍候重试');
        }
        $db_transaction = TransactionLog::getDb()->beginTransaction();
        try {
            $goods_id = $this->goods[0]['id'] ?? null;
            $goods_price = $this->goods[0]['price'] ?? null;
            $goods_title = $this->goods[0]['title'] ?? null;
            $refund_goods_num = $this->goods[0]['num'] ?? 0;

            $transaction_log_id_map = array_column($this->transaction_logs, null, 'id');
            $items_logs = TransactionItemsLog::find()->where([
                'tid' => array_keys($transaction_log_id_map),
                'user_id' => $this->from_id,
                'status' => TransactionLog::STATUS_UNDONE,
            ])->andWhere('goods_num > 0')->orderBy('create_time ASC')->all();
            if (empty($items_logs)) {
                throw new HttpException(404, '未找到待确认的交易');
            }
            if ($refund_goods_num > 0 && array_sum(array_column($items_logs, 'goods_num')) < $refund_goods_num) {
                throw new HttpException(403, '待退款的商品数大于可退款的商品数');
            }

            $left_refund_goods_num = $refund_goods_num;
            $cancel_order_ids = $confirm_order_ids = [];
            $cancel_order_goods_num = 0;
            foreach ($items_logs as $item) {
                /**
                 * @var TransactionItemsLog $item
                 * @var TransactionLog $transaction_log
                 */
                $transaction_log = $transaction_log_id_map[$item->tid];

                if ($left_refund_goods_num <= 0) {
                    // 剩余的交易订单确认
                    $target_status = TransactionLog::STATUS_SUCCESS;
                    $confirm_order_ids[] = $transaction_log->id;
                    PayAccountPurchaseDetail::confirm($transaction_log);
                } else {
                    // 依次取消交易订单（进行退钻）
                    $target_status = TransactionLog::STATUS_CANCEL;
                    $cancel_order_ids[] = $transaction_log->id;
                    PayAccountPurchaseDetail::cancel($transaction_log);

                    $left_refund_goods_num -= min($left_refund_goods_num, $item->goods_num);
                    $cancel_order_goods_num += $item->goods_num;
                }

                if (!TransactionItemsLog::updateAll([
                    'status' => $target_status,
                    'modified_time' => $_SERVER['REQUEST_TIME'],
                ], ['id' => $item->id, 'modified_time' => $item->modified_time])) {
                    throw new HttpException(403, '多个订单正在处理中，请稍候重试');
                }

                $transaction_log->status = $target_status;
                $transaction_log->confirm_time = $_SERVER['REQUEST_TIME'];
                if (!$transaction_log->save()) {
                    throw new HttpException(500, MUtils2::getFirstError($transaction_log));
                }
            }
            if (!empty($cancel_order_ids)) {
                // 取消的订单商品数 > 待退款的商品数，生成一个差额的订单
                if ($cancel_order_goods_num > $refund_goods_num) {
                    $this->gift_id = $goods_id;
                    $this->title = $goods_title;
                    $this->num = $cancel_order_goods_num - $refund_goods_num;

                    $this->price = $goods_price * $this->num;
                    [, $rate] = $this->getSellerAndRate();
                    $scope = $this->getPayAccountScope();
                    [$transaction_id,] = $this->generateTransaction($this->price, $rate, TransactionLog::STATUS_SUCCESS, $scope);
                    $tradelog_detail = TransactionLogDetail::findOne(['id' => $transaction_id]);
                    $tradelog_detail->more = array_merge($tradelog_detail->more ?: [], [
                        'canceled_transaction_ids' => $cancel_order_ids,
                        'refund_goods_num' => $refund_goods_num,
                    ]);
                    if (!$tradelog_detail->save()) {
                        throw new HttpException(500, MUtils2::getFirstError($tradelog_detail));
                    }
                }
                // 发送退钻系统通知
                MMessageAssign::sendSysMsg(
                    $this->from_id,
                    '【“仙剑奇缘”直播活动】飞镖余额退还钻石',
                    sprintf('您在“仙剑奇缘”活动中的剩余飞镖已折算为钻石退回钱包；剩余充值飞镖数：%d，退回钻石数：%d，请留意“我的钱包”内钻石余额变化～（贵族钻石将退回礼物面板-贵族 tab 内）',
                        $refund_goods_num, $refund_goods_num * $goods_price)
                );
            }

            $db_transaction->commit();
        } catch (\Exception $e) {
            $db_transaction->rollBack();
            throw $e;
        } finally {
            $redis->unlock($lock);
        }

        $accounts = PayAccounts::getAccounts($this->from_id, [PayAccount::SCOPE_COMMON, PayAccount::SCOPE_LIVE]);
        return [
            'balance' => $accounts->getTotalBalanceByScope(PayAccount::SCOPE_COMMON),
            'live_noble_balance' => $accounts->getTotalBalanceByScope(PayAccount::SCOPE_LIVE),
            'cancel_order_ids' => $cancel_order_ids,
            'confirm_order_ids' => $confirm_order_ids,
        ];
    }

}

