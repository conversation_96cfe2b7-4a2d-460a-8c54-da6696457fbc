<?php

namespace app\forms;

use app\models\PayAccount;
use app\models\PayAccounts;
use app\models\TransactionLog;
use Exception;
use yii\helpers\Json;

class TransactionFormLive extends TransactionForm
{
    const SCENARIO_BUY = 'buy';
    const SCENARIO_REFUND = 'refund';

    const PURCHASE_HISTORY_ATTR = [
        TransactionLog::ATTR_COMMON,
        TransactionLog::ATTR_LIVE_RENEWAL_NOBLE,
        TransactionLog::ATTR_LIVE_REGISTER_NOBLE,
        TransactionLog::ATTR_LIVE_REBATE_GIFT,
        TransactionLog::ATTR_LIVE_LUCKY_GIFT,
        TransactionLog::ATTR_LIVE_REGISTER_SUPER_FAN,
        TransactionLog::ATTR_LIVE_RENEWAL_SUPER_FAN,
        TransactionLog::ATTR_LIVE_BUY_FUKUBUKURO,
        TransactionLog::ATTR_LIVE_BUY_GASHAPON,
        TransactionLog::ATTR_LIVE_WISH_POOL,
        TransactionLog::ATTR_LIVE_RED_PACKET,
        TransactionLog::ATTR_LIVE_REGISTER_NOBLE_TRIAL,
        TransactionLog::ATTR_LIVE_RENEWAL_NOBLE_TRIAL,
        TransactionLog::ATTR_LIVE_DANMAKU,
        TransactionLog::ATTR_LIVE_BUY_LUCKY_BAG,
        TransactionLog::ATTR_LIVE_TOKEN,
    ];

    const GOODS_TYPE_RED_PACKAGE = 1;
    const GOODS_TYPE_WISH_GOODS = 2;
    const GOODS_TYPE_DANMAKU = 3;
    const GOODS_TYPE_LUCKY_BAG = 4;
    const GOODS_TYPE_LUCKY_BOX = 5;
    const GOODS_TYPE_LIVE_TOKEN = 6;

    public $to_id = 0;

    public $attr = TransactionLog::ATTR_COMMON;

    public function __construct($scenario = self::SCENARIO_BUY)
    {
        parent::__construct(['scenario' => $scenario]);
    }

    public function scenarios()
    {
        return array_merge(parent::scenarios(), [
            self::SCENARIO_BUY => ['from_id', 'to_id', 'gift_id', 'price', 'num', 'title', 'noble', 'live_open_log_id'],
        ]);
    }

    public static function newTransactionForm(int $goods_type, string $scenario = self::SCENARIO_BUY)
    {
        switch ($goods_type) {
            case self::GOODS_TYPE_RED_PACKAGE:
                return new TransactionFormRedPacket($scenario);
            case self::GOODS_TYPE_WISH_GOODS:
                return new TransactionFormWishGoods($scenario);
            case self::GOODS_TYPE_DANMAKU:
                return new TransactionFormLiveDanmaku($scenario);
            case self::GOODS_TYPE_LUCKY_BAG:
                return new TransactionFormLuckyBag($scenario);
            case self::GOODS_TYPE_LUCKY_BOX:
                return new TransactionFormLuckyBox($scenario);
            case self::GOODS_TYPE_LIVE_TOKEN:
                return new TransactionFormLiveToken($scenario);
            default:
                throw new Exception('不支持的商品类型：' . $goods_type);
        }
    }

    protected function isNeedCalculcateCreatorRevenue()
    {
        return false;
    }

    public function beforeBuy()
    {
        if (self::SCENARIO_BUY !== $this->getScenario()) {
            throw new Exception('场景错误');
        }
        if (!$this->validate()) {
            return false;
        }

        return true;
    }

    public function buy()
    {
        if (!$this->beforeBuy()) {
            return false;
        }

        [$seller, $rate] = $this->getSellerAndRate();
        $db_transaction = TransactionLog::getDb()->beginTransaction();
        try {
            $scope = $this->getPayAccountScope();
            [$transaction_id, $balance, $tax, $common_coin_costs, $noble_coin_costs] = $this->generateTransaction($this->price, $rate, TransactionLog::STATUS_SUCCESS, $scope);
            $live_noble_balance = PayAccounts::getAccounts($this->from_id, PayAccount::SCOPE_LIVE)->getTotalBalance();
            if ($this->isNeedCalculcateCreatorRevenue()) {
                $this->calculateLiveRevenue($seller, $this->price, $tax, $rate);
            }

            $db_transaction->commit();
        } catch (Exception $e) {
            $db_transaction->rollBack();
            throw $e;
        }

        return [
            'transaction_id' => $transaction_id,
            'balance' => $balance,
            'live_noble_balance' => $live_noble_balance,
            'price' => $this->price,
            'context' => Json::encode([
                'type' => $this->type,
                'attr' => $this->attr,
                'transaction_id' => $transaction_id,
                'tax' => $tax,
                'common_coins' => $common_coin_costs,
                'noble_coins' => $noble_coin_costs,
                'price' => $this->price,
            ]),
        ];
    }

    public static function formatOrderTitle(TransactionLog $transaction_log)
    {
        switch ($transaction_log->attr) {
            case $transaction_log::ATTR_LIVE_REGISTER_NOBLE:
                return sprintf('贵族--开通%s', $transaction_log->title);
            case $transaction_log::ATTR_LIVE_RENEWAL_NOBLE:
                return sprintf('贵族--续费%s', $transaction_log->title);
            case $transaction_log::ATTR_LIVE_LUCKY_GIFT:
                return sprintf('礼物--%s', $transaction_log->title . ' × ' . $transaction_log::LUCKY_GIFT_OPEN_GIFT_NUM);
            case $transaction_log::ATTR_LIVE_REGISTER_SUPER_FAN:
                return sprintf('超粉--开通 %d 个月', $transaction_log->num);
            case $transaction_log::ATTR_LIVE_RENEWAL_SUPER_FAN:
                return sprintf('超粉--续费 %d 个月', $transaction_log->num);
            case $transaction_log::ATTR_LIVE_BUY_FUKUBUKURO:
                return sprintf('福袋--%s × %d', $transaction_log->title, $transaction_log->num);
            case $transaction_log::ATTR_LIVE_BUY_GASHAPON:
                return sprintf('抽奖--%s', $transaction_log->title);
            case $transaction_log::ATTR_LIVE_WISH_POOL:
                return sprintf('许愿池--%s', $transaction_log->title . ' × ' . $transaction_log->num);
            case $transaction_log::ATTR_LIVE_RED_PACKET:
                return sprintf('红包--%s', $transaction_log->title . ' × ' . $transaction_log->num);
            case $transaction_log::ATTR_LIVE_DANMAKU:
                return sprintf('玩法--弹幕 × %d', $transaction_log->num);
            case $transaction_log::ATTR_LIVE_BUY_LUCKY_BOX:
                return sprintf('宝盒--%s抽盒 × %d', $transaction_log->title, $transaction_log->num);
            case $transaction_log::ATTR_LIVE_TOKEN:
            case $transaction_log::ATTR_LIVE_BUY_LUCKY_BAG:
            case $transaction_log::ATTR_LIVE_REGISTER_NOBLE_TRIAL:
            case $transaction_log::ATTR_LIVE_RENEWAL_NOBLE_TRIAL:
                return sprintf('%s × %d', $transaction_log->title, $transaction_log->num);
            default:
                if (0 === $transaction_log->gift_id) {
                    return sprintf('玩法--提问 × %d', $transaction_log->num);
                }
                return sprintf('礼物--%s', $transaction_log->title . ' × ' . $transaction_log->num);
        }
    }

}
