<?php

namespace app\forms;

use app\models\Balance;
use app\models\TopupMenu;

class TopupGoods
{
    /**
     * @var TopupMenu 充值项
     */
    private $_ccy;

    /**
     * @var int 充值数量
     */
    private $_quantity;

    /**
     * @var int 实际支付总价格（单位：分）
     */
    private $_real_total_price;

    /**
     * @var string 第三方平台交易 ID
     */
    private $_transaction_id;

    public function __construct(string $transaction_id, TopupMenu $ccy, int $quantity, int $real_total_price)
    {
        $this->_transaction_id = $transaction_id;
        $this->_ccy = $ccy;
        $this->_quantity = $quantity;
        $this->_real_total_price = $real_total_price;
    }

    /**
     * 获取第三方平台交易 ID
     *
     * @return string
     */
    public function getTransactionId(): string
    {
        return $this->_transaction_id;
    }

    /**
     * 获取商品购买数量
     *
     * @return int
     */
    public function getQuantity(): int
    {
        return $this->_quantity;
    }

    /**
     * 获取商品的 ID
     *
     * @return int
     */
    public function getGoodsId()
    {
        return $this->_ccy->id;
    }

    /**
     * 获取商品的单价（单位：分）
     *
     * @return int
     */
    public function getUnitPrice(): int
    {
        return Balance::profitUnitConversion($this->_ccy->price, Balance::CONVERT_YUAN_TO_FEN);
    }

    /**
     * 获取商品的总价格（单位：分）
     *
     * @return int
     */
    public function getTotalPrice(): int
    {
        return $this->getUnitPrice() * $this->_quantity;
    }

    /**
     * 获取商品的总价格（单位：元）
     *
     * @return float|int
     */
    public function getTotalPriceInYuan()
    {
        return Balance::profitUnitConversion($this->getTotalPrice(), Balance::CONVERT_FEN_TO_YUAN);
    }

    /**
     * 获取实际支付的总价格（单位：分）
     *
     * @return int
     */
    public function getRealTotalPrice(): int
    {
        return $this->_real_total_price;
    }

    /**
     * 获取钻石数量
     *
     * @return int
     */
    public function getDiamond(): int
    {
        return $this->_ccy->num * $this->_quantity;
    }

    public function getGoodsType()
    {
        return $this->_ccy->ccy;
    }

}
