<?php

namespace app\forms;

use app\components\auth\AuthJingDong;
use app\components\auth\AuthJingDongV2;
use app\components\util\JingDongTopupException;
use app\models\Balance;
use app\models\Mowangskuser;
use app\models\RechargeOrder;
use Exception;
use missevan\util\MobileNumber;
use missevan\util\MUtils;
use Yii;

class JingDongTransactionForm
{
    /**
     * @var AuthJingDong|AuthJingDongV2
     */
    private $_client;

    public function __construct($client)
    {
        $this->_client = $client;
    }

    private function getBuyer(MobileNumber $user_mobile): ?Mowangskuser
    {
        $user_info = Yii::$app->sso->getUserInfoByMobile($user_mobile->mobile_num, $user_mobile->region_num);
        if (!$user_info) {
            return null;
        }

        $model = new Mowangskuser();
        $model->setAttributes([
            'id' => $user_info['id'],
            'confirm' => $user_info['confirm'],
        ], false);
        return $model;
    }

    public function topup(TopupGoods $topup_goods, MobileNumber $user_mobile, ?string $notify_url, UserContext $user_context): string
    {
        $user = $this->getBuyer($user_mobile);
        if (!$user) {
            throw new JingDongTopupException(sprintf('账号未注册：mobile[%s]', $user_mobile->mobile_num), $this->_client->getErrorCodeErrorAccount());
        }
        if ($user->isBanTopupAndConsume()) {
            throw new JingDongTopupException(sprintf('账号已被封禁：user_id[%d]', $user->id), $this->_client->getErrorCodeErrorAccount());
        }

        $transaction = Yii::$app->paydb->beginTransaction();
        try {
            $detail_more = [
                'jingdong_goods_quantity' => $topup_goods->getQuantity(),
            ];
            if ($notify_url) {
                $detail_more['jingdong_notify_url'] = $notify_url;
            }
            $user_context->add('more', $detail_more);
            $order = new RechargeOrder([
                'uid' => $user->id,
                'tid' => $topup_goods->getTransactionId(),
                'cid' => $topup_goods->getGoodsId(),
                'price' => $topup_goods->getTotalPriceInYuan(),
                'num' => $topup_goods->getDiamond(),
                'ccy' => $topup_goods->getGoodsType(),
                'status' => RechargeOrder::STATUS_SUCCESS,
                'type' => RechargeOrder::TYPE_JINGDONG,
                'confirm_time' => $_SERVER['REQUEST_TIME'],
                'detail' => $user_context->toArray(),
            ]);
            if (!$order->save()) {
                throw new JingDongTopupException(MUtils::getFirstError($order), $this->_client->getErrorCodeSystemError());
            }
            $order_detail = $order->generateDetail($topup_goods->getRealTotalPrice());
            if (!$order_detail->save()) {
                throw new JingDongTopupException(MUtils::getFirstError($order_detail), $this->_client->getErrorCodeSystemError());
            }

            $buyer = Balance::getByPk($user->id);
            $buyer->updateCounters([
                'android' => $order->num,
                'all_topup' => $order->num,
                'all_coin' => $order->num,
            ]);
            $transaction->commit();

            return $order->getOrderId();
        } catch (Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

}
