<?php

namespace app\forms;

use app\models\Balance;
use app\models\GuildBalance;
use app\models\PayAccount;
use app\models\PayAccounts;
use app\models\TransactionItemsLog;
use app\models\TransactionItemsLogList;
use app\models\TransactionLog;
use Exception;
use missevan\util\MUtils;
use yii\web\HttpException;

/**
 * 白给礼物送礼类
 *
 * Class TransactionFormRebateGift
 * @package app\forms
 */
class TransactionFormRebateGift extends TransactionForm
{
    // 白给礼物来源交易 attr
    const REBATE_GIFT_CONTEXT_TRANSACTION_ATTR = [
        TransactionLog::ATTR_COMMON,
        TransactionLog::ATTR_LIVE_BUY_FUKUBUKURO,
        TransactionLog::ATTR_LIVE_WISH_POOL,
        TransactionLog::ATTR_LIVE_RED_PACKET,
    ];

    /**
     * @var array|GoodsForm[]
     */
    public $gifts = [];

    public function __construct($scenario = self::SCENARIO_BUY)
    {
        $this->attr = TransactionLog::ATTR_LIVE_REBATE_GIFT;
        parent::__construct(['scenario' => $scenario]);
    }

    public function scenarios()
    {
        return array_merge(parent::scenarios(), [
            self::SCENARIO_BUY => ['from_id', 'to_id', 'gifts', 'live_open_log_id'],
        ]);
    }

    public function rules()
    {
        return array_merge(parent::rules(), [
            ['gifts', 'required'],
            ['gifts', 'checkGifts'],
        ]);
    }

    public function attributeLabels()
    {
        return array_merge(parent::attributeLabels(), [
            'gifts' => '组合礼物',
        ]);
    }

    public function checkGifts()
    {
        if (empty($this->gifts)) {
            return $this->addError('gifts', '没有要送出的礼物');
        }
        if (count(array_unique(array_column($this->gifts, 'id'))) > 1) {
            return $this->addError('gifts', '暂不支持一次性送出不同的礼物');
        }

        $transaction_ids = [];
        foreach ($this->gifts as $idx => $gift) {
            $goods = new GoodsForm(GoodsForm::SCENARIO_REBATE_GIFT, LiveGiftContext::class);
            $goods->load($gift, '');
            if (!$goods->validate()) {
                return $this->addError('gifts', MUtils::getFirstError($goods));
            }
            $this->gifts[$idx] = $goods;
            if ($context = $goods->getContextInfo()) {
                $transaction_ids[] = (int)$context->transaction_id;
            }
        }

        if (!empty($transaction_ids)) {
            $transaction_ids = array_unique($transaction_ids);
            $transaction_log_count = (int)TransactionLog::find()->where([
                'id' => $transaction_ids,
                // 红包场景：A 送出红包，A、B、C 抢到红包中的礼物，则红包的 from_id 和红包礼物的 from_id 不一定是同一个人
                // 'from_id' => $this->from_id,
                'type' => [TransactionLog::TYPE_LIVE, TransactionLog::TYPE_GUILD_LIVE],
                'attr' => self::REBATE_GIFT_CONTEXT_TRANSACTION_ATTR,
                'status' => TransactionLog::STATUS_SUCCESS,
            ])->count();
            if ($transaction_log_count !== count($transaction_ids)) {
                return $this->addError('gifts', '礼物未找到对应完整交易记录');
            }
        }
    }

    public function sendGift()
    {
        if (self::SCENARIO_BUY !== $this->getScenario()) {
            throw new Exception('场景错误');
        }
        if (!$this->validate()) {
            return false;
        }
        if ($this->from_id === $this->to_id) {
            throw new HttpException(403, '不可以打赏自己', 200020002);
        }

        /**
         * @var $seller Balance|GuildBalance
         */
        [$seller, $rate] = $this->getSellerAndRate();
        $seller_update = [];

        $now = $_SERVER['REQUEST_TIME'];
        $db_transaction = TransactionLog::getDb()->beginTransaction();
        try {
            $tradelog = new TransactionLog([
                'from_id' => $this->from_id,
                'to_id' => $this->to_id,
                'attr' => $this->attr,
                'type' => $this->type,
                'status' => TransactionLog::STATUS_SUCCESS,
                'rate' => $rate,
            ]);
            $trade_items = new TransactionItemsLogList();
            foreach ($this->gifts as $gift) {
                /**
                 * @var GoodsForm $gift
                 * @var LiveGiftContext $context
                 */
                if ($context = $gift->getContextInfo()) {
                    $gift_tax = $context->giftTax($gift);
                    $coins = $context->giftCostCoins($gift);
                    $noble_coins = $context->giftCostNobleCoins($gift);

                    $more_detail = $coins + $noble_coins + ['context_transaction_id' => $context->transaction_id];
                    // WORKAROUND: 兼容历史存量数据，背包礼物没有永久的，后续去除此兼容
                    if (!is_null($context->attr)) {
                        $more_detail['context_attr'] = $context->attr;
                    }
                    if (!is_null($context->type)) {
                        $more_detail['context_type'] = $context->type;
                    }
                } else {
                    // 白给礼物渠道费率使用安卓渠道费率
                    // https://www.tapd.bilibili.co/********/prong/stories/view/11********002727084
                    $gift_tax = $tradelog->calcTax([PayAccount::COIN_FIELD_ANDROID => $gift->getTotalPrice()]);
                    $more_detail = [];
                }

                $gift_price = $gift->getTotalPrice();
                $gift_income = Balance::profitUnitConversion($gift_price, Balance::CONVERT_DIAMOND_TO_YUAN);
                if (!$seller->isGuild()) {
                    $profit_in_fen = Balance::profitUnitConversion(($gift_income - $gift_tax) * $rate, Balance::CONVERT_YUAN_TO_FEN);
                    $seller_update['new_live_profit'] = ($seller_update['new_live_profit'] ?? 0) + $profit_in_fen;
                    $seller_update['new_all_live_profit'] = ($seller_update['new_all_live_profit'] ?? 0) + $profit_in_fen;
                }

                $item = new TransactionItemsLog([
                    'goods_id' => $gift->id,
                    'goods_title' => $gift->title,
                    'goods_price' => $gift->price,
                    'goods_num' => $gift->num,
                    'user_id' => $this->from_id,
                    'status' => TransactionLog::STATUS_SUCCESS,
                    'type' => $this->type,
                    'create_time' => $now,
                    'modified_time' => $now,
                ]);
                $item->more_detail = ['income' => $gift_income, 'tax' => $gift_tax] + $more_detail;
                $trade_items->addItem($item);
            }

            $tradelog->setAttributes([
                'gift_id' => $trade_items->getGoodsID(),
                'title' => $trade_items->getGoodsTitle(),
                'num' => $trade_items->getTotalGoodsNum(),
                'income' => $trade_items->getTotalIncome(),
                'tax' => $trade_items->getTotalTax(),
            ]);
            $this->setTransactionLogDetail($tradelog);
            if (!$tradelog->save()) {
                throw new Exception(MUtils::getFirstError($tradelog));
            }
            if ($trade_items->save($tradelog->id) === 0) {
                throw new Exception('保存失败');
            }
            if ($seller_update) {
                $seller->updateCounters($seller_update);
            }

            $db_transaction->commit();
        } catch (Exception $e) {
            $db_transaction->rollBack();
            throw $e;
        }

        $pay_accounts = PayAccounts::getAccounts($this->from_id, [PayAccount::SCOPE_COMMON, PayAccount::SCOPE_LIVE]);
        return [
            'transaction_id' => $tradelog->id,
            'balance' => $pay_accounts->getTotalBalanceByScope(PayAccount::SCOPE_COMMON),
            'live_noble_balance' => $pay_accounts->getTotalBalanceByScope(PayAccount::SCOPE_LIVE),
        ];
    }
}
