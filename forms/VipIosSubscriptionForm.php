<?php

namespace app\forms;

use app\components\auth\appleservernotification\ServerNotification;
use app\models\AdTrack;
use app\models\Balance;
use app\models\IosReceipt;
use app\models\MUserVip;
use app\models\MVip;
use app\models\VipFeeDeductedRecord as DeductedRecord;
use app\models\VipSubscriptionSignAgreement as SignAgreement;
use Exception;
use missevan\util\MUtils;
use Yii;
use yii\web\HttpException;

/**
 * Class VipIosSubscriptionForm
 * @package app\forms
 * @link https://info.missevan.com/pages/viewpage.action?pageId=118095008
 */
class VipIosSubscriptionForm
{
    private $_message;

    public function __construct(ServerNotification $message)
    {
        $this->_message = $message;
    }

    /**
     * @throws Exception
     */
    public function processNotification()
    {
        if (!in_array($this->getNotificationType(), ServerNotification::NOTIFICATION_TYPE_LIST)) {
            throw new Exception('未知的通知类型：' . $this->getNotificationType());
        }

        $product_id = $this->getProductId();
        $transaction_id = $this->getTransactionId();
        $agreement_no = $this->getAgreementNo();

        $vip = MVip::getVipByProductId($product_id, MVip::PLATFORM_IOS);
        $deducted_record = DeductedRecord::getRecord($transaction_id, DeductedRecord::PAY_TYPE_IOS);
        $agreement = SignAgreement::getAgreementByNo($agreement_no, SignAgreement::PAY_TYPE_IOS);

        $redis = Yii::$app->redis;
        $lock = $redis->generateKey(LOCK_VIP_IOS_SUBSCRIPTION, $agreement_no);
        $transaction = null;
        try {
            if (!$redis->lock($lock, ONE_MINUTE)) {
                throw new HttpException(400, '操作过于频繁，请稍候再试');
            }

            $transaction = DeductedRecord::getDb()->beginTransaction();
            switch ($this->getNotificationType()) {
                case 'SUBSCRIBED':  // 订阅（首次订阅，过期后再次订阅）
                case 'DID_RENEW':  // 自动续订
                case 'DID_CHANGE_RENEWAL_PREF':  // 变更订阅（如包月改为包季）
                    $this->processMakeSubscription($agreement, $deducted_record, $vip);
                    break;
                case 'DID_CHANGE_RENEWAL_STATUS':  // 关闭/开启自动续订
                    $this->processSwitchAutoRenew($agreement, $deducted_record, $vip);
                    break;
                case 'EXPIRED':  // 订阅过期
                case 'REVOKE':  // 用户通过家庭共享赋权的内购，不再可用
                case 'GRACE_PERIOD_EXPIRED':  // 订阅过期
                    $this->processExpired($agreement, $deducted_record, $vip);
                    break;
                case 'REFUND':  // 退款
                    $this->processRefund($agreement, $deducted_record, $vip);
                    break;
                default:
                    return;
            }

            $transaction->commit();
            $transaction = null;
        } catch (Exception $e) {
            if ($transaction) {
                $transaction->rollBack();
            }
            if ($e instanceof HttpException && $e->statusCode >= 500) {
                Yii::error(sprintf('iOS 订阅处理失败：error=%s', $e->getMessage()), __METHOD__);
            }
            throw $e;
        } finally {
            $redis->unlock($lock);
        }
    }

    public function getNotificationType()
    {
        return $this->_message->notificationType;
    }

    public function getNotificationSubType()
    {
        return $this->_message->subtype;
    }

    public function getAgreementNo()
    {
        return $this->_message->data->signedTransactionInfo->originalTransactionId;
    }

    public function getTransactionId()
    {
        return $this->_message->data->signedTransactionInfo->transactionId;
    }

    public function getProductId()
    {
        return $this->_message->data->signedTransactionInfo->productId;
    }

    private function isUpgradeSubscriptionNotification()
    {
        return $this->getNotificationType() === 'DID_CHANGE_RENEWAL_PREF' && $this->getNotificationSubType() === 'UPGRADE';
    }

    public function processMakeSubscription(?SignAgreement $agreement, ?DeductedRecord $deducted_record, MVip $vip)
    {
        // 对于变更订阅，只处理升级订阅（iOS 对于升级订阅会立即生效，并按比例退还原订阅的费用）
        // 降级及取消降级在 notificationType=DID_RENEW 时处理
        if ($this->getNotificationType() === 'DID_CHANGE_RENEWAL_PREF' && $this->getNotificationSubType() !== 'UPGRADE') {
            // subtype=UPGRADE: 升级订阅立即生效
            // subtype=DOWNGRADE: 降级，新的续订将在当前订阅过期后生效，在新的续订生效时的回调通知时进行处理
            // subtype=null: 用户更改续订首选项回当前订阅（取消降级）
            if ($agreement
                    && MUserVip::newMainValidVipQuery($agreement->user_id)->andWhere(['vip_id' => $agreement->vip_id])->exists()
                    && $agreement->isExpired()) {
                // 当前协议已过期（如取消自动订阅），用户还在有效期，然后点降级或取消降级
                $renew_vip_id = MVip::parseVipId($this->_message->data->signedRenewalInfo->autoRenewProductId);
                if ($this->getNotificationType() === 'DID_CHANGE_RENEWAL_PREF'
                        && ($this->getNotificationSubType() === 'DOWNGRADE'  // 降级
                            || is_null($this->getNotificationSubType())  // 取消降级
                    )) {
                    $agreement = SignAgreement::newAgreement($this->getAgreementNo(), $agreement->user_id, $renew_vip_id, SignAgreement::PAY_TYPE_IOS, SignAgreement::STATUS_ACTIVE);
                    if (!$agreement->save()) {
                        throw new Exception(MUtils::getFirstError($agreement));
                    }
                }
            }

            return;
        }

        // 已处理
        if ($deducted_record) {
            $this->setDeductedRecordExtraInfo($deducted_record, $vip);
            if (!$deducted_record->save()) {
                throw new Exception(MUtils::getFirstError($deducted_record));
            }
            return;
        }

        $user_id = 0;
        $previous_deduct_record = null;

        // 开通过
        if ($agreement) {
            // 升级订阅时，旧的订阅协议立即失效
            if ($this->isUpgradeSubscriptionNotification()) {
                $user_id = $agreement->user_id;
                $agreement->expireNow();
                $previous_deduct_record = DeductedRecord::find()
                    ->where(['sign_agreement_id' => $agreement->id, 'pay_type' => DeductedRecord::PAY_TYPE_IOS])
                    ->orderBy('id DESC')->one();
                if (!$previous_deduct_record) {
                    throw new Exception(sprintf('未找到对应的扣费记录：user_id=%d, sign_agreement_id=%d', $agreement->user_id, $agreement->id), __METHOD__);
                }
            }

            // 协议正在生效中
            if ($agreement->isActive()) {
                $user_id = $agreement->user_id;
                // 本次订阅的计划与原计划不一致，代表之前对原订阅进行降级后，此时以新订阅计划进行扣费续费
                if ($vip->id !== $agreement->vip_id) {
                    $agreement->expireNow();
                    $agreement = null;
                }
            } else {
                // 订阅已过期
                $agreement = null;
            }
        }
        if (!$agreement) {
            $agreement = SignAgreement::newAgreement($this->getAgreementNo(), $user_id, $vip->id, SignAgreement::PAY_TYPE_IOS, SignAgreement::STATUS_ACTIVE);
            if (!$agreement->save()) {
                throw new Exception(MUtils::getFirstError($agreement));
            }
        }

        // 升级订阅，对原订阅的会员有效期进行扣减
        if ($this->isUpgradeSubscriptionNotification() && $previous_deduct_record) {
            $previous_deduct_record->next_deduct_time = $_SERVER['REQUEST_TIME'];
            if (!$previous_deduct_record->save()) {
                throw new Exception(MUtils::getFirstError($previous_deduct_record));
            }
            MUserVip::deductLeftDuration($previous_deduct_record);
        }

        $deducted_record = DeductedRecord::newRecord($this->getTransactionId(), $user_id, $vip, DeductedRecord::PAY_TYPE_IOS, $agreement, DeductedRecord::STATUS_SUCCESS);
        $deducted_record->tax = DeductedRecord::calculateTax($deducted_record->price, DeductedRecord::PAY_TYPE_IOS);
        $this->setDeductedRecordExtraInfo($deducted_record, $vip);
        if (!$deducted_record->save()) {
            throw new Exception(MUtils::getFirstError($deducted_record));
        }

        // 若没有 user_id，即用户不在 app 内购买，需要在 app 内恢复购买
        if ($deducted_record->user_id) {
            $is_renew = MUserVip::isVipUser($deducted_record->user_id);
            MUserVip::createOrExtend($deducted_record->user_id, $vip, $deducted_record->id);
            MUserVip::sendSuccessfulNotification($deducted_record->user_id, $is_renew);
            AdTrack::callbackPay($deducted_record->user_id, Balance::profitUnitConversion($deducted_record->price, Balance::CONVERT_FEN_TO_YUAN));
        }
    }

    private function setDeductedRecordExtraInfo(DeductedRecord $deducted_record, MVip $vip)
    {
        if ($deducted_record->next_deduct_time === 0) {
            $deducted_record->next_deduct_time = intval($this->_message->data->signedTransactionInfo->expiresDate / 1000);
        }
        if ($vip->isMonthlyDeductFeeSchedule() && $this->_message->data->signedTransactionInfo->isIntroductoryOffer()) {
            $deducted_record->is_first_topup_discount = true;  // 包月首月优惠
        }
        // 设置实际支付的价格
        $real_price_in_fen = $this->_message->data->signedTransactionInfo->getPriceInFen();
        if ($deducted_record->price !== $real_price_in_fen) {
            $deducted_record->expect_price = $deducted_record->price;

            $deducted_record->price = $real_price_in_fen;
            $deducted_record->tax = DeductedRecord::calculateTax($deducted_record->price, DeductedRecord::PAY_TYPE_IOS);
            Yii::error(sprintf('iOS 订阅开通预期价格与实际价格不一致：expect_price=%d, real_price=%d',
                $deducted_record->expect_price, $deducted_record->price), __METHOD__);
        }
        $deducted_record->more = array_merge($deducted_record->more ?: [], [
            'notification_type' => $this->getNotificationType(),
            'notification_sub_type' => $this->getNotificationSubType(),
        ]);
    }
    /**
     * 处理订阅过期
     *
     * @return void
     */
    public function processExpired(?SignAgreement $agreement, ?DeductedRecord $deducted_record, MVip $vip)
    {
        if (!$agreement || !$agreement->isActive()) {
            return;
        }
        $agreement->expireNow();
    }

    /**
     * 开启/关闭自动续订
     *
     * @return void
     */
    public function processSwitchAutoRenew(?SignAgreement $agreement, ?DeductedRecord $deducted_record, MVip $vip)
    {
        switch ($this->getNotificationSubType()) {
            case 'AUTO_RENEW_DISABLED':  // 用户关闭订阅自动续订，或者在用户请求退款之后，Apple Store 关闭订阅自动续订
                if (!$agreement) {
                    throw new Exception(sprintf('未找到对应的 iOS 订阅签约记录：agreement_no[%s]', $this->getAgreementNo()));
                }
                if ($agreement->isActive()) {
                    $agreement->expireNow();
                }
                break;
            case 'AUTO_RENEW_ENABLED':  // 用户重新开启自动续订
                $last_agreement = SignAgreement::getAgreementByNo($this->getAgreementNo(), SignAgreement::PAY_TYPE_IOS);
                if (!$last_agreement) {
                    throw new Exception(sprintf('未找到旧的 iOS 订阅签约记录：agreement_no[%s]', $this->getAgreementNo()));
                }
                if ($last_agreement->isActive()) {
                    if ($last_agreement->vip_id === $vip->id) {
                        return;
                    } else {
                        $last_agreement->expireNow();
                    }
                }
                $agreement = SignAgreement::newAgreement($this->getAgreementNo(), $last_agreement->user_id, $last_agreement->vip_id, SignAgreement::PAY_TYPE_IOS, SignAgreement::STATUS_ACTIVE);
                if (!$agreement->save()) {
                    throw new Exception(MUtils::getFirstError($agreement));
                }
                break;
            default:
                throw new Exception(sprintf('未知的 subtype：%s', $this->getNotificationSubType()));
        }
    }

    public function processRefund(?SignAgreement $agreement, ?DeductedRecord $deducted_record, MVip $vip)
    {
        if (!$this->_message->data->signedTransactionInfo->isAutoRenewableSubscriptionType() && !$this->_message->data->signedTransactionInfo->isNonRenewingSubscriptionType()) {
            throw new Exception('只处理订阅（自动续期、非自动续期）型商品退款');
        }
        if (!$deducted_record) {
            throw new Exception(sprintf('扣费记录未找到: transaction_id=%s', $this->getTransactionId()));
        }
        if (!$deducted_record->isSuccess()) {
            throw new Exception('未找到可处理的订单: deduct_record_id=%d', $deducted_record->id);
        }
        $deducted_record->status = DeductedRecord::STATUS_CANCELED;
        if (!$deducted_record->save()) {
            throw new Exception(MUtils::getFirstError($deducted_record));
        }
        MUserVip::deductLeftDuration($deducted_record);
    }

    public static function parseReceipt(string $receipt, string $transaction_id, int $user_id)
    {
        $record = IosReceipt::newRecord($user_id, $transaction_id, $receipt);
        if (!$receipt_body = IosReceipt::verifyReceipt($receipt)) {
            throw new Exception('小票验证失败');
        }

        if (YII_ENV_PROD && $receipt_body['environment'] !== 'Production') {
            throw new HttpException(403, '测试小票不可用于生产环境');
        }
        if (!array_key_exists('status', $receipt_body)
                || $receipt_body['status'] !== 0
                || !RechargeForm::isValidAppPackageName($receipt_body['receipt']['bundle_id'] ?? '')
        ) {
            $record->status = IosReceipt::STATUS_INVALID;
            $record->ignoreExceptionSave(true, 'iOS 会员开通日志数据记录异常');
            throw new HttpException(400, '小票验证失败');
        }

        $receipt_list = array_reduce(array_merge($receipt_body['receipt']['in_app'] ?? [], $receipt_body['latest_receipt_info'] ?? []), function ($carry, $item) use ($transaction_id) {
            if (!MVip::isVipProductId($item['product_id']) || array_key_exists('cancellation_date_ms', $item)) {
                return $carry;
            }
            if (!$transaction_id || $transaction_id === $item['transaction_id']) {
                $carry[] = [
                    'vip_id' => MVip::parseVipId($item['product_id']),
                    // 小票 ID
                    'transaction_id' => $item['transaction_id'],
                    // 首次购买小票 ID（对于自动续期型订阅，此值不变，可用做订阅协议号）
                    'original_transaction_id' => $item['original_transaction_id'],
                    // 包月首月优惠体验价
                    'is_in_promotion' => ($item['is_in_intro_offer_period'] ?? 'false') === 'true',
                    // 购买时间
                    'purchase_time' => intval($item['purchase_date_ms'] / 1000),
                    // 自动续期型订阅的下次扣费时间
                    'next_deduct_time' => intval(($item['expires_date_ms'] ?? 0) / 1000),
                ];
            }

            return $carry;
        }, []);
        $receipt_list = array_values(array_column($receipt_list, null, 'transaction_id'));  // 可能存在重复的 transaction_id
        usort($receipt_list, function ($next, $pre) {
            return $next['purchase_time'] > $pre['purchase_time'];  // 按购买时间小到到排序
        });

        if (count($receipt_list) === 0) {
            $record->status = IosReceipt::STATUS_INVALID;
            $record->ignoreExceptionSave(true, 'iOS 会员开通日志数据记录异常');
            throw new HttpException(400, '小票验证失败');
        }
        $record->status = IosReceipt::STATUS_SUCCESS;
        $record->ignoreExceptionSave(true, 'iOS 会员开通日志数据记录异常');

        $result = [
            'receipt_list' => $receipt_list,
            'renewal_info' => null,
        ];

        // https://developer.apple.com/documentation/appstorereceipts/responsebody/pending_renewal_info
        if ($pending_renewal_info = $receipt_body['pending_renewal_info'][0] ?? null) {
            $result['renewal_info'] = [
                'vip_id' => MVip::parseVipId($pending_renewal_info['product_id']),  // 当前的订阅套餐
                'next_term_vip_id' => MVip::parseVipId($pending_renewal_info['auto_renew_product_id']),  // 下一扣费周期的订阅套餐
                'agreement_no' => $pending_renewal_info['original_transaction_id'],  // 协议编号
                'auto_renew_status' => (int)$pending_renewal_info['auto_renew_status'],  // 自动续费的开关状态（1 开启、0 关闭）
                'is_auto_renew_expired' => is_null($pending_renewal_info['expiration_intent'] ?? null),  // 过期状态
            ];
        }
        return $result;
    }

    public static function checkIosAgreement(int $user_id, string $agreement_no, int $vip_id, int $next_term_vip_id, bool $is_subscription_enabled)
    {
        $current_active_agreement = SignAgreement::getActiveAgreement($user_id, SignAgreement::PAY_TYPE_IOS);
        if ($current_active_agreement && $current_active_agreement->agreement_no !== $agreement_no) {
            // 使用其它 apple id 重复开通
            // 可开通单月/单季，不可重复包月/包季
            return $current_active_agreement;
        }

        $agreement = SignAgreement::getAgreementByNo($agreement_no, SignAgreement::PAY_TYPE_IOS)
            ?: SignAgreement::newAgreement($agreement_no, $user_id, $vip_id, SignAgreement::PAY_TYPE_IOS, SignAgreement::STATUS_ACTIVE);
        if ($agreement->user_id === 0) {  // app 外开通后
            Yii::error(sprintf('订阅协议用户 ID 为空：agreement_id=%d', $agreement->id), __METHOD__);
            $agreement->user_id = $user_id;
            if (!$agreement->save()) {
                throw new Exception(MUtils::getFirstError($agreement));
            }
        }
        // 同一个 apple id 给不同的 M 号购买
        // 苹果对于同一个自动续期型商品（如包月），未过期前只能买一次（苹果限制）
        // 如果先用 M 号 A 买包月，再用 M 号 B 买包季，则扣掉 A 的包月剩余有效期，不再自动续期，B 开通包季套餐并立即生效
        // 如果先用 M 号 A 买包季，再用 M 号 B 买包月，则 A 的自动续费协议失效，会员有效期直至包季过期，B 账号在 A 过期后开始生效包月套餐
        if ($agreement->user_id !== $user_id) {
            Yii::error(sprintf('原订阅用户与订阅用户不一致：agreement_id=%d, old_user_id=%d, new_user_id=%d', $agreement->id, $agreement->user_id, $user_id), __METHOD__);
            // PASS
        }

        // 未开启自动订阅，或订阅已过期
        if (!$is_subscription_enabled) {
            if (!$agreement->isExpired()) {
                $agreement->markExpired();
            } else {
                $agreement = null;
            }
            return $agreement;
        }

        // 仍在自动订阅中：升级（旧订阅失效、新订阅立即生效）
        if ($agreement->vip_id !== $vip_id) {
            if ($agreement->isActive()) {
                $agreement->expireNow();
            }
            $deducted_record = DeductedRecord::find()->where(['sign_agreement_id' => $agreement->id])->orderBy('id DESC')->one();
            if ($deducted_record) {
                MUserVip::deductLeftDuration($deducted_record);  // 扣除未使用的剩余时长
            }
            return SignAgreement::newAgreement($agreement_no, $user_id, $vip_id, SignAgreement::PAY_TYPE_IOS, SignAgreement::STATUS_ACTIVE);
        }

        // 仍在自动订阅中：降级
        if ($next_term_vip_id !== $vip_id) {
            if ($agreement->user_id !== $user_id) {  // 切换账号降级：账号 A 为包季，切换到账号 B 开通包月，A 的订阅协议失效，B 的订阅协议生效
                if ($agreement->isActive()) {
                    $agreement->expireNow();
                }
                $new_agreement = SignAgreement::newAgreement($agreement_no, $user_id, $next_term_vip_id, SignAgreement::PAY_TYPE_IOS, SignAgreement::STATUS_ACTIVE);
                if (!$new_agreement->save()) {
                    throw new Exception(MUtils::getFirstError($new_agreement));
                }
            } else {
                // 同一个账号降级：在回调中处理原协议失效，新协议立即生效
                // PASS
            }
            return null;
        }

        // 开通、恢复购买
        if (!$agreement->isActive()) {
            $agreement = SignAgreement::newAgreement($agreement_no, $user_id, $vip_id, SignAgreement::PAY_TYPE_IOS, SignAgreement::STATUS_ACTIVE);
        }
        return $agreement;
    }

    public static function checkIosDeductedRecords(int $user_id, array $receipt_list, string $out_order_no)
    {
        $transaction_ids = array_column($receipt_list, 'transaction_id');
        $vip_ids = array_column($receipt_list, 'vip_id');

        /**
         * @var MVip[] $vip_menu_list
         * @var DeductedRecord[] $existed_deducted_records
         */
        $vip_menu_list = MVip::find()->where(['id' => $vip_ids])->indexBy('id')->all();
        $existed_deducted_records = DeductedRecord::find()
            ->where(['transaction_id' =>  $transaction_ids, 'pay_type' => DeductedRecord::PAY_TYPE_IOS])
            ->indexBy('transaction_id')->all();
        $precreated_deducted_record = null;
        if ($out_order_no && $out_order_no !== DeductedRecord::getIosDefaultOrderId()) {
            ['id' => $order_id] = DeductedRecord::parseTradeNo($out_order_no);
            $precreated_deducted_record = DeductedRecord::findOne(['id' => $order_id, 'pay_type' => DeductedRecord::PAY_TYPE_IOS, 'status' => DeductedRecord::STATUS_PENDING]);
        }
        $standby_deducted_records = [];
        foreach ($receipt_list as $receipt_item) {
            if (!$vip = $vip_menu_list[$receipt_item['vip_id']] ?? null) {
                Yii::error(sprintf('未找到对应的 vip_id=%d, transaction_id=%s', $receipt_item['vip_id'], $receipt_item['transaction_id']), __METHOD__);
                continue;
            }
            if (!$deduct_record = $existed_deducted_records[$receipt_item['transaction_id']] ?? null) {
                if ($precreated_deducted_record && $precreated_deducted_record->vip_id === $vip->id) {
                    $deduct_record = $precreated_deducted_record;
                    $deduct_record->transaction_id = $receipt_item['transaction_id'];
                } else {
                    $deduct_record = new DeductedRecord([
                        'user_id' => $user_id,
                        'transaction_id' => $receipt_item['transaction_id'],
                        'vip_id' => $vip->id,
                        'sign_agreement_id' => 0,
                        'pay_type' => DeductedRecord::PAY_TYPE_IOS,
                        'price' => 0,
                        'tax' => 0,
                        'status' => DeductedRecord::STATUS_PENDING,
                        'next_deduct_time' => 0,
                    ]);
                }
            }
            if (($deduct_record->isSuccess() && $deduct_record->user_id === 0) || $deduct_record->isPending()) {
                if ($deduct_record->user_id === 0) {  // app 外开通
                    Yii::error(sprintf('订单用户 ID 为空：deduct_record_id=%d', $deduct_record->id), __METHOD__);
                    $deduct_record->user_id = $user_id;
                }
                if ($deduct_record->user_id !== $user_id) {
                    // 本次请求更新的订单可能是上次未更新的订单
                    // 上次开通后的前一用户退出前未更新订单，登录了新用户而发起更新前一用户的订单
                    Yii::error(sprintf('订单用户 ID 与实际不一致：deduct_record_id=%d, old_user_id=%d, new_user_id=%d', $deduct_record->id, $deduct_record->user_id, $user_id),
                        __METHOD__);
                    $deduct_record->user_id = $user_id;
                }
                if ($deduct_record->vip_id !== $vip->id) {
                    // 原有订阅的 vip_id 与实际的不一致，以实际为准
                    Yii::error(sprintf('订单 vip_id 与实际不一致：deduct_record_id=%d, old_vip_id=%d, new_vip_id=%d', $deduct_record->id, $deduct_record->user_id, $user_id),
                        __METHOD__);
                    $deduct_record->vip_id = $vip->id;
                }

                if ($deduct_record->status !== DeductedRecord::STATUS_SUCCESS) {
                    $deduct_record->status = DeductedRecord::STATUS_SUCCESS;
                }

                $deduct_record->price = $vip->getPriceToDeduct($receipt_item['is_in_promotion']);
                $deduct_record->tax = DeductedRecord::calculateTax($deduct_record->price, $deduct_record->pay_type);
                if ($receipt_item['is_in_promotion'] && $vip->isMonthlyDeductFeeSchedule()) {
                    // 连续包月且用户未享受过首次优惠时，价格为首次优惠价格
                    // NOTICE: 连续包月首月优惠跟着苹果账户体系走，以苹果的数据为准
                    $deduct_record->is_first_topup_discount = true;
                }
                $deduct_record->next_deduct_time = $receipt_item['next_deduct_time'];
                $deduct_record->more = ($deduct_record->more ?: []) + DeductedRecord::getMoreExtraInfo(Yii::$app->equip, UserContext::fromUser(Yii::$app->request));

                $deduct_record->carry([$vip, $receipt_item['purchase_time']]);
                $standby_deducted_records[] = $deduct_record;
            }
        }

        return $standby_deducted_records;
    }
}
