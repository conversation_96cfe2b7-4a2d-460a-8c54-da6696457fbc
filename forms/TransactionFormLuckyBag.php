<?php

namespace app\forms;

use app\models\Balance;
use app\models\Drama;
use app\models\PayAccount;
use app\models\PayAccountPurchaseDetail;
use app\models\PayAccounts;
use app\models\TransactionItemsLog;
use app\models\TransactionItemsLogList;
use app\models\TransactionLog;
use app\models\TransactionLogDetail;
use Exception;
use missevan\util\Logger;
use missevan\util\MUtils as MUtils2;
use yii\db\Expression;
use yii\helpers\Json;
use yii\web\HttpException;

/**
 * Class TransactionFormLuckyBag
 * @package app\forms
 *
 * 涉及到变更的商品数说明：
 * TransactionItemsLog.goods_num 剩余可兑换的商品数
 * TransactionItemsLog.more.successful_goods_num 成功的商品数（退款时会发生变更）
 * TransactionItemsLog.more.refund_goods_num 退款的商品数
 * TransactionItemsLog.more.redeem_goods_num 兑换的商品数
 * TransactionItemsLog.more.common_coins 购买后可用于分配的钻石明细（兑换或退款时会发生变更）
 * TransactionItemsLog.more.common_coins_redeem 兑换的钻石明细
 * TransactionItemsLog.more.common_coins_refund 退款的钻石明细
 */
final class TransactionFormLuckyBag extends TransactionFormLive
{
    /**
     * @var array|GoodsForm
     */
    public $package_info;
    /**
     * @var array|GoodsForm[]
     */
    public $goods = [];

    /**
     * @var int
     */
    public $transaction_id;

    public function scenarios()
    {
        return array_merge(parent::scenarios(), [
            self::SCENARIO_BUY => ['from_id', 'live_open_log_id', 'package_info', 'goods'],
            self::SCENARIO_REFUND => ['transaction_id', 'goods'],
        ]);
    }

    public function rules()
    {
        return array_merge(parent::rules(), [
            ['package_info', 'checkPackageInfo'],
            ['goods', 'checkGoods'],
            [['transaction_id'], 'integer'],
        ]);
    }

    public function checkPackageInfo()
    {
        $form = new GoodsForm();
        $form->load($this->package_info, '');
        if (!$form->validate()) {
            return $this->addError('package_info', MUtils2::getFirstError($form));
        }
        if ($form->num !== 1) {
            return $this->addError('package_info', '单次只支持发送一个福袋');
        }

        $this->package_info = $form;
    }

    public function checkGoods($attribute)
    {
        if (empty($this->$attribute)) {
            return $this->addError($attribute, '福袋中没有对应的商品');
        }
        $goods = [];
        foreach ($this->$attribute as $i => $good) {
            $goods_form = new GoodsForm(GoodsForm::SCENARIO_LUCKY_BAG);
            $goods_form->load($good, '');
            if (!$goods_form->validate()) {
                return $this->addError($attribute, MUtils2::getFirstError($goods_form));
            }
            if ($goods_form->transaction_type !== GoodsForm::TRANSACTION_TYPE_DRAMA) {
                return $this->addError($attribute, '目前福袋中的商品只支持广播剧');
            }

            // 如果多个同样的商品，则合并为一项，便于后面退款处理
            $goods_form->num += ($goods[$goods_form->id]['num'] ?? 0);
            $goods[$goods_form->id] = $goods_form;
        }

        $this->$attribute = array_values($goods);
        if ($this->getScenario() === self::SCENARIO_BUY) {
            $vip_discount_info = [];
            // NOTICE: 目前福袋中只支持同一种剧集，如果有多个剧集的时候，需要将 rpc 请求提取到循环外
            foreach ($this->$attribute as $good) {
                /**
                 * @var GoodsForm $good
                 */
                $drama = Drama::rpc('api/get-drama-price', ['drama_id' => $good->id, 'user_id' => $this->from_id]);
                if (!$drama) {
                    return $this->addError($attribute, '剧集不存在或为免费剧');
                }
                if ($drama['type'] !== Drama::PAY_TYPE_DRAMA) {
                    return $this->addError($attribute, '剧集非整剧付费类型');
                }
                // NOTICE: 喵喵福袋剧集暂不支持会员折扣购买
                // $vip_discount = $drama['vip_discount'] ?? null;
                $vip_discount = null;
                // NOTICE: 目前仅整剧付费类型的剧集可以发福袋
                $price = self::getDramaPrice($this->is_vip_buyer, Drama::PAY_TYPE_DRAMA, $drama['price'], $vip_discount);
                if ($price !== $good->price) {
                    return $this->addError($attribute, '福袋中的广播剧价值与实际售价不一致');
                }
                if (self::hasVipDiscount($this->is_vip_buyer, $drama['price'], $vip_discount)) {
                    // 会员折扣价购买时，记录会员折扣信息
                    $vip_discount_info[$good->id] = [
                        'discount_price' => $price * $good->num,
                        'original_price' => $drama['price'] * $good->num,
                        'rate' => $vip_discount['rate'],
                        'num' => $good->num,
                        'type' => self::VIP_DISCOUNT_TYPE_BUY_DRAMA,
                    ];
                }
            }
            if ($vip_discount_info) {
                $this->vip_discount_info = $vip_discount_info;
            }
        }
    }

    protected function isNeedCalculcateCreatorRevenue()
    {
        return false;
    }

    public function beforeBuy()
    {
        if (!parent::beforeBuy()) {
            return false;
        }
        $total_price = array_reduce($this->goods, function ($result, $item) {
            /**
             * @var GoodsForm $item
             */
            $result += $item->getTotalPrice();
            return $result;
        }, 0);
        if ($total_price !== $this->package_info->getTotalPrice()) {
            $this->addError('price', '商品价格总额不一致');
            return false;
        }

        return true;
    }

    private function getOrderTitle()
    {
        // 目前福袋中只支持同一个剧
        return sprintf('%s--%s', $this->package_info->title, $this->goods[0]->title);
    }

    private function getSuccessfulGoodsNum(array $items)
    {
        return array_reduce($items, function ($num, $item) {
            /**
             * @var TransactionItemsLog $item
             */
            $num += $item->more_detail['successful_goods_num'];
            return $num;
        }, 0);
    }

    public function buy()
    {
        if (!$this->beforeBuy()) {
            return false;
        }

        [
            'rate' => $rate,
            'type' => $this->type,
            'guild_id' => $this->guild_id,
        ] = $this->getCreatorTradeInfo($this->to_id);

        $db_transaction = TransactionLog::getDb()->beginTransaction();
        try {
            $pay_accounts = PayAccounts::getAccounts($this->from_id, PayAccount::SCOPE_COMMON);
            if ($pay_accounts->getTotalBalance() < $this->package_info->getTotalPrice()) {
                throw new HttpException(400, '您的余额不足，请充值后购买', *********);
            }

            $transaction_log = new TransactionLog([
                'from_id' => $this->from_id,
                'to_id' => 0,
                'gift_id' => $this->package_info->id,
                'title' => $this->getOrderTitle(),

                'type' => $this->type,
                'attr' => TransactionLog::ATTR_LIVE_BUY_LUCKY_BAG,
                'status' => TransactionLog::STATUS_SUCCESS,

                'income' => $this->package_info->getTotalPriceInCurrency(),
                'rate' => $rate,
            ]);
            $this->setTransactionLogDetail($transaction_log);

            $now = $_SERVER['REQUEST_TIME'];
            $trade_items = new TransactionItemsLogList();
            $total_common_coins = [];
            foreach ($this->goods as $good) {
                $price = $good->getTotalPrice();
                // 计算消耗的钻石数
                $common_coins = [];
                $pay_accounts->cost($price);
                $change_accounts = $pay_accounts->getChangeAccounts();
                foreach ($change_accounts as $acc) {
                    $common_coins[$acc->getCoinField()] = ($common_coins[$acc->getCoinField()] ?? 0) + $acc->consume_amount;
                }
                $pay_accounts->updateAccounts();
                $pay_accounts->refresh();

                // 福袋中的各商品明细记录
                $item = new TransactionItemsLog([
                    'goods_id' => $good->id,
                    'goods_title' => $good->title,
                    'goods_price' => $good->price,
                    'goods_num' => $good->num,  // 可兑换的商品数
                    'user_id' => $this->from_id,
                    'status' => TransactionLog::STATUS_SUCCESS,
                    'type' => $this->type,
                    'create_time' => $now,
                    'modified_time' => $now,
                    'more_detail' => [
                        'transaction_type' => $good->transaction_type,
                        'common_coins' => $common_coins,
                        'successful_goods_num' => $good->num,  // 交易成功的商品数
                    ],
                ]);
                // 明细中也记录会员折扣信息
                if (isset($this->vip_discount_info[$good->id])) {
                    $item->more_detail['vip_discount'] = $this->vip_discount_info[$good->id];
                }
                $item->carry($change_accounts);
                $trade_items->addItem($item);

                foreach ($common_coins as $coin_type => $coin_num) {
                    // 交易记录的的钻石消耗总额、税费总额
                    $transaction_log->{"{$coin_type}_coin"} += $coin_num;
                    $transaction_log->all_coin += $coin_num;
                    $total_common_coins[$coin_type] = $coin_num + ($total_common_coins[$coin_type] ?? 0);
                }
            }

            $transaction_log->num = $this->getSuccessfulGoodsNum($trade_items->items());
            $transaction_log->tax = $transaction_log->calcTax($total_common_coins);
            if (!$transaction_log->save()) {
                Logger::errorf('transaction_log save failed: user_id[%d], error[%s]', [$this->from_id, MUtils2::getFirstError($transaction_log)], __METHOD__);
                throw new Exception('保存失败');
            }

            // 保存钻石账户消耗明细
            if (!$trade_items->save($transaction_log->id, function (TransactionItemsLog $items_log) use ($transaction_log) {
                $change_accounts = $items_log->getCarryData();
                PayAccountPurchaseDetail::purchaseDetail(
                    $change_accounts,
                    $transaction_log,
                    PayAccountPurchaseDetail::STATUS_CONFIRM,
                    ['transaction_items_log_id' => $items_log->id]
                );
            })) {
                throw new Exception('保存失败');
            }

            $db_transaction->commit();
        } catch (Exception $e) {
            $db_transaction->rollBack();
            throw $e;
        }

        return [
            'transaction_id' => $transaction_log->id,
            'balance' => PayAccounts::getAccounts($transaction_log->from_id, PayAccount::SCOPE_COMMON)->getTotalBalance(),
            'price' => $this->package_info->getTotalPrice(),
            'context' => Json::encode([
                'type' => $transaction_log->type,
                'attr' => $transaction_log->attr,
                'transaction_id' => $transaction_log->id,
                'tax' => $transaction_log->tax,
                'common_coins' => $trade_items->getTotalCommonCoins(),
                'price' => $this->package_info->getTotalPrice(),
            ]),
        ];
    }

    public function refund()
    {
        if (!$this->validate()) {
            return;
        }

        $db_transaction = TransactionLog::getDb()->beginTransaction();
        try {
            /**
             * @var TransactionLog $tradelog
             */
            $tradelog = TransactionLog::findOneForUpdate('id = :id AND type IN (:type_single, :type_guild) AND attr = :attr AND status = :status', [
                ':id' => $this->transaction_id,
                ':type_single' => TransactionLog::TYPE_LIVE,
                ':type_guild' => TransactionLog::TYPE_GUILD_LIVE,
                ':attr' => TransactionLog::ATTR_LIVE_BUY_LUCKY_BAG,
                ':status' => TransactionLog::STATUS_SUCCESS,
            ]);
            if (!$tradelog) {
                throw new HttpException(404, '未找到可退款的交易');
            }
            if (!$tradelog_detail = TransactionLogDetail::findOne(['id' => $tradelog->id])) {
                throw new Exception('未找到对应的交易记录情况');
            }

            $itemlogs = TransactionItemsLog::find()->where(['tid' => $this->transaction_id, 'status' => TransactionLog::STATUS_SUCCESS])->all();
            if (!empty(array_diff(array_column($this->goods, 'id'), array_column($itemlogs, 'goods_id')))) {
                throw new HttpException(400, '包含不可退款的商品');
            }
            /**
             * @var PayAccountPurchaseDetail[] $account_purchase_details
             */
            $account_purchase_details = PayAccountPurchaseDetail::find()
                ->where(['tid' => $this->transaction_id, 'status' => PayAccountPurchaseDetail::STATUS_CONFIRM])->all();

            $legacy_coins_refund = $new_coins_refund = [];
            $trade_items = new TransactionItemsLogList($itemlogs);
            foreach ($this->goods as $good) {
                $found = false;
                foreach ($itemlogs as $itemlog) {
                    /**.
                     * @var TransactionItemsLog $itemlog
                     */
                    if ($itemlog->goods_id === $good->id
                            && $itemlog->more_detail['transaction_type'] === $good->transaction_type
                            && $good->num > 0
                            && $itemlog->goods_num >= $good->num
                    ) {
                        // 计算退还的钻石（以购买福袋时的商品价格为准）
                        $price = $good->num * $itemlog->goods_price;
                        $pay_accounts = new PayAccounts(PayAccounts::legacyBalanceToAccounts($itemlog->more_detail['common_coins'], $itemlog->user_id));
                        $pay_accounts->cost($price, PayAccount::COIN_DEFAULT_REFUND_ORDER);

                        foreach ($pay_accounts->getChangeAccounts() as $acc) {
                            $coin_field = $acc->getCoinField();
                            $coin_num = $acc->consume_amount;
                            // 退款的钻石
                            $itemlog->more_detail['common_coins_refund'][$coin_field] = $coin_num + ($itemlog->more_detail['common_coins_refund'][$coin_field] ?? 0);
                            // 剩余的可退钻石
                            $itemlog->more_detail['common_coins'][$coin_field] = $itemlog->more_detail['common_coins'][$coin_field] - $coin_num;
                            // 需要退还的各类型钻石总和
                            if ($acc->isLegacy()) {
                                $legacy_coins_refund[$acc->type] = $coin_num + ($legacy_coins_refund[$acc->type] ?? 0);
                            } else {
                                $new_coins_refund[$acc->type] = $coin_num + ($new_coins_refund[$acc->type] ?? 0);
                            }
                        }

                        // 记录退款的商品数量
                        $itemlog->more_detail['refund_goods_num'] = $good->num + ($itemlog->more_detail['refund_goods_num'] ?? 0);
                        // 交易成功的商品数
                        $itemlog->more_detail['successful_goods_num'] -= $good->num;

                        // 更新明细数据
                        $updated = TransactionItemsLog::updateAll([
                            'goods_num' => new Expression('goods_num - :num', [':num' => $good->num]),
                            'more' => Json::encode($itemlog->more_detail),
                            'modified_time' => $_SERVER['REQUEST_TIME'],
                        ], ['id' => $itemlog->id, 'goods_num' => $itemlog->goods_num, 'modified_time' => $itemlog->modified_time]);
                        if ($updated <= 0) {
                            throw new HttpException(500, '正在处理中');
                        }
                        // 可兑换的商品数量
                        $itemlog->goods_num -= $good->num;
                        $itemlog->modified_time = $_SERVER['REQUEST_TIME'];

                        $found = true;
                        break;
                    }
                }
                if (!$found) {
                    throw new HttpException(400, '包含不可退款的商品');
                }
            }

            foreach ($new_coins_refund as $type_index => $coin_num) {
                $left = $coin_num;
                foreach ($account_purchase_details as &$purchase_detail) {
                    if ($left <= 0) {
                        break;
                    }
                    if ($type_index !== $purchase_detail->more['account_type'] || $purchase_detail->purchase_amount <= 0) {
                        continue;
                    }

                    $deduct = min($left, $purchase_detail->purchase_amount);
                    $left -= $deduct;

                    $purchase_detail->setAttributes([
                        'purchase_amount' => $purchase_detail->purchase_amount - $deduct,
                        'more' => array_merge($purchase_detail->more, [
                            'refund' => ($purchase_detail->more['refund'] ?? 0) + $deduct,
                        ]),
                    ]);
                    if (!$purchase_detail->save()) {
                        throw new Exception(MUtils2::getFirstError($purchase_detail));
                    }
                }

                $coin_field = (new PayAccount(['type' => $type_index]))->getCoinField() . '_coin';
                $more = $tradelog_detail->more ?: [];
                $more[$coin_field] = ($more[$coin_field] ?? 0) - $coin_num;
                $tradelog_detail->more = $more;
            }

            // 退还钻石
            foreach ($legacy_coins_refund as $type_index => $coin_num) {
                PayAccount::generateCommonCoin([$type_index => $coin_num], $tradelog->id, $tradelog->from_id);
            }
            $coin_num_refund = array_sum($legacy_coins_refund) + array_sum($new_coins_refund);
            Balance::updateAllCounters([
                'all_coin' => $coin_num_refund,
                'all_consumption' => -$coin_num_refund,
            ], ['id' => $tradelog->from_id]);
            foreach ($new_coins_refund as $type_index => $num) {
                PayAccount::generateCommonCoin([$type_index => $num], $tradelog->id, $tradelog->from_id);
            }

            // 重新计算流水、税费
            $new_coins = $trade_items->getTotalCoins();
            $legacy_coins = [];
            array_walk($new_coins, function ($coin_num, $coin_type) use (&$legacy_coins) {
                if (in_array($coin_type, Balance::balanceFields())) {
                    $legacy_coins["{$coin_type}_coin"] = ($legacy_coins["{$coin_type}_coin"] ?? 0) + $coin_num;
                } elseif (in_array($coin_type, ['new_ios', 'new_googlepay'])) {
                    // iOS、GooglePay 新充值档位消耗记在 ios_coin/googlepay_coin 里
                    $legacy_coins[str_replace('new_', '', $coin_type) . '_coin'] = ($legacy_coins[str_replace('new_', '', $coin_type) . '_coin'] ?? 0) + $coin_num;
                }
            });
            $new_coin_sum = array_sum($new_coins);
            $income = Balance::profitUnitConversion($new_coin_sum, Balance::CONVERT_DIAMOND_TO_YUAN);
            $tax = $tradelog->calcTax($new_coins);
            $update_fields = array_merge($legacy_coins, [
                'all_coin' => new Expression('GREATEST(all_coin, :num) - :num', [':num' => $coin_num_refund]),
                'revenue' => self::calcProfit($income, $tax, $tradelog->rate),
                'income' => $income,
                'tax' => $tax,
                // 退款后重新计算福袋中成功的商品（剧集）总数
                'num' => $this->getSuccessfulGoodsNum($itemlogs),
                'modified_time' => $_SERVER['REQUEST_TIME'],
            ]);

            // 完全退款时不再显示对应的消费记录
            if ($new_coin_sum === 0) {
                $update_fields['status'] = TransactionLog::STATUS_REFUND_DIAMOND;
                TransactionItemsLog::updateAll([
                    'status' => TransactionLog::STATUS_REFUND_DIAMOND,
                    'modified_time' => $_SERVER['REQUEST_TIME'],
                ], ['tid' => $tradelog->id]);
            }

            // 更新交易记录
            if (!TransactionLog::updateByPk($tradelog->id, $update_fields)) {
                throw new Exception('未找到对应的交易记录');
            }
            if (!$tradelog_detail->save()) {
                throw new Exception(MUtils2::getFirstError($tradelog_detail));
            }
            $db_transaction->commit();
        } catch (Exception $e) {
            $db_transaction->rollBack();
            throw $e;
        }

        return [
            'transaction_id' => $tradelog->id,
            'balance' => Balance::getByPk($tradelog->from_id)->getTotalBalance(),
        ];
    }

}
