<?php

namespace app\forms;

class GashaponGoodsForm extends GoodsForm
{
    /**
     * 商品总价（单位：钻石）
     * @var int
     */
    public $total_price;

    public function scenarios()
    {
        return [
            self::SCENARIO_DEFAULT => ['id', 'title', 'total_price', 'num'],
        ];
    }

    public function rules()
    {
        return array_merge(parent::rules(), [
            [['total_price', 'num'], 'required'],
            [['total_price'], 'number', 'integerOnly' => true, 'min' => 1],
        ]);
    }

    public function attributeLabels()
    {
        return array_merge(parent::attributeLabels(), [
            'total_price' => '商品总价',
        ]);
    }

    public function getTotalPrice()
    {
        return $this->total_price;
    }

}
