<?php

namespace app\forms;

use app\components\models\BalanceInterface;
use app\components\util\MUtils;
use app\models\PayAccount;
use app\models\PayAccounts;
use app\models\TransactionItemsLog;
use app\models\TransactionItemsLogList;
use app\models\TransactionLog;
use Exception;
use Yii;
use yii\web\HttpException;

final class TransactionFormGashapon extends TransactionForm
{
    /**
     * @var GashaponGoodsForm|null
     */
    public $goods;

    /**
     * @var GoodsForm[]|array
     */
    public $gifts = [];

    public function scenarios()
    {
        return array_merge(parent::scenarios(), [
            self::SCENARIO_BUY => ['from_id', 'to_id', 'goods', 'gifts', 'noble', 'live_open_log_id'],
        ]);
    }

    public function rules()
    {
        return array_merge(parent::rules(), [
            [['from_id', 'to_id', 'goods', 'gifts'], 'required'],
            ['goods', 'checkGoods'],
            ['gifts', 'checkGifts'],
        ]);
    }

    public function checkGoods($attribute)
    {
        $goods = new GashaponGoodsForm();
        $goods->load($this->goods, '');
        if (!$goods->validate()) {
            return $this->addError('goods', MUtils::getFirstError($goods));
        }
        $this->goods = $goods;
    }

    public function checkGifts()
    {
        if (empty($this->gifts)) {
            return $this->addError('gifts', '没有要送出的礼物');
        }
        foreach ($this->gifts as $i => $gift) {
            $gift_form = new GoodsForm();
            $gift_form->load($gift, '');
            if (!$gift_form->validate()) {
                return $this->addError('gifts', MUtils::getFirstError($gift_form));
            }
            $this->gifts[$i] = $gift_form;
        }
    }

    private function beforeBuy()
    {
        if (self::SCENARIO_BUY !== $this->getScenario()) {
            throw new Exception('场景错误');
        }
        if (!$this->validate()) {
            return false;
        }
        if ($this->from_id === $this->to_id) {
            throw new HttpException(403, '不可以打赏自己', 200020002);
        }

        return true;
    }

    public function buy()
    {
        if (!$this->beforeBuy()) {
            return false;
        }

        [$price, $this->gift_id, $this->title, $this->num, $this->attr] = [
            $this->goods->getTotalPrice(),
            $this->goods->id,
            $this->goods->title,
            $this->goods->num,
            TransactionLog::ATTR_LIVE_BUY_GASHAPON,
        ];
        /**
         * @var BalanceInterface $seller
         */
        [$seller, $rate] = $this->getSellerAndRate();

        $db_transaction = TransactionLog::getDb()->beginTransaction();
        try {
            $scope = $this->getPayAccountScope();
            [$transaction_id, $balance, $tax, $common_coin_costs, $noble_coin_costs] = $this->generateTransaction($price, $rate, TransactionLog::STATUS_SUCCESS, $scope);
            $this->sendGifts($seller, $transaction_id, $price, $common_coin_costs, $noble_coin_costs, $tax, $rate);

            $db_transaction->commit();
        } catch (Exception $e) {
            $db_transaction->rollBack();
            throw $e;
        }

        return [
            'transaction_id' => $transaction_id,
            'balance' => $balance,
            'live_noble_balance' => PayAccounts::getAccounts($this->from_id, PayAccount::SCOPE_LIVE)->getTotalBalance(),
            'price' => $this->goods->getTotalPrice(),
        ];
    }

    private function sendGifts(BalanceInterface $seller, int $goods_transaction_id, int $goods_price, array $common_coin_costs, array $noble_coin_costs, float $tax, float $revenue_rate)
    {
        $trade_details = new TransactionItemsLogList();
        foreach ($this->gifts as $gift) {
            $ratio = $gift->getTotalPrice() / $goods_price;
            $gift_common_coin_costs = array_map(function ($coin_num) use ($ratio) {
                return floor($coin_num * $ratio);
            }, $common_coin_costs);
            $gift_noble_coin_costs = array_map(function ($coin_num) use ($ratio) {
                return floor($coin_num * $ratio);
            }, $noble_coin_costs);


            $tradelog = new TransactionLog([
                'from_id' => $this->from_id,
                'to_id' => $this->to_id,
                'attr' => TransactionLog::ATTR_LIVE_GASHAPON_GIFT,
                'type' => $this->type,
                'status' => TransactionLog::STATUS_SUCCESS,
                'gift_id' => $gift->id,
                // 后续礼物名参数为完整的名称，不再手动拼接
                'title' => preg_match('/（.+）$/', $gift->title) ? $gift->title : ($gift->title . '（超能魔方）'),
                'num' => $gift->num,
                'rate' => $revenue_rate,
                'income' => $gift->getTotalPriceInCurrency(),
                'tax' => ceil($ratio * $tax * 100) / 100,
                'all_coin' => $gift->getTotalPrice(),
            ]);
            $this->setTransactionLogDetail($tradelog);
            if (!$tradelog->save()) {
                throw new Exception(MUtils::getFirstError($tradelog));
            }

            $this->calculateLiveRevenue($seller, $gift->getTotalPrice(), $tradelog->tax, $revenue_rate);
            $trade_details->addItem(new TransactionItemsLog([
                'tid' => $tradelog->id,
                'goods_id' => $tradelog->gift_id,
                'goods_title' => $tradelog->title,
                'goods_price' => $gift->price,
                'goods_num' => $gift->num,
                'user_id' => $tradelog->from_id,
                'status' => $tradelog->status,
                'type' => $tradelog->type,
                'more_detail' => $gift_common_coin_costs + $gift_noble_coin_costs + [
                    'income' => $tradelog->income,
                    'tax' => $tradelog->tax,
                    'context_transaction_id' => $goods_transaction_id,
                    'context_attr' => TransactionLog::ATTR_LIVE_BUY_GASHAPON,
                    'context_type' => $this->type,
                ],
            ]));
        }
        if (!$trade_details->save()) {
            throw new Exception('保存失败');
        }
    }

}
