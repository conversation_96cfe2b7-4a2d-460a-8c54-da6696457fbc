<?php

namespace app\forms;

use app\models\PayAccount;
use app\models\PayAccounts;
use app\models\TransactionLog;
use Exception;
use yii\helpers\Json;

final class TransactionFormFukubukuro extends TransactionForm
{
    const SCENARIO_BUY = 'buy';

    public function __construct($scenario)
    {
        switch ($scenario) {
            case self::SCENARIO_BUY:
                $this->attr = TransactionLog::ATTR_LIVE_BUY_FUKUBUKURO;
                break;
            default:
                throw new Exception('场景错误');
        }

        parent::__construct(['scenario' => $scenario]);
    }

    public function scenarios()
    {
        return array_merge(parent::scenarios(), [
            self::SCENARIO_BUY => ['from_id', 'gift_id', 'price', 'title', 'noble', 'live_open_log_id'],
        ]);
    }

    public function buy()
    {
        if (self::SCENARIO_BUY !== $this->getScenario()) {
            throw new Exception('场景错误');
        }
        if (!$this->validate()) {
            return false;
        }

        $this->to_id = 0;
        [, $rate] = $this->getSellerAndRate();
        $db_transaction = TransactionLog::getDb()->beginTransaction();
        try {
            $scope = $this->getPayAccountScope();
            [$transaction_id, $balance, $tax, $common_coin_costs, $noble_coin_costs] = $this->generateTransaction($this->price, $rate, TransactionLog::STATUS_SUCCESS, $scope);
            $db_transaction->commit();
        } catch (Exception $e) {
            $db_transaction->rollBack();
            throw $e;
        }

        return [
            'transaction_id' => $transaction_id,
            'balance' => $balance,
            'live_noble_balance' => PayAccounts::getAccounts($this->from_id, PayAccount::SCOPE_LIVE)->getTotalBalance(),
            'price' => $this->price,
            'context' => Json::encode([
                'type' => $this->type,
                'attr' => $this->attr,
                'transaction_id' => $transaction_id,
                'tax' => $tax,
                'common_coins' => $common_coin_costs,
                'noble_coins' => $noble_coin_costs,
                'price' => $this->price,
            ]),
        ];
    }

}
