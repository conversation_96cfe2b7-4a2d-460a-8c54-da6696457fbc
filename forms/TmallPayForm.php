<?php

namespace app\forms;

use app\components\util\MUtils;
use app\components\util\TmallPayException;
use app\models\AdTrack;
use app\models\Balance;
use app\models\TopupMenu;
use app\models\MEvent;
use app\models\MobileNumber;
use app\models\PayAccount;
use app\models\PayAccounts;
use app\models\PayBadDebt;
use app\models\RechargeOrder;
use Exception;
use libphonenumber\PhoneNumberUtil;
use missevan\util\MUtils as MUtils2;
use Yii;
use yii\db\Expression;
use yii\web\HttpException;

class TmallPayForm
{
    // 天猫（淘宝游戏）充值回调类型
    const TMALL_METHOD_QUERY = 'taobao.game.charge.zc.query';
    const TMALL_METHOD_ORDER = 'taobao.game.charge.zc.order';
    const TMALL_METHOD_CANCEL = 'taobao.game.charge.zc.cancel';

    // 天猫（淘宝游戏）充值商品一级分类，定义为充值设备类型
    const SECTION1_IOS = 258687;
    const SECTION1_ANDROID = 258686;

    // 充值商品库存不足代码
    const ORDER_CODE_HAS_NO_STOCK = '0303';
    // 充值商品购买数量非法代码
    const ORDER_CODE_ILLEGAL_NUMBER = '0302';
    // 充值商品正在维护不可购买代码
    const ORDER_CODE_GOODS_MAINTENANCE = '0304';
    // 充值商品当前不可充值或充值失败
    const ORDER_CODE_GOODS_UNABLE = '0503';

    public static function getResponseType($method)
    {
        switch ($method) {
            case self::TMALL_METHOD_QUERY:
                return 'gamezctopquery';
            case self::TMALL_METHOD_ORDER:
                return 'gamezctoporder';
            case self::TMALL_METHOD_CANCEL:
                return 'gamezctopcancel';
            default:
                throw new HttpException(400, '参数错误');
        }
    }

    /**
     * 校验天猫充值签名是否正确
     *
     * @link https://open.taobao.com/doc.htm?docId=101617&docType=1
     * @return bool
     */
    public static function checkTmallPaySign()
    {
        // $header_params = Yii::$app->request->headers->toArray();
        $get_params = Yii::$app->request->get();
        $post_params = Yii::$app->request->post();
        $params = $get_params + $post_params;
        unset($params['sign']);

        ksort($params);
        $string = '';
        array_walk($params, function ($v, $k) use (&$string) {
            $string .= $k . $v;
        });

        // if (preg_match('/(json|xml)/', Yii::$app->request->contentType)) {
        //     $string .= Yii::$app->request->rawBody;
        // }

        $sign = strtoupper(md5(TMALL_APPSECRET . $string . TMALL_APPSECRET));
        return $sign === Yii::$app->request->get('sign');
    }

    /**
     * 查询天猫充值订单
     * @param string $tb_order_no
     * @return array
     * @throws TmallPayException
     */
    public static function queryTmallPay($tb_order_no)
    {
        $order = RechargeOrder::findOne([
            'tid' => $tb_order_no,
            'type' => [RechargeOrder::TYPE_TMALL_ANDROID, RechargeOrder::TYPE_TMALL_IOS],
            'origin' => RechargeOrder::ORIGIN_TMALL,
        ]);
        if (!$order) {
            // 每个订单的第一次调用，需要返回的状态是 REQUEST_FAILED，错误码 0104，错误原因订单未创建
            throw new TmallPayException(self::TMALL_METHOD_QUERY, 'REQUEST_FAILED', '0104', '订单未创建');
        }
        switch ($order->status) {
            case RechargeOrder::STATUS_SUCCESS:
                return [
                    self::getResponseType(self::TMALL_METHOD_QUERY) => [
                        'tbOrderNo' => $tb_order_no,
                        'coopOrderSuccessTime' => date('YmdHis', $order->confirm_time),
                        'coopOrderStatus' => 'SUCCESS',
                        'failedReason' => '',
                        'coopOrderNo' => $order->getOrderId(),
                        'failedCode' => '',
                        'coopOrderSnap' => "猫耳FM {$order->num} 钻石",
                    ],
                ];
                break;
            case RechargeOrder::STATUS_CREATE:
                throw new TmallPayException(self::TMALL_METHOD_QUERY, 'UNDERWAY', '', '', [
                    'coopOrderSuccessTime' => '',
                    'coopOrderNo' => $order->getOrderId(),
                    'coopOrderSnap' => "猫耳FM {$order->num} 钻石",
                ]);
                break;
            case RechargeOrder::STATUS_CANCELED:
                throw new TmallPayException(self::TMALL_METHOD_QUERY, 'CANCEL', '', '', [
                    'coopOrderSuccessTime' => '',
                    'coopOrderNo' => $order->getOrderId(),
                    'coopOrderSnap' => "猫耳FM {$order->num} 钻石",
                ]);
                break;
            case RechargeOrder::STATUS_ERROR:
                throw new TmallPayException(self::TMALL_METHOD_QUERY, 'ORDER_FAILED', '0503', '', [
                    'coopOrderSuccessTime' => '',
                    'coopOrderNo' => $order->getOrderId(),
                    'coopOrderSnap' => "猫耳FM {$order->num} 钻石",
                ]);
                break;
            default:
                Yii::error("天猫充值订单记录状态错误：ID {$order->id}, 状态值 {$order->status}", __METHOD__);
                throw new TmallPayException(self::TMALL_METHOD_QUERY, 'GENERAL_ERROR', '9999');
        }
    }

    /**
     * 解析用户手机号
     *
     * @param string $customer 用户手机号
     *   支持格式：
     *     1. "手机号"："13004777040"（默认指定国家为中国）
     *     2.
     *       1)"+国家号手机号": "+8613004777040", "+14387958768"
     *       2)"+国家号-手机号": "+86-13004777040", "*************"
     *       3)"+00国家号手机号": "008613004777040", "0014387958768"
     *       4)"+00国家号-手机号": "0086-13004777040", "001-4387958768"
     * @return MobileNumber|null
     */
    public static function getMobileNumber($customer)
    {
        $phone_util = PhoneNumberUtil::getInstance();
        try {
            $num = $phone_util->parse($customer, 'CN');
            if ($phone_util->isValidNumber($num)) {
                return new MobileNumber($num->getNationalNumber(), $num->getCountryCode());
            }
            return null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 充值天猫充值订单
     *
     * @param string $tb_order_no
     * @param UserContext $user_context 充值订单传参（UA、IP）
     * @return array
     * @throws TmallPayException
     */
    public static function orderTmallPay($tb_order_no, UserContext $user_context)
    {
        $customer = Yii::$app->request->get('customer');  // 用户手机号
        $card_id = (int)Yii::$app->request->get('cardId');  // 商品 ID
        $card_num = (int)Yii::$app->request->get('cardNum');  // 商品数量
        $sum = Yii::$app->request->get('sum');  // 总金额
        $section1 = (int)Yii::$app->request->get('section1', self::SECTION1_IOS);  // 用户设备
        if (self::SECTION1_IOS === $section1) {
            $type = RechargeOrder::TYPE_TMALL_IOS;
        } else {
            $type = RechargeOrder::TYPE_TMALL_ANDROID;
        }

        // TODO: buyerIp from tbOrderSnap
        if (!$mobile_info = self::getMobileNumber($customer)) {
            throw new TmallPayException(self::TMALL_METHOD_ORDER, 'ORDER_FAILED', '0502', '手机号格式有误');
        }
        $user = Yii::$app->sso->getUserInfoByMobile($mobile_info->mobile_num, $mobile_info->region_num);
        if (!$user) {
            throw new TmallPayException(self::TMALL_METHOD_ORDER, 'ORDER_FAILED', '0502', '无法找到该手机号对应的用户');
        }

        if (!$ccy = TopupMenu::findOne(['id' => $card_id, 'device' => TopupMenu::DEVICE_TMALL])) {
            throw new TmallPayException(self::TMALL_METHOD_ORDER, 'ORDER_FAILED', '0305', '无法找到对应的商品');
        }
        $user_id = $user['id'];
        if ($ccy->isLimitStock()) {
            // 若为库存受限商品，获取充值活动（目前仅充值活动的商品有库存限制）
            $stock_topup_info = self::getStockTopupInfo();
            $topup_menu_ids = $stock_topup_info['topup_menu_ids'];
            if (!is_array($topup_menu_ids) || !in_array($ccy->id, $topup_menu_ids)) {
                Yii::error("抢购商品 ID（{$ccy->id}）不在活动（ID: {$stock_topup_info['event_id']}）配置中的充值商品 ID 中",
                    __METHOD__);
                throw new TmallPayException(self::TMALL_METHOD_ORDER, 'ORDER_FAILED',
                    self::ORDER_CODE_GOODS_MAINTENANCE, '商品维护中，暂时不可购买');
            }
            // 优惠充值接口提前 3 分钟开放，避免天猫的时间快于我们
            $buy_start_time = $stock_topup_info['buy_start_time'] - (3 * ONE_MINUTE);
            $buy_end_time = $stock_topup_info['buy_end_time'];
            // 天猫充值优惠商品仅在特定时间或对特定人员开放购买
            $special_user_ids = $stock_topup_info['special_user_ids'] ?? [];
            if (!in_array($user_id, $special_user_ids)
                    // 活动结束时间配置的是结束时间的最后 1 秒（如结束日的 23:59:59）
                    && ($_SERVER['REQUEST_TIME'] < $buy_start_time || $_SERVER['REQUEST_TIME'] > $buy_end_time)) {
                // 特殊用户购买充值优惠活动商品不对购买时间做限制
                throw new TmallPayException(self::TMALL_METHOD_ORDER, 'ORDER_FAILED',
                    self::ORDER_CODE_GOODS_MAINTENANCE, '商品维护中，暂时不可购买');
            }
        }

        if (!$ccy->checkBuyNumber($card_num)) {
            // 若购买数量非法，抛出异常
            throw new TmallPayException(self::TMALL_METHOD_ORDER, 'ORDER_FAILED',
                self::ORDER_CODE_ILLEGAL_NUMBER, '购买数量非法');
        }

        $pay_need = Balance::profitUnitConversion($ccy->price * $card_num, Balance::CONVERT_YUAN_TO_FEN);
        $pay_real = Balance::profitUnitConversion($sum, Balance::CONVERT_YUAN_TO_FEN);
        if ($pay_need !== $pay_real) {
            $log_msg = sprintf('天猫充值金额错误：用户 %d 实付金额 %d 应付金额 %d',
                $user_id, $sum, $ccy->price * $card_num);
            Yii::error($log_msg, __METHOD__);
            throw new TmallPayException(self::TMALL_METHOD_ORDER, 'ORDER_FAILED', '0302', '充值金额有误');
        }

        $order = RechargeOrder::findOne([
            'tid' => $tb_order_no,
            'type' => $type,
            'uid' => $user_id,
        ]);
        if ($order) {
            switch ($order->status) {
                case RechargeOrder::STATUS_SUCCESS:
                    throw new TmallPayException(self::TMALL_METHOD_ORDER, 'SUCCESS', '', '', [
                        'coopOrderSuccessTime' => date('YmdHis', $order->confirm_time),
                        'coopOrderNo' => $order->getOrderId(),
                        'coopOrderSnap' => "猫耳FM {$order->num} 钻石",
                    ]);
                case RechargeOrder::STATUS_CREATE:
                    throw new TmallPayException(self::TMALL_METHOD_ORDER, 'UNDERWAY', '', '', [
                        'coopOrderNo' => $order->getOrderId(),
                        'coopOrderSnap' => "猫耳FM {$order->num} 钻石",
                    ]);
                case RechargeOrder::STATUS_CANCELED:
                    throw new TmallPayException(self::TMALL_METHOD_ORDER, 'FAILED', '0901', '订单已被取消', [
                        'coopOrderNo' => $order->getOrderId(),
                        'coopOrderSnap' => "猫耳FM {$order->num} 钻石",
                    ]);
                case RechargeOrder::STATUS_ERROR:
                    throw new TmallPayException(self::TMALL_METHOD_ORDER, 'ORDER_FAILED', '0503', '充值失败', [
                        'coopOrderNo' => $order->getOrderId(),
                        'coopOrderSnap' => "猫耳FM {$order->num} 钻石",
                    ]);
            }
        }

        // 同一个订单可能请求多次，需要保持结果幂等性，故检查库存操作放在检查订单之后
        if ($ccy->isLimitStock()) {
            if (!$ccy->hasStock($card_num)) {
                // 若为库存受限商品且库存量不足时抛出异常
                throw new TmallPayException(self::TMALL_METHOD_ORDER, 'ORDER_FAILED',
                    self::ORDER_CODE_HAS_NO_STOCK, '商品库存不足');
            }
            $buy_limit_type = $ccy->buyLimit($card_num, $user_id);
            if ($buy_limit_type === TopupMenu::LIMIT_TYPE_BUY_NUMBER) {
                // 若用户购买数量到达上限时抛出异常
                throw new TmallPayException(self::TMALL_METHOD_ORDER, 'ORDER_FAILED',
                    self::ORDER_CODE_ILLEGAL_NUMBER, '商品购买数量达到上限');
            } elseif ($buy_limit_type === TopupMenu::LIMIT_TYPE_NOT_BUY_DRAMA) {
                // 若未满足购买条件时抛出异常
                throw new TmallPayException(self::TMALL_METHOD_ORDER, 'ORDER_FAILED',
                    self::ORDER_CODE_GOODS_UNABLE, '用户未满足购买商品条件');
            }
        }

        $connection = RechargeOrder::getDb();
        $transaction = $connection->beginTransaction();
        try {
            $order = new RechargeOrder([
                'uid' => $user_id,
                'tid' => $tb_order_no,
                'cid' => $ccy->id,
                // 订单中价格显示总金额（而非充值的单价）用于后台统计显示及用户前台查看
                'price' => $ccy->price * $card_num,
                // 购买数量有多个时 app_missevan.recharge_order.num 存储总钻石数
                'num' => $ccy->num * $card_num,
                'ccy' => $ccy->ccy,
                'status' => RechargeOrder::STATUS_SUCCESS,
                'type' => $type,
                'origin' => RechargeOrder::ORIGIN_TMALL,
                'confirm_time' => $_SERVER['REQUEST_TIME'],
                'detail' => $user_context->toArray(),
            ]);
            if (!$order->save()) {
                throw new TmallPayException(self::TMALL_METHOD_ORDER, 'ORDER_FAILED', '0503',
                    MUtils2::getFirstError($order));
            }
            $order_detail = $order->generateDetail();
            if (!$order_detail->save()) {
                throw new TmallPayException(self::TMALL_METHOD_ORDER, 'ORDER_FAILED', '0503',
                    MUtils2::getFirstError($order_detail));
            }

            $balance = Balance::getByPk($user_id);
            if (RechargeOrder::TYPE_TMALL_IOS === $type) {
                $balance->updateCounters([
                    'tmallios' => $order->num, 'all_topup' => $order->num, 'all_coin' => $order->num
                ]);
            } else {
                $balance->updateCounters([
                    'android' => $order->num, 'all_topup' => $order->num, 'all_coin' => $order->num
                ]);
            }
            // 减掉库存
            if ($ccy->isLimitStock() && !$ccy->updateCounterStock(-$card_num)) {
                // 若为库存受限商品且库存不足更新失败抛出异常
                throw new TmallPayException(self::TMALL_METHOD_ORDER, 'ORDER_FAILED',
                    self::ORDER_CODE_HAS_NO_STOCK, '商品库存不足');
            }
            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            if ($e instanceof TmallPayException) {
                throw $e;
            }
            throw new TmallPayException(self::TMALL_METHOD_ORDER, 'ORDER_FAILED', '0503', $e->getMessage());
        }

        // 充值回传广告平台（出错时不会抛出异常）
        AdTrack::callbackPay($user_id, $order->price);
        return [
            self::getResponseType(self::TMALL_METHOD_ORDER) => [
                'tbOrderNo' => $tb_order_no,
                'coopOrderSuccessTime' => date('YmdHis', $order->confirm_time),
                'coopOrderStatus' => 'SUCCESS',
                'failedReason' => '',
                'coopOrderNo' => $order->getOrderId(),
                'failedCode' => '',
                'coopOrderSnap' => "猫耳FM {$order->num} 钻石",
            ],
        ];
    }

    /**
     * 获取有库存限制的商品充值购买信息
     *
     * @return array
     * @throws TmallPayException
     */
    private static function getStockTopupInfo(): array
    {
        // 若为库存受限商品，获取充值活动（目前仅充值活动的商品有库存限制）
        $topup_event = MEvent::getTopupEvent();
        if (!$topup_event) {
            throw new TmallPayException(self::TMALL_METHOD_ORDER, 'ORDER_FAILED',
                self::ORDER_CODE_GOODS_MAINTENANCE, '商品维护中，暂时不可购买');
        }
        // 使用积分抽奖开始时间作为抢购开始时间，且不支持时间偏移
        if (!is_array($topup_event->extended_fields)
                || !key_exists('topup', $topup_event->extended_fields)
                || !key_exists('topup_menu_ids', $topup_event->extended_fields['topup'])
                || !key_exists('buy_start_time', $topup_event->extended_fields['topup'])
                || !key_exists('buy_end_time', $topup_event->extended_fields['topup'])) {
            Yii::error("充值优惠活动（ID: {$topup_event->id}）配置错误", __METHOD__);
            throw new TmallPayException(self::TMALL_METHOD_ORDER, 'ORDER_FAILED',
                self::ORDER_CODE_GOODS_MAINTENANCE, '商品维护中，暂时不可购买');
        }
        $return = $topup_event->extended_fields['topup'];
        $return['event_id'] = $topup_event->id;
        return $return;
    }

    /**
     * 取消天猫充值订单
     *
     * @param string $tb_order_no
     * @return array
     * @throws HttpException
     * @throws TmallPayException
     * @throws \Throwable
     * @throws \yii\base\Exception
     */
    public static function cancelTmallPay($tb_order_no)
    {
        $order = RechargeOrder::findOne([
            'tid' => $tb_order_no,
            'type' => [RechargeOrder::TYPE_TMALL_ANDROID, RechargeOrder::TYPE_TMALL_IOS],
            'origin' => RechargeOrder::ORIGIN_TMALL,
        ]);
        if (!$order) {
            throw new TmallPayException(self::TMALL_METHOD_CANCEL, 'FAILED', '0104', '找不到对应的订单');
        }
        switch ($order->status) {
            case RechargeOrder::STATUS_SUCCESS:
                $connection = RechargeOrder::getDb();
                $transaction = $connection->beginTransaction();
                try {
                    $deduct_order = RechargeOrder::TYPE_TMALL_IOS === $order->type
                        ? PayAccount::COIN_TMALL_IOS_REFUND_ORDER
                        : PayAccount::COIN_DEFAULT_REFUND_ORDER;

                    $pay_accounts = PayAccounts::getAccounts($order->uid, PayAccount::SCOPE_COMMON);
                    $pay_accounts->refund($order, $deduct_order);
                    $transaction->commit();

                    return [
                        self::getResponseType(self::TMALL_METHOD_CANCEL) => [
                            'tbOrderNo' => $tb_order_no,
                            'coopOrderSuccessTime' => date('YmdHis', $order->create_time),
                            'coopOrderStatus' => 'SUCCESS',
                            'failedReason' => '',
                            'coopOrderNo' => $order->getOrderId(),
                            'failedCode' => '',
                            'coopOrderSnap' => "猫耳FM {$order->num} 钻石",
                        ],
                    ];
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    if ($e instanceof TmallPayException) {
                        throw $e;
                    }
                    throw new TmallPayException(self::TMALL_METHOD_CANCEL, 'FAILED', '9999', $e->getMessage());
                }
                break;
            case RechargeOrder::STATUS_CREATE:
                throw new TmallPayException(self::TMALL_METHOD_CANCEL, 'UNDERWAY', '', '', [
                    'coopOrderNo' => $order->getOrderId(),
                    'coopOrderSnap' => "猫耳FM {$order->num} 钻石",
                ]);
                break;
            case RechargeOrder::STATUS_CANCELED:
                throw new TmallPayException(self::TMALL_METHOD_CANCEL, 'CANCEL', '0901', '已取消', [
                    'coopOrderNo' => $order->getOrderId(),
                    'coopOrderSnap' => "猫耳FM {$order->num} 钻石",
                ]);
                break;
            case RechargeOrder::STATUS_ERROR:
                throw new TmallPayException(self::TMALL_METHOD_CANCEL, 'FAILED', '0901', '订单未充值成功', [
                    'coopOrderNo' => $order->getOrderId(),
                    'coopOrderSnap' => "猫耳FM {$order->num} 钻石",
                ]);
                break;
        }
    }

}
