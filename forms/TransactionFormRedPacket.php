<?php

namespace app\forms;

use app\models\TransactionLog;

final class TransactionFormRedPacket extends TransactionFormLive
{

    public function scenarios()
    {
        return array_merge(parent::scenarios(), [
            self::SCENARIO_BUY => ['from_id', 'to_id', 'gift_id', 'price', 'num', 'title', 'live_open_log_id'],
        ]);
    }

    public function beforeBuy()
    {
        if (!parent::beforeBuy()) {
            return false;
        }

        $this->attr = TransactionLog::ATTR_LIVE_RED_PACKET;
        return true;
    }

}
