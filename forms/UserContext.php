<?php

namespace app\forms;

use app\components\util\Equipment;
use yii\web\Request;

class UserContext
{
    /**
     * @var string 设备 User-Agent
     */
    public $user_agent;
    /**
     * @var string 终端 IP
     */
    public $ip;
    /**
     * @var string|null 设备 equip_id
     */
    public $equip_id;
    /**
     * @var string|null 设备 buvid
     */
    public $buvid;

    /**
     * @var Equipment 设备信息
     */
    public $equipment;

    /**
     * @var array 额外信息
     */
    private $_extra = [];

    public function __construct(string $user_agent, string $ip, ?string $equip_id = null, ?string $buvid = null, ?Equipment $equipment = null)
    {
        $this->user_agent = $user_agent;
        $this->ip = $ip;
        if ($equip_id) {
            $this->equip_id = $equip_id;
        }
        if ($buvid) {
            $this->buvid = $buvid;
        }
        if ($equipment) {
            $this->equipment = $equipment;
            if (!$this->equip_id) {
                $this->equip_id = $equipment->getEquipId();
            }
            if (!$this->buvid) {
                $this->buvid = $equipment->getBuvid();
            }
        }
    }

    public function add(string $key, mixed $value): void
    {
        $this->_extra[$key] = $value;
    }

    public function toArray(): array
    {
        $result = [
            'user_agent' => $this->user_agent,
            'ip' => $this->ip,
        ];
        if ($this->equip_id) {
            $result['equip_id'] = $this->equip_id;
        }
        if ($this->buvid) {
            $result['buvid'] = $this->buvid;
        }

        return array_merge($result, $this->_extra);
    }

    public static function fromRpc(Request $request)
    {
        $user_agent = trim($request->post('user_agent'));
        $ip = trim($request->post('ip'));
        $equip_id = trim($request->post('equip_id'));
        $buvid = trim($request->post('buvid'));

        return new self($user_agent, $ip, $equip_id, $buvid);
    }

    public static function fromUser(Request $request, Equipment $equipment = null)
    {
        $user_agent = $request->userAgent ?: '';
        $ip = $request->userIP ?: '';

        return new self($user_agent, $ip, null, null, $equipment);
    }
}
