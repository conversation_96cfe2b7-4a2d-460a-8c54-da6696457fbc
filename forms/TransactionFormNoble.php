<?php

namespace app\forms;

use app\components\util\MUtils;
use app\models\Balance;
use app\models\PayAccount;
use app\models\PayAccounts;
use app\models\TransactionLog;
use app\models\UserNoble;
use Exception;
use Yii;
use yii\web\HttpException;

class TransactionFormNoble extends TransactionForm
{

    /**
     * @var int 返回的贵族钻石数（单位为钻石）
     */
    public $rebate;

    /**
     * @var boolean 是否为贵族体验卡
     */
    public $is_trial = false;

    /**
     * @var int 贵族最大的有效期截止时间点（含），秒级时间戳
     */
    public $max_expire_time = 0;

    public function __construct($scenario = self::SCENARIO_NOBLE)
    {
        parent::__construct(['scenario' => $scenario]);
    }

    public function rules()
    {
        $rules = parent::rules();
        $rules[] = ['noble', 'setAttrByNoble'];
        $rules[] = ['is_trial', 'boolean'];
        return $rules;
    }

    public function scenarios()
    {
        $scenarios = parent::scenarios();
        // 开通或续费贵族时，gift_id 表示贵族 id，noble 为 true 表示开通，false 表示续费
        $scenarios[self::SCENARIO_NOBLE] = ['from_id', 'to_id', 'price', 'rebate', 'gift_id', 'noble', 'title', 'live_open_log_id', 'max_expire_time', 'is_trial', 'num'];
        return $scenarios;
    }

    public function checkPrice($attribute)
    {
        $THRESHOLD = $this->is_trial ? 0 : 1;
        if ((int)$this->$attribute < $THRESHOLD) {
            $error = '价格不能小于 ' . $THRESHOLD;
            $this->addError($attribute, $error);
        }
    }

    public function setAttrByNoble()
    {
        if ($this->is_trial) {
            $this->attr = $this->noble
                ? TransactionLog::ATTR_LIVE_REGISTER_NOBLE_TRIAL
                : TransactionLog::ATTR_LIVE_RENEWAL_NOBLE_TRIAL;
        } else {
            $this->attr = $this->noble
                ? TransactionLog::ATTR_LIVE_REGISTER_NOBLE
                : TransactionLog::ATTR_LIVE_RENEWAL_NOBLE;
        }
    }

    protected function getSellerAndRate()
    {
        [$seller,] = parent::getSellerAndRate();
        // 这里不使用公会及主播的分成比例（公会结算分成比例不使用 transaction_log.rate）
        // 素人主播贵族开通分成比例使用 0.24
        $rate = $this->noble ? 0.24 : 0;
        // 贵族开通旧分成为 0.3
        // if ($_SERVER['REQUEST_TIME'] < Live::TIMESTAMP_NEW_RATE) {
        //     $rate = $this->noble ? 0.3 : 0;
        // }
        return [$seller, $rate];
    }

    public function buyNoble()
    {
        if (self::SCENARIO_NOBLE !== $this->getScenario()) {
            throw new Exception('场景错误');
        }
        if ($this->isBuyerAndSellerSame()) {
            throw new HttpException(403, '不可以在自己的房间开通贵族', *********);
        }

        /**
         * @var $seller Balance|\app\models\GuildBalance
         */
        [$seller, $rate] = $this->getSellerAndRate();
        $db_transaction = TransactionLog::getDb()->beginTransaction();
        try {
            // 生成交易记录
            [$transaction_id, $balance, $tax, $costs] = $this->generateTransaction($this->price, $rate, TransactionLog::STATUS_SUCCESS);

            // 获取贵族钻石
            $user_noble = new UserNoble($this->price, $this->rebate, $this->max_expire_time);
            $user_noble->getRebate($costs, $transaction_id, $this->from_id);
            // 计算主播收益
            $this->calculateLiveRevenue($seller, $this->price, $tax, $rate);

            $this->updateNobleCoinExpireTime();

            $db_transaction->commit();
            $db_transaction = null;

            // 贵族钻石可用余额
            $live_noble_balance = PayAccounts::getAccounts($this->from_id, PayAccount::SCOPE_LIVE)->getTotalBalance();
            return [
                'transaction_id' => $transaction_id,
                'balance' => $balance,
                'live_noble_balance' => $live_noble_balance,
                'price' => $this->price,
            ];
        } catch (Exception $e) {
            if ($db_transaction) {
                $db_transaction->rollBack();
            }
            throw $e;
        }
    }

    public function buyTrialNoble()
    {
        if ($this->getScenario() !== self::SCENARIO_NOBLE) {
            throw new Exception('场景错误');
        }
        if ($this->isBuyerAndSellerSame()) {
            throw new HttpException(403, '不可以在自己的房间开通贵族', *********);
        }
        if ($this->price !== 0) {
            Yii::error('贵族体验卡价格必须为 0', __METHOD__);
            $this->price = 0;
        }
        if ($this->rebate !== 0) {
            Yii::error('贵族体验卡返钻必须为 0', __METHOD__);
            $this->rebate = 0;
        }
        // 用来设置 type 和 guild_id，不使用返回值
        $this->getSellerAndRate();
        [$transaction_id, $balance, $live_noble_balance] = $this->generateTrialNobleTransaction();
        return [
            'transaction_id' => $transaction_id,
            'balance' => $balance,
            'live_noble_balance' => $live_noble_balance,
            'price' => 0,
        ];
    }

    public function generateTrialNobleTransaction()
    {
        $db_transaction = TransactionLog::getDb()->beginTransaction();
        try {
            $transaction = new TransactionLog();
            $transaction->load($this->toArray(), '');
            $transaction->all_coin = 0;
            $transaction->income = 0;
            $transaction->rate = 0;
            $transaction->tax = 0;
            $transaction->status = TransactionLog::STATUS_SUCCESS;
            $this->setTransactionLogDetail($transaction);
            if (!$transaction->save()) {
                throw new HttpException(500, MUtils::getFirstError($transaction));
            }
            $tid = $transaction->id;

            $this->updateNobleCoinExpireTime();

            $db_transaction->commit();
            $db_transaction = null;

            $pay_accounts = PayAccounts::getAccounts($this->from_id, [PayAccount::SCOPE_COMMON, PayAccount::SCOPE_LIVE]);
            return [
                $tid,
                $pay_accounts->getTotalBalanceByScope(PayAccount::SCOPE_COMMON),
                $pay_accounts->getTotalBalanceByScope(PayAccount::SCOPE_LIVE),
            ];
        } catch (Exception $e) {
            if ($db_transaction) {
                $db_transaction->rollBack();
            }
            throw $e;
        }
    }

    /**
     * 更新存量贵族钻石有效期
     */
    private function updateNobleCoinExpireTime(): void
    {
        if ($this->max_expire_time) {
            PayAccount::updateNobleCoinExpireTime($this->from_id, $this->max_expire_time, $this->is_trial);
        }
    }
}
