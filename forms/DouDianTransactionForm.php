<?php

namespace app\forms;

use app\components\auth\douyin\AuthDouDian;
use app\components\auth\douyin\DouDianException;
use app\components\auth\douyin\DouDianGoods;
use app\components\auth\douyin\DouDianOrder;
use app\components\auth\douyin\DouDianMsgInterface;
use app\models\DramaBoughtDetailLog;
use app\models\MMessageAssign;
use app\models\PayAccounts;
use app\models\RechargeOrderDetail;
use missevan\util\MUtils as MUtils2;
use app\models\Balance;
use app\models\Drama;
use app\models\Mowangskuser;
use app\models\PayAccount;
use app\models\PayAccountPurchaseDetail;
use app\models\RechargeOrder;
use app\models\TransactionLog;
use Exception;
use Yii;
use yii\db\Expression;
use yii\helpers\Html;
use yii\web\HttpException;

/**
 * Class DouDianTransactionForm
 *
 * @package app\forms
 *
 * @method void createOrder(DouDianMsgInterface $msg, UserContext $user_context)
 * @method void pay(DouDianMsgInterface $msg)
 * @method void refund(DouDianMsgInterface $msg)
 */
class DouDianTransactionForm
{
    /**
     * @var AuthDouDian
     */
    private $_client;

    public function __construct(AuthDouDian $client)
    {
        $this->_client = $client;
    }

    public function __call($method, $args)
    {
        if (!$this->isLegalShopId($args[0])) {
            throw new HttpException(403, '店铺 ID 错误');
        }

        return $this->{'_' . $method}(...$args);
    }

    private function isLegalShopId(DouDianMsgInterface $msg)
    {
        return $this->_client->isLegalShopId($msg->getShopId());
    }

    /**
     * @param DouDianMsgInterface $msg
     * @param UserContext $user_context 充值订单传参（UA、IP）
     * @throws DouDianException|HttpException
     */
    private function _createOrder(DouDianMsgInterface $msg, UserContext $user_context): void
    {
        // 获取抖店订单
        $shop_order = $this->_client->getShopOrder($msg->getShopOrderId());
        if (!$shop_order->isReadyToPay()
                && !$shop_order->isReadyToDeliveryGoods()
                && !$shop_order->isDelivered()
                && !$shop_order->isFinished()
        ) {
            throw new DouDianException(400, '订单状态错误');
        }
        if ($shop_order->getPriceInFen() !== $shop_order->getSumPriceByGoods()) {
            throw new HttpException(400, '待支付金额与商品明细汇总金额不一致');
        }

        // 获取订单商品信息
        $all_goods = $shop_order->getGoods();
        if (count($all_goods) !== 1) {
            throw new HttpException(403, '订单中只能包含一种商品');
        }
        /**
         * @var DouDianGoods
         */
        $goods = $all_goods[0];
        if ($goods->getNum() !== 1) {
            throw new HttpException(403, '单个商品只能购买一次');
        }

        if (!$drama_id = $goods->getId()) {
            throw new HttpException(400, '商品编号错误');
        }
        $msg->setShopOrder($shop_order);

        // 抖店消息推送不一致与订单业务类型不一致时不进行处理
        // 例：虚拟商品通过全局消息推送处理、充值直连模式商品通过 SPI 推送处理
        if ($shop_order->getBiz() !== $msg->getBiz()) {
            return;
        }

        $drama = null;
        try {
            // TODO: 后续改成 /rpc/api/get-drama-price 获取
            $drama = Drama::rpc('api/get-drama-details-by-id', [
                'drama_id' => $drama_id,
            ]);
        } catch (Exception $e) {
            Yii::error('doudian get drama details error: ' . $e->getMessage(), __METHOD__);
            throw new DouDianException(500, '服务器暂时维护中，请稍候再试');
        }
        if (!$drama) {
            throw new HttpException(404, '广播剧不存在或已下架');
        }
        $drama_price_in_fen = Balance::profitUnitConversion($drama['drama']['price'], Balance::CONVERT_DIAMOND_TO_FEN);
        // 抖店商品价格设置错误则进行阻断购买
        if ($shop_order->getOriginalPriceInFen() !== $drama_price_in_fen) {
            throw new HttpException(403, sprintf('抖店商品价格设置错误：预期 %d（单位：分），实际 %d（单位：分）', $drama_price_in_fen, $shop_order->getOriginalPriceInFen()));
        }
        if ($shop_order->getPriceInFen() !== $drama_price_in_fen) {
            // 抖店平台存在优惠券或补贴，使得待支付金额与广播剧售价不一致，此处只记录日志不进行阻断
            // throw new HttpException(400, '待支付金额与广播剧售价不匹配');
            Yii::error(
                sprintf(
                    '抖店商品 %s 待支付金额 %d（单位：分）与广播剧售价 %d（单位：分）不匹配，抖店订单号 %s',
                    $drama['drama']['name'],
                    $shop_order->getPriceInFen(),
                    Balance::profitUnitConversion($drama['drama']['price'], Balance::CONVERT_DIAMOND_TO_FEN),
                    $shop_order->getOrderId()
                ),
                __METHOD__
            );
        }
        if ($drama['drama']['pay_type'] !== Drama::PAY_TYPE_DRAMA) {
            throw new HttpException(403, '目前仅支持整剧付费的广播剧购买');
        }

        // 获取订单中商品接收人的手机号
        $mobile = $shop_order->getBuyerTelephone();
        /**
         * @var Mowangskuser $user
         */
        [$user, $is_new] = self::getBuyer($mobile);
        if (!$is_new && $user->isBanTopupAndConsume()) {
            throw new HttpException(403, '账号已被封禁，不可进行充值消费操作');
        }

        if (RechargeOrder::find()->where(['tid' => $shop_order->getOrderId(), 'type' => RechargeOrder::TYPE_DOUDIAN])->exists()) {
            throw new HttpException(403, '订单已创建');
        }
        // 创建充值订单
        $order = self::generateTopupOrder(
            $shop_order->getOrderId(),
            $user->id,
            $drama['drama']['id'],
            Balance::profitUnitConversion($drama['drama']['price'], Balance::CONVERT_DIAMOND_TO_YUAN),
            $user_context
        );
        if (!$order->save()) {
            Yii::error(sprintf('doudian create order error: %s', MUtils2::getFirstError($order)), __METHOD__);
            throw new DouDianException(500, '服务器暂时维护中，请稍候再试');
        }
        $order_detail = $order->generateDetail($shop_order->getPriceInFen(), $shop_order->getPromotionPriceInFen());
        if (!$order_detail->save()) {
            Yii::error(sprintf('doudian create order error: %s', MUtils2::getFirstError($order_detail)), __METHOD__);
            throw new DouDianException(500, '服务器暂时维护中，请稍候再试');
        }

        $msg->setSellerOrder($order);
    }

    /**
     * @param string $mobile
     * @return array
     * @throws \yii\web\HttpException
     */
    private static function getBuyer(string $mobile)
    {
        $mobile_info = MUtils2::getMobileNumber($mobile);
        $user = Yii::$app->sso->getUserInfoByMobile($mobile_info->mobile_num, $mobile_info->region_num);
        $is_new = false;

        if (!$user) {
            $is_new = true;
            $user_info = Yii::$app->sso->ensure($mobile_info->mobile_num, $mobile_info->region_num);
            $user_info['user_id'] = $user_info['id'];
            $login_form = new LoginForm(['scenario' => LoginForm::SCENARIO_ENSURE_REGISTER]);
            $login_form->type = LoginForm::MOBILE;
            $login_form->retryRegist($user_info, false);
            $user = ['id' => $user_info['id'], 'username' => $user_info['username']];
        }

        $model = new Mowangskuser();
        $model->setAttributes($user, false);
        return [$model, $is_new];
    }

    public static function generateTopupOrder(string $shop_order_id, int $user_id, int $goods_id, $price_in_yuan, UserContext $user_context)
    {
        $coin_num = Balance::profitUnitConversion($price_in_yuan, Balance::CONVERT_YUAN_TO_DIAMOND);
        if ($coin_num === 0) {
            // 如果实际支付了 0.01 元（如使用了优惠券等），则钻石字段记为 1 钻，避免重复购买处理为等额钻石时变成加 0 钻问题
            $coin_num = 1;
        }
        return new RechargeOrder([
            'uid' => $user_id,
            'cid' => $goods_id,
            'tid' => $shop_order_id,
            'ccy' => 0,
            'num' => $coin_num,
            'price' => $price_in_yuan,
            'status' => RechargeOrder::STATUS_CREATE,
            'type' => RechargeOrder::TYPE_DOUDIAN,
            'origin' => RechargeOrder::ORIGIN_DOUDIAN,
            'detail' => $user_context->toArray(),
        ]);
    }

    private static function topup(Balance $buyer, RechargeOrder $order, DouDianOrder $shop_order)
    {
        try {
            // 更新充值订单
            if (RechargeOrder::updateAll(['status' => RechargeOrder::STATUS_SUCCESS, 'modified_time' => $_SERVER['REQUEST_TIME'], 'confirm_time' => $_SERVER['REQUEST_TIME']],
                ['id' => $order->id, 'status' => RechargeOrder::STATUS_CREATE]) <= 0) {
                throw new HttpException(404, '未找到对应订单');
            }

            $shop_goods = $shop_order->getGoods()[0];
            // 生成抖店渠道钻石账户
            $accounts = PayAccount::generate([PayAccount::TYPE_COIN_INDEX_DOUDIAN => $order->num],
                $order->id, $buyer->id, 0, PayAccount::ATTR_COMMON_COIN, PayAccount::SCOPE_COMMON,
                function ($acc) use ($shop_goods) {
                    /**
                     * @var PayAccount $acc
                     */
                    // 若带货主播为精选联盟，且不在设置的白名单中，则渠道率以 2 倍计
                    // 小店自卖或精选联盟的猫耳官方的带货主播，渠道费为 1%（抖店平台费率 1%）
                    // 精选联盟的非猫耳官方的带货主播，渠道费为 2%（抖店平台费率 1% + 带货佣金费率 1%）
                    // https://info.missevan.com/pages/viewpage.action?pageId=********
                    if ($shop_goods->getClientBizType() === DouDianGoods::CLIENT_BIZ_ALLY
                            && !in_array($shop_goods->getAuthorId(), Yii::$app->params['service']['doudian']['normal_fee_rate_author_ids'])) {
                        if ($shop_goods->getAuthorId() === ***********) {
                            // 带货达人（铁铁的书架，UID ***********）对不同广播剧走不同渠道费
                            if (in_array($shop_goods->getId(), [47639, 49881, 49880])
                                    || ($shop_goods->getId() === 51915 && $_SERVER['REQUEST_TIME'] <= strtotime('2025-06-30 23:59:59'))
                                    || ($shop_goods->getId() === 52457 && $_SERVER['REQUEST_TIME'] <= strtotime('2024-06-06 23:59:59'))
                                    || ($shop_goods->getId() === 52984 && $_SERVER['REQUEST_TIME'] <= strtotime('2025-07-14 23:59:59'))
                                    || ($shop_goods->getId() === 53304 && $_SERVER['REQUEST_TIME'] <= strtotime('2025-07-12 23:59:59'))
                                    || ($shop_goods->getId() === 56872 && $_SERVER['REQUEST_TIME'] <= strtotime('2025-09-26 23:59:59'))
                                    || ($shop_goods->getId() === 56874 && $_SERVER['REQUEST_TIME'] <= strtotime('2025-03-15 23:59:59'))
                                    || ($shop_goods->getId() === 59041 && $_SERVER['REQUEST_TIME'] <= strtotime('2024-12-31 23:59:59'))
                                    || ($shop_goods->getId() === 61215 && $_SERVER['REQUEST_TIME'] <= strtotime('2025-12-12 23:59:59'))
                                    || ($shop_goods->getId() === 61216 && $_SERVER['REQUEST_TIME'] <= strtotime('2025-12-12 23:59:59'))
                                    || ($shop_goods->getId() === 61217 && $_SERVER['REQUEST_TIME'] <= strtotime('2025-12-12 23:59:59'))
                                    || ($shop_goods->getId() === 51913 && $_SERVER['REQUEST_TIME'] <= strtotime('2024-05-20 23:59:59'))
                                    || ($shop_goods->getId() === 51911 && $_SERVER['REQUEST_TIME'] <= strtotime('2025-06-30 23:59:59'))
                                    || ($shop_goods->getId() === 51910 && $_SERVER['REQUEST_TIME'] <= strtotime('2025-06-30 23:59:59'))
                            ) {
                                $acc->setSpecialFeeRate($acc->getFeeRate(new TransactionLog(['type' => TransactionLog::TYPE_DRAMA])) * 2);
                            } elseif (($shop_goods->getId() === 52347 && $_SERVER['REQUEST_TIME'] >= strtotime('2023-05-24 00:00:00') && $_SERVER['REQUEST_TIME'] <= strtotime('2023-06-03 23:59:59'))
                                || (in_array($shop_goods->getId(), [15861, 58443, 58444, 58445, 58446, 58489, 58262,
                                    58263, 58265, 17496, 58217, 23329, 59047, 59048, 59049, 21102, 24727, 26165, 58219,
                                    58220, 29122, 29125, 37773, 42264, 29154, 29198, 30835, 33758, 36817, 41326, 20624,
                                    29130, 36979, 29151, 42264, 41369, 42777, 44564, 45209, 25790, 39231, 47546, 21594,
                                    29117, 43127, 38864, 44183, 34478, 24660, 29129, 13774, 20160, 52355, 52292, 52102,
                                    47497, 55998, 52394, 55082, 52307, 50454, 52351, 57335])
                                    && $_SERVER['REQUEST_TIME'] >= strtotime('2023-02-18 00:00:00')
                                    && $_SERVER['REQUEST_TIME'] <= strtotime('2023-06-30 23:59:59'))
                            ) {
                                $acc->setSpecialFeeRate($acc->getFeeRate(new TransactionLog(['type' => TransactionLog::TYPE_DRAMA])) * 5);
                            } else {
                                // PASS
                                // 其他广播剧走普通的渠道费率
                            }
                        } elseif ($shop_goods->getAuthorId() === 67733688910) {
                            // 精选联盟-特殊达人（听雨工作室TY，UID 67733688910）对不同广播剧走不同渠道费
                            if (($shop_goods->getId() === 62452 && $_SERVER['REQUEST_TIME'] <= strtotime('2025-10-20 23:59:59'))
                                    || (in_array($shop_goods->getId(), [63496, 67704]) && $_SERVER['REQUEST_TIME'] <= strtotime('2025-11-01 23:59:59'))
                                    || ($shop_goods->getId() === 66304 && $_SERVER['REQUEST_TIME'] <= strtotime('2026-01-09 23:59:59'))
                            ) {
                                $acc->setSpecialFeeRate($acc->getFeeRate(new TransactionLog(['type' => TransactionLog::TYPE_DRAMA])) * 2);
                            } else {
                                // PASS
                                // 其他广播剧走普通的渠道费率
                            }
                        } else {
                            // 其它达人走渠道费为 2%
                            $acc->setSpecialFeeRate($acc->getFeeRate(new TransactionLog(['type' => TransactionLog::TYPE_DRAMA])) * 2);
                        }
                    }
                });

            // 更新总钻石、总充值冗余字段
            $buyer->updateAttributes([
                'all_coin' => new Expression('all_coin + :consume', [':consume' => $order->num]),
                'all_topup' => new Expression('all_topup + :consume', [':consume' => $order->num]),
            ]);

            return new PayAccounts($accounts);
        } catch (Exception $e) {
            Yii::error('抖店充值钻石失败：' . $e->getMessage(), __METHOD__);
            throw $e;
        }
    }

    private static function consume(Balance $buyer, RechargeOrder $order, DouDianGoodsForm $goods, PayAccounts $acc)
    {
        try {
            // 扣钻处理
            $remaining = $acc->cost($order->num);
            if (!$acc->hasChanged() || $remaining !== 0) {
                throw new Exception('余额不足');
            }
            $acc->updateAccounts();

            // 创建消费记录
            // --购自抖音「猫耳FM旗舰店」
            $tradelog = new TransactionLog([
                'from_id' => $buyer->id,
                'to_id' => $goods->getOwnerID(),
                'type' => $goods->getTransactionLogType(),
                'status' => TransactionLog::STATUS_SUCCESS,
                'gift_id' => $goods->getGoodsID(),
                'title' => $goods->getGoodsTitle(),
                'all_coin' => $order->num,
                'income' => $order->price,
                'rate' => $goods->getRevenueRate(),
                'attr' => $goods->getTransactionLogAttr(),
            ]);
            $tradelog->tax = $tradelog->calcTax($acc);
            if (!$tradelog->save()) {
                Yii::error(sprintf('doudian pay error: %s', MUtils2::getFirstError($tradelog)), __METHOD__);
                throw new DouDianException(500, '购买失败');
            }

            // 生成抖店渠道钻石账户消费明细
            PayAccountPurchaseDetail::purchaseDetail($acc->getChangeAccounts(), $tradelog, PayAccountPurchaseDetail::STATUS_CONFIRM);

            // 更新总消费及总钻石冗余字段
            $buyer->updateAttributes([
                'all_consumption' => new Expression('all_consumption + :consume', [':consume' => $order->num]),
                'all_coin' => new Expression('GREATEST(all_coin, :consume) - :consume', [':consume' => $order->num]),
            ]);
        } catch (Exception $e) {
            Yii::error('抖店消耗钻石失败：' . $e->getMessage(), __METHOD__);
            throw $e;
        }
    }

    /**
     * @param DouDianMsgInterface $msg
     * @throws DouDianException|HttpException
     */
    private function _pay(DouDianMsgInterface $msg): void
    {
        // 获取抖店订单
        if (!$shop_order = $msg->getShopOrder()) {
            $shop_order = $this->_client->getShopOrder($msg->getShopOrderId());
        }
        if (!$shop_order->isReadyToDeliveryGoods()
                && !$shop_order->isDelivered()
                && !$shop_order->isFinished()
        ) {
            throw new DouDianException(400, '订单状态错误');
        }
        // 抖店消息推送不一致与订单业务类型不一致时不进行处理
        // 例：虚拟商品通过全局消息推送处理、充值直连模式商品通过 SPI 推送处理
        if ($shop_order->getBiz() !== $msg->getBiz()) {
            return;
        }

        $transaction = RechargeOrder::getDb()->beginTransaction();
        try {
            // 查询充值订单
            $order = RechargeOrder::findOne([
                'tid' => $shop_order->getOrderId(),
                'status' => RechargeOrder::STATUS_CREATE,
            ]);
            if (!$order) {
                // 抖店的付款通知可能先于下单通知到达，或下单逻辑未完成，或主从延迟等原因，造成付款逻辑查询不到对应的订单
                // 此时抛出自定义的抖店异常类，让抖店稍后再次重发通知
                throw new DouDianException(404, '未找到对应订单');
            }
            $real_price_in_fen = (int)RechargeOrderDetail::find()->select('real_price')->where(['id' => $order->id])->scalar();
            if ($shop_order->getPriceInFen() !== $real_price_in_fen) {
                throw new HttpException(400, '订单金额不匹配');
            }

            $goods = DouDianGoodsForm::initiateByDramaId($order->cid, $order->uid);
            $buyer = Balance::getByPk($order->uid);
            $drama_id = $goods->getGoodsID();

            $success_purchase = false;
            if (TransactionLog::isPurchased($buyer->id, $drama_id, TransactionLog::TYPE_DRAMA)) {
                Yii::warning(sprintf('抖店用户 %d 重复购买剧集 %d，为其充值等额钻石 %d', $buyer->id, $drama_id, $order->num));
                // TODO: 将抖店渠道钻石暂时加到 android 上，后续将抖店渠道钻石账户迁移至 pay_account 中
                $buyer->updateAttributes([
                    'android' => new Expression('android + :coin', [':coin' => $order->num]),
                    'all_coin' => new Expression('all_coin + :coin', [':coin' => $order->num]),
                    'all_topup' => new Expression('all_topup + :coin', [':coin' => $order->num]),
                ]);
                if (RechargeOrder::updateAll(['status' => RechargeOrder::STATUS_SUCCESS, 'modified_time' => $_SERVER['REQUEST_TIME'], 'confirm_time' => $_SERVER['REQUEST_TIME']],
                    ['id' => $order->id, 'status' => RechargeOrder::STATUS_CREATE]) <= 0) {
                    throw new HttpException(404, '未找到对应订单');
                }
                $goods_title = Html::encode($goods->getGoodsTitle());
                MMessageAssign::sendSysMsg(
                    $buyer->id,
                    '钻石充值成功',
                    sprintf(
                        '由于您在抖音小店重复购买了广播剧%s，现已折合为 %d 钻石到您账号中。',
                        preg_match('/《.+?》/', $goods_title) ? $goods_title : "《{$goods_title}》",
                        $order->num
                    )
                );
            } else {
                $accounts = self::topup($buyer, $order, $shop_order);
                self::consume($buyer, $order, $goods, $accounts);
                $success_purchase = true;
            }

            // 虚拟物品会自动变更为已发货状态
            // 手动更新为已发货状态
            // if ($shop_order->getBiz() === DouDianOrder::ORDER_BIZ_VIRTUAL && $shop_order->isReadyToDeliveryGoods()) {
            //     $this->_client->deliverGoods($order->tid);
            // }

            $transaction->commit();
            $transaction = null;

            try {
                Drama::subscribe($drama_id, $buyer->id, 1);
            } catch (Exception $e) {
                Yii::error('subscribe drama error: ' . $e->getMessage(), __METHOD__);
                // PASS
            }
            if ($success_purchase) {
                // 添加购买整剧 databus 消息
                DramaBoughtDetailLog::addLog($buyer->id, $drama_id, DramaBoughtDetailLog::ORIGIN_DOUDIAN);
            }
        } catch (Exception $e) {
            if ($transaction) {
                $transaction->rollBack();
            }
            throw $e;
        }
    }

    /**
     * @param DouDianMsgInterface $msg
     * @throws Exception
     */
    private function _refund(DouDianMsgInterface $msg): void
    {
        if ($msg->getBiz() !== DouDianOrder::ORDER_BIZ_TOPUP) {
            return;
        }

        $now = $_SERVER['REQUEST_TIME'];
        $topup_order = $msg->getSellerOrder();

        $transaction = RechargeOrder::getDb()->beginTransaction();
        try {
            $row_num_affected = RechargeOrder::updateAll(['status' => RechargeOrder::STATUS_CANCELED, 'modified_time' => $now],
                [
                    'status' => RechargeOrder::STATUS_SUCCESS,
                    'id' => $topup_order->id,
                ]
            );
            if ($row_num_affected !== 1) {
                throw new Exception('充值记录更新失败');
            }

            $buyer = Balance::getByPk($topup_order->uid);
            $consume_detail = PayAccount::find()
                ->alias('t1')
                ->select('t2.id, t2.account_id, t2.tid')
                ->innerJoin(PayAccountPurchaseDetail::tableName() . ' AS t2', 't2.account_id = t1.id')
                ->where([
                    't1.tid' => $topup_order->id,
                    't1.user_id' => $topup_order->uid,
                    't1.status' => PayAccount::STATUS_SUCCESS,
                    't2.status' => PayAccountPurchaseDetail::STATUS_CONFIRM,
                ])
                ->limit(1)->asArray()->one();
            if ($consume_detail) {
                $row_num_affected = TransactionLog::updateAll(['status' => TransactionLog::STATUS_REFUND, 'modified_time' => $now], [
                    'id' => (int)$consume_detail['tid'],
                    'status' => TransactionLog::STATUS_SUCCESS,
                ]);
                if ($row_num_affected !== 1) {
                    throw new Exception('消费记录更新失败');
                }
                $row_num_affected = PayAccount::updateAll([
                    'status' => PayAccount::STATUS_REFUND,
                    'modified_time' => $now,
                ], ['id' => (int)$consume_detail['account_id'], 'status' => PayAccount::STATUS_SUCCESS]);
                if ($row_num_affected !== 1) {
                    throw new Exception('账户消费明细记录更新失败');
                }
                $row_num_affected = PayAccountPurchaseDetail::updateAll(['status' => PayAccountPurchaseDetail::STATUS_CANCEL, 'modified_time' => $now], [
                    'id' => (int)$consume_detail['id'],
                    'status' => PayAccountPurchaseDetail::STATUS_CONFIRM,
                ]);
                if ($row_num_affected !== 1) {
                    throw new Exception('账户消费明细记录更新失败');
                }
                $row_num_affected = $buyer->updateAttributes([
                    'all_topup' => new Expression('GREATEST(all_topup, :coin) - :coin', [':coin' => $topup_order->num]),
                    'all_consumption' => new Expression('GREATEST(all_consumption, :coin) - :coin', [':coin' => $topup_order->num])
                ]);
                if ($row_num_affected !== 1) {
                    Yii::error(sprintf('抖店退款余额记录更新失败：user_id[%d]', $topup_order->uid), __METHOD__);
                    // PASS
                }
            } else {
                // 重复购买后退款
                $row_num_affected = $buyer->updateAttributes([
                    'android' => new Expression('GREATEST(android, :coin) - :coin', [':coin' => $topup_order->num]),
                    'all_topup' => new Expression('GREATEST(all_topup, :coin) - :coin', [':coin' => $topup_order->num]),
                    'all_coin' => new Expression('GREATEST(all_coin, :coin) - :coin', [':coin' => $topup_order->num])
                ]);
                if ($row_num_affected !== 1) {
                    Yii::error(sprintf('抖店退款余额记录更新失败：user_id[%d]', $topup_order->uid), __METHOD__);
                    // PASS
                }
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

}
